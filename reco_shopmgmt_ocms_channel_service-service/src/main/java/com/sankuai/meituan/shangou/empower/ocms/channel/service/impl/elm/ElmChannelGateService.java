package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.linz.boot.util.Strings;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ChannelErrorCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ChannelVirtualSysParamConstant.ElmSysParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ChannelVirtualSysParamConstant.PoiParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ElmResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.RateLimitLevelEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ChannelVirtualConfigErrorCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ElmSignUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.message.XMSender;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.RHINO_UPTIMATE_SET;

/**
 * 饿了么请求公共服务接口
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:09
 **/
@Service("elmChannelGateService")
public class ElmChannelGateService extends BaseChannelGateService {
    @Value("${elm.url.base}")
    private String baseUrl;

    @Value("${elm.url.getAccessToken}")
    private String getAccessTokenUrl;

    @Value("${elm.url.oauth2Token}")
    private String getOauth2TokenUrl;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private ElmChannelVirtualConfigServiceImpl elmChannelVirtualConfigService;

    @Resource
    private CommonLogger log;

    /**
     * 传入门店id返回map结构、如果确定是返回dto请使用sendPostReturnDto、
     * 确定是多门店请求返回结果是map请使用sendPostAppMapDto
     *
     * @param postUrlEnum 请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam    请求业务参数
     * @return
     */
    @Deprecated
    @Override
    public <T> T sendPost(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
        if (CollectionUtils.isEmpty(baseRequest.getStoreIdList())) {
            return sendPostReturnDto(postUrlEnum, baseRequest, bizParam);
        }
        return super.sendPostAppMapDto(postUrlEnum, baseRequest, bizParam);
    }

    /**
     * 返回非map结构
     */
    public <T> T sendPostReturnDto(ChannelPostInter postUrlEnum, Long tenantId, Integer channelId, Object bizParam, Long storeId) {
        BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(channelId);
        if (storeId > NumberUtils.LONG_ZERO) {
            baseRequest.setStoreIdList(Lists.newArrayList(storeId));
        }

        return sendPostReturnDto(postUrlEnum, baseRequest, bizParam);
    }

    /**
     * 传入门店id也返回对象、订单相关service大部分是返回对象
     * 多品牌各渠道发送请求公共方法门店维度 post
     *
     * @param postUrlEnum 请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam    请求业务参数
     * @return
     */
    public <T> T sendPostReturnDto(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
        return super.sendPostAppDto(postUrlEnum, baseRequest, bizParam);
    }

    /**
     * 各渠道发送请求公共方法
     *
     * @param url      请求URL
     * @param bizParam 请求业务参数
     * @return
     */
    @Override
    public Map<String, Object> sendPost(String url, String method, BaseRequest baseRequest, Object bizParam) {
        return super.sendPostApp(url, method, baseRequest, bizParam);
    }

    @Override
    protected Map<String, Object> generatePostParams(String url, String method, Map<String, Object> sysParam, Map<String, Object> bizParam) {
        return generatePostParams(method, sysParam, bizParam);
    }

    /**
     * 向渠道发起post请求
     * @return
     * @param <T>
     */
    public <T> T postToChannel(ChannelPostInter postUrlEnum, Map<String, Object> sysParam, Map<String, Object> bizParam){
        Map<String, Object> postParams = generatePostParams(postUrlEnum.getUrl(), sysParam, bizParam);
        // 调用
        ChannelResponseDTO dto = dealPostResult(postRequest(postUrlEnum, postParams, null), postUrlEnum.getResultClass());
        if(Objects.isNull(dto)){
            log.warn("ElmChannelGateService.postToChannel 执行post请求结果转换ChannelResponseDTO为null 直接返回null");
            return null;
        }

        log.info("【渠道原始日志】, operate:{}, url:{}, success:{}, isErp:{}, request:{}, postParams:{}, result:{}",
                postUrlEnum.getUrlShortName(),
                postUrlEnum.getUrl(),
                parseSuccess(dto),
                postParams,
                JacksonUtils.toJson(dto));
        return (T) dto;
    }

    /**
     * 为ISV应用构建系统参数
     * @param appKey
     * @param secret
     * @param accessToken
     * @return
     */
    public Map<String, Object> buildIsvSysParam(String appKey, String secret, String accessToken){
        Map<String, Object> sysParam = new HashMap<>();
        sysParam.put("source", appKey);
        sysParam.put("version", 3);
        sysParam.put("access_token", accessToken);
        sysParam.put("encrypt", "");
        sysParam.put("secret", secret);
        return sysParam;
    }

    @Override
    public <T> T postToChannel(String channelPoiCode, String channelOnlinePoiCode, ChannelPostInter postUrlEnum, Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest) {
        replaceStoreId(bizParamMap, channelOnlinePoiCode);

        Map<String, Object> postParams = generatePostParams(postUrlEnum.getUrl(), sysParam, bizParamMap);

        String uuid = channelOnlinePoiCode;
        if (postUrlEnum.getRateLimitLevel() == RateLimitLevelEnum.APP) {
                Object obj = sysParam.get("source");
                if (obj != null)
                    uuid = String.valueOf(obj);
            }

        // 限频
        if (postUrlEnum.requestLimited()) {
            long waitTime = clusterRateLimiter.tryAcquire(postUrlEnum.generateLimitedResourceKey(), uuid, baseRequest.isAsyncInvoke());
            if (waitTime != 0) {
                if (RHINO_UPTIMATE_SET.contains(postUrlEnum)) {
                    log.info("ElmChannelGateService.postToChannel appId:{} url:{} 未获取到令牌 抛出异常", uuid, postUrlEnum.getUrl());
                    throw new InvokeChannelTooMuchException(waitTime);
                } else {
                    log.info("ElmChannelGateService.postToChannel appId:{} url:{} 未获取到令牌 直接返回null", uuid, postUrlEnum.getUrl());
                    if (MccConfigUtil.getChannelLimiterErrorPoster().contains(postUrlEnum)) {
                        return (T) ChannelResponseDTO.defaultLimitErrorResponse();
                    }
                    return null;
                }
            }
        }

        // 调用
        ChannelResponseDTO dto = dealPostResult(postRequest(postUrlEnum, postParams, baseRequest), postUrlEnum.getResultClass());
        channelResponseMetric(ChannelTypeEnum.ELEM, postUrlEnum, baseRequest, dto, ChannelResponseDTO::getResponseError);
        if(Objects.isNull(dto)){
            log.warn("ElmChannelGateService.postToChannel 执行post请求结果转换ChannelResponseDTO为null 直接返回null");
            return null;
        }

        log.info("【渠道原始日志】, operate:{}, url:{}, success:{}, isErp:{}, request:{}, postParams:{}, result:{}",
                postUrlEnum.getUrlShortName(),
                postUrlEnum.getUrl(),
                parseSuccess(dto),
                baseRequest.isErpTenant(),
                baseRequest,
                postParams,
                JacksonUtils.toJson(dto));

        // 限频错误重试
        if (ElmResultCodeEnum.ERROR_20501.getCodeStr().equals(dto.getErrno())) {
            log.info("进入阈值错误重试,postParams:{}", postParams);
            if (RHINO_UPTIMATE_SET.contains(postUrlEnum)){
                log.info("ElmChannelGateService.postToChannel ele 触发限频，价格相关接口抛出异常，客户端重试");
                throw new InvokeChannelTooMuchException(0);
            }else {
                log.info("ElmChannelGateService.postToChannel ele 触发限频，其他接口服务端立即重试");
                try {
                    Thread.sleep(MccConfigUtil.getELMRetryDelayTime(postUrlEnum.getUrl()));
                    dto = dealPostResult(postRequest(postUrlEnum, postParams, baseRequest), postUrlEnum.getResultClass());
                } catch (InterruptedException e) {
                    log.error("ElmChannelGateService.postToChannel超过阈值重试线程等待处理异常,postParams:{} ", postParams, e);
                    Thread.currentThread().interrupt();
                }
            }

        }

        return (T) dto;
    }

    private boolean parseSuccess(ChannelResponseDTO dto){
        if (dto == null || dto.getBody() == null || dto.getBody().getErrno() == null) {
            return false;
        }

        return ChannelErrorCode.ELM.SUCCESS_INTEGER.equals(dto.getBody().getErrno());
    }

    private void replaceStoreId(Map<String, Object> bizParamMap, String outStoreId) {
        if (StringUtils.isNotBlank(outStoreId)) {
            bizParamMap.put(Constant.FIELD_NAME_STOREID_ELM, outStoreId);
        }
    }

    private ChannelResponseDTO dealPostResult(String resultJson, Class resultClass) {
        ChannelResponseDTO dto = ChannelResponseDTO.parseResult(resultJson, resultClass);

        return dto;
    }

    private Map<String, Object> generatePostParams(String method, Map<String, Object> sysParam, Map<String, Object> bizParam) {
        sysParam.put("body", new Gson().toJson(bizParam));
        sysParam.put("cmd", method);
        sysParam.put("timestamp", String.valueOf(DateUtils.unixTime()));
        sysParam.put("ticket", ElmSignUtils.getTicket());
        String sign = ElmSignUtils.getSign(sysParam);
        sysParam.put("sign", sign);
        sysParam.remove("secret");

        return sysParam;
    }

    @Override
    public String getPostUrl(ChannelPostInter postUrlEnum) {
        return baseUrl;
    }

    //仅给饿了么获取令牌渠道使用
    public Map<String, Object> sendPost(Map<String, Object> systemParam) {
        //参数校验
        validateElmSendPost(systemParam);

        return postUrl(getAccessTokenUrl, null,systemParam);
    }

    //仅给饿了么获取令牌渠道使用
    public Map<String, Object> oauth2Token(Map<String, Object> systemParam) {
        //参数校验
        validateElmSendPost(systemParam);

        return postUrl(getOauth2TokenUrl, null,systemParam);
    }

    private void validateElmSendPost(Map<String, Object> systemParm) {
        Preconditions.checkArgument(StringUtils.isNotBlank(systemParm.toString()), "sendPost url is blank");
        Preconditions.checkArgument(systemParm.containsKey("client_id") && systemParm.get("client_id") != null, "系统参数client_id不存在");
        Preconditions.checkArgument(systemParm.containsKey("client_secret") && systemParm.get("client_secret") != null, "系统参数client_secret不存在");
//        Preconditions.checkArgument(systemParm.containsKey("code") && systemParm.get("code") != null, "系统参数code不存在");
    }

    /**
     * 获取渠道虚拟系统参数
     */
    @Override
    protected Map<String, Object> getChannelVirtualSysParam(Long tenantId, Integer channelId) {
        return getChannelVirtualSysParamByLion();
    }

    /**
     * 通过Lion获取虚拟门店参数
     * @param baseRequest
     * @return
     */
    @Override
    protected ChannelStoreDO getChannelVirtualPoiParamByLion(BaseRequest baseRequest) {
        ChannelStoreDO storeParam = new ChannelStoreDO();
        int channelId = ChannelTypeEnum.ELEM.getCode();
        long tenantId = baseRequest.getTenantId();

        Map<String, String> poiMap = MccConfigUtil.getChannelVirtualPoiParam(channelId);

        // 参数为空校验
        validatePoiMapEmpty(poiMap);

        String channelPoiCode = poiMap.get(PoiParam.CHANNEL_POI_CODE);
        String channelOnlinePoiCode = poiMap.get(PoiParam.CHANNEL_ONLINE_POI_CODE);

        // 渠道门店可用性校验
        validatePoiAvailable(tenantId, channelId, channelOnlinePoiCode, null);

        storeParam.setChannelId(channelId);
        storeParam.setTenantId(tenantId);
        storeParam.setChannelPoiCode(channelPoiCode);
        storeParam.setChannelOnlinePoiCode(channelOnlinePoiCode);
        return storeParam;
    }

    @Override
    protected Map<String, Object> getChannelVirtualSysParamByLion() {
        int channelId = ChannelTypeEnum.ELEM.getCode();

        Map<String, Object> sysParamMap = MccConfigUtil.getChannelVirtualSysParam(channelId);

        // 校验参数
        validateSysParamMap(sysParamMap);

        // 填充其他字段
        fillSysParamMap(sysParamMap);

        return sysParamMap;
    }

    private void fillSysParamMap(Map<String, Object> sysParamMap) {
        if (!sysParamMap.containsKey(ElmSysParam.ENCRYPT)) {
            sysParamMap.put(ElmSysParam.ENCRYPT, "");
        }

        if (!sysParamMap.containsKey(ElmSysParam.VERSION)) {
            sysParamMap.put(ElmSysParam.VERSION, 3);
        }
    }

    private void validateSysParamMap(Map<String, Object> sysParamMap) {
        if (Objects.isNull(sysParamMap)) {
            throw new BizException("未配置饿了么渠道虚拟系统参数", ChannelVirtualConfigErrorCodeEnum.CHANNEL_VIRTUAL_CONFIG_PARAM_LACK.getValue());
        }

        if (!sysParamMap.containsKey(ElmSysParam.SOURCE)) {
            throw new BizException(String.format("饿了么渠道虚拟系统参数%s未配置", ElmSysParam.SOURCE), ChannelVirtualConfigErrorCodeEnum.CHANNEL_VIRTUAL_CONFIG_PARAM_LACK.getValue());
        }

        if (!sysParamMap.containsKey(ElmSysParam.SECRET)) {
            throw new BizException(String.format("饿了么渠道虚拟系统参数%s未配置", ElmSysParam.SECRET), ChannelVirtualConfigErrorCodeEnum.CHANNEL_VIRTUAL_CONFIG_PARAM_LACK.getValue());
        }

        if (!sysParamMap.containsKey(ElmSysParam.TICKET)) {
            throw new BizException(String.format("饿了么渠道虚拟系统参数%s未配置", ElmSysParam.TICKET), ChannelVirtualConfigErrorCodeEnum.CHANNEL_VIRTUAL_CONFIG_PARAM_LACK.getValue());
        }
    }

    private void validatePoiAvailable(long tenantId, int channelId, String channelOnlinePoiCode, String divideScene) {
        ChannelPoiIdRequest request = new ChannelPoiIdRequest();
        request.setTenantId(tenantId);
        request.setStoreId(PoiParam.DEFAULT_STORE_ID);
        request.setChannelId(channelId);
        // 这里将channelOnlinePoiCode映射为channelPoiCode，
        request.setChannelPoiCode(channelOnlinePoiCode);

        ChannelPoiStatusResponse response = elmChannelVirtualConfigService.getPoiStatusByVirtualConfig(request, divideScene);
        ResultStatus status = response.getStatus();
        if (!Objects.equals(status.getCode(), ResultCode.SUCCESS.getCode())) {
            notifyUnavailablePoiParamOperators();
            throw new BizException(Strings.of("饿了么获取渠道类目测试门店失效{}", status.getMsg()), ChannelVirtualConfigErrorCodeEnum.CHANNEL_VIRTUAL_CONFIG_UNAVAILABLE.getValue());
        }
    }

    private void notifyUnavailablePoiParamOperators() {
        String[] misList = MccConfigUtil.getChannelVirtualConfigUnavailableOperators();
        XMSender.sendText("饿了么获取渠道类目测试门店失效，请及时更换饿了么测试门店", misList);
    }

    private void validatePoiMapEmpty(Map<String, String> poiMap) {
        if (Objects.isNull(poiMap)) {
            throw new BizException("未配置饿了么虚拟门店参数", ChannelVirtualConfigErrorCodeEnum.CHANNEL_VIRTUAL_CONFIG_PARAM_LACK.getValue());
        }

        if (!poiMap.containsKey(PoiParam.CHANNEL_POI_CODE)) {
            throw new BizException(String.format("饿了么虚拟门店参数%s未配置", PoiParam.CHANNEL_POI_CODE), ChannelVirtualConfigErrorCodeEnum.CHANNEL_VIRTUAL_CONFIG_PARAM_LACK.getValue());
        }

        if (!poiMap.containsKey(PoiParam.CHANNEL_ONLINE_POI_CODE)) {
            throw new BizException(String.format("饿了么虚拟门店参数%s未配置", PoiParam.CHANNEL_ONLINE_POI_CODE), ChannelVirtualConfigErrorCodeEnum.CHANNEL_VIRTUAL_CONFIG_PARAM_LACK.getValue());
        }
    }

    /**
     * 通过隔离的虚拟系统配置信息发送请求
     *
     * @param postUrlEnum 请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam    请求业务参数
     * @return
     */
    public <T> T sendPostAppMapDtoByDivideScene(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam, String divideScene) {

        // 参数校验
        Preconditions.checkArgument(StringUtils.isNotBlank(postUrlEnum.getUrl()), "sendPost url is blank");
        Preconditions.checkNotNull(postUrlEnum.getResultClass(), "sendPost ResultClass is null");
        Preconditions.checkNotNull(bizParam, "sendPost bizParam is blank");

        // 业务参数实体转换为Map
        Map<String, Object> bizParamMap = ConverterUtils.convertToMap(bizParam, baseRequest.getChannelId());

        // 通过虚拟配置返回渠道门店和系统参数
        ChannelStoreDO poi = getChannelVirtualPoiByDivideScene(baseRequest, divideScene);
        Map<String, Object> appChannelSysParams = getChannelVirtualSysParamByDivideScene(divideScene);

        // 构造参数
        Map<Long, T> res = Maps.newHashMap();
        Map<String, Object> finalBizParamMap = bizParamMap;
        long tenantId = baseRequest.getTenantId();
        int channelId = baseRequest.getChannelId();
        baseRequest.getStoreIdList().forEach(storeId -> {
            String channelOnlinePoiCode = poi.getChannelOnlinePoiCode();
            BaseRequest baseRequest1 = new BaseRequest();
            baseRequest1.setChannelId(channelId);
            baseRequest1.setTenantId(tenantId);
            baseRequest1.setStoreIdList(Lists.newArrayList(storeId));
            T object = postToChannel(poi.getChannelPoiCode(), channelOnlinePoiCode, postUrlEnum, appChannelSysParams, finalBizParamMap, baseRequest);
            res.put(storeId, object);
        });

        return (T) res;
    }

    /**
     * 通过隔离的虚拟系统配置信息向渠道发送请求
     *
     * @param url      请求URL
     * @param bizParam 请求业务参数
     * @return
     */
    public Map<String, Object> sendPostByDivideScene(String url, String method, BaseRequest baseRequest, Object bizParam, String divideScene) {
        // 获取系统参数
        Map<String, Object> sysParam = getChannelVirtualSysParamByDivideScene(divideScene);

        // 请求
        return sendPost(url, method, baseRequest, bizParam, sysParam);
    }

    /**
     * 通过Lion获取隔离的虚拟门店参数
     * @param baseRequest
     * @return
     */
    protected ChannelStoreDO getChannelVirtualPoiByDivideScene(BaseRequest baseRequest, String divideScene) {
        ChannelStoreDO storeParam = new ChannelStoreDO();
        long tenantId = baseRequest.getTenantId();

        Map<String, String> poiMap = MccConfigUtil.getChannelVirtualPoiByDivideScene(divideScene);

        // 参数为空校验
        validatePoiMapEmpty(poiMap);

        String channelPoiCode = poiMap.get(PoiParam.CHANNEL_POI_CODE);
        String channelOnlinePoiCode = poiMap.get(PoiParam.CHANNEL_ONLINE_POI_CODE);

        // 渠道门店可用性校验
        validatePoiAvailable(tenantId, EnhanceChannelType.ELEM.getChannelId(), channelOnlinePoiCode, divideScene);

        storeParam.setChannelId(EnhanceChannelType.ELEM.getChannelId());
        storeParam.setTenantId(tenantId);
        storeParam.setChannelPoiCode(channelPoiCode);
        storeParam.setChannelOnlinePoiCode(channelOnlinePoiCode);
        return storeParam;
    }

    protected Map<String, Object> getChannelVirtualSysParamByDivideScene(String divideScene) {

        Map<String, Object> sysParamMap = MccConfigUtil.getChannelVirtualSysParamByDivideScene(divideScene);

        // 校验参数
        validateSysParamMap(sysParamMap);

        // 填充其他字段
        fillSysParamMap(sysParamMap);

        return sysParamMap;
    }
}
