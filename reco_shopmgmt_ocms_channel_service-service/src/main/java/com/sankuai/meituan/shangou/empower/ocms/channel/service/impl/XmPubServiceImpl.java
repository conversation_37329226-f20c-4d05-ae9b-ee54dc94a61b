package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.XmPubService;
import com.sankuai.xm.pubapi.thrift.Constants;
import com.sankuai.xm.pubapi.thrift.GroupPushMessageWithUids;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/7/19
 */
@Slf4j
@Service
public class XmPubServiceImpl implements XmPubService {

    private static final int DEFAULT_FONT_SIZE = 12;

    @Resource
    private PusherInfo pusherInfo;

    @Resource
    private PushMessageServiceI.Iface pushMessageService;

    @Data
    public static class XBody {
        //是否加粗
        private boolean bold;

        //字体类型
        private String fontName;
        //字体大小
        private int fontSize;
        //字体内容
        private String text;

        private short cipherType;
    }

    @Override
    public void pushMessageToGroup(String messageBody, long groupId) {
        try {
            GroupPushMessageWithUids groupPushMessageWithUids = new GroupPushMessageWithUids();
            groupPushMessageWithUids.setCts(System.currentTimeMillis());
            groupPushMessageWithUids.setMessageType(Constants.TEXT);
            XBody xBody = new XBody();
            xBody.setFontSize(DEFAULT_FONT_SIZE);
            xBody.setText(messageBody);
            groupPushMessageWithUids.setMessageBodyJson(JacksonUtils.toJson(xBody));
            groupPushMessageWithUids.setGid(groupId);
            groupPushMessageWithUids.setPusherInfo(pusherInfo);
            groupPushMessageWithUids.setUids(Lists.newArrayList(-1L));
            String s = pushMessageService.pushToRoomWithUids(groupPushMessageWithUids);
            if (Objects.isNull(s)) {
                log.info("pushRoomMessage messageId is null pushInfo:{} groupId:{} msg:{}", pusherInfo, groupId, messageBody);
            }
            else {
                log.info("pushRoomMessage messageId :{},pushInfo:{} groupId:{}  msg:{}", s , pusherInfo, groupId, messageBody);
            }
        }
        catch (Exception ex) {
            log.error("pushRoomMessage  Exception, groupId:{} msg:{}", groupId, messageBody, ex);
        }

    }
}
