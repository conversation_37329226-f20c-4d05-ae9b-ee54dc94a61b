package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhBatchUpdateItemStatusParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhBatchUpdateSaleStatusParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhUpdateItemStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhUpdateSaleStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhBatchUpdateItemStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhBatchUpdateSaleStatusRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QnhConverterUtils {

    public static QnhBatchUpdateSaleStatusParam convertBatchUpdateStatusParam(QnhBatchUpdateSaleStatusRequest request,
                                                                              Map<Long, String> qnhRegionCodeMap) {
        QnhBatchUpdateSaleStatusParam statusParam = new QnhBatchUpdateSaleStatusParam();
        List<QnhBatchUpdateSaleStatusParam.UpdateSaleStatusItem> items = Lists.newArrayList();
        statusParam.setItem(items);
        for (QnhUpdateSaleStatusDTO param : request.getUpdateParams()) {
            QnhBatchUpdateSaleStatusParam.UpdateSaleStatusItem updateSaleStatusItem = new QnhBatchUpdateSaleStatusParam
                    .UpdateSaleStatusItem();
            updateSaleStatusItem.setRegion_code(qnhRegionCodeMap.get(param.getStoreId()));
            updateSaleStatusItem.setQnhid(param.getQnhId());
            updateSaleStatusItem.setItem_code(null);
            updateSaleStatusItem.setBarcode(param.getBarcode());
            updateSaleStatusItem.setSale_status(param.getSaleStatus());
            items.add(updateSaleStatusItem);
        }

        return statusParam;
    }

    public static QnhBatchUpdateItemStatusParam convertBatchUpdateStatusParam(QnhBatchUpdateItemStatusRequest request,
                                                                              Map<Long, String> qnhRegionCodeMap) {
        QnhBatchUpdateItemStatusParam statusParam = new QnhBatchUpdateItemStatusParam();
        List<QnhBatchUpdateItemStatusParam.UpdateItemStatusItem> items = Lists.newArrayList();
        statusParam.setItem(items);
        for (QnhUpdateItemStatusDTO param : request.getUpdateParams()) {
            QnhBatchUpdateItemStatusParam.UpdateItemStatusItem updateSaleStatusItem = new QnhBatchUpdateItemStatusParam
                    .UpdateItemStatusItem();
            updateSaleStatusItem.setRegion_code(qnhRegionCodeMap.get(param.getStoreId()));
            updateSaleStatusItem.setQnhid(param.getQnhId());
            updateSaleStatusItem.setItem_code(null);
            updateSaleStatusItem.setBarcode(param.getBarcode());
            updateSaleStatusItem.setStatus(param.getItemStatus());
            items.add(updateSaleStatusItem);
        }

        return statusParam;
    }

}
