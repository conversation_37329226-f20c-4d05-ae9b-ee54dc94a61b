package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.common.enums.ReturnGoodsStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.RefundGoodsFreightDutyEnum;
import com.meituan.shangou.saas.order.platform.enums.RefundGoodsFreightNewTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.RefundGoodsWayEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ChannelConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhChannelOrderSettlementDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MTFeeDetailEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MTSettleStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MtActivityTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpuStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.RefundProductDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ShippingTime;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordinate;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementSkuBenefitDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementSkuShippingDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.SettlementFeeDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.PRE_REFUND_GOODS_SHIPPING_FEE;
import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.USER_REFUND_GOODS_SHIPPING_FEE;

/**
 * <AUTHOR>
 * @date 2019-06-25
 * @desc 美团
 */
@Slf4j
public class MtConverterUtil {

    private static final String MT_BILL_TIME_CHARS = "~";
    private static final int MT_BILL_TIME_CHARS_LENGTH = 2;
    private static final String MT_RECIPIENT_ADDRESS_SPLIT_MARK = "@#";
    //答复类型：0-未处理；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超过3小时自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
    private static final String RES_TYPE = "resType";


    public static List<SettlementFeeDetail> convertSettlementFeeList(MTOrderSettlementListResult mtOrderSettlementListResult) {
        //处理
        List<SettlementFeeDetail> settlementFeeDetailList = Lists.newArrayList();
        for (MTFeeDetailEnum value : MTFeeDetailEnum.values()) {
            if (StringUtils.isBlank(value.getCode())) {
                continue;
            }
            String fieldValue = ConverterUtils.getFieldValue(mtOrderSettlementListResult, value.getCode());
            if (fieldValue != null) {
                settlementFeeDetailList.add(buildSettlementFeeDetail(fieldValue, value, mtOrderSettlementListResult.isRefund()));
            }
        }
        return settlementFeeDetailList;
    }

    private static SettlementFeeDetail buildSettlementFeeDetail(String fieldValue, MTFeeDetailEnum detailEnum, boolean isRefund) {
        double feeValue = Double.parseDouble(fieldValue);
        SettlementFeeDetail settlementFeeDetail = new SettlementFeeDetail();
        settlementFeeDetail.setFeeKey(detailEnum.getCode());
        settlementFeeDetail.setFeeDesc(detailEnum.getDescription());
        settlementFeeDetail.setFeeValue(BigDecimal.valueOf(feeValue)
                .multiply(BigDecimal.valueOf(detailEnum.getConvertPoint()))
                .setScale(0, RoundingMode.HALF_UP).longValue());
        if (isRefund && detailEnum.getSymbol() == 0) {
            settlementFeeDetail.setFeeValue(-settlementFeeDetail.getFeeValue());
        }
        return settlementFeeDetail;
    }

    public static List<SettlementFeeDetail> convertQnhSettlementFeeDetail(QnhChannelOrderSettlementDetail detail) {
        List<SettlementFeeDetail> details = new ArrayList<>();
        for (MTFeeDetailEnum value : MTFeeDetailEnum.values()) {
            if (StringUtils.isBlank(value.getQnhField())) {
                continue;
            }
            String qnhFieldValue = ConverterUtils.getFieldValue(detail, value.getQnhField());
            if (qnhFieldValue != null) {
                details.add(buildSettlementFeeDetail(qnhFieldValue, value, detail.isRefund()));
            }
        }
        return details;
    }


    public static MTSettleStatusEnum convertMtSettleStatus(int mtSettleStatus) {
        return MTSettleStatusEnum.enumOf(mtSettleStatus);

    }

    public static long convertSettlementFinishDate(String mtBillTime, long settlementDate) {
        if (StringUtils.isNotBlank(mtBillTime)) {
            String[] split = mtBillTime.split(MT_BILL_TIME_CHARS);
            if (split.length == MT_BILL_TIME_CHARS_LENGTH) {
                return DateUtils.parse(split[1], "yyyy-MM-dd").getTime();
            }
        }

        return settlementDate;
    }

    public static long orderCreateTime(ChannelOrderDetail channelOrderDetail) {
        if (channelOrderDetail != null) {
            if (channelOrderDetail.getCtime() != null) {
                return TimeUnit.SECONDS.toMillis(channelOrderDetail.getCtime());
            } else if (channelOrderDetail.getUtime() != null) {
                return TimeUnit.SECONDS.toMillis(channelOrderDetail.getUtime());
            }
        }
        return System.currentTimeMillis();
    }

    /**
     * 退款类型：1-全额退款；2-部分退款；3-退差价; 5-自定义金额退
     **/
    public static int convertRefundType(int refund_type) {
        switch (refund_type) {
            case 1:
                return RefundTypeEnum.ALL.getValue();
            case 2:
                return RefundTypeEnum.PART.getValue();
            case 3:
                return RefundTypeEnum.WEIGHT.getValue();
            case 5:
                return RefundTypeEnum.AMOUNT.getValue();
            default:
                return RefundTypeEnum.PART.getValue();

        }
    }
    /**
     * 补充扩展字段。
     **/
    public static Map<String, String> convertExtend(MtOrderAfsApplyDTO mtOrderAfsApplyDTO) {
        Map<String, String> extend = Maps.newHashMap();
        if (mtOrderAfsApplyDTO != null) {
            extend.put(RES_TYPE, String.valueOf(mtOrderAfsApplyDTO.getRes_type()));
        }

//        //todo 自测用后面删除
//        if (StringUtils.isBlank(mtOrderAfsApplyDTO.getUser_refund_goods_shipping_fee())) {
//            mtOrderAfsApplyDTO.setUser_refund_goods_shipping_fee("1");
//        }

        if (StringUtils.isNotBlank(mtOrderAfsApplyDTO.getUser_refund_goods_shipping_fee())) {
            //转成分
            int preReturnFreight = new BigDecimal(mtOrderAfsApplyDTO.getUser_refund_goods_shipping_fee()).multiply(new BigDecimal(100)).intValue();
            extend.put(PRE_REFUND_GOODS_SHIPPING_FEE, Integer.toString(preReturnFreight));
            if (mtOrderAfsApplyDTO.getStatus() > 20) {
                extend.put(USER_REFUND_GOODS_SHIPPING_FEE, Integer.toString(preReturnFreight));

            }
        }

        return extend;
    }

    public static Integer convertReturnFreightDuty(MtOrderAfsApplyDTO mtOrderAfsApplyDTO) {
        int returnGoodsWay = mtOrderAfsApplyDTO.getReturn_goods_way();
        if (returnGoodsWay == 3 || returnGoodsWay == 4){
            int userRefundGoodsShippingFee = MoneyUtils.yuanToFenOrDefault(mtOrderAfsApplyDTO.getUser_refund_goods_shipping_fee());
            if (userRefundGoodsShippingFee > 0){
                return RefundGoodsFreightDutyEnum.MERCHANT.getValue();
            }
            return RefundGoodsFreightDutyEnum.USER.getValue();
        }
        return RefundGoodsFreightDutyEnum.UN_KNOW.getValue();
    }

    public static Integer convertReturnFreightNewType(MtOrderAfsApplyDTO mtOrderAfsApplyDTO) {
        try {
            int returnGoodsWay = mtOrderAfsApplyDTO.getReturn_goods_way();
            //  如果退货方式不是3或4，则返回未知运费类型,页面不做展示
            if(returnGoodsWay != 3 && returnGoodsWay != 4) {
                return RefundGoodsFreightNewTypeEnum.UN_KNOW.getValue();
            }
            Integer userRefundGoodsShippingFeeReason = mtOrderAfsApplyDTO.getUser_refund_goods_shipping_fee_reason();
            if(Objects.nonNull(userRefundGoodsShippingFeeReason)){
                return userRefundGoodsShippingFeeReason;
            }
            int userRefundGoodsShippingFee = MoneyUtils.yuanToFenOrDefault(mtOrderAfsApplyDTO.getUser_refund_goods_shipping_fee());
            if (userRefundGoodsShippingFee > 0){
                return RefundGoodsFreightNewTypeEnum.MERCHANT.getValue();
            }
            return RefundGoodsFreightNewTypeEnum.USER.getValue();
        } catch (Exception e) {
            log.error("convertReturnFreightNewType is error orderId: {}, e= ", mtOrderAfsApplyDTO.getOrder_id(), e);
        }
        return RefundGoodsFreightNewTypeEnum.UN_KNOW.getValue();
    }

    public static Boolean convertOnlyRefund(MtOrderAfsApplyDTO mtOrderAfsApplyDTO) {
        return mtOrderAfsApplyDTO.getService_type() == 2 && mtOrderAfsApplyDTO.getStatus() == 21 && mtOrderAfsApplyDTO.getFinal_review_type() == 1;
    }

    public static Integer convertReturnGoodsWay(int returnGoodsWay) {
        if (returnGoodsWay == 1){
            return RefundGoodsWayEnum.USER_RETURN.getValue();
        }
        if (returnGoodsWay == 2){
            return RefundGoodsWayEnum.MERCHANT_RETURN.getValue();
        }
        if (returnGoodsWay == 3 || returnGoodsWay == 4){
            return RefundGoodsWayEnum.USER_CALL_RIDER.getValue();
        }
        return RefundGoodsWayEnum.UN_KNOW.getValue();
    }
    /**
     * 退款类型：1-全额退款；2-部分退款；3-退差价。
     **/
    public static int convertCommissionValue(ChannelRefundPartialEstimateCharge refundPartialEstimateCharge) {
        if (refundPartialEstimateCharge !=null && StringUtils.isNotBlank(refundPartialEstimateCharge.getPlatform_charge_fee())){
            return MoneyUtils.yuanToFen(Double.parseDouble(refundPartialEstimateCharge.getPlatform_charge_fee()));
        }
        return 0;
    }

    public static long convertTime(int time) {
        return time * 1000L;
    }

    /**
     * 解析是否为商家自配送
     *
     * @param logisticsCode 订单配送方式
     * @return
     */
    public static int isSelfDelivery(String logisticsCode) {
        //自配送 1003实际为自配+众包，需要商家自己通过渠道配置保证只发自配
        if ("0000".equals(logisticsCode) || "1003".equals(logisticsCode)) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 解析原始配送类型
     *
     * @param logisticsCode 订单配送方式
     * @return
     */
    public static int convertDeliveryType(String logisticsCode) {
        if (StringUtils.isBlank(logisticsCode)) {
            return DistributeTypeEnum.UN_KNOWN.getValue();
        }

        switch (logisticsCode) {
            case "0000":
                return DistributeTypeEnum.SELF_DELIVERY.getValue();
            case "1001":
            case "1002":
            case "1004":
                return DistributeTypeEnum.ZHUAN_SONG.getValue();
            case "2002":
                return DistributeTypeEnum.KUAI_SONG.getValue();
            case "3001":
                return DistributeTypeEnum.COMBO.getValue();
            case "1003":
                return DistributeTypeEnum.ZONG_BAO.getValue();
            case "4001":
            case "4011":
            case "4012":
            case "4015":
                return DistributeTypeEnum.QIKE.getValue();
            default:
                return DistributeTypeEnum.UN_KNOWN.getValue();
        }
    }


    /**
     * 解析获取用户隐私号
     *
     * @param channelOrderDetail
     * @return
     */
    public static String getUserPrivacyPhone(ChannelOrderDetail channelOrderDetail) {
        /**
         *  判断receipt_phone是否是真实号码
         *  1. 号码是11位数字
         *  2. 备注信息里面没有形如"手机号 158****9248"字段
         */
        String recipientPhone = channelOrderDetail.getRecipient_phone();
        boolean recipientPhoneIsReal = PhoneNumberUtils.isValidElevenNumberMobileNumber(recipientPhone);
        String cautionRemarkPhone = getCautionRemarkPhone(channelOrderDetail);
        if (recipientPhoneIsReal && StringUtils.isBlank(cautionRemarkPhone)) {
            // receipt_phone是真实号码，使用该号码作为隐私号返回
            return PhoneNumberUtils.transferToPrivacyPhone(recipientPhone);
        }
        // 返回订单备注中的隐私号码
        return cautionRemarkPhone;
    }

    /**
     * 获取订单备注中的手机虚拟号
     *
     * @param channelOrderDetail 【如遇缺货】： 缺货时电话与我沟通 收餐人隐私号 13049479512_4121，手机号 158****9248 到店自取
     * @return 13049479512_4121
     */
    private static String getCautionRemarkPhone(ChannelOrderDetail channelOrderDetail) {
        String privacyPhoneRemarkPattern = ".+手机号 \\d{3}\\*{4}\\d{4}.*";
        String caution = channelOrderDetail.getCaution();
        if (StringUtils.isBlank(caution)){
            return "";
        }
        if (Pattern.matches(privacyPhoneRemarkPattern, caution)) {
            int index = caution.indexOf("手机号 ");
            return caution.substring(index + 4, index + 15);
        }
        // 如果用户没有开启隐私号模式，订单备注信息中没有隐私号，返回空字符串
        return "";
    }

    public static long getLatestCloseTime(String shippingTime) {
        if (StringUtils.isBlank(shippingTime)) {
            return 0L;
        }
        String[] shippingTimeList = shippingTime.split(";", Calendar.DAY_OF_WEEK);
        if (shippingTimeList.length == Calendar.DAY_OF_WEEK) {
            int week = todayForWeek();
            shippingTime = shippingTimeList[week];
        }
        if (StringUtils.isBlank(shippingTime)) {
            return 0L;
        }
        String[] todayShippingTimes = shippingTime.split(",");
        String[] todayShippingTimeList = todayShippingTimes[todayShippingTimes.length - 1].split("-");
        if (todayShippingTimeList.length != 2) {
            return 0L;
        }
        return ConverterUtils.getMillsByHour(todayShippingTimeList[1]);
    }


    public static List<ShippingTime> convertShippingTime(String shippingTime) {
        if (StringUtils.isEmpty(shippingTime)) {
            return Lists.newArrayList();
        }

        return Arrays.asList(StringUtils.split(shippingTime, ";"))
                .stream().map(s -> {
                    String[] times = StringUtils.split(s, "-");
                    if (times.length < 2) {
                        return new ShippingTime();
                    }
                    return new ShippingTime(times[0], times[1]);
                }).collect(Collectors.toList());
    }


    /**
     * 判断当前日期是星期几<br>
     * <br>
     *
     * @return dayForWeek 判断结果<br>
     * @Exception 发生异常<br>
     */
    private static int todayForWeek() {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        int dayForWeek = 0;
        if (c.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            dayForWeek = 6;
        } else {
            dayForWeek = c.get(Calendar.DAY_OF_WEEK) - 2;
        }
        return dayForWeek;
    }

    /**
     * 可售时间转换成美团开放平台需要的map格式
     *
     * @param json
     * @return
     */
    public static Map<String, String> convertStringToMapForAvailableTimes(String json) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(json);
        Map<String, String> currentDataMap = (Map) jsonObject;
        return currentDataMap;
    }


    /**
     * 开放平台返回的格式有问题，沟通结果为先自己看能不能处理，去到最外层的双引号
     * {"monday":"\"04:00-05:00\"","tuesday":"\"04:00-05:00\"","wednesday":"\"04:00-05:00\"","thursday":"\"04:00-05:00\"","friday":"\"04:00-05:00\"","saturday":"\"04:00-05:00\"","sunday":"\"04:00-05:00\""}
     *
     * @param map
     * @return
     */
    public static String convertMapToStringForAvailableTimes(Map<String, String> map) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String str = entry.getValue().replaceAll("\\\"", "");
            entry.setValue(str);
        }
        return JSONObject.toJSONString(map);
    }


    /**
     * 对单分类、多分类做兼容逻辑处理
     * 优先用code，没有code用name
     * 用于美团渠道多分类编码列表的生成，如果多分类列表中只有一个分类，作为单分类处理
     * 将末级店内分类列表转换成需要的字符串
     * 美团开放平台的限制为：category_code_list与category_name、category_code、category_name_list字段必须且只能填写一个
     *
     * @param categoryList
     * @return
     */
    public static String convert2MTCategory_code_list(List<ChannelLeafStoreCategory> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return null;
        }
        if (categoryList.size() == 1) {
            return null;
        }
        List<String> codes = categoryList.stream().map(ChannelLeafStoreCategory::getCategoryCode).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(codes)) {
            return null;
        }
        String result = StringUtils.join(codes, ",");
        result = String.format("\"%s\"", StringUtils.join(result.split(","), "\",\""));
        return "[" + result + "]";
    }

    /**
     * 对单分类、多分类做兼容逻辑处理
     * 优先用code，没有code用name
     * 用于美团渠道多分类名称列表的生成,如果多分类列表中只有一个分类，作为单分类处理
     * 将末级店内分类列表转换成需要的字符串
     * 美团开放平台的限制为：category_code_list与category_name、category_code、category_name_list字段必须且只能填写一个
     *
     * @param categoryList
     * @return
     */
    public static String convert2MTCategory_name_list(List<ChannelLeafStoreCategory> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return null;
        }
        if (categoryList.size() == 1) {
            return null;
        }
        List<String> codes = categoryList.stream().map(ChannelLeafStoreCategory::getCategoryCode).filter(Objects::nonNull).collect(Collectors.toList());
        //说明有category_code_list，category_name_list已经不能再赋值了
        if (CollectionUtils.isNotEmpty(codes)) {
            return null;
        }
        List<String> names = categoryList.stream().map(ChannelLeafStoreCategory::getCategoryName).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(names)) {
            return null;
        }
        String result = StringUtils.join(names, ",");
        result = String.format("\"%s\"", StringUtils.join(result.split(","), "\",\""));
        return "[" + result + "]";
    }

    /**
     * 对单分类、多分类做兼容逻辑处理，优先用code，没有code用name
     *
     * @param skuInfoDTO
     * @return
     */
    public static String convert2MTCategoryName(SkuInfoDTO skuInfoDTO) {
        if (CollectionUtils.isEmpty(skuInfoDTO.getLeafStoreCategoryList())) {
            return skuInfoDTO.isSetChannelFrontCategory() ? null : skuInfoDTO.getFrontCategoryName();
        }
        if (skuInfoDTO.getLeafStoreCategoryList().size() == 1) {
            if (StringUtils.isEmpty(skuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryCode())) {
                return skuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryName();
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 对单分类、多分类做兼容逻辑处理，优先用code，没有code用name
     *
     * @param skuInfoDTO
     * @return
     */
    public static String convert2MTCategoryCode(SkuInfoDTO skuInfoDTO) {
        if (CollectionUtils.isEmpty(skuInfoDTO.getLeafStoreCategoryList())) {
            return skuInfoDTO.getChannelFrontCategory();
        }
        if (skuInfoDTO.getLeafStoreCategoryList().size() == 1) {
            return skuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryCode();
        } else {
            return null;
        }
    }

    // 创建SPU映射转换
    public static String convertSpec(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        // 传空字符串置空
        if (Objects.nonNull(skuInSpuInfoDTO.getSpec()) && Strings.isBlank(skuInSpuInfoDTO.getSpec())) {
            return "EMPTY_VALUE";
        }
        return skuInSpuInfoDTO.getSpec();
    }

    public static String convertPrice(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        // 更新商品不更新价格
        if (!skuInSpuInfoDTO.isSetPrice()) {
            return null;
        }
        if (skuInSpuInfoDTO.getPrice() < 0) {
            return String.valueOf(0);
        } else {
            return String.valueOf(skuInSpuInfoDTO.getPrice());
        }
    }

    public static String convertStock(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (!skuInSpuInfoDTO.isSetStock()) {
            return null;
        }
        return String.valueOf(skuInSpuInfoDTO.getStock());
    }

    /**
     * UPC转美团平台UPC
     *
     * @param skuInSpuInfoDTO
     * @return
     */
    public static String convertUpc(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        String upc = skuInSpuInfoDTO.getUpc();

        return convertUpc(upc);
    }

    public static String convertUpc(String upc) {
        if (StringUtils.isBlank(upc)) {
            return "no_upc";
        }

        int upcMaxLength = MccConfigUtil.getMtSkuUpcMaxLength();
        if (upc.length() > upcMaxLength) {
            return null;
        }

        return upc;
    }

    public static String convertWeightForUnit(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (StringUtils.isBlank(skuInSpuInfoDTO.getWeightUnit())) {
            return null;
        }
        return new BigDecimal(String.valueOf(skuInSpuInfoDTO.getWeightForUnit())).stripTrailingZeros().toPlainString();
    }


    public static String convertWeightUnit(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (StringUtils.isBlank(skuInSpuInfoDTO.getWeightUnit())) {
            return null;
        }
        return skuInSpuInfoDTO.getWeightUnit();
    }

    public static Integer convertWeight(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (StringUtils.isNotBlank(skuInSpuInfoDTO.getWeightUnit())) {
            return null;
        }
        return skuInSpuInfoDTO.getWeight();
    }

    /*
     * ladder_box_num和ladder_box_price的组合与box_num和box_price互斥
     */

    public static String convertBoxNum(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (skuInSpuInfoDTO.getIsLadderBox() > 0) {
            return null;
        }
        if (skuInSpuInfoDTO.getBoxQuantity() <= 0) {
            return String.valueOf(1);
        } else {
            return String.valueOf(skuInSpuInfoDTO.getBoxQuantity());
        }
    }

    public static String convertBoxPrice(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (skuInSpuInfoDTO.getIsLadderBox() > 0) {
            return null;
        }
        if (skuInSpuInfoDTO.getBoxPrice() <= 0) {
            return String.valueOf(0);
        } else {
            return String.valueOf(skuInSpuInfoDTO.getBoxPrice());
        }
    }

    public static String convertLadderBoxNum(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (skuInSpuInfoDTO.getIsLadderBox() == 0) {
            return null;
        }
        return String.valueOf(skuInSpuInfoDTO.getLadderBoxQuantity());
    }

    public static String convertLadderBoxPrice(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (skuInSpuInfoDTO.getIsLadderBox() == 0) {
            return null;
        }
        return String.valueOf(skuInSpuInfoDTO.getLadderBoxPrice());
    }

    public static Integer convertStatus(SpuInfoDTO spuInfoDTO) {
        // 商品上下架状态（1-上架；2-下架）-> 美团平台 0-上架，1-下架
        if (spuInfoDTO.getStatus() == SpuStatusEnum.ON_LINE.getCode()) {
            return 0;
        }
        return 1;
    }

    public static Integer convertIsSpecialty(SpuInfoDTO spuInfoDTO) {
        if (spuInfoDTO.getIsSpecialty() < 0) {
            return null;
        } else {
            return Integer.valueOf(spuInfoDTO.getIsSpecialty());
        }
    }

    public static Integer convertSequence(SpuInfoDTO spuInfoDTO) {
        return Integer.valueOf(spuInfoDTO.getSequence());
    }

    public static String convert2MTCategoryCode(SpuInfoDTO spuInfoDTO) {
        if (CollectionUtils.isEmpty(spuInfoDTO.getLeafStoreCategoryList())) {
            return spuInfoDTO.getLeafStoreCategoryCode();
        }
        if (spuInfoDTO.getLeafStoreCategoryList().size() == 1) {
            return spuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryCode();
        } else {
            return null;
        }
    }

    // 拉取SPU映射转换

    public static Integer convertBoxNum(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getBox_num())) {
            return Double.valueOf(channelSkuInfoDTO.getBox_num()).intValue();
        } else if (StringUtils.isNotBlank(channelSkuInfoDTO.getLadder_box_num())) {
            return Double.valueOf(channelSkuInfoDTO.getLadder_box_num()).intValue();
        } else {
            return 1;
        }
    }

    public static Double convertBoxPrice(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getBox_price())) {
            return Double.valueOf(channelSkuInfoDTO.getBox_price());
        } else if (StringUtils.isNotBlank(channelSkuInfoDTO.getLadder_box_price())) {
            return Double.valueOf(channelSkuInfoDTO.getLadder_box_price());
        } else {
            return 0.0D;
        }
    }

    public static Integer convertLadderBoxQuantity(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getLadder_box_num())) {
            return Double.valueOf(channelSkuInfoDTO.getLadder_box_num()).intValue();
        } else {
            return 0;
        }
    }

    public static Double convertLadderBoxPrice(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getLadder_box_price())) {
            return Double.valueOf(channelSkuInfoDTO.getLadder_box_price());
        }
        return 0.0D;
    }

    public static String convertUnit(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getUnit())) {
            return channelSkuInfoDTO.getUnit();
        } else {
            return "份";
        }
    }

    public static String convertPictureContents(SpuInfoDTO spuInfoDTO) {
        List<String> pictureContents = spuInfoDTO.getPictureContents();
        if (CollectionUtils.isEmpty(pictureContents)) {
            return Strings.EMPTY;
        }
        return StringUtils.join(pictureContents, ',');
    }

    public static Integer convertForbidSingleOrder(SpuInfoDTO dto) {
        if (!dto.isSetForbidSingleOrder()) {
            return null;
        }

        return dto.getForbidSingleOrder();
    }

    public static Integer convertWeight(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (channelSkuInfoDTO.getWeight() > 0) {
            return channelSkuInfoDTO.getWeight();
        } else {
            return 0;
        }
    }

    public static Double convertWeightForUnit(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getWeight_for_unit())) {
            return Double.valueOf(channelSkuInfoDTO.getWeight_for_unit());
        } else {
            return 0.0D;
        }
    }

    public static Double convertPrice(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getPrice())) {
            return Double.valueOf(channelSkuInfoDTO.getPrice());
        } else {
            return 0.0D;
        }
    }

    public static Integer convertStock(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isBlank(channelSkuInfoDTO.getStock())) {
            return 99999;
        } else if (channelSkuInfoDTO.getStock().equalsIgnoreCase("*")) {
            return 99999;
        } else {
            return Integer.valueOf(channelSkuInfoDTO.getStock());
        }
    }

    public static String convertStock(SkuInSpuStockDTO skuStockDTO) {
        if (skuStockDTO.getStockQty() < 0) {
            return String.valueOf(0);
        } else {
            return String.valueOf((int) skuStockDTO.getStockQty());
        }
    }


    public static List<ChannelSettlementSkuBenefitDetail> convertSkuBenefitDetailList(List<MTOrderSettlementListResult
            .WmAppOrderSkuBenefitDetail> wmAppOrderSkuBenefitDetailList) {
        return Optional.ofNullable(wmAppOrderSkuBenefitDetailList).map(List::stream).orElse(Stream.empty())
                .map(MTOrderSettlementListResult.WmAppOrderSkuBenefitDetail::toChannelSettlementSkuBenefityDetail)
                .collect(Collectors.toList());
    }

    public static List<ChannelSettlementSkuShippingDetail> convertSkuShippingDetailList(List<MTOrderSettlementListResult
            .WmAppOrderSkuShippingDetail> wmAppOrderShippingActDetails) {
        return Optional.ofNullable(wmAppOrderShippingActDetails).map(List::stream).orElse(Stream.empty())
                .map(MTOrderSettlementListResult.WmAppOrderSkuShippingDetail::toChannelSettlementSkuShippingDetail)
                .collect(Collectors.toList());
    }

    public static String splitRecipientAddress(String recipientAddress){
        if (StringUtils.isBlank(recipientAddress)
                || !StringUtils.contains(recipientAddress, MT_RECIPIENT_ADDRESS_SPLIT_MARK)){
            return recipientAddress;
        }
        return StringUtils.substring(recipientAddress,0, StringUtils.lastIndexOf(recipientAddress, MT_RECIPIENT_ADDRESS_SPLIT_MARK));
    }

    public static Pair<String/*begin*/, String/*end*/> parseShippingPeriodRange(String periodRange) {
        if (StringUtils.isBlank(periodRange)) {
            return Pair.of(null, null);
        }

        if (!StringUtils.contains(periodRange, "-")) {
            return Pair.of(null, null);
        }

        String[] split = StringUtils.split(periodRange, "-");

        if (split.length != 2) {
            return Pair.of(null, null);
        }

        return Pair.of(split[0], split[1]);

    }

    public static List<Coordinate> convert2CoordinateList(List<OriginPoiShippingInfoDTO.OriginCoordination> originCoordinationList) {
        return originCoordinationList.stream().map(MtConverterUtil::convert2Coordinate).collect(Collectors.toList());
    }



    public static Coordinate convert2Coordinate(OriginPoiShippingInfoDTO.OriginCoordination originCoordination) {
        Coordinate coordinate = new Coordinate();
        coordinate.setLongitude(BigDecimal.valueOf(originCoordination.getY()).scaleByPowerOfTen(-6).doubleValue());
        coordinate.setLatitude(BigDecimal.valueOf(originCoordination.getX()).scaleByPowerOfTen(-6).doubleValue());

        return coordinate;
    }

    /**
     * 退单接口获取门店整单优惠
     * @param refundPartialEstimateCharge
     * @return
     */
    public static int convertAfsPoiPromotion(ChannelRefundPartialEstimateCharge refundPartialEstimateCharge){
        if (refundPartialEstimateCharge !=null && StringUtils.isNotBlank(refundPartialEstimateCharge.getActivity_poi_amount())){
            return MoneyUtils.yuanToFen(Double.parseDouble(refundPartialEstimateCharge.getActivity_poi_amount()));
        }
        return 0;
    }

    /**
     * 退单接口获取平台整单优惠
     * @param refundPartialEstimateCharge
     * @return
     */
    public static int convertAfsPlatPromotion(ChannelRefundPartialEstimateCharge refundPartialEstimateCharge) {
        if (refundPartialEstimateCharge !=null && StringUtils.isNotBlank(refundPartialEstimateCharge.getActivity_meituan_amount())){
            return MoneyUtils.yuanToFen(Double.parseDouble(refundPartialEstimateCharge.getActivity_meituan_amount()));
        }
        return 0;
    }

    /**
     * 退单接口获取商品行门店优惠
     * @param channelRetailRefundPartialEstimateCharge
     * @return
     */
    public static int convertItemAfsTotalPoiPromotion(ChannelRetailRefundPartialEstimateCharge channelRetailRefundPartialEstimateCharge){
        if (channelRetailRefundPartialEstimateCharge != null && StringUtils.isNotBlank(channelRetailRefundPartialEstimateCharge.getTotal_poi_charge())){
            return MoneyUtils.yuanToFen(new BigDecimal(channelRetailRefundPartialEstimateCharge.getTotal_poi_charge()).doubleValue());
        }
        return 0;
    }

    public static int convertItemAfsTotalPlatPromotion(ChannelRetailRefundPartialEstimateCharge channelRetailRefundPartialEstimateCharge){
        if (channelRetailRefundPartialEstimateCharge != null
                && StringUtils.isNotBlank(channelRetailRefundPartialEstimateCharge.getTotal_poi_charge())
                && StringUtils.isNotBlank(channelRetailRefundPartialEstimateCharge.getTotal_reduce_price())){
            BigDecimal totalPoiCharge = new BigDecimal(channelRetailRefundPartialEstimateCharge.getTotal_poi_charge());
            BigDecimal totalReducePrice = new BigDecimal(channelRetailRefundPartialEstimateCharge.getTotal_reduce_price());
            return MoneyUtils.yuanToFen(totalReducePrice.subtract(totalPoiCharge));
        }
        return 0;
    }


    /**
     * 退单接口获取商品优惠
     * @param type 1退还平台整单优惠 2退还商家整单优惠 3退还平台单品优惠 4退还商家单品优惠
     * @param channelRetailRefundPartialEstimateCharge
     * @return
     */
    public static int convertItemAfsPromotion(int type, ChannelRetailRefundPartialEstimateCharge channelRetailRefundPartialEstimateCharge){
        if (channelRetailRefundPartialEstimateCharge != null
                && CollectionUtils.isNotEmpty(channelRetailRefundPartialEstimateCharge.getOrder_retail_activity_details())){
            try {
                int refundPlatOrderPromotion = 0;
                int refundPoiOrderPromotion = 0;
                int refundPlatItemPromotion = 0;
                int refundPoiItemPromotion = 0;
                for (ChannelOrderRetailActivityDetails orderRetailActivityDetail : channelRetailRefundPartialEstimateCharge.getOrder_retail_activity_details()) {
                    if (MtActivityTypeEnum.isItemPromotion(orderRetailActivityDetail.getType())) {
                        refundPlatItemPromotion += MoneyUtils.yuanToFen(new BigDecimal(orderRetailActivityDetail.getMt_charge()));
                        refundPoiItemPromotion += MoneyUtils.yuanToFen(new BigDecimal(orderRetailActivityDetail.getPoi_charge()));
                    } else {
                        refundPlatOrderPromotion += MoneyUtils.yuanToFen(new BigDecimal(orderRetailActivityDetail.getMt_charge()));
                        refundPoiOrderPromotion += MoneyUtils.yuanToFen(new BigDecimal(orderRetailActivityDetail.getPoi_charge()));
                    }
                }
                switch (type) {
                    case 1:
                        return -refundPlatOrderPromotion;
                    case 2:
                        return refundPoiOrderPromotion;
                    case 3:
                        return -refundPlatItemPromotion;
                    case 4:
                        return refundPoiItemPromotion;
                    default:
                        return 0;
                }
            } catch (Exception e) {
                log.error("convertItemAfsPromotion error, type:{}, channelRetailRefundPartialEstimateCharge:{}", type, channelRetailRefundPartialEstimateCharge, e);
            }
        }
        return 0;
    }

    public static Integer convertAuditType(MtOrderAfsApplyDTO orderAfsApplyDTO) {
        return MtAuditTypeServiceUtils.getAuditTypeFromRefundDetail(orderAfsApplyDTO);
    }

    public static Integer convertReturnGoodsStatus(int returnGoodsWay, int logisticsStatus) {
        log.info("convertReturnGoodsStatus returnGoodsWay:{}, logisticsStatus:{}", returnGoodsWay, logisticsStatus);
        switch (returnGoodsWay) {
            case 1:
                return ReturnGoodsStatusEnum.WAIT_CUSTOM_SENDBACK.getCode();
            case 2:
                return ReturnGoodsStatusEnum.WAIT_STORE_PICKUP.getCode();
            case 3:
            case 4:
                if (logisticsStatus == MccConfigUtil.getMtReturnLogisticStatusEndDeliveryCode()) {
                    return ReturnGoodsStatusEnum.RIDER_DELIVERED.getCode();
                }
                if (logisticsStatus >= MccConfigUtil.getMtReturnLogisticStatusStartDeliveryCode() && logisticsStatus <= MccConfigUtil.getMtReturnLogisticStatusEndDeliveryCode()) {
                    return ReturnGoodsStatusEnum.RIDER_DELIVERING.getCode();
                }
                return ReturnGoodsStatusEnum.WAITING_FOR_RIDER.getCode();

            default:
                return ReturnGoodsStatusEnum.UNKNOWN.getCode();
        }
    }
}
