package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import java.util.List;

import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelVirtualAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.CopAccessConfigListPageQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.CopAccessConfigQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;

/**
 * 租户各渠道开放平台请求系统参数配置信息服务类
 *
 * <AUTHOR>
 * @create 2018-12-28 下午7:11
 **/
public interface CopAccessConfigService {

    /**
     * 全字段插入（ID自增，不需要赋值）
     *
     * @param record 参数实体对象
     * @return 影响行数
     */
    int insert(CopAccessConfigDO record);

    /**
     * 根据主键删除（物理删除）
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 按主键更新给定字段（不修改字段不需要赋值）
     *
     * @param record 参数实体对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(CopAccessConfigDO record);

    /**
     * 按主键查询全字段信息
     *
     * @param id 主键
     * @return 查询结果
     */
    CopAccessConfigDO selectByPrimaryKey(Long id);

    /**
     * 获取指定租户访问指定渠道时的请求系统参数Json串
     *
     * @param tenantId 租户Id
     * @param channelId 渠道Id
     * @return 系统参数josn串
     */
    @Deprecated
    String selectSysParams(Long tenantId, Integer channelId);

    /**
     * 获取指定租户访问指定渠道时的请求系统参数Json串
     *
     * @param tenantId 租户Id
     * @param channelId 渠道Id
     * @param appId 应用Id
     * @return 系统参数josn串
     */
    String selectAppSysParams(Long tenantId, Integer channelId, Long appId);


    /**
     * 根据平台（渠道）的租户标识映射租户Id
     *
     * @param channelId 渠道Id
     * @param tenantAppId 租户在渠道的唯一标识
     * @return 租户id
     */
    Long selectTenantId(Integer channelId, String tenantAppId);

    /**
     * 获取租户渠道配置
     * @param tenantId
     * @param channelId
     * @return
     */
    List<CopAccessConfigDO> findTenantChannelConfigApp(Long tenantId, Integer channelId);

    /**
     * 分页查询租户渠道配置
     * @param pageQuery
     * @return
     */
     List<CopAccessConfigDO> pageQueryTenantChannelConfig(CopAccessConfigListPageQueryDTO pageQuery);

    /**
     * 获取租户渠道配置
     * @param tenantId
     * @param channelId
     * @return
     */
    @Deprecated
    CopAccessConfigDO findTenantChannelConfig(Long tenantId, Integer channelId);

    /**
     * 租户渠道商品名称是否拼接规格
     * @param baseRequest
     * @return
     */
    boolean findTenantNeedContactSpec(BaseRequest baseRequest);

    /**
     * 获取当前租户已开通渠道列表
     * @param tenantId
     * @return
     */
    List<Integer> getChannelsByTenant(Long tenantId);

    /**
     * 根据平台（渠道）的租户标识及渠道Id获取系统参数Json串
     * @param tenantAppId
     * @param channelId
     * @return
     */
    String selectSysParams(String tenantAppId, Integer channelId);

    /**
     * 根据平台（渠道）的租户标识及渠道Id获取渠道配置
     * @param tenantAppId
     * @param channelId
     * @return
     */
    CopAccessConfigDO selectByTenantAppIdAndChannelId(String tenantAppId, Integer channelId);

    /**
     * 根据tenantAppId、渠道Id、租户Id列表查询租户渠道配置信息
     * @param tenantAppId
     * @param channelId
     * @param tenantIds
     * @return
     */
    List<CopAccessConfigDO> selectByTenantAppIdChannelIdTenantIds(String tenantAppId, Integer channelId, List<Long> tenantIds);


    /**
     * 根据平台（渠道）的租户标识及渠道Id获取渠道配置
     * @param tenantId
     * @param channelId
     * @return
     */
    CopAccessConfigDO selectByTenantIdAndChannelId(Long tenantId, Integer channelId);
    /**
     * 根据抖音的租户标识及总店Id 获取 渠道配置
     * 
     * @param tenantAppId
     * @param shopId
     * @return
     */
    CopAccessConfigDO selectDouyinByTenantAppIdAndShopId(String tenantAppId, String shopId);

    /**
     * 批量根据抖音的租户标识及总店Id 获取 渠道配置
     * @param dtos
     * @return
     */
    List<CopAccessConfigDO> selectDouyinByCopAccessConfigQueryDTOList(List<CopAccessConfigQueryDTO> dtos);

    /**
     * 根据有赞的租户标识及总店Id 获取 渠道配置
     *
     * @param tenantAppId
     * @param shopId
     * @return
     */
    CopAccessConfigDO selectYzByTenantAppIdAndShopId(String tenantAppId, String shopId);

    /**
     * 批量根据有赞的租户标识及总店Id 获取 渠道配置
     * @param dtos
     * @return
     */
    List<CopAccessConfigDO> selectYzByCopAccessConfigQueryDTOList(List<CopAccessConfigQueryDTO> dtos);

    /**
     * 将令牌信息保存到channel_cop_access_config的sys_params中
     * @param copAccessConfigDO

     * @return
     */
    int updateSysParams(CopAccessConfigDO copAccessConfigDO);


    /**
     * 将令牌信息保存到channel_cop_access_config的sys_params中
     * @param copAccessConfigDO

     * @return
     */
    int updateWeChatSysParams(CopAccessConfigDO copAccessConfigDO);
}
