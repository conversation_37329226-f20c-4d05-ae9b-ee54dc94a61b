package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2023/05/17
 */

@Service("mtDrunkHorseChannelGateService")
public class DrunkHorseChannelGateServiceImpl extends MtChannelGateService  {
    @Value("${mt_drunkhorse.url.base}")
    private String baseUrl;

    @Override
    public String getBaseUrl() {
        return baseUrl;
    }
}
