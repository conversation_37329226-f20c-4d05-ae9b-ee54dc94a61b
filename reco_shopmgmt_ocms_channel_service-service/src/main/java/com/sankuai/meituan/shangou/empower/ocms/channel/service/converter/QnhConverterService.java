package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhChannelSpec;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhOfflineCategory;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhOnlineCategory;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhOnlineCategoryQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhProduct;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhProductQueryId;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhProductQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhUpdateOnlinePriceParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhOfflineCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhOnlineCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhProductDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhQueryId;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhStoreProductDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhStoreSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhOnlineCategoryListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhProductQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhStoreProductListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhUpdateOnlinePriceRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;
import java.util.Map;

/**
 * @Author: wangyihao04
 * @Date: 2021-12-27 21:55
 * @Mail: <EMAIL>
 */
@Mapper(componentModel = "spring")
public interface QnhConverterService {

    String MTWM = "MTWM";

    @Mappings({
            @Mapping(target = "barcode", source = "barcode"),
            @Mapping(target = "item_code", source = "itemCode"),
            @Mapping(target = "data_type", source = "dataType"),
            @Mapping(target = "ids", source = "ids"),
            @Mapping(target = "page_no", source = "pageNo"),
            @Mapping(target = "page_size", source = "pageSize"),
    })
    QnhProductQueryParam qnhProductQueryParamMapping(QnhProductQueryRequest request);

    @Mappings({
            @Mapping(target = "barcode", source = "barcode"),
            @Mapping(target = "qnhid", source = "qnhId"),
            @Mapping(target = "skuid", source = "skuId"),
            @Mapping(target = "price", source = "onlinePrice"),
    })
    QnhUpdateOnlinePriceParam qnhUpdateOnlinePriceParamMapping(QnhUpdateOnlinePriceRequest request);

    @Mappings({
            @Mapping(target = "qnhid", source = "qnhId"),
    })
    QnhProductQueryId qnhProductQueryIdMapping(QnhQueryId id);

    List<QnhProductQueryId> qnhProductQueryIdMapping(List<QnhQueryId> ids);

    @Mappings({
            @Mapping(target = "barcode", source = "barcode"),
            @Mapping(target = "item_code", source = "itemCode"),
            @Mapping(target = "data_type", source = "dataType"),
            @Mapping(target = "page_no", source = "pageNo"),
            @Mapping(target = "page_size", source = "pageSize"),
            @Mapping(target = "item_type", source = "itemType"),
    })
    QnhProductQueryParam qnhProductQueryParamMapping(QnhStoreProductListRequest request);

    @Mappings({
            @Mapping(target = "qnhId", source = "qnhid"),
            @Mapping(target = "itemCode", source = "item_code"),
            @Mapping(target = "barcode", source = "barcode"),
            @Mapping(target = "bid", source = "bid"),
            @Mapping(target = "status", source = "status"),
            @Mapping(target = "masterImgUrl", source = "master_img_url"),
            @Mapping(target = "images", source = "images"),
            @Mapping(target = "MDesc", source = "m_desc"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "onlineCatCode", source = "online_cat_code"),
            @Mapping(target = "onlineCatName", source = "online_cat_name"),
            @Mapping(target = "onlineSupCode", source = "online_sup_code"),
            @Mapping(target = "onlineSupName", source = "online_sup_name"),
            @Mapping(target = "brandCode", source = "brand_code"),
            @Mapping(target = "brandName", source = "brand_name"),
            @Mapping(target = "spec", source = "spec"),
            @Mapping(target = "MSpec", source = "m_spec"),
            @Mapping(target = "grossWeight", source = "gross_weight"),
            @Mapping(target = "sourceCatCode", source = "source_cat_code"),
            @Mapping(target = "sourceCatName", source = "source_cat_name"),
            @Mapping(target = "pkNumber", source = "pk_number"),
            @Mapping(target = "dwzhxs", source = "dwzhxs"),
            @Mapping(target = "origin1", source = "origin1"),
            @Mapping(target = "warranty", source = "warranty"),
            @Mapping(target = "subTitle", source = "sub_title"),
            @Mapping(target = "MXsms", source = "m_xsms"),
            @Mapping(target = "MBaseUomName", source = "m_base_uom_name"),
            @Mapping(target = "baseUomName", source = "base_uom_name"),
            @Mapping(target = "lunchBoxFee", source = "lunch_box_fee"),
            @Mapping(target = "lunchBoxNum", source = "lunch_box_num"),
            @Mapping(target = "itemType", source = "item_type"),
            @Mapping(target = "zbNumber", source = "zb_number"),
            @Mapping(target = "jddjCat", source = "jddj_cat"),
            @Mapping(target = "mtwmCat", source = "mtwm_cat"),
            @Mapping(target = "elemCat", source = "elem_cat"),
            @Mapping(target = "zbCodes", source = "zb_codes"),
            @Mapping(target = "skuList", source = "skulist"),
            @Mapping(target = "kssq", source = "kssq"),
            @Mapping(target = "lj", source = "lj"),
            @Mapping(target = "sx", source = "sx"),
            @Mapping(target = "createStatus", source = "create_status"),
    })
    QnhProductDTO qnhProductDTOMapping(QnhProduct product);

    List<QnhProductDTO> qnhProductDTOMapping(List<QnhProduct> products);

    @Mappings({
            @Mapping(target = "skuCode", source = "code"),
            @Mapping(target = "barcode", source = "barcode"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "spec", expression = "java(extractSpec(sku.getChannel_spec()))"),
            @Mapping(target = "weight", source = "weight"),
            //@Mapping(target = "quantity", source = "quantity"),
            @Mapping(target = "skuId", source = "skuid"),
    })
    QnhSkuDTO qnhSkuDTOMapping(QnhSku sku);

    List<QnhSkuDTO> qnhSkuDTOMapping(List<QnhSku> sku);

    default String extractSpec(Map<String, QnhChannelSpec> channel_spec) {
        if (MapUtils.isEmpty(channel_spec)) {
            return null;
        }
        QnhChannelSpec mtSpec = channel_spec.get(MTWM);
        return mtSpec != null ? mtSpec.getValue_name() : null;
    }

    @Mappings({
            @Mapping(target = "qnhId", source = "qnhid"),
            @Mapping(target = "itemCode", source = "item_code"),
            @Mapping(target = "barcode", source = "barcode"),
            @Mapping(target = "bid", source = "bid"),
            @Mapping(target = "status", source = "status"),
            @Mapping(target = "saleStatus", source = "sale_status"),
            @Mapping(target = "masterImgUrl", source = "master_img_url"),
            @Mapping(target = "images", source = "images"),
            @Mapping(target = "categoryList",
                    expression = "java(mergeQnhOnlineCategories(product.getCategorys(), product" +
                            ".getAuxiliary_categoryInfos()))"),
            @Mapping(target = "MDesc", source = "m_desc"),
            @Mapping(target = "brandCode", source = "brand_code"),
            @Mapping(target = "brandName", source = "brand_name"),
            @Mapping(target = "MSpec", source = "m_spec"),
            @Mapping(target = "grossWeight", source = "gross_weight"),
            @Mapping(target = "pkNumber", source = "pk_number"),
            @Mapping(target = "dwzhxs", source = "dwzhxs"),
            @Mapping(target = "origin1", source = "origin1"),
            @Mapping(target = "warranty", source = "warranty"),
            @Mapping(target = "subTitle", source = "sub_title"),
            @Mapping(target = "MXsms", source = "m_xsms"),
            @Mapping(target = "MBaseUomName", source = "m_base_uom_name"),
            @Mapping(target = "lunchBoxFee", source = "lunch_box_fee"),
            @Mapping(target = "lunchBoxNum", source = "lunch_box_num"),
            @Mapping(target = "referencePrice", source = "reference_price"),
            @Mapping(target = "salePrice", source = "sale_price"),
            @Mapping(target = "stock", source = "stock"),
            @Mapping(target = "saleStatusType", source = "sale_status_type"),
            @Mapping(target = "itemType", source = "item_type"),
            @Mapping(target = "zbNumber", source = "zb_number"),
            @Mapping(target = "jddjCat", source = "jddj_cat"),
            @Mapping(target = "mtwmCat", source = "mtwm_cat"),
            @Mapping(target = "elemCat", source = "elem_cat"),
            @Mapping(target = "zbCodes", source = "zb_codes"),
            @Mapping(target = "skuList", source = "skulist"),
            @Mapping(target = "kssq", source = "kssq"),
            @Mapping(target = "lj", source = "lj"),
            @Mapping(target = "sx", source = "sx"),
            @Mapping(target = "qgsl", source = "qgsl"),
            @Mapping(target = "mtwmOutKey", source = "mtwm_out_key"),
            @Mapping(target = "elemOutKey", source = "ebls_out_key"),
            @Mapping(target = "pluCode", source = "plu_code"),
    })
    QnhStoreProductDTO qnhStoreProductDTOMapping(QnhProduct product);

    List<QnhStoreProductDTO> qnhStoreProductDTOMapping(List<QnhProduct> products);

    default List<QnhCategoryDTO> mergeQnhOnlineCategories(List<QnhCategoryDTO> categorys, List<QnhCategoryDTO>
            auxiliary_categoryInfos) {
        List<QnhCategoryDTO> allCategories = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(categorys)) {
            allCategories.addAll(categorys);
        }
        if (CollectionUtils.isNotEmpty(auxiliary_categoryInfos)) {
            allCategories.addAll(auxiliary_categoryInfos);
        }

        return allCategories;
    }

    @Mappings({
            @Mapping(target = "skuCode", source = "code"),
            @Mapping(target = "barcode", source = "barcode"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "spec", expression = "java(extractSpec(sku.getChannel_spec()))"),
            @Mapping(target = "weight", source = "weight"),
            @Mapping(target = "zbQuantity", source = "quantity"),
            @Mapping(target = "skuId", source = "skuid"),
            @Mapping(target = "referencePrice", source = "reference_price"),
            @Mapping(target = "salePrice", source = "sale_price"),
            @Mapping(target = "stock", source = "stock"),
            @Mapping(target = "qgsl", source = "qgsl"),
            @Mapping(target = "lunchBoxFee", source = "lunch_box_fee"),
            @Mapping(target = "lunchBoxNum", source = "lunch_box_num"),
    })
    QnhStoreSkuDTO qnhStoreSkuDTOMapping(QnhSku sku);

    List<QnhStoreSkuDTO> qnhStoreSkuDTOMapping(List<QnhSku> sku);

    @Mappings({
            @Mapping(target = "data_type", source = "dataType"),
            @Mapping(target = "page_no", source = "pageNo"),
            @Mapping(target = "page_size", source = "pageSize"),
    })
    QnhOnlineCategoryQueryParam qnhOnlineCategoryQueryParamMapping(QnhOnlineCategoryListRequest request);

    List<QnhOnlineCategoryDTO> qnhOnlineCategoryDTOMapping(List<QnhOnlineCategory> category);

    @Mappings({
            @Mapping(target = "sort", source = "rowno"),
    })
    QnhOnlineCategoryDTO qnhOnlineCategoryDTOMapping(QnhOnlineCategory category);

    QnhOfflineCategoryDTO convertOfflineCategory(QnhOfflineCategory source);

    List<QnhOfflineCategoryDTO> convertOfflineCategories(List<QnhOfflineCategory> source);

}
