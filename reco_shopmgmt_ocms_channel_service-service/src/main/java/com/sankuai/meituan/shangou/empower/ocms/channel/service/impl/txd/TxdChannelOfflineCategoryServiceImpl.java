package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostTxdEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChildChannelOfflineCategoryQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOfflineCategoryService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.TxdConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.taobao.api.ApiException;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.response.AlibabaWdkSkuCategoryQueryResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2024/4/19 18:20
 **/
@Service("txdChannelOfflineCategoryService")
@Slf4j
public class TxdChannelOfflineCategoryServiceImpl implements ChannelOfflineCategoryService {

    @Autowired
    private TxdBaseService txdBaseService;

    @Resource
    private BaseConverterService baseConverterService;

    private static final ChannelOfflineCategoryDTO ROOT_CATEGORY = new ChannelOfflineCategoryDTO() {{
        setChannelCode("");
    }};

    @Resource(name = "txdCategoryThreadPool")
    private ExecutorService txdCategoryThreadPool;
    @Override
    public ChannelOfflineCategoryCreateResponse createChannelOfflineCategory(ChannelOfflineCategoryCreateRequest request) {
        ChannelOfflineCategoryCreateResponse resultData = new ChannelOfflineCategoryCreateResponse();
        resultData.setStatus(ChannelStatus.buildSuccess());
        ChannelOfflineCategoryDTO channelOfflineCategoryDTO = request.getChannelOfflineCategoryDTO();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        if (Objects.isNull(channelOfflineCategoryDTO)) {
            return resultData;
        }
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.OFFLINE_CATEGORY_CREATE, baseRequest,
                    TxdConvertUtil.buildCategoryAddRequest(request.getChannelOfflineCategoryDTO()));
            return TxdConvertUtil.genAddCategoryResult(commonResponse, channelOfflineCategoryDTO);
        } catch (ApiException e) {
            log.warn("调用淘鲜达创建分类接口异常, request {}.", request, e);
            resultData.setStatus(ChannelStatus.build(TxdConvertUtil.parseTxdErrorCode(e.getErrCode()), e.getMessage()));
        }
        catch (Exception e) {
            log.warn("调用淘鲜达创建分类接口异常, request {}.", request, e);
            resultData.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), ResultCode.FAIL.getMsg()));
        }
        return resultData;
    }

    @Override
    public ChannelOfflineCategoryResponse updateChannelOfflineCategory(ChannelOfflineCategoryUpdateRequest request) {
        ChannelOfflineCategoryResponse resultData = new ChannelOfflineCategoryResponse();
        resultData.setStatus(ChannelStatus.buildSuccess());
        ChannelOfflineCategoryDTO channelOfflineCategoryDTO = request.getChannelOfflineCategoryDTO();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        if (Objects.isNull(channelOfflineCategoryDTO)) {
            return resultData;
        }
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.OFFLINE_CATEGORY_UPDATE, baseRequest,
                    TxdConvertUtil.buildCategoryUpdateRequest(channelOfflineCategoryDTO));
            return TxdConvertUtil.genUpdateCategoryResult(commonResponse);
        }
        catch (ApiException e) {
            log.warn("调用淘鲜达更新分类接口异常, request {}.", request, e);
            resultData.setStatus(ChannelStatus.build(TxdConvertUtil.parseTxdErrorCode(e.getErrCode()), e.getMessage()));
        }
        catch (Exception e) {
            log.warn("调用淘鲜达更新分类接口异常, request {}.", request, e);
            resultData.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), ResultCode.FAIL.getMsg()));
        }
        return resultData;
    }

    @Override
    public ChannelOfflineCategoryResponse deleteChannelOfflineCategory(ChannelOfflineCategoryDeleteRequest request) {
        ChannelOfflineCategoryResponse resultData = new ChannelOfflineCategoryResponse();
        resultData.setStatus(ChannelStatus.buildSuccess());
        String channelOfflineCategoryCode = request.getChannelOfflineCategoryCode();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        if (StringUtils.isBlank(channelOfflineCategoryCode)) {
            return resultData;
        }
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.OFFLINE_CATEGORY_DELETE,baseRequest,
                    TxdConvertUtil.buildDeleteRequest(channelOfflineCategoryCode));
            return TxdConvertUtil.genDeleteCategoryResult(commonResponse);
        }
        catch (ApiException e) {
            log.warn("调用淘鲜达删除分类接口异常, request {}.", request, e);
            resultData.setStatus(ChannelStatus.build(TxdConvertUtil.parseTxdErrorCode(e.getErrCode()), e.getMessage()));
        }
        catch (Exception e) {
            log.warn("调用淘鲜达删除分类接口异常, request {}.", request, e);
            resultData.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), ResultCode.FAIL.getMsg()));
        }
        return resultData;
    }

    @Override
    public ChannelOfflineCategoryQueryResponse queryAllChannelOfflineCategory(ChannelOfflineCategoryQueryRequest request) {
        ChannelOfflineCategoryQueryResponse resultData = new ChannelOfflineCategoryQueryResponse();
        resultData.setStatus(ChannelStatus.buildSuccess());

        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        Map<Long, ChannelOfflineCategoryDTO> codeToCategoryMap = new ConcurrentHashMap<Long, ChannelOfflineCategoryDTO>();

        // 初始化查询root分类
        List<ChannelOfflineCategoryDTO> offlineCategoryDTOList = Lists.newArrayList(ROOT_CATEGORY);
        try {
            while (CollectionUtils.isNotEmpty(offlineCategoryDTOList)) {
                List<Future<TaobaoResponse>> futureList = Lists.newArrayListWithCapacity(offlineCategoryDTOList.size());

                for (ChannelOfflineCategoryDTO channelOfflineCategoryDTO : offlineCategoryDTOList) {
                    futureList.add(txdCategoryThreadPool.submit(() -> {
                        return getTaoBaoCategoryInfo(channelOfflineCategoryDTO, baseRequest);
                    }));
                }

                List<ChannelOfflineCategoryDTO> havingChildCategories = new ArrayList<>();
                for (Future<TaobaoResponse> listFuture : futureList) {
                    TaobaoResponse taobaoResponse = listFuture.get(5L, TimeUnit.SECONDS);
                    if (!taobaoResponse.isSuccess()) {
                        resultData.setStatus(ChannelStatus.build(TxdConvertUtil.parseTxdErrorCode(taobaoResponse.getErrorCode()),
                                taobaoResponse.getMsg()));
                        return resultData;
                    }

                    AlibabaWdkSkuCategoryQueryResponse queryResponse = (AlibabaWdkSkuCategoryQueryResponse) taobaoResponse;
                    AlibabaWdkSkuCategoryQueryResponse.ApiResult apiResult = queryResponse.getResult();
                    if (!apiResult.getSuccess()) {
                        resultData.setStatus(ChannelStatus.build(TxdConvertUtil.parseTxdErrorCode(apiResult.getErrCode()),
                                apiResult.getErrMsg()));
                        return resultData;
                    }

                    List<ChannelOfflineCategoryDTO> partChildList = TxdConvertUtil.genQueryCategoryResult(queryResponse, codeToCategoryMap);
                    // 将非Leaf分类加入下一次查询队列
                    havingChildCategories.addAll(Fun.filter((partChildList),
                            child -> !TxdConvertUtil.LEFT.equals(child.getIsLeaf()) && child.getLevel() < TxdConvertUtil.MAX_CATEGORY_LEVEL));
                }

                // 在查询子分类的信息
                offlineCategoryDTOList = havingChildCategories;
            }
            resultData.setChannelOfflineCategoryList(Lists.newArrayList(codeToCategoryMap.values()));
            return resultData;
        }
        catch (Exception e) {
            log.warn("调用淘鲜达查询分类接口异常, request {}.", request, e);
            resultData.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), ResultCode.FAIL.getMsg()));
        }
        return resultData;
    }

    @Override
    public ChannelOfflineCategoryQueryResponse queryChildChannelOfflineCategory(ChildChannelOfflineCategoryQueryRequest request) {
        ChannelOfflineCategoryQueryResponse resultData = new ChannelOfflineCategoryQueryResponse();
        resultData.setStatus(ChannelStatus.buildSuccess());

        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        Map<Long, ChannelOfflineCategoryDTO> codeToCategoryMap = new ConcurrentHashMap<Long, ChannelOfflineCategoryDTO>();

        try {
            ChannelOfflineCategoryDTO parent = new ChannelOfflineCategoryDTO();
            parent.setChannelCode(request.getParentCategoryCode());
            TaobaoResponse taobaoResponse = getTaoBaoCategoryInfo(parent, baseRequest);
            if (!taobaoResponse.isSuccess()) {
                resultData.setStatus(ChannelStatus.build(TxdConvertUtil.parseTxdErrorCode(taobaoResponse.getErrorCode()),
                        taobaoResponse.getMsg()));
                return resultData;
            }

            AlibabaWdkSkuCategoryQueryResponse queryResponse = (AlibabaWdkSkuCategoryQueryResponse) taobaoResponse;
            AlibabaWdkSkuCategoryQueryResponse.ApiResult apiResult = queryResponse.getResult();
            if (!apiResult.getSuccess()) {
                resultData.setStatus(ChannelStatus.build(TxdConvertUtil.parseTxdErrorCode(apiResult.getErrCode()),
                        apiResult.getErrMsg()));
                return resultData;
            }

            List<ChannelOfflineCategoryDTO> childCategories = TxdConvertUtil.genQueryCategoryResult(queryResponse, codeToCategoryMap);
            Optional<ChannelOfflineCategoryDTO> any = childCategories.stream().filter(child -> Objects.equals(child.getChannelCode(), request.getParentCategoryCode())).findAny();
            if (any.isPresent()) {
                childCategories.clear();
            }

            resultData.setChannelOfflineCategoryList(childCategories);
        }
        catch (Exception e) {
            log.warn("调用淘鲜达查询分类接口异常, request {}.", request, e);
            resultData.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), ResultCode.FAIL.getMsg()));
        }
        return resultData;
    }

    private TaobaoResponse getTaoBaoCategoryInfo(ChannelOfflineCategoryDTO channelOfflineCategoryDTO, BaseRequest baseRequest) {
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.OFFLINE_CATEGORY_QUERY, baseRequest,
                    TxdConvertUtil.buildQueryCategoryRequest(channelOfflineCategoryDTO.getChannelCode()));
            return commonResponse;
        }
        catch (Exception e) {
            log.error("查询淘鲜达线下分类异常 code:{} ", channelOfflineCategoryDTO.getChannelCode(), e);
            throw new RuntimeException(e);
        }
    }
}
