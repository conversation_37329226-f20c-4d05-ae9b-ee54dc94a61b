package com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt;

/**
 * <AUTHOR>
 * @date 2025-09-25
 * @email <EMAIL>
 */
public enum MarkupExecuteTypeEnum {

    ENABLE(1, "启用"),
    DISABLE(2, "不启用"),
    KEEP(3, "保持原值")

    ;

    private int code;
    private String desc;

    MarkupExecuteTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static MarkupExecuteTypeEnum valueOfCode(int code) {
        for (MarkupExecuteTypeEnum obj : MarkupExecuteTypeEnum.values()) {
            if (java.util.Objects.equals(obj.code, code)) {
                return obj;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

}
