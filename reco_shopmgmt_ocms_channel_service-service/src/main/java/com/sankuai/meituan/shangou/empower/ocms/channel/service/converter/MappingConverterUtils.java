package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.SpecialPictureInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ProductSpecialPicture;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;

import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Encoder;

/**
 * 实体映射工具类
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:18
 **/
@Slf4j
@Component
public class MappingConverterUtils {

    public String byteBufferBase64Encode (byte[] data) {
        Preconditions.checkNotNull(data, "byteBufferBase64Encode data is null");

        return new BASE64Encoder().encode(data);
    }

    public byte[] byteBuffer (byte[] data) {
        return data;
    }

    public String listAsString (List<String> value){
        if (CollectionUtils.isEmpty(value))
            return null;

        return String.join(",", value);
    }

    public static List<String> stringAsList(String pictures){
        return Arrays.asList(pictures.split(","));
    }

    public static List<String> convertQualificationPictures(String qualificationPictures){
        if (StringUtils.isBlank(qualificationPictures)){
            return Lists.newArrayList();
        }
        return Arrays.asList(qualificationPictures.split(","));
    }

    public static List<ProductSpecialPicture> convertSpecialPictureInfos(List<SpecialPictureInfoDTO> special_pictures) {
        if(CollectionUtils.isEmpty(special_pictures)){
            return null;
        }
        return special_pictures.stream().filter(Objects::nonNull).map(productSpecialPicture -> {
            ProductSpecialPicture specialPicture = new ProductSpecialPicture();
            specialPicture.setSpecialPictureType(productSpecialPicture.getSpecial_picture_type());
            specialPicture.setSpecialPictureTitle(productSpecialPicture.getSpecial_picture_title());
            if(StringUtils.isNotBlank(productSpecialPicture.getSpecial_picture_urls())){
                specialPicture.setSpecialPictures(
                        Arrays.asList(productSpecialPicture.getSpecial_picture_urls().split(",")));
            }
            return specialPicture;
        }).collect(Collectors.toList());
    }

    public static List<String> convertPictureContents(String pictureContents){
        if (StringUtils.isBlank(pictureContents)){
            return Lists.newArrayList();
        }
        return Arrays.asList(pictureContents.split(","));
    }


    public static boolean convertCombination(Integer isCombination) {
        return Objects.equals(isCombination, ProjectConstant.YES);
    }
}
