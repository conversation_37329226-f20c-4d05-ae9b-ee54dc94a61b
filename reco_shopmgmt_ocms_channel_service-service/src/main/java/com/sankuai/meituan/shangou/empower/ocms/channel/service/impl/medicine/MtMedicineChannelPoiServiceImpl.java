package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPoiMappingInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelPoiServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.LIST_PARTITION_NUM;
import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.RHINO_UPTIMATE_SET;

/**
 * <AUTHOR>
 * @date 2020/8/19 17:35
 * Description: 美团医药-渠道门店相关服务
 */
@Service("mtMedicineChannelPoiService")
public class MtMedicineChannelPoiServiceImpl extends MtChannelPoiServiceImpl implements ChannelPoiService {

    @Autowired
    private MtMedicineChannelGateService mtMedicineChannelGateService;

    @Resource(name = "mtBrandChannelPoiService")
    private ChannelPoiService mtBrandChannelPoiService;

    @Override
    public GetPoiInfoResponse batchGetPoiDetails(BatchGetPoiDetailsRequest request) {
        return mtBrandChannelPoiService.batchGetPoiDetails(request);
    }

    @Override
    public GetPoiIdsResponse getPoiInnerId(BatchGetPoiDetailsRequest request) {
        return mtBrandChannelPoiService.getPoiInnerId(request);
    }

    @Override
    public BaseChannelGateService getMtChannelGateService() {
        return mtMedicineChannelGateService;
    }
}
