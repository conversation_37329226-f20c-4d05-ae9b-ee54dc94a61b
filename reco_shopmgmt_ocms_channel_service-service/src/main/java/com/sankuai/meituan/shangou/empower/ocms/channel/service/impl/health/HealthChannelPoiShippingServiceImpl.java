package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.health;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiShippingService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelPoiShippingServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 新供给侧美团渠道门店配送服务
 * （一租户多品牌需求中，将新供给和歪马的服务代码拆分开来）
 *
 * <AUTHOR>
 */
@Service("healthChannelPoiShippingService")
@Slf4j
public class HealthChannelPoiShippingServiceImpl extends MtBrandChannelPoiShippingServiceImpl implements ChannelPoiShippingService {

}

