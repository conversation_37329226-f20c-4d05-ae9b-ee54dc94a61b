package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.MtChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelCommonServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelGateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 歪马专用美团渠道部分公共业务功能
 *
 * @description:
 * @author: zhaolei12
 * @create: 2019-03-15 11:38
 */
@Service("mtDrunkHorseBrandChannelCommonService")
public class DrunkHorseBrandChannelCommonServiceImpl extends MtBrandChannelCommonServiceImpl implements MtChannelCommonService, ChannelCommonService {

    @Autowired
    @Qualifier("mtDrunkHorseBrandChannelGateService")
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.actRetailDiscountList}")
    private String actRetailDiscountListUrl;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.actAllGetByAppFoodCodes}")
    private String actAllGetByAppFoodCodesUrl;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.getOriginList}")
    private String getOriginListUrl;

    @Override
    public MtBrandChannelGateService getMtBrandChannelGateService() {
        return mtBrandChannelGateService;
    }

    @Override
    public String getActRetailDiscountListUrl() {
        return actRetailDiscountListUrl;
    }

    @Override
    public String getActAllGetByAppFoodCodesUrl() {
        return actAllGetByAppFoodCodesUrl;
    }

    @Override
    public String getGetOriginListUrl() {
        return getOriginListUrl;
    }
}
