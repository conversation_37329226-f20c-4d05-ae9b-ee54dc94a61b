package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.Bssert;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinQualificationParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinQualificationResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelQualificationService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetQualificationRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetQualificationResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QualificationConfig;

@Service("dyChannelQualificationService")
public class DouyinChannelQualificationServiceImpl implements ChannelQualificationService {

    @Autowired
    private DouyinChannelGateService douyinChannelGateService;
    @Override
    public GetQualificationResponse queryChannelQualification(GetQualificationRequest request) {

        Assert.throwIfNull(request,"请求为空");
        Assert.throwIfNull(request.getCategoryId(),"渠道类目id为空");

        GetQualificationResponse response = new GetQualificationResponse();

        List<QualificationConfig> qualificationConfigList = new LinkedList<>();

        ChannelResponseDTO<DouyinQualificationResult> channelResponseDTO =
                douyinChannelGateService.sendPost(ChannelPostDouyinEnum.GET_PRODUCT_QUALIFICATION_CONFIG, request.getBaseInfo(), null,
                        DouyinQualificationParam.builder().category_id(request.getCategoryId()).build());
        Bssert.throwIfTrue(!channelResponseDTO.isSuccess(),"查询渠道接口失败");

        qualificationConfigList.addAll(channelResponseDTO.getCoreData().getConfig_list().stream().map(DouyinConvertUtil::buildQulifactionInfo).collect(Collectors.toList()));
        response.setQualificationConfigList(qualificationConfigList);
        response.setStatus(ResultGenerator.genSuccessResult());
        return response;
    }
}
