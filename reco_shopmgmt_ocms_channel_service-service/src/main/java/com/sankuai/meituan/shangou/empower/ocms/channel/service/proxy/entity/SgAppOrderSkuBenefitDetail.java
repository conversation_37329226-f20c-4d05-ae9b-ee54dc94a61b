/*
 * Copyright (c) 2010-2011 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * Пусть вечная мира во всем мире！
 *
 * @Author: wangruiguo
 * @Date: 2021/7/27 5:34 下午
 * @Description: 商品优惠详情
 */
public class SgAppOrderSkuBenefitDetail {

    private String app_food_code;
    private String name;
    private String sku_id;
    private int count;//总数
    private double originPrice;//单个sku商品原价
    private double totalOriginPrice;//sku商品总原价
    private double activityPrice;//单个商品优惠后价格
    private double totalActivityPrice;//sku商品优惠后总价格
    private double totalReducePrice;//sku商品总优惠金额
    private double boxNumber;//单个商品餐盒个数
    private double boxPrice;//单个商品餐盒费
    private double totalBoxPrice;//sku商品总餐盒费
    private double totalMtCharge;//sku商品所有活动美团承担总金额
    private double totalPoiCharge;//sku商品所有活动商家承担总金额
    private String upc;
    private List<SgAppOrderActDetail> wmAppOrderActDetails;//sku商品参与的活动详情
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String app_medicine_code;

    public String getApp_food_code() {
        return app_food_code;
    }

    public void setApp_food_code(String app_food_code) {
        this.app_food_code = app_food_code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSku_id() {
        return sku_id;
    }

    public void setSku_id(String sku_id) {
        this.sku_id = sku_id;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public double getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(double originPrice) {
        this.originPrice = originPrice;
    }

    public double getTotalOriginPrice() {
        return totalOriginPrice;
    }

    public void setTotalOriginPrice(double totalOriginPrice) {
        this.totalOriginPrice = totalOriginPrice;
    }

    public double getActivityPrice() {
        return activityPrice;
    }

    public void setActivityPrice(double activityPrice) {
        this.activityPrice = activityPrice;
    }

    public double getTotalActivityPrice() {
        return totalActivityPrice;
    }

    public void setTotalActivityPrice(double totalActivityPrice) {
        this.totalActivityPrice = totalActivityPrice;
    }

    public double getTotalReducePrice() {
        return totalReducePrice;
    }

    public void setTotalReducePrice(double totalReducePrice) {
        this.totalReducePrice = totalReducePrice;
    }

    public double getBoxNumber() {
        return boxNumber;
    }

    public void setBoxNumber(double boxNumber) {
        this.boxNumber = boxNumber;
    }

    public double getBoxPrice() {
        return boxPrice;
    }

    public void setBoxPrice(double boxPrice) {
        this.boxPrice = boxPrice;
    }

    public double getTotalBoxPrice() {
        return totalBoxPrice;
    }

    public void setTotalBoxPrice(double totalBoxPrice) {
        this.totalBoxPrice = totalBoxPrice;
    }

    public double getTotalMtCharge() {
        return totalMtCharge;
    }

    public void setTotalMtCharge(double totalMtCharge) {
        this.totalMtCharge = totalMtCharge;
    }

    public double getTotalPoiCharge() {
        return totalPoiCharge;
    }

    public void setTotalPoiCharge(double totalPoiCharge) {
        this.totalPoiCharge = totalPoiCharge;
    }

    public String getUpc() {
        return upc;
    }

    public void setUpc(String upc) {
        this.upc = upc;
    }

    public List<SgAppOrderActDetail> getWmAppOrderActDetails() {
        return wmAppOrderActDetails;
    }

    public void setWmAppOrderActDetails(List<SgAppOrderActDetail> wmAppOrderActDetails) {
        this.wmAppOrderActDetails = wmAppOrderActDetails;
    }

    public String getApp_medicine_code() {
        return app_medicine_code;
    }

    public void setApp_medicine_code(String app_medicine_code) {
        this.app_medicine_code = app_medicine_code;
    }

    @Override
    public String toString() {
        return "SgAppOrderSkuBenefitDetail{" +
                "app_food_code='" + app_food_code + '\'' +
                ", name='" + name + '\'' +
                ", sku_id='" + sku_id + '\'' +
                ", count=" + count +
                ", originPrice=" + originPrice +
                ", totalOriginPrice=" + totalOriginPrice +
                ", activityPrice=" + activityPrice +
                ", totalActivityPrice=" + totalActivityPrice +
                ", totalReducePrice=" + totalReducePrice +
                ", boxNumber=" + boxNumber +
                ", boxPrice=" + boxPrice +
                ", totalBoxPrice=" + totalBoxPrice +
                ", totalMtCharge=" + totalMtCharge +
                ", totalPoiCharge=" + totalPoiCharge +
                ", upc='" + upc + '\'' +
                ", wmAppOrderActDetails=" + wmAppOrderActDetails +
                ", app_medicine_code='" + app_medicine_code + '\'' +
                '}';
    }
}
