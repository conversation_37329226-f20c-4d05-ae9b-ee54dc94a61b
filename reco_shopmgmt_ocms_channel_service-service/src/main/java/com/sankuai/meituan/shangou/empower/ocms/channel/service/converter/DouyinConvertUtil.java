package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelMaterialSearchResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelMaterialUploadResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelQualificationDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelSpuCreateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinBrandListResult.Brand;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.SpecDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.GetDistributedProductDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelMaterialDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ChannelProductUpdateRuleDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialUploadResult;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin.DouyinConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.AttrValueInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BrandInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.MatchableRule;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QualificationConfig;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RuleClause;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuSellStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.util.*;
import java.util.stream.Collectors;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoSortItemDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.CategoryAttrValueTypeEnum;

/**
 * @Author: luokai14
 * @Date: 2023/12/18 6:45 下午
 * @Mail: <EMAIL>
 */
public class DouyinConvertUtil {


    private final static int DOU_YIN_PIC_SIZE = 5;
    private final static long DOU_YIN_DEFAULT_BRAND_ID = 596120136L;
    /**
     * 承诺发货时间
     */
    private final static int DELIVERY_DELAY_DAY = 2;

    public static ChannelSpuCreateDTO buildMerchantSpuCreateDTO(MerchantSpuDTO merchantSpuDTO, BaseRequest baseRequest){

        ChannelSpuCreateDTO channelSpuCreateDTO = new ChannelSpuCreateDTO();
        // 1 名称
        channelSpuCreateDTO.setName(merchantSpuDTO.getName());
        // 2 自定义外键
        channelSpuCreateDTO.setOuter_product_id(merchantSpuDTO.getCustomSpuId());
        if (StringUtils.isNotEmpty(merchantSpuDTO.getChannelSpuId())) {
            channelSpuCreateDTO.setProduct_id(merchantSpuDTO.getChannelSpuId());
        }
        // 3 商品类型
        channelSpuCreateDTO.setProduct_type(merchantSpuDTO.getProductType());
        // 4 渠道类目id
        channelSpuCreateDTO.setCategory_leaf_id(merchantSpuDTO.getChannelCategoryId());
        // 5 卖点
        channelSpuCreateDTO.setRecommend_remark(merchantSpuDTO.getSellPoint());
        // 6 抖音地址图片
        if (CollectionUtils.isNotEmpty(merchantSpuDTO.getPictureChannelUrlList())) {

            // 图片去重
            List<String> distinctList = Fun.distinct(merchantSpuDTO.getPictureChannelUrlList(), url -> url);
            // 使用前5张
            List<String> subList = distinctList;
            if (distinctList.size() > DOU_YIN_PIC_SIZE) {
                subList = distinctList.subList(0, DOU_YIN_PIC_SIZE);
            }
            String urlStr = subList.stream()
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.joining("|"));
            channelSpuCreateDTO.setPic(urlStr);
        }
        // 7 抖音地址图片详情-使用图片详情填充-总图片数量不得超过50张
        if (CollectionUtils.isNotEmpty(merchantSpuDTO.getPictureContentChannelUrlList())) {
            String picContentUrlStr = merchantSpuDTO.getPictureContentChannelUrlList()
                    .stream()
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.joining("|"));
            channelSpuCreateDTO.setDescription(picContentUrlStr);
        }
        // 8 减库存类型
        channelSpuCreateDTO.setReduce_type(merchantSpuDTO.getReduceType());
        // 9 运费模板id
        channelSpuCreateDTO.setFreight_id(merchantSpuDTO.getFreightId());
        // 10 商家号码
        channelSpuCreateDTO.setMobile(merchantSpuDTO.getMobile());
        // 11 是否审核
        channelSpuCreateDTO.setCommit(merchantSpuDTO.getCommit());
        // 12 资质
        channelSpuCreateDTO.setQuality_list(Fun.map(merchantSpuDTO.getQualityList(), ChannelQualificationDTO::of));
        // 13 规格
        List<SpecDTO> specDTOS = Fun.map(merchantSpuDTO.getSkuList(), SpecDTO::of);
        /*if (CollectionUtils.isNotEmpty(specDTOS)) {
            channelSpuCreateDTO.setSpec_prices(JacksonUtils.toJson(specDTOS));
        }*/
        if (CollectionUtils.isNotEmpty(specDTOS)) {
            channelSpuCreateDTO.setSpec_prices_v2(JacksonUtils.toJson(specDTOS));
        }
        // 14 规格名
        channelSpuCreateDTO.setSpec_info(JacksonUtils.toJson(SpecInfoDTO.of(merchantSpuDTO.getSkuList())));

        // channelSpuCreateDTO.setSpecs(getSpecs(merchantSpuDTO.getSkuList()));
        // 15 渠道类目属性
        if (StringUtils.isNotEmpty(merchantSpuDTO.getCategoryProperties())) {
            channelSpuCreateDTO.setProduct_format_new(merchantSpuDTO.getCategoryProperties());
        }
        // 16 售后属性
        if (MapUtils.isNotEmpty(merchantSpuDTO.getAfterSaleService())) {
            channelSpuCreateDTO.setAfter_sale_service(merchantSpuDTO.getAfterSaleService());
        }
        // 17 抖音视频id
        if (StringUtils.isNotEmpty(merchantSpuDTO.getChannelVideoId())) {
            channelSpuCreateDTO.setMaterial_video_id(merchantSpuDTO.getChannelVideoId());
        }

        // 18 品牌
        if (StringUtils.isNotEmpty(merchantSpuDTO.getBrandCode())) {
            channelSpuCreateDTO.setStandard_brand_id(Long.valueOf(merchantSpuDTO.getBrandCode()));
        } else {
            // 抖音接口文档:品牌id，通过/brand/list获取，无品牌id则传596120136
            channelSpuCreateDTO.setStandard_brand_id(DouyinConstant.DOU_YIN_DEFAULT_BRAND_ID);
        }

        // 19 承诺发货时间
        if (MccConfigUtil.getDouyinDeliveryDelayDayDefaultSwitch(baseRequest.getTenantId())) {
            channelSpuCreateDTO.setDelivery_delay_day(DELIVERY_DELAY_DAY);
        }


        return channelSpuCreateDTO;
    }

    public static CategoryInfoUpdateDTO buildCategoryUpdateDTO(CategoryInfoSortItemDTO categoryInfoSortItemDTO, String parentCode, String channelParentCategoryCode){

        CategoryInfoUpdateDTO categoryInfoUpdateDTO = new CategoryInfoUpdateDTO();
        categoryInfoUpdateDTO.setCode(categoryInfoSortItemDTO.getCode());
        categoryInfoUpdateDTO.setParentCode(parentCode);
        categoryInfoUpdateDTO.setChannelCategoryCode(categoryInfoSortItemDTO.getChannelCategoryCode());
        // 父渠道分类编码为0时传null值（一级分类排序的情况下，父渠道分类编码传0会导致排序失效）
        categoryInfoUpdateDTO.setChannelParentCategoryCode(Objects.equals(ProjectConstant.PARENT_CATEGORY_CODE_ZERO, channelParentCategoryCode) ? null : channelParentCategoryCode);
        categoryInfoUpdateDTO.setName(categoryInfoSortItemDTO.getName());
        categoryInfoUpdateDTO.setSort(categoryInfoSortItemDTO.getSort());
        return categoryInfoUpdateDTO;
    }


    private static String getSpecs(List<MerchantSkuDTO> skuDTOList){
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return "";
        }
        String specNameStr = skuDTOList.stream()
                .map(MerchantSkuDTO::getSpecValueName)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining(","));
        return String.format("规格|%s", specNameStr);

    }

    public static MaterialUploadResult convertToMaterialUploadResult(ChannelMaterialUploadResult originMaterialUploadResult,
                                                                     String materialType) {
        if (Objects.isNull(originMaterialUploadResult)) {
            return new MaterialUploadResult();
        }
        Map<String, ChannelMaterialUploadResult.MaterialUploadSuccessResult> originSuccessMap = originMaterialUploadResult.getSuccess_map();
        Map<String, ChannelMaterialUploadResult.MaterialUploadFailedResult> originFailedMap = originMaterialUploadResult.getFailed_map();
        List<ChannelMaterialDto> successList = new ArrayList<>();
        List<ChannelMaterialDto> failedList = new ArrayList<>();
        if (MapUtils.isNotEmpty(originSuccessMap)) {
            successList.addAll(originSuccessMap.entrySet()
                    .stream()
                    .map(it -> convertToUploadSuccessChannelMaterialDto(it, materialType))
                    .collect(Collectors.toList()));
        }
        if (MapUtils.isNotEmpty(originFailedMap)) {
            failedList.addAll(originFailedMap.entrySet()
                    .stream()
                    .map(it -> convertToUploadFailedChannelMaterialDto(it, materialType))
                    .collect(Collectors.toList()));
        }
        return MaterialUploadResult.builder()
                .successUploadList(successList)
                .failedUploadList(failedList)
                .build();
    }

    private static ChannelMaterialDto convertToUploadSuccessChannelMaterialDto(Map.Entry<String,
            ChannelMaterialUploadResult.MaterialUploadSuccessResult> entry, String materialType) {
        return ChannelMaterialDto.builder()
                .requestId(entry.getKey())
                .materialType(materialType)
                .materialId(entry.getValue().getMaterialId())
                .name(entry.getValue().getName())
                .folderId(entry.getValue().getFolderId())
                .originUrl(entry.getValue().getOriginUrl())
                .byteUrl(entry.getValue().getByteUrl())
                .auditStatus(entry.getValue().getAuditStatus())
                .isNew(entry.getValue().getIsNew())
                .build();
    }

    private static ChannelMaterialDto convertToUploadFailedChannelMaterialDto(Map.Entry<String,
            ChannelMaterialUploadResult.MaterialUploadFailedResult> entry, String materialType) {
        return ChannelMaterialDto.builder()
                .requestId(entry.getKey())
                .materialType(materialType)
                .errCode(entry.getValue().getErrCode())
                .errMsg(entry.getValue().getErrMsg())
                .build();
    }

    public static List<ChannelMaterialDto> convertToMaterialList(List<ChannelMaterialSearchResult.MaterialInfo> originMaterialList) {
        if (CollectionUtils.isEmpty(originMaterialList)) {
            return Collections.emptyList();
        }
        return Fun.map(originMaterialList, materialInfo -> ChannelMaterialDto.builder()
                .materialId(materialInfo.getMaterial_id())
                .materialType(materialInfo.getMaterial_type())
                .name(materialInfo.getMaterial_name())
                .folderId(materialInfo.getFolder_id())
                .originUrl(materialInfo.getOrigin_url())
                .byteUrl(materialInfo.getByte_url())
                .auditStatus(materialInfo.getAudit_status())
                .auditRejectDesc(materialInfo.getAudit_reject_desc())
                .operateStatus(materialInfo.getOperate_status())
                .vid(Optional.ofNullable(materialInfo.getVideoInfo())
                        .map(ChannelMaterialSearchResult.MaterialInfo.VideoInfo::getVid)
                        .orElse(null))
                .build());
    }

    public static GetDistributedProductDTO buildGetDistributedProductDTO(SpuInfoDTO spuInfoDTO, Long storeId) {
        GetDistributedProductDTO getDistributedProductDTO = new GetDistributedProductDTO();
        getDistributedProductDTO.setMain_product_id(Long.valueOf(spuInfoDTO.getMerchantChannelSpuId()));
        getDistributedProductDTO.setStore_ids(Lists.newArrayList(storeId));
        getDistributedProductDTO.setPage(0L);
        getDistributedProductDTO.setSize(10L);
        return getDistributedProductDTO;
    }

    public static CatInfo buildCatInfo(ChannelCategoryQueryResult.CategoryInfoResult categoryInfoResult, Long parentCategoryId, String parentCategoryName) {
        CatInfo catInfo = new CatInfo();
        catInfo.setCatId(String.valueOf(categoryInfoResult.getCategory_id()));
        catInfo.setName(categoryInfoResult.getCategory_name());
        catInfo.setSequence(Integer.parseInt(categoryInfoResult.getRank().toString()));
        catInfo.setParentId(Optional.ofNullable(parentCategoryId).map(String::valueOf).orElse(null));
        catInfo.setParentName(parentCategoryName);
        catInfo.setIsLeaf(parentCategoryId != null);
        catInfo.setDepth(parentCategoryId == null ? 1 : 2);
        return catInfo;
    }


    public static CatInfo buildCatInfo(ChannelShopCategoryQueryResult categoryInfoResult) {
        CatInfo catInfo = new CatInfo();
        catInfo.setCatId(String.valueOf(categoryInfoResult.getId()));
        catInfo.setName(categoryInfoResult.getName());
        catInfo.setParentId(Optional.ofNullable(categoryInfoResult.getParent_id()).map(String::valueOf).orElse(null));
        catInfo.setIsLeaf(categoryInfoResult.is_leaf());
        catInfo.setDepth((int)categoryInfoResult.getLevel());
        return catInfo;
    }

    public static ChannelCreateSubProductDTO spuCreateMapping(SpuInfoDTO spuInfoDTO, Long channelStoreId) {
        if (spuInfoDTO == null) {
            return null;
        }
        ChannelCreateSubProductDTO channelCreateSubProductDTO = new ChannelCreateSubProductDTO();
        channelCreateSubProductDTO.setMain_product_id(spuInfoDTO.getMerchantChannelSpuId());
        channelCreateSubProductDTO.setStore_id(channelStoreId);
        return channelCreateSubProductDTO;
    }

    public static ChannelSpuShelfDTO spuOnShelfMapping(SingleSpuSellStatusDTO spuInfoDTO) {
        if (spuInfoDTO == null) {
            return null;
        }
        ChannelSpuShelfDTO channelSpuShelfDTO = new ChannelSpuShelfDTO();
        channelSpuShelfDTO.setProduct_id(spuInfoDTO.getChannelSpuId());

        return channelSpuShelfDTO;
    }

    public static ChannelSpuShelfDTO spuOffShelfMapping(SingleSpuSellStatusDTO spuInfoDTO) {
        if (spuInfoDTO == null) {
            return null;
        }
        ChannelSpuShelfDTO channelSpuShelfDTO = new ChannelSpuShelfDTO();
        channelSpuShelfDTO.setProduct_id(spuInfoDTO.getChannelSpuId());
        return channelSpuShelfDTO;
    }

    public static ChannelEditSkuPriceDTO editSkuPriceMapping(SkuPriceDTO skuPriceDTO) {
        if (skuPriceDTO == null) {
            return null;
        }
        ChannelEditSkuPriceDTO channelEditSkuPriceDTO = new ChannelEditSkuPriceDTO();
        channelEditSkuPriceDTO.setPrice((long) MoneyUtils.yuanToFen(skuPriceDTO.getPrice()));
        channelEditSkuPriceDTO.setSku_id(Long.valueOf(skuPriceDTO.getChannelSkuId()));
        channelEditSkuPriceDTO.setProduct_id(Long.valueOf(skuPriceDTO.getChannelSpuId()));
        return channelEditSkuPriceDTO;
    }

    public static CategoryAttrInfo buildCatProp(DouyinCategoryPropDetailDTO douyinCategoryPropDetailDTO) {
        CategoryAttrInfo categoryAttrInfo = new CategoryAttrInfo();
        categoryAttrInfo.setAttrId(String.valueOf(douyinCategoryPropDetailDTO.getProperty_id()));
        categoryAttrInfo.setAttrName(douyinCategoryPropDetailDTO.getProperty_name());
        switch(douyinCategoryPropDetailDTO.getType()){
            case "text":
                categoryAttrInfo.setAttrValueType(CategoryAttrValueTypeEnum.TEXT.getCode());
                break;
            case "select":
                categoryAttrInfo.setAttrValueType(CategoryAttrValueTypeEnum.SINGLE_SELECTOR.getCode());
                break;
            case "multi_select":
                categoryAttrInfo.setAttrValueType(CategoryAttrValueTypeEnum.MULTI_SELECTOR.getCode());
                break;
            case "timestamp":
                categoryAttrInfo.setAttrValueType(CategoryAttrValueTypeEnum.TIMESTAMP.getCode());
                break;
            case "timerange":
                categoryAttrInfo.setAttrValueType(CategoryAttrValueTypeEnum.TIME_RANGE.getCode());
                break;
            default:
                categoryAttrInfo.setAttrValueType(CategoryAttrValueTypeEnum.TEXT.getCode());
        }
        categoryAttrInfo.setNeed(Objects.equals(douyinCategoryPropDetailDTO.getRequired(),1L)?"1":"2");
        categoryAttrInfo.setMultiSelectMax(douyinCategoryPropDetailDTO.getMulti_select_max());
        categoryAttrInfo.setPropertyType(douyinCategoryPropDetailDTO.getProperty_type());
        categoryAttrInfo.setSequence(douyinCategoryPropDetailDTO.getSequence().intValue());
        categoryAttrInfo.setDiyType(douyinCategoryPropDetailDTO.getDiy_type());
        categoryAttrInfo.setImportantType(douyinCategoryPropDetailDTO.getImportant_type());

        if(CollectionUtils.isNotEmpty(douyinCategoryPropDetailDTO.getOptions())){
            categoryAttrInfo.setValueList(douyinCategoryPropDetailDTO.getOptions().stream().map(DouyinConvertUtil::buildCatPropOption).collect(Collectors.toList()));
        }
        return categoryAttrInfo;
    }

    public static AttrValueInfo buildCatPropOption(DouyinCategoryPropOptionDTO douyinCategoryPropOptionDTO){
        AttrValueInfo attrValueInfo = new AttrValueInfo();
        attrValueInfo.setName(douyinCategoryPropOptionDTO.getName());
        attrValueInfo.setValue(douyinCategoryPropOptionDTO.getName());
        attrValueInfo.setValueId(String.valueOf(douyinCategoryPropOptionDTO.getValue_id()));
        attrValueInfo.setSequence(douyinCategoryPropOptionDTO.getSequence().intValue());
        return attrValueInfo;
    }

    public static QualificationConfig buildQulifactionInfo(DouyinQualificationDTO douyinQualificationDTO) {
        if(douyinQualificationDTO == null){
            return null;
        }
        QualificationConfig qualificationConfig = new QualificationConfig();
        qualificationConfig.setKey(douyinQualificationDTO.getKey());
        qualificationConfig.setName(douyinQualificationDTO.getName());
        qualificationConfig.setTextList(douyinQualificationDTO.getText_list());
        qualificationConfig.setIsRequired(douyinQualificationDTO.is_required());
        if(CollectionUtils.isEmpty(douyinQualificationDTO.getMatchable_rule())){
            return qualificationConfig;
        }

        List<MatchableRule> matchableRuleList = douyinQualificationDTO.getMatchable_rule().stream().map(e->{
            MatchableRule matchableRule = new MatchableRule();
            matchableRule.setIsQualificationRequired(e.is_qualification_required());
            if(MapUtils.isEmpty(e.getRule_clause())){
                return matchableRule;
            }
            Map<Long,RuleClause> ruleClauseMap = new HashMap<>();
            e.getRule_clause().entrySet().stream().forEach(entry->{
                RuleClause ruleClause = new RuleClause();
                ruleClause.setPropertyValues(entry.getValue().getProperty_values());
                ruleClause.setOperandStr(entry.getValue().getOperand_str());
                ruleClauseMap.put(entry.getKey(),ruleClause);
            });
            return matchableRule;
        }).collect(Collectors.toList());
        return qualificationConfig.setMatchableRuleList(matchableRuleList);
    }

    public static BrandInfo buildBrandInfo( Brand brand) {
        if(brand == null){
            return null;
        }
        BrandInfo brandInfo = new BrandInfo();
        brandInfo.setBrandId(String.valueOf(brand.getBrand_id()));
        brandInfo.setBrandName(brand.getName());
        return brandInfo;
    }

    public static ChannelProductUpdateRuleDTO convertToChannelProductUpdateRuleDTO(DouyinProductUpdateRuleResult coreData) {
        if(coreData == null){
            return null;
        }
        ChannelProductUpdateRuleDTO channelProductUpdateRuleDTO = new ChannelProductUpdateRuleDTO();
        if(coreData.getAfter_sale_rule()==null){
            return channelProductUpdateRuleDTO;
        }

        if(coreData.getAfter_sale_rule().getSupply_day_return_rule() == null){
            return channelProductUpdateRuleDTO;
        }

        ChannelProductUpdateRuleDTO.SupplyDayReturnRuleDTO  supplyDayReturnRuleDTO = new ChannelProductUpdateRuleDTO.SupplyDayReturnRuleDTO();
        supplyDayReturnRuleDTO.setEnable(coreData.getAfter_sale_rule().getSupply_day_return_rule().isEnable());
        supplyDayReturnRuleDTO.setOptions(
                Fun.map(coreData.getAfter_sale_rule().getSupply_day_return_rule().getOptions(),
                op->{
                    ChannelProductUpdateRuleDTO.OptionDTO optionDTO = new ChannelProductUpdateRuleDTO.OptionDTO();
                    optionDTO.setName(op.getName());
                    optionDTO.setValue(op.getValue());
                    return optionDTO;
                }));

        ChannelProductUpdateRuleDTO.AfterSaleRuleDTO  afterSaleRuleDTO = new ChannelProductUpdateRuleDTO.AfterSaleRuleDTO();
        afterSaleRuleDTO.setSupplyDayReturnRule(supplyDayReturnRuleDTO);
        channelProductUpdateRuleDTO.setAfterSaleRule(afterSaleRuleDTO);
        return channelProductUpdateRuleDTO;
    }

    public static List<CategoryAttrInfo> buildSaleAttrList(DouyinProductUpdateRuleResult.ProductSpecRule productSpecRule) {
        if (productSpecRule == null || CollectionUtils.isEmpty(productSpecRule.getRequired_spec_details())) {
            return Collections.emptyList();
        }
        return Fun.map(productSpecRule.getRequired_spec_details(), detail -> {
            List<AttrValueInfo> valueList = Fun.map(detail.getProperty_values(), value -> {
                AttrValueInfo attrValueInfo = new AttrValueInfo();
                attrValueInfo.setValueId(String.valueOf(value.getSell_property_value_id()));
                attrValueInfo.setValue(value.getSell_property_value_name());
                return attrValueInfo;
            });
            CategoryAttrInfo categoryAttrInfo = new CategoryAttrInfo();
            categoryAttrInfo.setAttrId(String.valueOf(detail.getSell_property_id()));
            categoryAttrInfo.setAttrName(detail.getSell_property_name());
            categoryAttrInfo.setValueList(valueList);
            categoryAttrInfo.setNeed(BooleanUtils.isTrue(detail.getIs_required()) ? "1" : "2");
            categoryAttrInfo.setSupportExtend(BooleanUtils.isTrue(detail.getSupport_diy()) ? "1" : "0");
            categoryAttrInfo.setDiyType(BooleanUtils.isTrue(detail.getSupport_diy()) ? 1L : 0L);
            return categoryAttrInfo;
        });
    }
}
