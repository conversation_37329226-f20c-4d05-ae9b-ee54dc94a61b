package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzan;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.RefundGoodsRequest;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AddressExtra;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.ExpressTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.SkuProperty;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.Specification;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.TradeCommonMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.YzOrderStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelNotifyEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.YzConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.PayStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.PayTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.AgreeRefundRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.CalculateRefundRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.CalculateRefundResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ChannelGiftInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ChannelOrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ChannelPartRefundGoodsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ChannelPartRefundGoodsResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetOrderAfsApplyListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetOrderAfsApplyListResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetOrderStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetOrderStatusResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GoodsActivityDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GoodsSettlementInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GoodsSettlementResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderDeliveryDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderDiscountDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderInvoiceDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderProductDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderShouldSettlementInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderShouldSettlementResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiAdjustOrderRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiAdjustOrderResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiCancelOrderRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiConfirmOrderRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiConfirmReceiveGoodsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiPartRefundRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PreparationMealCompleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.QueryChannelAbnormalOrderRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.QueryChannelAbnormalOrderResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.QueryChannelOrderListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.QueryChannelOrderListResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.RejectRefundRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.SelfDeliveryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.UpdateDeliveryInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.UpdateOrderDeliveryStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.OrderStatusConverter;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.UrlUtil;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelOrderStatus;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.json.GsonUtils;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.gen.v4_0_0.api.YouzanTradeGet;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanTradeGetParams;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanTradeGetResult;
import com.youzan.cloud.open.security.SecretClient;

import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/06/03 16:28
 * 原有赞不被使用，新有赞使用yzToolChannelOrderService
 */
@Deprecated
@Slf4j
@Service("yzChannelOrderService")
public class YzChannelOrderServiceImpl extends YouZanBaseService implements ChannelOrderService {

    private static final int SKU_PROPERTY_STEP = 2;
    private static final long MIN = TimeUnit.MINUTES.toMillis(1);

    @Autowired
    private YzConverterService converterService;
    @Autowired
    private CopChannelStoreService copChannelStoreService;
    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Override
    public ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request) {
        return null;
    }

    @Override
    public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
        GetChannelOrderDetailResult result = new GetChannelOrderDetailResult();
        try {
            AppMessage appMessage = getAppMessage(request.getTenantId(), request.getSotreId());
            //和有赞技术沟通，有赞订单token为门店维度，每个门店需要单独维护token
            YouzanTradeGetParams tradeGetParams = new YouzanTradeGetParams();
            tradeGetParams.setTid(request.getOrderId());
            YouzanTradeGetResult tradeGetResult = getResult4YouZanWithRetry(appMessage, new YouzanTradeGet(tradeGetParams), YouzanTradeGetResult.class);
            decryptOrderMessage(appMessage, tradeGetResult.getData());
            log.info("YzChannelOrderServiceImpl.getChannelOrderDetail, request:{}, result:{}", request,
                    GsonUtils.toJSONString(tradeGetResult));
            ChannelOrderDetailDTO channelOrderDetailDTO = getChannelOrderDetailDTO(tradeGetResult.getData());
            channelOrderDetailDTO.setStoreId(request.getSotreId());
            return result.setStatus(ResultGenerator.genSuccessResult())
                    .setChannelOrderDetail(channelOrderDetailDTO);
        }
        catch (SDKException e) {
            log.error("search order detail error, request->{}, exception:", request, e);
            return result.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST));

        }
        catch (Exception e) {
            log.error("order detail error, exception:", e);
            return result.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST));
        }
    }

    @Override
    public ResultStatus preparationMealComplete(PreparationMealCompleteRequest request) {
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request) {
        return null;
    }

    @Override
    public GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request) {
        return null;
    }

    @Override
    public ResultStatus agreeRefund(AgreeRefundRequest request) {
        return null;
    }

    @Override
    public ResultStatus rejectRefund(RejectRefundRequest request) {
        return null;
    }

    @Override
    public ResultStatus refundGoods(RefundGoodsRequest request) {
        return null;
    }

    @Override
    public ResultStatus poiCancelOrder(PoiCancelOrderRequest request) {
        return null;
    }

    @Override
    public ResultStatus poiPartRefundApply(PoiPartRefundRequest request) {
        return null;
    }

    @Override
    public CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request) {
        return null;
    }

    @Override
    public ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request) {
        return null;
    }

    @Override
    public PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request) {
        return null;
    }

    @Override
    public OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request) {
        return null;
    }

    @Override
    public GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request) {
        return null;
    }

    @Override
    public GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request) {
        return null;
    }

    /**
     * 注意：有赞仅同步订单发货，即骑手已取货
     * 由旭日系统去调用有赞订单发货接口去变更发货状态，我们暂时不调用（代码保留）
     */
    @Override
    public ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request) {
        return buildSuccessResultStatus();
//        try {
//            AppMessage appMessage = getAppMessage(request.getTenantId(), request.getStoreId());
//            String accessToken = accessTokenService.getAccessToken(appMessage);
//            int status = request.getStatus();
//            switch (status) {
//                case 50: {
//                    YouzanLogisticsOnlineConfirm youzanLogisticsOnlineConfirm = new YouzanLogisticsOnlineConfirm();
//                    YouzanLogisticsOnlineConfirmParams params = new YouzanLogisticsOnlineConfirmParams();
//                    params.setTid(request.getOrderId());
//                    youzanLogisticsOnlineConfirm.setAPIParams(params);
//                    log.info("begin invoke youzanLogisticsOnlineConfirm,param = {},accessToken = {}", params, accessToken);
//                    YouzanLogisticsOnlineConfirmResult result = yzClient.invoke(youzanLogisticsOnlineConfirm, new Token(accessToken), YouzanLogisticsOnlineConfirmResult.class);
//                    log.info("finish invoke youzanLogisticsOnlineConfirm,param = {},accessToken = {}, result = {}", params, accessToken, result);
//                    if (!result.getSuccess()) {
//                        return new ResultStatus().setCode(ResultCode.CHANNEL_SYSTEM_ERROR.getCode()).setMsg(ResultCode.CHANNEL_SYSTEM_ERROR.getMsg());
//                    }
//                    return buildSuccessResultStatus();
//                }
//                default: {
//                    log.warn("YzChannelOrderServiceImpl.updateOrderDeliveryStatus  无法处理配送状态:{}", status);
//                    //对于非法状态不做处理
//                    return buildSuccessResultStatus();
//                }
//            }
//        }
//        catch (SDKException e) {
//            log.error("YzChannelOrderServiceImpl.updateOrderDeliveryStatus throw SDKException", e);
//            return new ResultStatus().setCode(ResultCode.CHANNEL_SYSTEM_ERROR.getCode()).setMsg(ResultCode.CHANNEL_SYSTEM_ERROR.getMsg());
//        }
//        catch (Exception e) {
//            log.error("YzChannelOrderServiceImpl.updateOrderDeliveryStatus throw exception", e);
//            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg());
//        }
    }

    @Override
    public ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request) {
        //渠道无对应接口，直接返回成功
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ResultCode.SUCCESS.getCode());
        resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
        return resultStatus;
    }

    @Override
    public ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request) {
        return updateDeliveryInfo(request);
    }

    @Override
    public ResultStatus selfDelivery(SelfDeliveryRequest request) {
        return null;
    }

    @Override
    public ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request) {
        return new ResultStatus(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(), ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg(), null);
    }

    @Override
    public QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request) {
        return null;
    }

    @Override
    public ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request) {
        return null;
    }

    @Override
    public QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request) {
        return null;
    }

    @Override
    public QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request) {
        return null;
    }

    @Override
    public ChannelOrderDetailDTO getOrderDetail4ChannelMessage(ChannelTypeEnum channelType, ChannelNotifyEnum channelNotify,
                                                               Long tenantId, String msg) {
        if (ChannelTypeEnum.YOU_ZAN != channelType) {
            return null;
        }
        TradeCommonMessage<String> tradMsg = JSON.parseObject(msg, new TypeReference<TradeCommonMessage<String>>() {
        });
        String message = UrlUtil.urlDecodeSafe(tradMsg.getMsg());
        YouzanTradeGetResult.YouzanTradeGetResultData createMsg = JSON.parseObject(message,
                new TypeReference<YouzanTradeGetResult.YouzanTradeGetResultData>() {
                });
        int status = OrderStatusConverter.yzOrderStatusMapping(tradMsg.getStatus());
        Long storeId = copChannelStoreService.selectChannelStoreId(tenantId, channelType.getCode(),
                String.valueOf(tradMsg.getChannelPoiCode()));
        // 隐私数据解密
        decryptOrderMessage(getAppMessage(tenantId, storeId), createMsg);
        ChannelOrderDetailDTO channelOrderDetailDTO = getChannelOrderDetailDTO(createMsg);
        channelOrderDetailDTO.setChannelOrderStatus(status);
        channelOrderDetailDTO.setStoreId(storeId);
        channelOrderDetailDTO.setOriginalPoiId(String.valueOf(tradMsg.getChannelPoiCode()));
        channelOrderDetailDTO.setChannelStoreName(tradMsg.getChannelStoreName());
        return channelOrderDetailDTO;
    }

    public void decryptOrderMessage(AppMessage appMessage,
                                    YouzanTradeGetResult.YouzanTradeGetResultData tradeGetResultData) {
        if (tradeGetResultData == null) {
            return;
        }
        YouzanTradeGetResult.YouzanTradeGetResultFullorderinfo fullOrderInfo = tradeGetResultData.getFullOrderInfo();
        if (fullOrderInfo == null) {
            return;
        }
        SecretClient clientSecret = getAccessTokenService().getClientSecret(appMessage);
        YouzanTradeGetResult.YouzanTradeGetResultAddressinfo addressInfo = fullOrderInfo.getAddressInfo();
        if (addressInfo != null) {
            addressInfo.setSelfFetchInfo(decrypt(clientSecret, appMessage.getGrantId(), addressInfo.getSelfFetchInfo()));
            addressInfo.setReceiverName(decrypt(clientSecret, appMessage.getGrantId(), addressInfo.getReceiverName()));
            addressInfo.setReceiverTel(decrypt(clientSecret, appMessage.getGrantId(), addressInfo.getReceiverTel()));
            addressInfo.setDeliveryAddress(decrypt(clientSecret, appMessage.getGrantId(), addressInfo.getDeliveryAddress()));
        }
        YouzanTradeGetResult.YouzanTradeGetResultBuyerinfo buyerInfo = fullOrderInfo.getBuyerInfo();
        if (buyerInfo != null) {
            buyerInfo.setBuyerPhone(decrypt(clientSecret, appMessage.getGrantId(), buyerInfo.getBuyerPhone()));
        }
        YouzanTradeGetResult.YouzanTradeGetResultInvoiceinfo invoiceInfo = fullOrderInfo.getInvoiceInfo();
        if (invoiceInfo != null) {
            invoiceInfo.setEmail(decrypt(clientSecret, appMessage.getGrantId(), invoiceInfo.getEmail()));
        }
    }

    public ChannelOrderDetailDTO getChannelOrderDetailDTO(YouzanTradeGetResult.YouzanTradeGetResultData tradeGetResultData) {
        ChannelOrderDetailDTO detail = converterService.orderDetailMapping(tradeGetResultData);
        YouzanTradeGetResult.YouzanTradeGetResultOrderinfo orderInfo =
                Optional.ofNullable(tradeGetResultData).map(YouzanTradeGetResult.YouzanTradeGetResultData::getFullOrderInfo)
                        .map(YouzanTradeGetResult.YouzanTradeGetResultFullorderinfo::getOrderInfo).orElse(null);

        if (orderInfo == null) {
            throw new BizException(ResultCode.ORDER_NOT_EXIST.getCode(), "订单数据不完整 order info is null");
        }
        detail.setChannelOrderStatus(OrderStatusConverter.yzOrderStatusMapping(orderInfo.getStatus()));
        YouzanTradeGetResult.YouzanTradeGetResultPayinfo payInfo = tradeGetResultData.getFullOrderInfo().getPayInfo();
        if (payInfo == null && detail.getChannelOrderStatus() != ChannelOrderStatus.CANCELED.getValue()
                && detail.getChannelOrderStatus() >= ChannelOrderStatus.NEW_ORDER.getValue()) {
            throw new BizException(ResultCode.ORDER_NOT_EXIST.getCode(), "订单数据不完整 pay info is null");
        }
        if (payInfo != null) {
            detail.setActualPayAmt(MoneyUtils.yuanToFen(Optional.ofNullable(payInfo.getRealPayment())
                    .filter(StringUtils::isNotEmpty).orElse(payInfo.getPayment())));
            detail.setBizReceiveAmt(detail.getActualPayAmt());
            detail.setOriginalAmt(Math.addExact(MoneyUtils.nullableYuanToFen(payInfo.getTotalFee()),
                    MoneyUtils.nullableYuanToFen(payInfo.getPostFee())));
            detail.setFreight(MoneyUtils.nullableYuanToFen(payInfo.getPostFee()));
        }

        detail.setChannelOrderId(orderInfo.getTid());
        detail.setCreateTime(Optional.ofNullable(orderInfo.getCreated()).map(Date::getTime).orElse(System.currentTimeMillis()));
        // 渠道订单为终态设置订单完成时间
        detail.setCompletedTime((detail.getChannelOrderStatus() == ChannelOrderStatus.FINISHED.getValue()
                || detail.getChannelOrderStatus() == ChannelOrderStatus.CANCELED.getValue()) && detail.getActualPayAmt() > 0
                ? Optional.ofNullable(orderInfo.getSuccessTime()).map(Date::getTime).orElse(0L) : 0L);
        // 设置支付时间
        detail.setPayTime(Optional.ofNullable(tradeGetResultData.getFullOrderInfo())
                .map(e->e.getOrderInfo())
                .map(e->e.getPayTime())
                .map(Date::getTime)
                .orElse(0L));

        YouzanTradeGetResult.YouzanTradeGetResultInvoiceinfo invoiceInfo = tradeGetResultData.getFullOrderInfo().getInvoiceInfo();
        if (invoiceInfo != null) {
            detail.setIsNeedInvoice(true);
            OrderInvoiceDetailDTO orderInvoiceDetailDTO = new OrderInvoiceDetailDTO();
            orderInvoiceDetailDTO.setInvoiceTitle(invoiceInfo.getUserName());
            orderInvoiceDetailDTO.setTaxNo(invoiceInfo.getTaxpayerId());
            detail.setInvoiceDetail(orderInvoiceDetailDTO);
        }
        detail.setPayType(orderInfo.getPayType() != null && orderInfo.getPayType() != Constant.YOU_ZAN_CASH_PAY_CODE ?
                PayTypeEnum.ONLINE.getValue() : PayTypeEnum.CASH_DELIVERY.getValue());
        detail.setPayStatus(YzOrderStatusEnum.nameOf(orderInfo.getStatus()).ordinal() > 1 ?
                PayStatusEnum.PAID.getValue() : PayStatusEnum.UNPAID.getValue());
        detail.setOriginalPoiId(String.valueOf(orderInfo.getNodeKdtId()));
        List<YouzanTradeGetResult.YouzanTradeGetResultOrders> products = tradeGetResultData.getFullOrderInfo().getOrders();
        if (CollectionUtils.isEmpty(products)) {
            throw new BizException(ResultCode.ORDER_NOT_EXIST.getCode(), "订单数据不完整 orders info is null");
        }
        List<OrderProductDetailDTO> productDetailDTOList = new ArrayList<>(products.size());
        List<OrderProductDetailDTO> giftProductDTO = new ArrayList<>();
        for (YouzanTradeGetResult.YouzanTradeGetResultOrders product : products) {
            OrderProductDetailDTO orderProductDetailDTO = converterService.productMapping(product);
            orderProductDetailDTO.setWeight(StringUtils.isEmpty(product.getWeight()) ? 1000 : Long.parseLong(product.getWeight()));
            //规格
            String specification = orderProductDetailDTO.getSpecification();
            if (StringUtils.isNotEmpty(specification)) {
                List<String> specifications = JSON.parseObject(specification, new TypeReference<List<Specification>>() {
                }).stream().map(Specification::getSpecification).collect(Collectors.toList());
                orderProductDetailDTO.setSpecification(String.join(",", specifications));
            }
            //属性
            if (StringUtils.isNotEmpty(orderProductDetailDTO.getSkuProperty())) {
                List<SkuProperty> skuProperties = JSON.parseObject(orderProductDetailDTO.getSkuProperty(), new TypeReference<List<SkuProperty>>() {
                });
                if (skuProperties.size() % SKU_PROPERTY_STEP == 0) {
                    List<SkuProperty> realSkuProperties = new ArrayList<>(skuProperties.size() / SKU_PROPERTY_STEP);
                    for (int i = 0; i < skuProperties.size(); i++) {
                        SkuProperty skuProperty = skuProperties.get(i);
                        // 获取属性值、且跳过属性值对象遍历
                        skuProperty.setValue(skuProperties.get(++i).getValue());
                        realSkuProperties.add(skuProperty);
                    }
                    orderProductDetailDTO.setSkuProperty(realSkuProperties.stream()
                            .filter(v -> StringUtils.isNotEmpty(v.getName()) && StringUtils.isNotEmpty(v.getValue()))
                            .map(SkuProperty::getProperty).collect(Collectors.joining(",")));
                }
            }
            if (product.getIsPresent() != null && product.getIsPresent()) {
                giftProductDTO.add(orderProductDetailDTO);
            }
            if (product.getIsPresent() == null || !product.getIsPresent()) {
                productDetailDTOList.add(orderProductDetailDTO);
            }
        }
        detail.setSkuDetails(productDetailDTOList);
        setOrderActivities(tradeGetResultData, detail, productDetailDTOList, giftProductDTO);
        // 配送信息
        detail.setDeliveryDetail(setOrderDeliveryInfo(tradeGetResultData));
        // 预订单逻辑处理
        bookingDeal(detail, orderInfo);
        // 到店自提备注拼接
        if (ExpressTypeEnum.SELF_MENTION.getDesc().equals(detail.getDeliveryDetail().getDeliveryMethod())) {
            detail.setComment(StringUtils.isNotEmpty(detail.getComment()) ? detail.getComment().concat(" ").concat("到店自取") : "到店自取");
        }
        YouzanTradeGetResult.YouzanTradeGetResultBuyerinfo buyerInfo = tradeGetResultData.getFullOrderInfo().getBuyerInfo();
        //微信H5和微信小程序（有赞小程序和小程序插件）的订单会返回微信weixin_openid，
        // 三方App（有赞APP开店）的订单会返回open_user_id
        if (buyerInfo != null) {
            detail.setExtData(JSON.toJSONString(ImmutableMap.of("outerUserId", buyerInfo.getOuterUserId())));
        }
        if (detail.getChannelOrderStatus() > ChannelOrderStatus.NEW_ORDER.getValue() && detail.getActualPayAmt() > 0) {
            // 有赞订单详情没有订单确认时间、设置为支付时间后推1秒钟
            detail.setConfirmTime(Optional.ofNullable(orderInfo.getPayTime())
                    .map(time -> time.getTime() + TimeUnit.SECONDS.toMillis(1)).orElse(0L));
        }
        return detail;
    }

    private void bookingDeal(ChannelOrderDetailDTO detail, YouzanTradeGetResult.YouzanTradeGetResultOrderinfo orderInfo) {
        String reservationOrder = Optional.ofNullable(orderInfo.getOrderExtra()).
                map(YouzanTradeGetResult.YouzanTradeGetResultOrderextra::getIsReservationOrder).orElse(StringUtils.EMPTY);
        if (StringUtils.isEmpty(reservationOrder) && detail.getDeliveryDetail() != null) {
            //根据时间判断
            long intervalsTime = (detail.getDeliveryDetail().getArrivalTime() - detail.getCreateTime()) / MIN;
            int intervalsMin = MccConfigUtil.youzanDeliveryIntervalsMin();
            detail.setIsBooking(intervalsMin > 0 && intervalsTime > intervalsMin);
            return;
        }
        detail.setIsBooking(reservationOrder.equals("1"));
    }

    private OrderDeliveryDetailDTO setOrderDeliveryInfo(YouzanTradeGetResult.YouzanTradeGetResultData tradeGetResultData) {
        //地址信息完善
        //拼接规则delivery_address@delivery_province+delivery_city+delivery_district+delivery_address
        OrderDeliveryDetailDTO deliveryDetailDTO = new OrderDeliveryDetailDTO();
        Integer expressType = tradeGetResultData.getFullOrderInfo().getOrderInfo().getExpressType();
        ExpressTypeEnum expressTypeEnum = ExpressTypeEnum.codeOf(expressType);
        deliveryDetailDTO.setDeliveryMethod(expressTypeEnum.getDesc());
        deliveryDetailDTO.setIsSelfDelivery((expressTypeEnum == ExpressTypeEnum.KUAI_DI ||
                expressTypeEnum == ExpressTypeEnum.SAME_CITY_DELIVERY) ? 1 : 0);

        YouzanTradeGetResult.YouzanTradeGetResultAddressinfo addressInfo = tradeGetResultData.getFullOrderInfo().getAddressInfo();
        String address =
                new StringBuilder(StringUtils.EMPTY).append(Optional.ofNullable(addressInfo.getDeliveryAddress()).orElse(StringUtils.EMPTY))
                        .append("@#")
                        .append(Optional.ofNullable(addressInfo.getDeliveryProvince()).orElse(StringUtils.EMPTY))
                        .append(Optional.ofNullable(addressInfo.getDeliveryCity()).orElse(StringUtils.EMPTY))
                        .append(Optional.ofNullable(addressInfo.getDeliveryDistrict()).orElse(StringUtils.EMPTY))
                        .append(Optional.ofNullable(addressInfo.getDeliveryAddress()).orElse(StringUtils.EMPTY)).toString().trim();
        deliveryDetailDTO.setUserAddress(address);
        deliveryDetailDTO.setUserName(addressInfo.getReceiverName());
        deliveryDetailDTO.setUserPhone(addressInfo.getReceiverTel());
        deliveryDetailDTO.setUserPrivacyPhone(addressInfo.getReceiverTel());
        deliveryDetailDTO.setArrivalTime(Optional.ofNullable(addressInfo.getDeliveryStartTime())
                .map(Date::getTime).orElse(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(30)));
        deliveryDetailDTO.setArrivalEndTime(Optional.ofNullable(addressInfo.getDeliveryEndTime()).map(Date::getTime)
                .filter(time -> time >= deliveryDetailDTO.getArrivalTime()).orElse(deliveryDetailDTO.getArrivalTime()));
        String addressExtra = addressInfo.getAddressExtra();
        if (StringUtils.isNotEmpty(addressExtra)) {
            AddressExtra addressExtObj = JSON.parseObject(addressExtra, AddressExtra.class);
            deliveryDetailDTO.setLatitude(Optional.ofNullable(addressExtObj.getLatitude()).orElse(0D));
            deliveryDetailDTO.setLongitude(Optional.ofNullable(addressExtObj.getLongitude()).orElse(0D));
        }
        return deliveryDetailDTO;
    }

    private void setOrderActivities(YouzanTradeGetResult.YouzanTradeGetResultData tradeGetResultData,
                                    ChannelOrderDetailDTO detail, List<OrderProductDetailDTO> productDetailDTOList, List<OrderProductDetailDTO> giftProductDTO) {
        YouzanTradeGetResult.YouzanTradeGetResultOrderpromotion orderPromotion = tradeGetResultData.getOrderPromotion();
        detail.setActivities(new ArrayList<>());
        detail.setSkuSharedActivities(new ArrayList<>());
        if (orderPromotion == null) {
            return;
        }
        // 总活动金额=商品维度活动+订单维度活动金额
        int totalDis = Math.addExact(MoneyUtils.nullableYuanToFen(orderPromotion.getOrderDiscountFee()),
                MoneyUtils.nullableYuanToFen(orderPromotion.getItemDiscountFee()));
        detail.setTotalDiscount(totalDis);

        if (CollectionUtils.isNotEmpty(orderPromotion.getOrder())) {
            detail.setActivities(converterService.orderActivitiyInfosMapping(orderPromotion.getOrder()));
        }

        if (CollectionUtils.isNotEmpty(orderPromotion.getItem())) {
            for (YouzanTradeGetResult.YouzanTradeGetResultItem itemDis : orderPromotion.getItem()) {
                GoodsActivityDetailDTO goodsActivityDetailDTO = converterService.activityDetailMapping(itemDis);
                if (itemDis.getIsPresent() == null || !itemDis.getIsPresent()) {
                    OrderProductDetailDTO product =
                            setSkuInfo2ActivityInfoAndReturn(productDetailDTOList, itemDis.getOid(), goodsActivityDetailDTO);
                    goodsActivityDetailDTO.setTotalDiscount(Optional.ofNullable(itemDis.getPromotions())
                            .orElse(Collections.emptyList()).stream().mapToInt(promotion ->
                                    MoneyUtils.yuanToFen(promotion.getDiscountFee())).sum());
                    goodsActivityDetailDTO.setTenantCost(goodsActivityDetailDTO.getTotalDiscount());
                    if (product != null && Math.subtractExact(product.getOriginalPrice(),
                            goodsActivityDetailDTO.getTotalDiscount()) == product.getSalePrice()) {
                        // 设置商品活动价格=商品原价-商品优惠价格
                        goodsActivityDetailDTO.setActivityPrice(product.getSalePrice());
                        // 存在商品活动、将销售价格设置为原价、currentPrice会获取 activityPrice
                        product.setSalePrice(product.getOriginalPrice());
                    }
                    detail.getSkuSharedActivities().add(goodsActivityDetailDTO);
                }

                if (itemDis.getIsPresent() != null && itemDis.getIsPresent()) {
                    setSkuInfo2ActivityInfoAndReturn(giftProductDTO, itemDis.getOid(), goodsActivityDetailDTO);
                    OrderDiscountDetailDTO orderDiscountDetailDTO = new OrderDiscountDetailDTO();
                    OrderProductDetailDTO product = findProduct4List(giftProductDTO, itemDis.getOid());
                    goodsActivityDetailDTO.setTotalDiscount(Optional.ofNullable(itemDis.getPromotions())
                            .orElse(Collections.emptyList()).stream().mapToInt(promotion ->
                                    MoneyUtils.yuanToFen(promotion.getDiscountFee())).sum());
                    orderDiscountDetailDTO.setActDiscount(Optional.ofNullable(itemDis.getPromotions())
                            .orElse(Collections.emptyList()).stream().mapToInt(promotion ->
                                    MoneyUtils.yuanToFen(promotion.getDiscountFee())).sum());
                    orderDiscountDetailDTO.setBizCharge(goodsActivityDetailDTO.getTenantCost());
                    orderDiscountDetailDTO.setRemark("赠品");
                    ChannelGiftInfo giftInfo = new ChannelGiftInfo();
                    if (product != null) {
                        giftInfo.setName(product.getSkuName());
                        giftInfo.setSkuId(product.getSkuId());
                        giftInfo.setQuantity(product.getQuantity());
                        giftInfo.setSpu(product.getCustomSpu());
                        orderDiscountDetailDTO.setGiftInfo(giftInfo);
                        detail.getActivities().add(orderDiscountDetailDTO);
                    }
                }
            }
        }
    }

    private OrderProductDetailDTO setSkuInfo2ActivityInfoAndReturn(List<OrderProductDetailDTO> productDetailDTOSList, String channelItemId,
                                                                   GoodsActivityDetailDTO goodsActivityDetailDTO) {
        OrderProductDetailDTO product = findProduct4List(productDetailDTOSList, channelItemId);
        if (product != null) {
            goodsActivityDetailDTO.setCustomSkuId(product.getSkuId());
            goodsActivityDetailDTO.setSkuCount(product.getQuantity());
        }
        return product;
    }

    private OrderProductDetailDTO findProduct4List(List<OrderProductDetailDTO> productDetailDTOSList, String channelItemId) {
        return productDetailDTOSList.stream().filter(product -> product.getChannelItemId().equals(channelItemId)).findAny().orElse(null);
    }

    private AppMessage getAppMessage(Long tenantId, Long storeId) {
        CopAccessConfigDO tenantChannelConfig = copAccessConfigService.findTenantChannelConfig(tenantId, ChannelTypeEnum.YOU_ZAN.getCode());
        if (tenantChannelConfig == null) {
            throw new BizException(ResultCode.CHANNEL_APP_ID_INVALID.getCode(), "租户未绑定");
        }
        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(), storeId);
        if (channelStore == null) {
            throw new BizException(ResultCode.CHANNEL_POI_CODE_INVALID.getCode(), "门店未绑定");
        }
        return AppMessage.builder()
                .clientId(tenantChannelConfig.getTenantAppId())
                .grantId(channelStore.getChannelPoiCode())
                .clientSecret(JSON.parseObject(tenantChannelConfig.getSysParams()).getString(ProjectConstant.SECRET))
                .build();
    }


    private String decrypt(SecretClient secretClient, String grantId, String message) {
        try {
            if (StringUtils.isEmpty(message)) {
                return message;
            }
            if (secretClient.isEncrypt(message)) {
                return secretClient.decrypt(Long.parseLong(grantId), message);
            }
            return message;
        }
        catch (Exception e) {
            log.error("decrypt message error, message:{},exception:", message, e);
            return message;
        }
    }

    private ResultStatus buildSuccessResultStatus() {
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ResultCode.SUCCESS.getCode());
        resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
        return resultStatus;
    }


}
