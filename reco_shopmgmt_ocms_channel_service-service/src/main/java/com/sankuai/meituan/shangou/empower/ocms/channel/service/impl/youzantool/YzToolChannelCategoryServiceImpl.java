package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Result;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostYZEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCategoryService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.YzConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemSecondgroupQueryByupperid;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemSecondgroupQueryByupperidResult;
import com.youzan.cloud.open.sdk.gen.v3_0_0.api.YouzanItemcategoriesTagDelete;
import com.youzan.cloud.open.sdk.gen.v3_0_0.api.YouzanItemcategoriesTagUpdate;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanItemcategoriesTagDeleteResult;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanItemcategoriesTagUpdateResult;
import com.youzan.cloud.open.sdk.gen.v4_0_0.api.YouzanItemcategoriesTagAdd;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanItemcategoriesTagAddResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 作者：guohuqi
 * 时间：2022/12/8 2:39 PM
 * 功能：
 **/
@Slf4j
@Service("yzToolChannelCategoryService")
public class YzToolChannelCategoryServiceImpl extends YouZanToolBaseService implements ChannelCategoryService {

    @Resource(name = "yzCategoryThreadPool")
    private ExecutorService yzCategoryThreadPool;

    @Override
    public CreateCategoryResponse createCategory(CategoryRequest request) {

        CreateCategoryResponse resultData = new CreateCategoryResponse(ResultGenerator.genSuccessResult(), Lists.newArrayList());
        AppMessage appMessage = getMainAppMessage(request.getBaseInfo().getTenantId());

        // 上游接口已经按门店分组进行调用
        request.getParamList().stream()
                .forEach(categoryInfoDTO -> {
                    try {
                        YouzanItemcategoriesTagAdd youzanItemcategoriesTagAdd = YzConverterUtil.converterToYouzanItemcategoriesTagAdd(categoryInfoDTO);
                        YouzanItemcategoriesTagAddResult result = getResult4YouZanByRhino(ChannelPostYZEnum.CREATE_STORE_CATEGORY, appMessage, youzanItemcategoriesTagAdd, YouzanItemcategoriesTagAddResult.class);
                        log.info("创建分类信息结果：categoryInfoDTO {}, result {}.", categoryInfoDTO, JSON.toJSONString(result));
                        resultData.getData().add(YzConverterUtil.converterYouzanItemcategoriesTagAddResult(result, categoryInfoDTO, ""));
                    } catch (Exception e) {
                        log.error("调用有赞创建分类接口失败, categoryInfoDTO {}.", categoryInfoDTO, e);
                        CategoryPoiResult errorResult = YzConverterUtil.converterYouzanItemcategoriesTagAddResult(null, categoryInfoDTO, e.getMessage());
                        resultData.getData().add(YzConverterUtil.processErrorCode(errorResult, e));
                    }
                });

        return resultData;
    }

    @Override
    public UpdateCategoryResponse updateCategory(CategoryUpdateRequest request) {

        UpdateCategoryResponse resultData = new UpdateCategoryResponse(ResultGenerator.genSuccessResult(), Lists.newArrayList());
        AppMessage appMessage = getMainAppMessage(request.getBaseInfo().getTenantId());

        // 上游接口已经按门店分组进行调用
        request.getParamList().stream()
                .forEach(categoryInfoUpdateDTO -> {
                    try {
                        YouzanItemcategoriesTagUpdate youzanItemcategoriesTagUpdate = YzConverterUtil.converterToYouzanItemcategoriesTagUpdate(categoryInfoUpdateDTO);
                        YouzanItemcategoriesTagUpdateResult result = getResult4YouZanByRhino(ChannelPostYZEnum.UPDATE_STORE_CATEGORY,
                                appMessage, youzanItemcategoriesTagUpdate, YouzanItemcategoriesTagUpdateResult.class);
                        log.info("更新分类信息结果：categoryInfoUpdateDTO {}, result {}.", categoryInfoUpdateDTO, JSON.toJSONString(result));
                        resultData.getData().add(YzConverterUtil.converterYouzanItemcategoriesTagUpdateResult(result, categoryInfoUpdateDTO, ""));
                    } catch (Exception e) {
                        log.error("调用有赞更新分类接口失败, categoryInfoUpdateDTO {}.", categoryInfoUpdateDTO, e);
                        CategoryPoiResult errorResult = YzConverterUtil.converterYouzanItemcategoriesTagUpdateResult(null, categoryInfoUpdateDTO, e.getMessage());
                        resultData.getData().add(YzConverterUtil.processErrorCode(errorResult, e));
                    }
                });

        return resultData;
    }

    @Override
    public UpdateCategoryResponse deleteCategory(CategoryDeleteRequest request) {

        UpdateCategoryResponse resultData = new UpdateCategoryResponse(ResultGenerator.genSuccessResult(), Lists.newArrayList());
        AppMessage appMessage = getMainAppMessage(request.getBaseInfo().getTenantId());

        // 上游接口已经按门店分组进行调用
        request.getParamList().stream()
                .forEach(categoryInfoDeleteDTO -> {
                    try {
                        YouzanItemcategoriesTagDelete youzanItemcategoriesTagAdd = YzConverterUtil.converterToYouzanItemcategoriesTagDelete(categoryInfoDeleteDTO);
                        YouzanItemcategoriesTagDeleteResult result = getResult4YouZanByRhino(ChannelPostYZEnum.DELETE_STORE_CATEGORY,
                                appMessage, youzanItemcategoriesTagAdd, YouzanItemcategoriesTagDeleteResult.class);
                        log.info("删除分类信息结果：categoryInfoDeleteDTO {}, result {}.", categoryInfoDeleteDTO, JSON.toJSONString(result));
                        resultData.getData().add(YzConverterUtil.converterYouzanItemcategoriesTagDeleteResult(result, categoryInfoDeleteDTO, ""));
                    } catch (Exception e) {
                        log.error("调用有赞删除分类接口失败, categoryInfoDeleteDTO {}.", categoryInfoDeleteDTO, e);
                        CategoryPoiResult categoryPoiResult = YzConverterUtil.converterYouzanItemcategoriesTagDeleteResult(null, categoryInfoDeleteDTO, e.getMessage());
                        resultData.getData().add(YzConverterUtil.processErrorCode(categoryPoiResult, e));
                    }
                });

        return resultData;
    }

    @Override
    public UpdateCategoryResponse sortCategory(CategorySortRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.EMPTY_LIST);
    }

    @Override
    public GetCategoryResponse batchGetCategory(CatRequest req) {
        return new GetCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setCatInfoList(Collections.EMPTY_LIST);
    }

    @Override
    public GetCategoryResponse getSecondCategoryByParentId(CatRequest request) {
        GetCategoryResponse response = new GetCategoryResponse(ResultGenerator.genSuccessResult(), Collections.emptyList(), Collections.emptyMap());
        if (CollectionUtils.isEmpty(request.getIds())) {
            return response;
        }

        if (request.getIds().size() > 10) {
            response.setStatus(ResultGenerator.genFailResult("单次请求数不能超过10个"));
            return response;
        }

        long tenantId = request.getBaseInfo().getTenantId();
        AppMessage appMessage = getMainAppMessage(tenantId);

        List<Future<Pair<String, Result<List<CatInfo>>>>> futureList = request.getIds().stream()
                .map(parentId -> yzCategoryThreadPool.submit(() -> getSecondCategoryBySingleParentId(tenantId, parentId, appMessage)))
                .collect(Collectors.toList());

        Map<String, String> failCatMsgMap = new HashMap<>();
        List<CatInfo> resultList = new ArrayList<>();
        futureList.forEach(future -> {
            try {
                Pair<String, Result<List<CatInfo>>> singleResultPair = future.get(10L,
                        TimeUnit.SECONDS);
                Result<List<CatInfo>> queryResult = singleResultPair.getValue();
                if (queryResult.getCode() == 0) {
                    resultList.addAll(queryResult.getData());
                } else {
                    failCatMsgMap.put(singleResultPair.getKey(), queryResult.getMessage());
                }
            } catch (Exception e) {
                log.warn("创建总部商品未知异常", e);
            }
        });
        response.setCatInfoList(resultList);
        response.setFailCatMsgMap(failCatMsgMap);
        return response;
    }

    @Override
    public CategoryProductRulesResponse getCategoryProductRules(CategoryAttrRequest request) {
        return new CategoryProductRulesResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE)).setRuleList(Collections.emptyList());
    }

    private Pair<String, Result<List<CatInfo>>> getSecondCategoryBySingleParentId(long tenantId, String parentId, AppMessage appMessage) {
        Result<List<CatInfo>> catResult = new Result<>();
        try {
            YouzanItemSecondgroupQueryByupperid youzanItemSecondgroupQueryByupperid = YzConverterUtil.convert2YouzanItemSecondgroupQueryByupperid(parentId);
            YouzanItemSecondgroupQueryByupperidResult result = getResult4YouZanByRhino(ChannelPostYZEnum.GET_SECOND_CATEGORY_BY_FIRST_CATEGORY,
                    appMessage, youzanItemSecondgroupQueryByupperid, YouzanItemSecondgroupQueryByupperidResult.class);
            log.info("根据一级分类查询二级分类列表 tenantId:{}, parentId {}, result {}.", tenantId, parentId, result);
            if (result == null || !result.getSuccess()) {
                log.warn("根据一级分类查询二级分类列表失败 tenantId:{}, parentId {}, result {}.", tenantId, parentId, result);
                catResult.setCode(ResultCode.FAIL.getCode());
                catResult.setMessage(result == null ? "渠道返回为空" : result.getMessage());
            } else {
                List<CatInfo> catInfoList = Collections.emptyList();
                List<YouzanItemSecondgroupQueryByupperidResult.YouzanItemSecondgroupQueryByupperidResultData> secondGroups = result.getData();
                if (CollectionUtils.isNotEmpty(secondGroups)) {
                    catInfoList = secondGroups.stream()
                            .filter(Objects::nonNull)
                            .map(YzConverterUtil::convert2CatInfo)
                            .collect(Collectors.toList());
                }
                catResult.setCode(ResultCode.SUCCESS.getCode());
                catResult.setData(catInfoList);
            }
        }
        catch (BizException e) {
            log.warn("根据一级分类查询二级分类列表业务异常, tenantId:{},parentId {}.", tenantId, parentId, e);
            catResult.setCode(e.getErrorCode());
            catResult.setMessage(e.getMessage());
        }
        catch (Exception e) {
            log.warn("根据一级分类查询二级分类列表异常, tenantId:{},parentId {}.", tenantId, parentId, e);
            catResult.setCode(ResultCode.FAIL.getCode());
            catResult.setMessage(e.getMessage());
        }
        return Pair.of(parentId, catResult);
    }

    @Override
    public CategoryAttrResponse getCategoryAttr(CategoryAttrRequest request) {
        return new CategoryAttrResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setGeneralAttrList(Collections.EMPTY_LIST).setSaleAttrList(Collections.EMPTY_LIST);
    }

    @Override
    public CategoryAttrValueResponse getCategoryAttrValue(CategoryAttrValueRequest request) {
        return new CategoryAttrValueResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setValueList(Collections.EMPTY_LIST);
    }

    @Override
    public UpdateCategoryResponse degradeCategory(CategoryDegradeRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.EMPTY_LIST);
    }

    @Override
    public UpdateCategoryResponse adjustCategoryLevel(CategoryLevelAdjustRequest request) {
        log.info("调整分类的层级信息 request {}.", request);
        UpdateCategoryResponse resp = new UpdateCategoryResponse().setStatus(ResultGenerator.genSuccessResult()).setData(org.assertj.core.util.Lists.newArrayList());
        request.getParamList().forEach(data -> {
            /* 删除原等级分类 */
            CategoryDeleteRequest deleteRequst = new CategoryDeleteRequest()
                    .setBaseInfo(request.getBaseInfo())
                    .setParamList(Lists.newArrayList(YzConverterUtil.converterAdjustCategoryLevelDel(data)));

            UpdateCategoryResponse delResp = deleteCategory(deleteRequst);
            if (ResultCode.SUCCESS.getCode() == delResp.status.code && delResp.getData().get(0).getResultCode() == 0) {
                /* 创建目标等级分类 */
                CategoryRequest createRequst = new CategoryRequest()
                        .setParamList(Lists.newArrayList(YzConverterUtil.converterAdjustCategoryLevel(data)))
                        .setBaseInfo(request.getBaseInfo());
                CreateCategoryResponse createResp = createCategory(createRequst);

                resp.getData().addAll(createResp.getData());
            } else {
                resp.getData().addAll(delResp.getData());
            }
        });

        return resp;
    }

    @Override
    public UpdateCategoryResponse updateCategoryChannelCode(CategoryUpdateRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.EMPTY_LIST);
    }

    @Override
    public UpdateCategoryResponse updateSmartSortSwitch(CategorySmartSortSwitchRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.EMPTY_LIST);
    }

    @Override
    public CategorySmartSortQueryResponse queryStoreCategorySmartSort(CategorySmartSortQueryRequest request) {
        return new CategorySmartSortQueryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.EMPTY_LIST);
    }

    @Override
    public RecommendCategoryResponse recommendCategory(RecommendCategoryRequest request) {
        return new RecommendCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public UpdateCategoryResponse topCategory(CategoryTopRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.EMPTY_LIST);
    }

    @Override
    public GetCategoryResponse queryStoreCategoryList(CatRequest request) {
        return null;
    }
}
