package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ElmCommentInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCommentService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ChannelCommentConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 饿了么渠道评价内部服务接口
 *
 * <AUTHOR>
 */
@Service("elmChannelCommentService")
public class ElmChannelCommentServiceImpl implements ChannelCommentService {

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CommonLogger log;

    @Override
    public CommentListQueryResponse queryCommentList(CommentListQueryRequest request) {
        CommentListQueryResponse response = new CommentListQueryResponse();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
                .setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getStoreId()));

        // 构建评价查询业务参数
        Map<String, Object> bizParam = ChannelCommentConverterUtil.buildElmCommentQueryBizParam(request);

        // 调用评价查询接口
        Map<Long, ChannelResponseDTO<String>> channelResponseDTOMap =
                elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.COMMENT_QUERY, baseRequest, bizParam);

        // 解析评价查询结果
        if (channelResponseDTOMap != null && channelResponseDTOMap.get(request.getStoreId()) != null) {
            ChannelResponseDTO<String> channelResponseDTO = channelResponseDTOMap.get(request.getStoreId());
            boolean success = channelResponseDTO.isSuccess();
            if (!success) {
                return response.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg()));
            } else {
                List<ChannelCommentDTO> channelCommentDTOList = new ArrayList<>();
                JSONObject jsonObject = JSON.parseObject(channelResponseDTO.getCoreData());
                if (jsonObject != null) {
                    List<ElmCommentInfo> elmCommentInfoList = JSON.parseArray(jsonObject.getString(
                            ProjectConstant.COMMENT_ELM_COMMENT_LIST), ElmCommentInfo.class);
                    if (CollectionUtils.isNotEmpty(elmCommentInfoList)) {
                        elmCommentInfoList.stream().forEach(elmCommentInfo ->
                                channelCommentDTOList.addAll(elmCommentInfo.convertToChannelCommentDTOList(request.getTenantId())));
                    }
                }
                return response.setStatus(ResultGenerator.genSuccessResult()).setCommentDTOList(channelCommentDTOList);
            }
        } else {
            return response.setStatus(ResultGenerator.genFailResult("调用饿了么评价查询接口失败"));
        }
    }

    @Override
    public CommentReplyResponse reply(CommentReplyRequest request) {
        CommentReplyResponse response = new CommentReplyResponse();

        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
                .setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getStoreId()));

        // 构建评价回复业务参数
        Map<String, Object> bizParam = ChannelCommentConverterUtil.buildElmCommentReplyBizParam(request);

        // 调用评回复接口
        Map<Long, ChannelResponseDTO<String>> channelResponseDTOMap =
                elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.COMMENT_REPLY, baseRequest, bizParam);

        // 解析评价回复结果
        if (channelResponseDTOMap != null && channelResponseDTOMap.get(request.getStoreId()) != null) {
            ChannelResponseDTO<String> channelResponseDTO = channelResponseDTOMap.get(request.getStoreId());
            boolean success = channelResponseDTO.isSuccess();
            if (!success) {
                return response.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg()));
            } else {

                return response.setStatus(ResultGenerator.genSuccessResult());
            }
        } else {
            return response.setStatus(ResultGenerator.genFailResult("调用饿了么评价回复接口失败"));
        }
    }
}
