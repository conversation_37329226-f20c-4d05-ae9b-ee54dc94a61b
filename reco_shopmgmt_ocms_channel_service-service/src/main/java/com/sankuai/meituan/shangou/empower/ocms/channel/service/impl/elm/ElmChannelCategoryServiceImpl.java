package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.CatPropertyDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelCatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelCategorySortDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCategoryService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrValueRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrValueResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryDegradeRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryLevelAdjustRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryPoiResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryProductRulesResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySmartSortQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySmartSortQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySmartSortSwitchRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySortRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryTopRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CreateCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductChannelErrorMappingUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 饿了么渠道商品分类内部服务接口
 *
 * <AUTHOR>
 * @create 2019-01-09 下午4:39
 **/
@Service("elmChannelCategoryService")
public class ElmChannelCategoryServiceImpl implements ChannelCategoryService {

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private ElmConverterService elmConverterService;

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private CommonLogger log;

    @Value("${elm.url.base}")
    private String baseUrl;
    @Value("${elm.url.catlist}")
    private String catlist;
    @Value("${elm.url.categoryAttrList}")
    private String getCategoryAttrList;

    @Value("${elm.url.categoryCreate}")
    private String categoryCreate;
    @Value("${elm.url.categoryUpdate}")
    private String categoryUpdate;

    @Override
    public CreateCategoryResponse createCategory(CategoryRequest request) {
        log.info("开始创建饿百前台分类 CategoryRequest:{}", request);
        CreateCategoryResponse resp = new CreateCategoryResponse();
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo(), request.getParamList());
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setData(Collections.emptyList());
        }

        List<CategoryPoiResult> categoryPoiResults = Lists.newArrayList();
        for (CategoryInfoDTO categoryInfoDTO : request.getParamList()) {
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setStoreId(categoryInfoDTO.getStoreId())
                    .setCode(categoryInfoDTO.getCode());
            if ((categoryInfoDTO.getSort() < ProjectConstant.ELM_CAT_MIN_SORT)
                    || (categoryInfoDTO.getSort() > ProjectConstant.ELM_CAT_MAX_SORT)) {
                log.warn("创建饿百前台分类sort不合法,最小值为1，最大值100000 categoryInfoDTO:{}", categoryInfoDTO);
                categoryPoiResult.setResultCode(1)
                        .setMsg("sort不在1到100000内");
                categoryPoiResults.add(categoryPoiResult);
                continue;
            }

            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }

            Map<String, Object> createParam = Maps.newHashMap();
            String outStoreId = channelStoreDOMap.get(KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(),
                    request.getBaseInfo().getChannelId(), categoryInfoDTO.getStoreId())).getChannelOnlinePoiCode();
            createParam.put("shop_id", outStoreId);
            if (StringUtils.isNotEmpty(categoryInfoDTO.getChannelParentCode())) {
                createParam.put("parent_category_id", categoryInfoDTO.getChannelParentCode());
            } else {
                createParam.put("parent_category_id", 0);
            }
            createParam.put("name", categoryInfoDTO.getName());
            createParam.put("rank", categoryInfoDTO.getSort());

            // 限频检查
            if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SHOP_CATEGORY_CREATE, outStoreId)) {
                log.warn("创建饿百前台分类 获取令牌失败 categoryInfoDTO：{}", categoryInfoDTO);
                categoryPoiResults.add(categoryPoiResult.setResultCode(ResultCode.TRIGGER_LIMIT.getCode()).setMsg(ResultCode.TRIGGER_LIMIT.getMsg()));
                continue;
            }

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(request.getBaseInfo().getTenantId())
                    .setChannelId(request.getBaseInfo().getChannelId())
                    .setStoreIdList(Lists.newArrayList(categoryInfoDTO.getStoreId()));

            Map<String, Object> createCategoryMap = elmChannelGateService.sendPostApp(baseUrl, categoryCreate, baseRequest, createParam);
            log.info("创建饿百前台分类结果 createCategoryMap:{}", createCategoryMap);
            JSONObject createCategoryJsonObj = JSON.parseObject(String.valueOf(createCategoryMap.get(ProjectConstant.BODY)));
            String error = createCategoryJsonObj.getString(ProjectConstant.ERROR);
            String errNo = createCategoryJsonObj.getString(ProjectConstant.ERRNO);
            if (createCategoryJsonObj.getInteger(ProjectConstant.ERRNO) != 0 || !Constant.ELM_SUCCESS_MSG.equals(error)) {
                //填充统一渠道错误码
                ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.ELEM, errNo, error);
                categoryPoiResult.setResultCode(1).setMsg(error).setChannelUnifyError(unifyErrorEnum);
                log.error("创建饿百前台分类失败 createCategoryMap:{}", createCategoryMap);
            } else {
                String category_id = createCategoryJsonObj.getJSONObject(ProjectConstant.DATA).getString("category_id");
                categoryPoiResult.setChannelCode(category_id)
                        .setResultCode(0);
            }
            categoryPoiResults.add(categoryPoiResult);
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    @Override
    public UpdateCategoryResponse updateCategory(CategoryUpdateRequest request) {
        log.info("开始更新饿百前台分类 CategoryRequest:{}", request);
        UpdateCategoryResponse resp = new UpdateCategoryResponse();
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo(), request.getParamList());
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setData(Collections.emptyList());
        }
        List<CategoryPoiResult> categoryPoiResults = Lists.newArrayList();
        for (CategoryInfoUpdateDTO categoryInfoDTO : request.getParamList()) {
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setStoreId(categoryInfoDTO.getStoreId())
                    .setCode(categoryInfoDTO.getCode());
            if ((categoryInfoDTO.getSort() < ProjectConstant.ELM_CAT_MIN_SORT)
                    || (categoryInfoDTO.getSort() > ProjectConstant.ELM_CAT_MAX_SORT)) {
                log.warn("更新饿百前台分类sort不合法,最小值为1，最大值100000 categoryInfoDTO:{}", categoryInfoDTO);
                categoryPoiResult.setResultCode(1).setMsg("sort不在1到100000内");
            }

            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }

            String outStoreId = channelStoreDOMap.get(KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(),
                    request.getBaseInfo().getChannelId(), categoryInfoDTO.getStoreId())).getChannelOnlinePoiCode();
            // 限频检查
            if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.CATEGORY_UPDATE, outStoreId)) {
                log.warn("修改饿百前台分类 获取令牌失败 不阻塞流程 直接调用接口， categoryInfoDTO：{}", categoryInfoDTO);
            }

            Map<String, Object> updateParam = new HashMap<>();
            updateParam.put("shop_id", outStoreId);
            updateParam.put("category_id", categoryInfoDTO.getChannelCategoryCode());
            updateParam.put("name", categoryInfoDTO.getName());
            updateParam.put("rank", categoryInfoDTO.getSort());

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(request.getBaseInfo().getTenantId())
                    .setChannelId(request.getBaseInfo().getChannelId())
                    .setStoreIdList(Lists.newArrayList(categoryInfoDTO.getStoreId()));

            Map<String, Object> updateCategoryMap = elmChannelGateService.sendPostApp(baseUrl, categoryUpdate, baseRequest, updateParam);
            log.info("更新饿百前台分类结果 updateCategoryMap:{}", updateCategoryMap);
            JSONObject updateCategoryJsonObj = JSON.parseObject(String.valueOf(updateCategoryMap.get(ProjectConstant.BODY)));
            String error = updateCategoryJsonObj.getString(ProjectConstant.ERROR);
            String errNo = updateCategoryJsonObj.getString(ProjectConstant.ERRNO);
            if (updateCategoryJsonObj.getInteger(ProjectConstant.ERRNO) != 0 || !Constant.ELM_SUCCESS_MSG.equals(error)) {
                //填充统一渠道错误码
                ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.ELEM, errNo, error);
                categoryPoiResult.setResultCode(1).setMsg(error).setChannelUnifyError(unifyErrorEnum);
                log.error("创建饿百前台分类失败 createCategoryMap:{}", updateCategoryMap);
            } else {
                categoryPoiResult.setResultCode(0);
            }
            categoryPoiResults.add(categoryPoiResult);
        }

        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    @Override
    public UpdateCategoryResponse deleteCategory(CategoryDeleteRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        request.getParamList().forEach(data -> {
            try {
                BaseRequest baseRequest = new BaseRequest()
                        .setTenantId(request.getBaseInfo().getTenantId())
                        .setChannelId(request.getBaseInfo().getChannelId())
                        .setStoreIdList(Lists.newArrayList(data.getStoreId()));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.CATEGORY_DELETE,
                        baseRequest, elmConverterService.deleteCategory(data));

                // 组装返回结果
                ProductResultDataUtils.combineResultData(resultData, postResult, data.getChannelCategoryCode(), ChannelTypeEnum.ELEM);
            } catch (IllegalArgumentException e) {
                log.error("ElmChannelSkuServiceImpl.deleteCategory 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getChannelCategoryCode());

            } catch (Exception e) {
                log.error("ElmChannelCategoryServiceImpl.deleteCategory 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getChannelCategoryCode());
            }
        });

        return convertResultData(resultData);
    }

    @Override
    public UpdateCategoryResponse sortCategory(CategorySortRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        request.getParamList().forEach(data -> {
            // 业务参数转换
            List<ChannelCategorySortDTO> bizDataList = null;
            try {
                bizDataList = elmConverterService.sortCategory(data.getSortItemList());
            } catch (Exception e) {
                log.error("ElmChannelSkuServiceImpl.sortCategory 参数转换失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.VALIDATE_CONVERT_ERROR, data.getCode());
            }

            if (bizDataList != null) {
                // 业务参数处理
                bizDataList.forEach(sku -> {
                    try {
                        BaseRequest baseRequest = new BaseRequest()
                                .setTenantId(request.getBaseInfo().getTenantId())
                                .setChannelId(request.getBaseInfo().getChannelId())
                                .setStoreIdList(Lists.newArrayList(data.getStoreId()));

                        // 调用渠道接口
                        Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.CATEGORY_SORT,
                                baseRequest, sku);

                        // 组装返回结果
                        ResultDataUtils.combineResultData(resultData, postResult, String.valueOf(sku.getCategory_id()));

                    } catch (IllegalArgumentException e) {
                        log.error("ElmChannelSkuServiceImpl.sortCategory 参数校验失败, data:{}", data, e);
                        ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getCode());

                    } catch (Exception e) {
                        log.error("ElmChannelCategoryServiceImpl.sortCategory 服务异常, data:{}", data, e);
                        ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, String.valueOf(sku.getCategory_id()));
                    }
                });
            }
        });

        return convertResultData(resultData);
    }

    @Override
    public GetCategoryResponse batchGetCategory(CatRequest req) {
        GetCategoryResponse resp = new GetCategoryResponse();
        if ((req.getDepth() < ProjectConstant.ELM_CAT_MIN_DEPTH) || (req.getDepth() > ProjectConstant.ELM_CAT_MAX_DEPTH)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.INVALID_PARAM, "depth字段不合法"));
        }
        List<CatInfo> catInfoList = Lists.newArrayList();
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(req.getBaseInfo().getTenantId())
                .setChannelId(req.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(req.getStoreId()));
        for (String id : req.getIds()) {
            Map<String, Object> param = new HashMap<>();
            param.put(ProjectConstant.DEPTH, req.getDepth());
            param.put(ProjectConstant.PARENT_ID, id);
            Map<String, Object> catMap = elmChannelGateService.sendPostApp(baseUrl, catlist, baseRequest, param);

            JSONObject catInfoJsonObject = (JSONObject) catMap.get(ProjectConstant.BODY);
            if ((int) catInfoJsonObject.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, catInfoJsonObject.getString(ProjectConstant.ERROR)));
            }
            JSONArray catJsonarr = catInfoJsonObject.getJSONArray(ProjectConstant.DATA);
            List<ChannelCatInfo> catInfos = catJsonarr.toJavaList(ChannelCatInfo.class);
            catInfoList.addAll(elmConverterService.catInfoMappings(catInfos));
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(catInfoList);
    }

    @Override
    public GetCategoryResponse getSecondCategoryByParentId(CatRequest req) {
        return new GetCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE)).setCatInfoList(Collections.emptyList());
    }

    @Override
    public CategoryProductRulesResponse getCategoryProductRules(CategoryAttrRequest request) {
        return new CategoryProductRulesResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE)).setRuleList(Collections.emptyList());
    }

    @Override
    public CategoryAttrResponse getCategoryAttr(CategoryAttrRequest request) {
        CategoryAttrResponse response = new CategoryAttrResponse().setStatus(ResultGenerator.genSuccessResult());
        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(), request.getStoreId());
        if (channelStore == null) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.INVALID_PARAM, "牵牛花门店对应饿了么渠道门店不存在"));
        }
        Map<String, Object> param = new HashMap<>();
        param.put("cat3_id", request.getCategoryId());
        param.put("shop_id", channelStore.getChannelOnlinePoiCode());

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));

        Map<String, Object> catMap = elmChannelGateService.sendPostApp(baseUrl, getCategoryAttrList, baseRequest, param);
        if (catMap == null) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询渠道类目属性信息失败"));
        }
        JSONObject catInfoJsonObject = (JSONObject) catMap.get(ProjectConstant.BODY);
        String error = catInfoJsonObject.getString(ProjectConstant.ERROR);
        if ((int) catInfoJsonObject.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode() || !Constant.ELM_SUCCESS_MSG.equals(error)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, error));
        }
        JSONArray jsonArray = catInfoJsonObject.getJSONArray(ProjectConstant.DATA);
        if (jsonArray == null || jsonArray.isEmpty()) {
            return response;
        }
        List<CatPropertyDTO> catPropertyDTOList = jsonArray.toJavaList(CatPropertyDTO.class);
        List<CategoryAttrInfo> generalAttrList = Lists.newArrayList();
        List<CategoryAttrInfo> saleAttrList = Lists.newArrayList();
        catPropertyDTOList.forEach(cat -> {
            if (cat.getSaleProp()) {
                saleAttrList.add(cat.convert2Info());
            } else {
                generalAttrList.add(cat.convert2Info());
            }
        });
        return response.setGeneralAttrList(generalAttrList)
                .setSaleAttrList(saleAttrList)
                .setUpcRequired(-1)
                .setWeightRequired(-1);
    }


    @Override
    public CategoryAttrValueResponse getCategoryAttrValue(CategoryAttrValueRequest request) {
        return new CategoryAttrValueResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public UpdateCategoryResponse degradeCategory(CategoryDegradeRequest request) {
        UpdateCategoryResponse resp = new UpdateCategoryResponse().setStatus(ResultGenerator.genSuccessResult()).setData(Lists.newArrayList());
        request.getParamList().forEach(data -> {
            /* 删除一级分类 */
            CategoryDeleteRequest deleteRequst = new CategoryDeleteRequest()
                    .setBaseInfo(request.getBaseInfo())
                    .setParamList(Lists.newArrayList(baseConverterService.degradeCategoryDel(data)));

            UpdateCategoryResponse delResp = deleteCategory(deleteRequst);
            if (ResultCode.SUCCESS.getCode() == delResp.status.code && delResp.getData().get(0).getResultCode() == 0) {
                /* 删除成功后创建二级分类 */
                CategoryRequest createRequst = new CategoryRequest()
                        .setParamList(Lists.newArrayList(baseConverterService.degradeCategory(data)))
                        .setBaseInfo(request.getBaseInfo());
                CreateCategoryResponse createResp = createCategory(createRequst);
                resp.getData().addAll(createResp.getData());
            } else {
                resp.getData().addAll(delResp.getData());
            }
        });

        return resp;
    }

    @Override
    public UpdateCategoryResponse adjustCategoryLevel(CategoryLevelAdjustRequest request) {
        // 饿了么开放平台不支持层级调整，需要先删除原分类后新建分类
        UpdateCategoryResponse resp = new UpdateCategoryResponse().setStatus(ResultGenerator.genSuccessResult()).setData(Lists.newArrayList());
        request.getParamList().forEach(data -> {
            /* 删除原等级分类 */
            CategoryDeleteRequest deleteRequest = new CategoryDeleteRequest()
                    .setBaseInfo(request.getBaseInfo())
                    .setParamList(Lists.newArrayList(baseConverterService.adjustCategoryLevelDel(data)));

            UpdateCategoryResponse delResp = deleteCategory(deleteRequest);
            if (ResultCode.SUCCESS.getCode() == delResp.status.code) {
                /* 创建目标等级分类 */
                CategoryInfoDTO categoryInfoDTO = null;
                switch (data.getAdjustType()) {
                    case DEGRADE:
                        categoryInfoDTO = baseConverterService.degradeCategoryLevel(data);
                        break;
                    case UPGRADE:
                        categoryInfoDTO = baseConverterService.upgradeCategoryLevel(data);
                        break;
                    default:
                        break;
                }
                CategoryRequest createRequest = new CategoryRequest()
                        .setParamList(Lists.newArrayList(categoryInfoDTO))
                        .setBaseInfo(request.getBaseInfo());
                CreateCategoryResponse createResp = createCategory(createRequest);
                resp.getData().addAll(createResp.getData());
            } else {
                delResp.getData().get(0).setCode(data.getCode()).setChannelCode(data.getChannelCode())
                        .setResultCode(ResultCode.FAIL.getCode()).setMsg("删除失败");
                resp.getData().addAll(delResp.getData());
            }
        });

        return resp;
    }

    /**
     * 将ResultData转换为UpdateCategoryResponse
     *
     * @param resultData
     * @return
     */
    private UpdateCategoryResponse convertResultData(ResultData resultData) {
        UpdateCategoryResponse resp = baseConverterService.toUpdateCategoryResponse(resultData);
        resp.getData().addAll(baseConverterService.toUpdateCategoryResponseSuc(resultData.getSucData()));

        return resp;
    }

    @Override
    public UpdateCategoryResponse updateCategoryChannelCode(CategoryUpdateRequest request) {
        // ignore impl
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.SUCCESS))
                .setData(Collections.emptyList());
    }

    @Override
    public UpdateCategoryResponse updateSmartSortSwitch(CategorySmartSortSwitchRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.emptyList());
    }

    @Override
    public CategorySmartSortQueryResponse queryStoreCategorySmartSort(CategorySmartSortQueryRequest request) {
        return new CategorySmartSortQueryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.emptyList());
    }

    @Override
    public RecommendCategoryResponse recommendCategory(RecommendCategoryRequest request) {
        return new RecommendCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public UpdateCategoryResponse topCategory(CategoryTopRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public GetCategoryResponse queryStoreCategoryList(CatRequest request) {
        return null;
    }
}
