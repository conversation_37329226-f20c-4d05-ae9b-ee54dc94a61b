package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelBrandInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.CopAccessConfigMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelBrandService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.BrandStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BrandInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetBrandResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.RecommendBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.RecommendBrandResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @author:huchangwu
 * @date: 2019/1/20
 * @time: 下午3:42
 */
@Service("jddjChannelBrandService")
public class JddjChannelBrandServiceImpl implements ChannelBrandService {

    @Resource
    private JddjConverterService jddjConverterService;
    @Resource
    private JddjChannelGateService jddjChannelGateService;
    @Resource
    private BaseConverterService baseConverterService;
    @Resource
    private ClusterRateLimiter clusterRateLimiter;
    @Resource
    private CommonLogger log;

    @Value("${jddj.url.base}" + "${jddj.url.brandlist}")
    private String brandlist;

    @Value("${jddj.url.base}" + "${jddj.url.recommendBrandAndCate}")
    private String recommendBrandUrl;

    @Autowired
    private CopAccessConfigMapper copAccessConfigMapper;

    @Override
    public GetBrandResponse batchGetBrand(GetBrandRequest req) {
        GetBrandResponse resp = new GetBrandResponse();
        if (req.getPageNum() < 1) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "pageNum不合法"));
        }
        List<String> fields = Lists.newArrayList();
        fields.add(ProjectConstant.JDDJ_FIELD_BRAND_ID);
        fields.add(ProjectConstant.JDDJ_FIELD_BRAND_NAME);
        fields.add(ProjectConstant.JDDJ_FIELD_BRAND_STATUS);
        Map<String, Object> param = new HashMap();
        if (StringUtils.isNotEmpty(req.getBrandName())) {
            param.put(ProjectConstant.BRAND_NAME, req.getBrandName());
        }
        param.put(ProjectConstant.FIELDS, fields);
        param.put(ProjectConstant.PAGE_NO, req.getPageNum());
        param.put(ProjectConstant.PAGE_SIZE, 50);
        BaseRequest baseRequest = baseConverterService.baseRequest(req.getBaseInfo());
        if (baseRequest.getAppId() <= 0) {
            List<Integer> appIdList = copAccessConfigMapper.queryChannelAppIds(req.getBaseInfo().getTenantId(), req.getBaseInfo().getChannelId());
            if (CollectionUtils.isEmpty(appIdList)) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "租户未配置该渠道应用"));
            }
            baseRequest.setAppId(appIdList.get(0).longValue());
        }
        Map<String, Object> sysParams = safeGetSysParam(baseRequest);
        String appId = String.valueOf(sysParams.get(ProjectConstant.JDDJ_APP_KEY));
        // 限频检查
        if (org.apache.commons.lang3.StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.BRAND_LIST, String.valueOf(req.getBaseInfo().getTenantId()))) {
            log.warn("拉取京东门店详情 获取令牌失败 不阻塞流程 直接调用接口，request {}", param);
        }
        Map brandMap = jddjChannelGateService.sendPostApp(brandlist, null, baseRequest, param);
        if (Integer.parseInt((String) brandMap.get(ProjectConstant.CODE)) != 0) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询品牌信息失败"));
        }
        String brandJsonstr = (String) brandMap.get(ProjectConstant.DATA);
        JSONObject brandJson = JSON.parseObject(brandJsonstr).getJSONObject(ProjectConstant.RESULT);

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(brandJson.getInteger(ProjectConstant.PAGE_NO));

        int originTotalPage = (int)Math.ceil(brandJson.getInteger(ProjectConstant.COUNT) * 1.0 / brandJson.getInteger(ProjectConstant.PAGE_SIZE));
        pageInfo.setTotalPage(originTotalPage);
        List<ChannelBrandInfo> brandInfos = Optional.ofNullable(brandJson.getJSONArray(ProjectConstant.RESULT))
                .orElse(new JSONArray()).toJavaList(ChannelBrandInfo.class);
        List<BrandInfo> brandInfoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(brandInfos)) {
            return resp.setStatus(ResultGenerator.genSuccessResult()).setPageInfo(pageInfo).setBrandInfoList(brandInfoList);
        }
        for (ChannelBrandInfo channelBrandInfo : brandInfos) {
            BrandInfo brandInfo = new BrandInfo();
            brandInfo.setBrandStatus(BrandStatusEnum.VALID.getValue());
            if (channelBrandInfo.getBrandStatus() == null || channelBrandInfo.getBrandStatus() != ProjectConstant.JDDJ_BRAND_VALID) {
                log.info("品牌已失效,{}",JSON.toJSONString(channelBrandInfo));
                brandInfo.setBrandStatus(BrandStatusEnum.INVALID.getValue());
                //continue;
            }

            brandInfo.setBrandId(channelBrandInfo.getId());
            brandInfo.setBrandName(channelBrandInfo.getBrandName());
            brandInfoList.add(brandInfo);
        }
        return resp.setStatus(ResultGenerator.genSuccessResult())
                .setPageInfo(pageInfo)
                .setBrandInfoList(brandInfoList);
    }

    @Override
    public RecommendBrandResponse getRecommendBrand(RecommendBrandRequest req){
        List<String> fields = Lists.newArrayList();
        fields.add(ProjectConstant.JDDJ_FIELD_BRAND);
        Map<String,Object> param = new HashMap();
        param.put(ProjectConstant.PRODUCT_NAME, req.getSpuName());
        param.put(ProjectConstant.FIELDS, fields);

        // 对于ERP的多应用租户，使用第一个应用获取推荐类目即可
        BaseRequest baseRequest = new BaseRequest().setChannelId(req.getChannelId()).setTenantId(req.getTenantId());
        if (baseRequest.getAppId() <= 0) {
            List<Integer> appIdList = copAccessConfigMapper.queryChannelAppIds(req.getTenantId(), req.getChannelId());
            if (CollectionUtils.isEmpty(appIdList)) {
                RecommendBrandResponse recommendBrandResponse = new RecommendBrandResponse();
                recommendBrandResponse.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(),"租户未配置该渠道应用"));
                return recommendBrandResponse;
            }
            baseRequest.setAppId(appIdList.get(0).longValue());
        }
        Map<String, Object> sysParams = safeGetSysParam(baseRequest);
        String appId = String.valueOf(sysParams.get(ProjectConstant.JDDJ_APP_KEY));

        // 限频检查
        if (org.apache.commons.lang3.StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.RECOMMEND_CATE_BRAND, String.valueOf(req.getTenantId()))) {
            log.warn("按名称查询推荐品牌，获取令牌失败，request {}", param);
            RecommendBrandResponse recommendBrandResponse = new RecommendBrandResponse();
            recommendBrandResponse.setStatus(ChannelStatus.build(ResultCode.TRIGGER_LIMIT.getCode(), ResultCode.TRIGGER_LIMIT.getMsg()));
            return recommendBrandResponse;
        }

        Map<String, Object> brandMap = jddjChannelGateService.sendPostApp(recommendBrandUrl, null, baseRequest, param);
        if(brandMap == null || brandMap.get(ProjectConstant.CODE) == null){
            String message = String.format("查询推荐品牌出现通讯异常,请求参数:%s",JSON.toJSONString(param));
            log.warn(message);
            RecommendBrandResponse recommendBrandResponse = new RecommendBrandResponse();
            recommendBrandResponse.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(),message));
            return recommendBrandResponse;
        }
        if (Integer.parseInt((String) brandMap.get(ProjectConstant.CODE)) != 0) {
            String msg = (String) brandMap.get(ProjectConstant.MSG);
            String message = String.format("查询推荐品牌失败,message:%s",msg);
            log.warn(message);
            RecommendBrandResponse recommendBrandResponse = new RecommendBrandResponse();
            recommendBrandResponse.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(),message));
            return recommendBrandResponse;
        }
        String brandJsonstr = (String) brandMap.get(ProjectConstant.DATA);
        JSONObject brandJson = JSON.parseObject(brandJsonstr).getJSONObject(ProjectConstant.RESULT);

        String brandId = brandJson.getString(ProjectConstant.JDDJ_FIELD_BRANDID);
        List<String> brandIds = new ArrayList();
        if(StringUtils.isNotEmpty(brandId)){
            brandIds.add(brandId);
        }
        RecommendBrandResponse recommendBrandResponse = new RecommendBrandResponse();
        recommendBrandResponse.setBrandIdList(brandIds);
        recommendBrandResponse.setStatus(ChannelStatus.buildSuccess());
        return recommendBrandResponse;
    }

    private Map<String, Object> safeGetSysParam(BaseRequest baseRequest) {
        if (baseRequest.isSetAppId() && baseRequest.getAppId() > 0) {
            return jddjChannelGateService.getSysParamByChannelIdAndAppId(baseRequest.getTenantId(), baseRequest.getChannelId(), baseRequest.getAppId());
        }
        return jddjChannelGateService.getChannelSysParams(new BaseRequest().setChannelId(baseRequest.getChannelId()).setTenantId(baseRequest.getTenantId()));
    }
}
