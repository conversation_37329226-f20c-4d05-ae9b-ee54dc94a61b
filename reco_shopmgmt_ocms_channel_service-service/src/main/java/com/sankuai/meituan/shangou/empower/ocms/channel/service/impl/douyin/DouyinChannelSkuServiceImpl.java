package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelCategoryQueryResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSkuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 抖音渠道商品内部服务接口
 *
 * @author: chenlishu
 * @create: 2023/12/20
 */
@Service("dyChannelSkuService")
public class DouyinChannelSkuServiceImpl implements ChannelSkuService {

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Autowired
    private DouyinChannelGateService douyinChannelGateService;

    @Resource
    private CommonLogger log;

    @Override
    public ResultData skuCreate(SkuInfoRequest request) {
        return null;
    }


    @Override
    public ResultData upcCreate(SkuInfoRequest request) {
        return null;
    }

    @Override
    public ResultData updateSku(SkuInfoRequest request) {
        return null;
    }

    @Override
    public ResultData deleteSku(SkuInfoDeleteRequest request) {
        return null;
    }

    @Override
    public ResultData pictureUpload(PictureUploadRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    @Override
    public PictureUploadResult getPictureUploadStatus(PictureUploadStatusRequest request) {
        return null;
    }

    @Override
    public ResultData updateSkuSellStatus(SkuSellStatusInfoRequest request) {
        return null;
    }

    @Override
    public ResultData updateSkuSellStatusForCleaner(SkuSellStatusInfoRequest request) {
        return null;
    }

    @Override
    public ResultData updateCustomSkuId(UpdateCustomSkuIdRequest request) {
        return null;
    }

    @Override
    public GetSkuInfoResponse getSkuInfo(GetSkuInfoRequest request) {

        return null;
    }

    @Override
    public BatchGetSkuInfoResponse batchGetSkuInfo(BatchGetSkuInfoRequest request) {

        return null;
    }

    @Override
    public GetCategoryResponse batchGetChannelStoreCategoryInfo(CatRequest request) {
        GetCategoryResponse response = new GetCategoryResponse().setStatus(ResultGenerator.genSuccessResult());
        if (request == null || request.getBaseInfo() == null){
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取抖音店内分类失败"));
        }
        BaseRequestSimple baseInfo = request.getBaseInfo();
        List<CatInfo> catInfos = Collections.emptyList();
        try {
            Map<String, Object> queryParam = Collections.emptyMap();
            Map<String, Long> tenantCateTemplateConfig = MccConfigUtil.getDouyinTenantCategoryTemplateConfig();
            Long templateId = Optional.ofNullable(tenantCateTemplateConfig)
                    .filter(MapUtils::isNotEmpty)
                    .map(it -> it.get(String.valueOf(baseInfo.getTenantId())))
                    .orElse(null);
            if (templateId != null) {
                queryParam = new HashMap<>(2);
                queryParam.put(ProjectConstant.DOUYIN_TEMP_ID, templateId);
            }
            ChannelResponseDTO<ChannelCategoryQueryResult> channelResponseDTO = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SHOP_MARKET_CATEGORY_QUERY, baseInfo, null, queryParam);
            if (channelResponseDTO.isSuccess()) {
                ChannelCategoryQueryResult channelCategoryQueryResult = channelResponseDTO.getCoreData();
                // 分类信息list不为空
                if (Objects.nonNull(channelCategoryQueryResult) && CollectionUtils.isNotEmpty(channelCategoryQueryResult.getCategory_info_list())){
                    List<ChannelCategoryQueryResult.CategoryInfoResult> categoryInfoList = channelCategoryQueryResult.getCategory_info_list();
                    catInfos = new ArrayList<>(categoryInfoList.size() * 2);
                    // 添加分类信息返回结果
                    addCategoryInfoResults(catInfos, categoryInfoList, null, null);
                }
            } else {
                log.warn("douyinChannelCategoryServiceImpl.batchGetCategory 请求抖音返回失败， channelResponseDTO：{}", channelResponseDTO);
                String errMsg = Optional.ofNullable(channelResponseDTO.getSub_msg()).orElse(channelResponseDTO.getMsg());
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, errMsg));
            }
        } catch (IllegalArgumentException e) {
            log.error("douyinChannelCategoryServiceImpl.batchGetCategory 参数校验失败, request:{}", request, e);
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, e.getMessage()));
        } catch (Exception e) {
            log.error("douyinChannelCategoryServiceImpl.batchGetCategory 服务异常, request:{}", request, e);
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, e.getMessage()));
        }
        return response.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(catInfos);
    }

    /**
     * 添加类目信息返回结果
     * @param catInfos list容器
     * @param categoryInfoList 待转换分类信息
     * @param parentCategoryId 父分类id
     * @param parentCategoryName 父分类名称
     */
    private static void addCategoryInfoResults(List<CatInfo> catInfos, List<ChannelCategoryQueryResult.CategoryInfoResult> categoryInfoList
            , Long parentCategoryId, String parentCategoryName) {
        for (ChannelCategoryQueryResult.CategoryInfoResult categoryInfo : categoryInfoList) {
            catInfos.add(DouyinConvertUtil.buildCatInfo(categoryInfo, parentCategoryId, parentCategoryName));
            // 如果有子分类，递归添加
            if (CollectionUtils.isNotEmpty(categoryInfo.getChildren_category_info_list())){
                addCategoryInfoResults(catInfos, categoryInfo.getChildren_category_info_list(), categoryInfo.getCategory_id(), categoryInfo.getCategory_name());
            }
        }
    }

    @Override
    public ResultData changeCustomSkuId(ChangeCustomSkuIdRequest request) {
        return null;
    }

    /**
     * 查询商品违规信息
     * @param request
     * @return
     */
    @Override
    public BatchGetViolateSkuResponse batchGetViolateSku(BatchGetViolateSkuRequest request) {
        return null;
    }
}
