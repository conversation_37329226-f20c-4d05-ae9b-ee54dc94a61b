package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

/**
 * Пусть вечная мира во всем мире！
 *
 * @Author: wangruiguo
 * @Date: 2021/7/27 5:40 下午
 * @Description: 
 */
public class OrderDischargeDetail {

    //商品类活动ID
    private Long actItemId;

    //买赠活动中的赠品ID
    private Long g_i_i;

    //活动批次ID
    private Long uuid;

    @Override
    public String toString() {
        return "OrderDischargeDetail{" +
                "actItemId=" + actItemId +
                ", g_i_i=" + g_i_i +
                ", uuid=" + uuid +
                '}';
    }

    public Long getActItemId() {
        return actItemId;
    }

    public void setActItemId(Long actItemId) {
        this.actItemId = actItemId;
    }

    public Long getG_i_i() {
        return g_i_i;
    }

    public void setG_i_i(Long g_i_i) {
        this.g_i_i = g_i_i;
    }

    public Long getUuid() {
        return uuid;
    }

    public void setUuid(Long uuid) {
        this.uuid = uuid;
    }
}
