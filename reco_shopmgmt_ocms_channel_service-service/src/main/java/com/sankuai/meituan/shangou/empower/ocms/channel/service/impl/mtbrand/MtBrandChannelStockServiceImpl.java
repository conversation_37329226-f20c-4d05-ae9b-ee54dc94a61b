package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStoreStockInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuCreateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSpuUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelStockService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStockDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelBaseMsgThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSpuThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultSpuDataUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 美团渠道商品库存内部服务接口
 *
 * <AUTHOR>
 * @create 2019-01-09 下午4:41
 **/
@Service("mtBrandChannelStockService")
public class MtBrandChannelStockServiceImpl implements ChannelStockService {
    public static final int STOCK_UPDATE_MAX_COUNT = 50;

    @Value("${mt.url.stockQuery}" + "${mt.url.skulist}")
    private String skuList;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private MtConverterService mtConverterService;

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private MtChannelGateService mtChannelGateService;

    @Resource
    private ChannelSpuThriftService.Iface channelSpuThriftService;

    @Resource
    private ChannelBaseMsgThriftService.Iface channelBaseMsgThriftService;

    @Resource
    private CommonLogger log;

    @Override
    public ResultSpuData updateStockBySpu(SpuStockRequest request) {
        try {
            BaseRequestSimple baseInfo = request.getBaseInfo();
            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
            //按照门店分组
            Map<Long, List<SpuStockDTO>> map = request.getParamList().stream().collect(Collectors.groupingBy(SpuStockDTO::getStoreId));

            map.forEach((storeId, spuStockDTOList) -> {

                // 返回结果组装用标识
                List<SpuKey> bizKeyList = new ArrayList<>();
                spuStockDTOList.forEach(spuStockDTO -> {
                    SpuKey spuKey = new SpuKey().setCustomSpuId(spuStockDTO.getCustomSpuId());
                    List<SkuKey> skuKeys = new ArrayList<>();
                    spuStockDTO.getSkuStockInfo().forEach(skuInSpuStockDTO -> {
                        SkuKey skuKey = new SkuKey().setCustomSkuId(skuInSpuStockDTO.getCustomSkuId());
                        skuKeys.add(skuKey);
                    });
                    spuKey.setSkus(skuKeys);
                    bizKeyList.add(spuKey);
                });
                ListUtils.listPartition(spuStockDTOList, STOCK_UPDATE_MAX_COUNT).forEach(data -> {
                    // 业务参数转换
                    ChannelSpuUpdateDTO dto = mtConverterService.updateSpuInfo(JSON.toJSONString(mtConverterService.updateStockBySpuMappings(data)));

                    // 调用渠道接口
                    Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.SPU_STOCK_UPDATE, baseInfo, storeId, dto);

                    // 组装返回结果
                    boolean isErpTenant = Objects.nonNull(request.getBaseInfo()) ? request.getBaseInfo().isErpTenant() : false;
                    ResultSpuDataUtils.combinePartResultData(resultData, postResult, bizKeyList, ChannelTypeEnum.MEITUAN, isErpTenant);
                });
            });
            return resultData;
        } catch (InvokeChannelTooMuchException e) {
            log.warn("MtChannelStockServiceImpl.updateStockBySpu, 触发限频, request:{}", request, e);
        } catch (Exception e) {
            log.warn("MtChannelStockServiceImpl.updateStockBySpu, 批量修改商品库存服务异常, request:{}", request, e);
        }
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量修改商品库存失败");
    }

    @Override
    public ResultData updateStockMultiChannel(SkuStockMultiChannelRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long storeId = request.skuStockList.get(0).storeId;
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.tenantId)
                .setChannelId(request.skuStockList.get(0).channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        // 分页调用
        ListUtils.listPartition(request.skuStockList, STOCK_UPDATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = Lists.newArrayList();
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuStockMultiChannelDTO::getSkuId).collect(Collectors.toList());

                // 业务参数转换
                ChannelSkuUpdateDTO dto = mtConverterService.updatePrice(JSON.toJSONString(mtConverterService.updateStockM(data)));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPostAppMapDto(ChannelPostMTEnum.STOCK_UPDATE, baseRequest, dto);

                // 组装返回结果
                ResultDataUtils.combinePartResultData(resultData, postResult, bizKeyList);

            } catch (IllegalArgumentException e) {
                log.warn("MtChannelStockServiceImpl.updateStockMultiChannel 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList, storeId);

            } catch (InvokeChannelTooMuchException e) {
                log.warn("MtChannelStockServiceImpl.updateStockMultiChannel 触发限频, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList, storeId);
            } catch (Exception e) {
                log.warn("MtChannelStockServiceImpl.updateStockMultiChannel 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList, storeId);
            }
        });
        return resultData;
    }

    @Override
    public BatchGetStockInfoResponse batchGetStockInfo(BatchGetStockInfoRequest request) {
        BatchGetStockInfoResponse response = new BatchGetStockInfoResponse();
        // 获取渠道门店编码
        ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(), request.getStoreId());
        if (Objects.isNull(channelStoreDO) || StringUtils.isBlank(channelStoreDO.getChannelOnlinePoiCode())) {
            throw new IllegalArgumentException("channel store is not exist,storeId:" + request.getStoreId());
        }

        tryAcquire(channelStoreDO);

        Map<String, Object> bizParam = buildQueryStockParam(channelStoreDO.getChannelOnlinePoiCode(), request.getPageSize(), request.getPageNum());
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(com.google.common.collect.Lists.newArrayList(request.getStoreId()));
        // 请求线上渠道
        Map skuInfoMap = mtBrandChannelGateService.sendGet(skuList, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(skuInfoMap) || Objects.isNull(skuInfoMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(skuInfoMap.get(ProjectConstant.DATA))) {
            throw new ChannelBizException("query mt sku stock error");
        }

        //将渠道原始返回对象转换成目标DTO
        List<ChannelSkuStockDTO> skuStockDTOS = convert2TargetDTO((JSONArray) skuInfoMap.get(ProjectConstant.DATA));

        //构建分页信息、offset信息
        setPageInfo(skuInfoMap, request.getPageSize(), request.getPageNum(), response);

        return response.setStatus(ResultGenerator.genSuccessResult()).setSkuStocks(skuStockDTOS);
    }

    private void tryAcquire(ChannelStoreDO channelStoreDO) {
        String uuid = Optional.ofNullable(channelStoreDO.getTenantId()).map(v -> v.toString()).orElse(channelStoreDO.getChannelOnlinePoiCode());
        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.BATCH_GET_SKUINFO, uuid);
        if (!tryAcquire) {
            throw new InvokeChannelTooMuchException(0);
        }
    }

    /**
     * 将渠道原始返回对象转换成目标DTO
     *
     * @param skuInfoJSONArray
     * @return
     */
    private List<ChannelSkuStockDTO> convert2TargetDTO(JSONArray skuInfoJSONArray) {
        if (Objects.isNull(skuInfoJSONArray)) {
            return Collections.emptyList();
        }

        List<ChannelSkuStockDTO> skuStockDTOS = com.google.common.collect.Lists.newArrayList();
        skuInfoJSONArray.forEach(item -> {
            ChannelSkuCreateDTO channelSkuDTO = new ChannelSkuCreateDTO();
            // 异常情况处理，可能存在没有sku，过滤
            if (StringUtils.isBlank(((JSONObject) item).getString("skus"))) {
                log.warn("MtChannelSku.batchGetSkuInfo skus or skus is empty json:{}", item);
                return;
            }
            List<ChannelSkuInfoDTO> channelSkuInfoDTOS = JSONArray.parseArray((((JSONObject) item).get("skus")).toString(), ChannelSkuInfoDTO.class);
            if (CollectionUtils.isEmpty(channelSkuInfoDTOS)) {
                log.warn("MtChannelSku.batchGetSkuInfo skus or skus is empty json:{}", item);
                return;
            }

            channelSkuDTO.setApp_food_code(((JSONObject) item).getString("app_food_code"));

            for (ChannelSkuInfoDTO channelSkuInfoDTO : channelSkuInfoDTOS) {
                ChannelSkuStockDTO skuStockDTO = new ChannelSkuStockDTO();
                skuStockDTO.setCustomSkuId(channelSkuInfoDTO.getSku_id());
                skuStockDTO.setCustomSpuId(channelSkuDTO.getApp_food_code());
                if (StringUtils.isBlank(channelSkuInfoDTO.getStock())) {
                    log.warn("MtChannelSku.batchGetSkuInfo channel stock is empty:{}", JacksonUtils.toJson(channelSkuInfoDTO));
                } else {
                    skuStockDTO.setStockQty(Double.valueOf(channelSkuInfoDTO.getStock()));
                }
                skuStockDTOS.add(skuStockDTO);
            }
        });
        return skuStockDTOS;
    }

    /**
     * 构建分页信息、offset信息
     *
     * @param skuInfoMap
     * @param pageSize
     * @param response
     */
    private void setPageInfo(Map skuInfoMap, int pageSize, int pageNum, BatchGetStockInfoResponse response) {
        if (MapUtils.isEmpty(skuInfoMap)) {
            return;
        }

        JSONObject extraInfo = (JSONObject) skuInfoMap.get(ProjectConstant.EXTRA_INFO);
        int totalCount = extraInfo != null && null != extraInfo.get(ProjectConstant.TOTAL_COUNT) ? (int) extraInfo.get(ProjectConstant.TOTAL_COUNT) : 0;
        int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : (totalCount / pageSize) + 1;
        response.setPageInfo(new PageInfo(pageNum, pageSize, totalPage, totalCount));
    }

    /**
     * 构建查询库存请求参数
     * @param channelOnlinePoiCode
     * @param pageSize
     * @param pageNum
     * @return
     */
    private Map<String, Object> buildQueryStockParam(String channelOnlinePoiCode, int pageSize, int pageNum) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put("offset", (pageNum - 1) * pageSize);
        bizParam.put("limit", pageSize);
        bizParam.put("app_poi_code", channelOnlinePoiCode);
        return bizParam;
    }
}
