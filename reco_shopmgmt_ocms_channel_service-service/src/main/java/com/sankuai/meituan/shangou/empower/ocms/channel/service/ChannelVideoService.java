package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.BatchVideoUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.BatchVideoUploadResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoBindRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoBindResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoUploadResponse;

/**
 * @Author: wangyihao04
 * @Date: 2022-03-21 17:08
 * @Mail: <EMAIL>
 */
public interface ChannelVideoService {
    VideoUploadResponse uploadVideo(VideoUploadRequest request);

    VideoBindResponse updateVideoBindRelation(VideoBindRequest request);

    VideoDeleteResponse deleteVideo(VideoDeleteRequest request);

    BatchVideoUploadResponse batchUploadVideo(BatchVideoUploadRequest request);
}
