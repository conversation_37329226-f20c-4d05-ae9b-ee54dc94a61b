package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.DeliveryRegion;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.PoiShippingUpdateParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.Region;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiShippingService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/4/10
 * desc: 饿了么渠道门店配送服务
 */
@Service("elmChannelPoiShippingService")
@Slf4j
public class ElmChannelPoiShippingServiceImpl implements ChannelPoiShippingService {

    @Resource
    private ElmConverterService elmConverterService;

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Override
    public ResultStatus updatePoiShipping(UpdatePoiShippingRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(request.getTenantId())
                    .setChannelId(request.getChannelId())
                    .setStoreIdList(Lists.newArrayList(request.getShopId()));;
            PoiShippingUpdateParam bizParam = convertPoiShippingUpdateParam(request);
            ChannelResponseDTO<String> resultData = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.SHOP_UPDATE, baseRequest, bizParam);
            log.info("ElmChannelOrderServiceImpl.updatePoiShipping, request:{}, resultMap:{}", request, resultData);
            return getResultStatus(resultData);
        } catch (Exception e) {
            log.error("elmChannelOrderService updatePoiShipping ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public ResultStatus batchUpdatePoiShipping(BatchUpdatePoiShippingRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus resetPoiShipping(ResetPoiShippingRequest resetPoiShippingRequest) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus deletePoiShipping(DeletePoiShippingRequest deletePoiShippingRequest) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public QueryPoiShippingResponse queryPoiShipping(QueryPoiShippingRequest queryPoiShippingRequest) {
        return new QueryPoiShippingResponse(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getCode(), ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg(), null);
    }

    @Override
    public QueryPoiShippingAreaResponse batchQueryPoiShippingAreaInfo(BatchQueryPoiShippingAreaRequest request) {
        return new QueryPoiShippingAreaResponse(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getCode(), ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg(), null);
    }

    @Override
    public QueryPoiShippingAreaResponse queryPoiShippingAreaInfo(QueryPoiShippingRequest request) {
        return new QueryPoiShippingAreaResponse(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getCode(), ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg(), null);
    }

    @Override
    public ResultStatus updatePoiRegularPeriodShippingByShippingAreaId(UpdatePoiRegularPeriodShippingByShippingAreaIdRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus updatePoiSpecialPeriodShippingByShippingAreaId(UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus deletePoiShippingByShippingAreaId(DeletePoiShippingByShippingIdRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    /***
     * 1.4 接口返回状态码说明
     * 所有操作如果成功则返回状态码0，如果失败状态码为非0
     * **/
    private ResultStatus getResultStatus(ChannelResponseDTO resultData) {
        if (Objects.isNull(resultData)) {
            return ResultGenerator.genFailResult("操作失败");
        } else if (resultData.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        } else {
            String errorMsg = resultData.getErrorMsg();
            return ResultGenerator.genFailResult(errorMsg);
        }
    }

    private PoiShippingUpdateParam convertPoiShippingUpdateParam(UpdatePoiShippingRequest request) {
        PoiShippingUpdateParam poiShippingUpdateParam = new PoiShippingUpdateParam();
        String shop_id = getAppPoiCode(request);
        poiShippingUpdateParam.setShop_id(shop_id);
        poiShippingUpdateParam.setDelivery_regions(Lists.newArrayList());
        DeliveryRegion deliveryRegion = new DeliveryRegion();
        poiShippingUpdateParam.getDelivery_regions().add(deliveryRegion);
        deliveryRegion.setDelivery_fee(String.valueOf(request.getShippingFee()));
        deliveryRegion.setDelivery_time(String.valueOf(request.getShippingTime()));
        deliveryRegion.setMin_order_free(String.valueOf(request.getMinOrderPrice()));
        deliveryRegion.setName("delivery" + System.currentTimeMillis());
        deliveryRegion.setRegion(Lists.newArrayList());
        for (Coordinate coordinate : request.getCoordinates()) {
            Region region = new Region();
            region.setLongitude(Double.valueOf(coordinate.getLongitude()).floatValue());
            region.setLatitude(Double.valueOf(coordinate.getLatitude()).floatValue());
            deliveryRegion.getRegion().add(region);
        }
        return poiShippingUpdateParam;
    }

    private String getAppPoiCode(UpdatePoiShippingRequest request) {
        Map<String, ChannelStoreDO> pois = copChannelStoreService.getChannelPoiCode(request.getTenantId(), request.getChannelId(), Arrays.asList(request.getShopId()));
        if (!pois.containsKey("" + request.getTenantId() + "-" + request.getChannelId() + "-" + request.getShopId())) {
            return null;
        }
        return pois.get("" + request.getTenantId() + "-" + request.getChannelId() + "-" + request.getShopId()).getChannelOnlinePoiCode();
    }
}
