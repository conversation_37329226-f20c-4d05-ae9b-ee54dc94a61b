package com.sankuai.meituan.shangou.empower.ocms.channel.constant;

public enum PayTypeEnum {
    TYPE_GOODS_FIRST(0L, "货到付款"),

    TYPE_WECHAT(1L, "微信"),

    TYPE_ALIPAY(2L, "支付宝"),

    TYPE_MMP(3L, "小程序"),

    TYPE_BANK_CARD(4L, "银行卡"),

    TYPE_BALANCE(5L, "余额"),

    TYPE_NONEED(7L, "无需支付(0元单)"),

    TYPE_DOU(8L, "DOU分期(信用支付)"),

    TYPE_NEW_CARD(9L, "新卡支付"),

    TYPE_LATER(12L, "先用后付"),

    TYPE_UNKNOWN(99L, "抖音新增的未知渠道"),;

    public static PayTypeEnum getType(Long typeValue) {
        for (PayTypeEnum payTypeEnum : PayTypeEnum.values()) {
            if (payTypeEnum.getValue().equals(typeValue)) {
                return payTypeEnum;
            }
        }
        // 如果typeValue为 null，代码也不会报错，并且会走到这里返回 UNKNOWN
        return TYPE_UNKNOWN;
    }

    private Long value;
    private String desc;

    PayTypeEnum(Long value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Long getValue() {
        return value;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
