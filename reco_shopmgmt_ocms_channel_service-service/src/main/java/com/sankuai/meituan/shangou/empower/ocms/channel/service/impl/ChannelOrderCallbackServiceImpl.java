package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.doudian.open.utils.JsonUtil;
import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.meituan.mtrace.Tracer;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.common.enums.*;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.model.PartRefundProductInfo;
import com.meituan.shangou.saas.mq.*;
import com.meituan.shangou.saas.o2o.dto.model.*;
import com.meituan.shangou.saas.o2o.dto.request.*;
import com.meituan.shangou.saas.o2o.dto.response.*;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.o2o.service.QnhOrderChannelThriftService;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.meituan.shangou.saas.tenant.thrift.TenantThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.TenantBaseDto;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.request.OldQnhSearchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.QnhTenantMigrateListResponse;
import com.meituan.shangou.saas.utils.OcmsSplitTraceUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ChannelConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeadlineTimeDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.delivery.ExtJsonInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.delivery.FarmPaoTuiDeliveryCallbackRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.drunkhorse.AfterSaleCommonOpScenario;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.drunkhorse.DrunkHorsePartRefundGoodsDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.MockTestException;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.StoreIdNotExistException;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.message.ChannelPoiCallbackMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.FarmPaoTuiPostStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ebls.IMMessageBody;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ebls.IMMessageContent;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ebls.ReplyMessagePayload;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm.ElmChannelOrderServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm.ElmRefundService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.health.HealthChannelRiderTransferRiderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj.JddjAfterSaleService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.common.MedicineTenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.orderTrack.OrderTrackService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.tenant.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd.TxdChannelOrderCallbackService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.route.RouteServiceFactory;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.PayStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.LockOrderStateEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.ServiceTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.*;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSpuMessageCallBackDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ChannelSpuMessageCallBackTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.ChannelSpuMessageCallBackRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.DeliveryCallbackThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.SelfDeliveryCallbackReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.ChannelPoiBaseInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.AppInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByPoiRequest;
import com.sankuai.sgfulfillment.comment.thrift.ChannelCommentThriftService;
import com.sankuai.sgfulfillment.comment.thrift.dto.CommentSyncResponse;
import com.sankuai.sgfulfillment.comment.thrift.dto.SyncJddjCommentRequest;
import joptsimple.internal.Strings;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.PRE_REFUND_GOODS_SHIPPING_FEE;
import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.SUCCESS;
import static com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil.getChangeOrderRedisLockSeconds;
import static com.sankuai.meituan.shangou.empower.ocms.channel.utils.OrderStatusConverter.jddjInvoiceTypeMapping;

/**
 * @description: 渠道订单回调接口实现
 * @author: zhaolei12
 * @create: 2019/1/28 上午10:22
 */
@Service("channelOrderCallbackService")
public class ChannelOrderCallbackServiceImpl implements ChannelOrderCallbackService {

    @Resource
    private RouteServiceFactory routeServiceFactory;

    @Resource
    private ChannelOrderThriftServiceProxy channelOrderThriftServiceProxy;

    @Resource
    private ChannelOrderConverterService channelOrderConverterService;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private MtConverterService mtConverterService;

    @Resource
    private ElmConverterService elmConverterService;

    @Resource
    private ChannelDeliveryService channelDeliveryService;

    @Resource(name = "mtBrandChannelOrderService")
    private MtChannelOrderFormParser mtChannelOrderFormParser;

    @Resource
    private JddjAfterSaleService jddjAfterSaleService;

    @Resource
    private JddjConverterService jddjConverterService;

    @Resource
    private DrunkHorseConverterService drunkHorseConverterService;

    @Resource
    private TmsThriftServiceProxy tmsThriftServiceProxy;

    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Autowired
    private ChannelPaoTuiService channelPaoTuiService;

    @Resource
    private ElmChannelOrderServiceImpl elmChannelOrderService;

    @Resource
    private ElmRefundService elmRefundService;

    @Resource
    private DeliveryOrderCheckWrapper deliveryOrderCheckWrapper;

    @Resource
    private DeliveryOperationThriftService deliveryOperationThriftService;

    @Resource
    private HealthChannelOrderSensitiveHelper healthChannelOrderSensitiveHelper;

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private CommonLogger log;

    @Value("${maltfarm.url.callback}")
    private String callbackUrl;

    @Value("${dap.url.callback}")
    private String dapCallbackUrl;

    @Autowired
    private ChannelOrderNotifyMessageProducer channelOrderNotifyMessageProducer;

    @Autowired
    private NewSupplyChannelOrderNotifyMessageProducer newSupplyChannelOrderNotifyMessageProducer;

    @Autowired
    private NewSupplyChannelOrderMtNotifyMessageProducer newSupplyChannelOrderMtNotifyMessageProducer;

    @Autowired
    private NewSupplyChannelOrderJddjNotifyMessageProducer newSupplyChannelOrderJddjNotifyMessageProducer;

    @Autowired
    private NewSupplyChannelOrderElemNotifyMessageProducer newSupplyChannelOrderElemNotifyMessageProducer;

    @Autowired
    private NewSupplyChannelOrderDeliveryMessageProducer newSupplyChannelOrderDeliveryMessageProducer;

    @Autowired
    private NewSupplyChannelMtOrderDeliveryMessageProducer newSupplyChannelMtOrderDeliveryMessageProducer;

    @Autowired
    private NewSupplyChannelElemOrderDeliveryMessageProducer newSupplyChannelElemOrderDeliveryMessageProducer;

    @Autowired
    private NewSupplyChannelJddjOrderDeliveryMessageProducer newSupplyChannelJddjOrderDeliveryMessageProducer;

    @Autowired
    private NewSupplyChannelChangeOrderNotifyMessageProducer newSupplyChannelChangeOrderNotifyMessageProducer;

    @Autowired
    private ChannelOrderRefundMessageProducer channelOrderRefundMessageProducer;

    @Autowired
    private NewSupplyChannelOrderRefundMessageProducer newSupplychannelOrderRefundMessageProducer;

    @Autowired
    private NewSupplyChannelMtOrderRefundMessageProducer newSupplyChannelMtOrderRefundMessageProducer;

    @Autowired
    private NewSupplyChannelJddjOrderRefundMessageProducer newSupplyChannelJddjOrderRefundMessageProducer;

    @Autowired
    private NewSupplyChannelElemOrderRefundMessageProducer newSupplyChannelElemOrderRefundMessageProducer;

    @Autowired
    private ChannelPressureTestOrderNotifyMessageProducer channelPressureTestOrderNotifyMessageProducer;

    @Autowired
    private NewSupplyYzDeliveryChangeMessageProducer newSupplyYzDeliveryChangeMessageProducer;

    @Autowired
    private ChannelSpuCallbackProducerWrapper callbackProducerWrapper;

    @Autowired
    private ChannelPoiThriftServiceProxy channelPoiThriftServiceProxy;

    @Autowired
    private DeliveryCallbackThriftService deliveryCallbackThriftService;

    @Resource
    private OrderTrackService orderTrackService;

    @Autowired
    private ChannelPoiCallbackMessageProducer channelPoiCallbackMessageProducer;

    @Autowired
    private DxPushMessageProducer dxPushMessageProducer;

    @Autowired
    private TenantThriftService tenantThriftService;
    @Resource
    private BizOrderThriftService bizOrderThriftService;

    @Resource
    private ChannelCommentThriftService channelCommentThriftService;

    @Autowired
    IEblsIMMessageTranfer eblsImService;

    @Resource
    private NewSupplyDeliveryCacheService newSupplyDeliveryCacheService;

    @Resource
    private TenantService tenantService;

    @Resource
    private MedicineTenantService medicineTenantService;

    @Autowired
    TenantRemoteService tenantRemoteService;

    @Resource
    private TxdChannelOrderCallbackService txdChannelOrderCallbackService;

    @Resource
    private HealthChannelRiderTransferRiderService healthChannelRiderTransferRiderService;

    @Resource
    private DrunkHorseOrderService drunkHorseOrderService;

    @Resource
    private DrunkHorseCacheService drunkHorseCacheService;

    @Resource
    private PoiApiChannelPoiThriftServiceProxy poiApiChannelPoiThriftServiceProxy;

    @Resource
    private PoiChannelAppThriftServiceProxy poiChannelAppThriftServiceProxy;

    @Resource
    private NewSupplyOrderCacheService newSupplyOrderCacheService;

    @Resource
    private QnhOrderChannelThriftService qnhOrderChannelThriftService;


    @Resource
    private SelfDeliveryOrderProducer selfDeliveryOrderProducer;

    private final static Integer IS_AFTERSALE_LAST_APPLY = 1;
    /**
     * 退款图片最大长度限制
     */
    private final static Integer MAX_REFUND_PIC_LENGTH = 700;
    /**
     * 用户拒收service标记
     */
    private final static String REJECT_BY_CUSTOMER = "9";

    private static final String NEW_ORDER_RECEIVE_FIALED_REPORT = "告警类型: 订单生单失败\n渠道: %s\n租户appId: %s\n渠道门店: %s\n订单号: %s\n消息类型: %s\nTraceId: %s\n异常原因: %s\n";

    public static final String NEW_ORDER_CREATE_FAIL_CATEGORY = "new_order_failed_push_dx";

    public static final String JD_ADJUST_ORDER_PREFIX = "JDTZ";

    @Override
    public ResultStatus orderNotify(OrderNotifyRequest request) {
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        if (Objects.isNull(channelTypeEnum)) {
            log.error("ChannelOrderCallbackServiceImpl.orderNotify, 未知渠道编码, request:{}", request);
            return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
        }
        ChannelNotifyEnum notifyEnum = EnumUtil.getEnumByAbbrev(request.getAction(), ChannelNotifyEnum.class);
        if (Objects.isNull(notifyEnum)) {
            log.info("未知action，忽略该请求:{}", request);
            ResultStatus status = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK).setMsg("未知action");
            return wrapResult(channelTypeEnum, status, request.getAction(), request);
        }
        //1.区分渠道channelCode
        //2.区分消息action（订单状态消息、配送状态消息、订单修改消息等）需要枚举
        //3.区分状态status（订单状态、配送状态）
        //4.调用中台订单模块
        //5.返回处理结果（不同渠道要求的code不同，美团识别data，饿百京东识别code）
        ResultStatus resultStatus = null;
        String exceptionMsg = "";
        try {
            switch (channelTypeEnum) {
                case MEITUAN:
                case MT_MEDICINE:
                    resultStatus = mtNotifyHandler(channelTypeEnum, notifyEnum, request);
                    break;
                case ELEM:
                    resultStatus = CallBackResultUtil.genResult(getChannelSysParams(channelTypeEnum, request), elmNotifyHandler(channelTypeEnum, notifyEnum, request), request.getAction());
                    break;
                case JD2HOME:
                    resultStatus = CallBackResultUtil.genResult(jddjNotifyHandler(channelTypeEnum, notifyEnum, request));
                    break;
                case YOU_ZAN:
                    resultStatus = yzNotifyHandler(notifyEnum, request);
                    break;
                case TXD:
                    resultStatus = CallBackResultUtil.genResultForTxd(txdChannelOrderCallbackService.txdNotifyHandler(notifyEnum, request));
                    break;
                case MT_DRUNK_HORSE:
                    return drunkHorseNotifyHandler(notifyEnum, request);
                default:
                    return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
            }
        }
        catch (StoreIdNotExistException e) {
            log.info("门店不存在，中台未接该门店，忽略该请求:{}", request);
            ResultStatus status = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK).setMsg("未知门店");
            return wrapResult(channelTypeEnum, status, request.getAction(), request);
        }
        catch (MockTestException e) {
            log.info("测试订单，忽略该请求:{}", request);
            ResultStatus status = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK).setMsg("测试订单");
            return wrapResult(channelTypeEnum, status, request.getAction(), request);
        } catch (IllegalArgumentException e) {
            exceptionMsg = "可能出现租户配置错误:{} ,req:{}";
            log.warn(exceptionMsg, e.getMessage(), request, e);
        } catch (Exception e) {
            exceptionMsg = "处理渠道消息出现未知异常。";
            log.error(exceptionMsg, e);
        }
        recordMetricAndSendDxMsg(notifyEnum, channelTypeEnum, resultStatus, request, exceptionMsg);
        return resultStatus;
    }

    private void recordMetricAndSendDxMsg(ChannelNotifyEnum notifyEnum,ChannelTypeEnum channelTypeEnum,
                                          ResultStatus resultStatus,OrderNotifyRequest request,String exceptionMsg) {
        try {
            String action = notifyEnum.getAbbrev();
            MetricHelper metricHelper = MetricHelper.build().name("new_order_delay").tag("channel", channelTypeEnum.getDesc())
                    .tag("action", action).tag("type","ocms");
            String result = resultStatus == null
                    || !Objects.equals(resultStatus.getCode(), ResultCode.SUCCESS.getCode()) ? "fail" : "success";
            metricHelper.tag("result", result).count();
            StoreKey storeKey = new StoreKey(NEW_ORDER_CREATE_FAIL_CATEGORY, request.getOrderId());
            if (judgePushDxFilter(request.getTenantAppId(), action, channelTypeEnum.getCode(),exceptionMsg,resultStatus)
                    && !newSupplyOrderCacheService.isExistStoreKey(storeKey)) {
                if ("fail".equals(result)) {
                    Long tenantId = tenantRemoteService.getTenantIdParam(channelTypeEnum.getCode(),
                            request.getTenantAppId());
                    TenantBaseDto tenantBaseDto = tenantRemoteService.queryTenantBaseInfo(tenantId);
                    String tenantName = tenantBaseDto != null ? tenantBaseDto.getTenantName() : "tenantId";
                    String msg = String.format(NEW_ORDER_RECEIVE_FIALED_REPORT, channelTypeEnum.getDesc(), tenantName,
                            request.getAppPoiCode(), request.getOrderId(), notifyEnum.getDesc(), Tracer.id(),
                            resultStatus != null ? resultStatus.getMsg() : "处理渠道消息出现未知异常");
                    DspDxPushHttpsUtils.pushWithMisIds(msg, MccConfigUtil.getDxMsgReceivers().toArray(new String[0]));
                    newSupplyOrderCacheService.setValue(storeKey, "1", 300);
                }
            }
        } catch (Exception e) {
            log.warn("埋点发送大象消息异常。", e);
        }
    }

    private boolean judgePushDxFilter(String tenantAppId,String action,Integer channelId,String exceptionMsg,ResultStatus resultStatus) {
        List<String> tenantIds = MccConfigUtil.getDxPushFilter().get("tenantId");
        List<String> actions = MccConfigUtil.getDxPushFilter().get("action");
        List<String> channelIds = MccConfigUtil.getDxPushFilter().get("channelId");
        List<String> msgs = MccConfigUtil.getDxPushFilter().get("msg");
        //异常信息过滤
        boolean msgFilter = msgs.stream().anyMatch(
                msg -> (Optional.ofNullable(exceptionMsg).orElse("").contains(msg))
                        || Optional.ofNullable((resultStatus != null ? resultStatus.getMsg() : "")).orElse("").contains(msg));
        return !tenantIds.contains(tenantAppId) && actions.contains(action)
                && !channelIds.contains(String.valueOf(channelId)) && !msgFilter;
    }


    private ResultStatus wrapResult(ChannelTypeEnum channelTypeEnum, ResultStatus status,
                                    String notifyAction, OrderNotifyRequest request) {
        ResultStatus finalResult = null;

        switch (channelTypeEnum) {
            case JD2HOME:
                finalResult = CallBackResultUtil.genResult(status);//京东返回结果
                break;
            case ELEM:
                if (ResultCodeEnum.SUCCESS.getValue() == status.getCode()) {
                    status.setMsg(ResultCode.SUCCESS.getMsg());
                    if (ChannelNotifyEnum.ELM_ORDER_CREATE.getAbbrev().equals(notifyAction)) {
                        status.setData(request.getOrderId());
                    }
                }
                Map<String, Object> config = Collections.emptyMap();
                // 饿了么ISV不再查询请求参数
                if (!MccConfigUtil.checkElmIsvAppKey(request.getTenantAppId())) {
                    config = getChannelSysParams(channelTypeEnum, request);
                }
                finalResult = CallBackResultUtil.genResult(config, status, notifyAction);//饿了么返回结果
                break;
            case MEITUAN:
            case MT_MEDICINE:
                finalResult = status;//美团结果，不需要封装
                break;
            default:
                finalResult = status;//默认按美团结果，不需要封装
                break;
        }
        return finalResult;
    }


    /**
     * 构建订单状态变更请求参数
     */
    @Data
    private class OrderStatusChangeRequestBuilder {
        private String orderId;
        private String appPoiCode;
        private String tenantAppId;
        private String status;
        private String timestamp;
        private String remark;
        private ChannelTypeEnum channelTypeEnum;
        private ChannelOrderDetailDTO orderDetailDTO;
        private Integer auditType;
        private OrderStatusChangeRequest build() {
            OrderStatusChangeRequest statusChangeRequest = new OrderStatusChangeRequest();
            statusChangeRequest.setChannelOrderId(orderId);
            statusChangeRequest.setChannelId(channelTypeEnum.getCode());
            statusChangeRequest.setChangeTime(ConverterUtils.stringToMillis(timestamp));
            statusChangeRequest.setRemark(remark);
            Long storeId = null;
            Long appId = NumberUtils.LONG_ZERO;
            Long tenantId = NumberUtils.LONG_ZERO;
            // 饿了么ISV应用
            if (ChannelTypeEnum.ELEM.equals(channelTypeEnum) && MccConfigUtil.checkElmStoreId(tenantAppId) && MccConfigUtil.checkElmIsvAppKey(tenantAppId)) {
                log.info("饿了么ISV应用:{}查询订单状态变化请求参数",tenantAppId);
                Optional<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTO = poiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO(appPoiCode, false);
                storeId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getPoiId).orElse(null);
                statusChangeRequest.setStoreId(storeId);
                statusChangeRequest.setTenantId(channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getTenantId).orElse(NumberUtils.LONG_ZERO));
                statusChangeRequest.setAppId(channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getQnhAppId).orElse(NumberUtils.LONG_ZERO));
            } else { // 非饿了么ISV应用
                CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), tenantAppId);
                appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
                statusChangeRequest.setTenantId(getTenantIdParam(accessConfig, tenantAppId));
                statusChangeRequest.setAppId(appId);
                if (StringUtils.isNotEmpty(appPoiCode) && accessConfig != null) {
                    storeId = copChannelStoreService.selectChannelStoreId(accessConfig.getTenantId(), accessConfig.getChannelId(),
                            appPoiCode);
                    statusChangeRequest.setStoreId(storeId);
                }
            }
            if (orderDetailDTO != null && MccConfigUtil.mtOrderNotifyUseMsgDetailSwitch(statusChangeRequest.getTenantId())) {
                //如果订单详情已经存在，直接使用订单详情
                log.info("订单详情不为空,order:{}", orderId);
                checkShopId(orderDetailDTO, statusChangeRequest.getTenantId());
                if (statusChangeRequest.getStoreId() <= NumberUtils.LONG_ZERO){
                    statusChangeRequest.setStoreId(orderDetailDTO.getStoreId());
                }
                statusChangeRequest.setOrderJsonString(JSON.toJSONString(orderDetailDTO));
            }
            else {
                log.info("获取订单详情,order:{}, tenantId:{}, storeId:{}", orderId, statusChangeRequest.getTenantId(), storeId);
                ChannelOrderDetailDTO orderDetail = getOrderDetail(statusChangeRequest.getTenantId(), channelTypeEnum.getCode(), orderId, appId, storeId);
                this.setOrderDetailDTO(orderDetail);
                if (statusChangeRequest.getStoreId() <= NumberUtils.LONG_ZERO){
                    statusChangeRequest.setStoreId(orderDetail.getStoreId());
                }
                statusChangeRequest.setOrderJsonString(orderDetail == null ? "" : JSON.toJSONString(orderDetail));
            }

            statusChangeRequest.setAuditType(auditType);
            return statusChangeRequest;
        }
    }

    /**
     * 查询数据库获取租户ID
     */
    private Long getTenantIdParam(int channelId, String tenantAppId) {
        Long tenantId = copAccessConfigService.selectTenantId(channelId, tenantAppId);
        Long qnhTenantId=MccConfigUtil.getQnhTenantId(tenantAppId);
        if(qnhTenantId!=0L) {
            log.info("tenantId:{},qnhTenantId:{}", tenantId, qnhTenantId);
            tenantId = qnhTenantId;
        }
        if (Objects.isNull(tenantId)) {
            log.error("ChannelOrderCallbackServiceImpl.getTenantIdParam, 未获取到渠道租户ID, channelId:{}, tenantAppId:{}",
                    channelId, tenantAppId);
            return 0L;
        }
        return tenantId;
    }





    /**
     * 查询数据库获取租户ID
     */
    private Long getTenantIdParam(CopAccessConfigDO copAccessConfig, String tenantAppId) {
        Long tenantId = Optional.ofNullable(copAccessConfig).map(CopAccessConfigDO::getTenantId).orElse(null);
        Long qnhTenantId = MccConfigUtil.getQnhTenantId(tenantAppId);
        if (qnhTenantId != 0L) {
            log.info("tenantId:{},qnhTenantId:{}", tenantId, qnhTenantId);
            tenantId = qnhTenantId;
        }
        if (Objects.isNull(tenantId)) {
            return 0L;
        }
        return tenantId;
    }

    private String getJddjSecret(int channelId, String tenantAppId) {
        Map<String, Object> appChannelSysParams = getAppChannelSysParams(channelId, tenantAppId);

        if (appChannelSysParams == null || appChannelSysParams.get(ProjectConstant.SECRET) == null) {
            log.error("ChannelOrderCallbackServiceImpl.getJddjSecret, 未查询到京东对应的密钥, channelId:{}, tenantAppId:{}",
                    channelId, tenantAppId);
            throw new BizException("未查询到京东对应的密钥");
        }

        String secret = (String) appChannelSysParams.get(ProjectConstant.SECRET);

        if (StringUtils.isBlank(secret)) {
            log.error("ChannelOrderCallbackServiceImpl.getJddjSecret, 查询到京东对应的密钥为空, channelId:{}, tenantAppId:{}",
                    channelId, tenantAppId);
            throw new BizException("查询到京东对应的密钥为空");
        }

        return secret;
    }

    private Map<String, Object> getAppChannelSysParams(int channelId, String tenantAppId) {

        CopAccessConfigDO copAccessConfigDO = getAccessConfig(channelId, tenantAppId);

        if (copAccessConfigDO == null || StringUtils.isBlank(copAccessConfigDO.getSysParams())) {
            log.error("ChannelOrderCallbackServiceImpl.getAppChannelSysParams, 未获取到渠道租户信息, channelId:{}, tenantAppId:{}",
                    channelId, tenantAppId);
            return null;
        }

        Map<String, Object> sysParam = JSON.parseObject(copAccessConfigDO.getSysParams());

        return sysParam;
    }

    /**
     * 查询数据库获取租户ID
     */
    private CopAccessConfigDO getAccessConfig(int channelId, String tenantAppId) {
        CopAccessConfigDO copAccessConfig = copAccessConfigService.selectByTenantAppIdAndChannelId(tenantAppId, channelId);
        if (copAccessConfig == null) {
            log.error("ChannelOrderCallbackServiceImpl.getTenantIdParam, 未获取到渠道租户ID, channelId:{}, tenantAppId:{}",
                    channelId, tenantAppId);
        }
        return copAccessConfig;
    }

    private ChannelOrderStatus getOrderStatus(long tenantId, int channelId, String orderId, long appId, Long storeId) {
        GetOrderStatusRequest request =
                new GetOrderStatusRequest().setTenantId(tenantId).setChannelId(channelId).setOrderId(orderId).setAppId(appId);
        if (storeId != null) {
            request.setStoreId(storeId);
        }
        GetOrderStatusResult result = routeServiceFactory.selectChannelOrderService(channelId, tenantId).getOrderStatus(request);

        if (Objects.nonNull(result) && result.getOrderStatus() != null) {
            return ChannelOrderStatus.findByValue(result.getOrderStatus().getStatus());
        }

        return null;
    }

    private ChannelOrderDetailDTO getOrderDetail(long tenantId, int channelId, String orderId, long appId, Long storeId) {
        GetChannelOrderDetailRequest request =
                new GetChannelOrderDetailRequest().setTenantId(tenantId).setChannelId(channelId).setOrderId(orderId).setAppId(appId);
        if (storeId != null) {
            request.setSotreId(storeId);
        }
        GetChannelOrderDetailResult result = routeServiceFactory.selectChannelOrderService(channelId, tenantId).getChannelOrderDetail(request);
        if (Objects.nonNull(result) && result.getStatus().getCode() == ResultCode.SUCCESS.getCode() && result.getChannelOrderDetail() != null) {
            checkShopId(result.getChannelOrderDetail(), tenantId);
            return result.getChannelOrderDetail();
        }
        checkJddjIsMock(tenantId, channelId, orderId);
        return null;
    }

    private String getOrderDetail(long tenantId, int channelId, String orderId, long appId) {
        GetChannelOrderDetailRequest request =
                new GetChannelOrderDetailRequest().setTenantId(tenantId).setChannelId(channelId).setOrderId(orderId).setAppId(appId);
        GetChannelOrderDetailResult result = routeServiceFactory.selectChannelOrderService(channelId, tenantId).getChannelOrderDetail(request);
        if (Objects.nonNull(result) && result.getStatus().getCode() == ResultCode.SUCCESS.getCode() && result.getChannelOrderDetail() != null) {
            checkShopId(result.getChannelOrderDetail(), tenantId);
            return JSON.toJSONString(result.getChannelOrderDetail());
        }
        checkJddjIsMock(tenantId, channelId, orderId);
        return "";
    }

    private Long getShopId(String orderDetail){
        try {
            if(StringUtils.isNotBlank(orderDetail)) {
                ChannelOrderDetailDTO channelOrderDetailDTO = JSON.parseObject(orderDetail, ChannelOrderDetailDTO.class);
                if (Objects.nonNull(channelOrderDetailDTO)) {
                    return channelOrderDetailDTO.getStoreId();
                }
            }
        }catch (Exception e){
            log.error("从订单信息中获取门店失败", e);
        }
        return NumberUtils.LONG_ZERO;
    }


    private String getOrderDetail(long tenantId, int channelId, long storeId, String orderId, long appId) {
        GetChannelOrderDetailRequest request =
                new GetChannelOrderDetailRequest().setTenantId(tenantId).setChannelId(channelId).setOrderId(orderId).setAppId(appId).setSotreId(storeId);
        GetChannelOrderDetailResult result = routeServiceFactory.selectChannelOrderService(channelId, tenantId).getChannelOrderDetail(request);
        if (Objects.nonNull(result) && result.getStatus().getCode() == ResultCode.SUCCESS.getCode() && result.getChannelOrderDetail() != null) {
            checkShopId(result.getChannelOrderDetail(), tenantId);
            return JSON.toJSONString(result.getChannelOrderDetail());
        }
        checkJddjIsMock(tenantId, channelId, orderId);
        return "";
    }

    private ChannelOrderDetailDTO getOrderDetailDTO(long tenantId, int channelId, String orderId, long appId) {
        GetChannelOrderDetailRequest request =
                new GetChannelOrderDetailRequest().setTenantId(tenantId).setChannelId(channelId).setOrderId(orderId).setAppId(appId);
        GetChannelOrderDetailResult result = routeServiceFactory.selectChannelOrderService(channelId, tenantId).getChannelOrderDetail(request);
        if (Objects.nonNull(result) && result.getStatus().getCode() == ResultCode.SUCCESS.getCode() && result.getChannelOrderDetail() != null) {
            checkShopId(result.getChannelOrderDetail(), tenantId);
            return result.getChannelOrderDetail();
        }
        checkJddjIsMock(tenantId, channelId, orderId);
        return null;
    }

    private GoodsSettlementResult queryGoodsSettlementInfo(long tenantId, int channelId, String orderId, long storeId) {
        GoodsSettlementInfoRequest request =
                new GoodsSettlementInfoRequest().setTenantId(tenantId).setChannelId(channelId).setOrderId(orderId).setStoreId(storeId);
        GoodsSettlementResult result = routeServiceFactory.selectChannelOrderService(channelId, tenantId).queryGoodsSettlementInfo(request);
        if (Objects.nonNull(result) && result.getStatus().getCode() == ResultCode.SUCCESS.getCode()) {
            return result;
        }
        return null;
    }

    private OrderAfsApplyDTO getAfsDetail(long tenantId, long shopId, int channelId, String orderId, long appId, String channelAfsId) {
        GetOrderAfsApplyListRequest request =
                new GetOrderAfsApplyListRequest()
                        .setTenantId(tenantId)
                        .setStoreId(shopId)
                        .setChannelOrderId(orderId)
                        .setAppId(appId)
                        .setChannelType(channelId)
                        .setAfterSaleId(channelAfsId);
        GetOrderAfsApplyListResult result = routeServiceFactory.selectChannelOrderService(channelId, tenantId).getOrderAfsApplyList(request);
        if (Objects.nonNull(result) && result.getStatus().getCode() == ResultCode.SUCCESS.getCode() && CollectionUtils.isNotEmpty(result.getAfsApplyList())) {
            Optional<OrderAfsApplyDTO> orderAfsApplyDTO = result.getAfsApplyList().stream().filter(v -> Objects.equals(channelAfsId, v.getAfterSaleId())).findFirst();
            return orderAfsApplyDTO.orElse(null);
        }
        checkJddjIsMock(tenantId, channelId, orderId);
        return null;
    }

    /***
     * 检查storeId是否存在，如果不存在说明商家没有接中台，忽略该请求
     * **/
    private void checkShopId(ChannelOrderDetailDTO channelOrderDetail, long tenantId) {
        long storeId = channelOrderDetail.getStoreId();
        if (ConfigUtilAdapter.getBoolean("check.shop.ignore.tenant." + tenantId, false)) {
            log.info("租户：{}不校验门店是否存在, order:{}", tenantId, channelOrderDetail.getChannelOrderId());
            return;
        }
        else if (storeId <= 0) {
            log.info("租户:{}， 门店ID非法:{}, order:{}", tenantId, storeId, channelOrderDetail.getChannelOrderId());
            MetricHelper.build().name("order.shop.not.exit").tag("tenantId", String.valueOf(tenantId)).tag("shopName", channelOrderDetail.getChannelStoreName()).count(1);
            throw new StoreIdNotExistException();
        }

    }

    private void checkJddjIsMock(long tenantId, int channelType, String channelOrderId) {
        if (ChannelTypeEnum.JD2HOME.getCode() == channelType) {
            //如果租户开启了回调测试 且 京东的订单在沙箱环境能找到
            if (MccConfigUtil.getJddjMockResponseTenant().getTenantIdList().contains(tenantId)
                //&& isMockOrder(tenantId, channelType, channelOrderId)//mock的数据也查不到订单
            ) {
                throw new MockTestException();
            }
        }
    }


    private ChannelOrderDetailDTO getOrderDetailModel(long tenantId, int channelId, String orderId,long appId) {
        GetChannelOrderDetailRequest request = new GetChannelOrderDetailRequest().setTenantId(tenantId).setChannelId(channelId).setOrderId(orderId).setAppId(appId);
        GetChannelOrderDetailResult result = routeServiceFactory.selectChannelOrderService(channelId).getChannelOrderDetail(request);
        if (Objects.nonNull(result) && result.getStatus().getCode() == ResultCode.SUCCESS.getCode()) {
            return result.getChannelOrderDetail();
        }
        return null;
    }

    /**
     * 设置配送状态
     */
    private void installDeliveryStatus(ChannelTypeEnum channelTypeEnum, DeliveryStatusChangeRequest statusChangeRequest, String status) {
        try {
            int deliveryStatus = Integer.parseInt(status);
            switch (channelTypeEnum) {
                case MEITUAN:
                case MT_MEDICINE:
                    statusChangeRequest.setDeliveryStatus((ChannelStatusConvertUtil.mtDeliveryStatusMapping(deliveryStatus)));
                    break;
                case ELEM:
                    statusChangeRequest.setDeliveryStatus((ChannelStatusConvertUtil.elmDeliveryStatusMapping(deliveryStatus)));
                    break;
                case JD2HOME:
                    statusChangeRequest.setDeliveryStatus((ChannelStatusConvertUtil.jddjDeliveryStatusMapping(deliveryStatus)));
                    break;
                default:
                    log.warn("ChannelOrderCallbackServiceImpl.callOrderStatusChange, 未知渠道编码, channelTypeEnum:{}", channelTypeEnum);
            }
        }
        catch (NumberFormatException e) {
            log.error("ChannelOrderCallbackServiceImpl.callOrderStatusChange, 渠道配送状态类型转换异常, channelTypeEnum:{}, status:{}",
                    channelTypeEnum, status);
        }
    }

    /**
     * 设置自配送状态
     */
    private void installSelfDeliveryStatus(ChannelTypeEnum channelTypeEnum, DeliveryStatusChangeRequest statusChangeRequest, String status) {
        try {
            int deliveryStatus = Integer.parseInt(status);
            switch (channelTypeEnum) {
                case MEITUAN:
                case MT_MEDICINE:
                    statusChangeRequest.setDeliveryStatus((ChannelStatusConvertUtil.mtSelfDeliveryStatusMapping(deliveryStatus)));
                    break;
                default:
                    log.warn("ChannelOrderCallbackServiceImpl.callOrderStatusChange, 未知渠道编码, channelTypeEnum:{}", channelTypeEnum);
            }
        }
        catch (NumberFormatException e) {
            log.error("ChannelOrderCallbackServiceImpl.callOrderStatusChange, 渠道配送状态类型转换异常, channelTypeEnum:{}, status:{}",
                    channelTypeEnum, status);
        }
    }

    private void installRefundSku(ChannelTypeEnum channelTypeEnum, OrderPartRefundRequest partParam, String productJson, ChannelOrderDetailDTO orderDetailDTO, Long tenantId, String orderId, String tenantAppId) {
        switch (channelTypeEnum) {
            case MEITUAN:
            case MT_MEDICINE:
                Map<String, Long> customerSkuId2ChannelWeight = orderDetailDTO.getSkuDetails().stream().filter(item -> item.getWeight() > 0)
                        .collect(Collectors.toMap(OrderProductDetailDTO::getSkuId, OrderProductDetailDTO::getWeight, (v1, v2) -> v2));
                log.info("ChannelOrderCallbackServiceImpl.installRefundSku customerSkuId2ChannelWeight:{}", customerSkuId2ChannelWeight);
                List<RefundSku> products = mtConverterService.partRefundSkuInfoList(JSONObject.parseArray(productJson, ChannelPartRefundSkuInfo.class));
                if(!MccConfigUtil.isMtDrunkHorseTenant(tenantAppId) && !Objects.equals(channelTypeEnum, ChannelTypeEnum.MT_MEDICINE) && checkIsMtWeightRefund(products)){
                    buildPartParamForMeiTuan(partParam, tenantId, orderId, products);
                    break;
                }
                for (RefundSku refundSku: products){
                    Long skuWeight = customerSkuId2ChannelWeight.getOrDefault(refundSku.getSku(), 0L);
                    if(skuWeight > 0 && refundSku.getRefundWeight() > 0){
                        refundSku.setPartialRefundCount(BigDecimal.valueOf(refundSku.getRefundWeight()).divide(BigDecimal.valueOf(skuWeight), 3, RoundingMode.HALF_UP).doubleValue());
                    }
                }
                partParam.setProducts(products);
                break;
            case ELEM:
                partParam.setProducts(elmConverterService.refundSkuMappingList(JSONObject.parseArray(productJson, RefundProductDTO.class)));
                break;
            case JD2HOME:
                JddjAfterSaleDetail afterSaleDetail = JSON.parseObject(productJson, JddjAfterSaleDetail.class);
                partParam.setProducts(jddjConverterService.partRefundSkuInfoList(afterSaleDetail.getAfsDetailList()));
                break;
            default:
                log.warn("ChannelOrderCallbackServiceImpl.installRefundSku, 未知渠道编码, channelTypeEnum:{}", channelTypeEnum);
                break;
        }
    }

    private void buildPartParamForMeiTuan(OrderPartRefundRequest partParam, Long tenantId, String orderId, List<RefundSku> products){
        try {
            BizOrderQueryForItemRequest bizOrderRequest = BizOrderQueryForItemRequest.builder()
                    .orderBizType(DynamicOrderBizType.MEITUAN_WAIMAI.getValue())
                    .viewOrderId(orderId)
                    .tenantId(tenantId)
                    .fromMaster(true)
                    .build();
            BizOrderQueryResponse response = bizOrderThriftService.queryOrderAndItemByViewOrderId(bizOrderRequest);
            log.info("ChannelOrderCallbackServiceImpl.buildPartParamForMeiTuan bizOrderRequest:{}, bizOrderQueryResponse:{}", bizOrderRequest, response);
            Map<SkuAndSpuKey, BizOrderItemModel> bizOrderItemModelMap = new HashMap<>();
            if (Objects.nonNull(response) && Objects.nonNull(response.getBizOrderModel()) && Objects.nonNull(response.getBizOrderModel().getBizOrderItemModelList())) {
                bizOrderItemModelMap = response.getBizOrderModel().getBizOrderItemModelList().stream()
                        .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCustomerSkuId()) && Objects.nonNull(item.getCustomerSpu()) && Objects.nonNull(item.getChannelWeight()) && item.getChannelWeight() > 0)
                        .collect(Collectors.toMap(v -> new SkuAndSpuKey(v.getCustomerSkuId(), v.getCustomerSpu()), orderProductDetailDTO -> orderProductDetailDTO, (key1, key2) -> key1));
            }
            log.info("ChannelOrderCallbackServiceImpl.buildPartParamForMeiTuan products:{}, bizOrderItemModelMap:{}", products, bizOrderItemModelMap);
            for (RefundSku refundSku: products){
                BizOrderItemModel bizOrderItemModel = bizOrderItemModelMap.get(new SkuAndSpuKey(refundSku.getSku(), refundSku.getCustomSpuId()));
                if(Objects.nonNull(bizOrderItemModel) && bizOrderItemModel.getChannelWeight() > 0 && refundSku.getRefundWeight() > 0){
                    refundSku.setPartialRefundCount(BigDecimal.valueOf(refundSku.getRefundWeight()).divide(BigDecimal.valueOf(bizOrderItemModel.getChannelWeight()), 3, RoundingMode.HALF_UP).doubleValue());
                }
            }
            partParam.setProducts(products);
        }catch (Exception e){
            log.error("ChannelOrderCallbackServiceImpl.buildPartParamForMeiTuan error!", e);
        }
    }

    private void installRefundSku(ChannelTypeEnum channelTypeEnum, OrderPartRefundGoodsRequest partParam, String productJson) {
        switch (channelTypeEnum) {
            case MEITUAN:
            case MT_MEDICINE:
                partParam.setProducts(mtConverterService.partRefundSkuInfoList(JSONObject.parseArray(productJson, ChannelPartRefundSkuInfo.class)));
                break;
            case ELEM:
                partParam.setProducts(elmConverterService.refundSkuMappingList(JSONObject.parseArray(productJson, RefundProductDTO.class)));
                break;
            case JD2HOME:
                JddjAfterSaleDetail afterSaleDetail = JSON.parseObject(productJson, JddjAfterSaleDetail.class);
                partParam.setProducts(jddjConverterService.partRefundSkuInfoList(afterSaleDetail.getAfsDetailList()));
                break;
            default:
                log.warn("ChannelOrderCallbackServiceImpl.installRefundSku, 未知渠道编码, channelTypeEnum:{}", channelTypeEnum);
                break;
        }
    }

    private Map<String, Object> getChannelSysParams(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request) {
        // 饿了么ISV应用不在cop表里存储，获取参数直接查poi接口
        if (ChannelTypeEnum.ELEM.equals(channelTypeEnum) && MccConfigUtil.checkElmStoreId(request.getTenantAppId()) && MccConfigUtil.checkElmIsvAppKey(request.getTenantAppId())) {
            convertElmNotifyParam(channelTypeEnum, request, null);
            Optional<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTO = poiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO(request.getAppPoiCode(), false);
            Long tenantId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getTenantId).orElse(NumberUtils.LONG_ZERO);
            Long storeId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getPoiId).orElse(ProjectConstant.UNKNOW_STORE_ID);
            try {
                AppInfoDTO appInfoDTO = poiChannelAppThriftServiceProxy.queryAppInfoByPoi(new AppInfoQueryByPoiRequest(tenantId, storeId, ChannelTypeEnum.ELEM.getCode()));
                if (appInfoDTO != null) {
                    Map<String, Object> poiChannelSysParamMap = new HashMap<>();
                    poiChannelSysParamMap.put("source", appInfoDTO.getAppKey());
                    poiChannelSysParamMap.put("secret", appInfoDTO.getSecret());
                    poiChannelSysParamMap.put("version", 3);
                    poiChannelSysParamMap.put("access_token", appInfoDTO.getAccessToken());
                    return poiChannelSysParamMap;
                } else {
                    return Collections.emptyMap();
                }
            } catch (Exception e) {
                log.error("ChannelOrderCallbackServiceImpl.饿了么ISV应用:{}获取sysParam失败", request.getTenantAppId());
                return Collections.emptyMap();
            }
        }
        CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
        return accessConfig == null ? Collections.emptyMap() : JSON.parseObject(accessConfig.getSysParams());
    }

    private void convertElmNotifyParam(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request, ChannelNotifyEnum notifyEnum) {
        if (StringUtils.isNotBlank(request.getBody())) {
            String body = urlDecode(request.getBody());

            ChannelOrderNotify channelOrderNotify = JSON.parseObject(body, ChannelOrderNotify.class);
            request.setOrderId(channelOrderNotify.getOrder_id());
            request.setStatus(channelOrderNotify.getStatus());
            request.setDeliveryStatus(channelOrderNotify.getStatus());
            request.setNotifyType(channelOrderNotify.getType());
            if (MccConfigUtil.checkElmStoreId(request.getTenantAppId())) {
                request.setAppPoiCode(channelOrderNotify.getPlatform_shop_id());
            }

            if(StringUtils.isBlank(request.getAppPoiCode())){
                request.setAppPoiCode(channelOrderNotify.getShop_id());
            }

            /*
            if (notifyEnum == ChannelNotifyEnum.ELM_ORDER_USER_CANCEL) {
                request.setRefundId(channelOrderNotify.getRefund_order_id());
                request.setNotifyType(channelOrderNotify.getCancel_type());
                request.setPictureJson(channelOrderNotify.getPictures());
            }
            installReason(request, channelOrderNotify);
            request.setResType(channelOrderNotify.getType());
            request.setRefundPrice(channelOrderNotify.getRefund_price());
            request.setProductJson(channelOrderNotify.getRefund_products());
            request.setRefundApplyTime(request.getTimestamp());
            request.setIsAppeal(channelOrderNotify.isAppeal());
            if (notifyEnum == ChannelNotifyEnum.ELM_ORDER_PART_REFUND) {
                CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
                Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
                request.setRefundId(channelOrderNotify.getRefund_id());
                Map<String, List<RefundDetail>> refundDetailMap = getElmRefundRecord(
                        getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId()), request.getOrderId(), appId);
                if (MapUtils.isNotEmpty(refundDetailMap) && refundDetailMap.containsKey(request.getRefundId())) {
                    request.setRefundApplyTime(refundDetailMap.get(request.getRefundId()).get(0).getApply_time());
                }
                request.setPictureJson(channelOrderNotify.getPhotos());
            }
            */

            log.info("饿百转变之后的request:{}", request);
        }

    }

    private void convertJddjNotifyParam(OrderNotifyRequest request, ChannelNotifyEnum notifyEnum) {
        String encryptJdParamJson = request.getEncryptJdParamJson();
        if (StringUtils.isNotBlank(encryptJdParamJson)) {
            String secret = getJddjSecret(ChannelTypeEnum.JD2HOME.getCode(), request.getTenantAppId());

            try {
                String decryptJdParamJson = JddjAESUtils.decryptAES(encryptJdParamJson, secret);
                request.setJdParamJson(decryptJdParamJson);
            } catch (JddjAESUtils.JddjAesException e) {
                log.error("京东到家消息解密失败", e);
                throw new BizException("京东到家消息解密失败");
            }
        }

        if (StringUtils.isNotBlank(request.getJdParamJson())) {
            JdNotifyParamInfo paramInfo = JSON.parseObject(urlDecode(request.getJdParamJson()), JdNotifyParamInfo.class);
            request.setOrderId(StringUtils.isBlank(paramInfo.getBillId()) ? paramInfo.getOrderId() : paramInfo.getBillId());
            if (notifyEnum == ChannelNotifyEnum.JDDJ_PUSH_DELIVERY) {
                request.setDeliveryStatus(paramInfo.getDeliveryStatus());
                request.setDispatcherName(paramInfo.getDeliveryManName());
                request.setDispatcherMobile(paramInfo.getDeliveryManPhone());
                request.setTimestamp(paramInfo.getDeliveryStatusTime());
                request.setFailType(paramInfo.getFailType());
                request.setDealDeadline(paramInfo.getDealDeadline());
            }
            else {
                request.setStatus(paramInfo.getStatusId());
                if (notifyEnum == ChannelNotifyEnum.JDDJ_USER_CANCEL_ORDER_BEFORE_DELIVERY
                        || notifyEnum == ChannelNotifyEnum.JDDJ_LOCK_ORDER
                        || notifyEnum == ChannelNotifyEnum.JDDJ_CANCEL_ORDER
                ) {
                    request.setRefundReason(paramInfo.getRemark());
                    request.setRefundApplyTime(request.getTimestamp());
                }
            }
        }
        log.info("京东转变之后的request:{}", request);
    }

    private String urlDecode(String urlParam) {
        if (StringUtils.isNotBlank(urlParam)) {
            try {
                urlParam = URLDecoder.decode(urlParam, Charsets.UTF_8.displayName());
            }
            catch (Exception e) {
                log.warn("Exception:" + Charsets.UTF_8.displayName() + e.getMessage());
            }
        }
        return urlParam;
    }

    private void installReason(OrderNotifyRequest request, ChannelOrderNotify channelOrderNotify) {
        request.setCancelReason(channelOrderNotify.getReason());
        if (StringUtils.isNotBlank(channelOrderNotify.getAddition_reason())) {
            request.setCancelReason(StringUtils.join(channelOrderNotify.getReason(), ";", channelOrderNotify.getAddition_reason()));
        }
        request.setRefundReason(channelOrderNotify.getCancel_reason());
        if (StringUtils.isNotBlank(channelOrderNotify.getAddition_reason())) {
            request.setRefundReason(StringUtils.join(channelOrderNotify.getCancel_reason(), ";", channelOrderNotify.getAddition_reason()));
        }
        if (StringUtils.isNotBlank(channelOrderNotify.getRefuse_reason())) {
            request.setRefundReason(channelOrderNotify.getRefuse_reason());
        }
        if (StringUtils.isBlank(request.getCancelReason())) {
            request.setCancelReason(ProjectConstant.OTHER_REASON);
        }
        if (StringUtils.isBlank(request.getRefundReason())) {
            request.setRefundReason(ProjectConstant.OTHER_REASON);
        }
    }

    private Map<String, List<RefundDetail>> getElmRefundRecord(long tenantId, String orderId, Long appId) {
        PartRefundDetailInfo refundRecordForElm = channelDeliveryService.getRefundRecordForElm(tenantId, orderId, appId);
        if (Objects.isNull(refundRecordForElm)) {
            return Maps.newHashMap();
        }
        List<RefundDetail> refundDetailList = Lists.newArrayList();
        List<RefundDetail> refundList = refundRecordForElm.getRefund_detail();
        if (CollectionUtils.isNotEmpty(refundList)) {
            refundDetailList.addAll(refundList);
        }
        List<List<RefundDetail>> historyRefundList = refundRecordForElm.getHistory_refund_detail();
        if (CollectionUtils.isNotEmpty(historyRefundList)) {
            historyRefundList.forEach(refundDetailList::addAll);
        }
        if (CollectionUtils.isEmpty(refundDetailList)) {
            return Maps.newHashMap();
        }
        return refundDetailList.stream().filter(refundDetail ->
                        refundDetail != null && refundDetail.getRefund_id() != null)
                .collect(Collectors.groupingBy(RefundDetail::getRefund_id));
    }

    private OrderStatusChangeRequest buildOrderStatusChangeRequest(ChannelTypeEnum channelTypeEnum,
                                                                   OrderNotifyRequest request, ChannelOrderStatus status) {
        return buildOrderStatusChangeRequest(channelTypeEnum, request, null, status);
    }

    /**
     * 构造订单状态变更请求参数
     */
    private OrderStatusChangeRequest buildOrderStatusChangeRequest(ChannelTypeEnum channelTypeEnum,
                                                                   OrderNotifyRequest request, ChannelOrderDetailDTO orderDetailDTO, ChannelOrderStatus status) {
        OrderStatusChangeRequestBuilder builder = new OrderStatusChangeRequestBuilder();
        builder.setChannelTypeEnum(channelTypeEnum);
        builder.setTenantAppId(request.getTenantAppId());
        builder.setTimestamp(request.getTimestamp());
        builder.setOrderId(request.getOrderId());
        builder.setStatus(request.getStatus());
        builder.setRemark(request.getCancelReason());
        builder.setOrderDetailDTO(orderDetailDTO);
        builder.setAppPoiCode(request.getAppPoiCode());
        builder.setAuditType(MtAuditTypeServiceUtils.getAuditTypeFromCancel(request));
        OrderStatusChangeRequest orderStatusChangeRequest = builder.build().setOrderStatus(status.getValue());
        if (ChannelOrderStatus.FINISHED.equals(status)
                && Lion.getConfigRepository().getBooleanValue("switch.channel.order.complete.time", false)) {
            if (builder.getOrderDetailDTO() != null && builder.getOrderDetailDTO().getCompletedTime() > 0) {
                orderStatusChangeRequest.setChangeTime(builder.getOrderDetailDTO().getCompletedTime());
            }
        }

        if (ChannelOrderStatus.CANCELED.equals(status)) {
            if (builder.getOrderDetailDTO() != null && builder.getOrderDetailDTO().getCancelTime() > 0) {
                orderStatusChangeRequest.setChangeTime(builder.getOrderDetailDTO().getCancelTime());
            }
        }

        // 重置
        builder.setOrderDetailDTO(orderDetailDTO);
        dealLockOrderOperation(orderStatusChangeRequest, request);
        return orderStatusChangeRequest;
    }

    private void dealLockOrderOperation(OrderStatusChangeRequest orderStatusChangeRequest, OrderNotifyRequest request) {
        try {
            // 默认未锁单
            orderStatusChangeRequest.setLockOrderLabel(LockOrderStateEnum.UN_LOCK.getCode());
            orderStatusChangeRequest.setLockOrderPackageId(request.getMtPkgId());
            if (!MccConfigUtil.checkLockOrderTenant(orderStatusChangeRequest.getTenantId())) {
                log.info("ChannelOrderCallbackServiceImpl.dealLockOrderOperation MccConfigUtil is false");
                return;
            }
            // 需要隔离歪马租户
            if (MccConfigUtil.isMtDrunkHorseTenant(request.getTenantAppId())){
                log.info("ChannelOrderCallbackServiceImpl.dealLockOrderOperation isMtDrunkHorseTenant 歪马租户");
                return;
            }
            // 需要隔离医药租户
            try {
                List<Long> uwmsTenantIds = medicineTenantService.getAllUwmsTenantIds();
                if(uwmsTenantIds.contains(orderStatusChangeRequest.getTenantId())){
                    return;
                }
            }catch (Exception e){
                log.error("ChannelOrderCallbackServiceImpl.dealLockOrderOperation 查询医药租户出错 channelOrderId: {}, e= ", orderStatusChangeRequest.getChannelOrderId(), e);
            }

            // 设置锁单状态和承运商单号
            if (request.getDeliveryAvailable() == BoolTypeEnum.YES.getValue()) {
                orderStatusChangeRequest.setLockOrderLabel(request.getDeliveryAvailable());
            } else {
                orderStatusChangeRequest.setLockOrderLabel(request.getIsLockedOrder());
            }
        }catch (Exception e){
            log.error("ChannelOrderCallbackServiceImpl.dealLockOrderOperation is error channelOrderId: {}, e= ", orderStatusChangeRequest.getChannelOrderId(), e);
        }
    }

    /**
     * 构造订单信息变更请求参数
     */
    private OrderInfoChangeRequest buildOrderInfoChangeRequest(OrderInfoChangeType orderInfoChangeType, ChannelTypeEnum channelTypeEnum,
                                                               ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        OrderInfoChangeRequest orderInfoChangeRequest = new OrderInfoChangeRequest();
        orderInfoChangeRequest.setChannelOrderId(request.getOrderId());
        orderInfoChangeRequest.setChannelType(channelTypeEnum.getCode());
        orderInfoChangeRequest.setRemark(notifyEnum.getDesc());
        Long tenantId = NumberUtils.LONG_ZERO;
        Long appId = NumberUtils.LONG_ZERO;
        String orderDetailStr = "";
        // 饿了么ISV应用
        if (ChannelTypeEnum.ELEM.equals(channelTypeEnum) && MccConfigUtil.checkElmStoreId(request.getTenantAppId()) && MccConfigUtil.checkElmIsvAppKey(request.getTenantAppId())) {
            Optional<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTO = poiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO(request.getAppPoiCode(), false);
            orderInfoChangeRequest.setStoreId(channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getPoiId).orElse(NumberUtils.LONG_ZERO));
            tenantId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getTenantId).orElse(NumberUtils.LONG_ZERO);
            appId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getQnhAppId).orElse(NumberUtils.LONG_ZERO);
            ChannelOrderDetailDTO orderDetail = getOrderDetail(tenantId, channelTypeEnum.getCode(), request.getOrderId(), appId, orderInfoChangeRequest.getStoreId());
            orderDetailStr = orderDetail == null ? "" : JSON.toJSONString(orderDetail);
        } else {
            CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
            tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
            appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
            orderDetailStr = getOrderDetail(tenantId, channelTypeEnum.getCode(),
                        request.getOrderId(), appId);
        }
        orderInfoChangeRequest.setTenantId(tenantId);
        orderInfoChangeRequest.setAppId(appId);
        orderInfoChangeRequest.setTimestamp(ConverterUtils.stringToMillis(request.getTimestamp()));
        orderInfoChangeRequest.setOrderJsonString(orderDetailStr);
        orderInfoChangeRequest.setOrderInfoChangeType(orderInfoChangeType);

        if (orderInfoChangeType == OrderInfoChangeType.INVOICE) {
            if (StringUtils.isNotBlank(request.getJdParamJson())) {
                JdNotifyParamInfo paramInfo = JSON.parseObject(urlDecode(request.getJdParamJson()), JdNotifyParamInfo.class);
                // 转换京东发票消息

                orderInfoChangeRequest.setInvoiceType(jddjInvoiceTypeMapping(paramInfo.getInvoiceType()));
                orderInfoChangeRequest.setInvoiceTitle(paramInfo.getInvoiceTitle());
                orderInfoChangeRequest.setInvoiceTaxNo(paramInfo.getInvoiceDutyNo() == null ? "" : paramInfo.getInvoiceDutyNo());
            } else if(Objects.equals(channelTypeEnum, ChannelTypeEnum.ELEM)) {
                // 转换饿了么发票信息
                OrderChangeInfo orderChangeInfo = JsonUtil.fromJson(request.getBody(), OrderChangeInfo.class);
                OrderChangeInvoiceInfo orderChangeInvoiceInfo = orderChangeInfo.getInvoiceInfo();
                orderInfoChangeRequest.setInvoiceType(orderChangeInvoiceInfo.getInvoiceType());
                orderInfoChangeRequest.setInvoiceTitle(orderChangeInvoiceInfo.getInvoiceTitle() == null ? "" : orderChangeInvoiceInfo.getInvoiceTitle());
                orderInfoChangeRequest.setInvoiceTaxNo(orderChangeInvoiceInfo.getTaxerId() == null ? "" : orderChangeInvoiceInfo.getTaxerId());
            }
        }

        return orderInfoChangeRequest;
    }

    /**
     * 构造配送状态变更请求参数
     */
    private DeliveryStatusChangeRequest buildDeliveryStatusChangeRequest(ChannelTypeEnum channelTypeEnum,
                                                                         ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        DeliveryStatusChangeRequest deliveryStatusChangeRequest = new DeliveryStatusChangeRequest();
        if ((ChannelTypeEnum.MEITUAN.equals(channelTypeEnum) || ChannelTypeEnum.MT_MEDICINE.equals(channelTypeEnum)) &&
                StringUtils.isNotBlank(request.getChangeTime()) && MccConfigUtil.isUseDeliveryChangeTime()) {
            deliveryStatusChangeRequest.setChangeTime(ConverterUtils.stringToMillis(request.getChangeTime()));
        } else {
            deliveryStatusChangeRequest.setChangeTime(ConverterUtils.stringToMillis(request.getTimestamp()));
        }
        deliveryStatusChangeRequest.setChannelOrderId(request.getOrderId());
        deliveryStatusChangeRequest.setChannelType(channelTypeEnum.getCode());
        deliveryStatusChangeRequest.setRemark(notifyEnum.getDesc());
        deliveryStatusChangeRequest.setRiderName(CommonUtils.decode(request.getDispatcherName()));
        deliveryStatusChangeRequest.setRiderPhone(request.getDispatcherMobile());
        Long tenantId = NumberUtils.LONG_ZERO;
        Long appId = NumberUtils.LONG_ZERO;
        Long storeId = ProjectConstant.UNKNOW_STORE_ID;
        // 饿了么ISV应用
        if (ChannelTypeEnum.ELEM.equals(channelTypeEnum) && MccConfigUtil.checkElmStoreId(request.getTenantAppId()) && MccConfigUtil.checkElmIsvAppKey(request.getTenantAppId())) {
            log.info("饿了么ISV应用:{}查询配送状态变化请求参数",request.getTenantAppId());
            Optional<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTO = poiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO(request.getAppPoiCode(), false);
            tenantId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getTenantId).orElse(NumberUtils.LONG_ZERO);
            appId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getQnhAppId).orElse(NumberUtils.LONG_ZERO);
            storeId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getPoiId).orElse(ProjectConstant.UNKNOW_STORE_ID);
            deliveryStatusChangeRequest.setTenantId(tenantId);
            deliveryStatusChangeRequest.setAppId(appId);
            deliveryStatusChangeRequest.setOrderJsonString(getOrderDetail(tenantId, channelTypeEnum.getCode(), storeId, request.getOrderId(), appId));
        } else {
            CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
            tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
            appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
            deliveryStatusChangeRequest.setTenantId(tenantId);
            deliveryStatusChangeRequest.setAppId(appId);
            deliveryStatusChangeRequest.setOrderJsonString(getOrderDetail(tenantId, channelTypeEnum.getCode(), request.getOrderId(), appId));
        }
        if (ChannelTypeEnum.ELEM == channelTypeEnum) {
            OrderDeliveryInfo orderDeliveryInfo = channelDeliveryService.getOrderDeliveryInfo(tenantId, request.getOrderId(), appId, storeId);
            if (Objects.nonNull(orderDeliveryInfo)) {
                if (StringUtils.isNotBlank(orderDeliveryInfo.getName())) {
                    deliveryStatusChangeRequest.setRiderName(orderDeliveryInfo.getName());
                    deliveryStatusChangeRequest.setRiderPhone(orderDeliveryInfo.getPhone());
                }
                try {
                    deliveryStatusChangeRequest.setExceptionDescription(
                            ChannelStatusConvertUtil.elmDeliveryExceptionDescriptionMapping(orderDeliveryInfo.getSubstatus()));
                }
                catch (Exception e) {
                    log.warn("解析饿了么配送异常失败:{}", orderDeliveryInfo.getSubstatus(), e);
                }
            }
        }
        installDeliveryStatus(channelTypeEnum, deliveryStatusChangeRequest, request.getDeliveryStatus());
        deliveryStatusChangeRequest.setChannelDeliveryCode(request.getDeliveryStatus());
        ExtJsonInfoDTO extJsonInfoDTO=new ExtJsonInfoDTO();
        if(StringUtils.isNotEmpty(request.getFailType())){
            extJsonInfoDTO.setFailType(request.getFailType());
        }
        if(StringUtils.isNotEmpty(request.getDealDeadline())){
            extJsonInfoDTO.setDealDeadline(request.getDealDeadline());
        }
        if(StringUtils.isNotEmpty(request.getMtPkgId())){
            extJsonInfoDTO.setLockOrderPackageId(request.getMtPkgId());
        }
        if(request.getIsFourWheelDelivery()>0){
            extJsonInfoDTO.setIsFourWheelDelivery(request.getIsFourWheelDelivery());
            // 四轮配送查询履约服务费和商家预计收入
            try {
                if (StringUtils.isNotBlank(deliveryStatusChangeRequest.getOrderJsonString())) {
                    ChannelOrderDetailDTO orderDetailDTO = JSON
                            .parseObject(deliveryStatusChangeRequest.getOrderJsonString(), ChannelOrderDetailDTO.class);
                    if (Objects.nonNull(orderDetailDTO)) {
                        extJsonInfoDTO.setBizReceiveAmt(orderDetailDTO.getBizReceiveAmt());
                        extJsonInfoDTO.setPerformanceServiceFee(orderDetailDTO.getPerformanceServiceFee());
                    }
                }
            } catch (Exception e) {
                log.error("四轮配送获取从订单详情中获取履约服务费失败", e);
            }
        }
        if(request.getIsManual()>0){
            extJsonInfoDTO.setIsManual(request.getIsManual());
        }
        deliveryStatusChangeRequest.setExtJson(JSON.toJSONString(extJsonInfoDTO));
        return deliveryStatusChangeRequest;
    }


    /**
     * 构造自配送状态变更请求参数
     */
    private DeliveryStatusChangeRequest buildMTSelfDeliveryStatusChangeRequest(ChannelTypeEnum channelTypeEnum,
                                                                               ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        DeliveryStatusChangeRequest deliveryStatusChangeRequest = new DeliveryStatusChangeRequest();
        deliveryStatusChangeRequest.setChangeTime(ConverterUtils.stringToMillis(request.getTimestamp()));
        deliveryStatusChangeRequest.setChannelOrderId(request.getOrderId());
        deliveryStatusChangeRequest.setChannelType(channelTypeEnum.getCode());
        deliveryStatusChangeRequest.setRemark(notifyEnum.getDesc());
        deliveryStatusChangeRequest.setRiderName(CommonUtils.decode(request.getDispatcherName()));
        deliveryStatusChangeRequest.setRiderPhone(request.getDispatcherMobile());
        CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
        Long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
        Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
        deliveryStatusChangeRequest.setTenantId(tenantId);
        deliveryStatusChangeRequest.setAppId(appId);
        deliveryStatusChangeRequest.setOrderJsonString(getOrderDetail(tenantId, channelTypeEnum.getCode(), request.getOrderId(), appId));
        installSelfDeliveryStatus(channelTypeEnum, deliveryStatusChangeRequest, request.getDeliveryStatus());
        return deliveryStatusChangeRequest;
    }

    /**
     * 请求中台订单服务推送订单状态变更消息
     */
    private ResultStatus doOrderStatusChange(OrderStatusChangeRequest statusChangeRequest, ChannelNotifyEnum notifyEnum) {
        if (MccConfigUtil.orderOcmsSplitSwitch(statusChangeRequest.getTenantId(), statusChangeRequest.getStoreId(), statusChangeRequest.getChannelId())){
            return sendOrderStatusChangeEvent(statusChangeRequest, notifyEnum, statusChangeRequest.getOrderJsonString());
        }
        if (MccConfigUtil.orderOcmsSplitCompare(statusChangeRequest.getTenantId(), statusChangeRequest.getStoreId(), statusChangeRequest.getChannelId())){
            sendOrderStatusChangeEvent(statusChangeRequest, notifyEnum, statusChangeRequest.getOrderJsonString());
        }
        OrderStatusNotifyResponse orderResponse = channelOrderThriftServiceProxy.orderStatusChangeNotify(statusChangeRequest);
        if (Objects.isNull(orderResponse) || Objects.isNull(orderResponse.getOrderResponse())) {
            return ResultGenerator.genResult(ResultCode.FAIL, "推送[" + notifyEnum.getDesc() + "]消息失败").setData(ProjectConstant.NG);
        }
        if (orderResponse.getOrderResponse().getCode() == ResultCodeEnum.SUCCESS.getValue()) {
            if (notifyEnum == ChannelNotifyEnum.ELM_ORDER_CREATE) {
                return ResultGenerator.genSuccessResult().setData(String.valueOf(orderResponse.getOrderId()));
            }
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        return ResultGenerator.genResult(ResultCode.FAIL, StringUtils.join("订单消息处理失败, ", orderResponse.getOrderResponse().getMsg())).setData(ProjectConstant.NG);
    }

    private ResultStatus sendOrderInfoChangeEvent(OrderInfoChangeRequest request){
        ChannelChangeOrderEvent changeOrderEvent = new ChannelChangeOrderEvent();
        changeOrderEvent.setChannelOrderId(request.getChannelOrderId());
        changeOrderEvent.setChannelType(request.getChannelType());
        changeOrderEvent.setOrderJsonString(request.getOrderJsonString());
        changeOrderEvent.setRemark(request.getRemark());
        changeOrderEvent.setTenantId(request.getTenantId());
        changeOrderEvent.setAppId(request.getAppId());
        changeOrderEvent.setShopId(getShopId(request.getOrderJsonString()));
        changeOrderEvent.setEventId(convertOrderInfoChangeType(request.getOrderInfoChangeType()));
        changeOrderEvent.setTimestamp(System.currentTimeMillis());
        Map<String, String> ext = Maps.newHashMap();
        ext.put("invoiceTitle", request.getInvoiceTitle());
        ext.put("invoiceTaxNo", request.getInvoiceTaxNo());
        ext.put("invoiceType", String.valueOf(request.getInvoiceType()));
        changeOrderEvent.setExt(ext);
        if (MccConfigUtil.orderOcmsSplitCompare(changeOrderEvent.getTenantId(), changeOrderEvent.getShopId(), changeOrderEvent.getChannelType())) {
            OcmsSplitTraceUtil.putUniqueId(changeOrderEvent.getTenantId(), changeOrderEvent.getShopId(), changeOrderEvent.getOrderBizType(),
                    changeOrderEvent.getChannelOrderId(), null, changeOrderEvent.getEventType().toString(), String.valueOf(changeOrderEvent.getEventId()), OcmsSplitTraceUtil.DATA_TYPE_OCMS);
        }
        if(newSupplyChannelChangeOrderNotifyMessageProducer.sendMessageSync(changeOrderEvent)) {
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        } else {
            return ResultGenerator.genResult(ResultCode.FAIL, "订单修改消息推送失败").setData(ProjectConstant.NG);
        }
    }


    private int convertOrderInfoChangeType(OrderInfoChangeType orderInfoChangeType) {
        if (orderInfoChangeType == null) {
            return ChannelOrderChangeEventEnum.DELIVERY_CUSTOMER.getEventId();
        }
        switch (orderInfoChangeType){
            case DELIVERY_CUSTOMER:
                return ChannelOrderChangeEventEnum.DELIVERY_CUSTOMER.getEventId();
            case FREIGHT:
                return ChannelOrderChangeEventEnum.FREIGHT.getEventId();
            case INVOICE:
                return ChannelOrderChangeEventEnum.INVOICE.getEventId();
            case TO_SELF_DELIVERY:
                return ChannelOrderChangeEventEnum.TO_SELF_DELIVERY.getEventId();
            default:
                log.error("未知数据变更类别{}", orderInfoChangeType);
                return ChannelOrderChangeEventEnum.DELIVERY_CUSTOMER.getEventId();

        }
    }


    private ResultStatus sendOrderStatusChangeEvent(OrderStatusChangeRequest statusChangeRequest, ChannelNotifyEnum notifyEnum, String orderJsonString){
        ChannelOrderEvent channelOrderEvent = new ChannelOrderEvent();
        channelOrderEvent.setChannelOrderId(statusChangeRequest.getChannelOrderId());
        channelOrderEvent.setChannelType(statusChangeRequest.getChannelId());
        channelOrderEvent.setExt(statusChangeRequest.getExt());
        channelOrderEvent.setOrderJsonString(orderJsonString);
        channelOrderEvent.setRemark(statusChangeRequest.getRemark());
        channelOrderEvent.setTenantId(statusChangeRequest.getTenantId());
        channelOrderEvent.setAppId(statusChangeRequest.getAppId());
        channelOrderEvent.setShopId(statusChangeRequest.getStoreId());
        channelOrderEvent.setTimestamp(System.currentTimeMillis());
        channelOrderEvent.setLockOrderLabel(statusChangeRequest.getLockOrderLabel());
        channelOrderEvent.setLockOrderPackageId(statusChangeRequest.getLockOrderPackageId());
        channelOrderEvent.setAuditType(statusChangeRequest.getAuditType());

        ChannelOrderStatus orderStatus = ChannelOrderStatus.findByValue(statusChangeRequest.getOrderStatus());
        switch (orderStatus){
            case NEW_ORDER:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.NEW_ORDER.getEventId());
                break;
            case BIZ_CONFIRMED:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.BIZ_CONFIRMED.getEventId());
                break;
            case FINISHED:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.FINISHED.getEventId());
                break;
            case CANCELED:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.CANCELED.getEventId());
                break;
            case COMPLETED_PREPARE_MEAL:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.COMPLETED_PREPARE_MEAL.getEventId());
                break;
            case FULFILLMENT:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.FULFILLMENT.getEventId());
                break;
            case CANCEL_APPLIED:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.CANCEL_APPLIED.getEventId());
                break;
            case LOCKED:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.LOCKED.getEventId());
                break;
            case UNLOCKED:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.UNLOCKED.getEventId());
                break;
            case SETTLEMENT_AMOUINT_READY:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.SETTLEMENT_AMOUINT_READY.getEventId());
                break;
            case ORDER_ACTIVITY_DETAIL_READY:
                channelOrderEvent.setEventId(ChannelOrderEventEnum.ORDER_ACTIVITY_DETAIL_READY.getEventId());
                break;
            default:
                log.error("订单状态变更事件未找到对应事件,{}", statusChangeRequest);
                break;
        }

        if (channelOrderEvent.getShopId() <= BigInteger.ZERO.intValue()) {
            log.error("订单 find channel store lt 0, 门店未绑定, tenantId:{}", channelOrderEvent.getTenantId());
            ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            result.setMsg("success");
        }
        if (MccConfigUtil.orderOcmsSplitCompare(channelOrderEvent.getTenantId(), channelOrderEvent.getShopId(), channelOrderEvent.getChannelType())){
            OcmsSplitTraceUtil.putUniqueId(channelOrderEvent.getTenantId(),channelOrderEvent.getShopId(),channelOrderEvent.getOrderBizType(),
                channelOrderEvent.getChannelOrderId(),null,channelOrderEvent.getEventType().toString(), String.valueOf(channelOrderEvent.getEventId()),OcmsSplitTraceUtil.DATA_TYPE_OCMS);
        }
        return orderNotifyToOrderDomain(channelOrderEvent);
    }

    public ResultStatus orderNotifyToOrderDomain(ChannelOrderEvent channelOrderEvent){
        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        result.setMsg("success");
        if (MccConfigUtil.orderNotifyThroughMafka(channelOrderEvent.getTenantId(), channelOrderEvent.getChannelType())){
            ChannelType channelType = ChannelType.findByValue(channelOrderEvent.getChannelType());
            boolean rs;
            switch (channelType){
                case MEITUAN:
                    channelOrderEvent.setEventType(ChannelOrderEventType.CHANNEL_MT_ORDER_EVENT.getEventType());
                    rs = newSupplyChannelOrderMtNotifyMessageProducer.sendMessageSync(channelOrderEvent);
                    break;
                case JD2HOME:
                    channelOrderEvent.setEventType(ChannelOrderEventType.CHANNEL_JDDJ_ORDER_EVENT.getEventType());
                    rs = newSupplyChannelOrderJddjNotifyMessageProducer.sendMessageSync(channelOrderEvent);
                    break;
                case ELEM:
                    channelOrderEvent.setEventType(ChannelOrderEventType.CHANNEL_ELEM_ORDER_EVENT.getEventType());
                    rs = newSupplyChannelOrderElemNotifyMessageProducer.sendMessageSync(channelOrderEvent);
                    break;
                default:
                    rs = newSupplyChannelOrderNotifyMessageProducer.sendMessageSync(channelOrderEvent);
                    break;
            }
            log.error("orderNotifyToOrderDomain 订单消息异步发送失败");
            return rs ? result: ResultGenerator.genFailResult("消息发送失败");
        }else {
            BizOrderCreateResponse bizOrderCreateResponse = null;
            try {
                bizOrderCreateResponse = qnhOrderChannelThriftService.orderStatusNotify(channelOrderEvent.convertToChannelOrderRequest());
            } catch (TException e) {
                return ResultGenerator.genFailResult(e.getMessage());
            }
            if (bizOrderCreateResponse.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue()){
                return result;
            }
            return ResultGenerator.genFailResult(bizOrderCreateResponse.getStatus().getMessage());
        }
    }

    public ResultStatus refundNotifyToOrderDomain(ChannelAfterSaleEvent channelAfterSaleEvent){
        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        result.setMsg("success");
        if (MccConfigUtil.orderNotifyThroughMafka(channelAfterSaleEvent.getTenantId(), channelAfterSaleEvent.getChannelType())){
            ChannelType channelType = ChannelType.findByValue(channelAfterSaleEvent.getChannelType());
            boolean rs;
            switch (channelType){
                case MEITUAN:
                    channelAfterSaleEvent.setEventType(ChannelOrderEventType.CHANNEL_MT_AFTER_SALE_EVENT.getEventType());
                    rs = newSupplyChannelMtOrderRefundMessageProducer.sendMessageSync(channelAfterSaleEvent);
                    break;
                case JD2HOME:
                    channelAfterSaleEvent.setEventType(ChannelOrderEventType.CHANNEL_JDDJ_AFTER_SALE_EVENT.getEventType());
                    rs = newSupplyChannelJddjOrderRefundMessageProducer.sendMessageSync(channelAfterSaleEvent);
                    break;
                case ELEM:
                    channelAfterSaleEvent.setEventType(ChannelOrderEventType.CHANNEL_ELEM_AFTER_SALE_EVENT.getEventType());
                    rs = newSupplyChannelElemOrderRefundMessageProducer.sendMessageSync(channelAfterSaleEvent);
                    break;
                default:
                    rs = newSupplychannelOrderRefundMessageProducer.sendMessageSync(channelAfterSaleEvent);
                    break;
            }
            log.error("refundNotifyToOrderDomain 售后消息异步发送失败");
            return rs ? result: ResultGenerator.genFailResult("消息发送失败");
        }else {
            BizOrderCreateResponse bizOrderCreateResponse = null;
            try {
                bizOrderCreateResponse = qnhOrderChannelThriftService.orderRefundNotify(channelAfterSaleEvent.convertToChannelRefundRequest());
            } catch (TException e) {
                return ResultGenerator.genFailResult(e.getMessage());
            }
            if (bizOrderCreateResponse.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue()){
                return result;
            }
            return ResultGenerator.genFailResult(bizOrderCreateResponse.getStatus().getMessage());
        }
    }
    
    



    /**
     * 请求中台订单服务推送订单信息变更消息
     */
    private ResultStatus doOrderInfoChange(OrderInfoChangeRequest orderInfoChangeRequest) {
        Long shopId = getShopId(orderInfoChangeRequest.getOrderJsonString());
        if (MccConfigUtil.orderOcmsSplitSwitch(orderInfoChangeRequest.getTenantId(), shopId, orderInfoChangeRequest.getChannelType())){
            return sendOrderInfoChangeEvent(orderInfoChangeRequest);
        }
        if (MccConfigUtil.orderOcmsSplitCompare(orderInfoChangeRequest.getTenantId(), shopId, orderInfoChangeRequest.getChannelType())){
            sendOrderInfoChangeEvent(orderInfoChangeRequest);
        }
        OrderResponse orderResponse = channelOrderThriftServiceProxy.orderInfoChangeNotify(orderInfoChangeRequest);
        if (Objects.isNull(orderResponse)) {
            return ResultGenerator.genResult(ResultCode.FAIL, "推送订单信息修改消息失败").setData(ProjectConstant.NG);
        }
        if (orderResponse.getCode() == ResultCodeEnum.SUCCESS.getValue()) {
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        return ResultGenerator.genResult(ResultCode.FAIL, StringUtils.join("订单修改消息推送失败, ", orderResponse.getMsg())).setData(ProjectConstant.NG);
    }

    /**
     * 请求orderbiz推送订单信息变更消息
     */
    private ResultStatus sendOrderInfoChangeMsg(OrderStatusChangeRequest orderStatusChangeRequest, ChannelNotifyEnum notifyEnum) {
        String key = orderStatusChangeRequest.getChannelOrderId() + "_" + orderStatusChangeRequest.getChannelId();
        if(!newSupplyOrderCacheService.setChangeOrderNxValue(key, key, getChangeOrderRedisLockSeconds())){
            log.info("已有修改信息放入队列中，忽略此次修改推送，key:{}", key);
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        // // 给orderbiz发送订单修改消息
        ChannelChangeOrderEvent orderChangeEvent = new ChannelChangeOrderEvent(orderStatusChangeRequest.getChannelOrderId(), orderStatusChangeRequest.getTenantId(),
                orderStatusChangeRequest.getStoreId(), orderStatusChangeRequest.getAppId(),  ChannelOrderEventEnum.CHANGE_ORDER_INFO.getEventId(), orderStatusChangeRequest.getChannelId(),
                orderStatusChangeRequest.getChangeTime(),
                notifyEnum.getAbbrev(), Collections.emptyMap(), "");


        if(newSupplyChannelChangeOrderNotifyMessageProducer.sendMessageSync(orderChangeEvent)) {
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        } else {
            return ResultGenerator.genResult(ResultCode.FAIL, "订单修改消息推送失败").setData(ProjectConstant.NG);
        }
    }

    /**
     * 请求中台订单服务推送配送状态变更消息
     */
    private ResultStatus doDeliveryStatusChange(DeliveryStatusChangeRequest statusChangeRequest, String rawDeliveryStatus,
                                                boolean riderTransfer,String mtPkgId, String platformCode) {
        try {

            if(isSelfDeliveryStatus(statusChangeRequest)){
                if (MccConfigUtil.getElmSelfDeliveryFilterStatus().contains(Integer.parseInt(rawDeliveryStatus))) {
                    return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                }
                if (!MccConfigUtil.getSelfDeliveryOrderConsumeSwitch(statusChangeRequest.getTenantId())) {
                    return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                }
                Optional<com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum> deliveryStatusOptional =
                        ElmSelfDeliveryStatusEnum.mapToDeliveryStatus(Integer.parseInt(rawDeliveryStatus));
                if (!deliveryStatusOptional.isPresent()) {
                    return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                }
                SelfDeliveryOrderContent content = new SelfDeliveryOrderContent();
                content.setOrderId(statusChangeRequest.getChannelOrderId());
                content.setStatus(deliveryStatusOptional.get().getCode());
                content.setRiderName(statusChangeRequest.getRiderName());
                content.setRiderPhone(statusChangeRequest.getRiderPhone());
                content.setChannelType(statusChangeRequest.getChannelType());
                content.setTenantId(statusChangeRequest.getTenantId());
                content.setOperateTime(statusChangeRequest.getChangeTime());
                selfDeliveryOrderProducer.sendMessage(content, content.getOrderId());
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }

            Integer selfStatus = 18;
            if (isPaoTuiDeliveryStatusChange(statusChangeRequest, Integer.parseInt(rawDeliveryStatus),mtPkgId,platformCode)) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            // 骑手转派百川侧不做处理
            if(riderTransfer){
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }

            //转自送成功通知TMS
            if (Objects.nonNull(rawDeliveryStatus) && Objects.equals(Integer.parseInt(rawDeliveryStatus),selfStatus)) {

                SelfDeliveryCallbackReq selfDeliveryCallbackReq = new SelfDeliveryCallbackReq();
                selfDeliveryCallbackReq.setChannelOrderId(statusChangeRequest.getChannelOrderId());
                selfDeliveryCallbackReq.setOrderBizType(OrderBizTypeEnum.ELE_ME.getValue());
                deliveryCallbackThriftService.selfDeliveryCallback(selfDeliveryCallbackReq);
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
        }
        catch (BizException e) {
            //业务相关异常，主动重试。包括调用订单/tms失败等异常
            log.error("isFarmPaoTuiDeliveryStatusChange throw BizException", e);
            return ResultGenerator.genResult(ResultCode.FAIL, "配送状态变更消息推送失败").setData(ProjectConstant.NG);
        }
        catch (Exception e) {
            //其他异常忽略，走老逻辑
            log.error("isFarmPaoTuiDeliveryStatusChange throw Exception", e);
        }

        //尝试获取配送异常
        statusChangeRequest.setExceptionDescription(queryExceptionDescription(statusChangeRequest));
        if (MccConfigUtil.orderNotifyThroughMafka(statusChangeRequest.getTenantId(), statusChangeRequest.getChannelType())){
            return sendOrderDeliveryStatusChangeEvent(statusChangeRequest);
        }
        if(MccConfigUtil.orderDeliveryOcmsSplitSwitch(statusChangeRequest.getTenantId())) {
            return orderDeliveryNotify(statusChangeRequest);
        }
        OrderResponse orderResponse = channelOrderThriftServiceProxy.deliveryStatusChangeNotify(statusChangeRequest);
        if (Objects.isNull(orderResponse)) {
            return ResultGenerator.genResult(ResultCode.FAIL, "推送配送状态变更消息失败").setData(ProjectConstant.NG);
        }
        if (orderResponse.getCode() == ResultCodeEnum.SUCCESS.getValue()) {
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        return ResultGenerator.genResult(ResultCode.FAIL, StringUtils.join("配送状态变更消息推送失败, ", orderResponse.getMsg())).setData(ProjectConstant.NG);
    }

    private ChannelDeliveryEvent buildChannelDeliveryEventFromRequest(DeliveryStatusChangeRequest statusChangeRequest) {
        ChannelDeliveryEvent channelDeliveryEvent = new ChannelDeliveryEvent();
        channelDeliveryEvent.setChannelOrderId(statusChangeRequest.getChannelOrderId());
        channelDeliveryEvent.setChannelType(statusChangeRequest.getChannelType());
        channelDeliveryEvent.setTimestamp(statusChangeRequest.getChangeTime() == 0L ? System.currentTimeMillis() :statusChangeRequest.getChangeTime());
        channelDeliveryEvent.setRiderName(statusChangeRequest.getRiderName());
        channelDeliveryEvent.setRiderPhone(statusChangeRequest.getRiderPhone());
        channelDeliveryEvent.setRemark(statusChangeRequest.getRemark());
        channelDeliveryEvent.setTenantId(statusChangeRequest.getTenantId());
        channelDeliveryEvent.setEventId(ChannelDeliveryEventEnum.FULFILLMENT_DELIVERY_UPDATE.getEventId());
        channelDeliveryEvent.setAppId(statusChangeRequest.getAppId());
        channelDeliveryEvent.setExceptionDescription(statusChangeRequest.getExceptionDescription());
        channelDeliveryEvent.setStatus(convert2DeliveryStatus(statusChangeRequest));
        channelDeliveryEvent.setChannelDeliveryCode(statusChangeRequest.getChannelDeliveryCode());
        channelDeliveryEvent.setExtJson(statusChangeRequest.getExtJson());
        channelDeliveryEvent.setElmCheckDistributeStatus(true);
        return channelDeliveryEvent;
    }

    private ResultStatus sendOrderDeliveryStatusChangeEvent(DeliveryStatusChangeRequest statusChangeRequest) {
        ChannelDeliveryEvent channelDeliveryEvent = buildChannelDeliveryEventFromRequest(statusChangeRequest);
        ChannelType channelType = ChannelType.findByValue(statusChangeRequest.getChannelType());
        boolean rs;
        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        result.setMsg("success");
        switch (channelType){
            case MEITUAN:
                channelDeliveryEvent.setEventType(ChannelOrderEventType.CHANNEL_MT_DELIVERY_EVENT.getEventType());
                rs = newSupplyChannelMtOrderDeliveryMessageProducer.sendMessageSync(channelDeliveryEvent, channelDeliveryEvent.getChannelOrderId());
                break;
            case ELEM:
                channelDeliveryEvent.setEventType(ChannelOrderEventType.CHANNEL_ELEM_DELIVERY_EVENT.getEventType());
                rs = newSupplyChannelElemOrderDeliveryMessageProducer.sendMessageSync(channelDeliveryEvent, channelDeliveryEvent.getChannelOrderId());
                break;
            case JD2HOME:
                channelDeliveryEvent.setEventType(ChannelOrderEventType.CHANNEL_JDDJ_DELIVERY_EVENT.getEventType());
                rs = newSupplyChannelJddjOrderDeliveryMessageProducer.sendMessageSync(channelDeliveryEvent, channelDeliveryEvent.getChannelOrderId());
                break;
            default:
                rs = newSupplyChannelOrderDeliveryMessageProducer.sendMessageSync(channelDeliveryEvent, channelDeliveryEvent.getChannelOrderId());
                break;
        }
        return rs ? result: ResultGenerator.genFailResult("消息发送失败");
    }

    private ResultStatus orderDeliveryNotify(DeliveryStatusChangeRequest statusChangeRequest) {
        ChannelDeliveryEvent channelDeliveryEvent = buildChannelDeliveryEventFromRequest(statusChangeRequest);
        boolean rs = newSupplyChannelOrderDeliveryMessageProducer.sendMessageSync(channelDeliveryEvent, channelDeliveryEvent.getChannelOrderId());
        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        result.setMsg("success");
        return rs ? result: ResultGenerator.genFailResult("消息发送失败");
    }

    private Integer convert2DeliveryStatus(DeliveryStatusChangeRequest statusChangeRequest) {
        DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(statusChangeRequest.getDeliveryStatus());
        DistributeStatusEnum distributeStatusEnum = convert2DeliveryStatusEnum(deliveryStatus);
        if (ChannelType.ELEM.getValue() == statusChangeRequest.getChannelType() &&
                statusChangeRequest.getDeliveryStatus() == DeliveryStatus.DELIVERY_FAILED.getValue()) {
            distributeStatusEnum = DistributeStatusEnum.DISTRIBUTE_REJECTED;
        }
        return distributeStatusEnum.getValue();
    }

    private DistributeStatusEnum convert2DeliveryStatusEnum(DeliveryStatus deliveryStatus){
        if(Objects.isNull(deliveryStatus)){
            return DistributeStatusEnum.DISTRIBUTE_UNKNOWN;
        }
        switch (deliveryStatus){
            case WAIT_DELIVERY:
                return DistributeStatusEnum.DISTRIBUTE_UNKNOWN;
            case APPLY_DELIVERY:
                return DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER;
            case TAKE_MEAL_FAIL:
                return DistributeStatusEnum.DISTRIBUTE_FAILED;
            case RIDER_TAKEN_MEAL:
                return DistributeStatusEnum.RIDER_TAKE_GOODS;
            case DELIVERY_FAILED:
                return DistributeStatusEnum.DISTRIBUTE_FAILED;
            case DELIVERY_COMPLETED:
                return DistributeStatusEnum.RIDER_DELIVERED;
            case NEW_DELIVERY_ORDER:
                return DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER;
            case WAIT_DISPATCH_RIDER:
                return DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER;
            case RIDER_ARRIVE_SHOP:
                return DistributeStatusEnum.RIDER_REACH_SHOP;
            case RIDER_ACCEPTED_ORDER:
                return DistributeStatusEnum.RIDER_ASSIGNED;
            case DELIVERY_EXCEPTION:
                return DistributeStatusEnum.DISTRIBUTE_EXCEPTION;
            case DELIVERY_CANCEL:
                return DistributeStatusEnum.DISTRIBUTE_CANCELED;
            case DELIVERY_NORMAL:
                return DistributeStatusEnum.DISTRIBUTE_NORMAL;
            default: {
                log.error("未知配送状态:{}", deliveryStatus);
                return DistributeStatusEnum.DISTRIBUTE_UNKNOWN;
            }
        }

    }


    private boolean isSelfDeliveryStatus(DeliveryStatusChangeRequest statusChangeRequest){

        try {

            if(MccConfigUtil.elemSelfDeliveryStatusSwitch()){
                return false;
            }

            if(!ChannelTypeEnum.isElem(statusChangeRequest.getChannelType())){
                return false;
            }

            BizOrderQueryRequest request = new BizOrderQueryRequest();
            request.setTenantId(statusChangeRequest.getTenantId());
            request.setShopId(0L);
            request.setViewOrderId(statusChangeRequest.getChannelOrderId());
            request.setOrderBizType(OrderBizTypeEnum.ELE_ME.getValue());
            request.setFromMaster(true);
            Optional<BizOrderModel> bizOrderModelOpt = channelOrderThriftServiceProxy.queryOrderInfo(request);
            if (!bizOrderModelOpt.isPresent()) {
                log.info("bizOrderModelOpt is not present");
                return false;
            }
            if(bizOrderModelOpt.get().getDeliveryModel()==null){
                log.info("delivery model is null");
                return false;
            }

            if(!Objects.equals(bizOrderModelOpt.get().getDeliveryModel().getIsSelfDelivery(),1)){
                Optional<List<TDeliveryOrder>> tDeliveryOrders = tmsThriftServiceProxy.queryDeliveryOrderByOrderId(bizOrderModelOpt.get().getOrderId());
                if (!tDeliveryOrders.isPresent()) {
                    log.info("tDeliveryOrders is not present");
                    return false;
                }
                List<TDeliveryOrder> deliveryOrderList = tDeliveryOrders.get();
                if(CollectionUtils.isEmpty(deliveryOrderList)){
                    log.info("deliveryOrderList is not empty");
                    return false;
                }
                List<Integer> deliveryChannelList = deliveryOrderList.stream().map(TDeliveryOrder::getDeliveryChannel).distinct().collect(Collectors.toList());
                if(CollectionUtils.isEmpty(deliveryOrderList)){
                    log.info("deliveryOrderList is not empty");
                    return false;
                }
                if(deliveryChannelList.size() == 1 && deliveryChannelList.contains(DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode())){
                    log.info("delivery is platform");
                    return false;
                }
            }
            return true;
        }catch (Exception e){
            log.error("isSelfDeliveryStatus error",e);
            return false;
        }
    }

    private boolean isPaoTuiDeliveryStatusChange(DeliveryStatusChangeRequest statusChangeRequest, int rawDeliveryStatus,String mtPkgId, String platformCode) {
        if (!ChannelTypeEnum.isMeiTuan(statusChangeRequest.getChannelType()) || Lion.getConfigRepository().getBooleanValue("delivery.paotui.status.callback.fallback", false)) {
            log.info("isPaoTuiDeliveryStatusChange is not meituan");
            return false;
        }

        BizOrderQueryRequest request = new BizOrderQueryRequest();
        request.setTenantId(statusChangeRequest.getTenantId());
        request.setShopId(0L);
        request.setViewOrderId(statusChangeRequest.getChannelOrderId());
        request.setOrderBizType(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue());
        request.setFromMaster(true);
        Optional<BizOrderModel> bizOrderModelOpt = channelOrderThriftServiceProxy.queryOrderInfo(request);
        if (!bizOrderModelOpt.isPresent()) {
            log.info("bizOrderModelOpt is not present");
            return false;
        }
        Optional<List<TDeliveryOrder>> tDeliveryOrders = tmsThriftServiceProxy.queryDeliveryOrderByOrderId(bizOrderModelOpt.get().getOrderId());
        if (!tDeliveryOrders.isPresent()) {
            log.info("tDeliveryOrders is not present");
            return false;
        }
        boolean isValid = deliveryOrderCheckWrapper.checkValidPaoTuiOrder(tDeliveryOrders.get());
        if (!isValid) {
            log.info("tDeliveryOrders is not valid");
            return false;
        }
        if (! MccConfigUtil.getPaoTuiPlatformCodeList().contains(platformCode)) {
            log.info("mcc code is not contain ");
            return false;
        }
        boolean isValidCallback = true;
        List<DeliveryPlatformEnum> platformEnumList = deliveryOrderCheckWrapper.getDeliveryPlatform(tDeliveryOrders.get());

        TDeliveryOrder deliveryOrder = tDeliveryOrders.get().stream().filter(TDeliveryOrder::getActiveStatus).findFirst().orElse(tDeliveryOrders.get().stream().max(Comparator.comparing(TDeliveryOrder::getId)).get());

        if(platformEnumList.contains(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM)){
            isValidCallback = postToMaltFarm(statusChangeRequest, rawDeliveryStatus, bizOrderModelOpt,deliveryOrder);
        }else if(platformEnumList.contains(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM)){
            isValidCallback = postToDap(statusChangeRequest, rawDeliveryStatus, bizOrderModelOpt,mtPkgId,deliveryOrder);
        }
        log.info("is not use malt dap platformEnumList:{}",platformEnumList);

        return isValidCallback;
    }
    private boolean postToMaltFarm(DeliveryStatusChangeRequest statusChangeRequest, int rawDeliveryStatus,
                                   Optional<BizOrderModel> bizOrderModelOpt,TDeliveryOrder deliveryOrder) {
        try {
            Cat.logEvent(ProjectConstant.MALT_FARM_PAOTUI_STATUS_CAT_STR, rawDeliveryStatus+"");
            if(MccConfigUtil.isMaltFallbackSwitch()){
                log.error("malt paotui status fallback, orderId:{}", bizOrderModelOpt.get().getOrderId());
                Cat.logEvent(ProjectConstant.MALT_FARM_PAOTUI_STATUS_FALLBACK_CAT_STR, rawDeliveryStatus+"");
                // 麦芽田降级、不进行重试、直接更新订单上配送状态
                return false;
            }
            String orderId = String.valueOf(deliveryOrder.getOrderId());
            if(StringUtils.isNotEmpty(deliveryOrder.getDeliveryOrderId())){
                orderId = deliveryOrder.getDeliveryOrderId();
            }
            String postJson =
                    JSON.toJSONString(FarmPaoTuiDeliveryCallbackRequest.convertFromDeliveryStatusChange(orderId
                            , statusChangeRequest, rawDeliveryStatus));
            log.info("FarmPaoTuiDeliveryStatusChange.postJson = {}", postJson);
            String responseStr = HttpClientUtil.postJson(callbackUrl,
                    MccConfigUtil.getMaltHttpConnectionTimeOut(),
                    MccConfigUtil.getMaltHttpSocketTimeOut(),
                    postJson);
            if (StringUtils.isEmpty(responseStr)) {
                log.info("FarmPaoTuiDeliveryStatusChange.postJson error,response = {}", responseStr);
                Cat.logEvent(ProjectConstant.MALT_FARM_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_返回为空");
                throw new BizException("调用麦芽田状态回调失败");
            }
            log.info("FarmPaoTuiDeliveryStatusChange.postJson response = {}", responseStr);
            String data = JSON.parseObject(responseStr).getString("data");
            //特殊返回：当麦芽田无对应有效运单时返回
            if (Objects.equals(ProjectConstant.FARM_PAO_TUI_INVALID_CALLBACK, data)) {
                Cat.logEvent(ProjectConstant.MALT_FARM_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_无有效运单");
                return false;
            }
            if (!Objects.equals(ProjectConstant.OK, data)) {
                Cat.logEvent(ProjectConstant.MALT_FARM_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_返回结果不成功");
                log.info("FarmPaoTuiDeliveryStatusChange.postJson error,response = {}", responseStr);
                throw new BizException("调用麦芽田状态回调失败");
            }
            Cat.logEvent(ProjectConstant.MALT_FARM_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_suc");
            return true;
        } catch (BizException e) {
            Cat.logEvent(ProjectConstant.MALT_FARM_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_Exception");
            //如果post失败，也应该重试
            log.info("postToMaltFarm error", e);
            throw new BizException("调用麦芽田状态回调失败");
        } catch (Exception e) {
            Cat.logEvent(ProjectConstant.MALT_FARM_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_Exception");
            //如果post失败，也应该重试
            log.error("postToMaltFarm error", e);
            throw new BizException("调用麦芽田状态回调失败");
        }
    }

    List<Integer> queryLocationData=Arrays.asList(DeliveryStatus.RIDER_ACCEPTED_ORDER.getValue(),DeliveryStatus.RIDER_ARRIVE_SHOP.getValue(),DeliveryStatus.RIDER_TAKEN_MEAL.getValue(),DeliveryStatus.DELIVERY_COMPLETED.getValue());
    private boolean postToDap(DeliveryStatusChangeRequest statusChangeRequest,int rawDeliveryStatus, Optional<BizOrderModel> bizOrderModelOpt,String mtPkgId,TDeliveryOrder deliveryOrder) {
        try {
            // 青云智送配送压单和发配送处理逻辑一致、两个状态都通知会导致青云锁竞争失败、需要过滤配送压单状态
            if(Objects.equals(statusChangeRequest.getDeliveryStatus(), DeliveryStatus.APPLY_DELIVERY.getValue())){
                return true;
            }
            if (bizOrderModelOpt.get().getDeliveryModel().getLockOrderLabel() && Objects.equals(statusChangeRequest.getDeliveryStatus(), DeliveryStatus.DELIVERY_CANCEL.getValue())) {
                Boolean existCancelDelivery = newSupplyDeliveryCacheService.isExistCancelDelivery(String.valueOf(deliveryOrder.getOrderId()));
                if(!existCancelDelivery) {
                    newSupplyDeliveryCacheService.set(String.valueOf(deliveryOrder.getOrderId()),1);
                    return true;
                }
            }
            if(MccConfigUtil.isDapFallbackSwitch()){
                log.error("dap paotui status fallback, orderId:{}", bizOrderModelOpt.get().getOrderId());
                Cat.logEvent(ProjectConstant.DAP_FARM_PAOTUI_STATUS_FALLBACK_CAT_STR, rawDeliveryStatus+"");
                // 青云降级、不进行重试、直接更新订单上配送状态
                return false;
            }
            CoordinateTransformUtil.CoordinatePoint pointDTO = null;
            if(queryLocationData.contains(statusChangeRequest.getDeliveryStatus())){
                pointDTO = channelPaoTuiService.getRiderPosition(bizOrderModelOpt.get().getOrderId());
            }
            FarmPaoTuiPostStatusRequest request=new FarmPaoTuiPostStatusRequest();
            request.setChangeTime(ConverterUtils.millisToSeconds(statusChangeRequest.getChangeTime()));
            request.setDispatcherName(statusChangeRequest.getRiderName());
            request.setDispatcherMobile(statusChangeRequest.getRiderPhone());
            request.setStatus(ChannelStatusConvertUtil.deliveryStatusToDap(statusChangeRequest.getDeliveryStatus()));
            String orderId = String.valueOf(deliveryOrder.getOrderId());
            if(StringUtils.isNotEmpty(deliveryOrder.getDeliveryOrderId())){
                orderId = deliveryOrder.getDeliveryOrderId();
            }
            Map<String,Object> paramMap= new HashMap<>(ConverterUtils.dapPaoTuiConvert(orderId, request,pointDTO,mtPkgId));
            log.info("DapPaoTuiDeliveryStatusChange.paramMap = {}", paramMap);
            String responseStr = HttpClientUtil.post(dapCallbackUrl,
                    MccConfigUtil.getDapHttpConnectionTimeOut(),
                    MccConfigUtil.getDapHttpSocketTimeOut(),
                    paramMap);
            if (StringUtils.isEmpty(responseStr)) {
                Cat.logEvent(ProjectConstant.DAP_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_无有效运单");
                log.info("DapPaoTuiDeliveryStatusChange.postJson error,response = {}", responseStr);
                throw new BizException("调用青云智送状态回调失败");
            }
            log.info("DapPaoTuiDeliveryStatusChange.postJson response = {}", responseStr);
            Map<String,Object> data = JSON.parseObject(responseStr);
            //特殊返回：当麦芽田无对应有效运单时返回
            if (MapUtils.isEmpty(data)) {
                Cat.logEvent(ProjectConstant.DAP_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_无有效运单");
                log.info("DapPaoTuiDeliveryStatusChange.postJson error,response = {}", responseStr);
                throw new BizException("调用青云智送状态回调失败");
            }
            if (!Objects.equals(0, data.get("code"))) {
                Cat.logEvent(ProjectConstant.DAP_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_返回结果不成功");
                log.info("DapPaoTuiDeliveryStatusChange.postJson error,response = {}", responseStr);
                throw new BizException("调用青云智送状态回调失败");
            }
            Cat.logEvent(ProjectConstant.DAP_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_suc");
            return true;
        } catch (BizException e) {
            Cat.logEvent(ProjectConstant.DAP_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_Exception");
            //如果post失败，也应该重试
            log.info("postToDap error", e);
            throw new BizException("调用青云智送状态回调失败");
        } catch (Exception e) {
            Cat.logEvent(ProjectConstant.DAP_PAOTUI_POST_CAT_STR, rawDeliveryStatus+"_Exception");
            //如果post失败，也应该重试
            log.error("postToDap error", e);
            throw new BizException("调用青云智送状态回调失败");
        }
    }

    /**
     * 获取渠道配送异常异常描述
     */
    private String queryExceptionDescription(DeliveryStatusChangeRequest statusChangeRequest) {
        try {
            //非饿了么渠道不提供配送异常描述
            if (ChannelTypeEnum.ELEM.getCode() != statusChangeRequest.getChannelType()) {
                return StringUtils.EMPTY;
            }

            //订单配送状态不是异常或取消状态，无需获取配送异常描述
            if (DeliveryStatus.DELIVERY_EXCEPTION.getValue() != statusChangeRequest.getDeliveryStatus()) {
                return StringUtils.EMPTY;
            }

            ChannelOrderDetailDTO orderDetail = JSON.parseObject(statusChangeRequest.getOrderJsonString(), ChannelOrderDetailDTO.class);
            long storeId = Optional.ofNullable(orderDetail).map(ChannelOrderDetailDTO::getStoreId).orElse(NumberUtils.LONG_ZERO);
            OrderDeliveryInfo orderDeliveryInfo = channelDeliveryService.getOrderDeliveryInfo(statusChangeRequest.getTenantId(), statusChangeRequest.getChannelOrderId(), statusChangeRequest.getAppId(), storeId);
            if (Objects.nonNull(orderDeliveryInfo)) {
                return ChannelStatusConvertUtil.elmDeliveryExceptionDescriptionMapping(orderDeliveryInfo.getSubstatus());
            }
        }
        catch (Exception e) {
            log.warn("解析饿了么配送异常失败", e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 请求中台整单退款消息
     */
    private ResultStatus doRefund(ChannelTypeEnum channelTypeEnum, int refundType, int sponsor, OrderNotifyRequest request) {
        return doRefund(channelTypeEnum, refundType, sponsor, 0, request);
    }

    private ResultStatus doRefund(RefundParamDto refundParamDto){
        OrderAllRefundRequest orderAllRefundRequest = channelOrderConverterService.orderAllRefundNotify(refundParamDto.getRequest());
        CopAccessConfigDO accessConfig = getAccessConfig(refundParamDto.getChannelTypeEnum().getCode(), refundParamDto.getRequest().getTenantAppId());
        Long tenantId = getTenantIdParam(accessConfig, refundParamDto.getRequest().getTenantAppId());
        Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
        String orderDetail = getOrderDetail(tenantId, refundParamDto.getChannelTypeEnum().getCode(), refundParamDto.getRequest().getOrderId(), appId);
        Long storeId = getShopId(orderDetail);
        refundParamDto.setAppId(appId);
        refundParamDto.setRefundAll(true);
        refundParamDto.setTenantId(tenantId);
        refundParamDto.setShopId(storeId);
        orderAllRefundRequest.setChannelType(refundParamDto.getChannelTypeEnum().getCode());

        if (StringUtils.isBlank(orderAllRefundRequest.getReason())) {
            orderAllRefundRequest.setReason("取消申请");
        }
        // mock 代码
        /*if(skipMtAllRefund(refundParamDto.getChannelTypeEnum().getCode(), tenantId)){
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }*/
       if (MccConfigUtil.orderOcmsSplitSwitch(tenantId, storeId, orderAllRefundRequest.getChannelType())){
            return doRefundByEvent(refundParamDto);
        }
        if (MccConfigUtil.orderOcmsSplitCompare(tenantId, storeId, orderAllRefundRequest.getChannelType())){
            doRefundByEvent(refundParamDto);
        }
        //获取渠道订单详情，根据其中门店信息，校验门店是否接中台

        orderAllRefundRequest.setRefundAmount(ConverterUtils.stringToInt(refundParamDto.getRequest().getRefundPrice()));
        orderAllRefundRequest.setTenantId(tenantId);
        orderAllRefundRequest.setAfsApplySourceType(refundParamDto.getAfsApplySourceType());
        orderAllRefundRequest.setChannelType(refundParamDto.getChannelTypeEnum().getCode());
        orderAllRefundRequest.setRefundType(refundParamDto.getRefundType());
        orderAllRefundRequest.setSponsor(refundParamDto.getSponsor());
        orderAllRefundRequest.setRefundId(refundParamDto.getRequest().getRefundId());
        orderAllRefundRequest.setAfsApplyType(refundParamDto.getAfsApplyType());// 售中售后申请
        orderAllRefundRequest.setRefundApplyTime(ConverterUtils.stringToMillis(refundParamDto.getRequest().getRefundApplyTime()));
        orderAllRefundRequest.setIsAppeal(refundParamDto.getRequest().getIsAppeal() == IsAppealEnum.IS_APPEAL.getCode() ? true : false);
        orderAllRefundRequest.setRefundPicList(ListUtils.listTruncated4TotalLength(BeanUtil.fromJson(refundParamDto.getRequest().getPictureJson(),
                new org.codehaus.jackson.type.TypeReference<List<String>>() {}, Collections.emptyList()), MAX_REFUND_PIC_LENGTH));

        orderAllRefundRequest.setAuditType(MtAuditTypeServiceUtils.getAuditTypeFromRefund(refundParamDto.getRequest()));

        QueryDeadlineTimeDTO queryDeadlineTimeDTO = QueryDeadlineTimeDTO.builder()
                .refundId(orderAllRefundRequest.getRefundId())
                .refundApplyTime(orderAllRefundRequest.getRefundApplyTime())
                .channelOrderId(orderAllRefundRequest.getChannelOrderId())
                .channelType(orderAllRefundRequest.getChannelType())
                .tenantId(orderAllRefundRequest.getTenantId())
                .appPoiCode(refundParamDto.getRequest().getAppPoiCode())
                .build();
        orderAllRefundRequest.setProcessDeadline(queryProcessDeadline(queryDeadlineTimeDTO, appId, orderDetail));
        OrderResponse orderResponse = channelOrderThriftServiceProxy.orderAllRefundNotify(orderAllRefundRequest);
        if (Objects.isNull(orderResponse)) {
            return ResultGenerator.genResult(ResultCode.FAIL, "推送整单退款消息失败").setData(ProjectConstant.NG);
        }
        if (orderResponse.getCode() == ResultCodeEnum.SUCCESS.getValue()) {
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        else if (isJddjMockOrder(tenantId, orderAllRefundRequest.getChannelOrderId(), refundParamDto.getChannelTypeEnum())) {
            log.info("京东测试订单那，忽略该请求:{}", refundParamDto.getRequest());
            throw new MockTestException();
        }
        return ResultGenerator.genResult(ResultCode.FAIL, "整单退款消息推送失败").setData(ProjectConstant.NG);
    }

    private Long getOrderDetailStoreId(QueryDeadlineTimeDTO queryDeadlineTimeDTO, String orderDetail){
        try {
            if(StringUtils.isNotBlank(orderDetail)) {
                ChannelOrderDetailDTO channelOrderDetailDTO = JSON.parseObject(orderDetail, ChannelOrderDetailDTO.class);
                if (Objects.nonNull(channelOrderDetailDTO)) {
                    return channelOrderDetailDTO.getStoreId();
                }
            } else {
                if(StringUtils.isNotBlank(queryDeadlineTimeDTO.getAppPoiCode())) {
                    Long storeId = copChannelStoreService.selectChannelStoreId(queryDeadlineTimeDTO.getTenantId(), queryDeadlineTimeDTO.getChannelType(), queryDeadlineTimeDTO.getAppPoiCode());
                    if (storeId != null && storeId > 0) {
                        return storeId;
                    }
                }
            }
        }catch (Exception e){
            log.error("从订单信息中获取门店失败", e);
        }
        return NumberUtils.LONG_ZERO;
    }

    private ResultStatus doRefund(ChannelTypeEnum channelTypeEnum, int refundType, int sponsor, int afsApplyType, OrderNotifyRequest request) {
        return doRefund(RefundParamDto.builder()
                .channelTypeEnum(channelTypeEnum)
                .refundType(refundType)
                .afsApplyType(afsApplyType)
                .afsApplySourceType(AfterSaleSourceTypeEnum.CHANNEL.getValue())
                .request(request)
                .sponsor(sponsor)
                .refundAll(true)
                .build());
    }



    private ResultStatus doRefundGoods(ChannelTypeEnum channelTypeEnum, int refundType, int sponsor, OrderNotifyRequest request) {
        return doRefundGoods(channelTypeEnum, refundType, sponsor, 1, request);
    }

    /**
     * 退款消息
     * 1、京东拒绝单不存在售后
     * 2、美团全单退不存在明细
     * 3、金额退克重退不走该链路
     * @param refundParamDto
     * @return
     */
    private ResultStatus doRefundByEvent(RefundParamDto refundParamDto){
        ChannelAfterSaleEvent afterSaleEvent = new ChannelAfterSaleEvent();
        int channelType = refundParamDto.getChannelTypeEnum().getCode();
        int refundType = refundParamDto.getRefundType();
        OrderNotifyRequest request = refundParamDto.getRequest();
        afterSaleEvent.setEventId(convertRefundEventIdByRefundType(refundType));
        afterSaleEvent.setAppId(refundParamDto.getAppId());
        afterSaleEvent.setTimestamp(System.currentTimeMillis());
        afterSaleEvent.setShopId(refundParamDto.getShopId());
        afterSaleEvent.setTenantId(refundParamDto.getTenantId());
        afterSaleEvent.setSponsor(refundParamDto.getSponsor());
        afterSaleEvent.setChannelType(channelType);
        afterSaleEvent.setAfsApplyType(refundParamDto.getAfsApplyType());
        afterSaleEvent.setAppeal(request.getIsAppeal() == IsAppealEnum.IS_APPEAL.getCode());
        afterSaleEvent.setReason(refundParamDto.getRequest().getRefundReason());
        afterSaleEvent.setAfsApplySourceType(refundParamDto.getAfsApplySourceType());
        afterSaleEvent.setAuditType(MtAuditTypeServiceUtils.getAuditTypeFromRefund(refundParamDto.getRequest()));


        if (channelNotExistAfterSaleApply(refundParamDto)){
            return dealWithChannelNotExistAfterSaleApply(refundParamDto, afterSaleEvent);
        }
        OrderAfsApplyDTO orderAfsApplyDTO = getAfsDetail(refundParamDto.getTenantId(), refundParamDto.getShopId(), channelType,  request.getOrderId(), refundParamDto.getAppId(), request.getRefundId());
        //渠道无退单的需要单独处理
        if (Objects.isNull(orderAfsApplyDTO)) {
            log.error("未查询到售后单详情 tenantId:{}, storeId:{}, channelType:{}, channelOrderId:{}, refundId:{}", refundParamDto.getTenantId(), refundParamDto.getShopId(), channelType, request.getOrderId(), request.getRefundId());
            return ResultGenerator.genResult(ResultCode.UNDEAL_ERROR).setData(ProjectConstant.NG);
        }
        if (ChannelType.MEITUAN.getValue() == channelType && MccConfigUtil.getMtRefundUseAfsDetail(refundParamDto.getTenantId())) {
            afterSaleEvent.setEventId(convertRefundEventIdByRefundType(ChannelStatusConvertUtil.mtRefundGoodsTypeMapping(orderAfsApplyDTO.getChannelAfsStatus(),
                    request.getIsAppeal(), String.valueOf(orderAfsApplyDTO.getApplyOpType()))));
        }
        afterSaleEvent.setRefundType(refundParamDto.isRefundAll() ? RefundTypeEnum.ALL.getValue(): RefundTypeEnum.PART.getValue());
        afterSaleEvent.setReturnGoodsWay(orderAfsApplyDTO.getReturnGoodsWay());
        afterSaleEvent.setReturnFreightDuty(orderAfsApplyDTO.getReturnFreightDuty());
        afterSaleEvent.setRefundGoodFreightNewType(orderAfsApplyDTO.getRefundGoodFreightNewType());
        afterSaleEvent.setOnlyRefund(refundParamDto.isOnlyRefund());
        List<PartRefundProductInfo> refundProductInfos = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(orderAfsApplyDTO.getAfsProductList())) {
            orderAfsApplyDTO.getAfsProductList().forEach(
                    afsProduct -> {
                        PartRefundProductInfo refundProductInfo = new PartRefundProductInfo();
                        // 财务相关字段
                        BeanUtils.copyProperties(afsProduct, refundProductInfo);
                        refundProductInfo.setSkuName(afsProduct.getSkuName());
                        refundProductInfo.setCustomSkuId(afsProduct.getSkuId());
                        refundProductInfo.setCount(afsProduct.getCount());
                        refundProductInfo.setSkuRefundAmount(afsProduct.getSkuRefundAmount());
                        refundProductInfo.setFoodPrice(afsProduct.getFoodPrice());
                        refundProductInfo.setBoxPrice(afsProduct.getBoxPrice());
                        refundProductInfo.setBoxNum(afsProduct.getBoxNum());
                        refundProductInfo.setRefundWeight(afsProduct.getRefundWeight());
                        refundProductInfo.setSpec(afsProduct.getSpec());
                        refundProductInfo.setChannelOrderItemId(afsProduct.getChannelOrderItemId());
                        refundProductInfo.setRefundPlatOrderPromotion(afsProduct.getRefundPlatOrderPromotion());
                        refundProductInfo.setRefundPoiOrderPromotion(afsProduct.getRefundPoiOrderPromotion());
                        refundProductInfo.setRefundPoiItemPromotion(afsProduct.getRefundPoiItemPromotion());
                        refundProductInfo.setRefundPlatItemPromotion(afsProduct.getRefundPlatItemPromotion());
                        refundProductInfos.add(refundProductInfo);
                    }
            );
            boolean isAmountRefund = orderAfsApplyDTO.getAfsProductList().stream()
                    .map(RefundProductDTO::getPartialRefundCount)
                    .filter(Objects::nonNull).anyMatch(v -> v > 0);
            if (isAmountRefund) {
                afterSaleEvent.setRefundType(RefundTypeEnum.AMOUNT.getValue());
            }
        }
        afterSaleEvent.setRefundApplyTime(orderAfsApplyDTO.getRefundApplyTime());
        //仅退款或退货退款
        afterSaleEvent.setAfterSaleServiceType(orderAfsApplyDTO.getServiceType());
        afterSaleEvent.setChannelOrderId(orderAfsApplyDTO.getChannelOrderId());
        afterSaleEvent.setAfterSaleId(orderAfsApplyDTO.getAfterSaleId());
        afterSaleEvent.setRefundPicList(ListUtils.listTruncated4TotalLength(BeanUtil.fromJson(request.getPictureJson(),
                new org.codehaus.jackson.type.TypeReference<List<String>>() {}, Collections.emptyList()), MAX_REFUND_PIC_LENGTH));
        afterSaleEvent.setPlatLogisticsPromotion(orderAfsApplyDTO.getPlatLogisticsPromotion());
        afterSaleEvent.setPoiLogisticsPromotion(orderAfsApplyDTO.getPoiLogisticsPromotion());
        afterSaleEvent.setPlatPackageIncome(orderAfsApplyDTO.getPlatPackageIncome());
        afterSaleEvent.setPlatPackagePromotion(orderAfsApplyDTO.getPlatPackagePromotion());
        afterSaleEvent.setPayPackageFee(orderAfsApplyDTO.getPayPackageFee());
        afterSaleEvent.setPoiPackageIncome(orderAfsApplyDTO.getPoiPackageIncome());
        afterSaleEvent.setPoiPackagePromotion(orderAfsApplyDTO.getPoiPackagePromotion());
        afterSaleEvent.setOriginalPackageFee(orderAfsApplyDTO.getOriginalPackageFee());
        afterSaleEvent.setFreightFee(orderAfsApplyDTO.getFreight());
        afterSaleEvent.setMerchantIncome(orderAfsApplyDTO.getMerchantIncome());
        if (refundType == OrderRefundFullTypeEnum.CUSTOMER_SENT.getValue()){
            if(Objects.equals(orderAfsApplyDTO.getReturnGoodsWay(), RefundGoodsWayEnum.USER_CALL_RIDER.getValue())){
                afterSaleEvent.setReturnGoodsStatus(ReturnGoodsStatusEnum.RIDER_DELIVERING.getCode());
            }else {
                afterSaleEvent.setReturnGoodsStatus(ReturnGoodsStatusEnum.CUSTOM_SENT.getCode());
            }
        }else {
            afterSaleEvent.setReturnGoodsStatus(orderAfsApplyDTO.getReturnGoodsStatusType());
        }
        afterSaleEvent.setPickUpRefundGoodsAddress(orderAfsApplyDTO.getPickUpRefundGoodsAddress());
        afterSaleEvent.setRefundGoodsPrivacyContactPhone(orderAfsApplyDTO.getRefundGoodsPrivacyContactPhone());
        //有地址变更退费
        if(StringUtils.isNotBlank(orderAfsApplyDTO.getAddressChangeFee()) && refundParamDto.isRefundAll()){
            afterSaleEvent.setAddressChangeFee(orderAfsApplyDTO.getAddressChangeFee());
        }
        setReturnFreight(afterSaleEvent, orderAfsApplyDTO);

        afterSaleEvent.setCommission(orderAfsApplyDTO.getCommission());
        afterSaleEvent.setCommissionType(orderAfsApplyDTO.getCommissionType());
        afterSaleEvent.setRefundAmount(orderAfsApplyDTO.getRefundPrice());
        afterSaleEvent.setRefundProducts(refundProductInfos);
        if (refundParamDto.isRefundAll() && Objects.equals(refundParamDto.getChannelTypeEnum(), ChannelTypeEnum.JD2HOME)) {
            afterSaleEvent.setRefundProducts(Lists.newArrayList());
        }
        afterSaleEvent.setIsCanReturnGoods(orderAfsApplyDTO.getIsCanReturnGoods());
        afterSaleEvent.setChannelExtRefundType(orderAfsApplyDTO.getChannelExtRefundType());
        afterSaleEvent.setProcessDeadline(orderAfsApplyDTO.getProcessDeadline());
        afterSaleEvent.setFirstAutoNegoType(orderAfsApplyDTO.getFirstAutoNegoType());

        if (MccConfigUtil.orderOcmsSplitCompare(afterSaleEvent.getTenantId(), afterSaleEvent.getShopId(), afterSaleEvent.getChannelType())) {
            OcmsSplitTraceUtil.putUniqueId(afterSaleEvent.getTenantId(), afterSaleEvent.getShopId(), afterSaleEvent.getOrderBizType(),
                    afterSaleEvent.getChannelOrderId(), afterSaleEvent.getAfterSaleId(), afterSaleEvent.getEventType().toString(), String.valueOf(afterSaleEvent.getEventId()), OcmsSplitTraceUtil.DATA_TYPE_OCMS);
        }
        if (MccConfigUtil.orderOcmsSplitSwitch(refundParamDto.getTenantId(), refundParamDto.getShopId(), afterSaleEvent.getChannelType())){
            logAfterSaleEvent(afterSaleEvent, refundType);
        }
        return refundNotifyToOrderDomain(afterSaleEvent);
    }


    private static void setReturnFreight(ChannelAfterSaleEvent afterSaleEvent, OrderAfsApplyDTO orderAfsApplyDTO) {
        Optional.ofNullable(orderAfsApplyDTO.getExtend()).ifPresent(extend -> {
            Optional.ofNullable(extend.get("preReturnFreight")).map(Integer::parseInt).ifPresent(afterSaleEvent::setPreReturnFreight);
            Optional.ofNullable(extend.get("returnFreight")).map(Integer::parseInt).ifPresent(afterSaleEvent::setReturnFreight);
        });
        afterSaleEvent.setCostCustomerFreightFee(orderAfsApplyDTO.getCostCustomerFreightFee());
    }

    private void logAfterSaleEvent(ChannelAfterSaleEvent afterSaleEvent, int refundApplyType) {
        OrderTrackEvent orderTrackEvent = new OrderTrackEvent();
        orderTrackEvent.setTrackSource(TrackSource.CHANNEL.getType());
        orderTrackEvent.setOperateTime(Instant.now().toEpochMilli());
        orderTrackEvent.setTenantId(afterSaleEvent.getTenantId());
        orderTrackEvent.setOrderBizType(afterSaleEvent.getOrderBizType());
        orderTrackEvent.setUnifyOrderId(afterSaleEvent.getChannelOrderId());
        if(afterSaleEvent.isOnlyRefund()){
            orderTrackEvent.setTrackOpType(TrackOpType.AGREE_ONLY_REFUND_WITHOUT_RETURN_GOODS.getOpType());
        }else {
            ChannelTrackTypeEnum channelTrackTypeEnum = convertTrackOpType(afterSaleEvent, refundApplyType);
            if (Objects.isNull(channelTrackTypeEnum)) {
                log.error("未查询到渠道操作类型,{},{}", afterSaleEvent, refundApplyType);
                return;
            }
            orderTrackEvent.setTrackOpType(channelTrackTypeEnum.getOpType().getOpType());
        }
        log.info("ocms拆分轨迹信息,{}", orderTrackEvent);
        orderTrackService.sendAsyncMessage(orderTrackEvent);
    }

    private ChannelTrackTypeEnum convertTrackOpType(ChannelAfterSaleEvent afterSaleEvent, int refundApplyType) {
        return ChannelTrackTypeEnum.codeOf(AfterSalePatternEnum.enumOf(afterSaleEvent.getRefundType()), afterSaleEvent.getAfterSaleServiceType() == 2, OrderRefundFullTypeEnum.findByValue(refundApplyType), afterSaleEvent.getChannelType());
    }

    private ResultStatus dealWithChannelNotExistAfterSaleApply(RefundParamDto refundParamDto, ChannelAfterSaleEvent afterSaleEvent) {
        afterSaleEvent.setEventId(convertRefundEventIdByRefundType(refundParamDto.getRefundType()));
        OrderNotifyRequest request = refundParamDto.getRequest();
        afterSaleEvent.setChannelOrderId(request.getOrderId());
        afterSaleEvent.setAfterSaleId(request.getRefundId());
        afterSaleEvent.setAfsApplySourceType(AfterSaleSourceTypeEnum.LOCAL.getValue());
        afterSaleEvent.setRefundApplyTime(ConverterUtils.stringToMillis(request.getRefundApplyTime()));
        if (refundParamDto.getRefundType() == OrderRefundGoodsType.REJECT_BY_CUSTOMER.getValue()){
            afterSaleEvent.setAfterSaleServiceType(AfterSaleServiceTypeEnum.REJECT_BY_CUSTOMER.getValue());
            afterSaleEvent.setRefundType(RefundTypeEnum.REJECT_BY_CUSTOMER.getValue());
        }else {
            afterSaleEvent.setAfterSaleServiceType(AfterSaleServiceTypeEnum.REFUND.getValue());
            afterSaleEvent.setRefundType(refundParamDto.isRefundAll() ? RefundTypeEnum.ALL.getValue() : RefundTypeEnum.PART.getValue());
        }
        if (Objects.nonNull(refundParamDto.getPartParam())){
            List<PartRefundProductInfo> refundProductInfos = new LinkedList<>();
            if (CollectionUtils.isNotEmpty(refundParamDto.getPartParam().getProducts())) {
                refundParamDto.getPartParam().getProducts().forEach(
                        afsProduct -> {
                            PartRefundProductInfo refundProductInfo = new PartRefundProductInfo();
                            // 财务相关字段
                            BeanUtils.copyProperties(afsProduct, refundProductInfo);
                            refundProductInfo.setSkuName(afsProduct.getSkuName());
                            refundProductInfo.setCustomSkuId(afsProduct.getCustomSpuId());
                            refundProductInfo.setCount(afsProduct.getCount());
                            refundProductInfo.setSkuRefundAmount(afsProduct.getSkuRefundAmount());
                            refundProductInfo.setFoodPrice(afsProduct.getFoodPrice());
                            refundProductInfo.setBoxPrice(afsProduct.getBoxPrice());
                            refundProductInfo.setBoxNum(afsProduct.getBoxNum());
                            refundProductInfo.setRefundWeight(afsProduct.getRefundWeight());
                            refundProductInfo.setSpec(afsProduct.getSpec());
                            refundProductInfo.setItemId(afsProduct.getItemId());
                            refundProductInfos.add(refundProductInfo);
                        }
                );
                afterSaleEvent.setRefundProducts(refundProductInfos);
                afterSaleEvent.setRefundAmount(refundParamDto.getPartParam().getRefundAmount());
                afterSaleEvent.setRefundApplyTime(refundParamDto.getPartParam().getRefundApplyTime());
            }
        }
        // 增加京东调整单运费以及包装费记录
        dealJddjAdjustOrderFee(refundParamDto, afterSaleEvent);
        return refundNotifyToOrderDomain(afterSaleEvent);
    }

    private void dealJddjAdjustOrderFee(RefundParamDto refundParamDto, ChannelAfterSaleEvent afterSaleEvent) {
        try {
            // 只有配置了开关的租户才会存储调整单包装费运费数据
            if(!MccConfigUtil.checkDealJddjAdjustOrderFee(afterSaleEvent.getTenantId())){
                return;
            }
            if (!ChannelTypeEnum.JD2HOME.equals(refundParamDto.getChannelTypeEnum())) {
                return;
            }
            if (!refundParamDto.getRequest().getRefundId().startsWith(JD_ADJUST_ORDER_PREFIX)) {
                return;
            }
            if(StringUtils.isEmpty(refundParamDto.getPartParam().getOrderJsonString())) {
                return;
            }
            // 此处记录变更后的正单运费和包装费金额，后续settlement中进行相减处理
            ChannelOrderDetailDTO channelOrderDetailDTO = JSON.parseObject(refundParamDto.getPartParam().getOrderJsonString(), ChannelOrderDetailDTO.class);
            afterSaleEvent.setFreightFee(channelOrderDetailDTO.getFreight());
            afterSaleEvent.setPoiLogisticsPromotion(channelOrderDetailDTO.getPoiLogisticsPromotion());
            afterSaleEvent.setPlatLogisticsPromotion(channelOrderDetailDTO.getPlatLogisticsPromotion());
            afterSaleEvent.setPoiLogisticsIncome(channelOrderDetailDTO.getPoiLogisticsIncome());
            afterSaleEvent.setOriginalPackageFee(channelOrderDetailDTO.getPackageAmt());
            afterSaleEvent.setPayPackageFee(channelOrderDetailDTO.getPayPackageAmt());
            afterSaleEvent.setPoiPackageIncome(channelOrderDetailDTO.getPoiPackageAmt());
            afterSaleEvent.setPlatPackageIncome(channelOrderDetailDTO.getPlatPackageAmt());
            // 使用分摊数据计算调整后运费优惠
            dealJddjAdjustOrderFee(afterSaleEvent, channelOrderDetailDTO);
            // mock代码上线删除
            //mockJddjAdjustOrderFee(afterSaleEvent);
        } catch (Exception e) {
            log.error("dealJddjAdjustOrderFee error, refundParamDto:{}, afterSaleEvent:{}", refundParamDto, afterSaleEvent, e);
        }
    }

    private void dealJddjAdjustOrderFee(ChannelAfterSaleEvent afterSaleEvent, ChannelOrderDetailDTO channelOrderDetailDTO) {
        GoodsSettlementResult goodsSettlementResult = queryGoodsSettlementInfo(afterSaleEvent.getTenantId(),
                afterSaleEvent.getChannelType(), afterSaleEvent.getChannelOrderId(), afterSaleEvent.getShopId());
        if(goodsSettlementResult == null || CollectionUtils.isEmpty(goodsSettlementResult.getFreightActivityDTOList())) {
            return;
        }
        int poiLogisticsPromotion = 0;
        int platLogisticsPromotion = 0;
        for(OrderDiscountDetailDTO orderDiscountDetailDTO : goodsSettlementResult.getFreightActivityDTOList()) {
            platLogisticsPromotion += orderDiscountDetailDTO.getChannelCharge();
            poiLogisticsPromotion += orderDiscountDetailDTO.getBizCharge();
        }
        //自送
        if (Objects.equals(channelOrderDetailDTO.getDeliveryDetail().isSelfDelivery, 1)) {
            afterSaleEvent.setPoiLogisticsIncome(channelOrderDetailDTO.getFreight() - poiLogisticsPromotion - channelOrderDetailDTO.getPoiFarDistanceFreight());
        } else {
            afterSaleEvent.setPoiLogisticsIncome(-poiLogisticsPromotion - channelOrderDetailDTO.getPoiFarDistanceFreight());
        }
        afterSaleEvent.setPoiLogisticsPromotion(poiLogisticsPromotion);
        afterSaleEvent.setPlatLogisticsPromotion(platLogisticsPromotion);

    }

    private void mockJddjAdjustOrderFee(ChannelAfterSaleEvent afterSaleEvent) {
        ChannelAfterSaleEvent channelAfterSaleEvent = MccConfigUtil.getMockJddjAdjustOrderFee(afterSaleEvent.getTenantId());
        if(channelAfterSaleEvent != null) {
            afterSaleEvent.setFreightFee(afterSaleEvent.getFreightFee() - Optional.ofNullable(channelAfterSaleEvent.getFreightFee()).orElse(0));
            afterSaleEvent.setPoiLogisticsPromotion(afterSaleEvent.getPoiLogisticsPromotion() - Optional.ofNullable(channelAfterSaleEvent.getPoiLogisticsPromotion()).orElse(0));
            afterSaleEvent.setPlatLogisticsPromotion(afterSaleEvent.getPlatLogisticsPromotion() - Optional.ofNullable(channelAfterSaleEvent.getPlatLogisticsPromotion()).orElse(0));
            afterSaleEvent.setPoiLogisticsIncome(afterSaleEvent.getPoiLogisticsIncome() - Optional.ofNullable(channelAfterSaleEvent.getPoiLogisticsIncome()).orElse(0));
            afterSaleEvent.setOriginalPackageFee(afterSaleEvent.getOriginalPackageFee() - Optional.ofNullable(channelAfterSaleEvent.getOriginalPackageFee()).orElse(0));
            afterSaleEvent.setPayPackageFee(afterSaleEvent.getPayPackageFee() - Optional.ofNullable(channelAfterSaleEvent.getPayPackageFee()).orElse(0));
            afterSaleEvent.setPoiPackageIncome(afterSaleEvent.getPoiPackageIncome() - Optional.ofNullable(channelAfterSaleEvent.getPoiPackageIncome()).orElse(0));
            afterSaleEvent.setPlatPackageIncome(afterSaleEvent.getPlatPackageIncome() - Optional.ofNullable(channelAfterSaleEvent.getPlatPackageIncome()).orElse(0));
        }
    }

    private boolean channelNotExistAfterSaleApply(RefundParamDto refundParamDto) {
        if (ChannelTypeEnum.JD2HOME.equals(refundParamDto.getChannelTypeEnum())){
            if (refundParamDto.getRefundType() == OrderRefundGoodsType.REJECT_BY_CUSTOMER.getValue()){
                return true;
            }
            if (refundParamDto.getAfsApplySourceType() == AfterSaleSourceTypeEnum.LOCAL.getValue()){
                return true;
            }
            if (refundParamDto.getRequest().getRefundId().startsWith(JD_ADJUST_ORDER_PREFIX)){
                return true;
            }
        }
        return false;
    }

    private int convertRefundEventIdByRefundType(int refundType) {
        OrderRefundFullTypeEnum refundGoodsTypeEnum = OrderRefundFullTypeEnum.findByValue(refundType);
        switch (refundGoodsTypeEnum) {
            case APPLY:
            case APPLY_APPEAL:
            case FIRST_APPLY_APPEAL:
                //更新也通过freezeOrder实现
            case UPDATE_APPLY:
                return ChannelAfterSaleEventEnum.APPLY.getEventId();
            case FIRST_APPLY_AGREE:
            case FIRST_APPLY_APPEAL_AGREE:
            case REFUND_WAITE_RETURN:
                return ChannelAfterSaleEventEnum.AGREE.getEventId();
            case FIRST_APPLY_REJECT:
            case FIRST_APPLY_APPEAL_REJECT:
                return ChannelAfterSaleEventEnum.REJECT.getEventId();
            case TENANT_AGREE:
            case CHANNEL_SYSTEM_AGREE:
            case CALL_CENTER_AGREE:
            case CHANNEL_CUSTOMER_AGREE:
            case REFUND_SUCCESS:
                return ChannelAfterSaleEventEnum.REFUND_SUCCESS.getEventId();
            case CALL_CENTER_REJECT:
            case TENANT_REJECT:
                return ChannelAfterSaleEventEnum.REJECT_RETURN.getEventId();
            case APPLY_CANCELED:
            case APPLY_CANCEL_BY_CUSTOMER:
            case APPEAL_CANCEL_BY_CUSTOMER:
            case APPLY_CANCEL_BY_CHANNEL:
                return ChannelAfterSaleEventEnum.APPLY_CANCELED.getEventId();
            case REJECT_BY_CUSTOMER:
                return ChannelAfterSaleEventEnum.REJECT_BY_CUSTOMER.getEventId();
            case CUSTOMER_SENT:
                return ChannelAfterSaleEventEnum.CUSTOMER_SENT_BACK_GOODS.getEventId();
            case REFUND_CUSTOMER_SENT:
                return ChannelAfterSaleEventEnum.REFUND_CUSTOMER_SENT.getEventId();
            default:
                return ChannelAfterSaleEventEnum.UN_KNOWN.getEventId();

        }
    }

    private ResultStatus doRefundGoods(ChannelTypeEnum channelTypeEnum, int refundType, int sponsor, int afsApplyType, OrderNotifyRequest request) {
        OrderAllRefundGoodsRequest orderAllRefundGoodsRequest = channelOrderConverterService.orderAllRefundGoodsNotify(request);
        CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
        Long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
        Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);


        //获取渠道订单详情，根据其中门店信息，校验门店是否接中台
        String orderDetail = getOrderDetail(tenantId, channelTypeEnum.getCode(), request.getOrderId(), appId);
        Long storeId = getShopId(orderDetail);
        boolean onlyRefund = Objects.equals(request.getServiceType(), "2") && Objects.equals(request.getStatus(), "21") && request.getFinalReviewType() == 1;

        ChannelTypeEnum channelType = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);

        if (Objects.nonNull(channelType) && MccConfigUtil.orderOcmsSplitSwitch(tenantId, storeId, channelType.getCode())){
            return doRefundByEvent(RefundParamDto.builder()
                    .refundType(refundType)
                    .channelTypeEnum(channelTypeEnum)
                    .request(request)
                    .afsApplySourceType(AfterSaleSourceTypeEnum.CHANNEL.getValue())
                    .afsApplyType(afsApplyType)
                    .sponsor(sponsor)
                    .refundAll(true)
                    .tenantId(tenantId)
                    .appId(appId)
                    .shopId(storeId)
                    .onlyRefund(onlyRefund)
                    .build());
        }
        if (Objects.nonNull(channelType) && MccConfigUtil.orderOcmsSplitCompare(tenantId, storeId, channelType.getCode())){
            doRefundByEvent(RefundParamDto.builder()
                    .refundType(refundType)
                    .channelTypeEnum(channelTypeEnum)
                    .request(request)
                    .afsApplySourceType(AfterSaleSourceTypeEnum.CHANNEL.getValue())
                    .afsApplyType(afsApplyType)
                    .sponsor(sponsor)
                    .refundAll(true)
                    .tenantId(tenantId)
                    .appId(appId)
                    .shopId(storeId)
                    .onlyRefund(onlyRefund)
                    .build());
        }
        orderAllRefundGoodsRequest.setRefundAmount(ConverterUtils.stringToInt(request.getRefundPrice()));
        orderAllRefundGoodsRequest.setTenantId(tenantId);
        orderAllRefundGoodsRequest.setChannelType(channelTypeEnum.getCode());
        orderAllRefundGoodsRequest.setRefundType(refundType);
        orderAllRefundGoodsRequest.setSponsor(sponsor);
        orderAllRefundGoodsRequest.setRefundId(request.getRefundId());
        orderAllRefundGoodsRequest.setAfsApplyType(afsApplyType);// 售中售后申请
        orderAllRefundGoodsRequest.setServiceType(Integer.parseInt(request.getServiceType()));
        orderAllRefundGoodsRequest.setRefundApplyTime(ConverterUtils.stringToMillis(request.getRefundApplyTime()));
        orderAllRefundGoodsRequest.setIsAppeal(request.getIsAppeal() == IsAppealEnum.IS_APPEAL.getCode() ? true : false);
        orderAllRefundGoodsRequest.setOnlyRefund(onlyRefund);
        log.info("京东退货退款 request.pictureJson : {}", request.getPictureJson());
        orderAllRefundGoodsRequest.setRefundPicList(ListUtils.listTruncated4TotalLength(BeanUtil.fromJson(request.getPictureJson(),
                new org.codehaus.jackson.type.TypeReference<List<String>>() {}, Collections.emptyList()), MAX_REFUND_PIC_LENGTH));
        log.info("京东退货退款 orderAllRefundGoodsRequest.refundPicList : {}", orderAllRefundGoodsRequest.getRefundPicList());
        if (StringUtils.isBlank(orderAllRefundGoodsRequest.getReason())) {
            orderAllRefundGoodsRequest.setReason("取消申请");
        }
        QueryDeadlineTimeDTO queryDeadlineTimeDTO = QueryDeadlineTimeDTO.builder()
                .refundId(orderAllRefundGoodsRequest.getRefundId())
                .refundApplyTime(orderAllRefundGoodsRequest.getRefundApplyTime())
                .channelOrderId(orderAllRefundGoodsRequest.getChannelOrderId())
                .channelType(orderAllRefundGoodsRequest.getChannelType())
                .tenantId(orderAllRefundGoodsRequest.getTenantId())
                .appPoiCode(request.getAppPoiCode())
                .build();
        orderAllRefundGoodsRequest.setProcessDeadline(queryProcessDeadline(queryDeadlineTimeDTO, appId, orderDetail));
        OrderResponse orderResponse = channelOrderThriftServiceProxy.orderAllRefundGoodsNotify(orderAllRefundGoodsRequest);
        if (Objects.isNull(orderResponse)) {
            return ResultGenerator.genResult(ResultCode.FAIL, "推送整单退款消息失败").setData(ProjectConstant.NG);
        }
        if (orderResponse.getCode() == ResultCodeEnum.SUCCESS.getValue()) {
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        else if (isJddjMockOrder(tenantId, orderAllRefundGoodsRequest.getChannelOrderId(), channelTypeEnum)) {
            log.info("京东测试订单那，忽略该请求:{}", request);
            throw new MockTestException();
        }
        return ResultGenerator.genResult(ResultCode.FAIL, "整单退款消息推送失败").setData(ProjectConstant.NG);
    }

    private boolean isJddjMockOrder(long tenantId, String channelOrderId, ChannelTypeEnum channelTypeEnum) {
        if (ChannelTypeEnum.JD2HOME.equals(channelTypeEnum)) {
            return MccConfigUtil.getJddjMockResponseTenant().getTenantIdList().contains(tenantId);
            //&& jddjMockTestService.isMockOrder(tenantId, channelOrderId);//mock环境查不到订单
        }
        return false;
    }

    private ResultStatus doPartRefund(ChannelTypeEnum channelTypeEnum, int refundType, int sponsor, OrderNotifyRequest request) {
        return doPartRefund(channelTypeEnum, refundType, sponsor, 0, request);
    }


    /**
     * 请求中台部分退款消息
     */
    private ResultStatus doPartRefund(ChannelTypeEnum channelTypeEnum, int refundType, int sponsor, int afsApplyType, OrderNotifyRequest request) {
        OrderPartRefundRequest partParam;
        if (ChannelTypeEnum.isMeiTuan(channelTypeEnum)) {
            partParam = channelOrderConverterService.mtOrderPartRefundNotify(request);
        } else {
            partParam = channelOrderConverterService.orderPartRefundNotify(request);
        }
        CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
        Long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
        Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
        partParam.setTenantId(tenantId);
        partParam.setChannelType(channelTypeEnum.getCode());
        partParam.setOrderPartRefundType(refundType);
        partParam.setSponsor(sponsor);
        partParam.setRefundId(request.getRefundId());
        partParam.setAfsApplyType(afsApplyType);
        partParam.setRefundApplyTime(ConverterUtils.stringToMillis(request.getRefundApplyTime()));
        partParam.setIsAppeal(request.getIsAppeal() == IsAppealEnum.IS_APPEAL.getCode() ? true : false);
        ChannelOrderDetailDTO orderDetailDTO = getOrderDetailDTO(partParam.getTenantId(), channelTypeEnum.getCode(), request.getOrderId(), appId);
        try {
            installRefundSku(channelTypeEnum, partParam, request.getProductJson(), orderDetailDTO, tenantId, request.getOrderId(), request.getTenantAppId());
        } catch (NullPointerException e) {
            log.warn("部分退款消息推送失败, 订单详情:{}", orderDetailDTO);
            // 美团渠道可能因为orderDetailDTO出现npe
            return ResultGenerator.genResult(ResultCode.FAIL, "部分退款消息推送失败").setData(ProjectConstant.NG);
        }
        Long shopId = Optional.ofNullable(orderDetailDTO).map(ChannelOrderDetailDTO::getStoreId).orElse(NumberUtils.LONG_ZERO);
        partParam.setOrderJsonString(Optional.ofNullable(orderDetailDTO).map(JSONObject::toJSONString).orElse(""));
        partParam.setRefundPicList(ListUtils.listTruncated4TotalLength(BeanUtil.fromJson(request.getPictureJson(),
                new org.codehaus.jackson.type.TypeReference<List<String>>() {}, Collections.emptyList()), MAX_REFUND_PIC_LENGTH));
        partParam.setAuditType(MtAuditTypeServiceUtils.getAuditTypeFromRefund(request));


        log.info("OrderPartRefundRequest partParam:{}",partParam);


        if (checkIsMtAmountRefund(partParam)) {
            //按金额灵活退
            if (ChannelTypeEnum.MEITUAN.getCode() != channelTypeEnum.getCode()){
                return ResultGenerator.genResult(ResultCode.FAIL,"非美团渠道按金额灵活退").setData(ProjectConstant.NG);
            }
            OrderAfsApplyDTO orderAfsApplyDTO = getAfsDetail(partParam.getTenantId(), shopId, channelTypeEnum.getCode(),  request.getOrderId(), appId, request.getRefundId());
            BizAfterSaleCreateOrUpdateRequest bizAfterSaleCreateOrUpdateRequest = buildBizAfterSaleCreateOrUpdateReq4MtAmountRefund(partParam, channelTypeEnum, orderAfsApplyDTO);
            BizAfterSaleCreateOrUpdateResponse response = channelOrderThriftServiceProxy.orderAfterSaleNotify(bizAfterSaleCreateOrUpdateRequest);

            if (Objects.isNull(response) || response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                return ResultGenerator.genResult(ResultCode.FAIL, "推送按金额灵活退款消息失败").setData(ProjectConstant.NG);
            }
            if (response.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
        } else if (checkIsMtWeightRefund(partParam)) {
            //按克重退
            OrderAfsApplyDTO orderAfsApplyDTO = getAfsDetail(partParam.getTenantId(), shopId, channelTypeEnum.getCode(),  request.getOrderId(), appId, request.getRefundId());
            BizAfterSaleCreateOrUpdateRequest bizAfterSaleCreateOrUpdateRequest = buildBizAfterSaleCreateOrUpdateRequest(partParam, channelTypeEnum, orderAfsApplyDTO);
            BizAfterSaleCreateOrUpdateResponse response = channelOrderThriftServiceProxy.orderAfterSaleNotify(bizAfterSaleCreateOrUpdateRequest);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())
                    || !Objects.equals(response.getStatus().getCode(), ResultCodeEnum.SUCCESS.getValue())) {
                return ResultGenerator.genResult(ResultCode.FAIL, "推送克重退款消息失败").setData(ProjectConstant.NG);
            } else {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
        } else {
            //件数退款
            if (MccConfigUtil.orderOcmsSplitSwitch(tenantId, shopId, partParam.getChannelType())){
                return doRefundByEvent(RefundParamDto.builder()
                        .sponsor(sponsor)
                        .refundType(refundType)
                        .request(request)
                        .shopId(shopId)
                        .appId(appId)
                        .tenantId(tenantId)
                        .afsApplyType(afsApplyType)
                        .channelTypeEnum(channelTypeEnum)
                        .afsApplySourceType(AfterSaleSourceTypeEnum.CHANNEL.getValue())
                        .partParam(partParam)
                        .build());
            }
            if (MccConfigUtil.orderOcmsSplitCompare(tenantId, shopId, partParam.getChannelType())){
                doRefundByEvent(RefundParamDto.builder()
                        .sponsor(sponsor)
                        .refundType(refundType)
                        .request(request)
                        .shopId(shopId)
                        .appId(appId)
                        .tenantId(tenantId)
                        .afsApplyType(afsApplyType)
                        .channelTypeEnum(channelTypeEnum)
                        .partParam(partParam)
                        .afsApplySourceType(AfterSaleSourceTypeEnum.CHANNEL.getValue())
                        .build());
            }

            QueryDeadlineTimeDTO queryDeadlineTimeDTO = QueryDeadlineTimeDTO.builder()
                    .refundId(partParam.getRefundId())
                    .refundApplyTime(partParam.getRefundApplyTime())
                    .channelOrderId(partParam.getChannelOrderId())
                    .channelType(partParam.getChannelType())
                    .tenantId(partParam.getTenantId())
                    .storeId(shopId)
                    .appPoiCode(request.getAppPoiCode())
                    .build();
            partParam.setProcessDeadline(queryProcessDeadline(queryDeadlineTimeDTO, appId, Strings.EMPTY));
            OrderResponse orderResponse = channelOrderThriftServiceProxy.orderPartRefundNotify(partParam);
            if (Objects.isNull(orderResponse)) {
                return ResultGenerator.genResult(ResultCode.FAIL, "推送部分退款消息失败").setData(ProjectConstant.NG);
            }
            if (orderResponse.getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            } else if (isJddjMockOrder(tenantId, partParam.getChannelOrderId(), channelTypeEnum)) {
                log.info("京东测试订单那，忽略部分退款请求:{}", request);
                throw new MockTestException();
            }
        }
        return ResultGenerator.genResult(ResultCode.FAIL, "部分退款消息推送失败").setData(ProjectConstant.NG);
    }

    // mock代码
    private boolean skipMtPartRefund(int channelType, Long tenantId){
        MockMtOrderAndRefundDTO mockMtOrderAndRefundDTO = MccConfigUtil.mockMtOrderAndRefund(tenantId);
        if(mockMtOrderAndRefundDTO != null && ChannelTypeEnum.MEITUAN.getCode() == channelType) {
            return Objects.equals(mockMtOrderAndRefundDTO.getSkipPartRefund(), true);
        }
        return false;
    }

    // mock代码
    private boolean skipMtAllRefund(int channelType, Long tenantId){
        MockMtOrderAndRefundDTO mockMtOrderAndRefundDTO = MccConfigUtil.mockMtOrderAndRefund(tenantId);
        if(mockMtOrderAndRefundDTO != null && ChannelTypeEnum.MEITUAN.getCode() == channelType) {
            return Objects.equals(mockMtOrderAndRefundDTO.getSkipAllRefund(), true);
        }
        return false;
    }

    private Long queryProcessDeadline(QueryDeadlineTimeDTO queryDeadlineTimeDTO, Long appId, String orderDetail) {
        try{
            if(Objects.isNull(queryDeadlineTimeDTO)){
                return NumberUtils.LONG_ZERO;
            }
            log.info("ChannelOrderCallbackServiceImpl.queryProcessDeadline request:{}", queryDeadlineTimeDTO);
            Long tenantId = queryDeadlineTimeDTO.getTenantId();
            if(!isSetProcessDeadline(tenantId)){
                log.info("此租户不设置退款处理截止时间,tenantId:{}", tenantId);
                return NumberUtils.LONG_ZERO;
            }
            Long storeId = queryDeadlineTimeDTO.getStoreId();
            if((Objects.isNull(storeId) || Objects.equals(NumberUtils.LONG_ZERO, storeId))){
                storeId = getOrderDetailStoreId(queryDeadlineTimeDTO, orderDetail);
            }
            long refundApplyTime = queryDeadlineTimeDTO.getRefundApplyTime();
            int channelType = queryDeadlineTimeDTO.getChannelType();
            DynamicChannelType dynamicChannelType = DynamicChannelType.findOf(channelType);
            if(Objects.equals(dynamicChannelType, DynamicChannelType.MEITUAN)
                    || Objects.equals(dynamicChannelType, DynamicChannelType.ELEM)
                    || Objects.equals(dynamicChannelType, DynamicChannelType.DOU_YIN)){
                OrderAfsApplyDTO orderAfsApplyDTO = getAfsDetail(queryDeadlineTimeDTO.getTenantId(), storeId, channelType,  queryDeadlineTimeDTO.getChannelOrderId(), appId, queryDeadlineTimeDTO.getRefundId());
                return Objects.nonNull(orderAfsApplyDTO) ? orderAfsApplyDTO.getProcessDeadline(): 0L ;
            }else if(Objects.equals(dynamicChannelType, DynamicChannelType.JD2HOME)){
                return refundApplyTime +  30 * 60 * 1000;
            }
        }catch (Exception e){
            log.error("queryProcessDeadline error", e);
        }
        return NumberUtils.LONG_ZERO;
    }


    /**
     * 是否需要设置处理截止时间
     * @param tenantId
     * @return
     */
    public boolean isSetProcessDeadline(Long tenantId) {
        if (MccConfigUtil.isMtDrunkHorseTenant(tenantId.toString())){
            return false;
        }
        try {
            List<Long> uwmsTenantIds = medicineTenantService.getAllUwmsTenantIds();
            if(uwmsTenantIds.contains(tenantId)){
                return false;
            }
            return MccConfigUtil.isSetProcessDeadlineTenant(tenantId);
        }catch (Exception e){
            log.info("获取是否需要设置处理截止时间失败", e);
        }
        return false;
    }



    private BizAfterSaleCreateOrUpdateRequest buildBizAfterSaleCreateOrUpdateReq4MtAmountRefund(OrderPartRefundRequest partParam, ChannelTypeEnum channelTypeEnum, OrderAfsApplyDTO orderAfsApplyDTO){
        ChannelOrderDetailDTO orderDetail = JSON.parseObject(partParam.getOrderJsonString(), ChannelOrderDetailDTO.class);
        BizAfterSaleCreateOrUpdateRequest request = new BizAfterSaleCreateOrUpdateRequest();
        request.setTenantId(partParam.getTenantId());
        request.setChannelId(channelTypeEnum.getCode());
        request.setOrderViewId(partParam.getChannelOrderId());
        request.setAfterSaleId(partParam.getRefundId());
        request.setReason(partParam.getReason());
        request.setSponsor(partParam.getSponsor());
        request.setRefundAmount(partParam.getRefundAmount());
        request.setAppeal(partParam.isIsAppeal());
        request.setAfsApplyType(partParam.getAfsApplyType());
        request.setAfsApplyPattern(AfterSalePatternEnum.AMOUNT.getValue());

        request.setOperateTime(partParam.getTimestamp());
        request.setOperateType(partParam.getOrderPartRefundType());
        request.setOrderSource(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue());
        request.setCommission(Optional.ofNullable(orderAfsApplyDTO).map(OrderAfsApplyDTO::getCommission).orElse(0));
        request.setPoiPromotion(orderAfsApplyDTO.getPoiPromotion());
        request.setPlatPromotion(orderAfsApplyDTO.getPlatPromotion());
        request.setCommission(orderAfsApplyDTO.getCommission());
        request.setPackageAmt(orderAfsApplyDTO.getPlatPackageAmt());
        request.setScoreDeduction(orderAfsApplyDTO.getScoreDeduction());
        request.setSelfPickServiceFee(orderAfsApplyDTO.getSelfPickServiceFee());
        request.setPlatLogisticsPromotion(orderAfsApplyDTO.getPlatLogisticsPromotion());
        request.setPoiLogisticsPromotion(orderAfsApplyDTO.getPoiLogisticsPromotion());
        if (Objects.nonNull(orderDetail)) {
            request.setShopId(orderDetail.getStoreId());

        }
        request.setAuditType(partParam.getAuditType());
        request.setProcessDeadline(Optional.ofNullable(orderAfsApplyDTO).map(OrderAfsApplyDTO::getProcessDeadline).orElse(0L));
        List<RefundSku> products = partParam.getProducts();
        List<BizOrderItemAfterSaleCreateModel> orderItems = Lists.newArrayList();
        Map<Long, RefundSku> map = products.stream().collect(Collectors.toMap(RefundSku::getItemId, a -> a, (k1, k2) -> k1));
        // 判断是否走新逻辑
        boolean refundNewTrack = MccConfigUtil.getMtUseRefundNewTrackTenantsSwitch(partParam.getTenantId());
        for (RefundProductDTO dto : orderAfsApplyDTO.getAfsProductList()) {
            BizOrderItemAfterSaleCreateModel bizOrderItemAfterSaleCreateModel = new BizOrderItemAfterSaleCreateModel();
            bizOrderItemAfterSaleCreateModel.setCustomerSkuId(dto.getSkuId());
            bizOrderItemAfterSaleCreateModel.setSkuName(dto.getSkuName());
            bizOrderItemAfterSaleCreateModel.setQuantity(dto.getCount());
            bizOrderItemAfterSaleCreateModel.setRefundWeight(dto.getRefundWeight());
            bizOrderItemAfterSaleCreateModel.setSkuCode(map.get(dto.getItemId()) != null ? map.get(dto.getItemId()).getUpc() : "");
            bizOrderItemAfterSaleCreateModel.setItemId(dto.getItemId());
            bizOrderItemAfterSaleCreateModel.setRefundAmt(dto.getSkuRefundAmount());
            bizOrderItemAfterSaleCreateModel.setCustomerSpu(dto.getCustomSpu());
            bizOrderItemAfterSaleCreateModel.setCustomSkuIdVersion(BizOrderItemCreateForMiddleModel.CUSTOM_SKU_VERSION_SPU);
            bizOrderItemAfterSaleCreateModel.setSpec(dto.getSpec());
            bizOrderItemAfterSaleCreateModel.setFoodPrice(dto.getFoodPrice());
            bizOrderItemAfterSaleCreateModel.setBoxAmt(dto.getBoxNum() * dto.getBoxPrice());
            bizOrderItemAfterSaleCreateModel.setFlexCount(dto.getFlexCount());
            bizOrderItemAfterSaleCreateModel.setRefundPlatPromotionAmt(dto.getRefundPlatPromotion());
            bizOrderItemAfterSaleCreateModel.setRefundPoiPromotionAmt(dto.getRefundPoiPromotion());
            bizOrderItemAfterSaleCreateModel.setRefundPlatOrderPromotion(dto.getRefundPlatOrderPromotion());
            bizOrderItemAfterSaleCreateModel.setRefundPoiOrderPromotion(dto.getRefundPoiOrderPromotion());
            if(refundNewTrack) {
                bizOrderItemAfterSaleCreateModel.setRefundPoiItemPromotion(dto.getRefundPoiItemPromotion());
                bizOrderItemAfterSaleCreateModel.setRefundPlatItemPromotion(dto.getRefundPlatItemPromotion());
            } else {
                bizOrderItemAfterSaleCreateModel.setRefundPoiItemPromotion(dto.getPoiTotalItemPromotion());
                bizOrderItemAfterSaleCreateModel.setRefundPlatItemPromotion(dto.getPlatTotalItemPromotion());
            }
            orderItems.add(bizOrderItemAfterSaleCreateModel);
        }
        request.setProducts(orderItems);
        return request;
    }

    private ResultStatus doPartRefundGoods(ChannelTypeEnum channelTypeEnum, int refundType, int sponsor, OrderNotifyRequest request) {
        return doPartRefundGoods(channelTypeEnum, refundType, sponsor, 1, request);
    }

    /**
     * 退货退款请求中台部分退款消息
     */
    private ResultStatus doPartRefundGoods(ChannelTypeEnum channelTypeEnum, int refundType, int sponsor, int afsApplyType, OrderNotifyRequest request) {
        CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
        OrderPartRefundGoodsRequest partParam = channelOrderConverterService.mtOrderPartRefundGoodsNotify(request);;
        Long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
        Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
        partParam.setTenantId(tenantId);
        partParam.setChannelType(channelTypeEnum.getCode());
        partParam.setOrderPartRefundType(refundType);
        partParam.setSponsor(sponsor);
        partParam.setRefundId(request.getRefundId());
        partParam.setAfsApplyType(afsApplyType);
        partParam.setServiceType(Integer.parseInt(request.getServiceType()));
        partParam.setRefundApplyTime(ConverterUtils.stringToMillis(request.getRefundApplyTime()));
        partParam.setIsAppeal(request.getIsAppeal() == IsAppealEnum.IS_APPEAL.getCode() ? true : false);
        if (ChannelTypeEnum.isJD2Home(partParam.getChannelType())) {
            partParam.setRefundAmount(Integer.parseInt(request.getRefundPrice()));
        }else {
            partParam.setRefundAmount(MoneyUtils.yuanToFen(request.getRefundPrice()));
        }
        installRefundSku(channelTypeEnum, partParam, request.getProductJson());
        ChannelOrderDetailDTO orderDetailDTO = getOrderDetailDTO(partParam.getTenantId(), channelTypeEnum.getCode(), request.getOrderId(), appId);
        Long shopId = Optional.ofNullable(orderDetailDTO).map(ChannelOrderDetailDTO::getStoreId).orElse(NumberUtils.LONG_ZERO);
        partParam.setOrderJsonString(Optional.ofNullable(orderDetailDTO).map(JSONObject::toJSONString).orElse(""));
        log.info("退货退款 request.pictureJson : {} ", request.getPictureJson());
        partParam.setRefundPicList(ListUtils.listTruncated4TotalLength(BeanUtil.fromJson(request.getPictureJson(),
                new org.codehaus.jackson.type.TypeReference<List<String>>() {}, Collections.emptyList()), MAX_REFUND_PIC_LENGTH));
        log.info("退货退款 partParam.refundPicList : {}", partParam.getRefundPicList());

        if (checkIsMtWeightRefund(partParam)) {
            OrderAfsApplyDTO orderAfsApplyDTO = getAfsDetail(partParam.getTenantId(), shopId, channelTypeEnum.getCode(),  request.getOrderId(), appId, request.getRefundId());
            BizAfterSaleCreateOrUpdateRequest bizAfterSaleCreateOrUpdateRequest = buildBizAfterSaleCreateOrUpdateRequest(partParam, orderAfsApplyDTO, channelTypeEnum);
            BizAfterSaleCreateOrUpdateResponse response = channelOrderThriftServiceProxy.orderAfterSaleNotify(bizAfterSaleCreateOrUpdateRequest);
            if (Objects.isNull(response)) {
                return ResultGenerator.genResult(ResultCode.FAIL, "推送克重退款消息失败").setData(ProjectConstant.NG);
            }
            if (response.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
        }
        //件数退款
        else {
            boolean onlyRefund = Objects.equals(request.getServiceType(), "2") && Objects.equals(request.getStatus(), "21") && request.getFinalReviewType() == 1;
            partParam.setOnlyRefund(onlyRefund);
            if (MccConfigUtil.orderOcmsSplitSwitch(tenantId, shopId, partParam.getChannelType())){
                return doRefundByEvent(RefundParamDto.builder()
                        .sponsor(sponsor)
                        .refundType(refundType)
                        .shopId(shopId)
                        .appId(appId)
                        .tenantId(tenantId)
                        .request(request)
                        .afsApplyType(afsApplyType)
                        .channelTypeEnum(channelTypeEnum)
                        .afsApplySourceType(AfterSaleSourceTypeEnum.CHANNEL.getValue())
                        .onlyRefund(onlyRefund)
                        .build());
            }

            if (MccConfigUtil.orderOcmsSplitCompare(tenantId, shopId, partParam.getChannelType())){
                 doRefundByEvent(RefundParamDto.builder()
                        .sponsor(sponsor)
                        .refundType(refundType)
                        .request(request)
                         .shopId(shopId)
                         .appId(appId)
                         .tenantId(tenantId)
                        .afsApplyType(afsApplyType)
                        .channelTypeEnum(channelTypeEnum)
                        .afsApplySourceType(AfterSaleSourceTypeEnum.CHANNEL.getValue())
                         .onlyRefund(onlyRefund)
                        .build());
            }


            QueryDeadlineTimeDTO queryDeadlineTimeDTO = QueryDeadlineTimeDTO.builder()
                    .refundId(partParam.getRefundId())
                    .refundApplyTime(partParam.getRefundApplyTime())
                    .channelOrderId(partParam.getChannelOrderId())
                    .channelType(partParam.getChannelType())
                    .tenantId(partParam.getTenantId())
                    .storeId(shopId)
                    .appPoiCode(request.getAppPoiCode())
                    .build();
            partParam.setProcessDeadline(queryProcessDeadline(queryDeadlineTimeDTO, appId, Strings.EMPTY));
            OrderResponse orderResponse = channelOrderThriftServiceProxy.orderPartRefundGoodsNotify(partParam);
            if (Objects.isNull(orderResponse)) {
                return ResultGenerator.genResult(ResultCode.FAIL, "推送部分退款消息失败").setData(ProjectConstant.NG);
            }
            if (orderResponse.getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            else if (isJddjMockOrder(tenantId, partParam.getChannelOrderId(), channelTypeEnum)) {
                log.info("京东测试订单那，忽略部分退款请求:{}", request);
                throw new MockTestException();
            }
        }
        return ResultGenerator.genResult(ResultCode.FAIL, "部分退款消息推送失败").setData(ProjectConstant.NG);
    }

    private ResultStatus elmSpuNotifyHandler(ChannelTypeEnum channelType, ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        log.info("ChannelOrderCallbackServiceImpl.spuCreateNotifyHandler, 饿了么回调商品消息 request:{}", request);
        if (StringUtils.isBlank(request.getBody())) {
            return ResultGenerator.genFailResult("body 为空");
        }

        Long tenantId = copAccessConfigService.selectTenantId(channelType.getCode(), request.getTenantAppId());
        if (Objects.isNull(tenantId)) {
            log.error("ChannelOrderCallbackServiceImpl.elmSpuNotifyHandler, 未获取到渠道租户ID, tenantAppId:{}", request.getTenantAppId());
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }

        Long qnhTenantId=MccConfigUtil.getQnhTenantId(request.getTenantAppId());
        if (qnhTenantId.equals(tenantId)) {
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }

        ElmSpuNotify elmSpuNotify = JSON.parseObject(request.getBody(), ElmSpuNotify.class);
        if (Objects.isNull(elmSpuNotify) || !elmSpuNotify.isValidNotify()) {
            return ResultGenerator.genFailResult("商品信息缺失");
        }

        if (notifyEnum == ChannelNotifyEnum.ELM_SKU_UPDATE_PUSH && !elmSpuNotify.isKeyAttributesChange()) {
            // 当更新字段不涉及
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        ChannelStoreDO channelStoreDO = copChannelStoreService.selectChannelStore(tenantId, channelType.getCode(), elmSpuNotify.getShop_id());// 校验租户门店
        long storeId = channelStoreDO.getStoreId();
        if (storeId == ProjectConstant.UNKNOW_STORE_ID) {
            log.info("ChannelCallBackServiceImpl.deleteSpuNotify, return [门店 校验不通过],tenantId:{} storeId:{}", tenantId, storeId);
            return ResultGenerator.genFailResult("未获取到门店信息");
        }

        ChannelSpuMessageCallBackTypeEnum callBackTypeEnum = null;
        if (notifyEnum == ChannelNotifyEnum.ELM_SKU_CREATE_PUSH) {
            callBackTypeEnum = ChannelSpuMessageCallBackTypeEnum.CREATE;
        } else if (notifyEnum == ChannelNotifyEnum.ELM_SKU_UPDATE_PUSH) {
            callBackTypeEnum = ChannelSpuMessageCallBackTypeEnum.UPDATE;
        } else if (notifyEnum == ChannelNotifyEnum.ELM_SKU_DELETE_PUSH) {
            callBackTypeEnum = ChannelSpuMessageCallBackTypeEnum.DELETE;
        } else {
            return ResultGenerator.genResult(ResultCode.CHANNEL_NOTIFY_UNKNOWN).setData(ProjectConstant.NG);
        }

        ChannelSpuMessageCallBackDTO spuMessageCallBackDTO = new ChannelSpuMessageCallBackDTO();
        spuMessageCallBackDTO.setCustomSpuId(elmSpuNotify.getCustom_sku_id());
        spuMessageCallBackDTO.setStoreId(storeId);
        spuMessageCallBackDTO.setAppId(channelStoreDO.getIntAppId());
        spuMessageCallBackDTO.setChannelSpuId(elmSpuNotify.getSku_id());
        ChannelSpuMessageCallBackRequest createSpuRequest = new ChannelSpuMessageCallBackRequest();
        createSpuRequest.setTenantId(tenantId);
        createSpuRequest.setChannelId(channelType.getCode());
        createSpuRequest.setChannelSpuMessageCallBackTypeEnum(callBackTypeEnum);
        createSpuRequest.setChannelSpuMessageCallBackDTOS(Arrays.asList(spuMessageCallBackDTO));
        sendCallbackMessageByMq(createSpuRequest);

        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }

    /**
     * MQ消息方式通知商品同步，租户-门店维度发送
     *
     * @param request
     */
    private void sendCallbackMessageByMq(ChannelSpuMessageCallBackRequest request) {
        log.info("ChannelOrderCallbackServiceImpl.sendCallbackMessageByMq, 向product-biz发送商品新增消息 request:{}", request);

        SpuChangeCallbackMessage message = new SpuChangeCallbackMessage();
        message.setTenantId(request.getTenantId());
        message.setChannelId(request.getChannelId());
        message.setSyncType(request.getChannelSpuMessageCallBackTypeEnum());

        request.getChannelSpuMessageCallBackDTOS()
                .stream()
                .filter(Objects::nonNull)
                .map(SpuChangeCallbackMessage.SpuUpdateInnerMessage::new)
                .collect(Collectors.groupingBy(SpuChangeCallbackMessage.SpuUpdateInnerMessage::getStoreId))
                .forEach((storeId, innerMessages) -> {
                    message.setInnerMessageList(innerMessages);

                    String partKey = String.format("%d_%d_%d", message.getTenantId(), storeId, message.getChannelId());
                    callbackProducerWrapper.sendMessage(message, partKey);
                });
    }

    private ResultStatus mtNotifyHandler(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        if (StringUtils.isNotBlank(request.getPickUpData())) {
            PickUpDataInfo pickUpDataInfo = JSON.parseObject(CommonUtils.decode(request.getPickUpData()), PickUpDataInfo.class);
            if (Objects.nonNull(pickUpDataInfo) && StringUtils.isNotBlank(pickUpDataInfo.getPick_time())) {
                request.setOrderId(pickUpDataInfo.getOrder_view_id());
                request.setTimestamp(pickUpDataInfo.getPick_time());
                request.setAppPoiCode(pickUpDataInfo.getApp_poi_code());
            }
        }
        convertMtNotifyParam(notifyEnum, request);
        if (Objects.equals(notifyEnum, ChannelNotifyEnum.MT_ORDER_REFUND_DELIVERY_STATUS_NOTIFY)
                && MccConfigUtil.filterTenantAppIds(request.getTenantAppId())){
            // 过滤消息
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        if (StringUtils.isBlank(request.getOrderId())) {
            log.info("order id is null ,request:{}",request);
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        if (MccConfigUtil.isMtDrunkHorseTenant(request.getTenantAppId())){
            handlerDrunkHorseCrossOrder(channelTypeEnum, request);
        }

        if(!isStoreValid(channelTypeEnum, request)){
            log.info("门店未配置或者无效，忽略该请求:{}", request);
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK).setMsg("未知门店");
        }

        // 闪购平台退货退款消息中无notify_type和res_type字段,service_type>0表示开通退货退款
        ServiceTypeEnum serviceType = ServiceTypeEnum.enumOf(request.getServiceType());

        //2.区分消息action（订单状态消息、配送状态消息、订单修改消息等）需要枚举
        switch (notifyEnum) {
            case MT_ORDER_PAID:
                return mtOrderPayMessage(channelTypeEnum, notifyEnum, request);
            case MT_ORDER_CONFIRM:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.BIZ_CONFIRMED), notifyEnum);
            case MT_ORDER_DELIVERY:
                return doDeliveryStatusChange(buildDeliveryStatusChangeRequest(channelTypeEnum, notifyEnum, request),
                        request.getDeliveryStatus(), false,request.getMtPkgId(), request.getPlatformCode());
            case MT_ORDER_SELF_DELIVERY:
                return doDeliveryStatusChange(buildMTSelfDeliveryStatusChangeRequest(channelTypeEnum, notifyEnum, request),
                        request.getDeliveryStatus(), false,request.getMtPkgId(),request.getPlatformCode());
            case MT_ORDER_CHANGE:
                return doOrderInfoChange(buildOrderInfoChangeRequest(OrderInfoChangeType.DELIVERY_CUSTOMER, channelTypeEnum, notifyEnum, request));
            case MT_ORDER_FINISH:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.FINISHED), notifyEnum);
            case MT_ORDER_CANCEL:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.CANCELED), notifyEnum);
            case MT_ORDER_REFUND:
                log.info("ChannelOrderCallbackServiceImpl.mtNotifyHandler MT_ORDER_REFUND request:{}, serviceType:{}", request, serviceType);
                switch (serviceType){
                    case REFUND:
                        return doRefund(channelTypeEnum, ChannelStatusConvertUtil.mtRefundGoodsTypeMapping(request.getStatus(), request.getIsAppeal(), request.getOpType()),
                                ChannelStatusConvertUtil.mtRefundGoodsSponsorMapping(request.getOpType()), request);
                    case REFUND_GOODS:
                        return doRefundGoods(channelTypeEnum, ChannelStatusConvertUtil.mtRefundGoodsTypeMapping(request.getStatus(), request.getIsAppeal(), request.getOpType()),
                                ChannelStatusConvertUtil.mtRefundGoodsSponsorMapping(request.getOpType()), request);
                    default:
                        request.setIsAppeal(getMTIsAppealByNotifyRequest(request) ? IsAppealEnum.IS_APPEAL.getCode() : IsAppealEnum.UN_APPEAL.getCode());
                        return doRefund(channelTypeEnum, ChannelStatusConvertUtil.mtRefundTypeMapping(request.getNotifyType(), request.getResType(), request.getIsAppeal()),
                                ChannelStatusConvertUtil.mtSponsorMapping(request.getNotifyType(), request.getResType(), request.getIsAppeal(), request.getTenantAppId()), request);
                }
            case MT_ORDER_PART_REFUND:
                log.info("ChannelOrderCallbackServiceImpl.mtNotifyHandler MT_ORDER_PART_REFUND request:{}, serviceType:{}", request, serviceType);
                switch (serviceType){
                    case REFUND:
                        return doPartRefund(channelTypeEnum, ChannelStatusConvertUtil.mtRefundGoodsTypeMapping(request.getStatus(), request.getIsAppeal(), request.getOpType()),
                                ChannelStatusConvertUtil.mtRefundGoodsSponsorMapping(request.getOpType()), request);
                    case REFUND_GOODS:
                        return doPartRefundGoods(channelTypeEnum, ChannelStatusConvertUtil.mtRefundGoodsTypeMapping(request.getStatus(), request.getIsAppeal(), request.getOpType()),
                                ChannelStatusConvertUtil.mtRefundGoodsSponsorMapping(request.getOpType()), request);
                    default:
                        request.setIsAppeal(getMTIsAppealByNotifyRequest(request) ? IsAppealEnum.IS_APPEAL.getCode() : IsAppealEnum.UN_APPEAL.getCode());
                        return doPartRefund(channelTypeEnum, ChannelStatusConvertUtil.mtRefundPartTypeMapping(request.getNotifyType(), request.getResType(), request.getIsAppeal()),
                                ChannelStatusConvertUtil.mtPartSponsorMapping(request.getNotifyType(), request.getResType(), request.getIsAppeal(), request.getTenantAppId()), request);
                }
            case MT_ORDER_PICK_UP:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.COMPLETED_PREPARE_MEAL), notifyEnum);
            case MT_ORDER_RIDER_TRANSFER_NOTIFY:
                return doRiderTransfer(channelTypeEnum, notifyEnum, request);
            case MT_ORDER_RIDER_TRANSFER_RIDER_NOTIFY:
                return doRiderTransferRider(channelTypeEnum, notifyEnum, request);
            case MT_ORDER_COMPENSATION_NOTIFY:
                return doUpdateOrderCompensation(channelTypeEnum, request);
            case MT_ORDER_TO_SELF_DELIVERY_NOTIFY:
                return doOrderInfoChange(buildOrderInfoChangeRequest(OrderInfoChangeType.TO_SELF_DELIVERY, channelTypeEnum, notifyEnum, request));
            case MT_REFUND_ADDRESS_CHANGE_FEE:
                return doRefundAddressChangeFee(channelTypeEnum, notifyEnum, request);
            case MT_SELF_DELIVERY_ORDER_STATUS:
                return doOrderSelfDelivery(channelTypeEnum, request);
            case MT_ORDER_REFUND_DELIVERY_STATUS_NOTIFY:
                return doReturnGoodsDeliveryStatus(channelTypeEnum, request);
            default:
                return ResultGenerator.genResult(ResultCode.CHANNEL_NOTIFY_UNKNOWN).setData(ProjectConstant.NG);
        }
    }

    private ResultStatus mtOrderPayMessage(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, OrderNotifyRequest request){
        //只有美团创建订单才需要解析订单详情
        Long tenantId = tenantRemoteService.getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId());
        Map<String, String> orderDetailForm = null;
        if(MccConfigUtil.mtOrderNotifyReturnDetial(tenantId) && MapUtils.isNotEmpty(request.getChannelOrderDetailForm())){
            Map<String, String> channelOrderDetailForm = request.getChannelOrderDetailForm();
            orderDetailForm = new HashMap<>();
            for (Map.Entry<String, String> entry : channelOrderDetailForm.entrySet()) {
                try {
                    String property = UrlUtil.urlDecodeSafe(entry.getKey());
                    String propertyValue = UrlUtil.urlDecodeSafe(entry.getValue());
                    orderDetailForm.put(property, propertyValue);
                } catch (Exception e) {
                    log.error("解析渠道消息订单详情错误,orderDetailMap:{}", channelOrderDetailForm, e);
                    orderDetailForm = null;
                    break;
                }
            }
            if(Objects.nonNull(orderDetailForm) && checkOrderDetailDTO(orderDetailForm)){
                orderDetailForm = null;
            }
        }
        ChannelOrderDetailDTO orderDetailDTO = null;
        if (orderDetailForm != null) {
            try {
                if (healthChannelOrderSensitiveHelper.yYneedDecodeWhole()) {
                    // 20240522 医药敏感字段 修改 orderDetailForm 几个加密字段
                    healthChannelOrderSensitiveHelper.pushInfoDecodeSensitiveInfo(
                            tenantId,
                            channelTypeEnum.getCode(), request.getTenantAppId(), orderDetailForm,
                            mtBrandChannelGateService);
                }
            } catch (Exception e) {
                log.error("医药敏感字段 修改 orderDetailForm 加密字段，不阻塞主流程。req:{}, form:{}", request, orderDetailForm, e);
            }
            try {
                orderDetailDTO = mtChannelOrderFormParser.parseOrderForm(tenantRemoteService.getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId()), channelTypeEnum.getCode(), orderDetailForm);
                log.info("解析订单详情:{}",JSON.toJSONString(orderDetailDTO));//打印测试数据
            }
            catch (Exception e) {
                log.error("解析订单详情失败，不阻塞主流程。req:{}, form:{}", request, orderDetailForm, e);
            }
        }

        OrderStatusChangeRequest orderStatusChangeRequest = buildOrderStatusChangeRequest(channelTypeEnum, request, orderDetailDTO, ChannelOrderStatus.NEW_ORDER);
        return doOrderStatusChange(orderStatusChangeRequest, notifyEnum);
    }

    private boolean checkOrderDetailDTO(Map<String, String> orderDetailForm) {
        //校验渠道支付消息是否为支付消息，很可能不是，校验了商品明细、序号、订单原价，支付时间
        if (MapUtils.isEmpty(orderDetailForm)) {
            return true;
        }
        if (!orderDetailForm.containsKey("day_seq")) {
            return true;
        }
        if (!orderDetailForm.containsKey("detail")) {
            return true;
        }
        if (!orderDetailForm.containsKey("order_send_time")) {
            return true;
        }
        if (!orderDetailForm.containsKey("original_price")) {
            return true;
        }
        return false;
    }

    private ResultStatus doReturnGoodsDeliveryStatus(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request) {
        CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
        Long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
        ChannelAfterSaleEvent afterSaleEvent = new ChannelAfterSaleEvent();
        afterSaleEvent.setEventId(ChannelAfterSaleEventEnum.REFUND_GOODS_STATUS.getEventId());
        afterSaleEvent.setTimestamp(System.currentTimeMillis());
        afterSaleEvent.setTenantId(tenantId);
        afterSaleEvent.setChannelType(channelTypeEnum.getCode());
        afterSaleEvent.setAfterSaleId(request.getRefundId());
        afterSaleEvent.setAppId(accessConfig.getAppId());
        afterSaleEvent.setChannelOrderId(request.getOrderId());
        Integer returnGoodsStatus = convertReturnGoodsStatus(request.getLogisticsStatus());
        afterSaleEvent.setReturnGoodsStatus(returnGoodsStatus);
        if (Objects.equals(returnGoodsStatus, ReturnGoodsStatusEnum.UNKNOWN.getCode())) {
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        boolean rs = newSupplychannelOrderRefundMessageProducer.sendMessageSync(afterSaleEvent);
        if(rs) {
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }else {
            return ResultGenerator.genFailResult("消息处理失败");
        }

    }

    private Integer convertReturnGoodsStatus(int logisticsStatus) {
        if (logisticsStatus == MccConfigUtil.getMtReturnLogisticStatusEndDeliveryCode()) {
            return ReturnGoodsStatusEnum.RIDER_DELIVERED.getCode();
        }
        if (logisticsStatus >= MccConfigUtil.getMtReturnLogisticStatusStartDeliveryCode() && logisticsStatus <= MccConfigUtil.getMtReturnLogisticStatusEndDeliveryCode()) {
            return ReturnGoodsStatusEnum.RIDER_DELIVERING.getCode();
        }
        return ReturnGoodsStatusEnum.WAITING_FOR_RIDER.getCode();
    }

    private ResultStatus doOrderSelfDelivery(ChannelTypeEnum channelTypeEnum,OrderNotifyRequest request) {
        try {
            if (MccConfigUtil.mtSelfDeliveryStatusSwitch()) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            if (MccConfigUtil.getMtSelfDeliveryFilterStatus().contains(Integer.parseInt(request.getDeliveryStatus()))) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            Optional<com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum> deliveryStatusOptional =
                    MtSelfDeliveryStatusEnum.mapToDeliveryStatus(Integer.parseInt(request.getDeliveryStatus()));
            if (!deliveryStatusOptional.isPresent()) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }

            Long tenantId, storeId;

            Optional<ChannelPoiBaseInfoDTO> channelPoiBaseInfoOptional = poiApiChannelPoiThriftServiceProxy
                    .queryRelationByChannelPoiCode(channelTypeEnum, request.getAppPoiCode());
            if (channelPoiBaseInfoOptional.isPresent()) {
                ChannelPoiBaseInfoDTO channelPoiBaseInfo = channelPoiBaseInfoOptional.get();
                tenantId = channelPoiBaseInfo.getTenantId();
                storeId = channelPoiBaseInfo.getPoiId();
            } else {
                Cat.logEvent("ORDER_SELF_DELIVERY", "QUERY_DB");
                log.info("门店对应不唯一，兜底查询");
                CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
                tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
                // 无效租户直接过滤
                if(tenantId <= NumberUtils.LONG_ZERO){
                    log.info("doOrderSelfDelivery tenantId is null");
                    return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                }
                storeId = copChannelStoreService.selectChannelStoreId(tenantId, channelTypeEnum.getCode(), request.getAppPoiCode());
            }

            if (!MccConfigUtil.getSelfDeliveryOrderConsumeSwitch(tenantId)) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            if (MccConfigUtil.getDHTenantIdList().contains(String.valueOf(tenantId))) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }

            GetLogisticsStatusResult orderLogisticsStatus = channelDeliveryService.getMtOrderLogisticsStatus(tenantId, request.getOrderId(),
                    Optional.ofNullable(storeId).orElse(NumberUtils.LONG_ZERO));
            int deliveryStatus = deliveryStatusOptional.get().getCode();

            SelfDeliveryOrderContent content = new SelfDeliveryOrderContent();
            content.setOrderId(request.getOrderId());
            content.setStatus(deliveryStatus);
            content.setChannelType(channelTypeEnum.getCode());
            content.setTenantId(tenantId);
            if (StringUtils.isNotBlank(request.getDealDeadline())) {
                content.setOperateTime(DateUtils.seconds2MS(Long.parseLong(request.getDealDeadline())));
            } else if (StringUtils.isNotBlank(request.getTimestamp())) {
                content.setOperateTime(DateUtils.seconds2MS(Long.parseLong(request.getTimestamp())));
            } else {
                content.setOperateTime(System.currentTimeMillis());
            }
            LogisticsStatusDTO dto = orderLogisticsStatus.getLogisticsStatus();
            if (Objects.nonNull(dto)) {
                content.setRiderName(dto.getRiderName());
                content.setRiderPhone(dto.getRiderPhone());
            }
            selfDeliveryOrderProducer.sendMessage(content, content.getOrderId());
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        } catch (Exception e) {
            log.error("doOrderSelfDelivery error: ", e);
            return ResultGenerator.genResult(ResultCode.FAIL).setData(ProjectConstant.NG);
        }
    }


    private ResultStatus doRefundAddressChangeFee(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        try {
            CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
            Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
            Long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
            Long storeId = copChannelStoreService.selectChannelStoreId(tenantId, channelTypeEnum.getCode(), request.getAppPoiCode());
            GetRefundAddressChangeFeeRequest changeFeeRequest = new GetRefundAddressChangeFeeRequest();
            changeFeeRequest.setChannelId(channelTypeEnum.getCode());
            changeFeeRequest.setOrderId(request.getOrderId());
            changeFeeRequest.setAppId(appId);
            changeFeeRequest.setTenantId(tenantId);
            setChangeFeeRequestStoreId(storeId, changeFeeRequest);
            // 需要隔离歪马租户
            if (MccConfigUtil.isMtDrunkHorseTenant(request.getTenantAppId())){
                log.info("ChannelOrderCallbackServiceImpl.doRefundAddressChangeFee isMtDrunkHorseTenant 歪马租户, viewOrderId: {}", request.getOrderId());
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            // 需要隔离医药租户
            try {
                List<Long> uwmsTenantIds = medicineTenantService.getAllUwmsTenantIds();
                if(uwmsTenantIds.contains(tenantId)){
                    log.info("ChannelOrderCallbackServiceImpl.doRefundAddressChangeFee 是医药租户, viewOrderId: {}", request.getOrderId());
                    return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                }
            }catch (Exception e){
                log.error("ChannelOrderCallbackServiceImpl.doRefundAddressChangeFee 查询医药租户出错 channelOrderId: {}, e= ", request.getOrderId(), e);
            }

            RefundAddressChangeFeeDTO result = routeServiceFactory.selectChannelOrderService(channelTypeEnum.getCode(), tenantId).getRefundAddressChangeFeeInfo(changeFeeRequest);

            if(Objects.isNull(result) || StringUtils.isBlank(result.getMoney())){
                log.info("ChannelOrderCallbackServiceImpl.doRefundAddressChangeFee 获取地址变更退费为空 或者 money or refund_time is null ");
                return ResultGenerator.genResult(ResultCode.FAIL, "处理地址变更退费失败").setData(ProjectConstant.NG);
            }

            BizRefundAddressChangeFeeRequest bizRequest = BizRefundAddressChangeFeeRequest.builder()
                    .channelId(channelTypeEnum.getCode())
                    .viewOrderId(request.getOrderId())
                    .tenantId(tenantId)
                    .money(result.getMoney())
                    .build();

            BizRefundAddressChangeFeeResponse response = bizOrderThriftService.doRefundAddressChangeFee(bizRequest);
            log.info("ChannelOrderCallbackServiceImpl.doRefundAddressChangeFee bizRequest: {}, response: {}", bizRequest, response);
            if(Objects.nonNull(response) && Objects.nonNull(response.getStatus()) && StatusCodeEnum.SUCCESS.getCode() == response.getStatus().getCode()){
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }else{
                return ResultGenerator.genResult(ResultCode.FAIL, "处理地址变更退费失败").setData(ProjectConstant.NG);
            }
        } catch (Exception e) {
            log.error("ChannelOrderCallbackServiceImpl.doRefundAddressChangeFee error", e);
            return ResultGenerator.genResult(ResultCode.FAIL, "处理地址变更退费失败").setData(ProjectConstant.NG);
        }
    }

    private void setChangeFeeRequestStoreId(Long storeId, GetRefundAddressChangeFeeRequest changeFeeRequest) {
        if(storeId <= NumberUtils.LONG_ZERO){
            return;
        }
        changeFeeRequest.setStoreIdList(ImmutableList.of(storeId));
    }

    /****
     * 处理歪马门店跨渠道下单
     * */
    private void handlerDrunkHorseCrossOrder(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request) {
        CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
        if (accessConfig == null && ChannelTypeEnum.MEITUAN.equals(channelTypeEnum)
                && MccConfigUtil.isMtDrunkHorseTenant(request.getTenantAppId())
                && MccConfigUtil.isMtChannelWxMallPoiSwitchOpen()){

            DrunkHorseWrongPoiMapping existedPoiMapping = drunkHorseCacheService.getCrossOrderMapping(request.getOrderId());
            if (existedPoiMapping != null){
                log.info("门店跨渠道下单，order:{}, 替换appPoiCode和appId, 原poiCode:{}，目标poiCode:{}, 原appId:{}, 目标appId:{}",
                        request.getOrderId(),
                        request.getAppPoiCode(),
                        existedPoiMapping.getTargetAppPoiCode(),
                        request.getTenantAppId(),
                        existedPoiMapping.getTargetTenantAppId());
                request.setAppPoiCode(StringUtils.defaultString(existedPoiMapping.getTargetAppPoiCode(), request.getAppPoiCode()));
                request.setTenantAppId(StringUtils.defaultString(existedPoiMapping.getTargetTenantAppId(), request.getTenantAppId()));
                return;
            }
            if (StringUtils.isBlank(request.getAppPoiCode())){
                //售后信息，没有传appPoiCode
                return;
            }
            // 可能是外卖渠道出现微商城门店导致, 通过微商城渠道查一次
            DrunkHorseCopAccessAndStore drunkHorseCopAccessAndStore = drunkHorseOrderService.queryCrossPoiAccessAndStoreConfig(request.getTenantAppId(), request.getAppPoiCode(), ChannelTypeEnum.MEITUAN.getCode(), ChannelTypeEnum.MT_DRUNK_HORSE.getCode());
            if (drunkHorseCopAccessAndStore != null){
                log.info("歪马门店跨渠道下单，order:{}", request.getOrderId());
                DrunkHorseWrongPoiMapping drunkHorseWrongPoiMapping = new DrunkHorseWrongPoiMapping(
                        request.getAppPoiCode(),
                        drunkHorseCopAccessAndStore.getCrossPoiAccessConfigDO().getAppId(),
                        ChannelTypeEnum.MEITUAN.getCode(),
                        ChannelTypeEnum.MT_DRUNK_HORSE.getCode(),
                        drunkHorseCopAccessAndStore.getChannelStoreDO().getStoreId(),
                        drunkHorseCopAccessAndStore.getChannelStoreDO().getChannelOnlinePoiCode(),
                        drunkHorseCopAccessAndStore.getCopAccessConfigDO().getTenantAppId());
                log.info("歪马门店跨渠道下单, 替换appPoiCode和appId, 原poiCode:{}，目标poiCode:{}, 原appId:{}, 目标appId:{}",
                        request.getAppPoiCode(),
                        drunkHorseCopAccessAndStore.getChannelStoreDO().getChannelOnlinePoiCode(),
                        request.getTenantAppId(),
                        drunkHorseCopAccessAndStore.getCopAccessConfigDO().getTenantAppId());
                request.setAppPoiCode(drunkHorseCopAccessAndStore.getChannelStoreDO().getChannelOnlinePoiCode());
                request.setTenantAppId(drunkHorseCopAccessAndStore.getCopAccessConfigDO().getTenantAppId());
                if (drunkHorseCacheService.setPoiCrossOrderMapping(request.getOrderId(), drunkHorseWrongPoiMapping)){
                    //发送告警
                    orderTrackService.alertDhOrderFromMtChannel(channelTypeEnum.getCode(), request.getTenantAppId(), request.getOrderId(), request.getAppPoiCode());
                }
            }
        }
    }

    private void convertMtNotifyParam(ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        try {
            if(ChannelNotifyEnum.MT_ORDER_RIDER_TRANSFER_NOTIFY == notifyEnum){
                OrderRiderTransferNotify transferNotify = JSON.parseObject(UrlUtil.urlDecodeSafeBean(request.getBody()),
                        OrderRiderTransferNotify.class);
                if(transferNotify != null){
                    request.setAppPoiCode(transferNotify.getApp_poi_code());
                    request.setDispatcherName(transferNotify.getRider_name());
                    request.setDispatcherMobile(transferNotify.getRider_phone());
                    request.setOrderId(transferNotify.getWm_order_id_view());
                }
            }
        }
        catch (Exception e) {
            log.warn("convert data 2 orderNotifyRequest error, data:{}", request.getBody());
        }

        convertMtNotifyRiderParam(notifyEnum, request);
    }

    /**
     * 转换骑手转单骑手的通知参数到request中
     *
     * @param notifyEnum
     * @param request
     */
    private void convertMtNotifyRiderParam(ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        try {
            if (MccConfigUtil.getMtConvertRiderTransferRiderParamSwitch() && ChannelNotifyEnum.MT_ORDER_RIDER_TRANSFER_RIDER_NOTIFY == notifyEnum) {
                OrderRiderTransferRiderNotifyDTO transferNotify = JSON.parseObject(UrlUtil.urlDecodeSafeBean(request.getBody()),
                    OrderRiderTransferRiderNotifyDTO.class);
                if (transferNotify != null) {
                    request.setOrderId(transferNotify.getWm_order_id_view());
                    // 将解码后的骑手转单骑手的通知参数转换为request对象中 后续逻辑会用到
                    request.setBody(JsonUtil.toJson(transferNotify));
                }
            }
        } catch (Exception e) {
            log.warn("riderTransferRiderNotify convert data 2 orderNotifyRequest error, data:{}", request.getBody());
        }
    }

    private boolean isStoreValid(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request) {
        try {
            // 是否开启校验，未开启则放行
            if (!MccConfigUtil.mtCheckStoreValidSwitch()) {
                return true;
            }

            // 是否包含在灰度配置中, 存在则放行
            if (MccConfigUtil.isGrayTenantAppId(request.getTenantAppId())) {
                log.info("不校验此应用推送的门店有效性, tenantAppId:{}", request.getTenantAppId());
                return true;
            }

            // 没有门店code，直接放行
            if (StringUtils.isEmpty(request.getAppPoiCode())) {
                return true;
            }
            CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
            // 校验租户门店
            if (accessConfig != null) {
                //通过新系统租户ID查老系统的商家ID
                if (MccConfigUtil.isMigrateDataCheck() && Objects.nonNull(accessConfig.getTenantId()) &&
                        accessConfig.getTenantId() > 1002705) {
                    OldQnhSearchRequest oldQnhSearchRequest = new OldQnhSearchRequest();
                    oldQnhSearchRequest.setTenantIdList(Lists.newArrayList(accessConfig.getTenantId()));
                    QnhTenantMigrateListResponse response = null;
                    try {
                        response = tenantThriftService.queryTenantByOldQnhCondition(oldQnhSearchRequest);
                        log.info("thrift查询租户映射关系返回：{}", JSON.toJSONString(response));
                        //没有数据直接放行，有数据则检查返回tenantList里商家的switchNew字段，为0时说明是脏数据
                        if(Objects.nonNull(response) && response.getStatus().getCode() == ResponseCodeEnum.SUCCESS.getValue() && CollectionUtils.isNotEmpty(response.getTenantList())){
                            Integer switchNew = response.getTenantList().get(0).getSwitchNew();
                            if (Objects.equals(switchNew,ProjectConstant.NO)){
                                log.error("美团渠道回调存在脏数据，TenantId:{}",accessConfig.getTenantId());
                                return false;
                            }
                        }
                    }catch (Exception e){
                        log.error("校验商家是否正式迁移完成失败，errorMsg:{}",e.getMessage());
                    }
                }

                return copChannelStoreService.selectChannelStoreId(accessConfig.getTenantId(), accessConfig.getChannelId(),
                        request.getAppPoiCode()) > 0;
            }
        } catch (Exception e) {
            log.warn("check store is  invalid, errorMsg:{}", e.getMessage());
        }
        return false;
    }

    /**
     * 更新赔付信息
     * @param channelTypeEnum
     * @param request
     * @return
     */
    private ResultStatus doUpdateOrderCompensation(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request){
        try {
            List<Integer> needSaveCompensateTypeList = Arrays.asList(OrderCompensateTypeEnum.NOT_ICE.getValue(), OrderCompensateTypeEnum.FAST_DELIVERY_OVER_TIME.getValue(), OrderCompensateTypeEnum.THAW.getValue());
            if (!needSaveCompensateTypeList.contains(request.getCompensateType())) {
                log.info("非指定赔付消息，无须处理");
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
            Long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
            Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
            Long storeId = copChannelStoreService.selectChannelStoreId(tenantId, channelTypeEnum.getCode(),
                    request.getAppPoiCode());
            Integer orderBizType = DynamicOrderBizType.channelId2OrderBizTypeValue(channelTypeEnum.getCode());
            PoiBatchCompensationRequest req =
                    new PoiBatchCompensationRequest()
                            .setTenantId(tenantId)
                            .setChannelId(channelTypeEnum.getCode())
                            .setOrderViewIds(Lists.newArrayList(request.getOrderId()))
                            .setChannelPoiId(request.getAppPoiCode())
                            .setStoreId(storeId)
                            .setCompensateType(OrderCompensateTypeEnum.ALL.getValue())
                            .setAppId(appId);
            log.info("ChannelOrderCallbackServiceImpl.doUpdateOrderCompensation, req:{}", req);
            QueryCompensationOrderListResult result = routeServiceFactory.selectChannelOrderService(channelTypeEnum.getCode(), tenantId).queryCompensateOrderList(req);
            if (Objects.isNull(result) || result.getStatus().getCode() != ResultCode.SUCCESS.getCode() || CollectionUtils.isEmpty(result.getOrderCompensationDTOList())) {
                log.info("ChannelOrderCallbackServiceImpl.doUpdateOrderCompensation 渠道无赔付信息，不处理");
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            List<CompensationInfoRequest> compensationInfoRequestList = result.getOrderCompensationDTOList().stream()
                    .filter(item -> needSaveCompensateTypeList.contains(item.getCompensateType()))
                    .map(item -> CompensationInfoRequest.builder()
                            .compensateType(item.getCompensateType())
                            .compensateTime(item.getCompensateTime())
                            .amount(item.getAmount())
                            .reason(item.getReason())
                            .responsibleParty(item.getResponsibleParty())
                            .build()).collect(Collectors.toList());
            BizOrderCompensationUpdateV2Request compensationUpdateV2Request = BizOrderCompensationUpdateV2Request.builder()
                    .tenantId(tenantId)
                    .shopId(storeId)
                    .orderBizType(orderBizType)
                    .viewOrderId(request.getOrderId())
                    .compensationInfoList(compensationInfoRequestList)
                    .build();
            BizOrderCompensationUpdateResponse orderResponse = channelOrderThriftServiceProxy.updateOrderCompensation(compensationUpdateV2Request);
            sendCompensationTrack(tenantId, orderBizType, request.getOrderId(), request.getResponsibleParty(), request.getCompensateType());
            if (Objects.nonNull(orderResponse) && orderResponse.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
        }catch (Exception e) {
            log.error("ChannelOrderCallbackServiceImpl.doUpdateOrderCompensation error", e);
        }
        return ResultGenerator.genResult(ResultCode.FAIL, "更新订单赔付信息失败").setData(ProjectConstant.NG);
    }

    private void sendCompensationTrack(Long tenantId, Integer orderBizType, String viewOrderId, int responsibleParty, int compensateType){
        if(Objects.isNull(tenantId) || Objects.isNull(orderBizType) || StringUtils.isBlank(viewOrderId)){
            return;
        }
        Integer trackOpType = null;
        HashMap<Object, Object> extMap = new HashMap<>();
        if(ResponsiblePartyEnum.MERCHANT.getValue() == responsibleParty && Objects.equals(compensateType, OrderCompensateTypeEnum.FAST_DELIVERY_OVER_TIME.getValue())){
            trackOpType = TrackOpType.ORDER_MERCHANT_COMPENSATION_PUSH.getOpType();
        }else if(ResponsiblePartyEnum.PLATFORM.getValue() == responsibleParty && Objects.equals(compensateType, OrderCompensateTypeEnum.FAST_DELIVERY_OVER_TIME.getValue())){
            trackOpType = TrackOpType.ORDER_DELIVERY_COMPENSATION_PUSH.getOpType();
        }else {
            trackOpType = TrackOpType.ORDER_COMPENSATION_PUSH.getOpType();
            extMap.put("responsibleParty", responsibleParty);
            extMap.put("compensateType", compensateType);
        }
        OrderTrackEvent orderTrackEvent = new OrderTrackEvent();
        orderTrackEvent.setTrackSource(TrackSource.CHANNEL.getType());
        orderTrackEvent.setTrackOpType(trackOpType);
        orderTrackEvent.setOperateTime(Instant.now().toEpochMilli());
        orderTrackEvent.setTenantId(tenantId);
        orderTrackEvent.setOrderBizType(orderBizType);
        orderTrackEvent.setUnifyOrderId(viewOrderId);
        if(MapUtils.isNotEmpty(extMap)){
            orderTrackEvent.setExt(JsonUtil.toJson(extMap));
        }
        orderTrackService.sendAsyncMessage(orderTrackEvent);
    }

    private ResultStatus doRiderTransfer(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        try {
            log.info("doRiderTransfer request:{}",request);
            CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
            Long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
            // 无效租户直接过滤
            if(tenantId <= NumberUtils.LONG_ZERO){
                log.info("doRiderTransfer tenantId is null request:{}",request);
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
            DeliveryStatusChangeRequest deliveryStatusChangeRequest = new DeliveryStatusChangeRequest();
            deliveryStatusChangeRequest.setChannelType(ChannelTypeEnum.MEITUAN.getCode());
            deliveryStatusChangeRequest.setChangeTime(ConverterUtils.stringToMillis(request.getTimestamp()));
            deliveryStatusChangeRequest.setRiderName(request.getDispatcherName());
            deliveryStatusChangeRequest.setRiderPhone(request.getDispatcherMobile());
            deliveryStatusChangeRequest.setChannelOrderId(request.getOrderId());
            deliveryStatusChangeRequest.setRemark(notifyEnum.getDesc());
            deliveryStatusChangeRequest.setTenantId(tenantId);
            deliveryStatusChangeRequest.setAppId(appId);
            GetLogisticsStatusRequest logisticsStatusRequest = new GetLogisticsStatusRequest();
            logisticsStatusRequest.setChannelId(ChannelTypeEnum.MEITUAN.getCode());
            logisticsStatusRequest.setOrderId(request.getOrderId());
            logisticsStatusRequest.setTenantId(tenantId);
            Long storeId = copChannelStoreService.selectChannelStoreId(tenantId, ChannelTypeEnum.MEITUAN.getCode(),
                    request.getAppPoiCode());
            GetLogisticsStatusResult logisticsStatus = channelDeliveryService.getMtOrderLogisticsStatus(tenantId, request.getOrderId(),
                    Optional.ofNullable(storeId).orElse(NumberUtils.LONG_ZERO));
            if(logisticsStatus == null || logisticsStatus.getLogisticsStatus() == null
                    || logisticsStatus.getLogisticsStatus().getStatus() <= NumberUtils.INTEGER_ZERO){
                log.info("doRiderTransfer logisticsStatus is error request:{},logisticsStatus:{}",request,logisticsStatus);
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            deliveryStatusChangeRequest.setDeliveryStatus(logisticsStatus.getLogisticsStatus().getStatus());
            return doDeliveryStatusChange(deliveryStatusChangeRequest,
                    String.valueOf(deliveryStatusChangeRequest.getDeliveryStatus()), true,request.getMtPkgId(), request.getPlatformCode());
        }
        catch (Exception e) {
            return ResultGenerator.genResult(ResultCode.FAIL, "骑手转派变更消息推送失败").setData(ProjectConstant.NG);
        }
    }

    private ResultStatus doRiderTransferRider(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        try {
            log.info("doRiderTransferRider request:{}",JsonUtil.toJson(request));

            // 1. 获取当前租户ID
            Long tenantId = getCurrTenantId(channelTypeEnum, request);
            // 无效租户直接过滤
            if (null == tenantId || tenantId <= NumberUtils.LONG_ZERO) {
                log.info("doRiderTransferRider.getCurrTenantId tenantId is null request:{}", JsonUtil.toJson(request));
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }

            // 根据请求参数获取运单
            MaltFarmAggRiderTransferDTO maltFarmAggRiderTransferDTO = healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(channelTypeEnum, request, tenantId, true);
            if (null == maltFarmAggRiderTransferDTO) {
                log.info("经过灰度相关逻辑未获取到需要回调麦芽田信息。request = {}", JsonUtil.toJson(request));
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }

            // 5. 调用 麦芽田/青云 发送消息
            boolean isValidCallback = postToMaltFarm(maltFarmAggRiderTransferDTO);
            if(isValidCallback){
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }else{
                return ResultGenerator.genResult(ResultCode.FAIL, "骑手转派骑手变更消息推送失败").setData(ProjectConstant.NG);
            }
        } catch (Exception e) {
            log.error("doRiderTransferRider error", e);
            return ResultGenerator.genResult(ResultCode.FAIL, "骑手转派骑手变更消息推送失败").setData(ProjectConstant.NG);
        }
    }

    /**
     * 获取当前的租户ID
     * @param channelTypeEnum
     * @param request
     * @return
     */
    private Long getCurrTenantId(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request) {
        CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
        return getTenantIdParam(accessConfig, request.getTenantAppId());
    }

    /**
     * 给麦芽田发送骑手转单消息
     *
     * @param maltFarmAggRiderTransferDTO
     * @return
     */
    private boolean postToMaltFarm(MaltFarmAggRiderTransferDTO maltFarmAggRiderTransferDTO) {
        String orderId = maltFarmAggRiderTransferDTO.getOrderId();
        try {
            if (MccConfigUtil.isMaltFallbackSwitch()) {
                log.info("FarmPaoTuiDeliveryRiderTransfer fallback, orderId:{}", orderId);
                Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, orderId + "");
                // 麦芽田降级、不进行重试、直接更新订单上配送状态
                return false;
            }
            String postJson = JSON.toJSONString(maltFarmAggRiderTransferDTO);
            log.info("FarmPaoTuiDeliveryRiderTransfer.postJson | callbackUrl = {} | paramJsons = {}", callbackUrl, postJson);
            String responseStr = HttpClientUtil.postJson(callbackUrl,
                MccConfigUtil.getMaltHttpConnectionTimeOut(),
                MccConfigUtil.getMaltHttpSocketTimeOut(),
                postJson);
            if (StringUtils.isEmpty(responseStr)) {
                log.info("FarmPaoTuiDeliveryRiderTransfer.postJson error | callbackUrl = {} | paramJsons = {}", callbackUrl, postJson);
                Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, "麦芽田返回为空，orderId：" + orderId);
                throw new BizException("调用麦芽田骑手转骑手回调失败");
            }
            log.info("FarmPaoTuiDeliveryRiderTransfer.postJson | callbackUrl = {} | response = {}", callbackUrl, responseStr);
            String data = JSON.parseObject(responseStr).getString("data");
            if (!Objects.equals(ProjectConstant.OK, data)) {
                Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, "麦芽田返回结果不成功，orderId：" + orderId);
                log.info("FarmPaoTuiDeliveryRiderTransfer.postJson error | callbackUrl = {} | paramJsons = {}", callbackUrl, postJson);
                throw new BizException("调用麦芽田骑手转骑手回调失败");
            }
            Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, "Successful");
            return true;
        } catch (BizException e) {
            Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, "exception_" + e.getMessage());
            //如果post失败，也应该重试
            log.info("postToMaltFarm riderTransferRiderNotify bizerror", e);
            throw new BizException("调用麦芽田骑手转骑手回调失败");
        } catch (Exception e) {
            Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, "exception_" + e.getMessage());
            //如果post失败，也应该重试
            log.error("postToMaltFarm riderTransferRiderNotify error", e);
            throw new BizException("调用麦芽田骑手转骑手回调失败");
        }
    }

    /**
     * 构建麦芽田回到接口参数
     * @param paoTuiRiderTransferDTO
     * @param deliveryOrder
     * @return
     */
    private MaltFarmAggRiderTransferDTO buildMaltFarmAggRiderTransferDTO(PaoTuiRiderTransferDTO paoTuiRiderTransferDTO, TDeliveryOrder deliveryOrder) {
        String orderId = String.valueOf(deliveryOrder.getOrderId());
        if(StringUtils.isNotEmpty(deliveryOrder.getDeliveryOrderId())){
            orderId = deliveryOrder.getDeliveryOrderId();
        }
        MaltFarmAggRiderTransferDTO maltFarmAggRiderTransferDTO = MaltFarmAggRiderTransferDTO.builder()
            // 我们的运单id就是给他们的orderId
            .orderId(orderId)
            // 同麦芽田商量后决定传递固定值20：已抢单
            .status(20)
            .dispatcherName(paoTuiRiderTransferDTO.getRiderName())
            .dispatcherMobile(paoTuiRiderTransferDTO.getRiderPhone())
            .timestamp(paoTuiRiderTransferDTO.getTimestamp().intValue())
            .build();
        maltFarmAggRiderTransferDTO.setSign(
            FarmPaoTuiSignUtils.generateSignatureFromRequest(maltFarmAggRiderTransferDTO));
        return maltFarmAggRiderTransferDTO;
    }

    private ResultStatus elmNotifyHandler(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        convertElmNotifyParam(channelTypeEnum, request, notifyEnum);
        long tenantId = 0L;
        // 饿了么ISV查询需要通过渠道门店ID
        if (ChannelTypeEnum.ELEM.equals(channelTypeEnum) && MccConfigUtil.checkElmStoreId(request.getTenantAppId()) && MccConfigUtil.checkElmIsvAppKey(request.getTenantAppId())) {
            Optional<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTO = poiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO(request.getAppPoiCode(), false);
            tenantId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getTenantId).orElse(NumberUtils.LONG_ZERO);
        } else {
            tenantId = tenantRemoteService.getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId());
        }
        switch (notifyEnum) {
            case ELM_ORDER_CREATE:
                if (StringUtils.isBlank(request.getOrderId())) {
                    return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                }
                if (MccConfigUtil.getMockResponseTenant().getTenantIdList().contains(tenantId)) {
                    return ResultGenerator.genSuccessResult().setData(request.getOrderId());
                }
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.NEW_ORDER), notifyEnum);
            case ELM_ORDER_STATUS:
                if (StringUtils.isBlank(request.getOrderId())) {
                    return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                }
                ChannelOrderStatus channelOrderStatus = ChannelStatusConvertUtil.elmOrderStatusMapping(request.getStatus());
                if (Objects.isNull(channelOrderStatus)) {
                    return ResultGenerator.genResult(ResultCode.CHANNEL_ORDER_STATUS_INVALID).setData(ProjectConstant.NG);
                }
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, channelOrderStatus), notifyEnum);
            case ELM_ORDER_MODIFY:
                if (StringUtils.isBlank(request.getOrderId()) || StringUtils.isEmpty(request.getBody())) {
                    return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                }
                OrderChangeInfo orderChangeInfo = JsonUtil.fromJson(request.getBody(), OrderChangeInfo.class);
                // type为空或者修改祝福语不处理
                if(StringUtils.isEmpty(orderChangeInfo.getOrderModifyType()) || orderChangeInfo.checkGreetingChange()) {
                    return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                }
                if(orderChangeInfo.checkInvoiceInfoChange()) {
                    // 发票修改走ocms_service
                    return doOrderInfoChange(buildOrderInfoChangeRequest(OrderInfoChangeType.INVOICE, channelTypeEnum, notifyEnum, request));
                } else {
                    // 饿了么手机号修改后查询详情可能会查到更改前的虚拟号，所以需要存入redis，后续保存时使用推送的手机虚拟号
                    if(orderChangeInfo.checkReceiverPhoneChange()) {
                        newSupplyOrderCacheService.setChangeOrderChangeValue(request.getOrderId(), channelTypeEnum.getCode(), orderChangeInfo.getReceiverPhone(), getChangeOrderRedisLockSeconds() * 3);
                    }
                    // 订单修改走mq发送给order_biz
                    return sendOrderInfoChangeMsg(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.NEW_ORDER), notifyEnum);
                }
            case ELM_ORDER_DELIVERY_STATUS:
                if (StringUtils.isBlank(request.getOrderId())) {
                    return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                }
                return doDeliveryStatusChange(buildDeliveryStatusChangeRequest(channelTypeEnum, notifyEnum, request),
                        request.getDeliveryStatus(), false, request.getMtPkgId(), request.getPlatformCode());
            case ELM_ORDER_REVERSE_PUSH:
                return elmRefundService.doRefund(tenantId, request.getTenantAppId(), request.getBody(), request.getAppPoiCode());
            case ELM_SHOP_BIND_MSG:
                return elmShopBind(channelTypeEnum, request);
            case ELM_SHOP_UNBIND_MSG:
                return elmShopUnbind(channelTypeEnum, request, tenantId);
            case ELM_SHOP_MSG_PUSH:
                return elmShopSync(channelTypeEnum, request);
            case ELM_SKU_CREATE_PUSH:
            case ELM_SKU_UPDATE_PUSH:
            case ELM_SKU_DELETE_PUSH:
                return elmSpuNotifyHandler(channelTypeEnum, notifyEnum, request);
            case ELM_IM_MESSAGE_PUSH:
                return elmIMessagePush(channelTypeEnum, request);
            case ELM_IM_ACK_MESSAGE_PUSH:
                return elmIMessageAckPush(channelTypeEnum, request);
            default:
                return ResultGenerator.genResult(ResultCode.CHANNEL_NOTIFY_UNKNOWN).setData(ProjectConstant.NG);
        }
    }

    private ResultStatus elmIMessageAckPush(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request) {
        String bodyStr = request.getBody();
        JSONObject bodyJson = JSONObject.parseObject(bodyStr);
        String payloadStr = bodyJson.getString("payload");
        JSONObject payloadJson = JSONObject.parseObject(payloadStr);

        IMMessageContent imMessageContent = new IMMessageContent();
        imMessageContent.setCmd(request.getAction());
//        imMessageContent.setVersion();
        imMessageContent.setSource(request.getTenantAppId());
        imMessageContent.setTimestamp(Long.valueOf(request.getTimestamp()));

        IMMessageBody body = new IMMessageBody();
        body.setBizType(bodyJson.getString("bizType"));
        body.setSubBizType(bodyJson.getString("subBizType"));
        body.setPlatformShopId(bodyJson.getString("platformShopId"));

        ReplyMessagePayload payload = new ReplyMessagePayload();
        payload.setMsgId(payloadJson.getString("msgId"));
        payload.setGroupId(payloadJson.getString("cid"));
        payload.setSenderId(payloadJson.getString("uid"));

        imMessageContent.setBody(body);
        body.setPayload(payload);
        eblsImService.ReadMessagePush2MQ(imMessageContent);
        return ResultGenerator.genSuccessResult().setData(request.getOrderId());
    }

    private ResultStatus elmIMessagePush(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request) {
        log.info("【饿了么IM消息】接收："+JSONObject.toJSONString(request));
        String bodyStr = request.getBody();
        JSONObject bodyJson = JSONObject.parseObject(bodyStr);
        String payloadStr = bodyJson.getString("payload");
        JSONObject payloadJson = JSONObject.parseObject(payloadStr);

        IMMessageContent imMessageContent = new IMMessageContent();
        imMessageContent.setCmd(request.getAction());
//        imMessageContent.setVersion();
        imMessageContent.setSource(request.getTenantAppId());
        imMessageContent.setTimestamp(Long.valueOf(request.getTimestamp()));

        IMMessageBody body = new IMMessageBody();
        body.setBizType(bodyJson.getString("bizType"));
        body.setSubBizType(bodyJson.getString("subBizType"));
        body.setPlatformShopId(bodyJson.getString("platformShopId"));

        ReplyMessagePayload payload = new ReplyMessagePayload();
        payload.setSenderId(payloadJson.getString("senderId"));
        payload.setCreateTime(payloadJson.getLong("createTime"));
        payload.setGroupId(payloadJson.getString("groupId"));
        payload.setMsgId(payloadJson.getString("msgId"));
        payload.setContent(payloadJson.getString("content"));
        payload.setContentType(payloadJson.getString("contentType"));
        payload.setOrderId(payloadJson.getString("orderId"));
        payload.setReceiverIds(payloadJson.getJSONArray("receiverIds").toJavaList(String.class));

        imMessageContent.setBody(body);
        body.setPayload(payload);

        eblsImService.IMMessagePush2MQ(imMessageContent);
        return ResultGenerator.genSuccessResult().setData(request.getOrderId());
    }

    private ResultStatus elmShopSync(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request) {
        String body = request.getBody();
        JSONObject jsonObject = JSONObject.parseObject(body);
        String shop_id = jsonObject.getString("shop_id");
        Long tenantId;
        Long appId;
        if (!MccConfigUtil.checkElmIsvAppKey(request.getTenantAppId())){
            //品牌应用
            CopAccessConfigDO copAccessConfigDO = copAccessConfigService.selectByTenantAppIdAndChannelId(request.getTenantAppId(), channelTypeEnum.getCode());
            if(copAccessConfigDO == null) {
                return ResultGenerator.genSuccessResult().setData(request.getOrderId());
            }
            tenantId = copAccessConfigDO.getTenantId();
            appId = copAccessConfigDO.getAppId();
        }else {
            //isv 应用
            List<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTOS = poiApiChannelPoiThriftServiceProxy.queryRelByAppKeyAndChannelPoiCodes(
                    request.getTenantAppId(),
                    Lists.newArrayList(shop_id),
                    channelTypeEnum.getCode()
            );
            if (CollectionUtils.isEmpty(channelPoiBaseInfoDTOS)) {
                //没有该门店,无需处理
                return ResultGenerator.genSuccessResult().setData(request.getOrderId());
            }
            ChannelPoiBaseInfoDTO channelPoiBaseInfoDTO = channelPoiBaseInfoDTOS.get(0);
            tenantId = channelPoiBaseInfoDTO.getTenantId();
            appId = channelPoiBaseInfoDTO.getQnhAppId();
        }
//        channelPoiThriftServiceProxy.incrementAndSynTenantChannelStoreInfo(tenantId, channelTypeEnum.getCode(),
//                shop_id,appId);
        if (MccConfigUtil.getAsyncTenant().contains(-1L) || MccConfigUtil.getAsyncTenant().contains(tenantId)) {
            ChannelPoiCallbackMessage message = new ChannelPoiCallbackMessage();
            message.setTenantId(tenantId);
            message.setChannelPoiCode(shop_id);
            message.setChannelId(channelTypeEnum.getCode());
            message.setAppId(appId);
            message.setType(ChannelPoiCallbackTypeEnum.POI_SYNC.getType());
            message.setMessage(null);
            boolean send = channelPoiCallbackMessageProducer.sendMessageSync(message);
            if (!send) {
                log.error("elmShopUnbind channelPoiCallbackMessageProducer.sendMessageSync fail.msg:{}", message);
            }
        }else {
            channelPoiThriftServiceProxy.incrementAndSynTenantChannelStoreInfo(tenantId, channelTypeEnum.getCode(),
                shop_id,appId);
        }

        return ResultGenerator.genSuccessResult().setData(request.getOrderId());
    }

    /**
     * fixme 回调统一解析成OrderNotifyRequest有点过度复用了，不同回调格式并不一样：如门店解绑消息和订单消息。当前按照订单回调结构在写，导致门店回调数据缺失
     * @param channelTypeEnum
     * @param request
     * @param tenantId
     * @return
     */
    private ResultStatus elmShopUnbind(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request, long t) {
        long tenantId = t;
        String body = request.getBody();
        JSONObject jsonObject = JSONObject.parseObject(body);
        JSONArray shop_list = jsonObject.getJSONArray("shop_list");
        // 很坑的逻辑，不同回调的返回体格式是不一样。外面传来的tenantId解析是按订单回调统一格式解析的，门店解绑格式不一样，所以外部取不到tenantId，这里先解决elm
        if (!shop_list.isEmpty()
                && ChannelTypeEnum.ELEM.equals(channelTypeEnum)
                && MccConfigUtil.checkElmStoreId(request.getTenantAppId())
                && MccConfigUtil.checkElmIsvAppKey(request.getTenantAppId())
                && MccConfigUtil.checkUnbindElmIsvAppKey(request.getTenantAppId())) {
            JSONObject storeJson = (JSONObject) shop_list.get(0);
            String shop_id = storeJson.getString("shop_id");
            Optional<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTO = poiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO(shop_id, false);
            tenantId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getTenantId).orElse(NumberUtils.LONG_ZERO);
        }
        for (Object o : shop_list) {
            JSONObject storeJson = (JSONObject) o;
            String shop_id = storeJson.getString("shop_id");
//            channelPoiThriftServiceProxy.unBindChannelPoi(tenantId, channelTypeEnum.getCode(),
//                    shop_id);

            if (MccConfigUtil.getAsyncTenant().contains(-1L) || MccConfigUtil.getAsyncTenant().contains(tenantId)) {
                // token需要在ocms channel 服务删除
                channelPoiThriftServiceProxy.deleteToke(tenantId, channelTypeEnum.getCode(), shop_id);
                ChannelPoiCallbackMessage message = new ChannelPoiCallbackMessage();
                message.setTenantId(tenantId);
                message.setChannelPoiCode(shop_id);
                message.setChannelId(channelTypeEnum.getCode());
                message.setType(ChannelPoiCallbackTypeEnum.POI_UNBIND.getType());
                message.setMessage(null);
                boolean send = channelPoiCallbackMessageProducer.sendMessageSync(message);
                if (!send) {
                    log.error("elmShopUnbind channelPoiCallbackMessageProducer.sendMessageSync fail.msg:{}", message);
                    // 原来也是无视了处理结果，统一返回成功的，为了避免引入异常，保持一致。而且丢mafka异常应该比服务调用异常率低
                    //            return ResultGenerator.genFailResult("");
                }
                dxPushMessageProducer.pushPoiBindMessage(tenantId,null,ChannelType.ELEM.getValue(),shop_id,"门店解绑应用");
            } else {
                channelPoiThriftServiceProxy.unBindChannelPoi(tenantId, channelTypeEnum.getCode(),
                        shop_id);
            }
        }
        return ResultGenerator.genSuccessResult().setData(request.getOrderId());
    }

    private ResultStatus elmShopBind(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request) {
        if (MccConfigUtil.checkElmIsvAppKey(request.getTenantAppId())) {
            log.info("ChannelOrderCallbackServiceImpl.elmShopBind:识别到elm isv 绑定门店回调，暂不处理！request:{}",request);
            return ResultGenerator.genSuccessResult().setData(request.getOrderId());
        }
        CopAccessConfigDO copAccessConfigDO = copAccessConfigService.selectByTenantAppIdAndChannelId(request.getTenantAppId(), channelTypeEnum.getCode());
        if(copAccessConfigDO == null) {
            return ResultGenerator.genSuccessResult().setData(request.getOrderId());
        }
        long tenantId = copAccessConfigDO.getTenantId();
        Long appId = copAccessConfigDO.getAppId();
        String body = request.getBody();
        JSONObject jsonObject = JSONObject.parseObject(body);
        JSONArray shop_list = jsonObject.getJSONArray("shop_list");
        for (Object o : shop_list) {
            JSONObject storeJson = (JSONObject) o;
            String shop_id = storeJson.getString("shop_id");
//            channelPoiThriftServiceProxy.incrementAndSynTenantChannelStoreInfo(tenantId, channelTypeEnum.getCode(),
//                    shop_id, appId);
            if (MccConfigUtil.getAsyncTenant().contains(-1L) || MccConfigUtil.getAsyncTenant().contains(tenantId)) {
                ChannelPoiCallbackMessage message = new ChannelPoiCallbackMessage();
                message.setTenantId(tenantId);
                message.setChannelPoiCode(shop_id);
                message.setChannelId(channelTypeEnum.getCode());
                message.setType(ChannelPoiCallbackTypeEnum.POI_BIND.getType());
                message.setAppId(appId);
                message.setMessage(null);
                boolean send = channelPoiCallbackMessageProducer.sendMessageSync(message);
                if (!send) {
                    log.error("elmShopBind channelPoiCallbackMessageProducer.sendMessageSync fail.msg:{}", message);
                    // 原来也是无视了处理结果，统一返回成功的，为了避免引入异常，保持一致。而且丢mafka异常应该比服务调用异常率低
//            return ResultGenerator.genFailResult("");
                }
//                dxPushMessageProducer.pushPoiBindMessage(tenantId,appId.toString(),ChannelType.ELEM.getValue(),shop_id,"门店绑定应用");
            }else {
                channelPoiThriftServiceProxy.incrementAndSynTenantChannelStoreInfo(tenantId, channelTypeEnum.getCode(),
                    shop_id, appId);
            }

        }
        return ResultGenerator.genSuccessResult().setData(request.getOrderId());
    }

    private ResultStatus jddjNotifyHandler(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        //解析并设置京东消息信息
        convertJddjNotifyParam(request, notifyEnum);
        if(MccConfigUtil.isIgnoreChannelMessage(notifyEnum, request.getOrderId())){
            log.info("命中忽略消息名单，忽略处理, orderId:{}", request.getOrderId());
            return ResultGenerator.genResult(ResultCode.SUCCESS).setData(ProjectConstant.OK);
        }
        switch (notifyEnum) {
            case JDDJ_NEW_ORDER:
                return handleJd2homeOrderStatus(channelTypeEnum, notifyEnum, request);
            case JDDJ_ORDER_ADJUST:
                return handleJddjOrderAdjust(request);
            case JDDJ_ORDER_WAIT_OUT:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.BIZ_CONFIRMED), notifyEnum);
            case JDDJ_PUSH_DELIVERY:
                return doDeliveryStatusChange(buildDeliveryStatusChangeRequest(channelTypeEnum, notifyEnum, request),
                        request.getDeliveryStatus(), false, request.getMtPkgId(), request.getPlatformCode());
            case JDDJ_ORDER_INFO_CHANGE:
                return doOrderInfoChange(buildOrderInfoChangeRequest(OrderInfoChangeType.DELIVERY_CUSTOMER, channelTypeEnum, notifyEnum, request));
            case JDDJ_ORDER_ADD_TIPS:
                return doOrderInfoChange(buildOrderInfoChangeRequest(OrderInfoChangeType.FREIGHT, channelTypeEnum, notifyEnum, request));
            case JDDJ_FINISH_ORDER:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.FINISHED), notifyEnum);
            case JDDJ_ORDER_FINISH:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.FINISHED), notifyEnum);
            case JDDJ_PICK_FINISH_ORDER:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.COMPLETED_PREPARE_MEAL), notifyEnum);
            case JDDJ_ACCOUNTING:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.SETTLEMENT_AMOUINT_READY), notifyEnum);
            case JDDJ_CANCEL_ORDER:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.CANCELED), notifyEnum);
            case JDDJ_LOCK_ORDER:
                if(JdLockOrderEnum.enumOf(request.getRefundReason()) == JdLockOrderEnum.REJECT){
                    setRejectByCustomerRequest(request);
                    return doRefundGoods(channelTypeEnum, OrderRefundGoodsType.REJECT_BY_CUSTOMER.getValue(), OrderRefundSponsor.TENANT.getValue(), AfsApplyType.AFTER_SALE.getValue(), request);
                }
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.LOCKED), notifyEnum);
            case JDDJ_START_DELIVERY:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.FULFILLMENT), notifyEnum);
            case JDDJ_UNLOCK_ORDER:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.UNLOCKED), notifyEnum);
            case JDDJ_USER_CANCEL_ORDER_BEFORE_DELIVERY:
                //售中用户退款
                return handleJdOnSaleRefundApply(channelTypeEnum, ChannelStatusConvertUtil.jddjOnSaleRefundTypeMapping(request.getStatus()), OrderRefundSponsor.CUSTOMER.getValue(), AfsApplyType.ON_SALE.getValue(), request, ChannelNotifyEnum.JDDJ_USER_CANCEL_ORDER_BEFORE_DELIVERY);
             case JDDJ_USER_CANCEL_ORDER_BEFORE_DELIVERY_REVOCATION:
                //售中用户撤销退款
                return handleJdOnSaleRefundApply(channelTypeEnum, OrderAllRefundType.APPLY_CANCEL_BY_CUSTOMER.getValue(), OrderRefundSponsor.CUSTOMER.getValue(), AfsApplyType.ON_SALE.getValue(), request, ChannelNotifyEnum.JDDJ_USER_CANCEL_ORDER_BEFORE_DELIVERY_REVOCATION);
            case JDDJ_VENDER_AUDIT_APPLY_CANCEL_ORDER:
                //商家审核用户取消申请消息, 同意或驳回
                return handleJdOnSaleRefundApply(channelTypeEnum, ChannelStatusConvertUtil.jddjOnSaleRefundTypeMapping(request.getStatus()), OrderRefundSponsor.CUSTOMER.getValue(), AfsApplyType.ON_SALE.getValue(), request, ChannelNotifyEnum.JDDJ_VENDER_AUDIT_APPLY_CANCEL_ORDER);

            //todo  是否需要删除
            case JDDJ_NEW_AFTERSALE:
                return doJdAfterSale(channelTypeEnum, notifyEnum, request);
            case JDDJ_AFTERSALE_STATUS:
                //售后订单状态变化 对于京东的新建售后消息(newApplyAfterSaleBill) 会同时发送售后单状态消息和新建售后单消息，只接收一个
                return doJdAfterSale(channelTypeEnum, notifyEnum, request);
            case JDDJ_UPDATE_AFTERSALE:
                //用户更新售后订单
                return doJdAfterSale(channelTypeEnum, notifyEnum, request);
            case JDDJ_ORDER_FINANCE_READY:
                // 京东商品活动分摊金额
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.ORDER_ACTIVITY_DETAIL_READY), notifyEnum);
            case JDDJ_STORE_CRUD:
                return jdPoiCallback(channelTypeEnum, request);
            case JDDJ_ORDER_INVOICE:
                return doOrderInfoChange(buildOrderInfoChangeRequest(OrderInfoChangeType.INVOICE, channelTypeEnum, notifyEnum, request));
            case JDDJ_DELIVERY_CARRIER_MODIFY:
                return doSelfDelivery(request,OrderBizTypeEnum.JING_DONG);
            case JDDJ_AFTER_SALE_JD_BILL_FINISH:
                //售后单最终完成消息（物竞天择）
                return doAfterSaleJdBillFinish(request,OrderBizTypeEnum.JING_DONG);
            case JDDJ_COMMENT_PUSH:
            case JDDJ_COMMENT_AUDIT:
                return jdCommentPush(request);

            default:
                return ResultGenerator.genResult(ResultCode.CHANNEL_NOTIFY_UNKNOWN).setData(ProjectConstant.NG);
        }
    }

    private ResultStatus jdCommentPush(OrderNotifyRequest request) {
        try {
            CopAccessConfigDO accessConfig = getAccessConfig(ChannelTypeEnum.JD2HOME.getCode(), request.getTenantAppId());
            long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
            SyncJddjCommentRequest req = new SyncJddjCommentRequest();
            req.setChannelOrderId(request.getOrderId());
            req.setTenantId(tenantId);
            CommentSyncResponse commentSyncResponse = channelCommentThriftService.syncJddjChannelComment(req);
            if (Objects.equals(commentSyncResponse.getStatus().getCode(), SUCCESS)){
               return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }else {
                log.error("同步京东渠道评价失败,{}, reponse,{}", request, commentSyncResponse);
                return ResultGenerator.genFailResult("同步京东渠道评价失败");
            }
        }catch (Exception e){
            log.error("jdCommentPush error request:{}", request, e);
            return ResultGenerator.genFailResult("同步渠道评价失败");
        }
    }

    private ResultStatus doAfterSaleJdBillFinish(OrderNotifyRequest request, OrderBizTypeEnum orderBizTypeEnum) {
        try {
            log.info("doAfterSaleJdBillFinish request : {} , orderBizTypeEnum: {}", request, orderBizTypeEnum);
            // 1 需要根据通知的消息去反查接口
        }catch (Exception e){
            log.error("doAfterSaleJdBillFinish error request:{},orderBizTypeEnum:{}", request, orderBizTypeEnum);
            ResultGenerator.genFailResult("转自配送失败");
        }
        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }

    // 京东用户取消订单申请
    private ResultStatus handleJdOnSaleRefundApply(ChannelTypeEnum channelTypeEnum, int refundType, int sponsor, int afsApplyType, OrderNotifyRequest request, ChannelNotifyEnum channelNotify) {
        // 京东取消订单只会发生一次，售后单号设置每单为唯一
        request.setRefundId("JDA" + request.getOrderId());

        long tenantId = tenantRemoteService.getTenantIdParam(ChannelTypeEnum.JD2HOME.getCode(), request.getTenantAppId());
        // 重复消息处理
        BizOrderQueryRequest queryRequest = BizOrderQueryRequest.builder()
                .orderBizType(OrderBizTypeEnum.JING_DONG.getValue())
                .tenantId(tenantId)
                .viewOrderId(request.getOrderId())
                .fromMaster(true)
                .build();

        Optional<BizOrderModel> bizOrderModel = channelOrderThriftServiceProxy.queryOrderInfo(queryRequest);

        if (!bizOrderModel.isPresent()) {
            log.error("未查询到当前订单, request:{}", request);
            return ResultGenerator.genResult(ResultCode.FAIL).setData("未查询到当前订单");
        }

        BizOrderModel bizOrderInDb = bizOrderModel.get();

        if (channelNotify == ChannelNotifyEnum.JDDJ_USER_CANCEL_ORDER_BEFORE_DELIVERY && bizOrderInDb.getOrderStatus() == OrderStatusEnum.REFUND_APPLIED.getValue()) {
            // 用户申请取消订单
            log.info("当前订单退款处理中，可能已经处理过了, request:{}", request);
            return ResultGenerator.genResult(ResultCode.SUCCESS).setData(ProjectConstant.OK);
        }

        if ((channelNotify == ChannelNotifyEnum.JDDJ_VENDER_AUDIT_APPLY_CANCEL_ORDER
                || channelNotify == ChannelNotifyEnum.JDDJ_USER_CANCEL_ORDER_BEFORE_DELIVERY_REVOCATION)
                && bizOrderInDb.getOrderStatus() != OrderStatusEnum.REFUND_APPLIED.getValue()) {
            // 商家审核订单或撤销订单
            log.info("当前订单不为退款中，可能已经处理过了, request:{}", request);
            return ResultGenerator.genResult(ResultCode.SUCCESS).setData(ProjectConstant.OK);
        }

        return doRefund(RefundParamDto.builder()
                .afsApplySourceType(AfterSaleSourceTypeEnum.LOCAL.getValue())
                .refundType(refundType)
                .afsApplyType(afsApplyType)
                .sponsor(sponsor)
                .channelTypeEnum(channelTypeEnum)
                .request(request)
                .build());
    }

    private ResultStatus jdPoiCallback(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request) {
        JdNotifyParamInfo paramInfo = JSON.parseObject(urlDecode(request.getJdParamJson()), JdNotifyParamInfo.class);
        String shopId = paramInfo.getBillId();
        //消息状态ID(12003:新增门店消息,12004:删除门店消息,12009:修改门店消息 )
        String statusId = paramInfo.getStatusId();
        CopAccessConfigDO copAccessConfigDO = copAccessConfigService.selectByTenantAppIdAndChannelId(request.getTenantAppId(), channelTypeEnum.getCode());
        if (copAccessConfigDO == null) {
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        long tenantId = copAccessConfigDO.getTenantId();
        Long appId = copAccessConfigDO.getAppId();
        if("12003".equals(statusId) || "12009".equals(statusId)) {
//            channelPoiThriftServiceProxy.incrementAndSynTenantChannelStoreInfo(tenantId, channelTypeEnum.getCode(),
//                    shopId, appId);
            if (MccConfigUtil.getAsyncTenant().contains(-1L) || MccConfigUtil.getAsyncTenant().contains(tenantId)) {
                ChannelPoiCallbackMessage message = new ChannelPoiCallbackMessage();
                message.setAppId(appId);
                message.setTenantId(tenantId);
                message.setChannelPoiCode(shopId);
                message.setChannelId(channelTypeEnum.getCode());
                message.setType(ChannelPoiCallbackTypeEnum.POI_SYNC.getType());
                message.setMessage(null);
                boolean send = channelPoiCallbackMessageProducer.sendMessageSync(message);
                if (!send) {
                    log.error("jdPoiCallback channelPoiCallbackMessageProducer.sendMessageSync fail.msg:{}", message);
                }
//                if ("12003".equals(statusId)) {
//                    dxPushMessageProducer.pushPoiBindMessage(tenantId,appId.toString(),ChannelType.JD2HOME.getValue(),shopId,"门店绑定应用");
//                }
            } else {
                channelPoiThriftServiceProxy.incrementAndSynTenantChannelStoreInfo(tenantId, channelTypeEnum.getCode(),
                        shopId, appId);
            }
        } else if("12004".equals(statusId)) {
//            channelPoiThriftServiceProxy.unBindChannelPoi(tenantId, channelTypeEnum.getCode(),
//                    shopId);
            if (MccConfigUtil.getAsyncTenant().contains(-1L) || MccConfigUtil.getAsyncTenant().contains(tenantId)) {
                // token需要在ocms channel 服务删除
                channelPoiThriftServiceProxy.deleteToke(tenantId, channelTypeEnum.getCode(), shopId);
                ChannelPoiCallbackMessage message = new ChannelPoiCallbackMessage();
                message.setAppId(appId);
                message.setTenantId(tenantId);
                message.setChannelPoiCode(shopId);
                message.setChannelId(channelTypeEnum.getCode());
                message.setType(ChannelPoiCallbackTypeEnum.POI_UNBIND.getType());
                message.setMessage(null);
                boolean send = channelPoiCallbackMessageProducer.sendMessageSync(message);
                if (!send) {
                    log.error("jdPoiCallback channelPoiCallbackMessageProducer.sendMessageSync unbind fail.msg:{}", message);
                }
                dxPushMessageProducer.pushPoiBindMessage(tenantId,appId.toString(),ChannelType.JD2HOME.getValue(),shopId,"门店解绑应用");
            }else {
                channelPoiThriftServiceProxy.unBindChannelPoi(tenantId, channelTypeEnum.getCode(),
                        shopId);
            }
        }
        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }


    /**
     * 有赞
     */
    private ResultStatus yzNotifyHandler(ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        try {
            //2.区分消息action（订单状态消息、配送状态消息、订单修改消息等）需要枚举
            Long tenantId = tenantRemoteService.getTenantIdParamByChannelPoiCode(ChannelTypeEnum.YOU_ZAN.getCode(), request.getAppPoiCode());
            if (tenantId == BigInteger.ZERO.longValue()) {
                return ResultGenerator.genResult(ResultCode.CHANNEL_APP_ID_INVALID).setData(ProjectConstant.NG);
            }
            ChannelOrderService channelOrderService = routeServiceFactory.selectChannelOrderService(ChannelTypeEnum.YOU_ZAN.getCode(), tenantId);

            ChannelOrderEvent orderEvent = null;

            ChannelOrderDetailDTO channelDetail;

            switch (notifyEnum) {
                case YZ_PAID:
                case YZ_BUYER_PAY:
                    channelDetail = channelOrderService.getOrderDetail4ChannelMessage(ChannelTypeEnum.YOU_ZAN, notifyEnum,
                            tenantId, request.getBody());

                    //发送mq
                    orderEvent = new ChannelOrderEvent(channelDetail.getChannelOrderId(),
                            tenantId, channelDetail.getStoreId(), channelDetail.getChannelOrderStatus(),
                            ChannelTypeEnum.YOU_ZAN.getCode(), System.currentTimeMillis(), notifyEnum.getAbbrev(), Collections.emptyMap(),
                            JacksonUtils.toJson(channelDetail));
                    break;
                case YZ_SELLER_SHIP:
                    TradeCommonMessage<String> sellerShipment = JSON.parseObject(request.getBody(),
                            new TypeReference<TradeCommonMessage<String>>() {});
                    TradeSellerShipMsg tradeSellerShipMsg = JSON.parseObject(UrlUtil.urlDecodeSafe(sellerShipment.getMsg()),
                            new TypeReference<TradeSellerShipMsg>() {
                            });
                    Long storeId = copChannelStoreService.selectChannelStoreId(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(),
                            String.valueOf(sellerShipment.getChannelPoiCode()));
                    //获取订单详情并处理
                    return handleYzSellerShip(tradeSellerShipMsg.getTid(),storeId,tenantId, tradeSellerShipMsg.getUpdateTime());
                case YZ_SUCCESS:
                    TradeCommonMessage<String> tradeSuccessMsg = JSON.parseObject(request.getBody(),
                            new TypeReference<TradeCommonMessage<String>>() {
                            });
                    TradeSuccess tradeSuccess = JSON.parseObject(UrlUtil.urlDecodeSafe(tradeSuccessMsg.getMsg()),
                            new TypeReference<TradeSuccess>() {
                            });
                {
                    Long shopId = copChannelStoreService.selectChannelStoreId(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(),
                            String.valueOf(tradeSuccessMsg.getChannelPoiCode()));

                    //发送mq
                    orderEvent = new ChannelOrderEvent(tradeSuccess.getTid(),
                            tenantId, shopId,
                            OrderStatusConverter.yzOrderStatusMapping(tradeSuccessMsg.getStatus()), ChannelTypeEnum.YOU_ZAN.getCode(),
                            getYZCompletedTime(tenantId, shopId, tradeSuccess),
                            notifyEnum.getAbbrev(), Collections.emptyMap(),
                            null);
                }
                    break;
                case YZ_CLOSE:
                    TradeCommonMessage<String> tradeCloseMsg = JSON.parseObject(request.getBody(),
                            new TypeReference<TradeCommonMessage<String>>() {});
                    TradeClose closeMsg = JSON.parseObject(UrlUtil.urlDecodeSafe(tradeCloseMsg.getMsg()),
                            new TypeReference<TradeClose>() {});

                    GetChannelOrderDetailRequest req = new GetChannelOrderDetailRequest();

                    Long shopId = copChannelStoreService.selectChannelStoreId(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(),
                            String.valueOf(tradeCloseMsg.getChannelPoiCode()));

                    req.setOrderId(closeMsg.getTid());
                    req.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
                    req.setTenantId(tenantId);
                    req.setSotreId(shopId);

                    GetChannelOrderDetailResult channelOrderDetail = channelOrderService.getChannelOrderDetail(req);

                    if (channelOrderDetail != null && channelOrderDetail.getChannelOrderDetail() != null) {
                        channelDetail = channelOrderDetail.getChannelOrderDetail();

                        // 交易关闭的消息，只处理支付过的订单，未支付但取消的订单过滤
                        if (channelDetail.getPayStatus() == PayStatusEnum.PAID.getValue()) {
                            //发送mq
                            orderEvent = new ChannelOrderEvent(closeMsg.getTid(),
                                    tenantId, shopId,
                                    OrderStatusConverter.yzOrderStatusMapping(tradeCloseMsg.getStatus()), ChannelTypeEnum.YOU_ZAN.getCode(),
                                    Optional.ofNullable(closeMsg.getUpdateTime()).map(Date::getTime).orElse(System.currentTimeMillis()),
                                    notifyEnum.getAbbrev(), ImmutableMap.of(
                                    "closeReason", closeMsg.getCloseReason(), "closeType", String.valueOf(closeMsg.getCloseType())),
                                    null);
                        }
                    }

                    break;

                case YZ_REFUND_BUYER_CREATED:
                case YZ_REFUND_REFUND_AGAIN:
                    return yzAfterSaleApply(tenantId, request, OcmsRefundSponsorEnum.CUSTOMER.getValue(), ChannelAfterSaleEventEnum.APPLY, false);
                case YZ_REFUND_SELLER_CREATED:
                    return yzAfterSaleApply(tenantId, request, OcmsRefundSponsorEnum.TENANT.getValue(), ChannelAfterSaleEventEnum.APPLY, false);
                case YZ_SYS_REFUND:
                    // 目前无此类型数据
                    return yzAfterSaleApply(tenantId, request, OcmsRefundSponsorEnum.CHANNEL.getValue(), ChannelAfterSaleEventEnum.APPLY, false);
                case YZ_REFUND_SUCCESS:
                case YZ_REFUND_SELLER_AGREE:
                case YZ_SELLER_AGREE_RETURN_GOODS:
                case YZ_REFUND_SELLER_REFUSE:
                case YZ_SELLER_REFUSE_BUYER_RETURN_GOODS:
                case YZ_REFUND_CLOSED:
                    return yzAfterSaleChange(tenantId, request, notifyEnum);
                case YZ_REFUND_INTER_APPLIED:
                    return yzAfterSaleApply(tenantId, request, OcmsRefundSponsorEnum.CUSTOMER.getValue(), ChannelAfterSaleEventEnum.APPEAL_APPLY, true);
                case YZ_REFUND_BUYER_RETURN_GOODS:
                    // 用户发货等同于重新发起售后并一审通过
                    return yzAfterSaleApply(tenantId, request, OcmsRefundSponsorEnum.CUSTOMER.getValue(), ChannelAfterSaleEventEnum.USER_RETURN_GOODS, false);
                case YZ_DELIVERY_TAKE_OUT_ORDER_UPDATE:
                    return yzDeliveryTakeOutOrderUpdate(request);
                default:
                    break;
            }

            if (orderEvent != null && orderEvent.getShopId() <= BigInteger.ZERO.intValue()) {
                log.error("有赞订单 find channel store lt 0, 门店未绑定, tenantId:{}", orderEvent.getTenantId());
                ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
                result.setMsg("success");
            }

            if (orderEvent != null && orderEvent.getShopId() >= BigInteger.ZERO.intValue()) {
                newSupplyChannelOrderNotifyMessageProducer.sendMessageSync(orderEvent);
            }


            ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            result.setMsg("success");
            return result;
        } catch (Exception e) {
            log.error("you zan order notify handler error, request:{} , exception:", request, e);
            return ResultGenerator.genResult(ResultCode.FAIL).setData(ProjectConstant.NG);
        }
    }

    private ResultStatus handleYzSellerShip(String channelOrderId,Long storeId, Long tenantId, Date updateTime) {
        ChannelOrderService channelOrderService = routeServiceFactory.selectChannelOrderService(ChannelTypeEnum.YOU_ZAN.getCode(), tenantId);
        GetChannelOrderDetailRequest channelOrderDetailRequest = new GetChannelOrderDetailRequest();
        channelOrderDetailRequest.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
        channelOrderDetailRequest.setOrderId(channelOrderId);
        channelOrderDetailRequest.setTenantId(tenantId);
        channelOrderDetailRequest.setSotreId(storeId);
        GetChannelOrderDetailResult channelOrderDetail = channelOrderService.getChannelOrderDetail(channelOrderDetailRequest);
        //判断是不是自提单
        String deliveryMethod = Optional.ofNullable(channelOrderDetail.getChannelOrderDetail().getDeliveryDetail().getDeliveryMethod()).orElse(StringUtils.EMPTY);
        if (StringUtils.isEmpty(deliveryMethod) || !ExpressTypeEnum.SELF_MENTION.getDesc().equals(deliveryMethod)) {
            log.info("有赞卖家发货：handleYzSellerShip 处理非自提单 tenantId:{},orderId:{}",tenantId,channelOrderId);
            //非自提有赞单，更改db状态即可
            return dealNonSelfPickupDeliveryInfo(channelOrderId, storeId, tenantId, updateTime);
        }
        int status = channelOrderDetail.getChannelOrderDetail().getChannelOrderStatus();
        ChannelOrderStatus statusEnum = ChannelOrderStatus.findByValue(status);
        //判断订单状态是不是完成/取消
        if (statusEnum == ChannelOrderStatus.FINISHED || statusEnum == ChannelOrderStatus.CANCELED) {
            log.info("有赞卖家发货：handleYzSellerShip 完结订单不处理 tenantId:{},orderId:{}",tenantId,channelOrderId);
            return ResultGenerator.genResult(ResultCode.SUCCESS);
        }
        MarkSignByOrderIdRequest request = new MarkSignByOrderIdRequest();
        request.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
        request.setOrderId(channelOrderId);
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        ResultStatus resultStatus = channelOrderService.markSignByOrderId(request);
       return resultStatus;


    }

    private ResultStatus dealNonSelfPickupDeliveryInfo(String channelOrderId,Long storeId, Long tenantId, Date operateTime) {
        try {
            BizDistributeStatusUpdateRequest request = new BizDistributeStatusUpdateRequest();
            request.setViewOrderId(channelOrderId);
            request.setOrderBizType(OrderBizTypeEnum.YOU_ZAN_MIDDLE.getValue());
            request.setStatus(DistributeStatusEnum.RIDER_TAKE_GOODS.getValue());
            request.setShopId(storeId);
            request.setTenantId(tenantId);
            request.setOperateTime(Objects.nonNull(operateTime) ? operateTime.getTime() : null);
            BizDistributeStatusUpdateResponse result = bizOrderThriftService.updateDeliveryInfoDistributeStatus(request);
            if(Objects.isNull(result) || Objects.isNull(result.getStatus()) || StatusCodeEnum.UNVALID_PARAM.getCode() == result.getStatus().getCode()){
                // 调用失败或处理参数错误的情形返回错误触发重试
                log.info("dealNonSelfPickupDeliveryInfo is fail result: {}", result);
                return ResultGenerator.genResult(ResultCode.FAIL);
            }
        }catch (TException e){
            log.info("ChannelOrderCallbackServiceImpl.dealNonSelfPickupDeliveryInfo error channelOrderId: {}, e: ", channelOrderId, e);
        }
        return ResultGenerator.genResult(ResultCode.SUCCESS);
    }

    private long getYZCompletedTime(Long tenantId, Long shopId, TradeSuccess tradeSuccess) {
        long completedTime;
        try {
            ChannelOrderDetailDTO channelDetail = getOrderDetail(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(), tradeSuccess.getTid(), 0, shopId);
            if (channelDetail != null && channelDetail.getCompletedTime() > 0) {
                completedTime = channelDetail.getCompletedTime();
            } else {
                completedTime = Optional.ofNullable(tradeSuccess.getUpdateTime()).map(Date::getTime).orElse(System.currentTimeMillis());
            }
        } catch (Exception e) {
            log.warn("getYZCompletedTime error {}", e.getMessage());
            completedTime = Optional.ofNullable(tradeSuccess.getUpdateTime()).map(Date::getTime).orElse(System.currentTimeMillis());
        }
        return completedTime;
    }

    private ResultStatus yzAfterSaleChange(Long tenantId, OrderNotifyRequest request, ChannelNotifyEnum channelNotifyEnum) {


        ChannelAfterSaleEventEnum channelAfterSaleEvent = null;
        ServiceTypeEnum serviceType = null;

        TradeCommonMessage<String> refundCommonMsg = JSON.parseObject(request.getBody(),
                new TypeReference<TradeCommonMessage<String>>() {
                });

        YzRefundChangeMsg yzRefundChangeMsg = JSON.parseObject(UrlUtil.urlDecodeSafe(refundCommonMsg.getMsg()),
                new TypeReference<YzRefundChangeMsg>() {
                });

        YzRefundTypeEnum yzRefundTypeEnum = YzRefundTypeEnum.enumOf(yzRefundChangeMsg.getRefundType());

        if (yzRefundTypeEnum.equals(YzRefundTypeEnum.REFUND_ONLY)) {
            serviceType = ServiceTypeEnum.REFUND;
        } else if (yzRefundTypeEnum.equals(YzRefundTypeEnum.REFUND_AND_RETURN)) {
            serviceType = ServiceTypeEnum.REFUND_GOODS;
        } else {
            log.error("不支持的退款类型");
            return ResultGenerator.genFailResult("不支持的退款类型", yzRefundChangeMsg.getRefundType());
        }

        switch (channelNotifyEnum) {
            case YZ_REFUND_SUCCESS:
            case YZ_REFUND_SELLER_AGREE:
                channelAfterSaleEvent = ChannelAfterSaleEventEnum.REFUND_SUCCESS;
                break;
            case YZ_SELLER_AGREE_RETURN_GOODS:
                // 一审通过
                channelAfterSaleEvent = ChannelAfterSaleEventEnum.AGREE;
                break;
            case YZ_REFUND_SELLER_REFUSE:
                if (ServiceTypeEnum.REFUND.equals(serviceType)) {
                    channelAfterSaleEvent = ChannelAfterSaleEventEnum.REJECT;
                } else if (ServiceTypeEnum.REFUND_GOODS.equals(serviceType)) {
                    // 二审驳回
                    channelAfterSaleEvent = ChannelAfterSaleEventEnum.REJECT_RETURN;
                } else {
                    channelAfterSaleEvent = ChannelAfterSaleEventEnum.REJECT;
                }
                break;
            case YZ_SELLER_REFUSE_BUYER_RETURN_GOODS:
                if (ServiceTypeEnum.REFUND_GOODS.equals(serviceType)) {
                    // 二审驳回
                    channelAfterSaleEvent = ChannelAfterSaleEventEnum.REJECT_RETURN;
                } else {
                    channelAfterSaleEvent = ChannelAfterSaleEventEnum.REJECT;
                }
                break;
            case YZ_REFUND_CLOSED:
                channelAfterSaleEvent = ChannelAfterSaleEventEnum.APPLY_CANCELED;
                break;
            default:
                log.error("不支持的退款消息类型");
                return ResultGenerator.genFailResult("不支持的退款消息类型", channelNotifyEnum.getAbbrev());
        }

        ChannelAfterSaleEvent afterSaleEvent = null;

        afterSaleEvent = new ChannelAfterSaleEvent();

        afterSaleEvent.setChannelOrderId(yzRefundChangeMsg.getTid());
        afterSaleEvent.setChannelType(ChannelTypeEnum.YOU_ZAN.getCode());
        afterSaleEvent.setEventId(channelAfterSaleEvent.getEventId());
        afterSaleEvent.setTenantId(tenantId);
        afterSaleEvent.setTimestamp(System.currentTimeMillis());
        afterSaleEvent.setAfterSaleId(yzRefundChangeMsg.getRefundId());
        afterSaleEvent.setReason(yzRefundChangeMsg.getRefundReason());
        afterSaleEvent.setRefundApplyTime(DateUtils.date2Millisecond(yzRefundChangeMsg.getUpdateTime()));
        afterSaleEvent.setRefundAmount(MoneyUtils.yuanToFen(yzRefundChangeMsg.getRefundedFee()));
        afterSaleEvent.setShopId(copChannelStoreService.selectChannelStoreId(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(),
                String.valueOf(refundCommonMsg.getChannelPoiCode())));
        afterSaleEvent.setAfterSaleServiceType(serviceType.getCode());


        // 消息中没有商品明细
        afterSaleEvent.setRefundProducts(Collections.emptyList());

        if (afterSaleEvent != null && afterSaleEvent.getShopId() <= BigInteger.ZERO.intValue()) {
            log.warn("有赞售后 find channel store lt 0, 门店未绑定, tenantId:{}", afterSaleEvent.getTenantId());
            ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            result.setMsg("success");
        }


        if (afterSaleEvent != null && afterSaleEvent.getShopId() >= BigInteger.ZERO.intValue()) {
            newSupplychannelOrderRefundMessageProducer.sendMessageSync(afterSaleEvent);
        }

        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        result.setMsg("success");
        return result;
    }

    private ResultStatus yzAfterSaleApply(Long tenantId, OrderNotifyRequest request, Integer sponsor, ChannelAfterSaleEventEnum channelAfterSaleEvent, boolean isAppeal) {
        ChannelAfterSaleEvent afterSaleEvent = null;

        TradeCommonMessage<String> refundCommonMsg = JSON.parseObject(request.getBody(),
                new TypeReference<TradeCommonMessage<String>>() {
                });

        YzRefundMsg yzRefundMsg = JSON.parseObject(UrlUtil.urlDecodeSafe(refundCommonMsg.getMsg()),
                new TypeReference<YzRefundMsg>() {
                });


        afterSaleEvent = new ChannelAfterSaleEvent();

        YzRefundTypeEnum yzRefundTypeEnum = YzRefundTypeEnum.enumOf(yzRefundMsg.getRefundType());

        ServiceTypeEnum serviceTypeEnum = ServiceTypeEnum.REFUND;

        if (YzRefundTypeEnum.REFUND_ONLY.equals(yzRefundTypeEnum)) {
            serviceTypeEnum = ServiceTypeEnum.REFUND;
        } else if (YzRefundTypeEnum.REFUND_AND_RETURN.equals(yzRefundTypeEnum)) {
            serviceTypeEnum = ServiceTypeEnum.REFUND_GOODS;
        } else {
            log.warn("不支持的售后类型{}", yzRefundMsg.getRefundType());
            ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            result.setMsg("success");
        }


        afterSaleEvent.setAfterSaleServiceType(serviceTypeEnum.getCode());
        afterSaleEvent.setChannelOrderId(yzRefundMsg.getTid());
        afterSaleEvent.setChannelType(ChannelTypeEnum.YOU_ZAN.getCode());
        afterSaleEvent.setEventId(channelAfterSaleEvent.getEventId());
        afterSaleEvent.setAppeal(isAppeal);
        afterSaleEvent.setTenantId(tenantId);
        afterSaleEvent.setTimestamp(System.currentTimeMillis());
        afterSaleEvent.setAfterSaleId(yzRefundMsg.getRefundId());
        // 无法分辨全单退还是部分退
        afterSaleEvent.setRefundType(RefundTypeEnum.PART.getValue());
        // 不使用消息体里的原因，因其不包含用户留言
        afterSaleEvent.setReason("");
        afterSaleEvent.setRefundApplyTime(DateUtils.date2Millisecond(yzRefundMsg.getUpdateTime()));
        afterSaleEvent.setRefundAmount(MoneyUtils.yuanToFen(yzRefundMsg.getRefundedFee()));
        afterSaleEvent.setSponsor(sponsor);
        afterSaleEvent.setShopId(copChannelStoreService.selectChannelStoreId(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(),
                String.valueOf(refundCommonMsg.getChannelPoiCode())));

        // 消息中没有商品明细
        afterSaleEvent.setRefundProducts(Collections.emptyList());

        if (afterSaleEvent != null && afterSaleEvent.getShopId() <= BigInteger.ZERO.intValue()) {
            log.warn("有赞售后 find channel store lt 0, 门店未绑定, tenantId:{}", afterSaleEvent.getTenantId());
            ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            result.setMsg("success");
        }


        if (afterSaleEvent != null && afterSaleEvent.getShopId() >= BigInteger.ZERO.intValue()) {
            newSupplychannelOrderRefundMessageProducer.sendMessageSync(afterSaleEvent);
        }

        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        result.setMsg("success");
        return result;
    }

    private ResultStatus yzDeliveryTakeOutOrderUpdate(OrderNotifyRequest request) {
        YzDeliveryTakeOutOrderUpdateMsg msg;
        try {
            msg = JSON.parseObject(request.getBody(), YzDeliveryTakeOutOrderUpdateMsg.class);
        } catch (Exception e) {
            log.error("yzDeliveryTakeOutOrderUpdate exception", e);
            ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.ERROR);
            result.setMsg("failed");
            return result;
        }
        if (Objects.nonNull(msg)) {
            newSupplyYzDeliveryChangeMessageProducer.sendMessageSync(msg);
        }

        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        result.setMsg("success");
        return result;
    }

    /**
     * 歪马
     */
    private ResultStatus drunkHorseNotifyHandler(ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        log.info("drunkHorseNotifyHandler,request: {}", request);
        try {
            if (StringUtils.isBlank(request.getOrderId())) {
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }
            Long tenantId = tenantRemoteService.getTenantIdParam(ChannelTypeEnum.MT_DRUNK_HORSE.getCode(), request.getTenantAppId());
            DrunkHorseCopAccessAndStore drunkHorseCopAccessAndStore = null;
            if (tenantId == BigInteger.ZERO.longValue()) {
                //处理跨渠道下单
                tenantId = handleWxMallCrossOrder(request);
                if (tenantId == null || tenantId <= 0L){
                    return ResultGenerator.genResult(ResultCode.CHANNEL_APP_ID_INVALID).setData(ProjectConstant.NG);
                }
            }

            if (ConfigUtilAdapter.getBoolean("drunk.horse.filter.aftersale.causeby.repay", true)
                    && request.getApplyOpScenario() == AfterSaleCommonOpScenario.SYSTEM_APPLY_FOR_REPEAT_PAY){
                // 因为用户重复支付，推送了订单取消消息。这类消息应该要过滤掉
                return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            }

            Long shopId = copChannelStoreService.selectChannelStoreId(tenantId, ChannelTypeEnum.MT_DRUNK_HORSE.getCode(),
                    String.valueOf(request.getWmPoiId()));
            switch (notifyEnum) {
                case DH_ORDER_PAID:
                case DH_ORDER_CONFIRM:
                case DH_ORDER_FINISH:
                case DH_ORDER_CANCELED:
                    OrderEvent orderEvent = buildOrderEvent(request, notifyEnum, tenantId, shopId);
                    // 压测流量通过topic和正常流量隔离
                    boolean sendResult = isTest(shopId) && Tracer.isTest()
                            ? channelPressureTestOrderNotifyMessageProducer.sendMessageSync((OcmsPressureTestOrderEvent) orderEvent)
                            : channelOrderNotifyMessageProducer.sendMessageSync((OcmsOrderEvent) orderEvent);
                    break;
                case DH_ORDER_REFUND:
                    OcmsAfterSaleEvent refundEvent = new OcmsAfterSaleEvent();
                    refundEvent.setChannelOrderId(request.getOrderId());
                    refundEvent.setChannelType(ChannelTypeEnum.MT_DRUNK_HORSE.getCode());
                    refundEvent.setEventId(OrderStatusConverter.dhAfterSaleStatusMapping(DrunkHorseRefundStatusEnum.enumOf(Integer.valueOf(request.getStatus()))));
                    refundEvent.setTenantId(tenantId);
                    refundEvent.setTimestamp(System.currentTimeMillis());
                    if (request.getRefundFlow() > 0) {
                        refundEvent.setRefundFlow(String.valueOf(request.getRefundFlow()));
                    }
                    refundEvent.setAfterSaleId(request.getRefundId());
                    refundEvent.setRefundType(OrderStatusConverter.dhAfterSaleTypeMapping(request.getRefundType()));
                    refundEvent.setReason(request.getRefundReason());
                    refundEvent.setRefundApplyTime(Long.valueOf(request.getRefundApplyTime()));
                    refundEvent.setRefundAmount(MoneyUtils.yuanToFen(request.getRefundPrice()));
                    refundEvent.setAuditType(DrunkHorseAuditTypeUtils.getAuditTypeFromRefund(request));
                    refundEvent.setSponsor(DrunkHorseConverterUtil.translateSponsor(
                            Integer.valueOf(StringUtils.defaultString(request.getOpType(), "0")),
                            request.getApplyOpScenario()));
                    if (request.getWmPoiId() > 0){
                        refundEvent.setShopId(shopId != null ? shopId : 0L);
                    }
                    if (refundEvent.getRefundType() == RefundTypeEnum.PART.getValue()) {
                        List<DrunkHorsePartRefundGoodsDTO> channelPartRefundSkuInfos =
                                JSON.parseArray(request.getProductJson(), DrunkHorsePartRefundGoodsDTO.class);
                        List<PartRefundProductInfo> refundProductDTOS = drunkHorseConverterService.partRefundSpuEventInfoList(channelPartRefundSkuInfos);
                        refundEvent.setRefundProducts(refundProductDTOS);
                    }
                    if (isDealAfterSale(refundEvent)){
                        //reason设置成审批文案
                        Map<String, String> opScenarioMap = Lion.getConfigRepository().getMap("drunkhorse.wechat.opScenario", String.class, DrunkHorseConverterUtil.defaultOpScenarioMap);
                        if (opScenarioMap != null){
                            refundEvent.setOpScenario(opScenarioMap.get(String.valueOf(request.getApplyOpScenario())));
                        }
                    }
                    channelOrderRefundMessageProducer.sendMessageSync(refundEvent, request.getOrderId());
                    break;
                default:
                    break;
            }

            ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            result.setMsg("success");
            return result;
        } catch (Exception e) {
            log.error("drunkhorse order notify handler error, request:{} , exception:", request, e);
            return ResultGenerator.genResult(ResultCode.FAIL).setData(ProjectConstant.NG);
        }
    }

    private Long handleWxMallCrossOrder(OrderNotifyRequest request) {
        DrunkHorseWrongPoiMapping existedPoiMapping = drunkHorseCacheService.getCrossOrderMapping(request.getOrderId());
        if (existedPoiMapping != null){
            log.info("门店跨渠道下单，order:{}, 替换wmPoiId和appId, 原poiCode:{}，目标poiCode:{}, 原appId:{}, 目标appId:{}",
                    request.getWmPoiId(),
                    existedPoiMapping.getTargetAppPoiCode(),
                    request.getTenantAppId(),
                    existedPoiMapping.getTargetTenantAppId());
                    // 设置门店ID
            request.setWmPoiId(
                            NumberUtils.isParsable(existedPoiMapping.getTargetAppPoiCode()) ? NumberUtils.toLong(existedPoiMapping.getTargetAppPoiCode()) : request.getWmPoiId());
            request.setTenantAppId(StringUtils.defaultString(existedPoiMapping.getTargetTenantAppId(), request.getTenantAppId()));
            return getTenantIdParam(ChannelTypeEnum.MT_DRUNK_HORSE.getCode(), request.getTenantAppId());
        }
        if (MccConfigUtil.isMtChannelWxMallPoiSwitchOpen() && request.getWmPoiId() > 0){
            DrunkHorseCopAccessAndStore drunkHorseCopAccessAndStore = drunkHorseOrderService.queryCrossPoiAccessAndStoreConfig(request.getTenantAppId(), String.valueOf(request.getWmPoiId()), ChannelTypeEnum.MT_DRUNK_HORSE.getCode(), ChannelTypeEnum.MEITUAN.getCode());
            if (drunkHorseCopAccessAndStore != null){
                //覆盖门店ID
                boolean flag = drunkHorseCacheService.setPoiCrossOrderMapping(request.getOrderId(),
                        new DrunkHorseWrongPoiMapping(String.valueOf(request.getWmPoiId()),
                                drunkHorseCopAccessAndStore.getCrossPoiAccessConfigDO().getAppId(),
                                ChannelTypeEnum.MT_DRUNK_HORSE.getCode(),
                                ChannelTypeEnum.MEITUAN.getCode(),
                                drunkHorseCopAccessAndStore.getStoreId(),
                                drunkHorseCopAccessAndStore.getChannelStoreDO().getChannelOnlinePoiCode(),
                                drunkHorseCopAccessAndStore.getCopAccessConfigDO().getTenantAppId()));
                if (flag) {
                    orderTrackService.alertMtPoiOrderFromDhChannel(ChannelTypeEnum.MT_DRUNK_HORSE.getCode(), request.getTenantAppId(), request.getOrderId(), request.getAppPoiCode());
                }
                log.info("歪马美团门店进入微商城下单，order:{}, 替换wmPoiId和appId, 原poiCode:{}，目标poiCode:{}, 原appId:{}, 目标appId:{}",
                        request.getWmPoiId(),
                        drunkHorseCopAccessAndStore.getChannelStoreDO().getChannelOnlinePoiCode(),
                        request.getTenantAppId(),
                        drunkHorseCopAccessAndStore.getCopAccessConfigDO().getTenantAppId());
                request.setWmPoiId(NumberUtils.toLong(drunkHorseCopAccessAndStore.getChannelStoreDO().getChannelOnlinePoiCode()));
                request.setTenantAppId(drunkHorseCopAccessAndStore.getCopAccessConfigDO().getTenantAppId());
                return drunkHorseCopAccessAndStore.getCopAccessConfigDO().getTenantId();
            }

        }
        return null;
    }

    private Integer getAuditType(OrderNotifyRequest request) {
        if(Objects.equals(request.getExtremeSpeedRefund(), AuditTypeEnum.QUICK_REFUND_AUDIT.getCode())) {
            return AuditTypeEnum.QUICK_REFUND_AUDIT.getCode();
        } else {
            return AuditTypeEnum.UNKNOWN_AUDIT.getCode();
        }
    }

    private boolean isDealAfterSale(OcmsAfterSaleEvent refundEvent) {
        return OcmsAfterSaleEventEnum.AGREE.getEventId() == refundEvent.getEventId()
                || OcmsAfterSaleEventEnum.REJECT.getEventId() == refundEvent.getEventId();
    }

    // 京东到家订单调整消息处理
    // 商家调整订单，调整的是商品数量，处理逻辑
    // 1、先创建售后单，2、退款成功
    private ResultStatus handleJddjOrderAdjust(OrderNotifyRequest request) {
        CopAccessConfigDO accessConfig = getAccessConfig(ChannelTypeEnum.JD2HOME.getCode(), request.getTenantAppId());
        long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
        Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);


        request.setCancelReason("京东部分退（调整单）产生退单");

        // 补充部分退款需要的信息
        BizOrderQueryRequest queryRequest = BizOrderQueryRequest.builder()
                .orderBizType(OrderBizTypeEnum.JING_DONG.getValue())
                .tenantId(tenantId)
                .viewOrderId(request.getOrderId())
                .fromMaster(true)
                .build();

        Optional<BizOrderModel> bizOrderModel = channelOrderThriftServiceProxy.queryOrderInfo(queryRequest);

        if (!bizOrderModel.isPresent()) {
            log.error("未查询到当前订单, request:{}", request);
            return ResultGenerator.genResult(ResultCode.FAIL).setData("未查询到当前订单");
        }


        BizOrderModel bizOrderInDb = bizOrderModel.get();

        List<BizOrderItemModel> bizOrderItemModelList = bizOrderInDb.getBizOrderItemModelList();

        if (CollectionUtils.isEmpty(bizOrderItemModelList)) {
            log.error("当前订单的商品明细为空, request:{}", request);
            return ResultGenerator.genResult(ResultCode.FAIL).setData(ProjectConstant.NG);
        }

        BizOrderAfterSaleQueryRequest bizOrderAfterSaleQueryRequest = new BizOrderAfterSaleQueryRequest();
        bizOrderAfterSaleQueryRequest.setViewOrderId(bizOrderInDb.getViewOrderId());
        bizOrderAfterSaleQueryRequest.setOrderBizType(bizOrderInDb.getOrderBizType());
        bizOrderAfterSaleQueryRequest.setTenantId(bizOrderInDb.getTenantId());
        bizOrderAfterSaleQueryRequest.setAfterSaleApplyStatusList(Lists.list(AfterSaleApplyStatusEnum.AUDITED.getValue(), AfterSaleApplyStatusEnum.COMMIT.getValue()));

        Optional<List<BizOrderAfterSaleRefundModel>> bizOrderAfterSaleRefundModels = channelOrderThriftServiceProxy.queryOrderAfterSale(bizOrderAfterSaleQueryRequest);

        List<BizOrderAfterSaleRefundModel> partRefundSuccessAfterSaleList = bizOrderAfterSaleRefundModels.orElse(Lists.emptyList()).stream().filter(item -> item.getAfsPattern() == AfterSalePatternEnum.PART.getValue()).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(partRefundSuccessAfterSaleList)) {
            log.warn("京东订单调整,已经存在提交和审核通过的部分退款售后单，消息可能发重，忽略该消息:{}", request);
            return ResultGenerator.genResult(ResultCode.SUCCESS).setData(ProjectConstant.OK);
        }


        ChannelOrderDetailDTO orderDetailModel = getOrderDetailModel(tenantId, ChannelTypeEnum.JD2HOME.getCode(), request.getOrderId(),appId);

        if (orderDetailModel == null) {
            log.error("未查询到当前调整后的订单, request:{}", request);
            return ResultGenerator.genResult(ResultCode.FAIL).setData(ProjectConstant.NG);
        }

        List<OrderProductDetailDTO> skuDetails = orderDetailModel.getSkuDetails();

        if (skuDetails == null) {
            skuDetails = new ArrayList<>();
        }

        // 0元商品是赠品，不参与计算
        Map<JDSkuKey, OrderProductDetailDTO> stringOrderProductDetailDTOMap = skuDetails.stream()
                .filter(skuDetail -> skuDetail.getSalePrice() > 0)
                .collect(Collectors.toMap(v->new JDSkuKey(v.getSkuId(), v.getSalePrice()), orderProductDetailDTO -> orderProductDetailDTO, (key1, key2) -> key1));

        JddjAfterSaleDetail jddjAfterSaleDetail = new JddjAfterSaleDetail();

        jddjAfterSaleDetail.setAfsMoney(Long.valueOf(bizOrderInDb.getActualPayAmt() - orderDetailModel.getActualPayAmt()));
        request.setRefundPrice(String.valueOf(bizOrderInDb.getActualPayAmt() - orderDetailModel.getActualPayAmt()));

        List<JddjAfsServiceDetail> jddjAfsServiceDetails = new ArrayList<>();

        jddjAfterSaleDetail.setAfsDetailList(jddjAfsServiceDetails);

        bizOrderItemModelList.forEach(bizOrderItemModel -> {
            // 0元商品是赠品，不参与订单调整
            if (bizOrderItemModel.getTotalPayAmount() != 0) {
                OrderProductDetailDTO orderProductDetailDTO = stringOrderProductDetailDTOMap.get(new JDSkuKey(bizOrderItemModel.getCustomerSkuId(), bizOrderItemModel.getCurrentPrice()));

                // 调整后的订单没有这个商品，个数调整为0
                int diffQuantity = orderProductDetailDTO == null ? bizOrderItemModel.getQuantity() : bizOrderItemModel.getQuantity() - orderProductDetailDTO.getQuantity();


                if (diffQuantity > 0) {
                    JddjAfsServiceDetail jddjAfsServiceDetail = new JddjAfsServiceDetail();

                    jddjAfsServiceDetail.setWareName(bizOrderItemModel.getSkuName());
                    jddjAfsServiceDetail.setSkuIdIsv(bizOrderItemModel.getCustomerSkuId());
                    jddjAfsServiceDetail.setSkuCount(diffQuantity);
                    jddjAfsServiceDetail.setUpcCode(bizOrderItemModel.getBarCode());
                    jddjAfsServiceDetail.setCashMoney(bizOrderItemModel.getCurrentPrice() * diffQuantity);
                    jddjAfsServiceDetail.setPayPrice(bizOrderItemModel.getCurrentPrice() * diffQuantity);
                    jddjAfsServiceDetail.setAfsMoney(bizOrderItemModel.getCurrentPrice() * diffQuantity);
                    // 餐盒费不准确
                    jddjAfsServiceDetail.setMealBoxMoney(1);

                    jddjAfsServiceDetails.add(jddjAfsServiceDetail);

                } else if (diffQuantity < 0){
                    // 不应该出现这种情况
                    log.error("订单调整出现了调整后数量多余调整前数量的商品");
                }
            }
        });

        request.setProductJson(JSONObject.toJSONString(jddjAfterSaleDetail));
        request.setRefundId(JD_ADJUST_ORDER_PREFIX+request.getOrderId());

        ResultStatus resultStatus = doPartRefund(ChannelTypeEnum.JD2HOME, OrderPartRefundType.APPLY.getValue(),
                OrderRefundSponsor.CHANNEL.getValue(), request);

        if (resultStatus.getCode() != ResultCode.SUCCESS.getCode()) {

            log.error("订单调整失败 {}, requst:{}", resultStatus, request);
            return resultStatus;
        }

        return doPartRefund(ChannelTypeEnum.JD2HOME, OrderPartRefundType.CHANNEL_SYSTEM_AGREE.getValue(),
                OrderRefundSponsor.CHANNEL.getValue(), request);
    }

    private ResultStatus doSelfDelivery(OrderNotifyRequest request,OrderBizTypeEnum orderBizTypeEnum){
        try {
            SelfDeliveryCallbackReq req=new SelfDeliveryCallbackReq();
            req.setChannelOrderId(request.getOrderId());
            req.setOrderBizType(orderBizTypeEnum.getValue());
            deliveryCallbackThriftService.selfDeliveryCallback(req);
        }catch (Exception e){
            log.error("doSelfDelivery error request:{},orderBizTypeEnum:{}",request,orderBizTypeEnum);
            ResultGenerator.genFailResult("转自配送失败");
        }
        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }



    private ResultStatus doJdAfterSale(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        JddjGetAfterSaleDetailRequest getAfterSaleDetailRequest = new JddjGetAfterSaleDetailRequest();
        getAfterSaleDetailRequest.setAfterSaleId(request.getOrderId());//售后订单ID
        getAfterSaleDetailRequest.setChannelId(channelTypeEnum.getCode());
        CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
        Long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
        Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
        getAfterSaleDetailRequest.setTenantId(tenantId);
        getAfterSaleDetailRequest.setAppId(appId);
        JddjAfterSaleDetailResult afterSaleDetailResult = jddjAfterSaleService.afterSaleDetail(getAfterSaleDetailRequest);
        JddjAfterSaleDetail afterSaleDetail = afterSaleDetailResult.getAfterSaleDetail();
        if (afterSaleDetail == null) {
            //判断是否是回调测试
            if (MccConfigUtil.getJddjMockResponseTenant().getTenantIdList().contains(tenantId)) {
                log.info("京东测试订单那，忽略该请求:{}", request);
                throw new MockTestException();
            }
            log.error("京东售后，查询售后详情失败：{}, result:{}", getAfterSaleDetailRequest, afterSaleDetailResult);
            return ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST).setData(ProjectConstant.NG);
        }
        //判断是否是需要处理的通知类型
        if (ChannelStatusConvertUtil.jddjAfterSaleRefundAllRefundTypeMapping(request.getStatus(), notifyEnum) <= 0
                && ChannelStatusConvertUtil.jddjAfterSaleRefundPartRefundTypeMapping(request.getStatus(), notifyEnum) <= 0
                && ChannelStatusConvertUtil.jddjAfterSaleRefundAllRefundGoodsTypeMapping(request.getStatus(),notifyEnum,afterSaleDetail.getExtMap()) <= 0) {
            log.info("不用处理的京东售后信息, detail:{},status:{}", JSON.toJSONString(afterSaleDetail),request.getStatus());
            return ResultGenerator.genSuccessResult();
        }
        //判断是全单退款还是部分退款
        boolean isAllRefund = isAllRefund(tenantId, afterSaleDetail, channelTypeEnum,appId);

        request.setRefundId(getAfterSaleDetailRequest.getAfterSaleId());
        String refundId = request.getOrderId();
        request.setOrderId(afterSaleDetail.getOrderId());//由于doRefund认为orderId是订单id，但是京东传过来的是售后ID
        String cancelReason = StringUtils.join(JDAfterSaleReasonEnum.enumOf(afterSaleDetail.getQuestionTypeCid()).getValue(),
                StringUtils.isNotBlank(afterSaleDetail.getQuestionDesc()) ? ":" + afterSaleDetail.getQuestionDesc() : "");
        request.setRefundReason(cancelReason);
        request.setCancelReason(cancelReason);
        request.setPictureJson(StringUtils.isNotBlank(afterSaleDetail.getQuestionPic()) ? afterSaleDetail.getQuestionPic() : null);
        request.setIsAppeal(JddjApproveTypeEnum.CUSTOMER_SERVICE.getCode().equals(afterSaleDetail.getApproveType()) ? IsAppealEnum.IS_APPEAL.getCode() : IsAppealEnum.UN_APPEAL.getCode());

        if (JddjDutyAssumeEnum.JDDJ.getCode().equals(afterSaleDetail.getDutyAssume()) || JddjDutyAssumeEnum.LOGISTICS.getCode().equals(afterSaleDetail.getDutyAssume())) {
            // 京东到家物流责任的售后单不需要审核，作为申诉单
            request.setIsAppeal(IsAppealEnum.IS_APPEAL.getCode());
        }

        log.info("退款退货 isAllRefund : {} , request : {}", isAllRefund, request);
        try {
            if (isAllRefund) {
                long refundAmount = Optional.ofNullable(afterSaleDetail.getCashMoney()).orElse(0L)
                        + Optional.ofNullable(afterSaleDetail.getMealBoxMoney()).orElse(0L)
                        + Optional.ofNullable(afterSaleDetail.getOrderFreightMoney()).orElse(0L)
                        + Optional.ofNullable(afterSaleDetail.getPackagingMoney()).orElse(0L)
                        + Optional.ofNullable(afterSaleDetail.getPlatformIntegralDeductMoney()).orElse(0L);
                request.setRefundPrice("" + refundAmount);

                //全单退款
                if(JDApplyDealTypeEnum.enumOf(afterSaleDetail.getApplyDeal()) == JDApplyDealTypeEnum.REFUND_GOODS){
                    int refundGoodsType = ChannelStatusConvertUtil.jddjAfterSaleRefundAllRefundGoodsTypeMapping(request.getStatus(), notifyEnum, afterSaleDetail.getExtMap());
                    setRefundGoodsRequest(request);
                    return doRefundGoods(channelTypeEnum, refundGoodsType, ChannelStatusConvertUtil.jddjSponsorMapping(afterSaleDetail.getOrderSource()), AfsApplyType.AFTER_SALE.getValue(), request);
                }
                int refundType = ChannelStatusConvertUtil.jddjAfterSaleRefundAllRefundTypeMapping(request.getStatus(), notifyEnum);
                return doRefund(channelTypeEnum, refundType, ChannelStatusConvertUtil.jddjSponsorMapping(afterSaleDetail.getOrderSource()), AfsApplyType.AFTER_SALE.getValue(), request);
            }
            else {
                //部分退款
                int refundType = ChannelStatusConvertUtil.jddjAfterSaleRefundPartRefundTypeMapping(request.getStatus(), notifyEnum);
                //用户售后实际退款总金额totalMoney=应退商品金额cashMoney+应退订单餐盒费mealBoxMoney+应退订单运费金额orderFreightMoney+应退包装费金额packagingMoney+应退到家积分金额platformIntegralDeductMoney
                long refundAmount = Optional.ofNullable(afterSaleDetail.getCashMoney()).orElse(0L)
                        + Optional.ofNullable(afterSaleDetail.getMealBoxMoney()).orElse(0L)
                        + Optional.ofNullable(afterSaleDetail.getOrderFreightMoney()).orElse(0L)
                        + Optional.ofNullable(afterSaleDetail.getPackagingMoney()).orElse(0L)
                        + Optional.ofNullable(afterSaleDetail.getPlatformIntegralDeductMoney()).orElse(0L);
                request.setRefundPrice("" + refundAmount);
                request.setRefundId(getAfterSaleDetailRequest.getAfterSaleId());//售后订单的ID
                request.setProductJson(JSON.toJSONString(afterSaleDetail));
                request.setRefundApplyTime(DateUtils.formatDate(new Date(afterSaleDetail.getCreateTime()), DateUtils.DefaultLongFormat));
                // 京东退货退款
                if (JDApplyDealTypeEnum.enumOf(afterSaleDetail.getApplyDeal()) == JDApplyDealTypeEnum.REFUND_GOODS) {
                    setRefundGoodsRequest(request);
                    int refundGoodsType = ChannelStatusConvertUtil.jddjAfterSaleRefundAllRefundGoodsTypeMapping(request.getStatus(), notifyEnum, afterSaleDetail.getExtMap());
                    return doPartRefundGoods(channelTypeEnum, refundGoodsType, ChannelStatusConvertUtil.jddjSponsorMapping(afterSaleDetail.getOrderSource()), AfsApplyType.AFTER_SALE.getValue(), request);
                }
                return doPartRefund(channelTypeEnum, refundType,
                        ChannelStatusConvertUtil.jddjSponsorMapping(afterSaleDetail.getOrderSource()), AfsApplyType.AFTER_SALE.getValue(), request);
            }
        }
        finally {
            request.setOrderId(refundId);//环境变量赋值
        }

    }


    private boolean isAllRefund(long tenantId, JddjAfterSaleDetail afterSaleDetail, ChannelTypeEnum channelTypeEnum,long appId){
        //判断是全单退款还是部分退款
        AtomicBoolean refundAllOrder = new AtomicBoolean(IS_AFTERSALE_LAST_APPLY.equals(afterSaleDetail.getIsLastApply()));
        if (refundAllOrder.get()){
            return true;
        }
        //如果是部分退款，不一定是真正部分退款
        //京东巨坑，全单退款有可能不一致。退款申请的时候是部分退款，审批的时候是全单退款。（isLastApply字段）
        ChannelOrderDetailDTO orderDetail = getOrderDetailModel(tenantId, channelTypeEnum.getCode(), afterSaleDetail.getOrderId(),appId);
        if (orderDetail == null) {
            log.error("京东售后，查询订单详情失败：{}, result:{}", afterSaleDetail);
            throw new BizException(ResultCode.ORDER_NOT_EXIST.getMsg());
        }
        int totalCount = orderDetail.getSkuDetails().stream().mapToInt(OrderProductDetailDTO::getQuantity).sum();
        int refundCount = afterSaleDetail.getAfsDetailList().stream()
                .filter(afs -> !(Objects.nonNull(afs.getPartialRefundRatioShow()) && afs.getPartialRefundRatioShow() > 0 && afs.getPartialRefundRatioShow() < 1))
                .mapToInt(JddjAfsServiceDetail::getSkuCount).sum();
        //退完所有商品
        if (refundCount >= totalCount){
            return true;
        }

        //不能通过包装费和运费来判断是否整单退
        AtomicBoolean hasRemainProduct = new AtomicBoolean(false);
        log.info("doJdAfterSale orderDetail:{}, afterSaleDetail:{}", orderDetail.getSkuDetails(), afterSaleDetail.getAfsDetailList());
        orderDetail.getSkuDetails().forEach(e -> {
            String skuId = e.getSkuId();
            Integer salePrice = e.getSalePrice();
            Optional<JddjAfsServiceDetail> notWholeRefund = afterSaleDetail.getAfsDetailList().stream()
                    .filter(afs -> Objects.nonNull(afs.getPartialRefundRatioShow()) && afs.getPartialRefundRatioShow() > 0 && afs.getPartialRefundRatioShow() < 1)
                    .findAny();
            //存在非整件售后，则非整单退
            if (notWholeRefund.isPresent()) {
                hasRemainProduct.set(true);//非全单退款,存在非整件退
                return;
            }
            //是否存在商品未退完
            Optional<JddjAfsServiceDetail> afsDetail = afterSaleDetail.getAfsDetailList().stream()
                    .filter(afs -> StringUtils.equals(afs.getSkuIdIsv(), skuId) && Objects.equals(salePrice, afs.getPayPrice()))
                    .findAny();
            if (!afsDetail.isPresent() || afsDetail.get().getSkuCount() < e.getQuantity()) {
                hasRemainProduct.set(true);//非全单退款
            }
        });
        return !hasRemainProduct.get();//如果有剩余商品没有退，说明不是全单，如果没有说明是全单退款

    }


    /**
     * 京东到家特殊逻辑，32000状态需先调用创建订单，成功后调用确认订单
     * 自动接单，新建订单时，只有一条消息32000
     * 人工接单，会先来41000，商家确认后再来一条32000
     */
    private ResultStatus handleJd2homeOrderStatus(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
        switch (request.getStatus()) {
            case ProjectConstant.JDDJ_NEW_ORDER_STATUS:
                return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.NEW_ORDER), notifyEnum);
            case ProjectConstant.JDDJ_BIZ_CONFIRM_STATUS:
                ResultStatus status = doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, ChannelOrderStatus.NEW_ORDER), notifyEnum);
                if (status.getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                    CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request.getTenantAppId());
                    Long appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);

                    // 因为京东门店手动接单，也有传32000的情况，无法区分是自动接单还是需人工接单，因此，这里再查一次订单状态，以实际为准
                    ChannelOrderStatus channelOrderStatus = getOrderStatus(accessConfig.getTenantId(), channelTypeEnum.getCode(),
                            request.getOrderId(), appId, null);

                    if (channelOrderStatus == null) {
                        log.error("查询京东渠道订单状态映射为空{}", request);
                        return ResultGenerator.genResult(ResultCode.UNDEAL_ERROR).setData(ProjectConstant.NG);
                    }

                    // 查询的订单实际状态不为新建订单时，才发送消息
                    if (channelOrderStatus != ChannelOrderStatus.NEW_ORDER) {
                        return doOrderStatusChange(buildOrderStatusChangeRequest(channelTypeEnum, request, channelOrderStatus), notifyEnum);
                    }
                }
                return status;
            default:
                return ResultGenerator.genResult(ResultCode.CHANNEL_ORDER_STATUS_UNKNOWN).setData(ProjectConstant.NG);
        }
    }

    /**
     * 判断是否为美团渠道的按金额灵活退
     * @param partParam
     * @return
     */
    private boolean checkIsMtAmountRefund(OrderPartRefundRequest partParam){
        if (!ChannelTypeEnum.isMeiTuan(partParam.getChannelType())){
            return false;
        }
        if (!MccConfigUtil.isTenantHasMtAmountRefund(partParam.getTenantId())){
            return false;
        }
        for (RefundSku refundSku : partParam.getProducts()) {
            if (refundSku.getPartRefundType() == ChannelConstant.MT_AMOUNT_REFUND){
                log.info("当前退款消息为金额退");
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否美团渠道的克重退款
     *
     * @param partParam
     * @return
     */
    private boolean checkIsMtWeightRefund(OrderPartRefundRequest partParam) {
        if (!ChannelTypeEnum.isMeiTuan(partParam.getChannelType())) {
            return false;
        }
        for (RefundSku refundSku : partParam.getProducts()) {
            //只要请求中商品数量为0且带有克重退款重量那么一定是克重退款
            if (refundSku.getCount() == 0 && refundSku.getRefundWeight() != 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否美团渠道的克重退款
     *
     * @param products
     * @return
     */
    private boolean checkIsMtWeightRefund(List<RefundSku> products) {
        if(CollectionUtils.isEmpty(products)){
            return false;
        }
        for (RefundSku refundSku : products) {
            //只要请求中商品数量为0且带有克重退款重量那么一定是克重退款
            if (refundSku.getCount() == 0 && refundSku.getRefundWeight() != 0) {
                return true;
            }
        }
        return false;
    }


    /**
     * 用户拒收货物参数配置
     * @param request
     */
    private void setRejectByCustomerRequest(OrderNotifyRequest request){
        request.setRefundId("JDR" + request.getOrderId());
        request.setServiceType(REJECT_BY_CUSTOMER);
        request.setCancelReason(JdLockOrderEnum.REJECT.getName());
    }

    /**
     * 退货退款参数配置
     * @param request
     */
    private void setRefundGoodsRequest(OrderNotifyRequest request){
        request.setServiceType(String.valueOf(ServiceTypeEnum.REFUND_GOODS.getCode()));
        if (request.getRefundApplyTime() != null) {
            request.setRefundApplyTime(String.valueOf(DateUtils.date2TimeStamp(request.getRefundApplyTime(), "yyyy-MM-dd HH:mm:ss")));
        }
    }

    /**
     * 判断是否美团渠道的克重退款-退货退款
     *
     * @param partParam
     * @return
     */
    private boolean checkIsMtWeightRefund(OrderPartRefundGoodsRequest partParam) {
        if (!ChannelTypeEnum.isMeiTuan(partParam.getChannelType())) {
            return false;
        }
        for (RefundSku refundSku : partParam.getProducts()) {
            //只要请求中商品数量为0且带有克重退款重量那么一定是克重退款
            if (refundSku.getCount() == 0 && refundSku.getRefundWeight() != 0) {
                return true;
            }
        }

        return false;
    }

    private BizAfterSaleCreateOrUpdateRequest buildBizAfterSaleCreateOrUpdateRequest(OrderPartRefundGoodsRequest partParam, OrderAfsApplyDTO orderAfsApplyDTO, ChannelTypeEnum channelTypeEnum) {
        ChannelOrderDetailDTO orderDetail = JSON.parseObject(partParam.getOrderJsonString(), ChannelOrderDetailDTO.class);
        BizAfterSaleCreateOrUpdateRequest request = new BizAfterSaleCreateOrUpdateRequest();
        request.setTenantId(partParam.getTenantId());
        request.setChannelId(channelTypeEnum.getCode());
        request.setOrderViewId(partParam.getChannelOrderId());
        request.setAfterSaleId(partParam.getRefundId());
        request.setReason(partParam.getReason());
        request.setSponsor(partParam.getSponsor());
        request.setRefundAmount(partParam.getRefundAmount());
        request.setAppeal(partParam.isIsAppeal());
        request.setAfsApplyType(partParam.getAfsApplyType());
        request.setAfsApplyPattern(AfterSalePatternEnum.WEIGHT.getValue());
        request.setOperateTime(partParam.getTimestamp());
        request.setOperateType(partParam.getOrderPartRefundType());
        request.setOrderSource(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue());
        request.setCommission(Optional.ofNullable(orderAfsApplyDTO).map(OrderAfsApplyDTO::getCommission).orElse(0));
        if (Objects.nonNull(orderDetail)) {
            request.setShopId(orderDetail.getStoreId());

        }
        if (orderAfsApplyDTO != null && orderAfsApplyDTO.getExtend() != null) {
            String preRefundGoodsShippingFee = orderAfsApplyDTO.getExtend().get(PRE_REFUND_GOODS_SHIPPING_FEE);
            if (preRefundGoodsShippingFee != null) {
                request.setPreReturnFreight(Integer.parseInt(preRefundGoodsShippingFee));
            }
            request.setProcessDeadline(Optional.of(orderAfsApplyDTO.getProcessDeadline()).orElse(0L));
        }
        List<RefundSku> products = partParam.getProducts();
        List<BizOrderItemAfterSaleCreateModel> orderItems = Lists.newArrayList();
        for (RefundSku refundSku : products) {
            BizOrderItemAfterSaleCreateModel bizOrderItemAfterSaleCreateModel = new BizOrderItemAfterSaleCreateModel();
            bizOrderItemAfterSaleCreateModel.setCustomerSkuId(refundSku.getSku());
            bizOrderItemAfterSaleCreateModel.setSkuName(refundSku.getSkuName());
            bizOrderItemAfterSaleCreateModel.setQuantity(refundSku.getCount());
            bizOrderItemAfterSaleCreateModel.setRefundWeight(refundSku.getRefundWeight());
            bizOrderItemAfterSaleCreateModel.setSkuCode(refundSku.getUpc());
            bizOrderItemAfterSaleCreateModel.setRefundAmt(refundSku.getSkuRefundAmount());
            bizOrderItemAfterSaleCreateModel.setCustomerSpu(refundSku.getCustomSpuId());
            bizOrderItemAfterSaleCreateModel.setCustomSkuIdVersion(BizOrderItemCreateForMiddleModel.CUSTOM_SKU_VERSION_SPU);
            bizOrderItemAfterSaleCreateModel.setSpec(refundSku.getSpec());
            bizOrderItemAfterSaleCreateModel.setItemId(refundSku.getItemId());
            bizOrderItemAfterSaleCreateModel.setFoodPrice(refundSku.getFoodPrice());
            orderItems.add(bizOrderItemAfterSaleCreateModel);
        }
        request.setProducts(orderItems);
        mergeAfsDetail(request, orderAfsApplyDTO);
        return request;

    }

    private BizAfterSaleCreateOrUpdateRequest buildBizAfterSaleCreateOrUpdateRequest(OrderPartRefundRequest partParam, ChannelTypeEnum channelTypeEnum, OrderAfsApplyDTO orderAfsApplyDTO) {
        ChannelOrderDetailDTO orderDetail = JSON.parseObject(partParam.getOrderJsonString(), ChannelOrderDetailDTO.class);
        BizAfterSaleCreateOrUpdateRequest request = new BizAfterSaleCreateOrUpdateRequest();
        request.setTenantId(partParam.getTenantId());
        request.setChannelId(channelTypeEnum.getCode());
        request.setOrderViewId(partParam.getChannelOrderId());
        request.setAfterSaleId(partParam.getRefundId());
        request.setReason(partParam.getReason());
        request.setSponsor(partParam.getSponsor());
        request.setRefundAmount(partParam.getRefundAmount());
        request.setAppeal(partParam.isIsAppeal());
        request.setAfsApplyType(partParam.getAfsApplyType());
        request.setAfsApplyPattern(AfterSalePatternEnum.WEIGHT.getValue());

        request.setOperateTime(partParam.getTimestamp());
        request.setOperateType(partParam.getOrderPartRefundType());
        request.setOrderSource(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue());
        request.setCommission(Optional.ofNullable(orderAfsApplyDTO).map(OrderAfsApplyDTO::getCommission).orElse(0));
        if (Objects.nonNull(orderDetail)) {
            request.setShopId(orderDetail.getStoreId());

        }
        request.setProcessDeadline(Optional.ofNullable(orderAfsApplyDTO).map(OrderAfsApplyDTO::getProcessDeadline).orElse(0L));
        List<RefundSku> products = partParam.getProducts();

        List<BizOrderItemAfterSaleCreateModel> orderItems = Lists.newArrayList();
        for (RefundSku refundSku : products) {
            BizOrderItemAfterSaleCreateModel bizOrderItemAfterSaleCreateModel = new BizOrderItemAfterSaleCreateModel();
            bizOrderItemAfterSaleCreateModel.setCustomerSkuId(refundSku.getSku());
            bizOrderItemAfterSaleCreateModel.setSkuName(refundSku.getSkuName());
            bizOrderItemAfterSaleCreateModel.setQuantity(refundSku.getCount());
            bizOrderItemAfterSaleCreateModel.setPartialRefundCount(refundSku.getPartialRefundCount());
            bizOrderItemAfterSaleCreateModel.setRefundWeight(refundSku.getRefundWeight());
            bizOrderItemAfterSaleCreateModel.setSkuCode(refundSku.getUpc());
            bizOrderItemAfterSaleCreateModel.setItemId(refundSku.getItemId());
            bizOrderItemAfterSaleCreateModel.setRefundAmt(refundSku.getSkuRefundAmount());
            bizOrderItemAfterSaleCreateModel.setCustomerSpu(refundSku.getCustomSpuId());
            bizOrderItemAfterSaleCreateModel.setCustomSkuIdVersion(BizOrderItemCreateForMiddleModel.CUSTOM_SKU_VERSION_SPU);
            bizOrderItemAfterSaleCreateModel.setSpec(refundSku.getSpec());
            bizOrderItemAfterSaleCreateModel.setFoodPrice(refundSku.getFoodPrice());
            bizOrderItemAfterSaleCreateModel.setBoxAmt(refundSku.getBoxNum() * refundSku.getBoxPrice());
            orderItems.add(bizOrderItemAfterSaleCreateModel);
        }
        request.setProducts(orderItems);
        mergeAfsDetail(request, orderAfsApplyDTO);
        return request;
    }

    private static void mergeAfsDetail(BizAfterSaleCreateOrUpdateRequest freezeRequest, OrderAfsApplyDTO orderAfsApplyDTO){
        if (orderAfsApplyDTO != null){
            freezeRequest.setCommission(orderAfsApplyDTO.getCommission());
            freezeRequest.setPackageAmt(orderAfsApplyDTO.getPlatPackageAmt());
            freezeRequest.setRefundFreight(orderAfsApplyDTO.getFreight());
            freezeRequest.setScoreDeduction(orderAfsApplyDTO.getScoreDeduction());
            freezeRequest.setSelfPickServiceFee(orderAfsApplyDTO.getSelfPickServiceFee());
            freezeRequest.setPlatLogisticsPromotion(orderAfsApplyDTO.getPlatLogisticsPromotion());
            freezeRequest.setPoiLogisticsPromotion(orderAfsApplyDTO.getPoiLogisticsPromotion());
            if (CollectionUtils.isNotEmpty(freezeRequest.getProducts())) {
                freezeRequest.getProducts().forEach(v -> {
                    //针对非整件场景
                    List<RefundProductDTO> matchRefundProductList = orderAfsApplyDTO.getAfsProductList().stream()
                            .filter(afsApply -> Objects.equals(afsApply.getSkuId(), v.getCustomerSkuId()))
                            .collect(Collectors.toList());
                    Optional<RefundProductDTO> matchRefundProduct = matchRefundProductList.stream().findFirst();
                    if (matchRefundProduct.isPresent()) {
                        v.setRefundPlatPromotionAmt(matchRefundProduct.get().getRefundPlatPromotion());
                        v.setRefundPoiPromotionAmt(matchRefundProduct.get().getRefundPoiPromotion());
                        v.setRefundPlatItemPromotion(matchRefundProduct.get().getRefundPlatItemPromotion());
                        v.setRefundPoiItemPromotion(matchRefundProduct.get().getRefundPoiItemPromotion());
                        v.setRefundPlatOrderPromotion(matchRefundProduct.get().getRefundPlatOrderPromotion());
                        v.setRefundPoiOrderPromotion(matchRefundProduct.get().getRefundPoiOrderPromotion());
                        v.setBoxAmt(matchRefundProduct.get().getBoxAmt());
                    }
                });
            }
        }
    }

    private boolean getMTIsAppealByNotifyRequest(OrderNotifyRequest request) {
        if (request.getIsAppeal() == IsAppealEnum.IS_APPEAL.getCode()) {
            return true;
        }
        else {
            //美团渠道：
            //用户发起申诉、用户取消申述，渠道下发is_appeal为1
            //客服同意申诉、客服驳回申诉，渠道下发is_appeal为0，需要结合resType判断
            return ResTypeEnum.MT_AGREE_REFUND.getAbbrev().equals(request.getResType())
                    || ResTypeEnum.MT_DISAGREE_REFUND.getAbbrev().equals(request.getResType())
                    || ResTypeEnum.MT_USER_REFUND_APPEAL.getAbbrev().equals(request.getResType());
        }
    }



    private OrderEvent buildOrderEvent(OrderNotifyRequest request, ChannelNotifyEnum notifyEnum,
                                       Long tenantId, Long shopId) {
        if (isTest(shopId)) {
            OcmsPressureTestOrderEvent orderEvent = new OcmsPressureTestOrderEvent();
            orderEvent.setChannelOrderId(request.getOrderId());
            orderEvent.setChannelType(ChannelTypeEnum.MT_DRUNK_HORSE.getCode());
            orderEvent.setEventId(OrderStatusConverter.dhOrderStatusMapping(notifyEnum));
            orderEvent.setTenantId(tenantId);
            orderEvent.setTimestamp(System.currentTimeMillis());
            orderEvent.setShopId(shopId);
            // 压测订单、则打上压测标识
            Tracer.setTest(true);
            log.info("压测订单: {}", JacksonUtils.toJson(orderEvent));
            return orderEvent;
        } else {
            OcmsOrderEvent orderEvent = new OcmsOrderEvent();
            orderEvent.setChannelOrderId(request.getOrderId());
            orderEvent.setChannelType(ChannelTypeEnum.MT_DRUNK_HORSE.getCode());
            orderEvent.setEventId(OrderStatusConverter.dhOrderStatusMapping(notifyEnum));
            orderEvent.setTenantId(tenantId);
            orderEvent.setTimestamp(System.currentTimeMillis());
            orderEvent.setShopId(shopId);
            if (notifyEnum == ChannelNotifyEnum.DH_ORDER_CANCELED) {
                orderEvent.setExt(ImmutableMap.of("closeReason", StringUtils.defaultString(request.getRefundReason(), StringUtils.EMPTY)));
                orderEvent.setAuditType(DrunkHorseAuditTypeUtils.getAuditTypeFromCancel(request));
            }
            orderEvent.setClientType(request.getOrderSource());
            return orderEvent;
        }
    }

    /**
     * 通过压测门店标识压测订单
     * @param shopId
     * @return
     */
    private boolean isTest(Long shopId) {
        return MccConfigUtil.drunkHorsePressureTestSwitch() && StringUtils.isNotBlank(MccConfigUtil.getDrunkHorsePressureTestPoiConfig())
                && MccConfigUtil.getDrunkHorsePressureTestPoiConfig().contains(String.valueOf(shopId));
    }

}