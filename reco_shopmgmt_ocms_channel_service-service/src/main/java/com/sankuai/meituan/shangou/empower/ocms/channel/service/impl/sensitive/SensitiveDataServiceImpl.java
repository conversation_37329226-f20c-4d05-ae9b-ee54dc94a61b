package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.sensitive;

import org.springframework.stereotype.Service;

import com.meituan.shangou.saas.order.platform.common.sensitive.annotations.SensitiveData;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.SensitiveDataService;

import lombok.extern.slf4j.Slf4j;

/**
 * 敏感数据token化处理类
 *
 * <AUTHOR>
 * @since 8/2/22 4:03 PM
 */
@Slf4j
@Service
public class SensitiveDataServiceImpl implements SensitiveDataService {


    @Override
    @SensitiveData(request = true, response = false)
    public Object encode(Object obj) {
        return obj;
    }
}
