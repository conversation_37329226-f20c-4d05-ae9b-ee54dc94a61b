package com.sankuai.meituan.shangou.empower.ocms.channel.service;


import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetBrandResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.RecommendBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.RecommendBrandResponse;

/**
 * @author:huchangwu
 * @date: 2019/1/20
 * @time: 下午3:36
 */
public interface ChannelBrandService {

    GetBrandResponse batchGetBrand(GetBrandRequest req);

    RecommendBrandResponse getRecommendBrand(RecommendBrandRequest req);

}
