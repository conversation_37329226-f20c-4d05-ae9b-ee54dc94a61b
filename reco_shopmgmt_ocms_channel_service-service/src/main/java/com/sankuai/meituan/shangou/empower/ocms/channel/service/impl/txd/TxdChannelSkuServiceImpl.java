package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostTxdEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSkuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.TxdConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChangeCustomSkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSkuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuSellStatusInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConcurrentUtils;
import com.taobao.api.ApiException;
import com.taobao.api.TaobaoResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/4/18
 */
@Slf4j
@Service("txdChannelSkuService")
public class TxdChannelSkuServiceImpl implements ChannelSkuService {


    // 淘鲜达图片上传rhino动态线程池
    private static ThreadPoolExecutor executor = Rhino.newThreadPool("txd_upload_picture_thread_pool",
                    DefaultThreadPoolProperties.Setter()
                            .withCoreSize(10).withMaxSize(20).withBlockingQueue(new SynchronousQueue<>())
                            .withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy())
                            .withThreadFactory(new ThreadFactoryBuilder().setNameFormat("txd_upload_picture_thread_pool" + "-%d").build()))
            .getExecutor();

    @Autowired
    private TxdBaseService txdBaseService;

    @Override
    public ResultData skuCreate(SkuInfoRequest request) {
        return null;
    }

    @Override
    public ResultData upcCreate(SkuInfoRequest request) {
        return null;
    }

    @Override
    public ResultData updateSku(SkuInfoRequest request) {
        return null;
    }

    @Override
    public ResultData deleteSku(SkuInfoDeleteRequest request) {
        return null;
    }

    @Override
    public ResultData pictureUpload(PictureUploadRequest request) {
        ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        List<Callable<ResultData>> callables = new ArrayList<>(request.getParamList().size());
        for (PictureUploadDTO pictureUploadDTO : request.getParamList()) {
            callables.add(() -> uploadSinglePicture(pictureUploadDTO, request.getBaseInfo()));
        }
        List<ResultData> batchResultList = ConcurrentUtils.concurrentExecute(executor, callables, "淘鲜达上传图片异常");
        if (CollectionUtils.isNotEmpty(batchResultList)) {
            for (ResultData resultSingleData : batchResultList) {
                if (CollectionUtils.isNotEmpty(resultSingleData.getSucData())) {
                    resultData.getSucData().addAll(resultSingleData.getSucData());
                }
                if (CollectionUtils.isNotEmpty(resultSingleData.getErrorData())) {
                    resultData.getErrorData().addAll(resultSingleData.getErrorData());
                }
            }
        }
        return resultData;
    }

    private ResultData uploadSinglePicture(PictureUploadDTO pictureUploadDTO, BaseRequest baseRequest) {
        ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.PICTURE_UPLOAD, baseRequest, TxdConvertUtil.buildPictureUploadRequest(pictureUploadDTO));
            return TxdConvertUtil.genPictureUploadResponse(commonResponse, pictureUploadDTO);
        }
        catch (ApiException e) {
            log.warn("调用淘鲜达更新商品价格接口异常, request {}.", pictureUploadDTO, e);
            ResultErrorSku resultErrorSku = new ResultErrorSku().setSkuId(pictureUploadDTO.getUid()).setErrorCode(TxdConvertUtil.parseTxdErrorCode(e.getErrCode())).setErrorMsg(e.getMessage());
            resultData.getErrorData().add(resultErrorSku);
        }
        catch (Exception e) {
            log.warn("更新淘鲜达商品价格系统异常, request {}.", pictureUploadDTO, e);
            ResultErrorSku resultErrorSku = new ResultErrorSku().setSkuId(pictureUploadDTO.getUid()).setErrorCode(ResultCode.FAIL.getCode()).setErrorMsg(e.getMessage());
            resultData.getErrorData().add(resultErrorSku);
        }
        return resultData;
    }


    @Override
    public PictureUploadResult getPictureUploadStatus(PictureUploadStatusRequest request) {
        return null;
    }

    @Override
    public ResultData updateSkuSellStatus(SkuSellStatusInfoRequest request) {
        return null;
    }

    @Override
    public ResultData updateCustomSkuId(UpdateCustomSkuIdRequest request) {
        return null;
    }

    @Override
    public BatchGetSkuInfoResponse batchGetSkuInfo(BatchGetSkuInfoRequest request) {
        return null;
    }

    @Override
    public GetSkuInfoResponse getSkuInfo(GetSkuInfoRequest request) {
        return null;
    }

    @Override
    public GetCategoryResponse batchGetChannelStoreCategoryInfo(CatRequest request) {
        return null;
    }

    @Override
    public ResultData changeCustomSkuId(ChangeCustomSkuIdRequest request) {
        return null;
    }
}
