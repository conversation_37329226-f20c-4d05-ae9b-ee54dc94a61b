package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Maps;
import com.meituan.mtrace.Tracer;
import com.meituan.mtrace.instrument.util.JsonUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelOrderDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelOrderDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtErrorStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mock.MockOrderChannelUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelOrderServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ChannelOrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.HealthChannelOrderSensitiveHelper;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/***
 * 医药处理订单相关逻辑，旧的是直接使用的歪马的，走的零售接口，新的为了兼容isv方式，使用赋能的
 * author : <EMAIL>
 * data : 2020/8/6
 * time : 下午1:56
 **/
@Service("mtMedicineChannelOrderService")
public class MtMedicineChannelOrderServiceImpl extends MtBrandChannelOrderServiceImpl {


    @Resource
    private CommonLogger log;

    @Resource
    private MockOrderChannelUtil mockOrderChannelUtil;

    @Resource
    private HealthChannelOrderSensitiveHelper healthChannelOrderSensitiveHelper;

    @Value("${mt.url.base}" + "${mt.url.orderDetail}")
    private String orderDetailUrl;

    @Override
    public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
        GetChannelOrderDetailResult orderDetailResult = new GetChannelOrderDetailResult();
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
            .setTenantId(request.getTenantId()).setAppId(request.getAppId());
        addStoreId2BaseRequest(request.getSotreId(), baseRequest);
        Map<String, String> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
        Map<String, Object> resultMap;
        if (Tracer.isTest() && MccConfigUtil.orderPressTestEnable()) {
            resultMap = mockOrderChannelUtil.mockMtOrderDetail(request);
        } else {
            resultMap = mtBrandChannelGateService.sendGet(orderDetailUrl, null, baseRequest, bizParam);
        }
        log.info("MtChannelOrderServiceImpl.getChannelOrderDetail, request:{}, resultMap:{}", request, resultMap);
        if (CollectionUtils.isEmpty(resultMap)) {
            return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单详情失败"));
        }
        return orderDetailAnalysis(baseRequest, resultMap, orderDetailResult);
    }

    private GetChannelOrderDetailResult orderDetailAnalysis(BaseRequest baseRequest, Map<String, Object> result,
        GetChannelOrderDetailResult orderDetailResult) {
        // 返回数据反序列化
        ChannelOrderDetailResponse orderDetailResponse = JSON.parseObject(JSON.toJSONString(result),
            ChannelOrderDetailResponse.class);
        if (Objects.isNull(orderDetailResponse) || StringUtils.isBlank(orderDetailResponse.getData())) {
            return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "订单详情数据反序列化失败"));
        }
        // 返回状态校验
        if (ProjectConstant.NG.equals(orderDetailResponse.getData())) {
            MtErrorStatus error = orderDetailResponse.getError();
            if (Objects.isNull(error)) {
                return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST));
            }
            return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST, error.getMsg()));
        }
        long tenantId = baseRequest.getTenantId();
        int channelId = baseRequest.getChannelId();

        // 20240522 敏感字段治理重构
        String responseData = orderDetailResponse.getData();
        ChannelOrderDetailDTO channelOrderDetailDTO = null;

        // 判断是否需要对敏感信息进行解码
        boolean decodeWhole = healthChannelOrderSensitiveHelper.yYneedDecodeWhole();
//        boolean checkYyOrder = healthChannelOrderSensitiveHelper.checkYyOrder(channelId, tenantId);
        if (decodeWhole) {
            channelOrderDetailDTO = getChannelOrderDetailDTOWithYySensitive(baseRequest, tenantId, channelId,
                responseData);
            // 如果解码后的DTO为空，或者 不需要解码，则直接解析原始数据
            if (channelOrderDetailDTO == null) {
                channelOrderDetailDTO = parseOrderForm(tenantId, channelId, responseData);
            }
        } else {
            channelOrderDetailDTO = parseOrderForm(tenantId, channelId, responseData);
        }

        return orderDetailResult.setStatus(ResultGenerator.genSuccessResult())
            .setChannelOrderDetail(channelOrderDetailDTO);
    }

    private ChannelOrderDetailDTO getChannelOrderDetailDTOWithYySensitive(BaseRequest baseRequest, long tenantId,
        int channelId, String responseData) {
        ChannelOrderDetail channelOrderDetail = convertResponseData2Detail(responseData);
        if (ObjectUtils.isEmpty(channelOrderDetail)) {
            return null;
        }
        // 医药敏感字段
        dealSensitiveInfo(baseRequest, channelOrderDetail);
        if (ObjectUtils.isNotEmpty(channelOrderDetail)) {
            return orderDetail2ChannelOrderDetailDto(tenantId, channelId, channelOrderDetail);
        } else {
            log.error("医药敏感字段解密之前非空 {}，解密之后空 {}", responseData, JsonUtil.serialize(channelOrderDetail));
            return null;
        }
    }

    private ChannelOrderDetailDTO orderDetail2ChannelOrderDetailDto(long tenantId, int channelId,
        ChannelOrderDetail channelOrderDetail) {
        try {
            return doOrderDetail2ChannelOrderDetailDto(tenantId, channelId, channelOrderDetail);
        } catch (Exception e) {
            log.error("订单映射异常", e);
            throw new IllegalArgumentException("订单映射异常");
        }
    }

    private void dealSensitiveInfo(BaseRequest baseRequest, ChannelOrderDetail channelOrderDetail) {
        try {
            healthChannelOrderSensitiveHelper.doSensitiveInfo(baseRequest, mtBrandChannelGateService,
                channelOrderDetail);
        } catch (Exception e) {
            log.error("处理医药敏感字段异常 {}, {}, {}", JsonUtil.serialize(baseRequest), JsonUtil.serialize(channelOrderDetail),
                    e.getMessage(), e);
        }
    }

    private ChannelOrderDetail convertResponseData2Detail(String orderDetailForm) {
        ChannelOrderDetail channelOrderDetail = getChannelOrderDetailFromStr(orderDetailForm);
        if (Objects.isNull(channelOrderDetail)) {
            return null;
        }
        return channelOrderDetail;
    }

}
