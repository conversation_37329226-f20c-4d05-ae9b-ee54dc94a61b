package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.ali.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class AliDrugTraceQueryDrugInfoResponse extends AliResponse {

    /**
     * 最外层结果
     */
    @JSONField(name = "result")
    private ResultModel result;

    /**
     * 码对象
     *
     * <AUTHOR> auto create
     * @since 1.0, null
     */
    @Data
    public static class CodeStatusTypeDto {
        /**
         * 码状态（A:已激活;I:已核注;O:已核销;C:已注销;S:已售出;E:码不存在）
         */
        @JSONField(name = "code_status")
        private String codeStatus;

    }

    /**
     * 企业信息对象
     *
     * <AUTHOR> auto create
     * @since 1.0, null
     */
    @Data
    public static class PUserEntDto {

        /**
         * 企业名称
         */
        @JSONField(name = "ent_name")
        private String entName;
        /**
         * 企业id
         */
        @JSONField(name = "ref_ent_id")
        private String refEntId;
    }

    /**
     * 药品基本信息对象
     *
     * <AUTHOR> auto create
     * @since 1.0, null
     */
    @Data
    public static class DrugEntBaseDto {

        /**
         * 批准文号
         */
        @JSONField(name = "approval_licence_no")
        private String approvalLicenceNo;
        /**
         * 药品id
         */
        @JSONField(name = "drug_ent_base_info_id")
        private String drugEntBaseInfoId;
        /**
         * 有效期
         */
        @JSONField(name = "exprie")
        private String exprie;
        /**
         * 药品名称
         */
        @JSONField(name = "physic_name")
        private String physicName;
        /**
         * 药品类型描述
         */
        @JSONField(name = "physic_type_desc")
        private String physicTypeDesc;
        /**
         * 包装规格
         */
        @JSONField(name = "pkg_spec_crit")
        private String pkgSpecCrit;
        /**
         * 制剂规格
         */
        @JSONField(name = "prepn_spec")
        private String prepnSpec;
        /**
         * 剂型描述
         */
        @JSONField(name = "prepn_type_desc")
        private String prepnTypeDesc;
    }

    /**
     * 生产信息集合
     *
     * <AUTHOR> auto create
     * @since 1.0, null
     */
    @Data
    public static class ProduceInfoDto {

        /**
         * 批次号
         */
        @JSONField(name = "batch_no")
        private String batchNo;
        /**
         * 有效期至
         */
        @JSONField(name = "expire_date")
        private String expireDate;
        /**
         * 最小包装数量
         */
        @JSONField(name = "pkg_amount")
        private String pkgAmount;
        /**
         * 生产日期
         */
        @JSONField(name = "produce_date_str")
        private String produceDateStr;
    }

    /**
     * 码生产信息对象
     *
     * <AUTHOR> auto create
     * @since 1.0, null
     */
    @Data
    public static class CodeProduceInfoDto {

        /**
         * 生产信息集合
         */
        @JSONField(name = "produce_info_list")
        private Map<String, List<ProduceInfoDto>> produceInfoList;
    }

    /**
     * 内层大对象
     *
     * <AUTHOR> auto create
     * @since 1.0, null
     */
    @Data
    public static class CodeFullInfoDto {

        /**
         * 追溯码
         */
        @JSONField(name = "code")
        private String code;
        /**
         * 码生产信息对象
         */
        @JSONField(name = "code_produce_info_d_t_o")
        private CodeProduceInfoDto codeProduceInfoDTO;
        /**
         * 码对象
         */
        @JSONField(name = "code_status_type_d_t_o")
        private CodeStatusTypeDto codeStatusTypeDTO;
        /**
         * 药品基本信息对象
         */
        @JSONField(name = "drug_ent_base_d_t_o")
        private DrugEntBaseDto drugEntBaseDTO;
        /**
         * 企业信息对象
         */
        @JSONField(name = "p_user_ent_d_t_o")
        private PUserEntDto pUserEntDTO;
        /**
         * 码等级【1代表最小码 如：申请的包装比例是1:5:10, 对应的码等级就是3、2、1, 代表大码、中码、小码】
         */
        @JSONField(name = "package_level")
        private String packageLevel;
    }

    /**
     * 最外层结果
     *
     * <AUTHOR> auto create
     * @since 1.0, null
     */
    @Data
    public static class ResultModel {

        /**
         * 内层大对象
         */
        @JSONField(name = "models")
        private ModelWrapper models;
        /**
         * 消息码
         */
        @JSONField(name = "msg_code")
        private String msgCode;
        /**
         * 消息提示内容
         */
        @JSONField(name = "msg_info")
        private String msgInfo;
        /**
         * 查询成功失败标记
         */
        @JSONField(name = "response_success")
        private Boolean responseSuccess;
    }

    @Data
    public static class ModelWrapper {
        @JSONField(name = "code_full_info_dto")
        private List<CodeFullInfoDto> dataList;
    }

}
