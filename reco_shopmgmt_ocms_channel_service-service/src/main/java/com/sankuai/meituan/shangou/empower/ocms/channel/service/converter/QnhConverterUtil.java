package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.QnhProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhChannelOrderAbnormalDataDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhChannelOrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhChannelOrderPartRefundGoodsDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhChannelOrderPromotionDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.QnhChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.FavoritesStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.TenantCancelOrderReasonEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.AfterSaleRecordStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderAllRefundType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Slf4j
public final class QnhConverterUtil {
    private QnhConverterUtil() {
    }

    /**
     * 转换订单详情
     *
     * @param qnhOrderDetailDTO
     * @return
     */
    public static ChannelOrderDetailDTO qnhChannelOrderDetail2ChannelDetail(QnhChannelOrderDetailDTO qnhOrderDetailDTO) {
        if (qnhOrderDetailDTO == null || qnhOrderDetailDTO.getHead() == null) {
            return null;
        }
        ChannelOrderDetailDTO orderDetailDTO = new ChannelOrderDetailDTO();
        Map<String,Object> extMap=new HashMap<>();
        orderDetailDTO.setChannelOrderId(qnhOrderDetailDTO.getHead().getChannel_sheetno());
        orderDetailDTO.setChannelExtraOrderId(qnhOrderDetailDTO.getHead().getChannel_sheetno());
        orderDetailDTO.setActualPayAmt(MoneyUtils.yuanToFen(qnhOrderDetailDTO.getHead().getPayable_value()));
        Double originalAmt = qnhOrderDetailDTO.getHead().getTotal_disc_value()
                + qnhOrderDetailDTO.getHead().getTotal_dp_disc()
                + qnhOrderDetailDTO.getHead().getPayable_value();
        orderDetailDTO.setOriginalAmt(MoneyUtils.yuanToFen(originalAmt));
        if (qnhOrderDetailDTO.getHead().getTotal_sale_value() != null) {
            orderDetailDTO.setBizReceiveAmt(MoneyUtils.yuanToFen(qnhOrderDetailDTO.getHead().getTotal_sale_value()));
        }
        orderDetailDTO.setFreight(MoneyUtils.yuanToFen(qnhOrderDetailDTO.getHead().getPayable_logistics_value()));
        orderDetailDTO.setCreateTime(ConverterUtils.secondsToMillis(qnhOrderDetailDTO.getHead().getOrder_time()));
        orderDetailDTO.setIsNeedInvoice(false);
        orderDetailDTO.setPayType(qnhPayType(qnhOrderDetailDTO.getHead().getPay_type()));
        orderDetailDTO.setOrderSerialNumber(qnhOrderDetailDTO.getHead().getOuter_small_id());
        orderDetailDTO.setPackageAmt(MoneyUtils.yuanToFen(qnhOrderDetailDTO.getHead().getPackage_fee()));
        int totalDiscount=MoneyUtils.yuanToFen(qnhOrderDetailDTO.getHead().getTotal_disc_value());
        if(qnhOrderDetailDTO.getHead().getLogistics_disc()!=null){
            int logisticsDisc=MoneyUtils.yuanToFen(qnhOrderDetailDTO.getHead().getLogistics_disc());
            totalDiscount=Math.max(0,totalDiscount+logisticsDisc);
        }
        orderDetailDTO.setTotalDiscount(totalDiscount);
        orderDetailDTO.setComment(qnhOrderDetailDTO.getHead().getNote());
        orderDetailDTO.setChannelStoreName(qnhOrderDetailDTO.getHead().getRegion_name());
        orderDetailDTO.setStatus(OrderStatusConverter.qnhOrderStatusMapping(Integer.parseInt(qnhOrderDetailDTO.getHead().getOrder_status())));
        orderDetailDTO.setChannelOrderStatus(orderDetailDTO.getStatus());
        orderDetailDTO.setCompletedTime(ConverterUtils.secondsToMillis(qnhOrderDetailDTO.getHead().getComplete_time()));
        orderDetailDTO.setConfirmTime(ConverterUtils.secondsToMillis(qnhOrderDetailDTO.getHead().getConfirm_time()));
        orderDetailDTO.setIsBooking(qnhOrderDetailDTO.getHead().getPre_order());
        orderDetailDTO.setChannelId(QnhChannelTypeEnum.valueOfEnum(qnhOrderDetailDTO.getHead().getChannel_keyword()).getCode());
        orderDetailDTO.setLogisticFetchTime(ConverterUtils.secondsToMillis(qnhOrderDetailDTO.getHead().getStart_delivery_time()));
        orderDetailDTO.setLogisticCompletedTime(ConverterUtils.secondsToMillis(qnhOrderDetailDTO.getHead().getDelivery_time()));
        orderDetailDTO.setFavoritesStatus(convertToFavorites(qnhOrderDetailDTO.getHead().getIs_favorites()));
        OrderDeliveryDetailDTO deliveryDetailDTO = new OrderDeliveryDetailDTO();
        deliveryDetailDTO.setUserName(qnhOrderDetailDTO.getHead().getName());
        deliveryDetailDTO.setUserAddress(qnhOrderDetailDTO.getHead().getAddress());
        deliveryDetailDTO.setUserPhone(qnhOrderDetailDTO.getHead().getMobile());
        deliveryDetailDTO.setUserPhoneIsValid(!StringUtils.contains(qnhOrderDetailDTO.getHead().getMobile(), "*"));
        deliveryDetailDTO.setArrivalTime(ConverterUtils.secondsToMillis(qnhOrderDetailDTO.getHead().getGetset_start_time()));
        deliveryDetailDTO.setArrivalEndTime(ConverterUtils.secondsToMillis(qnhOrderDetailDTO.getHead().getGetset_end_time()));
        if(StringUtils.isNotEmpty(qnhOrderDetailDTO.getHead().getApply_reason())){
            extMap.put("cancelReason",qnhOrderDetailDTO.getHead().getApply_reason());
        }
        if (ObjectUtils.isNotEmpty(qnhOrderDetailDTO.getHead().getInvoiceFlag())) {
            extMap.put("invoiceFlag", qnhOrderDetailDTO.getHead().getInvoiceFlag());
        }

        double longitude = 0;
        double latitude = 0;
        if(StringUtils.isNotEmpty(qnhOrderDetailDTO.getHead().getDeliveryGeo())){
            String[] geo = qnhOrderDetailDTO.getHead().getDeliveryGeo().split(",");
            try{
                longitude = Double.parseDouble(geo[0]);
                latitude = Double.parseDouble(geo[1]);
            }catch (Exception e){
                log.info("convert DeliveryGeo error。geo:{}",qnhOrderDetailDTO.getHead().getDeliveryGeo());
            }
            if ("1".equals(qnhOrderDetailDTO.getHead().getDeliveryType())) {
                CoordinateTransformUtil.CoordinatePoint point = CoordinateTransformUtil.bd09ToGcj02(longitude, latitude);
                longitude = point.getLongitude();
                latitude = point.getLatitude();
            }
        }
        deliveryDetailDTO.setLatitude(latitude);
        deliveryDetailDTO.setLongitude(longitude);
        deliveryDetailDTO.setIsSelfDelivery(qnhIsSelfDelivery(qnhOrderDetailDTO.getHead().getLogistics_mode_keyword()));
        deliveryDetailDTO.setDeliveryMethod(qnhDeliveryMethod(qnhOrderDetailDTO.getHead().getLogistics_mode_keyword()));
        orderDetailDTO.setDeliveryDetail(deliveryDetailDTO);
        List<OrderProductDetailDTO> productDetailDTOList = convertToProductDetailList(qnhOrderDetailDTO);
        orderDetailDTO.setSkuDetails(productDetailDTOList);

        if (qnhOrderDetailDTO.getHead().getInvoice() != null) {
            orderDetailDTO.setIsNeedInvoice(qnhOrderDetailDTO.getHead().getInvoice().getNeed());
            if (orderDetailDTO.isIsNeedInvoice()) {
                OrderInvoiceDetailDTO invoiceDetailDTO = new OrderInvoiceDetailDTO();
                invoiceDetailDTO.setInvoiceTitle(qnhOrderDetailDTO.getHead().getInvoice().getTitle());
                invoiceDetailDTO.setTaxNo(qnhOrderDetailDTO.getHead().getInvoice().getDuty_no());
                invoiceDetailDTO.setInvoiceType(qnhInvoiceType(qnhOrderDetailDTO.getHead().getInvoice().getType()));
                orderDetailDTO.setInvoiceDetail(invoiceDetailDTO);
            }
        }
        if(MapUtils.isNotEmpty(extMap)){
            orderDetailDTO.setExtData(JacksonUtils.simpleSerialize(extMap));
        }

        try {
            orderDetailDTO.getDeliveryDetail().setUserPrivacyPhone(getPrivacyPhone(deliveryDetailDTO.getUserPhone()));
        }catch (Exception e){
            log.error("qnhChannelOrderDetail2ChannelDetail privacy phone error orderDetailDTO:{}",orderDetailDTO,e);
        }

        if(MccConfigUtil.getQnhOrderActivityGiftSwitch()){
            try {
                orderDetailDTO.setActivities(convertToOrderActivityList(qnhOrderDetailDTO));
                orderDetailDTO.setSkuSharedActivities(convertToSkuActivity(qnhOrderDetailDTO));
            }catch (Exception e){
                log.error("qnhChannelOrderDetail2ChannelDetail activity error orderDetailDTO:{}",orderDetailDTO,e);
            }
        }

        return orderDetailDTO;
    }

    public static List<OrderDiscountDetailDTO> convertToOrderActivityList(QnhChannelOrderDetailDTO qnhOrderDetailDTO){
        if(CollectionUtils.isEmpty(qnhOrderDetailDTO.getHead().getPromotion())){
            return null;
        }
        List<OrderDiscountDetailDTO> activityList=new ArrayList<>();
        qnhOrderDetailDTO.getHead().getPromotion().forEach(promotion->{

            if(CollectionUtils.isEmpty(promotion.getGift())){
                OrderDiscountDetailDTO activity=new OrderDiscountDetailDTO();
                activity.setActivityId(promotion.getActivity_id());
                activity.setActDiscount(MoneyUtils.yuanToFen(promotion.getDisc_value()));
                activity.setType(promotion.getActivity_type_code());
                activity.setRemark(promotion.getActivity_type_name());
                activity.setChannelCharge(MoneyUtils.yuanToFen(promotion.getPlatform_disc()));
                activity.setBizCharge(MoneyUtils.yuanToFen(promotion.getBusiness_disc()));
                activityList.add(activity);
            }else {
                promotion.getGift().forEach(gift->{
                    OrderDiscountDetailDTO activity=new OrderDiscountDetailDTO();
                    activity.setRemark(QnhProjectConstant.ACTIVITY_GIFT_DESC);
                    ChannelGiftInfo giftInfo=new ChannelGiftInfo();
                    giftInfo.setName(gift.getItem_name());
                    giftInfo.setQuantity(gift.getItem_num() == null ? 0:gift.getItem_num().intValue());
                    giftInfo.setThirdPartSkuId(gift.getItem_code());
                    giftInfo.setThirdPartSpuId(gift.getSpu_code());
                    activity.setGiftInfo(giftInfo);
                    activityList.add(activity);
                });
            }
        });
        return activityList;
    }

    public static List<GoodsActivityDetailDTO> convertToSkuActivity(QnhChannelOrderDetailDTO qnhOrderDetailDTO){
        if(CollectionUtils.isEmpty(qnhOrderDetailDTO.getItems())){
            return null;
        }
        List<GoodsActivityDetailDTO> goodsActivityList=new ArrayList<>();
        qnhOrderDetailDTO.getItems().forEach(item->{
            if(CollectionUtils.isEmpty(item.getPromotion())){
                return;
            }
            GoodsActivityDetailDTO goodsActivity=new GoodsActivityDetailDTO();
            goodsActivity.setOriginPrice(MoneyUtils.yuanToFen(item.getOriginal_price()));
            goodsActivity.setTotalDiscount(MoneyUtils.yuanToFen(item.getDisc_bt())+MoneyUtils.yuanToFen(item.getDisc_bt_sj()));
            goodsActivity.setActivityPrice(Math.floorDiv(MoneyUtils.yuanToFen(item.getSale_value()),item.getSale_qty().longValue()));
            goodsActivity.setThirdPartItemId(item.getId());
            goodsActivity.setThirdPartSkuId(item.getItem_code());
            goodsActivity.setThirdPartSpuId(item.getSpu_code());
            long count=0;
            long channelCost=0;
            long tenantCost=0;
            List<GoodsSharedActivityItem> goodActivityDetail=new ArrayList<>();
            for (QnhChannelOrderPromotionDTO promotion : item.getPromotion()){
                GoodsSharedActivityItem goodsActivityItem=new GoodsSharedActivityItem();
                goodsActivityItem.setActivityId(promotion.getActivity_id());
                goodsActivityItem.setChannelPromotionType(promotion.getActivity_type_code());
                goodsActivityItem.setPromotionRemark(promotion.getActivity_type_name());
                goodsActivityItem.setPromotionCount(promotion.getHd_item_num()==null ? 0:promotion.getHd_item_num().intValue());
                goodsActivityItem.setChannelCost(MoneyUtils.yuanToFen(promotion.getPlatform_disc()));
                goodsActivityItem.setTenantCost(MoneyUtils.yuanToFen(promotion.getBusiness_disc()));
                goodActivityDetail.add(goodsActivityItem);
                count+=goodsActivityItem.getPromotionCount();
                channelCost+=goodsActivityItem.getChannelCost();
                tenantCost+=goodsActivityItem.getTenantCost();
            }
            goodsActivity.setSkuCount(count);
            goodsActivity.setChannelCost(channelCost);
            goodsActivity.setTenantCost(tenantCost);
            goodsActivity.setGoodActivityDetail(goodActivityDetail);
            goodsActivityList.add(goodsActivity);
        });
        return goodsActivityList;
    }

    public static String getPrivacyPhone(String phone){
        if(StringUtils.isEmpty(phone)){
            return "";
        }
        boolean recipientPhoneIsReal = PhoneNumberUtils.isValidElevenNumberMobileNumber(phone);
        if(recipientPhoneIsReal){
            return PhoneNumberUtils.transferToPrivacyPhone(phone);
        }
        return phone;
    }

    private static FavoritesStatusEnum convertToFavorites(Boolean favorites){
        if(favorites==null){
            return FavoritesStatusEnum.UNKNOWN;
        }else if(favorites){
            return FavoritesStatusEnum.ALREADY;
        }
        return FavoritesStatusEnum.NOT;
    }

    /**
     * 转换订单商品详情
     *
     * @param qnhOrderDetailDTO
     * @return
     */
    private static List<OrderProductDetailDTO> convertToProductDetailList(QnhChannelOrderDetailDTO qnhOrderDetailDTO) {
        if (CollectionUtils.isEmpty(qnhOrderDetailDTO.getItems())) {
            return Collections.emptyList();
        }
        List<OrderProductDetailDTO> detailDTOList = new ArrayList<>();
        qnhOrderDetailDTO.getItems().forEach(item -> {
            if(MccConfigUtil.getQnhOrderActivityGiftSwitch()){
                if(item.getGift()){
                    return;
                }
            }
            OrderProductDetailDTO detailDTO = new OrderProductDetailDTO();
            detailDTO.setThirdPartItemId(item.getId());
            detailDTO.setThirdPartSkuId(item.getItem_code());
            detailDTO.setSkuName(item.getItem_name());
            detailDTO.setSpecification(item.getSpec() == null ? "": item.getSpec());
            detailDTO.setThirdPartSpuId(item.getSpu_code());
            detailDTO.setQuantity(item.getSale_qty().intValue());
            detailDTO.setSalePrice(MoneyUtils.yuanToFen(item.getSale_price()));
            detailDTO.setOriginalPrice(MoneyUtils.yuanToFen(item.getOriginal_price()));
            detailDTO.setPackageCount(item.getBox_num().intValue());
            detailDTO.setPackageFee(MoneyUtils.yuanToFen(item.getBox_value()));
            detailDTO.setPackagePrice(MoneyUtils.yuanToFen(item.getBox_price()));
            detailDTO.setUpcCode(item.getBarcode());
            detailDTO.setSkuProperty(item.getSale_property() == null ? "":item.getSale_property());
            detailDTO.setSkuPicUrls(item.getImage());
            detailDTO.setChannelCost(MoneyUtils.yuanToFen(item.getDisc_bt()));
            detailDTO.setTenantCost(MoneyUtils.yuanToFen(item.getDisc_bt_sj()));
            int totalDiscount=MoneyUtils.yuanToFen(item.getDisc_value())+MoneyUtils.yuanToFen(item.getDisc_value_pt())+detailDTO.getChannelCost()+detailDTO.getTenantCost();
            detailDTO.setTotalDiscount(totalDiscount);
            if (item.getWeight() != null) {
                detailDTO.setWeight(item.getWeight().longValue());
            }
            detailDTOList.add(detailDTO);
        });
        return detailDTOList;
    }

    /**
     * 订单退款详情
     *
     * @param qnhOrderDetailDTO
     * @return
     */
    public static OrderAfsApplyDTO convertToAfsApply(QnhChannelOrderDetailDTO qnhOrderDetailDTO) {
        if (qnhOrderDetailDTO == null || qnhOrderDetailDTO.getHead() == null) {
            return null;
        }
        OrderAfsApplyDTO afsApplyDTO = new OrderAfsApplyDTO();
        afsApplyDTO.setChannelOrderId(qnhOrderDetailDTO.getHead().getChannel_sheetno());
        afsApplyDTO.setChannelType(QnhChannelTypeEnum.valueOfEnum(qnhOrderDetailDTO.getHead().getChannel_keyword()).getCode());
        afsApplyDTO.setApplyReason(qnhOrderDetailDTO.getHead().getReturn_apply_reason());
//        int refundPrice=MoneyUtils.yuanToFen(qnhOrderDetailDTO.getHead().getTotal_item_value());
//        refundPrice=Math.max(0,refundPrice-MoneyUtils.yuanToFen(qnhOrderDetailDTO.getHead().getTotal_disc_value()));
        afsApplyDTO.setRefundPrice(MoneyUtils.yuanToFen(qnhOrderDetailDTO.getHead().getTotal_sale_value()));
        afsApplyDTO.setRefundType(Integer.parseInt(qnhOrderDetailDTO.getHead().getPart_all()));
        afsApplyDTO.setRefundApplyTime(ConverterUtils.secondsToMillis(qnhOrderDetailDTO.getHead().getOrder_time()));
        afsApplyDTO.setAfterSaleId(qnhOrderDetailDTO.getHead().getSheetno());
        afsApplyDTO.setAfterSaleStatus(qnhAfsStatus(qnhOrderDetailDTO.getHead().getOrder_status()));
        afsApplyDTO.setAfterSaleRecordStatus(qnhAfsRecordStatus(qnhOrderDetailDTO.getHead().getOrder_status()));
        afsApplyDTO.setAfsProductList(convertToRefundProductList(qnhOrderDetailDTO));
        afsApplyDTO.setResReason(qnhOrderDetailDTO.getHead().getReturn_audit_remark());
        afsApplyDTO.setRefundImgUrl(qnhOrderDetailDTO.getHead().getReturn_apply_url());
        if(StringUtils.isEmpty(afsApplyDTO.getResReason())){
            afsApplyDTO.setResReason("");
        }
        return afsApplyDTO;
    }

    /**
     * 订单退款商品详情
     *
     * @param qnhOrderDetailDTO
     * @return
     */
    public static List<RefundProductDTO> convertToRefundProductList(QnhChannelOrderDetailDTO qnhOrderDetailDTO) {
        if (CollectionUtils.isEmpty(qnhOrderDetailDTO.getItems())) {
            return Collections.emptyList();
        }
        List<RefundProductDTO> refundProductList = new ArrayList<>();
        qnhOrderDetailDTO.getItems().forEach(item -> {
            RefundProductDTO productDTO = new RefundProductDTO();
            productDTO.setSkuName(item.getItem_name());
            productDTO.setThirdPartSkuId(item.getItem_code());
            productDTO.setThirdPartSpuId(item.getSpu_code());
            productDTO.setThirdPartItemId(item.getId());
            productDTO.setCount(item.getSale_qty().intValue());
            productDTO.setRefundWeight(item.getReturn_weight()==null?0:item.getReturn_weight());
            int refundAmount=MoneyUtils.yuanToFen(item.getSale_value());
            if(item.getDisc_value()!=null){
                refundAmount=Math.max(0,refundAmount-MoneyUtils.yuanToFen(item.getDisc_value()));
            }
            if(item.getDisc_value_pt()!=null){
                refundAmount=Math.max(0,refundAmount-MoneyUtils.yuanToFen(item.getDisc_value_pt()));
            }
            if(item.getBox_value()!=null){
                refundAmount=refundAmount+MoneyUtils.yuanToFen(item.getBox_value());
            }
            productDTO.setSkuRefundAmount(refundAmount);
            productDTO.setFoodPrice(MoneyUtils.yuanToFen(item.getOriginal_price()));
            productDTO.setBoxPrice(MoneyUtils.yuanToFen(item.getBox_value()));
            productDTO.setBoxNum(item.getBox_num().intValue());
            productDTO.setSpec(item.getSpec() == null ? "":item.getSpec());
            refundProductList.add(productDTO);
        });
        return refundProductList;
    }

    public static List<PartRefundGoodDetailDTO> convertPartRefundGoodDetailList(List<QnhChannelOrderPartRefundGoodsDetailDTO> partRefundGoodsDetailList) {
        if (CollectionUtils.isEmpty(partRefundGoodsDetailList)) {
            return Collections.emptyList();
        }
        List<PartRefundGoodDetailDTO> goodDetailDTOList = new ArrayList<>();
        partRefundGoodsDetailList.forEach(goods -> {
            PartRefundGoodDetailDTO detailDTO = new PartRefundGoodDetailDTO();
            detailDTO.setCustomSpu(goods.getOut_key());
            detailDTO.setCount(goods.getAllow_ret_qty().intValue());
            detailDTO.setSkuName(goods.getItem_name());
            detailDTO.setThirdPartSkuId(goods.getItem_code());
            detailDTO.setThirdPartItemId(goods.getId());
            detailDTO.setRefundPrice(MoneyUtils.yuanToFen(goods.getSale_price()));
            goodDetailDTOList.add(detailDTO);
        });
        return goodDetailDTOList;
    }

    public static List<WeightPartRefundGoodsDTO> convertWeightPartRefundGoodDetailList(List<QnhChannelOrderPartRefundGoodsDetailDTO> partRefundGoodsDetailList) {
        if (CollectionUtils.isEmpty(partRefundGoodsDetailList)) {
            return Collections.emptyList();
        }
        List<WeightPartRefundGoodsDTO> goodDetailDTOList = new ArrayList<>();
        partRefundGoodsDetailList.forEach(goods -> {
            WeightPartRefundGoodsDTO detailDTO = new WeightPartRefundGoodsDTO();
            detailDTO.setAllow_ret_qty(goods.getAllow_ret_qty());
            detailDTO.setItem_name(goods.getItem_name());
            detailDTO.setThirdPartSkuId(goods.getItem_code());
            detailDTO.setWeight(goods.getWeight());
            detailDTO.setBarcode(goods.getBarcode());
            detailDTO.setThirdPartItemId(goods.getId());
            detailDTO.setSale_qty(MoneyUtils.yuanToFen(goods.getSale_qty()));
            detailDTO.setOriginal_price(MoneyUtils.yuanToFen(goods.getOriginal_price()));
            detailDTO.setSale_price(MoneyUtils.yuanToFen(goods.getSale_price()));
            goodDetailDTOList.add(detailDTO);
        });
        return goodDetailDTOList;
    }

    public static List<AbnormalOrderWithChannel> convertAbOrderWithChannel(QnhChannelOrderAbnormalDataDetailDTO dataDetailDTO) {
        if (CollectionUtils.isEmpty(dataDetailDTO.getRows())) {
            return Collections.emptyList();
        }
        List<AbnormalOrderWithChannel> orderWithChannels = new ArrayList<>();
        dataDetailDTO.getRows().forEach(rows -> {
            AbnormalOrderWithChannel orderWithChannel = new AbnormalOrderWithChannel();
            orderWithChannel.setChannelOrderId(rows.getChannel_sheetno());
            orderWithChannel.setChannelId(QnhChannelTypeEnum.valueOfEnum(rows.getChannel_keyword()).getCode());
            orderWithChannels.add(orderWithChannel);
        });
        return orderWithChannels;
    }

    public static List<AbnormalReturnOrderInfo> convertAbReturnOrder(QnhChannelOrderAbnormalDataDetailDTO dataDetailDTO) {
        if (CollectionUtils.isEmpty(dataDetailDTO.getRows())) {
            return Collections.emptyList();
        }
        List<AbnormalReturnOrderInfo> returnOrderWithChannels = new ArrayList<>();
        dataDetailDTO.getRows().forEach(rows -> {
            AbnormalReturnOrderInfo returnOrderInfo = new AbnormalReturnOrderInfo();
            returnOrderInfo.setChannelOrderId(rows.getChannel_sheetno());
            returnOrderInfo.setChannelId(QnhChannelTypeEnum.valueOfEnum(rows.getChannel_keyword()).getCode());
            returnOrderInfo.setReturnOrderId(rows.getReturn_order_sn());
            returnOrderWithChannels.add(returnOrderInfo);
        });
        return returnOrderWithChannels;
    }

    /**
     * 牵牛花：退单状态（1：新单，2：处理中，4：拒绝，5： 已退货，6：已退款，0：取消）
     * 匹配上不的先用 -1
     *
     * @param status
     * @return
     */
    public static int qnhAfsStatus(String status) {
        switch (status) {
            case "1":
                return OrderAllRefundType.APPLY.getValue();
            case "2":
                return -1;
            case "4":
                return OrderAllRefundType.TENANT_REJECT.getValue();
            case "5":
                return OrderAllRefundType.REFUND_SUCCESS.getValue();
            case "6":
                return OrderAllRefundType.REFUND_SUCCESS.getValue();
            case "0":
                return OrderAllRefundType.APPLY_CANCEL_BY_CUSTOMER.getValue();
            default:
                return -1;
        }
    }

    /**
     * 牵牛花：退单状态（1：新单，2：处理中，4：拒绝，5： 已退货，6：已退款，0：取消）
     * 匹配上不的先用 -1
     *
     * @param status
     * @return
     */
    public static int qnhAfsRecordStatus(String status) {
        switch (status) {
            case "1":
                return AfterSaleRecordStatus.COMMIT.getValue();
            case "2":
                return AfterSaleRecordStatus.AUDIT_ING.getValue();
            case "4":
                return AfterSaleRecordStatus.AUDITED_REJECT.getValue();
            case "5":
                return -1;
            case "6":
                return AfterSaleRecordStatus.FINISH.getValue();
            case "0":
                return AfterSaleRecordStatus.CANCEL.getValue();
            default:
                return -1;
        }
    }


    /**
     * 牵牛花：物流方式 0=配送  1=自提  2=商家自送 3=美团配送，4=蜂鸟配送，5=达达配送，6=点我达配送 7-三方配送 8顺丰配送
     * 百川：通过订单配送方式转换:0配送到家、1自配送
     *
     * @param logisticsModeKeyword
     * @return
     */
    public static int qnhIsSelfDelivery(String logisticsModeKeyword) {
        if (StringUtils.isEmpty(logisticsModeKeyword)) {
            return 0;
        } else if (MccConfigUtil.qnhPlatformDeliveryMode().contains(logisticsModeKeyword)) {
            return 0;
        }
        return 1;
    }

    /**
     * 牵牛花：付款方式 1 网上支付；2 货到付款
     * 百川：支付类型：1-货到付款，2-在线支付
     *
     * @param payType
     * @return
     */
    public static int qnhPayType(int payType) {
        return payType == 1 ? 2 : 1;
    }

    /**
     * 牵牛花：发票类型 0：个人、1：企业
     * 百川：发票类型：0无发票、1公司、2个人
     *
     * @param type
     * @return
     */
    public static int qnhInvoiceType(String type) {
        if ("0".equals(type)) {
            return 2;
        } else if ("1".equals(type)) {
            return 1;
        }
        return 0;
    }

    /**
     * 牵牛花：0=渠道配送  1=自提  2=商家自送 3=美团配送，4=蜂鸟配送，5=达达配送，6=点我达配送 7-三方配送 8顺丰配送
     *
     * @return
     */
    public static String qnhDeliveryMethod(String logisticsModeKeyword) {
        if (StringUtils.isEmpty(logisticsModeKeyword)) {
            return "";
        }
        switch (logisticsModeKeyword) {
            case "0":
                return "渠道配送";
            case "1":
                return "用户自提";
            case "2":
                return "商家自送";
            case "3":
                return "美团配送";
            case "4":
                return "蜂鸟配送";
            case "5":
                return "达达配送";
            case "6":
                return "点我达配送";
            case "7":
                return "三方配送";
            case "8":
                return "顺丰配送";
            default:
                return "未知";
        }
    }

    public static String convertDeliveryStatus(int status) {
        DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(status);
        if (Objects.isNull(deliveryStatus)) {
            log.warn("牵牛花无法处理配送状态:{}", status);
            throw new ChannelBizException("牵牛花无法处理配送状态:" + status);
        }
        switch (deliveryStatus) {
            case WAIT_DISPATCH_RIDER:
                return "1";
            case RIDER_ACCEPTED_ORDER:
                return "2";
            case RIDER_ARRIVE_SHOP:
                return "3";
            case RIDER_TAKEN_MEAL:
                return "4";
            case DELIVERY_COMPLETED:
                return "5";
            case TAKE_MEAL_FAIL:
            case DELIVERY_FAILED:
            case DELIVERY_EXCEPTION:
                return "6";
            case DELIVERY_CANCEL:
                return "0";
            default:
                return "-1";
        }
    }

    public static int convertReasonCode(int channelId,int code){
        if(channelId == ChannelTypeEnum.ELEM.getCode()){
            return TenantCancelOrderReasonEnum.SELF_DEFINED.getValue();
        }else if(channelId == ChannelTypeEnum.MEITUAN.getCode()
                || channelId == ChannelTypeEnum.QUAN_QIU_WA.getCode()
                || channelId == ChannelTypeEnum.SELF_CHANNEL.getCode()
                || channelId == ChannelTypeEnum.JD2HOME.getCode()
                || channelId == ChannelTypeEnum.YOU_ZAN.getCode()){
            return 2007;
        }
        throw new ChannelBizException("不支持渠道:" + channelId);
    }
}
