package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.wechat;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.weixin.WeChatUpdateTokenResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.WeChatChannelCommonService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class UpdateTokenService {


	private static final String DELIMITER = "_|_";

	private static final String TOKEN_DELIMITER = "_token_";

	private static final Interner<String> POOL = Interners.newWeakInterner();

	private static final String REDIS_TOKEN_UPDATE_SET_NX = "wx-auth-query";

	@Resource(name = "redisMasterOnlyClient")
	private RedisStoreClient redisStoreClient;

	@Resource
	private WeChatChannelCommonService weChatChannelCommonService;

	@Resource
	private CopAccessConfigService copAccessConfigService;

	public void updateToken4WeChat(String appId, String secret, long tenantId) {
		String lock = POOL.intern(String.join(DELIMITER, appId, secret));
		synchronized (lock) {
			try {
				if (tryAcquire(appId, secret)) {
					Map<String, Object> sysParam = new HashMap<>();
					sysParam.put(ProjectConstant.WECHAT_APP_ID_KEY, appId);
					sysParam.put(ProjectConstant.SECRET, secret);
					String token = doUpdateToken(sysParam, JSON.toJSONString(sysParam), tenantId);
				}
			} catch (Exception e) {
				log.error("update weChat token error, exception:", e);
				clearAcquire(appId, secret);
			}
			finally {
				lock.notifyAll();
			}
		}
	}

	public String doUpdateToken(Map<String, Object> sysParam, String jsonSysParam, long tenantId) {
		try {
			log.info("更新token 获取系统参数, tenantI:{}", tenantId);

			WeChatUpdateTokenResponse response = weChatChannelCommonService.getAccessToken(tenantId);
			if (StringUtils.isBlank(response.getAccess_token())){
				log.error("更新token, 获取渠道token失败, tenantId:{}", tenantId);
				throw new BizException("update token error");
			}
			String token = response.getAccess_token();
			sysParam.put(ProjectConstant.WECHAT_ACCESS_TOKEN, token);

			CopAccessConfigDO copAccessConfigDO = getCopAccessConfigDO(tenantId, ChannelTypeEnum.WECHAT.getCode(), GsonUtil.toJSONString(sysParam));
			log.info("更新数据库配置，tenantId:{}, config:{}", tenantId, copAccessConfigDO);

			int result = copAccessConfigService.updateWeChatSysParams(copAccessConfigDO);
			if (result == 1) {
				return token;
			}
			return null;

		} catch (Exception e) {
			log.error("更新token失败");
			throw new BizException("update token error");
		}
	}

	private boolean tryAcquire(String appId, String secret) {
		Integer expireSeconds = Lion.getConfigRepository().getIntValue("weChat-pullnew-token-expires", 1800);
		return redisStoreClient.setnx(new StoreKey(REDIS_TOKEN_UPDATE_SET_NX, appId, TOKEN_DELIMITER, secret),
			System.currentTimeMillis(), expireSeconds);
	}

	private void clearAcquire(String appId, String secret) {
		try {
			redisStoreClient.delete(new StoreKey(REDIS_TOKEN_UPDATE_SET_NX, appId, TOKEN_DELIMITER, secret));
		}
		catch (Exception e) {
			log.error("clear acquire error, exception:", e);
		}
	}

	private CopAccessConfigDO getCopAccessConfigDO(Long tenantId, Integer channelId, String authorize) {
		CopAccessConfigDO copAccessConfigDO = new CopAccessConfigDO();
		copAccessConfigDO.setTenantId(tenantId);
		copAccessConfigDO.setChannelId(channelId);
		copAccessConfigDO.setSysParams(authorize);
		return copAccessConfigDO;
	}
}
