package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelActivityParamInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelActivityService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.MtChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DrunkHorseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelActivityServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ActTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.SettingTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultBuilderUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/08/01
 */
@Service("mtDrunkHorseChannelActivityService")
public class DrunkHorseChannelActivityServiceImpl extends MtChannelActivityServiceImpl {
}
