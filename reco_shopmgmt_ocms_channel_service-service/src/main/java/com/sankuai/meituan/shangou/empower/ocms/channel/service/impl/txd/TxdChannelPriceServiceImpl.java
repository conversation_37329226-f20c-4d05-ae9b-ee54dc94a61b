package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostTxdEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPriceService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.TxdConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductResultDataUtils;
import com.taobao.api.ApiException;
import com.taobao.api.TaobaoResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/4/18
 */
@Slf4j
@Service("txdChannelPriceService")
public class TxdChannelPriceServiceImpl implements ChannelPriceService {

    @Autowired
    private TxdBaseService txdBaseService;


    @Override
    public ResultData updatePrice(SkuPriceRequest request) {
        return null;
    }

    @Override
    public ResultData updatePriceMultiChannel(SkuPriceMultiChannelRequest request) {

        ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        Map<Long, List<SkuPriceMultiChannelDTO>> storePriceMap = Fun.groupingBy(request.getParamList(), SkuPriceMultiChannelDTO::getStoreId);

        storePriceMap.forEach((storeId, oneStoreParams) -> {
            List<String> skuKeys = Fun.map(oneStoreParams, SkuPriceMultiChannelDTO::getSkuId);
            BaseRequest storeRequest = new BaseRequest()
                    .setTenantId(request.getTenantId())
                    .setChannelId(EnhanceChannelType.TXD.getChannelId())
                    .setStoreIdList(Lists.newArrayList(storeId));

            try {
                TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SKU_PRICE_UPDATE, storeRequest, TxdConvertUtil.buildPriceRequest(txdBaseService.getChannelPoiCode(storeId, storeRequest), oneStoreParams));
                ResultData oneStoreResult = TxdConvertUtil.genPriceResult(storeId, skuKeys, commonResponse);

                resultData.getSucData().addAll(oneStoreResult.getSucData());
                resultData.getErrorData().addAll(oneStoreResult.getErrorData());
            }
            catch (ApiException e) {
                log.warn("调用淘鲜达更新商品价格接口异常, request {}", request, e);
                ProductResultDataUtils.combineExceptionDataList(resultData, TxdConvertUtil.parseTxdErrorCode(e.getErrCode()),
                        e.getMessage(), skuKeys, storeId, ChannelTypeEnum.TXD);
            }
            catch (Exception e) {
                log.warn("更新淘鲜达商品价格系统异常, request {}.", request, e);
                ProductResultDataUtils.combineExceptionDataList(resultData, ResultCode.FAIL.getCode(), e.getMessage(), skuKeys, storeId, ChannelTypeEnum.TXD);
            }
        });
        return resultData;
    }

    @Override
    public List<ChannelStorePriceInfo> batchGetPriceInfo(BaseRequest request, List<Long> skuIds) {
        return Collections.emptyList();
    }
}
