package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelPriceServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2022/01/20
 */
@Service("mtDrunkHorseChannelPriceService")
public class DrunkHorseChannelPriceServiceImpl extends MtChannelPriceServiceImpl {

    @Autowired
    @Qualifier("mtDrunkHorseChannelGateService")
    private MtChannelGateService mtChannelGateService;


    @Override
    public MtChannelGateService getMtChannelGateService() {
        return mtChannelGateService;
    }
}
