package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.drunkhorse.AfterSaleCommonOpScenario;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.drunkhorse.DaySeqDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.drunkhorse.DrunkHorsePartRefundGoodsDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.drunkhorse.DrunkHorseRefundFoodDomain;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.StoreIdNotExistException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.DrunkHorseOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.MtChannelOrderFormParser;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DrunkHorseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelPoiThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.TspThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.FavoritesStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.TenantCancelOrderReasonEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.tsp.orderfulfillment.merchant.client.dto.resp.poifulfill.PoiSeqVo;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: liuyonggao
 * @create: 2021/10/11 上午10:22
 */
@Service("mtDrunkHorseChannelOrderService")
@Rhino
public class DrunkHorseChannelOrderServiceImpl implements ChannelOrderService, MtChannelOrderFormParser {

	@Resource
	private DrunkHorseConverterService drunkHorseConverterService;

	@Resource
	private TspThriftServiceProxy tspThriftServiceProxy;

	@Resource
	private ChannelPoiThriftServiceProxy channelPoiThriftServiceProxy;

	@Resource
	private DrunkHorseChannelGateService drunkHorseChannelGateService;

	@Resource
	private CopChannelStoreService copChannelStoreService;

	@Autowired
	private RedisCacheWrapper redisCacheWrapper;

	@Resource
	private DrunkHorseOrderService drunkHorseOrderService;

	@Resource
	private CommonLogger log;

	@Resource
	private ConfigThriftService configThriftService;

	private static final Integer BIZ_RECEIVE_AMT_DEGRADE = 4;

	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.detail}")
	private String orderDetailUrl;
	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.cityDegrade}")
	private String cityDegradeUrl;
	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.orderConfirm}")
	private String orderConfirmsUrl;
	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.orderCancel}")
	private String orderCancelUrl;
	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.orderLogisticsSync}")
	private String innerNetOrderLogisticsSyncUrl;

	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.orderLogisticsSuspend}")
	private String innerNetOrderLogisticsSuspendUrl;
	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.orderGetPartRefundFoods}")
	private String orderGetPartRefundFoods;
	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.orderAfsApplyListUrl}")
	private String orderAfsApplyListUrl;

	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.getOrderDaySeq}")
	private String orderDaySeqUrl;
	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.getOrderIdByDaySeq}")
	private String getOrderIdByDaySeqUrl;

	@Value("${drunkhorse.url.base}" + "${drunkhorse.url.uploadArrivalPictures}")
	private String uploadArrivalPicturesUrl;

	/**
	 * 批次最多100个
	 * @param request
	 * @return
	 */
	@Degrade(rhinoKey = "DrunkHorseChannelOrderServiceImpl-batchGetStoreLatestDaySeq",
			fallBackMethod = "batchGetStoreLatestDaySeqFallBack",
			timeoutInMilliseconds = 3000)
	@Override
	public LatestDaySeqResult batchGetStoreLatestDaySeq(DaySeqRequest request)  {
		LatestDaySeqResult result = new LatestDaySeqResult();
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.DATE_TIME, String.valueOf(request.getDateTime()));
		Map<Long, String> storeMap = onlinePoiIds(request.getTenantId(), request.getChannelId(), request.getStoreIds());
		bizParam.put(ProjectConstant.APP_POI_CODES, Joiner.on(",").join(storeMap.values()));

		Map<String, Object> getResult = drunkHorseChannelGateService.sendGet(orderDaySeqUrl, null, baseRequest, bizParam);
		log.info("DHChannelOrderServiceImpl.batchGetStoreLatestDaySeq, request:{}, getResult:{}", request, getResult);
		if (MapUtils.isEmpty(getResult)) {
			throw new ChannelBizException("调用渠道查询最新日单号解析失败");
		}
		ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);

		if (Objects.isNull(channelResponseDTO) || ProjectConstant.NG.equals(channelResponseDTO.getData())) {
			throw new ChannelBizException("调用渠道查询最新日单号解析失败");
		}

		List<DaySeqDTO> daySeqDTOS = JSON.parseArray(String.valueOf(getResult.get(ProjectConstant.DATA)), DaySeqDTO.class);
		result.setStatus(ResultGenerator.genSuccessResult());
		if (CollectionUtils.isNotEmpty(daySeqDTOS)) {
			Map<String, Long> appCodeMap = new HashMap<>();
			storeMap.entrySet().forEach(entry -> appCodeMap.put(entry.getValue(), entry.getKey()));
			result.setOrderDaySeqInfos(daySeqDTOS.stream().map(dto -> convert(dto, appCodeMap)).collect(Collectors.toList()));
		}
		return result;

	}

	/**
	 * 单门店批量根据日单号查询订单单号
	 * @param request
	 * @return
	 */
	@Degrade(rhinoKey = "DrunkHorseChannelOrderServiceImpl-batchGetOrderByDaySeq",
			fallBackMethod = "batchGetOrderByDaySeqFallBack",
			timeoutInMilliseconds = 3000)
	@Override
	public LatestDaySeqResult batchGetOrderByDaySeq(DaySeqRequest request)  {
		LatestDaySeqResult result = new LatestDaySeqResult();
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.DATE_TIME, String.valueOf(request.getDateTime()));
		bizParam.put(ProjectConstant.APP_POI_CODE, onlinePoiId(request.getTenantId(), request.getChannelId(), request.getStoreIds().get(0)));
		bizParam.put(ProjectConstant.DAY_SEQS, Joiner.on(",").join(request.getDaySeqs()));

		Map<String, Object> getResult = drunkHorseChannelGateService.sendGet(getOrderIdByDaySeqUrl, null, baseRequest, bizParam);
		log.info("DHChannelOrderServiceImpl.batchGetStoreLatestDaySeq, request:{}, getResult:{}", request, getResult);
		if (MapUtils.isEmpty(getResult)) {
			throw new ChannelBizException("调用根据日单号查询订单失败");
		}
		ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
		if (Objects.isNull(channelResponseDTO) || ProjectConstant.NG.equals(channelResponseDTO.getData())) {
			throw new ChannelBizException("调用根据日单号查询订单解析失败");
		}
		List<DaySeqDTO> daySeqDTOS = JSON.parseArray(String.valueOf(getResult.get(ProjectConstant.DATA)), DaySeqDTO.class);
		result.setStatus(ResultGenerator.genSuccessResult());
		if (CollectionUtils.isNotEmpty(daySeqDTOS)) {
			result.setOrderDaySeqInfos(daySeqDTOS.stream().map(dto -> convert(dto, request.getStoreIds().get(0))).collect(Collectors.toList()));
		}
		return result;
	}

	@Override
	@Degrade(rhinoKey = "DrunkHorseChannelOrderServiceImpl-poiConfirmOrder",
			fallBackMethod = "poiConfirmOrderFallBack",
			timeoutInMilliseconds = 3000)
	public ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request) {
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setOrderId(request.getOrderId());
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		bizParam.put(ProjectConstant.APP_POI_CODE, onlinePoiId(request.getTenantId(), request.getChannelId(), request.getStoreId()));

		Map<String, Object> getResult = drunkHorseChannelGateService.sendGet(orderConfirmsUrl, null, baseRequest, bizParam);
		log.info("DHChannelOrderServiceImpl.poiConfirmOrder, request:{}, getResult:{}", request, getResult);
		if (MapUtils.isEmpty(getResult)) {
			throw new ChannelBizException("调用渠道商家确认订单失败");
		}
		ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
		if (Objects.isNull(channelResponseDTO)) {
			throw new ChannelBizException("调用渠道商家确认订单返回数据解析失败");
		}
		if (channelResponseDTO.isSuccess()) {
			return ResultGenerator.genSuccessResult();
		}
		// 此处考虑的是正常业务逻辑的接单失败，不降级
		return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
	}

	/***
	 *
	 * 注意：开放平台添加新的参数，请改一下订单降级链路接口，保证添加一样的参数。见接口：getChannelOrderDetailFallBack
	 * **/
	@Degrade(rhinoKey = "DrunkHorseChannelOrderServiceImpl-getChannelOrderDetail",
			fallBackMethod = "getChannelOrderDetailFallBack",
			timeoutInMilliseconds = 3000)
	@Override
	public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
		GetChannelOrderDetailResult orderDetailResult = new GetChannelOrderDetailResult();
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setOrderId(request.getOrderId());
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		bizParam.put(ProjectConstant.APP_POI_CODE, onlinePoiId(request.getTenantId(), request.getChannelId(), request.getSotreId()));

		Map<String, Object> resultMap = drunkHorseChannelGateService.sendGet(orderDetailUrl, null, baseRequest, bizParam);
		log.info("DHChannelOrderServiceImpl.getChannelOrderDetail, request:{}, resultMap:{}", request, resultMap);
		if (CollectionUtils.isEmpty(resultMap)) {
			throw new ChannelBizException("调用渠道获取订单详情失败");
		}
		return orderDetailAnalysis(request.getTenantId(), request.getChannelId(), resultMap, orderDetailResult, request.getSotreId());
	}

	/**
	 * 查询隐私号在某城市是否降级
	 * @param cityId 城市ID
	 * @return .
	 */
	private boolean isPrivacyNumberCityDegrade(String cityId, int channelId, long tenantId) {
		// 隐私号是否全局降级
		if (MccConfigUtil.isDHPrivacyPhoneDegrade()) {
			return true;
		}
		try {
			List<String> cities = redisCacheWrapper.getDegradeCities(ChannelTypeEnum.MT_DRUNK_HORSE.getCode());
			// 无缓存--需从开放平台处获取
			if (cities == null) {
				cities = redisCacheWrapper.tryWithLock("privacy_phone_city_degrade", () -> getDegradeCityFromOpen(channelId, tenantId));
			}
			// 有缓存 && 未降级
			if (cities == null || cities.isEmpty()) {
				return false;
			}
			// 有缓存 && 降级
			for (String city: cities) {
				// 前缀匹配
				if (cityId.startsWith(city)) {
					return true;
				}
			}
		} catch (Exception ex) {
			log.error("judge city degrade fail, think city is not degrade,reason:{}", ex);
		}
		return false;
	}

	/**
	 * 从开放平台获取隐私号降级城市
	 * @param channelId 渠道id
	 * @param tenantId 租户id
	 * @return 返回降级城市
	 */
	private List<String>  getDegradeCityFromOpen(int channelId, long tenantId) {
		BaseRequest baseRequest = new BaseRequest().setChannelId(channelId)
				.setTenantId(tenantId);
		Map<String, Object> resultMap = drunkHorseChannelGateService
				.sendGet(cityDegradeUrl, null, baseRequest, Maps.newHashMap());
		log.info("get degrade cities, resultMap:{}", resultMap);
		DrunkHorseDegradeCityResponse response = JSON.parseObject(JSON.toJSONString(resultMap),
				DrunkHorseDegradeCityResponse.class);
		if (!NumberUtils.INTEGER_ONE.equals(response.getResult_code())) {
			log.warn("get degrade cities error, think all city is not degrade");
			return new ArrayList<>();
		}
		// 缓存
		redisCacheWrapper.setPrivacyDegradeCities(ChannelTypeEnum.MT_DRUNK_HORSE.getCode(), JSON.toJSONString(response));
		return response.getSuccess_list();
	}


	private ChannelOrderDetailDTO parseOrderForm(long tenantId, int channelId, String orderDetailForm, Long storeId) {
		ChannelOrderDetail channelOrderDetail = null;
		if (StringUtils.startsWith(orderDetailForm, "{") && StringUtils.endsWith(orderDetailForm, "}")) {
			//json 格式
			channelOrderDetail = JSON.parseObject(orderDetailForm, ChannelOrderDetail.class);
		} else {
			//表单格式
			channelOrderDetail = new ChannelOrderDetail();
			String pairs[] = StringUtils.split(orderDetailForm, "&");
			for (String pair : pairs) {
				String kv[] = StringUtils.split(pair, "=");
				if (kv.length == 2) {
					String key = kv[0];
					String value = kv[1];
					try {
						if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
							BeanUtils.setProperty(channelOrderDetail, UrlUtil.urlDecodeSafe(key), UrlUtil.urlDecodeSafe(value));
						}
					} catch (Exception e) {
						log.error("解析form表单错误,target值无法赋值,key:{}, value:{}", key, value, e);
					}
				}
			}
		}
		if (Objects.isNull(channelOrderDetail)) {
			return null;
		}
		return orderDetail2ChannelOrderDetailDto(tenantId, channelId, channelOrderDetail, storeId);

	}

	private ChannelOrderDetailDTO orderDetail2ChannelOrderDetailDto(long tenantId, int channelId, ChannelOrderDetail channelOrderDetail, Long storeId) {
		//返回数据映射
		ChannelOrderDetailDTO channelOrderDetailDTO = drunkHorseConverterService.channelOrderDetailMapping(channelOrderDetail);
		DrunkHorseOrderExtra orderExtra = new DrunkHorseOrderExtra();
		orderExtra.setIsBrandFirstOrder(Optional.ofNullable(channelOrderDetail.getIs_brand_first_order()).orElse(0));
		orderExtra.setTraceNo(Optional.ofNullable(channelOrderDetail.getTrace_no()).orElse(""));
		orderExtra.setOuterUserId(channelOrderDetailDTO.getUserId());
		orderExtra.setMallId(Objects.nonNull(channelOrderDetail.getMall_id()) ? String.valueOf(channelOrderDetail.getMall_id()) : "");
		channelOrderDetailDTO.setExtData(JSON.toJSONString(orderExtra));
		//计算总优惠金额
		channelOrderDetailDTO.setTotalDiscount(channelOrderDetailDTO.getOriginalAmt() - channelOrderDetailDTO.getActualPayAmt());
		//门店信息
		channelOrderDetailDTO.setStoreId(storeId);
		//channelId
		channelOrderDetailDTO.setChannelId(channelId);

		if(CollectionUtils.isNotEmpty(channelOrderDetail.getOrder_tag_list())){
			channelOrderDetailDTO.setMtFacaiWine(channelOrderDetail.getOrder_tag_list().contains(MtOrderTagEnum.FA_CAI_WINE.getCode()));
		}

		//商家应收金额
		if (StringUtils.isNotBlank(channelOrderDetail.getPoi_receive_detail())){
			populatePoiReceiveDetail(channelOrderDetail, channelOrderDetailDTO);
		} else {
			log.info("歪马订单没有商家对账信息:{}", channelOrderDetail.getOrder_id());
			List<Integer> incmpModules = channelOrderDetailDTO.getDegradeModules();
			if (incmpModules == null){
				incmpModules = new ArrayList<>();
				channelOrderDetailDTO.setDegradeModules(incmpModules);
			}
			if (!incmpModules.contains(BIZ_RECEIVE_AMT_DEGRADE)){
				log.info("添加商家信息降级标记");
				incmpModules.add(BIZ_RECEIVE_AMT_DEGRADE);
			}
		}

		OrderDeliveryDetailDTO deliveryDetailDTO = drunkHorseConverterService.deliveryDetailMapping(channelOrderDetail);
		deliveryDetailDTO.setUserName(joinUserName(deliveryDetailDTO.getUserName(), channelOrderDetail.getRecipient_gender()));
		channelOrderDetailDTO.setDeliveryDetail(deliveryDetailDTO);
		//单独处理收货人姓名，拼接性别
		//开票信息
		channelOrderDetailDTO.setInvoiceDetail(drunkHorseConverterService.invoiceDetailMapping(channelOrderDetail));
		// 商品信息
		List<OrderSkuDetail> orderSkuDetailList = JSON.parseArray(channelOrderDetail.getDetail(), OrderSkuDetail.class);
		List<OrderProductDetailDTO> orderProductDetailAllList = drunkHorseConverterService.orderSkuDetailListMapping(orderSkuDetailList);
		channelOrderDetailDTO.setSkuDetails(orderProductDetailAllList);

		List<OrderProductDetailDTO> orderProductDetailDTOSFromAct = Lists.newArrayList();
		if (StringUtils.isNotBlank(channelOrderDetail.getExtras())) {
			// 活动信息
			List<OrderActivitiesInfo> orderActivitiesInfoList = JSON.parseArray(channelOrderDetail.getExtras(), OrderActivitiesInfo.class);
			// 过滤空对象
			// 对象中有如下格式：[{"mt_charge":0.0,"poi_charge":1.0,"reduce_fee":1.0,"remark":"满2.0元减1.0元","type":2},{}]
			// 需要过滤掉最后的{}
			orderActivitiesInfoList = orderActivitiesInfoList.stream()
					.filter(item -> Objects.nonNull(item.getReduce_fee()) && Objects.nonNull(item.getMt_charge()) && Objects.nonNull(item.getPoi_charge()))
					.collect(Collectors.toList());
			extractPromotionFromActivities(storeId, channelOrderDetailDTO, orderActivitiesInfoList);

			List<OrderDiscountDetailDTO> orderDiscountDetailDTOS = drunkHorseConverterService.orderActivitieInfoListMapping(orderActivitiesInfoList);
			log.info("orderDiscountDetailDTOS:{}", orderDiscountDetailDTOS);
			// 非歪马租户需将线上赠品从促销信息移除并放入订单明细；若关闭灰度开关则只将ERP租户的促销信息移除并放入订单明细
			if (CollectionUtils.isNotEmpty(orderDiscountDetailDTOS)) {
				if (MccConfigUtil.dhAddGiftToItemSwitch(tenantId)) {
					orderDiscountDetailDTOS.forEach(item -> item.setGiftInfo(null));
					orderProductDetailDTOSFromAct = extractGiftFromPromotion(orderActivitiesInfoList);
				}
			}
			channelOrderDetailDTO.setActivities(orderDiscountDetailDTOS);
		}

		String goodsActivities = channelOrderDetail.getSku_benefit_detail();
		if (StringUtils.isNotBlank(goodsActivities)) {
			List<SkuBenefitDetail> skuBenefitDetails = JSON.parseArray(goodsActivities, SkuBenefitDetail.class);
			channelOrderDetailDTO.setSkuSharedActivities(drunkHorseConverterService.convertGoodsActivityDetails(skuBenefitDetails));
			extractSkuPromotionFromSkuBenefitV3(storeId, channelOrderDetailDTO.getSkuDetails(), skuBenefitDetails, tenantId);
		}

		if (CollectionUtils.isNotEmpty(orderProductDetailDTOSFromAct)) {
			orderProductDetailAllList.addAll(orderProductDetailDTOSFromAct);
		}

		String userMemberInfoStr = channelOrderDetail.getUser_member_info();
		if (StringUtils.isNotBlank(userMemberInfoStr)) {
			log.info("orderDetail2ChannelOrderDetailDto 订单会员信息不为空 :{} ", channelOrderDetail);
			ChannelOrderDetail.UserMemberInfo userMemberInfo = JSON.parseObject(userMemberInfoStr, ChannelOrderDetail.UserMemberInfo.class);
			if (Objects.nonNull(userMemberInfo)) {
				channelOrderDetailDTO.setMemberCardCode(userMemberInfo.getCard_code());
			}
		}

		// 设置用户隐私号
		try {
			channelOrderDetailDTO.getDeliveryDetail().setUsePrivacyPhone(
					channelOrderDetail.getPri_phone() == null ? NumberUtils.INTEGER_ZERO: channelOrderDetail.getPri_phone());
			// 未使用隐私号--将真实手机号进行脱敏保存
			if (!NumberUtils.INTEGER_ONE.equals(channelOrderDetail.getPri_phone())) {
				channelOrderDetailDTO.getDeliveryDetail().setUserPrivacyPhone(MtConverterUtil.getUserPrivacyPhone(channelOrderDetail));
				// 使用隐私号--填充隐私号相关字段
			} else {
				// 隐私号可能为空
				if (StringUtils.isNotBlank(channelOrderDetail.getPrivacy_phone())) {
					channelOrderDetailDTO.getDeliveryDetail()
							.setUserPrivacyPhone(channelOrderDetail.getPrivacy_phone().replace(',', '_'));
					List<String> backupPhones = channelOrderDetail.getBackup_privacy_phones();
					if (CollectionUtils.isNotEmpty(backupPhones)) {
						for (int i = 0; i < backupPhones.size(); ++i) {
							backupPhones.set(i, backupPhones.get(i).replace(',', '_'));
						}
					}
					channelOrderDetailDTO.getDeliveryDetail().setBackUpUserPrivacyPhone(backupPhones);
					channelOrderDetailDTO.setCityPrivacyDegrade(isPrivacyNumberCityDegrade(String.valueOf(channelOrderDetail.getCity_id()), channelId, tenantId));
				}
			}
			//订单降级
			recordOrderDowngrade(tenantId, channelOrderDetailDTO.getStoreId(), channelOrderDetail);

		} catch (Exception ex) {
			log.error("设置用户隐私号异常", ex);
		}

		// 首先取门店基础服务的id、不满足条件再获取APP方门店id
		channelOrderDetailDTO.setOriginalPoiId(Objects.nonNull(channelOrderDetail.getWm_poi_id())
				&& channelOrderDetail.getWm_poi_id() > BigInteger.ZERO.longValue()
				? channelOrderDetail.getWm_poi_id().toString() : channelOrderDetail.getApp_poi_code());
		// 设置收藏状态
		channelOrderDetailDTO.setFavoritesStatus(Optional.ofNullable(channelOrderDetail.getIs_favorites())
				.map(favorites -> favorites ? FavoritesStatusEnum.ALREADY : FavoritesStatusEnum.NOT)
				.orElse(FavoritesStatusEnum.UNKNOWN));
		channelOrderDetailDTO.setOrderSource(channelOrderDetail.getOrder_source());
		return channelOrderDetailDTO;
	}

	private List<OrderProductDetailDTO> extractGiftFromPromotion(List<OrderActivitiesInfo> orderActivitiesInfoList) {
		List<OrderProductDetailDTO> giftProductDetail = Lists.newArrayList();
		try {
			if (CollectionUtils.isEmpty(orderActivitiesInfoList)) {
				return giftProductDetail;
			}
			for (OrderActivitiesInfo giftOrderActivitiesInfo : orderActivitiesInfoList) {
				if (Objects.isNull(giftOrderActivitiesInfo.getAct_extend_msg()) || giftOrderActivitiesInfo.getAct_extend_msg().getGift_num() == 0) {
					continue;
				}
				OrderProductDetailDTO orderProductDetailDTO = drunkHorseConverterService.orderGiftInfoMapping(giftOrderActivitiesInfo.getAct_extend_msg());
				orderProductDetailDTO.setPoiItemPromotion(0);
				orderProductDetailDTO.setOriginalPrice(0);

				JSONObject extJSON = new JSONObject();
				if (orderProductDetailDTO.isSetExtData()) {
					try {
						String extData = orderProductDetailDTO.getExtData();
						extJSON = JSON.parseObject(extData);
					} catch (Exception e) {
						log.error("解析ext异常，detail：{}", JSON.toJSONString(orderProductDetailDTO), e);
					}
				}
				// 添加线上赠品标（giftType  :  0 (平台赠品)），标志打在明细的扩展字段
				extJSON.put("giftType", 0);
				//需要将赠品的 mainSkuId 存入extData
				String mainSkuId = giftOrderActivitiesInfo.getAct_extend_msg().getSku_id();
				if(StringUtils.isNotBlank(mainSkuId)) {
					extJSON.put("mainSkuId", mainSkuId);
				}
				orderProductDetailDTO.setExtData(extJSON.toJSONString());
				giftProductDetail.add(orderProductDetailDTO);
			}
		}catch (Exception e){
			log.error("解析赠品异常",e);
		}

		return giftProductDetail;
	}

	private void populatePoiReceiveDetail(ChannelOrderDetail channelOrderDetail, ChannelOrderDetailDTO channelOrderDetailDTO) {
		if (!Lion.getConfigRepository().getBooleanValue("switch.drunkhorse.order.biz.receive.amount", true)){
			log.info("歪马实收金额，取poi_receive_detail开关未开");
			return;
		}
		PoiReceiveDetail poiReceiveDetail = JSON.parseObject(channelOrderDetail.getPoi_receive_detail(), PoiReceiveDetail.class);
		if (Objects.nonNull(poiReceiveDetail)) {
			if (poiReceiveDetail.getWmPoiReceiveCent() != null){
				channelOrderDetailDTO.setBizReceiveAmt(poiReceiveDetail.getWmPoiReceiveCent().intValue());
			}
			if (poiReceiveDetail.getFoodShareFeeChargeByPoi() != null){
				channelOrderDetailDTO.setPlatformFee(poiReceiveDetail.getFoodShareFeeChargeByPoi().intValue());
			}
			//商家对账信息相关的额外信息
			if (StringUtils.isNotEmpty(poiReceiveDetail.getReconciliationExtras())){
				ReconciliationExtras reconciliationExtras = JSON.parseObject(poiReceiveDetail.getReconciliationExtras(), ReconciliationExtras.class);
				if (Objects.nonNull(reconciliationExtras)){
					channelOrderDetailDTO.setPerformanceServiceFee(MoneyUtils.yuanToFen(reconciliationExtras.getPerformanceServiceFee()));
				}
			}
		}
	}

	private void recordOrderDowngrade(long tenantId, long storeId, ChannelOrderDetail channelOrderDetail) {
		if (channelOrderDetail.hasDownGrade()) {
			log.info("[美团歪马订单降级], tenantId:{}, storeId:{}, order:{}, incmp:{}", tenantId, storeId, channelOrderDetail.getOrder_id(), channelOrderDetail.getInvoice_title());
			MetricHelper.build().name("order.downgrade").tag("channel", "drunkhorse").count();
		}

		Optional.ofNullable(channelOrderDetail.getIncmp_modules()).map(List::stream).orElse(Stream.empty()).forEach(incmp -> {
			MetricHelper.build().name("order.downgrade.drunkhorse.module").tag("module", String.valueOf(incmp)).count();
		});
	}


	private GetChannelOrderDetailResult orderDetailAnalysis(long tenantId, int channelId, Map<String, Object> result,
															GetChannelOrderDetailResult orderDetailResult, Long storeId) {
		//返回数据反序列化
		ChannelOrderDetailResponse orderDetailResponse = JSON.parseObject(JSON.toJSONString(result),
				ChannelOrderDetailResponse.class);
		if (Objects.isNull(orderDetailResponse) || StringUtils.isBlank(orderDetailResponse.getData())) {
			throw new ChannelBizException("订单详情数据反序列化失败");
		}
		//返回状态校验
		if (ProjectConstant.NG.equals(orderDetailResponse.getData())) {
			MtErrorStatus error = orderDetailResponse.getError();
			if (Objects.isNull(error)) {
				return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST));
			}
			log.error("歪马订单不存在, msg:{}", error.getMsg());
			return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST, error.getMsg()));
		}
		//返回数据映射
		ChannelOrderDetailDTO channelOrderDetailDTO = parseOrderForm(tenantId, channelId, orderDetailResponse.getData(), storeId);
		return orderDetailResult.setStatus(ResultGenerator.genSuccessResult())
				.setChannelOrderDetail(channelOrderDetailDTO);
	}

	@Override
	public GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request) {
		GetChannelOrderDetailRequest req = new GetChannelOrderDetailRequest();
		req.setChannelId(request.getChannelId());
		req.setOrderId(request.getOrderId());
		req.setTenantId(request.getTenantId());
		req.setSotreId(request.getStoreId());

		GetOrderStatusResult resp = new GetOrderStatusResult();
		OrderStatusDTO orderStatusDTO = new OrderStatusDTO().setOrderId(request.getOrderId());
		resp.setOrderStatus(orderStatusDTO);

		GetChannelOrderDetailResult detail = getChannelOrderDetail(req);
		resp.setStatus(detail.getStatus());
		if (resp.getStatus().getCode() == ResultCode.SUCCESS.getCode()) {
			orderStatusDTO.setStatus(detail.getChannelOrderDetail().getStatus());
		}
		return resp;
	}

	@Override
	public ResultStatus agreeRefund(AgreeRefundRequest request) {
		try {
			ChannelOrderCancelRelDTO dto = drunkHorseConverterService.agreeRefund(request);
			dto.setApp_poi_code(onlinePoiId(request.getTenantId(), request.getChannelId(), request.getStoreId()));

			ChannelResponseDTO resultData = drunkHorseChannelGateService
					.sendPost(ChannelPostDrunkHorseEnum.AGREE_REFUND, request.getTenantId(), request.getChannelId(),dto);

			return dealResult(resultData);
		} catch (Exception e) {
			log.error("dhChannelOrderService agreeRefund ERROR!", e);
			return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
		}
	}

	private ResultStatus dealResult(ChannelResponseDTO resultData) {
		if (ProjectConstant.NG.equals(resultData.getData())) {
			return ResultGenerator.genFailResult(resultData.getErrorMsg());
		}
		return ResultGenerator.genSuccessResult();
	}

	@Override
	public ResultStatus rejectRefund(RejectRefundRequest request) {
		try {
			ChannelOrderCancelRelDTO dto = drunkHorseConverterService.rejectRefund(request);
			dto.setApp_poi_code(onlinePoiId(request.getTenantId(), request.getChannelId(), request.getStoreId()));

			ChannelResponseDTO resultData = drunkHorseChannelGateService.sendPost(ChannelPostDrunkHorseEnum.REJECT_REFUND,
					request.getTenantId(), request.getChannelId(), dto);

			return dealResult(resultData);
		} catch (Exception e) {
			log.error("dHChannelOrderService rejectRefund ERROR!", e);
			return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
		}
	}

	@Override
	public ResultStatus refundGoods(RefundGoodsRequest request) {
		return null;
	}

	@Override
	public ResultStatus poiCancelOrder(PoiCancelOrderRequest request) {
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setOrderId(request.getOrderId());
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		bizParam.put(ProjectConstant.ORDER_REASON, request.getReason());
		bizParam.put(ProjectConstant.APP_POI_CODE, onlinePoiId(request.getTenantId(), request.getChannelId(), request.getStoreId()));

		TenantCancelOrderReasonEnum tenantCancelOrderReasonEnum = TenantCancelOrderReasonEnum.findByValue(request.getReason_code());
		int mtCancelReason = OrderRefundCodeConventer.mtRefundCodeMapping(tenantCancelOrderReasonEnum);
		bizParam.put(ProjectConstant.ORDER_REASON_CODE, String.valueOf(mtCancelReason));

		Map<String, Object> getResult = drunkHorseChannelGateService.sendEncodedGet(orderCancelUrl, null, baseRequest, bizParam);
		log.info("poiCancelOrder, request:{}, getResult:{}", request, getResult);
		if (MapUtils.isEmpty(getResult)) {
			return ResultGenerator.genFailResult("调用渠道商家取消订单失败");
		}
		ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
		if (Objects.isNull(channelResponseDTO)) {
			return ResultGenerator.genFailResult("调用渠道商家取消订单返回数据解析失败");
		}
		if (channelResponseDTO.isSuccess()) {
			return ResultGenerator.genSuccessResult();
		}
		return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
	}

	@Override
	public CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request) {
		return null;
	}

	@Override
	public ResultStatus poiPartRefundApply(PoiPartRefundRequest request) {
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setOrderId(request.getOrderId());
		Map<String, Object> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		bizParam.put(ProjectConstant.ORDER_REASON, request.getReason());
		bizParam.put(ProjectConstant.APP_POI_CODE, onlinePoiId(request.getTenantId(), request.getChannelId(), request.getStoreId()));

		List<?> refundProducts = request.getRefundProducts().stream().map(DrunkHorseRefundFoodDomain::fromDTO).map(refundProductInfoDTO -> {
			Map<String, Object> map = Maps.newHashMap();
			if (recoverPartRefundSku()){
				// 歪马部分退款不传sku信息，这里只是为了以防万一，这种方式有问题，保留代码
				map.put("app_spu_code", refundProductInfoDTO.getAppFoodCode());
				map.put("sku_id", refundProductInfoDTO.getCustomSkuId());
			}
			map.put("count", refundProductInfoDTO.getCount());
			map.put("item_id", refundProductInfoDTO.getItemId());
			return map;
		}).collect(Collectors.toList());
		bizParam.put(ProjectConstant.SPU_DATA, JSON.toJSONString(refundProducts));
		ChannelResponseDTO channelResponseDTO = drunkHorseChannelGateService.sendPost(ChannelPostDrunkHorseEnum.PART_REFUND_APPLY, baseRequest, bizParam);

		log.info("DhChannelOrderServiceImpl.poiPartRefundApply, request:{}, getResult:{}", request, channelResponseDTO);
		if (Objects.isNull(channelResponseDTO)) {
			return ResultGenerator.genFailResult("商家发起部分退款失败,返回结果为空");
		}
		if (channelResponseDTO.isSuccess()) {
			//需要refund_id
			ResultStatus resultStatus = ResultGenerator.genSuccessResult();
			resultStatus.setData(String.valueOf(channelResponseDTO.getData()));
			return resultStatus;
		}
		return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());


	}

	private boolean recoverPartRefundSku() {
		return !ConfigUtilAdapter.getBoolean("drunkhorse_replace_sku_part_refund_switch", true);
	}

	@Override
	public ResultStatus preparationMealComplete(PreparationMealCompleteRequest request) {
		return ResultGenerator.genResult(ResultCode.SUCCESS);

	}

	@Override
	public ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request) {
		return ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE);
	}

	@Override
	public PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request) {
		return null;

	}

	@Override
	public OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request) {
		return null;

	}

	@Override
	public GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request) {
		return null;

	}

	@Override
	public GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request) {
		return null;

	}

	private boolean positionIsValidForPlatform(double latitude, double longitude) {
		if (latitude > 0 && longitude > 0) {
			return true;
		}
		return false;
	}

	@Override
	public GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request) {
		GetOrderAfsApplyListResult resp = new GetOrderAfsApplyListResult();

		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelType())
				.setTenantId(request.getTenantId())
				.setOrderId(request.getChannelOrderId());
		Map param = Maps.newHashMap();
		param.put(ProjectConstant.ORDER_ID, request.getChannelOrderId());
		param.put(ProjectConstant.APP_POI_CODE, onlinePoiId(request.getTenantId(), request.getChannelType(), request.getStoreId()));

		Map resultMap = drunkHorseChannelGateService.sendGet(orderAfsApplyListUrl, null, baseRequest, param);
		if (MapUtils.isEmpty(resultMap)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用歪马渠道查询订单售后申请失败"));
		}
		if ("ng".equals(resultMap.get(ProjectConstant.DATA))) {
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(resultMap.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
		} else {
			List<MtOrderAfsApplyDTO> afsApplyDTOs = JSON.parseArray(String.valueOf(resultMap.get(ProjectConstant.DATA)), MtOrderAfsApplyDTO.class);
			afsApplyDTOs = afsApplyDTOs.stream().filter(e -> e.getApply_op_scenario() != AfterSaleCommonOpScenario.SYSTEM_APPLY_FOR_REPEAT_PAY).collect(Collectors.toList());

			List<OrderAfsApplyDTO> orderAfsApplyList = drunkHorseConverterService.convertAfsApplyDTOs(afsApplyDTOs);
			if (StringUtils.isNotBlank(request.getAfterSaleId())) {
				orderAfsApplyList = orderAfsApplyList.stream().filter(e -> StringUtils.equals(e.getAfterSaleId(), request.getAfterSaleId())).collect(Collectors.toList());
			}
			resp.setStatus(ResultGenerator.genSuccessResult()).setAfsApplyList(orderAfsApplyList);
		}
		return resp;
	}

	@Override
	public ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request) {
		return new ResultStatus()
				.setCode(ResultCode.SUCCESS.getCode())
				.setMsg(ResultCode.SUCCESS.getMsg());
	}

	@Override
	public ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request) {
		//兼容历史逻辑，changeType为新增字段，为0表示更新运单状态以及骑手位置
		if (request.getChangeType() == 0) {
			// 只同步骑手位置
			if (request.isOnlySyncRiderPosition()) {
				if (!positionIsValidForPlatform(request.getLatitude(), request.getLongitude())) {
					log.error("DrunkHorseChannelOrderServiceImpl.updateDeliveryInfo error, rider position is not valid, req:{}", request);
					throw new ChannelBizException("rider position is not valid: latitude = " + request.getLatitude() + " Longitude = " + request.getLongitude());
				}

				return updateDeliveryInfo(request, DrunkHorseLogisticSyncTypeEnum.ONLY_SYNC_RIDER_POSITION);
			}

			// 同步运单状态和骑手位置,骑手位置作为次要信息同步失败不影响主流程
			ResultStatus updateOrderStatusResult = updateDeliveryInfo(request, DrunkHorseLogisticSyncTypeEnum.ONLY_SYNC_DELIVERY_ORDER_STATUS);
			if (positionIsValidForPlatform(request.getLatitude(),request.getLongitude())) {
				ResultStatus updatePositionResult = updateDeliveryInfo(request, DrunkHorseLogisticSyncTypeEnum.ONLY_SYNC_RIDER_POSITION);
				if (updatePositionResult.getCode() != ResultCode.SUCCESS.getCode()) {
					log.warn("向微商城同步歪马骑手位置失败, req: {}, result: {}", request, updatePositionResult);
				}
			}

			return updateOrderStatusResult;
		} else if (request.getChangeType() == 1 || request.getChangeType() == 2) {
			//暂停配送/继续配送
			return this.updateDeliverySuspend(request);

		} else {
			return new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null);
		}

	}

	@Override
	public ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request) {
		return updateDeliveryInfo(request);
	}

	@Override
	public ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request) {
		return updateDeliveryInfo(request, DrunkHorseLogisticSyncTypeEnum.ONLY_SYNC_RIDER_INFO);
	}

	//更新运单暂停配送-继续配送状态
	private ResultStatus updateDeliverySuspend(UpdateDeliveryInfoRequest request) {
		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setOrderId(request.getOrderId());

		Map<String, Object> param = new HashMap<>();
		param.put("order_id", request.getOrderId());
		param.put(ProjectConstant.APP_POI_CODE, onlinePoiId(request.getTenantId(), request.getChannelId(), request.getShopId()));
		//changeType: 1-暂停配送；2-继续配送; suspend_status:1-暂停配送；0-继续配送
		param.put("suspend_status", request.getChangeType() == 1 ? 1 : 0);

		log.info("DHChannelOrderServiceImpl.updateDeliverySuspend  调用歪马接口 baseRequest:{}   param:{}", baseRequest, param);
		Map<String, Object> result = drunkHorseChannelGateService.sendPost(innerNetOrderLogisticsSuspendUrl, null, baseRequest, param);
		log.info("DHChannelOrderServiceImpl.updateDeliverySuspend  调用歪马接口返回:{}", result);
		if (result != null && result.get("result_code") != null && (Integer) result.get("result_code") == 1) {
			return new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null);
		}

		log.error("同步运单暂停-继续配送状态失败, result:{}, request:{}", result, request);
		Cat.logEvent("DHChannelOrderService","updateDeliverySuspendError");
		return new ResultStatus(ResultCode.FAIL.getCode(), ResultCode.FAIL.getMsg(), null);
	}

	private ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request, DrunkHorseLogisticSyncTypeEnum syncTypeEnum) {
		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setOrderId(request.getOrderId());

		ResultStatus resultStatus = new ResultStatus();
		Map<String, Object> param = buildBusinessParam(request, syncTypeEnum);
		if (Objects.isNull(param)) {
			log.warn("美团歪马更新配送信息条件不满足，不更新，直接返回成功:{}", request);
			resultStatus.setCode(ResultCode.SUCCESS.getCode());
			resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
			return resultStatus;
		}
		String url = innerNetOrderLogisticsSyncUrl;
		log.info("DHChannelOrderServiceImpl.updateDeliveryInfo  调用歪马接口 baseRequest:{}   param:{}", baseRequest, param);
		Map result = drunkHorseChannelGateService.sendPost(url, null, baseRequest, param);
		log.info("DHChannelOrderServiceImpl.updateDeliveryInfo  调用歪马接口返回:{}", result);

		if (Objects.isNull(result) || "ng".equals(result.get(ProjectConstant.DATA))) {
			resultStatus.setCode(ResultCode.FAIL.getCode());
			resultStatus.setMsg(ResultCode.FAIL.getMsg());

			// 骑手接单时商家还没有接单,导致向歪马微商城同步订单状态失败
			if (syncTypeEnum == DrunkHorseLogisticSyncTypeEnum.ONLY_SYNC_DELIVERY_ORDER_STATUS
					&& Objects.nonNull(result)
					&& Objects.nonNull(result.get(ProjectConstant.ERROR))
					&& "1041".equals(JSON.parseObject(String.valueOf(result.get(ProjectConstant.ERROR))).getString(ProjectConstant.CODE))) {
				log.error("骑手接单时商家还没有接单,导致向歪马微商城同步订单状态失败, result:{}, request:{}", result, request);
				Cat.logEvent("DHChannelOrderService","updateOrderStatusError");
			}
		} else {
			resultStatus.setCode(ResultCode.SUCCESS.getCode());
			resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
		}
		return resultStatus;
	}

	@Override
	public ResultStatus selfDelivery(SelfDeliveryRequest request) {
		return null;
	}

	@Override
	public QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request) {
		return null;
	}

	@Override
	public ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request) {

		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		bizParam.put(ProjectConstant.APP_POI_CODE, onlinePoiId(request.getTenantId(), request.getChannelId(), request.getStoreId()));

		BaseRequest baseRequest = new BaseRequest()
				.setTenantId(request.getTenantId())
				.setChannelId(request.getChannelId())
				.setOrderId(request.getOrderId());
		String url = orderGetPartRefundFoods;
		log.info("DhChannelOrderServiceImpl.queryChannelPartRefundGoodsDetail  调用歪马接口 baseRequest:{}   param:{}", baseRequest, bizParam);
		Map result = drunkHorseChannelGateService.sendGet(url, null, baseRequest, bizParam);
		log.info("DhChannelOrderServiceImpl.queryChannelPartRefundGoodsDetail  调用歪马接口返回:{}", result);
		ChannelPartRefundGoodsResult resp = new ChannelPartRefundGoodsResult();
		if (MapUtils.isEmpty(result)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用歪马渠道查询订单部分退款商品失败"));
		}
		if ("ng".equals(result.get(ProjectConstant.DATA))) {
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(result.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
		} else {
			List<DrunkHorsePartRefundGoodsDTO> partRefundGoodsDTOList = JSON.parseArray(String.valueOf(result.get(ProjectConstant.DATA)), DrunkHorsePartRefundGoodsDTO.class);

			if (CollectionUtils.isEmpty(partRefundGoodsDTOList)) {
				return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单部分退款商品详情为空"));
			}

			List<PartRefundGoodDetailDTO> partRefundGoodDetailDTOList = drunkHorseConverterService.convertPartRefundGoodDetailDTOs(partRefundGoodsDTOList);
			resp.setStatus(ResultGenerator.genSuccessResult())
					.setPartRefundGoodsList(partRefundGoodDetailDTOList);
		}
		return resp;
	}

	@Override
	public QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request) {
		return null;
	}

	@Override
	public QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request) {
		return null;
	}

	@Override
	@MethodLog(logRequest = true, logResponse = true)
	public ResultStatus updateDeliveryProofPhoto(UpdateDeliveryProofPhotoRequest request) {
		if(CollectionUtils.isEmpty(request.getPhotoUrlList())) {
			return new ResultStatus(ResultCode.FAIL.getCode(), "送达照片不能为空", null);
		}

		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setOrderId(request.getOrderId());

		ResultStatus resultStatus = new ResultStatus();
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		bizParam.put(ProjectConstant.APP_POI_CODE, onlinePoiId(request.getTenantId(), request.getChannelId(), request.getStoreId()));
		bizParam.put("pictures", Joiner.on(",").join(request.getPhotoUrlList()));

		String url = uploadArrivalPicturesUrl;
		log.info("DHChannelOrderServiceImpl.updateDeliveryProofPhoto  调用更新送达照片接口 baseRequest:{}   param:{}", baseRequest, bizParam);
		Map result = drunkHorseChannelGateService.sendPost(url, null, baseRequest, bizParam);
		log.info("DHChannelOrderServiceImpl.updateDeliveryProofPhoto  调用更新送达照片接口返回:{}", result);

		if (Objects.isNull(result) || "ng".equals(result.get(ProjectConstant.DATA))) {
			resultStatus.setCode(ResultCode.FAIL.getCode());
			resultStatus.setMsg(ResultCode.FAIL.getMsg());

		} else {
			resultStatus.setCode(ResultCode.SUCCESS.getCode());
			resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
		}
		return resultStatus;
	}

	public ResultStatus poiConfirmOrderFallBack(PoiConfirmOrderRequest request) {
		// 此处直连
		Map<Long, ChannelPoiInfoDTO> storeMap = channelPoiThriftServiceProxy
				.queryDrunkHorseChannelPoiInfos(request.getTenantId(), Lists.newArrayList(request.getStoreId()));

		boolean result = tspThriftServiceProxy.confirm2Tsp(Long.valueOf(request.getOrderId()), storeMap.get(request.getStoreId()));

		if (result) {
			return ResultGenerator.genSuccessResult();
		}
		return ResultGenerator.genFailResult("同步接单失败");
	}

	/**
	 * 此处处理单门店维度的批量日单号
	 * @param request
	 * @return
	 */
	public LatestDaySeqResult batchGetOrderByDaySeqFallBack(DaySeqRequest request)  {
		LatestDaySeqResult result = new LatestDaySeqResult();
		// 此处直连
		Map<Long, ChannelPoiInfoDTO> storeMap = channelPoiThriftServiceProxy
				.queryDrunkHorseChannelPoiInfos(request.getTenantId(), request.getStoreIds());

		List<ChannelPoiInfoDTO> channelPoiInfoDTOS = new ArrayList<>(storeMap.values());
		if (CollectionUtils.isEmpty(channelPoiInfoDTOS)) {
			result.setStatus(ResultGenerator.genFailResult("未查询到渠道门店id"));
			return result;
		}
		Map<Integer, Long> daySeqMap = tspThriftServiceProxy.batchGetOrderIdByDaySeq(
				Long.valueOf(channelPoiInfoDTOS.get(0).getChannelPoiId()),
				String.valueOf(request.getDateTime()),
				request.getDaySeqs());

		if (MapUtils.isNotEmpty(daySeqMap)) {
			result.setStatus(ResultGenerator.genSuccessResult());
			result.setOrderDaySeqInfos(daySeqMap.entrySet()
					.stream()
					.map(entry -> {
						OrderDaySeq daySeq = new OrderDaySeq();
						daySeq.setDaySeq(entry.getKey());
						daySeq.setOrderId(entry.getValue());
						return daySeq;
					})
					.collect(Collectors.toList()));
		} else {
			result.setStatus(ResultGenerator.genFailResult("未查询到日单号"));
		}
		return result;
	}

	public LatestDaySeqResult batchGetStoreLatestDaySeqFallBack(DaySeqRequest request)  {
		LatestDaySeqResult result = new LatestDaySeqResult();
		// 此处直连
		Map<Long, ChannelPoiInfoDTO> storeMap = channelPoiThriftServiceProxy
				.queryDrunkHorseChannelPoiInfos(request.getTenantId(), request.getStoreIds());

		Map<Long, Long> channelStoreMap = storeMap.values()
				.stream()
				.collect(Collectors.toMap(dto -> Long.valueOf(dto.getChannelPoiId()), ChannelPoiInfoDTO::getPoiId));
		List<ChannelPoiInfoDTO> channelPoiInfoDTOS = new ArrayList<>(storeMap.values());
		List<PoiSeqVo> poiSeqVos = tspThriftServiceProxy.batchGetLatestDaySeq(
				channelPoiInfoDTOS
						.stream()
						.map(dto -> Long.valueOf(dto.getChannelPoiId()))
						.collect(Collectors.toList()),
				String.valueOf(request.getDateTime()));

		if (CollectionUtils.isNotEmpty(poiSeqVos)) {
			result.setStatus(ResultGenerator.genSuccessResult());
			result.setOrderDaySeqInfos(
					poiSeqVos.stream()
							.map(it -> {
								OrderDaySeq daySeq = new OrderDaySeq();
								daySeq.setDaySeq(it.getSeq());
								// 转中台门店id
								daySeq.setStoreId(channelStoreMap.getOrDefault(it.getPoiId(), 0l));
								return daySeq;
							})
							.collect(Collectors.toList()));
		} else {
			result.setStatus(ResultGenerator.genFailResult("未查询到门店最新日单号"));
		}
		return result;
	}

	public GetChannelOrderDetailResult getChannelOrderDetailFallBack(GetChannelOrderDetailRequest request) {
		GetChannelOrderDetailResult result = new GetChannelOrderDetailResult();
		result.setStatus(ResultGenerator.genFailResult("查详情失败"));
		// 此处直连
		try {
			String data = tspThriftServiceProxy.getOrderDetail(Long.valueOf(request.getOrderId()));
			if (Objects.nonNull(data)) {
				result.setChannelOrderDetail(parseOrderForm(request.getTenantId(), request.getChannelId(), data, request.getSotreId()));
				result.setStatus(ResultGenerator.genSuccessResult());
				return result;
			}

		} catch (Exception e) {
			log.error("getChannelOrderDetailFallBack error", e);
		}
		return result;
	}



	private Map<String, Object> buildBusinessParam(UpdateDeliveryInfoRequest request, DrunkHorseLogisticSyncTypeEnum type) {
		Map<String, Object> param = Maps.newHashMap();
		param.put("order_id", request.getOrderId());
		param.put(ProjectConstant.APP_POI_CODE, onlinePoiId(request.getTenantId(), request.getChannelId(), request.getShopId()));

		if (StringUtils.isEmpty(request.getRiderName()) || StringUtils.isEmpty(request.getRiderPhone())) {
			log.warn("美团歪马更新配送信息，无骑手姓名和电话");
			return null;
		}

		// tsp只允许在"骑手已取货"状态同步骑手位置
		if (type == DrunkHorseLogisticSyncTypeEnum.ONLY_SYNC_RIDER_POSITION &&
				request.getStatus() != DeliveryStatus.RIDER_TAKEN_MEAL.getValue()) {
			log.warn("运单状态不是骑手已取货,不向歪马微商城同步骑手位置");
			return null;
		}
		param.put("courier_name", request.getRiderName());
		param.put("courier_phone", request.getRiderPhone());
		param.put("type", type.getCode());

		try {
			param.put("logistics_status", convertDeliveryStatus(request.getStatus()));
		} catch (ChannelBizException e) {
			log.warn("美团歪马渠道订单更新配送状态转换失败 status:{}", request.getStatus(), e);
			return null;
		}

		param.put("latitude", String.valueOf(request.getLatitude()));
		param.put("longitude", String.valueOf(request.getLongitude()));
		return param;
	}

	private int convertDeliveryStatus(int status) {
		DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(status);
		if (Objects.isNull(deliveryStatus)) {
			log.warn("歪马渠道无法处理配送状态:{}", status);
			throw new ChannelBizException("歪马渠道无法处理配送状态:" + status);
		}
		switch (deliveryStatus) {
			//已创建配送包裹
			case WAIT_DISPATCH_RIDER:
				return 1;
			//骑手已接单
			case RIDER_ACCEPTED_ORDER:
				return 10;
			//骑手已到店
			case RIDER_ARRIVE_SHOP:
				return 15;
			//骑手已取餐
			case RIDER_TAKEN_MEAL:
				return 20;
			//骑手已送达
			case DELIVERY_COMPLETED:
				return 40;
			//配送单已取消
			case DELIVERY_CANCEL:
				return 100;
			//无法处理，抛出异常
			default:
				throw new ChannelBizException("美团歪马渠道无法处理配送状态:" + status);
		}
	}

	private String onlinePoiId(long tenantId, int channelId, long storeId) {
		// 获取渠道门店编码
		String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
		Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
				.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
		if (MapUtils.isEmpty(channelStoreDOMap)) {
			throw new StoreIdNotExistException("未查询到配置门店");
		}
		return channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
	}

	private Map<Long, String> onlinePoiIds(long tenantId, int channelId, List<Long> storeIds) {
		// 获取渠道门店编码
		Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
				.getChannelPoiCode(tenantId, channelId, storeIds);
		if (MapUtils.isEmpty(channelStoreDOMap)) {
			throw new StoreIdNotExistException("未查询到配置门店");
		}
		return channelStoreDOMap.values().stream().collect(Collectors.toMap(ChannelStoreDO::getStoreId, ChannelStoreDO::getChannelOnlinePoiCode));

	}

	private String joinUserName(String name, Integer gender) {
		String append;
		if (Objects.nonNull(gender) && gender == 2) {
			append = "(女士)";
		} else {
			append = "(先生)";
		}
		return name + append;
	}

	private OrderDaySeq convert(DaySeqDTO dto, Map<String, Long> appCodeMap) {
		OrderDaySeq daySeq = new OrderDaySeq();
		daySeq.setDaySeq(dto.getDay_seq());
		daySeq.setStoreId(appCodeMap.get(dto.getApp_poi_code()));
		return daySeq;
	}

	private OrderDaySeq convert(DaySeqDTO dto, Long storeId) {
		OrderDaySeq daySeq = new OrderDaySeq();
		daySeq.setDaySeq(dto.getDay_seq());
		daySeq.setStoreId(storeId);
		daySeq.setOrderId(dto.getOrder_id());
		return daySeq;
	}

	@Override
	public ChannelOrderDetailDTO parseOrderForm(long tenantId, int channelId, String orderDetailForm) {
		return null;
	}

	@Override
	public ChannelOrderDetailDTO parseOrderForm(long tenantId, int channelId, Map<String, String> orderDetailMap) {
		return null;
	}

	/**
	 * 提取歪马各项优惠信息
	 * @param channelOrderDetailDTO
	 * @param orderActivitiesInfoList
	 */
	private void extractPromotionFromActivities(Long shopId, ChannelOrderDetailDTO channelOrderDetailDTO, List<OrderActivitiesInfo> orderActivitiesInfoList) {
		if (!MccConfigUtil.isDrunkMergeShop(shopId)) {
			// 非灰度租户，则跳过优惠均摊
			return;
		}
		int platPromotion = 0;
		int poiPromotion = 0;
		int platItemPromotion = 0;
		int poiItemPromotion = 0;
		int platLogisticPromotion = 0;
		int poiLogisticPromotion = 0;
		int platPackagePromotion = 0;
		int poiPackagePromotion = 0;
		if (CollectionUtils.isNotEmpty(orderActivitiesInfoList)){
			for (OrderActivitiesInfo orderActivitiesInfo : orderActivitiesInfoList){
				if (orderActivitiesInfo.getType() == null){
					continue;
				}
				int type = orderActivitiesInfo.getType();
				int mtCharge = Optional.ofNullable(orderActivitiesInfo.getMt_charge()).map(MoneyUtils::yuanToFen).orElse(0);
				int poiCharge = Optional.ofNullable(orderActivitiesInfo.getPoi_charge()).map(MoneyUtils::yuanToFen).orElse(0);
				if (MccConfigUtil.getDrunkHorseItemPromotionActivityCodeList().contains(type)){
					platItemPromotion += mtCharge;
					poiItemPromotion += poiCharge;
				} else if (MccConfigUtil.getDrunkHorseLogisticPromotionActivityCodeList().contains(type)){
					platLogisticPromotion += mtCharge;
					poiLogisticPromotion += poiCharge;
				} else if (MccConfigUtil.getDrunkHorsePackagePromotionActivityCodeList().contains(type)){
					platPackagePromotion += mtCharge;
					poiPackagePromotion += poiCharge;
				} else {
					platPromotion += mtCharge;
					poiPromotion += poiCharge;
				}
			}
		}
		channelOrderDetailDTO.setPlatPromotion(platPromotion);
		channelOrderDetailDTO.setPoiPromotion(poiPromotion);
		channelOrderDetailDTO.setPlatItemPromotion(platItemPromotion);
		channelOrderDetailDTO.setPoiItemPromotion(poiItemPromotion);
		channelOrderDetailDTO.setPlatLogisticsPromotion(platLogisticPromotion);
		channelOrderDetailDTO.setPoiLogisticsPromotion(poiLogisticPromotion);
		channelOrderDetailDTO.setPlatPackagePromotion(platPackagePromotion);
		channelOrderDetailDTO.setPoiPackagePromotion(poiPackagePromotion);

		// 歪马微商城无平台包装费收入
		channelOrderDetailDTO.setPlatPackageAmt(-platPackagePromotion);
		// 商家包装费收入=包装费- 优惠金额商家承担
		channelOrderDetailDTO.setPoiPackageAmt(channelOrderDetailDTO.getPackageAmt() - poiPackagePromotion);
		channelOrderDetailDTO.setPayPackageAmt(channelOrderDetailDTO.getPackageAmt() - poiPackagePromotion - platPackagePromotion);
		// 微商城商家运费收入 = 应收配送费 - 运费商家优惠 + 地址变更费(微商城无值)
		channelOrderDetailDTO.setPoiLogisticsIncome(channelOrderDetailDTO.getFreight() - poiLogisticPromotion);
	}

	private void extractSkuPromotionFromSkuBenefitV3(Long shopId, List<OrderProductDetailDTO> skuDetails, List<SkuBenefitDetail> skuBenefitDetails, Long tenantId) {
		if (!MccConfigUtil.isDrunkMergeShop(shopId)) {
			// 非灰度租户，则跳过优惠均摊
			return;
		}
		log.info("extractSkuPromotionFromSkuBenefitV3");
		if (CollectionUtils.isEmpty(skuBenefitDetails)){
			return;
		}
		for (SkuBenefitDetail skuBenefitDetail : skuBenefitDetails){
			if (CollectionUtils.isEmpty(skuBenefitDetail.getWmAppOrderActDetails())){
				continue;
			}
			List<OrderProductDetailDTO> targetSkuDetails = skuDetails.stream()
					.filter(sku -> Objects.equals(sku.getSkuId(), skuBenefitDetail.getSku_id()) && Objects.equals(sku.getCustomSpu(), skuBenefitDetail.getApp_food_code()))
					.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(targetSkuDetails)){
				//todo找不到匹配商品明细，出错
				continue;
			}
			int totalPlatPromotion = MoneyUtils.yuanToFen(skuBenefitDetail.getTotalMtCharge());
			int totalPoiPromotion = MoneyUtils.yuanToFen(skuBenefitDetail.getTotalPoiCharge());

			List<WmAppOrderActDetail> itemActDetails = skuBenefitDetail.getWmAppOrderActDetails().stream()
					.filter(act -> MccConfigUtil.getDrunkHorseItemPromotionActivityCodeList().contains(act.getType()))
					.collect(Collectors.toList());
			//单品优惠
			Set<WmAppOrderActDetail> usedItemActDetails = new HashSet<>();
			for (OrderProductDetailDTO productDetailDTO : targetSkuDetails){
				if (CollectionUtils.isEmpty(itemActDetails)){
					productDetailDTO.setActualSalePrice(productDetailDTO.getOriginalPrice());
					continue;
				}
				//无单品优惠
				if(Objects.equals(productDetailDTO.getOriginalPrice(), productDetailDTO.getSalePrice())){
					continue;
				}
				if (targetSkuDetails.size() == 1){
					int platItemPromotion = 0;
					int poiItemPromotion = 0;
					for (WmAppOrderActDetail itemActDetail : itemActDetails){
						platItemPromotion += MoneyUtils.yuanToFen(itemActDetail.getMtCharge()) * itemActDetail.getCount();
						poiItemPromotion += MoneyUtils.yuanToFen(itemActDetail.getPoiCharge()) * itemActDetail.getCount();
					}
					productDetailDTO.setPlatItemPromotion(platItemPromotion);
					productDetailDTO.setPoiItemPromotion(poiItemPromotion);
					totalPlatPromotion -= platItemPromotion;
					totalPoiPromotion -= poiItemPromotion;
				} else {
					//一个sku只能参加一个单品优惠
					for (WmAppOrderActDetail itemActDetail : itemActDetails){
						if (usedItemActDetails.contains(itemActDetail)){
							continue;
						}
						if (itemActDetail.getCount() == productDetailDTO.getQuantity()) {
							int actualSalePrice = productDetailDTO.getOriginalPrice() - MoneyUtils.yuanToFen(itemActDetail.getMtCharge()) - MoneyUtils.yuanToFen(itemActDetail.getPoiCharge());
							int platItemPromotion = MoneyUtils.yuanToFen(itemActDetail.getMtCharge()) * itemActDetail.getCount();
							int poiItemPromotion = MoneyUtils.yuanToFen(itemActDetail.getPoiCharge()) * itemActDetail.getCount();
							productDetailDTO.setPlatItemPromotion(platItemPromotion);
							productDetailDTO.setPoiItemPromotion(poiItemPromotion);
							productDetailDTO.setActualSalePrice(actualSalePrice);
							totalPlatPromotion -= platItemPromotion;
							totalPoiPromotion -= poiItemPromotion;
							usedItemActDetails.add(itemActDetail);
							break;
						}
					}
				}
			}
			// 复制一份。累积减的时候使用，不能影响原值。
			int lastPoiPromotion = totalPoiPromotion;
			int lastPlatPromotion = totalPlatPromotion;
			int totalQuantity = targetSkuDetails.stream().mapToInt(OrderProductDetailDTO::getQuantity).sum();
			int totalShare = targetSkuDetails.stream().map(v->v.getOriginalPrice() * v.getQuantity() - v.getPoiItemPromotion() - v.getPlatItemPromotion()).reduce(0, Integer::sum);

			//整单优惠
			for (int index = 0; index < targetSkuDetails.size(); index++){
				OrderProductDetailDTO productDetailDTO = targetSkuDetails.get(index);
				if (index == targetSkuDetails.size() - 1){
					productDetailDTO.setPoiPromotion(lastPoiPromotion);
					productDetailDTO.setPlatPromotion(lastPlatPromotion);
				} else {
					BigDecimal percent;
					if (MccConfigUtil.splitOrderPromotionBySalePrice(tenantId)) {
						int saleMoney = productDetailDTO.getOriginalPrice() * productDetailDTO.getQuantity() - productDetailDTO.getPoiItemPromotion() - productDetailDTO.getPlatItemPromotion();
						percent = BigDecimal.valueOf(saleMoney).divide(BigDecimal.valueOf(totalShare),4, RoundingMode.HALF_UP);
					} else {
						percent = BigDecimal.valueOf(productDetailDTO.getQuantity()).divide(BigDecimal.valueOf(totalQuantity),4, RoundingMode.HALF_UP);
					}
					int poiPromotion = BigDecimal.valueOf(totalPoiPromotion).multiply(percent).setScale(0, RoundingMode.HALF_UP).intValue();
					int platItemPromotion = BigDecimal.valueOf(totalPlatPromotion).multiply(percent).setScale(0, RoundingMode.HALF_UP).intValue();
					productDetailDTO.setPoiPromotion(poiPromotion);
					productDetailDTO.setPlatPromotion(platItemPromotion);
					lastPoiPromotion -= poiPromotion;
					lastPlatPromotion -= platItemPromotion;
				}
			}
		}
	}

}
