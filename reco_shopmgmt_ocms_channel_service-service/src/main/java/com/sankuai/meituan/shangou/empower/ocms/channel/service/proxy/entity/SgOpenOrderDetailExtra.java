package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

/**
 * Created by chenchong
 */
public class SgOpenOrderDetailExtra {

    /**
     * 套餐明细
     */
    private String packageDeatil;
    /**
     * 分类名称
     */
    private String cate;
    /**
     * 商品分类，是否为套餐，1套餐商品，非1单品
     */
    private Integer attr_type;

    public String getPackageDeatil() {
        return packageDeatil;
    }

    public void setPackageDeatil(String packageDeatil) {
        this.packageDeatil = packageDeatil;
    }

    public String getCate() {
        return cate;
    }

    public void setCate(String cate) {
        this.cate = cate;
    }

    public Integer getAttr_type() {
        return attr_type;
    }

    public void setAttr_type(Integer attr_type) {
        this.attr_type = attr_type;
    }

    @Override
    public String toString() {
        return "WmOpenOrderDetailExtra{" +
                "packageDeatil='" + packageDeatil + '\'' +
                ", cate='" + cate + '\'' +
                ", attr_type=" + attr_type +
                '}';
    }
}
