package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dianping.zebra.util.StringUtils;
import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.linz.thrift.response.ThriftResponse;
import com.meituan.linz.thrift.response.ThriftResponseBuilder;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinProductUpdateRuleParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinProductUpdateRuleResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ChannelProductUpdateRuleDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ChannelProductUpdateRuleRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelProductUpdateRuleService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;

@Service("dyChannelProductUpdateRuleService")
public class DouyinProductUpdateRuleServiceImpl implements ChannelProductUpdateRuleService {

    @Autowired
    private DouyinChannelGateService douyinChannelGateService;


    @Override
    public ThriftResponse<ChannelProductUpdateRuleDTO> queryChannelProductUpdateRule(ChannelProductUpdateRuleRequest request) {

        Assert.throwIfNull(request,"请求为空");
        Assert.throwIfBlank(request.getCategoryId(),"渠道类目id为空");

        BaseRequestSimple baseRequest = new BaseRequestSimple();
        baseRequest.setChannelId(request.getChannelId());
        baseRequest.setTenantId(request.getTenantId());

        ChannelResponseDTO<DouyinProductUpdateRuleResult> channelResponseDTO =
                douyinChannelGateService.sendPost(ChannelPostDouyinEnum.GET_PRODUCT_UPDATE_RULE, baseRequest, null,
                        DouyinProductUpdateRuleParam.builder()
                                .category_id(Long.parseLong(request.getCategoryId()))
                                .standard_brand_id(StringUtils.isNotBlank(request.getBrandId())?Long.parseLong(request.getBrandId()):null)
                                .spu_id(StringUtils.isNotBlank(request.getSpuId())?Long.parseLong(request.getSpuId()):null)
                                .build()
                );

        Bssert.throwIfTrue(!channelResponseDTO.isSuccess(),"查询渠道接口失败");

        ThriftResponse<ChannelProductUpdateRuleDTO> response = new ThriftResponse<>();

        return ThriftResponseBuilder.buildSuccessResp(DouyinConvertUtil.convertToChannelProductUpdateRuleDTO(channelResponseDTO.getCoreData()));
    }
}
