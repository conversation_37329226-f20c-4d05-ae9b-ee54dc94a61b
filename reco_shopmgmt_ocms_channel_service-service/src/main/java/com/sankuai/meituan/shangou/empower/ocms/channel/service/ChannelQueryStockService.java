package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.model.ChannelProductStockCheckDto;

import java.util.List;

/**
 * 渠道查询库存服务
 * @author: goulei02
 * @date: 2024/8/7
 */
public interface ChannelQueryStockService {

    /**
     * 根据渠道商品及下单量查询库存牵牛花库存（牵牛花B涉及下单量）
     * @param appId 应用id
     * @param appPoiCode 开放平台/渠道门店id
     * @param channelProductCountList 渠道商品校验库存信息列表
     * @return
     */
    List<ChannelProductStockCheckDto> queryStock(String appId, String appPoiCode, List<ChannelProductStockCheckDto> channelProductCountList);
}
