package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine;

import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/11/03
 */
@Service("mtMedicineChannelCommonService")
public class MtMedicineChannelCommonServiceImpl extends MtChannelBaseServiceImpl {

    @Autowired
    private MtMedicineChannelGateService mtMedicineChannelGateService;

    /**
     * @return 美团医药
     */
    @Override
    public ChannelTypeEnum currentChannelType() {
        return ChannelTypeEnum.MT_MEDICINE;
    }

    @Override
    public BaseChannelGateService getChannelGateService() {
        return mtMedicineChannelGateService;
    }
}
