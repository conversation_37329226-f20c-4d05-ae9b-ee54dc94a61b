package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ChannelErrorCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStockQueryByCustomDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStoreStockDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JDChannelSkuInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JDSkuInfoListResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JDSpuInfoListResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JdChannelSpuInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ManageProductShopCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSpuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BasePageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.ChannelEnum;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.SaleStatusEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Author: luokai14
 * @Date: 2022/8/17 11:01 上午
 * @Mail: <EMAIL>
 */
@Slf4j
@Service("jddjChannelSpuService")
public class JddjChannelSpuServiceImpl implements ChannelSpuService {

    /**
     * 替换店内分类
      */
    public static final int OP_REPLACE = 3;
    @Resource
    JddjChannelGateService jddjChannelGateService;

    @Resource
    JddjChannelSkuServiceImpl jddjChannelSkuService;

    @Resource
    JddjChannelStockServiceImpl jddjChannelStockService;

    @Resource
    JddjChannelPriceServiceImpl jddjChannelPriceService;

    @Resource
    CopChannelStoreService copChannelStoreService;

    @Resource
    BaseConverterService baseConverterService;

    @Autowired
    private JddjChannelAppIdUtils jddjChannelAppIdUtils;

    @Autowired
    private JddjChannelAttrServiceImpl jddjChannelAttrService;

    @Resource(name = "jddjProductThreadPool")
    private ExecutorService jddjProductThreadPool;

    @Override
    public ResultSpuData updateWeightBySpu(SpuWeightInfoRequest request) {
        return null;
    }

    /**
     * 百川门店渠道商品创建/更新等价于京东到家门店商品变更可售状态,同时设置库存
     *
     * @param request
     * @return
     */
    @Override
    public ResultSpuData spuCreate(SpuInfoRequest request) {
        return saveStoreSpuInfo(request, false, true);
    }

    @Override
    public ResultSpuData spuCreateForCleaner(SpuInfoRequest request) {
        return saveStoreSpuInfo(request, true, true);
    }

    /**
     * 保存任务接口
     *
     * @param request
     * @param isCleaner
     * @param isCreate
     * @return
     */
    private ResultSpuData saveStoreSpuInfo(SpuInfoRequest request, boolean isCleaner, boolean isCreate) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        List<SpuInfoDTO> spuList = request.getParamList();
        if (CollectionUtils.isEmpty(spuList)) {
            return resultData;
        }

        try {
            Map<String, SpuKey> spuKeyMap = Fun.toMap(spuList, SpuInfoDTO::getSpuId, this::getSpuKey);
            if (request.isJddStoreManageCategory) {
                spuList = checkShopCategoryEmpty(request, spuList, spuKeyMap, resultData);
            }
            // 更新价格和库存，并填充ResultSpuData
            List<SpuInfoDTO> priceStockAllSuccessSpus = updatePriceStockAndFillResult(request, spuList, spuKeyMap, resultData, isCreate, isCleaner);
            // 更新自定义属性
            updateProperties(request, resultData,isCleaner);

            // 价格和库存全部成功，再更新上下架，并填充ResultSpuData
            updateSaleStatusAndFillResult(request, priceStockAllSuccessSpus, spuKeyMap, resultData, isCreate, isCleaner);

            if (request.isJddStoreManageCategory) {
                updateProductShopCategoryAndFillResult(request, spuKeyMap, resultData);
            }
        } catch (IllegalArgumentException e) {
            log.error("JddjChannelSpuServiceImpl.updateRegionSpu 参数校验失败, data:{}", spuList, e);
            ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), Fun.map(spuList, this::getSpuKey));
        } catch (Exception e) {
            log.error("JddjChannelSpuServiceImpl.updateRegionSpu 服务异常, data:{}", spuList, e);
            ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), Fun.map(spuList, this::getSpuKey));
        }

        return resultData;
    }

    private List<SpuInfoDTO> checkShopCategoryEmpty(SpuInfoRequest request,
                                                    List<SpuInfoDTO> spuList, Map<String, SpuKey> spuKeyMap,
                                                    ResultSpuData resultData) {
        if (CollectionUtils.isEmpty(spuList)) {
            return spuList;
        }

        // 没有店内分类的spu写入失败记录
        spuList.stream().filter(spuInfo -> CollectionUtils.isEmpty(spuInfo.getLeafStoreCategoryList()))
                .forEach(spuInfo -> {
                    SpuKey spuKey = spuKeyMap.get(spuInfo.getSpuId());
                    if (Objects.isNull(spuKey)) {
                        return;
                    }
                    resultData.addToErrorData(new ResultErrorSpu().setChannelId(ChannelEnum.JDDJ.getCode())
                            .setSpuInfo(spuKey)
                            .setErrorCode(ResultCode.FAIL.getCode())
                            .setStoreId(request.getBaseInfo().getStoreIdList().get(0))
                            .setErrorMsg("线上店内分类为空"));
                });

        return Fun.filter(spuList, spuInfo -> CollectionUtils.isNotEmpty(spuInfo.getLeafStoreCategoryList()));
    }

    private void updateProductShopCategoryAndFillResult(SpuInfoRequest request, Map<String, SpuKey> spuKeyMap, ResultSpuData resultData) {

        List<Future<Pair<SpuInfoDTO, ChannelResponseDTO>>> futureList = request.getParamList().stream()
                .filter(spuInfoDTO -> CollectionUtils.isNotEmpty(spuInfoDTO.getLeafStoreCategoryList()))
                .map(spuInfoDTO -> jddjProductThreadPool.submit(() -> {
                    return doCallUpdateProductShopCategory(request, spuInfoDTO);
                })).collect(Collectors.toList());

        futureList.forEach(future -> {
            try {
                Pair<SpuInfoDTO, ChannelResponseDTO> resultPair = future.get(3, TimeUnit.SECONDS);
                SpuInfoDTO spuInfoDTO = resultPair.getKey();
                ChannelResponseDTO responseDTO = resultPair.getValue();

                // 解析操作结果
                if (Objects.isNull(responseDTO) || !responseDTO.isSuccess()) {
                    ChannelResponseResult<String> innerResult =
                            Optional.ofNullable(responseDTO).map(ChannelResponseDTO::getDataResponse).orElse(null);
                    if (Objects.nonNull(innerResult)) {
                        String msg = Optional.ofNullable(innerResult).map(ChannelResponseResult::getMsg).orElse("京东更新商品店内分类未知错误");
                        String code = Optional.ofNullable(innerResult).map(ChannelResponseResult::getCode).orElse("-1");
                        ProductChannelUnifyErrorEnum resErrorEnum =
                                ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.JD2HOME, code, msg);

                        resultData.addToErrorData(new ResultErrorSpu().setChannelId(ChannelEnum.JDDJ.getCode())
                                .setSpuInfo(spuKeyMap.get(spuInfoDTO.getSpuId()))
                                .setErrorCode(Integer.valueOf(code))
                                .setChannelUnifyError(resErrorEnum)
                                .setStoreId(request.getBaseInfo().getStoreIdList().get(0))
                                .setErrorMsg(msg));
                        return;
                    }

                    String msg = Optional.ofNullable(responseDTO).map(ChannelResponseDTO::getMsg).orElse("京东更新商品店内分类未知错误");
                    String code = Optional.ofNullable(responseDTO).map(ChannelResponseDTO::getCode).orElse("-1");
                    ProductChannelUnifyErrorEnum resErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.JD2HOME, code, msg);

                    resultData.addToErrorData(new ResultErrorSpu().setChannelId(ChannelEnum.JDDJ.getCode())
                            .setSpuInfo(spuKeyMap.get(spuInfoDTO.getSpuId()))
                            .setErrorCode(Integer.valueOf(code))
                            .setChannelUnifyError(resErrorEnum)
                            .setStoreId(request.getBaseInfo().getStoreIdList().get(0))
                            .setErrorMsg(msg));
                }
            }
            catch (Exception e) {
                log.warn("京东更新商品店内分类未知异常", e);
                throw new BizException("京东更新商品店内分类未知异常:" + e.getMessage());
            }
        });
    }

    private Pair<SpuInfoDTO, ChannelResponseDTO> doCallUpdateProductShopCategory(SpuInfoRequest request, SpuInfoDTO spuInfoDTO) {
        ManageProductShopCategoryDTO bizData = new ManageProductShopCategoryDTO();
        bizData.setOutStationNo("{}");
        if (SpecTypeEnum.MULTI.getCode() == spuInfoDTO.specType) {
            bizData.setOutSpuId(spuInfoDTO.getCustomSpuId());
        }
        else {
            bizData.setOutSkuId(spuInfoDTO.getCustomSpuId());
        }
        bizData.setShopCategories(spuInfoDTO.getLeafStoreCategoryList().stream().map(ChannelLeafStoreCategory::getCategoryCode).map(Long::valueOf).collect(Collectors.toList()));
        // 覆盖操作
        bizData.setOpType(OP_REPLACE);
        bizData.setOpPin("qnh_ocmschannel");

        Map<Long, ChannelResponseDTO<String>> responseDTOMap =
                jddjChannelGateService.sendPostAppMapDto(ChannelPostJDDJEnum.MANAGE_PRODUCT_SHOP_CATEGORY, request.getBaseInfo(), bizData);
        ChannelResponseDTO<String> responseDTO = Optional.ofNullable(responseDTOMap).map(map -> map.get(request.getBaseInfo().getStoreIdList().get(0))).orElse(null);

        return Pair.of(spuInfoDTO, responseDTO);
    }

    private ResultData updateProperties(SpuInfoRequest request, ResultSpuData resultData,boolean isCleaner) {
        if(MccConfigUtil.notUpdateJddjProperties(request.getBaseInfo().getTenantId())){
            return ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        }
        return isCleaner ? jddjChannelAttrService.updateAttrForCleaner(request)
                : jddjChannelAttrService.updateAttr(request);
    }

    private List<SpuInfoDTO> updatePriceStockAndFillResult(SpuInfoRequest request, List<SpuInfoDTO> spuList,
                                                           Map<String, SpuKey> spuKeyMap, ResultSpuData resultData,
                                                           boolean isCreate, boolean isCleaner) {
        if (CollectionUtils.isEmpty(spuList)) {
            return Collections.emptyList();
        }
        // 获取设置库存和价格的spu列表
        List<SpuInfoDTO> needUpdatePriceStocks = getNeedUpdatePriceStocks(isCreate, spuList);

        ResultData priceData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        ResultData stockResult = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isNotEmpty(needUpdatePriceStocks)) {
            // 更新价格
            SkuPriceMultiChannelRequest priceRequest = JddjConvertUtil.conver2SkuPriceRequestBySpus(request.getBaseInfo(), needUpdatePriceStocks);
            priceData = isCleaner ? jddjChannelPriceService.updatePriceMultiChannelForCleaner(priceRequest)
                    : jddjChannelPriceService.updatePriceMultiChannel(priceRequest);
            // 更新库存
            SkuStockRequest stockRequest = JddjConvertUtil.conver2SkuStockRequestBySpus(request.getBaseInfo(), needUpdatePriceStocks);
            stockResult = isCleaner ? jddjChannelStockService.updateStockForCleaner(stockRequest)
                    : jddjChannelStockService.updateStock(stockRequest);
        }

        // 需要更新上下架的商品
        List<SpuInfoDTO> allSuccessSpus = new ArrayList<>();
        for (SpuInfoDTO spuInfo : spuList) {
            SpuKey spuKey = spuKeyMap.get(spuInfo.getSpuId());
            List<String> skuIds = Fun.map(spuInfo.getSkus(), SkuInSpuInfoDTO::getCustomSkuId);

            // 获取该spu下所有sku的库存失败信息，价格失败信息
            List<ResultErrorSku> priceErrors = Fun.filter(priceData.getErrorData(), error -> skuIds.contains(error.getSkuId()));
            List<ResultErrorSku> stockErrors = Fun.filter(stockResult.getErrorData(), error -> skuIds.contains(error.getSkuId()));

            if (CollectionUtils.isNotEmpty(stockErrors)) {
                log.warn("库存同步失败 spuInfo:{}, stockErrors:{}", spuInfo, stockErrors);
                // 填充库存失败信息
                convertAndMerge2SpuResultByErrorSkus(resultData, stockErrors, spuKey, false);
            } else if (CollectionUtils.isNotEmpty(priceErrors)) {
                log.warn("价格同步失败 spuInfo:{}, priceErrors:{}", spuInfo, priceErrors);
                // 填充价格失败信息
                convertAndMerge2SpuResultByErrorSkus(resultData, priceErrors, spuKey, false);
            } else {
                // 所有sku的价格和库存更新成功的spu
                allSuccessSpus.add(spuInfo);
            }
        }
        return allSuccessSpus;
    }

    private List<SpuInfoDTO> getNeedUpdatePriceStocks(boolean isCreate, List<SpuInfoDTO> spuList) {
        // 获取设置库存和价格的spu列表
        List<SpuInfoDTO> needUpdateSpuList;
        if (isCreate) {
            // 创建场景：所有规格都是新增的
            needUpdateSpuList = spuList;
        } else {
            // 更新场景：筛选出新增的规格
            needUpdateSpuList = new ArrayList<>();
            spuList.forEach(spuInfoDTO -> {
                List<SkuInSpuInfoDTO> createSkus = spuInfoDTO.getSkus().stream()
                        .filter(sku -> sku.getChangeType() == SkuChangeTypeEunm.CREATE.getCode())
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(createSkus)) {
                    SpuInfoDTO newSpuInfoDTO = new SpuInfoDTO(spuInfoDTO);
                    newSpuInfoDTO.setSkus(createSkus);
                    needUpdateSpuList.add(newSpuInfoDTO);
                }
            });
        }
        return needUpdateSpuList;
    }

    private void updateSaleStatusAndFillResult(SpuInfoRequest request, List<SpuInfoDTO> updateSaleStatusSpus,
                                               Map<String, SpuKey> spuKeyMap, ResultSpuData resultData,
                                               boolean isCreate, boolean isCleaner) {
        if (CollectionUtils.isEmpty(updateSaleStatusSpus)) {
            return;
        }
        // 更新上下架
        List<ResultErrorSku> saleStatusErrors = new ArrayList<>();
        Fun.groupingBy(updateSaleStatusSpus, SpuInfoDTO::getStatus)
                .forEach((status, batchUpdateSaleStatusSpus) -> {
                    // 门店商品本身就是初始为下架状态，工具情况下，初始化门店商品上下架时，不重复更新
                    if (isCreate && isCleaner && SaleStatusEnum.OFF_SALE.getCode() == status) {
                        return;
                    }
                    List<SpuKey> spuKeys = Fun.map(batchUpdateSaleStatusSpus, spu -> spuKeyMap.get(spu.getSpuId()));
                    SkuSellStatusInfoRequest statusRequest = JddjConvertUtil.Conver2SkuSellStatusInfoRequest(request.getBaseInfo(), spuKeys, status);
                    ResultData skuResult = isCleaner ? jddjChannelSkuService.updateSkuSellStatusForCleaner(statusRequest) :
                            jddjChannelSkuService.updateSkuSellStatus(statusRequest);
                    if (CollectionUtils.isNotEmpty(skuResult.getErrorData())) {
                        log.warn("上下架同步失败 statusRequest:{},errorResult:{}", statusRequest, skuResult.getErrorData());
                        saleStatusErrors.addAll(skuResult.getErrorData());
                    }
                });

        // spu纬度填充上下架失败信息
        for (SpuInfoDTO spuInfo : updateSaleStatusSpus) {
            SpuKey spuKey = spuKeyMap.get(spuInfo.getSpuId());
            List<String> skuIds = Fun.map(spuInfo.getSkus(), SkuInSpuInfoDTO::getCustomSkuId);
            List<ResultErrorSku> spuSaleStatusErrors = Fun.filter(saleStatusErrors, error -> skuIds.contains(error.getSkuId()));
            convertAndMerge2SpuResultByErrorSkus(resultData, spuSaleStatusErrors, spuKey, false);
        }
    }

    private ResultSpuData convertAndMerge2SpuResult(ResultSpuData resultData, ResultData skuResultData, SpuKey spuKey,
                                                    boolean isDelOpt) {
        return convertAndMerge2SpuResultByErrorSkus(resultData, skuResultData.getErrorData(), spuKey, isDelOpt);
    }


    private ResultSpuData convertAndMerge2SpuResultByErrorSkus(ResultSpuData resultData, List<ResultErrorSku> errorDatas, SpuKey spuKey,
                                                    boolean isDelOpt) {
        // 若是删除逻辑，并且渠道侧删除失败，当渠道侧商品已删除时，删除返回成功
        if (isDelOpt && CollectionUtils.isNotEmpty(errorDatas)) {
            boolean  isDelSuccess = errorDatas.stream()
                    .allMatch(sku -> MccConfigUtil.getJddjNotExistStoreSpuMsgs().contains(sku.getErrorMsg()));
            if (isDelSuccess) {
                resultData.addToSucData(new ResultSuccessSpu().setSpuInfo(spuKey).setChannelId(ChannelEnum.JDDJ.getCode()));
                return resultData;
            }
        }

        //合并sku返回信息
        if (CollectionUtils.isNotEmpty(errorDatas)) {
            StringBuilder sb = new StringBuilder();
            errorDatas.forEach(skuError -> {
                sb.append("skuId:");
                sb.append(skuError.getSkuId());
                sb.append(skuError.getErrorMsg());
                sb.append(",");
            });

            //填充统一渠道错误码
            String channelErrorCode = null;
            if (CollectionUtils.isNotEmpty(errorDatas)) {
                channelErrorCode = String.valueOf(errorDatas.get(0).getErrorCode());
            }
            ProductChannelUnifyErrorEnum resErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.JD2HOME, channelErrorCode,
                sb.toString());
            int errorCode = Optional.ofNullable(errorDatas.get(0)).map(ResultErrorSku::getErrorCode).orElse(ResultCode.FAIL.getCode());
            Long storeId = Optional.ofNullable(errorDatas.get(0)).map(ResultErrorSku::getStoreId).orElse(0L);
            resultData.addToErrorData(new ResultErrorSpu().setChannelId(ChannelEnum.JDDJ.getCode())
                    .setSpuInfo(spuKey)
                    .setErrorCode(errorCode)
                    .setChannelUnifyError(resErrorEnum)
                    .setStoreId(storeId)
                    .setErrorMsg(sb.toString()));
        } else {
            resultData.addToSucData(new ResultSuccessSpu().setSpuInfo(spuKey).setChannelId(ChannelEnum.JDDJ.getCode()).setStoreId(spuKey.getStoreId()));
        }
        return resultData;
    }


    private SpuKey getSpuKey(SpuInfoDTO data) {
        SpuKey spuKey = new SpuKey().setCustomSpuId(data.getCustomSpuId());
        List<SkuKey> skuKeys = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(data.getSkus())) {
            data.getSkus().forEach(skuInSpuInfoDTO -> {
                SkuKey skuKey = new SkuKey().setCustomSkuId(skuInSpuInfoDTO.getCustomSkuId());
                skuKeys.add(skuKey);
            });
        }
        spuKey.setSkus(skuKeys);
        return spuKey;
    }

    @Override
    public ResultSpuData upcCreate(SpuInfoRequest request) {
        return upcCreateCommon(request, false);
    }

    @Override
    public ResultSpuData upcCreateForCleaner(SpuInfoRequest request) {
        return upcCreateCommon(request, true);
    }

    private ResultSpuData upcCreateCommon(SpuInfoRequest request, boolean isCleaner) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        request.getParamList().forEach(item -> {
            SpuKey spuKey = getSpuKey(item);
            try {
                //更新库存
                SkuStockRequest stockRequest = JddjConvertUtil.conver2SkuStockRequestBySpus(request.getBaseInfo(), Arrays.asList(item));
                ResultData stockResult = isCleaner ? jddjChannelStockService.updateStockForCleaner(stockRequest) : jddjChannelStockService.updateStock(stockRequest);
                //更新库存失败，则直接返回失败
                if (CollectionUtils.isNotEmpty(stockResult.getErrorData())) {
                    convertAndMerge2SpuResult(resultData, stockResult, spuKey, false);
                } else {
                    //更新可售状态
                    SkuSellStatusInfoRequest statusRequest = JddjConvertUtil.Conver2SkuSellStatusInfoRequest(request.getBaseInfo(), Arrays.asList(spuKey), item.getStatus());
                    ResultData skuResult = isCleaner ? jddjChannelSkuService.updateSkuSellStatusForCleaner(statusRequest) : jddjChannelSkuService.updateSkuSellStatus(statusRequest);
                    // 结果组装
                    convertAndMerge2SpuResult(resultData, skuResult, spuKey, false);
                }
            } catch (IllegalArgumentException e) {
                log.error("JddjChannelSpuServiceImpl.upcCreate 参数校验失败,isCleaner:{}, data:{}", isCleaner, item, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), Lists.newArrayList(spuKey));
            } catch (Exception e) {
                log.error("JddjChannelSpuServiceImpl.upcCreate 服务异常, isCleaner:{}, data:{}", isCleaner, item, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), Lists.newArrayList(spuKey));
            }
        });
        return resultData;
    }

    @Override
    public ResultSpuData updateBySpuOrUpc(SpuInfoRequest request) {
        return saveStoreSpuInfo(request, false, false);
    }

    @Override
    public ResultSpuData updateBySpuOrUpcForCleaner(SpuInfoRequest request) {
        return saveStoreSpuInfo(request, true, false);
    }

    //删除渠道商品对应京东商品sku设置为不可售
    @Override
    public ResultSpuData deleteSpu(SpuInfoDeleteRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }

        if (MccConfigUtil.getJddjBatchDeleteSwitch(request.getBaseInfo().getTenantId())) {
            return deleteBatchSpu(request);
        }
        request.getParamList().forEach(item -> {
            SpuKey spuKey = item.getSpuKey();
            try {
                ResultData skuResult = jddjChannelSkuService.updateSkuSellStatus(JddjConvertUtil.Conver2SkuSellStatusInfoRequest(request.getBaseInfo(), Lists.newArrayList(spuKey), item.getStoreId(), 2));
                // 结果组装
                convertAndMerge2SpuResult(resultData, skuResult, spuKey, true);
            } catch (IllegalArgumentException e) {
                log.error("JddjChannelSpuServiceImpl.deleteSpu 参数校验失败, data:{}", item, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), Lists.newArrayList(spuKey));
            } catch (Exception e) {
                log.error("JddjChannelSpuServiceImpl.deleteSpu 服务异常, data:{}", item, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), Lists.newArrayList(spuKey));
            }
        });
        return resultData;
    }

    public ResultSpuData deleteBatchSpu(SpuInfoDeleteRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        Fun.groupingBy(request.getParamList(), SpuInfoDeleteDTO::getStoreId).forEach((storeId, oneStoreSpuList) -> {
            List<SpuKey> spuKeys = Fun.map(oneStoreSpuList, SpuInfoDeleteDTO::getSpuKey);
            if (CollectionUtils.isEmpty(spuKeys)) {
                return;
            }
            //更新上下架状态为下架
            ResultData skuResult = jddjChannelSkuService.updateSkuSellStatus(JddjConvertUtil.Conver2SkuSellStatusInfoRequest(request.getBaseInfo(), spuKeys,
                    storeId, 2));
            // 组装结果
            for (SpuKey spuKey : spuKeys) {
                List<String> skuIds = Fun.map(spuKey.getSkus(), SkuKey::getCustomSkuId);
                List<ResultErrorSku> spuSaleStatusErrors = Fun.filter(skuResult.getErrorData(), error -> skuIds.contains(error.getSkuId()));
                convertAndMerge2SpuResultByErrorSkus(resultData, spuSaleStatusErrors, spuKey, true);
            }
        });
        return resultData;
    }

    @Override
    public ResultSpuData deleteCategoryAndSpu(BaseRequest request) {
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量删除分类和商品服务失败");
    }

    @Override
    public ResultSpuData deleteSku(SkuInSpuInfoDeleteRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        //分门店删除
        request.getParamList().stream().collect(Collectors.groupingBy(SkuInSpuInfoDeleteDTO::getStoreId)).forEach((storeId, storeSkus) -> {
            storeSkus.stream().collect(Collectors.groupingBy(SkuInSpuInfoDeleteDTO::getCustomSpuId)).forEach((spu, skus) -> {
                SpuKey spuKey = new SpuKey();
                List<SkuKey> skuKeys = skus.stream().map(skuDto -> {
                    SkuKey skuKey = new SkuKey();
                    skuKey.setCustomSkuId(skuDto.getCustomSkuId());
                    return skuKey;
                }).collect(Collectors.toList());
                spuKey.setSkus(skuKeys);
                spuKey.setCustomSpuId(spu);
                try {
                    ResultData skuResult = jddjChannelSkuService.updateSkuSellStatus(JddjConvertUtil.Conver2SkuSellStatusInfoRequest(request.getBaseInfo(), Lists.newArrayList(spuKey), storeId, 2));
                    convert2SpuResult(resultData, skuResult, spuKey);
                } catch (IllegalArgumentException e) {
                    log.error("JddjChannelSpuServiceImpl.deleteSku 参数校验失败, data:{}", skus, e);
                    ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), Lists.newArrayList(spuKey));
                } catch (Exception e) {
                    log.error("JddjChannelSpuServiceImpl.deleteSku 服务异常, data:{}", skus, e);
                    ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), Lists.newArrayList(spuKey));
                }
            });
        });
        return resultData;
    }

    private ResultSpuData convert2SpuResult(ResultSpuData resultData, ResultData skuResultData, SpuKey spuKey) {
        // 若是删除逻辑，并且渠道侧删除失败，当渠道侧商品已删除时，删除返回成功
        if (CollectionUtils.isNotEmpty(skuResultData.getErrorData())) {
            boolean  isDelSuccess = skuResultData.getErrorData().stream()
                    .allMatch(sku -> MccConfigUtil.getJddjNotExistStoreSpuMsgs().contains(sku.getErrorMsg()));
            if (isDelSuccess) {
                skuResultData.getErrorData().forEach(errorSku -> {
                    resultData.addToSucData(new ResultSuccessSpu()
                            .setStoreId(errorSku.getStoreId())
                            .setSpuInfo(spuKey));
                });
                return resultData;
            }
        }
        // 若spu下的所有规格都不存在，则幂等视为成功
        if (CollectionUtils.isNotEmpty(skuResultData.getErrorData())) {
            skuResultData.getErrorData().forEach(errorData -> {
                resultData.addToErrorData(new ResultErrorSpu()
                        .setStoreId(errorData.getStoreId())
                        .setSpuInfo(spuKey)
                        .setErrorCode(errorData.getErrorCode())
                        .setErrorMsg(errorData.getSkuId() + ":" + errorData.getErrorMsg()));
            });
        }
        if (CollectionUtils.isNotEmpty(skuResultData.getSucData())) {
            skuResultData.getSucData().forEach(sucData -> {
                resultData.addToSucData(new ResultSuccessSpu()
                        .setStoreId(sucData.getStoreId())
                        .setSpuInfo(spuKey));
            });
        }
        return resultData;
    }

    @Override
    public ResultSpuData updateSpuSellStatus(SpuSellStatusInfoRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        request.getParamList().forEach(item -> {
            try {
                ResultData skuResult = jddjChannelSkuService.updateSkuSellStatus(
                        JddjConvertUtil.Conver2SkuSellStatusInfoRequest(request.getBaseInfo(), item.getSpuKeys(), item.getStoreId(),
                                item.getSpuStatus()));
                List<ResultErrorSku> saleStatusErrors = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(skuResult.getErrorData())) {
                    saleStatusErrors.addAll(skuResult.getErrorData());
                }
                // spu纬度填充组装结果
                for (SpuKey spuKey : item.getSpuKeys()) {
                    spuKey.setStoreId(item.getStoreId());
                    List<String> skuIds = Fun.map(spuKey.getSkus(), SkuKey::getCustomSkuId);
                    List<ResultErrorSku> spuSaleStatusErrors = Fun.filter(saleStatusErrors, error -> skuIds.contains(error.getSkuId()));
                    convertAndMerge2SpuResultByErrorSkus(resultData, spuSaleStatusErrors, spuKey, false);
                }
            } catch (IllegalArgumentException e) {
                log.error("JddjChannelSkuServiceImpl.updateSpuSellStatus 参数校验失败, data:{}", item, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), item.getSpuKeys());
            } catch (Exception e) {
                log.error("JddjChannelSkuServiceImpl.updateSpuSellStatus 服务异常, data:{}", item, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), item.getSpuKeys());
            }
        });
        return resultData;
    }
    @Override
    public ResultData updateSpuSellStatusByChannelSkuIds(SpuSellStatusInfoByChannelSkuIdRequest request) {
        ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        try {
            return jddjChannelSkuService.updateSkuSellStatusByChannelSkuIds(request);
        }
        catch (IllegalArgumentException e) {
            log.error("JddjChannelSkuServiceImpl.updateSpuSellStatus 参数校验失败, data:{}", request, e);
            resultData.setStatus(ResultGenerator.genFailResult(e.getMessage()));
        }
        catch (Exception e) {
            log.error("JddjChannelSkuServiceImpl.updateSpuSellStatus 服务异常, data:{}", request, e);
            resultData.setStatus(ResultGenerator.genFailResult(e.getMessage()));
        }
        return resultData;
    }

    @Override
    public ResultSpuData updateCustomSpuIdByOriginId(UpdateCustomSpuIdByOriginIdRequest request) {
        return null;
    }

    @Override
    public ResultSpuData updateCustomSpuIdByNameAndSpec(UpdateCustomSpuIdByNameAndSpecRequest request) {
        return null;
    }

    @Override
    public GetSpuInfoResponse getSpuInfo(GetSpuInfoRequest request) {
        // 多规格处理
        if (request.getSpecType() == SpecTypeEnum.MULTI.getCode()) {
            return getSpuInfo4MultiSpec(request);
        }
        // 京东到家没有商品详情及列表查询接口，详情接口暂时只填充单规格的价格、库存及上下架信息
        String jdSkuId = request.getSpuId();
        if (StringUtils.isEmpty(jdSkuId)) {
            return new GetSpuInfoResponse().setStatus(ResultGenerator.genFailResult("GetSpuInfoRequest.spuId不能为空"));
        }
        GetSpuInfoResponse response = new GetSpuInfoResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));
        BaseRequestSimple baseInfo = request.getBaseInfo();
        SpuInfoDTO spuInfo = new SpuInfoDTO();
        spuInfo.setCustomSpuId(request.getCustomSpuId());
        String jdCustomSkuId = request.getCustomSpuId();
        ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(baseInfo.getTenantId(), baseInfo.getChannelId(), request.getStoreId());
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        // 注意京东渠道存在多应用情况，sendPostAppDto要求应用ID和门店ID必须任传一个，应用ID优先级高于门店ID
        // 当前明确是需要通过门店进行查询，但是担心request.getBaseInfo()有误传递应用ID的情况，因此这儿强制抹掉应用ID
        baseRequest.setAppId(0L);
        baseRequest.setStoreIdList(Collections.singletonList(request.getStoreId()));
        // 查询价格
        ChannelStorePriceQueryDTO postData = new ChannelStorePriceQueryDTO(channelStoreDO.getChannelPoiCode(), Lists.newArrayList(Long.parseLong(jdSkuId)));
        // 调用渠道接口
        ChannelResponseDTO<List<ChannelStorePriceInfo>> priceResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SHOP_PRICE_LIST, baseRequest, postData);
        if (ChannelErrorCode.CommonErrorCode.matchRhinoLimit(priceResult.getCode()) && MccConfigUtil.getChannelLimiterErrorPoster().contains(ChannelPostJDDJEnum.SHOP_PRICE_LIST)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.TRIGGER_LIMIT, ProjectConstant.TRIGGER_LIMIT_MSG));
        }
        List<ChannelStorePriceInfo> priceList = extractListResult(priceResult);
        SkuInSpuInfoDTO skuInfo = new SkuInSpuInfoDTO();
        if (CollectionUtils.isNotEmpty(priceList)) {
            response.setSpuInfo(spuInfo);
            spuInfo.setSkus(Lists.newArrayList(skuInfo));
            skuInfo.setCustomSkuId(jdCustomSkuId);
            ChannelStorePriceInfo priceInfo = priceList.get(0);
            skuInfo.setPrice(MoneyUtils.fenToYuan(priceInfo.getPrice()).doubleValue());
        } else {
            return response;
        }
        // 查询库存及上下架状态
        ChannelStockQueryByCustomDTO queryDTO = ChannelStockQueryByCustomDTO.build(channelStoreDO.getChannelOnlinePoiCode(),jdCustomSkuId);
        // 调用渠道接口
        ChannelResponseDTO<List<ChannelStoreStockDetail>> stockResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.STOCK_QUERY, baseRequest, queryDTO);
        List<ChannelStoreStockDetail> detailList = extractListResult(stockResult);
        if (CollectionUtils.isNotEmpty(detailList) && detailList.get(0).getCode() == ResultCode.SUCCESS.getCode()) {
            // 设置库存
            skuInfo.setStock(detailList.get(0).getCurrentQty());
            // 设置上下架状态
            spuInfo.setStatus(Objects.equals(detailList.get(0).getVendibility(), 0) ? SaleStatusEnum.ON_SALE.getCode() : SaleStatusEnum.OFF_SALE.getCode());
        }

        // 获取主档商品填充商品名称，此处降级处理，调用成功则返回，失败不返回
        String spuName = getSingleSpuName(request, baseRequest) ;
        if (StringUtils.isNotBlank(spuName)) {
            spuInfo.setName(spuName);
        }
        return response;
    }

    /**
     * 多规格获取商品信息
     * 备注：京东不设置规格名称，因为京东多规格是基于属性的，没有规格名称
     *
     * @param request
     * @return
     */
    public GetSpuInfoResponse getSpuInfo4MultiSpec(GetSpuInfoRequest request) {
        GetSpuInfoResponse response = new GetSpuInfoResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));
        if (CollectionUtils.isEmpty(request.getSkuList())) {
            return response.setStatus(ResultGenerator.genFailResult("GetSpuInfoRequest.skuList不能为空"));
        }

        BaseRequestSimple baseInfo = request.getBaseInfo();
        ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(baseInfo.getTenantId(), baseInfo.getChannelId(), request.getStoreId());
        if (channelStoreDO == null) {
            return response.setStatus(ResultGenerator.genFailResult("门店绑定关系不存在"));
        }
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        baseRequest.setAppId(0L);
        baseRequest.setStoreIdList(Collections.singletonList(request.getStoreId()));
        SpuInfoDTO spuInfo = new SpuInfoDTO();
        // 设置价格
        List<SkuInSpuInfoDTO> skuInfoList = setPriceInfo(request.getSkuList(), channelStoreDO, baseRequest);
        if (CollectionUtils.isEmpty(skuInfoList)) {
            return response;
        }
        // 设置库存
        setStockInfo(spuInfo, skuInfoList, channelStoreDO, baseRequest, request.getCustomSkuId());

        spuInfo.setCustomSpuId(request.getCustomSpuId());
        spuInfo.setSkus(skuInfoList);
        response.setSpuInfo(spuInfo);

        return response;
    }

    /**
     * 设置价格
     *
     * @param skuList        商品SKU列表
     * @param channelStoreDO 渠道门店信息
     * @param baseRequest    基础请求对象
     * @return
     */
    private List<SkuInSpuInfoDTO> setPriceInfo(List<SkuKey> skuList, ChannelStoreDO channelStoreDO, BaseRequest baseRequest) {
        List<String> channelSkuIdList = skuList.stream().map(SkuKey::getChannelSkuId).collect(Collectors.toList());
        List<Long> channelSkuIdList4Long = channelSkuIdList.stream().map(Long::parseLong).collect(Collectors.toList());
        ChannelStorePriceQueryDTO postData = new ChannelStorePriceQueryDTO(channelStoreDO.getChannelPoiCode(), channelSkuIdList4Long);
        ChannelResponseDTO<List<ChannelStorePriceInfo>> priceResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SHOP_PRICE_LIST, baseRequest, postData);
        List<ChannelStorePriceInfo> priceList = extractListResult(priceResult);
        if (CollectionUtils.isEmpty(priceList)) {
            return Collections.emptyList();
        }
        Map<Long, ChannelStorePriceInfo> storeStockDetailMap = priceList.stream()
                .collect(Collectors.toMap(ChannelStorePriceInfo::getSkuId, Function.identity(), (v1, v2) -> v2));

        return skuList.stream()
                .map(skuKey -> {
                    SkuInSpuInfoDTO skuInfo = new SkuInSpuInfoDTO();
                    skuInfo.setCustomSkuId(skuKey.getCustomSkuId());
                    skuInfo.setChannelSkuId(skuKey.getChannelSkuId());
                    if (storeStockDetailMap.get(Long.valueOf(skuKey.getChannelSkuId())) != null) {
                        skuInfo.setPrice(MoneyUtils.fenToYuan(storeStockDetailMap.get(Long.valueOf(skuKey.getChannelSkuId())).getPrice()).doubleValue());
                    }
                    return skuInfo;
                })
                .collect(Collectors.toList());
    }

    private void setStockInfo(SpuInfoDTO spuInfo, List<SkuInSpuInfoDTO> skuInfoList, ChannelStoreDO channelStoreDO,
                              BaseRequest baseRequest, String customSkuId) {
        List<String> customSkuIdList = skuInfoList.stream().map(SkuInSpuInfoDTO::getCustomSkuId).collect(Collectors.toList());
        ChannelStockQueryByCustomDTO queryDTO = ChannelStockQueryByCustomDTO.build(channelStoreDO.getChannelOnlinePoiCode(), customSkuIdList);
        ChannelResponseDTO<List<ChannelStoreStockDetail>> stockResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.STOCK_QUERY, baseRequest, queryDTO);

        List<ChannelStoreStockDetail> detailList = extractListResult(stockResult);
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }

        Map<String, ChannelStoreStockDetail> storeStockDetailMap = detailList.stream()
                .collect(Collectors.toMap(ChannelStoreStockDetail::getOutSkuId, Function.identity(), (v1, v2) -> v2));

        skuInfoList.forEach(skuInfo -> {
            ChannelStoreStockDetail storeStockDetail = storeStockDetailMap.get(skuInfo.getCustomSkuId());
            if (storeStockDetail != null && storeStockDetail.getCode() == ResultCode.SUCCESS.getCode()) {
                skuInfo.setStock(storeStockDetail.getCurrentQty());
                if (StringUtils.isBlank(skuInfo.getCustomSkuId())) {
                    skuInfo.setCustomSkuId(storeStockDetail.getOutSkuId());
                }
            }
        });
        ChannelStoreStockDetail storeStockDetail = storeStockDetailMap.get(customSkuId);
        // 设置上下架状态，京东渠道售卖状态即上下架状态
        spuInfo.setStatus(Objects.equals(storeStockDetail.getVendibility(), 0) ? SaleStatusEnum.ON_SALE.getCode() : SaleStatusEnum.OFF_SALE.getCode());
    }

    /**
     * erp租户获取单规格商品名称
     * @param request
     * @param baseRequest
     * @return
     */
    private String getSingleSpuName(GetSpuInfoRequest request, BaseRequest baseRequest) {
        // 构造参数
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put("pageNo",String.valueOf(1));
        bizParam.put("pageSize",String.valueOf(1));
        bizParam.put("skuId", request.getSpuId());
        bizParam.put("isFilterDel",String.valueOf(0));
        // 请求线上渠道
        ChannelResponseDTO<JDSkuInfoListResult> jdSkuInfoListResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.BATCH_GET_SKUINFO_NEW,
                baseRequest, bizParam);

        if (jdSkuInfoListResult == null) {
            return StringUtils.EMPTY;
        }
        if (!jdSkuInfoListResult.isSuccess() || !jdSkuInfoListResult.getDataResponse().isSuccess() || jdSkuInfoListResult.getCoreData() == null) {
            return StringUtils.EMPTY;
        }

        List<JDChannelSkuInfo> jdChannelSkuInfoList = JacksonUtils.parseList(jdSkuInfoListResult.getCoreData().getResult(), JDChannelSkuInfo.class);
        if(CollectionUtils.isNotEmpty(jdChannelSkuInfoList)){
            return jdChannelSkuInfoList.get(0).getSkuName();
        }
        return StringUtils.EMPTY;
    }

    private <T> List<T> extractListResult(ChannelResponseDTO<List<T>> response) {
        if (response == null || !response.isSuccess() || !response.getDataResponse().isSuccess() || response.getCoreData() == null) {
            return null;
        }
        return response.getCoreData();
    }

    @Override
    public GetSpuInfosResponse getSpuInfoList(GetSpuInfosRequest request) {
        return null;
    }


    @Override
    public BatchGetSpuInfoResponse batchGetSpuInfo(BatchGetSpuInfoRequest request) {

        // 多规格商品查询
        if (request.isMultiSpec()) {
            return batchGetSpuInfoForMultiSpec(request);
        }

        BatchGetSpuInfoResponse response = new BatchGetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        if (request == null || request.getBaseInfo() == null){
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "拉取京东到家商品信息失败"));
        }
        BaseRequestSimple baseInfo = request.getBaseInfo();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        //如果是查找总部商品，需要appId
        if (request.isTenantProduct()) {
            baseRequest.setAppId(jddjChannelAppIdUtils.safeGetChannelAppId(baseInfo.getTenantId(), baseInfo.getAppId()));
        } else {
            // 注意京东渠道存在多应用情况，sendPostAppDto要求应用ID和门店ID必须任传一个，应用ID优先级高于门店ID
            // 当前明确是需要通过门店进行查询，但是担心request.getBaseInfo()有误传递应用ID的情况，因此这儿强制抹掉应用ID
            baseRequest.setAppId(0L);
            baseRequest.setStoreIdList(Collections.singletonList(request.getStoreId()));
        }
        ChannelStoreDO channelStoreDO = null;
        // 查询总部商品不需要校验门店
        if(!request.tenantProduct){
            channelStoreDO = copChannelStoreService.getChannelStore(baseInfo.getTenantId(), baseInfo.getChannelId(), request.getStoreId());
            if (Objects.isNull(channelStoreDO)) {
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSpuInfos(Collections.emptyList());
            }
        }
        // 构造参数
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put("pageNo",String.valueOf(request.getPageNum()));
        bizParam.put("pageSize",String.valueOf(request.getPageSize()));
        bizParam.put("searchAfterSkuId", StringUtils.isNotBlank(request.getOffset()) ? Long.valueOf(request.getOffset()) : null);
        bizParam.put("isFilterDel",String.valueOf(0));
        // 请求线上渠道
        ChannelResponseDTO<JDSkuInfoListResult> jdSkuInfoListResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.BATCH_GET_SKUINFO_NEW, baseRequest, bizParam);
        if (jdSkuInfoListResult == null) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, ProjectConstant.NO_RESPONSE));
        }
        if (!jdSkuInfoListResult.isSuccess() || !jdSkuInfoListResult.getDataResponse().isSuccess() || jdSkuInfoListResult.getCoreData() == null) {
            if (ChannelErrorCode.CommonErrorCode.matchRhinoLimit(jdSkuInfoListResult.getCode()) && MccConfigUtil.getChannelLimiterErrorPoster().contains(ChannelPostJDDJEnum.BATCH_GET_SKUINFO_NEW)) {
                return response.setStatus(ResultGenerator.genResult(ResultCode.TRIGGER_LIMIT, jdSkuInfoListResult.getMsg()));
            }

            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, jdSkuInfoListResult.getMsg()));
        }
        if (jdSkuInfoListResult.getCoreData().getResult() == null) {
            return buildBatchSpuInfoResponse(request, jdSkuInfoListResult, response, null);
        }
        List<JDChannelSkuInfo> jdChannelSkuInfoList = JacksonUtils.parseList(jdSkuInfoListResult.getCoreData().getResult(), JDChannelSkuInfo.class);
        // 返回空列表
        if (CollectionUtils.isEmpty(jdChannelSkuInfoList)) {
            return buildBatchSpuInfoResponse(request, jdSkuInfoListResult, response, null);
        }
        List<SpuInfoDTO> spuInfoDTOList = Fun.map(jdChannelSkuInfoList, JDChannelSkuInfo::toDTO);
        // 查询总部商品不需要填充上下架状态，直接返回
        if(request.tenantProduct){
            return buildBatchSpuInfoResponse(request, jdSkuInfoListResult, response, spuInfoDTOList);
        }

        // 查询门店商品上下架状态
        List<String> jdCustomSkuIdList = Fun.map(jdChannelSkuInfoList, JDChannelSkuInfo::getOutSkuId);
        ChannelStockQueryByCustomDTO queryDTO = ChannelStockQueryByCustomDTO.build(channelStoreDO.getChannelOnlinePoiCode(), jdCustomSkuIdList);
        // 调用渠道接口
        ChannelResponseDTO<List<ChannelStoreStockDetail>> stockResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.STOCK_QUERY, baseRequest, queryDTO);
        List<ChannelStoreStockDetail> detailList = extractListResult(stockResult);
        if (CollectionUtils.isNotEmpty(detailList)) {
            Map<String, ChannelStoreStockDetail> channelStoreStockDetailMap = Fun.toMapQuietly(detailList, ChannelStoreStockDetail::getOutSkuId);
            // 设置上下架状态
            spuInfoDTOList.forEach(spuInfoDTO -> {
                if (channelStoreStockDetailMap.containsKey(spuInfoDTO.getCustomSpuId())
                        && channelStoreStockDetailMap.get(spuInfoDTO.getCustomSpuId()).getCode() == ResultCode.SUCCESS.getCode()) {
                    spuInfoDTO.setStatus(Objects.equals(channelStoreStockDetailMap.get(spuInfoDTO.getCustomSpuId()).getVendibility(), 0)
                            ? SaleStatusEnum.ON_SALE.getCode() : SaleStatusEnum.OFF_SALE.getCode());
                }
            });
        }
        return buildBatchSpuInfoResponse(request, jdSkuInfoListResult, response, spuInfoDTOList);
    }


    // 分页查询京东多规格商品-无门店上下架状态
    public BatchGetSpuInfoResponse batchGetSpuInfoForMultiSpec(BatchGetSpuInfoRequest request) {
        BatchGetSpuInfoResponse response = new BatchGetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        if (request == null || request.getBaseInfo() == null) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "拉取京东到家商品信息失败"));
        }
        BaseRequestSimple baseInfo = request.getBaseInfo();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        //如果是查找总部商品，需要appId
        if (request.isTenantProduct()) {
            baseRequest.setAppId(jddjChannelAppIdUtils.safeGetChannelAppId(baseInfo.getTenantId(), baseInfo.getAppId()));
        }
        else {
            // 注意京东渠道存在多应用情况，sendPostAppDto要求应用ID和门店ID必须任传一个，应用ID优先级高于门店ID
            // 当前明确是需要通过门店进行查询，但是担心request.getBaseInfo()有误传递应用ID的情况，因此这儿强制抹掉应用ID
            baseRequest.setAppId(0L);
            baseRequest.setStoreIdList(Collections.singletonList(request.getStoreId()));
        }
        ChannelStoreDO channelStoreDO = null;
        // 查询总部商品不需要校验门店
        if (!request.tenantProduct) {
            channelStoreDO = copChannelStoreService.getChannelStore(baseInfo.getTenantId(), baseInfo.getChannelId(), request.getStoreId());
            if (Objects.isNull(channelStoreDO)) {
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                        .setSpuInfos(Collections.emptyList());
            }
        }
        // 构造参数
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put("pageNo", String.valueOf(request.getPageNum()));
        bizParam.put("pageSize", String.valueOf(request.getPageSize()));
        bizParam.put("searchAfterSpuId", StringUtils.isNotBlank(request.getOffset()) ? Long.valueOf(request.getOffset()) : null);
        // 请求线上渠道
        ChannelResponseDTO<JDSpuInfoListResult> spuResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.BATCH_GET_SPU_INFO,
                baseRequest, bizParam);
        if (spuResult == null) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, ProjectConstant.NO_RESPONSE));
        }
        if (!spuResult.isSuccess() || !spuResult.getDataResponse().isSuccess() || spuResult.getCoreData() == null) {
            if (ChannelErrorCode.CommonErrorCode.matchRhinoLimit(spuResult.getCode()) && MccConfigUtil.getChannelLimiterErrorPoster().contains(ChannelPostJDDJEnum.BATCH_GET_SKUINFO_NEW)) {
                return response.setStatus(ResultGenerator.genResult(ResultCode.TRIGGER_LIMIT, spuResult.getMsg()));
            }

            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, spuResult.getMsg()));
        }
        if (spuResult.getCoreData().getResult() == null) {
            return buildMultiSpuInfoResponse(request, spuResult, response, null);
        }
        List<JdChannelSpuInfo> channelSpuInfos = JacksonUtils.parseList(spuResult.getCoreData().getResult(), JdChannelSpuInfo.class);
        // 返回空列表
        if (CollectionUtils.isEmpty(channelSpuInfos)) {
            return buildMultiSpuInfoResponse(request, spuResult, response, null);
        }
        List<SpuInfoDTO> spuInfoDTOList = Fun.map(channelSpuInfos, JdChannelSpuInfo::toDTO);
        return buildMultiSpuInfoResponse(request, spuResult, response, spuInfoDTOList);
    }

    private BatchGetSpuInfoResponse buildMultiSpuInfoResponse(BatchGetSpuInfoRequest request,
                                                              ChannelResponseDTO<JDSpuInfoListResult> spuResult,
                                                              BatchGetSpuInfoResponse response, List<SpuInfoDTO> spuInfoDTOList) {
        int totalCount = null != spuResult.getCoreData().getCount() ? spuResult.getCoreData().getCount() : 0;
        int totalPage = totalCount % request.getPageSize() == 0 ? totalCount / request.getPageSize() : (totalCount / request.getPageSize()) + 1;
        PageInfo pageInfo = new PageInfo(request.getPageNum(), request.getPageSize(), totalPage, totalCount);
        response.setSpuInfos(spuInfoDTOList).setPageInfo(pageInfo);
        // 设置游标
        Long searchAfterSpuId = spuResult.getCoreData() != null ? spuResult.getCoreData().getSearchAfterSpuId() :  null;
        response.setOffset(searchAfterSpuId != null ? String.valueOf(searchAfterSpuId) : null);
        return response;
    }


    private BatchGetSpuInfoResponse buildBatchSpuInfoResponse(BatchGetSpuInfoRequest request, ChannelResponseDTO<JDSkuInfoListResult> jdSkuInfoListResult,
                                                              BatchGetSpuInfoResponse response, List<SpuInfoDTO> spuInfoDTOList) {
        int totalCount = null != jdSkuInfoListResult.getCoreData().getCount() ? jdSkuInfoListResult.getCoreData().getCount() : 0;
        int totalPage = totalCount % request.getPageSize() == 0 ? totalCount / request.getPageSize() : (totalCount / request.getPageSize()) + 1;
        PageInfo pageInfo = new PageInfo(request.getPageNum(), request.getPageSize(), totalPage, totalCount);
        response.setSpuInfos(spuInfoDTOList).setPageInfo(pageInfo);
        // 设置游标
        Long searchAfterSkuId = jdSkuInfoListResult.getCoreData() != null ? jdSkuInfoListResult.getCoreData().getSearchAfterSkuId() :  null;
        response.setOffset(searchAfterSkuId != null ? String.valueOf(searchAfterSkuId) : null);
        return response;
    }

    @Override
    public GetHeadQuarterSpuInfoResponse batchGetHeadQuarterSpuInfo(BasePageRequest request) {
        return null;
    }

    @Override
    public QueryAuditStatusResponse getAuditStatus(QueryAuditStatusRequest request) {
        return null;
    }

    @Override
    public RecommendChannelCategoryQueryResponse queryRecommendChannelCategory(RecommendChannelCategoryQueryRequest request) {
        return null;
    }

    @Override
    public SkuId2AppFoodCodeResponse queryAppFoodCodeBySkuId(QueryAppFoodCodeBySkuIdRequest request) {
        return null;
    }

    @Override
    public ResultStatus updateCustomSpuStoreCategory(UpdateCustomSpuStoreCategoryRequest request) {
        return null;
    }

    @Override
    public ResultSpuData batchUpdateSpuStoreCategoryCode(BatchUpdateCustomSpuStoreCategoryRequest request) {
        return new ResultSpuData().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public SensitiveWordCheckResponse checkSensitiveWords(SensitiveWordCheckRequest request) {
        return null;
    }

    @Override
    public QueryNormAuditDelSpuResponse queryNormAuditDelSpuInfo(QueryNormAuditDelSpuRequest request) {
        return null;
    }

    @Override
    public QueryChannelSpuIdResponse queryChannelSpuId(QueryChannelSpuIdRequest request) {
        return null;
    }

    @Override
    public ResultSpuData updateStoreCategory(SpuStoreCategoryInfoRequest request) {
        return null;
    }

    @Override
    public ProductSequenceBatchSetResponse batchSetSequence(ProductSequenceBatchSetRequest request) {
        return null;
    }

    @Override
    public QueryQualityProblemResponse queryQualityProblem(QueryQualityProblemRequest request) {
        return null;
    }

    @Override
    public ResultStatus updateOptionFieldBySpu(UpdateSpuOptionFieldRequest request) {
        return ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }

    @Override
    public ResultStatus submitAppealInfo(SubmitAppealInfoRequest request) {
        return null;
    }
}
