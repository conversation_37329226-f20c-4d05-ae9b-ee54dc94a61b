package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.google.common.collect.ImmutableList;
import com.google.gson.Gson;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ChannelErrorCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JDSettlementListParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JDSettlementOrderDetailParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JddjAfterSaleDetailResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JddjGetAfterSaleDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JddjSettleOrderDetailContent;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JddjSettleOrderListContent;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.BalanceBillListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.CheckBillResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JdBalanceBillParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JdCheckBillParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSettlementService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.BalanceBillDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelBalanceBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelBalanceBillPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelCheckBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelCheckBillResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementByIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementPageDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementAndDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementPageDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.DouyinBalanceBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.DouyinBalanceBillPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.JDBalanceBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.JDBalanceBillPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.JDBalanceBillResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 京东结算业务接口实现
 *
 * <AUTHOR>
 * @since 2021/3/1
 */
@Service("jddjChannelSettlementService")
public class JddjChannelSettlementServiceImpl implements ChannelSettlementService {

    @Resource
    private JddjConverterService jddjConverterService;

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private CommonLogger log;

    @Resource
    private JddjChannelOrderServiceImpl jddjChannelOrderService;

    @Override
    public ChannelSettlementPageResponse getChannelSettlementList(ChannelSettlementPageRequest request) {

        ChannelSettlementPageResponse result = new ChannelSettlementPageResponse();
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getShopId(), baseRequest);
            Map<String, ChannelStoreDO> poiMap = copChannelStoreService.getChannelPoiCode(baseRequest.getTenantId(), baseRequest.getChannelId(), Lists.newArrayList(request.getShopId()));
            String channelStoreKey = KeyUtils.genChannelStoreKey(baseRequest.getTenantId(), baseRequest.getChannelId(), request.getShopId());
            if (!poiMap.containsKey(channelStoreKey)) {
                log.error("京东渠道门店信息未找到 渠道门店映射 request:{} poiMap:{}", request, poiMap);
                return result.setResultStatus(ResultGenerator.genFailResult("门店信息未找到 渠道门店映射"));
            }

            String channelOnlinePoiCode = MccConfigUtil.useJDChannelPoiCode() ? poiMap.get(channelStoreKey).getChannelPoiCode() : poiMap.get(channelStoreKey).getChannelOnlinePoiCode();
            request.setShopId(Long.parseLong(channelOnlinePoiCode));
            JDSettlementListParam param = jddjConverterService.convertChannelSettlementPageRequset(request);
            ChannelResponseDTO<JddjSettleOrderListContent> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SETTLEMENT_ORDER_LIST, baseRequest, param);
            if (!channelResponseDTO.isSuccess() || !channelResponseDTO.getDataResponse().settlementIsSuccess()) {
                log.info("获取京东渠道结账单失败 request:{},channelResponseDTO:{}", request, channelResponseDTO);
                ResultCode resultCode = convert2ResultCode(channelResponseDTO.getCode());
                return result.setResultStatus(ResultGenerator.genResult(resultCode, channelResponseDTO.getErrorMsg()));
            }
            JddjSettleOrderListContent jdSettlementInfos = channelResponseDTO.getCoreData();
            log.info("获取京东渠道结账单成功 request:{} response:{}", request, JSON.toJSONString(jdSettlementInfos));
            ChannelSettlementPageDTO channelSettlementPageDto = jddjConverterService.convertChannelSettlementPage(jdSettlementInfos.getSettleOrderList());

            result.setChannelSettlementPageDto(channelSettlementPageDto);
            result.setResultStatus(ResultGenerator.genSuccessResult());
        } catch (Exception e) {
            log.error("查询京东渠道账单发生异常 request:{}", request, e);
            return result.setResultStatus(ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }

        return result;    }

    @Override
    public ChannelSettlementAndDetailResponse getChannelOrderSettlementListById(ChannelOrderSettlementByIdRequest request) {

        ChannelSettlementAndDetailResponse response = new ChannelSettlementAndDetailResponse();
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getShopId(), baseRequest);
            JDSettlementOrderDetailParam param = jddjConverterService.convertChannelSettlementDetailByIdRequest(request);
            ChannelResponseDTO<JddjSettleOrderDetailContent> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SETTLEMENT_ORDER_DETAIL, baseRequest, param);
            if (!channelResponseDTO.isSuccess() || !channelResponseDTO.getDataResponse().settlementIsSuccess()) {
                log.info("请求京东渠道查询渠道账单失败 tenantId:{} settlementId:{}", request.getTenantId(), request.getSettlementId());
                ResultCode resultCode = convert2ResultCode(channelResponseDTO.getCode());
                return response.setResultStatus(ResultGenerator.genResult(resultCode, channelResponseDTO.getErrorMsg()));
            }
            JddjSettleOrderDetailContent jdSettlementInfos = channelResponseDTO.getCoreData();
            ChannelOrderSettlementPageDTO channelOrderSettlementPageDto = jddjConverterService.convertChannelOrderSettlementPage(jdSettlementInfos.getSettleDetails());

            fillChannelOrderId(request, channelOrderSettlementPageDto);

            ChannelSettlementDTO settleOrderDTO = jddjConverterService.convertChannelSettlement(jdSettlementInfos.getSettleOrderBo());
            log.info("获取京东渠道账单成功 request:{} response:{}", request, JSON.toJSONString(jdSettlementInfos));
            response.setChannelOrderSettlementPageDto(channelOrderSettlementPageDto);
            response.setChannelSettlementDto(settleOrderDTO);
            response.setResultStatus(ResultGenerator.genSuccessResult());
        } catch (Exception e) {
            log.error("获取京东渠道账单发生异常 request:{}", request, e);
            return response.setResultStatus(ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }

        return response;
    }

    private void fillChannelOrderId(ChannelOrderSettlementByIdRequest request, ChannelOrderSettlementPageDTO channelOrderSettlementPageDto) {
        // 转换申诉类型的订单号
        Optional.ofNullable(channelOrderSettlementPageDto.getChannelOrderSettlementDtoList())
                .ifPresent(channelOrderSettlementDTOList ->
                        channelOrderSettlementDTOList.forEach(
                                channelOrderSettlementDTO -> {
                                    int orderChargeType = channelOrderSettlementDTO.getOrderChargeType();
                                    if (orderChargeType == 1004 || orderChargeType == 1005 || orderChargeType == 1006) {
                                        JddjGetAfterSaleDetailRequest jddjGetAfterSaleDetailRequest = new JddjGetAfterSaleDetailRequest();
                                        jddjGetAfterSaleDetailRequest.setTenantId(request.getTenantId());
                                        jddjGetAfterSaleDetailRequest.setChannelId(request.getChannelId());
                                        jddjGetAfterSaleDetailRequest.setAfterSaleId(channelOrderSettlementDTO.getChannelOrderId());
                                        jddjGetAfterSaleDetailRequest.setStoreId(request.getShopId());
                                        JddjAfterSaleDetailResult jddjAfterSaleDetailResult = jddjChannelOrderService.afterSaleDetail(jddjGetAfterSaleDetailRequest);

                                        if (jddjAfterSaleDetailResult != null && jddjAfterSaleDetailResult.getAfterSaleDetail() != null) {
                                            channelOrderSettlementDTO.setChannelOrderId(jddjAfterSaleDetailResult.getAfterSaleDetail().getOrderId());
                                        } else {
                                            log.error("售后申诉的订单号查询失败");
                                        }
                                    }
                                }
                        )
                );
    }

    @Override
    public ChannelOrderSettlementPageResponse getChannelOrderSettlementList(ChannelOrderSettlementPageRequest request) {
        return null;
    }

    /**
     * 分页查询对账单接口
     * 接口文档：https://opendj.jd.com/staticnew/widgets/resources.html?groupid=182&apiid=0bedea74a477409990b2ff8666a9ff44
     * @param request
     * @return
     */
    @Override
    public ChannelBalanceBillPageResponse getBalanceBillList(ChannelBalanceBillPageRequest request){
        ChannelBalanceBillPageResponse response = new ChannelBalanceBillPageResponse();
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getShopId(), baseRequest);
            JdBalanceBillParam param = jddjConverterService.convertBalanceBillByBalanceBillRequest(request);
            repairJdFusion(param);
            List<JdBalanceBillParam> paramList = requestInBatches(param);
            /*查看账单请求的数量，看看流量大不大*/
            log.info("京东分页查询对账单接口总入参：{},订单数量: {}",gson.toJson(param),param.getOrderIds().size());
            /*全部的账单结果*/
            List<BalanceBillDTO> balanceBillDTOAllList = new ArrayList<>();
            response.setBalanceBillDTOList(balanceBillDTOAllList);
            for(JdBalanceBillParam jdBalanceBillParam : paramList){
                jdBalanceBillParam.setPageNum(1);
                jdBalanceBillParam.setPageSize(MccConfigUtil.getBalanceBillPageSize());
                BalanceBillListResponse.SettleOrderPage settleOrderPage = null;
                /*获取一次请求的结果*/
                log.info("京东分页查询对账单接口入参：{}",gson.toJson(jdBalanceBillParam));
                settleOrderPage = getBalanceBillList(baseRequest, jdBalanceBillParam);
                if(Objects.isNull(settleOrderPage)){
                    return response;
                }
                response.setResultStatus(ResultGenerator.genSuccessResult());
                /*设置一次查询结果的账单列表*/
                List<BalanceBillDTO> balanceBillDTOList = jddjConverterService.convertBalanceBillDTOs(settleOrderPage.getResult());
                response.getBalanceBillDTOList().addAll(balanceBillDTOList);
                /*分页处理*/
                final Integer pages = settleOrderPage.getPages();
                Long pageNum = settleOrderPage.getPageNum();
                pageNum = pageNum + 1;
                while (pageNum <= pages) {
                    /*在上一次查询的基础上,页码加1*/
                    jdBalanceBillParam.setPageNum(pageNum.intValue());
                    /*获取一次请求的结果*/
                    settleOrderPage = getBalanceBillList(baseRequest, jdBalanceBillParam);
                    if(Objects.isNull(settleOrderPage)){
                        return response;
                    }
                    /*设置一次以上的查询结果的账单列表*/
                    response.getBalanceBillDTOList().addAll
                            (Optional.ofNullable(jddjConverterService.convertBalanceBillDTOs(settleOrderPage.getResult())).orElse(new ArrayList<>()));
                    pageNum++;
                }
            }
            log.info("京东分页查询对账单接口返回总结果：{}",gson.toJson(response.getBalanceBillDTOList()));
        } catch (Exception e) {
            log.error("获取京东渠道账单发生异常 request:{}", request, e);
            return response.setResultStatus(ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }

        return response;
    }

    /**
     * 京东到家
     */
    private static final int JDDJ = 1;
    /**
     * 物竞天择
     */
    private static final int WJTZ = 2;
    /**
     * 天选订单
     */
    private static final int JDTX = 3;

    private static final Gson gson = new Gson();
    /**
     * 获取一次请求的京东的账单列表
     * @return
     */
    private BalanceBillListResponse.SettleOrderPage getBalanceBillList(BaseRequest baseRequest, JdBalanceBillParam jdBalanceBillParam){
        ChannelResponseDTO<BalanceBillListResponse> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SETTLEMENT_BALANCE_BILL, baseRequest, jdBalanceBillParam);
        if (!channelResponseDTO.isSuccess() || !channelResponseDTO.getDataResponse().settlementIsSuccess()) {
            log.error("请求京东渠道分页查询对账单接口失败 tenantId:{} param:{}", baseRequest.getTenantId(), jdBalanceBillParam);
            return null;
        }
        log.info("channel京东账单的请求结果 ： {}",gson.toJson(channelResponseDTO));
        final String balanceBillListResponseString = channelResponseDTO.getData();
        if(StringUtils.isNotBlank(balanceBillListResponseString)){
            BalanceBillListResponse.BalanceBillListData  balanceBill = gson.fromJson(balanceBillListResponseString,BalanceBillListResponse.BalanceBillListData .class);
            log.info("京东账单的请求结果 ： {}",gson.toJson(balanceBill));
            if(Objects.nonNull(balanceBill)){
                BalanceBillListResponse.BalanceBillListContent balanceBillListContent = balanceBill.extractData();
                if (Objects.isNull(balanceBillListContent)) {
                    return null;
                }
                //分页获取所有
                BalanceBillListResponse.SettleOrderPage settleOrderPage = balanceBill.extractData().getBillList();
                List <BalanceBillListResponse.BalanceBillDTO> balanceBillDTOS = settleOrderPage.getResult();
                int finalApiBilltype = getApiBillType(jdBalanceBillParam);
                if (!CollectionUtils.isEmpty(balanceBillDTOS)) {
                    balanceBillDTOS.forEach(balanceBillDTO -> {
                        balanceBillDTO.setApiBillOrderType(finalApiBilltype);
                    });
                }
                settleOrderPage.setResult(balanceBillDTOS);
                return settleOrderPage;
            }else {
                log.info("请求京东渠道分页查询对账单接口范围数据为空 tenantId:{} param:{}", baseRequest.getTenantId(), jdBalanceBillParam);
            }
        }
        return null;
    }

    private static int getApiBillType(JdBalanceBillParam jdBalanceBillParam) {
        int apiBillOrderType = 0;
        if (jdBalanceBillParam.getBillType() != null && jdBalanceBillParam.getBillType() > 0) {
            apiBillOrderType = jdBalanceBillParam.getBillType();
        } else {
            if (jdBalanceBillParam.getJdFusion() != null) {
                apiBillOrderType = jdBalanceBillParam.getJdFusion() ? WJTZ : JDDJ;
            } else {
                apiBillOrderType = JDTX;
            }
        }
        return apiBillOrderType;
    }

    /**
     * 京东分页计费接口每次请求的最大订单数为 200
     * 将订单号，按照200一组进行分批
     * @return
     */
    private List<JdBalanceBillParam> requestInBatches(JdBalanceBillParam param){
        log.info("balance_bill_order_num : {}",MccConfigUtil.getBalanceBillRequestOrderNum());
        List<JdBalanceBillParam> paramList = new ArrayList<>();
        if(Objects.isNull(param)){
            return paramList;
        }
        List<Long> orderIds = param.getOrderIds();
        List <Long> tempOrderList = new ArrayList <>();

        for(Long orderId : orderIds){
            tempOrderList.add(orderId);
            if (Objects.equals(tempOrderList.size(),MccConfigUtil.getBalanceBillRequestOrderNum())) {
                JdBalanceBillParam jdBalanceBillParam = new JdBalanceBillParam();
                BeanUtils.copyProperties(param, jdBalanceBillParam);
                jdBalanceBillParam.setOrderIds(tempOrderList);
                /*thrift接口定义的类型int，默认值是0，但是京东账单入参，没有0的定义，老系统默认传的也是null*/
                jdBalanceBillParam.setOrderType(Objects.equals(jdBalanceBillParam.getOrderType(),0) ? null : jdBalanceBillParam.getOrderType());
                jdBalanceBillParam.setOrderStatus(Objects.equals(jdBalanceBillParam.getOrderStatus(),0) ? null : jdBalanceBillParam.getOrderStatus());
                jdBalanceBillParam.setSettleStatus(Objects.equals(jdBalanceBillParam.getSettleStatus(),0) ? null : jdBalanceBillParam.getSettleStatus());
                paramList.add(jdBalanceBillParam);
                /*200个订单请求后，置空*/
                tempOrderList = new ArrayList <>();
            }
        }

        /*最后一次不满足200的订单的请求*/
        if (CollectionUtils.isNotEmpty(tempOrderList)) {
            JdBalanceBillParam jdBalanceBillParam = new JdBalanceBillParam();
            BeanUtils.copyProperties(param, jdBalanceBillParam);
            jdBalanceBillParam.setOrderIds(tempOrderList);
            /*thrift接口定义的类型int，默认值是0，但是京东账单入参，没有0的定义，老系统默认传的也是null*/
            jdBalanceBillParam.setOrderType(Objects.equals(jdBalanceBillParam.getOrderType(),0) ? null : jdBalanceBillParam.getOrderType());
            jdBalanceBillParam.setOrderStatus(Objects.equals(jdBalanceBillParam.getOrderStatus(),0) ? null : jdBalanceBillParam.getOrderStatus());
            jdBalanceBillParam.setSettleStatus(Objects.equals(jdBalanceBillParam.getSettleStatus(),0) ? null : jdBalanceBillParam.getSettleStatus());
            paramList.add(jdBalanceBillParam);
        }
        return paramList;
    }
    /**
     * 查询订单计费明细接口
     * 接口文档：https://opendj.jd.com/staticnew/widgets/resources.html?groupid=182&apiid=b396331af4bf4d84b315541b54915071
     * @param request
     * @return
     */
    @Override
    public ChannelCheckBillResponse getCheckBillList(ChannelCheckBillPageRequest request){
        ChannelCheckBillResponse response = new ChannelCheckBillResponse();
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getShopId(), baseRequest);
            JdCheckBillParam jdCheckBillParam = jddjConverterService.convertCheckBillByCheckBillRequest(request);
            log.info("京东查询订单计费明细接口入参：{}",gson.toJson(request));
            /*获取一次请求的结果*/
            ChannelResponseDTO<CheckBillResponse> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SETTLEMENT_CHECK_BILL, baseRequest, jdCheckBillParam);
            if (!channelResponseDTO.isSuccess() || !channelResponseDTO.getDataResponse().settlementIsSuccess()) {
                log.error("请求京东查询订单计费明细接口失败 tenantId:{} param:{} 结果:{}",
                        request.getTenantId(), gson.toJson(jdCheckBillParam),gson.toJson(channelResponseDTO));
                response.setResultStatus(ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg()));
                response.setPlatCode(channelResponseDTO.getCode());
                response.setPlatMsg(channelResponseDTO.getMsg());
                return response;
            }
            String billListDataString = channelResponseDTO.getData();
            response.setPlatCode(channelResponseDTO.getCode());
            response.setPlatMsg(channelResponseDTO.getMsg());
            if(StringUtils.isNotBlank(billListDataString)){
                CheckBillResponse.BillListData billListData = gson.fromJson(billListDataString,CheckBillResponse.BillListData.class);
                List<CheckBillResponse.BillSearchResult> billSearchResults = billListData.extractListData();
                response.setResultStatus(ResultGenerator.genSuccessResult());
                response.setTotalCount(billListData.getTotalCount());
                response.setBillSearchResultList(Optional.ofNullable(jddjConverterService.convertCheckBills(billSearchResults)).orElse(new ArrayList<>()));
            }
            log.info("查询京东计费明细接口 tenantId:{}, 入参:{}, 结果:{}",
                    request.getTenantId(),gson.toJson(jdCheckBillParam),gson.toJson(response));
        } catch (Exception e) {
            log.error("查询京东订单计费明细接口发生异常 request:{}", request, e);
            return response.setResultStatus(ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }

        return response;
    }

    private ResultCode convert2ResultCode(String code) {
        if (ChannelErrorCode.JDDJ.REQUEST_LIMIT.equals(code)) {
            return ResultCode.TRIGGER_LIMIT;
        }

        return ResultCode.FAIL;
    }
    private void repairJdFusion(JdBalanceBillParam param){
        //20250523,zhaohailong03,jd侧期望的传参数方式：
    //        到家账单：billType=1，jdFusion=false
    //        小时购账单：billType=2，jdFusion=true
    //        天选账单：billType=3，jdFusion=null
    // 或
    //        到家账单：billType=1，jdFusion=null
    //        小时购账单：billType=2，jdFusion=null
    //        天选账单：billType=3，jdFusion=null
        //但是当前接口的request中jdFusion的类型定义为了bool，不能传null
        //因此为了保险，不修改接口定义，这里判断billType如果有值，则将jdFusion处理为null
        Boolean needRepair = Lion.getConfigRepository().getBooleanValue("jd.bill.repair.switch", true);
        if (needRepair) {
            if (param.getBillType() != null && param.getBillType() > 0) {
                param.setJdFusion(null);
            }
        }
    }
    /**
     * 异常处理参考
     * MtChannelSettlementServiceImpl#getChannelOrderSettlementList
     * @param request
     * @return
     */
    @Override
    public JDBalanceBillPageResponse getBalanceBill(JDBalanceBillPageRequest request) {
        JDBalanceBillPageResponse response = new JDBalanceBillPageResponse();

        JdBalanceBillParam param = jddjConverterService.convertBalanceBillByJDBalanceBillPageRequest(request);
        repairJdFusion(param);
        long tenantId = request.getTenantId();
        int channelId = request.getChannelId();
        BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId);
        addStoreId2BaseRequest(request.getShopIds().get(0), baseRequest);
        /*牵牛花门店列表转京东门店列表*/
        if(!Objects.equals(tenantId,0) &&
                !Objects.equals(channelId,0) &&
                CollectionUtils.isNotEmpty(request.getShopIds())){
            /*京东门店列表*/
            Map<Long,Long> mappingShopIdMap = new HashMap<>();
            Map<Long,String> notMappingShopIdMap = new HashMap<>();
            for(Long shopId : request.getShopIds()){
                Map<String, ChannelStoreDO> poiMap =
                        copChannelStoreService.getChannelPoiCode(tenantId, channelId, request.getShopIds());
                String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId,channelId,shopId);
                ChannelStoreDO channelStoreDO =  poiMap.get(channelStoreKey);
                if(channelStoreDO != null){
                    try {
                        Long channelPoiCode = Long.valueOf(channelStoreDO.getChannelPoiCode());
                        mappingShopIdMap.put(shopId,channelPoiCode);
                    } catch (NumberFormatException e) {
                        notMappingShopIdMap.put(shopId,channelStoreDO.getChannelPoiCode());
                        log.error("京东门店id非数字！",e);
                    }
                }else {
                    notMappingShopIdMap.put(shopId,null);
                }
            }
            if(mappingShopIdMap.size() > 0){
                log.info("牵牛花门店列表：{},京东门店列表 ：{}",mappingShopIdMap.keySet(),mappingShopIdMap.values());
                param.setShopIds(new ArrayList<>(mappingShopIdMap.values()));
            }
            if(notMappingShopIdMap.size() > 0){
                log.error("牵牛花门店列表：{},京东门店列表 ：{}，映射出错！",notMappingShopIdMap.keySet(),notMappingShopIdMap.values());
                response.setResultStatus(ResultGenerator.genResult(ResultCode.CHANNEL_STORE_CONFIG_ERROR, "门店渠道配置信息不存在!"));
                return response;
            }
        }

        /*thrift接口定义的类型int，默认值是0，但是京东账单入参，没有0的定义，老系统默认传的也是null*/
        param.setOrderType(Objects.equals(param.getOrderType(),0) ? null : param.getOrderType());
        param.setOrderStatus(Objects.equals(param.getOrderStatus(),0) ? null : param.getOrderStatus());
        param.setSettleStatus(Objects.equals(param.getSettleStatus(),0) ? null : param.getSettleStatus());
        log.info("getBalanceBill京东账单的请求入参 ： {},tenantId : {}",gson.toJson(param),request.getTenantId());
        ChannelResponseDTO<JDBalanceBillResult> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SETTLEMENT_BALANCE_BILL, baseRequest, param);
        if (!channelResponseDTO.isSuccess() || !channelResponseDTO.getDataResponse().settlementIsSuccess()) {
            log.error("请求分页查询对账单接口失败 tenantId:{} param:{} response:{}",
                    baseRequest.getTenantId(), gson.toJson(request),gson.toJson(channelResponseDTO));
            com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult jdBalanceBillResult =
                    new com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult();
            // 京东平台级code
            jdBalanceBillResult.setCode(channelResponseDTO.getCode());
            jdBalanceBillResult.setMsg(channelResponseDTO.getMsg());

            ChannelResponseResult<JDBalanceBillResult> dataResponse = channelResponseDTO.getDataResponse();
            if(dataResponse != null){
                com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult.JDBalanceBillData jdBalanceBillData =
                        new com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult.JDBalanceBillData();
                // 京东业务级code
                String code = dataResponse.getCode();
                String msg = dataResponse.getMsg();
                jdBalanceBillData.setCode(convertCode(code));
                jdBalanceBillData.setMsg(msg);
                jdBalanceBillResult.setData(jdBalanceBillData);
            }
            response.setResult(jddjConverterService.convertJDBalanceBillResult(jdBalanceBillResult));
            return response.setResultStatus(ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg()));
        }
        log.info("channel getBalanceBill京东账单的请求结果 ： {}",gson.toJson(channelResponseDTO));
        try {
            com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult jdBalanceBillResult =
                    new com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult();
            jdBalanceBillResult.setCode(channelResponseDTO.getCode());
            jdBalanceBillResult.setMsg(channelResponseDTO.getMsg());
            final String dataJson = channelResponseDTO.getData();
            if(StringUtils.isNotBlank(dataJson)){
                com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult.JDBalanceBillData jdBalanceBillData =
                        gson.fromJson(dataJson,com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult.JDBalanceBillData.class);
                jdBalanceBillResult.setData(jdBalanceBillData);
            }
            response.setResult(jddjConverterService.convertJDBalanceBillResult(jdBalanceBillResult));
            response.setResultStatus(ResultGenerator.genSuccessResult());
            log.info("getBalanceBill京东账单的请求结果 ： {}",gson.toJson(response));
            return response;
        } catch (Exception e) {
            log.error("查询京东分页账单接口发生异常 request:{}", request, e);
            return response.setResultStatus(ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }
    }

    @Override
    public DouyinBalanceBillPageResponse getDouyinBalanceBill(DouyinBalanceBillPageRequest request) {
        return null;
    }

    private void addStoreId2BaseRequest(long storeId, BaseRequest baseRequest){
        if(storeId <= NumberUtils.LONG_ZERO){
            return;
        }
        baseRequest.setStoreIdList(ImmutableList.of(storeId));
    }

    /**
     * 京东业务级code 转 Integer
     * @param code 京东业务级code
     */
    private Integer convertCode(String code){
        try {
            return Integer.valueOf(code);
        } catch (NumberFormatException e) {
            log.error("京东账单业务code转换失败,{}",code, e);
            // 无效京东业务code（不能是京东业务code的值），不能返回null，因为thrift 只能定义小int，涉及自动拆箱
            return -1;
        }
    }
}