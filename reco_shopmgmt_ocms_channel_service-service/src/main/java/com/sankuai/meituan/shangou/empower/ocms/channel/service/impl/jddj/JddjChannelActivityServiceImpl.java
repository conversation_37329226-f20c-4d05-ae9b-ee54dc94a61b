package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelActivityService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ActTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ItemOpTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultBuilderUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhaolei12
 * @create: 2019-03-06 16:23
 */
@Service("jddjChannelActivityService")
public class JddjChannelActivityServiceImpl implements ChannelActivityService {

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private JddjConverterService jddjConverterService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private CommonLogger log;

    @Override
    public ActivityResponse saveActivity(ActTypeEnum actType, BaseRequest baseRequest, List<ChannelActivityInfo> activityList) {
        ActivityResponse activityResponse = ResultBuilderUtil.genActivityResponse(ResultGenerator.genSuccessResult());
        try {
            Map<Long, List<ChannelActivityInfo>> map = activityList.stream()
                .collect(Collectors.groupingBy(ChannelActivityInfo::getStoreId));
            map.forEach((storeId, activityInfoList) -> {
                baseRequest.setStoreIdList(Lists.newArrayList(storeId));
                switch (actType) {
                    case CREATE:
                        createActivity(baseRequest, activityInfoList, activityResponse);
                        break;
                    case EDIT:
                        editActivity(baseRequest, activityInfoList, activityResponse);
                        break;
                    default:
                        break;
                }
            });
            return activityResponse;
        } catch (Exception e) {
            log.error("JddjChannelActivityServiceImpl.saveActivity, actType:{}, baseRequest:{}, activityList:{}", actType, baseRequest, activityList, e);
        }
        return activityResponse.setStatus(ResultGenerator.genFailResult("渠道网关保存服务异常"));
    }

    /**
     * 创建新活动
     */
    private void createActivity(BaseRequest baseRequest, List<ChannelActivityInfo> activityInfoList, ActivityResponse response) {
        List<String> skuList = activityInfoList.stream().map(ChannelActivityInfo::getSkuId).collect(Collectors.toList());
        ChannelActivityInfo channelActivityInfo = activityInfoList.get(0);
        //1.创建主活动信息
        String channelActivityId = createMain(baseRequest, skuList, channelActivityInfo, response);
        if (StringUtils.isBlank(channelActivityId)) {
            return;
        }
        //2.创建规则信息
        channelActivityInfo.setChannelActivityId(channelActivityId);
        boolean ruleStatus = createRule(baseRequest, skuList, channelActivityInfo, response);
        if (!ruleStatus) {
            return;
        }
        //3.添加活动商品
        boolean itemStatus = addActivityItem(baseRequest, activityInfoList, channelActivityInfo, response);
        if (!itemStatus) {
            return;
        }
        //4.活动提交保存
        submit(baseRequest, skuList, channelActivityInfo, channelActivityId, response);
    }

    /**
     * 创建活动主信息
     */
    private String createMain(BaseRequest baseRequest, List<String> skuList, ChannelActivityInfo channelActivityInfo, ActivityResponse response) {
        ChannelActivityMainInfo channelActivityMainInfo = jddjConverterService.channelActivityMainInfo(channelActivityInfo);
        Map<Long, ChannelResponseDTO<ChannelResponseResult<String>>> postResult = jddjChannelGateService
            .sendPost(ChannelPostJDDJEnum.CREATE_PROMOTION_INFO, baseRequest, channelActivityMainInfo);
        log.info("JddjChannelActivityServiceImpl.createMain, 渠道创建活动主信息返回数据, postResult:{}", postResult);
        if (MapUtils.isEmpty(postResult)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道创建活动主信息失败", response, true);
            return Strings.EMPTY;
        }
        ChannelResponseDTO<ChannelResponseResult<String>> responseDTO = postResult.get(baseRequest.getStoreIdList().get(0));
        if (responseDTO.isSuccess()) {
            //获取返回结果数据
            ChannelResponseResult<ChannelResponseResult<String>> dataResponse = responseDTO.getDataResponse();
            if (Objects.isNull(dataResponse)) {
                ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道创建活动主信息失败", response, true);
                return Strings.EMPTY;
            }
            return dataResponse.getData();
        }
        ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, responseDTO.getErrorMsg(), response, true);
        return Strings.EMPTY;
    }

    /**
     * 设置活动规则
     */
    private boolean createRule(BaseRequest baseRequest, List<String> skuList, ChannelActivityInfo activityInfo,
        ActivityResponse response) {
        ChannelActivityRuleInfo activityRuleInfo = jddjConverterService.channelActivityRuleInfo(activityInfo);
        Map<Long, ChannelResponseDTO<ChannelResponseResult<String>>> postResult = jddjChannelGateService
            .sendPost(ChannelPostJDDJEnum.CREATE_PROMOTION_RULE, baseRequest, activityRuleInfo);
        log.info("JddjChannelActivityServiceImpl.createRule, 渠道设置活动规则返回数据, postResult:{}", postResult);
        if (MapUtils.isEmpty(postResult)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道设置活动规则信息失败", response, true);
            return false;
        }
        ChannelResponseDTO<ChannelResponseResult<String>> ruleResponseDTO = postResult.get(baseRequest.getStoreIdList().get(0));
        if (Objects.isNull(ruleResponseDTO)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道设置活动规则信息失败", response, false);
            return false;
        }
        if (ruleResponseDTO.isSuccess()) {
            return true;
        }
        ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, ruleResponseDTO.getErrorMsg(), response, false);
        return false;
    }

    /**
     * 提交活动信息
     */
    private void submit(BaseRequest baseRequest, List<String> skuList, ChannelActivityInfo activityInfo,
        String channelActivityId, ActivityResponse response) {
        //拿到活动ID等信息提交活动（此方法还需要一个参数）
        ChannelActivitySubmitInfo submitInfo = new ChannelActivitySubmitInfo();
        submitInfo.setInfoId(channelActivityId);
        submitInfo.setOutInfoId(String.valueOf(activityInfo.getActivityId()));
        submitInfo.setTraceId("oacc-112233");
        submitInfo.setTimeStamp(DateUtils.formateDateSS(new Date()));
        Map<Long, ChannelResponseDTO<ChannelResponseResult<String>>> postResult = jddjChannelGateService
            .sendPost(ChannelPostJDDJEnum.CREATE_PROMOTION, baseRequest, submitInfo);
        log.info("JddjChannelActivityServiceImpl.submit, 渠道提交保存活动返回数据, postResult:{}", postResult);
        if (MapUtils.isEmpty(postResult)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道提交活动信息失败", response, true);
            return;
        }
        ChannelResponseDTO<ChannelResponseResult<String>> responseDTO = postResult.get(baseRequest.getStoreIdList().get(0));
        if (Objects.isNull(responseDTO)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道提交活动信息返回数据解析失败", response, false);
            return;
        }
        if (responseDTO.isSuccess()) {
            ResultBuilderUtil.activitySuccessResultAnalysis(baseRequest, skuList, channelActivityId, response);
            return;
        }
        ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, responseDTO.getErrorMsg(), response, false);
    }

    /**
     * 编辑活动
     */
    private void editActivity(BaseRequest baseRequest, List<ChannelActivityInfo> activityInfoList, ActivityResponse response) {
        List<String> skuList = activityInfoList.stream().map(ChannelActivityInfo::getSkuId).collect(Collectors.toList());
        ChannelActivityInfo channelActivityInfo = activityInfoList.get(0);
        //更新主活动信息
        boolean editBase = editBase(baseRequest, skuList, channelActivityInfo, response);
        if (!editBase) {
            return;
        }
        //按编辑操作类型分组（新增商品、修改活动）
        Map<ItemOpTypeEnum, List<ChannelActivityInfo>> editMap = activityInfoList.stream()
            .collect(Collectors.groupingBy(ChannelActivityInfo::getItemOpType));
        editMap.forEach((itemOpType, groupActivityList) -> {
            switch (itemOpType) {
                case ADD:
                    addActivityItem(baseRequest, groupActivityList, channelActivityInfo, response);
                    break;
                case UPDATE:
                    editPromotionSkuCount(baseRequest, groupActivityList, channelActivityInfo, response);
                    break;
                case DELETE:
                    delActivityItem(baseRequest, groupActivityList, channelActivityInfo, response);
                    break;
                default:
                    break;
            }
        });
    }

    /**
     * 编辑促销活动基本信息
     */
    private boolean editBase(BaseRequest baseRequest, List<String> skuList, ChannelActivityInfo activityInfo,
        ActivityResponse response) {
        //1.修改活动结束时间（需要调整的活动结束时间（原结束时间减去当前时间小于15分钟，不允许修改；修改后的结束时间小于原结束时间，不允许修改））
        ActivityTimeChangeInfo changeInfo = jddjConverterService.activityTimeChangeInfo(activityInfo);
        Map<Long, ChannelResponseDTO<String>> updateTimeResult = jddjChannelGateService
            .sendPost(ChannelPostJDDJEnum.ADJUST_PROMOTION_TIME, baseRequest, changeInfo);
        log.info("JddjChannelActivityServiceImpl.editBase, 渠道修改活动结束时间返回数据, postResult:{}", updateTimeResult);
        if (MapUtils.isEmpty(updateTimeResult)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道修改活动结束时间失败", response, true);
            return false;
        }
        ChannelResponseDTO<String> channelResponseDTO = updateTimeResult.get(baseRequest.getStoreIdList().get(0));
        if (Objects.isNull(channelResponseDTO)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道修改活动结束时间返回数据解析失败", response, true);
            return false;
        }
        if (channelResponseDTO.isSuccess()) {
            return true;
        }
        ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, channelResponseDTO.getErrorMsg(), response, false);
        return false;
    }

    /**
     * 编辑促销活动商品促销数量
     */
    private void editPromotionSkuCount(BaseRequest baseRequest, List<ChannelActivityInfo> activityInfoList,
        ChannelActivityInfo channelActivityInfo, ActivityResponse response) {
        List<String> skuList = activityInfoList.stream().map(ChannelActivityInfo::getSkuId).collect(Collectors.toList());
        //1.单品实时促销商品促销数量调整。单品实时促销接口是单品直降活动工具，使用此接口调整直降活动会立即生效。
        AcitvityCountChangeInfo countChangeInfo = jddjConverterService.activityCountChangeInfo(channelActivityInfo);
        countChangeInfo.setInfoId(channelActivityInfo.getChannelActivityId());
        List<AcitvitySkuCountChangeInfo> countChangeInfoList = jddjConverterService.activitySkuCountChangeInfoList(activityInfoList);

        //门店编码映射
        List<Long> storeIdList = activityInfoList.stream().map(ChannelActivityInfo::getStoreId).collect(Collectors.toList());
        Map<String, ChannelStoreDO> poiMap = copChannelStoreService.getChannelPoiCode(baseRequest.getTenantId(), baseRequest.getChannelId(), storeIdList);
        countChangeInfoList.forEach(skuCountChangeInfo -> {
            String channelStoreKey = KeyUtils.genChannelStoreKey(baseRequest.getTenantId(), baseRequest.getChannelId(), Long.parseLong(skuCountChangeInfo.getOutStationNo()));
            if (MapUtils.isNotEmpty(poiMap) && Objects.nonNull(poiMap.get(channelStoreKey))) {
                skuCountChangeInfo.setOutStationNo(poiMap.get(channelStoreKey).getChannelOnlinePoiCode());
            }
        });
        countChangeInfo.setAdjustSkuList(countChangeInfoList);
        Map<Long, ChannelResponseDTO<ChannelResponseResult<List<ChannelResponseDataErr>>>> postResult = jddjChannelGateService
            .sendPost(ChannelPostJDDJEnum.ADJUST_PROMOTION_COUNT, baseRequest, countChangeInfo);
        log.info("JddjChannelActivityServiceImpl.editPromotionSkuCount, 渠道修改活动商品促销数量返回数据, postResult:{}", postResult);
        if (MapUtils.isEmpty(postResult)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道修改活动商品促销数量失败", response, true);
            return;
        }
        ChannelResponseDTO<ChannelResponseResult<List<ChannelResponseDataErr>>> channelResponseDTO = postResult.get(baseRequest.getStoreIdList().get(0));
        if (Objects.isNull(channelResponseDTO)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道修改活动商品促销数量解析失败", response, false);
            return;
        }
        installResponseWithDetail(baseRequest, channelActivityInfo, response, skuList, channelResponseDTO);
    }

    /**
     * 解析返回数据，如果有部分成功设置商品级错误信息
     */
    private boolean installResponseWithDetail(BaseRequest baseRequest, ChannelActivityInfo channelActivityInfo, ActivityResponse response,
        List<String> skuList,
        ChannelResponseDTO<ChannelResponseResult<List<ChannelResponseDataErr>>> channelResponseDTO) {
        ChannelResponseResult<ChannelResponseResult<List<ChannelResponseDataErr>>> activityData =
            channelResponseDTO.getDataResponse();
        if (channelResponseDTO.isSuccess()) {
            if (Objects.isNull(activityData) || Objects.isNull(activityData.getResultData())) {
                return true;
            }
            ChannelResponseResult<List<ChannelResponseDataErr>> dataResult = activityData.getResultData();
            List<ChannelResponseDataErr> errorList = dataResult.getDataList();
            if (CollectionUtils.isEmpty(errorList)) {
                return true;
            }
            installItemResult(baseRequest, skuList, errorList, channelActivityInfo, response);
            return true;
        } else {
            if (Objects.isNull(activityData) || Objects.isNull(activityData.getResultData())) {
                ResultBuilderUtil
                    .activityErrorResultAnalysis(baseRequest, skuList, channelResponseDTO.getErrorMsg(), response, false);
                return false;
            }
            ChannelResponseResult<List<ChannelResponseDataErr>> dataResult = activityData.getResultData();
            List<ChannelResponseDataErr> errorList = dataResult.getDataList();
            if (CollectionUtils.isEmpty(errorList)) {
                ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, channelResponseDTO.getErrorMsg(), response, false);
                return false;
            }
            installItemResult(baseRequest, skuList, errorList, channelActivityInfo, response);
            return false;
        }
    }

    private void installItemResult(BaseRequest baseRequest, List<String> skuList,List<ChannelResponseDataErr> errorList,
        ChannelActivityInfo channelActivityInfo, ActivityResponse response) {
        Map<String, ChannelResponseDataErr> errMap = errorList.stream().collect(
            Collectors.toMap(ChannelResponseDataErr::getOutSkuId, Function.identity()));
        //部分失败情况组装成功和失败信息
        skuList.forEach(skuId -> {
            if (errMap.containsKey(skuId)) {
                ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, Lists.newArrayList(skuId),
                    errMap.get(skuId).getFailReason(), response, false);
            } else {
                ResultBuilderUtil.activitySuccessResultAnalysis(baseRequest, skuId, channelActivityInfo.getChannelActivityId(), response);
            }
        });
    }

    /**
     * 添加活动商品
     */
    private boolean addActivityItem(BaseRequest baseRequest, List<ChannelActivityInfo> activityInfoList,
        ChannelActivityInfo channelActivityInfo, ActivityResponse response) {
        List<String> skuList = activityInfoList.stream().map(ChannelActivityInfo::getSkuId).collect(Collectors.toList());
        //转换商品字段
        ChannelActivityItemInfo activityItemInfo = jddjConverterService.channelActivityItemInfo(activityInfoList.get(0));
        activityItemInfo.setInfoId(channelActivityInfo.getChannelActivityId());
        List<ChannelActivitySkuInfo> activitySkuInfoList = jddjConverterService.channelActivitySkuInfoList(activityInfoList);

        //门店编码映射
        List<Long> storeIdList = activityInfoList.stream().map(ChannelActivityInfo::getStoreId).collect(Collectors.toList());
        Map<String, ChannelStoreDO> poiMap = copChannelStoreService.getChannelPoiCode(baseRequest.getTenantId(), baseRequest.getChannelId(), storeIdList);
        activitySkuInfoList.forEach(activitySkuInfo -> {
            String channelStoreKey = KeyUtils.genChannelStoreKey(baseRequest.getTenantId(), baseRequest.getChannelId(), Long.parseLong(activitySkuInfo.getOutStationNo()));
            if (MapUtils.isNotEmpty(poiMap) && Objects.nonNull(poiMap.get(channelStoreKey))) {
                activitySkuInfo.setOutStationNo(poiMap.get(channelStoreKey).getChannelOnlinePoiCode());
            }
        });
        activityItemInfo.setSkus(activitySkuInfoList);
        Map<Long, ChannelResponseDTO<ChannelResponseResult<List<ChannelResponseDataErr>>>> postResult = jddjChannelGateService
            .sendPost(ChannelPostJDDJEnum.CREATE_PROMOTION_SKU, baseRequest, activityItemInfo);
        log.info("JddjChannelActivityServiceImpl.addActivityItem, 渠道添加活动商品返回数据, postResult:{}", postResult);
        if (MapUtils.isEmpty(postResult)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道添加活动商品失败", response, true);
            return false;
        }
        ChannelResponseDTO<ChannelResponseResult<List<ChannelResponseDataErr>>> addItemResponseDTO = postResult.get(baseRequest.getStoreIdList().get(0));
        if (Objects.isNull(addItemResponseDTO)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道添加活动商品返回数据解析失败", response, false);
            return false;
        }
        return installResponseWithDetail(baseRequest, channelActivityInfo, response, skuList, addItemResponseDTO);
    }

    /**
     * 活动部分商品取消
     */
    private void delActivityItem(BaseRequest baseRequest, List<ChannelActivityInfo> activityInfoList,
        ChannelActivityInfo channelActivityInfo, ActivityResponse response) {
        List<String> skuList = activityInfoList.stream().map(ChannelActivityInfo::getSkuId).collect(Collectors.toList());
        //转换商品字段
        ChannelActivityCancelItemInfo cancelItemInfo = jddjConverterService.channelActivityCancelItemInfo(channelActivityInfo);
        cancelItemInfo.setInfoId(channelActivityInfo.getChannelActivityId());
        List<ChannelActivityCancelSkuInfo> activityCancelSkuInfoList = jddjConverterService.channelActivityCancelSkuInfoList(activityInfoList);
        //门店编码映射
        List<Long> storeIdList = activityInfoList.stream().map(ChannelActivityInfo::getStoreId).collect(Collectors.toList());
        Map<String, ChannelStoreDO> poiMap = copChannelStoreService.getChannelPoiCode(baseRequest.getTenantId(), baseRequest.getChannelId(), storeIdList);
        activityCancelSkuInfoList.forEach(activityCancelSkuInfo -> {
            String channelStoreKey = KeyUtils.genChannelStoreKey(baseRequest.getTenantId(), baseRequest.getChannelId(),
                Long.parseLong(activityCancelSkuInfo.getOutStationNo()));
            if (MapUtils.isNotEmpty(poiMap) && Objects.nonNull(poiMap.get(channelStoreKey))) {
                activityCancelSkuInfo.setOutStationNo(poiMap.get(channelStoreKey).getChannelOnlinePoiCode());
            }
        });
        cancelItemInfo.setCancelSkus(activityCancelSkuInfoList);
        Map<Long, ChannelResponseDTO<ChannelResponseResult<String>>> postResult = jddjChannelGateService
            .sendPost(ChannelPostJDDJEnum.CANCEL_PROMOTION_SKU, baseRequest, cancelItemInfo);
        log.info("JddjChannelActivityServiceImpl.delActivityItem, 渠道删除活动商品请求参数, postResult:{}", postResult);
        if (MapUtils.isEmpty(postResult)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道删除活动商品失败", response, true);
            return;
        }
        ChannelResponseDTO<ChannelResponseResult<String>> delResponseDTO = postResult
            .get(baseRequest.getStoreIdList().get(0));
        if (Objects.isNull(delResponseDTO)) {
            ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, "调用渠道删除活动商品返回数据解析失败", response, false);
            return;
        }
        if (delResponseDTO.isSuccess()) {
            ResultBuilderUtil.activitySuccessResultAnalysis(baseRequest, skuList, channelActivityInfo.getChannelActivityId(), response);
            return;
        }
        ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, delResponseDTO.getErrorMsg(), response, false);
    }

    @Override
    public ActivityResponse batchDelete(BaseRequest baseRequest, List<ChannelPoiParamInfo> channelPoiParamInfoList) {
        ActivityResponse response = new ActivityResponse();
        List<ActivityResultDataInfo> errorResultDataInfoList = Collections.synchronizedList(new ArrayList<>());
        List<ActivityResultDataInfo> successResultDataInfoList = Collections.synchronizedList(new ArrayList<>());
        ResultStatus resultStatus = ResultGenerator.genSuccessResult();

        channelPoiParamInfoList.forEach(channelPoiInfo -> {
            deleteActivity(baseRequest, errorResultDataInfoList, successResultDataInfoList, channelPoiInfo);
        });

        response.setSuccessData(successResultDataInfoList).setErrorData(errorResultDataInfoList).setStatus(resultStatus);
        return response;
    }

    @Override
    public QueryActivityResponse queryActivityList(BaseRequest request) {
        return new QueryActivityResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
    }

    @Override
    public QuerySkuActivityInfoResponse querySkuActivityInfos(QuerySkuActivityInfoRequest request) {
        return new QuerySkuActivityInfoResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
    }

    @Override
    public QueryCanModifyPriceResponse querySpuCanModifyPrice(QueryCanModifyPriceRequest request,Integer count) {
        log.warn("jddj未开通查询商品是否可以改价功能");
        return new QueryCanModifyPriceResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
    }

    private void deleteActivity(BaseRequest baseRequest, List<ActivityResultDataInfo> errorResultDataInfoList, List<ActivityResultDataInfo> successResultDataInfoList, ChannelPoiParamInfo channelPoiParamInfo) {
        if (CollectionUtils.isEmpty(channelPoiParamInfo.getChannelActivityIdList())) {
            log.warn("activityIdList is empty, channelPoiParamInfo:{}", channelPoiParamInfo);
            return;
        }

        Set<String> activityIdSet = Sets.newHashSet(channelPoiParamInfo.getChannelActivityIdList());
        activityIdSet.forEach(activityId -> {
            try {
                ChannelActivityDiscountDeleteDTO activityDiscountDeleteDTO = ChannelActivityDiscountDeleteDTO.builder()
                        .infoId(Long.valueOf(activityId))
                        .build();

                Map<Long, ChannelResponseDTO<String>> postResult = jddjChannelGateService.sendPost(ChannelPostJDDJEnum.ACTIVITY_DISCOUNT_DELETE,
                        new BaseRequestSimple().setChannelId(channelPoiParamInfo.getChannelId()).setTenantId(baseRequest.getTenantId()),
                        channelPoiParamInfo.getStoreId(),
                        activityDiscountDeleteDTO);

                if (MapUtils.isEmpty(postResult)) {
                    ResultStatus tempResultStatus = new ResultStatus().setCode(ResultCodeEnum.FAIL.getValue()).setMsg(ResultCode.UNDEAL_ERROR.getMsg());
                    errorResultDataInfoList.addAll(buildResultDataInfoList(channelPoiParamInfo, activityId, tempResultStatus));
                    return;
                }
                ChannelResponseDTO<String> channelResponseDTO = postResult.get(channelPoiParamInfo.getStoreId());
                if (channelResponseDTO == null) {
                    ResultStatus tempResultStatus = new ResultStatus().setCode(ResultCodeEnum.FAIL.getValue()).setMsg(ResultCode.UNDEAL_ERROR.getMsg());
                    errorResultDataInfoList.addAll(buildResultDataInfoList(channelPoiParamInfo, activityId, tempResultStatus));
                    return;
                }
                if (!channelResponseDTO.isSuccess()) {
                    ResultStatus tempResultStatus = new ResultStatus().setCode(ResultCodeEnum.FAIL.getValue()).setMsg(channelResponseDTO.getErrorMsg());
                    errorResultDataInfoList.addAll(buildResultDataInfoList(channelPoiParamInfo, activityId, tempResultStatus));
                    return;
                }

                successResultDataInfoList.addAll(buildResultDataInfoList(channelPoiParamInfo, activityId, ResultGenerator.genSuccessResult()));
            }catch (IllegalArgumentException e) {
                log.error("batch delete error. msg:{}, param:{}", e.getMessage(), baseRequest, e);
                ResultStatus tempResultStatus = new ResultStatus().setCode(ResultCodeEnum.FAIL.getValue()).setMsg(e.getMessage());
                errorResultDataInfoList.addAll(buildResultDataInfoList(channelPoiParamInfo, activityId, tempResultStatus));
                return;
            } catch (Exception e) {
                log.error("batch delete error. msg:{}, param:{}", e.getMessage(), baseRequest, e);
                ResultStatus tempResultStatus = new ResultStatus().setCode(ResultCodeEnum.FAIL.getValue()).setMsg(e.getMessage());
                errorResultDataInfoList.addAll(buildResultDataInfoList(channelPoiParamInfo, activityId, tempResultStatus));
                return;
            }
        });
    }

    /**
     * 将批量结果打平
     *
     * @param channelPoiParamInfo
     * @param resultStatus
     * @return
     */
    private List<ActivityResultDataInfo> buildResultDataInfoList(ChannelPoiParamInfo channelPoiParamInfo, String activityId, ResultStatus resultStatus) {
        List<ActivityResultDataInfo> resultDataInfoList = new ArrayList<>();
        if (channelPoiParamInfo == null || StringUtils.isBlank(activityId)) {
            return resultDataInfoList;
        }

        resultDataInfoList.add(
                new ActivityResultDataInfo()
                        .setChannelId(channelPoiParamInfo.getChannelId())
                        .setStoreId(channelPoiParamInfo.getStoreId())
                        .setChannelActivityId(activityId)
                        .setData(resultStatus)
        );
        return resultDataInfoList;
    }
}
