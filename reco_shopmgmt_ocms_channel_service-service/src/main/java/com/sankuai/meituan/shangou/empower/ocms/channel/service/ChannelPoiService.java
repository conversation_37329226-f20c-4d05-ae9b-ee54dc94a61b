package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.google.common.collect.Lists;
import com.meituan.kafka.log.Log;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseChannelPoiRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiDetailsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiIdsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiAuthRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.GetPoiIdsResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.GetPoiInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.GetPoiPromotionInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiAddressUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiPrebookDaysUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiPromotionInfoUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiShippingTimeUpdateRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @author:huchangwu
 * @date: 2019/1/16
 * @time: 下午4:17
 */
@Slf4j
public interface ChannelPoiService {

    /**
     * 20230704 - chenjianhui05
     * 本接口没有传app id 或者 store ID，对于多应用场景有问题
     * 并且也无明确的业务场景需求
     * 查阅了近 6 个月的raptor未发现有调用量
     *
     * ***** 因此废弃此接口，各方勿使用 ******
     * ***** 因此废弃此接口，各方勿使用 ******
     * ***** 因此废弃此接口，各方勿使用 ******
     *
     * ***** 未支持京东多应用，各方勿使用 ******
     * ***** 未支持京东多应用，各方勿使用 ******
     * ***** 未支持京东多应用，各方勿使用 ******
     *
     * @param req
     * @return
     */
    @Deprecated
    GetPoiInfoResponse batchGetPoiInfo(BaseRequestSimple req);

    GetPoiIdsResponse batchGetPoiIds(BaseRequestSimple req);

    GetPoiIdsResponse batchGetPoiIdsBySysParam(BatchGetPoiIdsRequest req);

    GetPoiInfoResponse batchGetPoiDetails(BatchGetPoiDetailsRequest request);

    /**
     * 获取根据渠道的开放平台门店Id，查询渠道的外卖门店Id
     * @param request
     * @return
     */
    GetPoiIdsResponse getPoiInnerId(BatchGetPoiDetailsRequest request);


    /**
     * 门店开始营业
     * @param request
     * @return
     */
    ResultStatus poiOpen(ChannelPoiIdRequest request);


    /**
     * 门店停止营业
     * @param request
     * @return
     */
    ResultStatus poiClose(ChannelPoiIdRequest request);


    /**
     * 门店公告信息更新
     * @param request
     * @return
     */
    ResultStatus promotionInfoUpdate(PoiPromotionInfoUpdateRequest request);


    /**
     * 营业时间更新
     * @param request
     * @return
     */
    ResultStatus shippingTimeUpdate(PoiShippingTimeUpdateRequest request);

    /**
     * 使门店接受预订单
     * @param request
     * @return
     */
    ResultStatus prebookStatusOpen(ChannelPoiIdRequest request);


    /**
     * 使门店不接受预订单
     * @param request
     * @return
     */
    ResultStatus prebookStatusClose(ChannelPoiIdRequest request);

    /**
     * 更新门店的接受预定的日期范围
     * @param request
     * @return
     */
    ResultStatus updatePrebookDays(PoiPrebookDaysUpdateRequest request);

    /**
     * 获取门店公告信息
     * @param request
     * @return
     */
    GetPoiPromotionInfoResponse getPoiPromotionInfo(ChannelPoiIdRequest request);


    /**
     * 获取门店营业状态
     * @param channelPoiIdRequest
     * @return
     */
    ChannelPoiStatusResponse getPoiStatus(ChannelPoiIdRequest channelPoiIdRequest);


    /**
     * 门店授权
     * @param request
     * @return
     */
    ResultStatus poiAuth(ChannelPoiAuthRequest request);


    /**
     * 门店解析授权
     * @param request
     * @return
     */
    ResultStatus poiDeAuth(ChannelPoiAuthRequest request);

    ResultStatus updatePoiAddress(PoiAddressUpdateRequest request);

    UpdateSaleafterAddressMessageResponse updateSaleafterAddressMessage(UpdateSaleafterAddressMessageRequest request);

    /**
     * 查询门店商品审核状态信息
     * @return
     */
    //ResultStatus queryAuditStatus();

    ResultStatus poiHotlineUpdate(ChannelPoiHotlineRequest request);

    /**
     * 查询门店权限列表：当前仅支持 饿了么渠道连锁管品模式下门店商品编辑权限
     * @param request
     * @return
     */
    QueryPoiAuthDetailResponse queryPoiAuthDetailInfo(BaseChannelPoiRequestSimple request);

    /**
     * 更新门店权限：当前仅支持 饿了么渠道连锁管品模式下门店商品编辑权限
     * @param request
     * @return
     */
    ResultStatus updatePoiAuthInfo(UpdatePoiAuthInfoRequest request);

    default List<ChannelPoiInfo> queryChannelPoiInfoList(long tenantId, List<String> channelPoiCode, int channelId) {
        return Lists.newArrayList();
    }

    /**
     * 查询门店类目信息
     * 
     * @param request
     * @return
     */
    default ChannelPoiCategoryResponse queryChannelPoiCategory(ChannelPoiCategoryRequest request) {
        ChannelPoiCategoryResponse response = new ChannelPoiCategoryResponse();
        response.setCode(ResultCodeEnum.FAIL.getValue());
        response.setMsg("渠道不支持查询门店类目信息");
        return response;
    }
}
