package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/18
 */
public class TxdConstant {

    public static final String defaultHttpsAddressUrl = "https://eco.taobao.com/router/rest";


    /**
     * 默认图片类目
     */
    public static final Long DEFAULT_PICTURE_CATEGORY_ID = 0L;


    /**
     * 默认图片数量
     */
    public static final int DEFAULT_PICTURE_LIMIT = 5;

    /**
     * 默认售卖单位
     */
    public static final String DEFAULT_UNIT = "件";

    /**
     * 仅称重商品使用的售卖单位
     */
    public static final List<String> SUPPORT_WEIGHT_UNIT = Arrays.asList("g","kg","克","千克");

    // 更新库存类型默认为全量
    public static final Long defaultUpdateStockType = 1L;
    public static final Long defaultBillType = 9036L;
    public static final String defaultStockOrderType = "9001";
    public static final String STOCK_OPERATOR="牵牛花";

    /**
     * 保质期，默认0，表示不管理保质期。
     */
    public static final long DEFAULT_PERIOD = 0L;

    /**
     * 商品类型。默认1，普通商品。
     */
    public static final long DEFAULT_ITEM_TYPE = 1L;

    /**
     * 易碎品标记，默认0，否。
     */
    public static final long DEFAULT_FRAGILE_FLAG = 0L;

    /**
     * 淘鲜达合作商家默认填1。
     */
    public static final String DEFAULT_PURCHASE_SPEC = "1";

    /**
     * 均重。非称重品默认填1。
     */
    public static final String DEFAULT_AVG_WEIGHT = "1";

    /**
     * 预扣款重量。非称重品默认填1。
     */
    public static final String DEFAULT_PRE_MINUS_WEIGHT = "1";

    /**
     * 非称重品默认填1。
     */
    public static final long DEFAULT_STEP_QUANTITY = 1L;

    /**
     * 起购量默认值。
     */
    public static final long DEFAULT_PURCHASE_QUANTITY = 1L;

}
