package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.UpdateCustomSpuId;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpecTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.OrderRefundCodeConventer;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RefundSku;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 饿了么实体转换统一接口类
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:09
 **/
@Mapper(componentModel = "spring", uses = {MappingConverterUtils.class}, imports = {ConverterUtils.class, ChannelPriceUpdateDTO.class,
        ChannelSkuCreateDTO.class, MoneyUtils.class, CollectionUtils.class, OrderRefundCodeConventer.class, StringUtils.class, ElmConverterUtil.class, BigDecimal.class})
public interface ElmConverterService {



    @Mappings({
    })
    ChannelPictureUploadDTO pictureUploadMapping(PictureUploadDTO param);

    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "upc", source = "upc"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "status", expression = "java(param.getSkuStatus() == 1 ? 1 : 0)"),
            @Mapping(target = "brand_id", source = "brand"),
            @Mapping(target = "photos", expression = "java(ChannelSkuCreateDTO.toPhotos(param.getPictures()))"),
            @Mapping(target = "left_num", expression = "java(param.isSetStock()?param.getStock():null)"),
            @Mapping(target = "sale_price", expression = "java(param.isSetPrice()?Double.valueOf(param.getPrice()*100).intValue():null)"),
            @Mapping(target = "market_price", expression = "java(param.isSetPrice()?Double.valueOf(param.getPrice()*100).intValue():null)"),
            @Mapping(target = "weight", expression = "java(param.isSetWeight()?Double.valueOf(param.getWeight()).intValue():null)"),
            @Mapping(target = "custom_sku_id", source = "skuId"),
            @Mapping(target = "category_id", expression = "java(ElmConverterUtil.convert2ELMCategoryCode(param))"),
            @Mapping(target = "brand_name", source = "brandName"),
            @Mapping(target = "cat1_id", source = "categoryFirst"),
            @Mapping(target = "cat2_id", source = "categorySecond"),
            @Mapping(target = "cat3_id", source = "category"),
            @Mapping(target = "production_addr1", source = "productionArea"),
            @Mapping(target = "production_addr2", source = "productionArea"),
            @Mapping(target = "production_addr3", source = "productionArea"),
            @Mapping(target = "predict_cat", expression = "java(ChannelSkuCreateDTO.getElmPredictCat(param.getCategoryFirst(), param.getCategorySecond(), param.getCategory()))")
    })
    ChannelSkuCreateDTO skuCreateMapping(SkuInfoDTO param);


    default ChannelSpuCreateOrUpdateDTO convertForOptional(SpuInfoDTO param, boolean optByChannelSpuId) {
        ChannelSpuCreateOrUpdateDTO dto = new ChannelSpuCreateOrUpdateDTO();

        // 区分使用商家自定义商品ID还是渠道商品ID更新
        if (optByChannelSpuId) {
            dto.setSku_id(param.getChannelSpuId());
        } else {
            dto.setCustom_sku_id(param.getCustomSpuId());
        }
        // 类目属性
        List<PropValueDTO> propValueDTOS = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCommonAttributes())) {
            propValueDTOS.addAll(PropValueDTO.buildCommonPropValueList(param.getCommonAttributes()));
        }
        if (param.getSpecType() == SpecTypeEnum.MULTI.getCode()) {
            List<SkuSpecDTO> skuSpecDTOList = Fun.map(param.getSkus(), sku -> {
                SkuSpecDTO skuSpecDTO = new SkuSpecDTO();
                skuSpecDTO.setSku_spec_id(sku.getChannelSkuId());
                skuSpecDTO.setSku_spec_custom_id(sku.getCustomSkuId());
                return skuSpecDTO;
            });

            if (CollectionUtils.isNotEmpty(skuSpecDTOList)) {
                dto.setSku_spec(skuSpecDTOList);
                PropValueDTO defaultProp = PropValueDTO.ofMultiSpecType(SpecTypeEnum.MULTI.getCode());
                propValueDTOS.add(defaultProp);
            }
        }
        // 设置类目属性
        if (CollectionUtils.isNotEmpty(propValueDTOS)) {
            dto.setCat_property(propValueDTOS);
        }
        return dto;
    }


    default ChannelSpuCreateOrUpdateDTO convert(SpuInfoDTO param, boolean update, boolean optByChannelSpuId,
                                                Long tenantId, int multiSpecType, boolean updateMustMultiSpecPrice,
                                                boolean isConvenienceOrFlagshipStoreMode, boolean isErpSpuTenant,
                                                boolean isMedicineUmWareHouse) {
        ChannelSpuCreateOrUpdateDTO dto = new ChannelSpuCreateOrUpdateDTO();
        // 公共属性
        dto.setName(param.getName());
        dto.setBrand_name(param.getBrandName());
        dto.setPhotos(ChannelSpuCreateOrUpdateDTO.toPhotos(param.getPictures()));
        dto.setCat_prop_rec_enable(param.isRecommendCategoryProperty());
        List<PropValueDTO> propValueDTOS = PropValueDTO.buildCommonPropValueList(param.getCommonAttributes());
        dto.setCat_property(propValueDTOS);
        // 区分使用商家自定义商品ID还是渠道商品ID更新
        if (optByChannelSpuId) {
            dto.setSku_id(param.getChannelSpuId());
        } else {
            dto.setCustom_sku_id(param.getCustomSpuId());
        }
        dto.setCategory_list(ElmConverterUtil.convert2ELMCategoryCode(param));
        dto.setStatus(param.getStatus() == 1 ? 1 : 0);
        // 无人仓 饿了么渠道根据配置决定是否过滤表情符
        if(isMedicineUmWareHouse){
            String regex = MccConfigUtil.getFilterSpecEmojiByRegexForElm();
            if(StringUtils.isNotBlank(regex) && StringUtils.isNotBlank(param.getDescription())){
                dto.setDesc(param.getDescription().replaceAll(regex, ""));
            }else{
                dto.setDesc(param.getDescription());
            }
        }else{
            dto.setDesc(param.getDescription());

        }
        dto.setSummary(param.getSellPoint());
        // 商家自定义属性
        dto.fillSkuProperty(param.getProperties(), isConvenienceOrFlagshipStoreMode, isErpSpuTenant);
        // 可售时间
        dto.fillAvailableTimes(param.getSkus().get(0).getAvailableTimes());
        if (param.getSpecType() == SpecTypeEnum.SINGLE.getCode()) {
            dto.setCat3_id(param.getChannelCategoryId());
            // 单规格属性
            SkuInSpuInfoDTO sku = param.getSkus().get(0);
            if (update) {
                dto.setUpc_update(sku.getUpc());
                if(isConvenienceOrFlagshipStoreMode){
                    // 类目属性为空时，添加空对象将渠道侧类目属性置空
                    if(CollectionUtils.isEmpty(dto.getCat_property())){
                        dto.getCat_property().add(new PropValueDTO());
                    }
                }
            } else {
                dto.setUpc(sku.getUpc());
            }

            dto.setLeft_num(sku.getStock());

            // 设置最小起购数，已全量
            if (sku.getMinPurchaseQuantity() > 0 ) {
                if (MccConfigUtil.getElmMinPurchaseQuantitySwitch(tenantId)) {
                    dto.setMinimum(sku.getMinPurchaseQuantity());
                } else if (CollectionUtils.isEmpty(dto.getSku_property())) {
                    dto.setMinimum(sku.getMinPurchaseQuantity());
                }
            }
            // 注意：饿了么单规格在创建时才需要必填价格，这儿针对基础类型做下兼容
            if (!update || sku.isSetPrice()) {
                dto.setSale_price(MoneyUtils.yuanToFen(sku.getPrice()));
            }
            dto.setWeight(sku.getWeight());
            dto.setUpc(sku.getUpc());
        } else {
            if (!update || MccConfigUtil.isElmUpdateCatId3WhenMultiSpec()) {
                dto.setCat3_id(param.getChannelCategoryId());
            }
            // 多规格属性
            // 对于部分特殊的饿了么渠道类目，属性值的propertyId取特殊值
            PropValueDTO defaultProp = PropValueDTO.ofMultiSpecType(multiSpecType);
            propValueDTOS.add(defaultProp);
            //AtomicLong valueId = new AtomicLong(1);
            List<SkuSpecDTO> skuSpecList = param.getSkus().stream().map(sku -> {
                SkuSpecDTO skuSpec = new SkuSpecDTO();

                //如果需要以channelSkuId为key 来替换饿了么渠道商品的customSkuId
                if (sku.updateCumstomSkuId && StringUtils.isNotBlank(sku.getChannelSkuId())){
                    skuSpec.setSku_spec_id(sku.getChannelSkuId());
                }

                skuSpec.setSku_spec_custom_id(sku.getCustomSkuId());
                // 对于部分特殊的饿了么渠道类目，属性值的propertyId取特殊值
                PropValueDTO selfProp = PropValueDTO.ofMultiSpecType(multiSpecType);
                //selfProp.setValueId(valueId.getAndIncrement());
                selfProp.setValueText(sku.getSpec());
                if (CollectionUtils.isEmpty(sku.getSaleAttrValueList())) {
                    skuSpec.setSpec_property(selfProp);
                } else {
                    skuSpec.setSpec_property(PropValueDTO.buildFromSaleAttrValueDTOList(sku.getSaleAttrValueList()));
                }
                skuSpec.setUpc(sku.getUpc());
                skuSpec.setLeft_num(sku.getStock());

                //创建场景必传价格字段
                if(!update){
                    skuSpec.setSale_price(MoneyUtils.yuanToFen(sku.getPrice()));
                }else{//跟新场景
                    if(updateMustMultiSpecPrice){//跟新场景需要更新价格
                        skuSpec.setSale_price(MoneyUtils.yuanToFen(sku.getPrice()));
                    }else{//跟新场景不是继续跟新价格
                        if (sku.isSetPrice()) {
                            skuSpec.setSale_price(MoneyUtils.yuanToFen(sku.getPrice()));
                        }
                    }
                }

                skuSpec.setWeight(sku.getWeight());
                return skuSpec;
            }).collect(Collectors.toList());
            dto.setSku_spec(skuSpecList);
        }
        dto.setCat_property(CollectionUtils.isEmpty(dto.getCat_property()) ? null : dto.getCat_property());
        return dto;
    }

    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "sku_id", source = "skuId"),
            @Mapping(target = "custom_sku_id", source = "customSkuId"),
    })
    UpdateCustomSkuId updateCustomSkuIdMapping(UpdateCustomSkuIdDTO param);

    @Mappings({
            @Mapping(target = "skuId", source = "custom_sku_id"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "unit", constant = "1"),
            @Mapping(target = "spec", constant = "份"),
            @Mapping(target = "weight", source = "weight"),
            @Mapping(target = "upc", source = "upc"),
            @Mapping(target = "stock", source = "left_num"),
            @Mapping(target = "price", source = "sale_price"),
            @Mapping(target = "sourceType", constant = "3"),
            @Mapping(target = "skuStatus", expression = "java(skuCreateDTO.getStatus() == 1 ? 1 : 2)"),
            @Mapping(target = "frontCategory", source = "category_id"),
            @Mapping(target = "frontCategoryName", source = "category_name"),
            @Mapping(target = "channelFrontCategory", source = "category_id"),
            @Mapping(target = "channelFrontCategoryName", source = "category_name"),
            @Mapping(target = "pictures", expression = "java(ChannelSkuCreateDTO.toPhotoList(skuCreateDTO.getPhotos()))"),
            @Mapping(target = "productionArea", source = "production_addr3"),
            @Mapping(target = "sequence", source = "sequence"),
            @Mapping(target = "isSp", source = "isSp"),
    })
    SkuInfoDTO skuCreateDTOMapping(ChannelSkuCreateDTO skuCreateDTO);

    List<SkuInfoDTO> skuCreateDTOsMapping(List<ChannelSkuCreateDTO> skuCreateDTOs);

    @Mappings({
            @Mapping(target = "catId", source = "categoryId"),
            @Mapping(target = "name", source = "categoryName"),
            @Mapping(target = "sequence", source = "sequence"),
            @Mapping(target = "depth", source = "depth"),
            @Mapping(target = "parentId", source = "parentId"),
            @Mapping(target = "customCatId", source = "customCategoryId")
    })
    CatInfo channelStoreCategoryDTOMapping(ChannelStoreCategoryDTO data);

    List<CatInfo> channelStoreCategoryDTOMapping(List<ChannelStoreCategoryDTO> data);

    @Mappings({
            @Mapping(target = "skuId", source = "sku_id"),
            @Mapping(target = "customSkuId", source = "custom_sku_id"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "unit", constant = "份"),// 1.7.0调整
            @Mapping(target = "spec", constant = ""),
            @Mapping(target = "weight", source = "weight"),
            @Mapping(target = "upc", source = "upc"),
            @Mapping(target = "stock", source = "left_num"),
            @Mapping(target = "price", expression = "java(MoneyUtils.fenToYuan(channelSkuInfo.getSale_price()).doubleValue())"),
            @Mapping(target = "sourceType", constant = "3"),
            @Mapping(target = "skuStatus", expression = "java(channelSkuInfo.getStatus() == 1 ? 1 : 2)"),
            @Mapping(target = "frontCategory", expression = "java(CollectionUtils.isNotEmpty(channelSkuInfo.getCustom_cat_list()) ? channelSkuInfo.getCustom_cat_list().get(0).getCustom_cat_id() : \"\")"),
            @Mapping(target = "frontCategoryName", expression = "java(CollectionUtils.isNotEmpty(channelSkuInfo.getCustom_cat_list()) ? channelSkuInfo.getCustom_cat_list().get(0).getCustom_cat_name() : \"\")"),
            @Mapping(target = "channelFrontCategory", expression = "java(CollectionUtils.isNotEmpty(channelSkuInfo.getCustom_cat_list()) ? channelSkuInfo.getCustom_cat_list().get(0).getCustom_cat_id() : \"\")"),
            @Mapping(target = "channelFrontCategoryName", expression = "java(CollectionUtils.isNotEmpty(channelSkuInfo.getCustom_cat_list()) ? channelSkuInfo.getCustom_cat_list().get(0).getCustom_cat_name() : \"\")"),
            @Mapping(target = "pictures", expression = "java(ChannelSkuInfo.toPhotoList(channelSkuInfo.getPhotos()))"),
            @Mapping(target = "productionArea", source = "production_addr3"),
            // 1.7.0 新增
            //@Mapping(target = "description", source = "summary"),
            /*@Mapping(target = "sequence", source = "sequence"),
            @Mapping(target = "isSp", source = "isSp"),*/
    })
    SkuInfoDTO channelSkuInfoMapping(ChannelSkuInfo channelSkuInfo);

    List<SkuInfoDTO> channelSkuInfoListMapping(List<ChannelSkuInfo> channelSkuInfoList);

    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "category_id", expression = "java(StringUtils.isNotBlank(data.getChannelCategoryCode())?Long.valueOf(data.getChannelCategoryCode()):null)"),
            @Mapping(target = "category_name", expression = "java(StringUtils.isNotBlank(data.getChannelCategoryCode())?null:data.getChannelCategoryName())")
    })
    ChannelCategoryDeleteDTO deleteCategory(CategoryInfoDeleteDTO data);

    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "category_id", source = "channelCategoryCode"),
            @Mapping(target = "rank", expression = "java(ConverterUtils.sortConvert(data.getSort()))"),//source = "sort"),
    })
    ChannelCategorySortDTO sortCategory(CategoryInfoSortItemDTO data);

    List<ChannelCategorySortDTO> sortCategory(List<CategoryInfoSortItemDTO> data);

    @Mappings({
            @Mapping(target = "appPoiCode", source = "shop_id"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "province", source = "province"),
            @Mapping(target = "city", source = "city"),
            @Mapping(target = "county", source = "county"),
            @Mapping(target = "address", source = "address"),
            @Mapping(target = "longitude", source = "longitude"),
            @Mapping(target = "latitude", source = "latitude"),
            @Mapping(target = "picUrl", source = "shop_logo"),
            @Mapping(target = "invoiceSupport", source = "invoice_support"),
            @Mapping(target = "isOnline", constant = "1"),
            @Mapping(target = "closeTime", expression = "java(ConverterUtils.getElmCloseTime(channelPoiInfo.getBusiness_time()))"),
            @Mapping(target = "shippingTime", expression = "java(ConverterUtils.convertShippingTime(channelPoiInfo.getBusiness_time()))"),
//            @Mapping(target = "shippingTime", source = "business_time"),
            @Mapping(target = "channelPoiCode", source = "baidu_shop_id"),
            @Mapping(target = "hotline", source = "service_phone")
    })
    PoiInfo poiInfoMapping(ChannelPoiInfo channelPoiInfo);

    @Mappings({
            @Mapping(target = "catId", source = "cat_id"),
            @Mapping(target = "name", source = "cat_name"),
            @Mapping(target = "parentId", source = "parent_id"),
            @Mapping(target = "depth", source = "depth")
    })
    CatInfo catInfoMapping(ChannelCatInfo channelCatInfo);

    List<CatInfo> catInfoMappings(List<ChannelCatInfo> channelCatInfos);

    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "custom_sku_id", source = "skuId")
    })
    ChannelSkuDeleteDTO skuDeleteMapping(SkuInfoDeleteDTO skuInfoDeleteDTO);

    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "custom_sku_id", expression = "java(ChannelPriceUpdateDTO.valueOf(request.paramList))")
    })
    ChannelPriceUpdateDTO updatePrice(SkuPriceRequest request);

    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "custom_sku_id", expression = "java(ChannelPriceUpdateDTO.valueOfM(request.paramList))")
    })
    ChannelPriceUpdateDTO updatePriceMultiChannel(SkuPriceMultiChannelRequest request);


    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "custom_sku_id", expression = "java(ChannelPriceUpdateDTO.valueOfSingleSpec(request.paramList))")
    })
    ChannelPriceUpdateDTO updatePriceMultiChannelSingelSpec(SkuPriceMultiChannelRequest request);

    @Mappings({
        @Mapping(target = "shop_id", constant = "{}"),
        @Mapping(target = "skuid_price", expression = "java(ChannelPriceUpdateDTO.valueOfSingleSpecByChanelSkuId(request.paramList))")
    })
    ChannelPriceUpdateDTO updatePriceMultiChannelSingelSpecByChannelSkuId(SkuPriceMultiChannelRequest request);

    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "custom_sku_id", expression = "java(ChannelStockUpdateDTO.valueOfM(request.skuStockList))")
    })
    ChannelStockUpdateDTO updateStockMultiChannel(SkuStockMultiChannelRequest request);

    @Mappings({
            @Mapping(source = "customSkuId", target = "spec_custom_id"),
            @Mapping(source = "stockQty", target = "left_num")
    })
    ChannelSpecStockDetail updateSpuStock(SkuInSpuStockDTO spuStockDTO);

    List<ChannelSpecStockDetail> updateSpuStock(List<SkuInSpuStockDTO> spuStockDTO);

    @Mappings({
            @Mapping(source = "orderId", target = "order_id")
    })
    ChannelOrderDetailParam     channelOrderDetailParamMapping(GetChannelOrderDetailRequest request);

    @Mappings({
            @Mapping(source = "order_id", target = "channelOrderId"),
            @Mapping(source = "eleme_order_id", target = "channelExtraOrderId"),
            @Mapping(source = "user_fee", target = "actualPayAmt"),
            @Mapping(source = "total_fee", target = "originalAmt"),
            @Mapping(source = "shop_fee", target = "bizReceiveAmt"),
            @Mapping(source = "send_fee", target = "freight"),
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(channelOrderDetail.getCreate_time()))", target = "createTime"),
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(channelOrderDetail.getPay_time()))", target = "payTime"),
            @Mapping(expression = "java(ConverterUtils.codeToBool(channelOrderDetail.getNeed_invoice()))", target = "isNeedInvoice"),
            @Mapping(source = "pay_type", target = "payType"),
            @Mapping(source = "pay_status", target = "payStatus"),
            @Mapping(source = "order_index", target = "orderSerialNumber"),
            @Mapping(source = "package_fee", target = "packageAmt"),
            @Mapping(source = "remark", target = "comment"),
            @Mapping(source = "discount_fee", target = "totalDiscount"),
            @Mapping(source = "down_flag", target = "downFlag"),
            @Mapping(source = "three_card_no", target = "threeCardNo"),
            @Mapping(expression = "java(ElmConverterUtil.isBooking(channelOrderDetail.getSend_immediately()))", target = "isBooking"),
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(channelOrderDetail.getCancel_time()))", target = "cancelTime"),
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(channelOrderDetail.getFinished_time()))", target = "completedTime"),
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(channelOrderDetail.getConfirm_time()))", target = "confirmTime"),
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(channelOrderDetail.getPickup_time()))", target = "logisticFetchTime"),
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(channelOrderDetail.getDelivery_time()))", target = "logisticCompletedTime"),
            @Mapping(target = "dispatcherName", constant = ""),
            @Mapping(target = "dispatcherPhone", source = "delivery_phone"),
            @Mapping(expression = "java(channelOrderDetail.getCommission().intValue())", target = "platformFee"),
            @Mapping(source = "merchant_total_fee", target = "poiPackageAmt"),
            @Mapping(source = "order_from", target = "channelOrderFrom"),
    })
    ChannelOrderDetailDTO channelOrderDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
            @Mapping(source = "custom_sku_id", target = "customSpu"),
            @Mapping(source = "product_name", target = "skuName"),
            @Mapping(expression = "java(StringUtils.isNotEmpty(orderSkuDetail.getCustomSkuSpecId()) ? orderSkuDetail.getCustomSkuSpecId() : orderSkuDetail.getCustom_sku_id())", target = "channelSkuId"),
            @Mapping(source = "product_amount", target = "quantity"),
            @Mapping(source = "product_price", target = "salePrice"),
            @Mapping(source = "package_amount", target = "packageCount"),
            @Mapping(source = "package_price", target = "packagePrice"),
            @Mapping(source = "package_fee", target = "packageFee"),
            @Mapping(source = "upc", target = "upcCode"),
            @Mapping(source = "baidu_product_id", target = "baiduProductId"),
            @Mapping(expression = "java(ElmConverterUtil.extractProductWeight(orderSkuDetail))", target = "weight"),
            @Mapping(source = "product_price", target = "originalPrice"),
            @Mapping(expression = "java(StringUtils.isNotEmpty(orderSkuDetail.getCustomSkuSpecId()) ? orderSkuDetail.getCustomSkuSpecId() : orderSkuDetail.getCustom_sku_id())", target = "skuId"),
            @Mapping(source = "sub_biz_order_id", target = "channelItemId"),
            @Mapping(expression = "java(ElmConverterUtil.extractSkuProperty(orderSkuDetail.getProduct_features()))", target = "skuProperty"),
            @Mapping(expression = "java(ElmConverterUtil.extractItemType(orderSkuDetail))", target = "itemType"),
            @Mapping(expression = "java(ElmConverterUtil.convertProductLabelList(orderSkuDetail))", target = "channelLabelList")
    })
    OrderProductDetailDTO orderSkuDetailMapping(OrderSkuDetail orderSkuDetail);

    List<OrderProductDetailDTO> orderSkuDetailListMapping(List<OrderSkuDetail> orderSkuDetailList);

    @Mappings({
            @Mapping(source = "activity_id", target = "activityId"),
            @Mapping(source = "fee", target = "actDiscount"),
            @Mapping(source = "baidu_rate", target = "channelCharge"),
            @Mapping(source = "shop_rate", target = "bizCharge"),
            @Mapping(source = "agent_rate", target = "agentCharge"),
            @Mapping(source = "logistics_rate", target = "logisticsCharge"),
            @Mapping(source = "desc", target = "remark"),
            @Mapping(source = "type", target = "type")
    })
    OrderDiscountDetailDTO orderActivitieInfoMapping(OrderActivitiesInfo orderActivitiesInfo);

    List<OrderDiscountDetailDTO> orderActivitieInfoListMapping(List<OrderActivitiesInfo> orderActivitiesInfoList);

    @Mappings({
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(channelOrderDetail.getSend_time()))", target = "arrivalTime"),
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(channelOrderDetail.getLatest_send_time()))", target = "arrivalEndTime"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.elmDeliveryMethodMapping(channelOrderDetail.getDelivery_party(),channelOrderDetail.getBusiness_type()))", target = "deliveryMethod"),
            @Mapping(expression = "java(ElmConverterUtil.convertDeliveryType(channelOrderDetail.getDelivery_party()))", target = "originalDeliveryType"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.eleSelfDeliveryCode(channelOrderDetail.getBusiness_type(),channelOrderDetail.getPick_up_code()))", target = "selfFetchCode"),
            @Mapping(source = "expensive_product_pickup_code", target = "expensiveProductPickupCode")
    })
    OrderDeliveryDetailDTO deliveryDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
            @Mapping(source = "invoice_title", target = "invoiceTitle"),
            @Mapping(source = "taxer_id", target = "taxNo")
    })
    OrderInvoiceDetailDTO invoiceDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "custom_sku_id", expression = "java(ChannelSkuPropUpdateDTO.toSkus(data.getSkuId()))")
    })
    ChannelSkuPropUpdateDTO updateSkuSellStatus(SkuSellStatusInfoDTO data);

    @Mappings({
        @Mapping(target = "shop_id", constant = "{}"),
        @Mapping(target = "sku_id", expression = "java(ChannelSkuPropUpdateDTO.toChannelSkus(data.getSkuId()))")
    })
    ChannelSkuPropUpdateDTO updateSellStatusByChannelSkuId(SkuSellStatusInfoDTO data);

    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "sku_id", expression = "java(ChannelSkuPropUpdateByChannelSkuIdDTO.toChannelSkuIds(data.getSkuId()))")
    })
    ChannelSkuPropUpdateByChannelSkuIdDTO updateSkuSellStatusByChannelSkuId(SkuSellStatusInfoDTO data);

    @Mappings({
            @Mapping(target = "order_id", source = "orderId"),
    })
    ChannelOrderCancelRelDTO agreeRefund(AgreeRefundRequest request);

    @Mappings({
            @Mapping(target = "order_id", source = "orderId"),
            @Mapping(target = "refuse_reason", source = "reason"),
    })
    ChannelOrderCancelRelDTO rejectRefund(RejectRefundRequest request);

    @Mappings({
            @Mapping(target = "skuName", source = "name"),
            @Mapping(target = "sku", source = "custom_sku_id"),
            @Mapping(target = "upc", source = "upc"),
            @Mapping(target = "count", source = "number"),
            @Mapping(target = "skuRefundAmount", source = "total_refund"),
            @Mapping(target = "foodPrice", constant = "0"),
            @Mapping(target = "boxPrice", constant = "0"),
            @Mapping(target = "boxNum", constant = "0")
    })
    @Deprecated
    RefundSku partRefundSkuInfo(PartRefundSkuInfo partRefundSkuInfo);

    @Deprecated
    List<RefundSku> partRefundSkuInfoList(List<PartRefundSkuInfo> partRefundSkuInfoList);


    @Mappings({
            @Mapping(target = "skuName", source = "skuName"),
            @Mapping(target = "sku", source = "skuId"),
            // @Mapping(target = "upc", source = "upc"),
            @Mapping(target = "count", source = "count"),
            @Mapping(target = "skuRefundAmount", source = "skuRefundAmount"),
            @Mapping(target = "foodPrice", constant = "0"),
            @Mapping(target = "boxPrice", constant = "0"),
            @Mapping(target = "boxNum", constant = "0")
    })
    RefundSku refundSkuMapping(RefundProductDTO refundProductDTO);

    List<RefundSku> refundSkuMappingList(List<RefundProductDTO> refundProductDTOS);




    @Mappings({
            @Mapping(target = "shop_id", constant = "{}"),
            @Mapping(target = "custom_sku_id", source = "skuId"),
            @Mapping(target = "category_id", source = "channelFrontCategory"),
    })
    ChannelSkuCategoryMapDTO skuCreateMappingCat(SkuInfoDTO data);

    @Mappings({
            @Mapping(source = "orderId", target = "order_id")
    })
    PoiConfirmOrderParam poiConfirmOrderParamMapping(PoiConfirmOrderRequest request);


    @Mappings({
            @Mapping(target = "custom_sku_id", expression = "java(StringUtils.defaultIfBlank(refundProductInfoDTO.getSkuId(), refundProductInfoDTO.getCustomSkuId()))"),
            @Mapping(target = "number", expression = "java(String.valueOf(refundProductInfoDTO.getCount()))")
    })
    PoiPartRefundProductInfo refundProductMapping(RefundProductInfoDTO refundProductInfoDTO);

    @Mappings({
            @Mapping(source = "orderId", target = "order_id"),
            @Mapping(source = "refundProducts", target = "products")
    })
    PoiPartRefundApplyParam poiPartRefundApply(PoiPartRefundRequest request);


    @Mappings({
            @Mapping(target = "platform_sku_id", expression = "java(StringUtils.defaultIfBlank(refundProductInfoDTO.getSkuId(), refundProductInfoDTO.getCustomSkuId()))"),
            @Mapping(target = "sub_biz_order_id", source = "channelOrderItemId"),
            @Mapping(target = "number", expression = "java(String.valueOf(refundProductInfoDTO.getCount()))")
    })
    ElmPoiReverseApplyParam.ElmRefundProduct elmRefundProductMapping(RefundProductInfoDTO refundProductInfoDTO);

    @Mappings({
            @Mapping(source = "orderId", target = "order_id"),
            @Mapping(expression = "java(ElmConverterUtil.generateIdempotentId())", target = "idempotent_id"),
            @Mapping(constant = "2", target = "refund_type"),
            @Mapping(source = "reasonCode", target = "reason_code"),
            @Mapping(source = "reason", target = "reason_remarks"),
            @Mapping(source = "refundProducts", target = "refund_product_list"),
    })
    ElmPoiReverseApplyParam poiPartRefundApplyMapping(PoiPartRefundRequest request);

    @Mappings({
            @Mapping(target = "platform_sku_id", expression = "java(StringUtils.defaultIfBlank(refundProductInfoDTO.getSkuId(), refundProductInfoDTO.getCustomSkuId()))"),
            @Mapping(target = "sub_biz_order_id", source = "channelOrderItemId"),
            @Mapping(target = "refund_amount", source = "refundAmount")
    })
    ElmPoiReverseApplyParam.ElmRefundProduct elmRefundProductMapping(MoneyRefundProductInfoDTO refundProductInfoDTO);

    @Mappings({
            @Mapping(source = "orderId", target = "order_id"),
            @Mapping(expression = "java(ElmConverterUtil.generateIdempotentId())", target = "idempotent_id"),
            @Mapping(constant = "2", target = "refund_type"),
            @Mapping(source = "reasonCode", target = "reason_code"),
            @Mapping(source = "reasonRemarks", target = "reason_remarks"),
            @Mapping(source = "refundProductInfoList", target = "refund_product_list")
    })
    ElmPoiReverseApplyParam moneyRefundMapping(MoneyRefundRequest request);

    @Mappings({
            @Mapping(source = "orderId", target = "order_id"),
            @Mapping(source = "reason", target = "reason"),
            @Mapping(target = "type", expression = "java(OrderRefundCodeConventer.elmRefundCodeMapping(poiCancelOrderRequest.getReason_code()))"),
    })
    PoiCancelApplyParam poiCancelOrderApply(PoiCancelOrderRequest poiCancelOrderRequest);

    @Mappings({
            @Mapping(source = "orderId", target = "order_id"),
            @Mapping(expression = "java(ElmConverterUtil.generateIdempotentId())", target = "idempotent_id"),
            @Mapping(constant = "1", target = "refund_type"),
            @Mapping(source = "reason_code", target = "reason_code"), // 直接使用前段传递的编码
            @Mapping(source = "reason", target = "reason_remarks"),
    })
    ElmPoiReverseApplyParam poiCancelOrderApplyMapping(PoiCancelOrderRequest poiCancelOrderRequest);

    @Mappings({
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(billGetResult.getDate()))", target = "accountTimeStart"),
            @Mapping(expression = "java(ConverterUtils.secondStringToMillis(billGetResult.getDate()))", target = "settleFinishDate"),
            @Mapping(expression = "java(ElmConverterUtil.generateSettleOrderId(billGetResult))", target = "settleOrderId"),
            @Mapping(expression = "java(billGetResult.getPay_fee())", target = "settleAmount"),
            @Mapping(expression = "java(ElmConverterUtil.parseElmSettlementFeeDetailList(billGetResult.getOrder_detail_fee()))", target = "settlementFeeDetailList"),
            @Mapping(expression = "java(com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum.ELEM.getCode())", target = "channelId"),
            @Mapping(constant = "饿了么账户余额", target = "accountNo"),
            @Mapping(constant = "3", target = "payMethod"),
            @Mapping(constant = "余额钱包", target = "payMethodDesc"),
            @Mapping(constant = "-1", target = "settleStatus"),
            @Mapping(constant = "--", target = "settleStatusDesc"),
            @Mapping(source = "order_count", target = "orderCount"),
    })
    ChannelSettlementDTO convertChannelSettlement(BillGetResult billGetResult);

    @Mappings({
            @Mapping(expression = "java(com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum.ELEM.getCode())", target = "channelId"),
            @Mapping(expression = "java(BigDecimal.valueOf(billOrderDetail.getAmount()).multiply(BigDecimal.valueOf(100)).longValue())", target = "settleMilliAmt"),
            @Mapping(expression = "java(billOrderDetail.getOrder_id())", target = "channelOrderId"),
            @Mapping(expression = "java(ElmConverterUtil.secondStringToMillis(billOrderDetail.getTrade_create_time()))", target = "settlementDate"),
            @Mapping(expression = "java(ElmConverterUtil.parseElmSettlementFeeDetailList(billOrderDetail.getOrder_detail_fee()))", target = "settlementFeeDetailList"),
            @Mapping(expression = "java(ElmConverterUtil.parseElmSettlementExtraInfoDetailList(billOrderDetail.getOrder_detail_fee()))", target = "settlementExtraInfoDetailList"),
            @Mapping(expression = "java(billOrderDetail.getOrder_detail_fee().getBusinessTypeDesc())", target = "orderChargeTypeDesc"),
            @Mapping(expression = "java(billOrderDetail.getOrder_detail_fee().getRtsRefundOrderId())", target = "refundId"),
            @Mapping(expression = "java(ElmConverterUtil.secondStringToMillis(billOrderDetail.getOrder_create_time()))", target = "orderTime"),
            @Mapping(expression = "java(StringUtils.isNotBlank(billOrderDetail.getOrder_index()) && !billOrderDetail.getOrder_index().equalsIgnoreCase(\"null\") ? billOrderDetail.getOrder_index() : null)", target = "orderIndex"),
            @Mapping(source = "ele_order_id", target = "extOrderId")
    })
    ChannelOrderSettlementDTO convertChannelSettlementDetail(BillOrderDetail billOrderDetail);

    @Mappings({
            @Mapping(expression = "java(ChannelStatusConvertUtil.elmDeliveryStatusMapping(Integer.parseInt(data.getStatus())))", target = "status"),
            @Mapping(source = "name", target = "riderName"),
            @Mapping(source = "phone", target = "riderPhone")
    })
    LogisticsStatusDTO convertLogistcsStatus(ElmLogisticsStatusDTO data);

    @Mappings({
            @Mapping(source = "custom_sku_id", target = "skuId"),
            @Mapping(source = "shop_refund", target = "refundPoiPromotion"),
            @Mapping(source = "shop_ele_refund", target = "refundPlatPromotion"),
            @Mapping(source = "name", target = "skuName"),
            @Mapping(source = "number", target = "count"),
            @Mapping(source = "total_refund", target = "skuRefundAmount")
    })
    RefundProductDTO convertRefundProductDTO(RefundDetail refundDetail);

    List<RefundProductDTO> convertRefundProductDTOs(List<RefundDetail> refundDetails);

    @Mappings({
            @Mapping(target = "storeId", source = "storeId"),
            @Mapping(target = "skuId", source = "channelSkuId"),
            @Mapping(target = "customSkuId", source = "customSkuId")
    })
    UpdateCustomSkuIdDTO convert2UpdateCustomSkuIdDTO(ChangeCustomSkuIdDTO param);


    default ChannelSpuCategoryMapDTO convert2ChannelSpuCategoryMapDTO(UpdateCustomSpuStoreCategoryRequest request) {
        ChannelSpuCategoryMapDTO dto = new ChannelSpuCategoryMapDTO();
        dto.setCustom_sku_id(request.getCustomSpuId());
        dto.setCategory_list(request.getCategoryRankList().stream()
                .map(c -> {
                    ChannelSpuCategoryMapDTO.CategoryRankDTO category = new ChannelSpuCategoryMapDTO.CategoryRankDTO();
                    category.setCategory_id(c.getCategoryId());
                    category.setRank(c.getRank());
                    return category;
                }).collect(Collectors.toList()));

        return dto;
    }

    default ChannelSpuCategoryMapDTO convert2ChannelSpuCategoryMapDTO(ProductStoreCategoryDTO productStoreCategoryDTO) {
        ChannelSpuCategoryMapDTO dto = new ChannelSpuCategoryMapDTO();
        dto.setCustom_sku_id(productStoreCategoryDTO.getCustomSpuId());
        dto.setCategory_list(productStoreCategoryDTO.getCategoryRankList().stream().map(c -> {
            ChannelSpuCategoryMapDTO.CategoryRankDTO category = new ChannelSpuCategoryMapDTO.CategoryRankDTO();
            category.setCategory_id(c.getCategoryId());
            return category;
        }).collect(Collectors.toList()));

        return dto;
    }

}
