package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.doudian.open.api.address_list.AddressListRequest;
import com.doudian.open.api.address_list.AddressListResponse;
import com.doudian.open.api.address_list.data.AddressListItem;
import com.doudian.open.api.address_list.param.AddressListParam;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailRequest;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailResponse;
import com.doudian.open.api.afterSale_Detail.param.AfterSaleDetailParam;
import com.doudian.open.api.afterSale_fillLogistics.AfterSaleFillLogisticsRequest;
import com.doudian.open.api.afterSale_fillLogistics.AfterSaleFillLogisticsResponse;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptRequest;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptResponse;
import com.doudian.open.api.order_batchDecrypt.data.DecryptInfosItem;
import com.doudian.open.api.order_batchDecrypt.param.CipherInfosItem;
import com.doudian.open.api.order_batchDecrypt.param.OrderBatchDecryptParam;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.DoudianOpConfig;
import com.doudian.open.exception.DoudianOpException;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DecryptReqDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DecryptRespDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DyOrderDecryptTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.DouyinConfigDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetOrderAfsApplyListResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MetricUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 抖音进行重试接口业务类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class DouyinRetryChannelService extends DouyinBaseService {

    /**
     * 查询门店地址列表,根据老qnh逻辑进行重试
     *
     * @param requestConfig
     * @param storeId
     * @return
     */
    @Retryable(value = BizException.class, maxAttempts = 3, backoff = @Backoff(delay = 50))
    public List<AddressListItem> getAddressList(DouyinConfigDto requestConfig, Long storeId) {
        AddressListRequest request = new AddressListRequest();
        request.setConfig(requestConfig.getDoudianOpConfig());
        AddressListParam param = request.getParam();
        // 门店一般不会维护很多地址,简单实现,写死
        param.setPageNo(1L);
        param.setPageSize(10L);
        param.setOrderBy("desc");
        param.setOrderField("update_time");
        param.setStoreId(storeId);
        AddressListResponse response;
        try {
            response = request.execute(requestConfig.getAccessToken());
            log.info("storeId{}查询门店地址列表返回数据:{}", storeId, response);
            return response.getData().getAddressList();
        } catch (DoudianOpException e) {
            log.error("storeId{}查询门店地址列表异常", storeId, e);
            throw new BizException(ResultCode.FAIL.getCode(), "查询门店地址列表接口失败");
        } catch (Exception e) {
            log.error("storeId{}查询门店地址列表异常", storeId, e);
        }
        return Lists.newArrayList();
    }

    /**
     * 填写商家售后物流信息,根据老qnh逻辑进行重试
     * 
     * @return
     */
    @Retryable(value = BizException.class, maxAttempts = 3, backoff = @Backoff(delay = 50))
    public boolean fillAfterSaleLogistics(AfterSaleFillLogisticsRequest request, AccessToken accessToken) {
        AfterSaleDetailRequest dyRequest = new AfterSaleDetailRequest();
        dyRequest.setConfig(request.getConfig());
        AfterSaleDetailParam param = dyRequest.getParam();
        param.setAfterSaleId(request.getParam().getAftersaleId().toString());
        param.setNeedOperationRecord(true);
        dyRequest.setParam(param);
        AfterSaleDetailResponse queryResponse = dyRequest.execute(accessToken);
        log.info("填写商家售后物流信息查询抖音渠道退款信息，request：{},response:{}", JSON.toJSONString(request),JSON.toJSONString(queryResponse));
        if (queryResponse != null
                && queryResponse.isSuccess()
                && queryResponse.getData() != null
                && queryResponse.getData().getProcessInfo() != null
                && queryResponse.getData().getProcessInfo().getAfterSaleInfo() != null
        ) {
            // 判断是否需要跳过请求接口
            if(!MccConfigUtil.douyinSendFillAfterSaleLogisticsWhitelist(
                            queryResponse.getData().getProcessInfo().getAfterSaleInfo().getReturnMethod())){
                log.error("非商家取货场景,不请求售后商家填写物流信息");
                return true;
            }
        }

        AfterSaleFillLogisticsResponse response;
        try {
            response = request.execute(accessToken);
        } catch (DoudianOpException e) {
            log.error("售后单号{}售后商家填写物流信息异常", request.getParam().getAftersaleId(), e);
            throw new BizException(ResultCode.FAIL.getCode(), "售后商家填写物流信息接口失败");
        }
        log.info("售后单号{}售后商家填写物流信息返回数据:{}", request.getParam().getAftersaleId(), response);
        if (!response.isSuccess()) {
            throw new BizException(ResultCode.FAIL.getCode(), response.getMsg());
        }
        return true;
    }

    public DecryptRespDto decryptWithoutRetry(long tenantId, String orderId, DecryptReqDto reqDto, int httpReadTimeout) {
        OrderBatchDecryptRequest request = buildDecryptRequest(orderId, reqDto);
        DecryptRespDto respDto = new DecryptRespDto();
        Instant start = Instant.now();
        try {
            OrderBatchDecryptResponse response = doDecrypt(tenantId, orderId, request, httpReadTimeout);
            log.info("订单敏感信息解密{}, 耗时{}, orderId: {}, resp:{}", response.isSuccess() ? "成功" : "失败",
                    Duration.between(start, Instant.now()).toMillis(), orderId, response);

            for (DecryptInfosItem decryptInfo : response.getData().getDecryptInfos()) {
                if (decryptInfo.getErrNo() != 0) {
                    continue;
                }
                // 加密类型 1、 地址加密类型 2、 姓名加密类型 3、 手机号加密类型 4、身份证类型 5、手机号加密类型(不会返回虚拟号)
                if (1L == decryptInfo.getDataType()) {
                    respDto.setAddress(decryptInfo.getDecryptText());
                } else if (2L == decryptInfo.getDataType()) {
                    respDto.setName(decryptInfo.getDecryptText());
                } else if (3L == decryptInfo.getDataType() || 5L == decryptInfo.getDataType()) {
                    respDto.setMobile(decryptInfo.getDecryptText());
                    respDto.setIsVirtualTel(decryptInfo.getIsVirtualTel());
                    respDto.setPhoneNoA(decryptInfo.getPhoneNoA());
                    respDto.setPhoneNoB(decryptInfo.getPhoneNoB());
                }
            }
            MetricUtils.count("DOUYIN_DECRYPT", "result", "success", 1);
        } catch (Exception e) {
            log.warn("调用渠道解密敏感信息接口失败, 耗时{}, orderId: {}, exception: ",
                    Duration.between(start, Instant.now()).toMillis(), orderId, e);
            MetricUtils.count("DOUYIN_DECRYPT", "result", "fail", 1);
        }

        return respDto;
    }

    private OrderBatchDecryptResponse doDecrypt(long tenantId, String orderId, OrderBatchDecryptRequest request, int timeout) {
        AccessToken token = prepareRequestConfigAndToken(request, tenantId);
        String douDianYunUrl = Lion.getConfigRepository().get("config.douyin.doudianyun.url");
        log.info("获取配置openRequestUrl:{}", douDianYunUrl);
        if (StringUtils.isNotBlank(douDianYunUrl)) {
            DoudianOpConfig config = request.getConfig();
            // 在这里hack config, 指向抖店云负载均衡
            config.setOpenRequestUrl(douDianYunUrl);
            config.setHttpClientReadTimeout(timeout);
            request.setConfig(config);
        }
        log.info("请求订单敏感信息解密接口{}, orderId: {}, req: {}", request.getUrlPath(), orderId, request);
        return request.execute(token);
    }

    private static OrderBatchDecryptRequest buildDecryptRequest(String orderId, DecryptReqDto reqDto) {
        OrderBatchDecryptRequest request = new OrderBatchDecryptRequest();
        OrderBatchDecryptParam param = request.getParam();
        List<CipherInfosItem> itemList = new ArrayList<>();
        param.setCipherInfos(itemList);

        List<String> cipherTextList = new ArrayList<>();
        if (StringUtils.isNotBlank(reqDto.getName())) {
            cipherTextList.add(reqDto.getName());
        }
        if (StringUtils.isNotBlank(reqDto.getMobile())) {
            cipherTextList.add(reqDto.getMobile());
        }
        if (StringUtils.isNotBlank(reqDto.getAddress())) {
            cipherTextList.add(reqDto.getAddress());
        }

        for (String s : cipherTextList) {
            CipherInfosItem item = new CipherInfosItem();
            item.setAuthId(orderId);
            item.setCipherText(s);
            itemList.add(item);
        }
        return request;
    }

}
