package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import java.util.LinkedList;
import java.util.List;


import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelBrandQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinBrandListResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.RecommendBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.RecommendBrandResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelBrandService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BrandInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetBrandResponse;

@Service("dyChannelBrandService")
public class DouyinChannelBrandService implements ChannelBrandService {

    @Autowired
    private DouyinChannelGateService douyinChannelGateService;


    @Override
    public GetBrandResponse batchGetBrand(GetBrandRequest request) {
        Assert.throwIfNull(request,"品牌请求为空");
        Assert.throwIfNull(request.getCategoryId(),"品牌请求没有类目id");
        Assert.throwIfNull(request.getBaseInfo(),"品牌请求无基础信息");
        Assert.throwIfNull(request.getBaseInfo().getChannelId(),"品牌请求缺少渠道id");
        Assert.throwIfNull(request.getBaseInfo().getTenantId(),"品牌请求缺少租户id");
        BaseRequest baseRequest =
                new BaseRequest().setChannelId(request.getBaseInfo().getChannelId()).setTenantId(request.getBaseInfo().getTenantId());

        GetBrandResponse response = new GetBrandResponse();
        List<BrandInfo> channelBrandInfoList = new LinkedList<>();

        ChannelResponseDTO<DouyinBrandListResult> channelResponseDTO =
                douyinChannelGateService.sendPost(ChannelPostDouyinEnum.GET_BRAND_LIST, baseRequest,
                ChannelBrandQueryParam.builder().query(request.getBrandName()).category_id(Long.parseLong(request.getCategoryId())).build());
        DouyinBrandListResult douyinBrandListResult = channelResponseDTO.getCoreData();
        if(CollectionUtils.isNotEmpty(douyinBrandListResult.getAuth_brand_list())){
            channelBrandInfoList.addAll(Fun.map(douyinBrandListResult.getAuth_brand_list(),DouyinConvertUtil::buildBrandInfo));
        }

        if(CollectionUtils.isNotEmpty(douyinBrandListResult.getBrand_list())){
            channelBrandInfoList.addAll(Fun.map(douyinBrandListResult.getBrand_list(),DouyinConvertUtil::buildBrandInfo));

        }
        //追加无品牌选项
        BrandInfo brandInfo = new BrandInfo();
        brandInfo.setBrandId(String.valueOf(DouyinConstant.DOU_YIN_DEFAULT_BRAND_ID));
        brandInfo.setBrandName(DouyinConstant.NO_BRAND);
        channelBrandInfoList.add(brandInfo);


        response.setStatus(ResultGenerator.genSuccessResult());
        response.setBrandInfoList(channelBrandInfoList);
        return response;
    }

    @Override
    public RecommendBrandResponse getRecommendBrand(RecommendBrandRequest request) {
        RecommendBrandResponse recommendBrandResponse = new RecommendBrandResponse();
        recommendBrandResponse.setStatus(ChannelStatus.build(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(),ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg()));
        return recommendBrandResponse;
    }
}
