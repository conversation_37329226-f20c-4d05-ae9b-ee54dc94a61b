package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ActTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;

import java.util.List;

public interface ChannelActivityService {

    ActivityResponse saveActivity(ActTypeEnum actType, BaseRequest baseRequest, List<ChannelActivityInfo> activityList);

    ActivityResponse batchDelete(BaseRequest baseRequest, List<ChannelPoiParamInfo> channelPoiParamInfoList);

    QueryActivityResponse queryActivityList(BaseRequest request);

    QuerySkuActivityInfoResponse querySkuActivityInfos(QuerySkuActivityInfoRequest request);

    QueryCanModifyPriceResponse querySpuCanModifyPrice(QueryCanModifyPriceRequest request, Integer count);
}
