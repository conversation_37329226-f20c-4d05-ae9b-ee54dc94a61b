package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR>
 * @date 2021/10/21 8:00 下午
 */
public class SgAppBuyOrderExtraActExtendMsg {

    private String app_food_code;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String app_medicine_code;
    private String sku_id;
    private String gifts_app_food_code;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String gifts_app_medicine_code;
    private String gifts_sku_id;
    private String gifts_name;
    private int gift_num;

    public String getApp_food_code() {
        return app_food_code;
    }

    public void setApp_food_code(String app_food_code) {
        this.app_food_code = app_food_code;
    }

    public String getApp_medicine_code() {
        return app_medicine_code;
    }

    public void setApp_medicine_code(String app_medicine_code) {
        this.app_medicine_code = app_medicine_code;
    }

    public String getSku_id() {
        return sku_id;
    }

    public void setSku_id(String sku_id) {
        this.sku_id = sku_id;
    }

    public String getGifts_app_food_code() {
        return gifts_app_food_code;
    }

    public void setGifts_app_food_code(String gifts_app_food_code) {
        this.gifts_app_food_code = gifts_app_food_code;
    }

    public String getGifts_app_medicine_code() {
        return gifts_app_medicine_code;
    }

    public void setGifts_app_medicine_code(String gifts_app_medicine_code) {
        this.gifts_app_medicine_code = gifts_app_medicine_code;
    }

    public String getGifts_sku_id() {
        return gifts_sku_id;
    }

    public void setGifts_sku_id(String gifts_sku_id) {
        this.gifts_sku_id = gifts_sku_id;
    }

    public String getGifts_name() {
        return gifts_name;
    }

    public void setGifts_name(String gifts_name) {
        this.gifts_name = gifts_name;
    }

    public int getGift_num() {
        return gift_num;
    }

    public void setGift_num(int gift_num) {
        this.gift_num = gift_num;
    }

    @Override
    public String toString() {
        return "SgAppBuyOrderExtraActExtendMsg{" +
                "app_food_code='" + app_food_code + '\'' +
                ", app_medicine_code='" + app_medicine_code + '\'' +
                ", sku_id='" + sku_id + '\'' +
                ", gifts_app_food_code='" + gifts_app_food_code + '\'' +
                ", gifts_app_medicine_code='" + gifts_app_medicine_code + '\'' +
                ", gifts_sku_id='" + gifts_sku_id + '\'' +
                ", gifts_name='" + gifts_name + '\'' +
                ", gift_num=" + gift_num +
                '}';
    }
}
