
package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelActivityService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ActTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultBuilderUtil;

import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: zhaolei12
 * @create: 2019-03-18 18:34
 */
@Slf4j
@Service("elmChannelActivityService")
public class ElmChannelActivityServiceImpl implements ChannelActivityService {

    @Override
    public ActivityResponse saveActivity(ActTypeEnum actType, BaseRequest baseRequest,
                                         List<ChannelActivityInfo> activityList) {
        List<Long> storeIdList = Lists.newArrayList();
        List<String> skuList = Lists.newArrayList();
        activityList.forEach(channelActivityInfo -> {
            storeIdList.add(channelActivityInfo.getStoreId());
            skuList.add(channelActivityInfo.getSkuId());
        });
        baseRequest.setStoreIdList(storeIdList);
        ActivityResponse activityResponse = ResultBuilderUtil.genActivityResponse(ResultGenerator.genSuccessResult());
        ResultBuilderUtil.activityErrorResultAnalysis(baseRequest, skuList, ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg(), activityResponse, false);
        return activityResponse;
    }

    @Override
    public ActivityResponse batchDelete(BaseRequest baseRequest, List<ChannelPoiParamInfo> channelPoiParamInfoList) {
        ActivityResponse activityResponse = ResultBuilderUtil.genActivityResponse(ResultGenerator.genSuccessResult());
        List<Long> storeIdList = Lists.newArrayList();
        channelPoiParamInfoList.forEach(channelPoiParamInfo -> storeIdList.add(channelPoiParamInfo.getStoreId()));
        baseRequest.setStoreIdList(storeIdList);
        channelPoiParamInfoList.forEach(channelPoiParamInfo -> channelPoiParamInfo.getChannelActivityIdList().forEach(actId -> {
            if (StringUtils.isBlank(actId)) {
                ResultBuilderUtil.activityDelErrorResultAnalysis(baseRequest, actId, null, ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg(), activityResponse, false);
            } else {
                ResultBuilderUtil.activityDelErrorResultAnalysis(baseRequest, actId, null, "活动编码不能为空", activityResponse, false);
            }
        }));

        return activityResponse;
    }

    @Override
    public QueryActivityResponse queryActivityList(BaseRequest request) {
        return new QueryActivityResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
    }

    @Override
    public QuerySkuActivityInfoResponse querySkuActivityInfos(QuerySkuActivityInfoRequest request) {
        return new QuerySkuActivityInfoResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
    }

    @Override
    public QueryCanModifyPriceResponse querySpuCanModifyPrice(QueryCanModifyPriceRequest request,Integer count) {
        log.warn("ele渠道未开通查询商品能否改价功能");
        return new QueryCanModifyPriceResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
    }
}
