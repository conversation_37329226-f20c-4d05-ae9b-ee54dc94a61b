package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.RecommendCategoryBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.RecommendCategoryBrandResponse;

/**
 * <AUTHOR>
 * @description 渠道类目品牌推荐服务
 * @since 2023/12/19 16:55
 */
public interface ChannelCategoryBrandService {
    /**
     * 获取推荐渠道类目与品牌
     *
     * @param req
     * @return
     */
    RecommendCategoryBrandResponse getRecommendCategoryBrand(RecommendCategoryBrandRequest req);
}
