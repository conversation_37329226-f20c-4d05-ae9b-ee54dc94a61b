package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzan;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseChannelPoiRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiAddressUpdateRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiDetailsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiIdsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.core.client.auth.Token;
import com.youzan.cloud.open.sdk.core.client.core.DefaultYZClient;
import com.youzan.cloud.open.sdk.core.oauth.model.OAuthToken;
import com.youzan.cloud.open.sdk.core.oauth.token.TokenParameter;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanShopChainDescendentOrganizationList;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanShopChainDescendentOrganizationListParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanShopChainDescendentOrganizationListResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 有赞渠道门店管理功能.
 *
 * @author: liwei101
 * @since: 2021/6/7 09:35
 */
@Service("yzChannelPoiService")
@Slf4j
public class YzChannelPoiService implements ChannelPoiService {

    public static final int RESULT_SUCCESS_CODE = 200;

    public static final int SHOP_QUERY_PAGE_SIZE = 50;

    @Autowired
    private DefaultYZClient yzClient;

    @Autowired
    private MtChannelGateService mtChannelGateService;

    @Override
    public GetPoiInfoResponse batchGetPoiInfo(BaseRequestSimple req) {
        Map<String, Object> sysParam = mtChannelGateService
                .getChannelSysParams(new BaseRequest().setChannelId(req.getChannelId()).setTenantId(req.getTenantId()));

        try {
            List<PoiInfo> authorizedShops = getAuthorizedShops(sysParam, null);
            return new GetPoiInfoResponse(ResultGenerator.genSuccessResult(), authorizedShops);
        }
        catch (ChannelBizException e) {
            return new GetPoiInfoResponse(ResultGenerator.genResult(ResultCode.FAIL, e.getMessage()), null);
        }
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIds(BaseRequestSimple req) {
        Map<String, Object> sysParam = mtChannelGateService
                .getChannelSysParams(new BaseRequest().setChannelId(req.getChannelId()).setTenantId(req.getTenantId()));

        try {
            List<PoiInfo> authorizedShops = getAuthorizedShops(sysParam, null);

            return new GetPoiIdsResponse(ResultGenerator.genSuccessResult(), Fun.map(authorizedShops, PoiInfo::getAppPoiCode));
        }
        catch (ChannelBizException e) {
            return new GetPoiIdsResponse(ResultGenerator.genResult(ResultCode.FAIL, e.getMessage()), null);
        }

    }

    @Override
    public GetPoiIdsResponse batchGetPoiIdsBySysParam(BatchGetPoiIdsRequest req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        return resp;
    }

    @Override
    public GetPoiInfoResponse batchGetPoiDetails(BatchGetPoiDetailsRequest req) {
        Map<String, Object> sysParam = mtChannelGateService
                .getChannelSysParams(new BaseRequest().setChannelId(req.getChannelId()).setTenantId(req.getTenantId()));

        try {
            List<PoiInfo> authorizedShops = getAuthorizedShops(sysParam, req.getStoreIds());

            return new GetPoiInfoResponse(ResultGenerator.genSuccessResult(), authorizedShops);
        }
        catch (ChannelBizException e) {
            return new GetPoiInfoResponse(ResultGenerator.genResult(ResultCode.FAIL, e.getMessage()), null);
        }
    }

    private List<PoiInfo> getAuthorizedShops(Map<String, Object> sysParam, List<String> shopIdList) {
        String tokenPlanText = getYouzanToken(sysParam);

        try {
            List<YouzanShopChainDescendentOrganizationListResult.YouzanShopChainDescendentOrganizationListResultData> yzShopDatas =
                    queryAllShops(new Token(tokenPlanText));

            List<PoiInfo> poiInfos = filterShop(Fun.map(yzShopDatas, this::convertToPoiInfo), shopIdList);

            String appKey = (String) sysParam.get(ProjectConstant.ELM_CLIENT_ID);
            poiInfos.forEach(p -> p.setAppKey(appKey));

            return poiInfos;
        }
        catch (SDKException e) {
            throw new ChannelBizException(e);
        }

    }

    private List<YouzanShopChainDescendentOrganizationListResult.YouzanShopChainDescendentOrganizationListResultData> queryAllShops(Token token) throws SDKException {

        List<YouzanShopChainDescendentOrganizationListResult.YouzanShopChainDescendentOrganizationListResultData> shopList = new ArrayList<>();

        YouzanShopChainDescendentOrganizationListResult result = yzClient.invoke(buildAuthorizedShopReq(1), token,
                YouzanShopChainDescendentOrganizationListResult.class);
        checkResultIsSuccess(result);
        shopList.addAll(result.getData());

        int totalSize = result.getTotal();
        int totalPage = (int) Math.ceil(BigDecimal.valueOf(totalSize).divide(BigDecimal.valueOf(SHOP_QUERY_PAGE_SIZE)).doubleValue());

        for (int i = 1; i < totalPage; i++) {
            result = yzClient.invoke(buildAuthorizedShopReq(i + 1), token,
                    YouzanShopChainDescendentOrganizationListResult.class);
            checkResultIsSuccess(result);
            shopList.addAll(result.getData());
        }

        return shopList;

    }

    private YouzanShopChainDescendentOrganizationList buildAuthorizedShopReq(int pageNum) {
        YouzanShopChainDescendentOrganizationList youzanShopChainDescendentOrganizationList = new YouzanShopChainDescendentOrganizationList();
        //创建参数对象,并设置参数
        YouzanShopChainDescendentOrganizationListParams youzanShopChainDescendentOrganizationListParams = new YouzanShopChainDescendentOrganizationListParams();
        youzanShopChainDescendentOrganizationListParams.setPageNum(pageNum);
        youzanShopChainDescendentOrganizationListParams.setPageSize(SHOP_QUERY_PAGE_SIZE);
        youzanShopChainDescendentOrganizationList.setAPIParams(youzanShopChainDescendentOrganizationListParams);


        return youzanShopChainDescendentOrganizationList;
    }

    private void checkResultIsSuccess(YouzanShopChainDescendentOrganizationListResult result) {
        if (result.getCode() != RESULT_SUCCESS_CODE) {
            throw new ChannelBizException(result.getMessage());
        }
    }

    private List<PoiInfo> filterShop(List<PoiInfo> poiInfoList, List<String> shopIdList) {
        if (CollectionUtils.isEmpty(poiInfoList) || CollectionUtils.isEmpty(shopIdList)) {
            return poiInfoList;
        }

        return Fun.filter(poiInfoList, p -> shopIdList.contains(p.getAppPoiCode()));

    }

    private PoiInfo convertToPoiInfo(YouzanShopChainDescendentOrganizationListResult.YouzanShopChainDescendentOrganizationListResultData yzShop) {
        PoiInfo poiInfo = new PoiInfo();
        poiInfo.setAppPoiCode(String.valueOf(yzShop.getKdtId()));
        poiInfo.setName(yzShop.getName());
        poiInfo.setIsOnline(1);
        poiInfo.setChannelPoiCode(String.valueOf(yzShop.getKdtId()));
        poiInfo.setShippingTime("00:00-23:59");
        poiInfo.setOpenLevel(1);

        return poiInfo;
    }

    private String getYouzanToken(Map<String, Object> sysParam) {
        String clientId = (String) sysParam.get(ProjectConstant.ELM_CLIENT_ID);
        String secret = (String) sysParam.get(ProjectConstant.SECRET);
        String grantId = (String) sysParam.get(ProjectConstant.YZ_GRANT_ID);

        try {
            TokenParameter tokenParameter = TokenParameter
                    .self()
                    .clientId(clientId)
                    .clientSecret(secret)
                    .grantId(grantId)
                    .build();
            OAuthToken token = yzClient.getOAuthToken(tokenParameter);
            return token.getAccessToken();
        }
        catch (SDKException e) {
            throw new ChannelBizException(e);
        }
    }

    /**
     * 获取根据渠道的开放平台门店Id，查询渠道的外卖门店Id
     *
     * @param request
     * @return
     */
    @Override
    public GetPoiIdsResponse getPoiInnerId(BatchGetPoiDetailsRequest request) {
        GetPoiIdsResponse response = new GetPoiIdsResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能"));
        return response;
    }

    /**
     * 门店开始营业
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiOpen(ChannelPoiIdRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 门店停止营业
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiClose(ChannelPoiIdRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 门店公告信息更新
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus promotionInfoUpdate(PoiPromotionInfoUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 营业时间更新
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus shippingTimeUpdate(PoiShippingTimeUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 使门店接受预订单
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus prebookStatusOpen(ChannelPoiIdRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 使门店不接受预订单
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus prebookStatusClose(ChannelPoiIdRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 更新门店的接受预定的日期范围
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus updatePrebookDays(PoiPrebookDaysUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 获取门店公告信息
     *
     * @param request
     * @return
     */
    @Override
    public GetPoiPromotionInfoResponse getPoiPromotionInfo(ChannelPoiIdRequest request) {
        GetPoiPromotionInfoResponse response = new GetPoiPromotionInfoResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能"));
        return response;
    }

    /**
     * 获取门店营业状态
     *
     * @param channelPoiIdRequest
     * @return
     */
    @Override
    public ChannelPoiStatusResponse getPoiStatus(ChannelPoiIdRequest channelPoiIdRequest) {
        ChannelPoiStatusResponse response = new ChannelPoiStatusResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能"));
        return response;
    }

    /**
     * 门店授权
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 门店解析授权
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiDeAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    @Override
    public ResultStatus poiHotlineUpdate(ChannelPoiHotlineRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    @Override
    public QueryPoiAuthDetailResponse queryPoiAuthDetailInfo(BaseChannelPoiRequestSimple request) {
        return new QueryPoiAuthDetailResponse().setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能"));
    }

    @Override
    public ResultStatus updatePoiAuthInfo(UpdatePoiAuthInfoRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能");
    }

    @Override
    public ResultStatus updatePoiAddress(PoiAddressUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    @Override
    public UpdateSaleafterAddressMessageResponse updateSaleafterAddressMessage(UpdateSaleafterAddressMessageRequest request) {
        return ResultGenerator.UpdateSafeAddressResultData(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }


}
