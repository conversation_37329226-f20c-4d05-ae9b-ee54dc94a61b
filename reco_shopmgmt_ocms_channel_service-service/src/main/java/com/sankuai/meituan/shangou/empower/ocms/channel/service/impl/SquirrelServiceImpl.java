package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.SquirrelService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;

/**
 * 缓存center类
 *
 * <AUTHOR>
 * @create 2019-01-02 上午11:44
 **/
@Service("squirrelService")
public class SquirrelServiceImpl implements SquirrelService {

    @Resource(name = "redisClient")
    private RedisStoreClient redisStoreClient;

    @Resource
    private CommonLogger log;

    @Override
    public boolean priceCheckLock(String skuId, Long ts) {
        return true;
    }

    @Override
    public boolean stockCheckLock(String skuId, Long ts)          {
        return true;
    }

    @Override
    public ResultData getDefaultPictureCache(long tenantId, int channelId) {
        StoreKey storeKey = new StoreKey(ProjectConstant.DEFAULT_PICTURE_CACHE, tenantId, channelId);
        ResultData resultData = null;
        try {
            String result = redisStoreClient.get(storeKey);
            resultData = JSON.parseObject(result, ResultData.class);
        } catch (Exception e) {
            log.warn("获取默认图片缓存失败， tenantId:{}, channelId:{}", tenantId, channelId, e);
            return null;
        } finally {
            log.info("获取默认图片缓存，tenantId:{}, channelId:{}, resultData:{}", tenantId, channelId, resultData);
        }
        return resultData;
    }

    @Override
    public void setDefaultPictureCache(long tenantId, int channelId, ResultData resultData) {
        StoreKey storeKey = new StoreKey(ProjectConstant.DEFAULT_PICTURE_CACHE, tenantId, channelId);
        try {
            redisStoreClient.set(storeKey, JSON.toJSONString(resultData));
        } catch (Exception e) {
            log.warn("设置默认图片缓存失败， tenantId:{}, channelId:{}", tenantId, channelId, e);
            return;
        } finally {
            log.info("设置默认图片缓存，tenantId:{}, channelId:{}, resultData:{}", tenantId, channelId, resultData);
        }
    }
}
