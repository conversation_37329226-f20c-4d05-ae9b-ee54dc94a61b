package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.TxdConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.PoiChannelAppThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.AppInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByTenantRequest;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.AlibabaWdkShopQueryRequest;
import com.taobao.api.response.AlibabaWdkShopQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;

/**
 * @program: reco_shopmgmt_ocms_channel_service_2
 * @description:
 * @author: jinyi
 * @create: 2024-05-20 20:23
 **/
@Slf4j
@Service("txdChannelPoiService")
public class TxdChannelPoiServiceImpl implements ChannelPoiService {

    @Autowired
    private PoiChannelAppThriftServiceProxy poiChannelAppThriftServiceProxy;

    @Resource
    private TxdConverterService txdConverterService;


    @Override
    public GetPoiInfoResponse batchGetPoiInfo(BaseRequestSimple req) {
        GetPoiInfoResponse response = new GetPoiInfoResponse();
        return response.setStatus(ResultGenerator.genFailResult("淘鲜达不支持该功能"));
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIds(BaseRequestSimple req) {
        GetPoiIdsResponse response = new GetPoiIdsResponse();
        return response.setStatus(ResultGenerator.genFailResult("淘鲜达不支持该功能"));
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIdsBySysParam(BatchGetPoiIdsRequest req) {
        GetPoiIdsResponse response = new GetPoiIdsResponse();
        return response.setStatus(ResultGenerator.genFailResult("淘鲜达不支持该功能"));
    }

    @Override
    public GetPoiInfoResponse batchGetPoiDetails(BatchGetPoiDetailsRequest request) {
        GetPoiInfoResponse resp = new GetPoiInfoResponse();
        long tenantId = request.getTenantId();
        List<String> shopIdList = request.getStoreIds();

        try {
            List<PoiInfo> data = getPoiInfoList(tenantId, shopIdList);
            resp.setStatus(ResultGenerator.genSuccessResult());
            resp.setPoiInfoList(data);
        } catch (InvokeChannelTooMuchException e) {
            log.info("调用渠道接口次数超过限制,返回TRIGGER_LIMIT(999)+等待时间 重试 。");
            return resp.setStatus(new ResultStatus(ResultCodeEnum.TRIGGER_LIMIT.getValue(), "",
                    String.valueOf(e.getWaitTimeMills())));
        } catch (Exception e) {
            log.error("调用渠道接口发生异常:", e);
            return resp.setStatus(
                    new ResultStatus(StatusCodeEnum.SYS_ERROR.getCode(), "", String.valueOf(e.getMessage())));
        }
        return resp;
    }


    private List<PoiInfo> getPoiInfoList(Long tenantId, List<String> shopIdList){
        List<PoiInfo> poiInfoList = new ArrayList<>();

        // 目前一个租户仅能接入一个淘鲜达商家，后期会考虑接入多个
        List<AppInfoDTO> appInfoDTOList = poiChannelAppThriftServiceProxy.queryAppInfoByTenant(new AppInfoQueryByTenantRequest(tenantId, Arrays.asList(
                ChannelTypeEnum.TXD.getCode())));

        Map<String, PoiInfo> poiInfoMap = new HashMap<>();
        
        // 因为无法确定门店是通过哪个淘鲜达商家接入的，需要遍历应用信息请求渠道
        for (AppInfoDTO appInfoDTO : appInfoDTOList){
            for (String shopId : shopIdList) {
                if (poiInfoMap.containsKey(shopId)){
                    continue;
                }
                PoiInfo poiInfo = getPoiInfo(appInfoDTO, shopId);
                if (poiInfo != null){
                    poiInfoMap.put(shopId, poiInfo);
                }
            }
        }

        poiInfoList = Fun.map(poiInfoMap.values(), Function.identity());
        return poiInfoList;
    }
    
    private PoiInfo getPoiInfo(AppInfoDTO appInfoDTO, String shopId){
        try {
            TaobaoClient client = new DefaultTaobaoClient(MccConfigUtil.getTxdHttpsUrl(), appInfoDTO.getAppKey(),
                    appInfoDTO.getSecret());
            AlibabaWdkShopQueryRequest req = new AlibabaWdkShopQueryRequest();
            req.setOuCode(shopId);
            AlibabaWdkShopQueryResponse rsp = client.execute(req, appInfoDTO.getAccessToken());
            if (Objects.isNull(rsp) || !rsp.isSuccess()) {
                log.error("getPoiInfo 失败, appInfoDTO:{}, shopId:{}", appInfoDTO, shopId);
                return null;
            }
            if (CollectionUtils.isEmpty(rsp.getResult().getModels())){
                log.info("alibaba.wdk.shop.query 查询门店不存在, appInfoDTO={}, shopId={}", appInfoDTO, shopId);
                return null;
            }
            AlibabaWdkShopQueryResponse.ShopDo shopDo = rsp.getResult().getModels().get(0);
            PoiInfo poiInfo = txdConverterService.poiInfoMapping(shopDo);
            poiInfo.setAppId(appInfoDTO.getQnhAppId());
            poiInfo.setAppKey(appInfoDTO.getAppKey());
            return poiInfo;
        } catch (Exception e) {
            log.error("getPoiInfo error, appInfoDTO:{}, shopId:{}", appInfoDTO, shopId, e);
            return null;
        }
    }
    

    @Override
    public GetPoiIdsResponse getPoiInnerId(BatchGetPoiDetailsRequest request) {
        GetPoiIdsResponse response = new GetPoiIdsResponse();
        return response.setStatus(ResultGenerator.genFailResult("淘鲜达不支持该功能"));
    }

    @Override
    public ResultStatus poiOpen(ChannelPoiIdRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public ResultStatus poiClose(ChannelPoiIdRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public ResultStatus promotionInfoUpdate(PoiPromotionInfoUpdateRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public ResultStatus shippingTimeUpdate(PoiShippingTimeUpdateRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public ResultStatus prebookStatusOpen(ChannelPoiIdRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public ResultStatus prebookStatusClose(ChannelPoiIdRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public ResultStatus updatePrebookDays(PoiPrebookDaysUpdateRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public GetPoiPromotionInfoResponse getPoiPromotionInfo(ChannelPoiIdRequest request) {
        GetPoiPromotionInfoResponse response = new GetPoiPromotionInfoResponse();
        return response.setStatus(ResultGenerator.genFailResult("淘鲜达不支持该功能"));
    }

    @Override
    public ChannelPoiStatusResponse getPoiStatus(ChannelPoiIdRequest channelPoiIdRequest) {
        ChannelPoiStatusResponse response= new ChannelPoiStatusResponse();
        List<PoiInfo> poiInfoList = getPoiInfoList(channelPoiIdRequest.getTenantId(), Collections.singletonList(channelPoiIdRequest.getChannelPoiCode()));
        if (CollectionUtils.isEmpty(poiInfoList)){
            return response.setStatus(ResultGenerator.genFailResult("门店不存在"));
        }
        PoiInfo poiInfo = poiInfoList.get(0);
        response.setStatus(ResultGenerator.genSuccessResult());
        response.setPoiStatus(poiInfo.getOpenLevel());
        return response;
    }

    @Override
    public ResultStatus poiAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public ResultStatus poiDeAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public ResultStatus updatePoiAddress(PoiAddressUpdateRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public UpdateSaleafterAddressMessageResponse updateSaleafterAddressMessage(UpdateSaleafterAddressMessageRequest request) {
        UpdateSaleafterAddressMessageResponse response = new UpdateSaleafterAddressMessageResponse();

        return response.setStatus(ResultGenerator.genFailResult("淘鲜达不支持该功能"));
    }

    @Override
    public ResultStatus poiHotlineUpdate(ChannelPoiHotlineRequest request) {
        return ResultGenerator.genFailResult("淘鲜达渠道不支持");
    }

    @Override
    public QueryPoiAuthDetailResponse queryPoiAuthDetailInfo(BaseChannelPoiRequestSimple request) {
        return new QueryPoiAuthDetailResponse().setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能"));
    }

    @Override
    public ResultStatus updatePoiAuthInfo(UpdatePoiAuthInfoRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能");
    }

    @Override
    public ChannelPoiCategoryResponse queryChannelPoiCategory(ChannelPoiCategoryRequest request) {
        return ChannelPoiService.super.queryChannelPoiCategory(request);
    }
}
