package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.RecommendBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.RecommendBrandResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelBrandService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetBrandResponse;
import org.springframework.stereotype.Service;

/**
 * @author: qiuweizhi
 * @date: 2023/2/21
 * @time: 3:41 PM
 */
@Service("yzToolChannelBrandService")
public class YzToolChannelBrandServiceImpl implements ChannelBrandService {
    
    @Override
    public GetBrandResponse batchGetBrand(GetBrandRequest req) {
        return new GetBrandResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public RecommendBrandResponse getRecommendBrand(RecommendBrandRequest req){
        RecommendBrandResponse recommendBrandResponse = new RecommendBrandResponse();
        recommendBrandResponse.setStatus(ChannelStatus.build(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(),ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg()));
        return recommendBrandResponse;
    }
}
