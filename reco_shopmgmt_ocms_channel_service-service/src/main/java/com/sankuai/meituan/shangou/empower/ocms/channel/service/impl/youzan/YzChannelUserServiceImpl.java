package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzan;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.YzChannelUserService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanUsersInfoQuery;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanUsersInfoQueryParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanUsersInfoQueryResult;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class YzChannelUserServiceImpl extends YouZanBaseService implements YzChannelUserService {


	@Autowired
	private MtChannelGateService mtChannelGateService;

	@Override
	public YouzanUsersInfoQueryResult queryYzUser(YouzanUsersInfoQueryParams request, Long tenantId) {

		try {
			YouzanUsersInfoQuery youzanUsersInfoQuery = new YouzanUsersInfoQuery();
			youzanUsersInfoQuery.setAPIParams(request);
			log.info("query youzan user detail, weChatOpenId: {}, tenantId: {}", request.getWeixinOpenId(), tenantId);
			YouzanUsersInfoQueryResult result = getResult4YouZanWithRetry(appMessage(tenantId), youzanUsersInfoQuery,
					YouzanUsersInfoQueryResult.class);
            log.info("get youzan user info: {}", JSON.toJSONString(result));
			return result;
		} catch (SDKException e) {
			log.error("search user detail error, request->{}, exception:", request, e);
			return null;
		} catch (Exception e) {
			return null;
		}
	}

	private AppMessage appMessage(long tenantId) throws SDKException {
		
		Map<String, Object> sysParam = mtChannelGateService.getChannelSysParams(new BaseRequest().setChannelId(ChannelTypeEnum.YOU_ZAN.getCode()).setTenantId(tenantId));
		String clientId = (String) sysParam.get(ProjectConstant.ELM_CLIENT_ID);
		String secret = (String) sysParam.get(ProjectConstant.SECRET);
		String grantId = (String) sysParam.get(ProjectConstant.YZ_GRANT_ID);
		return AppMessage
				.builder()
				.grantId(grantId)
				.clientId(clientId)
				.clientSecret(secret)
				.build();
	}
}
