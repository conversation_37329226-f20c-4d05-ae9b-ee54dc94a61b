package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.google.common.base.Joiner;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.BaseQualityRuleItemDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.CalculateSpuQualityDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.CalculateSpuQualityDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.FieldsDimensionScoreDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ProductSkuInfoDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ProductSpuScoreDetailDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelQualityService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.shangou.product.quality.enums.GetScoreTypeEnum;
import com.sankuai.shangou.product.quality.query.thrift.command.CalcSpuQualityDetailOnlineCommand;
import com.sankuai.shangou.product.quality.query.thrift.domain.BaseRuleItem;
import com.sankuai.shangou.product.quality.query.thrift.domain.CalcSpuQualityDto;
import com.sankuai.shangou.product.quality.query.thrift.domain.DimensionScoreDtl;
import com.sankuai.shangou.product.quality.query.thrift.domain.ProductProperty;
import com.sankuai.shangou.product.quality.query.thrift.domain.ProductSku;
import com.sankuai.shangou.product.quality.query.thrift.domain.ProductSpu;
import com.sankuai.shangou.product.quality.query.thrift.domain.RuleInterestDto;
import com.sankuai.shangou.product.quality.query.thrift.domain.SpuScoreDetail;
import com.sankuai.shangou.product.quality.query.thrift.result.CalcSpuQualityDetailOnlineResult;
import com.sankuai.shangou.product.quality.query.thrift.service.SpuQualityScoreThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/9/7
 */
@Service
@Slf4j
public class ChannelQualityServiceImpl implements ChannelQualityService {
    @Autowired
    private SpuQualityScoreThriftService.Iface spuQualityScoreThriftService;
    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Override
    public CalculateSpuQualityDetailResponse calcSpuQualityDetailInfo(CalculateSpuQualityDetailRequest request) {
        CalculateSpuQualityDetailResponse response;
        try {
            CalcSpuQualityDetailOnlineCommand command = convert2Command(request);
            CalcSpuQualityDetailOnlineResult result = spuQualityScoreThriftService.calcSpuQualityDetailOnline(command);
            log.info("查询商品质量分信息详情，command {}, result {}.", command, result);

            response = convert2Response(result);
        } catch (Exception e) {
            log.warn("查询商品质量分信息异常，request {}.", request, e);
            response = new CalculateSpuQualityDetailResponse();
            response.setCode(ResultCode.FAIL.getCode());
            response.setMsg("查询商品质量分信息失败，请稍后重试！");
        }

        return response;
    }

    private CalcSpuQualityDetailOnlineCommand convert2Command(CalculateSpuQualityDetailRequest request) {

        Set<String> scoreTypeList = new HashSet<>();
        ProductSpu productSpu = new ProductSpu();

        // 店铺ID
        if (Objects.nonNull(request.getStoreId())) {
            ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(request.getTenantId(), 100, request.getStoreId());
            if(Objects.nonNull(channelStore) && NumberUtils.isCreatable(channelStore.getChannelPoiCode())) {
                productSpu.setPoiId(Long.valueOf(channelStore.getChannelPoiCode()));
                scoreTypeList.add(GetScoreTypeEnum.SELLING_POINT.getType());
            }
        }

        // 商品编码
        Pair<Boolean, Long> channelSpuIdPair = convertStringToLong(request.getChannelSpuId());
        if (channelSpuIdPair.getLeft()) {
            productSpu.setSpuId(channelSpuIdPair.getRight());
        }

        // 商品名称
        if (StringUtils.isNotBlank(request.getSpuName())) {
            productSpu.setProductName(request.getSpuName());
            scoreTypeList.add(GetScoreTypeEnum.TITLE.getType());
        }

        // 分类，目前渠道分类只有末级分类可用，其父分类的id编码获取不到
        Pair<Boolean, Long> firstPair = convertStringToLong(request.getFirstChannelCategoryId());
        if (firstPair.getLeft()) {
            productSpu.setFirstCategoryId(firstPair.getRight());
            scoreTypeList.add(GetScoreTypeEnum.CATEGORY.getType());
        }
        Pair<Boolean, Long> secondPair = convertStringToLong(request.getSecondChannelCategoryId());
        if (secondPair.getLeft()) {
            productSpu.setSecondCategoryId(secondPair.getRight());
            scoreTypeList.add(GetScoreTypeEnum.CATEGORY.getType());
        }
        Pair<Boolean, Long> thirdPair = convertStringToLong(request.getThirdChannelCategoryId());
        if (thirdPair.getLeft()) {
            productSpu.setThirdCategoryId(thirdPair.getRight());
            scoreTypeList.add(GetScoreTypeEnum.CATEGORY.getType());
        }

        // 图片列表
        if (CollectionUtils.isNotEmpty(request.getImgUrlList())) {
            productSpu.setPictures(request.getImgUrlList());
            scoreTypeList.add(GetScoreTypeEnum.PIC.getType());
        }

        // 商品属性
        if (CollectionUtils.isNotEmpty(request.getPropertyItemDtoList())) {
            productSpu.setProperties(request.getPropertyItemDtoList().stream()
                    .map(dto -> {
                        ProductProperty productProperty = new ProductProperty();
                        productProperty.setPropertyId(dto.getPropertyId());
                        productProperty.setPropertyName(dto.getPropertyName());
                        if (Objects.nonNull(dto.getPropertyValueId())) {
                            productProperty.setPropertyValueId(dto.getPropertyValueId());
                        }
                        productProperty.setPropertyValueName(dto.getPropertyValueName());
                        productProperty.setPropertyValueExtend(JacksonUtils.toJson(dto.getStructPropertyList()));
                        return productProperty;
                    }).collect(Collectors.toList()));
            scoreTypeList.add(GetScoreTypeEnum.PROPERTY.getType());
        }

        // 品牌
        if (StringUtils.isNotBlank(request.getBrandName())) {
            productSpu.setBrandName(request.getBrandName());
        }

        // 卖点
        if (StringUtils.isNotBlank(request.getSellingPoint())) {
            productSpu.setSellPoint(request.getSellingPoint());
            scoreTypeList.add(GetScoreTypeEnum.SELLING_POINT.getType());
        }

        // 图片详情
        if (CollectionUtils.isNotEmpty(request.getPictureContentList())) {
            productSpu.setPicContent(Joiner.on(",").join(request.getPictureContentList()));
            scoreTypeList.add(GetScoreTypeEnum.PIC_CONTENT_URL.getType());
        }

        // 视频ID
        if (StringUtils.isNotBlank(request.getVideoId())) {
            productSpu.setVideo(request.getVideoId());
            scoreTypeList.add(GetScoreTypeEnum.VIDEO_DETAIL.getType());
        }

        // 规格列表
        if (CollectionUtils.isNotEmpty(request.getSkuInfoList())) {
            productSpu.setIsMultiSpec(request.getSkuInfoList().size() > 1);
            productSpu.setSkus(batchConvert2ProductSku(scoreTypeList, request.getSkuInfoList()));
        }

        CalcSpuQualityDto spuQualityDto = new CalcSpuQualityDto();
        spuQualityDto.setSpu(productSpu);
        if (BooleanUtils.isTrue(request.getCalculateAllFields())) {
            scoreTypeList.addAll(GetScoreTypeEnum.allSubTypeCodeList);
            scoreTypeList.add(GetScoreTypeEnum.TOTAL.getType());
        }
        spuQualityDto.setDimensionList(Lists.newArrayList(scoreTypeList));

        CalcSpuQualityDetailOnlineCommand command = new CalcSpuQualityDetailOnlineCommand();
        command.setSpuQualityDto(spuQualityDto);

        return command;
    }

    private Pair<Boolean, Long> convertStringToLong(String value) {
        // long的最大值9223372036854775807L为19位
        if (StringUtils.isBlank(value) || !NumberUtils.isCreatable(value) || value.length() > 19) {
            return Pair.of(Boolean.FALSE, null);
        }

        try {
            Long valueLong = Long.valueOf(value.trim());
            return Pair.of(Boolean.TRUE, valueLong);
        } catch (Exception e) {
            log.warn("当前参数值大于long的最大值，转换失败，value {}.", value, e);
        }

        return Pair.of(Boolean.FALSE, null);
    }

    private List<ProductSku> batchConvert2ProductSku(Set<String> scoreTypeList, List<ProductSkuInfoDto> skuInfoDtos) {
        List<ProductSku> productSkuList = new ArrayList<>();

        for (ProductSkuInfoDto skuInfoDto : skuInfoDtos) {
            ProductSku productSku = new ProductSku();
            Pair<Boolean, Long> productIdPair = convertStringToLong(skuInfoDto.getChannelSkuId());
            if (productIdPair.getLeft()) {
                productSku.setProductId(productIdPair.getRight());
            }

            // UPC
            if (StringUtils.isNotBlank(skuInfoDto.getUpc())) {
                productSku.setUpc(skuInfoDto.getUpc());
                scoreTypeList.add(GetScoreTypeEnum.UPC.getType());
            }

            // 规格名称
            if (StringUtils.isNotBlank(skuInfoDto.getSpec())) {
                productSku.setSpec(skuInfoDto.getSpec());
                scoreTypeList.add(GetScoreTypeEnum.SPEC.getType());
            }

            // 重量
            if (Objects.nonNull(skuInfoDto.getWeightForUnit())) {
                BigDecimal weight = BigDecimal.valueOf(skuInfoDto.getWeightForUnit());
                productSku.setWeight(weight.toString());
                scoreTypeList.add(GetScoreTypeEnum.WEIGHT.getType());
            }
            if (StringUtils.isNotBlank(skuInfoDto.getWeightUnit())) {
                productSku.setWeightUnit(skuInfoDto.getWeightUnit());
                scoreTypeList.add(GetScoreTypeEnum.WEIGHT.getType());
            }

            if (Objects.nonNull(skuInfoDto.getSeqNo())) {
                productSku.setSeqNo(skuInfoDto.getSeqNo());
            }
            productSku.setAllowUpcEmpty(BooleanUtils.isTrue(skuInfoDto.getAllowUpcEmpty()));

            productSkuList.add(productSku);
        }

        return productSkuList;
    }

    private CalculateSpuQualityDetailResponse convert2Response(CalcSpuQualityDetailOnlineResult result) {
        CalculateSpuQualityDetailResponse response = new CalculateSpuQualityDetailResponse();
        response.setCode(result.getStatus().getCode());
        response.setMsg(result.getStatus().getMsg());

        if (Objects.isNull(result.getSpuScoreDetail())) {
            return response;
        }

        SpuScoreDetail scoreDetail = result.getSpuScoreDetail();
        ProductSpuScoreDetailDto productSpuScoreDetailDto = new ProductSpuScoreDetailDto();
        productSpuScoreDetailDto.setSpuName(scoreDetail.getProductSpuName());
        if (scoreDetail.isSetQuality()) {
            productSpuScoreDetailDto.setQuality(scoreDetail.getQuality());
        }
        if (scoreDetail.isSetScore()) {
            productSpuScoreDetailDto.setScore(scoreDetail.getScore());
        }
        if (scoreDetail.isSetIsCategoryRuleHit()) {
            productSpuScoreDetailDto.setIsCategoryRuleHit(scoreDetail.getIsCategoryRuleHit());
        }
        productSpuScoreDetailDto.setTotalRuleInterestMap(Fun.toMap(scoreDetail.getTotalRuleInterestList(),
                RuleInterestDto::getCode, RuleInterestDto::getDesc));
        productSpuScoreDetailDto.setTitleScoreDto(convert2FieldsDimensionScoreDto(scoreDetail.getTitleScoreDtl()));
        productSpuScoreDetailDto.setImgUrlScoreDto(convert2FieldsDimensionScoreDto(scoreDetail.getPicScoreDtl()));
        productSpuScoreDetailDto.setChannelCategoryScoreDto(convert2FieldsDimensionScoreDto(scoreDetail.getCategoryScoreDtl()));
        productSpuScoreDetailDto.setPropScoreDto(convert2FieldsDimensionScoreDto(scoreDetail.getPropScoreDtl()));
        productSpuScoreDetailDto.setUpcScoreDto(convert2FieldsDimensionScoreDto(scoreDetail.getUpcScoreDtl()));
        productSpuScoreDetailDto.setWeightScoreDto(convert2FieldsDimensionScoreDto(scoreDetail.getWeightScoreDtl()));
        productSpuScoreDetailDto.setSpecScoreDto(convert2FieldsDimensionScoreDto(scoreDetail.getSpecScoreDtl()));
        productSpuScoreDetailDto.setSellingPointScoreDto(convert2FieldsDimensionScoreDto(scoreDetail.getSellingPointScoreDtl()));
        productSpuScoreDetailDto.setPictureContentScoreDto(convert2FieldsDimensionScoreDto(scoreDetail.getPicContentScoreDtl()));
        productSpuScoreDetailDto.setVideoScoreDto(convert2FieldsDimensionScoreDto(scoreDetail.getVideoScoreDtl()));

        response.setSpuScoreDetailDto(productSpuScoreDetailDto);
        return response;
    }

    private FieldsDimensionScoreDto convert2FieldsDimensionScoreDto(DimensionScoreDtl dtl) {
        if (Objects.isNull(dtl)) {
            return null;
        }

        FieldsDimensionScoreDto scoreDto = new FieldsDimensionScoreDto();
        if (dtl.isSetQuality()) {
            scoreDto.setQuality(dtl.getQuality());
        }
        if (dtl.isSetExtraScore()) {
            scoreDto.setExtraScore(dtl.getExtraScore());
        }

        if (MapUtils.isNotEmpty(dtl.getRuleItems())) {
            Map<String, List<BaseQualityRuleItemDto>> fieldsBaseRulesMap = new HashMap<>();
            dtl.getRuleItems().entrySet().forEach(entry -> {
                fieldsBaseRulesMap.put(String.valueOf(entry.getKey()), Fun.map(entry.getValue(), this::convert2BaseQualityRuleItemDto));
            });
            scoreDto.setFieldsBaseRulesMap(fieldsBaseRulesMap);
        }

        if (MapUtils.isNotEmpty(dtl.getExtra())) {
            scoreDto.setExtraMap(dtl.getExtra());
        }

        scoreDto.setRuleInterestMap(Fun.toMap(dtl.getRuleInterestList(), RuleInterestDto::getCode, RuleInterestDto::getDesc));
        return scoreDto;
    }

    private BaseQualityRuleItemDto convert2BaseQualityRuleItemDto(BaseRuleItem item) {
        BaseQualityRuleItemDto baseQualityRuleItemDto = new BaseQualityRuleItemDto();
        baseQualityRuleItemDto.setRuleId(item.getItemId());
        baseQualityRuleItemDto.setItemType(item.getItemType());
        baseQualityRuleItemDto.setRuleName(item.getItemName());
        baseQualityRuleItemDto.setResult(item.getResult());
        baseQualityRuleItemDto.setIsDefault(item.getIsDefault());
        if (item.isSetItemAddScore()) {
            baseQualityRuleItemDto.setItemAddScore(item.getItemAddScore());
        }
        baseQualityRuleItemDto.setProblemDesc(item.getProblemDesc());

        return baseQualityRuleItemDto;
    }
}
