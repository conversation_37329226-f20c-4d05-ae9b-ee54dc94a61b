package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.CommonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ElmSignUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
* @author: lijunbo03
* @Date: 2019/8/6
*/
@Service("elmChannelCommonService")
public class ElmChannelCommonServiceImpl implements ChannelCommonService {

    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private CommonLogger log;

    @Override
    public ResultStatus auth(String url, Map<String, String> params, String requestIp, String originalUrl) {
        String tenantAppId = params.get("source");
        if (Objects.isNull(tenantAppId)) {
            log.error("饿百验签，未获取到 source 入参");
            return ResultGenerator.genFailResult("未获取到 source 入参").setData("参数 source 为空");
        }
        CopAccessConfigDO copAccessConfigDO = copAccessConfigService.selectByTenantAppIdAndChannelId(tenantAppId, ChannelTypeEnum.ELEM.getCode());
        if (Objects.isNull(copAccessConfigDO) || Objects.isNull(copAccessConfigDO.getTenantId())) {
            log.error("饿百验签，未获取到租户配置：tenantAppId: [{}], channelId: [{}]", tenantAppId, ChannelTypeEnum.ELEM.getCode());
            return ResultGenerator.genFailResult("未获取到租户配置").setData("配置为空");
        }
        Long tenantId = copAccessConfigDO.getTenantId();

        // 白名单
        String elmWhiteList = MccConfigUtil.getElmWhiteList();
        if (StringUtils.isNotBlank(elmWhiteList)) {
            String[] tenantIdStrings = StringUtils.split(elmWhiteList, ',');
            for (String tenantIdString : tenantIdStrings) {
                Long configTenantId = Long.valueOf(tenantIdString.trim());
                if (tenantId.equals(configTenantId)) {
                    return ResultGenerator.genSuccessResult().setData(String.valueOf(tenantId));
                }
            }
        }

        String secret = getSecret(copAccessConfigDO);
        if (StringUtils.isBlank(secret)) {
            log.warn("饿百验签，饿百租户 tenantId:{}, secret为空", tenantId);
            return ResultGenerator.genFailResult("未获取到secret").setData(String.valueOf(tenantId));
        }
        params.put("secret", secret);
        String requestSign = params.remove("sign");
        String sign = ElmSignUtils.getSign(new HashMap<>(params));
        log.info("饿百验签，租户ID:{}, 开放平台sign:{}, 渠道网关sign:{}", tenantId, requestSign, sign);
        if (!sign.equalsIgnoreCase(requestSign)) {
            log.warn("饿百验签失败params:{}", params);
            if (!decodeOneTime(params, tenantId, requestSign)) {
                log.warn("饿百验签失败params:{}", params);
                return ResultGenerator.genFailResult("验签失败").setData(String.valueOf(tenantId));
            }
        }
        return ResultGenerator.genSuccessResult().setData(String.valueOf(tenantId));
    }

    private boolean decodeOneTime(Map<String, String> params, Long tenantId, String requestSign) {
        String sign = "";
        try {
            for (String key : params.keySet()) {
                String decodeValue = CommonUtils.decode(params.get(key));
                params.put(key, decodeValue);
            }
            sign = ElmSignUtils.getSign(new HashMap<>(params));
        } catch (Exception e) {
            log.warn("ElmAuth.CommonUtils.decode异常:", e);
        }
        log.info("饿百解码验签，租户ID:{}, 开放平台sign:{}, 渠道网关sign:{}", tenantId, requestSign, sign);
        return sign.equalsIgnoreCase(requestSign);
    }

    private String getSecret(CopAccessConfigDO copAccessConfigDO) {
        String config = copAccessConfigDO.getSysParams();
        return JSON.parseObject(config).getString("secret");
    }
}
