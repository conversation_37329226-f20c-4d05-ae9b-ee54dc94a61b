package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.squirrel.client.StoreKey;
import com.doudian.open.api.token.AccessTokenData;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.service.inf.kms.client.Kms;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.PoiCodeAppIdDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtAuthInfoRespDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtTokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.txd.TxdTokenResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.TokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.ChannelStoreBaseInfoMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DxPushMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelAuthorizeCallbackService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin.DouyinAccessTokenService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm.ElmChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj.JddjChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd.TxdAccessTokenService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool.YzToolAccessTokenService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool.YzToolChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.AuthCallbackThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.PoiChannelAppThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.EnumUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.AppInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByAppIdRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.auth.SetAccessConfigDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.auth.request.SetAccessConfigRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhangbo86
 * @Date: 2020/08/20 19:43
 * @Description: 根据从饿了么返回的临时令牌code，获取访问令牌及时长令牌，保存到db
 */
@Slf4j
@Service("ChannelAuthorizeCallbackServiceImpl")
public class ChannelAuthorizeCallbackServiceImpl implements ChannelAuthorizeCallbackService {

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Autowired
    private JddjChannelGateService jddjChannelGateService;

    @Autowired
    private YzToolAccessTokenService yzToolAccessTokenService;

    @Resource
    private ChannelStoreBaseInfoMapper channelStoreBaseInfoMapper;

    @Autowired
    private YzToolChannelPoiService yzToolChannelPoiService;

    @Autowired
    private DouyinAccessTokenService douyinAccessTokenService;

    @Autowired
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Autowired
    private TxdAccessTokenService txdAccessTokenService;
    @Autowired
    private DxPushMessageProducer dxPushMessageProducer;
    
    @Autowired
    private AuthCallbackThriftServiceProxy authCallbackThriftServiceProxy;

    @Autowired
    private PoiChannelAppThriftServiceProxy poiChannelAppThriftServiceProxy;

    private final int elmChannelCode = 200;

    private final int changeCount = 1;

    private final String authorization_code = "authorization_code";

    private final String need_refresh_token = "true";

    @Value("${jddj.url.base}" + "${jddj.url.verificationUpdateToken}")
    private String verificationUpdateToken;

    /**
     * 获取饿了么开放平台访问令牌
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus getAuthorizeToken(GetElmAccessTokenRequest request) {
        //参数已经在downstream中校验过
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        if (Objects.isNull(channelTypeEnum) || channelTypeEnum.getCode() != elmChannelCode) {
            log.error("ChannelAuthorizeCallbackServiceImpl.getAuthorizeToken, 未知渠道, request:{}", request);
            return new ResultStatus().setCode(ResultCode.CHANNEL_CODE_INVALID.getCode()).setMsg(ResultCode.CHANNEL_CODE_INVALID.getMsg());
        }

        //根据tenantAppId和channelCode获取系统参数
        String sysParamsJson = copAccessConfigService.selectSysParams(request.getTenantAppId(), channelTypeEnum.getCode());
        Map<String, Object> sysParam = JSON.parseObject(sysParamsJson);
        if (StringUtils.isEmpty(sysParamsJson) || !sysParam.containsKey(ProjectConstant.ELM_SOURCE) || !sysParam.containsKey(ProjectConstant.SECRET)) {
            log.error("ChannelAuthorizeCallbackServiceImpl.getAuthorizeToken, 系统参数获取失败, sysParams:{}", sysParamsJson);
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg()).setData("系统参数获取失败");
        }
        log.info("ChannelAuthorizeCallbackServiceImpl.getAuthorizeToken 获取系统参数, sysParam:{}", sysParamsJson);

        String appId = String.valueOf(sysParam.get(ProjectConstant.ELM_SOURCE));
        String secret = String.valueOf(sysParam.get(ProjectConstant.SECRET));

        //向elm开放平台请求令牌等参数
        Map params = new HashMap();
        params.put(ProjectConstant.ELM_GRANT_TYPE, authorization_code);
        params.put(ProjectConstant.ELM_NEED_REFRESH_TOKEN, need_refresh_token);
        params.put(ProjectConstant.ELM_CLIENT_ID, appId);
        params.put(ProjectConstant.ELM_CLIENT_SECRET, secret);
        params.put(ProjectConstant.ELM_CODE, request.getCode());
        Map getAuthorizeDetail = elmChannelGateService.sendPost(params);

        if (Objects.isNull(getAuthorizeDetail)) {
            log.error("ChannelAuthorizeCallbackServiceImpl.getAuthorizeToken 饿了么平台令牌获取失败, tenantAppId:{}, channelId:{}", request.getTenantAppId(), channelTypeEnum.getCode());
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg()).setData("饿了么平台令牌获取失败");
        }
        log.info("ChannelAuthorizeCallbackServiceImpl.getAuthorizeToken, AuthorizeDetail:{}", getAuthorizeDetail);

        //令牌结果保存到db
        sysParam.putAll(getAuthorizeDetail);

        String authorize = JSON.toJSONString(sysParam);
        log.info("ChannelAuthorizeCallbackServiceImpl.getAuthorizeToken, authorize:{}", authorize);
        CopAccessConfigDO copAccessConfigDO = getCopAccessConfigDO(request.getTenantAppId(), channelTypeEnum.getCode(), authorize);
        int result = copAccessConfigService.updateSysParams(copAccessConfigDO);
        if (result == changeCount) {
            return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg()).setData(request.getTenantAppId() + channelTypeEnum.getCode());
        } else {
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg()).setData(request.getTenantAppId() + channelTypeEnum.getCode());
        }
    }

    @Override
    public ResultStatus jddjToken(JddjAccessTokenRequest request) {
        log.info("开始处理京东回调: {}", JSON.toJSONString(request));
        sendDXMessage(request);
        ResultStatus resultStatus = new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg()).setData(request.getTenantAppId());
        //根据tenantAppId和channelCode获取系统参数
        String sysParamsJson = copAccessConfigService.selectSysParams(request.getTenantAppId(),
                ChannelTypeEnum.JD2HOME.getCode());
        if (StringUtils.isEmpty(sysParamsJson)) {
            return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg()).setData(request.getTenantAppId());
        }
        Map<String, Object> sysParam = JSON.parseObject(sysParamsJson);
        String oldToken = (String) sysParam.get("token");
        String token = request.getToken();
        try {
            token = URLDecoder.decode(token, "UTF-8");
            JSONObject jsonObject = JSONObject.parseObject(token);
            sysParam.put("token", jsonObject.getString("token"));
        } catch (Exception e) {
            log.error("jddj token失败", e);
        }
        String newToken = (String) sysParam.get("token");
        HashMap<String, Object> bizParam = new HashMap<>();
        bizParam.put("oldToken", oldToken);
        bizParam.put("newToken", newToken);
        bizParam.put("appKey", request.getTenantAppId());
        CopAccessConfigDO copAccessConfigDODB = copAccessConfigService.selectByTenantAppIdAndChannelId(request.getTenantAppId(), ChannelTypeEnum.JD2HOME.getCode());
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setTenantId(copAccessConfigDODB.getTenantId());
        baseRequest.setChannelId(copAccessConfigDODB.getChannelId());
        baseRequest.setAppId(copAccessConfigDODB.getAppId());

        Map<String ,Object> systemParam = JSON.parseObject(copAccessConfigDODB.getSysParams());
        systemParam.put("token",newToken); // 使用新token来调用接口

        Map resultMap = jddjChannelGateService.sendPost(verificationUpdateToken, null, baseRequest, bizParam,systemParam);
        if (Integer.parseInt((String) resultMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
            log.error("token确认失败", resultMap);
            dxPushMessageProducer.pushPoiBindMessage(baseRequest.getTenantId(),request.getTenantAppId(),baseRequest.getChannelId(),null,"京东Token确认失败");
            return ResultGenerator.genResult(ResultCode.FAIL, "token确认失败");
        }

        if (resultMap.containsKey("data") && resultMap.get("data") != null) {
           Map<String ,Object> data = JSON.parseObject(resultMap.get("data").toString());
           Object code =  data.get("code");
            if (code != null && (Integer.parseInt(code.toString())) != 0) {
                log.error("token确认返回失败", resultMap);
                dxPushMessageProducer.pushPoiBindMessage(baseRequest.getTenantId(),request.getTenantAppId(),baseRequest.getChannelId(),null,"京东Token确认失败");
                return ResultGenerator.genResult(ResultCode.FAIL, "token确认失败");
            }
        }

        String authorize = JSON.toJSONString(sysParam);
        CopAccessConfigDO copAccessConfigDO = getCopAccessConfigDO(request.getTenantAppId(), ChannelTypeEnum.JD2HOME.getCode(), authorize);
        copAccessConfigService.updateSysParams(copAccessConfigDO);
        dxPushMessageProducer.pushPoiBindMessage(baseRequest.getTenantId(),request.getTenantAppId(),baseRequest.getChannelId(),null,"京东Token更新成功");
        return resultStatus;
    }

    private void sendDXMessage(JddjAccessTokenRequest request) {
        try {
            if (MccConfigUtil.getSendJDTokenDxMessage()) {
                String token = request.getToken();
                token = URLDecoder.decode(token, "UTF-8");
                JSONObject jsonObject = JSONObject.parseObject(token);
                String uid =  Optional.ofNullable(jsonObject.getString("uid")).orElse("");
                String tokenString =  Optional.ofNullable(jsonObject.getString("token")).orElse("");
                String venderId = Optional.ofNullable(jsonObject.getString("venderId"))
                        .orElse(jsonObject.getString("user_nick"));
                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dateStr = sdf.format(date);
                String content = "京东渠道推送Token\n"+
                        "【appKey】：" + request.getTenantAppId() + "\n" +
                        "【token】：" + tokenString + "\n" +
                        "【开发者账号】：" + uid + "\n" +
                        "【商家Id】：" + venderId + "\n"+
                        "【授权时间】：" + dateStr + "\n";
                dxPushMessageProducer.pushJDTokenMessage(content);
            }
        }catch (Exception e){
            log.error("ChannelAuthorizeCallbackServiceImpl.jddjToken：京东Token处理完成，推送大象消息失败！");
        }

    }

    @Override
    public ResultStatus yzToken(YzAccessCodeRequest request) {
        String sysParamsJson = copAccessConfigService.selectSysParams(request.getTenantAppId(),
                ChannelTypeEnum.YOU_ZAN.getCode());
        if (StringUtils.isEmpty(sysParamsJson)) {
            return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg()).setData(request.getTenantAppId());
        }
        Map<String, Object> sysParam = JSON.parseObject(sysParamsJson);
        String clientId = (String) sysParam.get(ProjectConstant.ELM_CLIENT_ID);
        String secret = (String) sysParam.get(ProjectConstant.SECRET);
        AppMessage appMessage = new AppMessage();
        appMessage.setClientId(clientId);
        appMessage.setClientSecret(secret);
        appMessage.setCode(request.getCode());
        yzToolAccessTokenService.getAccessToken4Yz(appMessage);
        return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
    }

    @Override
    public ResultStatus douyinToken(DouyinAccessCodeRequest request) {

        String sysParamsJson = copAccessConfigService.selectSysParams(request.getAppkey(),
                ChannelTypeEnum.DOU_YIN.getCode());

        if (StringUtils.isEmpty(sysParamsJson)) {
            log.info("douyin auth callback，查询cop配置信息不存在，appKey={}, channelId={}", request.getAppkey(),
                    ChannelTypeEnum.DOU_YIN.getCode());
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg("appKey对应的应用不存在");
        }

        Map<String, Object> sysParam = JSON.parseObject(sysParamsJson);
        String appKey = (String)sysParam.get(ProjectConstant.DOUYIN_APP_KEY);
        String secret = (String)sysParam.get(ProjectConstant.DOUYIN_SECRET);
        ResultStatus resultStatus = douyinAccessTokenService.authCallBack(appKey, secret, request.getAuthCode());

        return resultStatus;
    }

    @Override
    public ResultStatus deleteDouyinToken(DeleteDouyinTokenRequest request) {
        if (Objects.isNull(request) || StringUtils.isBlank(request.getAppkey()) || StringUtils.isBlank(request.getShopId())) {
            log.info("deleteDouyinToken 参数错误，request={}", request);
            throw new IllegalArgumentException("参数错误");
        }

        douyinAccessTokenService.deleteTokenFromRedis(request.getAppkey(), request.getShopId());

        return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
    }

    @Override
    public TxdTokenInfoDTO txdToken(TxdTokenRequest request) {
        TxdTokenResult tokenResult = txdAccessTokenService.createAccessToken(request.getAppKey(), request.getSecret(), request.getCode());

        return tokenResult.convertToTxdTokenInfoDTO();
    }

    /**
     * 直接设置有赞token --- 迁移工具用
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus yzTokenSet(YzTokenSetRequest request) {
        // 获取 AppMessage
        AppMessage appMessage = null;
        if (request.getTenantId() > 0) {
            if (request.getType() == 2) {
                appMessage = yzToolChannelPoiService.getSubAppMessage(request.getTenantId(), request.getPoiId());
            } else {
                appMessage = yzToolChannelPoiService.getMainAppMessage(request.getTenantId());
            }
        } else if (StringUtils.isNotBlank(request.getClientId())) {
            String sysParamsJson = copAccessConfigService.selectSysParams(request.getClientId(),
                    ChannelTypeEnum.YOU_ZAN.getCode());
            if (StringUtils.isEmpty(sysParamsJson)) {
                return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg("clientId对应的应用不存在");
            }
            Map<String, Object> sysParam = JSON.parseObject(sysParamsJson);
            String clientId = (String) sysParam.get(ProjectConstant.ELM_CLIENT_ID);
            String secret = (String) sysParam.get(ProjectConstant.SECRET);
            appMessage = AppMessage.builder()
                    .clientId(clientId)
                    .grantId(request.getGrantId())
                    .clientSecret(secret)
                    .build();
            log.info("未绑定渠道门店则直接设置, 参数：{}", JSON.toJSONString(request));
        }
        if (appMessage == null) {
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg("获取AppMessage错误");
        }

        TokenMessage message = new TokenMessage();
        message.setToken(request.getToken());
        message.setRefreshToken(request.getRefreshToken());
        message.setExpires(request.getExpires());
        message.setTimestamp(request.getTimestamp());

        yzToolAccessTokenService.setToken2Redis(appMessage, message);
        return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
    }

    /**
     * 直接设置抖音token --- 迁移工具用
     */
    @Override
    public ResultStatus dyTokenSet(YzTokenSetRequest request) {
        log.info("开始处理抖音的token转发: {}", JSONObject.toJSONString(request));
        String shopId = request.getGrantId();
        String appKey = request.getClientId();
        
        // token转发到poi-channel
        sendTokenToPoiChannel(request, shopId, appKey);

        // 根据shop_id + appKey查询租户id，并将租户信息放到redis中
        if (StringUtils.isNotBlank(shopId)) {
            CopAccessConfigDO copAccessConfigDO = copAccessConfigService.selectDouyinByTenantAppIdAndShopId(appKey, shopId);
            if (copAccessConfigDO == null) {
                return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg("shopId对应的应用不存在");
            }
            AccessTokenData tokenData = new AccessTokenData();
            tokenData.setAccessToken(request.getToken());
            tokenData.setRefreshToken(request.getRefreshToken());
            tokenData.setShopId(shopId);
            tokenData.setExpiresIn(request.getExpires());
            douyinAccessTokenService.setToken2Redis(appKey, tokenData, copAccessConfigDO.getTenantId());
            // 发送mafka消息
            douyinAccessTokenService.sendAuthCallbackMsg(copAccessConfigDO);
        } else {
            // todo: 后续需要对接抖音物流应用，则需要在这里补充token设置逻辑；除shopId外，其他参数都存在
            log.info("抖音物流应用的token转发");
        }
        return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
    }

    /**
     * token转发到poi-channel
     * @param request
     * @param shopId
     * @param appKey
     */
    private void sendTokenToPoiChannel(YzTokenSetRequest request, String shopId, String appKey) {
        try{
            String appKeySecretJson = Kms.getByNameWithoutErrorLog(ProjectConstant.OCMS_APP_KEY, ProjectConstant.DOUYIN_APPKEY_SECRET_KMS_KEY);
            if (org.apache.commons.lang.StringUtils.isBlank(appKeySecretJson)){
                log.info("抖音渠道的appKey、secret未在kms配置");
            }
            Map<String,String>  appKeySecretMap =  JacksonUtils.parseMap(appKeySecretJson, String.class, String.class);
            String kmsSecret = appKeySecretMap.get(appKey);
            Long now = System.currentTimeMillis();
            SetAccessConfigDTO.Token token = SetAccessConfigDTO.Token.builder()
                    .accessToken(request.getToken())
                    .refreshToken(request.getRefreshToken())
                    .accessTokenGrantDate(now)
                    .accessTokenExpireDate(now + request.getExpires() * 1000)
                    .refreshTokenGrantDate(now)
                    .build();
            SetAccessConfigDTO setAccessConfigDTO = SetAccessConfigDTO.builder()
                    .channelId(request.getChannelId())
                    .channelType(1)
                    .appKey(appKey)
                    .appName(appKey)
                    .secret(kmsSecret)
                    .subAppKey(shopId)
                    .token(token)
                    .type(2)
                    .build();

            authCallbackThriftServiceProxy.setAccessConfig(new SetAccessConfigRequest(setAccessConfigDTO));
        }catch (Exception e){
            log.error("抖音token转发到poi-channel失败", e);
        }
    }

    //赋值
    private CopAccessConfigDO getCopAccessConfigDO(String tenantAppId, Integer channelId, String authorize) {
        CopAccessConfigDO copAccessConfigDO = new CopAccessConfigDO();
        copAccessConfigDO.setTenantAppId(tenantAppId);
        copAccessConfigDO.setChannelId(channelId);
        copAccessConfigDO.setSysParams(authorize);
        return copAccessConfigDO;
    }

    @Override
    public List<TokenInfo> getToken(TokenGetRequest request) {
        long tenantId = request.getTenantId();
        int channelId = request.getChannelId();
        List<TokenInfo> tokenList = new ArrayList<>();
        if (channelId == ChannelTypeEnum.MEITUAN.getCode()) {
            BaseRequest baseRequest = new BaseRequest();
            baseRequest.setChannelId(channelId);
            baseRequest.setTenantId(tenantId);
            baseRequest.setAppId(request.getAppId());

            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            for (String channelPoiCode: request.getChannelPoiCodes()) {
                try {
                    // 美团isv因为是新建的应用，所以获取token时，可以自动刷新
                    MtTokenMessage mtTokenMessage = mtBrandChannelGateService.getMtIsvAccessToken(tenantId, channelPoiCode, baseRequest, sysParam);
                    if (mtTokenMessage != null) {
                        TokenInfo tokenInfo = new TokenInfo();
                        tokenInfo.setChannelPoiCode(channelPoiCode);
                        tokenInfo.setToken(mtTokenMessage.getAccessToken());
                        tokenInfo.setRefreshToken(mtTokenMessage.getRefreshToken());
                        tokenInfo.setExpires(mtTokenMessage.getExpires());
                        tokenInfo.setRefreshExpires(mtTokenMessage.getRefreshExpires());
                        tokenList.add(tokenInfo);
                    }
                } catch (Exception e) {
                    log.warn("获取美团token异常，channelPoiCode:{}, baseRequest:{}", channelPoiCode, baseRequest);
                }
            }
        } else if (channelId == ChannelTypeEnum.YOU_ZAN.getCode()) {
            CopAccessConfigDO tenantChannelConfig = copAccessConfigService.findTenantChannelConfig(tenantId, channelId);
            if (tenantChannelConfig == null) {
                throw new BizException(ResultCode.CHANNEL_APP_ID_INVALID.getCode(), "租户未绑定有赞应用");
            }
            String sysParams = tenantChannelConfig.getSysParams();
            Map<String, String> sysParamsMap = JacksonUtils.parseMap(sysParams, String.class, String.class);
            String yzGrantId = sysParamsMap.get("grant_id"); // 有赞主店
            List<String> channelPoiCodes = new ArrayList<>(request.getChannelPoiCodes());
            channelPoiCodes.add(yzGrantId);
            long millisecondsIn28Days = 28 * 24 * 60 * 60 * 1000L;
            for (String channelPoiCode: channelPoiCodes) {
                try {
                    AppMessage appMessage = AppMessage.builder()
                            .clientId(tenantChannelConfig.getTenantAppId())
                            .grantId(channelPoiCode)
                            .build();
                    // 有赞只获取缓存中的token，不自动刷新，否则会与老系统的刷新机制冲突
                    // 老系统会自动刷新该token
                    TokenMessage tokenMessage = yzToolAccessTokenService.getToken4Redis(appMessage);
                    // redis查询结果为空
                    if (tokenMessage != null && tokenMessage != YzToolAccessTokenService.UN_VALID) {
                        TokenInfo tokenInfo = new TokenInfo();
                        tokenInfo.setChannelPoiCode(channelPoiCode);
                        tokenInfo.setToken(tokenMessage.getToken());
                        tokenInfo.setRefreshToken(tokenMessage.getRefreshToken());
                        tokenInfo.setExpires(tokenMessage.getExpires());
                        tokenInfo.setRefreshExpires(tokenMessage.getTimestamp() + millisecondsIn28Days);
                        tokenList.add(tokenInfo);
                    }
                } catch (Exception e) {
                    log.warn("获取有赞token异常，channelPoiCode:{}", channelPoiCode);
                }
            }
        } else if (channelId == ChannelTypeEnum.DOU_YIN.getCode()) {
            CopAccessConfigDO tenantChannelConfig = copAccessConfigService.findTenantChannelConfig(tenantId, channelId);
            if (tenantChannelConfig == null) {
                throw new BizException(ResultCode.CHANNEL_APP_ID_INVALID.getCode(), "租户未绑定抖音应用");
            }
            try {
                String sysParams = tenantChannelConfig.getSysParams();
                Map<String, String> sysParamsMap = JacksonUtils.parseMap(sysParams, String.class, String.class);
                String shopId = sysParamsMap.get("shop_id");
                String appKey = sysParamsMap.get("app_key");
                // 抖音只获取缓存中的token，不自动刷新，否则会与老系统的刷新机制冲突
                // 老系统会自动刷新该token
                DouyinTokenMessage tokenMessage = douyinAccessTokenService.getTokenFromRedis(appKey, shopId);
                // redis查询结果为空
                TokenInfo tokenInfo = new TokenInfo();
                tokenInfo.setChannelPoiCode(shopId);
                tokenInfo.setToken(tokenMessage.getAccessToken());
                tokenInfo.setRefreshToken(tokenMessage.getRefreshToken());
                tokenInfo.setExpires(tokenMessage.getAccessTokenExpireDate().getTime());
                tokenInfo.setRefreshExpires(DateUtils.addDays(tokenMessage.getRefreshTokenGrantDate(), 28).getTime());
                tokenList.add(tokenInfo);
            } catch (Exception e) {
                log.warn("获取抖音token异常，tenantId:{}", tenantId);
            }
        }
        return tokenList;
    }

    @Override
    public String refreshToken(TokenGetRequest request) {
        long tenantId = request.getTenantId();
        int channelId = request.getChannelId();
        if (channelId == ChannelTypeEnum.MEITUAN.getCode()) {
            List<PoiCodeAppIdDO> poiCodeAppIdDOS = channelStoreBaseInfoMapper.selectAllByPoiCode(tenantId, channelId, request.getChannelPoiCodes());
            // 刷新指定对应的appid是多少，一个租户可能会同时存在品牌和isv应用
            poiCodeAppIdDOS = poiCodeAppIdDOS.stream().filter(p -> Objects.equals(p.getAppId(), request.getAppId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(request.getChannelPoiCodes())) {
                poiCodeAppIdDOS = poiCodeAppIdDOS.stream().filter(p -> request.getChannelPoiCodes().contains(p.getPoiCode())).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(poiCodeAppIdDOS)) {
                log.info("该租户和应用下没有对应的美团渠道门店，request:{}", request);
                return String.format("租户:%s, 应用:%s 下没有对应的美团渠道门店", tenantId, request.getAppId());
            }

            BaseRequest baseRequest = new BaseRequest();
            baseRequest.setChannelId(channelId);
            baseRequest.setTenantId(tenantId);
            baseRequest.setAppId(request.getAppId());

            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            int success = 0;
            for (PoiCodeAppIdDO poiCodeAppIdDO : poiCodeAppIdDOS) {
                StoreKey storeKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + poiCodeAppIdDO.getPoiCode());
                MtTokenMessage tokenFromCache = mtBrandChannelGateService.getIsvTokenFromCache(tenantId, poiCodeAppIdDO.getPoiCode());
                if (tokenFromCache == null || !tokenFromCache.refreshValid()) {
                    log.info("渠道门店token不存在, tenantId:{}, channelPoiCode:{}, tokenFromCache:{}", tenantId, poiCodeAppIdDO.getPoiCode(), JSONObject.toJSONString(tokenFromCache));
                    continue;
                }
                MtTokenMessage mtTokenMessage = mtBrandChannelGateService.mtRefreshTokenAndSetCache(baseRequest, sysParam, tokenFromCache.getRefreshToken(), storeKey, poiCodeAppIdDO.getPoiCode(), tenantId);
                if (mtTokenMessage == null) {
                    log.info("手动刷新美团渠道token失败，请排查原因, tenantId:{}, channelPoiCode:{}, tokenFromCache:{}", tenantId, poiCodeAppIdDO.getPoiCode(), JSONObject.toJSONString(tokenFromCache));
                } else {
                    log.info("手动刷新美团ISV token, 刷新成功：{}", poiCodeAppIdDO.getPoiCode());
                    success++;
                }
            }
            log.info("手动刷新token，tenantId:{}, 渠道门店:{}, 总数:{}, 刷新成功数:{}", tenantId, JSONObject.toJSONString(poiCodeAppIdDOS), poiCodeAppIdDOS.size(), success);
            return String.format("tenantId:%s tenantAppId:%s 手动刷新token, 总数:%s, 刷新成功数:%s", tenantId, sysParam.get("app_id"), poiCodeAppIdDOS.size(), success);
        } else {
            throw new BizException(ResultCode.FAIL.getCode(), "当前不支持手动刷新该渠道的token");
        }
    }

    @Override
    public ElmIsvAccessTokenDTO getElmIsvToken(ElmIsvAuthRequest request) {
        ElmIsvAccessTokenDTO elmIsvAccessTokenDTO = new ElmIsvAccessTokenDTO();


        // 通过授权code获取token
        Map params = new HashMap();
        params.put(ProjectConstant.ELM_GRANT_TYPE, authorization_code);
        params.put(ProjectConstant.ELM_NEED_REFRESH_TOKEN, need_refresh_token);
        params.put(ProjectConstant.ELM_CLIENT_ID, request.getAppkey());
        params.put(ProjectConstant.ELM_CLIENT_SECRET, request.getSecret());
        params.put(ProjectConstant.ELM_CODE, request.getCode());
        Map<String, Object> authDetailMap = elmChannelGateService.oauth2Token(params);
        if (MapUtils.isEmpty(authDetailMap)){
            log.info("饿了么oauth2Token失败，request={}", request);
            throw new BizException("饿了么授权失败");
        }
        String accessToken = (String) authDetailMap.get("access_token");
        String refreshToken = (String) authDetailMap.get("refresh_token");
        // refresh_token的过期秒数
        Long rokenExpiresIn = Long.valueOf((String)authDetailMap.get("rtoken_expires_in"));
        // access_token剩余有效的秒数
        Long expiresIn = Long.valueOf((String) authDetailMap.get("expires_in"));


        // common.token.getuser获取授权用户openid及店铺信息
        try{
            Map<String, Object> bizParam = new HashMap<String, Object>();
            Map<String, Object> sysParam = elmChannelGateService.buildIsvSysParam(request.getAppkey(), request.getSecret(), accessToken);
            bizParam.put("page", 1);
            bizParam.put("pageSize", 20);

            ChannelResponseDTO channelResponseDTO = elmChannelGateService.postToChannel(ChannelPostELMEnum.COMMON_TOKEN_GETUSER, sysParam, bizParam);
            ChannelResponseResult<AuthInfoRespDTO> channelResponseResult = channelResponseDTO.getBody();
            if(!channelResponseDTO.isSuccess() || Objects.isNull(channelResponseResult.getData()) || Objects.isNull(channelResponseResult.getData().getOpenid())){
                log.info("获取饿了么授权信息失败，request={}, accessToken={}, channelResponseDTO={}", request, accessToken, channelResponseDTO);
                throw new BizException("获取饿了么授权信息失败");
            }
            elmIsvAccessTokenDTO.setOpenId(channelResponseResult.getData().getOpenid());
            elmIsvAccessTokenDTO.setShopName(Optional.ofNullable(channelResponseResult.getData().getName()).orElse(""));
        }catch (Exception e){
            log.error("获取饿了么授权信息失败，request={}, accessToken={}", request, accessToken, e);
            throw new BizException("获取饿了么授权信息失败");
        }

        Long currentTimeMillis = System.currentTimeMillis();
        elmIsvAccessTokenDTO.setAccessToken(accessToken);
        elmIsvAccessTokenDTO.setAccessTokenGrantDate(currentTimeMillis);
        elmIsvAccessTokenDTO.setAccessTokenExpireDate(currentTimeMillis + expiresIn * 1000L);
        elmIsvAccessTokenDTO.setRefreshToken(refreshToken);
        elmIsvAccessTokenDTO.setRefreshTokenGrantDate(currentTimeMillis);
        elmIsvAccessTokenDTO.setRefreshTokenExpireDate(currentTimeMillis + rokenExpiresIn * 1000L);

        return elmIsvAccessTokenDTO;
    }

    @Override
    public ElmIsvRefreshTokenDTO refreshElmIsvToken(ElmIsvRefreshTokenRequest request) {

        // 通过授权code获取token
        Map params = new HashMap();
        params.put(ProjectConstant.ELM_GRANT_TYPE, "refresh_token");
        params.put(ProjectConstant.ELM_CLIENT_ID, request.getAppkey());
        params.put(ProjectConstant.ELM_CLIENT_SECRET, request.getSecret());
        params.put("refreshToken", request.getRefreshToken());
        Map<String, Object> authDetailMap = elmChannelGateService.oauth2Token(params);
        if (MapUtils.isEmpty(authDetailMap)){
            log.info("饿了么oauth2Token失败，request={}", request);
            throw new BizException("饿了么刷新token失败");
        }
        String accessToken = (String) authDetailMap.get("access_token");
        String refreshToken = (String) authDetailMap.get("refresh_token");
        // refresh_token剩余有效的秒数
        Long expiresIn = Long.valueOf((String) authDetailMap.get("expires_in"));
        // access_token剩余有效的秒数
        Long accessExpiresIn = Long.valueOf((String) authDetailMap.get("access_expires_in"));

        Long currentTimeMillis = System.currentTimeMillis();
        ElmIsvRefreshTokenDTO refreshTokenDTO = new ElmIsvRefreshTokenDTO();
        refreshTokenDTO.setAccessToken(accessToken);
        refreshTokenDTO.setRefreshToken(refreshToken);
        refreshTokenDTO.setAccessTokenGrantDate(currentTimeMillis);
        refreshTokenDTO.setAccessTokenExpireDate(currentTimeMillis + accessExpiresIn * 1000L);
        refreshTokenDTO.setRefreshTokenGrantDate(currentTimeMillis);
        refreshTokenDTO.setRefreshTokenExpireDate(currentTimeMillis + expiresIn * 1000L);

        return refreshTokenDTO;
    }

    @Override
    public MtIsvAccessTokenDTO getMtIsvToken(MtIsvAuthRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("app_id", request.getAppkey());
        params.put("secret", request.getSecret());
        params.put(ProjectConstant.APP_POI_CODE, request.getAppPoiCode());
        params.put("response_type", "token");

        MtAuthInfoRespDTO authInfoRespDTO = mtBrandChannelGateService.fastGetToken(params);
        if (Objects.isNull(authInfoRespDTO)){
            log.error("美团授权获取token失败，request={}", request);
            throw new BizException("美团授权获取token失败");
        }

        MtIsvAccessTokenDTO mtIsvAccessTokenDTO = authInfoRespDTO.convertToMtIsvAccessTokenDTO();

        log.info("美团渠道获取token，request={}，result={}", request, mtIsvAccessTokenDTO);

        return mtIsvAccessTokenDTO;
    }

    @Override
    public MtIsvAccessTokenDTO refreshMtIsvToken(MtIsvRefreshTokenRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("app_id", request.getAppkey());
        params.put("secret", request.getSecret());
        params.put("grant_type", "refresh_token");
        params.put("refresh_token", request.getRefreshToken());

        MtAuthInfoRespDTO authInfoRespDTO = mtBrandChannelGateService.refreshToken(params);
        if (Objects.isNull(authInfoRespDTO)){
            log.error("美团刷新token失败，request={}", request);
            throw new BizException("美团刷新token失败");
        }

        MtIsvAccessTokenDTO mtIsvAccessTokenDTO = authInfoRespDTO.convertToMtIsvAccessTokenDTO();

        log.info("美团渠道刷新token，request={}，result={}", request, mtIsvAccessTokenDTO);

        return mtIsvAccessTokenDTO;
    }

    @Override
    public YouzanIsvTokenDTO getYouzanIsvToken(YouzanIsvAuthRequest request) {
        if (Objects.isNull(request) || StringUtils.isBlank(request.getAppkey())
                || StringUtils.isBlank(request.getSecret()) || StringUtils.isBlank(request.getCode())) {
            log.info("获取有赞渠道token失败，参数错误，request={}", request);
            throw new IllegalArgumentException("参数错误");
        }

        YouzanIsvTokenDTO tokenDTO =  yzToolAccessTokenService.getIsvToken(request.getAppkey(), request.getSecret(), request.getCode());

        return tokenDTO;
    }

    @Override
    public YouzanIsvTokenDTO refreshYouzanIsvToken(YouzanIsvRefreshTokenRequest request) {
        if (Objects.isNull(request) || StringUtils.isBlank(request.getAppkey())
                || StringUtils.isBlank(request.getSecret()) || StringUtils.isBlank(request.getRefreshToken())) {
            log.info("刷新有赞渠道token失败，参数错误，request={}", request);
            throw new IllegalArgumentException("参数错误");
        }

        YouzanIsvTokenDTO tokenDTO =  yzToolAccessTokenService.refreshIsvToken(request.getAppkey(), request.getSecret(), request.getRefreshToken());

        return tokenDTO;
    }

    @Override
    public List<MarketMessageRespDTO> getMarketOrderList(MarketOrderListRequest request) {

        AppInfoQueryByAppIdRequest appInfoQueryReq = new AppInfoQueryByAppIdRequest();
        appInfoQueryReq.setTenantId(request.getTenantId());
        appInfoQueryReq.setChannelId(Long.valueOf(request.getChannelId()).intValue());
        appInfoQueryReq.setQnhAppId(request.getQnhAppId());
        AppInfoDTO appInfoDTO = poiChannelAppThriftServiceProxy.queryAppInfoByAppId(appInfoQueryReq);
        if (Objects.isNull(appInfoDTO)){
            log.warn("getPoiInfoIForIsv appInfoDTO为空，tenantId={}，channelId={}, appId={}", request.getTenantId(), request.getChannelId(), request.getQnhAppId());
            return new ArrayList<>();
        }
        Map<String, Object> sysParam = elmChannelGateService.buildIsvSysParam(appInfoDTO.getAppKey(),
                appInfoDTO.getSecret(), appInfoDTO.getAccessToken());

        Map<String, Object> bizParam = new HashMap<>();
        bizParam.put("start", request.getQueryStartTime());
        bizParam.put("end", request.getQueryEndTime());
        bizParam.put("offset", request.getQueryOffset());
        bizParam.put("limit", 20);
        BaseRequest baseRequest = new BaseRequest().setChannelId(Long.valueOf(request.getChannelId()).intValue()).
                setTenantId(request.getChannelId()).setAppId(request.getQnhAppId());
        String baseUrl = elmChannelGateService.getPostUrl(ChannelPostELMEnum.MARKET_ORDER_LIST);
        try {
            Map<String, Object> resultMap = elmChannelGateService.sendPost(baseUrl, ChannelPostELMEnum.MARKET_ORDER_LIST.getUrl(), baseRequest, bizParam, sysParam);

            JSONObject resultJsonObject = (JSONObject) resultMap.get(ProjectConstant.BODY);
            ElmMarketOrderRespDTO elmMarketOrderRespDTO = resultJsonObject.toJavaObject(ElmMarketOrderRespDTO.class);
            if (ResultCode.SUCCESS.getCode() != elmMarketOrderRespDTO.getErrno()){
                log.error("获取饿了么服务市场订单 post resp err, tenantId: {} - channelId: {} - appId: {} - 错误信息：{}", request.getTenantId(),
                        request.getChannelId(), request.getQnhAppId(), elmMarketOrderRespDTO.getError());
                throw new BizException("获取饿了么服务市场订单失败 post resp err");
            }
            if (CollectionUtils.isEmpty(elmMarketOrderRespDTO.getData())) {
                return Lists.newArrayList();
            }
            return elmMarketOrderRespDTO.getData().stream().map(ElmMarketOrderDTO::convertToMarketMessageRespDTO).collect(Collectors.toList());
        } catch (Exception e){
            log.error("获取饿了么服务市场订单报错，request={}, err={}", request, e);
            throw new BizException("获取饿了么服务市场订单报错");
        }
    }
}
