package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.RHINO_UPTIMATE_SET;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.exceptions.TenantBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.PoiCodeAppIdDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChannelPoiStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JDDJCommomEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseChannelPoiRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DxPushMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.StringUtils;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelPoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiDetailsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiIdsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;

/**
 * @author:huchangwu
 * @date: 2019/1/17
 * @time: 下午9:37
 */
@Service("jddjChannelPoiService")
public class JddjChannelPoiServiceImpl implements ChannelPoiService {

    @Resource
    private JddjConverterService jddjConverterService;
    @Resource
    private JddjChannelGateService jddjChannelGateService;
    @Resource
    private BaseConverterService baseConverterService;
    @Resource
    private ClusterRateLimiter clusterRateLimiter;
    @Resource
    private CommonLogger log;
    @Resource
    private CopChannelStoreService copChannelStoreService;
    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Autowired
    private DxPushMessageProducer dxPushMessageProducer;

    @Value("${jddj.url.base}")
    private String bastUrl;
    @Value("${jddj.url.base}" + "${jddj.url.shoplist}")
    private String shoplist;
    @Value("${jddj.url.base}" + "${jddj.url.shopget}")
    private String shopget;

//

    /**
     * 20230704 - chenjianhui05
     * 本接口没有传app id 或者 store ID，对于多应用场景有问题
     * 并且也无明确的业务场景需求
     * 查阅了近 6 个月的raptor未发现有调用量
     *
     * ***** 因此废弃此接口，各方勿使用 ******
     * ***** 因此废弃此接口，各方勿使用 ******
     * ***** 因此废弃此接口，各方勿使用 ******
     *
     * ***** 未支持京东多应用，各方勿使用 ******
     * ***** 未支持京东多应用，各方勿使用 ******
     * ***** 未支持京东多应用，各方勿使用 ******
     * @param req
     * @return
     */
    @Override
    public GetPoiInfoResponse batchGetPoiInfo(BaseRequestSimple req) {

        Map<String, Object> sysParams = jddjChannelGateService.getChannelSysParams(new BaseRequest().setChannelId(req.getChannelId()).setTenantId(req.getTenantId()));
        String appId = String.valueOf(sysParams.get(ProjectConstant.JDDJ_APP_KEY));
        GetPoiInfoResponse resp = new GetPoiInfoResponse();
        /**
         * 查询门店列表
         */
        // 限频检查
        if (org.apache.commons.lang3.StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.SHOP_LIST, String.valueOf(req.getTenantId()))) {
            log.warn("拉取京东门店列表 获取令牌失败 不阻塞流程 直接调用接口，request {}", req);
        }
        Map shoplistMap = jddjChannelGateService.sendPostApp(shoplist, null, baseConverterService.baseRequest(req), null);
        if (Integer.parseInt((String) shoplistMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表失败"));
        }
        String shopidJsonStr = (String) shoplistMap.get(ProjectConstant.DATA);
        JSONArray shopidJsonarr = JSON.parseObject(shopidJsonStr).getJSONArray(ProjectConstant.RESULT);
        if (shopidJsonarr == null) {
            return resp.setStatus(ResultGenerator.genSuccessResult())
                    .setPoiInfoList(Collections.emptyList());
        }
        List<String> shopIds = shopidJsonarr.toJavaList(String.class);
        /**
         * 查询门店详情
         */
        resp.setStatus(ResultGenerator.genSuccessResult())
                .setPoiInfoList(Lists.newArrayList());
        for (String shopId : shopIds) {
            Map param = Maps.newHashMap();
            param.put(ProjectConstant.STORE_NO, shopId);
            // 限频检查
            if (org.apache.commons.lang3.StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.SHOP_DETAIL, String.valueOf(req.getTenantId()))) {
                log.warn("拉取京东门店详情 获取令牌失败 不阻塞流程 直接调用接口，request {}", param);
            }
            Map poiMap = jddjChannelGateService.sendPostApp(shopget, null, baseConverterService.baseRequest(req), param);
            if (Integer.parseInt((String) poiMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店信息失败"));
            }
            String shopInfoStr = (String) poiMap.get(ProjectConstant.DATA);
            JSONObject shopInfoJsonobject = JSON.parseObject(shopInfoStr).getJSONObject(ProjectConstant.RESULT);
            if (shopInfoJsonobject == null) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店信息失败"));
            }
            ChannelPoiInfo channelPoiInfo = shopInfoJsonobject.toJavaObject(ChannelPoiInfo.class);

            // 补充京东appPoiCode
            if (StringUtils.isBlank(channelPoiInfo.getOutSystemId())) {
                updateAppPoiCode(appId, req.getTenantId(), req.getChannelId(), resp, channelPoiInfo);
                if (resp.getStatus().getCode() == ResultCode.FAIL.getCode()) {
                    return resp;
                }
            }

            resp.getPoiInfoList().add(jddjConverterService.poiInfoMapping(channelPoiInfo));
        }

        return resp;
    }

    /**
     * 20230704 chenjianh05 支持京东多应用
     *
     * 这个实现是有问题的，如果应用太多会有性能问题
     * 但是，为了减少调用方的改造，先这样实现，美团和饿了么也是这样实现的
     *
     * @param req
     * @return
     */
    @Override
    public GetPoiIdsResponse batchGetPoiIds(BaseRequestSimple req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        long tenantId = req.getTenantId();
        int channelId = req.getChannelId();
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOList)) {
            log.warn("获取租户渠道应用数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取租户渠道应用数据失败"));
        }

        // 限频检查
        if (!clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.SHOP_LIST, String.valueOf(req.getTenantId()))) {
            log.warn("拉取京东门店列表 获取令牌失败 不阻塞流程 直接调用接口，request {}", req);
        }

        List<String> totalShopIdsInAllApps = new ArrayList<>();
        List<AppPoiCodeDTO> appPoiCodeDTOList = new ArrayList<>();
        // 按渠道/品牌应用维度进行查询
        for (CopAccessConfigDO copAccessConfigDO : copAccessConfigDOList) {
            long appId = copAccessConfigDO.getAppId() == null ? 1L : copAccessConfigDO.getAppId();
            BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);

            Map shoplistMap = jddjChannelGateService.sendPostApp(shoplist, null, baseRequest, null);
            if (Integer.parseInt((String) shoplistMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表失败"));
            }
            String shopidJsonStr = (String) shoplistMap.get(ProjectConstant.DATA);
            JSONArray shopidJsonarr = JSON.parseObject(shopidJsonStr).getJSONArray(ProjectConstant.RESULT);
            if (shopidJsonarr == null) {
                return resp.setStatus(ResultGenerator.genSuccessResult())
                        .setStoreIds(Collections.emptyList());
            }

            List<String> shopIds = shopidJsonarr.toJavaList(String.class);
            if (CollectionUtils.isNotEmpty(shopIds)) {
                totalShopIdsInAllApps.addAll(shopIds);
                // appPoiCode 关联 appId 进行透出
                for (String appPoiCode : shopIds) {
                    AppPoiCodeDTO appPoiCodeDTO = new AppPoiCodeDTO(appPoiCode, appId);
                    appPoiCodeDTOList.add(appPoiCodeDTO);
                }
            }
        }

        if (totalShopIdsInAllApps.isEmpty()) {
            log.warn("查询门店列表为空 tenantId: {} - channelId: {}", tenantId, channelId);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表为空"));
        }
        resp.setStoreIds(totalShopIdsInAllApps);
        resp.setAppPoiCodeDTOList(appPoiCodeDTOList);
        return resp;
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIdsBySysParam(BatchGetPoiIdsRequest req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        Map<String, Object> sysParam = JSON.parseObject(req.getSysParams());
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setTenantId(req.getTenantId());
        baseRequest.setChannelId(req.getChannelId());
        Map shoplistMap = jddjChannelGateService.sendPost(shoplist, null, baseRequest, null, sysParam);
        if (Integer.parseInt((String) shoplistMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表失败"));
        }
        String shopidJsonStr = (String) shoplistMap.get(ProjectConstant.DATA);
        JSONArray shopidJsonarr = JSON.parseObject(shopidJsonStr).getJSONArray(ProjectConstant.RESULT);
        if (shopidJsonarr == null) {
            return resp.setStatus(ResultGenerator.genSuccessResult())
                    .setStoreIds(Collections.emptyList());
        }
        List<String> shopIds = shopidJsonarr.toJavaList(String.class);
        return resp.setStoreIds(shopIds);
    }

    @Override
    public GetPoiInfoResponse batchGetPoiDetails(BatchGetPoiDetailsRequest request) {
        GetPoiInfoResponse resp = new GetPoiInfoResponse();
        long tenantId = request.getTenantId();
        int channelId = request.getChannelId();
        List<String> appPoiCodeList = request.getStoreIds();
        if (CollectionUtils.isEmpty(appPoiCodeList)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "需传入门店的开放平台编码"));
        }

        List<PoiInfo> poiInfoListInAllApps = new ArrayList<>();

        // 根据是否传入 appId 进行处理。
        // 注：当初始化拉取信息时，一定需要传入 appId，因为此时渠道门店信息表中还没有数据，也就不能根据 appPoiCode 去关联了
        if (request.isSetAppId()) {
            try {
                long appId = request.getAppId();
                List<PoiInfo> poiInfoList = getPoiDetailsOneApp(tenantId, channelId, appId, appPoiCodeList, request.isAsyncInvoke());
                if (CollectionUtils.isNotEmpty(poiInfoList)) {
                    poiInfoListInAllApps.addAll(poiInfoList);
                }
            } catch (InvokeChannelTooMuchException e) {
                log.info("调用渠道接口次数超过限制,返回TRIGGER_LIMIT(999)+等待时间 重试 。");
                return resp.setStatus(new ResultStatus(ResultCodeEnum.TRIGGER_LIMIT.getValue(), "", String.valueOf(e.getWaitTimeMills())));
            } catch (Exception e) {
                log.error("调用渠道接口发生异常:", e);
                return resp.setStatus(new ResultStatus(StatusCodeEnum.SYS_ERROR.getCode(), "", String.valueOf(e.getMessage())));
            }
        } else {
            // 根据 appPoiCode 获取 appId
            Map<String, Long> poiCodeAppIdMapping = poiCode2AppId(tenantId, channelId, appPoiCodeList);

            // 按照 appId 将 appPoiCode 分组
            Map<Long, List<String>> appPoiCodeGroupByAppIdMap = new HashMap<>();
            for (Map.Entry<String, Long> entry : poiCodeAppIdMapping.entrySet()) {
                String appPoiCode = entry.getKey();
                Long appId = entry.getValue();
                List<String> list = appPoiCodeGroupByAppIdMap.getOrDefault(appId, new ArrayList<>());
                list.add(appPoiCode);
                appPoiCodeGroupByAppIdMap.put(appId, list);
            }

            // 按照 appId 维度进行查询
            for (Map.Entry<Long, List<String>> entry : appPoiCodeGroupByAppIdMap.entrySet()) {
                long appId = entry.getKey();
                List<String> appPoiCodeListInAppId = entry.getValue();
                try {
                    List<PoiInfo> poiInfoList = getPoiDetailsOneApp(tenantId, channelId, appId, appPoiCodeListInAppId, request.isAsyncInvoke());
                    if (CollectionUtils.isNotEmpty(poiInfoList)) {
                        poiInfoListInAllApps.addAll(poiInfoList);
                    }
                } catch (InvokeChannelTooMuchException e) {
                    log.info("调用渠道接口次数超过限制,返回TRIGGER_LIMIT(999)+等待时间 重试 。");
                    return resp.setStatus(new ResultStatus(ResultCodeEnum.TRIGGER_LIMIT.getValue(), "", String.valueOf(e.getWaitTimeMills())));
                } catch (Exception e) {
                    log.error("调用渠道接口发生异常:", e);
                    return resp.setStatus(new ResultStatus(StatusCodeEnum.SYS_ERROR.getCode(), "", String.valueOf(e.getMessage())));
                }
            }
        }

        if (poiInfoListInAllApps.isEmpty()) {
            log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appPoiCodeList: {}", tenantId, channelId, appPoiCodeList);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表为空"));
        }
        resp.setStatus(ResultGenerator.genSuccessResult());
        resp.setPoiInfoList(poiInfoListInAllApps);

        return resp;
    }

    /**
     * 获取租户、渠道、poiCode 维度下，poiCode 到 appId 的映射信息
     * 当从渠道门店基础信息表中获取不到关联数据，且租户只关联一个品牌时，进行兜底
     *
     * @param tenantId    租户id
     * @param channelId   渠道id
     * @param poiCodeList 商家门店编码列表
     * @return
     */
    private Map<String, Long> poiCode2AppId(long tenantId, int channelId, List<String> poiCodeList) {
        Map<String, Long> poiCodeAppIdMapping = copChannelStoreService.getPoiCodeAppIdMapping(tenantId, channelId, poiCodeList);
        if (poiCodeAppIdMapping == null || poiCodeAppIdMapping.isEmpty() || poiCodeAppIdMapping.size() != poiCodeList.size()) {
            // 如果在渠道门店基础表中不存在数据，这里进行兜底
            // 或者存在部分数据时也进行兜底
            List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
            if (copAccessConfigDOList == null || copAccessConfigDOList.size() != 1) {
                // 租户维度只有单个品牌才能进行兜底，这里查询为空，或者超过一条记录，都认为是非法数据
                throw new IllegalArgumentException("租户关联的品牌数为空或超过一个，不能获取对应密钥信息");
            } else {
                Long appId = copAccessConfigDOList.get(0).getAppId();
                return poiCodeList.stream().collect(Collectors.toMap(poiCode -> poiCode, poiCode -> appId));
            }
        } else {
            return poiCodeAppIdMapping;
        }
    }

    private List<PoiInfo>  getPoiDetailsOneApp(Long tenantId, Integer channelId, Long appId, List<String> appPoiCodeListInAppId, boolean isAsyncInvoke) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);
        // 每个 appId 维度查询一次即可
        Map<String, Object> sysParam = jddjChannelGateService.getSysParam(baseRequest);
        String appKey = (String) sysParam.getOrDefault("app_key", "");

        List<PoiInfo> poiInfoList = new ArrayList<>();

        for (String shopId : appPoiCodeListInAppId) {
            Map param = Maps.newHashMap();
            param.put(ProjectConstant.STORE_NO, shopId);
            // 限频检查
            rateLimitManage(tenantId.toString(), ChannelPostJDDJEnum.SHOP_DETAIL, isAsyncInvoke);

            Map<String, Object> sysParamTmp = new HashMap<>(sysParam);
            Map poiMap = jddjChannelGateService.sendPost(shopget, null, baseConverterService
                    .baseRequest(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channelId)), param,sysParamTmp);
            if (Integer.parseInt((String) poiMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
                log.error("查询门店详情失败 tenantId: {} - channelId: {} - appId: {} - shopId: {} 错误信息：{}", tenantId, channelId, appId, shopId,
                        JacksonUtils.toJson(poiMap));
                throw new TenantBizException(StatusCodeEnum.SYS_ERROR.getCode(),"查询门店详情失败：" + JacksonUtils.toJson(poiMap));
            }
            String shopInfoStr = (String) poiMap.get(ProjectConstant.DATA);
            JSONObject shopInfoJsonobject = JSON.parseObject(shopInfoStr).getJSONObject(ProjectConstant.RESULT);
            if (shopInfoJsonobject == null) {
                log.error("查询门店详情失败 tenantId: {} - channelId: {} - appId: {} - shopId: {} 错误信息：{}", tenantId, channelId, appId, shopId,
                        "shopInfoJsonobject is null");
                throw new TenantBizException(StatusCodeEnum.SYS_ERROR.getCode(),"查询门店详情失败：" + "shopInfoJsonobject is null");
            }
            ChannelPoiInfo channelPoiInfo = shopInfoJsonobject.toJavaObject(ChannelPoiInfo.class);

            // 补充京东appPoiCode
            if (StringUtils.isBlank(channelPoiInfo.getOutSystemId())) {
                try{
                    updateAppPoiCode(appId.toString(), tenantId, channelId, channelPoiInfo);
                } catch(Exception e){
                    log.error("补充京东门店编码失败, appId:{}, poi:{}",appId, channelPoiInfo.getStationNo(), e);
                }
            }
            PoiInfo tmp1 = jddjConverterService.poiInfoMapping(channelPoiInfo);
            tmp1.setAppId(appId);
            tmp1.setAppKey(appKey);
            poiInfoList.add(tmp1);
        }


        return poiInfoList;
    }

    /**
     * 获取根据渠道的开放平台门店Id，查询渠道的外卖门店Id
     *
     * @param request
     * @return
     */
    @Override
    public GetPoiIdsResponse getPoiInnerId(BatchGetPoiDetailsRequest request) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        return resp.setStatus(ResultGenerator.genFailResult("京东到家不支持该功能"));
    }

    @Override
    public ResultStatus poiOpen(ChannelPoiIdRequest req) {
        return updateShopInfo(new BaseRequest()
                        .setTenantId(req.getTenantId())
                        .setChannelId(req.getChannelId())
                        .setStoreIdList(Collections.singletonList(req.getStoreId()))
                , req.getChannelPoiCode()
                , maps(Arrays.asList(ProjectConstant.CLOSE_STATUS), Arrays.asList("0")));
    }

    @Override
    public ResultStatus poiClose(ChannelPoiIdRequest req) {
        return updateShopInfo(new BaseRequest()
                        .setTenantId(req.getTenantId())
                        .setChannelId(req.getChannelId())
                        .setStoreIdList(Collections.singletonList(req.getStoreId()))
                ,
                req.getChannelPoiCode()
                , maps(Arrays.asList(ProjectConstant.CLOSE_STATUS), Arrays.asList("1")));
    }

    @Override
    public ResultStatus promotionInfoUpdate(PoiPromotionInfoUpdateRequest req) {
        return updateShopInfo(new BaseRequest()
                        .setTenantId(req.getTenantId())
                        .setChannelId(req.getChannelId())
                        .setStoreIdList(Collections.singletonList(req.getStoreId()))
                ,  req.getChannelPoiCode()
                , maps(Arrays.asList(ProjectConstant.STORE_NOTICE), Arrays.asList(req.getPromotionInfo())));
    }

    @Override
    public ResultStatus prebookStatusOpen(ChannelPoiIdRequest req) {
        return ResultGenerator.genFailResult("京东到家暂不支持预订单设置");
    }

    @Override
    public ResultStatus prebookStatusClose(ChannelPoiIdRequest req) {
        return ResultGenerator.genFailResult("京东到家暂不支持预订单设置");
    }

    /**
     * 更新门店的接受预定的日期范围
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus updatePrebookDays(PoiPrebookDaysUpdateRequest request) {
        return ResultGenerator.genFailResult("京东到家暂不支持预订单接受日期范围设置");
    }

    private Map<String,String> maps(List<String> keys, List<String> values) {
        Map<String,String> param = Maps.newHashMapWithExpectedSize(keys.size());

        for (int i=0;i<keys.size();i++) {
            param.put(keys.get(i), values.get(i));
        }

        return param;
    }


    /**
     * 20230704 chenjianhui05
     *
     * 请求参数必须有 appID
     * 或者，根据stationNo 去查 TODO
     *
     * @param baseRequest
     * @param stationNo
     * @param param
     * @return
     */
    private ResultStatus updateShopInfo(BaseRequest baseRequest, String stationNo, Map<String,String> param) {
//        Map<String, Object> sysParams = jddjChannelGateService.getChannelSysParams(baseRequest);
//        String appId = String.valueOf(sysParams.get(ProjectConstant.JDDJ_APP_KEY));
//        // 限频检查
//        if (org.apache.commons.lang3.StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.SHOP_LIST, String.valueOf(baseRequest.getTenantId()))) {
//            log.warn("拉取京东门店列表 获取令牌失败 不阻塞流程 直接调用接口，request {}", baseRequest);
//        }

        Map bizMap = Maps.newHashMap();
        bizMap.put(ProjectConstant.STATION_NO, stationNo);
        bizMap.put(ProjectConstant.OPERATOR, "admin_ocms");
        bizMap.putAll(param);

        Map resMap = jddjChannelGateService.sendPostApp(bastUrl + ChannelPostJDDJEnum.UPDATE_STORE_INFO.getUrl(), null,
                baseRequest, bizMap);
        if (Integer.parseInt((String) resMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
            return ResultGenerator.genResult(ResultCode.FAIL, "更新门店信息失败");
        }

        Object data = resMap.get(ProjectConstant.DATA);
        if (Objects.nonNull(data)){
            Map<String, Object> dataMap = JacksonUtils.parseMap(String.valueOf(data), String.class, Object.class);
            if (MapUtils.isNotEmpty(dataMap) && dataMap.get(ProjectConstant.CODE) != null && Integer.parseInt(String.valueOf(dataMap.get(ProjectConstant.CODE))) != ResultCode.SUCCESS.getCode()) {
                Object msg = dataMap.get(ProjectConstant.MSG) == null ? "更新门店信息失败" : dataMap.get(ProjectConstant.MSG);
                return ResultGenerator.genResult(ResultCode.FAIL, String.valueOf(msg));
            }
        }

        return ResultGenerator.genSuccessResult();
    }

    /**
     * 营业时间更新
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus shippingTimeUpdate(PoiShippingTimeUpdateRequest req) {

        if (CollectionUtils.isEmpty(req.getShippingTimes())) {
            return ResultGenerator.genResult(ResultCode.FAIL, "营业时间为空");
        }

        Map<String,String> param = Maps.newHashMap();
        param.put("serviceTimeStart1", String.valueOf(convertServiceTime(req.getShippingTimes().get(0).getStartTime())));
        param.put("serviceTimeEnd1", String.valueOf(convertServiceTime(req.getShippingTimes().get(0).getEndTime())));

        if (req.getShippingTimes().size() > 1) {
            param.put("serviceTimeStart2", String.valueOf(convertServiceTime(req.getShippingTimes().get(1).getStartTime())));
            param.put("serviceTimeEnd2", String.valueOf(convertServiceTime(req.getShippingTimes().get(1).getEndTime())));
        }

        return updateShopInfo(new BaseRequest()
                        .setTenantId(req.getTenantId())
                        .setChannelId(req.getChannelId())
                        .setStoreIdList(Collections.singletonList(req.getStoreId()))
                ,  req.getChannelPoiCode()
                , param);
    }


    private int convertServiceTime(String startTime) {
        String[] ts = org.apache.commons.lang3.StringUtils.split(startTime,":");
        if (ts.length != 2) {
            return 0;
        }

        return (Integer.valueOf(ts[0]) * 60 + Integer.valueOf(ts[1])) / 30;

    }

    /**
     * 获取门店公告信息
     *
     * @param request
     * @return
     */
    @Override
    public GetPoiPromotionInfoResponse getPoiPromotionInfo(ChannelPoiIdRequest request) {
        return null;
    }

    /**
     * 获取门店营业状态
     *
     * @param channelPoiIdRequest
     * @return
     */
    @Override
    public ChannelPoiStatusResponse getPoiStatus(ChannelPoiIdRequest channelPoiIdRequest) {
        ChannelPoiStatusResponse resp = new ChannelPoiStatusResponse();

        BatchGetPoiDetailsRequest request = new BatchGetPoiDetailsRequest();
        request.setTenantId(channelPoiIdRequest.getTenantId());
        request.setChannelId(channelPoiIdRequest.getChannelId());
        request.setStoreIds(Collections.singletonList(channelPoiIdRequest.getChannelPoiCode()));
        //todo查询appId，通过innerCode去拿appId
        Long appId = channelPoiCode2AppId(channelPoiIdRequest.getTenantId(),
                channelPoiIdRequest.getChannelId(),
                channelPoiIdRequest.getChannelPoiCode());
        if (appId!=null){
            request.setAppId(appId);
        }
        GetPoiInfoResponse getPoiInfoResponse = batchGetPoiDetails(request);
        if (getPoiInfoResponse.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
            resp.setStatus(ResultGenerator.genFailResult("获取门店营业状态失败"));
            return resp;
        }
        PoiInfo poiInfo = getPoiInfoResponse.getPoiInfoList().get(0);
        //0正常营业，1休息中
        resp.setPoiStatus(poiInfo.getOpenLevel()== JDDJCommomEnum.OPEN.getCode()? ChannelPoiStatusEnum.OPEN.getStatus() : ChannelPoiStatusEnum.CLOSED.getStatus());
        resp.setStatus(getPoiInfoResponse.status);
        return resp;
    }

    private Long channelPoiCode2AppId(long tenantId, int channelId, String channelPoiCode) {
        List<PoiCodeAppIdDO> poiCodeAppIdDOS = copChannelStoreService.selectAllByChannelPoiCode(tenantId, channelId, Collections.singletonList(channelPoiCode));
        if (CollectionUtils.isEmpty(poiCodeAppIdDOS)) {
            return null;
        }
        return poiCodeAppIdDOS.get(0).getAppId();
    }

    /**
     * 门店授权
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genFailResult("京东渠道不支持授权");
    }

    /**
     * 门店解析授权
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiDeAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genFailResult("京东渠道不支持解除授权");
    }

    @Override
    public ResultStatus poiHotlineUpdate(ChannelPoiHotlineRequest req) {
        String hotline = req.getHotline();
        Map<String,String> param = Maps.newHashMap();
        //7.15京东到家逻辑更改为：客服电话更改 渠道测 门店手机号和座机号
        param.put("phone", hotline);
        param.put("mobile", hotline);
        return updateShopInfo(new BaseRequest()
                        .setTenantId(req.getTenantId())
                        .setChannelId(req.getChannelId())
                        .setStoreIdList(Collections.singletonList(req.getStoreId()))
                        .setAppId(req.getAppId())
                , req.getChannelPoiCode()
                , param);
    }

    @Override
    public QueryPoiAuthDetailResponse queryPoiAuthDetailInfo(BaseChannelPoiRequestSimple request) {
        return new QueryPoiAuthDetailResponse().setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能"));
    }

    @Override
    public ResultStatus updatePoiAuthInfo(UpdatePoiAuthInfoRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能");
    }

    @Override
    public ResultStatus updatePoiAddress(PoiAddressUpdateRequest request) {
        return ResultGenerator.genFailResult("京东渠道不支持");
    }

    @Override
    public UpdateSaleafterAddressMessageResponse updateSaleafterAddressMessage(UpdateSaleafterAddressMessageRequest request) {
        return ResultGenerator.UpdateSafeAddressResultData(ResultCode.FAIL, "京东到家暂不支持此功能");
    }


    private void updateAppPoiCode(String appId, long tenantId, int channelId, GetPoiInfoResponse resp, ChannelPoiInfo channelPoiInfo) {
        Map updateParam = Maps.newHashMap();
        channelPoiInfo.setOutSystemId(channelPoiInfo.getStationNo());

        updateParam.put(ProjectConstant.STATION_NO, channelPoiInfo.getStationNo());
        updateParam.put(ProjectConstant.OPERATOR, "admin_ocms");
        updateParam.put(ProjectConstant.OUT_SYSTEM_ID, channelPoiInfo.getStationNo());
        // 限频检查
        if (org.apache.commons.lang3.StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.UPDATE_STORE_INFO, String.valueOf(tenantId))) {
            log.warn("更新京东信息 获取令牌失败 不阻塞流程 直接调用接口，request {}", updateParam);
        }
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setTenantId(tenantId);
        baseRequest.setChannelId(channelId);
        baseRequest.setAppId(Long.valueOf(appId));
        Map updateMap = jddjChannelGateService.sendPostApp(bastUrl + ChannelPostJDDJEnum.UPDATE_STORE_INFO.getUrl(), null
                , baseRequest, updateParam);

        if (Integer.parseInt((String) updateMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
            resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "补充京东门店编码失败"));
        }
        String updateResult = (String) updateMap.get(ProjectConstant.DATA);
        Integer updateCode = JSON.parseObject(updateResult).getInteger(ProjectConstant.CODE);
        if (updateCode != 0) {
            resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "补充京东门店编码失败"));
        }
    }
    private void updateAppPoiCode(String appId, long tenantId, int channelId,  ChannelPoiInfo channelPoiInfo) {
        Map updateParam = Maps.newHashMap();
        channelPoiInfo.setOutSystemId(channelPoiInfo.getStationNo());

        updateParam.put(ProjectConstant.STATION_NO, channelPoiInfo.getStationNo());
        updateParam.put(ProjectConstant.OPERATOR, "admin_ocms");
        updateParam.put(ProjectConstant.OUT_SYSTEM_ID, channelPoiInfo.getStationNo());
        // 限频检查
        if (org.apache.commons.lang3.StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.UPDATE_STORE_INFO, String.valueOf(tenantId))) {
            log.warn("更新京东信息 获取令牌失败 不阻塞流程 直接调用接口，request {}", updateParam);
        }
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setTenantId(tenantId);
        baseRequest.setChannelId(channelId);
        baseRequest.setAppId(Long.valueOf(appId));
        Map updateMap = jddjChannelGateService.sendPostApp(bastUrl + ChannelPostJDDJEnum.UPDATE_STORE_INFO.getUrl(), null
                , baseRequest, updateParam);

        if (Integer.parseInt((String) updateMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
            throw new RuntimeException("补充京东门店编码失败：" + appId + ",poi:" + channelPoiInfo.getStationNo());
        }
        String updateResult = (String) updateMap.get(ProjectConstant.DATA);
        Integer updateCode = JSON.parseObject(updateResult).getInteger(ProjectConstant.CODE);
        if (updateCode != 0) {
            throw new RuntimeException("补充京东门店编码失败：" + appId + ",poi:" + channelPoiInfo.getStationNo());
        }
    }

    /**
     * 限频控制
     * @param appId
     * @param postEnum
     * @param isAsync
     */
    private void rateLimitManage(String appId, ChannelPostJDDJEnum postEnum, Boolean isAsync) {
        if (org.apache.commons.lang3.StringUtils.isBlank(appId)) {
            return;
        }
        long waitTime = clusterRateLimiter.tryAcquire(postEnum, appId, isAsync);
        // 同步调用出现限频时，不进行限频优化
        if (waitTime != 0 && !isAsync) {
            log.warn("JddjChannelPoiService appId:{} url:{} 获取令牌失败 不阻塞流程 直接调用接口", appId, postEnum.getUrl());
            return;
        }
        if (waitTime !=0) {
            if (RHINO_UPTIMATE_SET.contains(postEnum)) {
                log.info("JddjChannelPoiService appId:{} url:{} 获取令牌失败 抛出异常", appId, postEnum.getUrl());
                throw new InvokeChannelTooMuchException(waitTime);
            } else {
                log.warn("JddjChannelPoiService appId:{} url:{} 获取令牌失败 不阻塞流程 直接调用接口", appId, postEnum.getUrl());
            }
        }
    }
}
