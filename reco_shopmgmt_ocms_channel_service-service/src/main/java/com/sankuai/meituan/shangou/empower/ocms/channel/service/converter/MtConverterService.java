package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.BatchVideoUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoBindRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.AreaInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QualificationPicturesInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordination;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.PoiShippingAreaInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.PoiShippingInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RefundSku;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.*;

/**
 * 美团实体转换统一接口类
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:09
 **/

@Mapper(componentModel = "spring", uses = {MappingConverterUtils.class}, imports = {
        SpuFoodDataInfo.class,ChannelSpuInfoDTO.class,
        ConverterUtils.class,SkuPriceInfo.class,SkuStockInfo.class,ChannelStoreCategory.class,
        ChannelSkuCreateDTO.class,SkuFoodDataInfo.class, MoneyUtils.class,
        ChannelStatusConvertUtil.class, StringUtils.class,DateUtils.class,
        BigDecimal.class, Objects.class, MtConverterUtil.class, BooleanUtils.class,
        Map.Entry.class, SpuPriceInfo.class, StructAttrTemplateDTO.class})
public interface MtConverterService {


    @Mappings({
            @Mapping(target = "img_data", source = "pictureData"),
            @Mapping(target = "img_name", source = "pictureName"),
    })
    ChannelPictureUploadDTO pictureUploadMapping(PictureUploadDTO param);

    @Mappings({
            @Mapping(target = "app_food_code", source = "skuId"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "price", expression = "java(param.isSetPrice()?Double.valueOf(param.getPrice()).floatValue():null)"),
            @Mapping(target = "min_order_count", source = "minPurchaseQuantity"),
            @Mapping(target = "unit", source = "unit"),
            @Mapping(target = "box_num", expression = "java(null)"),
            @Mapping(target = "box_price", expression = "java(null)"),
            @Mapping(target = "category_code", expression = "java(MtConverterUtil.convert2MTCategoryCode(param))"),
            @Mapping(target = "category_name", expression = "java(MtConverterUtil.convert2MTCategoryName(param))"),
            @Mapping(target = "is_sold_out", expression = "java(ChannelSkuCreateDTO.mapIs_sold_out(param.getSkuStatus()))"),
            @Mapping(target = "picture", source = "pictures"),
            @Mapping(target = "tag_id", expression = "java(ChannelSkuCreateDTO.toTagId(param.getCategoryFirst(), param.getCategorySecond(), param.getCategory()))"),
            @Mapping(target = "zh_name", source = "brandName"),
            @Mapping(target = "origin_name", source = "productionArea"),
            @Mapping(target = "description", source = "description"),
            @Mapping(target = "properties", source = "properties"),
            @Mapping(target = "is_specialty", source = "isSpecialty"),
            @Mapping(target = "category_code_list", expression = "java(MtConverterUtil.convert2MTCategory_code_list(param.getLeafStoreCategoryList()))"),
            @Mapping(target = "category_name_list", expression = "java(MtConverterUtil.convert2MTCategory_name_list(param.getLeafStoreCategoryList()))"),
            @Mapping(target = "product_name_supplement", source = "nameSupplement"),
            @Mapping(target = "product_name_supplement_seq", source = "nameSupplementSeq")
    })
    ChannelSkuCreateDTO skuCreateMapping(SkuInfoDTO param);

    @Mappings({
            @Mapping(target = "sku_id", expression = "java(StringUtils.isBlank(param.getChannelSkuId()) ? param.getSkuId() : param.getChannelSkuId())"),
            @Mapping(target = "spec", source = "spec"),
            @Mapping(target = "price", expression = "java(param.isSetPrice()?String.valueOf(param.getPrice()):null)"),
            @Mapping(target = "stock", expression = "java(param.isSetStock()?String.valueOf(param.getStock()):null)"),
            @Mapping(target = "upc", expression = "java(MtConverterUtil.convertUpc(param.upc))"),
            @Mapping(target = "weight",  expression = "java(param.isSetWeight()?Double.valueOf(param.getWeight()).intValue():null)"),
            @Mapping(target = "box_num", expression = "java(null)"),
            @Mapping(target = "box_price", expression = "java(null)"),
            @Mapping(target = "available_times", expression = "java(MtConverterUtil.convertStringToMapForAvailableTimes(param.getAvailableTimes()))"),
    })
    ChannelSkuInfoDTO skuInfoMapping(SkuInfoDTO param);

    @Mappings({
            @Mapping(target = "customSkuId", source = "app_food_code"),
            @Mapping(target = "skuId", expression = "java(skuCreateDTO.getSkus().size() > 0 ? skuCreateDTO.getSkus().get(0).getSku_id() : null)"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "minPurchaseQuantity", source = "min_order_count"),
            @Mapping(target = "unit", source = "unit"),
            //注意：此处不做逻辑处理
            @Mapping(target = "spec", expression = "java(skuCreateDTO.getSkus().size() > 0 ? skuCreateDTO.getSkus().get(0).getSpec() : null)"),
            @Mapping(target = "weight", expression = "java(skuCreateDTO.getSkus().size() > 0 ? skuCreateDTO.getSkus().get(0).getWeight() : null)"),
            @Mapping(target = "upc", expression = "java(skuCreateDTO.getSkus().size() > 0 ? skuCreateDTO.getSkus().get(0).getUpc() : null)"),
            @Mapping(target = "stock", expression = "java(skuCreateDTO.getSkus().size() > 0 ? Double.valueOf(StringUtils.isBlank(skuCreateDTO.getSkus().get(0).getStock()) || skuCreateDTO.getSkus().get(0).getStock().equalsIgnoreCase(\"*\") ? \"99999\" : skuCreateDTO.getSkus().get(0).getStock()).intValue() : null)"),
            @Mapping(target = "price", expression = "java(skuCreateDTO.getSkus().size() > 0 ? Double.valueOf(skuCreateDTO.getSkus().get(0).getPrice()) : null)"),
            // 代码处理 boxQuantity 和 boxPrice 赋值映射
            //@Mapping(target = "boxQuantity", expression = "java(skuCreateDTO.getSkus().size() > 0 ? (StringUtils.isBlank(skuCreateDTO.getSkus().get(0).getBox_num()) ? 1 : Double.valueOf(skuCreateDTO.getSkus().get(0).getBox_num()).intValue()) : null)"),
            //@Mapping(target = "boxPrice", expression = "java(skuCreateDTO.getSkus().size() > 0 ? (StringUtils.isBlank(skuCreateDTO.getSkus().get(0).getBox_price()) ? 0.0D : Double.valueOf(skuCreateDTO.getSkus().get(0).getBox_price())) : null)"),
            @Mapping(target = "sourceType", constant = "3"),
            @Mapping(target = "skuStatus", expression = "java(skuCreateDTO.getIs_sold_out() == 0 ? 1 : 2 )"),
            @Mapping(target = "frontCategory", source = "category_code"),
            @Mapping(target = "frontCategoryName", source = "category_name"),
            @Mapping(target = "channelFrontCategory", source = "category_code"),
            @Mapping(target = "channelFrontCategoryName", source = "category_name"),
            @Mapping(target = "pictures", expression = "java(MappingConverterUtils.stringAsList(skuCreateDTO.getPicture()))"),
            @Mapping(target = "brandName", source = "zh_name"),
            @Mapping(target = "productionArea", source = "origin_name"),
            @Mapping(target = "category", source = "tag_id"),
            @Mapping(target = "sequence", source = "sequence"),
            @Mapping(target = "isSp", source = "isSp"),
            @Mapping(target = "categoryList", source = "category_list"),
            // 1.7.0新增
            @Mapping(target = "availableTimes", expression = "java(skuCreateDTO.getSkus().size() > 0 ? MtConverterUtil.convertMapToStringForAvailableTimes(skuCreateDTO.getSkus().get(0).getAvailable_times()) : null)"),
            @Mapping(target = "description", source = "description"),
            @Mapping(target = "flavour", source = "flavour"),
            @Mapping(target = "productName", source = "product_name"),
            @Mapping(target = "isSpecialty", source = "is_specialty"),
            @Mapping(target = "videoId", source = "video_id"),
            @Mapping(target = "videoUrlMp4", source = "video_url_mp4"),
            @Mapping(target = "properties", source = "properties"),
            @Mapping(target = "auditStatus", source = "audit_status"),
            @Mapping(target = "isComplete", source = "is_complete"),
            @Mapping(target = "nameSupplement", source = "product_name_supplement"),
            @Mapping(target = "nameSupplementSeq", source = "product_name_supplement_seq"),
    })
    SkuInfoDTO skuCreateDTOMapping(ChannelSkuCreateDTO skuCreateDTO);
    List<SkuInfoDTO> skuCreateDTOsMapping(List<ChannelSkuCreateDTO> skuCreateDTOs);
    @Mappings({
            @Mapping(target = "firstCategoryCode", source = "category_code"),
            @Mapping(target = "firstCategoryName", source = "category_name"),
            @Mapping(target = "secondaryCategoryCode", source = "secondary_category_code"),
            @Mapping(target = "secondaryCategoryName", source = "secondary_category_name"),
    })
    ChannelStoreCategory channelShopCategoryDTOMapping(ChannelShopCategoryDTO data);
    List<ChannelStoreCategory> channelShopCategoryDTOMapping(List<ChannelShopCategoryDTO> data);

    @Mappings({
            @Mapping(target = "catId", source = "categoryId"),
            @Mapping(target = "name", source = "categoryName"),
            @Mapping(target = "sequence", source = "sequence"),
            @Mapping(target = "depth", source = "depth"),
            @Mapping(target = "parentId", source = "parentId"),
            @Mapping(target = "smartSort", source = "smartSort"),
            @Mapping(target = "customCatId", source = "customCategoryId"),
    })
    CatInfo channelStoreCategoryDTOMapping(ChannelStoreCategoryDTO data);
    List<CatInfo> channelStoreCategoryDTOMapping(List<ChannelStoreCategoryDTO> data);

    @Mappings({
            @Mapping(target = "app_food_code", source = "skuId"),
            @Mapping(target = "upc_code", source = "upc"),
            @Mapping(target = "min_order_count", source = "minPurchaseQuantity"),
            @Mapping(target = "category_code", expression = "java(MtConverterUtil.convert2MTCategoryCode(param))"),
            @Mapping(target = "category_name", expression = "java(MtConverterUtil.convert2MTCategoryName(param))"),
            @Mapping(target = "is_sold_out", expression = "java(ChannelSkuCreateDTO.mapIs_sold_out(param.getSkuStatus()))"),
            @Mapping(target = "picture", source = "pictures"),
            @Mapping(target = "description", source = "description"),
            @Mapping(target = "properties", source = "properties"),
            @Mapping(target = "is_specialty", source = "isSpecialty"),
            @Mapping(target = "category_code_list", expression = "java(MtConverterUtil.convert2MTCategory_code_list(param.getLeafStoreCategoryList()))"),
            @Mapping(target = "category_name_list", expression = "java(MtConverterUtil.convert2MTCategory_name_list(param.getLeafStoreCategoryList()))"),
    })
    ChannelUpcCreateDTO upcCreateMapping(SkuInfoDTO param);

    @Mappings({
            @Mapping(target = "sku_id", expression = "java(StringUtils.isBlank(param.getChannelSkuId()) ? param.getSkuId() : param.getChannelSkuId())"),
            @Mapping(target = "price", expression = "java(param.isSetPrice()?String.valueOf(param.getPrice()):null)"),
            @Mapping(target = "stock", expression = "java(param.isSetStock()?param.getStock():null)"),
            @Mapping(target = "box_num", expression = "java(null)"),
            @Mapping(target = "box_price", expression = "java(null)"),
            @Mapping(target = "available_times", expression = "java(MtConverterUtil.convertStringToMapForAvailableTimes(param.getAvailableTimes()))"),
    })
    ChannelStandardSku standardSkuMapping(SkuInfoDTO param);

    @Mappings({
            @Mapping(target = "sku_id", source = "skuId"),
            @Mapping(target = "spec", source = "spec"),
            @Mapping(target = "upc", source = "upc"),
            @Mapping(target = "price", source = "price"),
            @Mapping(target = "stock", source = "stock"),
            @Mapping(target = "weight", source = "weight"),
            @Mapping(target = "box_num", source = "boxQuantity"),
            @Mapping(target = "box_price", source = "boxPrice"),
            @Mapping(target = "available_times", expression = "java(MtConverterUtil.convertStringToMapForAvailableTimes(param.getAvailableTimes()))"),
    })
    ChannelUnstandardSku unStandardSkuMapping(SkuInfoDTO param);

    /**
     * 美团删除分类 code和name二选一 优先有code 不使用字段需指定为null 否则验签失败
     * @param data
     * @return
     */
    @Mappings({
            @Mapping(target = "category_code", expression = "java(StringUtils.isNotBlank(data.getChannelCategoryCode())?data.getChannelCategoryCode():null)"),
            @Mapping(target = "category_name", expression = "java(StringUtils.isNotBlank(data.getChannelCategoryCode())?null:data.getChannelCategoryName())"),
            @Mapping(target = "move_product_to_uncate", expression = "java(data.isSetForceDelete()?data.getForceDelete():null)")
            })
    ChannelCategoryDTO deleteCategory(CategoryInfoDeleteDTO data);

    @Mappings({
            @Mapping(target = "category_code_origin", source = "channelCategoryCode"),
            @Mapping(target = "category_name", source = "name"),
            @Mapping(target = "category_code", source = "channelCategoryCode"),
            @Mapping(target = "sequence", expression = "java(ConverterUtils.sortConvert(data.getSort()))"),//source = "sort"),
    })
    ChannelCategoryDTO sortCategory(CategoryInfoSortItemDTO data);
    List<ChannelCategoryDTO> sortCategory(List<CategoryInfoSortItemDTO> data);

    @Mappings({
            @Mapping(target = "appPoiCode", source = "app_poi_code"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "address", source = "address"),
            @Mapping(target = "longitude", source = "longitude"),
            @Mapping(target = "latitude", source = "latitude"),
            @Mapping(target = "picUrl", source = "pic_url"),
            @Mapping(target = "invoiceSupport", source = "invoice_support"),
            @Mapping(target = "openLevel", source = "open_level"),
            @Mapping(target = "promotionInfo", source = "promotion_info"),
            @Mapping(target = "closeTime", expression = "java(MtConverterUtil.getLatestCloseTime(channelPoiInfo.getShipping_time()))"),
            @Mapping(target = "shippingTime", source = "shipping_time"),
            @Mapping(target = "isOnline", source = "is_online"),
            @Mapping(target = "preBook", source = "pre_book"),
            @Mapping(target = "preBookMinDays", source = "pre_book_min_days"),
            @Mapping(target = "preBookMaxDays", source = "pre_book_max_days"),
            @Mapping(target = "hotline", source = "phone"),
            @Mapping(target = "originBrandId", source = "origin_brand_id")
    })
    PoiInfo poiInfoMapping(ChannelPoiInfo channelPoiInfo);

    List<PoiInfo> poiInfoListMapping(List<ChannelPoiInfo> channelPoiInfoList);

    @Mappings({
            @Mapping(target = "app_food_code", source = "skuId"),
            @Mapping(target = "is_delete_retail_cat", constant = "2")
    })
    ChannelSkuDeleteDTO skuDeleteMapping(SkuInfoDeleteDTO skuInfoDeleteDTO);

    @Mappings({
            @Mapping(target = "app_food_code", source = "skuId"),
            @Mapping(target = "skus", expression = "java(SkuPriceInfo.toSkus(skuPriceDTO))")
    })
    SpuPriceInfo updatePrice(SkuPriceDTO skuPriceDTO);

    List<SpuPriceInfo> updatePrice(List<SkuPriceDTO> skuPriceDTO);

    @Mappings({
            @Mapping(target = "food_data", source = "request")
    })
    ChannelSkuUpdateDTO updatePrice(String request);

    @Mappings({
            @Mapping(target = "app_food_code", source = "skuId"),
            @Mapping(target = "skus", expression = "java(SkuPriceInfo.toSkus(skuPriceDTO))")
    })
    SpuPriceInfo updatePrice(SkuPriceMultiChannelDTO skuPriceDTO);

    List<SpuPriceInfo> updatePriceM(List<SkuPriceMultiChannelDTO> skuPriceDTO);



    @Mappings({
            @Mapping(target = "app_food_code", expression = "java(data.getKey())"),
            @Mapping(target = "skus", expression = "java(SpuPriceInfo.toSkus(data.getValue()))"),
    })
    SpuPriceInfo convertSpuPriceInfo(Map.Entry<String, List<SkuPriceMultiChannelDTO>> data);

    List<SpuPriceInfo> convertSpuPriceInfos(List<Map.Entry<String, List<SkuPriceMultiChannelDTO>>> datas);



    @Mappings({
            @Mapping(target = "category_name_origin", source = "name"),
            @Mapping(target = "category_name", source = "name"),
            @Mapping(target = "category_code", source = "channelCode"),
            @Mapping(target = "target_level", constant = "2"),
            @Mapping(target = "target_parent_name", source = "parentName"),
    })
    ChannelCategoryDTO degradeCategory(CategoryInfoDegradeDTO data);

    @Mappings({
            @Mapping(target = "app_food_code", source = "skuId"),
            @Mapping(target = "skus", expression = "java(SkuStockInfo.toSkus(skuStockDTO))")
    })
    SpuStockInfo updateStock(SkuStockDTO skuStockDTO);
    List<SpuStockInfo> updateStock(List<SkuStockDTO> data);


    @Mappings({
            @Mapping(target = "app_food_code", source = "skuId"),
            @Mapping(target = "skus", expression = "java(SkuStockInfo.toSkus(skuStockDTO))")
    })
    SpuStockInfo updateStock(SkuStockMultiChannelDTO skuStockDTO);
    List<SpuStockInfo> updateStockM(List<SkuStockMultiChannelDTO> skuStockDTO);


    @Mappings({
        @Mapping(source = "order_id", target = "channelOrderId"),
        @Mapping(expression = "java(MoneyUtils.yuanToFen(channelOrderDetail.getTotal()))", target = "actualPayAmt"),
        @Mapping(expression = "java(MoneyUtils.yuanToFen(channelOrderDetail.getOriginal_price()))", target = "originalAmt"),
        @Mapping(expression = "java(MoneyUtils.yuanToFen(channelOrderDetail.getShipping_fee()))", target = "freight"),
        @Mapping(expression = "java(MtConverterUtil.orderCreateTime(channelOrderDetail))", target = "createTime"),
        @Mapping(expression = "java(ConverterUtils.codeToBool(channelOrderDetail.getHas_invoiced()))", target = "isNeedInvoice"),
        @Mapping(source = "city_id", target = "cityId"),
        @Mapping(source = "pay_type", target = "payType"),
        @Mapping(source = "pay_status", target = "payStatus"),
        @Mapping(source = "operator", target = "operator"),
        @Mapping(source = "day_seq", target = "orderSerialNumber"),
        @Mapping(source = "package_bag_money", target = "packageAmt"),
        @Mapping(source = "caution", target = "comment"),
        @Mapping(source = "wm_poi_name", target = "channelStoreName"),
        @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getOrder_confirm_time()))", target = "confirmTime"),
        @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getOrder_cancel_time()))", target = "cancelTime"),
        @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getOrder_completed_time()))", target = "completedTime"),
        @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getLogistics_fetch_time()))", target = "logisticFetchTime"),
        @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getLogistics_completed_time()))", target = "logisticCompletedTime"),
        @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getOrder_send_time()))", target = "payTime"),
        @Mapping(target = "dispatcherName", source = "logistics_dispatcher_name"),
        @Mapping(target = "dispatcherPhone", source = "logistics_dispatcher_mobile"),
        @Mapping(target = "downFlag", expression = "java(channelOrderDetail.getIncmp_code() == -1 ? 1 : 0)"),
        @Mapping(target = "degradeModules", source = "incmp_modules"),
        @Mapping(target = "positioningAddress", source = "positioning_address"),
        @Mapping(target = "openUserId", source = "openUid"),
        @Mapping(target = "newComment", source = "comment"),
        @Mapping(expression = "java(MoneyUtils.yuanToFenOrDefault(channelOrderDetail.getAcceleration_fee()))", target = "fastDeliveryAmt"),
        @Mapping(target = "addressChangeFee", source = "address_change_fee")

    })
    ChannelOrderDetailDTO channelOrderDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
        @Mapping(source = "sku_id", target = "skuId"),
        @Mapping(source = "food_name", target = "skuName"),
            @Mapping(source = "spec", target = "specification"),
            @Mapping(source = "app_food_code", target = "customSpu"),
            @Mapping(source = "quantity", target = "quantity"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderSkuDetail.getActual_price()))", target = "salePrice"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderSkuDetail.getOriginal_price()))", target = "originalPrice"),
            @Mapping(source = "box_num", target = "packageCount"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderSkuDetail.getBox_price()))", target = "packagePrice"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderSkuDetail.getBox_price()) * orderSkuDetail.getBox_num())", target = "packageFee"),
            @Mapping(source = "unit", target = "unit"),
            @Mapping(source = "food_property", target = "skuProperty"),
            @Mapping(source = "weight", target = "weight"),
            @Mapping(source = "upc", target = "upcCode"),
            @Mapping(source = "item_id", target = "itemId"),
            @Mapping(expression = "java(MtBrandConverterUtil.convertProductLabelList(orderSkuDetail.getLabel_list()))", target = "channelLabelList"),
            @Mapping(expression = "java(MtBrandConverterUtil.convertProductPickingDateRange(orderSkuDetail.getProduct_picking_date_range()))", target = "productPickingDateRange"),
            @Mapping(source = "is_consignment", target = "consignmentProduct"),
            @Mapping(source = "consignment_agent_id", target = "consignmentAgentId")
    })
    OrderProductDetailDTO orderSkuDetailMapping(OrderSkuDetail orderSkuDetail);
    List<OrderProductDetailDTO> orderSkuDetailListMapping(List<OrderSkuDetail> orderSkuDetailList);

    @Mappings({
            @Mapping(source = "act_detail_id", target = "activityId"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderActivitiesInfo.getReduce_fee()))", target = "actDiscount"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderActivitiesInfo.getMt_charge()))", target = "channelCharge"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderActivitiesInfo.getPoi_charge()))", target = "bizCharge"),
            @Mapping(source = "remark", target = "remark"),
            @Mapping(target = "giftInfo", expression = "java(convertChannelGiftInfo(orderActivitiesInfo.getAct_extend_msg()))"),
            @Mapping(expression = "java(Objects.nonNull(orderActivitiesInfo.getType()) ? String.valueOf(orderActivitiesInfo.getType()) : null)", target = "type"),
            @Mapping(source = "item_id", target = "itemId")  // 闪购开放平台文档里并没有这个字段
    })
    OrderDiscountDetailDTO orderActivitieInfoMapping(OrderActivitiesInfo orderActivitiesInfo);
    List<OrderDiscountDetailDTO> orderActivitieInfoListMapping(List<OrderActivitiesInfo> orderActivitiesInfoList);

    @Mappings({
            @Mapping(source = "sku_id", target = "mainSkuId"),
            @Mapping(source = "gifts_sku_id", target = "skuId"),
            @Mapping(source = "gifts_name", target = "name"),
            @Mapping(source = "gift_num", target = "quantity"),
            @Mapping(source = "gifts_app_food_code", target = "spu")
    })
    ChannelGiftInfo convertChannelGiftInfo(ActExtendMsg actExtendMsg);
    List<ChannelGiftInfo> convertChannelGiftInfos(List<ActExtendMsg> actExtendMsgList);

    @Mappings({
            @Mapping(source = "gifts_sku_id", target = "skuId"),
            @Mapping(source = "gifts_app_food_code", target = "customSpu"),
            @Mapping(source = "gifts_name", target = "skuName"),
            @Mapping(source = "gift_num", target = "quantity"),
            @Mapping(target = "itemType", constant = "1"),
            @Mapping(target = "salePrice", constant = "0")
    })
    OrderProductDetailDTO orderGiftInfoMapping(ActExtendMsg actExtendMsg);
    List<OrderProductDetailDTO> orderGiftInfoListMapping(List<ActExtendMsg> actExtendMsgList);

    @Mappings({
            @Mapping(expression = "java(MtConverterUtil.splitRecipientAddress(channelOrderDetail.getRecipient_address()))", target = "userAddress"),
            @Mapping(source = "recipient_phone", target = "userPhone"),
            @Mapping(source = "recipient_name", target = "userName"),
            @Mapping(expression = "java((channelOrderDetail.getEstimate_arrival_time() > 0 ? channelOrderDetail.getEstimate_arrival_time() : channelOrderDetail.getDelivery_time()) * 1000)", target = "arrivalTime"),
            @Mapping(expression = "java((channelOrderDetail.getEstimate_arrival_time() > 0 ? channelOrderDetail.getEstimate_arrival_time() : channelOrderDetail.getDelivery_time()) * 1000)", target = "arrivalEndTime"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.mtDeliveryMethodMapping(channelOrderDetail.getLogistics_code(), channelOrderDetail.getPick_type()))", target = "deliveryMethod"),
            @Mapping(expression = "java(!StringUtils.contains(channelOrderDetail.getRecipient_phone(), \"*\"))", target = "userPhoneIsValid"),
            @Mapping(source = "latitude", target = "latitude"),
            @Mapping(source = "longitude", target = "longitude"),
            @Mapping(expression = "java(MtConverterUtil.isSelfDelivery(channelOrderDetail.getLogistics_code()))", target = "isSelfDelivery"),
            @Mapping(expression = "java(MtConverterUtil.convertDeliveryType(channelOrderDetail.getLogistics_code()))", target = "originalDeliveryType"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.mtSelfDeliveryCode(channelOrderDetail.getPick_type(),channelOrderDetail.getDay_seq()))", target = "selfFetchCode"),
            @Mapping(expression = "java(Objects.nonNull(channelOrderDetail.getSuggest_pick_time()) &&  channelOrderDetail.getSuggest_pick_time() > 0 ?  channelOrderDetail.getSuggest_pick_time() * 60000 : 0)", target = "fastDeliveryPickUpTime"),
    })
    OrderDeliveryDetailDTO deliveryDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
        @Mapping(source = "invoice_title", target = "invoiceTitle"),
        @Mapping(source = "taxpayer_id", target = "taxNo")
    })
    OrderInvoiceDetailDTO invoiceDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "food_data",expression = "java(SkuFoodDataInfo.toFoodDatas(data.getSkuId()))"),
            @Mapping(target = "sell_status",expression = "java(ChannelSkuCreateDTO.mapIs_sold_out(data.getSkuStatus()))"),
    })
    ChannelSkuSellStatusDTO updateSkuSellStatus(SkuSellStatusInfoDTO data);

    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "sku_id",source = "skuId"),
            @Mapping(target = "app_food_code",source = "customSkuId"),
            @Mapping(target = "name", source = "skuName"),
            @Mapping(target = "spec", source = "spec"),
            @Mapping(target = "category_name", source = "categoryName"),
    })
    UpdateCustomSkuId updateCustomSkuId(UpdateCustomSkuIdDTO data);

    @Mappings({
            @Mapping(target = "order_id", source = "orderId"),
            @Mapping(target = "reason", source = "reason"),
    })
    ChannelOrderCancelRelDTO agreeRefund(AgreeRefundRequest request);


    @Mappings({
            @Mapping(target = "order_id", source = "orderId"),
            @Mapping(target = "reason", source = "reason"),
    })
    ChannelOrderCancelRelDTO rejectRefund(RejectRefundRequest request);


    @Mappings({
            @Mapping(target = "category_name_origin", source = "name"),
            @Mapping(target = "category_name", source = "name"),
            @Mapping(target = "category_code", source = "channelCode"),
            @Mapping(target = "target_level", constant = "1"),
    })
    ChannelCategoryDTO adjustCategoryLevel1(CategoryLevelAdjustDTO data);

    @Mappings({
            @Mapping(target = "category_name_origin", source = "name"),
            @Mapping(target = "category_name", source = "name"),
            @Mapping(target = "category_code", source = "channelCode"),
            @Mapping(target = "target_level", constant = "2"),
            @Mapping(target = "target_parent_name", source = "parentName"),
    })
    ChannelCategoryDTO adjustCategoryLevel2(CategoryLevelAdjustDTO data);

    @Mappings({
        @Mapping(target = "skuName", source = "food_name"),
        @Mapping(target = "sku", source = "sku_id"),
        @Mapping(target = "customSpuId", source = "app_food_code"),
        @Mapping(target = "upc", source = "upc"),
        @Mapping(target = "count", source = "count"),
        @Mapping(target = "skuRefundAmount", expression = "java(channelPartRefundSkuInfo.getCount()==0 || channelPartRefundSkuInfo.getPart_refund_type() == 5 ?MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getRefund_price()):MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getRefund_price() * channelPartRefundSkuInfo.getCount()))"),//商品退款金额是单价，需要转成退款总价
        @Mapping(target = "foodPrice", expression = "java(MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getFood_price()))"),
        @Mapping(target = "boxPrice", expression = "java(MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getBox_price()))"),
        @Mapping(target = "boxNum", source = "box_num"),
        @Mapping(target = "refundWeight",source ="refunded_weight" ),
            @Mapping(target = "partRefundType",source = "part_refund_type"),
        @Mapping(target = "itemId",source = "item_id")
    })
    RefundSku partRefundSkuInfo(ChannelPartRefundSkuInfo channelPartRefundSkuInfo);
    List<RefundSku> partRefundSkuInfoList(List<ChannelPartRefundSkuInfo> channelPartRefundSkuInfoList);

    @Mappings({
        @Mapping(target = "orderId", source = "order_id"),
        @Mapping(target = "realPhoneNumber", source = "real_phone_number"),
        @Mapping(target = "daySeq", source = "day_seq")
    })
    BatchPullPhoneDTO pullPhoneInfo(PullPhoneInfo pullPhoneInfo);
    List<BatchPullPhoneDTO> pullPhoneInfoList(List<PullPhoneInfo> pullPhoneInfoList);

    @Mappings({
        @Mapping(target = "app_food_code", source = "skuId"),
        @Mapping(target = "user_type", expression = "java(channelActivityInfo.getUserType().getValue())"),//枚举
        @Mapping(target = "start_time", source = "startTime"),
        @Mapping(target = "end_time", source = "endTime"),
        @Mapping(target = "order_limit", source = "orderLimit"),
        @Mapping(target = "day_limit", source = "dayLimit"),
        @Mapping(target = "period", source = "period"),
        @Mapping(target = "weeks_time", source = "weeksTime"),
        @Mapping(target = "setting_type", expression = "java(channelActivityInfo.getSettingType().getValue())"),//枚举
        @Mapping(target = "act_price", expression = "java(MoneyUtils.fenToYuan(channelActivityInfo.getPromotionPrice()).doubleValue())"),//分转元
        @Mapping(target = "discount_coefficient", source = "discountCoefficient"),
        @Mapping(target = "sequence", source = "sequence"),//不需要
        @Mapping(target = "item_id", source = "channelActivityId")//编辑时有值
    })
    ChannelActDataInfo channelActDataInfo(ChannelActivityInfo channelActivityInfo);
    List<ChannelActDataInfo> channelActDataInfoList(List<ChannelActivityInfo> channelActivityInfoList);

    @Mappings({
        @Mapping(source = "app_food_code", target = "skuId"),
        @Mapping(source = "user_type", target = "userType"),
        @Mapping(source = "start_time", target = "startTime"),
        @Mapping(source = "end_time", target = "endTime"),
        @Mapping(source = "order_limit", target = "orderLimit"),
        @Mapping(source = "day_limit", target = "dayLimit"),
        @Mapping(source = "period", target = "period"),
        @Mapping(source = "weeks_time", target = "weeksTime"),
        @Mapping(source = "setting_type", target = "settingType"),
        @Mapping(source = "act_price", target = "actPrice"),
        @Mapping(source = "discount_coefficient", target = "discountCoefficient"),
        @Mapping(source = "sequence", target = "sequence"),
        @Mapping(source = "item_id", target = "channelActivityId"),
        @Mapping(source = "status", target = "status"),
        @Mapping(source = "sku_id", target = "realSkuId")
    })
    ChannelActDataInfoDTO channelActDataInfoMapping(ChannelActDataInfo channelActDataInfo);
    List<ChannelActDataInfoDTO> channelActDataInfoListMapping(List<ChannelActDataInfo> channelActDataInfoList);


    @Mappings({
            @Mapping(source = "app_food_code", target = "customSpuId"),
            @Mapping(source = "type_name", target = "activityName"),
            @Mapping(source = "start_time", target = "startTime"),
            @Mapping(source = "end_time", target = "endTime"),
            @Mapping(source = "type", target = "activityType"),
            @Mapping(source = "status", target = "activityStatus"),
            @Mapping(source = "act_id", target = "activityId"),
            @Mapping(source = "can_modify_price", target = "isCanModifyPrice"),
    })
    SkuActivityInfo channelSkuActDataInfoMapping(ChannelSkuActivityDetail channelActDataInfo);
    List<SkuActivityInfo> channelSkuActDataInfoListMapping(List<ChannelSkuActivityDetail> channelActDataInfoList);

    @Mappings({
            @Mapping(source = "id", target = "catId"),
            @Mapping(source = "name", target = "name"),
            @Mapping(source = "level", target = "depth")
    })
    CatInfo channelCatInfoMapping(ChannelCatInfo channelCatInfo);
    List<CatInfo> channelCatInfosMapping(List<ChannelCatInfo> channelCatInfos);

    AttrOptionInfo attrOptionMapping(CategoryAttrOptionInfo categoryAttrOptionInfo);
    List<AttrOptionInfo> attrOptionsMapping(List<AttrOptionInfo> categoryAttrOptionInfos);


    @Mappings({
            @Mapping(source = "value_id", target = "valueId"),
            @Mapping(source = "value", target = "value"),
            @Mapping(source = "value_id_path", target = "valueIdPath"),
            @Mapping(source = "value_path", target = "valuePath")
    })
    AttrValueInfo attrValueMapping(CategoryAttrValueInfo attrValueInfo);
    List<AttrValueInfo> attrValuesMapping(List<CategoryAttrValueInfo> attrValueInfos);
    @Mappings({
            @Mapping(source = "attr_id", target = "attrId"),
            @Mapping(source = "attr_name", target = "attrName"),
            @Mapping(source = "attr_value_type", target = "attrValueType"),
            @Mapping(source = "character_type", target = "characterType"),
            @Mapping(source = "text_max_length", target = "textMaxLength"),
            @Mapping(source = "support_extend", target = "supportExtend"),
            @Mapping(source = "value_list", target = "valueList"),
            @Mapping(target = "structAttrTemplate", expression = "java(StructAttrTemplateDTO.getStructAttrTemplate(attrInfo))")
    })
    CategoryAttrInfo channelCategoryAttrMapping(ChannelCategoryAttrInfo attrInfo);

    List<CategoryAttrInfo> channelCategoryAttrsMapping(List<ChannelCategoryAttrInfo> channelCategoryAttrInfoList);

    @Mappings({
            @Mapping(source = "value_id", target = "valueId"),
            @Mapping(source = "value_name", target = "valueName"),
            @Mapping(expression = "java(BooleanUtils.isTrue(attrValueInfo.getIs_by_default()) ? true : false)", target = "byDefault"),
    })
    RuleValue ruleValueMapping(ProductRuleValueInfo attrValueInfo);
    List<RuleValue> ruleValueMapping(List<ProductRuleValueInfo> attrValueInfos);
    @Mappings({
            @Mapping(source = "rule_id", target = "ruleId"),
            @Mapping(source = "rule_name", target = "ruleName"),
            @Mapping(source = "value_list", target = "valueList"),
    })
    ProductRule productRuleMapping(ProductRuleInfo attrInfo);

    List<ProductRule> productRuleMapping(List<ProductRuleInfo> channelCategoryAttrInfoList);

    @Mappings({
            @Mapping(source = "qualification_picture_title", target = "qualificationPictureTitle"),
            @Mapping(source = "qualification_picture_description", target = "qualificationPictureDescription"),
            @Mapping(source = "qualification_picture_example", target = "qualificationPictureExample"),
    })
    QualificationPicturesInfo qualificationPicturesInfoMapping(com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.QualificationPicturesInfo info);

    List<QualificationPicturesInfo> qualificationPicturesInfoMapping(List<com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.QualificationPicturesInfo> infos);

    @Mappings({
            @Mapping(source = "is_qualification_pictures_need", target = "isQualificationPicturesNeed"),
            @Mapping(source = "qualification_pictures_info", target = "infos")
    })
    ProductQuaPicUnstructRule productUnStructRuleMapping(ProductUnstrucQuaPicRule ruleInfo);

    @Mappings({
            @Mapping(source = "special_picture_type", target = "specialPictureType"),
            @Mapping(source = "special_picture_title", target = "specialPictureTitle"),
            @Mapping(source = "special_picture_description", target = "specialPictureDescription"),
            @Mapping(source = "special_picture_example", target = "specialPictureExample"),
            @Mapping(source = "is_required", target = "isRequired"),
            @Mapping(source = "is_displayed", target = "isDisplayed"),
            @Mapping(source = "num_limit", target = "numLimit"),
    })
    ProductSpecialPictureRule specialPictureRuleMapping(ProductUnStructSpecialPictureRule rule);

    List<ProductSpecialPictureRule> specialPictureRuleMapping(List<ProductUnStructSpecialPictureRule> rules);

    @Mappings({
            @Mapping(source = "qualification_pictures_rule", target = "quaPicUnstructRule"),
            @Mapping(source = "special_pictures_rules", target = "specialPictureRules")
    })
    ProductUnstructRule productUnStructRuleMapping(ProductUnstructRuleInfo ruleInfo);

    @Mappings({
            @Mapping(target = "channelPromotionType", expression = "java(String.valueOf(wmAppOrderActDetail.getType()))"),
            @Mapping(target = "promotionRemark", source = "remark"),
            @Mapping(target = "channelCost", expression = "java(MoneyUtils.yuanToFen(wmAppOrderActDetail.getMtCharge()))"),
            @Mapping(target = "tenantCost", expression = "java(MoneyUtils.yuanToFen(wmAppOrderActDetail.getPoiCharge()))"),
            @Mapping(target = "activityId", source = "act_id"),
            @Mapping(target = "promotionCount", source = "count")
    })
    GoodsSharedActivityItem convertGoodsSharedActivityItem(WmAppOrderActDetail wmAppOrderActDetail);
    List<GoodsSharedActivityItem> convertGoodsSharedActivityItems(List<WmAppOrderActDetail> wmAppOrderActDetails);


    @Mappings({
            @Mapping(target = "customSpu", source = "app_food_code"),
            @Mapping(target = "customSkuId", source = "sku_id"),
            @Mapping(target = "skuCount", source = "count"),
            @Mapping(target = "totalDiscount", expression = "java(MoneyUtils.yuanToFen(skuBenefitDetail.getTotalReducePrice()))"),
            @Mapping(target = "originPrice", expression = "java(MoneyUtils.yuanToFen(skuBenefitDetail.getOriginPrice()))"),
            @Mapping(target = "activityPrice", expression = "java(MoneyUtils.yuanToFen(skuBenefitDetail.getActivityPrice()))"),
            @Mapping(target = "channelCost", expression = "java(MoneyUtils.yuanToFen(skuBenefitDetail.getTotalMtCharge()))"),
            @Mapping(target = "tenantCost", expression = "java(MoneyUtils.yuanToFen(skuBenefitDetail.getTotalPoiCharge()))"),
            @Mapping(target = "agentCost", constant = "0"),
            @Mapping(target = "logisticsCost", constant = "0"),
            @Mapping(target = "channelJiFenCost", constant = "0"),
            @Mapping(target = "channelPromotionType", constant = "0"),
            @Mapping(target = "goodActivityDetail", expression = "java(convertGoodsSharedActivityItems(skuBenefitDetail.getWmAppOrderActDetails()))")

    })
    GoodsActivityDetailDTO     convertGoodsActivityDetail(SkuBenefitDetail skuBenefitDetail);
    List<GoodsActivityDetailDTO> convertGoodsActivityDetails(List<SkuBenefitDetail> skuBenefitDetails);



    @Mappings({
            @Mapping(target = "app_poi_code", constant = ""),
            @Mapping(target = "start_date", expression = "java((int)(request.getAccountTimeStart()/1000))"),
            @Mapping(target = "end_date", expression = "java((int)(request.getAccountTimeEnd()/1000))"),
            @Mapping(target = "offset", constant = "0"),
            @Mapping(target = "limit", source = "pageSize"),

    })
    MTOrderSettlementListParam convertChannelSettlementDetailRequset(ChannelOrderSettlementPageRequest request);

    @Mappings({
            @Mapping(target = "channelId", constant = "100"),
            @Mapping(target = "channelSettleOrderId", expression = "java(mtOrderSettlementListResult.getSettleSettingId())"),
            @Mapping(target = "orderChargeType", expression = "java(mtOrderSettlementListResult.getBillChargeType())"),
            @Mapping(target = "orderChargeTypeDesc", expression = "java(mtOrderSettlementListResult.getChargeFeeDesc())"),
            @Mapping(target = "settleMilliAmt", source = "settleMilli"),
            @Mapping(target = "orderTime",expression = "java(StringUtils.isBlank(mtOrderSettlementListResult.getOrderTime()) || Objects.equals(\"0\",mtOrderSettlementListResult.getOrderTime())? (StringUtils.isNotBlank(mtOrderSettlementListResult.getFinishTime())? (DateUtils.date2Millisecond(mtOrderSettlementListResult.getFinishTime())):0) :DateUtils.date2Millisecond(mtOrderSettlementListResult.getOrderTime()))"),
            @Mapping(target = "settlementFinishDate", expression ="java(MtConverterUtil.convertSettlementFinishDate(mtOrderSettlementListResult.getSettleBillDesc(),Long.valueOf(mtOrderSettlementListResult.getDaliyBillDate())*1000))"),
            @Mapping(target = "settlementFeeDetailList", expression = "java(MtConverterUtil.convertSettlementFeeList(mtOrderSettlementListResult))"),
            @Mapping(target = "channelOrderId", expression = "java(mtOrderSettlementListResult.getWmOrderViewId())"),
            @Mapping(target = "rawSettleStatus", expression = "java(String.valueOf(MtConverterUtil.convertMtSettleStatus(mtOrderSettlementListResult.getAccountState()).getCode()))"),
            @Mapping(target = "rawSettleStatusDesc", expression = "java(MtConverterUtil.convertMtSettleStatus(mtOrderSettlementListResult.getAccountState()).getDesc())"),
            @Mapping(target = "settlementDate", expression = "java(Long.valueOf(mtOrderSettlementListResult.getDaliyBillDate())*1000)"),
            @Mapping(target = "refundTime", expression = "java(StringUtils.isNotBlank(mtOrderSettlementListResult.getRefundTime())? (DateUtils.date2Millisecond(mtOrderSettlementListResult.getRefundTime())):0)"),
            @Mapping(target = "refundId", expression = "java(mtOrderSettlementListResult.getRefund_id() > 0 ? String.valueOf(mtOrderSettlementListResult.getRefund_id()) : null)"),
            @Mapping(target = "orderFinishTime", expression = "java(StringUtils.isNotBlank(mtOrderSettlementListResult.getFinishTime())? (DateUtils.date2Millisecond(mtOrderSettlementListResult.getFinishTime())):0)"),
            @Mapping(target = "outId", source = "out_id"),
            @Mapping(target = "id", source = "id"),
            @Mapping(target = "skuBenefitDetailList", expression = "java(MtConverterUtil.convertSkuBenefitDetailList" + "(mtOrderSettlementListResult.getWmAppOrderSkuBenefitDetailList()))"),
            @Mapping(target = "wmAppOrderSkuShippingDetailList", expression = "java(MtConverterUtil.convertSkuShippingDetailList" + "(mtOrderSettlementListResult.getWmAppOrderSkuShippingDetailList()))"),
            @Mapping(target = "chargeFeeType", source = "chargeFeeType"),
            @Mapping(target = "originalOrderSettlementData", source = "originalOrderSettlementData")
    })
    ChannelOrderSettlementDTO convertChannelOrderSettlement(MTOrderSettlementListResult mtOrderSettlementListResult);



    @Mappings({
            @Mapping(expression = "java(ChannelStatusConvertUtil.mtDeliveryStatusMapping(data.getLogistics_status()))", target = "status"),
            @Mapping(source = "dispatcher_name", target = "riderName"),
            @Mapping(source = "dispatcher_mobile", target = "riderPhone")
    })
    LogisticsStatusDTO convertLogisticsStatus(MtLogisticsStatus data);

    @Mappings({
            @Mapping(target = "skuName", source = "channelPartRefundSkuInfo.food_name"),
            @Mapping(target = "customSpu", source = "channelPartRefundSkuInfo.app_food_code"),
            @Mapping(target = "skuId", source = "channelPartRefundSkuInfo.sku_id"),
            @Mapping(target = "count", source = "channelPartRefundSkuInfo.count"),
            @Mapping(target = "skuRefundAmount", expression = "java(MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getCount() == 0 || refundType == 5 ? channelPartRefundSkuInfo.getRefund_price() : channelPartRefundSkuInfo.getRefund_price() * channelPartRefundSkuInfo.getCount()))"),//商品退款金额是单价，需要转成退款总价
            @Mapping(target = "foodPrice", expression = "java(MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getFood_price()))"),
            @Mapping(target = "boxPrice", expression = "java(MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getBox_price()))"),
            @Mapping(target = "boxNum", source = "channelPartRefundSkuInfo.box_num"),
            @Mapping(target = "refundWeight", source = "channelPartRefundSkuInfo.refunded_weight"),
            @Mapping(target = "spec", source = "channelPartRefundSkuInfo.spec"),
            @Mapping(target = "poiTotalItemPromotion", expression = "java(MtConverterUtil.convertItemAfsTotalPoiPromotion(channelPartRefundSkuInfo.getRetail_refund_partial_estimate_charge()))"),
            @Mapping(target = "platTotalItemPromotion", expression = "java(MtConverterUtil.convertItemAfsTotalPlatPromotion(channelPartRefundSkuInfo.getRetail_refund_partial_estimate_charge()))"),
            @Mapping(target = "refundPlatOrderPromotion", expression = "java(MtConverterUtil.convertItemAfsPromotion(1, channelPartRefundSkuInfo.getRetail_refund_partial_estimate_charge()))"),
            @Mapping(target = "refundPoiOrderPromotion", expression = "java(MtConverterUtil.convertItemAfsPromotion(2, channelPartRefundSkuInfo.getRetail_refund_partial_estimate_charge()))"),
            @Mapping(target = "refundPlatItemPromotion", expression = "java(MtConverterUtil.convertItemAfsPromotion(3, channelPartRefundSkuInfo.getRetail_refund_partial_estimate_charge()))"),
            @Mapping(target = "refundPoiItemPromotion", expression = "java(MtConverterUtil.convertItemAfsPromotion(4, channelPartRefundSkuInfo.getRetail_refund_partial_estimate_charge()))"),
            @Mapping(target = "flexCount", source = "channelPartRefundSkuInfo.flex_count"),
            @Mapping(target = "itemId", source = "channelPartRefundSkuInfo.item_id")
    })
    RefundProductDTO convertRefundProductDTO(ChannelPartRefundSkuInfo channelPartRefundSkuInfo, int refundType);

    default List<RefundProductDTO> convertRefundProductDTOs(List<ChannelPartRefundSkuInfo> channelPartRefundSkuInfos, int refundType) {
        if (channelPartRefundSkuInfos == null) {
            return null;
        }

        List<RefundProductDTO> list = new ArrayList<RefundProductDTO>();

        for (ChannelPartRefundSkuInfo channelPartRefundSkuInfo : channelPartRefundSkuInfos) {

            list.add(convertRefundProductDTO(channelPartRefundSkuInfo, refundType));
        }

        return list;
    }



    @Mappings({
            @Mapping(constant = "100", target = "channelType"),
            @Mapping(source = "wm_order_id_view", target = "channelOrderId"),
            @Mapping(source = "refund_id", target = "afterSaleId"),
            @Mapping(expression = "java(MtConverterUtil.convertTime(mtOrderAfsApplyDTO.getCtime()))", target = "refundApplyTime"),
            @Mapping(expression = "java(MtConverterUtil.convertTime(mtOrderAfsApplyDTO.getUtime()))", target = "refundAuditTime"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(mtOrderAfsApplyDTO.getMoney()))", target = "refundPrice"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.mtResTypeMapping(String.valueOf(mtOrderAfsApplyDTO.getRes_type())))", target = "afterSaleStatus"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.mtAfsRecordMapping(String.valueOf(mtOrderAfsApplyDTO.getRes_type())))", target = "afterSaleRecordStatus"),
            @Mapping(expression = "java(MtConverterUtil.convertRefundType(mtOrderAfsApplyDTO.getRefund_type()))", target = "refundType"),
            @Mapping(expression = "java(convertRefundProductDTOs(mtOrderAfsApplyDTO.getWmAppRetailForOrderPartRefundList(), mtOrderAfsApplyDTO.getRefund_type()))", target = "afsProductList"),
            @Mapping(expression = "java(MtConverterUtil.convertCommissionValue(mtOrderAfsApplyDTO.getRefund_partial_estimate_charge()))", target = "commission"),
            @Mapping(constant = "1", target = "commissionType"),
            @Mapping(source = "apply_reason", target = "applyReason"),
            @Mapping(source = "res_reason", target = "resReason"),
            @Mapping(expression = "java(Integer.valueOf(6).equals(mtOrderAfsApplyDTO.getApply_type()))", target = "isAppeal"),//和开放平台确定该接口会返回申述记录，通过apply_type来判断
            @Mapping(source = "apply_op_user_type", target = "applyOpType"),
            @Mapping(source = "status", target = "channelAfsStatus"),
            @Mapping(source = "service_type", target = "serviceType"),
            @Mapping(expression = "java(MtConverterUtil.convertExtend(mtOrderAfsApplyDTO))", target = "extend"),
            @Mapping(source = "pictures", target = "refundImgUrl"),
            @Mapping(expression = "java(MtConverterUtil.convertAfsPoiPromotion(mtOrderAfsApplyDTO.getRefund_partial_estimate_charge()))", target = "poiPromotion"),
            @Mapping(expression = "java(MtConverterUtil.convertAfsPlatPromotion(mtOrderAfsApplyDTO.getRefund_partial_estimate_charge()))", target = "platPromotion"),
            @Mapping(expression = "java(MtConverterUtil.convertAuditType(mtOrderAfsApplyDTO))", target = "auditType"),
            @Mapping(source = "is_can_return_goods", target = "isCanReturnGoods"),
            @Mapping(source = "is_return_goods", target = "isReturnGoods"),
            @Mapping(expression = "java(MtConverterUtil.convertReturnGoodsWay(mtOrderAfsApplyDTO.getReturn_goods_way()))", target = "returnGoodsWay"),
            @Mapping(expression = "java(MtConverterUtil.convertReturnFreightDuty(mtOrderAfsApplyDTO))", target = "returnFreightDuty"),
            @Mapping(expression = "java(MtConverterUtil.convertReturnFreightNewType(mtOrderAfsApplyDTO))", target = "refundGoodFreightNewType"),
            @Mapping(expression = "java(MtConverterUtil.convertOnlyRefund(mtOrderAfsApplyDTO))", target = "onlyRefund"),
            @Mapping(source = "logistics_status", target = "logisticsStatus"),
            @Mapping(expression = "java(MtConverterUtil.convertTime(mtOrderAfsApplyDTO.getProcess_deadline()))", target = "processDeadline"),
//            @Mapping(expression = "java(MtConverterUtil.convertReturnGoodsStatus(mtOrderAfsApplyDTO.getReturn_goods_way()))", target = "returnGoodsStatusType"),
            @Mapping(expression = "java(MoneyUtils.yuanToFenOrDefault(mtOrderAfsApplyDTO.getDeduct_user_shipping_fee()))", target = "costCustomerFreightFee"),
            @Mapping(expression = "java(MtConverterUtil.convertReturnGoodsStatus(mtOrderAfsApplyDTO.getReturn_goods_way(), mtOrderAfsApplyDTO.getLogistics_status()))", target = "returnGoodsStatusType"),
            @Mapping(source = "firstAutoNegoType", target = "firstAutoNegoType")



    })
    OrderAfsApplyDTO convertAfsApplyDTO(MtOrderAfsApplyDTO mtOrderAfsApplyDTO);

    List<OrderAfsApplyDTO> convertAfsApplyDTOs(List<MtOrderAfsApplyDTO> afsApplyDTOs);

    @Mappings({
            @Mapping(target = "skuName", source = "food_name"),
            @Mapping(target = "skuId", source = "sku_id"),
            @Mapping(target = "count", source = "count"),
            @Mapping(target = "appFoodCode",source = "app_food_code"),
            @Mapping(target = "customSpu",source = "app_food_code"),
            @Mapping(target = "refundPrice", expression = "java(MoneyUtils.yuanToFen(mtPartRefundGoodsDTO.getRefund_price() ))"),
            @Mapping(target = "itemId", source = "item_id")

    })
    PartRefundGoodDetailDTO convertPartRefundGoodDetailDTO(MtPartRefundGoodsDTO mtPartRefundGoodsDTO);

    List<PartRefundGoodDetailDTO> convertPartRefundGoodDetailDTOs(List<MtPartRefundGoodsDTO> mtPartRefundGoodsDTOS);

    @Mappings({
            @Mapping(target = "skuId", source = "sku_id"),
            @Mapping(target = "skuName", source = "food_name"),
            @Mapping(target = "canRefundSkuCount", source = "count"),
            @Mapping(target = "canRefundMoney", expression = "java(MoneyUtils.yuanToFen(mtEcommercePartRefundGoodsDTO.getRefund_price()))"),
            @Mapping(target = "currentPrice", expression = "java(MoneyUtils.yuanToFen(mtEcommercePartRefundGoodsDTO.getFood_price()))"),
            @Mapping(target = "channelItemId", source = "item_id"),
            @Mapping(target = "customSpu", source = "app_spu_code")
    })
    ChannelOrderMoneyRefundItemDTO convertMoneyCheckRefundItemDTO(MtEcommercePartRefundGoodsDTO mtEcommercePartRefundGoodsDTO);

    List<ChannelOrderMoneyRefundItemDTO> convertMoneyCheckRefundItemDTOs(List<MtEcommercePartRefundGoodsDTO> mtEcommercePartRefundGoodsDTOS);

    @Mappings({
            @Mapping(target = "thirdPartItemId",source = "item_id"),
            @Mapping(target = "sale_price",expression = "java(MoneyUtils.yuanToFen(unitPartRefundGoodsDTO.getFood_price()))"),
            @Mapping(target = "weight", constant = "0"),
            @Mapping(target = "item_name", source = "food_name"),
            @Mapping(target = "allow_ret_qty", source = "count"),
            @Mapping(target = "skuId", source = "sku_id")

    })
    WeightPartRefundGoodsDTO convertWeightPartRefundGoodDTO(MtUnitPartRefundFoodsDTO unitPartRefundGoodsDTO);

    List<WeightPartRefundGoodsDTO> convertWeightPartRefundGoodDTOs(List<MtUnitPartRefundFoodsDTO> unitPartRefundGoodsDTOS);

    @Mappings({
            @Mapping(target = "thirdPartItemId", source = "item_id"),
            @Mapping(target = "sale_price", expression = "java(MoneyUtils.yuanToFen(unitPartRefundFoodsV2DTO.getFood_price()))"),
            @Mapping(target = "weight", constant = "0"),
            @Mapping(target = "item_name", source = "food_name"),
            @Mapping(target = "allow_ret_qty", source = "count"),
            @Mapping(target = "skuId", source = "sku_id")
    })
    WeightPartRefundGoodsDTO convertWeightRefundGoodDTO(MtUnitPartRefundFoodsV2DTO unitPartRefundFoodsV2DTO);

    List<WeightPartRefundGoodsDTO> convertWeightRefundGoodDTOs(List<MtUnitPartRefundFoodsV2DTO> unitPartRefundFoodsV2DTOS);


    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "app_food_code_origin", source = "orgCustomSkuId"),
            @Mapping(target = "app_food_code", source = "customSkuId"),
            @Mapping(target = "sku_id_origin", source = "orgChannelSkuId"),
            @Mapping(target = "sku_id", source = "channelSkuId")
    })
    ChangeCustomSkuId changeCustomSkuId(ChangeCustomSkuIdDTO data);

    SaleAttrValue saleAttrValueDTOMapping(SaleAttrValueDTO attr);
    List<SaleAttrValue> saleAttrValueDTOMappings(List<SaleAttrValueDTO> attrList);

    //以下是SPU相关数据转换
    @Mappings({
            @Mapping(target = "sku_id", source = "customSkuId"),
            @Mapping(target = "spec", expression = "java(MtConverterUtil.convertSpec(data))"),
            @Mapping(target = "unit", source = "unit"),
            @Mapping(target = "min_order_count", source = "minPurchaseQuantity"),
            @Mapping(target = "price", expression = "java(MtConverterUtil.convertPrice(data))"),
            @Mapping(target = "stock", expression = "java(MtConverterUtil.convertStock(data))"),
            @Mapping(target = "upc", expression = "java(MtConverterUtil.convertUpc(data))"),
            @Mapping(target = "available_times", expression = "java(MtConverterUtil.convertStringToMapForAvailableTimes(data.getAvailableTimes()))"),
            @Mapping(target = "weight",  expression = "java(MtConverterUtil.convertWeight(data))"),
            @Mapping(target = "location_code", source = "locationCode"),
            @Mapping(target = "weight_for_unit",  expression = "java(MtConverterUtil.convertWeightForUnit(data))"),
            @Mapping(target = "weight_unit",  expression = "java(MtConverterUtil.convertWeightUnit(data))"),
            @Mapping(target = "box_num", expression = "java(MtConverterUtil.convertBoxNum(data))"),
            @Mapping(target = "box_price", expression = "java(MtConverterUtil.convertBoxPrice(data))"),
            @Mapping(target = "ladder_box_num", expression = "java(MtConverterUtil.convertLadderBoxNum(data))"),
            @Mapping(target = "ladder_box_price", expression = "java(MtConverterUtil.convertLadderBoxPrice(data))"),
            @Mapping(target = "openSaleAttrValueList", source= "saleAttrValueList"),
            @Mapping(target = "sku_picture", source = "skuPicture"),
    })
    ChannelSkuInfoDTO skuInSpuInfoDTOMapping(SkuInSpuInfoDTO data);
    List<ChannelSkuInfoDTO> skuInSpuInfoDTOMappings(List<SkuInSpuInfoDTO> data);

    @Mappings({
            @Mapping(target = "app_food_code", source = "customSpuId"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "base_name", source = "name"),
            @Mapping(target = "description", source = "description"),
            @Mapping(target = "category_code", expression = "java(MtConverterUtil.convert2MTCategoryCode(data))"),
            @Mapping(target = "category_code_list", expression = "java(MtConverterUtil.convert2MTCategory_code_list(data.getLeafStoreCategoryList()))"),
            @Mapping(target = "is_sold_out", expression = "java(MtConverterUtil.convertStatus(data))"),
            @Mapping(target = "picture", source = "pictures"),
            @Mapping(target = "tag_id", source = "channelCategoryId"),
            @Mapping(target = "isSp", expression = "java(null)"),
            @Mapping(target = "zh_name", source = "brandName"),
            @Mapping(target = "origin_name", source = "productionArea"),
            @Mapping(target = "properties", source = "properties"),
            @Mapping(target = "is_specialty", expression = "java(MtConverterUtil.convertIsSpecialty(data))"),
            @Mapping(target = "sequence", expression = "java(MtConverterUtil.convertSequence(data))"),
            @Mapping(target = "sell_point", source = "sellPoint"),
            @Mapping(target = "picture_contents", expression = "java(MtConverterUtil.convertPictureContents(data))"),
            @Mapping(target = "video_id", source = "videoId"),
            @Mapping(target = "common_attr_value", expression = "java(ChannelSpuInfoDTO.convertCommonValue(data.getCommonAttributes()))"),
            @Mapping(target = "forbidSingleOrder", expression = "java(MtConverterUtil.convertForbidSingleOrder(data))"),
            @Mapping(target = "consignment_sales_info", source = "entityInfo"),
            @Mapping(target = "product_name_supplement", source = "nameSupplement"),
            @Mapping(target = "product_name_supplement_seq", source = "nameSupplementSeq")
    })
    ChannelSpuInfoDTO spuInfoDTOMapping(SpuInfoDTO data);
    List<ChannelSpuInfoDTO> spuInfoDTOMappings(List<SpuInfoDTO> data);

    @Mappings({
            @Mapping(target = "consignment_sales_id", source = "entityId"),
            @Mapping(target = "consignment_sales_type", source = "entityType")
    })
    ChannelSpuEntityDTO businessEntityMapping(SpuEntityDTO data);

    @Mappings({
            @Mapping(target = "sku_id", source = "customSkuId"),
            @Mapping(target = "unit", source = "unit"),
            @Mapping(target = "min_order_count", source = "minPurchaseQuantity"),
            @Mapping(target = "price", expression = "java(MtConverterUtil.convertPrice(data))"),
            @Mapping(target = "stock", expression = "java(data.isSetStock()?data.getStock():null)"),
            @Mapping(target = "available_times", expression = "java(MtConverterUtil.convertStringToMapForAvailableTimes(data.getAvailableTimes()))"),
            @Mapping(target = "location_code", source = "locationCode"),
            @Mapping(target = "box_num", expression = "java(MtConverterUtil.convertBoxNum(data))"),
            @Mapping(target = "box_price", expression = "java(MtConverterUtil.convertBoxPrice(data))"),
//            @Mapping(target = "ladder_box_num", expression = "java(MtConverterUtil.convertLadderBoxNum(data))"),
//            @Mapping(target = "ladder_box_price", expression = "java(MtConverterUtil.convertLadderBoxPrice(data))"),
    })
    ChannelStandardSku standardSkuInfoDTOMapping(SkuInSpuInfoDTO data);
    List<ChannelStandardSku> standardSkuInfoDTOMappings(List<SkuInSpuInfoDTO> data);

    @Mappings({
            @Mapping(target = "app_food_code", source = "customSpuId"),
            @Mapping(target = "description", source = "description"),
            @Mapping(target = "category_code", expression = "java(MtConverterUtil.convert2MTCategoryCode(data))"),
            @Mapping(target = "category_code_list", expression = "java(MtConverterUtil.convert2MTCategory_code_list(data.getLeafStoreCategoryList()))"),
            @Mapping(target = "is_sold_out", expression = "java(MtConverterUtil.convertStatus(data))"),
            @Mapping(target = "origin_name", source = "productionArea"),
            @Mapping(target = "properties", source = "properties"),
            @Mapping(target = "is_specialty", expression = "java(MtConverterUtil.convertIsSpecialty(data))"),
            @Mapping(target = "sequence", expression = "java(MtConverterUtil.convertSequence(data))"),
            @Mapping(target = "sell_point", source = "sellPoint"),
    })
    ChannelUpcCreateDTO upcInfoDTOMapping(SpuInfoDTO data);
    List<ChannelUpcCreateDTO> upcInfoDTOMappings(List<SpuInfoDTO> data);

    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "app_food_code", source = "customSpuId"),
            @Mapping(target = "is_delete_retail_cat", constant = "2")
    })
    ChannelSpuDeleteDTO spuDeleteMapping(SpuInfoDeleteDTO spuInfoDeleteDTO);


    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "app_food_code", source = "customSpuId"),
            @Mapping(target = "sku_id", source = "customSkuId"),
            @Mapping(target = "is_delete_retail_cat", constant = "2")
    })
    ChannelSkuInSpuDeleteDTO skuInSpuDeleteMapping(SkuInSpuInfoDeleteDTO skuInSpuInfoDeleteDTO);

    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "food_data",expression = "java(SpuFoodDataInfo.toFoodDatas(data.getCustomSpuIds()))"),
            @Mapping(target = "sell_status",expression = "java(ChannelSkuCreateDTO.mapIs_sold_out(data.getSpuStatus()))"),
    })
    ChannelSpuSellStatusDTO updateSpuSellStatusMapping(SpuInfoSellStatusDTO data);

    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "app_food_code_origin", source = "customSpuIdOrigin"),
            @Mapping(target = "app_food_code",source = "customSpuIdCurrent"),
            @Mapping(target = "sku_id_origin", source = "customSkuIdOrigin"),
            @Mapping(target = "sku_id", source = "customSkuIdCurrent"),
    })
    ChannelSpuAndSkuIdUpdateByOriginDTO updateCustomSpuIdByOriginIdMapping(UpdateCustomSpuIdByOriginIdDTO data);

    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "category_code",source = "categoryCode"),
            @Mapping(target = "spec", source = "spec"),
            @Mapping(target = "app_food_code", source = "customSpuId"),
            @Mapping(target = "sku_id", source = "customSkuId"),
    })
    ChannelSpuAndSkuIdUpdateByNameAndSpecDTO updateCustomSpuIdByNameAndSpecMapping(UpdateCustomSpuIdByNameAndSpecDTO data);

    @Mappings({
            @Mapping(target = "customSkuId", source = "sku_id"),
            @Mapping(target = "spec", source = "spec"),
            @Mapping(target = "weight",  expression = "java(MtConverterUtil.convertWeight(data))"),
            @Mapping(target = "weightForUnit",  expression = "java(MtConverterUtil.convertWeightForUnit(data))"),
            @Mapping(target = "weightUnit",  source = "weight_unit"),
            @Mapping(target = "price", expression = "java(MtConverterUtil.convertPrice(data))"),
            @Mapping(target = "stock", expression = "java(MtConverterUtil.convertStock(data))"),
            @Mapping(target = "upc", source = "upc"),
            @Mapping(target = "minPurchaseQuantity", source = "min_order_count"),
            @Mapping(target = "unit", expression = "java(MtConverterUtil.convertUnit(data))"),
            @Mapping(target = "boxQuantity", expression = "java(MtConverterUtil.convertBoxNum(data))"),
            @Mapping(target = "boxPrice", expression = "java(MtConverterUtil.convertBoxPrice(data))"),
            @Mapping(target = "availableTimes", expression = "java(MtConverterUtil.convertMapToStringForAvailableTimes(data.getAvailable_times()))"),
            @Mapping(target = "locationCode", source = "location_code"),
            @Mapping(target = "ladderBoxQuantity", expression = "java(MtConverterUtil.convertLadderBoxQuantity(data))"),
            @Mapping(target = "ladderBoxPrice", expression = "java(MtConverterUtil.convertLadderBoxPrice(data))"),
            @Mapping(target = "saleAttrValueList", source = "openSaleAttrValueList"),
            @Mapping(target = "skuPicture", source = "sku_picture"),
    })
    SkuInSpuInfoDTO channelSkuInfoDTOMapping(ChannelSkuInfoDTO data);
    List<SkuInSpuInfoDTO> channelSkuInfoDTOMappings(List<ChannelSkuInfoDTO> data);

    SaleAttrValueDTO saleAttrValueMapping(SaleAttrValue attr);
    List<SaleAttrValueDTO> saleAttrValueMappings(List<SaleAttrValue> attrList);

    @Mappings({
            @Mapping(target = "customSpuId", source = "app_food_code"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "description", source = "description"),
            @Mapping(target = "pictures", expression = "java(MappingConverterUtils.stringAsList(data.getPicture()))"),
            @Mapping(target = "categoryList", source = "category_list"),
            @Mapping(target = "channelCategoryId", source = "tag_id"),
            @Mapping(target = "brandName", source = "zh_name"),
            @Mapping(target = "productionArea", source = "origin_name"),
            @Mapping(target = "status", expression = "java(ChannelSpuInfoDTO.mapSpuStatus(data.getIs_sold_out()))"),
            @Mapping(target = "sourceType", constant = "3"),
            @Mapping(target = "sequence", source = "sequence"),
            @Mapping(target = "isSp", source = "isSp"),
            @Mapping(target = "isSpecialty", source = "is_specialty"),
            @Mapping(target = "properties", source = "properties"),
            @Mapping(target = "flavour", source = "flavour"),
            @Mapping(target = "productName", source = "product_name"),
            @Mapping(target = "videoId", source = "video_id"),
            @Mapping(target = "videoUrlMp4", source = "video_url_mp4"),
            @Mapping(target = "auditStatus", source = "audit_status"),
            @Mapping(target = "isComplete", source = "is_complete"),
            @Mapping(target = "indicatorItems", source = "indicator_items"),
            @Mapping(target = "sellPoint", source = "sell_point"),
            @Mapping(target = "pictureContents", expression = "java(MappingConverterUtils.convertPictureContents(data.getPicture_contents()))"),
            @Mapping(target = "commonAttributes", source = "common_attr_value"),
            @Mapping(target = "videoCoverUrl", source = "video_pic_large_url"),
            @Mapping(target = "normAuditStatus", source = "norm_audit_status"),
            @Mapping(target = "normAuditType", source = "norm_audit_type"),
            @Mapping(target = "nameSupplement", source = "product_name_supplement"),
            @Mapping(target = "nameSupplementSeq", source = "product_name_supplement_seq"),
    })
    SpuInfoDTO channelSpuInfoDTOMapping(ChannelSpuInfoDTO data);
    List<SpuInfoDTO> channelSpuInfoDTOMappings(List<ChannelSpuInfoDTO> datas);

    @Mappings({
        @Mapping(target = "sku_id", source = "customSkuId"),
        @Mapping(target = "stock", expression = "java(MtConverterUtil.convertStock(data))"),
    })
    SkuStockInfo updateStockBySpuSkuMapping(SkuInSpuStockDTO data);
    List<SkuStockInfo> updateStockBySpuSkuMappings(List<SkuInSpuStockDTO> datas);

    @Mappings({
            @Mapping(target = "app_food_code", source = "customSpuId"),
            @Mapping(target = "skus", source = "skuStockInfo"),
    })
    SpuStockInfo updateStockBySpuMapping(SpuStockDTO data);
    List<SpuStockInfo> updateStockBySpuMappings(List<SpuStockDTO> datas);

    @Mappings({
            @Mapping(target = "sku_id", source = "customSkuId"),
            @Mapping(target = "price", source = "price"),
    })
    SkuPriceInfo updatePriceBySpuSkuMapping(SkuInSpuPriceDTO data);
    List<SkuPriceInfo> updatePriceBySpuSkuMappings(List<SkuInSpuPriceDTO> datas);

    @Mappings({
            @Mapping(target = "app_food_code", source = "customSpuId"),
            @Mapping(target = "skus", source = "skuPriceInfo"),
    })
    SpuPriceInfo updatePriceBySpuMapping(SpuPriceDTO data);
    List<SpuPriceInfo> updatePriceBySpuMappings(List<SpuPriceDTO> datas);

    @Mappings({
            @Mapping(target = "food_data", source = "request")
    })
    ChannelSpuUpdateDTO updateSpuInfo(String request);

    @Mappings({
        @Mapping(target = "video_name", source = "videoName"),
            @Mapping(target = "video_url", source = "videoUrl"),
    })
    ChannelVideoUploadDTO uploadVideo(VideoUploadRequest request);

    @Mappings({
            @Mapping(target = "video_id", source = "videoId"),
            @Mapping(target = "app_spu_codes", expression = "java(StringUtils.join(request.getCustomSpuIds(), \",\"))"),
            @Mapping(target = "bind_type", source = "bindType"),
    })
    ChannelVideoBindDTO updateVideoBind(VideoBindRequest request);

    @Mappings({
            @Mapping(target = "video_id", source = "videoId"),
    })
    ChannelVideoDeleteDTO deleteVideo(VideoDeleteRequest request);

    default ChannelSpuAndSkuIdUpdateByNameAndSpecDTO valueOfUpdateId(UpdateCustomSpuIdByNameAndSpecDTO dto){
        ChannelSpuAndSkuIdUpdateByNameAndSpecDTO param = new ChannelSpuAndSkuIdUpdateByNameAndSpecDTO();
        param.setApp_poi_code(String.valueOf(dto.getStoreId()));
        param.setName(dto.getName());
        if (StringUtils.isNotBlank(dto.getCategoryCode())){
            param.setCategory_code(dto.getCategoryCode());
        }else {
            param.setCategory_name(dto.getCategoryName());
        }
        param.setSpec(dto.getSpec());
        param.setApp_food_code(dto.getCustomSpuId());
        param.setSku_id(dto.getCustomSkuId());
        return param;
    }

    @Mappings({
            @Mapping(target = "areaId", source = "valueId"),
            @Mapping(target = "areaName", source = "value"),
            @Mapping(target = "parentId", source = "parentId"),
            @Mapping(target = "level", source = "level"),
            @Mapping(target = "areaIdPath", source = "valueIdPath"),
            @Mapping(target = "areaNamePath", source = "valuePath"),
    })
    AreaInfo areaInfoMapping(OriginPlaceInfo source);
    List<AreaInfo> areaInfoListMapping(List<OriginPlaceInfo> source);

    @Mappings({
            @Mapping(target = "appShippingCode", source = "app_shipping_code"),
            @Mapping(target = "area", source = "area"),
            @Mapping(target = "minPrice", source = "min_price"),
            @Mapping(target = "shippingFee", source = "shipping_fee"),
            @Mapping(target = "type", source = "type"),
    })
    PoiShippingInfoDTO poiShippingInfoMapping(OriginPoiShippingInfoDTO source);
    List<PoiShippingInfoDTO> poiShippingInfoMapping(List<OriginPoiShippingInfoDTO> source);


    @Mappings({
            @Mapping(target = "appShippingCode", source = "app_shipping_code"),
            @Mapping(target = "area", expression = "java(MtConverterUtil.convert2CoordinateList(source.getArea()))"),
            @Mapping(target = "minPrice", source = "min_price"),
            @Mapping(target = "shippingFee", source = "shipping_fee"),
            @Mapping(target = "type", source = "type"),
            @Mapping(target = "shippingAreaId", source = "mt_shipping_id"),
            @Mapping(target = "shippingPeriodName", source = "shipping_period_name"),
            @Mapping(target = "timeRangeBegin", expression = "java(MtConverterUtil.parseShippingPeriodRange(source.getTime_range()).getLeft())"),
            @Mapping(target = "timeRangeEnd", expression = "java(MtConverterUtil.parseShippingPeriodRange(source.getTime_range()).getRight())"),
            @Mapping(target = "channelPoiId", source = "app_poi_code"),

    })
    PoiShippingAreaInfoDTO poiShippingAreaInfoMapping(OriginPoiShippingInfoDTO source);

    List<PoiShippingAreaInfoDTO> poiShippingAreaInfoMapping(List<OriginPoiShippingInfoDTO> source);

    @Mappings({
            @Mapping(target = "x", source = "x"),
            @Mapping(target = "y", source = "y"),
    })
    Coordination poiShippingCoordinationMapping(OriginPoiShippingInfoDTO.OriginCoordination source);
    List<Coordination> poiShippingCoordinationMapping(List<OriginPoiShippingInfoDTO.OriginCoordination> source);


    @Mappings({
            @Mapping(target = "app_spu_code_origin", source = "orgCustomSpuId"),
            @Mapping(target = "app_spu_code", source = "customSpuId"),
            @Mapping(target = "sku_id_origin", source = "orgCustomSkuId"),
            @Mapping(target = "sku_id", source = "customSkuId")
    })
    CustomSkuIdChangeInfo changeCustomSkuIdInfoMapping(CustomSkuIdChangeDTO data);
    List<CustomSkuIdChangeInfo> changeCustomSkuIdInfoMapping(List<CustomSkuIdChangeDTO> dataList);

    @Mappings({
            @Mapping(target = "video_name", source = "videoName"),
            @Mapping(target = "video_url", source = "videoUrl"),
    })
    ChannelVideoUploadDTO uploadVideo(BatchVideoUploadRequest request);

    @Mappings({
            @Mapping(target = "channelOrderId", source = "orderViewId"),
            @Mapping(target = "amount", expression = "java(MoneyUtils.yuanToFen(data.getAmount()))"),
            @Mapping(target = "reason", source = "reason"),
            @Mapping(target = "compensateTime", source = "executeTime"),
            @Mapping(target = "responsibleParty", source = "responsible_party"),
            @Mapping(target = "isTimeout", source = "is_timeout"),
            @Mapping(target = "compensateType", source = "compensate_type"),
    })
    OrderCompensationDTO compensationOrderMapping(CompensationOrderResultDTO.CompensationOrderInfo data);
    List<OrderCompensationDTO> compensationOrderMappings(List<CompensationOrderResultDTO.CompensationOrderInfo> datas);


}
