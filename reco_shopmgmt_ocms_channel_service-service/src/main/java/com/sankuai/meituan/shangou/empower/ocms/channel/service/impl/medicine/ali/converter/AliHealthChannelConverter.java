package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.ali.converter;

import com.sankuai.meituan.shangou.empower.ocms.channel.medicine.dto.AliDrugBaseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.medicine.dto.AliProductFullInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.medicine.dto.AliProductInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.ali.dto.AliDrugTraceQueryDrugInfoResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AliHealthChannelConverter {
    AliHealthChannelConverter INSTANCE = Mappers.getMapper(AliHealthChannelConverter.class);

    @Mappings({
            @Mapping(target = "productInfoList", expression = "java(source == null || source.getCodeProduceInfoDTO() == null || source.getCodeProduceInfoDTO().getProduceInfoList() == null || org.apache.commons.collections.CollectionUtils.isEmpty(source.getCodeProduceInfoDTO().getProduceInfoList().get(\"produce_info_dto\")) ? new java.util.ArrayList() : source.getCodeProduceInfoDTO().getProduceInfoList().get(\"produce_info_dto\").stream().map(item -> convert2AliProductInfo(item)).collect(java.util.stream.Collectors.toList()))"),
            @Mapping(target = "codeStatus", expression = "java(source == null || source.getCodeStatusTypeDTO() == null ? null : source.getCodeStatusTypeDTO().getCodeStatus())"),
            @Mapping(target = "drugEntBaseDTO", expression = "java(source == null ? null : convert2AliDrugBase(source.getDrugEntBaseDTO()))")
    })
    AliProductFullInfoDTO toAliProductFullInfoDTO(AliDrugTraceQueryDrugInfoResponse.CodeFullInfoDto source);

    AliProductInfoDTO convert2AliProductInfo(AliDrugTraceQueryDrugInfoResponse.ProduceInfoDto source);

    AliDrugBaseDTO convert2AliDrugBase(AliDrugTraceQueryDrugInfoResponse.DrugEntBaseDto drugEntBaseDto);
}
