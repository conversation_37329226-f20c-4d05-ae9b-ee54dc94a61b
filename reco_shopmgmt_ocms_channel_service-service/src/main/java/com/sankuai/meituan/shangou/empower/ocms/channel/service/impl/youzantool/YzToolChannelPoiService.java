package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseChannelPoiRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.youzan.cloud.open.sdk.core.client.auth.Auth;
import com.youzan.cloud.open.sdk.core.client.auth.Token;
import com.youzan.cloud.open.sdk.core.client.core.YouZanClient;
import com.youzan.cloud.open.sdk.core.oauth.model.OAuthToken;
import com.youzan.cloud.open.sdk.core.oauth.token.TokenParameter;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanShopSubshopUpdate;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanShopSubshopUpdateParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanShopSubshopUpdateResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiAddressUpdateRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool.response.YouzanShopBasicGetResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiDetailsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiIdsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.CoordinateTransformUtil;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.core.client.core.DefaultYZClient;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanShopChainDescendentOrganizationList;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanShopChainDescendentOrganizationListParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanShopChainDescendentOrganizationListResult;
import com.youzan.cloud.open.sdk.gen.v3_0_0.api.YouzanShopBasicGet;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanShopBasicGetParams;

import lombok.extern.slf4j.Slf4j;

/**
 * 有赞渠道门店管理功能.
 *
 * @author: liwei101
 * @since: 2021/6/7 09:35
 */
@Slf4j
@Service("yzToolChannelPoiService")
public class YzToolChannelPoiService extends YouZanToolBaseService implements ChannelPoiService {

    public static final int RESULT_SUCCESS_CODE = 200;

    public static final int SHOP_QUERY_PAGE_SIZE = 50;

    public static final BigDecimal LAT_LNG_PRECISION = BigDecimal.valueOf(1e6);


    @Autowired
    private DefaultYZClient yzClient;

    @Autowired
    private MtChannelGateService mtChannelGateService;

    @Override
    public GetPoiInfoResponse batchGetPoiInfo(BaseRequestSimple req) {

        try {

            List<PoiInfo> authorizedShops = getAuthorizedShops(req, null);
            fillAddress(authorizedShops, req.getTenantId());
            return new GetPoiInfoResponse(ResultGenerator.genSuccessResult(), authorizedShops);
        }
        catch (Exception e) {
            return new GetPoiInfoResponse(ResultGenerator.genResult(ResultCode.FAIL, e.getMessage()), null);
        }
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIds(BaseRequestSimple req) {
        try {
            List<PoiInfo> authorizedShops = getAuthorizedShops(req, null);

            return new GetPoiIdsResponse(ResultGenerator.genSuccessResult(), Fun.map(authorizedShops, PoiInfo::getAppPoiCode));
        }
        catch (ChannelBizException e) {
            return new GetPoiIdsResponse(ResultGenerator.genResult(ResultCode.FAIL, e.getMessage()), null);
        }

    }

    @Override
    public GetPoiIdsResponse batchGetPoiIdsBySysParam(BatchGetPoiIdsRequest req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        return resp;
    }

    @Override
    public GetPoiInfoResponse batchGetPoiDetails(BatchGetPoiDetailsRequest req) {
        try {
            BaseRequestSimple baseRequestSimple = new BaseRequestSimple();
            baseRequestSimple.setTenantId(req.getTenantId());
            List<PoiInfo> authorizedShops = getAuthorizedShops(baseRequestSimple, req.getStoreIds());
            long tenantId = req.getTenantId();

            fillAddress(authorizedShops, tenantId);
            return new GetPoiInfoResponse(ResultGenerator.genSuccessResult(), authorizedShops);
        }
        catch (Exception e) {
            return new GetPoiInfoResponse(ResultGenerator.genResult(ResultCode.FAIL, e.getMessage()), null);
        }
    }

    private void fillAddress(List<PoiInfo> authorizedShops, long tenantId) throws SDKException {
        AppMessage appMessage = getMainAppMessage(tenantId);
        for (PoiInfo authorizedShop : authorizedShops) {
            try {
                appMessage.setGrantId(authorizedShop.getAppPoiCode());
                YouzanShopBasicGet youzanShopBasicGet = new YouzanShopBasicGet();
                YouzanShopBasicGetParams youzanShopBasicGetParams = new YouzanShopBasicGetParams();
                youzanShopBasicGet.setAPIParams(youzanShopBasicGetParams);
                YouzanShopBasicGetResult result = getResult4YouZan(appMessage, youzanShopBasicGet,
                        YouzanShopBasicGetResult.class);
                if(result.getSuccess()) {
                    YouzanShopBasicGetResult.YouzanShopBasicGetResultData data = result.getData();
                    YouzanShopBasicGetResult.ShopAddress shopAddress = data.getShopAddress();
                    CoordinateTransformUtil.CoordinatePoint coordinatePoint =
                            CoordinateTransformUtil.bd09ToGcj02(Double.valueOf(shopAddress.getLng()),
                                    Double.valueOf(shopAddress.getLat()));
                    authorizedShop.setLongitude(BigDecimal.valueOf(coordinatePoint.getLongitude()).multiply(LAT_LNG_PRECISION).doubleValue());
                    authorizedShop.setLatitude(BigDecimal.valueOf(coordinatePoint.getLatitude()).multiply(LAT_LNG_PRECISION).doubleValue());
                    authorizedShop.setCity(shopAddress.getCity());
                    authorizedShop.setProvince(shopAddress.getProvince());
                    authorizedShop.setCounty(shopAddress.getCounty());
                    authorizedShop.setAddress(shopAddress.getAddress());
                    authorizedShop.setPicUrl(data.getLogo());
//                    YouzanShopBasicGetResult.CustomerServicePhoneNumber customerServicePhoneNumber = data.getCustomerServicePhoneNumber();
//                    if (StringUtils.isEmpty(customerServicePhoneNumber.getAreaCode())) {
//                        authorizedShop.setHotline(customerServicePhoneNumber.getPhoneNumber());
//                    }else {
//                        authorizedShop.setHotline(customerServicePhoneNumber.getAreaCode() +"-"+ customerServicePhoneNumber.getPhoneNumber());
//                    }

                }
            }catch (Exception e) {
                log.error("填充门店地址失败{}", authorizedShop, e);
            }

        }
    }

    private List<PoiInfo> getAuthorizedShops(BaseRequestSimple req, List<String> shopIdList) {

        try {
            AppMessage appMessage = getMainAppMessage(req.getTenantId());
            YouzanShopChainDescendentOrganizationListResult result = getResult4YouZan(appMessage, buildAuthorizedShopReq(1),
                    YouzanShopChainDescendentOrganizationListResult.class);
            List<YouzanShopChainDescendentOrganizationListResult.YouzanShopChainDescendentOrganizationListResultData> shopList = new ArrayList<>();
            checkResultIsSuccess(result);
            shopList.addAll(result.getData());

            int totalSize = result.getTotal();
            int totalPage = (int) Math.ceil(BigDecimal.valueOf(totalSize).divide(BigDecimal.valueOf(SHOP_QUERY_PAGE_SIZE)).doubleValue());

            for (int i = 1; i < totalPage; i++) {
                result = getResult4YouZan(appMessage, buildAuthorizedShopReq(i + 1),
                        YouzanShopChainDescendentOrganizationListResult.class);
                checkResultIsSuccess(result);
                shopList.addAll(result.getData());
            }
            List<PoiInfo> poiInfoList = filterShop(Fun.map(shopList, this::convertToPoiInfo), shopIdList);
            poiInfoList.forEach(p -> p.setAppKey(appMessage.getClientId()));
            return poiInfoList;
        }
        catch (SDKException e) {
            throw new ChannelBizException(e);
        }

    }

    private YouzanShopChainDescendentOrganizationList buildAuthorizedShopReq(int pageNum) {
        YouzanShopChainDescendentOrganizationList youzanShopChainDescendentOrganizationList = new YouzanShopChainDescendentOrganizationList();
        //创建参数对象,并设置参数
        YouzanShopChainDescendentOrganizationListParams youzanShopChainDescendentOrganizationListParams = new YouzanShopChainDescendentOrganizationListParams();
        youzanShopChainDescendentOrganizationListParams.setPageNum(pageNum);
        youzanShopChainDescendentOrganizationListParams.setPageSize(SHOP_QUERY_PAGE_SIZE);
        youzanShopChainDescendentOrganizationList.setAPIParams(youzanShopChainDescendentOrganizationListParams);


        return youzanShopChainDescendentOrganizationList;
    }

    private void checkResultIsSuccess(YouzanShopChainDescendentOrganizationListResult result) {
        if (result.getCode() != RESULT_SUCCESS_CODE) {
            throw new ChannelBizException(result.getMessage());
        }
    }

    private List<PoiInfo> filterShop(List<PoiInfo> poiInfoList, List<String> shopIdList) {
        if (CollectionUtils.isEmpty(poiInfoList) || CollectionUtils.isEmpty(shopIdList)) {
            return poiInfoList;
        }

        return Fun.filter(poiInfoList, p -> shopIdList.contains(p.getAppPoiCode()));

    }

    private PoiInfo convertToPoiInfo(YouzanShopChainDescendentOrganizationListResult.YouzanShopChainDescendentOrganizationListResultData yzShop) {
        PoiInfo poiInfo = new PoiInfo();
        poiInfo.setAppPoiCode(String.valueOf(yzShop.getKdtId()));
        poiInfo.setName(yzShop.getName());
        poiInfo.setIsOnline(1);
        poiInfo.setChannelPoiCode(String.valueOf(yzShop.getKdtId()));
        poiInfo.setShippingTime("00:00-23:59");
        poiInfo.setOpenLevel(1);
        poiInfo.setHotline("");
        return poiInfo;
    }

    /**
     * 获取根据渠道的开放平台门店Id，查询渠道的外卖门店Id
     *
     * @param request
     * @return
     */
    @Override
    public GetPoiIdsResponse getPoiInnerId(BatchGetPoiDetailsRequest request) {
        GetPoiIdsResponse response = new GetPoiIdsResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能"));
        return response;
    }

    /**
     * 门店开始营业
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiOpen(ChannelPoiIdRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 门店停止营业
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiClose(ChannelPoiIdRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 门店公告信息更新
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus promotionInfoUpdate(PoiPromotionInfoUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 营业时间更新
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus shippingTimeUpdate(PoiShippingTimeUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 使门店接受预订单
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus prebookStatusOpen(ChannelPoiIdRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 使门店不接受预订单
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus prebookStatusClose(ChannelPoiIdRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 更新门店的接受预定的日期范围
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus updatePrebookDays(PoiPrebookDaysUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 获取门店公告信息
     *
     * @param request
     * @return
     */
    @Override
    public GetPoiPromotionInfoResponse getPoiPromotionInfo(ChannelPoiIdRequest request) {
        GetPoiPromotionInfoResponse response = new GetPoiPromotionInfoResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能"));
        return response;
    }

    /**
     * 获取门店营业状态
     *
     * @param channelPoiIdRequest
     * @return
     */
    @Override
    public ChannelPoiStatusResponse getPoiStatus(ChannelPoiIdRequest channelPoiIdRequest) {
        ChannelPoiStatusResponse response = new ChannelPoiStatusResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能"));
        return response;
    }

    /**
     * 门店授权
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    /**
     * 门店解析授权
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiDeAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

    @Override
    public ResultStatus updatePoiAddress(PoiAddressUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }

//    @Override
//    public ResultStatus poiHotlineUpdate(ChannelPoiHotlineRequest request) {
//        AppMessage appMessage = getSubAppMessage(request.getTenantId(),request.getStoreId());
//        YouzanShopSubshopUpdate youzanShopSubshopUpdate = new YouzanShopSubshopUpdate();
//        YouzanShopSubshopUpdateParams apiParams = new YouzanShopSubshopUpdateParams();
//        String hotline = request.getHotline();
//        try {
//            if (StringUtils.isEmpty(hotline)) {
//                throw new IllegalArgumentException("电话号码不能为空");
//            }
//            if (hotline.contains("-")) {
//                int lastIndexOf = hotline.lastIndexOf("-");
//                apiParams.setPhoneNumber(hotline.substring(lastIndexOf + 1));
//                apiParams.setPhoneAreaCode(hotline.substring(0, lastIndexOf));
//            } else {
//                apiParams.setPhoneNumber(hotline);
//            }
//            apiParams.setStoreKdtId(Long.valueOf(request.getChannelPoiCode()));
////            apiParams.setBusinessTimeStatus(1);
//            youzanShopSubshopUpdate.setAPIParams(apiParams);
//            log.info("==============youzanShopSubshopUpdate:{}==================", JSONObject.toJSONString(youzanShopSubshopUpdate));
//            YouzanShopSubshopUpdateResult result4YouZan = getResult4YouZan(appMessage, youzanShopSubshopUpdate, YouzanShopSubshopUpdateResult.class);
//            if (result4YouZan.getSuccess()) {
//                return ResultGenerator.genResult(ResultCode.SUCCESS);
//            }
//            return ResultGenerator.genResult(ResultCode.FAIL);
//        } catch (SDKException e) {
//            throw new RuntimeException(e);
//        }
@Override
public ResultStatus poiHotlineUpdate(ChannelPoiHotlineRequest request) {
    return ResultGenerator.genResult(ResultCode.FAIL, "有赞渠道暂不支持此功能");
}

    @Override
    public QueryPoiAuthDetailResponse queryPoiAuthDetailInfo(BaseChannelPoiRequestSimple request) {
        return new QueryPoiAuthDetailResponse().setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能"));
    }

    @Override
    public ResultStatus updatePoiAuthInfo(UpdatePoiAuthInfoRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能");
    }

    @Override
    public UpdateSaleafterAddressMessageResponse updateSaleafterAddressMessage(UpdateSaleafterAddressMessageRequest request) {
        return ResultGenerator.UpdateSafeAddressResultData(ResultCode.FAIL, "有赞渠道暂不支持此功能");
    }


}
