package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.OriginPoiShippingInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ShippingData;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiShippingService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelPoiShippingServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("mtDrunkHorseChannelPoiShippingService")
@Slf4j
public class DrunkHorseChannelPoiShippingServiceImpl implements ChannelPoiShippingService {
    @Resource
    private DrunkHorseChannelGateService drunkHorseChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private MtConverterService mtConverterService;

    @Value("${drunkhorse.url.base}" + "${mt.url.shoplist}")
    private String shoplistUrl;

    @Value("${drunkhorse.url.base}" + "${mt.url.shippingDelete}")
    private String shippingDeleteUrl;

    @Value("${drunkhorse.url.base}" + "${mt.url.shippingReset}")
    private String shippingResetUrl;

    @Value("${drunkhorse.url.base}" + "${mt.url.shippingSave}")
    private String shippingSaveUrl;

    @Value("${drunkhorse.url.base}" + "${mt.url.shippingList}")
    private String shippingListUrl;

    @Value("${drunkhorse.url.base}" +"${mt.url.shippingBatchList}")
    private String shippingBatchList;

    @Value("${drunkhorse.url.base}" + "${mt.url.shippingSpecSave}")
    private String shippingSpecSaveUrl;

    @Value("${drunkhorse.url.base}" + "${mt.url.shippingBatchSave}")
    private String shippingBatchSaveUrl;

    @Override
    public ResultStatus updatePoiShipping(UpdatePoiShippingRequest request) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtChannelPoiShippingServiceImpl.updatePoiShipping  获取app_poi_code失败");
            return ResultGenerator.genFailResult("美团渠道同步配送范围失败");
        }
        ChannelResponseDTO channelResponseDTO;
        Map<String, Object> getResult;
        //保存新配送范围
        bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.MT_AREA, generateArea(request.getCoordinates()));
        bizParam.put(ProjectConstant.MT_MIN_PRICE,
                BigDecimal.valueOf(request.getMinOrderPrice()).scaleByPowerOfTen(-2)
                        .setScale(2, BigDecimal.ROUND_DOWN).toPlainString());
        bizParam.put(ProjectConstant.MT_APP_SHIPPING_CODE, "1");
        bizParam.put(ProjectConstant.MT_TYPE, "1");

        if (StringUtils.isNotEmpty(request.getApp_shipping_code())) {
            // 当请求中配送id不为空时，赋值真实配送id
            bizParam.put(ProjectConstant.MT_APP_SHIPPING_CODE, request.getApp_shipping_code());
        }
        // 增加配送费参数
        bizParam.put(ProjectConstant.MT_SHIPPING_FEE,
                BigDecimal.valueOf(request.getShippingFee()).scaleByPowerOfTen(-2)
                        .setScale(2, BigDecimal.ROUND_DOWN).toPlainString());

        getResult = drunkHorseChannelGateService.sendPost(shippingSaveUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(getResult)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围失败");
        }
        channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围返回数据解析失败");
        }
        if (channelResponseDTO.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }

        return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
    }

    @Override
    public ResultStatus batchUpdatePoiShipping(BatchUpdatePoiShippingRequest request) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtChannelPoiShippingServiceImpl.batchUpdatePoiShipping  获取app_poi_code失败");
            return ResultGenerator.genFailResult("美团渠道同步配送范围失败");
        }
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.SHIPPING_DATA,
                JSON.toJSONString(ShippingData.convertFromPoiShippingInfo(request.getPoiShippingInfo())));

        Map<String, Object> resultMap = drunkHorseChannelGateService.sendPost(shippingBatchSaveUrl, null, baseRequest,
                bizParam);
        if (MapUtils.isEmpty(resultMap)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围失败");
        }
        ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(resultMap),
                ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围返回数据解析失败");
        }
        if (channelResponseDTO.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
    }

    @Override
    public ResultStatus resetPoiShipping(ResetPoiShippingRequest request) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtChannelPoiShippingServiceImpl.resetPoiShipping 获取app_poi_code失败");
            return ResultGenerator.genFailResult("美团渠道重置配送范围失败");
        }
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.MT_APP_SHIPPING_CODE, "1");
        bizParam.put(ProjectConstant.MT_TYPE, "1");
        bizParam.put(ProjectConstant.MT_AREA, generateArea(request.getCoordinates()));
        bizParam.put(ProjectConstant.MT_MIN_PRICE,
                BigDecimal.valueOf(request.getMinOrderPrice()).scaleByPowerOfTen(-2)
                        .setScale(2, BigDecimal.ROUND_DOWN).toPlainString());
        bizParam.put(ProjectConstant.MT_SHIPPING_FEE,
                BigDecimal.valueOf(request.getShippingFee()).scaleByPowerOfTen(-2)
                        .setScale(2, BigDecimal.ROUND_DOWN).toPlainString());
        if (StringUtils.isNotEmpty(request.getApp_shipping_code())) {
            // 当请求中配送id不为空时，赋值真实配送id
            bizParam.put(ProjectConstant.MT_APP_SHIPPING_CODE, request.getApp_shipping_code());
        }

        Map<String, Object> resultMap = drunkHorseChannelGateService.sendPost(shippingResetUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(resultMap)) {
            return ResultGenerator.genFailResult("调用渠道删除配送范围失败");
        }
        ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(resultMap),
                ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道删除配送范围返回数据解析失败");
        }
        if (channelResponseDTO.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
    }

    @Override
    public ResultStatus deletePoiShipping(DeletePoiShippingRequest request) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtChannelPoiShippingServiceImpl.deletePoiShipping 获取app_poi_code失败");
            return ResultGenerator.genFailResult("美团渠道删除配送范围失败");
        }
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.MT_APP_SHIPPING_CODE, request.getApp_shipping_code());

        Map<String, Object> resultMap = drunkHorseChannelGateService.sendPost(shippingDeleteUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(resultMap)) {
            return ResultGenerator.genFailResult("调用渠道删除配送范围失败");
        }
        ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(resultMap),
                ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道删除配送范围返回数据解析失败");
        }
        if (channelResponseDTO.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
    }

    /**
     * 已废弃,用新接口 queryPoiShippingAreaInfo
     */
    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    @Deprecated
    public QueryPoiShippingResponse queryPoiShipping(QueryPoiShippingRequest request) {
        //获取系统级参数
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());

        //获取应用级参数
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getPoiId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("ChannelPoiShippingService.queryPoiShipping获取app_poi_code失败");
            return new QueryPoiShippingResponse(ResultCode.FAIL.getCode(), "查询渠道门店配送范围时获取app_poi_code失败", null);
        }
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);

        //调用开放平台
        log.info("query channel poi shipping list start, baseRequest:{}, bizParam:{}", baseRequest, bizParam);
        Map<String, Object> resultMap = drunkHorseChannelGateService.sendGet(shippingListUrl, null, baseRequest, bizParam);
        log.info("query channel poi shipping list end, result:{}", resultMap);

        if (MapUtils.isEmpty(resultMap) || Objects.isNull(resultMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(resultMap.get(ProjectConstant.DATA))) {
            return new QueryPoiShippingResponse(ResultCode.FAIL.getCode(), "调用渠道查询配送范围失败", null);
        }

        JSONArray jsonArray = (JSONArray) resultMap.get(ProjectConstant.DATA);
        List<OriginPoiShippingInfoDTO> originPoiShippingInfoDTOS = jsonArray.toJavaList(OriginPoiShippingInfoDTO.class);

        // 组装数据
        return new QueryPoiShippingResponse(ResultCode.SUCCESS.getCode(), "", mtConverterService.poiShippingInfoMapping(originPoiShippingInfoDTOS));
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public QueryPoiShippingAreaResponse queryPoiShippingAreaInfo(QueryPoiShippingRequest request) {
        //获取系统级参数
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());

        //获取应用级参数
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getPoiId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("ChannelPoiShippingService.queryPoiShipping获取app_poi_code失败");
            return new QueryPoiShippingAreaResponse(ResultCode.FAIL.getCode(), "查询渠道门店配送范围时获取app_poi_code失败", null);
        }
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);

        //调用开放平台
        log.info("query channel poi shipping list start, baseRequest:{}, bizParam:{}", baseRequest, bizParam);
        Map<String, Object> resultMap = drunkHorseChannelGateService.sendGet(shippingListUrl, null, baseRequest, bizParam);
        log.info("query channel poi shipping list end, result:{}", resultMap);

        if (MapUtils.isEmpty(resultMap) || Objects.isNull(resultMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(resultMap.get(ProjectConstant.DATA))) {
            return new QueryPoiShippingAreaResponse(ResultCode.FAIL.getCode(), "调用渠道查询配送范围失败", null);
        }

        JSONArray jsonArray = (JSONArray) resultMap.get(ProjectConstant.DATA);
        List<OriginPoiShippingInfoDTO> originPoiShippingInfoDTOS = jsonArray.toJavaList(OriginPoiShippingInfoDTO.class);

        // 组装数据
        List<PoiShippingAreaInfoDTO> poiShippingInfoDTOS = mtConverterService.poiShippingAreaInfoMapping(originPoiShippingInfoDTOS);
        poiShippingInfoDTOS.forEach(dto -> dto.setChannelPoiId(Long.parseLong(appPoiCode)));
        fillPoiId(poiShippingInfoDTOS, request.tenantId, request.getChannelId(), Collections.singletonList(request.getPoiId()));
        return new QueryPoiShippingAreaResponse(ResultCode.SUCCESS.getCode(), "", poiShippingInfoDTOS);
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public QueryPoiShippingAreaResponse batchQueryPoiShippingAreaInfo(BatchQueryPoiShippingAreaRequest request) {
        if (CollectionUtils.isEmpty(request.getPoiIds())) {
            return new QueryPoiShippingAreaResponse(ResultCode.INVALID_PARAM.getCode(), "poiIds不能为空", null);
        }

        //获取系统级参数
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());

        //获取应用级参数
        Map<String, Object> bizParam = Maps.newHashMap();
        List<String> appPoiCodes = getAppPoiCodeList(request.getTenantId(), request.getChannelId(), request.getPoiIds());

        if (CollectionUtils.isEmpty(appPoiCodes)) {
            log.error("ChannelPoiShippingService.queryPoiShipping获取app_poi_code失败");
            return new QueryPoiShippingAreaResponse(ResultCode.FAIL.getCode(), "查询渠道门店配送范围时获取app_poi_code失败", null);
        }
        bizParam.put(ProjectConstant.APP_POI_CODES, Joiner.on(",").join(appPoiCodes));

        //调用开放平台
        log.info("batch query channel poi shipping list start, baseRequest:{}, bizParam:{}", baseRequest, bizParam);
        Map<String, Object> resultMap = drunkHorseChannelGateService.sendGet(shippingBatchList, null, baseRequest, bizParam);
        log.info("batch query channel poi shipping list end, result:{}", resultMap);

        if (MapUtils.isEmpty(resultMap)
                || (!Objects.equals(resultMap.get(ProjectConstant.RESULT_CODE), 1) && !Objects.equals(resultMap.get(ProjectConstant.RESULT_CODE), 2))
                || Objects.isNull(resultMap.get(ProjectConstant.SUCCESS_LIST))) {
            return new QueryPoiShippingAreaResponse(ResultCode.FAIL.getCode(), "调用渠道查询配送范围失败", null);
        }

        JSONArray jsonArray = (JSONArray) resultMap.get(ProjectConstant.SUCCESS_LIST);
        List<OriginPoiShippingInfoDTO> originPoiShippingInfoDTOS = jsonArray.toJavaList(OriginPoiShippingInfoDTO.class);

        // 组装数据
        List<PoiShippingAreaInfoDTO> poiShippingInfoDTOS = mtConverterService.poiShippingAreaInfoMapping(originPoiShippingInfoDTOS);
        fillPoiId(poiShippingInfoDTOS, request.tenantId, request.getChannelId(), request.getPoiIds());
        return new QueryPoiShippingAreaResponse(ResultCode.SUCCESS.getCode(),"", poiShippingInfoDTOS);
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public ResultStatus updatePoiRegularPeriodShippingByShippingAreaId(UpdatePoiRegularPeriodShippingByShippingAreaIdRequest request) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtChannelPoiShippingServiceImpl.updatePoiShipping  获取app_poi_code失败");
            return ResultGenerator.genFailResult("美团渠道同步配送范围失败");
        }
        ChannelResponseDTO channelResponseDTO;
        Map<String, Object> getResult;
        //保存新配送范围
        bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.MT_AREA, generateArea(request.getCoordinates()));
        bizParam.put(ProjectConstant.MT_MIN_PRICE, String.valueOf(request.getMinOrderPrice()));
        bizParam.put(ProjectConstant.MT_TYPE, "1");

        if (request.getShippingAreaId() > 0) {
            // 当请求中业务方配送id不为空时，赋值真实配送id
            bizParam.put(ProjectConstant.MT_SHIPPING_ID, String.valueOf(request.getShippingAreaId()));
        }

        if (StringUtils.isNotBlank(request.getAppShippingCode())) {
            // 当请求中配送id不为空时，赋值真实业务方配送id
            bizParam.put(ProjectConstant.MT_APP_SHIPPING_CODE, String.valueOf(request.getAppShippingCode()));
        }

        // 增加配送费参数
        bizParam.put(ProjectConstant.MT_SHIPPING_FEE, String.valueOf(request.getShippingFee()));

        getResult = drunkHorseChannelGateService.sendPost(shippingSaveUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(getResult)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围失败");
        }
        channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围返回数据解析失败");
        }
        if (channelResponseDTO.isSuccess()) {
            //新增配送范围时回传areaId
            if (request.getShippingAreaId() == 0) {
                JSONArray jsonArray = (JSONArray) getResult.get(ProjectConstant.SUCCESS_LIST);
                return new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), jsonArray.get(0).toString());
            } else {
                return ResultGenerator.genSuccessResult();
            }
        }

        return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
    }


    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public ResultStatus updatePoiSpecialPeriodShippingByShippingAreaId(UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest request) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtChannelPoiShippingServiceImpl.updatePoiShipping  获取app_poi_code失败");
            return ResultGenerator.genFailResult("美团渠道同步配送范围失败");
        }
        ChannelResponseDTO channelResponseDTO;
        Map<String, Object> getResult;
        //保存新配送范围
        bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.MT_AREA, generateArea(request.getCoordinates()));
        bizParam.put(ProjectConstant.MT_MIN_PRICE, String.valueOf(request.getMinOrderPrice()));
        bizParam.put(ProjectConstant.TIME_RANGE, request.getTimeRangeStart() + "-" + request.getTimeRangeEnd());
        bizParam.put(ProjectConstant.MT_TYPE, "1");

        if (request.getShippingAreaId() > 0) {
            // 当请求中业务方配送id不为空时，赋值真实配送id
            bizParam.put(ProjectConstant.MT_SHIPPING_ID, String.valueOf(request.getShippingAreaId()));
        }

        if (StringUtils.isNotBlank(request.getAppShippingCode())) {
            // 当请求中配送id不为空时，赋值真实业务方配送id
            bizParam.put(ProjectConstant.MT_APP_SHIPPING_CODE, String.valueOf(request.getAppShippingCode()));
        }

        // 增加配送费参数
        bizParam.put(ProjectConstant.MT_SHIPPING_FEE, String.valueOf(request.getShippingFee()));

        getResult = drunkHorseChannelGateService.sendPost(shippingSpecSaveUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(getResult)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围失败");
        }
        channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围返回数据解析失败");
        }
        if (channelResponseDTO.isSuccess()) {
            //新增配送范围时回传areaId
            if (request.getShippingAreaId() == 0) {
                JSONArray jsonArray = (JSONArray) getResult.get(ProjectConstant.SUCCESS_LIST);
                return new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), jsonArray.get(0).toString());
            } else {
                return ResultGenerator.genSuccessResult();
            }
        }

        return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public ResultStatus deletePoiShippingByShippingAreaId(DeletePoiShippingByShippingIdRequest request) {
        if (request.getShippingAreaId() <= 0) {
            return ResultGenerator.genFailResult("shippingAreaId不合法");
        }

        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtChannelPoiShippingServiceImpl.deletePoiShipping 获取app_poi_code失败");
            return ResultGenerator.genFailResult("美团渠道删除配送范围失败");
        }
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.MT_SHIPPING_ID, String.valueOf(request.getShippingAreaId()));

        Map<String, Object> resultMap = drunkHorseChannelGateService.sendPost(shippingDeleteUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(resultMap)) {
            return ResultGenerator.genFailResult("调用渠道删除配送范围失败");
        }
        ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(resultMap),
                ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道删除配送范围返回数据解析失败");
        }
        if (channelResponseDTO.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
    }

    private String generateArea(List<Coordinate> coordinates) {
        JSONArray jsonArray = new JSONArray();
        for (Coordinate coordinate : coordinates) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("x", BigDecimal.valueOf(coordinate.getLatitude()).scaleByPowerOfTen(6).intValue());
            jsonObject.put("y", BigDecimal.valueOf(coordinate.getLongitude()).scaleByPowerOfTen(6).intValue());
            jsonArray.add(jsonObject);
        }
        return jsonArray.toJSONString();
    }

    private void fillPoiId(List<PoiShippingAreaInfoDTO> poiShippingInfoDTOS, Long tenantId, Integer channelId,
                           List<Long> shopIds) {
        Map<String, ChannelStoreDO> pois = copChannelStoreService.getChannelPoiCode(tenantId, channelId, shopIds);
        Map<String, Long> channelPoiCode2PoiId = pois.values().stream()
                .collect(Collectors.toMap(ChannelStoreDO::getChannelOnlinePoiCode, ChannelStoreDO::getStoreId));

        poiShippingInfoDTOS.forEach(dto -> {
            dto.setPoiId(channelPoiCode2PoiId.getOrDefault(String.valueOf(dto.getChannelPoiId()), 0L));
            dto.setChannelId(channelId);
        });
    }

    private String getAppPoiCode(long tenantId, int channelId, long shopId) {
        Map<String, ChannelStoreDO> pois = copChannelStoreService.getChannelPoiCode(tenantId, channelId,
                Arrays.asList(shopId));
        if (!pois.containsKey("" + tenantId + "-" + channelId + "-" + shopId)) {
            return null;
        }
        return pois.get("" + tenantId + "-" + channelId + "-" + shopId).getChannelOnlinePoiCode();
    }

    private List<String> getAppPoiCodeList(long tenantId, int channelId, List<Long> shopIds) {
        Map<String, ChannelStoreDO> pois = copChannelStoreService.getChannelPoiCode(tenantId, channelId, shopIds);

        return shopIds.stream().filter(poiId -> pois.containsKey("" + tenantId + "-" + channelId + "-" + poiId))
                .map(poiId -> pois.get("" + tenantId + "-" + channelId + "-" + poiId).getChannelOnlinePoiCode())
                .collect(Collectors.toList());
    }
}
