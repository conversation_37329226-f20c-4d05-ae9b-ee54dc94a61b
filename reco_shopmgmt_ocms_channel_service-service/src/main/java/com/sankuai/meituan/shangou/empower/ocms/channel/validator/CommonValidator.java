package com.sankuai.meituan.shangou.empower.ocms.channel.validator;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.CommonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import java.util.Iterator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;

/**
 * 基础校验类
 *
 * <AUTHOR>
 * @create 2019-01-21 下午3:06
 **/
@Slf4j
public class CommonValidator {
    /**
     * 接口方法路由前基础校验
     *
     * @param request
     * @param <T>
     */
    public static <T> T basicValidate(T request) {
        try {
            Preconditions.checkNotNull(request, "%s param is null", request.getClass().getSimpleName());

            Method[] methods = request.getClass().getMethods();
            Map<String, Method> map = new HashMap<>();
            for (Method method : methods) {
                map.put(method.getName(), method);
            }

            String methodName = "getBaseInfo";
            T baseRequest = null;
            if (map.containsKey(methodName)) {
                baseRequest = (T) map.get(methodName).invoke(request);
                Preconditions.checkNotNull(baseRequest, "%s baseRequest is null", request.getClass().getSimpleName());
                Field channelIdField = baseRequest.getClass().getDeclaredField("channelId");
                int channelId = (int) channelIdField.get(baseRequest);
                Preconditions.checkArgument(ChannelTypeEnum.abbrevMap.containsKey(channelId), "%s channelId=%s is not correct", request.getClass().getSimpleName(), channelId);

            }

            methodName = "getParamList";
            if (map.containsKey(methodName)) {
                T paramList = (T) map.get(methodName).invoke(request);
                Preconditions.checkNotNull(paramList, "%s paramList is null", request.getClass().getSimpleName());
            }

            methodName = "getParam";
            if (map.containsKey(methodName)) {
                T param = (T) map.get(methodName).invoke(request);
                Preconditions.checkNotNull(param, "%s param is null", request.getClass().getSimpleName());
            }

            methodName = "getStoreId";
            if (map.containsKey(methodName)) {
                T paramList = (T) map.get(methodName).invoke(request);
                Preconditions.checkNotNull(paramList, "%s storeId is null", request.getClass().getSimpleName());
            }

            methodName = "getChannelId";
            if (map.containsKey(methodName)) {
                T channelId = (T) map.get(methodName).invoke(request);
                Preconditions.checkNotNull(channelId, "%s channelId is null", request.getClass().getSimpleName());
            }

            return baseRequest;
        } catch (Exception e) {
            log.error("CommonValidator.basicValidate 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    /**
     * 接口方法路由钱基础校验
     *
     * @param request
     * @param <T>
     */
    public static <T> void basicValidatePlus(T request) {
        T baseRequest = basicValidate(request);

        try {
            if (baseRequest instanceof BaseRequest) {
                Preconditions.checkNotNull(((BaseRequest) baseRequest).getStoreIdList(), "%s storeIdList is null", request.getClass().getSimpleName());
            }
        } catch (Exception e) {
            log.error("CommonValidator.basicValidatePlus 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    /**
     * 校验BaseRequest 或 BaseRequestSimple
     * @param request
     * @param <T>
     */
    public static <T> void baseInfoValidate(T request) {
        try {
            if ((request instanceof BaseRequest)
                    || (request instanceof BaseRequestSimple)) {
                Field channelIdField = request.getClass().getDeclaredField("channelId");
                int channelId = (int) channelIdField.get(request);
                Preconditions.checkArgument(ChannelTypeEnum.abbrevMap.containsKey(channelId), "%s channelId=%s is not correct", request.getClass().getSimpleName(), channelId);
            }
        } catch (Exception e) {
            log.error("CommonValidator.baseInfoValidate 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    /**
     * 商品参数校验
     * @param request
     * @return
     */
    public static List<ResultErrorSku> bizParamValidate(SkuInfoRequest request, boolean isCreate) {
        Map<String, String> skuCheckMap = Maps.newHashMapWithExpectedSize(request.getParamListSize());
        Iterator<SkuInfoDTO> skuInfoIterator = request.getParamListIterator();
        boolean isCheck = request.getBaseInfo().getChannelId() != ChannelTypeEnum.MT_MEDICINE.getCode();
        while (skuInfoIterator.hasNext()) {
            SkuInfoDTO skuInfo = skuInfoIterator.next();
            StringBuilder builder = new StringBuilder();
            String skuName = skuInfo.getName().trim();
            if (isCheck && skuName.length() > 45) {
                builder.append(";商品名称超出最大长度45限制");
            }
            if (StringUtils.isNotBlank(skuInfo.getSkuId()) && skuInfo.getSkuId().trim().length() > 40) {
                builder.append(";商品编码超出最大长度40限制");
            }
            if (isCreate && (skuInfo.getPrice() < 0D || skuInfo.getPrice() > 30000D)) {
                builder.append(";价格超出限制0-30000.00");
            }
            if (skuInfo.getStock() < 0 || skuInfo.getStock() > 99999) {
                builder.append(";库存超出限制0-99999");
            }
            if (isCheck && (skuInfo.getWeight() < 0 || skuInfo.getWeight() > 200000)) {
                builder.append(";重量超出限制0-200000");
            }
            if (StringUtils.isNotBlank(skuInfo.getUpc()) && CommonUtils.hasSpecialChar(skuInfo.getUpc().trim())) {
                builder.append(";UPC不能含有特殊字符");
            }
            if (CollectionUtils.isNotEmpty(skuInfo.getPictures()) && skuInfo.getPictures().size() > MccConfigUtil.getChannelSkuImageMaxCount()) {
                builder.append(";图片数量超过最大限制").append(MccConfigUtil.getChannelSkuImageMaxCount());
            }

            //拦截非美团渠道设置的多分类信息（店内分类列表中大于1说明是设置了多分类）
            if(request.getBaseInfo().getChannelId() != ChannelTypeEnum.MEITUAN.getCode()){
                if(CollectionUtils.isNotEmpty(skuInfo.getLeafStoreCategoryList())){
                    if(skuInfo.getLeafStoreCategoryList().size() > 1){
                        builder.append(";非美团渠道,不支持绑定多个店内分类");
                    }
                }
            }

            // 如果参数校验失败，记录失败信息，请求参数移除商品
            if (builder.length() > 0) {
                skuCheckMap.put(skuInfo.getSkuId(), builder.toString().substring(1));
                skuInfoIterator.remove();
            }
        }
        List<ResultErrorSku> resultErrorSkuList = Lists.newArrayList();
        // 如果校验通过直接返回空集合
        if (MapUtils.isEmpty(skuCheckMap)) {
            return resultErrorSkuList;
        }
        // 校验失败，设置失败信息，渠道+门店+商品维度
        BaseRequest baseInfo = request.getBaseInfo();
        baseInfo.getStoreIdList().forEach(storeId -> {
            skuCheckMap.forEach((skuId, msg) -> {
                resultErrorSkuList.add(new ResultErrorSku().setChannelId(baseInfo.getChannelId()).setStoreId(storeId)
                    .setSkuId(skuId).setErrorCode(ResultCodeEnum.FAIL.getValue()).setErrorMsg(msg));
            });
        });
        return resultErrorSkuList;
    }
}
