package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;

import java.util.List;

/**
 * @description: 饿了么授权code获取回调接口，用于获取时长和访问令牌，并保存到数据库中
 * @author: zhangbo86
 * @create: 2020/8/20下午3:19
 */
public interface ChannelAuthorizeCallbackService {


    /**
     * elmtoken
     * @param request
     * @return
     */
    ResultStatus getAuthorizeToken(GetElmAccessTokenRequest request);


    /**
     * jdtoken
     * @param request
     * @return
     */
    ResultStatus jddjToken(JddjAccessTokenRequest request);

    /**
     * yzToken
     * @param request
     * @return
     */
    ResultStatus yzToken(YzAccessCodeRequest request);

    /**
     * 抖音开放平台token
     * 
     * @param request
     * @return
     */
    ResultStatus douyinToken(DouyinAccessCodeRequest request);

    /**
     * 删除抖音token
     *
     * @param request
     * @return
     */
    ResultStatus deleteDouyinToken(DeleteDouyinTokenRequest request) ;

    /**
     * 淘鲜达token获取
     * @param request
     * @return
     */
    TxdTokenInfoDTO txdToken(TxdTokenRequest request);

    /**
     * 直接设置有赞token --- 迁移工具用
     * @param request
     * @return
     */
    ResultStatus yzTokenSet(YzTokenSetRequest request);

    /**
     * 直接设置抖音token --- 迁移工具用
     */
    ResultStatus dyTokenSet(YzTokenSetRequest request);

    /**
     * 获取token —— 迁移工具用
     */
    List<TokenInfo> getToken(TokenGetRequest request);

    /**
     * 刷新token,  目前只有迁移应用会用
     */
    String refreshToken(TokenGetRequest request);

    /**
     * 获取饿了么ISV token
     * @param request
     * @return
     */
    ElmIsvAccessTokenDTO getElmIsvToken(ElmIsvAuthRequest request);

    /**
     * 刷新饿了么ISV token
     * @param request
     * @return
     */
    ElmIsvRefreshTokenDTO refreshElmIsvToken(ElmIsvRefreshTokenRequest request);

    /**
     * 获取美团ISV token
     * @param request
     * @return
     */
    MtIsvAccessTokenDTO getMtIsvToken(MtIsvAuthRequest request);

    /**
     * 刷新美团ISV token
     * @param request
     * @return
     */
    MtIsvAccessTokenDTO  refreshMtIsvToken(MtIsvRefreshTokenRequest request);


    /**
     * 获取有赞ISV token
     * @param request
     * @return
     */
    YouzanIsvTokenDTO getYouzanIsvToken(YouzanIsvAuthRequest request);

    /**
     * 刷新有赞ISV token
     * @param request
     * @return
     */
    YouzanIsvTokenDTO refreshYouzanIsvToken(YouzanIsvRefreshTokenRequest request);

    /**
     * 获取一段时间内的服务市场订单
     * @param request
     * @return
     */
    List<MarketMessageRespDTO> getMarketOrderList(MarketOrderListRequest request);
}
