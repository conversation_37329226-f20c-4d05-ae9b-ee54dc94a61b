package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStockUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelSysException;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelStockService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuPageQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuPageQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.StoreSpuThriftService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 京东到家渠道商品库存内部服务接口
 *
 * <AUTHOR>
 * @create 2019-01-09 下午4:41
 **/

@Service("jddjChannelStockService")
public class JddjChannelStockServiceImpl implements ChannelStockService {
    public static final int PRICE_UPDATE_MAX_COUNT = 50;
    public static final int STOCK_UPDATE_MAX_COUNT = 50;
    /**
     * 3-已上线(已上架+已下架)
     */
    public static final int SPU_ONLINE_STATUS = 3;
    /**
     * 0：成功，1：失败，6,传入的参数列表不能为空;
     */
    public static final int JD_CHANNEL_SKU_EXIST = 0;


    @Resource
    private JddjConverterService jddjConverterService;

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private StoreSpuThriftService storeSpuThriftService;

    @Resource
    private CommonLogger log;

    public ResultData updateStock(SkuStockRequest request) {
        return updateStockCommon(request,ChannelPostJDDJEnum.STOCK_UPDATE);
    }

    public ResultData updateStockForCleaner(SkuStockRequest request) {
        return updateStockCommon(request,ChannelPostJDDJEnum.STOCK_UPDATE_FOR_CLEANER);
    }

    private ResultData updateStockCommon(SkuStockRequest request, ChannelPostInter channelPostInter) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        // 分页调用
        ListUtils.listPartition(request.getSkuStockList(), PRICE_UPDATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuStockDTO::getSkuId).collect(Collectors.toList());
                // 业务参数转换
                ChannelStockUpdateDTO postData = jddjConverterService.updateStock(new SkuStockRequest().setSkuStockList(data));
                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = jddjChannelGateService.sendPostAppMapDto(channelPostInter, request.getBaseInfo(), postData);
                // 组装返回结果
                ResultDataUtils.combineResultData(resultData, postResult);
            } catch (IllegalArgumentException e) {
                log.warn("JddjChannelStockServiceImpl.updateStock 参数校验失败, channelPostInter:{}, data:{}", channelPostInter,data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);
            } catch (InvokeChannelTooMuchException e) {
                log.warn("JddjChannelStockServiceImpl.updateStock 触发限频,channelPostInter:{}, data:{}", channelPostInter,data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList);
            } catch (Exception e) {
                log.warn("JddjChannelStockServiceImpl.updateStock 服务异常,channelPostInter:{}, data:{}", channelPostInter,data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList);
            }
        });
        return resultData;
    }

    @Override
    public ResultSpuData updateStockBySpu(SpuStockRequest request) {
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return ResultGenerator.genResultSpuData(ResultCode.INVALID_PARAM, "同步参数列表为空");
        }

        BaseRequestSimple baseInfo = request.getBaseInfo();
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        /* 门店分组 */
        Map<Long, List<SpuStockDTO>> storeSpuMap = request.getParamList().stream().collect(Collectors.groupingBy(SpuStockDTO::getStoreId));

        try {
            storeSpuMap.forEach((storeId, spuStockDTOList) -> {

                ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(baseInfo.getTenantId(), baseInfo.getChannelId(), storeId);

                List<SkuInSpuStockDetail> skuStockDetailList = new ArrayList<>();
                spuStockDTOList.forEach(spu -> skuStockDetailList.addAll(
                        spu.getSkuStockInfo().stream()
                                .map(it -> SkuInSpuStockDetail.toSkuInSpuStockDetail(it, spu.getCustomSpuId()))
                                .collect(Collectors.toList())));

                ListUtils.listPartition(skuStockDetailList, STOCK_UPDATE_MAX_COUNT).forEach(data -> batchSyncStock(baseInfo, storeId, channelStoreDO, data, resultData));
            });
        } catch (Exception e) {
            log.warn("JddjChannelStockServiceImpl.updateStockBySpu, 批量修改商品库存服务异常, request:{}", request, e);
            return   ResultGenerator.genResultSpuData(ResultCode.FAIL, "JddjChannelStockServiceImpl.updateStockBySpu 批量修改商品库存失败");
        }
        log.info("JddjChannelStockServiceImpl.updateStockBySpu resultData:{}", resultData);
        return resultData;
    }

    @Override
    public ResultData updateStockMultiChannel(SkuStockMultiChannelRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long storeId = request.skuStockList.get(0).storeId;
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.tenantId).setChannelId(request.skuStockList.get(0).channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        // 分页调用
        ListUtils.listPartition(request.skuStockList, PRICE_UPDATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuStockMultiChannelDTO::getSkuId).collect(Collectors.toList());

                // 业务参数转换
                ChannelStockUpdateDTO postData = jddjConverterService.updateStockMultiChannel(new SkuStockMultiChannelRequest().setSkuStockList(data));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = jddjChannelGateService.sendPostAppMapDto(ChannelPostJDDJEnum.STOCK_UPDATE, baseRequest, postData);

                // 组装返回结果
                ResultDataUtils.combinePartResultData(resultData, postResult, bizKeyList);

            } catch (IllegalArgumentException e) {
                log.warn("JddjChannelStockServiceImpl.updateStockMultiChannel 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList, storeId);

            } catch (InvokeChannelTooMuchException e) {
                log.warn("JddjChannelStockServiceImpl.updateStockMultiChannel 触发限频, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList, storeId);
            } catch (Exception e) {
                log.warn("JddjChannelStockServiceImpl.updateStockMultiChannel 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList, storeId);
            }
        });
        return resultData;
    }

    private void batchSyncStock(BaseRequestSimple baseInfo, Long storeId, ChannelStoreDO channelStoreDO, List<SkuInSpuStockDetail> data, ResultSpuData resultData) {
        ResultSpuData temporaryResult = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        try {
            if (channelStoreDO == null) {
                log.error("查询渠道门店信息不存在, storeId:{}", storeId);
                throw new IllegalArgumentException("查询渠道门店信息不存在");
            }
            /*查询计算需要同步的库存*/
            Map<String, ChannelStoreStockDetail> skuStockMap = queryChannelStockQty(baseInfo, channelStoreDO, data.stream().map(SkuInSpuStockDetail::getCustomSkuId).collect(Collectors.toList()));

            Set<String> queryNotExistSkus = calcNeedSyncStock(data, skuStockMap);

            List<SkuInSpuStockDetail> sendDetailList = data.stream().filter(it -> !queryNotExistSkus.contains(it.getCustomSkuId())).collect(Collectors.toList());

            List<SkuInSpuStockDetail> notExistDetailList = data.stream().filter(it -> queryNotExistSkus.contains(it.getCustomSkuId())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(sendDetailList)) {
                // 业务参数转换
                List<SkuStockInfo> skuStockInfoList = jddjConverterService.updateSpuStock(sendDetailList);
                ChannelStockUpdateDTO channelStockUpdateDTO = new ChannelStockUpdateDTO();
                channelStockUpdateDTO.setOutStationNo(channelStoreDO.getChannelOnlinePoiCode());
                channelStockUpdateDTO.setUserPin("system");
                channelStockUpdateDTO.setSkuStockList(skuStockInfoList);

                BaseRequest baseRequest = getBaseRequest(baseInfo, channelStoreDO);

                // 调用渠道接口
                log.info("JddjChannelStockServiceImpl.updateStockBySpu start, baseRequestSimple:{}, channelStoreDO:{}, postData:{},", baseInfo, channelStoreDO, channelStockUpdateDTO);
                ChannelResponseDTO<ChannelStockResponceResult> postResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.STOCK_UPDATE, baseRequest, channelStockUpdateDTO);
                log.info("JddjChannelStockServiceImpl.updateStockBySpu end, baseRequestSimple:{}, channelStoreDO:{}, postData:{}, postResult:{}", baseInfo, channelStoreDO, channelStockUpdateDTO, postResult);

                // 组装返回结果
                installPartResultSpuData(temporaryResult, channelStoreDO.getStoreId(), postResult, sendDetailList);
            }

            installFailResultSpuData(temporaryResult, channelStoreDO.getStoreId(), notExistDetailList, "商品不存在");
            resultData.getSucData().addAll(temporaryResult.getSucData());
            resultData.getErrorData().addAll(temporaryResult.getErrorData());
        } catch (ChannelBizException e) {
            log.warn("JddjChannelStockServiceImpl 查询渠道库存异常, baseInfo:{}, storeId:{}, channelStoreDO:{}, data:{}",
                    baseInfo, storeId, channelStoreDO, data, e);
            installFailResultSpuData(resultData, storeId, data, StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : "调用渠道同步库存异常");
        } catch (InvokeChannelTooMuchException e) {
            log.warn("JddjChannelStockServiceImpl 触发限频, baseInfo:{}, storeId:{}, channelStoreDO:{}, data:{}", baseInfo,
                    storeId, channelStoreDO, data, e);
            installFailResultSpuData(resultData, storeId, data, "触发限频");
        } catch (Exception e) {
            log.warn("JddjChannelStockServiceImpl 同步渠道库存异常, baseInfo:{}, storeId:{}, channelStoreDO:{}, data:{}",
                    baseInfo, storeId, channelStoreDO, data, e);
            installFailResultSpuData(resultData, storeId, data,"调用渠道同步库存异常");
        }
    }

    private static BaseRequest getBaseRequest(BaseRequestSimple baseInfo, ChannelStoreDO channelStoreDO) {
        return new BaseRequest()
                .setChannelId(baseInfo.getChannelId())
                .setTenantId(baseInfo.getTenantId())
                .setAppId(0) // 默认值
                .setStoreIdList(Collections.singletonList(channelStoreDO.getStoreId()));
    }

    private Map<String, ChannelStoreStockDetail> queryChannelStockQty(BaseRequestSimple baseInfo, ChannelStoreDO channelStoreDO, List<String> customSkuIds) {
        ChannelStockQueryByCustomDTO queryDTO = new ChannelStockQueryByCustomDTO();
        queryDTO.setOutStationNo(channelStoreDO.getChannelOnlinePoiCode());
        queryDTO.setUserPin("system");
        queryDTO.setSkuIds(customSkuIds.stream().map(ChannelStockQueryByCustomDTO.SkuIdEntity::new).collect(Collectors.toList()));

        BaseRequest baseRequest = getBaseRequest(baseInfo, channelStoreDO);
        // 调用渠道接口
        ChannelResponseDTO<ChannelStoreStockDetail> postResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.STOCK_QUERY, baseRequest, queryDTO);
        log.info("JddjChannelStockServiceImpl 同步库存查询渠道库存, baseRequestSimple:{}, postData:{}, postResult:{}", baseInfo, queryDTO, postResult);

        if (!postResult.isSuccess() || postResult.getCoreData() == null || !(postResult.getCoreData() instanceof List)) {
            throw new ChannelBizException(Optional.ofNullable(postResult).map(ChannelResponseDTO::getErrorMsg).orElse("调用渠道查询库存异常"));
        }

        return ((List<ChannelStoreStockDetail>) postResult.getCoreData()).stream().filter(it -> it.getCode() == 0).collect(Collectors.toMap(ChannelStoreStockDetail::getOutSkuId, Function.identity(), (f, s) -> s));
    }

    private Set<String> calcNeedSyncStock(List<SkuInSpuStockDetail> skuStockDetailList, Map<String, ChannelStoreStockDetail> skuStockMap) {
        Set<String> queryNotExistSkus = Sets.newHashSet();
        skuStockDetailList.forEach(it -> {
            ChannelStoreStockDetail channelDetail = skuStockMap.get(it.getCustomSkuId());
            if (channelDetail != null && channelDetail.getCode() == 0
                    && channelDetail.getOrderQty() != null && channelDetail.getLockQty() != null) {
                it.setStockQty(it.getStockQty() + channelDetail.getOrderQty() + channelDetail.getLockQty());
            } else {
                queryNotExistSkus.add(it.getCustomSkuId());
            }
        });
        return queryNotExistSkus;
    }

    private void installPartResultSpuData(ResultSpuData resultData, Long storeId, ChannelResponseDTO postResult, List<SkuInSpuStockDetail> skuStockDetailList) {
        if (postResult == null) {
            throw new IllegalStateException("数据为空");
        }

        if (!postResult.isSuccess()) {
            installFailResultSpuData(resultData, storeId, skuStockDetailList, postResult.getErrorMsg());
            return;
        }

        Set<String> sucSkuSet = Sets.newHashSet();
        Map<String, String> failSkuMap = Maps.newHashMap();

        if (postResult.getCoreData() != null && postResult.getCoreData() instanceof List) {
            ((List) postResult.getCoreData()).forEach(item -> {
                ChannelStockResponceResult result = (ChannelStockResponceResult) item;
                if (postResult.isSuccess() && result.isSuccess() ) {
                    sucSkuSet.add(result.getOutSkuId());
                } else {
                    failSkuMap.put(result.getOutSkuId(), result.getMsg());
                }
            });
        }

        List<SkuInSpuStockDetail> sucSkuDetails = Lists.newArrayList();
        skuStockDetailList.forEach(sku -> {
            if (sucSkuSet.contains(sku.getCustomSkuId())) {
                sucSkuDetails.add(sku);
            } else if (failSkuMap.containsKey(sku.getCustomSkuId())) {
                SpuKey spuKey = new SpuKey().setCustomSpuId(sku.getCustomSpuId()).setSkus(Lists.newArrayList(new SkuKey().setCustomSkuId(sku.getCustomSkuId())));
                resultData.getErrorData().add(new ResultErrorSpu().setStoreId(storeId).setSpuInfo(spuKey)
                        .setErrorCode(ResultCodeEnum.FAIL.getValue()).setErrorMsg(failSkuMap.get(sku.getCustomSkuId())));
            } else {
                SpuKey spuKey = new SpuKey().setCustomSpuId(sku.getCustomSpuId()).setSkus(Lists.newArrayList(new SkuKey().setCustomSkuId(sku.getCustomSkuId())));
                resultData.getErrorData().add(new ResultErrorSpu().setStoreId(storeId).setSpuInfo(spuKey)
                        .setErrorCode(ResultCodeEnum.FAIL_ALLOW_RETRY.getValue()).setErrorMsg("系统未同步，请重试"));
            }
        });

        Map<String, List<SkuInSpuStockDetail>> spuMap = sucSkuDetails.stream().collect(Collectors.groupingBy(SkuInSpuStockDetail::getCustomSpuId));
        spuMap.forEach((spuId, detailList) -> {
            SpuKey spuKey = new SpuKey().setCustomSpuId(spuId);
            List<SkuKey> skuKeys = Lists.newArrayList();
            detailList.forEach(it -> skuKeys.add(new SkuKey().setCustomSkuId(it.getCustomSkuId())));
            spuKey.setSkus(skuKeys);

            resultData.getSucData().add(new ResultSuccessSpu().setStoreId(storeId).setSpuInfo(spuKey));
        });
    }

    private void installFailResultSpuData(ResultSpuData resultData, Long storeId, List<SkuInSpuStockDetail> skuStockDetailList, String errMsg) {
        if (CollectionUtils.isEmpty(skuStockDetailList)) {
            return;
        }

        Map<String, List<SkuInSpuStockDetail>> spuMap = skuStockDetailList.stream().collect(Collectors.groupingBy(SkuInSpuStockDetail::getCustomSpuId));
        spuMap.forEach((spuId, detailList) -> {
            SpuKey spuKey = new SpuKey().setCustomSpuId(spuId);
            List<SkuKey> skuKeys = Lists.newArrayList();
            detailList.forEach(it -> {
                skuKeys.add(new SkuKey().setCustomSkuId(it.getCustomSkuId()));
            });
            spuKey.setSkus(skuKeys);

            resultData.getErrorData().add(new ResultErrorSpu().setStoreId(storeId).setSpuInfo(spuKey)
                    .setErrorCode(ResultCodeEnum.FAIL.getValue()).setErrorMsg(errMsg));
        });
    }

    @Override
    public BatchGetStockInfoResponse batchGetStockInfo(BatchGetStockInfoRequest request) {
        BatchGetStockInfoResponse response = new BatchGetStockInfoResponse();
        // 分页查询百川spu信息
        StoreSpuPageQueryResponse spuPageQueryResponse = pageQueryStoreSpu(request);
        PageInfo pageInfo = new PageInfo(spuPageQueryResponse.getPageInfoDTO().getPage(), spuPageQueryResponse.getPageInfoDTO().getSize(), spuPageQueryResponse.getPageInfoDTO().getTotalPage(), spuPageQueryResponse.getPageInfoDTO().getTotal());

        //将StoreSpuDTO按sku打平
        List<ChannelSkuStockDTO> skuStockDTOList = spuDTOSpread2SkuList(spuPageQueryResponse.getStoreSpuList());
        if (CollectionUtils.isEmpty(skuStockDTOList)) {
            log.warn("storeSpuThriftService.pageQueryStoreSpu empty:{}", JacksonUtils.toJson(request));
            return response.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS))
                    .setSkuStocks(Collections.emptyList()).setPageInfo(pageInfo);
        }

        // 查询门店渠道信息
        ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(), request.getStoreId());
        if (Objects.isNull(channelStoreDO) || StringUtils.isBlank(channelStoreDO.getChannelOnlinePoiCode())) {
            throw new IllegalArgumentException("channel store is not exist,storeId:" + request.getStoreId());
        }

        //分批查询渠道库存
        Map<String, ChannelStoreStockDetail> stockDetailMap = Maps.newHashMap();
        ListUtils.listPartition(skuStockDTOList, STOCK_UPDATE_MAX_COUNT).forEach(data -> {
            List<String> customSkuIds = data.stream().map(ChannelSkuStockDTO::getCustomSkuId).collect(Collectors.toList());
            stockDetailMap.putAll(queryChannelStockQty(request.getBaseInfo(), channelStoreDO, customSkuIds));
        });

        //设置渠道库存量
        setChannelStockInfo(skuStockDTOList, stockDetailMap);

        return response.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS))
                .setSkuStocks(skuStockDTOList).setPageInfo(pageInfo);
    }

    /**
     * 设置渠道库存信息
     *
     * @param skuStockDTOList
     * @param stockDetailMap
     * @return
     */
    private void setChannelStockInfo(List<ChannelSkuStockDTO> skuStockDTOList, Map<String, ChannelStoreStockDetail> stockDetailMap) {
        if (CollectionUtils.isEmpty(skuStockDTOList)) {
            return;
        }

        for (ChannelSkuStockDTO skuStockDTO : skuStockDTOList) {
            ChannelStoreStockDetail stockDetail = stockDetailMap.get(skuStockDTO.getCustomSkuId());
            if (Objects.isNull(stockDetail) || JD_CHANNEL_SKU_EXIST != stockDetail.getCode()) {
                //标识此sku在渠道无库存信息
                skuStockDTO.setCustomSkuId(StringUtils.EMPTY);
                log.warn("djapi queryStockCenter failed,channelSkuId:{}", skuStockDTO.getCustomSkuId());
                continue;
            }

            skuStockDTO.setStockQty(Double.valueOf(stockDetail.getUsableQty()));
        }
    }

    /**
     * 分页查询百川spu信息
     *
     * @param request
     * @return
     */
    private StoreSpuPageQueryResponse pageQueryStoreSpu(BatchGetStockInfoRequest request) {
        try {
            StoreSpuPageQueryRequest req = new StoreSpuPageQueryRequest();
            req.setTenantId(request.getBaseInfo().getTenantId());
            req.setStoreIds(Lists.newArrayList(request.getStoreId()));
            req.setChannelIds(Lists.newArrayList(ChannelTypeEnum.JD2HOME.getCode()));
            req.setSpuStatus(SPU_ONLINE_STATUS);
            req.setPage(request.getPageNum());
            req.setPageSize(request.getPageSize());
            StoreSpuPageQueryResponse response = storeSpuThriftService.pageQueryStoreSpu(req);
            if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                throw new ChannelBizException("storeSpuThriftService.pageQueryStoreSpu failed");
            }
            return response;
        } catch (TException e) {
            throw new ChannelSysException("storeSpuThriftService.pageQueryStoreSpu system error",e);
        }
    }

    /**
     * 将StoreSpuDTO按sku打平
     *
     * @param storeSpuDTOS
     * @return
     */
    private List<ChannelSkuStockDTO> spuDTOSpread2SkuList(List<StoreSpuDTO> storeSpuDTOS) {
        if (CollectionUtils.isEmpty(storeSpuDTOS)) {
            return null;
        }

        List<ChannelSkuStockDTO> skuStockDTOList = Lists.newArrayList();
        for (StoreSpuDTO spuDTO : storeSpuDTOS) {
            List<ChannelSpuDTO> channelSpuDTOList = spuDTO.getChannelSpuList().stream().filter(t -> ChannelTypeEnum.JD2HOME.getCode() == t.getChannelId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(channelSpuDTOList)) {
                continue;
            }

            for (ChannelSpuDTO channelSpuDTO : channelSpuDTOList) {
                if (CollectionUtils.isEmpty(channelSpuDTO.getChannelSkuList())) {
                    continue;
                }
                List<ChannelSkuDTO> oneSpuSkuIds = channelSpuDTO.getChannelSkuList();
                if (CollectionUtils.isEmpty(oneSpuSkuIds)) {
                    continue;
                }

                oneSpuSkuIds.forEach(it-> {
                    ChannelSkuStockDTO skuStockDTO = new ChannelSkuStockDTO();
                    skuStockDTO.setSkuId(it.getSkuId());
                    skuStockDTO.setSpuId(it.getSpuId());
                    skuStockDTO.setCustomSkuId(it.getCustomSkuId());
                    skuStockDTOList.add(skuStockDTO);
                });
            }
        }

        return skuStockDTOList;
    }
}