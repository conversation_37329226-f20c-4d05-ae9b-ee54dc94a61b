package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class TxdRefundCancelMsg {
    @JSONField(name = "merchantCode")
    private String merchantCode;
    @JSONField(name = "outOrderId")
    private String outOrderId;
    @JSONField(name = "storeId")
    private String storeId;
    @JSONField(name = "refundId")
    private String refundId;
}
