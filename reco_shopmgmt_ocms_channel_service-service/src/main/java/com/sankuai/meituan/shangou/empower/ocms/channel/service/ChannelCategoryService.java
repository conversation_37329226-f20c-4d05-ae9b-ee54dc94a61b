package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreCategoryTopRequest;

/**
 * @description: 渠道商品品类内部服务接口，各渠道父接口
 * @author: ch<PERSON><PERSON>e
 * @create: 2019/1/7 下午5:27
 */
public interface ChannelCategoryService {

    /**
     * 创建商品前台分类接口
     *
     * @param request
     * @return
     */
    CreateCategoryResponse createCategory(CategoryRequest request);

    /**
     * 修改商品前台分类接口
     *
     * @param request
     * @return
     */
    UpdateCategoryResponse updateCategory(CategoryUpdateRequest request) ;

    /**
     * 删除商品前台分类接口
     * @param request
     * @return
     */
    UpdateCategoryResponse deleteCategory(CategoryDeleteRequest request);

    /**
     * 商品前台分类排序接口
     *
     * @param request
     * @return
     */
    UpdateCategoryResponse sortCategory(CategorySortRequest request);

    /**
     * 拉取渠道后台类目
     * @param req
     * @return
     */
    GetCategoryResponse batchGetCategory(CatRequest req);

    /**
     * 根据一级店内分类id 查询二级店内分类列表
     * @param req
     * @return
     */
    GetCategoryResponse getSecondCategoryByParentId(CatRequest req);

    /**
     * 获取渠道类目相应的商品发布规则
     * @param request
     * @return
     */
    CategoryProductRulesResponse getCategoryProductRules(CategoryAttrRequest request);

    /**
     * 获取渠道后台类目属性
     * @param request
     * @return
     */
    CategoryAttrResponse getCategoryAttr(CategoryAttrRequest request);


    /**
     * 获取渠道后台类目特殊属性值列表
     * @param request
     * @return
     */
    CategoryAttrValueResponse getCategoryAttrValue(CategoryAttrValueRequest request);

    /**
     * 渠道前台类目降级
     * @param request
     * @return
     */
    UpdateCategoryResponse degradeCategory(CategoryDegradeRequest request);

    /**
     * 渠道前台类目等级调整
     * @param request
     * @return
     */
    UpdateCategoryResponse adjustCategoryLevel(CategoryLevelAdjustRequest request);

    /**
     * 修改渠道前台分类的渠道编码（channelCode）
     *
     * @param request
     * @return
     */
    UpdateCategoryResponse updateCategoryChannelCode(CategoryUpdateRequest request);

    /**
     * 更新分类智能排序开关
     * @param request
     * @return
     */
    UpdateCategoryResponse updateSmartSortSwitch(CategorySmartSortSwitchRequest request);

    /**
     * 查询分类智能排序开关
     * @param request
     * @return
     */
    CategorySmartSortQueryResponse queryStoreCategorySmartSort(CategorySmartSortQueryRequest request);

    /**
     * 推荐渠道类目
     * @param request
     * @return
     */
    RecommendCategoryResponse recommendCategory(RecommendCategoryRequest request);


    UpdateCategoryResponse  topCategory(CategoryTopRequest request) ;


    GetCategoryResponse queryStoreCategoryList(CatRequest request);
}
