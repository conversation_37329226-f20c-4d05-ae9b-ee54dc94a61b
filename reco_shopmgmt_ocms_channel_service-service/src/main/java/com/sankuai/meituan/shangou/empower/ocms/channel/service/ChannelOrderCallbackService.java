package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderNotifyRequest;

/**
 * @description: 订单回调接口
 * @author: z<PERSON><PERSON>i12
 * @create: 2019/1/28 上午10:19
 */
public interface ChannelOrderCallbackService {

    ResultStatus orderNotify(OrderNotifyRequest request);
}
