package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuActAllDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.PullPhoneInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.GetAreaListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.GetAreaListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelActDataInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QuerySkuActivityInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuActivityInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.BatchPullPhoneRequest;

import java.util.List;

/**
 * @description: 美团公共业务接口
 * @author: zhaolei12
 * @create: 2019/1/28 上午10:19
 */
public interface MtChannelCommonService {

    List<PullPhoneInfo> batchPullPhoneNumber(BatchPullPhoneRequest request);

    /**
     * 查询门店折扣商品（用于创建活动幂等处理）
     */
    List<ChannelActDataInfoDTO> queryPoiActivity(BaseRequest baseRequest);

    /**
     * 查询指定商品的折扣信息
     *
     * @param request
     * @return
     */
    ChannelSkuActAllDetail querySkuActivityInfoList(QuerySkuActivityInfoRequest request);

    /**
     * 查询地区列表
     *
     * @param request
     * @return
     */
    default GetAreaListResponse getAreaList(GetAreaListRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

}
