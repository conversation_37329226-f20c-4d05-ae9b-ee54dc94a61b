package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.Status;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfosResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtTokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MtIsvTokenTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.TenantStoreKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.sgfnqnh.poi.api.client.thrift.ChannelPoiThriftService;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.ChannelPoiBaseInfoDTO;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.request.RelationQueryByChannelPoiCodesRequest;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.response.PoiChannelPoiRelationResponse;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.AppThriftService;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByPoiRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.response.AppInfoQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/24 19:30
 * @Description:
 */
@Slf4j
@Service
public class ChannelPoiThriftServiceProxy {

    private static final int SUCCESS = 0;

    @Resource
    private ChannelPoiManageThriftService channelPoiManageThriftService;

    @Autowired
    private ChannelThriftService.Iface channelThriftService;

    @Resource(name = "mtTokenRedisClient")
    private RedisStoreClient mtTokenRedisStoreClient;

    @Resource
    private AppThriftService poiChannelAppThriftService;

    @Resource
    private ChannelPoiThriftService channelStoreThriftService;
    
    @Autowired
    private CopAccessConfigService copAccessConfigService;

    /*
     * 获取淘鲜达商家编码
     */
    public String getTenantAppId(long tenantId, long storeId, int channelId) {
        AppInfoQueryByPoiRequest appInfoQueryByPoiRequest = new AppInfoQueryByPoiRequest(tenantId, storeId, channelId);
        try {
            AppInfoQueryResponse response = poiChannelAppThriftService.queryAppInfoByPoi(appInfoQueryByPoiRequest);
            log.info("获取淘鲜达商家编码的response:{}", JacksonUtils.toJson(response));
            if (response == null || response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()
                    || null==response.getData() || StringUtils.isBlank(response.getData().getSubAppKey())) {
                throw new RuntimeException("查询不到应用信息");
            }
            return response.getData().getSubAppKey();
        } catch (Exception e) {
            log.error("根据中台门店查询应用信息异常", e);
            return null;
        }
    }

    /**
     * 同步渠道门店信息
     *
     * @param tenantId
     * @param channelId
     * @param channelPoiCode
     * @return
     */
    public ResultStatus incrementAndSynTenantChannelStoreInfo(Long tenantId, Integer channelId, String channelPoiCode, long appId) {

        try {

            log.info("incrementAndSynTenantChannelStoreInfo: {},{},{}", tenantId, channelId, channelPoiCode);
            ChannelStoreInfoIncrementSynTenantRequest request = new ChannelStoreInfoIncrementSynTenantRequest();
            request.setTenantId(tenantId);
            request.setChannelId(channelId);
            request.setStoreIds(Arrays.asList(channelPoiCode));
            request.setAppId(appId);

            GetChannelStoreInfoResponse response = channelThriftService.getChannelStoreInfoIncrementAndSynTenant(request);

            log.info("resp:{}", response);
            if (response.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(response.getMsg());
        } catch (Exception e) {
            log.error("incrementAndSynTenantChannelStoreInfo 失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }

    }


    /**
     * 同步渠道门店信息
     *
     * @param tenantId
     * @param channelId
     * @param channelPoiCode
     * @return
     */
    public ResultStatus syncChannelPoi(Long tenantId, Integer channelId, String channelPoiCode) {

        try {

            log.info("request: {},{},{}", tenantId, channelId, channelPoiCode);
            Status status = channelPoiManageThriftService.syncSingleChannelPoi(tenantId, channelId, channelPoiCode);

            log.info("resp:{}", status);
            if (status.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(status.getMessage());
        } catch (Exception e) {
            log.error("同步门店失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }

    }

    /**
     * 同步渠道门店信息
     *
     * @param tenantId
     * @param channelId
     * @param channelPoiCode
     * @return
     */
    public ResultStatus unBindChannelPoi(Long tenantId, Integer channelId, String channelPoiCode) {

        try {
            log.info("unBindChannelPoi request: {},{},{}", tenantId, channelId, channelPoiCode);
            StoreKey storeKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + channelPoiCode);
            mtTokenRedisStoreClient.delete(storeKey);
            UnBindChannelStoreRequest unBindChannelStoreRequest = new UnBindChannelStoreRequest();
            unBindChannelStoreRequest.setTenantId(tenantId);
            unBindChannelStoreRequest.setChannelId(channelId);
            unBindChannelStoreRequest.setChannelPoiCode(channelPoiCode);
            UpdateChannelStoreInfoResponse updateChannelStoreInfoResponse = channelThriftService.unBindChannelPoi(unBindChannelStoreRequest);
            log.info("unBindChannelPoi resp:{}", updateChannelStoreInfoResponse);
            if (updateChannelStoreInfoResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(updateChannelStoreInfoResponse.getMsg());
        } catch (Exception e) {
            log.error("解绑渠道门店失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }

    }

    /**
     * 删除token
     * @param tenantId
     * @param channelId
     * @param channelPoiCode
     * @return
     */
    public ResultStatus deleteToke(Long tenantId, Integer channelId, String channelPoiCode) {
        try {
            log.info("deleteToke request: {},{},{}", tenantId, channelId, channelPoiCode);
            StoreKey storeKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + channelPoiCode);
            mtTokenRedisStoreClient.delete(storeKey);
        } catch (Exception e) {
            log.error("解绑渠道门店失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
        return ResultGenerator.genSuccessResult();
    }

    /**
     * 删除token
     * @param tenantId
     * @param channelId
     * @param channelPoiCode
     * @param appKey
     * @return
     */
    public ResultStatus deleteToken(Long tenantId, Integer channelId, String channelPoiCode, String appKey) {
        try {
            log.info("deleteToke request: {},{},{},{}", tenantId, channelId, channelPoiCode,appKey);
            StoreKey storeKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + channelPoiCode + appKey);
            mtTokenRedisStoreClient.delete(storeKey);
        } catch (Exception e) {
            log.error("解绑渠道门店失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
        return ResultGenerator.genSuccessResult();
    }

    /**
     * set美团渠道token
     * @param tenantId
     * @param channelPoiCode
     * @param mtTokenMessage
     * @return
     */
    public ResultStatus setMtToken(Long tenantId, String channelPoiCode, MtTokenMessage mtTokenMessage,String appKey){
        try {
            log.info("setMtToken request: tenantId:{}, channelPoiCode:{}, mtTokenMessage:{}", tenantId, channelPoiCode, channelPoiCode);
            StoreKey storeKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + channelPoiCode);
            // 租户id + 渠道门店id & 租户id + 渠道门店id + appKey 存储
            mtTokenRedisStoreClient.set(storeKey, mtTokenMessage);
            if (StringUtils.isNotBlank(appKey)){
                StoreKey newStoreKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + channelPoiCode + appKey);
                mtTokenRedisStoreClient.set(newStoreKey, mtTokenMessage);
            }
        } catch (Exception e) {
            log.error("set美团渠道token失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
        return ResultGenerator.genSuccessResult();
    }

    public Map<Long, ChannelPoiInfoDTO> queryDrunkHorseChannelPoiInfos(Long tenantId, List<Long> storeIds) {
        try {

            log.info("request: {},{}", tenantId, storeIds);
            PoiDetailInfosResponse response = channelPoiManageThriftService
                    .queryPoiDetailInfoByPoiIdsAndChannelIds(tenantId, storeIds, Lists.newArrayList(ChannelTypeEnum.MT_DRUNK_HORSE.getCode()), false, true);
            log.info("resp:{}", response);

            if (Objects.nonNull(response)
                    && CollectionUtils.isNotEmpty(response.getPoiDetailInfoDTOs())
                    && response.getStatus().getCode() == SUCCESS) {
                return response.getPoiDetailInfoDTOs()
                        .stream()
                        .filter(poi -> org.apache.commons.collections.CollectionUtils.isNotEmpty(poi.getChannelPoiInfoList()))
                        .flatMap(poi -> poi.getChannelPoiInfoList().stream())
                        .filter(poi -> Objects.nonNull(poi) && poi.getChannelId() == ChannelTypeEnum.MT_DRUNK_HORSE.getCode())
                        .collect(Collectors.toMap(dto -> dto.getPoiId(), it -> it, (oldValue, newValue) -> newValue));

            }
        } catch (Exception e) {
            log.error("同步门店失败", e);
        }
        return new HashMap<>();
    }

    public PoiDetailInfoDTO queryPoiDetailInfoByPoiId(Long tenantId, Long storeId) {

        try {
            PoiDetailInfoResponse response = channelPoiManageThriftService.queryPoiDetailInfoByPoiId(tenantId, storeId);
            if (response == null || response.getPoiDetailInfoDTO() == null) {
                return null;
            }
            PoiDetailInfoDTO poiDetailInfo = response.getPoiDetailInfoDTO();
            return poiDetailInfo;
        } catch (Exception e) {
            log.error("queryPoiDetailInfoByPoiId error tenantId:{},storeId:{}", tenantId, storeId);
        }
        return null;
    }

    public TenantStoreKey getTenantStoreKeyByChannelPoiCode(String appId, Integer channelId, String channelPoiCode) {
        List<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTOList = queryRelationByChannelPoiCode(appId, channelId, channelPoiCode);
        if (CollectionUtils.isEmpty(channelPoiBaseInfoDTOList)) {
            throw new ChannelBizException("未查询到渠道门店与牵牛花门店关系");
        }
        ChannelPoiBaseInfoDTO baseInfoDTO = channelPoiBaseInfoDTOList.get(0);
        return new TenantStoreKey(baseInfoDTO.getTenantId(), baseInfoDTO.getPoiId());
    }

    /**
     * 根据appKey、渠道id、三方门店id 查询中台门店信息
     * @param appKey
     * @param channelId
     * @param channelPoiCode
     * @return
     */
    public List<ChannelPoiBaseInfoDTO> queryRelationByChannelPoiCode(String appKey, Integer channelId, String channelPoiCode){
        List<ChannelPoiBaseInfoDTO> result = new ArrayList<>();
        try{
            RelationQueryByChannelPoiCodesRequest request = new RelationQueryByChannelPoiCodesRequest();
            request.setChannelId(channelId);
            request.setChannelPoiCodes(Lists.newArrayList(channelPoiCode));
            PoiChannelPoiRelationResponse response = channelStoreThriftService.queryRelationByChannelPoiCodes(request);
            if (response == null || response.getStatus() == null || response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                throw new ChannelBizException("查询渠道门店与牵牛花门店关系异常");
            }
            List<ChannelPoiBaseInfoDTO> allRelations = response.getChannelPoiBaseInfoDTOList();
            if (CollectionUtils.isEmpty(allRelations)) {
                return result;
            }

            // 查cop
            List<Long> tenantIdList = Fun.map(allRelations, ChannelPoiBaseInfoDTO::getTenantId);
            List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.selectByTenantAppIdChannelIdTenantIds(appKey, channelId, tenantIdList);

            // by tenantId + app_id 过滤
            Set<String> copTenantAndAppIdSet = Fun.map(copAccessConfigDOList, k -> k.getTenantId() + "_" + k.getAppId(), HashSet::new);
            result = Fun.filter(allRelations, k -> copTenantAndAppIdSet.contains(k.getTenantId() + "_" + k.getQnhAppId()));

        }catch (Exception e) {
            log.error("查询渠道门店与牵牛花门店关系异常, channelId={}, channelPoiCode={}", channelId, channelPoiCode);
            return result;
        }
        return result;
    }
}
