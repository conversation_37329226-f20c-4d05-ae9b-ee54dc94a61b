package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelPriceQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelPriceUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPriceService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;

import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 京东到家渠道商品价格内部服务接口
 *
 * <AUTHOR>
 * @create 2019-01-09 下午4:40
 **/
@Service("jddjChannelPriceService")
public class JddjChannelPriceServiceImpl implements ChannelPriceService {
    public static final int PRICE_UPDATE_MAX_COUNT = 50;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private JddjConverterService jddjConverterService;

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private CommonLogger log;

    @Override
    @Deprecated
    public ResultData updatePrice(SkuPriceRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        // 分页调用
        ListUtils.listPartition(request.getParamList(), PRICE_UPDATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuPriceDTO::getSkuId).collect(Collectors.toList());

                // 业务参数转换
                ChannelPriceUpdateDTO postData = jddjConverterService.updatePrice(new SkuPriceRequest().setParamList(data));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = jddjChannelGateService.sendPost(ChannelPostJDDJEnum.PRICE_UPDATE, getBaseRequest(request), postData);

                // 组装返回结果
                ResultDataUtils.combineResultData(resultData, postResult);

            } catch (IllegalArgumentException e) {
                log.error("JddjChannelPriceServiceImpl.updatePrice 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);

            } catch (Exception e) {
                log.error("JddjChannelPriceServiceImpl.updatePrice 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList);
            }
        });

        return resultData;
    }

    @Override
    public ResultData updatePriceMultiChannel(SkuPriceMultiChannelRequest request) {
        return updatePriceMultiChannelCommon(request, ChannelPostJDDJEnum.PRICE_UPDATE);
    }

    @Override
    public ResultData updatePriceMultiChannelForCleaner(SkuPriceMultiChannelRequest request) {
        return updatePriceMultiChannelCommon(request, ChannelPostJDDJEnum.PRICE_UPDATE_FOR_CLEANER);
    }

    private ResultData updatePriceMultiChannelCommon(SkuPriceMultiChannelRequest request, ChannelPostInter channelPostInter) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long storeId = request.getParamList().get(0).getStoreId();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.tenantId).setChannelId(request.paramList.get(0).channelId)
                .setStoreIdList(Lists.newArrayList(storeId)).setAsyncInvoke(request.isAsyncInvoke());

        // 分页调用
        for (List<SkuPriceMultiChannelDTO> datas : ListUtils.listPartition(request.getParamList(), PRICE_UPDATE_MAX_COUNT)) {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = datas.stream().map(SkuPriceMultiChannelDTO::getSkuId).collect(Collectors.toList());

                // 业务参数转换
                ChannelPriceUpdateDTO dto = jddjConverterService.updatePriceMultiChannel(new SkuPriceMultiChannelRequest().setParamList(datas));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = jddjChannelGateService.sendPostAppMapDto(channelPostInter, baseRequest, dto);

            // 组装返回结果
            ProductResultDataUtils.combinePartResultData(resultData, postResult, bizKeyList, true, ChannelTypeEnum.JD2HOME);

            } catch (IllegalArgumentException e) {
                log.error("JddjChannelPriceServiceImpl.updatePriceMultiChannel 参数校验失败, data:{}", datas, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList, storeId);
            } catch (InvokeChannelTooMuchException e) {
                log.warn("JddjChannelPriceServiceImpl.updatePriceMultiChannel 调用渠道触发限流, data:{}",
                        datas);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.TRIGGER_LIMIT, bizKeyList, storeId);
            } catch (Exception e) {
                log.error("JddjChannelPriceServiceImpl.updatePriceMultiChannel 服务异常, data:{}", datas, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList, storeId);
            }
        }
        return resultData;
    }

    @Override
    @Deprecated
    public List<ChannelStorePriceInfo> batchGetPriceInfo(BaseRequest request, List<Long> skuIds){
        List<ChannelStorePriceInfo> result = Lists.newArrayList();

        // 分页调用
        ListUtils.listPartition(skuIds, PRICE_UPDATE_MAX_COUNT).forEach(data -> {
            try {
                // 业务参数转换
                ChannelPriceQueryDTO postData = new ChannelPriceQueryDTO("", "",data);

                // 调用渠道接口
                Map<Long, ChannelResponseDTO<ChannelStorePriceInfo>> postResult = jddjChannelGateService.sendPost(ChannelPostJDDJEnum.SHOP_PRICE_LIST, request, postData);

                // 组装返回结果
                postResult.entrySet().forEach(entry -> {
                    result.addAll((List<ChannelStorePriceInfo>)entry.getValue().getCoreData());
                });

            } catch (IllegalArgumentException e) {
                log.error("JddjChannelPriceServiceImpl.batchGetPriceInfo 参数校验失败, request:{}, skuIds:{}", request, skuIds, e);

            } catch (Exception e) {
                log.error("JddjChannelPriceServiceImpl.batchGetPriceInfo 服务异常, request:{}, skuIds:{}", request, skuIds, e);
            }
        });
        return result;
    }

    @Override
    public BatchGetSkuPriceResponse batchGetSkuPrice(BatchGetSkuPriceRequest request) {
        BatchGetSkuPriceResponse response= new BatchGetSkuPriceResponse();
        response.setStatus(ResultGenerator.genSuccessResult());

        if (CollectionUtils.isEmpty(request.getChannelSkuIds())) {
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "sku列表不能为空"));
            return response;
        }

        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        baseRequest.setStoreIdList(Collections.singletonList(request.getStoreId()));
        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(baseRequest.getTenantId(), baseRequest.getChannelId(), request.getStoreId());
        if (channelStore == null) {
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店信息失败"));
            return response;
        }

        ChannelPriceQueryDTO queryDTO = new ChannelPriceQueryDTO();
        queryDTO.setStationNo(channelStore.getChannelPoiCode());
        queryDTO.setSkuIds(Fun.map(request.getChannelSkuIds(), Long::parseLong));

        ChannelResponseDTO<List<ChannelStorePriceInfo>> priceResult =
                jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SHOP_PRICE_LIST, baseRequest, queryDTO);

        if(priceResult == null){
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "京东接口返回值为空"));
            return response;
        }

        if (!priceResult.isSuccess()) {
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, priceResult.getErrorMsg()));
            return response;
        }

        List<SkuPriceInfoDTO> skuPriceDTOList = Fun.map(priceResult.getCoreData(), channelStorePriceInfo -> {
            SkuPriceInfoDTO dto = new SkuPriceInfoDTO();
            dto.setChannelSkuId(String.valueOf(channelStorePriceInfo.getSkuId()));
            dto.setPrice(channelStorePriceInfo.getPrice());
            return dto;
        });
        response.setSkuPriceInfos(skuPriceDTOList);
        return response;
    }
}
