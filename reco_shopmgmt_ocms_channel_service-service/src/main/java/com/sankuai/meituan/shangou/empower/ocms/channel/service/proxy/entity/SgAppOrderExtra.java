/*
 * Copyright (c) 2010-2011 meituan.com
 * All rights reserved.
 * 
 */
package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

/**
 * <AUTHOR>
 */
public class SgAppOrderExtra {

    private Float reduce_fee;
    private String remark;
    private Integer type;
    private Integer act_detail_id;//批量设置活动id
    private Float mt_charge;
    private Float poi_charge;
    private String discharge_detail;
    private String gift_name;
    /**20201224满赠｜买赠活动赠品信息**/
    private String gift_app_food_code;
    private String gift_sku_id ;
    private Integer gift_num;//默认份数为1
    private Integer type_9_coupon_type;

    @Override
    public String toString() {
        return "SgAppOrderExtra{" +
                "reduce_fee=" + reduce_fee +
                ", remark='" + remark + '\'' +
                ", type=" + type +
                ", act_detail_id=" + act_detail_id +
                ", mt_charge=" + mt_charge +
                ", poi_charge=" + poi_charge +
                ", discharge_detail='" + discharge_detail + '\'' +
                ", gift_name='" + gift_name + '\'' +
                ", gift_app_food_code='" + gift_app_food_code + '\'' +
                ", gift_sku_id='" + gift_sku_id + '\'' +
                ", gift_num=" + gift_num +
                ", type_9_coupon_type=" + type_9_coupon_type +
                '}';
    }

    public Float getReduce_fee() {
        return reduce_fee;
    }

    public void setReduce_fee(Float reduce_fee) {
        this.reduce_fee = reduce_fee;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getAct_detail_id() {
        return act_detail_id;
    }

    public void setAct_detail_id(Integer act_detail_id) {
        this.act_detail_id = act_detail_id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Float getMt_charge() {
        return mt_charge;
    }

    public void setMt_charge(Float mt_charge) {
        this.mt_charge = mt_charge;
    }

    public Float getPoi_charge() {
        return poi_charge;
    }

    public void setPoi_charge(Float poi_charge) {
        this.poi_charge = poi_charge;
    }

    public String getDischarge_detail() {
        return discharge_detail;
    }

    public void setDischarge_detail(String discharge_detail) {
        this.discharge_detail = discharge_detail;
    }

    public String getGift_name() {
        return gift_name;
    }

    public void setGift_name(String gift_name) {
        this.gift_name = gift_name;
    }

    public String getGift_app_food_code() {
        return gift_app_food_code;
    }

    public void setGift_app_food_code(String gift_app_food_code) {
        this.gift_app_food_code = gift_app_food_code;
    }

    public String getGift_sku_id() {
        return gift_sku_id;
    }

    public void setGift_sku_id(String gift_sku_id) {
        this.gift_sku_id = gift_sku_id;
    }

    public Integer getGift_num() {
        return gift_num;
    }

    public void setGift_num(Integer gift_num) {
        this.gift_num = gift_num;
    }

    public Integer getType_9_coupon_type() {
        return type_9_coupon_type;
    }

    public void setType_9_coupon_type(Integer type_9_coupon_type) {
        this.type_9_coupon_type = type_9_coupon_type;
    }
}
