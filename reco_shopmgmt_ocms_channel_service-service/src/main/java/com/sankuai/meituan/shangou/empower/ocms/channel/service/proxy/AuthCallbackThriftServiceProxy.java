package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.AuthCallbackThriftService;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.common.Status;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.common.enums.StatusCodeEnum;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.auth.request.MtIsvAuthCallbackRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.auth.request.SetAccessConfigRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.auth.response.MtAuthCallbackResponse;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.auth.response.SetAccessConfigResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @program: reco_shopmgmt_ocms_channel_service
 * @description: poi-channel授权回调facade
 * @author: jinyi
 * @create: 2024-04-24 19:35
 **/
@Service
@Slf4j
public class AuthCallbackThriftServiceProxy {

    @Resource
    private AuthCallbackThriftService authCallbackThriftService;

    public ResultStatus setAccessConfig(SetAccessConfigRequest request) {
        try {
            log.info("setAccessConfig request:{}", request);
            SetAccessConfigResponse response = authCallbackThriftService.setAccessConfig(request);
            log.info("setAccessConfig response:{}", response);
            if (response.getStatus().getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(response.getStatus().getMessage());
        } catch (Exception e) {
            log.error("setAccessConfig失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    public MtAuthCallbackResponse mtAuthCallback(MtIsvAuthCallbackRequest request){

        try {
            log.info("mtAuthCallback request:{}", request);

            MtAuthCallbackResponse response = authCallbackThriftService.mtAuthCallback(request);

            if (response.getTenantAppInfo() == null){
                log.info("authCallbackThriftService.mtAuthCallback返回的租户应用信息为空，request={}", request);
                response.setStatus(Status.build(StatusCodeEnum.SERVER_SYS_ERROR));
            }

            log.info("mtAuthCallback response:{}", response);
            return response;
        } catch (Exception e) {
            log.error("mtAuthCallback失败", e);

            MtAuthCallbackResponse response = new MtAuthCallbackResponse();
            response.setStatus(Status.build(StatusCodeEnum.SERVER_SYS_ERROR));

            return response;
        }
    }
}
