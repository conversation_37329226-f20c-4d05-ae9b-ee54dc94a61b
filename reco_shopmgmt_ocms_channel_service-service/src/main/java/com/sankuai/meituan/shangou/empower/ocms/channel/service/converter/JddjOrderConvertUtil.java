package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.dianping.lion.client.Lion;
import com.google.common.base.Preconditions;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelOrderDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JddjVenderAfsSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.OaosAdjustDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.OrderSkuDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ChannelSettleStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.AdjustOrderProductInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.MoneyRefundProductInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.MoneyRefundRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiPartRefundRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/***
 * author : <EMAIL> 
 * data : 2019/6/18 
 * time : 下午2:33
 **/
@Slf4j
public class JddjOrderConvertUtil {

    public static List<JddjVenderAfsSkuDTO> convertPartRefundSku(PoiPartRefundRequest request, ChannelOrderDetail channelOrderDetail) {

        return request.getRefundProducts().stream().map(e -> {
            JddjVenderAfsSkuDTO afsSkuDTO = new JddjVenderAfsSkuDTO();
            afsSkuDTO.setSkuCount(e.getCount());
            String skuIdIsv = StringUtils.defaultIfBlank(e.getSkuId(), e.getCustomSkuId());
            //查找线上skuId
            OrderSkuDetail skuDetail = channelOrderDetail.getProduct().stream().filter(orderSkuDetail -> StringUtils.equals(orderSkuDetail.getSkuIdIsv(), skuIdIsv)).findAny().get();
            afsSkuDTO.setSkuId(skuDetail.getSkuId());
            afsSkuDTO.setPromotionType(skuDetail.getPromotionType());
            // 查询开关
            Boolean actualWeightSwitch = Lion.getConfigRepository().getBooleanValue("channel.jjdj.refund.actualweight", true);
            // 查询实际重量
            if (actualWeightSwitch && Objects.nonNull(skuDetail.getSkuWeight())) {
                // 单位转为g
                afsSkuDTO.setActualWeight(BigDecimal.valueOf(skuDetail.getSkuWeight() * 1000).setScale(2,RoundingMode.HALF_UP).doubleValue());
            }
            return afsSkuDTO;
        }).collect(Collectors.toList());
    }

    public static List<JddjVenderAfsSkuDTO> convertMoneyRefundSku(MoneyRefundRequest request, ChannelOrderDetail channelOrderDetail){
        List<JddjVenderAfsSkuDTO> result = Lists.newArrayList();
        for (MoneyRefundProductInfoDTO moneyRefundProductInfoDTO : request.getRefundProductInfoList()){
            JddjVenderAfsSkuDTO afsSkuDTO = new JddjVenderAfsSkuDTO();
            //部分退
            afsSkuDTO.setIsPartialRefund(1);
            afsSkuDTO.setPartialRefundMoney((long)(moneyRefundProductInfoDTO.getRefundAmount()));
            //查找线上skuId
            List<OrderSkuDetail> skuDetailList = channelOrderDetail.getProduct().stream()
                    .filter(orderSkuDetail -> StringUtils.equals(orderSkuDetail.getSkuIdIsv(), moneyRefundProductInfoDTO.getCustomSkuId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skuDetailList)){
                log.error("京东金额退错误，渠道订单未找到明细req:{},detail{}", request, channelOrderDetail);
                continue;
            }
            Optional<OrderSkuDetail> matchItem = skuDetailList.stream().filter(v -> Objects.equals(v.getSkuJdPrice(), moneyRefundProductInfoDTO.getCurrentPrice())).findFirst();
            OrderSkuDetail targetSkuDetail = matchItem.orElseGet(() -> skuDetailList.get(0));
            afsSkuDTO.setSkuId(targetSkuDetail.getSkuId());
            afsSkuDTO.setPromotionType(targetSkuDetail.getPromotionType());
//            try{
//                afsSkuDTO.setSkuCount(BigDecimal.valueOf(moneyRefundProductInfoDTO.getRefundAmount()).divide(BigDecimal.valueOf(targetSkuDetail.getSkuJdPrice()), 0, RoundingMode.CEILING).intValue());
//            }catch (Exception exception){
//                log.error("京东金额退，计算退款数量出错{}", request, exception);
//                afsSkuDTO.setSkuCount(targetSkuDetail.getSkuCount());
//            }
            afsSkuDTO.setSkuCount(targetSkuDetail.getSkuCount());
            // 查询开关
            Boolean actualWeightSwitch = Lion.getConfigRepository().getBooleanValue("channel.jjdj.refund.actualweight", true);
            // 查询实际重量
            if (actualWeightSwitch && Objects.nonNull(targetSkuDetail.getSkuWeight())) {
                // 单位转为g
                afsSkuDTO.setActualWeight(BigDecimal.valueOf(targetSkuDetail.getSkuWeight() * 1000).setScale(2,RoundingMode.HALF_UP).doubleValue());
            }
            result.add(afsSkuDTO);
        }
        return result;
    }


    public static  int convertSettleStatusCode(int jdSettleStatusCode){

        switch (jdSettleStatusCode){
            case 20002:
                return ChannelSettleStatus.WAIT_TO_SETTLE.getValue();
            case 20003:
                return ChannelSettleStatus.SETTLE_SUCCESS.getValue();
            case 20004:
            case 20011:
                return ChannelSettleStatus.SETTLE_FAIL.getValue();
            case 20005:
                return ChannelSettleStatus.SETTLE_REJECT.getValue();
            case 20009:
                return ChannelSettleStatus.SETTLE_PAY_FAIL.getValue();
            case 20014:
                return ChannelSettleStatus.SETTLE_FREEZE.getValue();
            case 20015:
                return ChannelSettleStatus.PART_SETTLE_SUCCESS.getValue();
            default:
                log.error("京东不识别的结算单状态:{}", jdSettleStatusCode);
                return ChannelSettleStatus.UNKNOWN.getValue();
        }
    }


    public static  int connvertSettlePayMethod(int payMethod){
        switch (payMethod){
            case 1:
                break;
            case 2:
                break;
            case 3:
                break;
            default:

        }
        return 0;
    }

    public static List<OaosAdjustDTO> adjustDtos(List<AdjustOrderProductInfoDTO> adjustOrderProductInfoDTOs) {
        if (adjustOrderProductInfoDTOs != null){
            return adjustOrderProductInfoDTOs.stream().map(e->adjustDto(e)).collect(Collectors.toList());
        }
        return Lists.emptyList();
    }

    public static OaosAdjustDTO adjustDto(AdjustOrderProductInfoDTO adjustOrderProductInfoDTO) {

        if ( adjustOrderProductInfoDTO == null ) {
            return null;
        }
        OaosAdjustDTO oaosAdjustDTO = new OaosAdjustDTO();

        // 退款（调整）订单商品数量
        oaosAdjustDTO.setSkuCount(adjustOrderProductInfoDTO.getRemindSkuCount());
        // SkuId不存在时
        if (StringUtils.isBlank(adjustOrderProductInfoDTO.getSkuId()) && StringUtils.isNotBlank(adjustOrderProductInfoDTO.getExtCustomSkuId())) {
            oaosAdjustDTO.setSkuId(Long.valueOf(adjustOrderProductInfoDTO.getExtCustomSkuId()));
            return oaosAdjustDTO;
        }
        Preconditions.checkArgument(org.apache.commons.lang.StringUtils.isNotBlank(adjustOrderProductInfoDTO.getSkuId()), "skuId与extCustomSkuId不能同时为空");

        oaosAdjustDTO.setOutSkuId(adjustOrderProductInfoDTO.getSkuId());
        return oaosAdjustDTO;
    }

    public static  boolean isYuYueDelivery(ChannelOrderDetail channelOrderDetail){
        String businessTag = channelOrderDetail.getBusinessTag();
        return StringUtils.contains(businessTag, "one_dingshida") || StringUtils.contains(businessTag, "dj_aging_nextday"); //定时达 和隔夜达
    }

    /**
     * 是否营业即送
     * @param channelOrderDetail
     * @return
     */
    public static  boolean isOpeningDelivery(ChannelOrderDetail channelOrderDetail){
        String businessTag = channelOrderDetail.getBusinessTag();
        return StringUtils.contains(businessTag, "dj_aging_nextday"); //隔夜达
    }

    public static int isSelfDelivery(String deliveryCarrierNo) {
        //自配送
        if ("2938".equals(deliveryCarrierNo)) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 运费活动
     */
    public static boolean isLogisticsActivity(Integer discountType) {
        return Arrays.asList(7, 8, 12, 15, 16, 18).contains(discountType);
    }

    /**
     * 平台运费活动
     * @param discountType
     * @return
     */
    public static boolean isPlatformLogisticsActivity(Integer discountType) {
        return Arrays.asList(7, 12, 18).contains(discountType);
    }

    /**
     * 商家运费活动
     * @param discountType
     * @return
     */
    public static boolean isPoiLogisticsActivity(Integer discountType) {
        return Objects.equals(discountType, 8);
    }

    /**
     * 商家、平台分摊运费活动
     * @param discountType
     * @return
     */
    public static boolean isShareLogisticsActivity(Integer discountType) {
        return Arrays.asList(15, 16).contains(discountType);
    }
    public static String userPrivacyPhone(String lastFourDigitsOfBuyerMobile) {
        if (StringUtils.isBlank(lastFourDigitsOfBuyerMobile)) {
            return "***********";
        }

        return "*******" + lastFourDigitsOfBuyerMobile;
    }

    /**
     * 解析原始配送类型
     *
     * @param deliveryCarrierNo 承运商编号
     * @return
     */
    public static int convertDeliveryType(String deliveryCarrierNo) {
        if (StringUtils.isBlank(deliveryCarrierNo)) {
            return DistributeTypeEnum.UN_KNOWN.getValue();
        }

        switch (deliveryCarrierNo) {
            case "9966":
                return DistributeTypeEnum.JDDJ_DADA_ZHAUN_SONG.getValue();
            case "3587":
                return DistributeTypeEnum.JDDJ_TONG_CHENG.getValue();
            case "2938":
                return DistributeTypeEnum.SELF_DELIVERY.getValue();
            default:
                return DistributeTypeEnum.UN_KNOWN.getValue();
        }
    }

    /**
     * 根据promotionType=6买赠，判断是否为赠品，转换itemType
     * @param orderSkuDetail
     * @return itemType（0-正常商品，1-赠品）
     */
    public static Integer extractItemType(OrderSkuDetail orderSkuDetail) {
        if (orderSkuDetail.getPromotionType() == null) {
            return 0;
        }
        if (orderSkuDetail.getPromotionType() == 6) {
            return 1;
        }
        return 0;
    }

    public static String convertSkuProperty(String skuCostumeProperty) {
        if (StringUtils.isBlank(skuCostumeProperty)) {
            return null;
        }
        StringBuilder result = new StringBuilder();
        String[] properties = skuCostumeProperty.split(";");
        for (String property : properties) {
            if (StringUtils.isNotBlank(property)) {
                if (result.length() > 0) {
                    result.append(",");
                }
                result.append(property);
            }
        }
        if (result.length() == 0) {
            return null;
        }
        return result.toString();
    }
}
