package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JddjCommentInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JddjResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCommentService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ChannelCommentConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 京东到家渠道评价内部服务接口
 *
 * <AUTHOR>
 */
@Service("jddjChannelCommentService")
public class JddjChannelCommentServiceImpl implements ChannelCommentService {

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private CommonLogger log;

    @Override
    public CommentListQueryResponse queryCommentList(CommentListQueryRequest request) {
        CommentListQueryResponse response = new CommentListQueryResponse();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
                .setChannelId(request.getChannelId());
        addStoreId2BaseRequest(request.getStoreId(), baseRequest);
        // 构建评价查询业务参数
        Map<String, Object> bizParam = ChannelCommentConverterUtil.buildJddjCommentQueryBizParam(request);

        // 调用评价查询接口
        ChannelResponseDTO<JddjCommentInfo> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.COMMENT_QUERY,
                baseRequest, bizParam);
        // 解析评价查询结果
        if (!Objects.isNull(channelResponseDTO)) {
            // 平台级结果判断
            String apiCode = channelResponseDTO.getCode();
            if (JddjResultCodeEnum.ERROR_10032.getCode().equals(apiCode)) {
                return response.setStatus(ResultGenerator.genResult(ResultCode.TRIGGER_LIMIT, channelResponseDTO.getMsg()));
            }
            if (!JddjResultCodeEnum.SUCCESS.getCode().equals(apiCode)) {
                return response.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getMsg()));
            }

            // 业务级结果判断
            String bizCode = channelResponseDTO.getDataResponse().getCode();
            if (JddjResultCodeEnum.COMMENT_BIZ_SUCCESS.getCode().equals(bizCode)) {
                List<ChannelCommentDTO> channelCommentDTOList = null;
                if (channelResponseDTO.getDataResponse().getResultData() != null) {
                    ChannelCommentDTO channelCommentDTO = channelResponseDTO.getDataResponse()
                            .getResultData().convertToChannelCommentDTO(request.getTenantId());
                    if(channelCommentDTO == null){
                        channelCommentDTOList = Collections.EMPTY_LIST;
                        log.warn("jddj convertToChannelCommentDTO return null,channelCommentDTO:{}",channelResponseDTO.getDataResponse()
                                .getResultData());
                    }else{
                        channelCommentDTOList = Collections.singletonList(channelCommentDTO);
                    }

                }
                return response.setStatus(ResultGenerator.genSuccessResult()).setCommentDTOList(channelCommentDTOList);
            } else if (JddjResultCodeEnum.COMMENT_ORDER_NOT_EXISTS_COMMENT.getCode().equals(bizCode)) {
                return response.setStatus(ResultGenerator.genSuccessResult()).setCommentDTOList(Collections.EMPTY_LIST);
            } else {
                return response.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg()));
            }
        } else {
            return response.setStatus(ResultGenerator.genFailResult("调用京东到家评价查询接口失败"));
        }
    }

    @Override
    public CommentReplyResponse reply(CommentReplyRequest request) {

        CommentReplyResponse response = new CommentReplyResponse();

        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
                .setChannelId(request.getChannelId());

        // 校验渠道门店信息, 如果渠道门店信息不存在, 直接返回
        ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(request.getTenantId(),
                request.getChannelId(), request.getStoreId());
        if (channelStoreDO == null) {
            log.warn("渠道门店未启用, {}", KeyUtils.genChannelStoreKey(request.getTenantId(), request.getChannelId(),
                    request.getStoreId()));
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道门店未启用"));
        }

        addStoreId2BaseRequest(request.getStoreId(), baseRequest);
        // 构建评价回复业务参数
        Map<String, Object> bizParam = ChannelCommentConverterUtil.buildJddjCommentReplyBizParam(request, channelStoreDO);

        // 调用评价回复接口
        ChannelResponseDTO<JddjCommentInfo> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.COMMENT_REPLY,
                baseRequest, bizParam);

        // 解析评价回复结果
        if (!Objects.isNull(channelResponseDTO)) {
            // 平台级结果判断
            if (!JddjResultCodeEnum.SUCCESS.getCode().equals(channelResponseDTO.getCode())) {
                return response.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getMsg()));
            }

            // 业务级结果判断
            String bizCode = channelResponseDTO.getDataResponse().getCode();
            if (JddjResultCodeEnum.COMMENT_BIZ_SUCCESS.getCode().equals(bizCode)) {
                return response.setStatus(ResultGenerator.genSuccessResult());
            } else {
                return response.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg()));
            }
        } else {
            return response.setStatus(ResultGenerator.genFailResult("调用京东到家评价回复接口失败"));
        }
    }

    /**
     * 组装门店id
     * @param storeId
     * @param baseRequest
     */
    private void addStoreId2BaseRequest(long storeId, BaseRequest baseRequest){
        if(storeId <= NumberUtils.LONG_ZERO){
            return;
        }
        baseRequest.setStoreIdList(ImmutableList.of(storeId));
    }

}
