package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.YzSkuPriceAndStockUpdateInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelStockService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetStockInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetStockInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuStockDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStockDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStockRequest;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemUpdateBranchSkuResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 作者：guohuqi
 * 时间：2022/12/6 5:54 PM
 * 功能：
 **/
@Slf4j
@Service("yzToolChannelStockService")
public class YzToolChannelStockServiceImpl extends YouZanToolBaseService implements ChannelStockService {

    public static final String DEFAULT_NULL_MSG = "调用异常";
    @Resource
    private YzToolSpuCommonService yzSpuCommonService;

//    @Override
//    public ResultData updateStock(SkuStockRequest request) {
//        return new ResultData().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
//    }

    @Override
    public ResultSpuData updateStockBySpu(SpuStockRequest request) {

        ResultSpuData wholeResultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        if (CollectionUtils.isEmpty(request.getParamList())) {
            return wholeResultData;
        }
        // 判断租户类型，ERP租户(超市便利)使用单规格
        boolean needYzSingeSpec = yzSpuCommonService.needYzSingeSpec(request.getBaseInfo().getTenantId());

        // 有赞一个商品下，只有一个规格
        request.getParamList().forEach(paramRequest -> {
            if (paramRequest == null || CollectionUtils.isEmpty(paramRequest.getSkuStockInfo())) {
                return;
            }
            paramRequest.getSkuStockInfo().forEach(skuStockDTO -> {
                if (skuStockDTO == null) {
                    return;
                }
                SpuKey spuKey = buildSpuKey(paramRequest, skuStockDTO);
                long storeId = paramRequest.getStoreId();
                YzSkuPriceAndStockUpdateInfo priceAndStockUpdateInfo =
                        YzSkuPriceAndStockUpdateInfo.convertToStock(storeId, paramRequest.getMerchantChannelSpuId(), skuStockDTO);
                ResultSpuData itemResult;
                try {
                    YouzanItemUpdateBranchSkuResult yzResult;
                    if (needYzSingeSpec) {
                        yzResult = yzSpuCommonService.updateStockInfoNoSku(request.getBaseInfo().getTenantId(),
                                storeId,
                                Lists.newArrayList(priceAndStockUpdateInfo));

                    } else {
                        yzResult = yzSpuCommonService.updateStockInfo(request.getBaseInfo().getTenantId(),
                                storeId,
                                Lists.newArrayList(priceAndStockUpdateInfo));
                    }
                    itemResult = convertToSpuResult(yzResult, storeId, spuKey);
                } catch (Exception e) {
                    log.warn("调用有赞更新商品库存接口失败, skuStockDTO {}", skuStockDTO, e);
                    itemResult = convertToSpuResult(e.getMessage(), storeId, spuKey);
                }
                accumulateResult(wholeResultData, itemResult);
            });
        });

        return wholeResultData;
    }

    private void accumulateResult(ResultSpuData wholeResult, ResultSpuData itemResult) {
        ListUtils.emptyIfNull(itemResult.getSucData()).forEach(wholeResult::addToSucData);
        ListUtils.emptyIfNull(itemResult.getErrorData()).forEach(wholeResult::addToErrorData);
    }

    private SpuKey buildSpuKey(SpuStockDTO paramRequest, SkuInSpuStockDTO skuStockDTO) {
        // 有赞一个商品下，只有一个规格
        SkuKey skuKey = new SkuKey();
        skuKey.setChannelSkuId(skuStockDTO.getChannelSkuId());
        skuKey.setCustomSkuId(skuStockDTO.getCustomSkuId());
        SpuKey spuKey = new SpuKey();
        spuKey.setCustomSpuId(paramRequest.getCustomSpuId());
        spuKey.setChannelSpuId(paramRequest.getMerchantChannelSpuId());
        spuKey.setSkus(Lists.newArrayList(skuKey));
        return spuKey;
    }

    private ResultSpuData convertToSpuResult(YouzanItemUpdateBranchSkuResult yzResult, long storeId, SpuKey spuKey) {
        ResultSpuData spuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (Objects.isNull(yzResult)) {
            spuData.addToErrorData(buildFailedSpuErrorData(storeId, spuKey, DEFAULT_NULL_MSG));
        } else if (yzResult.getSuccess()) {
            spuData.addToSucData(new ResultSuccessSpu().setSpuInfo(spuKey).setChannelId(ChannelTypeEnum.YOU_ZAN.getCode()).setStoreId(storeId));
        } else if (!yzResult.getSuccess() && CollectionUtils.isEmpty(yzResult.getData())) {
            // 全部失败
            spuData.addToErrorData(buildFailedSpuErrorData(storeId, spuKey, yzResult.getMessage()));
        } else {
            // 部分失败
            yzResult.getData().stream()
                .filter(resultData -> !resultData.getIsSuccess())
                .forEach(resultData -> spuData.addToErrorData(buildFailedSpuErrorData(storeId, spuKey, resultData.getMessage())));
        }
        return spuData;
    }

    private ResultSpuData convertToSpuResult(String exceptionMsg, long storeId, SpuKey spuKey) {
        ResultSpuData spuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        spuData.addToErrorData(buildFailedSpuErrorData(storeId, spuKey, exceptionMsg));
        return spuData;
    }



    private ResultErrorSpu buildFailedSpuErrorData(long storeId, SpuKey spuKey, String msg) {
        return new ResultErrorSpu().setChannelId(ChannelTypeEnum.YOU_ZAN.getCode())
            .setStoreId(storeId)
            .setSpuInfo(spuKey)
            .setErrorCode(ResultCode.FAIL.getCode())
            .setErrorMsg(msg);
    }

    @Override
    public ResultData updateStockMultiChannel(SkuStockMultiChannelRequest request) {
        // 有赞不再支持无 spu 的商品
        return new ResultData().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
    }

//    @Override
//    public List<ChannelStoreStockInfo> batchGetStockAndStatusInfo(BaseRequestSimple request, long storeId, List<Long> skuIds) {
//        return null;
//    }

    @Override
    public BatchGetStockInfoResponse batchGetStockInfo(BatchGetStockInfoRequest request) {
        return new BatchGetStockInfoResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

}
