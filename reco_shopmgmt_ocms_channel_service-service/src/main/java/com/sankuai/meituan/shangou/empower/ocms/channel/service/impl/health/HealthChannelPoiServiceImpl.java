package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.health;


import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelCommonServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelPoiServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiDetailsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiIdsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 新供给侧渠道门店服务
 * （一租户多品牌需求中，将新供给和歪马的服务代码拆分开来）
 *
 * <AUTHOR>
 */
@Service("healthChannelPoiService")
public class HealthChannelPoiServiceImpl extends MtBrandChannelPoiServiceImpl implements ChannelPoiService {

}
