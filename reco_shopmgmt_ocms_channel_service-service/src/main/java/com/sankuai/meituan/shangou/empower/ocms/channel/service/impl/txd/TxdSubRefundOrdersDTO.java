package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class TxdSubRefundOrdersDTO {
    @JSONField(name = "refundFee")
    private Integer refundFee;
    @JSONField(name = "maxRefundFee")
    private Integer maxRefundFee;
    @JSONField(name = "outSubOrderId")
    private String outSubOrderId;
    @JSONField(name = "refundAmount")
    private Object refundAmount;
}
