package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DxPushMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.YouzanIsvTokenDTO;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.TokenMessage;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.core.client.core.DefaultYZClient;
import com.youzan.cloud.open.sdk.core.oauth.model.OAuthToken;
import com.youzan.cloud.open.sdk.core.oauth.token.TokenParameter;

import lombok.extern.slf4j.Slf4j;

/**
 * 有赞token 获取工具类
 *
 * <AUTHOR>
 * @since 2021/06/03 16:53
 */
@Slf4j
@Service
public class YzToolAccessTokenService {


    private static final int RETRY_MAX = 3;

    private static final long SECONDS_MILLS = TimeUnit.SECONDS.toMillis(1);

    private static final String TOKEN_DELIMITER = "_token_";

    public static final TokenMessage UN_VALID = TokenMessage.builder().expires(0L).build();


    @Autowired
    private DxPushMessageProducer dxPushMessageProducer;

    @Autowired
    private DefaultYZClient yzClient;

    @Resource(name = "yzTokenRedisClient")
    private RedisStoreClient redisStoreClient;

    private static final String REDIS_TOKEN_CATEGORY = "yz-token";

    public String getAccessToken(AppMessage appMessage) {
        try {
            TokenMessage tokenMessage = getToken4Redis(appMessage);
            // redis查询结果为空
            if (tokenMessage == UN_VALID) {
                log.error("有赞token无效请在有赞开放平台重新获取token,appMessage{}", appMessage);
                throw new BizException("get youzan token error");
            }
            if (tokenMessage.valid()) {
                return tokenMessage.getToken();
            }
            // token 过期获取新的token
            return refreshToken(appMessage, tokenMessage).getToken();
        }
        catch (Exception e) {
            log.error("get token 4 youzan error, exception:", e);
        }
        throw new BizException("有赞token无效请在有赞开放平台重新获取token error");
    }

    /**
     * 查询有赞实时token、刷新缓存
     *
     * @param tokenMessage token相关参数
     * @return token信息
     */
    public TokenMessage refreshToken(AppMessage appMessage, TokenMessage tokenMessage) {
        int retry = BigInteger.ZERO.intValue();
        while (++retry < RETRY_MAX) {
            try {
                TokenParameter tokenParameter = TokenParameter
                        .refresh()
                        .clientId(appMessage.getClientId())
                        .clientSecret(appMessage.getClientSecret())
                        .refreshToken(tokenMessage.getRefreshToken())
                        .build();
                log.info("request yz get token, request->{}", JSON.toJSONString(tokenParameter));
                OAuthToken token = yzClient.getOAuthToken(tokenParameter);
                log.info("response yz get token, response->{}", token);
                TokenMessage newtokenMessage = TokenMessage.builder().expires(token.getExpires())
                        .refreshToken(token.getRefreshToken())
                        .timestamp(System.currentTimeMillis())
                        .expires(token.getExpires() - TimeUnit.HOURS.toMillis(2))
                        .token(token.getAccessToken()).build();
                appMessage.setGrantId(token.getAuthorityId());
                // 有赞token有效时间默认为7天
                // redis过期时间存储为有赞平台的过期时间
                // save token to redis
                setToken2Redis(appMessage, newtokenMessage);
                return newtokenMessage;
            }
            catch (Exception e) {
                log.info("refresh youzan token error, retry:{}, exception:", retry, e);
            }
        }
        throw new BizException("get youzan token error");
    }

    public TokenMessage getToken4Redis(AppMessage appMessage) {
        int retry = BigInteger.ZERO.intValue();
        while (++retry <= RETRY_MAX) {
            try {
                String accessToken = redisStoreClient.get(new StoreKey(REDIS_TOKEN_CATEGORY,
                        String.valueOf(appMessage.getClientId()), TOKEN_DELIMITER, appMessage.getGrantId()));
                return StringUtils.isNotEmpty(accessToken) ? JSON.parseObject(accessToken, TokenMessage.class) : UN_VALID;
            }
            catch (Exception e) {
                log.info("get token 4 youzan error, retry:{}, exception:", retry, e);
            }
        }
        throw new BizException("get youzan token error");
    }

    /**
     * 查询有赞token
     *
     * @param appMessage token相关参数
     * @return token信息
     */
    public TokenMessage getAccessToken4Yz(AppMessage appMessage) {
        Map<String ,String> metaData = new HashMap<>();
        metaData.put("clientId",appMessage.getClientId());
        try {
            TokenParameter tokenParameter = TokenParameter
                    .code()
                    .clientId(appMessage.getClientId())
                    .clientSecret(appMessage.getClientSecret())
                    .code(appMessage.getCode())
                    .build();
            log.info("request yz get token, request->{}", JSON.toJSONString(tokenParameter));
            OAuthToken token = yzClient.getOAuthToken(tokenParameter);
            log.info("response yz get token, response->{}", token);
            TokenMessage tokenMessage = TokenMessage.builder().expires(token.getExpires())
                    .refreshToken(token.getRefreshToken())
                    .timestamp(System.currentTimeMillis())
                    .expires(token.getExpires() - TimeUnit.HOURS.toMillis(2))
                    .token(token.getAccessToken()).build();
            appMessage.setGrantId(token.getAuthorityId());
            // 有赞token有效时间默认为7天
            // redis过期时间存储为有赞平台的过期时间
            // save token to redis
            setToken2Redis(appMessage, tokenMessage);
            metaData.put("grantId",token.getAuthorityId());
            dxPushMessageProducer.pushPoiBindMessage(metaData,"有赞Token更新成功");
            return tokenMessage;
        }
        catch (SDKException e) {
            log.error("获取有赞token失败,appMessage{}",appMessage, e);
            dxPushMessageProducer.pushPoiBindMessage(metaData,"有赞Token更新失败");
            throw new BizException("get youzan token error", e);
        }

    }

    /**
     * 获取isv token（不更新DB）
     * @param appKey
     * @param secret
     * @param code
     * @return
     */
    public YouzanIsvTokenDTO getIsvToken(String appKey, String secret, String code) {
        if (StringUtils.isBlank(appKey) || StringUtils.isBlank(secret) || StringUtils.isBlank(code)) {
            log.info("getIsvToken param error, appKey = {}, secret = {}, code = {}", appKey, secret, code);
            throw new BizException("参数错误");
        }

        try{
            TokenParameter tokenParameter = TokenParameter
                    .code()
                    .clientId(appKey)
                    .clientSecret(secret)
                    .code(code)
                    .build();
            log.info("youzan getIsvToken, request = {}", JacksonUtils.toJson(tokenParameter));
            OAuthToken token = yzClient.getOAuthToken(tokenParameter);
            log.info("youzan getIsvToken, OAuthToken = {}", JacksonUtils.toJson(token));

            YouzanIsvTokenDTO tokenDTO = buildTokenDTO(token);

            return tokenDTO;
        }catch (Exception e) {
            log.error("getIsvToken error, appKey = {}, secret = {}, code = {}", appKey, secret, code, e);
            throw new BizException("获取有赞token失败");
        }

    }

    private YouzanIsvTokenDTO buildTokenDTO(OAuthToken token) {
        // 文档地址：https://doc.youzanyun.com/resource/doc/3116
        // refresh_token 过期时间：28天
        long now = System.currentTimeMillis();
        long refreshTokenExpireTime = now + TimeUnit.DAYS.toMillis(28);

        YouzanIsvTokenDTO tokenDTO = new YouzanIsvTokenDTO();
        tokenDTO.setAccessToken(token.getAccessToken());
        tokenDTO.setRefreshToken(token.getRefreshToken());
        tokenDTO.setAccessTokenGrantDate(now);
        tokenDTO.setAccessTokenExpireDate(token.getExpires());
        tokenDTO.setRefreshTokenGrantDate(now);
        tokenDTO.setRefreshTokenExpireDate(refreshTokenExpireTime);

        tokenDTO.setShopId(token.getAuthorityId());
        return tokenDTO;
    }

    public YouzanIsvTokenDTO refreshIsvToken(String appKey, String secret, String refreshToken) {
        if (StringUtils.isBlank(appKey) || StringUtils.isBlank(secret) || StringUtils.isBlank(refreshToken)) {
            log.info("refreshIsvToken param error, appKey = {}, secret = {}, refreshToken = {}", appKey, secret, refreshToken);
            throw new BizException("参数错误");
        }
        
        try{
            TokenParameter tokenParameter = TokenParameter
                    .refresh()
                    .clientId(appKey)
                    .clientSecret(secret)
                    .refreshToken(refreshToken)
                    .build();
            log.info("youzan refreshIsvToken, request = {}", JacksonUtils.toJson(tokenParameter));
            OAuthToken token = yzClient.getOAuthToken(tokenParameter);
            log.info("youzan refreshIsvToken, response = {}", JacksonUtils.toJson(token));

            YouzanIsvTokenDTO tokenDTO = buildTokenDTO(token);

            return tokenDTO;
        }catch (Exception e){
            log.error("refreshIsvToken error, appKey = {}, secret = {}, refreshToken = {}", appKey, secret, refreshToken, e);
            throw new BizException("刷新有赞token失败");
        }
        
    } 


    public void setToken2Redis(AppMessage appMessage, TokenMessage tokenMessage) {
        long expireSeconds = TimeUnit.DAYS.toSeconds(28);
        redisStoreClient.set(new StoreKey(REDIS_TOKEN_CATEGORY, appMessage.getClientId(), TOKEN_DELIMITER,
                appMessage.getGrantId()), JSON.toJSONString(tokenMessage), (int) expireSeconds);
    }

    private void clearToken(AppMessage appMessage) {
        try {
            redisStoreClient.delete(new StoreKey(REDIS_TOKEN_CATEGORY, String.valueOf(appMessage.getClientId()), TOKEN_DELIMITER,
                    appMessage.getGrantId()));
        }
        catch (Exception e) {
            throw new BizException("remove youzan token error,exception:", e);
        }
    }

}
