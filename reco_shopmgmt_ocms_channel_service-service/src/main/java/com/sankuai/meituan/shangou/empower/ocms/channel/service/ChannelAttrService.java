package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;

/**
 * @description: 渠道商品品类内部服务接口，各渠道父接口
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2024/11/25 下午5:27
 */
public interface ChannelAttrService {

    /**
     * 更新商品库存接口
     *
     * @param request
     * @return
     */
    ResultData updateAttr(SpuInfoRequest request);

    /**
     * 更新商品库存接口
     *
     * @param request
     * @return
     */
    ResultData updateAttrForCleaner(SpuInfoRequest request);
}
