package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum.SKU_SELL_STATIS_BY_CHANNEL_SKU_ID;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSkuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultBuilderUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.SaleStatusEnum;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 京东到家渠道商品内部服务接口
 *
 * @author: chenhaie
 * @create: 2019/1/7 下午5:27
 */
@Service("jddjChannelSkuService")
public class JddjChannelSkuServiceImpl implements ChannelSkuService {
    public static final int UPC_CREATE_MAX_COUNT = 50;

    @Value("${jddj.url.base}" + "${jddj.url.skulist}")
    private String skuList;

    @Value("${jddj.url.base}" + "${jddj.url.frontCatList}")
    private String frontCatList;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private JddjConverterService jddjConverterService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj.JddjChannelGateService jddjChannelGateService;

    @Resource
    private CommonLogger log;

    @Autowired
    private JddjChannelAppIdUtils jddjChannelAppIdUtils;

    @Override
    public ResultData skuCreate(SkuInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        getAndSetStoreIds(request.getBaseInfo());
        request.getParamList().forEach(data -> {
            try {
                // 调用渠道接口
                ChannelResponseDTO postResult = jddjChannelGateService.sendPost(ChannelPostJDDJEnum.SKU_CREATE, request.getBaseInfo(), jddjConverterService.skuCreateMapping(data));

                if (postResult.getDataResponse() != null) {
                    postResult.setSuccess(postResult.getDataResponse().getSuccess());
                    postResult.setCode(postResult.getDataResponse().getCode());
                    postResult.setMsg(postResult.getDataResponse().getMsg());
                }

                // 组装返回结果
                ResultDataUtils.combineResultData(resultData, postResult, data.getSkuId());

                if (CollectionUtils.isNotEmpty(resultData.getErrorData()) && CollectionUtils.isNotEmpty(request.getBaseInfo().getStoreIdList())) {
                    for (ResultErrorSku resultErrorSku : resultData.getErrorData()) {
                        resultErrorSku.setStoreId(request.getBaseInfo().getStoreIdList().get(0));
                    }
                }

            } catch (IllegalArgumentException e) {
                log.error("JddjChannelSkuServiceImpl.skuCreate 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getSkuId());

            } catch (Exception e) {
                log.error("JddjChannelSkuServiceImpl.skuCreate 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getSkuId());
            }
        });
        return resultData;
    }

    /**
     * 京东upc创建返回值结构：
     * 1.成功的最里层data(数据都在detail中)
     * {
     *     "result":"[]",
     *     "msg":"成功",
     *     "code":"0",
     *     "success":true,
     *     "detail":"[{"code":"0","msg":"成功","outSkuId":"101103105396624977","skuId":2031586288,"uniqueCode":"0000080768258"},{"code":"0","msg":"成功","outSkuId":"101103105396624911","skuId":2031586287,"uniqueCode":"0000080974482"}]"
     * }
     * 2.失败的最里层data(数据都在result中，而且没有skuId)
     * {
     *     "result":"[{"code":"11016","msg":"商品标识(uniqueCode)已存在"},{"code":"11016","msg":"商品标识(uniqueCode)已存在"}]",
     *     "msg":"成功",
     *     "code":"0",
     *     "success":true,
     *     "detail":"[]"
     * }
     * @param request
     * @return
     */
    @Override
    public ResultData upcCreate(SkuInfoRequest request) {
        ResultData returnData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return returnData;
        }

        List<ResultErrorSku> errorSkuList = Lists.newArrayList();
        List<ResultSuccessSku> successSkuList = Lists.newArrayList();
        returnData.setErrorData(errorSkuList);
        returnData.setSucData(successSkuList);
        getAndSetStoreIds(request.getBaseInfo());

        long storeId = ProjectConstant.CHANNEL_JD2HOME_GLOBAL_STORE_ID;

        ListUtils.listPartition(request.getParamList(), UPC_CREATE_MAX_COUNT).forEach(data -> {
            String ids = data.stream().map(SkuInfoDTO::getSkuId).collect(Collectors.joining(","));
            try {
                // 拼接业务参数
                ChannelUpcCreateDTO channelUpcCreateDTO = new ChannelUpcCreateDTO(jddjConverterService.upcCreateMapping(request.getParamList()));

                // 调用渠道接口
                ChannelResponseDTO postResult = jddjChannelGateService.sendPost(ChannelPostJDDJEnum.UPC_CREATE, request.getBaseInfo(), channelUpcCreateDTO);

                // 解析渠道返回值
                if (postResult.getDataResponse() != null){
                   List<ChannelCreateUpcResponseResult> resultDataList = (List<ChannelCreateUpcResponseResult>)postResult.getDataResponse().getResultData();
                   List<ChannelCreateUpcResponseResult> detailDataList = (List<ChannelCreateUpcResponseResult>)postResult.getDataResponse().getDetailData();
                   List<String> sucSkuList = Lists.newArrayList();

                   // 解析成功数据detail
                   if (CollectionUtils.isNotEmpty(detailDataList)){
                       detailDataList.forEach(detail->{
                           returnData.getSucData().add(new ResultSuccessSku().setSkuId(detail.getOutSkuId()).setStoreId(storeId));
                           sucSkuList.add(detail.getOutSkuId());
                       });
                   }

                   // 解析错误数据result
                   if (CollectionUtils.isNotEmpty(resultDataList)){
                       // 渠道失败列表并没有返回skuId
                       Set<String> failedSkuList = data.stream().map(SkuInfoDTO::getSkuId).filter(sku->!sucSkuList.contains(sku)).collect(Collectors.toSet());
                       failedSkuList.forEach(sku->{
                          returnData.getErrorData().add(new ResultErrorSku().setSkuId(sku).setErrorCode(resultDataList.get(0).getCode()).setErrorMsg(resultDataList.get(0).getMsg()).setStoreId(storeId));
                       });
                   }
                }else {
                    log.error("JddjChannelSkuServiceImpl.upcCreate upc创建渠道返回结果为null, postResult:{}", postResult);
                    data.forEach(skuInfoDTO -> {
                        returnData.getErrorData().add(new ResultErrorSku().setSkuId(skuInfoDTO.getSkuId()).setStoreId(ProjectConstant.CHANNEL_JD2HOME_GLOBAL_STORE_ID).setErrorMsg("解析upc结果失败").setErrorCode(ResultCodeEnum.FAIL.getValue()));
                    });
                }
            } catch (IllegalArgumentException e) {
                log.error("JddjChannelSkuServiceImpl.upcCreate 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(returnData, e.getMessage(), ids);

            } catch (Exception e) {
                log.error("JddjChannelSkuServiceImpl.upcCreate 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(returnData, ChannelResultStatusEnum.UNDEAL_ERROR, ids);
            }

        });

        return returnData;
    }

    @Override
    public ResultData updateSku(SkuInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        getAndSetStoreIds(request.getBaseInfo());
        request.getParamList().forEach(data -> {
            try {
                // 调用渠道接口
                ChannelSkuCreateDTO channelSkuCreateDTO = jddjConverterService.skuCreateMapping(data);
                ChannelResponseDTO postResult = jddjChannelGateService.sendPost(ChannelPostJDDJEnum.SKU_UPDATE, request.getBaseInfo(), channelSkuCreateDTO);
                if (postResult.getDataResponse() != null) {
                    postResult.setSuccess(postResult.getDataResponse().getSuccess());
                    postResult.setCode(postResult.getDataResponse().getCode());
                    postResult.setMsg(postResult.getDataResponse().getMsg());
                }

                // 组装返回结果
                ResultDataUtils.combineResultData(resultData, postResult, data.getSkuId());

                if (CollectionUtils.isNotEmpty(resultData.getErrorData()) && CollectionUtils.isNotEmpty(request.getBaseInfo().getStoreIdList())) {
                    for (ResultErrorSku resultErrorSku : resultData.getErrorData()) {
                        resultErrorSku.setStoreId(request.getBaseInfo().getStoreIdList().get(0));
                    }
                }

            } catch (IllegalArgumentException e) {
                log.error("JddjChannelSkuServiceImpl.updateSku 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getSkuId());

            } catch (Exception e) {
                log.error("JddjChannelSkuServiceImpl.updateSku 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getSkuId());
            }
        });
        return resultData;
    }

    @Override
    public ResultData deleteSku(SkuInfoDeleteRequest request) {
        ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
        BaseRequest baseInfo = request.getBaseInfo();
        request.getParamList().forEach(skuInfoDeleteDTO -> {
            try {
                // 调用渠道接口
                baseInfo.getStoreIdList().clear();
                ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService
                    .sendPost(ChannelPostJDDJEnum.SKU_UPDATE, request.getBaseInfo(), jddjConverterService.skuDeleteMapping(skuInfoDeleteDTO));
                log.info("JddjChannelSkuServiceImpl.deleteSku, 删除商品接口返回数据, baseInfo:{}, skuId:{}, channelResponseDTO:{}",
                    request.getBaseInfo(), skuInfoDeleteDTO.getSkuId(), channelResponseDTO);
                // 组装返回结果
                ResultBuilderUtil.resultAnalysis(baseInfo.getChannelId(), 0L, skuInfoDeleteDTO.getSkuId(), channelResponseDTO, resultData);
            } catch (Exception e) {
                log.error("JddjChannelSkuServiceImpl.deleteSku, 批量删除商品服务异常, request:{}", request, e);
                ResultBuilderUtil.resultAnalysis(baseInfo.getChannelId(), 0L, skuInfoDeleteDTO.getSkuId(),
                    ChannelResultStatusEnum.UNDEAL_ERROR.getDesc(), resultData);
            }
        });
        return resultData;
    }

    @Override
    public ResultData pictureUpload(PictureUploadRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    @Override
    public PictureUploadResult getPictureUploadStatus(PictureUploadStatusRequest request) {
        PictureUploadResult pictureUploadResult = new PictureUploadResult()
            .setStatus(ResultGenerator.genSuccessResult()).setImgHandleList(Lists.newArrayList());
        try {
            ChannelSkuPicUploadStatusDTO channelSkuPicUploadStatusDTO = jddjConverterService.pictureUploadStatusMapping(request.getParam());
            Map<Long, ChannelResponseDTO<List<ImgHandleQueryResult>>> postResult = jddjChannelGateService
                .sendPost(ChannelPostJDDJEnum.PICTURE_UPLOAD_STATUS, request.getBaseInfo(), channelSkuPicUploadStatusDTO);
            log.info("JddjChannelSkuServiceImpl.deleteSkuExecutor, 查询商品图片处理结果接口返回数据, skuIds:{}, result:{}",
                channelSkuPicUploadStatusDTO.getSkuIds(), postResult);
            List<ImgHandleQueryResult> imgHandleQueryResultList = imgHandleResultAnalysis(postResult);
            if (CollectionUtils.isEmpty(imgHandleQueryResultList)) {
                return pictureUploadResult;
            }
            return pictureUploadResult.setStatus(ResultGenerator.genSuccessResult())
                .setImgHandleList(jddjConverterService.imgHandleQueryResultMappingList(imgHandleQueryResultList));
        } catch (Exception e) {
            log.error("JddjChannelSkuServiceImpl.getPictureUploadStatus, 图片处理状态服务异常, request:{}", request, e);
        }
        return pictureUploadResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "图片处理状态失败"))
            .setImgHandleList(Lists.newArrayList());
    }

    @Override
    public ResultData updateSkuSellStatus(SkuSellStatusInfoRequest request) {
        return updateSkuSellStatusCommon(request, ChannelPostJDDJEnum.SKU_SELL_STATIS);
    }

    @Override
    public ResultData updateSkuSellStatusForCleaner(SkuSellStatusInfoRequest request) {
        return updateSkuSellStatusCommon(request, ChannelPostJDDJEnum.SKU_SELL_STATIS_FOR_CLEANER);
    }

    private ResultData updateSkuSellStatusCommon(SkuSellStatusInfoRequest request, ChannelPostInter channelPostInter) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }

        Lists.partition(request.getParamList(), UPC_CREATE_MAX_COUNT).forEach(datas -> {
            List<String> skuIds = new ArrayList<>();
            for (SkuSellStatusInfoDTO data : datas) {
                skuIds.addAll(Fun.map(data.getSkuId(), SkuIdDTO::getCustomSkuId));
            }
            try {
                // 调用渠道（支持京东多应用）
                BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
                // 有门店ID则不需要应用ID，为了避免误传参，这儿强制抹掉应用ID
                baseRequest.setAppId(0L);
                baseRequest.setStoreIdList(Collections.singletonList(datas.get(0).getStoreId()));
                Map<Long, ChannelResponseDTO> postResult = jddjChannelGateService.sendPostAppMapDto(channelPostInter, baseRequest, jddjConverterService.updateSkuSellStatus(datas));
                // 结果组装
                ProductResultDataUtils.combinePartResultData(resultData, postResult, skuIds, ChannelTypeEnum.JD2HOME);

            } catch (IllegalArgumentException e) {
                log.error("MtChannelSkuServiceImpl.updateSkuSellStatus 参数校验失败,channelPostInter:{}, data:{}", channelPostInter,datas, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds);

            } catch (Exception e) {
                log.error("MtChannelSkuServiceImpl.updateSkuSellStatus 参数校验失败,channelPostInter:{}, data:{}", channelPostInter,datas, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds);
            }
        });

        return resultData;
    }
    public ResultData updateSkuSellStatusByChannelSkuIds(SpuSellStatusInfoByChannelSkuIdRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        BaseRequestSimple baseInfo = request.getBaseInfo();
        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(request.getBaseInfo().getTenantId(),
                request.getBaseInfo().getChannelId(),
                request.getStoreId());
        if (channelStore == null) {
            resultData.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店信息失败"));
            return resultData;
        }
            List<String> skuIds = new ArrayList<>();
            try {
                // 调用渠道（支持京东多应用）
                BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
                // 有门店ID则不需要应用ID，为了避免误传参，这儿强制抹掉应用ID
                baseRequest.setAppId(0L);
                baseRequest.setStoreIdList(Collections.singletonList(request.getStoreId()));
                ChannelResponseDTO<List<UpdateVendibilityInfo>> postResult =
                        jddjChannelGateService.sendPostAppDto(SKU_SELL_STATIS_BY_CHANNEL_SKU_ID, baseRequest,
                                jddjConverterService.updateSkuSellStatus(channelStore.getChannelPoiCode(), request.getParamList()));

                // 结果组装
                if(postResult == null){
                    resultData.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "京东接口返回值为空"));
                    return resultData;
                }

                if (!postResult.isSuccess()) {
                    resultData.setStatus(ResultGenerator.genResult(ResultCode.FAIL, postResult.getErrorMsg()));
                    return resultData;
                }

                parseResult(request, resultData, baseInfo, postResult);


            } catch (IllegalArgumentException e) {
                log.error("JddjChannelSkuServiceImpl.updateSkuSellStatusByChannelSkuIds 参数校验失败,channelPostInter:{}, data:{}",
                        SKU_SELL_STATIS_BY_CHANNEL_SKU_ID,request, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds);

            } catch (Exception e) {
                log.error("JddjChannelSkuServiceImpl.updateSkuSellStatusByChannelSkuIds 批量修改门店商品可售状态接口,channelPostInter:{}, data:{}",
                        SKU_SELL_STATIS_BY_CHANNEL_SKU_ID,request, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds);
            }

        return resultData;
    }

    private static void parseResult(SpuSellStatusInfoByChannelSkuIdRequest request, ResultData resultData, BaseRequestSimple baseInfo, ChannelResponseDTO<List<UpdateVendibilityInfo>> postResult) {
        postResult.getCoreData().forEach(data -> {
            if (Objects.equals(data.getCode(), 0)){
                ResultErrorSku errorSku = new ResultErrorSku(baseInfo.getTenantId(), null, data.getReason(), baseInfo.getChannelId()
                        , data.getCode(), data.getSkuId(), null);
                if (CollectionUtils.isNotEmpty(resultData.getErrorData())){
                    resultData.getErrorData().add(errorSku);
                }else {
                    resultData.setErrorData(Arrays.asList(errorSku));
                }
            }else {
                ResultSuccessSku successSku = new ResultSuccessSku(request.getStoreId(), null, null, baseInfo.getChannelId(),
                        data.getSkuId());
                if (CollectionUtils.isNotEmpty(resultData.getErrorData())){
                    resultData.getSucData().add(successSku);
                }else {
                    resultData.setSucData(Arrays.asList(successSku));
                }
            }
        });
    }

    @Override
    public ResultData updateCustomSkuId(UpdateCustomSkuIdRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        List<UpdateCustomSkuIdDTO> paramList = request.getParamList();

        Map<Long, List<UpdateCustomSkuIdDTO>> storeSkuListMap = paramList.stream().collect(Collectors.groupingBy(UpdateCustomSkuIdDTO::getStoreId));

        // 按门店分组
        storeSkuListMap.forEach((storeId, storeList) -> {
            BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
            if (storeId != 0) {
                // 有门店ID则不需要应用ID，为了避免误传参，这儿强制抹掉应用ID
                baseRequest.setAppId(0L);
                baseRequest.setStoreIdList(Collections.singletonList(storeId));
            }
            // 分批处理
            Lists.partition(storeList, ProjectConstant.LIST_PARTITION_NUM).forEach(subList -> {

                List<String> customSkuIds = subList.stream().map(UpdateCustomSkuIdDTO::getCustomSkuId).collect(Collectors.toList());
                try {
                    // 转换请求参数
                    List<UpdateCustomSkuId> customSkuIdList = subList.stream().map(item -> jddjConverterService.updateCustomSkuIdMapping(item)).collect(Collectors.toList());

                    boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.UPDATE_CUSTOM_SKU_ID, String.valueOf(tenantId));
                    if (!tryAcquire) {
                        // 获取当前执行权失败，返回可重试
                        String format = MessageFormat.format(ResultCode.FAIL_ALLOW_RETRY.getMsg(), "根据京东到家商品编码更新商家商品编码接口限頻");
                        List<ResultErrorSku> errorSkuList = subList.stream().map(item -> new ResultErrorSku(storeId, item.getCustomSkuId(),
                                format, channelId, ResultCode.FAIL_ALLOW_RETRY.getCode(), item.getSkuId(), ProductChannelUnifyErrorEnum.RATE_LIMIT)).collect(Collectors.toList());
                        resultData.getErrorData().addAll(errorSkuList);
                        return;
                    }

                    // 调用渠道接口
                    HashMap<String, List<UpdateCustomSkuId>> paramMap = Maps.newHashMap();
                    paramMap.put("skuInfoList", customSkuIdList);
                    Map<Long, ChannelResponseDTO> postResult = jddjChannelGateService.sendPostAppMapDto(ChannelPostJDDJEnum.UPDATE_CUSTOM_SKU_ID, baseRequest, paramMap);
                    ResultDataUtils.combinePartResultData(resultData, postResult, customSkuIds);

                } catch (IllegalArgumentException e) {
                    log.error("JddjChannelSkuServiceImpl.updateCustomSkuId 参数校验失败, subList:{}", subList, e);
                    ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), customSkuIds);

                } catch (Exception e) {
                    log.error("JddjChannelSkuServiceImpl.updateCustomSkuId 参数校验失败, data:{}", subList, e);
                    ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, customSkuIds);
                }
            });
        });

        return resultData;
    }

    @Override
    public GetSkuInfoResponse getSkuInfo(GetSkuInfoRequest request) {

        GetSkuInfoResponse response = new GetSkuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        if (request == null || request.getBaseInfo() == null){
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "拉取京东到家商品信息失败"));
        }
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSkuInfo(null);
        }
        // 构造参数
        Map<String, String> bizParam = Maps.newHashMap();
        bizParam.put("skuId", request.getChannelSkuId());

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.BATCH_GET_SKUINFO, String.valueOf(request.getBaseInfo().getTenantId()));
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY)).setSkuInfo(null);
        }

        // 请求线上渠道
        Map skuInfoMap = jddjChannelGateService.sendPost(skuList,null, baseConverterService.baseRequest(request.getBaseInfo()), bizParam);
        JSONObject skuInfoMapBody = (JSONObject)JSONObject.parse(skuInfoMap.get(ProjectConstant.DATA).toString());
        if (Integer.valueOf(skuInfoMapBody.get(ProjectConstant.CODE).toString()) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }

        List<SkuInfoDTO> skuInfoDTOS = Lists.newArrayList();
        List<ChannelSkuCreateDTO> channelSkuCreateDTOS = Lists.newArrayList();
        JSONObject skuInfoMapData =  skuInfoMapBody.getJSONObject(ProjectConstant.RESULT);
        JSONArray skuInfoJSONArray = skuInfoMapData.getJSONArray("result");
        // 组装数据
        if (null != skuInfoJSONArray) {
            skuInfoJSONArray.forEach(item->{
                ChannelSkuCreateDTO channelSkuCreateDTO = new ChannelSkuCreateDTO();
                channelSkuCreateDTO.setSkuId(((JSONObject)item).getString("skuId"));
                channelSkuCreateDTO.setOutSkuId(((JSONObject)item).getString("outSkuId"));
                channelSkuCreateDTO.setCategoryId(((JSONObject)item).getLong("categoryId"));
                JSONArray categories = ((JSONObject)item).getJSONArray("shopCategories");
                if (CollectionUtils.isNotEmpty(categories)) {
                    channelSkuCreateDTO.setShopCategories(categories.get(0).toString());
                }
                channelSkuCreateDTO.setBrandId(((JSONObject)item).getLong("brandId"));
                channelSkuCreateDTO.setSkuName(((JSONObject)item).getString("skuName"));
                channelSkuCreateDTO.setSkuPrice(((JSONObject)item).getLong("skuPrice"));
                channelSkuCreateDTO.setWeight(((JSONObject)item).getFloat("weight").floatValue());
                channelSkuCreateDTO.setUpcCode(((JSONObject)item).getString("upcCode"));
                channelSkuCreateDTO.setFixedStatus(((JSONObject)item).getInteger("fixedStatus"));

                channelSkuCreateDTOS.add(channelSkuCreateDTO);
            });
        }
        skuInfoDTOS.addAll(jddjConverterService.skuCreateDTOsMapping(channelSkuCreateDTOS));
        //查询商品多分类列表字段赋值，保证对外提供的多分类查询字段有值有值
        //jddj渠道统一放到一级分类里边。
        if(CollectionUtils.isNotEmpty(skuInfoDTOS)){
            skuInfoDTOS.stream().forEach(item->{
                List<ChannelStoreCategory> channelStoreCategoryList = org.assertj.core.util.Lists.newArrayList();
                item.setCategoryList(channelStoreCategoryList);
                ChannelStoreCategory channelStoreCategory = new ChannelStoreCategory();
                channelStoreCategory.setFirstCategoryCode(item.getChannelFrontCategory());
                channelStoreCategory.setFirstCategoryName(item.getChannelFrontCategoryName());
                channelStoreCategoryList.add(channelStoreCategory);
            });
        }
        if (CollectionUtils.isEmpty(skuInfoDTOS)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }
        return response.setStatus(ResultGenerator.genSuccessResult()).setSkuInfo(skuInfoDTOS.get(0));
    }

    @Override
    public BatchGetSkuInfoResponse batchGetSkuInfo(BatchGetSkuInfoRequest request) {

        BatchGetSkuInfoResponse response = new BatchGetSkuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        if (request == null || request.getBaseInfo() == null){
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "拉取京东到家商品信息失败"));
        }
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSkuInfos(Collections.emptyList());
        }
        // 构造参数
        Map<String, String> bizParam = Maps.newHashMap();
        bizParam.put("pageNo",String.valueOf(request.getPageNum()));
        bizParam.put("pageSize",String.valueOf(request.getPageSize()));
        bizParam.put("isFilterDel",String.valueOf(0));

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.BATCH_GET_SKUINFO, String.valueOf(request.getBaseInfo().getTenantId()));
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY)).setSkuInfos(Collections.emptyList());
        }

        // 请求线上渠道
        Map skuInfoMap = jddjChannelGateService.sendPost(skuList,null, baseConverterService.baseRequest(request.getBaseInfo()), bizParam);
        JSONObject skuInfoMapBody = (JSONObject)JSONObject.parse(skuInfoMap.get(ProjectConstant.DATA).toString());
        if (Integer.valueOf(skuInfoMapBody.get(ProjectConstant.CODE).toString()) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }

        List<SkuInfoDTO> skuInfoDTOS = Lists.newArrayList();
        List<ChannelSkuCreateDTO> channelSkuCreateDTOS = Lists.newArrayList();
        JSONObject skuInfoMapData =  skuInfoMapBody.getJSONObject(ProjectConstant.RESULT);
        JSONArray skuInfoJSONArray = skuInfoMapData.getJSONArray("result");

        if (null != skuInfoJSONArray) {
            // 组装数据
            skuInfoJSONArray.forEach(item->{
                ChannelSkuCreateDTO channelSkuCreateDTO = new ChannelSkuCreateDTO();
                channelSkuCreateDTO.setSkuId(((JSONObject)item).getString("skuId"));
                channelSkuCreateDTO.setOutSkuId(((JSONObject)item).getString("outSkuId"));
                channelSkuCreateDTO.setCategoryId(((JSONObject)item).getLongValue("categoryId"));
                JSONArray categories = ((JSONObject)item).getJSONArray("shopCategories");
                if (CollectionUtils.isNotEmpty(categories)) {
                    channelSkuCreateDTO.setShopCategories(categories.get(0).toString());
                }
                channelSkuCreateDTO.setBrandId(((JSONObject)item).getLongValue("brandId"));
                channelSkuCreateDTO.setSkuName(((JSONObject)item).getString("skuName"));
                channelSkuCreateDTO.setSkuPrice(((JSONObject)item).getLongValue("skuPrice"));
                channelSkuCreateDTO.setWeight(((JSONObject)item).getFloatValue("weight"));
                channelSkuCreateDTO.setUpcCode(((JSONObject)item).getString("upcCode"));
    //          channelSkuCreateDTO.setProductDesc(((JSONObject)item).getString("productDescHtml"));
                channelSkuCreateDTO.setFixedStatus(((JSONObject)item).getIntValue("fixedStatus"));

                channelSkuCreateDTOS.add(channelSkuCreateDTO);
            });
        }
        skuInfoDTOS.addAll(jddjConverterService.skuCreateDTOsMapping(channelSkuCreateDTOS));
        //查询商品多分类列表字段赋值，保证对外提供的多分类查询字段有值有值
        //jddj渠道统一放到一级分类里边。
        if(CollectionUtils.isNotEmpty(skuInfoDTOS)){
            skuInfoDTOS.stream().forEach(item->{
                List<ChannelStoreCategory> channelStoreCategoryList = org.assertj.core.util.Lists.newArrayList();
                item.setCategoryList(channelStoreCategoryList);
                ChannelStoreCategory channelStoreCategory = new ChannelStoreCategory();
                channelStoreCategory.setFirstCategoryCode(item.getChannelFrontCategory());
                channelStoreCategory.setFirstCategoryName(item.getChannelFrontCategoryName());
                channelStoreCategoryList.add(channelStoreCategory);
            });
        }
        return response.setStatus(ResultGenerator.genSuccessResult()).setSkuInfos(skuInfoDTOS);
    }

    @Override
    public GetCategoryResponse batchGetChannelStoreCategoryInfo(CatRequest request) {
        GetCategoryResponse response = new GetCategoryResponse().setStatus(ResultGenerator.genSuccessResult());
        if (request == null || request.getBaseInfo() == null){
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "拉取京东到家前台分类失败"));
        }

        // 构造请求参数
        Map<String, List<String>> bizParam = Maps.newHashMap();
        List<String> list = Lists.newArrayList("ID","PID","SHOP_CATEGORY_NAME","SHOP_CATEGORY_LEVEL","SORT");
        bizParam.put("fields", list);

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.BATCH_GET_CATEGORYINFO, String.valueOf(request.getBaseInfo().getTenantId()));
        if (!tryAcquire) {
            log.warn("batchGetChannelStoreCategoryInfo 触发限流");
            return response.setStatus(ResultGenerator.genResult(ResultCode.TRIGGER_LIMIT));
        }

        // Map skuInfoMap = jddjChannelGateService.sendPost(frontCatList,null , baseConverterService.baseRequest(request.getBaseInfo()), bizParam);
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        // 兼容不传应用ID的情况
        int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), baseRequest.getAppId());
        baseRequest.setAppId(appId);
        Map skuInfoMap = jddjChannelGateService.sendPostApp(frontCatList,null , baseConverterService.baseRequest(request.getBaseInfo()), bizParam);
        JSONObject skuInfoMapBody = (JSONObject)JSONObject.parse(skuInfoMap.get(ProjectConstant.DATA).toString());
        if (Integer.valueOf(skuInfoMapBody.get(ProjectConstant.CODE).toString()) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }

        List<CatInfo> catInfos = Lists.newArrayList();
        List<ChannelStoreCategoryDTO> channelStoreCategoryDTOS = Lists.newArrayList();
        JSONArray catInfoJSONArray = skuInfoMapBody.getJSONArray("result");
        if (catInfoJSONArray == null) {
            return response.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(catInfos);
        }
        // 组装数据
        catInfoJSONArray.forEach(item->{
            ChannelStoreCategoryDTO channelStoreCategoryDTO = new ChannelStoreCategoryDTO();
            channelStoreCategoryDTO.setCategoryId(((JSONObject)item).getLong("id").toString());
            channelStoreCategoryDTO.setCategoryName(((JSONObject)item).getString("shopCategoryName"));
            channelStoreCategoryDTO.setDepth(((JSONObject)item).getInteger("shopCategoryLevel"));
            channelStoreCategoryDTO.setParentId(((JSONObject)item).getLong("pid").toString());
            channelStoreCategoryDTO.setSequence(new Long(((JSONObject)item).getLong("sort")).intValue());
            channelStoreCategoryDTOS.add(channelStoreCategoryDTO);
        });
        catInfos.addAll(jddjConverterService.channelStoreCategoryDTOMapping(channelStoreCategoryDTOS));
        return response.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(catInfos);
    }

    @Override
    public ResultData changeCustomSkuId(ChangeCustomSkuIdRequest request) {
        UpdateCustomSkuIdRequest updateCustomSkuIdRequest = new UpdateCustomSkuIdRequest();
        updateCustomSkuIdRequest.setBaseInfo(request.getBaseInfo());
        List<UpdateCustomSkuIdDTO> paramList = request.getParamList().stream().map(item ->
            jddjConverterService.convert2UpdateCustomSkuIdDTO(item)).collect(Collectors.toList());
        updateCustomSkuIdRequest.setParamList(paramList);
        return updateCustomSkuId(updateCustomSkuIdRequest);
    }

    private List<ImgHandleQueryResult>  imgHandleResultAnalysis(Map<Long, ChannelResponseDTO<List<ImgHandleQueryResult>>> postResult) {
        List<ImgHandleQueryResult> handleQueryResultList = Lists.newArrayList();
        postResult.forEach((k, v) -> {
            if (v.isSuccess()) {
                ChannelResponseResult<List<ImgHandleQueryResult>> channelResponseResult = v.getDataResponse();
                if (Objects.nonNull(channelResponseResult) && CollectionUtils.isNotEmpty(channelResponseResult.getResultData())) {
                    handleQueryResultList.addAll(channelResponseResult.getResultData());
                }
            }
        });
        return handleQueryResultList;
    }

    private List<OpenSkuManageReasonResult> violateHandleResultAnalysis(ChannelResponseDTO<List<OpenSkuManageReasonResult>> postResult) {
        List<OpenSkuManageReasonResult> handleQueryResultList = Lists.newArrayList();
            if (postResult.isSuccess()) {
                ChannelResponseResult<List<OpenSkuManageReasonResult>> channelResponseResult = postResult.getDataResponse();
                if (Objects.nonNull(channelResponseResult) && CollectionUtils.isNotEmpty(channelResponseResult.getResultData())) {
                    handleQueryResultList.addAll(channelResponseResult.getResultData());
                }
            }
        return handleQueryResultList;
    }
    /**
     * 京东到家新增修改商品是租户维度，将门店属性去掉，避免重复调用
     * 返回门店id拼接字符串，防止有需要的情况
     *
     * @param baseInfo
     * @return
     */
    private String getAndSetStoreIds(BaseRequest baseInfo) {
        List<Long> storeIds = baseInfo.getStoreIdList();
        if (CollectionUtils.isEmpty(storeIds)){
            return null;
        }
        baseInfo.setStoreIdList(null);
        return storeIds.stream().map(s -> String.valueOf(s)).collect(Collectors.joining(","));
    }


    @Override
    public BatchGetSkuSaleInfoResponse batchGetSkuSaleInfo(BatchGetSkuSaleInfoRequest request) {

        BatchGetSkuSaleInfoResponse response= new BatchGetSkuSaleInfoResponse();
        response.setStatus(ResultGenerator.genSuccessResult());

        if (CollectionUtils.isEmpty(request.getChannelSkuIds())) {
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "sku列表不能为空"));
            return response;
        }

        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        baseRequest.setStoreIdList(Collections.singletonList(request.getStoreId()));
        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(baseRequest.getTenantId(), baseRequest.getChannelId(), request.getStoreId());
        if (channelStore == null) {
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店信息失败"));
            return response;
        }

        ChannelStockQueryDTO channelStockQueryDTO = new ChannelStockQueryDTO();
        List<ChannelStockQueryDTO.BaseStockCenterRequest> requestList = Fun.map(request.getChannelSkuIds(), channelSkuId -> {
            ChannelStockQueryDTO.BaseStockCenterRequest stockCenterRequest = new ChannelStockQueryDTO.BaseStockCenterRequest();
            stockCenterRequest.setSkuId(Long.parseLong(channelSkuId));
            stockCenterRequest.setStationNo(channelStore.getChannelPoiCode());
            return stockCenterRequest;
        });
        channelStockQueryDTO.setListBaseStockCenterRequest(requestList);
        ChannelResponseDTO<List<SkuSaleStatusInfo>> stockResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SHOP_STOCK_LIST, baseRequest, channelStockQueryDTO);

        if(stockResult == null){
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "京东接口返回值为空"));
            return response;
        }

        if (!stockResult.isSuccess()) {
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, stockResult.getErrorMsg()));
            return response;
        }
        List<SkuSaleInfoDTO> skuSaleInfoDTOList = Fun.map(stockResult.getCoreData(), skuSaleStatusInfo -> {
            SkuSaleInfoDTO dto = new SkuSaleInfoDTO();
            dto.setChannelSkuId(skuSaleStatusInfo.getSkuId());
            dto.setSaleStatus(Objects.equals(skuSaleStatusInfo.getVendibility(), 0) ?
                    SaleStatusEnum.ON_SALE.getCode(): SaleStatusEnum.OFF_SALE.getCode());
            dto.setStock(skuSaleStatusInfo.getUsableQty());
            return dto;
        });
        response.setSkuSaleInfos(skuSaleInfoDTOList);
        return response;
    }

    /**
     * 查询商品违规信息
     * @param request
     * @return
     */
    @Override
    public BatchGetViolateSkuResponse batchGetViolateSku(BatchGetViolateSkuRequest request) {
        BatchGetViolateSkuResponse response = new BatchGetViolateSkuResponse()
            .setStatus(ResultGenerator.genSuccessResult()).setViolateSkuList(Lists.newArrayList());

        try {
            Map<String, Object> bizParam = Maps.newHashMap();
            bizParam.put("skuIds", request.getChannelSkuIds());
            BaseRequest baseRequest = new BaseRequest();
            baseRequest.setTenantId(request.getBaseInfo().getTenantId());
            baseRequest.setChannelId(request.getBaseInfo().getChannelId());
            int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), baseRequest.getAppId());
            baseRequest.setAppId(appId);
            // 调用渠道接口
            ChannelResponseDTO<List<OpenSkuManageReasonResult>> postResult  = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.VIOLATE_SKU_LIST, baseRequest, bizParam);
            log.info("JddjChannelSkuServiceImpl.batchGetViolateSku, 查询违规商品接口返回数据, skuIds:{}, result:{}",
                request.getChannelSkuIds(), postResult);
            List<OpenSkuManageReasonResult> violateSkuList =  violateHandleResultAnalysis(postResult);

            if (CollectionUtils.isEmpty(violateSkuList)) {
                return response;
            }
            return response.setStatus(ResultGenerator.genSuccessResult())
                .setViolateSkuList(jddjConverterService.convertViolateSkuList2DTO(violateSkuList));

        } catch (Exception e) {
            log.error("JddjChannelSkuServiceImpl.batchGetViolateSku, 查询违规商品异常, request:{}", request, e);
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询违规商品异常"))
                .setViolateSkuList(Lists.newArrayList());
        }
    }
}
