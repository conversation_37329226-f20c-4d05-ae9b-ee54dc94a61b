package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.health;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCommentService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelCommentServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelCommentServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentListQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentListQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentReplyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentReplyResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 美团渠道评价内部服务接口
 *
 * <AUTHOR>
 */
@Service("healthChannelCommentService")
public class HealthChannelCommentServiceImpl extends MtBrandChannelCommentServiceImpl implements ChannelCommentService {

}
