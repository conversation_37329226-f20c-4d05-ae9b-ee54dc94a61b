package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.orderTrack;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.base.Charsets;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.BaseDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelOrderNotify;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ElmOrderReverseNotify;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MaltFarmAggRiderTransferDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.TradeCommonMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.StoreIdNotExistException;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.OrderTrackMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.health.HealthChannelRiderTransferRiderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.XmPubService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ChannelStatusConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm.ElmRefundService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.PoiApiChannelPoiThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.route.RouteServiceFactory;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ChannelOrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.MtReturnDuringDeliveryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderAfsApplyDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.ServiceTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelOrderStatus;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.ChannelPoiBaseInfoDTO;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanTradeGetResult;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.time.Instant;
import java.util.*;

import joptsimple.internal.Strings;

/**
 * 订单渠道轨迹
 */
@Service
public class OrderTrackService {

    @Resource
    private CommonLogger log;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private OrderTrackMessageProducer orderTrackMessageProducer;

    @Resource
    private ElmRefundService elmRefundService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private RouteServiceFactory routeServiceFactory;

    @Resource
    private XmPubService xmPubService;

    @Resource
    private PoiThriftService poiThriftService;

    @Resource
    private PoiApiChannelPoiThriftServiceProxy poiApiChannelPoiThriftServiceProxy;

    private static final String DH_ALERT_ORDER_GROUP_ID = "alert_order_groupId";

    private static final String MT_POI_DH_CHANNEL_ALERT_ORDER_GROUP_ID = "mt_poi_dh_channel_alert_order_groupId";

    @Resource
    private MtConverterService mtConverterService;

    @Resource
    private TenantService tenantService;

    @Resource
    private HealthChannelRiderTransferRiderService healthChannelRiderTransferRiderService;

    /**
     * 发送渠道轨迹，歪马租户不发送
     * @param resultStatus
     * @param request
     */
    public void orderNotifyTrack(ResultStatus resultStatus, BaseDto request, String tagEnum) {
        if (request == null) {
            return;
        }
        try {
//            log.info("处理渠道轨迹信息，resultStatus:{},request:{},tagEnum:{}", resultStatus, request);

            //channelOrderId
            String channelOrderId = request.getChannelOrderId();

            Long tenantId = request.getTenantId();

            final OrderBizTypeEnum orderBizTypeEnum = OrderConvertUtils.convertBizType(request.getChannelTypeEnum());

            OrderTrackEvent orderTrackEvent = new OrderTrackEvent();
            orderTrackEvent.setTenantId(tenantId);
            orderTrackEvent.setOrderBizType(orderBizTypeEnum == null ? null : orderBizTypeEnum.getValue());
            orderTrackEvent.setUnifyOrderId(channelOrderId);
            orderTrackEvent.setOperateTime(Instant.now().toEpochMilli());
            orderTrackEvent.setTrackSource(TrackSource.CHANNEL.getType());
            orderTrackEvent.setTrackOpType(getTrackOpType(tagEnum,request));

            //发送轨迹信息
            if (Objects.nonNull(orderTrackEvent.getTrackOpType())) {
                orderTrackMessageProducer.sendMessage(orderTrackEvent, channelOrderId);
                Cat.logEvent("OrderTrackEvent", "SUCCESS");
//                log.info("End order notify track success,msg:{}", orderTrackEvent);
            }

//            log.info("处理渠道轨迹信息,不处理该轨迹");

        } catch (Exception e) {
            log.error("处理渠道轨迹信息报错", e);
            Cat.logEvent("OrderTrackEvent", "EXCEPTION_ERROR");
        }
    }

    private Integer getTrackOpType(String tagEnum, BaseDto request) {
        ChannelTypeEnum channelTypeEnum = request.getChannelTypeEnum();
        if (channelTypeEnum == ChannelTypeEnum.DOU_YIN) {
            DouyinMessageTagEnum douyinMessageTagEnum = DouyinMessageTagEnum.fromCode(tagEnum);

            switch (douyinMessageTagEnum) {
                case ARBITRATE_SERVICE_INTERVENE:
                    return TrackOpType.DY_ARBITRATE_SERVICE_INTERVENE.getOpType();
                case ARBITRATE_CANCELLED:
                    return TrackOpType.DY_ARBITRATE_CANCELLED.getOpType();
                case ARBITRATE_AUDITED:
                    return TrackOpType.DY_ARBITRATE_AUDITED.getOpType();
                case ARBITRATE_APPLIED:
                    return TrackOpType.DY_ARBITRATE_APPLIED.getOpType();
                case REFUND_CREATED:
                    return TrackOpType.DY_REFUND_CREATED.getOpType();
                case REFUND_MODIFIED:
                    return TrackOpType.DY_REFUND_MODIFIED.getOpType();
                case REFUND_CLOSED:
                    return TrackOpType.DY_REFUND_CLOSED.getOpType();
                case REFUND_SUCCESS:
                    return TrackOpType.DY_REFUND_SUCCESS.getOpType();
                case RETURN_APPLY_REFUSED:
                    return TrackOpType.DY_RETURN_APPLY_REFUSED.getOpType();
                case REFUND_REFUSED:
                    return TrackOpType.DY_REFUND_REFUSED.getOpType();
                case RETURN_APPLY_AGREED:
                    return TrackOpType.DY_RETURN_APPLY_AGREED.getOpType();
                case REFUND_AGREED:
                    return TrackOpType.DY_REFUND_AGREED.getOpType();
                default:
                    return TrackOpType.UN_KNOWN.getOpType();
            }

        }
        return TrackOpType.UN_KNOWN.getOpType();

    }

    /**
     * 发送渠道轨迹，歪马租户不发送
     * @param resultStatus
     * @param request
     */
    public void orderNotifyTrack(ResultStatus resultStatus, OrderNotifyRequest request) {
        try {
//            log.info("处理渠道轨迹信息，resultStatus:{},request:{}", resultStatus, request);
            if (Objects.isNull(resultStatus) || resultStatus.getCode() != ResultCode.SUCCESS.getCode()) {
                log.info("返回结果错误，不处理该轨迹");
                return;
            }

            if (MccConfigUtil.filterTenantAppIds(request.getTenantAppId()) && Objects.equals(request.getAction(),
                    ChannelNotifyEnum.MT_ORDER_REFUND_DELIVERY_STATUS_NOTIFY.getAbbrev())){
                return;
            }

            ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);

            if (channelTypeEnum == ChannelTypeEnum.TXD) {
                // log.info("渠道类型为淘鲜达，不处理该渠道的订单");
                return;
            }
            ChannelNotifyEnum notifyEnum = EnumUtil.getEnumByAbbrev(request.getAction(), ChannelNotifyEnum.class);
            //channelOrderId
            String channelOrderId = request.getOrderId();
            Long tenantId = null;
            Long appId = NumberUtils.LONG_ZERO;

            if (ChannelTypeEnum.ELEM.equals(channelTypeEnum) && MccConfigUtil.checkElmStoreId(request.getTenantAppId()) && MccConfigUtil.checkElmIsvAppKey(request.getTenantAppId())) {
                if (request.getAppPoiCode() == null) {
                    log.info("饿了么ISV应用:{}查询订单轨迹请求参数门店为空, 不处理该轨迹",request.getTenantAppId());
                    return;
                }
//                log.info("饿了么ISV应用:{}查询订单轨迹请求参数",request.getTenantAppId());
                Optional<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTO = poiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO(request.getAppPoiCode(), false);
                tenantId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getTenantId).orElse(null);
                appId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getQnhAppId).orElse(NumberUtils.LONG_ZERO);
            } else {
                CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request);
                if(ObjectUtils.isEmpty(accessConfig)) {
                    alertDhOrderFromMtChannel(channelTypeEnum.getCode(), request.getTenantAppId(), channelOrderId, request.getAppPoiCode());
                }
                tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
                appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
            }

            // 有赞获取订单Id
            if (Objects.equals(channelTypeEnum, ChannelTypeEnum.YOU_ZAN)) {
                ChannelOrderService channelOrderService = routeServiceFactory.selectChannelOrderService(ChannelTypeEnum.YOU_ZAN.getCode(), tenantId);
                // 只是拿一个orderId不需要这么做，直接解析body就可以了，不需要再去请求渠道，但只有部分消息body能解析出订单号，因此这里沿用之前的做法，只有订单创建相关的能成功解析出订单号
                if (MccConfigUtil.isYzUseOrderIdInBody()) {
                    try {
                        TradeCommonMessage<String> tradMsg = JSON.parseObject(request.getBody(), new TypeReference<TradeCommonMessage<String>>() {
                        });
                        String message = UrlUtil.urlDecodeSafe(tradMsg.getMsg());
                        YouzanTradeGetResult.YouzanTradeGetResultData createMsg = JSON.parseObject(message,
                                new TypeReference<YouzanTradeGetResult.YouzanTradeGetResultData>() {
                                });

                        channelOrderId =
                                Optional.ofNullable(createMsg).map(YouzanTradeGetResult.YouzanTradeGetResultData::getFullOrderInfo)
                                        .map(YouzanTradeGetResult.YouzanTradeGetResultFullorderinfo::getOrderInfo).map(YouzanTradeGetResult.YouzanTradeGetResultOrderinfo::getTid).orElse(null);
                    } catch (Exception e) {
                        log.warn("解析有赞订单号失败{}", e.getMessage());
                    }
                } else {

                    ChannelOrderDetailDTO channelDetail = channelOrderService.getOrderDetail4ChannelMessage(ChannelTypeEnum.YOU_ZAN, notifyEnum,
                            tenantId, request.getBody());
                    channelOrderId = channelDetail.getChannelOrderId();
                }

            }

            final OrderBizTypeEnum orderBizTypeEnum = OrderConvertUtils.convertBizType(channelTypeEnum);

            if (!ObjectUtils.allNotNull(channelTypeEnum, notifyEnum, channelOrderId, tenantId, orderBizTypeEnum)) {
                log.info("参数不完全channelTypeEnum:{},notifyEnum:{},channelOrderId:{},tenantId:{},orderBizTypeEnum:{}", channelTypeEnum, notifyEnum, channelOrderId, tenantId, orderBizTypeEnum);
                Cat.logEvent("OrderTrackEvent", "DATA_IN_COMPLETE_ERROR");
                return;
            }

//            log.info("处理渠道轨迹信息,channelOrderId:{},channelTypeEnum:{}，notifyEnum:{}", channelOrderId, channelTypeEnum, notifyEnum);
            OrderTrackEvent orderTrackEvent = new OrderTrackEvent();
            orderTrackEvent.setTenantId(tenantId);
            orderTrackEvent.setOrderBizType(orderBizTypeEnum == null ? null : orderBizTypeEnum.getValue());
            orderTrackEvent.setUnifyOrderId(channelOrderId);
            orderTrackEvent.setOperateTime(Instant.now().toEpochMilli());
            orderTrackEvent.setTrackSource(TrackSource.CHANNEL.getType());

            switch (channelTypeEnum) {
                case MEITUAN:
                    mtTrackHandler(notifyEnum, orderTrackEvent, request);
                    break;
                case ELEM:
                    elmTrackHandler(notifyEnum, orderTrackEvent, request, tenantId, appId);
                    break;
                case JD2HOME:
                    jddjTrackHandler(notifyEnum, orderTrackEvent, request);
                    break;
                default:
            }

            //发送轨迹信息
            if (Objects.nonNull(orderTrackEvent.getTrackOpType())) {
                orderTrackMessageProducer.sendMessage(orderTrackEvent, channelOrderId);
                Cat.logEvent("OrderTrackEvent", "SUCCESS");
//                log.info("End order notify track success,msg:{}", orderTrackEvent);
            }

//            log.info("处理渠道轨迹信息,不处理该轨迹");

        } catch (StoreIdNotExistException e) {
            log.info("渠道门店未绑定牵牛花,不处理该轨迹");
        } catch (Exception e) {
            log.error("处理渠道轨迹信息报错", e);
            Cat.logEvent("OrderTrackEvent", "EXCEPTION_ERROR");
        }
    }

    public void orderNotifyTrack(ResultStatus resultStatus, MtReturnDuringDeliveryRequest request) {
        try {
//            log.info("处理渠道轨迹信息，resultStatus:{},request:{}", resultStatus, request);
            if (Objects.isNull(resultStatus) || resultStatus.getCode() != ResultCode.SUCCESS.getCode()) {
                log.info("返回结果错误，不处理该轨迹");
                return;
            }

            ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
            //channelOrderId
            String channelOrderId = request.getOrderId();


            //tenantId
            CopAccessConfigDO accessConfig = getAccessConfig(channelTypeEnum.getCode(), request);

            Long tenantId = getTenantIdParam(accessConfig, request.getTenantAppId());
            final OrderBizTypeEnum orderBizTypeEnum = OrderConvertUtils.convertBizType(channelTypeEnum);

            if (!ObjectUtils.allNotNull(channelTypeEnum, channelOrderId, tenantId, orderBizTypeEnum)) {
                log.info("参数不完全channelTypeEnum:{},channelOrderId:{},tenantId:{},orderBizTypeEnum:{}", channelTypeEnum, channelOrderId, tenantId, orderBizTypeEnum);
                Cat.logEvent("OrderTrackEvent", "DATA_IN_COMPLETE_ERROR");
                return;
            }

//            log.info("处理渠道轨迹信息,channelOrderId:{},channelTypeEnum:{}", channelOrderId, channelTypeEnum);
            OrderTrackEvent orderTrackEvent = new OrderTrackEvent();
            orderTrackEvent.setTenantId(tenantId);
            orderTrackEvent.setOrderBizType(orderBizTypeEnum == null ? null : orderBizTypeEnum.getValue());
            orderTrackEvent.setUnifyOrderId(channelOrderId);
            orderTrackEvent.setOperateTime(Instant.now().toEpochMilli());
            orderTrackEvent.setTrackSource(TrackSource.CHANNEL.getType());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("afterSaleId", request.getRefundId());
            orderTrackEvent.setExt(jsonObject.toJSONString());
            Integer trackOpType = null;
            //没有审核状态的时候还是返货
            if(request.getPoiCheckResult() == 0){
                if(request.getStatus() == 0){
                    return;
                }
                if(ReturnDeliveryStatus.RETURNING.getCode() == request.getStatus()){
                    trackOpType = TrackOpType.MT_DELIVERY_RETURNING.getOpType();
                }else if(ReturnDeliveryStatus.SUCCESS.getCode() == request.getStatus()){
                    trackOpType = TrackOpType.MT_DELIVERY_RETURN_SUC.getOpType();
                }else if(ReturnDeliveryStatus.FAILURE.getCode() == request.getStatus()){
                    trackOpType = TrackOpType.MT_DELIVERY_RETURN_FAIL.getOpType();
                }

            }else{
                if(MerchantInspectionResult.PASS.getCode() == request.getPoiCheckResult()){
                    trackOpType = TrackOpType.MT_DELIVERY_RETURN_AUDIT_SUC.getOpType();
                }else if(MerchantInspectionResult.FAIL.getCode() == request.getPoiCheckResult()){
                    trackOpType = TrackOpType.MT_DELIVERY_RETURN_AUDIT_FAIL.getOpType();
                }else if(MerchantInspectionResult.AUTO_PASS.getCode() == request.getPoiCheckResult()){
                    trackOpType = TrackOpType.MT_DELIVERY_RETURN_AUTO_AUDIT_SUC.getOpType();
                }
            }
            orderTrackEvent.setTrackOpType(trackOpType);

            //发送轨迹信息
            if (Objects.nonNull(orderTrackEvent.getTrackOpType())) {
                orderTrackMessageProducer.sendMessage(orderTrackEvent, channelOrderId);
                Cat.logEvent("OrderTrackEvent", "SUCCESS");
//                log.info("End order notify track success,msg:{}", orderTrackEvent);
            }

//            log.info("处理渠道轨迹信息,不处理该轨迹");

        } catch (Exception e) {
            log.error("处理渠道轨迹信息报错", e);
            Cat.logEvent("OrderTrackEvent", "EXCEPTION_ERROR");
        }
    }

    private void jddjTrackHandler(ChannelNotifyEnum notifyEnum, OrderTrackEvent orderTrackEvent, OrderNotifyRequest request) {

        TrackOpType trackOpType = null;
        switch (notifyEnum) {
            case JDDJ_NEW_ORDER:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_NEW_ORDER.getOpType());
                break;
            case JDDJ_ORDER_ADJUST:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_ORDER_ADJUST.getOpType());
                break;
            case JDDJ_ORDER_WAIT_OUT:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_ORDER_WAIT_OUT.getOpType());
                break;
            case JDDJ_ORDER_INFO_CHANGE:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_ORDER_INFO_CHANGE.getOpType());
                break;
            case JDDJ_ORDER_ADD_TIPS:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_ORDER_ADD_TIPS.getOpType());
                break;
            case JDDJ_FINISH_ORDER:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_FINISH_ORDER.getOpType());
                break;
            case JDDJ_ORDER_FINISH:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_ORDER_FINISH.getOpType());
                break;
            case JDDJ_PICK_FINISH_ORDER:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_PICK_FINISH_ORDER.getOpType());
                break;
            case JDDJ_ACCOUNTING:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_ACCOUNTING.getOpType());
                break;
            case JDDJ_CANCEL_ORDER:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_CANCEL_ORDER.getOpType());
                break;
            case JDDJ_START_DELIVERY:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_START_DELIVERY.getOpType());
                break;
            case JDDJ_AFTERSALE_STATUS:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_AFTERSALE_STATUS.getOpType());
                break;
            case JDDJ_UPDATE_AFTERSALE:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_UPDATE_AFTERSALE.getOpType());
                break;
            case JDDJ_ORDER_FINANCE_READY:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_ORDER_FINANCE_READY.getOpType());
                break;
            case JDDJ_ORDER_INVOICE:
                orderTrackEvent.setTrackOpType(TrackOpType.JDDJ_ORDER_INVOICE.getOpType());
                break;
            default:
                break;
        }
    }

    private void elmTrackHandler(ChannelNotifyEnum notifyEnum, OrderTrackEvent orderTrackEvent, OrderNotifyRequest request, Long tenantId, Long appId) {
        switch (notifyEnum) {
            case ELM_ORDER_CREATE:
                if (StringUtils.isNotBlank(request.getOrderId())) {
                    orderTrackEvent.setTrackOpType(TrackOpType.ELM_ORDER_CREATE.getOpType());
                }
                break;
            case ELM_ORDER_STATUS:
                if (StringUtils.isNotBlank(request.getOrderId())) {
                    ChannelOrderStatus channelOrderStatus = ChannelStatusConvertUtil.elmOrderStatusMapping(request.getStatus());
                    eleTrackOpTypeHandler(channelOrderStatus, orderTrackEvent);
                }
                break;
            case ELM_ORDER_REVERSE_PUSH:
                ElmOrderReverseNotify elmOrderReverseNotify = JSON.parseObject(request.getBody(), ElmOrderReverseNotify.class);
                long storeId = copChannelStoreService.selectChannelStoreId(tenantId, ChannelTypeEnum.ELEM.getCode(), elmOrderReverseNotify.getPlatform_shop_id());
                log.info("isv trackHandler storeId:{}", storeId);
                OrderAfsApplyDTO orderAfsApplyDTO = elmRefundService.fetchAfterSale(tenantId, appId, elmOrderReverseNotify.getOrder_id(), elmOrderReverseNotify.getRefund_order_id(), storeId);
                if (orderAfsApplyDTO.getRefundType() == RefundTypeEnum.AMOUNT.getValue()) {
                    elmRefundTrackHandler(orderTrackEvent, elmOrderReverseNotify);
                }
                break;
            default:
                return;
        }
    }


    private void elmRefundTrackHandler(OrderTrackEvent orderTrackEvent, ElmOrderReverseNotify elmOrderReverseNotify) {
        ElmRefundStatusEnum newRefundStatus = EnumUtil.getEnumByCode(elmOrderReverseNotify.getCur_reverse_event().getRefund_status(), ElmRefundStatusEnum.class);
        ElmRefundStatusEnum oldRefundStatus = EnumUtil.getEnumByCode(elmOrderReverseNotify.getCur_reverse_event().getLast_refund_status(), ElmRefundStatusEnum.class);
        ElmOperatorRoleEnum operatorRole = EnumUtil.getEnumByCode(elmOrderReverseNotify.getCur_reverse_event().getOperator_role(), ElmOperatorRoleEnum.class);
        switch (operatorRole) {
            case CUSTOMER:
                if (oldRefundStatus == ElmRefundStatusEnum.INIT && newRefundStatus == ElmRefundStatusEnum.APPLY) {
                    orderTrackEvent.setTrackOpType(TrackOpType.ORDER_CUSTOMER_MONEY_REFUND.getOpType());
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("operatorName", "顾客");
                    orderTrackEvent.setExt(jsonObject.toJSONString());
                }
                break;
            default:
                if (newRefundStatus == ElmRefundStatusEnum.REFUND_SUCCESS) {
                    orderTrackEvent.setTrackOpType(TrackOpType.ORDER_MONEY_REFUND_SUCCESS.getOpType());
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("afterSaleId", elmOrderReverseNotify.getRefund_order_id());
                    orderTrackEvent.setExt(jsonObject.toJSONString());
                }
                return;
        }
    }

    private String getReasonType(OrderNotifyRequest request) {
        String urlParam = request.getBody();
        if (StringUtils.isNotBlank(urlParam)) {
            try {
                urlParam = URLDecoder.decode(urlParam, Charsets.UTF_8.displayName());
                ChannelOrderNotify channelOrderNotify = JSON.parseObject(urlParam, ChannelOrderNotify.class);
                return channelOrderNotify.getReason_type();
            } catch (Exception e) {
                log.warn("Exception:" + Charsets.UTF_8.displayName() + e.getMessage());
            }
        }
        return StringUtils.EMPTY;
    }

    private void eleTrackOpTypeHandler(ChannelOrderStatus channelOrderStatus, OrderTrackEvent orderTrackEvent) {
        if (Objects.isNull(channelOrderStatus)) {
            log.info("Elm 订单状态为空");
            return;
        }
        switch (channelOrderStatus) {
            case BIZ_CONFIRMED:
                orderTrackEvent.setTrackOpType(TrackOpType.ELM_BIZ_CONFIRMED.getOpType());
                break;
            case COMPLETED_PREPARE_MEAL:
                orderTrackEvent.setTrackOpType(TrackOpType.ELM_COMPLETED_PREPARE_MEAL.getOpType());
                break;
            case FULFILLMENT:
                orderTrackEvent.setTrackOpType(TrackOpType.ELM_FULFILLMENT.getOpType());
                break;
            case FINISHED:
                orderTrackEvent.setTrackOpType(TrackOpType.ELM_FINISHED.getOpType());
                break;
            case SETTLEMENT_AMOUINT_READY:
                orderTrackEvent.setTrackOpType(TrackOpType.ELM_SETTLEMENT_AMOUINT_READY.getOpType());
                break;
            case ORDER_ACTIVITY_DETAIL_READY:
                orderTrackEvent.setTrackOpType(TrackOpType.ELM_ORDER_ACTIVITY_DETAIL_READY.getOpType());
                break;
            case CANCEL_APPLIED:
                orderTrackEvent.setTrackOpType(TrackOpType.ELM_CANCEL_APPLIED.getOpType());
                break;
            case CANCELED:
                orderTrackEvent.setTrackOpType(TrackOpType.ELM_CANCELED.getOpType());
                break;
            case LOCKED:
                orderTrackEvent.setTrackOpType(TrackOpType.ELM_LOCKED.getOpType());
                break;
            case UNLOCKED:
                orderTrackEvent.setTrackOpType(TrackOpType.ELM_UNLOCKED.getOpType());
                break;
            default:
                break;
        }
    }

    private void mtTrackHandler(ChannelNotifyEnum notifyEnum, OrderTrackEvent orderTrackEvent, OrderNotifyRequest request) {

        ServiceTypeEnum serviceType = null;
        TrackOpType trackOpType = null;
        switch (notifyEnum) {
            case MT_ORDER_PAID:
                orderTrackEvent.setTrackOpType(TrackOpType.MT_ORDER_PAID.getOpType());
                break;
            case MT_ORDER_CONFIRM:
                orderTrackEvent.setTrackOpType(TrackOpType.MT_ORDER_CONFIRM.getOpType());
                break;
            case MT_ORDER_FINISH:
                orderTrackEvent.setTrackOpType(TrackOpType.MT_ORDER_FINISH.getOpType());
                break;
            case MT_ORDER_CANCEL:
                orderTrackEvent.setTrackOpType(TrackOpType.MT_ORDER_CANCEL.getOpType());
                break;
            case MT_ORDER_PICK_UP:
                orderTrackEvent.setTrackOpType(TrackOpType.MT_ORDER_PICK_UP.getOpType());
                break;
            case MT_ORDER_RIDER_TRANSFER_NOTIFY:
                orderTrackEvent.setTrackOpType(TrackOpType.MT_ORDER_RIDER_TRANSFER_NOTIFY.getOpType());
                break;
            case MT_ORDER_RIDER_TRANSFER_RIDER_NOTIFY:
                // 根据请求参数获取运单
                ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
                try {
                    // 这里面存在灰度相关逻辑
                    MaltFarmAggRiderTransferDTO maltFarmAggRiderTransferDTO = healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(
                        channelTypeEnum, request,
                        orderTrackEvent.getTenantId(), false);
                    // 走完了灰度逻辑 获取到了需要回调麦芽田的对象不为null 说明是需要生产日志的数据 其他情况均不写日志
                    if (null != maltFarmAggRiderTransferDTO) {
                        orderTrackEvent.setTrackOpType(TrackOpType.MT_ORDER_RIDER_TRANSFER_NOTIFY.getOpType());
                    }
                } catch (Exception e) {
                    // 如果获取麦芽田DTO异常 只记录日志，然后写日志走正常逻辑
                    log.info("mtTrackHandler channelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO exception", e);
                    Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, "mtTrackHandler channelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO exception");
                }
                break;
            default:
                return;
        }
    }

    /**
     * 查询数据库获取租户ID
     */
    private CopAccessConfigDO getAccessConfig(int channelId, OrderNotifyRequest request) {
        // 有赞需要特殊处理
        CopAccessConfigDO copAccessConfig = null;
        try {

            if (channelId == ChannelTypeEnum.YOU_ZAN.getCode()) {
                if(StringUtils.isBlank(request.getAppPoiCode())){
                    return null;
                }
                ChannelStoreDO channelStoreDO = copChannelStoreService.selectByChannelPoiCode(channelId, request.getAppPoiCode());

                if (Objects.isNull(channelStoreDO) || Objects.isNull(channelStoreDO.getTenantId())) {
                    log.error("copChannelStoreService.selectByChannelPoiCode, 未获取到渠道租户ID, channelId:{}, appPoiCode:{}",
                            channelId, request.getAppPoiCode());
                    return null;
                }
                copAccessConfig = new CopAccessConfigDO();
                copAccessConfig.setTenantId(channelStoreDO.getTenantId());
                copAccessConfig.setAppId(channelStoreDO.getAppId());
                return copAccessConfig;
            }
            //其他渠道
            copAccessConfig = copAccessConfigService.selectByTenantAppIdAndChannelId(request.getTenantAppId(), channelId);
            if (copAccessConfig == null) {
                log.info("OrderTrackService getAccessConfig, 未获取到渠道租户ID, channelId:{}, tenantAppId:{}",
                        channelId, request.getTenantAppId());
            }
        }catch (Exception e){
            log.error("处理渠道轨迹信息 OrderTrackService.getAccessConfig error e: ", e);
        }
        return copAccessConfig;
    }

    private CopAccessConfigDO getAccessConfig(int channelId, MtReturnDuringDeliveryRequest request) {
        // 有赞需要特殊处理
        CopAccessConfigDO copAccessConfig = null;
        try {
            //其他渠道
            copAccessConfig = copAccessConfigService.selectByTenantAppIdAndChannelId(request.getTenantAppId(), channelId);
            if (copAccessConfig == null) {
                log.info("OrderTrackService getAccessConfig, 未获取到渠道租户ID, channelId:{}, tenantAppId:{}",
                        channelId, request.getTenantAppId());
            }
        }catch (Exception e){
            log.error("处理渠道轨迹信息 OrderTrackService.getAccessConfig error e: ", e);
        }
        return copAccessConfig;
    }

    /**
     * 查询数据库获取租户ID
     */
    private Long getTenantIdParam(CopAccessConfigDO copAccessConfig, String tenantAppId) {
        Long tenantId = Optional.ofNullable(copAccessConfig).map(CopAccessConfigDO::getTenantId).orElse(null);
        Long qnhTenantId = MccConfigUtil.getQnhTenantId(tenantAppId);
        if (qnhTenantId != 0L) {
            log.info("tenantId:{},qnhTenantId:{}", tenantId, qnhTenantId);
            tenantId = qnhTenantId;
        }
        if (Objects.isNull(tenantId)) {
            return null;
        }
        return tenantId;
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public void sendAsyncMessage(OrderTrackEvent orderTrackEvent) {
        try {
            log.info("订单操作轨迹,orderTrackEvent:{}", orderTrackEvent);
            if (orderTrackEvent == null) {
                log.info("轨迹数据不完整,不记录轨迹，orderTrackEvent is null");
                Cat.logEvent("OrderTrackEvent", "DATA_IN_COMPLETE_ERROR");
                return;
            }

            if (!ObjectUtils.allNotNull(orderTrackEvent.getTrackOpType(), orderTrackEvent.getOrderBizType(), orderTrackEvent.getUnifyOrderId(),
                    orderTrackEvent.getTenantId())) {
                log.info("轨迹数据不完整,不记录轨迹");
                Cat.logEvent("OrderTrackEvent", "DATA_IN_COMPLETE_ERROR");
                return;
            }

            List<Long> drunkHorseTenantList = Optional.ofNullable(MccConfigUtil.getDrunkHorseTenantList()).orElse(Collections.emptyList());
            if (drunkHorseTenantList.contains(orderTrackEvent.getTenantId())) {
                log.info("歪马租户不记录轨迹");
                return;
            }

            orderTrackMessageProducer.sendMessage(orderTrackEvent, orderTrackEvent.getUnifyOrderId());
            Cat.logEvent("OrderTrackEvent", "SUCCESS");
            log.info("sendAsyncMessage order track success,msg:{}", orderTrackEvent);
        } catch (Exception e) {
            log.error("sendAsyncMessage order track error", e);
            Cat.logEvent("OrderTrackEvent", "EXCEPTION_ERROR");
        }
    }
    /**
    美团渠道订单出现在在歪马渠道门店,进行告警并推送至大象群组
    **/
    public void alertDhOrderFromMtChannel(int channelId, String tenantAppId, String orderId, String appPoiCode) {
            if(MccConfigUtil.isMtDrunkHorseTenant(tenantAppId) && ChannelTypeEnum.isMeiTuan(channelId) ) {
                log.info("OrderTrackService.alertDhOrderFromMtChannel:美团渠道订单出现在歪马渠道门店，不匹配, channelId:{}, tenantAppId:{}, orderId:{}", channelId, tenantAppId, orderId);
                Cat.logEvent("OrderTrackService","ErrorChannelOrder","orderId:" + orderId, "");
                pubAlertMessageToGroup(channelId, orderId, appPoiCode);
            }
    }

    /**
     * 闪购门店在微商城渠道下单,进行告警并推送至大象群组
     **/
    public void alertMtPoiOrderFromDhChannel(int channelId, String tenantAppId, String orderId, String appPoiCode) {
        if(MccConfigUtil.isMtDrunkHorseTenant(tenantAppId) && ChannelTypeEnum.isDrunkHorse(channelId) ) {
            log.info("OrderTrackService.alertMtOrderFromDhChannel: 闪购门店在微商城渠道下单，不匹配, channelId:{}, tenantAppId:{}, orderId:{}", channelId, tenantAppId, orderId);
            Cat.logEvent("OrderTrackService","DhErrorChannelOrder","orderId:" + orderId, "");
            pubAlertErrorChannelMessageToGroup(channelId, orderId, appPoiCode);
        }
    }

    /**
     推送美团渠道订单出现在微商城渠道门店消息至大象群组
     **/
    private void pubAlertMessageToGroup(int channelId, String orderId, String appPoiCode) {

        String text = "【告警】美团渠道在微商城渠道门店下单\n";
        text = text + "【订单号】" + orderId + "\n";
        text = text + "【门店】" + getPoiName(appPoiCode) + "\n";
        text = text + "【用户id】" + getUserId(channelId, orderId) + "\n";
        text = text + "【[订单详情查询|http://order-admin.waimai.sankuai.com/query/vporderinfo]】";
        long groupId = Lion.getConfigRepository().getLongValue(DH_ALERT_ORDER_GROUP_ID, 67149180078l);
        xmPubService.pushMessageToGroup(text, groupId);
    }

    /**
    推送美团门店订单出现在微商城渠道门店消息至大象群组
     **/
    private void pubAlertErrorChannelMessageToGroup(int channelId, String orderId, String appPoiCode) {
        String text = "【告警】微商城渠道在闪购渠道门店下单\n";
        text = text + "【订单号】" + orderId + "\n";
        text = text + "【门店】" + getPoiName(appPoiCode) + "\n";
        text = text + "【用户id】" + getUserId(channelId, orderId) + "\n";
        text = text + "【[订单详情查询|http://order-admin.waimai.sankuai.com/query/vporderinfo]】";
        long groupId = Lion.getConfigRepository().getLongValue(MT_POI_DH_CHANNEL_ALERT_ORDER_GROUP_ID, 68198401260l);
        xmPubService.pushMessageToGroup(text, groupId);
    }


    private String getUserId(int channelId, String orderId) {
        GetChannelOrderDetailRequest request = new GetChannelOrderDetailRequest();
        request.setChannelId(channelId);
        request.setOrderId(orderId);
        request.setTenantId(1000395L);
        GetChannelOrderDetailResult detail  =
                routeServiceFactory.selectChannelOrderService(channelId, 1000395L).getChannelOrderDetail(request);
        if (detail != null && detail.getChannelOrderDetail() != null) {
            return detail.getChannelOrderDetail().getUserId();
        } else {
            return Strings.EMPTY;
        }
    }

    private String getPoiName(String appPoiCode) {
        Long storeId = copChannelStoreService.selectChannelStoreId(1000395L, ChannelTypeEnum.MT_DRUNK_HORSE.getCode(), appPoiCode);
        PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(Arrays.asList(storeId), 1000395L);
        if (Objects.isNull(response) ||
                Objects.isNull(response.getPoiInfoMap()) ||
                Objects.isNull(response.getPoiInfoMap().get(storeId))) {
            return Strings.EMPTY;
        }
        return response.getPoiInfoMap().get(storeId).getPoiName();
    }
}
