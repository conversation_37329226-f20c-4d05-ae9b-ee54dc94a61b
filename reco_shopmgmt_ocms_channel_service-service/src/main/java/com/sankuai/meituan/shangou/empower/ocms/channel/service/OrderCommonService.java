package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.model.OrderBaseModel;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.ChannelPoiBaseInfoDTO;

import javax.annotation.Nullable;

public interface OrderCommonService {
    /**
     * 根据viewOrderId查询订单，主要用于和渠道交互时，可能有些信息之前没有传过来，
     * 在调用ocmschannel接口前新增这些信息的话要改动的地方太多了，于是收口在ocmschannel处理
     * @param tenantId
     * @param storeId
     * @param viewOrderId
     * @param orderBizType
     * @return
     */
    @Nullable
    BizOrderModel queryOrderByViewId(Long tenantId, Long storeId, String viewOrderId, DynamicOrderBizType orderBizType);

    /**
     * 根据viewOrderId查询订单基础数据，主要用于和渠道交互时，可能有些信息之前没有传过来，
     * 在调用ocmschannel接口前新增这些信息的话要改动的地方太多了，于是收口在ocmschannel处理，
     * 如果只需要基础数据，可以调用这个接口，这个接口链路下，会优先查 Tair
     * @param tenantId
     * @param storeId
     * @param viewOrderId
     * @param orderBizType
     * @return
     */
    @Nullable
    OrderBaseModel queryOrderBaseByViewId(Long tenantId, Long storeId, String viewOrderId, DynamicOrderBizType orderBizType);

    /**
     * 根据渠道 类型和渠道门店，查询中台门店信息
     * @param dynamicChannelType
     * @param channelPoiCode
     * @return
     */
    ChannelPoiBaseInfoDTO queryTenantInfoByChannelPoiInfo(DynamicChannelType dynamicChannelType,  String channelPoiCode);
}
