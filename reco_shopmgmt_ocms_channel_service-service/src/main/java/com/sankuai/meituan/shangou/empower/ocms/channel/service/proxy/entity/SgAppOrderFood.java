package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ji<PERSON><PERSON><PERSON>g on 14-3-20.
 */
public class SgAppOrderFood {

    private Long item_id;
    private String app_food_code;
    private String food_name;
    private Integer quantity;
    private Float price;
    private Float original_price;
    private Float actual_price;
    private Float box_num;
    private Float box_price;
    private String unit;
    private Float food_discount;
    private String sku_id;
    private String food_property;   // 菜品属性
    private String spec;            // 规格名称
    private Long wm_food_sku_id;    // 美团菜品skuId, 注意:此字段需确保任何情况不会推送给三方
    private Integer cart_id;         // 商品口袋号
    private String upc;
    private Long weight;
    private String weight_for_unit;
    private String weight_unit;
    // 以下均为美团内部id
    private long mt_tag_id;
    private long mt_spu_id;
    private long mt_sku_id;
    private SgOpenOrderDetailExtra detail_extra;
    private String warehouse_info;
    private String attr_names;// attrNames，属性名称，与food_property顺序一致
    private String attr_counts;// attrCounts，属性值的个数，与food_property顺序一致
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String app_medicine_code;
    // 商品图片
    private String picture;

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public String getApp_food_code() {
        return app_food_code;
    }

    public void setApp_food_code(String app_food_code) {
        this.app_food_code = app_food_code;
    }

    public String getFood_name() {
        return food_name;
    }

    public void setFood_name(String food_name) {
        this.food_name = food_name;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Float getPrice() {
        return price;
    }

    public void setPrice(Float price) {
        this.price = price;
    }

    public Float getBox_num() {
        return box_num;
    }

    public void setBox_num(Float box_num) {
        this.box_num = box_num;
    }

    public Float getBox_price() {
        return box_price;
    }

    public void setBox_price(Float box_price) {
        this.box_price = box_price;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Float getFood_discount() {
        return food_discount;
    }

    public void setFood_discount(Float food_discount) {
        this.food_discount = food_discount;
    }

    public String getSku_id() {
        return sku_id;
    }

    public void setSku_id(String sku_id) {
        this.sku_id = sku_id;
    }

    public String getFood_property() {
        return food_property;
    }

    public void setFood_property(String food_property) {
        this.food_property = food_property;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public Long getWm_food_sku_id() {
        return wm_food_sku_id;
    }

    public void setWm_food_sku_id(Long wm_food_sku_id) {
        this.wm_food_sku_id = wm_food_sku_id;
    }

    public Integer getCart_id() {
        return cart_id;
    }

    public void setCart_id(Integer cart_id) {
        this.cart_id = cart_id;
    }

    public String getUpc() {
        return upc;
    }

    public void setUpc(String upc) {
        this.upc = upc;
    }

    public Long getWeight() {
        return weight;
    }

    public void setWeight(Long weight) {
        this.weight = weight;
    }

    public String getWeight_for_unit() {
        return weight_for_unit;
    }

    public void setWeight_for_unit(String weight_for_unit) {
        this.weight_for_unit = weight_for_unit;
    }

    public String getWeight_unit() {
        return weight_unit;
    }

    public void setWeight_unit(String weight_unit) {
        this.weight_unit = weight_unit;
    }

    public long getMt_tag_id() {
        return mt_tag_id;
    }

    public void setMt_tag_id(long mt_tag_id) {
        this.mt_tag_id = mt_tag_id;
    }

    public long getMt_spu_id() {
        return mt_spu_id;
    }

    public void setMt_spu_id(long mt_spu_id) {
        this.mt_spu_id = mt_spu_id;
    }

    public long getMt_sku_id() {
        return mt_sku_id;
    }

    public void setMt_sku_id(long mt_sku_id) {
        this.mt_sku_id = mt_sku_id;
    }

    public SgOpenOrderDetailExtra getDetail_extra() {
        return detail_extra;
    }

    public void setDetail_extra(SgOpenOrderDetailExtra detail_extra) {
        this.detail_extra = detail_extra;
    }

    public String getWarehouse_info() {
        return warehouse_info;
    }

    public void setWarehouse_info(String warehouse_info) {
        this.warehouse_info = warehouse_info;
    }

    public String getAttr_names() {
        return attr_names;
    }

    public void setAttr_names(String attr_names) {
        this.attr_names = attr_names;
    }

    public String getAttr_counts() {
        return attr_counts;
    }

    public void setAttr_counts(String attr_counts) {
        this.attr_counts = attr_counts;
    }

    public Float getOriginal_price() {
        return original_price;
    }
    public void setOriginal_price(Float original_price) {
        this.original_price = original_price;
    }

    public Float getActual_price() {
        return actual_price;
    }

    public void setActual_price(Float actual_price) {
        this.actual_price = actual_price;
    }

    public String getApp_medicine_code() {
        return app_medicine_code;
    }

    public void setApp_medicine_code(String app_medicine_code) {
        this.app_medicine_code = app_medicine_code;
    }

    public Long getItem_id() {
        return item_id;
    }

    public void setItem_id(Long item_id) {
        this.item_id = item_id;
    }

    @Override
    public String toString() {
        return "SgAppOrderFood{" +
                "item_id=" + item_id +
                ", app_food_code='" + app_food_code + '\'' +
                ", food_name='" + food_name + '\'' +
                ", quantity=" + quantity +
                ", price=" + price +
                ", original_price=" + original_price +
                ", actual_price=" + actual_price +
                ", box_num=" + box_num +
                ", box_price=" + box_price +
                ", unit='" + unit + '\'' +
                ", food_discount=" + food_discount +
                ", sku_id='" + sku_id + '\'' +
                ", food_property='" + food_property + '\'' +
                ", spec='" + spec + '\'' +
                ", wm_food_sku_id=" + wm_food_sku_id +
                ", cart_id=" + cart_id +
                ", upc='" + upc + '\'' +
                ", weight=" + weight +
                ", weight_for_unit='" + weight_for_unit + '\'' +
                ", weight_unit='" + weight_unit + '\'' +
                ", mt_tag_id=" + mt_tag_id +
                ", mt_spu_id=" + mt_spu_id +
                ", mt_sku_id=" + mt_sku_id +
                ", detail_extra=" + detail_extra +
                ", warehouse_info='" + warehouse_info + '\'' +
                ", attr_names='" + attr_names + '\'' +
                ", attr_counts='" + attr_counts + '\'' +
                ", app_medicine_code='" + app_medicine_code + '\'' +
                ", picture='" + picture + '\'' +
                '}';
    }
}
