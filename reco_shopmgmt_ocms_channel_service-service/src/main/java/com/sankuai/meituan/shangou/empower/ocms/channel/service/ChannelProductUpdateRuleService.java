package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.meituan.linz.thrift.response.ThriftResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ChannelProductUpdateRuleDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ChannelProductUpdateRuleRequest;

/**
 * @author:huch<PERSON><PERSON>
 * @date: 2019/1/20
 * @time: 下午3:36
 */
public interface ChannelProductUpdateRuleService {

    ThriftResponse<ChannelProductUpdateRuleDTO> queryChannelProductUpdateRule(ChannelProductUpdateRuleRequest req);

}
