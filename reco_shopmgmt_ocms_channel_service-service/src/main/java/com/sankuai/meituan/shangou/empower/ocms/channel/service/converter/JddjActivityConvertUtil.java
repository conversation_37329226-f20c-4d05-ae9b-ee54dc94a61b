package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ActivityTypeEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON>12
 * @create: 2019-03-09 17:57
 */
@Slf4j
public class JddjActivityConvertUtil {

    @Getter
    public enum PromotionType {
        //
        SINGLE(3, "3", "单品直降"),
        UNKNOWN(99, "99", "未知");

        private int code;
        private String abbrev;
        private String desc;

        PromotionType(int code, String abbrev, String desc) {
            this.code = code;
            this.abbrev = abbrev;
            this.desc = desc;
        }
    }

    public static int getPromotionType(ActivityTypeEnum activityType) {
        if (activityType == ActivityTypeEnum.NORMAL) {
            return PromotionType.SINGLE.getCode();
        }
        return PromotionType.UNKNOWN.getCode();
    }

}
