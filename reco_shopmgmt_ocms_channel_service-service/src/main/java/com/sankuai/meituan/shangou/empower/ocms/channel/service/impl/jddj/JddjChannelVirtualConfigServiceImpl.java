package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.zebra.tool.util.StringUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelVirtualConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 京东渠道虚拟配置内部服务接口
 *
 * <AUTHOR> wuyongjiang
 * @since : 2023/4/26
 */
@Slf4j
@Service("jddjChannelVirtualConfigService")
public class JddjChannelVirtualConfigServiceImpl implements ChannelVirtualConfigService {
    @Value("${jddj.url.base}" + "${jddj.url.recommendBrandAndCate}")
    private String recommendBrandUrl;

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Override
    public RecommendCategoryResponse recommendCategoryByVirtualConfig(RecommendCategoryRequest request) {
        RecommendCategoryResponse resp;
        resp = getRecommendCategoryCode(request);
        if (resp.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
            return resp;
        } else if (resp.getChannelCategoryCode() == 0) {
            log.warn("JDDJ recommendCategory return 0 request:{}:", request);
            //京东渠道类目接口不稳定，有时会返回null,如果为空就进行一次重试
            resp = getRecommendCategoryCode(request);
        }
        return resp;
    }

    private RecommendCategoryResponse getRecommendCategoryCode(RecommendCategoryRequest request) {
        RecommendCategoryResponse resp = new RecommendCategoryResponse();

        // 构造参数
        List fields = Lists.newArrayList();
        fields.add(ProjectConstant.JDDJ_FIELD_CATEGORY);
        fields.add(ProjectConstant.JDDJ_FIELD_BRAND);
        Map param = new HashMap();
        param.put(ProjectConstant.PRODUCT_NAME, request.getName());
        param.put(ProjectConstant.FIELDS, fields);


        Map<String, Object> virtualSysParams;
        // 如果request传入了虚拟参数就直接用
        if (Objects.nonNull(request.getVirtualSysParam()) && StringUtil.isNotBlank(request.getVirtualSysParam())) {
            try {
                virtualSysParams = JSON.parseObject(request.getVirtualSysParam(), Map.class);
            } catch (Exception e) {
                log.error("JDDJ recommendCategory parse virtualSysParam error, request:{}", request, e);
                throw new IllegalArgumentException("虚拟参数格式错误");
            }
        } else {
            // 否则从根据租户查询
            virtualSysParams = jddjChannelGateService.getChannelVirtualSysParam(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId());
        }
        // 限频检查
        String appId = String.valueOf(virtualSysParams.get(ProjectConstant.JDDJ_APP_KEY));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.RECOMMEND_CATE_BRAND, String.valueOf(request.getBaseInfo().getTenantId()))) {
            log.warn("按名称查询推荐类目,获取令牌失败 不阻塞流程 直接调用接口，request {}", param);
        }

        // 发送请求
        Map brandMap = jddjChannelGateService.sendPostByVirtualSysParam(recommendBrandUrl, null,
                                baseConverterService.baseRequest(request.getBaseInfo()), param, virtualSysParams);

        if (Objects.isNull(brandMap)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用开放平台接口出现异常"));
        }

        // 根据状态码映射错误结果
        int code = Integer.parseInt((String) brandMap.get(ProjectConstant.CODE));
        if (code != 0) {
            String message = (String) brandMap.get(ProjectConstant.MSG);
            log.warn("查询京东推荐渠道类目失败, tenantId:{}, name:{}, message:{}", request.getBaseInfo().getTenantId(), request.getName(), message);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, buildErrorMessage(code, message)));
        }

        // 取出推荐的categoryId
        String brandJsonstr = (String) brandMap.get(ProjectConstant.DATA);
        JSONObject brandJson = JSON.parseObject(brandJsonstr).getJSONObject(ProjectConstant.RESULT);
        Long categoryId = brandJson.getLong(ProjectConstant.JDDJ_FIELD_CATEGORYID);
        Long brandId = brandJson.getLong(ProjectConstant.JDDJ_FIELD_BRANDID);

        // 成功结果构造
        resp.setStatus(new ResultStatus(ResultCode.SUCCESS.getCode(), "成功", null));
        resp.setUpcCode(request.getUpcCode());
        resp.setName(request.getName());
        if (categoryId != null) {
            resp.setChannelCategoryCode(categoryId);
        }
        if (brandId != null){
            resp.setBrandId(brandId);
        }

        return resp;
    }

    private String buildErrorMessage(int code, String message) {
        if (MccConfigUtil.isJdChannelConfigErrorCode(code)) {
            return "渠道信息填写错误，查询平台推荐类目信息失败";
        }
        return message;
    }
}
