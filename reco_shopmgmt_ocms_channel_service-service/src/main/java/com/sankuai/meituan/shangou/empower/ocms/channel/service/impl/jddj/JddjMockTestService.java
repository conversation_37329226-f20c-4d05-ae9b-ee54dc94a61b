package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailResult;

/***
 * author : <EMAIL> 
 * data : 2019/7/19 
 * time : 下午2:45
 **/
public interface JddjMockTestService {

    GetChannelOrderDetailResult getMockOrderDetail(GetChannelOrderDetailRequest request);

    boolean isMockOrder(long tenantId, String channelOrderId);

    boolean isMockAfterSaleOrder(long tenantId, String afterSaleId);

}
