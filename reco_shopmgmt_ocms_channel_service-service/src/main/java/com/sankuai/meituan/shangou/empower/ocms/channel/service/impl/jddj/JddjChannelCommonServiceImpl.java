package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelSaveOrgProductAbilityDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.channelconfig.SaveOrgProductAbilityRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.CommonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.JddjSignUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: lijunbo03
 * @Date: 2019/8/6
 */
@Service("jddjChannelCommonService")
public class JddjChannelCommonServiceImpl implements ChannelCommonService {

    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private CommonLogger log;

    @Override
    public ResultStatus auth(String url, Map<String, String> params, String requestIp, String originalUrl) {
        String tenantAppId = params.get("app_key");
        if (Objects.isNull(tenantAppId)) {
            log.error("京东到家验签，未获取到 app_key 入参");
            return ResultGenerator.genFailResult("未获取到 app_key 入参").setData("参数 app_key 为空");
        }

        CopAccessConfigDO copAccessConfigDO = copAccessConfigService.selectByTenantAppIdAndChannelId(tenantAppId, ChannelTypeEnum.JD2HOME.getCode());
        if (Objects.isNull(copAccessConfigDO) || Objects.isNull(copAccessConfigDO.getTenantId())) {
            log.error("饿百验签，未获取到租户配置：tenantAppId: [{}], channelId: [{}]", tenantAppId, ChannelTypeEnum.ELEM.getCode());
            return ResultGenerator.genFailResult("未获取到租户配置").setData("配置为空");
        }

        Long tenantId = copAccessConfigDO.getTenantId();

        // 白名单
        String jdWhiteList = MccConfigUtil.getJdWhiteList();
        Set<Long> tenantIds = new HashSet<>();
        if (StringUtils.isNotBlank(jdWhiteList)) {
            String[] tenantIdStrings = jdWhiteList.split(",");
            for (String tenantIdString : tenantIdStrings) {
                tenantIds.add(Long.valueOf(tenantIdString.trim()));
            }
        }
        if (tenantIds.contains(tenantId)) {
            return ResultGenerator.genSuccessResult().setData(String.valueOf(tenantId));
        }

        String secret = getSecret(copAccessConfigDO);
        if (StringUtils.isBlank(secret)) {
            log.warn("京东租户 tenantId:{}, secret为空", tenantId);
            return ResultGenerator.genFailResult("未获取到secret").setData(String.valueOf(tenantId));
        }
        String requestSign = params.remove("sign");
        String sign = JddjSignUtils.getSignByMD5(new HashMap<>(params), secret);
        log.info("京东验签，租户ID:{}, 开放平台sign:{}, 渠道网关sign:{}", tenantId, requestSign, sign);
        if (!sign.equalsIgnoreCase(requestSign)) {
            log.warn("京东验签失败params:{}", params);
            if (!decodeOneTime(params, tenantId, secret, requestSign)) {
                log.warn("京东验签失败params:{}", params);
                return ResultGenerator.genFailResult("验签失败").setData(String.valueOf(tenantId));
            }
        }
        return ResultGenerator.genSuccessResult().setData(String.valueOf(tenantId));
    }
//    public ResultStatus auth(String url, Map<String, String> params, String requestIp, String originalUrl) {
//        Long tenantId = copAccessConfigService.selectTenantId(ChannelTypeEnum.JD2HOME.getCode(), params.get("app_key"));
//        if (tenantId == null) {
//            return ResultGenerator.genFailResult("未获取到租户配置").setData("租户为空");
//        }
//
//        // 白名单
//        String jdWhiteList = MccConfigUtil.getJdWhiteList();
//        Set<Long> tenantIds = new HashSet<>();
//        if (StringUtils.isNotBlank(jdWhiteList)) {
//            String[] tenantIdStrings = jdWhiteList.split(",");
//            for (String tenantIdString : tenantIdStrings) {
//                tenantIds.add(Long.valueOf(tenantIdString.trim()));
//            }
//        }
//        if (tenantIds.contains(tenantId)) {
//            return ResultGenerator.genSuccessResult().setData(String.valueOf(tenantId));
//        }
//
//        String secret = getSecret(tenantId, ChannelTypeEnum.JD2HOME.getCode());
//        if (StringUtils.isBlank(secret)) {
//            log.warn("京东租户 tenantId:{}, secret为空", tenantId);
//            return ResultGenerator.genFailResult("未获取到secret").setData(String.valueOf(tenantId));
//        }
//        String requestSign = params.remove("sign");
//        String sign = JddjSignUtils.getSignByMD5(new HashMap<>(params), secret);
//        log.info("京东验签，租户ID:{}, 开放平台sign:{}, 渠道网关sign:{}", tenantId, requestSign, sign);
//        if (!sign.equalsIgnoreCase(requestSign)) {
//            log.warn("京东验签失败params:{}", params);
//            if (!decodeOneTime(params, tenantId, secret, requestSign)) {
//                log.warn("京东验签失败params:{}", params);
//                return ResultGenerator.genFailResult("验签失败").setData(String.valueOf(tenantId));
//            }
//        }
//        return ResultGenerator.genSuccessResult().setData(String.valueOf(tenantId));
//    }

    private boolean decodeOneTime(Map<String, String> params, Long tenantId, String secret, String requestSign) {
        String sign = "";
        try {
            for (String key : params.keySet()) {
                String decodeValue = CommonUtils.decode(params.get(key));
                params.put(key, decodeValue);
            }
            sign = JddjSignUtils.getSignByMD5(new HashMap<>(params), secret);
        } catch (Exception e) {
            log.warn("Jdauth.CommonUtils.decode异常：", e);
        }
        log.info("京东解码验签，租户ID:{}, 开放平台sign:{}, 渠道网关sign:{}", tenantId, requestSign, sign);
        return sign.equalsIgnoreCase(requestSign);
    }

    private String getSecret(CopAccessConfigDO copAccessConfigDO) {
        String config = copAccessConfigDO.getSysParams();
        return JSON.parseObject(config).getString("secret");
    }

    @Override
    public ResultStatus saveStoreProductAbility(SaveOrgProductAbilityRequest request) {
        try {
            BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());

            ChannelSaveOrgProductAbilityDTO channelSaveOrgProductAbilityDTO = new ChannelSaveOrgProductAbilityDTO();
            channelSaveOrgProductAbilityDTO.setOrgProductAbilityCode(request.getOrgProductAbilityCode());
            channelSaveOrgProductAbilityDTO.setOpt(request.getOpt());
            // 调用渠道接口
            ChannelResponseDTO postResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SAVE_ORG_PRODUCT_ABILITY, baseRequest, channelSaveOrgProductAbilityDTO);
            if (postResult.getDataResponse() == null) {
                return ResultGenerator.genFailResult("调用服务失败");
            }
            if(postResult.getDataResponse().isSuccess()){
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(postResult.getDataResponse().getMsg());
        } catch (IllegalArgumentException e) {
            log.error("JddjChannelSkuServiceImpl.saveStoreProductAbility 参数校验失败",  e);
            return ResultGenerator.genFailResult("参数校验失败");

        } catch (Exception e) {
            log.error("JddjChannelSkuServiceImpl.saveStoreProductAbility 服务异常.", e);
            return ResultGenerator.genFailResult("服务异常"+e.getMessage());
        }
    }

//    private String getSecret(long tenantId, int channelId) {
//        String config = copAccessConfigService.selectSysParams(tenantId, channelId);
//        return JSON.parseObject(config).getString("secret");
//    }
}