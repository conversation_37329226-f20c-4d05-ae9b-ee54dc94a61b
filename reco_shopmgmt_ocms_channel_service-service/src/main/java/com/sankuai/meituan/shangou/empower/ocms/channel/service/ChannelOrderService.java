package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelNotifyEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.RiderPointBatchSyncRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;

/**
 * @description: 订单业务接口
 * @author: zhaolei12
 * @create: 2019/1/28 上午10:19
 */
public interface ChannelOrderService {

    ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request);

    GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request);

    ResultStatus preparationMealComplete(PreparationMealCompleteRequest request);

    GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request);

    GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request);

    ResultStatus agreeRefund(AgreeRefundRequest request);

    ResultStatus rejectRefund(RejectRefundRequest request);

    ResultStatus refundGoods(RefundGoodsRequest request);

    ResultStatus poiCancelOrder(PoiCancelOrderRequest request);

    ResultStatus poiPartRefundApply(PoiPartRefundRequest request);

    CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request);

    ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request);

    PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request);

    OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request);

    GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request);

    GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request);

    /***
     * 更新订单配送状态
     * **/
    ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request);

    /***
     * 更新订单配送信息 如骑手信息
     * **/
    ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request);

    /***
     * 更新骑手信息
     * **/
    ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request);

    /**
     * 只更新骑手信息
     */
    ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request);

    /***
     * 转商家自配送
     * **/
    ResultStatus selfDelivery(SelfDeliveryRequest request);

    /***
     * 查询订单配送异常描述
     * **/
    QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request);


    /**
     * 查询渠道订单部分退款商品详情,暂时只有mt渠道
     */

    ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request);

    /**
     * 分页查询渠道订单订单列表
     */
    QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request);


    /**
     * 分页查询渠道异常订单订单列表,暂时只有mt渠道
     */
    QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request);

    default ChannelOrderDetailDTO getOrderDetail4ChannelMessage(ChannelTypeEnum channelType, ChannelNotifyEnum channelNotify,
                                                                Long tenantId, String msg){
        return null;
    }

    default LatestDaySeqResult batchGetStoreLatestDaySeq(DaySeqRequest request) {
        return null;
    }

    default LatestDaySeqResult batchGetOrderByDaySeq(DaySeqRequest request) {
        return null;
    }

    default GetSelfFetchCodeResult checkSelfFetchCode(SelfFetchCodeGetRequest request){
        return null;
    }

    default ResultStatus verifySelfFetchCode(VerifySelfFetchCodeRequest request){
        return null;
    }

    default ResultStatus markSignByOrderId(MarkSignByOrderIdRequest request){
        return ResultGenerator.genResult(ResultCode.SUCCESS);
    }


    default ResultStatus updateDeliveryProofPhoto(UpdateDeliveryProofPhotoRequest request) {
        return new ResultStatus(ResultCode.FAIL.getCode(), "该渠道未实现此功能", null);
    }


    default OrderPromotionResult queryOrderPromotionInfo(GoodsSettlementInfoRequest request) {
        return null;
    }

    default ChannelOrderMoneyRefundItemResult queryChannelOrderMoneyRefundItemList(ChannelOrderMoneyRefundItemRequest request) {
        return null;
    }

    default ResultStatus moneyRefund(MoneyRefundRequest request) {
        return null;
    }


    default ResultStatus startPickNotify(StartPickRequest request) {
        return null;
    }

    default WeightPartRefundGoodsResult queryWeightPartRefundGoodsDetail(WeightPartRefundGoodsRequest request){
        return null;
    }
    default MTOrderDaySeqResponse getOrderIdByDaySeq(MTOrderDaySeqRequest request) {
        return null;
    };

    default QueryAfsOrderListResult queryAfsOrderList(QueryChannelAfsOrderListRequest request){

        return null;
    }


    default MTOrderSeqResponse getOrderSeq(MTOrderDaySeqRequest request){
        return null;
    }

    default QueryReverseJddjResult queryReverseJddj(QueryReverseJddjRequest request) {
        return null;
    }

    default RejectReasonCodeResponse getRejectReasonAndCodeList(RejectReasonCodeRequest request) {return null;}

    default GetChannelReturnDuringDeliveryResult getChannelReturnDuringDelivery(GetChannelReturnDuringDeliveryRequest request) {
        return null;
    }

    default ResultStatus returnGoodsAudit(ChannelReturnGoodsAuditRequest request){
        return null;
    };

    /**
     * 骑手信息变更通知渠道
     */
    default ResultStatus riderInfoChange(RiderInfoChangeRequest request) {
        return null;
    }

    default QueryCompensationOrderListResult queryCompensateOrderList(PoiBatchCompensationRequest request){
        return null;
    }

    default RefundAddressChangeFeeDTO getRefundAddressChangeFeeInfo(GetRefundAddressChangeFeeRequest changeFeeRequest){
        return null;
    }

    /**
     * 解密用户敏感数据，目前仅抖音使用
     * @param request
     * @return
     */
    default DecryptUserSensitiveDataResponse decryptUserSensitiveData(DecryptUserSensitiveDataRequest request) {
        DecryptUserSensitiveDataResponse response = new DecryptUserSensitiveDataResponse();
        ResultStatus result = new ResultStatus();
        result.setCode(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode());
        result.setMsg(ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg());
        response.setStatus(result);
        return response;
    }

    /**
     * 获取渠道订单隐私号
     * @param request
     * @return
     */
    default GetOrderPrivacyPhoneResult queryOrderPrivacyPhone(GetOrderPrivacyPhoneRequest request){
        return null;
    }

    /**
     * 获取退单商家反货运费
     * @param request
     * @return
     */
    default GetOrderRefundGoodsFeeResult queryOrderRefundGoodsFee(GetOrderRefundGoodsFeeRequest request){
        return null;
    }

    /***
     * 批量更新骑手轨迹信息，配送中上传
     * **/
    default boolean batchSyncRiderPoint(RiderPointBatchSyncRequest request) {
        return true;
    }

    /***
     * 批量更新骑手所有轨迹信息，配送完成后上传
     * **/
    default boolean batchSyncRiderAllPoint(RiderPointBatchSyncRequest request) {
        return true;
    }
}
