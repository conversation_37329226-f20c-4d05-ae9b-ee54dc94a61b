package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import java.util.*;

import javax.annotation.Resource;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ChannelErrorCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSettlementService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;

/**
 * 饿了么结算业务接口实现
 *
 * <AUTHOR>
 * @since 2021/3/1
 */
@Service("elmChannelSettlementService")
public class ElmChannelSettlementServiceImpl implements ChannelSettlementService {

    @Resource
    private ElmConverterService elmConverterService;

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private CommonLogger log;
    /**
     * new Gson().toJson，null值的字段不会保留。
     * 下面的方式会保留null值字段
     */
    private final Gson GSON = new GsonBuilder().serializeNulls().create();

    @Override
    public ChannelSettlementPageResponse getChannelSettlementList(ChannelSettlementPageRequest request) {
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getShopId()));

        ChannelSettlementPageResponse channelSettlementPageResponse = new ChannelSettlementPageResponse();
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(0);

        ChannelSettlementPageDTO channelSettlementPageDto = new ChannelSettlementPageDTO();
        channelSettlementPageResponse.setChannelSettlementPageDto(channelSettlementPageDto);

        channelSettlementPageResponse.setResultStatus(resultStatus);

        List<String> dateList = generateDateList(request.getAccountTimeStart(), request.getAccountTimeEnd());
        for (String date : dateList) {
            BillGetParam bizParam = new BillGetParam();

            Map<String, ChannelStoreDO> channelStoreDoMap = copChannelStoreService.getChannelPoiCode(request.getTenantId(), request.getChannelId(), Arrays.asList(request.getShopId()));
            ChannelStoreDO channelStore =  channelStoreDoMap.get("" + request.getTenantId() + "-" + request.getChannelId() + "-" + request.getShopId());
            // 判断门店渠道是否存在
            if (channelStore == null) {
                return channelSettlementPageResponse.setResultStatus(ResultGenerator.genResult(ResultCode.CHANNEL_STORE_CONFIG_ERROR, "门店渠道配置信息不存在!"));
            }
            bizParam.setShopId(channelStore.getChannelOnlinePoiCode());
            bizParam.setPage("" + request.getPageNo());
            bizParam.setDate(date);
            log.info("查询饿了么账单 request:{}  bizParam:{}", request, JSON.toJSONString(bizParam));
            Map<Long, ChannelResponseDTO<BillGetResult>> resultData = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.BILL_GET, baseRequest, bizParam);
            log.info("查询饿了么账单 request:{}  response:{}", request, JSON.toJSONString(resultData));
            if (Objects.nonNull(resultData.get(request.getShopId()))) {
                String errno = resultData.get(request.getShopId()).getErrno();
                if(!ChannelErrorCode.ELM.SUCCESS.equals(errno)){
                    ResultCode resultCode = convert2ResultCode(errno);
                    return channelSettlementPageResponse.setResultStatus(ResultGenerator.genResult(resultCode, resultData.get(request.getShopId()).getErrorMsg()));
                }

                BillGetResult billGetResult = resultData.get(request.getShopId()).getBody().getData();
                if (billGetResult != null) {
                    if (StringUtils.isBlank(billGetResult.getDate())) {
                        log.warn("返回账期为空 billGetResult:{}", billGetResult);
                        billGetResult.setDate(date);
                    }
                    ChannelSettlementDTO channelSettleOrderDTO = elmConverterService.convertChannelSettlement(billGetResult);
                    if (Objects.nonNull(channelSettleOrderDTO)) {
                        // bill.get接口 单次请求的原始数据（门店+天粒度？）
                        String originalSettlementData = GSON.toJson(buildBillGetStandardResult(billGetResult));
                        channelSettleOrderDTO.setOriginalSettlementData(originalSettlementData);
                        channelSettlementPageResponse.getChannelSettlementPageDto().addToChannelSettlementDtoList(channelSettleOrderDTO);
                    }
                }
            }
        }

        PageInfo pageInfo = new PageInfo(request.getPageNo(), request.getPageSize(),
                CollectionUtils.isNotEmpty(channelSettlementPageResponse.getChannelSettlementPageDto().getChannelSettlementDtoList()) ? 1 : 0,
                CollectionUtils.isNotEmpty(channelSettlementPageResponse.getChannelSettlementPageDto().getChannelSettlementDtoList()) ?
                        channelSettlementPageResponse.getChannelSettlementPageDto().getChannelSettlementDtoList().size() : 0);
        channelSettlementPageDto.setPageInfo(pageInfo);
        log.info("查询饿了么账单结果 channelSettlementPageResponse={}", channelSettlementPageResponse);
        return channelSettlementPageResponse;
    }

    private BillGetStandardResult buildBillGetStandardResult(BillGetResult billGetResult){
        return BillGetStandardResult.builder()
                .date(billGetResult.getDate())
                .baiduShopId(billGetResult.getBaidu_shop_id())
                .shopId(billGetResult.getShop_id())
                .payEntity(billGetResult.getPay_entity())
                .orderCount(billGetResult.getOrder_count())
                .orderDetailFee(billGetResult.getOrder_detail_fee())
                .payFee(billGetResult.getPay_fee())
                .expendFee(billGetResult.getExpend_fee())
                .shopFee(billGetResult.getShop_fee())
                .paymentDate(billGetResult.getPayment_date())
                .settleStatus(billGetResult.getSettle_status())
                .build();
    }

    private List<String> generateDateList(Long accountTimeStart, Long accountTimeEnd) {
        List<String> dateList = Lists.newArrayList();
        Date date = new Date(accountTimeStart);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);

        while (calendar.getTimeInMillis() <= accountTimeEnd) {
            dateList.add("" + (calendar.getTimeInMillis() / 1000));
            calendar.add(Calendar.DATE, 1);
        }

        return dateList;
    }

    private ResultCode convert2ResultCode(String code) {
        if (ChannelErrorCode.ELM.REQUEST_LIMIT.equals(code)) {
            return ResultCode.TRIGGER_LIMIT;
        } else if (ChannelErrorCode.ELM.SHOP_NOT_EXIST.equals(code)){
            return ResultCode.CHANNEL_STORE_CONFIG_ERROR;
        } else {
            return ResultCode.CHANNEL_SYSTEM_ERROR;
        }
    }

    @Override
    public ChannelSettlementAndDetailResponse getChannelOrderSettlementListById(ChannelOrderSettlementByIdRequest request) {
        ChannelSettlementAndDetailResponse response = new ChannelSettlementAndDetailResponse();
        Long date = getDateBySettlementId(request.getSettlementId());
        if (date == null) {
            return response.setResultStatus(ResultGenerator.genResult(ResultCode.INVALID_PARAM));
        }
        String poiStoreId = request.getSettlementId().split("-")[0];

        Long shopId = copChannelStoreService.selectChannelStoreId(request.getTenantId(), request.getChannelId(), poiStoreId);

        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(shopId));

        BillGetParam bizParam = new BillGetParam();
        bizParam.setShopId(poiStoreId);
        bizParam.setPage("" + request.getPageNo());
        bizParam.setDate(date.toString());
        log.info("根据settlementId查询饿了么账单明细 request:{} bizParam:{}", request, JSON.toJSONString(bizParam));
        Map<Long, ChannelResponseDTO<BillOrderDetailResult>> resultData = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.BILL_ORDER_DETAIL, baseRequest, bizParam);
        log.info("根据settlementId查询饿了么账单明细 request:{} response:{}", request, JSON.toJSONString(resultData));

        if(Objects.isNull(resultData.get(shopId))){
            return response.setResultStatus(ResultGenerator.genResult(ResultCode.CHANNEL_SYSTEM_ERROR));
        }
        String errno = resultData.get(shopId).getErrno();
        if(!ChannelErrorCode.ELM.SUCCESS.equals(errno)){
            ResultCode resultCode = convert2ResultCode(errno);
            return response.setResultStatus(ResultGenerator.genResult(resultCode, resultData.get(shopId).getErrorMsg()));
        }
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(0);
        response.setResultStatus(resultStatus);

        ChannelOrderSettlementPageDTO channelSettlementDetailPageDto = new ChannelOrderSettlementPageDTO();
        if (Objects.nonNull(resultData.get(shopId).getBody().getData()) && CollectionUtils.isNotEmpty(resultData.get(shopId).getBody().getData().getOrder_list())) {
            for (BillOrderDetail billOrderDetail : resultData.get(shopId).getBody().getData().getOrder_list()) {
                ChannelOrderSettlementDTO channelOrderSettleDetailDTO = elmConverterService.convertChannelSettlementDetail(billOrderDetail);
                if (channelOrderSettleDetailDTO.getSettlementDate() == 0) {
                    channelOrderSettleDetailDTO.setSettlementDate(ConverterUtils.secondStringToMillis(bizParam.getDate()));
                }
                BillOrderDetailResult billOrderDetailResult = resultData.get(shopId).getBody().getData();
                String channelShopId = billOrderDetailResult.getShop_id();
                String baiduShopId = billOrderDetailResult.getBaidu_shop_id();
                String channelDate = billOrderDetailResult.getDate();
                // 设置原始数据
                channelOrderSettleDetailDTO.setOriginalOrderSettlementData
                        (GSON.toJson(buildBillOrderDetailStandard(billOrderDetail,channelShopId,baiduShopId,channelDate)));
                channelSettlementDetailPageDto.addToChannelOrderSettlementDtoList(channelOrderSettleDetailDTO);
            }
        }
        response.setChannelOrderSettlementPageDto(channelSettlementDetailPageDto);
        log.info("根据settlementId查询饿了么账单明细 response:{}", response);
        return response;
    }
    private BillOrderDetailStandard buildBillOrderDetailStandard(BillOrderDetail billOrderDetail,String shopId,String baiduShopId,String date){
        return BillOrderDetailStandard.builder()
                .orderIndex(billOrderDetail.getOrder_index())
                .orderFrom(billOrderDetail.getOrder_from())
                .orderId(billOrderDetail.getOrder_id())
                .eleOrderId(billOrderDetail.getEle_order_id())
                .payEntity(billOrderDetail.getPay_entity())
                .orderCreateTime(billOrderDetail.getOrder_create_time())
                .tradeCreateTime(billOrderDetail.getTrade_create_time())
                .amount(billOrderDetail.getAmount())
                .responsibleParty(billOrderDetail.getResponsible_party())
                .orderDetailFee(billOrderDetail.getOrder_detail_fee())
                .tradeId(billOrderDetail.getTrade_id())
                .paymentDate(billOrderDetail.getPayment_date())
                .id(billOrderDetail.getId())
                .baiduShopId(baiduShopId)
                .shopId(shopId)
                .date(date)
                .build();
    }
    @Override
    public ChannelOrderSettlementPageResponse getChannelOrderSettlementList(ChannelOrderSettlementPageRequest request) {
        return null;
    }

    private Long getDateBySettlementId(String settlementId) {
        try {
            String date = settlementId.split("-")[1];
            return Long.valueOf(date);
        }
        catch (Exception e) {
            log.error("settlementId格式错误 settlementId:{}", settlementId);
            return null;
        }
    }

    @Override
    public ChannelBalanceBillPageResponse getBalanceBillList(ChannelBalanceBillPageRequest request) {
        return null;
    }

    @Override
    public ChannelCheckBillResponse getCheckBillList(ChannelCheckBillPageRequest request) {
        return null;
    }

    @Override
    public JDBalanceBillPageResponse getBalanceBill(JDBalanceBillPageRequest request) {
        return null;
    }

    @Override
    public DouyinBalanceBillPageResponse getDouyinBalanceBill(DouyinBalanceBillPageRequest request) {
        return null;
    }
}