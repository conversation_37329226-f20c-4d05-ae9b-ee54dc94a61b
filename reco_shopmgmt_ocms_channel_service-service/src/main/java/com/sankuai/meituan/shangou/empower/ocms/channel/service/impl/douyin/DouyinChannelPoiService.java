package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.alibaba.fastjson.JSONObject;
import com.doudian.open.api.address_create.AddressCreateRequest;
import com.doudian.open.api.address_create.AddressCreateResponse;
import com.doudian.open.api.address_create.param.Address;
import com.doudian.open.api.address_create.param.AddressCreateParam;
import com.doudian.open.api.address_getAreasByProvince.AddressGetAreasByProvinceRequest;
import com.doudian.open.api.address_getAreasByProvince.AddressGetAreasByProvinceResponse;
import com.doudian.open.api.address_getAreasByProvince.data.DataItem;
import com.doudian.open.api.address_getAreasByProvince.data.SubDistrictsItem;
import com.doudian.open.api.address_getAreasByProvince.data.SubDistrictsItem_3;
import com.doudian.open.api.address_getAreasByProvince.data.SubDistrictsItem_4;
import com.doudian.open.api.address_getAreasByProvince.param.AddressGetAreasByProvinceParam;
import com.doudian.open.api.address_getProvince.AddressGetProvinceRequest;
import com.doudian.open.api.address_getProvince.AddressGetProvinceResponse;
import com.doudian.open.api.address_list.AddressListRequest;
import com.doudian.open.api.address_list.AddressListResponse;
import com.doudian.open.api.address_list.data.AddressListItem;
import com.doudian.open.api.address_list.param.AddressListParam;
import com.doudian.open.api.address_update.AddressUpdateRequest;
import com.doudian.open.api.address_update.AddressUpdateResponse;
import com.doudian.open.api.address_update.param.AddressUpdateParam;
import com.doudian.open.api.shop_editStore.ShopEditStoreRequest;
import com.doudian.open.api.shop_editStore.ShopEditStoreResponse;
import com.doudian.open.api.shop_editStore.param.ShopEditStoreParam;
import com.doudian.open.api.shop_getStoreDetail.ShopGetStoreDetailRequest;
import com.doudian.open.api.shop_getStoreDetail.ShopGetStoreDetailResponse;
import com.doudian.open.api.shop_getStoreDetail.param.ShopGetStoreDetailParam;
import com.doudian.open.api.shop_getStoreList.ShopGetStoreListRequest;
import com.doudian.open.api.shop_getStoreList.ShopGetStoreListResponse;
import com.doudian.open.api.shop_getStoreList.data.OpenTime;
import com.doudian.open.api.shop_getStoreList.data.Store;
import com.doudian.open.api.shop_getStoreList.data.StoreDetailListItem;
import com.doudian.open.api.shop_getStoreList.param.ShopGetStoreListParam;
import com.doudian.open.api.shop_storeSuspend.ShopStoreSuspendRequest;
import com.doudian.open.api.shop_storeSuspend.ShopStoreSuspendResponse;
import com.doudian.open.api.shop_storeSuspend.param.ShopStoreSuspendParam;
import com.doudian.open.api.shop_unsuspendStore.ShopUnsuspendStoreRequest;
import com.doudian.open.api.shop_unsuspendStore.ShopUnsuspendStoreResponse;
import com.doudian.open.api.shop_unsuspendStore.param.ShopUnsuspendStoreParam;
import com.doudian.open.api.token.AccessTokenData;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.AccessTokenBuilder;
import com.doudian.open.core.DoudianOpConfig;
import com.doudian.open.core.DoudianOpRequest;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChannelPoiStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.exceptions.TenantBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ChannelConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTenantSysParams;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DouyinCommomEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.DouyinChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.CoordinateTransformUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: reco_shopmgmt_ocms_channel_service
 * @description: 抖音渠道门店管理功能
 * @author: jinyi
 * @create: 2023-12-28 20:31
 **/
@Service("dyChannelPoiService")
@Slf4j
public class DouyinChannelPoiService implements ChannelPoiService {

    @Autowired
    private DouyinChannelCommonService douyinChannelCommonService;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    /**
     * 获取渠道列表
     *
     * @param req 请求参数
     * @return 渠道信息详情列表
     */
    @Override
    public GetPoiInfoResponse batchGetPoiInfo(BaseRequestSimple req) {
        GetPoiInfoResponse resp = new GetPoiInfoResponse();
        long tenantId = req.getTenantId();
        int channelId = req.getChannelId();
        try {
            List<PoiInfo> channelPoiList = getChannelPoiList(req.getTenantId(), req.getChannelId(),null);
            if (CollectionUtils.isEmpty(channelPoiList)) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表为空"));
            }
            resp.setStatus(ResultGenerator.genSuccessResult());
            resp.setPoiInfoList(channelPoiList);
            return resp;
        } catch (TenantBizException e) {
            log.error("DouyinChannelPoiService-batchGetPoiInfo：获取租户绑定渠道门店列表失败 tenantId: {} - channelId: {}", tenantId, channelId, e);
            return resp.setStatus(new ResultStatus(e.getErrorCode(),e.getMessage(),"获取租户绑定渠道门店列表失败"));
        }
    }

    /**
     * 获取渠道的POI列表
     *
     * @param tenantId  租户ID
     * @param channelId 渠道ID
     * @return 渠道的POI列表
     */
    private List<PoiInfo> getChannelPoiList(Long tenantId, Integer channelId,List<Long> poiIdList) {
        List<PoiInfo> poiInfoListInAllApps = new ArrayList<>();
        long sum = 0L,page = 0L ,total;
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOList)) {
            log.warn("DouyinChannelPoiService-getChannelPoiList：获取租户绑定渠道应用数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            throw new TenantBizException(StatusCodeEnum.SYS_ERROR,"该租户未开通抖音渠道");
        }
        DouyinTenantSysParams sysParams = douyinChannelCommonService.getTenantSysParamsByTenantId(tenantId);
        String appKey = sysParams.getAppKey();
        do {
            ShopGetStoreListRequest request = new ShopGetStoreListRequest();
            ShopGetStoreListParam param = request.getParam();
            if (!CollectionUtils.isEmpty(poiIdList)){
                param.setStoreIdList(poiIdList);
            }
            param.setPage(page);
            param.setPageSize(1000L);
            AccessToken accessToken = getAccessToken(request, tenantId);
            ShopGetStoreListResponse response = request.execute(accessToken);
            log.info("DouyinChannelPoiService-getChannelPoiList：查询门店列表 response={}", JacksonUtils.toJson(response));
            if (!response.isSuccess()) {
                log.error("DouyinChannelPoiService-getChannelPoiList：查询门店列表失败 tenantId: {} - channelId: {} - errorMessage:{}", tenantId, channelId, response.getMsg());
                throw new TenantBizException(Integer.valueOf(response.getCode()),response.getSubMsg(), null);
            }
            total = response.getData().getTotal();
            List<StoreDetailListItem> storeDetailList = response.getData().getStoreDetailList();
            sum += storeDetailList.size();
            page = page + 1;
            List<PoiInfo> subPoiInfoList = toPoiInfo(storeDetailList, appKey);
            poiInfoListInAllApps.addAll(subPoiInfoList);
        } while (total > sum);
        if (CollectionUtils.isEmpty(poiInfoListInAllApps)) {
            log.warn("DouyinChannelPoiService-getChannelPoiList：查询门店列表为空 tenantId: {} - channelId: {}", tenantId, channelId);
            return new ArrayList<>();
        }
        return poiInfoListInAllApps;
    }

    /**
     * 获取渠道门店id，channelPoiCode 列表与appId对应关系
     *
     * @param req 请求对象
     * @return 返回获取到的渠道门店id和appId对应关系的响应对象
     */
    @Override
    public GetPoiIdsResponse batchGetPoiIds(BaseRequestSimple req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        long tenantId = req.getTenantId();
        int channelId = req.getChannelId();
        try {
            List<PoiInfo> channelPoiList = getChannelPoiList(tenantId, channelId,null);
            if (CollectionUtils.isEmpty(channelPoiList)) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取渠道门店id列表为空"));
            }
            resp.setStoreIds(Fun.map(channelPoiList, PoiInfo::getAppPoiCode));
            return resp;
        } catch (TenantBizException e) {
            log.error("DouyinChannelPoiService-batchGetPoiIds：获取渠道门店id列表失败 tenantId: {} - channelId: {}", tenantId, channelId, e);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取渠道门店id列表失败"));
        }
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIdsBySysParam(BatchGetPoiIdsRequest req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse();
        resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "抖音渠道暂不支持此功能"));
        return resp;
    }

    /**
     * 获取渠道门店详情
     *
     * @param req
     * @return
     */
    @Override
    public GetPoiInfoResponse batchGetPoiDetails(BatchGetPoiDetailsRequest req) {
        GetPoiInfoResponse resp = new GetPoiInfoResponse();
        long tenantId = req.getTenantId();
        int channelId = req.getChannelId();
        try {
            List<String> channelPoiCodes = req.getStoreIds();
            if (CollectionUtils.isEmpty(channelPoiCodes)) {
                log.info("DouyinChannelPoiService-batchGetPoiDetails:查询渠道门店详情未传入渠道门店id,{}",req);
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询渠道门店详情未传入渠道门店id"));
            }
            List<Long> channelPoiCodeList = channelPoiCodes.stream().map(Long::valueOf).collect(Collectors.toList());
            List<PoiInfo> channelPoiList = getChannelPoiList(tenantId, channelId,channelPoiCodeList);
            if (CollectionUtils.isEmpty(channelPoiList)) {
                log.warn("DouyinChannelPoiService-batchGetPoiDetails:获取渠道门店列表为空 tenantId: {} - channelId: {} - appPoiCodeList: {}", tenantId, channelId, channelPoiCodes);
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取渠道门店列表为空"));
            }
            resp.setStatus(ResultGenerator.genSuccessResult());
            resp.setPoiInfoList(channelPoiList);
            return resp;
        } catch (TenantBizException e) {
            log.error("DouyinChannelPoiService-batchGetPoiDetails:获取渠道门店详情失败 tenantId: {} - channelId: {}", tenantId, channelId, e);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取渠道门店详情失败"));
        }
    }

    private List<PoiInfo> toPoiInfo(List<StoreDetailListItem> storeDetailList, String appKey) {
        List<PoiInfo> poiInfoList = new ArrayList<>();
        for (StoreDetailListItem storeDetailListItem : storeDetailList) {
            PoiInfo poiInfo = new PoiInfo();
            Store store = storeDetailListItem.getStore();
            poiInfo.setAddress(store.getAddress());
            poiInfo.setAppPoiCode(String.valueOf(store.getStoreId()));
            poiInfo.setProvince(store.getProvince());
            poiInfo.setCity(store.getCity());
            poiInfo.setCounty(store.getDistrict());
            poiInfo.setName(store.getName());
            OpenTime openTime = store.getOpenTime();
            Map<Long, String> dayMap = openTime.getDayMap();
            StringBuilder shippingTime = getShippingTime(dayMap);
            poiInfo.setShippingTime(shippingTime.toString());
            poiInfo.setChannelPoiCode(String.valueOf(store.getStoreId()));
            // 渠道返回经纬度有 "null"
            if (StringUtils.isNotBlank(store.getLatitude()) && StringUtils.isNotBlank(store.getLongitude())){
                double longitude = Double.parseDouble(store.getLongitude()) ;
                double latitude = Double.parseDouble(store.getLatitude());
                CoordinateTransformUtil.CoordinatePoint coordinatePoint = CoordinateTransformUtil.bd09ToGcj02(longitude, latitude);
                poiInfo.setLatitude(coordinatePoint.getLatitude());
                poiInfo.setLongitude(coordinatePoint.getLongitude());
            }
            poiInfo.setOpenLevel(store.getState().longValue() == DouyinCommomEnum.OPEN.getCode() ? ChannelPoiStatusEnum.OPEN.getStatus() : ChannelPoiStatusEnum.CLOSED.getStatus());
            poiInfo.setIsOnline(1);
            poiInfo.setAppKey(appKey);
            poiInfoList.add(poiInfo);
        }
        return poiInfoList;
    }

    private PoiInfo toPoiInfo(com.doudian.open.api.shop_getStoreDetail.data.Store store) {
        PoiInfo poiInfo = new PoiInfo();
        poiInfo.setAddress(store.getAddress());
        poiInfo.setAppPoiCode(String.valueOf(store.getStoreId()));
        poiInfo.setProvince(store.getProvince());
        poiInfo.setCity(store.getCity());
        poiInfo.setCounty(store.getDistrict());
        poiInfo.setName(store.getName());
        com.doudian.open.api.shop_getStoreDetail.data.OpenTime openTime = store.getOpenTime();
        Map<Long, String> dayMap = openTime.getDayMap();
        StringBuilder shippingTime = getShippingTime(dayMap);
        poiInfo.setShippingTime(shippingTime.toString());
        poiInfo.setChannelPoiCode(String.valueOf(store.getStoreId()));
        if (StringUtils.isNotBlank(store.getLatitude()) && StringUtils.isNotBlank(store.getLongitude())){
            double longitude = Double.parseDouble(store.getLongitude()) ;
            double latitude = Double.parseDouble(store.getLatitude());
            CoordinateTransformUtil.CoordinatePoint coordinatePoint = CoordinateTransformUtil.bd09ToGcj02(longitude, latitude);
            poiInfo.setLatitude(coordinatePoint.getLatitude());
            poiInfo.setLongitude(coordinatePoint.getLongitude());
        }
        poiInfo.setOpenLevel(store.getState().longValue() == DouyinCommomEnum.OPEN.getCode() ? ChannelPoiStatusEnum.OPEN.getStatus() : ChannelPoiStatusEnum.CLOSED.getStatus());
        return poiInfo;
    }

    private StringBuilder getShippingTime(Map<Long, String> dayMap) {
        StringBuilder shippingTime = new StringBuilder();
        for (long key : ChannelConstant.DY_OPEN_TIME_KEY) {
            String str = dayMap.get(key);
            if (StringUtils.isBlank(str)){
                shippingTime.append(ChannelConstant.DY_OPEN_DAY_SPLIT);
                continue;
            }
            str = str.replace(ChannelConstant.DY_OPEN_TIME_SPLIT, ChannelConstant.QNH_OPEN_TIME_SPLIT);
            shippingTime.append(str).append(ChannelConstant.DY_OPEN_DAY_SPLIT);
        }
        return shippingTime;
    }

    @Override
    public GetPoiIdsResponse getPoiInnerId(BatchGetPoiDetailsRequest request) {
        GetPoiIdsResponse response = new GetPoiIdsResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "抖音渠道暂不支持此功能"));
        return response;
    }

    @Override
    public ResultStatus poiOpen(ChannelPoiIdRequest channelPoiIdRequest) {
        ShopUnsuspendStoreRequest request = new ShopUnsuspendStoreRequest();
        ShopUnsuspendStoreParam param = request.getParam();
        param.setStoreId(Long.valueOf(channelPoiIdRequest.getChannelPoiCode()));
        param.setReason("牵牛花测操作门店营业");
        log.info("DouyinChannelPoiService.poiOpen-ShopUnsuspendStoreRequest:{}",request);
        ShopUnsuspendStoreResponse response = request.execute(getAccessToken(request, channelPoiIdRequest.getTenantId()));
        log.info("DouyinChannelPoiService.poiOpen-ShopUnsuspendStoreResponse:{}",response);
        if (!response.isSuccess()){
            return ResultGenerator.genResult(ResultCode.FAIL, response.getSubMsg());
        }
        return ResultGenerator.genResult(ResultCode.SUCCESS, "营业成功！");
    }

    @Override
    public ResultStatus poiClose(ChannelPoiIdRequest channelPoiIdRequest) {
        ShopStoreSuspendRequest request = new ShopStoreSuspendRequest();
        ShopStoreSuspendParam param = request.getParam();
        param.setStoreId(Long.valueOf(channelPoiIdRequest.getChannelPoiCode()));
        param.setReason("牵牛花测操作门店置休");
        log.info("DouyinChannelPoiService.poiClose-ShopStoreSuspendRequest:{}",request);
        ShopStoreSuspendResponse response = request.execute(getAccessToken(request, channelPoiIdRequest.getTenantId()));
        log.info("DouyinChannelPoiService.poiClose-ShopStoreSuspendResponse:{}",response);
        if (!response.isSuccess()){
            return ResultGenerator.genResult(ResultCode.FAIL, response.getSubMsg());
        }
        return ResultGenerator.genResult(ResultCode.SUCCESS, "置休成功！");
    }

    @Override
    public ResultStatus promotionInfoUpdate(PoiPromotionInfoUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "抖音渠道暂不支持此功能");
    }

    @Override
    public ResultStatus shippingTimeUpdate(PoiShippingTimeUpdateRequest poiShippingTimeUpdateRequest) {
        ShopEditStoreRequest request = new ShopEditStoreRequest();
        ShopEditStoreParam param = request.getParam();
        param.setStoreId(Long.valueOf(poiShippingTimeUpdateRequest.getChannelPoiCode()));
        com.doudian.open.api.shop_editStore.param.OpenTime openTime = new com.doudian.open.api.shop_editStore.param.OpenTime();
        openTime.setDayMap(getDyShoppingTimeMap(poiShippingTimeUpdateRequest));
        param.setOpenTime(openTime);
        log.info("DouyinChannelPoiService.shippingTimeUpdate-ShopEditStoreRequest:{}",request);
        ShopEditStoreResponse response = request.execute(getAccessToken(request, poiShippingTimeUpdateRequest.getTenantId()));
        log.info("DouyinChannelPoiService.shippingTimeUpdate-ShopEditStoreResponse:{}",response);
        if (!response.isSuccess()){
            return ResultGenerator.genResult(ResultCode.FAIL, response.getSubMsg());
        }
        return ResultGenerator.genResult(ResultCode.SUCCESS, "营业时间设置成功！");
    }

    private Map<Long, String> getDyShoppingTimeMap(PoiShippingTimeUpdateRequest request) {
        Map<Long, String> map = new HashMap<>();

        //没有单独设置星期几
        Set<Integer> shippingDays = request.getShippingDays();
        String timeStr = getTime(request.getShippingTimes());
        if (CollectionUtils.isEmpty(shippingDays)){
            for (long day : ChannelConstant.DY_OPEN_TIME_KEY) {
                map.put(day, timeStr);
            }
            return map;
        }

        //有设置具体的星期几
        for (Integer shippingDay : shippingDays) {
            //抖音的key是比牵牛花测多 1
            //牵牛花测 周一为0 抖音测为1 周日为6 抖音测为7
            map.put(shippingDay.longValue()+1, timeStr);
        }
        return map;

    }

    private String getTime(List<ShippingTime> shippingTimes) {
        StringBuilder timeStr = new StringBuilder();
        if (CollectionUtils.isEmpty(shippingTimes)){
            return timeStr.toString();
        }
        for (ShippingTime shippingTime : shippingTimes) {
            timeStr.append(shippingTime.getStartTime()).
                    append(ChannelConstant.DY_OPEN_TIME_UPDATE_SPLIT).
                    append(shippingTime.getEndTime()).
                    append(ChannelConstant.DY_OPEN_TIME_SPLIT)
            ;
        }
        //去掉最后一个分隔符
        if (timeStr.length() > 0) {
            timeStr.deleteCharAt(timeStr.length() - 1);
        }
        return timeStr.toString();
    }

    @Override
    public ResultStatus prebookStatusOpen(ChannelPoiIdRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "抖音渠道暂不支持此功能");
    }

    @Override
    public ResultStatus prebookStatusClose(ChannelPoiIdRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "抖音渠道暂不支持此功能");
    }

    @Override
    public ResultStatus updatePrebookDays(PoiPrebookDaysUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "抖音渠道暂不支持此功能");
    }

    @Override
    public GetPoiPromotionInfoResponse getPoiPromotionInfo(ChannelPoiIdRequest request) {
        GetPoiPromotionInfoResponse response = new GetPoiPromotionInfoResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "抖音渠道暂不支持此功能"));
        return response;
    }

    @Override
    public ChannelPoiStatusResponse getPoiStatus(ChannelPoiIdRequest channelPoiIdRequest) {
        ChannelPoiStatusResponse response = new ChannelPoiStatusResponse();
        PoiInfo poiInfo = getSingleShopInfo(channelPoiIdRequest.getTenantId(), channelPoiIdRequest.getChannelId(), channelPoiIdRequest.getChannelPoiCode());
        response.setStatus(ResultGenerator.genSuccessResult());
        response.setPoiStatus(poiInfo.getOpenLevel());
        return response;
    }

    private PoiInfo getSingleShopInfo(long tenantId, int channelId, String channelPoiCode) {
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOList)) {
            log.warn("DouyinChannelPoiService.getSingleShopInfo：获取单个门店信息详情失败 tenantId: {} - channelId: {}", tenantId, channelId);
            throw new TenantBizException(StatusCodeEnum.SYS_ERROR, "该租户未开通抖音渠道");
        }
        ShopGetStoreDetailRequest request = new ShopGetStoreDetailRequest();
        ShopGetStoreDetailParam param = request.getParam();
        param.setStoreId(Long.valueOf(channelPoiCode));
        ShopGetStoreDetailResponse response = request.execute(getAccessToken(request, tenantId));
        if (!response.isSuccess()) {
            log.error("DouyinChannelPoiService.getSingleShopInfo：获取单个门店信息详情失败 tenantId: {} - channelId: {} - channelPoiCode: {} - response :{}",
                    tenantId, channelId, channelPoiCode, response);
            throw new TenantBizException(StatusCodeEnum.SYS_ERROR, "获取单个门店信息详情失败");
        }
        com.doudian.open.api.shop_getStoreDetail.data.Store store = response.getData().getStoreDetail().getStore();
        return toPoiInfo(store);
    }

    @Override
    public ResultStatus poiAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "抖音渠道暂不支持此功能");
    }

    @Override
    public ResultStatus poiDeAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "抖音渠道暂不支持此功能");
    }

    @Override
    public ResultStatus updatePoiAddress(PoiAddressUpdateRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "抖音渠道暂不支持此功能");
    }

    @Override
    public UpdateSaleafterAddressMessageResponse updateSaleafterAddressMessage(UpdateSaleafterAddressMessageRequest request) {
        UpdateSaleafterAddressMessageResponse response = new UpdateSaleafterAddressMessageResponse();
        long addressId = 0L;
        try {
            addressId = handleAddressUpdate(request);
        } catch (TenantBizException e) {
            log.error("DouyinChannelPoiService-updateSaleafterAddressMessage:修改售后地址失败！req:{}",request,e);
            response.setStatus(new ResultStatus(e.getErrorCode(), e.getMessage(), null));
            return response;
        }
        response.setStatus(new ResultStatus(ResultCode.SUCCESS.getCode(), "成功", null));
        response.setAddressId(addressId);
        return response;
    }
    private Long handleAddressUpdate(UpdateSaleafterAddressMessageRequest request) throws TenantBizException {
        List<AddressListItem> addressList = getAddressList(request.getTenantId(), request.getChannelPoiCode());
        boolean isNewAddress = isNewAddress(request, addressList);
        return isNewAddress ? insertSaleafterAddress(request, request.tenantId) : updateSaleafterAddress(request, request.tenantId);
    }

    private boolean isNewAddress(UpdateSaleafterAddressMessageRequest request, List<AddressListItem> addressList) {
        if (CollectionUtils.isEmpty(addressList) || request.getAddressId() == 0) {
            return true;
        }
        //列表里是否包含地址id，包含返回false，不包含返回true
        return addressList.stream().noneMatch(item -> request.getAddressId() == item.getAddressId());
    }


    @Override
    public ResultStatus poiHotlineUpdate(ChannelPoiHotlineRequest channelPoiHotlineRequest) {
        ShopEditStoreRequest request = new ShopEditStoreRequest();
        ShopEditStoreParam param = request.getParam();
        param.setStoreId(Long.valueOf(channelPoiHotlineRequest.getChannelPoiCode()));
        param.setContact(channelPoiHotlineRequest.getHotline());
        log.info("DouyinChannelPoiService.poiHotlineUpdate-ShopEditStoreRequest:{}",request);
        ShopEditStoreResponse response = request.execute(getAccessToken(request, channelPoiHotlineRequest.getTenantId()));
        log.info("DouyinChannelPoiService.poiHotlineUpdate-response:{}",response);
        if (!response.isSuccess()){
            return ResultGenerator.genResult(ResultCode.FAIL, response.getSubMsg());
        }
        return ResultGenerator.genResult(ResultCode.SUCCESS, "营业时间设置成功！");

    }

    @Override
    public QueryPoiAuthDetailResponse queryPoiAuthDetailInfo(BaseChannelPoiRequestSimple request) {
        return new QueryPoiAuthDetailResponse().setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能"));
    }

    @Override
    public ResultStatus updatePoiAuthInfo(UpdatePoiAuthInfoRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能");
    }

    private Long updateSaleafterAddress(UpdateSaleafterAddressMessageRequest request, Long tenantId) {
        AddressUpdateRequest updateRequest = new AddressUpdateRequest();
        AddressUpdateParam param = updateRequest.getParam();
        param.setStoreId(Long.valueOf(request.getChannelPoiCode()));
        com.doudian.open.api.address_update.param.Address address = new com.doudian.open.api.address_update.param.Address();
        address.setLinkType(0);
        address.setProvinceId(request.getProvinceId());
        address.setCityId(request.getCityId());
        //港澳台街道id为空时，特殊处理不传参数
        if (request.getStreetId()!=0) {
            address.setStreetId(request.getStreetId());
        }
        address.setTownId(request.getDistrictId());
        address.setDetail(request.getDetail());
        address.setMobile(request.getPhone());
        address.setUserName(request.getReceiverName());
        address.setId(request.getAddressId());
        param.setAddress(address);
        AddressUpdateResponse response = updateRequest.execute(getAccessToken(updateRequest, tenantId));
        log.info( "DouyinChannelPoiService-updateSaleafterAddress:response：{}", JacksonUtils.toJson(response));
        if (!response.isSuccess()) {
            log.info( "DouyinChannelPoiService-updateSaleafterAddress:修改抖音售后地址失败：{},{}", response.getCode(),response.getSubMsg());
            throw new TenantBizException(Integer.valueOf(response.getCode()), response.getSubMsg());
        }
        return request.getAddressId();
    }

    private Long insertSaleafterAddress(UpdateSaleafterAddressMessageRequest request, Long tenantId) {
        AddressCreateRequest addressCreateRequest = new AddressCreateRequest();
        AddressCreateParam param = addressCreateRequest.getParam();
        param.setStoreId(Long.valueOf(request.getChannelPoiCode()));
        Address address = new Address();
        address.setLinkType(0);
        address.setProvinceId(request.getProvinceId());
        address.setCityId(request.getCityId());
        //港澳台街道id为空时，特殊处理不传参数
        if (request.getStreetId()!=0) {
            address.setStreetId(request.getStreetId());
        }
        address.setTownId(request.getDistrictId());
        address.setDetail(request.getDetail());
        address.setMobile(request.getPhone());
        address.setUserName(request.getReceiverName());
        param.setAddress(address);
        AddressCreateResponse response = addressCreateRequest.execute(getAccessToken(addressCreateRequest, tenantId));
        log.info( "DouyinChannelPoiService-insertSaleafterAddress:response：{}", JacksonUtils.toJson(response));
        if (!response.isSuccess()) {
            log.info( "DouyinChannelPoiService - insertSaleafterAddress -新增抖音售后地址失败：{}", JSONObject.toJSONString(response));
            throw new TenantBizException(Integer.valueOf(response.getCode()), response.getSubMsg());
        }
        return response.getData().getAddressId();
    }


    private AccessToken getAccessToken(DoudianOpRequest<?> request, Long tenantId) {
        DouyinTenantSysParams sysParams = douyinChannelCommonService.getTenantSysParamsByTenantId(tenantId);
        DoudianOpConfig config = new DoudianOpConfig();
        config.setAppKey(sysParams.getAppKey());
        config.setAppSecret(sysParams.getSecret());
        request.setConfig(config);
        AccessToken parse = AccessTokenBuilder.parse(sysParams.getAccessToken());
        AccessTokenData data = new AccessTokenData();
        data.setShopId(sysParams.getShopId());
        data.setAccessToken(sysParams.getAccessToken());
        parse.setData(data);
        return parse;
    }

    private List<AddressListItem> getAddressList(Long tenantId, String storeId) {
        AddressListRequest addressListRequest = new AddressListRequest();
        AddressListParam param = addressListRequest.getParam();
        param.setPageSize(50L);
        param.setPageNo(1L);
        param.setOrderBy("desc");
        param.setOrderField("update_time");
        param.setStoreId(Long.valueOf(storeId));
        AccessToken accessToken = getAccessToken(addressListRequest, tenantId);
        AddressListResponse response = addressListRequest.execute(accessToken);
        log.info( "DouyinChannelPoiService - getAddressList -获取抖音地址列表：response:{}",response);
        if (!response.isSuccess()) {
            log.info( "DouyinChannelPoiService - getAddressList -获取抖音地址列表失败：{}", JSONObject.toJSONString(response));
            throw new TenantBizException(Integer.valueOf(response.getCode()), response.getSubMsg());
        }
        return response.getData().getAddressList();
    }

    /**
     * 获取抖音渠道省份列表
     *
     * @param request 请求参数
     * @return 抖音渠道省份列表的响应
     */
    public GetProvinceListResponse getProvinceList(GetProvinceListRequest request) {
        GetProvinceListResponse getProvinceListResponse = new GetProvinceListResponse();
        AddressGetProvinceRequest addressProvinceListRequest = new AddressGetProvinceRequest();
        AccessToken accessToken = getAccessToken(addressProvinceListRequest, request.getTenantId());
        AddressGetProvinceResponse response = addressProvinceListRequest.execute(accessToken);
        log.info( "DouyinChannelPoiService - getProvinceList -获取抖音省级列表：response:{}",response);
        if (!response.isSuccess()) {
            log.info( "DouyinChannelPoiService - getAddressList -获取抖音省级列表失败：req:{},{},{}",request, response.getCode(),response.getSubMsg());
            throw new TenantBizException(Integer.valueOf(response.getCode()), response.getSubMsg());
        }
        List<com.doudian.open.api.address_getProvince.data.DataItem> data = response.getData();
        ArrayList<ProvinceResponse> provinceList = new ArrayList<>();
        for (com.doudian.open.api.address_getProvince.data.DataItem datum : data) {
            provinceList.add(new ProvinceResponse(datum.getProvince(), datum.getProvinceId()));
        }
        getProvinceListResponse.setProvinceList(provinceList);
        getProvinceListResponse.setStatus(new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null));
        return getProvinceListResponse;
    }

    /**
     * 获取抖音省份下级地区列表
     *
     * @param request 请求参数
     * @return 抖音省份下级地区列表的响应
     */
    public GetProvinceDistrictsListResponse getProvinceDistrictsList(GetProvinceListRequest request) {
        GetProvinceDistrictsListResponse districtsListResponse = new GetProvinceDistrictsListResponse();
        AddressGetAreasByProvinceRequest areasByProvinceRequest = new AddressGetAreasByProvinceRequest();
        AddressGetAreasByProvinceParam param = areasByProvinceRequest.getParam();
        param.setProvinceId(request.getProvinceId());
        AddressGetAreasByProvinceResponse response = areasByProvinceRequest.execute(getAccessToken(areasByProvinceRequest, request.getTenantId()));
        log.info( "DouyinChannelPoiService - getProvinceDistrictsList -获取抖音省下四级区域列表：response:{}",response);
        if (!response.isSuccess()) {
            log.info( "DouyinChannelPoiService - getProvinceDistrictsList -获取抖音省下四级区域列表失败：req:{},{},{}",request, response.getCode(),response.getSubMsg());
            throw new TenantBizException(Integer.valueOf(response.getCode()), response.getSubMsg());
        }
        if (CollectionUtils.isEmpty(response.getData())){
            districtsListResponse.setData(new ArrayList<>());
            districtsListResponse.setStatus(new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null));
            return districtsListResponse;
        }
        DataItem dataItem = response.getData().get(0);
        districtsListResponse.setData(getSubDistricts(dataItem.getSubDistricts()));
        districtsListResponse.setStatus(new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null));
        return districtsListResponse;
    }

    private List<ProvinceDistrictsCityDTO> getSubDistricts(List<SubDistrictsItem_3> subDistricts) {
        return Optional.ofNullable(subDistricts)
                .orElseGet(Collections::emptyList)
                .stream()
                .map(this::mapToProvinceDistrictsCityDTO)
                .collect(Collectors.toList());
    }

    private ProvinceDistrictsCityDTO mapToProvinceDistrictsCityDTO(SubDistrictsItem_3 subDistrict) {
        ProvinceDistrictsCityDTO provinceDistrictsDTO1 = new ProvinceDistrictsCityDTO();
        provinceDistrictsDTO1.setId(subDistrict.getCode());
        provinceDistrictsDTO1.setName(subDistrict.getName());
        provinceDistrictsDTO1.setLevel(subDistrict.getLevel());
        provinceDistrictsDTO1.setSubDistricts(getSubDistrictsItem4s(subDistrict.getSubDistricts()));
        return provinceDistrictsDTO1;
    }

    private List<ProvinceDistrictsDistrictsDTO> getSubDistrictsItem4s(List<SubDistrictsItem_4> subDistrictsItem4s) {
        return Optional.ofNullable(subDistrictsItem4s)
                .orElseGet(Collections::emptyList)
                .stream()
                .map(this::mapToProvinceDistrictsDistrictsDTO)
                .collect(Collectors.toList());
    }

    private ProvinceDistrictsDistrictsDTO mapToProvinceDistrictsDistrictsDTO(SubDistrictsItem_4 subDistrictsItem4) {
        ProvinceDistrictsDistrictsDTO provinceDistrictsDTO2 = new ProvinceDistrictsDistrictsDTO();
        provinceDistrictsDTO2.setId(subDistrictsItem4.getCode());
        provinceDistrictsDTO2.setName(subDistrictsItem4.getName());
        provinceDistrictsDTO2.setLevel(subDistrictsItem4.getLevel());
        provinceDistrictsDTO2.setSubDistricts(getSubDistrictsItem(subDistrictsItem4.getSubDistricts()));
        return provinceDistrictsDTO2;
    }

    private List<ProvinceDistrictsStreetDTO> getSubDistrictsItem(List<SubDistrictsItem> districts) {
        return Optional.ofNullable(districts)
                .orElseGet(Collections::emptyList)
                .stream()
                .map(this::mapToProvinceDistrictsStreetDTO)
                .collect(Collectors.toList());
    }

    private ProvinceDistrictsStreetDTO mapToProvinceDistrictsStreetDTO(SubDistrictsItem district) {
        ProvinceDistrictsStreetDTO provinceDistrict3 = new ProvinceDistrictsStreetDTO();
        provinceDistrict3.setId(district.getCode());
        provinceDistrict3.setName(district.getName());
        provinceDistrict3.setLevel(district.getLevel());
        return provinceDistrict3;
    }


}
