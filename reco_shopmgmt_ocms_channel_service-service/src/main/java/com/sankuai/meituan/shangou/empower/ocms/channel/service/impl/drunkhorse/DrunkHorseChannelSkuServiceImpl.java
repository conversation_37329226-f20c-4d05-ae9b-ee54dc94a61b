package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSkuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelSkuServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2022/01/03
 */
@Service("mtDrunkHorseChannelSkuService")
public class DrunkHorseChannelSkuServiceImpl extends MtChannelSkuServiceImpl implements ChannelSkuService {
    @Value("${mt_drunkhorse.url.base}" + "${mt.url.skulist}")
    private String skuList;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.skuDetail}")
    private String skuDetail;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.frontCatList}")
    private String frontCatList;

    @Autowired
    @Qualifier("mtDrunkHorseChannelGateService")
    private MtChannelGateService mtChannelGateService;

    // 应该没必要单起线程池了
//    @Resource(name = "mtPictureUploadThreadPool")
//    private ExecutorService mtPictureUploadThreadPool;


    @Override
    public String getSkuList() {
        return skuList;
    }

    @Override
    public String getSkuDetail() {
        return skuDetail;
    }

    @Override
    public String getFrontCatList() {
        return frontCatList;
    }

    @Override
    public MtChannelGateService getMtChannelGateService() {
        return mtChannelGateService;
    }
}
