package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;

import com.google.gson.Gson;
import com.meituan.shangou.saas.common.enums.ChannelAfterSaleEventEnum;
import com.meituan.shangou.saas.common.enums.ChannelOrderEventType;
import com.meituan.shangou.saas.common.enums.OcmsRefundSponsorEnum;
import com.meituan.shangou.saas.common.enums.ReturnGoodsStatusEnum;
import com.meituan.shangou.saas.dto.model.PartRefundProductInfo;
import com.meituan.shangou.saas.mq.ChannelAfterSaleEvent;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ElmOrderReverseNotify;

import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;

import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ElmCallCenterAppealTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ElmOperatorRoleEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ElmRefundStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ElmReturnGoodStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.NewSupplyChannelElemOrderRefundMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.NewSupplyChannelOrderRefundMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.PoiApiChannelPoiThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetOrderAfsApplyListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetOrderAfsApplyListResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderAfsApplyDTO;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ShopCardFee;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.EnumUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.ServiceTypeEnum;


import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.ChannelPoiBaseInfoDTO;
import javafx.util.Pair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.*;

@Service
public class ElmRefundService {

    @Resource
    private CommonLogger log;

    @Autowired
    private NewSupplyChannelOrderRefundMessageProducer newSupplychannelOrderRefundMessageProducer;

    @Autowired
    private NewSupplyChannelElemOrderRefundMessageProducer newSupplyChannelElemOrderRefundMessageProducer;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private ElmChannelOrderServiceImpl elmChannelOrderService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private PoiApiChannelPoiThriftServiceProxy poiApiChannelPoiThriftServiceProxy;


    // (oldRefundStatus,newRefundStatus) -> eventType
    private static Map<Pair<ElmRefundStatusEnum, ElmRefundStatusEnum>, Pair<ChannelAfterSaleEventEnum, String>> REFUND_ONLY_EVENT_MAPPING = new HashMap<>();

    // 退货退款事件映射情况1
    // ((oldRefundStatus,oldReturnGoodStatus),(newRefundStatus,newReturnGoodStatus)) -> eventType
    private static Map<Pair<Pair<ElmRefundStatusEnum, ElmReturnGoodStatusEnum>, Pair<ElmRefundStatusEnum, ElmReturnGoodStatusEnum>>
            , Pair<ChannelAfterSaleEventEnum, String>> RETURN_EVENT_MAPPING_1 = new HashMap<>();

    // 退货退款事件映射情况2 (主要是二审驳回后申诉)
    // ((oldRefundStatus,oldReturnGoodStatus),(newRefundStatus)) -> eventType
    private static Map<Pair<Pair<ElmRefundStatusEnum, ElmReturnGoodStatusEnum>, ElmRefundStatusEnum>,
            Pair<ChannelAfterSaleEventEnum, String>> RETURN_EVENT_MAPPING_2 = new HashMap<>();

    // 退货退款事件映射情况3(主要是二审申诉后的情况)
    // (oldRefundStatus,newRefundStatus) -> eventType
    private static Map<Pair<ElmRefundStatusEnum, ElmRefundStatusEnum>,
            Pair<ChannelAfterSaleEventEnum, String>> RETURN_EVENT_MAPPING_3 = new HashMap<>();

    // 退货退款事件映射情况4(主要是兜底的情况)
    // (newRefundStatus,newReturnGoodStatus) -> eventType
    private static Map<Pair<ElmRefundStatusEnum, ElmReturnGoodStatusEnum>,
            Pair<ChannelAfterSaleEventEnum, String>> RETURN_EVENT_MAPPING_4 = new HashMap<>();

    // 退货退款事件映射情况5(主要是兜底的情况，需要通过【操作人】确认)
    // (newRefundStatus,newReturnGoodStatus) -> eventType
    private static Map<Pair<Pair<ElmRefundStatusEnum, ElmReturnGoodStatusEnum>, ElmOperatorRoleEnum>,
            Pair<ChannelAfterSaleEventEnum, String>> RETURN_EVENT_MAPPING_5 = new HashMap<>();

    // 客服介入退款状态映射
    private static Map<Pair<ElmRefundStatusEnum, ElmRefundStatusEnum>,
            ElmCallCenterAppealTypeEnum> REFUND_CALLCENTER_TYPE_MAPPING = new HashMap<>();

    // 客服介入退货状态映射
    private static Map<Pair<Pair<ElmRefundStatusEnum, ElmReturnGoodStatusEnum>, Pair<ElmRefundStatusEnum, ElmReturnGoodStatusEnum>>,
            ElmCallCenterAppealTypeEnum> REFUNDGOOD_CALLCENTER_TYPE_MAPPING = new HashMap<>();

    static {
        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.INIT, ElmRefundStatusEnum.APPLY),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY, "仅退款-用户申请"));

        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.INIT, ElmRefundStatusEnum.REFUND_SUCCESS),
                new Pair<>(ChannelAfterSaleEventEnum.AGREE, "仅退款-商家/系统/客服等直接退"));

        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.APPLY, ElmRefundStatusEnum.CLOSED),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "仅退款-用户撤销"));

        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.CUSTOMER_APPEAL, ElmRefundStatusEnum.CLOSED),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "仅退款-用户申诉后撤销"));
        // 仅退款-商家拒绝后撤销(好像没这种情况)
        // REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.REJECT, ElmRefundStatusEnum.CLOSED), ChannelAfterSaleEventEnum.APPLY_CANCELED);
        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.REJECT, ElmRefundStatusEnum.REFUND_FAILED),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "仅退款-未发起仲裁撤销"));
        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.REJECT, ElmRefundStatusEnum.CLOSED),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "仅退款-拒绝后送达"));
        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.REJECT, ElmRefundStatusEnum.CUSTOMER_APPEAL),
                new Pair<>(ChannelAfterSaleEventEnum.APPEAL_APPLY, "仅退款-发起仲裁"));
        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.APPLY, ElmRefundStatusEnum.REJECT),
                new Pair<>(ChannelAfterSaleEventEnum.REJECT, "仅退款-商家拒绝"));
        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.APPLY, ElmRefundStatusEnum.REFUND_SUCCESS),
                new Pair<>(ChannelAfterSaleEventEnum.AGREE, "仅退款-商家同意"));
        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.CUSTOMER_APPEAL, ElmRefundStatusEnum.REFUND_SUCCESS),
                new Pair<>(ChannelAfterSaleEventEnum.AGREE, "仅退款-申诉后同意"));
        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.CUSTOMER_APPEAL, ElmRefundStatusEnum.REFUND_FAILED),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "仅退款-申诉后驳回"));
        REFUND_ONLY_EVENT_MAPPING.put(new Pair<>(ElmRefundStatusEnum.CUSTOMER_APPEAL, ElmRefundStatusEnum.CLOSED),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "仅退款-仲裁中送达"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.INIT, ElmReturnGoodStatusEnum.RETURN_INVALID),
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_APPLY)),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY, "退货退款-发起"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_APPLY),
                        new Pair<>(ElmRefundStatusEnum.CLOSED, ElmReturnGoodStatusEnum.RETURN_CLOSED)),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "退货退款-一审前撤销"));


        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_APPLY),
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_FIRST_AGREE)),
                new Pair<>(ChannelAfterSaleEventEnum.AGREE, "退货退款-一审同意"));

        // 这种情况未验证
        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_APPLY),
                        new Pair<>(ElmRefundStatusEnum.REFUND_SUCCESS, ElmReturnGoodStatusEnum.RETURN_SECOND_AGREE)),
                new Pair<>(ChannelAfterSaleEventEnum.REFUND_SUCCESS, "退货退款-商家直接同意退款"));

        // 这种情况未验证
        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_APPLY),
                        new Pair<>(ElmRefundStatusEnum.REJECT, ElmReturnGoodStatusEnum.RETURN_SECOND_REJECT)),
                new Pair<>(ChannelAfterSaleEventEnum.REJECT_RETURN, "退货退款-商家直接不同意退款"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_APPLY),
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_TENANT_FIRST_REJECT)),
                new Pair<>(ChannelAfterSaleEventEnum.REJECT, "退货退款-一审驳回"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_TENANT_FIRST_REJECT),
                        new Pair<>(ElmRefundStatusEnum.CLOSED, ElmReturnGoodStatusEnum.RETURN_CLOSED)),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "退货退款-一审驳回后撤销"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_TENANT_FIRST_REJECT),
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_CUSTOMER_APPEAL)),
                new Pair<>(ChannelAfterSaleEventEnum.APPEAL_APPLY, "退货退款-一审驳回后申诉"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_CUSTOMER_APPEAL),
                        new Pair<>(ElmRefundStatusEnum.CLOSED, ElmReturnGoodStatusEnum.RETURN_CLOSED)),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "退货退款-一审仲裁后撤销或被申诉驳回"));


        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_CUSTOMER_APPEAL),
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_FIRST_AGREE)),
                new Pair<>(ChannelAfterSaleEventEnum.AGREE, "退货退款-一审申诉同意"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_FIRST_AGREE),
                        new Pair<>(ElmRefundStatusEnum.CLOSED, ElmReturnGoodStatusEnum.RETURN_CLOSED)),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "退货退款-一审通过后用户撤回/超时未送出"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_FIRST_AGREE),
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_GOOD_SEND_OUT)),
                new Pair<>(ChannelAfterSaleEventEnum.CUSTOMER_SENT_BACK_GOODS, "退货退款-用户寄回货/商家取货"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_FIRST_AGREE),
                        new Pair<>(ElmRefundStatusEnum.REFUND_SUCCESS, ElmReturnGoodStatusEnum.RETURN_SECOND_AGREE)),
                new Pair<>(ChannelAfterSaleEventEnum.REFUND_SUCCESS, "退货退款-用户发货前直接二审同意"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_FIRST_AGREE),
                        new Pair<>(ElmRefundStatusEnum.REJECT, ElmReturnGoodStatusEnum.RETURN_SECOND_REJECT)),
                new Pair<>(ChannelAfterSaleEventEnum.REJECT_RETURN, "退货退款-用户发货前商家二审驳回"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_GOOD_SEND_OUT),
                        new Pair<>(ElmRefundStatusEnum.REFUND_SUCCESS, ElmReturnGoodStatusEnum.RETURN_SECOND_AGREE)),
                new Pair<>(ChannelAfterSaleEventEnum.REFUND_SUCCESS, "退货退款-二审同意"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_GOOD_SEND_OUT),
                        new Pair<>(ElmRefundStatusEnum.REJECT, ElmReturnGoodStatusEnum.RETURN_SECOND_REJECT)),
                new Pair<>(ChannelAfterSaleEventEnum.REJECT_RETURN, "退货退款-用户发货后二审驳回"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_GOOD_SEND_OUT),
                        new Pair<>(ElmRefundStatusEnum.CLOSED, ElmReturnGoodStatusEnum.RETURN_CLOSED)),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "退货退款-发货后撤回"));

        RETURN_EVENT_MAPPING_1.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.CUSTOMER_APPEAL, ElmReturnGoodStatusEnum.RETURN_SECOND_REJECT),
                        new Pair<>(ElmRefundStatusEnum.CLOSED, ElmReturnGoodStatusEnum.RETURN_CLOSED)),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "退货退款-二审申诉后撤回"));

        RETURN_EVENT_MAPPING_2.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.REJECT, ElmReturnGoodStatusEnum.RETURN_SECOND_REJECT), ElmRefundStatusEnum.CUSTOMER_APPEAL),
                new Pair<>(ChannelAfterSaleEventEnum.APPEAL_APPLY, "退货退款-二审驳回后申诉"));

        RETURN_EVENT_MAPPING_2.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.REJECT, ElmReturnGoodStatusEnum.RETURN_SECOND_REJECT), ElmRefundStatusEnum.REFUND_FAILED),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "退货退款-二审驳回超时未发起申诉"));

        RETURN_EVENT_MAPPING_2.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.REJECT, ElmReturnGoodStatusEnum.RETURN_SECOND_REJECT), ElmRefundStatusEnum.CLOSED),
                new Pair<>(ChannelAfterSaleEventEnum.APPLY_CANCELED, "退货退款-二审驳回后用户撤销"));

        RETURN_EVENT_MAPPING_3.put(new Pair<>(ElmRefundStatusEnum.CUSTOMER_APPEAL, ElmRefundStatusEnum.REFUND_SUCCESS),
                new Pair<>(ChannelAfterSaleEventEnum.REFUND_SUCCESS, "退货退款-二审申诉同意"));

        RETURN_EVENT_MAPPING_3.put(new Pair<>(ElmRefundStatusEnum.CUSTOMER_APPEAL, ElmRefundStatusEnum.REFUND_FAILED),
                new Pair<>(ChannelAfterSaleEventEnum.REJECT_RETURN, "退货退款-二审申诉驳回"));

        RETURN_EVENT_MAPPING_4.put(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_FIRST_AGREE),
                new Pair<>(ChannelAfterSaleEventEnum.AGREE, "退货退款-初审同意"));

        RETURN_EVENT_MAPPING_4.put(
                        new Pair<>(ElmRefundStatusEnum.REFUND_SUCCESS, ElmReturnGoodStatusEnum.RETURN_SECOND_AGREE),
                new Pair<>(ChannelAfterSaleEventEnum.REFUND_SUCCESS, "退货退款-客服介入中同意"));

        RETURN_EVENT_MAPPING_5.put(new Pair<>(new Pair<>(ElmRefundStatusEnum.INIT, ElmReturnGoodStatusEnum.RETURN_FIRST_AGREE), ElmOperatorRoleEnum.CUSTOMER),
                new Pair<>(ChannelAfterSaleEventEnum.AGREE, "退货退款-初审同意"));

        REFUND_CALLCENTER_TYPE_MAPPING.put(new Pair<>(ElmRefundStatusEnum.REJECT, ElmRefundStatusEnum.CUSTOMER_APPEAL), ElmCallCenterAppealTypeEnum.REFUND_APPEAL);

        REFUND_CALLCENTER_TYPE_MAPPING.put(new Pair<>(ElmRefundStatusEnum.CUSTOMER_APPEAL, ElmRefundStatusEnum.REFUND_SUCCESS), ElmCallCenterAppealTypeEnum.CALL_CENTER_AGREE_REFUND);

        REFUND_CALLCENTER_TYPE_MAPPING.put(new Pair<>(ElmRefundStatusEnum.CUSTOMER_APPEAL, ElmRefundStatusEnum.REFUND_FAILED), ElmCallCenterAppealTypeEnum.CALL_CENTER_REJECT_REFUND);

        REFUNDGOOD_CALLCENTER_TYPE_MAPPING.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_TENANT_FIRST_REJECT),
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_CUSTOMER_APPEAL)),
                ElmCallCenterAppealTypeEnum.CUSTOMER_REFUNDGOOD_APPEAL);

        REFUNDGOOD_CALLCENTER_TYPE_MAPPING.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_CUSTOMER_APPEAL),
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_FIRST_AGREE)),
                ElmCallCenterAppealTypeEnum.CALL_CENTER_AGREE_REFUNDGOOD);

        REFUNDGOOD_CALLCENTER_TYPE_MAPPING.put(new Pair<>(
                        new Pair<>(ElmRefundStatusEnum.APPLY, ElmReturnGoodStatusEnum.RETURN_CUSTOMER_APPEAL),
                        new Pair<>(ElmRefundStatusEnum.CLOSED, ElmReturnGoodStatusEnum.RETURN_CLOSED)),
                ElmCallCenterAppealTypeEnum.CALL_CENTER_REJECT_REFUNDGOOD);
    }

    public ResultStatus doRefund(Long tenantId, String tenantAppId, String messageBody, String appPoiCode) {
        ElmOrderReverseNotify elmOrderReverseNotify = JSON.parseObject(messageBody, ElmOrderReverseNotify.class);
        Long appId = NumberUtils.LONG_ZERO;
        Long storeId = ProjectConstant.UNKNOW_STORE_ID;
        if (MccConfigUtil.checkElmStoreId(tenantAppId) && MccConfigUtil.checkElmIsvAppKey(tenantAppId)) {
            log.info("饿了么ISV应用:{}查询订单退款请求参数",tenantAppId);
            Optional<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTO = poiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO(appPoiCode, false);
            tenantId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getTenantId).orElse(NumberUtils.LONG_ZERO);
            appId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getQnhAppId).orElse(NumberUtils.LONG_ZERO);
            storeId = channelPoiBaseInfoDTO.map(ChannelPoiBaseInfoDTO::getPoiId).orElse(ProjectConstant.UNKNOW_STORE_ID);
        } else {
            CopAccessConfigDO accessConfig = getAccessConfig(ChannelTypeEnum.ELEM.getCode(), tenantAppId);
            appId = Optional.ofNullable(accessConfig).map(CopAccessConfigDO::getAppId).orElse(NumberUtils.LONG_ZERO);
        }

        if (appId == 0) {
                log.error("未查询到饿了么appId tenantAppId{}", tenantAppId);
                return ResultGenerator.genResult(ResultCode.UNDEAL_ERROR).setData(ProjectConstant.NG);
        }

        log.info("isv doRefund appid:{}, storeId:{}", appId, storeId);
        // 因为部分情况无法通过消息的退款状态和退货来判定是 仅退款还是退货退款，统一先查退款详情
        OrderAfsApplyDTO orderAfsApplyDTO = fetchAfterSale(tenantId, appId, elmOrderReverseNotify.getOrder_id(), elmOrderReverseNotify.getRefund_order_id(), storeId);

        if (orderAfsApplyDTO == null) {
            log.error("未查询到饿了么退款单详情 {}, {}", elmOrderReverseNotify.getOrder_id(), elmOrderReverseNotify.getRefund_order_id());
            return ResultGenerator.genResult(ResultCode.AFTERSALE_NOT_EXIST).setData(ProjectConstant.NG);
        }


        ElmRefundStatusEnum newRefundStatus = EnumUtil.getEnumByCode(elmOrderReverseNotify.getCur_reverse_event().getRefund_status(), ElmRefundStatusEnum.class);

        ElmRefundStatusEnum oldRefundStatus = EnumUtil.getEnumByCode(elmOrderReverseNotify.getCur_reverse_event().getLast_refund_status(), ElmRefundStatusEnum.class);

        ElmReturnGoodStatusEnum newReturnGoodStatus = EnumUtil.getEnumByCode(elmOrderReverseNotify.getCur_reverse_event().getReturn_goods_status(), ElmReturnGoodStatusEnum.class);

        ElmReturnGoodStatusEnum oldReturnGoodStatus = EnumUtil.getEnumByCode(elmOrderReverseNotify.getCur_reverse_event().getLast_return_goods_status(), ElmReturnGoodStatusEnum.class);

        ServiceTypeEnum serviceType = ServiceTypeEnum.enumOf(String.valueOf(orderAfsApplyDTO.getServiceType()));

        OcmsRefundSponsorEnum sponsor = OcmsRefundSponsorEnum.valueOf(orderAfsApplyDTO.getApplyOpType());

        ChannelAfterSaleEventEnum channelAfterSaleEventEnum = null;

        if (serviceType == ServiceTypeEnum.REFUND) {
            channelAfterSaleEventEnum = refundOnlyEventMapping(elmOrderReverseNotify.getOrder_id(), elmOrderReverseNotify.getRefund_order_id(), oldRefundStatus, newRefundStatus);
        } else if (serviceType == ServiceTypeEnum.REFUND_GOODS) {
            ElmOperatorRoleEnum elmOperatorRole = EnumUtil.getEnumByCode(elmOrderReverseNotify.getCur_reverse_event().getOperator_role(), ElmOperatorRoleEnum.class);
            channelAfterSaleEventEnum = returnGoodsEventMapping(elmOrderReverseNotify.getOrder_id(), elmOrderReverseNotify.getRefund_order_id(), oldRefundStatus, oldReturnGoodStatus, newRefundStatus, newReturnGoodStatus, elmOperatorRole, tenantId, orderAfsApplyDTO);
            if (channelAfterSaleEventEnum != null) {
                switch (channelAfterSaleEventEnum) {
                    case REFUND_SUCCESS:
                        orderAfsApplyDTO.setReturnGoodsStatusType(ReturnGoodsStatusEnum.MERCHANT_CONFIRM.getCode());
                        break;
                    case REJECT_RETURN:
                        orderAfsApplyDTO.setReturnGoodsStatusType(ReturnGoodsStatusEnum.MERCHANT_REFUSE.getCode());
                        break;
                    case CUSTOMER_SENT_BACK_GOODS:
                        if (isCustomSendBackRefundGoods(newReturnGoodStatus, oldReturnGoodStatus, orderAfsApplyDTO.getReturnGoodsStatusType())) {
                            orderAfsApplyDTO.setReturnGoodsStatusType(ReturnGoodsStatusEnum.CUSTOM_SENT.getCode());
                        }
                        break;
                }
            }
        } else {
            log.error("当前退款类型不支持，{}", serviceType);
            return ResultGenerator.genResult(ResultCode.CHANNEL_RES_TYPE_UNKNOWN).setData(ProjectConstant.NG);
        }

        ElmCallCenterAppealTypeEnum callCenterAppealType = callCenterAppealEventMapping(elmOrderReverseNotify.getOrder_id(), elmOrderReverseNotify.getRefund_order_id(), oldRefundStatus, oldReturnGoodStatus, newRefundStatus, newReturnGoodStatus);


        if (channelAfterSaleEventEnum == null) {
            log.error("当前退款状态未识别，{},{},{},{}", oldRefundStatus, oldReturnGoodStatus, newRefundStatus, newReturnGoodStatus);
            return ResultGenerator.genResult(ResultCode.CHANNEL_RES_TYPE_UNKNOWN).setData(ProjectConstant.NG);
        }


        return afterSaleApplyOrUpdate(tenantId, appId, elmOrderReverseNotify, orderAfsApplyDTO, sponsor.getValue(), channelAfterSaleEventEnum, storeId, callCenterAppealType);
    }


    private ChannelAfterSaleEventEnum refundOnlyEventMapping(String channelOrderId, String afterSaleId, ElmRefundStatusEnum oldRefundStatus, ElmRefundStatusEnum newRefundStatus) {
        Pair<ChannelAfterSaleEventEnum, String> ret = REFUND_ONLY_EVENT_MAPPING.get(new Pair<>(oldRefundStatus, newRefundStatus));
        if (ret != null) {
            log.info("elm售后事件「{}」channelOrderId: {}, afterSaleId: {}", ret.getValue(), channelOrderId, afterSaleId);
            return ret.getKey();
        }
        return null;
    }

    private ChannelAfterSaleEventEnum returnGoodsEventMapping(String channelOrderId, String afterSaleId, ElmRefundStatusEnum oldRefundStatus, ElmReturnGoodStatusEnum oldReturnGoodStatus,
                                                              ElmRefundStatusEnum newRefundStatus, ElmReturnGoodStatusEnum newReturnGoodStatus, ElmOperatorRoleEnum elmOperatorRole,
                                                              Long tenantId, OrderAfsApplyDTO orderAfsApplyDTO) {


        Pair<ChannelAfterSaleEventEnum, String> ret = RETURN_EVENT_MAPPING_1.get(new Pair<>(
                new Pair(oldRefundStatus, oldReturnGoodStatus),
                new Pair(newRefundStatus, newReturnGoodStatus)));

        if (ret == null) {
            ret = RETURN_EVENT_MAPPING_2.get(new Pair<>(
                    new Pair(oldRefundStatus, oldReturnGoodStatus),
                    newRefundStatus));
        }

        if (ret == null) {
            ret = RETURN_EVENT_MAPPING_3.get(new Pair<>(
                    oldRefundStatus,
                    newRefundStatus));
        }

        // 增加兜底逻辑，需要通过【状态】和【操作人】共同确认
        if(ret == null && MccConfigUtil.checkSupportElmReturnGoodsToAgree(tenantId)){
            ret = RETURN_EVENT_MAPPING_5.get(new Pair<>(new Pair(oldRefundStatus, newReturnGoodStatus), elmOperatorRole));
            if(ret != null){
                if(orderAfsApplyDTO.getExtend() != null && orderAfsApplyDTO.getExtend().containsKey("fastRefundType")){
                    orderAfsApplyDTO.getExtend().remove("fastRefundType");
                }
                log.info("elm售后事件进入兜底处理, channelOrderId: {}, afterSaleId: {}, elmOperatorRole:{}, 订单状态{}, {}, {}, {}", channelOrderId, afterSaleId, elmOperatorRole, oldRefundStatus, oldReturnGoodStatus, newRefundStatus, newReturnGoodStatus);
            }
        }

        // 增加兜底处理
        if (ret == null) {
            ret = RETURN_EVENT_MAPPING_4.get(new Pair<>(newRefundStatus,newReturnGoodStatus));
            if (ret != null) {
                // 记录日志
                log.info("elm售后事件进入兜底处理, channelOrderId: {}, afterSaleId: {}, 订单状态{}, {}, {}, {}", channelOrderId, afterSaleId, oldRefundStatus, oldReturnGoodStatus, newRefundStatus, newReturnGoodStatus);
            }
        }


        if (ret != null) {
            log.info("elm售后事件「{}」channelOrderId: {}, afterSaleId: {}", ret.getValue(), channelOrderId, afterSaleId);
            return ret.getKey();
        }

        return null;
    }


    private ElmCallCenterAppealTypeEnum callCenterAppealEventMapping(String channelOrderId, String afterSaleId, ElmRefundStatusEnum oldRefundStatus, ElmReturnGoodStatusEnum oldReturnGoodStatus,
                                                                     ElmRefundStatusEnum newRefundStatus, ElmReturnGoodStatusEnum newReturnGoodStatus) {
        ElmCallCenterAppealTypeEnum ret = REFUNDGOOD_CALLCENTER_TYPE_MAPPING.get(new Pair<>(
                new Pair(oldRefundStatus, oldReturnGoodStatus),
                new Pair(newRefundStatus, newReturnGoodStatus)
        ));

        if (ret == null) {
            ret = REFUND_CALLCENTER_TYPE_MAPPING.get(new Pair<>(oldRefundStatus, newRefundStatus));
        }

        if (ret != null) {
            log.info("饿了么客服介入事件:{}, channelOrderId:{}, afterSaleId:{}", ret.getDescription(), channelOrderId, afterSaleId);
        }

        return ret;
    }


    // 饿了么售后事件发送
    private ResultStatus afterSaleApplyOrUpdate(Long tenantId, long appId, ElmOrderReverseNotify elmOrderReverseNotify, OrderAfsApplyDTO orderAfsApplyDTO, Integer sponsor, ChannelAfterSaleEventEnum channelAfterSaleEvent, long storeId, ElmCallCenterAppealTypeEnum callCenterAppealType) {
        ChannelAfterSaleEvent afterSaleEvent = new ChannelAfterSaleEvent();

        BeanUtils.copyProperties(orderAfsApplyDTO, afterSaleEvent);

        List<PartRefundProductInfo> refundProductInfos = new LinkedList<>();

        orderAfsApplyDTO.getAfsProductList().forEach(
                afsProduct -> {
                    PartRefundProductInfo refundProductInfo = new PartRefundProductInfo();

                    // 财务相关字段
                    BeanUtils.copyProperties(afsProduct, refundProductInfo);

                    refundProductInfo.setSkuName(afsProduct.getSkuName());
                    refundProductInfo.setCustomSkuId(afsProduct.getSkuId());
                    refundProductInfo.setCount(afsProduct.getCount());
                    refundProductInfo.setSkuRefundAmount(afsProduct.getSkuRefundAmount());
                    refundProductInfo.setFoodPrice(afsProduct.getFoodPrice());
                    refundProductInfo.setBoxPrice(afsProduct.getBoxPrice());
                    refundProductInfo.setBoxNum(afsProduct.getBoxNum());
                    refundProductInfo.setRefundWeight(afsProduct.getRefundWeight());
                    refundProductInfo.setSpec(afsProduct.getSpec());
                    refundProductInfo.setChannelOrderItemId(afsProduct.getChannelOrderItemId());
                    refundProductInfos.add(refundProductInfo);
                }
        );


        afterSaleEvent.setAfterSaleServiceType(orderAfsApplyDTO.getServiceType());
        afterSaleEvent.setChannelOrderId(elmOrderReverseNotify.getOrder_id());
        afterSaleEvent.setChannelType(ChannelTypeEnum.ELEM.getCode());
        afterSaleEvent.setEventId(channelAfterSaleEvent.getEventId());
        afterSaleEvent.setAppeal(channelAfterSaleEvent == ChannelAfterSaleEventEnum.APPEAL_APPLY);
        afterSaleEvent.setTenantId(tenantId);
        afterSaleEvent.setTimestamp(System.currentTimeMillis());
        afterSaleEvent.setAfterSaleId(elmOrderReverseNotify.getCur_reverse_event().getRefund_order_id());
        afterSaleEvent.setShopId(storeId);
        // 从消息上无法分辨全单退还是部分退
        afterSaleEvent.setRefundType(orderAfsApplyDTO.getRefundType());
        String reasonCodeDesc = Optional.ofNullable(elmOrderReverseNotify.getCur_reverse_event().getRefund_reason_desc()).orElse("");
        String reasonContent = Optional.ofNullable(elmOrderReverseNotify.getCur_reverse_event().getReason_content()).orElse("");
        String reason = reasonCodeDesc + (StringUtils.isBlank(reasonContent) ? "" : ":" + reasonContent);
        if (orderAfsApplyDTO.getExtend() != null && orderAfsApplyDTO.getExtend().containsKey("fastRefundType") && channelAfterSaleEvent.equals(ChannelAfterSaleEventEnum.AGREE)) {
            afterSaleEvent.setElmFastRefundType(orderAfsApplyDTO.getExtend().get("fastRefundType"));
        }
        afterSaleEvent.setReason(reason);
        afterSaleEvent.setRefundApplyTime(elmOrderReverseNotify.getCur_reverse_event().getOccur_time());
        afterSaleEvent.setRefundAmount(orderAfsApplyDTO.getRefundPrice());
        afterSaleEvent.setSponsor(sponsor);
        afterSaleEvent.setRefundPicList(elmOrderReverseNotify.getCur_reverse_event().getImage_list());
        afterSaleEvent.setAppId(appId);

        // 消息中没有商品明细
        afterSaleEvent.setRefundProducts(refundProductInfos);
        afterSaleEvent.setCommission(orderAfsApplyDTO.getCommission());
        afterSaleEvent.setCommissionType(orderAfsApplyDTO.getCommissionType());
        afterSaleEvent.setAfsApplyType(orderAfsApplyDTO.getAfsApplyType());



        afterSaleEvent.setPlatLogisticsPromotion(orderAfsApplyDTO.getPlatLogisticsPromotion());
        afterSaleEvent.setPoiLogisticsPromotion(orderAfsApplyDTO.getPoiLogisticsPromotion());
        afterSaleEvent.setPoiPackageIncome(orderAfsApplyDTO.getPoiPackageIncome());
        afterSaleEvent.setPlatPackageIncome(orderAfsApplyDTO.getPlatPackageIncome());
        afterSaleEvent.setPoiPackagePromotion(orderAfsApplyDTO.getPoiPackagePromotion());
        afterSaleEvent.setPlatPackagePromotion(orderAfsApplyDTO.getPlatPackagePromotion());
        afterSaleEvent.setPayPackageFee(orderAfsApplyDTO.getPayPackageFee());
        afterSaleEvent.setOriginalPackageFee(orderAfsApplyDTO.getOriginalPackageFee());
        afterSaleEvent.setFreightFee(orderAfsApplyDTO.getFreight());
        afterSaleEvent.setMerchantIncome(orderAfsApplyDTO.getMerchantIncome());
        afterSaleEvent.setReturnGoodsStatus(orderAfsApplyDTO.getReturnGoodsStatusType());
        afterSaleEvent.setPickUpRefundGoodsAddress(orderAfsApplyDTO.getPickUpRefundGoodsAddress());
        afterSaleEvent.setRefundGoodsPrivacyContactPhone(orderAfsApplyDTO.getRefundGoodsPrivacyContactPhone());
        if (callCenterAppealType != null) {
            afterSaleEvent.setElmCallCenterAppealType(callCenterAppealType.getCode());
        }
        if (orderAfsApplyDTO.getShopCardFee() != null) {
            com.meituan.shangou.saas.o2o.dto.model.ShopCardFee shopCardFee= new com.meituan.shangou.saas.o2o.dto.model.ShopCardFee();
            BeanUtils.copyProperties(orderAfsApplyDTO.getShopCardFee(),shopCardFee);
            afterSaleEvent.setShopCardFee(shopCardFee);
        }
        log.info("饿了么退单售后时间发送:{}",new Gson().toJson(afterSaleEvent));
        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        result.setMsg("success");
        if (afterSaleEvent != null) {
            if (MccConfigUtil.orderNotifyThroughMafka(afterSaleEvent.getTenantId(), afterSaleEvent.getChannelType())){
                afterSaleEvent.setEventType(ChannelOrderEventType.CHANNEL_ELEM_AFTER_SALE_EVENT.getEventType());
                newSupplyChannelElemOrderRefundMessageProducer.sendMessageSync(afterSaleEvent);
                return result;
            }
            newSupplychannelOrderRefundMessageProducer.sendMessageSync(afterSaleEvent);
        }
        return result;
    }

//    private ResultStatus afterSaleChange(Long tenantId, String tenantAppId, String messageBody, ChannelAfterSaleEventEnum channelAfterSaleEvent, ServiceTypeEnum serviceType) {
//
//
//        ElmOrderReverseNotify elmOrderReverseNotify = JSON.parseObject(messageBody, ElmOrderReverseNotify.class);
//
//        if (serviceType == null) {
//            OrderAfsApplyDTO orderAfsApplyDTO = fetchAfterSale(tenantId, tenantAppId, elmOrderReverseNotify.getOrder_id(), elmOrderReverseNotify.getRefund_order_id());
//
//            if (orderAfsApplyDTO == null) {
//                return ResultGenerator.genResult(ResultCode.AFTERSALE_NOT_EXIST).setData(ProjectConstant.NG);
//            }
//
//            serviceType = ServiceTypeEnum.enumOf(String.valueOf(orderAfsApplyDTO.getServiceType()));
//        }
//
//        ChannelAfterSaleEvent afterSaleEvent = new ChannelAfterSaleEvent();
//
//        afterSaleEvent.setChannelOrderId(elmOrderReverseNotify.getOrder_id());
//        afterSaleEvent.setChannelType(ChannelTypeEnum.ELEM.getCode());
//        afterSaleEvent.setEventId(channelAfterSaleEvent.getEventId());
//        afterSaleEvent.setTenantId(tenantId);
//        afterSaleEvent.setTimestamp(System.currentTimeMillis());
//        afterSaleEvent.setAfterSaleId(elmOrderReverseNotify.getRefund_order_id());
//        afterSaleEvent.setReason(elmOrderReverseNotify.getCur_reverse_event().getReason_content());
//        afterSaleEvent.setRefundApplyTime(elmOrderReverseNotify.getCur_reverse_event().getOccur_time());
//
//        afterSaleEvent.setRefundAmount(0);
//        afterSaleEvent.setAfterSaleServiceType(serviceType.getCode());
//        afterSaleEvent.setRefundPicList(elmOrderReverseNotify.getCur_reverse_event().getImage_list());
//
//
//        // 消息中没有商品明细
//        afterSaleEvent.setRefundProducts(Collections.emptyList());
//
//
//        if (afterSaleEvent != null) {
//            newSupplychannelOrderRefundMessageProducer.sendMessageSync(afterSaleEvent);
//        }
//
//        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
//        result.setMsg("success");
//        return result;
//    }

    private CopAccessConfigDO getAccessConfig(int channelId, String tenantAppId) {
        CopAccessConfigDO copAccessConfig = copAccessConfigService.selectByTenantAppIdAndChannelId(tenantAppId, channelId);
        if (copAccessConfig == null) {
            log.error("ChannelOrderCallbackServiceImpl.getTenantIdParam, 未获取到渠道租户ID, channelId:{}, tenantAppId:{}",
                    channelId, tenantAppId);
        }
        return copAccessConfig;
    }

    public OrderAfsApplyDTO  fetchAfterSale(Long tenantId, long appId, String channelOrderId, String channelRefundId, long storeId) {
        GetOrderAfsApplyListRequest getOrderAfsApplyListRequest = new GetOrderAfsApplyListRequest();

        getOrderAfsApplyListRequest.setTenantId(tenantId);
        getOrderAfsApplyListRequest.setAppId(appId);
        getOrderAfsApplyListRequest.setChannelOrderId(channelOrderId);
        getOrderAfsApplyListRequest.setAfterSaleId(channelRefundId);
        getOrderAfsApplyListRequest.setStoreId(storeId);

        GetOrderAfsApplyListResult getOrderAfsApplyListResult = elmChannelOrderService.getOrderAfsApplyList(getOrderAfsApplyListRequest);

        if (CollectionUtils.isEmpty(getOrderAfsApplyListResult.getAfsApplyList())) {
            log.error("elm查询售后单失败！");
            return null;
        }

        return getOrderAfsApplyListResult.getAfsApplyList().get(0);
    }

    private boolean isCustomSendBackRefundGoods(ElmReturnGoodStatusEnum newReturnGoodStatus, ElmReturnGoodStatusEnum oldReturnGoodStatus, int returnGoodsStatus) {
        if (ReturnGoodsStatusEnum.WAIT_CUSTOM_SENDBACK.getCode() != returnGoodsStatus) {
            return false;
        }
        return newReturnGoodStatus == ElmReturnGoodStatusEnum.RETURN_GOOD_SEND_OUT
                && oldReturnGoodStatus == ElmReturnGoodStatusEnum.RETURN_FIRST_AGREE;
    }
}
