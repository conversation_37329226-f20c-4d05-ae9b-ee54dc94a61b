package com.sankuai.meituan.shangou.empower.ocms.channel.service;


import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentListQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentListQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentReplyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentReplyResponse;

/**
 * 评价服务
 *
 * <AUTHOR>
 */
public interface ChannelCommentService {

    /**
     * 查询评价列表
     * @param request 查询评价列表参数
     * @return
     */
    CommentListQueryResponse queryCommentList(CommentListQueryRequest request);

    /**
     * 评价回复
     * @param request 评价回复参数
     * @return
     */
    CommentReplyResponse reply(CommentReplyRequest request);
}
