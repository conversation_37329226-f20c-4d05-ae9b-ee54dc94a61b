package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.MedicineChannelCallbackRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.MedicineChannelCallbackResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuNotifyRequest;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * @create 2019-06-10 8:35 PM
 **/
public interface MedicineChannelCallBackService {

    /**
     * 商品新增或修改推送
     * @param request
     * @return
     */
    MedicineChannelCallbackResponse skuNotify(MedicineChannelCallbackRequest request);
}
