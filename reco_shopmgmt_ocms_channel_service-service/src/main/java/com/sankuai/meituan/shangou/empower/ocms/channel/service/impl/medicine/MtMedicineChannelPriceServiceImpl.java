package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.medicine.ChannelMedicineBatchDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.medicine.ChannelMedicinePriceUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPriceService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtMedicineConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.loop.MedicineCloseLoopService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import javafx.util.Pair;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTMedicineEnum.MEDICINE_PRICE_BATCH_UPDATE;

/**
 * 美团医药渠道商品价格内部服务接口
 *
 * <AUTHOR>
 * @create 2019-01-09 下午4:40
 **/
@Service("mtMedicineChannelPriceService")
public class MtMedicineChannelPriceServiceImpl implements ChannelPriceService {

    public static final int PRICE_UPDATE_MAX_COUNT = 200;

    @Resource
    private MtMedicineChannelGateService mtMedicineChannelGateService;

    @Resource
    private CommonLogger log;

    @Resource
    private MedicineCloseLoopService medicineCloseLoopService;

    @Override
    public ResultData updatePrice(SkuPriceRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        if (medicineCloseLoopService.isUwmsCloseLoop(request.getTenantId(), request.getStoreId(), MedicineCloseLoopService.MedicineCloseLoopTypeEnum.PRODUCT)) {
            resultData.getSucData().addAll(request.getParamList().stream().map(item -> new ResultSuccessSku().setStoreId(request.getStoreId()).setSkuId(item.getSkuId())).collect(Collectors.toList()));
            return resultData;
        }

        // 分页调用
        ListUtils.listPartition(request.getParamList(), PRICE_UPDATE_MAX_COUNT)
                .forEach(data -> {
                    List<String> skuIds = Lists.newArrayList();
                    try {
                        // 返回结果组装用标识
                        skuIds = data.stream().map(SkuPriceDTO::getSkuId).collect(Collectors.toList());

                        // 业务参数转换
                        List<ChannelMedicinePriceUpdateDTO> list = data.stream()
                                .map(MtMedicineConverterUtil::transferMedicinePriceUpdateDto)
                                .collect(Collectors.toList());
                        ChannelMedicineBatchDTO dto = MtMedicineConverterUtil.transferBatchDto(JSON.toJSONString(list));

                        // 调用渠道接口
                        Map<Long, ChannelResponseDTO> postResult = mtMedicineChannelGateService.sendPostAppDto(MEDICINE_PRICE_BATCH_UPDATE, getBaseRequest(request), dto);

                        // 组装返回结果
                        ResultDataUtils.combineResultDataList(resultData, postResult, skuIds);

                    } catch (IllegalArgumentException e) {
                        log.error("MtMedicineChannelPriceServiceImpl.updatePrice 参数校验失败, data:{}", data, e);
                        ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds);

                    } catch (Exception e) {
                        log.error("MtMedicineChannelPriceServiceImpl.updatePrice 服务异常, data:{}", data, e);
                        ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds);
                    }
                });
        return resultData;
    }

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    public ResultData updatePriceMultiChannel(SkuPriceMultiChannelRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long storeId = request.getParamList().get(0).storeId;
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.tenantId)
                .setChannelId(request.getParamList().get(0).channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        // 过滤操作渠道商品黑名单门店
        List<SkuPriceMultiChannelDTO> filterResultList = filterBlockPriceList(request.getTenantId(), request.getParamList(), resultData);

        // 因为前面只有单门店，所以这里直接判断是否还有剩下的需要操作的列表就行
        if (CollectionUtils.isEmpty(filterResultList)) {
            return resultData;
        }

        // 分页调用
        ListUtils.listPartition(filterResultList, PRICE_UPDATE_MAX_COUNT)
                .forEach(data -> {
                    List<String> skuIds = data.stream().map(SkuPriceMultiChannelDTO::getSkuId).collect(Collectors.toList());
                    try {

                        // 业务参数转换
                        List<ChannelMedicinePriceUpdateDTO> list = data.stream()
                                .map(MtMedicineConverterUtil::transferMedicinePriceUpdateDto)
                                .distinct()
                                .collect(Collectors.toList());
                        ChannelMedicineBatchDTO dto = MtMedicineConverterUtil.transferBatchDto(JSON.toJSONString(list));

                        // 调用渠道接口
                        Map<Long, ChannelResponseDTO> postResult = mtMedicineChannelGateService.sendPostAppDto(MEDICINE_PRICE_BATCH_UPDATE, baseRequest, dto);

                        // 组装返回结果
                        ResultDataUtils.combinePartResultData(resultData, postResult, skuIds);

                    } catch (IllegalArgumentException e) {
                        log.error("MtMedicineChannelPriceServiceImpl.updatePrice 参数校验失败, data:{}", data, e);
                        ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds, storeId);

                    } catch (Exception e) {
                        log.error("MtMedicineChannelPriceServiceImpl.updatePrice 服务异常, data:{}", data, e);
                        ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds, storeId);
                    }
                });
        return resultData;
    }

    @Override
    public List<ChannelStorePriceInfo> batchGetPriceInfo(BaseRequest request, List<Long> skuIds) {
        return null;
    }

    private List<SkuPriceMultiChannelDTO> filterBlockPriceList(Long tenantId, List<SkuPriceMultiChannelDTO> paramList, ResultData resultData) {
        Map<Long, List<SkuPriceMultiChannelDTO>> sourceMap = new HashMap<>();
        sourceMap.put(tenantId, paramList);

        Pair<Map<Long, List<SkuPriceMultiChannelDTO>>, Map<Long, List<SkuPriceMultiChannelDTO>>> filterResult = medicineCloseLoopService.filterBlockedItems(sourceMap, MedicineCloseLoopService.MedicineCloseLoopTypeEnum.PRODUCT, SkuPriceMultiChannelDTO::getStoreId);

        // 将过滤掉的数据默认填充为成功
        if (filterResult.getValue() != null && CollectionUtils.isNotEmpty(filterResult.getValue().get(tenantId))) {
            resultData.getSucData().addAll(filterResult.getValue().get(tenantId).stream().map(item -> new ResultSuccessSku().setStoreId(item.getStoreId()).setSkuId(item.getSkuId())).collect(Collectors.toList()));
        }

        return CollectionUtils.isEmpty(filterResult.getKey().get(tenantId)) ? Collections.emptyList() : filterResult.getKey().get(tenantId);
    }

}
