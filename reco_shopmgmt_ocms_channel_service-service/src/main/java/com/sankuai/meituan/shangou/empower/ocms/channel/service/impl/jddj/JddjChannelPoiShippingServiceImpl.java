package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.PoiShippingUpdateParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiShippingService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/10
 * desc: 京东到家渠道门店配送服务
 */
@Service("jddjChannelPoiShippingService")
public class JddjChannelPoiShippingServiceImpl implements ChannelPoiShippingService {

    @Resource
    private JddjConverterService jddjConverterService;

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Value("${jddj.url.base}")
    private String bastUrl;

    @Resource
    private CommonLogger log;

    @Override
    public ResultStatus updatePoiShipping(UpdatePoiShippingRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(request.getTenantId())
                    .setChannelId(request.getChannelId())
                    .setStoreIdList(Lists.newArrayList(request.getShopId()));;

            PoiShippingUpdateParam poiShippingUpdateParam = convertPoiShippingUpdateParam(request);

            Map updateMap = jddjChannelGateService.sendPostApp(bastUrl + ChannelPostJDDJEnum.UPDATE_STORE_INFO.getUrl(), null, baseRequest, poiShippingUpdateParam);

            if (Integer.parseInt((String) updateMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
                return ResultGenerator.genFailResult((String)updateMap.get(ProjectConstant.MSG));
            }
            String updateResult = (String) updateMap.get(ProjectConstant.DATA);
            Integer updateCode = JSON.parseObject(updateResult).getInteger(ProjectConstant.CODE);
            if (updateCode != 0) {
                ResultGenerator.genFailResult(JSON.parseObject(updateResult).getString(ProjectConstant.MSG));
            }

            return ResultGenerator.genSuccessResult();

        } catch (Exception e) {
            log.error("jddjChannelOrderService updatePoiShipping ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public ResultStatus batchUpdatePoiShipping(BatchUpdatePoiShippingRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus resetPoiShipping(ResetPoiShippingRequest resetPoiShippingRequest) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus deletePoiShipping(DeletePoiShippingRequest deletePoiShippingRequest) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public QueryPoiShippingResponse queryPoiShipping(QueryPoiShippingRequest queryPoiShippingRequest) {
        return new QueryPoiShippingResponse(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getCode(), ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg(), null);
    }

    @Override
    public QueryPoiShippingAreaResponse batchQueryPoiShippingAreaInfo(BatchQueryPoiShippingAreaRequest request) {
        return new QueryPoiShippingAreaResponse(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getCode(), ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg(), null);
    }

    @Override
    public QueryPoiShippingAreaResponse queryPoiShippingAreaInfo(QueryPoiShippingRequest request) {
        return new QueryPoiShippingAreaResponse(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getCode(), ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg(), null);
    }

    @Override
    public ResultStatus updatePoiRegularPeriodShippingByShippingAreaId(UpdatePoiRegularPeriodShippingByShippingAreaIdRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus updatePoiSpecialPeriodShippingByShippingAreaId(UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus deletePoiShippingByShippingAreaId(DeletePoiShippingByShippingIdRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    private PoiShippingUpdateParam convertPoiShippingUpdateParam(UpdatePoiShippingRequest request) {
        PoiShippingUpdateParam poiShippingUpdateParam = new PoiShippingUpdateParam();
        String stationNo = getAppPoiCode(request);
        poiShippingUpdateParam.setStationNo(stationNo);
        poiShippingUpdateParam.setOperator("system");
        poiShippingUpdateParam.setCoordinateType(2);
        poiShippingUpdateParam.setCoordinatePoints(generateCoordinatePoints(request));
        return poiShippingUpdateParam;
    }

    private String generateCoordinatePoints(UpdatePoiShippingRequest request) {
        boolean first = true;
        StringBuilder sb = new StringBuilder();
        for (Coordinate coordinate : request.getCoordinates()) {
            if (!first) {
                sb.append(";");
            }
            sb.append(coordinate.getLongitude());
            sb.append(",");
            sb.append(coordinate.getLatitude());
            first = false;
        }
        return sb.toString();
    }

    private String getAppPoiCode(UpdatePoiShippingRequest request) {
        Map<String, ChannelStoreDO> pois = copChannelStoreService.getChannelPoiCode(request.getTenantId(), request.getChannelId(), Arrays.asList(request.getShopId()));
        if (!pois.containsKey("" + request.getTenantId() + "-" + request.getChannelId() + "-" + request.getShopId())) {
            return null;
        }
        // 京东到家的stationNo 是存储在 channelStore 的channelPoiCode
        return pois.get("" + request.getTenantId() + "-" + request.getChannelId() + "-" + request.getShopId()).getChannelPoiCode();
    }
}