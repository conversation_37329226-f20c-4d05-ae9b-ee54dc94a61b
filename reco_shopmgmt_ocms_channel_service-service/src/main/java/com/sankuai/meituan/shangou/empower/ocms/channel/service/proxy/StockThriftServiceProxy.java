package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.operation.virtualstock.QueryConfigSkuStock;
import com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.operation.virtualstock.SkuStockDefaultConfigQueryRequest;
import com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.operation.virtualstock.SkuStockDefaultConfigQueryResponse;
import com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.operation.virtualstock.VirtualStockOperationThriftService;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.RepositoryType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.TenantStoreKey;
import com.sankuai.shangou.centralstock.CentralStockQueryService;
import com.sankuai.shangou.centralstock.common.WarehouseKeyDTO;
import com.sankuai.shangou.centralstock.stock.SkuStockDTO;
import com.sankuai.shangou.centralstock.stock.SkuValidStockDTO;
import com.sankuai.shangou.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: goulei02
 * @date: 2024/8/8
 */
@Slf4j
@Service
public class StockThriftServiceProxy {

    @Resource
    private CentralStockQueryService centralStockQueryService;

    @Resource
    private VirtualStockOperationThriftService.Iface virtualStockOperationThriftService;

    /**
     * 查询销售库存线上可售库存
     * @param tenantStoreKey
     * @param skuIds
     * @return
     */
    public Map<String, Integer> querySaleStock(TenantStoreKey tenantStoreKey, List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        Map<String, Integer> skuStockMap = Maps.newHashMap();
        WarehouseKeyDTO warehouseKeyDTO = new WarehouseKeyDTO(tenantStoreKey.getTenantId(), tenantStoreKey.getStoreId());
        Lists.partition(skuIds, 50).forEach(partSkuIds -> {
            Result<List<SkuStockDTO>> result = centralStockQueryService.querySaleValidStocks(warehouseKeyDTO, partSkuIds);
            if (result == null || result.getCode() != ResultCode.SUCCESS.getCode()) {
                throw new ChannelBizException("查询库存失败");
            }
            Optional.ofNullable(result.getModule()).orElse(Collections.emptyList()).forEach(skuStockDTO -> skuStockMap.put(skuStockDTO.getSkuId(), Optional.ofNullable(skuStockDTO.getValidQuantity()).map(Double::intValue).orElse(0)));
        });
        return skuStockMap;
    }

    /**
     * 查询销售库存门店商品可用库存
     *
     * @param tenantStoreKey
     * @param skuIds
     * @return
     */
    public Map<String, Long> queryStoreSkuValidStock(TenantStoreKey tenantStoreKey, List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        Map<String, Long> skuStockMap = Maps.newHashMap();
        WarehouseKeyDTO warehouseKeyDTO = new WarehouseKeyDTO(tenantStoreKey.getTenantId(), tenantStoreKey.getStoreId());
        Lists.partition(skuIds, 50).forEach(partSkuIds -> {
            log.info("centralStockQueryService.queryStoreSkuValidStockFromSku warehouseKeyDTO:{},partSkuIds:{}", JacksonUtils.toJson(warehouseKeyDTO), JacksonUtils.toJson(partSkuIds));
            Result<List<SkuValidStockDTO>> result = centralStockQueryService.queryStoreSkuValidStockFromSku(warehouseKeyDTO, partSkuIds);
            log.info("centralStockQueryService.queryStoreSkuValidStockFromSku response:{}", JacksonUtils.toJson(result));

            if (result == null || result.getCode() != ResultCode.SUCCESS.getCode()) {
                throw new ChannelBizException("查询库存失败");
            }
            Optional.ofNullable(result.getModule()).orElse(Collections.emptyList()).forEach(skuStockDTO -> skuStockMap.put(skuStockDTO.getSkuId(), Optional.ofNullable(skuStockDTO.getValidQuantity()).map(Double::longValue).orElse(0L)));
        });
        return skuStockMap;
    }

    /**
     * 从stockbiz查询库存
     * @param tenantStoreKey
     * @param skuIds
     * @return
     */
    public Map<String, Long> queryStockFromStockBiz(TenantStoreKey tenantStoreKey, List<String> skuIds, RepositoryType repositoryType) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }
        Map<String, Long> skuStockMap = Maps.newHashMap();
        Lists.partition(skuIds, 100).forEach(partSkuIds -> {
            SkuStockDefaultConfigQueryRequest request = new SkuStockDefaultConfigQueryRequest();
            request.setTenantId(tenantStoreKey.getTenantId());
            request.setQueryConfigSkuStocks(partSkuIds.stream().map(sku -> new QueryConfigSkuStock(tenantStoreKey.getStoreId(), repositoryType == null ? RepositoryType.STORE.val() : repositoryType.val(), sku)).collect(Collectors.toList()));

            SkuStockDefaultConfigQueryResponse response;
            try {
                log.info("virtualStockOperationThriftService querySkuStockByDefaultConfig request:{}", request);
                response = virtualStockOperationThriftService.querySkuStockByDefaultConfig(request);
                log.info("virtualStockOperationThriftService querySkuStockByDefaultConfig response:{}", response);
            } catch (Exception e) {
                throw new ChannelBizException("查询库存失败", e);
            }
            if (response == null) {
                throw new ChannelBizException("查询库存失败");
            }

            Optional.ofNullable(response.getDefaultConfigSkuStocks())
                    .orElse(Collections.emptyList())
                    .forEach(skuStock -> {
                        String qtyStr = skuStock.getQuantity();
                        skuStockMap.put(skuStock.getSkuId(), StringUtils.isBlank(qtyStr) ? 0L : new BigDecimal(qtyStr).longValue());
                    });
        });
        return skuStockMap;
    }
}
