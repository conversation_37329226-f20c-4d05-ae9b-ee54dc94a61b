package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 闪购商品重量单位枚举
 */
public enum SgOpenRetailWeightUnitEnum {

    /**
     * 单位g
     */
    UNIT_G(1, "g", "克(g)", 1f),
    /**
     * 单位kg
     */
    UNIT_KG(2, "kg", "千克(kg)", 1000f),
    /**
     * 单位ml
     */
    UNIT_ML(3, "ml", "毫升(ml)", 1f),
    /**
     * 单位L
     */
    UNIT_L(4, "L", "升(L)", 1000f),
    /**
     * 单位磅
     */
    UNIT_POUND(5, "磅", "磅", 454f),
    /**
     * 单位斤
     */
    UNIT_JIN(6, "斤", "斤", 500f),
    /**
     * 单位两
     */
    UNIT_LIANG(7, "两", "两", 50f),
    /**
     * 未知类型
     */
    UNKNOWN(-1, "unknown", "未知类型", 1f);

    private int code;
    private String alias;
    private String unit;
    private float divisor;

    SgOpenRetailWeightUnitEnum(int code, String alias, String unit, float divisor){
        this.code = code;
        this.alias = alias;
        this.unit = unit;
        this.divisor = divisor;
    }

    private static Map<Integer, SgOpenRetailWeightUnitEnum> codeMap = Maps.newHashMap();
    private static Map<String, SgOpenRetailWeightUnitEnum> aliasMap = Maps.newHashMap();
    private static Map<String, SgOpenRetailWeightUnitEnum> unitMap = Maps.newHashMap();

    static {
        for (SgOpenRetailWeightUnitEnum one : values()) {
            codeMap.put(one.getCode(), one);
            aliasMap.put(one.getAlias(), one);
            unitMap.put(one.getUnit(), one);
        }
    }

    public int getCode() {
        return code;
    }

    public String getAlias() {
        return alias;
    }

    public String getUnit() {
        return unit;
    }

    public float getDivisor() {
        return divisor;
    }

    public static SgOpenRetailWeightUnitEnum findByCode(int code) {
        return codeMap.get(code) == null ? UNKNOWN : codeMap.get(code);
    }

    public static SgOpenRetailWeightUnitEnum findByUnit(String unit) {
        if (unitMap.get(unit) == null) {
            return aliasMap.get(unit) == null ? UNKNOWN : aliasMap.get(unit);
        } else {
            return unitMap.get(unit);
        }
    }

    /**
     * 根据重量单位将克重转化为其余单位重量值
     *
     * @param weight 克重
     * @param sgOpenRetailWeightUnitEnum 重量单位
     */
    public static String transformWeightWithWeightUnit(long weight, SgOpenRetailWeightUnitEnum sgOpenRetailWeightUnitEnum) {
        float weightAfterTransform = weight / sgOpenRetailWeightUnitEnum.getDivisor();
        return new BigDecimal(weightAfterTransform).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
    }

    @Override
    public String toString() {
        return "SgOpenRetailWeightUnitEnum{" +
                "code=" + code + '\'' +
                ", alias=" + alias + '\'' +
                ", unit=" + unit + '\'' +
                ", divisor='" + divisor +
                '}';
    }

}
