package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.medicine.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTMedicineEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSkuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtMedicineConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.loop.MedicineCloseLoopService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultBuilderUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import javafx.util.Pair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 美团医药渠道商品内部服务接口
 *
 * @author: liujianghua02
 * @create: 2020/7/27 下午5:27
 */
@Service("mtMedicineChannelSkuService")
public class MtMedicineChannelSkuServiceImpl implements ChannelSkuService {
    public static final int SKU_CREATE_MAX_COUNT = 200;

    @Value("${mt.url.base}" + "${mt.url.skulist}")
    private String skuList;

    @Value("${mt.url.base}" + "${mt.url.skuDetail}")
    private String skuDetail;

    @Value("${mt.url.base}" + "${mt.url.frontCatList}")
    private String frontCatList;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private MtMedicineChannelGateService mtMedicineChannelGateService;

    @Resource
    private CommonLogger log;

    @Resource
    private com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService mtChannelGateService;

    @Resource
    private MedicineCloseLoopService medicineCloseLoopService;

    /**
     * 批量创建药品
     * https://open-shangou.meituan.com/home/<USER>/84
     * 接口限流：按app维度，当前接口最高调用50次/秒。
     *
     * @param request
     * @return
     */
    @Override
    public ResultData skuCreate(SkuInfoRequest request) {
        return upcCreate(request);
    }

    @Override
    public ResultData upcCreate(SkuInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }

        // 过滤操作渠道商品黑名单门店
        List<Long> filteredStoreIds = filterParamByStoreIds(request.getBaseInfo().getTenantId(), request.getBaseInfo().getStoreIdList(), request.getParamList(), resultData, SkuInfoDTO::getSkuId);
        request.getBaseInfo().setStoreIdList(filteredStoreIds);

        // 因为这里是多门店操作，所以如果门店id为空，就判断为不需要再操作了
        if (CollectionUtils.isEmpty(request.getBaseInfo().getStoreIdList())) {
            return resultData;
        }

        // 分页调用
        ListUtils.listPartition(request.getParamList(), SKU_CREATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuInfoDTO::getSkuId).collect(Collectors.toList());

                List<ChannelMedicineCreateDTO> list = data.stream().map(item ->
                        MtMedicineConverterUtil.transferMedicineDto(item)).collect(Collectors.toList());

                ChannelMedicineBatchDTO dto = MtMedicineConverterUtil.transferBatchDto(JSON.toJSONString(list));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtMedicineChannelGateService
                        .sendPostAppDto(ChannelPostMTMedicineEnum.MEDICINE_BATCH_CREATE, request.getBaseInfo(), dto);

                // 组装返回结果
                ResultDataUtils.combinePartResultData(resultData, postResult, bizKeyList);

            } catch (IllegalArgumentException e) {
                log.error("mtMedicineChannelSkuService.skuCreate 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);

            } catch (Exception e) {
                log.error("mtMedicineChannelSkuService.skuCreate 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList);
            }
        });

        return resultData;
    }

    @Override
    public ResultData updateSku(SkuInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }

        // 更新操作不设置价格
        request.getParamList().stream().filter(Objects::nonNull).forEach(item -> item.setPriceIsSet(false));

        // 过滤操作渠道商品黑名单门店
        List<Long> filteredStoreIds = filterParamByStoreIds(request.getBaseInfo().getTenantId(), request.getBaseInfo().getStoreIdList(), request.getParamList(), resultData, SkuInfoDTO::getSkuId);
        request.getBaseInfo().setStoreIdList(filteredStoreIds);

        if (CollectionUtils.isEmpty(request.getBaseInfo().getStoreIdList())) {
            return resultData;
        }

        // 分页调用
        ListUtils.listPartition(request.getParamList(), SKU_CREATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuInfoDTO::getSkuId).collect(Collectors.toList());

                List<ChannelMedicineCreateDTO> list = data.stream()
                        .map(item -> MtMedicineConverterUtil.transferMedicineDto(item)).collect(Collectors.toList());

                ChannelMedicineBatchDTO dto = MtMedicineConverterUtil.transferBatchDto(JSON.toJSONString(list));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtMedicineChannelGateService
                        .sendPostAppDto(ChannelPostMTMedicineEnum.MEDICINE_BATCH_UPDATE, request.getBaseInfo(), dto);

                // 组装返回结果
                ResultDataUtils.combinePartResultData(resultData, postResult, bizKeyList);

            } catch (IllegalArgumentException e) {
                log.error("mtMedicineChannelSkuService.updateSku 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);

            } catch (Exception e) {
                log.error("mtMedicineChannelSkuService.updateSku 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList);
            }
        });

        return resultData;
    }


    @Override
    public ResultData deleteSku(SkuInfoDeleteRequest request) {
        try {
            ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);

            // 过滤操作渠道商品黑名单门店
            List<Long> filteredStoreIds = filterParamByStoreIds(request.getBaseInfo().getTenantId(), request.getBaseInfo().getStoreIdList(), request.getParamList(), resultData, SkuInfoDeleteDTO::getSkuId);
            request.getBaseInfo().setStoreIdList(filteredStoreIds);

            if (CollectionUtils.isEmpty(request.getBaseInfo().getStoreIdList())) {
                return resultData;
            }

            BaseRequest baseInfo = request.getBaseInfo();
            baseInfo.getStoreIdList().forEach(storeId ->
                    request.getParamList().forEach(skuInfoDeleteDTO -> {
                        ChannelMedicineDeleteDTO deleteDTO = MtMedicineConverterUtil.transferDeleteDto(skuInfoDeleteDTO);
                        Map<Long, ChannelResponseDTO> postResult = mtMedicineChannelGateService.sendPostAppDto(ChannelPostMTMedicineEnum.MEDICINE_DELETE, baseInfo, deleteDTO);
                        if (Objects.isNull(postResult)) {
                            ResultBuilderUtil.resultAnalysis(baseInfo.getChannelId(), storeId, skuInfoDeleteDTO.getSkuId(), "调用渠道接口失败", resultData);
                        }
                        ResultBuilderUtil.resultAnalysis(baseInfo.getChannelId(), storeId, skuInfoDeleteDTO.getSkuId(), postResult, resultData);
                    }));
            return resultData;
        } catch (Exception e) {
            log.error("mtMedicineChannelSkuService.deleteSku, MTYY批量删除商品服务异常, request:{}", request, e);
        }
        return ResultGenerator.genResultData(ResultCode.FAIL, "批量删除商品失败");
    }

    @Override
    public ResultData updateSkuSellStatus(SkuSellStatusInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        // 过滤操作渠道商品黑名单门店
        List<SkuSellStatusInfoDTO> filteredResult = filterSellStatusList(request.getBaseInfo().getTenantId(), request.getParamList(), resultData);

        if (CollectionUtils.isEmpty(filteredResult)) {
            return resultData;
        }

        filteredResult.forEach(data -> {
            List<String> skuIds = data.getSkuId().stream().map(SkuIdDTO::getCustomSkuId).collect(Collectors.toList());
            try {
                ChannelMedicineSellStatusDTO statusDto = MtMedicineConverterUtil.transferSellStatusDto(data);

                BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
                if (data.getStoreId() > 0) {
                    baseRequest.setStoreIdList(Lists.newArrayList(data.getStoreId()));
                }
                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtMedicineChannelGateService.sendPostAppDto(ChannelPostMTMedicineEnum.MEDICINE_BATCH_ISSOLDOUT,
                        baseRequest, statusDto);

                // 结果组装
                ResultDataUtils.combinePartResultData(resultData, postResult, skuIds);
            } catch (IllegalArgumentException e) {
                log.error("mtMedicineChannelSkuService.updateSkuSellStatus 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds);

            } catch (Exception e) {
                log.error("mtMedicineChannelSkuService.updateSkuSellStatus 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds);
            }
        });

        return resultData;
    }

    //查询门店药品列表
    @Override
    public BatchGetSkuInfoResponse batchGetSkuInfo(BatchGetSkuInfoRequest request) {

        BatchGetSkuInfoResponse response = new BatchGetSkuInfoResponse().setStatus(ResultGenerator.genSuccessResult());

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                request.getStoreId());
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSkuInfos(Collections.emptyList());
        }

        // 构造参数
        int pageSize = request.getPageSize();
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();

        bizParam.put("offset", String.valueOf((request.getPageNum() - 1) * pageSize));
        bizParam.put("limit", String.valueOf(pageSize));
        bizParam.put("app_poi_code", appPoiCode); //新的请求方法中，appcode会在请求方法中查询并调用http方法

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTMedicineEnum.MEDICINE_LIST, appPoiCode);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY)).setSkuInfos(Collections.emptyList());
        }

        // 请求线上渠道
        Map skuInfoMap = mtMedicineChannelGateService.sendGetApp(mtMedicineChannelGateService.getPostUrl(ChannelPostMTMedicineEnum.MEDICINE_LIST), null, baseConverterService.baseRequest(request.getBaseInfo()), bizParam);
        if (MapUtils.isEmpty(skuInfoMap) || Objects.isNull(skuInfoMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(skuInfoMap.get(ProjectConstant.DATA))) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团商品失败"));
        }

        List<SkuInfoDTO> skuInfoDTOS = Lists.newArrayList();
        JSONArray skuInfoJSONArray = (JSONArray) skuInfoMap.get(ProjectConstant.DATA);
        /*
        JSONObject extraInfo = (JSONObject) skuInfoMap.get(ProjectConstant.EXTRA_INFO);
        int totalCount = extraInfo != null && null != extraInfo.get(ProjectConstant.TOTAL_COUNT) ? (int) extraInfo.get(ProjectConstant.TOTAL_COUNT) : 0;
        int totalPage = totalCount % request.getPageSize() == 0 ? totalCount / request.getPageSize() : (totalCount / request.getPageSize()) + 1;
         */
        PageInfo pageInfo = new PageInfo(request.getPageNum(), request.getPageSize(), 0, 0);


        // 组装数据
        skuInfoJSONArray.forEach(item -> {
            ChannelMedicineInfoDTO channelMedicineInfoDTO = new ChannelMedicineInfoDTO();
            if (!request.isIgnoreCheck() && (StringUtils.isBlank(((JSONObject) item).getString("id")) || StringUtils.isBlank(((JSONObject) item).getString("category_code")) || StringUtils.isBlank(((JSONObject) item).getString("category_name")))) {
                log.error("MtyyChannelSku.batchGetSkuInfo request:{} skus or category_code or category_name is empty json:{}", request, item);
                return;
            }
            //channelMedicineInfoDTO.setApp_poi_code(appPoiCode);

            SkuInfoDTO skuInfoDTO = MtMedicineConverterUtil.transferSkuInfoDTO((JSONObject) item);
            //setBoxInfo(channelSkuCreateDTO, skuInfoDTO);// 设置包装盒信息
            skuInfoDTOS.add(skuInfoDTO);
        });
        return response.setStatus(ResultGenerator.genSuccessResult()).setSkuInfos(skuInfoDTOS).setPageInfo(pageInfo);

    }

    /**
     * 查询门店商品详情，开放平台未标明限流，暂不限流
     */
    @Override
    public GetSkuInfoResponse getSkuInfo(GetSkuInfoRequest request) {
        GetSkuInfoResponse response = new GetSkuInfoResponse().setStatus(ResultGenerator.genSuccessResult());

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                request.getStoreId());
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));

        if (MapUtils.isEmpty(channelStoreDOMap)) {
            log.error("MtMedicineChannelSkuServiceImpl.getSkuInfo, 查询美团医药渠道商品信息 无法找到对应门店信息, request: {}", request);
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSkuInfo(null);
        }

        // 构建请求
        Map<String, String> requestParams = Maps.newHashMap();

        requestParams.put("app_poi_code", channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode());
        requestParams.put("app_medicine_code", request.getCustomSkuId());

        Map<String, Object> queryChannelResult = mtMedicineChannelGateService.sendGetApp(mtMedicineChannelGateService.getPostUrl(ChannelPostMTMedicineEnum.MEDICINE_GET),
                null, baseConverterService.baseRequest(request.getBaseInfo()), requestParams);

        if (MapUtils.isEmpty(queryChannelResult) || queryChannelResult.get(ProjectConstant.DATA) == null) {
            log.error("MtMedicineChannelSkuServiceImpl.getSkuInfo, 查询美团医药渠道商品信息 查询结果为空, request: {}, response: {}", request, queryChannelResult);
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL));
        }

        // 实际调用接口的时候，如果查询不到结果，返回的值是 "{"data":"ng","error":{"msg":"不存在此商品","code":805}}"
        if (queryChannelResult.get(ProjectConstant.DATA) instanceof String && ProjectConstant.NG.equals(queryChannelResult.get(ProjectConstant.DATA))) {
            log.warn("MtMedicineChannelSkuServiceImpl.getSkuInfo, 查询美团医药渠道商品信息 查询失败, request: {}, response: {}");
            JSONObject errorInfo = (JSONObject) queryChannelResult.get(ProjectConstant.ERROR);
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, errorInfo.get(ProjectConstant.MSG)));
        }

        // 构造结果
        response.setSkuInfo(MtMedicineConverterUtil.transferSkuInfoDTO((JSONObject) (queryChannelResult.get("data"))));
        return response;
    }

    /**
     * 通过门店id列表过滤阻断上行的数据
     *
     * @return 未过滤的门店列表
     */
    private <T> List<Long> filterParamByStoreIds(Long tenantId, List<Long> storeIds, List<T> sourceList, ResultData resultData, Function<T, String> skuIdFunc) {
        Map<Long, List<Long>> tenantStoreIdMap = new HashMap<>();
        tenantStoreIdMap.put(tenantId, storeIds);

        Pair<Map<Long, List<Long>>, Map<Long, List<Long>>> filterResult = medicineCloseLoopService.filterBlockedItems(tenantStoreIdMap, MedicineCloseLoopService.MedicineCloseLoopTypeEnum.PRODUCT, Function.identity());

        if (MapUtils.isNotEmpty(filterResult.getValue()) && filterResult.getValue().get(tenantId) != null) {

            for (Long blockStoreId : filterResult.getValue().get(tenantId)) {
                resultData.getSucData().addAll(sourceList.stream().map(paramItem -> new ResultSuccessSku().setStoreId(blockStoreId).setSkuId(skuIdFunc.apply(paramItem))).collect(Collectors.toList()));
            }
        }

        // 如果有剩下的门店id，就将所有的列表返回，单独操作该门店
        return CollectionUtils.isEmpty(filterResult.getKey().get(tenantId)) ? Collections.emptyList() : filterResult.getKey().get(tenantId);
    }

    /**
     * 通过操作item列表过滤阻断上行的数据
     *
     * @return 未过滤的门店列表
     */
    private List<SkuSellStatusInfoDTO> filterSellStatusList(Long tenantId, List<SkuSellStatusInfoDTO> sourceList, ResultData resultData) {
        Map<Long, List<SkuSellStatusInfoDTO>> sourceMap = new HashMap<>();
        sourceMap.put(tenantId, sourceList);

        Pair<Map<Long, List<SkuSellStatusInfoDTO>>, Map<Long, List<SkuSellStatusInfoDTO>>> filterResult =
                medicineCloseLoopService.filterBlockedItems(sourceMap, MedicineCloseLoopService.MedicineCloseLoopTypeEnum.PRODUCT, SkuSellStatusInfoDTO::getStoreId);

        if (MapUtils.isNotEmpty(filterResult.getValue()) && filterResult.getValue().get(tenantId) != null) {
            for (SkuSellStatusInfoDTO blockedItem : filterResult.getValue().get(tenantId)) {
                resultData.getSucData().addAll(blockedItem.getSkuId().stream().map(skuIdDTO -> new ResultSuccessSku().setStoreId(blockedItem.getStoreId()).setSkuId(skuIdDTO.getCustomSkuId())).collect(Collectors.toList()));
            }
        }

        // 如果有剩下的门店id，就将所有的列表返回，单独操作该门店
        return CollectionUtils.isEmpty(filterResult.getKey().get(tenantId)) ? Collections.emptyList() : filterResult.getKey().get(tenantId);
    }


    // ================================= 以下方法暂不使用 =============================

    @Override
    public GetCategoryResponse batchGetChannelStoreCategoryInfo(CatRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    @Override
    public ResultData pictureUpload(PictureUploadRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    @Override
    public PictureUploadResult getPictureUploadStatus(PictureUploadStatusRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    @Override
    public ResultData changeCustomSkuId(ChangeCustomSkuIdRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    @Override
    public ResultData updateCustomSkuId(UpdateCustomSkuIdRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }


}
