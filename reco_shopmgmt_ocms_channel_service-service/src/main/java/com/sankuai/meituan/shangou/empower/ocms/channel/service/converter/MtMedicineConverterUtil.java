package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.medicine.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.client.medicine.dto.MedicineTenantSkuDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: MtMedicineConverterUtil
 * @Description: 美团医药转换处理
 * <AUTHOR>
 * @Date 2020/7/27 17:53
 */
@Slf4j
public class MtMedicineConverterUtil {

    private static final int channelMedicineStatus = 0;

    private static final int ocmsMedicineStatusOfOnline = 1;

    private static final int ocmsMedicineStatusOfOffline = 2;

    private static final int medicineCatDepth = 2;

    public static ChannelMedicineCreateDTO transferMedicineDto(SkuInfoDTO skuInfo) {
        ChannelMedicineCreateDTO createDTO = new ChannelMedicineCreateDTO();

        createDTO.setApp_medicine_code(skuInfo.getSkuId());

        createDTO.setUpc(skuInfo.getUpc());
        createDTO.setMedicine_no(skuInfo.getMedicineNo());
        createDTO.setSpec(skuInfo.getSpec());
        createDTO.setStock(String.valueOf(skuInfo.getStock()));

        ChannelLeafStoreCategory channelLeafStoreCategory = null;
        List<ChannelLeafStoreCategory> leafStoreCategoryList = skuInfo.getLeafStoreCategoryList();
        if (CollectionUtils.isNotEmpty(leafStoreCategoryList)) {
            channelLeafStoreCategory = leafStoreCategoryList.get(0);
        }
        // 医药目前支持单分类，故获取一个分类
        createDTO.setCategory_code(channelLeafStoreCategory != null ? channelLeafStoreCategory.getCategoryCode() : null);
        createDTO.setCategory_name(channelLeafStoreCategory != null ? channelLeafStoreCategory.getCategoryName() : null);

        // 上下架状态转换
        createDTO.setIs_sold_out(ChannelMedicineCreateDTO.mapIs_sold_out(skuInfo.getSkuStatus()));
        createDTO.setSequence(skuInfo.getSequence());

        // 药品限购详情
        createDTO.setLimit_sale_info(skuInfo.getProperties());

        //更新不传价格
        if (skuInfo.isSetPrice()) {
            createDTO.setPrice(String.valueOf(skuInfo.getPrice()));
        }
        return createDTO;
    }

    public static ChannelMedicineStockUpdateDTO transferMedicineStockUpdateDto(SkuStockDTO skuStockDTO) {
        return new ChannelMedicineStockUpdateDTO(
                skuStockDTO.getSkuId(),
                String.valueOf(skuStockDTO.getStoreId()),
                String.valueOf(Double.valueOf(skuStockDTO.getStockQty()).intValue())
        );
    }

    public static ChannelMedicinePriceUpdateDTO transferMedicinePriceUpdateDto(SkuPriceDTO skuPriceDTO) {
        return new ChannelMedicinePriceUpdateDTO(
                skuPriceDTO.getSkuId(),
                String.valueOf(skuPriceDTO.getPrice())
        );
    }

    public static ChannelMedicineStockUpdateDTO transferMedicineStockUpdateDto(SkuStockMultiChannelDTO skuStockMultiChannelDTO) {
        return new ChannelMedicineStockUpdateDTO(
                skuStockMultiChannelDTO.getSkuId(),
                String.valueOf(skuStockMultiChannelDTO.getStoreId()),
                String.valueOf(Double.valueOf(skuStockMultiChannelDTO.getStockQty()).intValue())
        );
    }

    public static ChannelMedicinePriceUpdateDTO transferMedicinePriceUpdateDto(SkuPriceMultiChannelDTO skuPriceMultiChannelDTO) {
        return new ChannelMedicinePriceUpdateDTO(
                skuPriceMultiChannelDTO.getSkuId(),
                String.valueOf(skuPriceMultiChannelDTO.getPrice())
        );
    }

    public static ChannelMedicineBatchDTO transferBatchDto(String string) {
        ChannelMedicineBatchDTO medicineUpdate = new ChannelMedicineBatchDTO();
        medicineUpdate.setMedicine_data(string);
        return medicineUpdate;
    }

    public static ChannelMedicineDeleteDTO transferDeleteDto(SkuInfoDeleteDTO skuInfoDeleteDTO) {
        ChannelMedicineDeleteDTO medicineDelete = new ChannelMedicineDeleteDTO();
        medicineDelete.setApp_medicine_code(skuInfoDeleteDTO.getSkuId());
        return medicineDelete;
    }

    public static ChannelMedicineSellStatusDTO transferSellStatusDto(SkuSellStatusInfoDTO statusInfoDTO) {
        ChannelMedicineSellStatusDTO dto = new ChannelMedicineSellStatusDTO();
        List<MedicineDataInfo> list = statusInfoDTO.getSkuId().stream().map(item -> {
            MedicineDataInfo dataInfo = new MedicineDataInfo();
            dataInfo.setApp_medicine_code(item.getCustomSkuId());
            dataInfo.setIs_sold_out(ChannelMedicineCreateDTO.mapIs_sold_out(statusInfoDTO.getSkuStatus()));
            return dataInfo;
        }).collect(Collectors.toList());

        dto.setMedicine_data(list);
        return dto;
    }

    public static SkuInfoDTO transferSkuInfoDTO(JSONObject item) {
        SkuInfoDTO skuInfoDTO = new SkuInfoDTO();
        skuInfoDTO.setName(item.getString("name"));
        // 这里为什么要这么写？？？？
        skuInfoDTO.setSkuId("app_medicine_code");
        // 为了避免改动上面出现问题，所以这里就加一个该字段
        skuInfoDTO.setCustomSkuId(item.getString("app_medicine_code"));
        skuInfoDTO.setMedicineNo(item.getString("medicine_no"));
        skuInfoDTO.setUpc(item.getString("upc"));
        skuInfoDTO.setSpec(item.getString("spec"));
        skuInfoDTO.setPrice(Float.parseFloat(item.getString("price")));
        skuInfoDTO.setStock(Integer.parseInt(item.getString("stock")));
        skuInfoDTO.setSkuStatus(Integer.parseInt(item.getString("is_sold_out")) == channelMedicineStatus ? ocmsMedicineStatusOfOnline : ocmsMedicineStatusOfOffline);
        skuInfoDTO.setSequence(Integer.parseInt(item.getString("sequence")));
        skuInfoDTO.setCtime(Integer.parseInt(item.getString("ctime")));
        skuInfoDTO.setUtime(Integer.parseInt(item.getString("utime")));
        skuInfoDTO.setMedicineType(Integer.parseInt(item.getString("medicine_type")));
        skuInfoDTO.setProperties(item.getString("limit_sale_info"));
        skuInfoDTO.setStockConfig(item.getString("stock_config"));
        skuInfoDTO.setLimitOpenSyncStockNow(Boolean.getBoolean(item.getString(" limit_open_sync_stock_now")));
        skuInfoDTO.setCategoryList(getCategoryList(item.getString("category_code"), item.getString("category_name")));
        return skuInfoDTO;
    }

    private static List<ChannelStoreCategory> getCategoryList(String categoryCode, String categoryName) {
        ChannelStoreCategory channelStoreCategory = new ChannelStoreCategory();

        if (StringUtils.isNotBlank(categoryCode)) {
            if (!categoryCode.contains(",")) {
                channelStoreCategory.setFirstCategoryCode(categoryCode);
            } else {
                String[] result = categoryCode.split(",");
                channelStoreCategory.setFirstCategoryCode(result.length >= 1 ? result[0] : null);
                channelStoreCategory.setSecondaryCategoryCode(result.length >= 2 ? result[1] : null);
            }
        }

        if (StringUtils.isNotBlank(categoryName)) {
            if (!categoryName.contains(",")) {
                channelStoreCategory.setFirstCategoryName(categoryName);
            } else {
                String[] result = categoryName.split(",");
                channelStoreCategory.setFirstCategoryName(result.length >= 1 ? result[0] : null);
                channelStoreCategory.setSecondaryCategoryName(result.length >= 2 ? result[1] : null);
            }
        }
        return Lists.newArrayList(channelStoreCategory);
    }

    public static CatInfo getCatInfo(ChannelMedicineCatDTO channelMedicineCatDTO, CatRequest catRequest) {
        CatInfo catInfo = new CatInfo();

        if (StringUtils.isNotBlank(channelMedicineCatDTO.getSecond_category_name())) {
            // 二级分类
            catInfo.setCatId(channelMedicineCatDTO.getSecond_category_code());
            catInfo.setName(channelMedicineCatDTO.getSecond_category_name());
            catInfo.setDepth(2);
            catInfo.setParentId(channelMedicineCatDTO.getCategory_code());
            catInfo.setParentName(channelMedicineCatDTO.getCategory_name());
            catInfo.setNamePath(channelMedicineCatDTO.getCategory_name() + ">" + channelMedicineCatDTO.getSecond_category_name());
            catInfo.setSequence(channelMedicineCatDTO.getSecond_sequence());
        } else {
            // 一级分类
            catInfo.setCatId(channelMedicineCatDTO.getCategory_code());
            catInfo.setName(channelMedicineCatDTO.getCategory_name());
            catInfo.setDepth(1);
            catInfo.setParentId("");
            catInfo.setParentName("");
            catInfo.setNamePath(channelMedicineCatDTO.getCategory_name());
            catInfo.setSequence(channelMedicineCatDTO.getSequence());
        }

        return catInfo;
    }
}
