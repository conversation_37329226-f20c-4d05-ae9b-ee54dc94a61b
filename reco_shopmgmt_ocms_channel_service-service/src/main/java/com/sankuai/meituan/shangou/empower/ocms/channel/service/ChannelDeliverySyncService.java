package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.ChangeDeliveryPlatformRequest;

/**
 * <AUTHOR>
 * @date 2023-10-13
 * @email <EMAIL>
 */
public interface ChannelDeliverySyncService {

    ResultStatus syncDeliveryPlatformChange(ChangeDeliveryPlatformRequest request);

}
