package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCommentService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ChannelCommentConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj.JddjChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.meituan.shangou.saas.common.json.GsonUtils;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemEvaluationQuery;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemEvaluationRepaly;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemEvaluationQueryParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemEvaluationQueryResult;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemEvaluationRepalyParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemEvaluationRepalyResult;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 京东到家渠道评价内部服务接口
 *
 * <AUTHOR>
 */
@Service("yzToolChannelCommentService")
public class YzToolChannelCommentServiceImpl extends YouZanToolBaseService implements ChannelCommentService {

    @Resource
    private CommonLogger log;

    @Override
    public CommentListQueryResponse queryCommentList(CommentListQueryRequest request) {
        try {
        // 构建评价查询业务参数
            AppMessage appMessage = getSubAppMessage(request.getTenantId(),request.getStoreId());
            YouzanItemEvaluationQueryParams youzanItemEvaluationQueryParams = buildRequestParams(request, appMessage);
            YouzanItemEvaluationQueryResult result = getResult4YouZan(appMessage,new YouzanItemEvaluationQuery(youzanItemEvaluationQueryParams),YouzanItemEvaluationQueryResult.class);

            if(!result.getSuccess()){
                return buildFailResponse(result.getMessage());
            }
            return buildSuccessResponse(result,request);
        } catch (SDKException e) {
            log.error("yz getCommentList error, request->{}, exception:", request, e);
            return buildFailResponse(e.getMessage());
        } catch (Exception e) {
            log.error("yz getCommentList error, request->{}, exception:", request, e);
            return buildFailResponse(e.getMessage());
        }
    }

    private CommentListQueryResponse buildSuccessResponse(YouzanItemEvaluationQueryResult result, CommentListQueryRequest request) {
        CommentListQueryResponse response = new CommentListQueryResponse();
        result.getData().getPageSize();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalNum(result.getData().getTotalCount());
        response.setPageInfo(pageInfo);
        response.setStatus(ResultGenerator.genSuccessResult());
        response.setCommentDTOList(result.getData().getItems().stream().map(item->buildChannelCOmmentDTO(item,request)).collect(Collectors.toList()));
        return response;
    }

    private CommentListQueryResponse buildFailResponse(String message) {
        CommentListQueryResponse response = new CommentListQueryResponse();
        return response.setStatus(ResultGenerator.genFailResult(message));
    }

    private YouzanItemEvaluationQueryParams buildRequestParams(CommentListQueryRequest request, AppMessage appMessage) {
        YouzanItemEvaluationQueryParams youzanItemEvaluationQueryParams = new YouzanItemEvaluationQueryParams();
        youzanItemEvaluationQueryParams.setTid(request.getChannelCommentId());
        youzanItemEvaluationQueryParams.setNodeKdtId(Long.parseLong(appMessage.getGrantId()));
        youzanItemEvaluationQueryParams.setStartCreated(DateUtil.parse(request.getStartTime(),DateUtil.YYYY_MM_DD_HH_MM_SS));
        youzanItemEvaluationQueryParams.setEndCreated(DateUtil.parse(request.getEndTime(),DateUtil.YYYY_MM_DD_HH_MM_SS));
        youzanItemEvaluationQueryParams.setPageNo(request.getPageNum());
        youzanItemEvaluationQueryParams.setPageSize(request.getPageSize());
        return youzanItemEvaluationQueryParams;
    }

    private ChannelCommentDTO buildChannelCOmmentDTO(YouzanItemEvaluationQueryResult.YouzanItemEvaluationQueryResultItems item, CommentListQueryRequest request) {
        ChannelCommentDTO channelCommentDTO = new ChannelCommentDTO();
        channelCommentDTO.setCommentId(String.valueOf(item.getId()));
        channelCommentDTO.setCommentContent(item.getContent());
        channelCommentDTO.setCommentTime(DateUtil.format(item.getCreatedAt(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        channelCommentDTO.setOrderId(request.getChannelCommentId());
        channelCommentDTO.setCommentLevel(ChannelCommentConverterUtil.calculateCommentLevel(
                request.getTenantId(), ChannelTypeEnum.YOU_ZAN.getCode(), item.getRate(), item.getCreatedAt()));
        channelCommentDTO.setOrderScore(item.getRate());
        channelCommentDTO.setQualityScore(item.getDescRate()==null?0:item.getDescRate());
        channelCommentDTO.setPackingScore(ProjectConstant.COMMENT_THRIFT_INT_VALUE_NULL);
        channelCommentDTO.setReplyStatus(ProjectConstant.COMMENT_MT_REPLY_STATUS_0);
        channelCommentDTO.setDeliveryScore(item.getLogisticsRate()==null?0:item.getLogisticsRate());
        channelCommentDTO.setCommentPictures(item.getBuyerPicture());
        channelCommentDTO.setOrderId(item.getTid());
        channelCommentDTO.setOrderItemList(Arrays.asList(item.getItemTitle()));
        //处理回复，时间取最大的，内容全部记录
        if(CollectionUtils.isNotEmpty(item.getItemEvaluationReplays())){
            channelCommentDTO.setReplyStatus(ProjectConstant.COMMENT_MT_REPLY_STATUS_1);
            List<YouzanItemEvaluationQueryResult.YouzanItemEvaluationQueryResultItemevaluationreplays> replyList = item.getItemEvaluationReplays();
            channelCommentDTO.setReplyTime(replyList
                    .stream()
                        .map(YouzanItemEvaluationQueryResult.YouzanItemEvaluationQueryResultItemevaluationreplays::getCreatedAt)
                        .sorted()
                        .map(date->DateUtil.format(date,DateUtil.YYYY_MM_DD_HH_MM_SS))
                        .findFirst().get());
            List<CommentContentDTO> baichuanCommentContentList = replyList.stream().map(this::convertToCommentContentDTO).collect(Collectors.toList());
            channelCommentDTO.setReplyContent(GsonUtils.toJSONString(baichuanCommentContentList));
        }
        //处理追评，时间取最大的，内容全部记录
        if(CollectionUtils.isNotEmpty(item.getItemAdditionalComments())){
            List<YouzanItemEvaluationQueryResult.YouzanItemEvaluationQueryResultItemadditionalcomments> addCommentList = item.getItemAdditionalComments();

            channelCommentDTO.setAddCommentTime(addCommentList
                    .stream()
                    .map(YouzanItemEvaluationQueryResult.YouzanItemEvaluationQueryResultItemadditionalcomments::getCreatedAt)
                    .sorted()
                    .map(date->DateUtil.format(date,DateUtil.YYYY_MM_DD_HH_MM_SS))
                    .findFirst().get());
            List<CommentContentDTO> baichuanCommentContentList = addCommentList.stream().map(this::convertToCommentContentDTO).collect(Collectors.toList());
            channelCommentDTO.setAddCommentContent(GsonUtils.toJSONString(baichuanCommentContentList));
        }
        return channelCommentDTO;
    }

    private CommentContentDTO convertToCommentContentDTO(YouzanItemEvaluationQueryResult.YouzanItemEvaluationQueryResultItemevaluationreplays youzanItemEvaluationQueryResultItemevaluationreplays) {
        CommentContentDTO commentContentDTO = new CommentContentDTO();
        commentContentDTO.setCommentContent(youzanItemEvaluationQueryResultItemevaluationreplays.getContent());
        commentContentDTO.setCommentTime(DateUtil.format(youzanItemEvaluationQueryResultItemevaluationreplays.getCreatedAt(),DateUtil.YYYY_MM_DD_HH_MM_SS));
        commentContentDTO.setCommentId(String.valueOf(youzanItemEvaluationQueryResultItemevaluationreplays.getId()));
        return commentContentDTO;
    }

    private CommentContentDTO convertToCommentContentDTO(YouzanItemEvaluationQueryResult.YouzanItemEvaluationQueryResultItemadditionalcomments youzanItemEvaluationQueryResultItemadditionalcomments) {
        CommentContentDTO commentContentDTO = new CommentContentDTO();
        commentContentDTO.setCommentContent(youzanItemEvaluationQueryResultItemadditionalcomments.getContent());
        commentContentDTO.setCommentTime(DateUtil.format(youzanItemEvaluationQueryResultItemadditionalcomments.getCreatedAt(),DateUtil.YYYY_MM_DD_HH_MM_SS));
        commentContentDTO.setCommentId(String.valueOf(youzanItemEvaluationQueryResultItemadditionalcomments.getId()));
        commentContentDTO.setPictureList(youzanItemEvaluationQueryResultItemadditionalcomments.getPicturesList());
        return commentContentDTO;
    }

    @Override
    public CommentReplyResponse reply(CommentReplyRequest request) {
        CommentReplyResponse response = new CommentReplyResponse();
        try {
            //创建参数对象,并设置参数
            YouzanItemEvaluationRepalyParams youzanItemEvaluationRepalyParams = new YouzanItemEvaluationRepalyParams();
            youzanItemEvaluationRepalyParams.setSellerComment(request.getReplyContent());
            youzanItemEvaluationRepalyParams.setId(Long.parseLong(request.getChannelCommentId()));

            AppMessage appMessage = getSubAppMessage(request.getTenantId(),request.getStoreId());

            YouzanItemEvaluationRepalyResult result = getResult4YouZan(appMessage,new YouzanItemEvaluationRepaly(youzanItemEvaluationRepalyParams),YouzanItemEvaluationRepalyResult.class);
            if(!result.getSuccess()){
                return response.setStatus(ResultGenerator.genFailResult(result.getMessage()));
            }
            return response.setStatus(ResultGenerator.genSuccessResult());

        } catch (SDKException e) {
            log.error("yz reply error, request->{}, exception:", request, e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("yz getCommentList error, request->{}, exception:", request, e);
            return response.setStatus(ResultGenerator.genFailResult(e.getMessage()));
        }
    }
}
