package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.CopAccessConfigMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/01/17
 */
@Service
public class JddjChannelAppIdUtils {

    @Autowired
    private CopAccessConfigMapper copAccessConfigMapper;

    @Autowired
    private TenantService tenantService;

    public int safeGetChannelAppId(Long tenantId, Integer appId) {
        if (appId != null && appId > 0) {
            return appId;
        }
        List<Integer> appIdList = copAccessConfigMapper.queryChannelAppIds(tenantId, EnhanceChannelType.JDDJ.getChannelId());
        return appIdList.get(0);
    }

    public int safeGetChannelAppId(Long tenantId, Long appId) {
        if (appId != null && appId > 0) {
            return appId.intValue();
        }
        List<Integer> appIdList = copAccessConfigMapper.queryChannelAppIds(tenantId, EnhanceChannelType.JDDJ.getChannelId());
        return appIdList.get(0);
    }

}

