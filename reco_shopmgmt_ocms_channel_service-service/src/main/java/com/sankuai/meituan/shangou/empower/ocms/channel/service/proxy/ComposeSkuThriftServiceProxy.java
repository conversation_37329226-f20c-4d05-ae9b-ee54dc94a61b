package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.ComposeSkuRelation;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.TenantStoreKey;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.PoiComposeSkuIdListCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.QueryPoiComposeSkuListResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerPoiComposeSkuThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: goulei02
 * @date: 2024/9/2
 */
@Slf4j
@Service
public class ComposeSkuThriftServiceProxy {

    @Resource
    private EmpowerPoiComposeSkuThriftService.Iface empowerPoiComposeSkuThriftService;

    public List<ComposeSkuRelation> queryComposeRelationByComposeSku(TenantStoreKey tenantStoreKey, List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }

        List<ComposeSkuRelation> composeSkuRelations = Lists.newArrayList();

        Lists.partition(skuIds, 50).forEach(partSkuIds -> {
            PoiComposeSkuIdListCommand cmd = new PoiComposeSkuIdListCommand();
            cmd.setTenantId(tenantStoreKey.getTenantId());
            cmd.setStoreId(tenantStoreKey.getStoreId());
            cmd.setSkuIdList(partSkuIds);
            QueryPoiComposeSkuListResult result;
            try {
                log.info("empowerPoiComposeSkuThriftService queryPoiComposeSkuBySkuIds cmd:{}", cmd);
                result = empowerPoiComposeSkuThriftService.queryPoiComposeSkuBySkuIds(cmd);
                log.info("empowerPoiComposeSkuThriftService queryPoiComposeSkuBySkuIds result:{}", result);
            } catch (Exception e) {
                throw new ChannelBizException("根据组合品获取组合品关系异常", e);
            }
            if (result == null || result.getStatus() == null || result.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                throw new ChannelBizException("根据组合品获取组合品关系失败");
            }
            List<ComposeSkuRelation> innerRelations = Optional.ofNullable(result.getSkuList()).orElse(Collections.emptyList()).stream().map(it -> {
                ComposeSkuRelation composeSkuRelation = new ComposeSkuRelation();
                composeSkuRelation.setParentSkuId(it.getSkuId());
                List<ComposeSkuRelation.ChildSkuRatio> childSkuRatioList = Optional.ofNullable(it.getChildSkuList()).orElse(Collections.emptyList()).stream().map(child -> {
                    ComposeSkuRelation.ChildSkuRatio childSkuRatio = new ComposeSkuRelation.ChildSkuRatio();
                    childSkuRatio.setChildSkuId(child.getSkuId());
                    childSkuRatio.setRatio(child.getAmount());
                    return childSkuRatio;
                }).collect(Collectors.toList());
                composeSkuRelation.setChildSkuRatioList(childSkuRatioList);
                return composeSkuRelation;
            }).collect(Collectors.toList());

            composeSkuRelations.addAll(innerRelations);
        });
        return composeSkuRelations;
    }
}
