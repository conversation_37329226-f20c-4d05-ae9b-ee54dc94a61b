package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.base.Preconditions;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.BatchRedistributeProductDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.BatchRedistributeProductResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelCreateSubProductResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelEditSkuPriceDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelEditSkuStockDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelGetDistributedProductResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelGetProductDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelProductDetailResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelSpuInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelSpuShelfDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.GetDistributedProductDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.QuerySpuListParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.QuerySpuListResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.StoreProductDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SkuChangeTypeEunm;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSpuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.tenant.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BasePageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSingleSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchUpdateCustomSpuStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelSpuDistributeInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetHeadQuarterSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAppFoodCodeBySkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAuditStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAuditStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryChannelSpuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryChannelSpuIdResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryNormAuditDelSpuRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryNormAuditDelSpuResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryQualityProblemRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryQualityProblemResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSkuInSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuSellStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuSellStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuId2AppFoodCodeResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuSellStatusInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStoreCategoryInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuWeightInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SubmitAppealInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuIdByNameAndSpecRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuIdByOriginIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConcurrentUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultSpuDataUtils;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.SaleStatusEnum;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;


/**
 * @Author: chenmingxuan02
 * @Date: 2023/12/19 11:01 上午
 * @Mail: <EMAIL>
 */
@Slf4j
@Service("dyChannelSpuService")
public class DouyinChannelSpuServiceImpl implements ChannelSpuService {
    /**
     * 主品在门店只能发布一次
     */
    private static final int STORE_DISTRIBUTE_ONCE = 1;

    // 抖音上线工具同步商品rhino动态线程池
    private static ThreadPoolExecutor executor = Rhino.newThreadPool("douyin_push_store_product_thread_pool",
            DefaultThreadPoolProperties.Setter()
            .withCoreSize(10).withMaxSize(20).withBlockingQueue(new SynchronousQueue<>())
            .withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy())
            .withThreadFactory(new ThreadFactoryBuilder().setNameFormat("douyin_push_store_product_thread_pool" + "-%d").build()))
            .getExecutor();

    @Autowired
    private DouyinChannelGateService douyinChannelGateService;

    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Autowired
    private BaseConverterService baseConverterService;

    @Autowired
    private TenantRemoteService tenantRemoteService;

    @Override
    public ResultSpuData spuCreate(SpuInfoRequest request) {
        return null;
    }


    @Override
    public ResultSpuData spuCreateForCleaner(SpuInfoRequest request) {
        return spuCreateCommon(request);
    }


    private ResultSpuData spuCreateCommon(SpuInfoRequest request){
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        List<Callable<ResultSingleSpuData>> callables = new ArrayList<>();
        for (SpuInfoDTO spuInfoDTO : request.getParamList()) {
            SingleSpuInfoRequest spuInfoRequest = new SingleSpuInfoRequest();
            spuInfoRequest.setBaseInfo(request.getBaseInfo());
            spuInfoRequest.setParam(spuInfoDTO);
            callables.add(() -> {
                try {
                    return createSingleSpu(spuInfoRequest);
                } catch (IllegalArgumentException e) {
                    log.warn("DouyinChannelSpuServiceImpl.spuCreateForCleaner, 参数校验失败, request:{}", request, e);
                    return ResultGenerator.genResultSingleSpuData(ResultCode.INVALID_PARAM, e.getMessage());
                } catch (Exception e) {
                    log.error("DouyinChannelSpuServiceImpl.spuCreateForCleaner, 服务异常, request:{}", request, e);
                    return ResultGenerator.genResultSingleSpuData(ResultCode.UNKNOWN_ERROR, e.getMessage());
                }
            });
        }

        List<ResultSingleSpuData> spuCreateResultList = ConcurrentUtils.concurrentExecute(executor, callables,
                "创建门店商品异常");
        if (CollectionUtils.isNotEmpty(spuCreateResultList)) {
            for (ResultSingleSpuData resultSingleSpuData : spuCreateResultList) {
                if (resultSingleSpuData.getSucData() != null) {
                    resultData.addToSucData(resultSingleSpuData.getSucData());
                }
                if (resultSingleSpuData.getErrorData() != null) {
                    resultData.addToErrorData(resultSingleSpuData.getErrorData());
                }
            }
        }
        return resultData;
    }

    @Override
    public ResultSingleSpuData createSingleSpu(SingleSpuInfoRequest request) {
        ResultSingleSpuData resultData = ResultGenerator.genResultSingleSpuData(ResultCode.SUCCESS);
        SpuInfoDTO spuInfoDTO = request.getParam();
        Preconditions.checkArgument(StringUtils.isNotBlank(spuInfoDTO.getMerchantChannelSpuId()), "merchantChannelSpuId is blank");
        Preconditions.checkArgument(StringUtils.isNotBlank(spuInfoDTO.getCustomSpuId()), "customSpuId is blank");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(request.getBaseInfo().getStoreIdList()), "storeIdList is empty");

        SpuKey spuKey = this.buildSpuKey(spuInfoDTO);

        Long storeId = request.getBaseInfo().getStoreIdList().get(0);
        // copy 一个baseRequest，删除storeIds，否则sendPost会返回Map对象
        BaseRequest baseRequest = new BaseRequest(request.getBaseInfo()).setStoreIdList(null);
        Long channelStoreId = getChannelStoreId(baseRequest, storeId);
        try {
            // 铺货到店品
            ResultErrorSpu resultErrorSpu = doCreateSubProduct(baseRequest, spuInfoDTO, channelStoreId, spuKey);
            if (Objects.nonNull(resultErrorSpu)) {
                return resultData.setErrorData(resultErrorSpu);
            }

            // 查询并映射渠道 spuAndSkuId
            resultErrorSpu = queryAndMappingChannelSpuAndSkuId(baseRequest, spuInfoDTO, channelStoreId, spuKey);
            if (Objects.nonNull(resultErrorSpu)) {
                return resultData.setErrorData(resultErrorSpu);
            }

            // 同步sku价格和库存
            resultErrorSpu = syncSkuPriceAndStock(baseRequest, spuInfoDTO, spuInfoDTO.getSkus(), spuKey);
            if (Objects.nonNull(resultErrorSpu)) {
                return resultData.setErrorData(resultErrorSpu);
            }

            // 同步spu上下架
            resultData = syncSellStatusOfSpu(baseRequest, storeId, spuInfoDTO, spuKey);

            checkCreateOrUpdateSucData(resultData);
        } catch (IllegalArgumentException e) {
            log.error("DouyinChannelSpuServiceImpl.createSingleSpu 参数校验失败, data:{}", spuInfoDTO, e);
            ResultSpuDataUtils.combineExceptionData(resultData, e.getMessage(), spuKey);
        } catch (Exception e) {
            log.error("DouyinChannelSpuServiceImpl.createSingleSpu 服务异常, data:{}", spuInfoDTO, e);
            ResultSpuDataUtils.combineExceptionData(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), spuKey);
        }

        return resultData;
    }

    /**
     * 创建门店品
     * @param baseRequest
     * @param spuInfoDTO
     * @param channelStoreId
     */
    private ResultErrorSpu doCreateSubProduct(BaseRequest baseRequest, SpuInfoDTO spuInfoDTO, Long channelStoreId, SpuKey spuKey) {
        ChannelResponseDTO<ChannelCreateSubProductResult> response = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.CREATE_SUB_PRODUCT,
                baseRequest, DouyinConvertUtil.spuCreateMapping(spuInfoDTO, channelStoreId));

        Preconditions.checkArgument(Objects.nonNull(response), "Create sub product result is null");
        if (!isCreateSubProductSuccess(response)) {
            return new ResultErrorSpu().setStoreId(-1).setSpuInfo(spuKey).setErrorMsg(response.getSub_msg()).setChannelUnifyError(response.getUnifyUnifiedErrorEnum());
        }
        return null;
    }

    private boolean isCreateSubProductSuccess(ChannelResponseDTO<ChannelCreateSubProductResult> response) {
        return response.isSuccess() || MccConfigUtil.getDouyinCreateSubProductError().stream().anyMatch(err -> StringUtils.contains(response.getSub_msg(), err));
    }

    /**
     * 查询渠道的铺品数据，并将渠道的spuId和skuId映射到结果
     *
     * @param baseRequest
     * @param spuInfoDTO
     * @param spuKey
     */
    private ResultErrorSpu queryAndMappingChannelSpuAndSkuId(BaseRequest baseRequest, SpuInfoDTO spuInfoDTO, Long channelStoreId,
                                                             SpuKey spuKey) {
        // 查询铺品结果
        ChannelResponseDTO<ChannelGetDistributedProductResult> getDistributedResponse =
                douyinChannelGateService.sendPost(ChannelPostDouyinEnum.GET_DISTRIBUTED_PRODUCT,
                        baseRequest, DouyinConvertUtil.buildGetDistributedProductDTO(spuInfoDTO, channelStoreId));
        Preconditions.checkArgument(Objects.nonNull(getDistributedResponse), "Query distributed products result is null");
        if (!getDistributedResponse.isSuccess()) {
            return new ResultErrorSpu().setStoreId(-1).setSpuInfo(spuKey).setErrorMsg(getDistributedResponse.getSub_msg()).setChannelUnifyError(getDistributedResponse.getUnifyUnifiedErrorEnum());
        }

        // 校验必须只能又一个铺品门店品
        List<ChannelGetDistributedProductResult.StoreProduct> storeProducts = getDistributedResponse.getCoreData().getStore_products();
        if (CollectionUtils.isEmpty(storeProducts) || STORE_DISTRIBUTE_ONCE != storeProducts.size()) {
            return new ResultErrorSpu().setStoreId(-1).setSpuInfo(spuKey).setErrorMsg(DouyinConstant.STORE_PRODUCT_NOT_EXIST).setChannelUnifyError(ProductChannelUnifyErrorEnum.STORE_SPU_NOT_EXIST);
        }
        ChannelGetDistributedProductResult.StoreProduct storeProduct = storeProducts.get(0);

        // 映射渠道spuId
        String storeSpuId = String.valueOf(storeProduct.getStore_product_id());
        spuKey.setChannelSpuId(storeSpuId);
        spuInfoDTO.setChannelSpuId(storeSpuId);

        // 本次操作涉及的sku信息映射渠道skuId
        Map<String, SkuInSpuInfoDTO> merchantChannelSkuId2SkuInfoMap = Fun.toMap(spuInfoDTO.getSkus(), SkuInSpuInfoDTO::getMerchantChannelSkuId);
        Map<String, SkuKey> customSkuId2SkuKeyMap = Fun.toMap(spuKey.getSkus(), SkuKey::getCustomSkuId);
        for (ChannelGetDistributedProductResult.SkuMapping skuMapping : storeProduct.getSku_mapping()) {
            String mainSkuId = String.valueOf(skuMapping.getMain_sku_id());
            String storeSkuId = String.valueOf(skuMapping.getStore_sku_id());

            //取出抖音侧规格对应的本次操作牵牛花sku信息
            SkuInSpuInfoDTO skuInSpuInfo = merchantChannelSkuId2SkuInfoMap.get(mainSkuId);
            if (skuInSpuInfo != null){
                //更新的规格需要保证牵牛花侧的channelSkuId与抖音侧一致
                if (SkuChangeTypeEunm.UPDATE.getCode() == skuInSpuInfo.getChangeType()){
                    if (!Objects.equals(skuInSpuInfo.getChannelSkuId(), storeSkuId)){
                        return new ResultErrorSpu().setStoreId(-1).setSpuInfo(spuKey).setErrorMsg(DouyinConstant.STORE_PRODUCT_NOT_EXIST).setChannelUnifyError(ProductChannelUnifyErrorEnum.STORE_SPU_NOT_EXIST);
                    }
                }

                skuInSpuInfo.setChannelSkuId(storeSkuId);
                //skuKey中也需要回填抖音channelSkuId
                Optional.ofNullable(customSkuId2SkuKeyMap.get(skuInSpuInfo.getCustomSkuId()))
                        .ifPresent(skuKey -> skuKey.setChannelSkuId(storeSkuId));
            }
        }
        return null;
    }

    /**
     * 同步新增的规格库存和价格
     * @param baseRequest
     * @param skus 需要同步的规格
     * @param spuKey
     * @return
     */
    private ResultErrorSpu syncSkuPriceAndStock(BaseRequest baseRequest, SpuInfoDTO spuInfoDTO, List<SkuInSpuInfoDTO> skus, SpuKey spuKey) {
        for (SkuInSpuInfoDTO skuInSpuInfoDTO : skus) {
            Preconditions.checkArgument(StringUtils.isNotBlank(skuInSpuInfoDTO.getChannelSkuId()), "channelSkuId is null of skuId: %s",
                    Optional.ofNullable(skuInSpuInfoDTO.getSkuId()).orElse(skuInSpuInfoDTO.getCustomSkuId()));

            ChannelResponseDTO result = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SKU_EDIT_PRICE,
                    baseRequest,
                    ChannelEditSkuPriceDTO.builder()
                            .price((long) MoneyUtils.yuanToFen(skuInSpuInfoDTO.getPrice()))
                            .product_id(Optional.ofNullable(spuInfoDTO.getChannelSpuId()).map(Long::valueOf).orElse(null))
                            .sku_id(Long.valueOf(skuInSpuInfoDTO.getChannelSkuId())).build());
            Preconditions.checkArgument(Objects.nonNull(result), "Edit sku price result is null");
            if (!result.isSuccess()) {
                return new ResultErrorSpu().setStoreId(-1).setSpuInfo(spuKey).setErrorMsg("Edit sku price error:" + result.getSub_msg()).setChannelUnifyError(result.getUnifyUnifiedErrorEnum());
            }

            result = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SKU_SYNC_STOCK,
                    baseRequest,
                    ChannelEditSkuStockDTO.builder()
                            .stock_num((long) skuInSpuInfoDTO.getStock())
                            .product_id(Optional.ofNullable(spuInfoDTO.getChannelSpuId()).map(Long::valueOf).orElse(null))
                            .sku_id(Long.valueOf(skuInSpuInfoDTO.getChannelSkuId())).build());
            Preconditions.checkArgument(Objects.nonNull(result), "Sync sku stock result is null");
            if (!result.isSuccess()) {
                return new ResultErrorSpu().setStoreId(-1).setSpuInfo(spuKey).setErrorMsg("Sync sku stock error:" + result.getSub_msg()).setChannelUnifyError(result.getUnifyUnifiedErrorEnum());
            }
        }
        return null;
    }

    /**
     * 删除规格库存置空
     * @param baseRequest
     * @param channelSkuIds
     * @param spuKey
     * @return
     */
    private ResultErrorSpu setStockToZero(BaseRequest baseRequest, List<String> channelSkuIds, SpuKey spuKey) {
        for (String channelSkuId : channelSkuIds) {
            ChannelResponseDTO result = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SKU_SYNC_STOCK,
                    baseRequest, ChannelEditSkuStockDTO.builder()
                            .stock_num(0l)
                            .sku_id(Long.valueOf(channelSkuId)).build());
            Preconditions.checkArgument(Objects.nonNull(result), "Sync sku stock result is null");
            if (!result.isSuccess()) {
                return new ResultErrorSpu().setStoreId(-1).setSpuInfo(spuKey).setErrorMsg("Sync sku stock error:" + result.getSub_msg()).setChannelUnifyError(result.getUnifyUnifiedErrorEnum());
            }
        }
        return null;
    }


    private ResultSingleSpuData syncSellStatusOfSpu(BaseRequest baseRequest, Long storeId, SpuInfoDTO spuInfoDTO,
                                                    SpuKey spuKey) {
        int sellStatus = spuInfoDTO.getStatus();
        SingleSpuSellStatusDTO param = new SingleSpuSellStatusDTO();
        param.setCustomSpuId(spuKey.getCustomSpuId());
        param.setChannelSpuId(spuKey.getChannelSpuId());
        param.setMerchantChannelSpuId(spuInfoDTO.getMerchantChannelSpuId());
        param.setStoreId(storeId);
        param.setSpuKey(spuKey);
        param.setSpuStatus(sellStatus);

        BaseRequestSimple baseRequestSimple = baseConverterService.baseSimpleRequest(baseRequest);
        return updateSingleSpuSellStatus(new SingleSpuSellStatusRequest(baseRequestSimple, param));
    }

    /**
     * 检测渠道的spuid和skuid都不为空
     * @param resultData
     */
    private static void checkCreateOrUpdateSucData(ResultSingleSpuData resultData) {
        if (Objects.isNull(resultData.getSucData())) {
            return;
        }

        String channelSpuId = Optional.ofNullable(resultData.getSucData())
                .map(ResultSuccessSpu::getSpuInfo)
                .map(SpuKey::getChannelSpuId).orElse(null);
        Preconditions.checkArgument(StringUtils.isNotBlank(channelSpuId), "Sub product channelSpuId is blank");

        Long noneChannelSkuCount =
                Optional.ofNullable(resultData.getSucData())
                        .map(ResultSuccessSpu::getSpuInfo)
                        .map(SpuKey::getSkus)
                        .map(skus -> skus.stream().filter(r -> StringUtils.isBlank(r.getChannelSkuId())).count())
                        .orElse(0L);
        Preconditions.checkArgument(noneChannelSkuCount == 0L, "Having skus without channelSkuId");
    }

    @Override
    public ResultSpuData distributeToStoreAsync(ChannelSpuDistributeInfoRequest request) {
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        Preconditions.checkArgument(request.getSpuKey() != null, "spu key is null");
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getSpuKey().getMerchantChannelSpuId()), "merchantChannelSpuId is null");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(request.getStoreIds()), "storeIds is empty");
        Preconditions.checkArgument(request.getStoreIds().size() <= 50, "storeIds over size");

        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        // 查询渠道门店id
        Map<String, ChannelStoreDO> channelPoiCodeMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo().getTenantId(),
                request.getBaseInfo().getChannelId(),
                request.getStoreIds());

        if (MapUtils.isEmpty(channelPoiCodeMap)) {
            List<ResultErrorSpu> errorSpus = Fun.map(request.getStoreIds(), storeId -> {
                ResultErrorSpu resultErrorSpu = new ResultErrorSpu();
                resultErrorSpu.setStoreId(storeId);
                resultErrorSpu.setSpuInfo(request.getSpuKey());
                resultErrorSpu.setErrorMsg("channel online poi id is empty");
                resultErrorSpu.setErrorCode(ResultCode.FAIL.getCode());
                return resultErrorSpu;
            });
            resultData.setErrorData(errorSpus);
            return resultData;
        }

        BatchRedistributeProductDTO batchRedistributeProductDTO = BatchRedistributeProductDTO
                .builder()
                .task_params(StoreProductDTO
                        .builder()
                        .main_product_id(request.getSpuKey().getMerchantChannelSpuId())
                        .add_store_ids(Fun.map(channelPoiCodeMap.values(), channelStoreDO -> Long.valueOf(channelStoreDO.getChannelOnlinePoiCode())))
                        .build())
                .build();
        // 查询铺品结果
        ChannelResponseDTO<BatchRedistributeProductResult> distributedResponse =
                douyinChannelGateService.sendPost(ChannelPostDouyinEnum.BATCH_DISTRIBUTED_PRODUCT,
                        baseRequest, batchRedistributeProductDTO);

        if (!distributedResponse.isSuccess()) {
            resultData.setStatus(new ResultStatus().setMsg(distributedResponse.getAggregateErrorMsg()).setCode(distributedResponse.getCode()));
            return resultData;
        }

        if (distributedResponse.getCoreData() == null) {
            resultData = ResultGenerator.genResultSpuData(ResultCode.CHANNEL_RESPONSE_NULL);
            return resultData;
        }

        List<ResultSuccessSpu> successSpus = Fun.map(request.getStoreIds(), storeId -> {
            ResultSuccessSpu resultSuccessSpu = new ResultSuccessSpu();
            resultSuccessSpu.setStoreId(storeId);
            resultSuccessSpu.setSpuInfo(request.getSpuKey());
            return resultSuccessSpu;
        });
        resultData.setSucData(successSpus);
        return resultData;
    }

    @Override
    public ResultSpuData getStoreDistributeStatus(ChannelSpuDistributeInfoRequest request){

        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        Preconditions.checkArgument(request.getSpuKey() != null, "spu key is null");
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getSpuKey().getMerchantChannelSpuId()), "merchantChannelSpuId is null");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(request.getStoreIds()), "storeIds is empty");
        Preconditions.checkArgument(request.getStoreIds().size() <= 50, "storeIds over size");

        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        // 查询渠道门店id
        Map<String, ChannelStoreDO> channelPoiCodeMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo().getTenantId(),
                request.getBaseInfo().getChannelId(),
                request.getStoreIds());

        if (MapUtils.isEmpty(channelPoiCodeMap)) {
            return resultData;
        }

        GetDistributedProductDTO getDistributedProductDTO = new GetDistributedProductDTO();
        getDistributedProductDTO.setMain_product_id(Long.valueOf(request.getSpuKey().getMerchantChannelSpuId()));
        getDistributedProductDTO.setStore_ids(Fun.map(channelPoiCodeMap.values(), channelStoreDO -> Long.valueOf(channelStoreDO.getChannelOnlinePoiCode())));
        getDistributedProductDTO.setPage(0L);
        getDistributedProductDTO.setSize(50L);
        // 查询铺品结果
        ChannelResponseDTO<ChannelGetDistributedProductResult> getDistributedResponse =
                douyinChannelGateService.sendPost(ChannelPostDouyinEnum.GET_DISTRIBUTED_PRODUCT,
                        baseRequest, getDistributedProductDTO);

        if (!getDistributedResponse.isSuccess()) {
            resultData.setStatus(new ResultStatus().setMsg(getDistributedResponse.getAggregateErrorMsg()).setCode(getDistributedResponse.getCode()));
            return resultData;
        }

        if (getDistributedResponse.getCoreData() == null) {
            resultData = ResultGenerator.genResultSpuData(ResultCode.CHANNEL_RESPONSE_NULL);
            return resultData;
        }

        if (CollectionUtils.isNotEmpty(getDistributedResponse.getCoreData().getStore_products())) {
            List<ResultSuccessSpu> successSpuList = new ArrayList<>();
            Map<String, Long> channelStoreMap = channelPoiCodeMap.values().stream().collect(Collectors.toMap(ChannelStoreDO::getChannelPoiCode,
                    ChannelStoreDO::getStoreId, (v1, v2) -> v2));
            for (ChannelGetDistributedProductResult.StoreProduct storeProduct : getDistributedResponse.getCoreData().getStore_products()) {

                if (storeProduct.getStore_info() == null) {
                    continue;
                }
                if (!channelStoreMap.containsKey(String.valueOf(storeProduct.getStore_info().getStore_id()))) {
                    continue;
                }
                Long storeId = channelStoreMap.get(String.valueOf(storeProduct.getStore_info().getStore_id()));
                ResultSuccessSpu successSpu = new ResultSuccessSpu();
                successSpu.setStoreId(storeId);
                SpuKey spuKey = new SpuKey();
                spuKey.setStoreId(storeId);
                spuKey.setChannelSpuId(String.valueOf(storeProduct.getStore_product_id()));
                spuKey.setMerchantChannelSpuId(String.valueOf(storeProduct.getMain_product_id()));
                if (CollectionUtils.isNotEmpty(storeProduct.getSku_mapping())) {
                    List<SkuKey> skus = new ArrayList<>();
                    for (ChannelGetDistributedProductResult.SkuMapping skuMapping : storeProduct.getSku_mapping()) {
                        SkuKey skuKey = new SkuKey();
                        skuKey.setChannelSkuId(String.valueOf(skuMapping.getStore_sku_id()));
                        skuKey.setMerchantChannelSkuId(String.valueOf(skuMapping.getMain_sku_id()));
                        skus.add(skuKey);
                    }
                    spuKey.setSkus(skus);
                }
                successSpu.setSpuInfo(spuKey);
                successSpuList.add(successSpu);
            }
            resultData.setSucData(successSpuList);
        }
        return resultData;
    }







    @Override
    public ResultSpuData upcCreate(SpuInfoRequest request) {
        return null;
    }

    @Override
    public ResultSpuData updateBySpuOrUpc(SpuInfoRequest request) {
        return null;
    }


    @Override
    public ResultSpuData updateBySpuOrUpcForCleaner(SpuInfoRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        List<Callable<ResultSingleSpuData>> callables = new ArrayList<>();
        for (SpuInfoDTO spuInfoDTO : request.getParamList()) {
            SingleSpuInfoRequest spuInfoRequest = new SingleSpuInfoRequest();
            spuInfoRequest.setBaseInfo(request.getBaseInfo());
            spuInfoRequest.setParam(spuInfoDTO);
            // 同步价格库存
            spuInfoRequest.setSyncAllSkuPriceAndStock(true);
            callables.add(() -> {
                try {
                    return updateSingleSpu(spuInfoRequest);
                } catch (IllegalArgumentException e) {
                    log.warn("DouyinChannelSpuServiceImpl.spuCreateForCleaner, 参数校验失败, request:{}", request, e);
                    return ResultGenerator.genResultSingleSpuData(ResultCode.INVALID_PARAM, e.getMessage());
                } catch (Exception e) {
                    log.error("DouyinChannelSpuServiceImpl.spuCreateForCleaner, 服务异常, request:{}", request, e);
                    return ResultGenerator.genResultSingleSpuData(ResultCode.UNKNOWN_ERROR, e.getMessage());
                }
            });
        }

        List<ResultSingleSpuData> spuCreateResultList = ConcurrentUtils.concurrentExecute(executor, callables,
                "更新门店商品异常");
        if (CollectionUtils.isNotEmpty(spuCreateResultList)) {
            for (ResultSingleSpuData resultSingleSpuData : spuCreateResultList) {
                if (resultSingleSpuData.getSucData() != null) {
                    resultData.addToSucData(resultSingleSpuData.getSucData());
                }
                if (resultSingleSpuData.getErrorData() != null) {
                    resultData.addToErrorData(resultSingleSpuData.getErrorData());
                }
            }
        }
        return resultData;
    }

    @Override
    public ResultSingleSpuData updateSingleSpu(SingleSpuInfoRequest request) {
        ResultSingleSpuData resultData = ResultGenerator.genResultSingleSpuData(ResultCode.SUCCESS);
        SpuInfoDTO spuInfoDTO = request.getParam();
        Preconditions.checkArgument(StringUtils.isNotBlank(spuInfoDTO.getMerchantChannelSpuId()), "merchantChannelSpuId is blank");
        Preconditions.checkArgument(StringUtils.isNotBlank(spuInfoDTO.getCustomSpuId()), "customSpuId is blank");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(request.getBaseInfo().getStoreIdList()), "storeIdList is empty");

        SpuKey spuKey = this.buildSpuKey(spuInfoDTO);

        Long storeId = request.getBaseInfo().getStoreIdList().get(0);
        // copy 一个baseRequest，删除storeIds，否则sendPost会返回Map对象
        BaseRequest baseRequest = new BaseRequest(request.getBaseInfo()).setStoreIdList(null);
        Long channelStoreId = getChannelStoreId(baseRequest, storeId);
        try {

            // 查询并映射渠道 spuAndSkuId，单规格无所谓，多规格一定要，这样才能获取默认铺品的新规格skuid
            ResultErrorSpu resultErrorSpu = queryAndMappingChannelSpuAndSkuId(baseRequest, spuInfoDTO, channelStoreId
                    , spuKey);
            if (Objects.nonNull(resultErrorSpu)) {
                return resultData.setErrorData(resultErrorSpu);
            }

            List<SkuInSpuInfoDTO> syncPriceAndStockSkus = null;
            if (BooleanUtils.isTrue(request.isSyncAllSkuPriceAndStock())) {
                syncPriceAndStockSkus = spuInfoDTO.getSkus();
            } else {
                //过滤出来创建的规格
                syncPriceAndStockSkus = Fun.filter(spuInfoDTO.getSkus(),
                        sku -> Objects.equals(sku.getChangeType(), SkuChangeTypeEunm.CREATE.getCode()));
            }

            // 同步新增的sku价格和库存
            if (CollectionUtils.isNotEmpty(syncPriceAndStockSkus)){
                resultErrorSpu = syncSkuPriceAndStock(baseRequest, spuInfoDTO, syncPriceAndStockSkus, spuKey);
                if (Objects.nonNull(resultErrorSpu)) {
                    return resultData.setErrorData(resultErrorSpu);
                }
            }

            // 同步spu上下架
            resultData = syncSellStatusOfSpu(baseRequest, storeId, spuInfoDTO, spuKey);
            checkCreateOrUpdateSucData(resultData);
        } catch (IllegalArgumentException e) {
            log.error("DouyinChannelSpuServiceImpl.updateSingleSpu 参数校验失败, data:{}", spuInfoDTO, e);
            ResultSpuDataUtils.combineExceptionData(resultData, e.getMessage(), spuKey);
        } catch (Exception e) {
            log.error("DouyinChannelSpuServiceImpl.updateSingleSpu 服务异常, data:{}", spuInfoDTO, e);
            ResultSpuDataUtils.combineExceptionData(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), spuKey);
        }
        return resultData;
    }

    @Override
    public ResultSpuData updateWeightBySpu(SpuWeightInfoRequest request) {
        return null;
    }

    @Override
    public ResultSpuData deleteSpu(SpuInfoDeleteRequest request) {
        return null;
    }

    @Override
    public ResultSingleSpuData deleteSingleSpu(SingleSpuInfoDeleteRequest request) {
        SpuInfoDeleteDTO spuDeleteParam = request.getParam();
        ResultSingleSpuData resultData = ResultGenerator.genResultSingleSpuData(ResultCode.SUCCESS);

        SpuKey spuKey = Optional.ofNullable(spuDeleteParam.getSpuKey()).orElse(this.getSpuKey(spuDeleteParam.getCustomSpuId()));

        try {
            ChannelResponseDTO postResult;
            if (request.isForceDelete()) {
                postResult = doHardDeleteSingleSpu(request, spuDeleteParam);
            }
            else {
                postResult = doSoftDeleteSingleSpu(request, spuDeleteParam);
            }
            Preconditions.checkArgument(Objects.nonNull(postResult), "Delete product (set offline) result is null");

            if (isDeleteSuccess(postResult)) {
                ResultSuccessSpu resultSuccessSpu = new ResultSuccessSpu().setChannelId(request.getBaseInfo().getChannelId())
                        .setStoreId(spuDeleteParam.getStoreId())
                        .setSpuInfo(spuKey);
                resultData.setSucData(resultSuccessSpu);
            } else {
                ResultErrorSpu resultErrorSpu = new ResultErrorSpu().setStoreId(-1).setSpuInfo(spuKey)
                        .setErrorMsg(postResult.getSub_msg()).setChannelUnifyError(postResult.getUnifyUnifiedErrorEnum());
                resultData.setErrorData(resultErrorSpu);
            }
        } catch (IllegalArgumentException e) {
            log.error("DouyinChannelSpuServiceImpl.deleteSingleSpu 参数校验失败, data:{}", spuDeleteParam, e);
            ResultSpuDataUtils.combineExceptionData(resultData, e.getMessage(), spuKey);
        } catch (Exception e) {
            log.error("DouyinChannelSpuServiceImpl.deleteSingleSpu 服务异常, data:{}", spuDeleteParam, e);
            ResultSpuDataUtils.combineExceptionData(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), spuKey);
        }

        return resultData;
    }

    /**
     * 硬删除门店品
     * @param request
     * @param spuDeleteParam
     * @return
     */
    private ChannelResponseDTO doHardDeleteSingleSpu(SingleSpuInfoDeleteRequest request, SpuInfoDeleteDTO spuDeleteParam) {
        ChannelSpuDeleteDTO channelSpuDeleteDTO = new ChannelSpuDeleteDTO();
        if (MccConfigUtil.isDouyinOperateSpuWithMerchantSpuId() && StringUtils.isNotBlank(spuDeleteParam.getMerchantChannelSpuId())) {
            channelSpuDeleteDTO.setProduct_id(Long.valueOf(spuDeleteParam.getMerchantChannelSpuId()));
            channelSpuDeleteDTO.setStore_id(getChannelStoreId(baseConverterService.baseRequest(request.getBaseInfo()),
                    spuDeleteParam.getStoreId()));
        }
        else {
            channelSpuDeleteDTO.setProduct_id(Long.valueOf(spuDeleteParam.getChannelSpuId()));
        }
        channelSpuDeleteDTO.setDelete_forever(true);
        ChannelResponseDTO postResult = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SPU_DELETE,
                baseConverterService.baseRequest(request.getBaseInfo()), channelSpuDeleteDTO);
        return postResult;
    }

    /**
     * 软删除门店品（下架）
     * @param request
     * @param spuDeleteParam
     * @return
     */
    private ChannelResponseDTO doSoftDeleteSingleSpu(SingleSpuInfoDeleteRequest request, SpuInfoDeleteDTO spuDeleteParam) {
        ChannelResponseDTO postResult;
        ChannelSpuShelfDTO channelSpuShelfDTO = new ChannelSpuShelfDTO();
        if (MccConfigUtil.isDouyinOperateSpuWithMerchantSpuId() && StringUtils.isNotBlank(spuDeleteParam.getMerchantChannelSpuId())) {
            channelSpuShelfDTO.setProduct_id(spuDeleteParam.getMerchantChannelSpuId());
            channelSpuShelfDTO.setStore_id(getChannelStoreId(baseConverterService.baseRequest(request.getBaseInfo()),
                    spuDeleteParam.getStoreId()));
        }
        else {
            channelSpuShelfDTO.setProduct_id(spuDeleteParam.getChannelSpuId());
        }
        postResult = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.PRODUCT_OFF_SHELF,
                baseConverterService.baseRequest(request.getBaseInfo()), channelSpuShelfDTO);
        return postResult;
    }

    private boolean isDeleteSuccess(ChannelResponseDTO postResult) {
        return postResult.isSuccess() || StringUtils.contains(postResult.getSub_msg(), DouyinConstant.DUPLICATE_OPERATION)
                || StringUtils.contains(postResult.getSub_msg(), DouyinConstant.PRODUCT_DELETED)
                || StringUtils.contains(postResult.getSub_msg(), DouyinConstant.SUB_PRODUCT_NOT_FOUND);
    }

    @Override
    public ResultSpuData deleteCategoryAndSpu(BaseRequest request) {
        return null;
    }

    @Override
    public ResultSpuData deleteSku(SkuInSpuInfoDeleteRequest request) {
        return null;
    }


    @Override
    public ResultSingleSpuData deleteSingleSku(SingleSkuInSpuInfoDeleteRequest request) {
        ResultSingleSpuData resultData = ResultGenerator.genResultSingleSpuData(ResultCode.SUCCESS);

        SkuInSpuInfoDeleteDTO param = request.getParam();
        SpuKey spuKey = new SpuKey();
        spuKey.setCustomSpuId(param.getCustomSpuId());
        SkuKey skuKey = new SkuKey();
        skuKey.setCustomSkuId(param.getCustomSkuId());
        spuKey.setSkus(Lists.newArrayList(skuKey));

        try {
            BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
            ResultErrorSpu resultErrorSpu = setStockToZero(baseRequest, Lists.newArrayList(param.getCustomSkuId()), spuKey);
            if (Objects.nonNull(resultErrorSpu)) {
                return resultData.setErrorData(resultErrorSpu);
            }
        } catch (IllegalArgumentException e) {
            log.error("DouyinChannelSpuServiceImpl.deleteSingleSku 参数校验失败, data:{}", param, e);
            ResultSpuDataUtils.combineExceptionData(resultData, e.getMessage(), spuKey);
        } catch (Exception e) {
            log.error("DouyinChannelSpuServiceImpl.deleteSingleSku 服务异常, data:{}", param, e);
            ResultSpuDataUtils.combineExceptionData(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), spuKey);
        }

        return resultData;
    }


    @Override
    public ResultSpuData updateSpuSellStatus(SpuSellStatusInfoRequest request) {
        return null;
    }

    @Override
    public ResultSingleSpuData updateSingleSpuSellStatus(SingleSpuSellStatusRequest request) {
        ResultSingleSpuData resultData = ResultGenerator.genResultSingleSpuData(ResultCode.SUCCESS);
        SingleSpuSellStatusDTO spuSellStatusParam = request.getParam();
        Preconditions.checkArgument(StringUtils.isNotBlank(spuSellStatusParam.getChannelSpuId()), "channelSpuId is blank");

        SpuKey spuKey = Optional.ofNullable(spuSellStatusParam.getSpuKey()).orElse(this.getSpuKey(spuSellStatusParam.getCustomSpuId()));

        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        try {
            ChannelResponseDTO postResult = null;
            switch (request.getParam().getSpuStatus()){
                case Constant.SPU_ON_SHELF:
                    postResult = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.PRODUCT_ON_SHELF,
                            baseRequest, DouyinConvertUtil.spuOnShelfMapping(spuSellStatusParam));
                    break;
                case Constant.SPU_OFF_SHELF:
                    if (MccConfigUtil.isDouyinOperateSpuWithMerchantSpuId() && StringUtils.isNotBlank(spuSellStatusParam.getMerchantChannelSpuId())){
                        // 基于渠道总部商品进行操作下架
                        ChannelSpuShelfDTO channelSpuShelfDTO = new ChannelSpuShelfDTO();
                        channelSpuShelfDTO.setProduct_id(spuSellStatusParam.getMerchantChannelSpuId());
                        channelSpuShelfDTO.setStore_id(getChannelStoreId(baseRequest, spuSellStatusParam.getStoreId()));
                        postResult = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.PRODUCT_OFF_SHELF,
                                baseRequest, channelSpuShelfDTO);
                    } else {
                        // 基于门店品进行商品操作下架
                        postResult = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.PRODUCT_OFF_SHELF,
                                baseRequest, DouyinConvertUtil.spuOffShelfMapping(spuSellStatusParam));
                    }
                    break;
                default:
                    throw new IllegalArgumentException("spuStatus is not valid");
            }
            Preconditions.checkArgument(Objects.nonNull(postResult), "Update spu sell status result is null");

            if (isOperationSuccess(postResult)) {
                ResultSuccessSpu resultSuccessSpu = new ResultSuccessSpu().setChannelId(request.getBaseInfo().getChannelId())
                        .setStoreId(spuSellStatusParam.getStoreId())
                        .setSpuInfo(spuKey);
                resultData.setSucData(resultSuccessSpu);
            } else {
                ResultErrorSpu resultErrorSpu = new ResultErrorSpu().setStoreId(spuSellStatusParam.getStoreId()).setSpuInfo(spuKey).setErrorMsg(postResult.getSub_msg()).setChannelUnifyError(postResult.getUnifyUnifiedErrorEnum());
                resultData.setErrorData(resultErrorSpu);
            }
        } catch (IllegalArgumentException e) {
            log.error("DouyinChannelSpuServiceImpl.updateSingleSpuSellStatus 参数校验失败, data:{}", spuSellStatusParam, e);
            ResultSpuDataUtils.combineExceptionData(resultData, e.getMessage(), spuKey);
        } catch (Exception e) {
            log.error("DouyinChannelSpuServiceImpl.updateSingleSpuSellStatus 服务异常, data:{}", spuSellStatusParam, e);
            ResultSpuDataUtils.combineExceptionData(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), spuKey);
        }

        return resultData;
    }

    private boolean isOperationSuccess(ChannelResponseDTO postResult) {
        return postResult.isSuccess() || StringUtils.contains(postResult.getSub_msg(), DouyinConstant.DUPLICATE_OPERATION);
    }

    @Override
    public ResultSpuData updateCustomSpuIdByOriginId(UpdateCustomSpuIdByOriginIdRequest request) {
        return null;
    }

    @Override
    public ResultSpuData updateCustomSpuIdByNameAndSpec(UpdateCustomSpuIdByNameAndSpecRequest request) {
        return null;
    }

    @Override
    public GetSpuInfoResponse getSpuInfo(GetSpuInfoRequest request) throws SDKException {
        ChannelGetProductDetailDTO getProductDetailDTO = new ChannelGetProductDetailDTO();

        // 非erp租户发送消息
        if (tenantRemoteService.isErpSpuTenant(request.getBaseInfo().getTenantId())) {
            Preconditions.checkArgument(StringUtils.isNotBlank(request.getSpuId()), "spuId is blank");
            // spuId就是渠道门店品的productId, biz稽核操作传输参数就是这么传的
            getProductDetailDTO.setProduct_id(Long.valueOf(request.getSpuId()));
        } else {
            Long channelStoreId = getChannelStoreId(baseConverterService.baseRequest(request.getBaseInfo()), request.getStoreId());
            getProductDetailDTO.setStore_id(channelStoreId);
            getProductDetailDTO.setOut_product_id(request.getCustomSpuId());
        }
        // 调用渠道接口
        ChannelResponseDTO<ChannelProductDetailResult> productResult =
                douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SPU_DETAIL, baseConverterService.baseRequest(request.getBaseInfo()),
                        getProductDetailDTO);

        Preconditions.checkArgument(Objects.nonNull(productResult), "Get product detail result is null");
        if (Optional.ofNullable(productResult.getSub_msg()).map(msg -> msg.contains(DouyinConstant.STORE_PRODUCT_NOT_EXIST)).orElse(false)) {
            GetSpuInfoResponse response = new GetSpuInfoResponse();
            response.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));
            response.setSpuInfo(null);
            return response;
        }

        Preconditions.checkArgument(productResult.isSuccess(), "Get product detail not success");

        ChannelProductDetailResult productDetail = productResult.getCoreData();
        // 转换sku字段
        List<SkuInSpuInfoDTO> skus =
                productDetail.getSpec_prices().stream().filter(Objects::nonNull).map(this::buildSkuInfoDTO).collect(Collectors.toList());

        // 转换spu字段
        SpuInfoDTO spuInfo = new SpuInfoDTO().setSkus(skus).setName(productDetail.getName());
        spuInfo.setChannelSpuId(String.valueOf(productDetail.getProduct_id())).setCustomSpuId(productDetail.getOuter_product_id());
        spuInfo.setStatus(Objects.equals(productDetail.getStatus(), DouyinConstant.DOUYIN_ONSALE_STATUS) ?
                SaleStatusEnum.ON_SALE.getCode() : SaleStatusEnum.OFF_SALE.getCode());

        GetSpuInfoResponse response = new GetSpuInfoResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));
        response.setSpuInfo(spuInfo);

        return response;
    }

    private SkuInSpuInfoDTO buildSkuInfoDTO(ChannelProductDetailResult.SpecPrice r) {
        Integer stock = 0;
        // 根据sku type获取总库存
        switch (r.getSku_type()) {
            case DouyinConstant.SKU_TYPE_0:
                stock = r.getStock_num().intValue();
                break;
            case DouyinConstant.SKU_TYPE_1:
                stock = Optional.ofNullable(r.getStock_num_map())
                        .map(Map::values).map(stocks -> stocks.stream().reduce(Long::sum).get())
                        .orElse(0L).intValue();
                break;
            default:
                log.warn("sku type of {} was not supported now", r.getSku_type());
                break;
        }
        return new SkuInSpuInfoDTO().setChannelSkuId(String.valueOf(r.getSku_id()))
                .setCustomSkuId(r.getOuter_sku_id())
                .setStock(stock)
                .setPrice(MoneyUtils.fenToYuan(r.getPrice()).doubleValue());
    }

    @Override
    public GetSpuInfosResponse getSpuInfoList(GetSpuInfosRequest request) {
        return null;
    }

    @Override
    public BatchGetSpuInfoResponse batchGetSpuInfo(BatchGetSpuInfoRequest request) {

        BatchGetSpuInfoResponse response = new BatchGetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        if (request == null || request.getBaseInfo() == null){
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "拉取抖音商品信息失败"));
        }
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        QuerySpuListParam param = QuerySpuListParam.of(request);
        // 查询总部商品不需要校验门店
        if (!request.isTenantProduct()) {
            Long channelStoreId = getChannelStoreId(baseRequest, request.getStoreId());
            param.setStore_id(channelStoreId);
        }
        // 构造参数
        ChannelResponseDTO<QuerySpuListResult> spuListResult = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SPU_LIST, baseRequest, param);

        if (!spuListResult.isSuccess()) {
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, spuListResult.getAggregateErrorMsg()));
            return response;
        }

        if (spuListResult.getCoreData() == null) {
            response.setSpuInfos(Collections.emptyList());
            response.setPageInfo(new PageInfo(request.getPageNum(), request.getPageSize(), 0, 0));
            return response;
        }

        if (CollectionUtils.isEmpty(spuListResult.getCoreData().getData())) {
            return buildBatchSpuInfoResponse(request, spuListResult.getCoreData(), response, Collections.emptyList());
        }

        List<SpuInfoDTO> spuInfoDTOList = Fun.map(spuListResult.getCoreData().getData(),
                spuChannelInfo -> ChannelSpuInfo.toDTO(spuChannelInfo, request.isTenantProduct()));
        return buildBatchSpuInfoResponse(request, spuListResult.getCoreData(), response, spuInfoDTOList);
    }

    private BatchGetSpuInfoResponse buildBatchSpuInfoResponse(BatchGetSpuInfoRequest request, QuerySpuListResult querySpuListResult,
                                                              BatchGetSpuInfoResponse response, List<SpuInfoDTO> spuInfoDTOList) {
        int totalCount = querySpuListResult.getTotal();
        int totalPage = totalCount % request.getPageSize() == 0 ? totalCount / request.getPageSize() : (totalCount / request.getPageSize()) + 1;
        PageInfo pageInfo = new PageInfo(request.getPageNum(), request.getPageSize(), totalPage, totalCount);
        response.setSpuInfos(spuInfoDTOList).setPageInfo(pageInfo);
        // 设置游标
        response.setOffset(querySpuListResult.getCursor_id());
        return response;
    }

    @Override
    public GetHeadQuarterSpuInfoResponse batchGetHeadQuarterSpuInfo(BasePageRequest request) {
        return null;
    }

    @Override
    public QueryAuditStatusResponse getAuditStatus(QueryAuditStatusRequest request) {
        return null;
    }

    @Override
    public RecommendChannelCategoryQueryResponse queryRecommendChannelCategory(RecommendChannelCategoryQueryRequest request) {
        return null;
    }

    @Override
    public SkuId2AppFoodCodeResponse queryAppFoodCodeBySkuId(QueryAppFoodCodeBySkuIdRequest request) {
        return null;
    }

    @Override
    public ResultStatus updateCustomSpuStoreCategory(UpdateCustomSpuStoreCategoryRequest request) {
        return null;
    }

    @Override
    public ResultSpuData batchUpdateSpuStoreCategoryCode(BatchUpdateCustomSpuStoreCategoryRequest request) {
        return null;
    }

    @Override
    public SensitiveWordCheckResponse checkSensitiveWords(SensitiveWordCheckRequest request) {
        return null;
    }

    @Override
    public QueryNormAuditDelSpuResponse queryNormAuditDelSpuInfo(QueryNormAuditDelSpuRequest request) {
        return null;
    }

    @Override
    public QueryChannelSpuIdResponse queryChannelSpuId(QueryChannelSpuIdRequest request) {
        return null;
    }

    @Override
    public ProductSequenceBatchSetResponse batchSetSequence(ProductSequenceBatchSetRequest request) {
        return null;
    }

    @Override
    public ResultSpuData updateStoreCategory(SpuStoreCategoryInfoRequest request) {
        return null;
    }

    @Override
    public QueryQualityProblemResponse queryQualityProblem(QueryQualityProblemRequest request) {
        return null;
    }

    @Override
    public ResultStatus submitAppealInfo(SubmitAppealInfoRequest request) {
        return null;
    }

    private SpuKey buildSpuKey(SpuInfoDTO data) {
        SpuKey spuKey = new SpuKey().setCustomSpuId(data.getCustomSpuId());
        List<SkuKey> skuKeys = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(data.getSkus())) {
            data.getSkus().forEach(skuInSpuInfoDTO -> {
                SkuKey skuKey = new SkuKey().setCustomSkuId(skuInSpuInfoDTO.getCustomSkuId());
                skuKeys.add(skuKey);
            });
        }
        spuKey.setSkus(skuKeys);
        return spuKey;
    }

    private SpuKey getSpuKey(String customSpuId) {
        return new SpuKey().setCustomSpuId(customSpuId).setSkus(new ArrayList<>());
    }

    private Long getChannelStoreId(BaseRequest request, Long storeId) {
        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(request.getTenantId(), request.getChannelId(), storeId);
        Preconditions.checkArgument(Objects.nonNull(channelStore), "channelStore does not exist of storeId:%s", storeId);
        return Long.valueOf(channelStore.getChannelPoiCode());
    }
}
