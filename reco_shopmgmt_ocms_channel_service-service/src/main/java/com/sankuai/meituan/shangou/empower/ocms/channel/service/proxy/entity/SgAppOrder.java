package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

import com.sankuai.meituan.waimai.service.order.datamanager.vo.result.order.WmPrivacyPhoneStatusCode;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.List;
import java.util.Set;

/**
 * Created by liuyonggao on 2021年12月24日19:15:00
 */
public class SgAppOrder {

    private Long order_id;
    private Float shipping_fee;
    private String app_order_code;
    private String app_poi_code;
    private Long wm_poi_id;
    private String wm_poi_name;
    private String wm_poi_address;
    private String wm_poi_phone;
    private String recipient_name;
    private String recipient_phone;
    private String recipient_address;
    private SgOpenRecipientAddressDetail recipient_address_detail;
    private Double total;
    private String caution;
    private String remark;
    private String shipper_phone;
    private Integer has_invoiced;
    private String invoice_title;
    private Integer status;
    private Integer is_third_shipping;
    private Integer pay_type;
    private Integer is_pre;
    private Integer expect_deliver_time;
    private Long ctime;
    private Long utime;
    private Long source_id;    // 订单来源类型
    private Integer package_bag_money;//打包袋字段
    private Integer shipping_type;
    private List<SgAppOrderFood> detail;
    private List<SgAppOrderExtra> extras;
    // 开发票方式，1美团发票合作商家，在线自动开具电子发票
    private int invMakeType;

    // 新增，送餐地址的经纬度
    private Double latitude;
    private Double longitude;
    // 门店平均送餐时间
    private Double avg_send_time;

    //新增订单预计到达时间
    private Integer estimate_arrival_time;

    private Long wm_order_id_view;
    private Double original_price;
    //三级城市
    private Long city_id;
    //二级物理城市
    private long location_city_id;
    private Long delivery_time;
    private String rider_fee;
    //新增,订单流水号
    private Integer day_seq;
    //是否是门店新客
    private boolean is_poi_first_order;
    //是否是预订单
    private Boolean is_pre_sale_order;
    //用户id
    private long user_id;
    private int dinners_number;
    private String logistics_code; // 配送方式
    private String taxpayer_id;
    private Integer pick_type;
    private Integer shipping_service; //配送服务
    private List<String> backup_recipient_phone;//备用隐私号
    private String channel;//订单来源
    private Long total_weight;//订单总重量
    // 预定人手机号 如果用户设置了隐私保护 手机号是隐私号，否则真实手机号
    private String order_phone_number;
    // 订单业务打标枚举 16-特价版订单
    private Set<Integer> order_tag_list;
    @JsonIgnore
    private boolean isSpecialOrder = false;
    //是否扫码配送订单
    private Boolean scanDeliverFlag;
    private Long openUserId; // 开放平台用户ID
    private String rider_name;
    //配送状态
    private int logisticsStatus;
    // 团餐订单标识 0子订单，1主订单
    private int ent_group_order_type;

    /**
     * 订单隐私号状态
     */
    private WmPrivacyPhoneStatusCode privacy_phone_status;
    private String privacy_phone_number;
    private String real_phone_number;

    //增加预订人隐私号相关字段
    private WmPrivacyPhoneStatusCode order_privacy_phone_status;
    //预订人隐私号码
    private String order_privacy_phone;
    //预订人真实号码
    private String order_phone;
    //订单品类
    private long poiCate;
    /**
     * buy order new field
     */
    // 是否品牌新客 1-是，0-不是
    private int is_brand_first_order;
    // 支付流水号
    private String trade_no;
    // 商品优惠详情
    private String sku_benefit_detail;
    // 订单确认时间
    private Long order_confirm_time;
    // 订单取消时间
    private Long order_cancel_time;
    // 订单完成时间
    private Long order_completed_time;
    // 骑手取货时间
    private int logistics_fetch_time;
    // 骑手完成配送时间
    private int logistics_completed_time;
    // 订单支付时间
    private Long order_pay_time;
    // extras
    private List<SgAppBuyOrderExtra> extras_buy;
    // 收件人性别
    private Integer recipient_gender;

    private Integer is_vip;

    public Integer getRecipient_gender() {
        return recipient_gender;
    }

    public void setRecipient_gender(Integer recipient_gender) {
        this.recipient_gender = recipient_gender;
    }

    public String getSku_benefit_detail() {
        return sku_benefit_detail;
    }

    public void setSku_benefit_detail(String sku_benefit_detail) {
        this.sku_benefit_detail = sku_benefit_detail;
    }

    public int getIs_brand_first_order() {
        return is_brand_first_order;
    }

    public void setIs_brand_first_order(int is_brand_first_order) {
        this.is_brand_first_order = is_brand_first_order;
    }

    public String getTrade_no() {
        return trade_no;
    }

    public void setTrade_no(String trade_no) {
        this.trade_no = trade_no;
    }

    public String getRecipient_phone() {
        return recipient_phone;
    }

    public void setRecipient_phone(String recipient_phone) {
        this.recipient_phone = recipient_phone;
    }

    public Long getOrder_id() {
        return order_id;
    }

    public void setOrder_id(Long order_id) {
        this.order_id = order_id;
    }

    public Float getShipping_fee() {
        return shipping_fee;
    }

    public void setShipping_fee(Float shipping_fee) {
        this.shipping_fee = shipping_fee;
    }

    public String getApp_order_code() {
        return app_order_code;
    }

    public void setApp_order_code(String app_order_code) {
        this.app_order_code = app_order_code;
    }

    public String getApp_poi_code() {
        return app_poi_code;
    }

    public void setApp_poi_code(String app_poi_code) {
        this.app_poi_code = app_poi_code;
    }

    public Long getWm_poi_id() {
        return wm_poi_id;
    }

    public void setWm_poi_id(Long wm_poi_id) {
        this.wm_poi_id = wm_poi_id;
    }

    public String getRecipient_name() {
        return recipient_name;
    }

    public void setRecipient_name(String recipient_name) {
        this.recipient_name = recipient_name;
    }

    public String getRecipient_address() {
        return recipient_address;
    }

    public void setRecipient_address(String recipient_address) {
        this.recipient_address = recipient_address;
    }

    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public String getCaution() {
        return caution;
    }

    public void setCaution(String caution) {
        this.caution = caution;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getShipper_phone() {
        return shipper_phone;
    }

    public void setShipper_phone(String shipper_phone) {
        this.shipper_phone = shipper_phone;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Long getUtime() {
        return utime;
    }

    public void setUtime(Long utime) {
        this.utime = utime;
    }

    public void setRecipient_address_detail(SgOpenRecipientAddressDetail recipient_address_detail) {
        this.recipient_address_detail = recipient_address_detail;
    }

    public SgOpenRecipientAddressDetail getRecipient_address_detail() {
        return recipient_address_detail;
    }

    public List<SgAppOrderFood> getDetail() {
        return detail;
    }

    public void setDetail(List<SgAppOrderFood> detail) {
        this.detail = detail;
    }

    public List<SgAppOrderExtra> getExtras() {
        return extras;
    }

    public void setExtras(List<SgAppOrderExtra> extras) {
        this.extras = extras;
    }

    public Integer getHas_invoiced() {
        return has_invoiced;
    }

    public void setHas_invoiced(Integer has_invoiced) {
        this.has_invoiced = has_invoiced;
    }

    public String getInvoice_title() {
        return invoice_title;
    }

    public void setInvoice_title(String invoice_title) {
        this.invoice_title = invoice_title;
    }

    public String getWm_poi_name() {
        return wm_poi_name;
    }

    public void setWm_poi_name(String wm_poi_name) {
        this.wm_poi_name = wm_poi_name;
    }

    public String getWm_poi_address() {
        return wm_poi_address;
    }

    public void setWm_poi_address(String wm_poi_address) {
        this.wm_poi_address = wm_poi_address;
    }

    public String getWm_poi_phone() {
        return wm_poi_phone;
    }

    public void setWm_poi_phone(String wm_poi_phone) {
        this.wm_poi_phone = wm_poi_phone;
    }

    public Integer getIs_third_shipping() {
        return is_third_shipping;
    }

    public void setIs_third_shipping(Integer is_third_shipping) {
        this.is_third_shipping = is_third_shipping;
    }

    public Integer getPay_type() {
        return pay_type;
    }

    public void setPay_type(Integer pay_type) {
        this.pay_type = pay_type;
    }

    public Integer getIs_pre() {
        return is_pre;
    }

    public void setIs_pre(Integer is_pre) {
        this.is_pre = is_pre;
    }

    public Integer getExpect_deliver_time() {
        return expect_deliver_time;
    }

    public void setExpect_deliver_time(Integer expect_deliver_time) {
        this.expect_deliver_time = expect_deliver_time;
    }

    public Long getSource_id() {
        return source_id;
    }

    public void setSource_id(Long source_id) {
        this.source_id = source_id;
    }

    public Integer getShipping_type() {
        return shipping_type;
    }

    public void setShipping_type(Integer shipping_type) {
        this.shipping_type = shipping_type;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Long getWm_order_id_view() {
        return wm_order_id_view;
    }

    public void setWm_order_id_view(Long wm_order_id_view) {
        this.wm_order_id_view = wm_order_id_view;
    }

    public Double getOriginal_price() {
        return original_price;
    }

    public void setOriginal_price(Double original_price) {
        this.original_price = original_price;
    }

    public Long getCity_id() {
        return city_id;
    }

    public void setCity_id(Long city_id) {
        this.city_id = city_id;
    }

    public Long getDelivery_time() {
        return delivery_time;
    }

    public void setDelivery_time(Long delivery_time) {
        this.delivery_time = delivery_time;
    }

    public Double getAvg_send_time() {
        return avg_send_time;
    }

    public void setAvg_send_time(Double avg_send_time) {
        this.avg_send_time = avg_send_time;
    }

    public String getRider_fee() {
        return rider_fee;
    }

    public void setRider_fee(String rider_fee) {
        this.rider_fee = rider_fee;
    }

    public Integer getDay_seq() {
        return day_seq;
    }

    public void setDay_seq(Integer day_seq) {
        this.day_seq = day_seq;
    }

    public boolean is_poi_first_order() {
        return is_poi_first_order;
    }

    public void setIs_poi_first_order(boolean is_poi_first_order) {
        this.is_poi_first_order = is_poi_first_order;
    }

    public Boolean getIs_pre_sale_order() {
        return is_pre_sale_order;
    }

    public void setIs_pre_sale_order(Boolean is_pre_sale_order) {
        this.is_pre_sale_order = is_pre_sale_order;
    }

    public long getUser_id() {
        return user_id;
    }

    public void setUser_id(long user_id) {
        this.user_id = user_id;
    }

    public int getDinners_number() {
        return dinners_number;
    }

    public void setDinners_number(int dinners_number) {
        this.dinners_number = dinners_number;
    }

    public String getLogistics_code() {
        return logistics_code;
    }

    public void setLogistics_code(String logistics_code) {
        this.logistics_code = logistics_code;
    }

    public String getTaxpayer_id() {
        return taxpayer_id;
    }

    public void setTaxpayer_id(String taxpayer_id) {
        this.taxpayer_id = taxpayer_id;
    }

    public Integer getPick_type() {
        return pick_type;
    }

    public void setPick_type(Integer pick_type) {
        this.pick_type = pick_type;
    }

    public Integer getShipping_service() {
        return shipping_service;
    }

    public void setShipping_service(Integer shipping_service) {
        this.shipping_service = shipping_service;
    }

    public Integer getPackage_bag_money() {
        return package_bag_money;
    }

    public void setPackage_bag_money(Integer package_bag_money) {
        this.package_bag_money = package_bag_money;
    }

    public List<String> getBackup_recipient_phone() {
        return backup_recipient_phone;
    }

    public void setBackup_recipient_phone(List<String> backup_recipient_phone) {
        this.backup_recipient_phone = backup_recipient_phone;
    }

    public boolean isIs_poi_first_order() {
        return is_poi_first_order;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Integer getEstimate_arrival_time() {
        return estimate_arrival_time;
    }

    public void setEstimate_arrival_time(Integer estimate_arrival_time) {
        this.estimate_arrival_time = estimate_arrival_time;
    }

    public Long getTotal_weight() {
        return total_weight;
    }

    public void setTotal_weight(Long total_weight) {
        this.total_weight = total_weight;
    }

    public String getOrder_phone_number() {
        return order_phone_number;
    }

    public void setOrder_phone_number(String order_phone_number) {
        this.order_phone_number = order_phone_number;
    }

    public long getLocation_city_id() {
        return location_city_id;
    }

    public void setLocation_city_id(long location_city_id) {
        this.location_city_id = location_city_id;
    }

    public int getInvMakeType() {
        return invMakeType;
    }

    public void setInvMakeType(int invMakeType) {
        this.invMakeType = invMakeType;
    }

    public boolean isSpecialOrder() {
        return isSpecialOrder;
    }

    public void setSpecialOrder(boolean specialOrder) {
        isSpecialOrder = specialOrder;
    }

    public Set<Integer> getOrder_tag_list() {
        return order_tag_list;
    }

    public void setOrder_tag_list(Set<Integer> order_tag_list) {
        this.order_tag_list = order_tag_list;
    }

    public Boolean getScanDeliverFlag() {
        return scanDeliverFlag;
    }

    public void setScanDeliverFlag(Boolean scanDeliverFlag) {
        this.scanDeliverFlag = scanDeliverFlag;
    }

    public Long getOpenUserId() {
        return openUserId;
    }

    public void setOpenUserId(Long openUserId) {
        this.openUserId = openUserId;
    }

    public String getRider_name() {
        return rider_name;
    }

    public void setRider_name(String rider_name) {
        this.rider_name = rider_name;
    }

    public int getLogisticsStatus() {
        return logisticsStatus;
    }

    public void setLogisticsStatus(int logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }

    public int getEnt_group_order_type() {
        return ent_group_order_type;
    }

    public void setEnt_group_order_type(int ent_group_order_type) {
        this.ent_group_order_type = ent_group_order_type;
    }

    public WmPrivacyPhoneStatusCode getPrivacy_phone_status() {
        return privacy_phone_status;
    }

    public void setPrivacy_phone_status(WmPrivacyPhoneStatusCode privacy_phone_status) {
        this.privacy_phone_status = privacy_phone_status;
    }

    public String getPrivacy_phone_number() {
        return privacy_phone_number;
    }

    public void setPrivacy_phone_number(String privacy_phone_number) {
        this.privacy_phone_number = privacy_phone_number;
    }

    public String getReal_phone_number() {
        return real_phone_number;
    }

    public void setReal_phone_number(String real_phone_number) {
        this.real_phone_number = real_phone_number;
    }

    public WmPrivacyPhoneStatusCode getOrder_privacy_phone_status() {
        return order_privacy_phone_status;
    }

    public void setOrder_privacy_phone_status(WmPrivacyPhoneStatusCode order_privacy_phone_status) {
        this.order_privacy_phone_status = order_privacy_phone_status;
    }

    public String getOrder_privacy_phone() {
        return order_privacy_phone;
    }

    public void setOrder_privacy_phone(String order_privacy_phone) {
        this.order_privacy_phone = order_privacy_phone;
    }

    public String getOrder_phone() {
        return order_phone;
    }

    public void setOrder_phone(String order_phone) {
        this.order_phone = order_phone;
    }

    public long getPoiCate() {
        return poiCate;
    }

    public void setPoiCate(long poiCate) {
        this.poiCate = poiCate;
    }


    public Long getOrder_confirm_time() {
        return order_confirm_time;
    }

    public void setOrder_confirm_time(Long order_confirm_time) {
        this.order_confirm_time = order_confirm_time;
    }

    public Long getOrder_cancel_time() {
        return order_cancel_time;
    }

    public void setOrder_cancel_time(Long order_cancel_time) {
        this.order_cancel_time = order_cancel_time;
    }

    public Long getOrder_completed_time() {
        return order_completed_time;
    }

    public void setOrder_completed_time(Long order_completed_time) {
        this.order_completed_time = order_completed_time;
    }

    public int getLogistics_fetch_time() {
        return logistics_fetch_time;
    }

    public void setLogistics_fetch_time(int logistics_fetch_time) {
        this.logistics_fetch_time = logistics_fetch_time;
    }

    public int getLogistics_completed_time() {
        return logistics_completed_time;
    }

    public void setLogistics_completed_time(int logistics_completed_time) {
        this.logistics_completed_time = logistics_completed_time;
    }

    public List<SgAppBuyOrderExtra> getExtras_buy() {
        return extras_buy;
    }

    public void setExtras_buy(List<SgAppBuyOrderExtra> extras_buy) {
        this.extras_buy = extras_buy;
    }

    public Long getOrder_pay_time() {
        return order_pay_time;
    }

    public void setOrder_pay_time(Long order_pay_time) {
        this.order_pay_time = order_pay_time;
    }

    public Integer getIs_vip() {
        return is_vip;
    }

    public void setIs_vip(Integer is_vip) {
        this.is_vip = is_vip;
    }

    @Override
    public String toString() {
        return "SgAppOrder{" +
                "order_id=" + order_id +
                ", shipping_fee=" + shipping_fee +
                ", app_order_code='" + app_order_code + '\'' +
                ", app_poi_code='" + app_poi_code + '\'' +
                ", wm_poi_id=" + wm_poi_id +
                ", wm_poi_name='" + wm_poi_name + '\'' +
                ", wm_poi_address='" + wm_poi_address + '\'' +
                ", wm_poi_phone='" + wm_poi_phone + '\'' +
                ", recipient_name='" + recipient_name + '\'' +
                ", recipient_phone='" + recipient_phone + '\'' +
                ", recipient_address='" + recipient_address + '\'' +
                ", recipient_address_detail=" + recipient_address_detail +
                ", total=" + total +
                ", caution='" + caution + '\'' +
                ", remark='" + remark + '\'' +
                ", shipper_phone='" + shipper_phone + '\'' +
                ", has_invoiced=" + has_invoiced +
                ", invoice_title='" + invoice_title + '\'' +
                ", status=" + status +
                ", is_third_shipping=" + is_third_shipping +
                ", pay_type=" + pay_type +
                ", is_pre=" + is_pre +
                ", expect_deliver_time=" + expect_deliver_time +
                ", ctime=" + ctime +
                ", utime=" + utime +
                ", source_id=" + source_id +
                ", package_bag_money=" + package_bag_money +
                ", shipping_type=" + shipping_type +
                ", detail=" + detail +
                ", extras=" + extras +
                ", invMakeType=" + invMakeType +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", avg_send_time=" + avg_send_time +
                ", estimate_arrival_time=" + estimate_arrival_time +
                ", wm_order_id_view=" + wm_order_id_view +
                ", original_price=" + original_price +
                ", city_id=" + city_id +
                ", location_city_id=" + location_city_id +
                ", delivery_time=" + delivery_time +
                ", rider_fee='" + rider_fee + '\'' +
                ", day_seq=" + day_seq +
                ", is_poi_first_order=" + is_poi_first_order +
                ", is_pre_sale_order=" + is_pre_sale_order +
                ", user_id=" + user_id +
                ", dinners_number=" + dinners_number +
                ", logistics_code='" + logistics_code + '\'' +
                ", taxpayer_id='" + taxpayer_id + '\'' +
                ", pick_type=" + pick_type +
                ", shipping_service=" + shipping_service +
                ", backup_recipient_phone=" + backup_recipient_phone +
                ", channel='" + channel + '\'' +
                ", total_weight=" + total_weight +
                ", order_phone_number='" + order_phone_number + '\'' +
                ", order_tag_list=" + order_tag_list +
                ", isSpecialOrder=" + isSpecialOrder +
                ", scanDeliverFlag=" + scanDeliverFlag +
                ", openUserId=" + openUserId +
                ", rider_name='" + rider_name + '\'' +
                ", logisticsStatus=" + logisticsStatus +
                ", ent_group_order_type=" + ent_group_order_type +
                ", privacy_phone_status=" + privacy_phone_status +
                ", privacy_phone_number='" + privacy_phone_number + '\'' +
                ", real_phone_number='" + real_phone_number + '\'' +
                ", order_privacy_phone_status=" + order_privacy_phone_status +
                ", order_privacy_phone='" + order_privacy_phone + '\'' +
                ", order_phone='" + order_phone + '\'' +
                ", poiCate=" + poiCate +
                ", is_brand_first_order=" + is_brand_first_order +
                ", trade_no='" + trade_no + '\'' +
                ", sku_benefit_detail='" + sku_benefit_detail + '\'' +
                ", order_confirm_time=" + order_confirm_time +
                ", order_cancel_time=" + order_cancel_time +
                ", order_completed_time=" + order_completed_time +
                ", logistics_fetch_time=" + logistics_fetch_time +
                ", logistics_completed_time=" + logistics_completed_time +
                ", order_pay_time=" + order_pay_time +
                ", extras_buy=" + extras_buy +
                ", recipient_gender=" + recipient_gender +
                ", is_vip=" + is_vip +
                '}';
    }
}
