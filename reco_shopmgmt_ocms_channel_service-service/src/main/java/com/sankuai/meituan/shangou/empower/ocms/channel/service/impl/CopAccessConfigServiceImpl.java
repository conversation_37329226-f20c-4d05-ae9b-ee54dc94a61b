package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.CopAccessConfigListPageQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.CopAccessConfigQueryDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.ChannelStoreMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.CopAccessConfigMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;

/**
 * 租户各渠道开放平台请求系统参数配置信息服务类
 *
 * <AUTHOR>
 * @create 2018-12-28 下午7:11
 **/
@Service("copAccessConfigService")
public class CopAccessConfigServiceImpl implements CopAccessConfigService {

    @Resource
    CopAccessConfigMapper copAccessConfigMapper;

    @Resource
    private ChannelStoreMapper channelStoreMapper;

    @Override
    public int insert(CopAccessConfigDO record) {
        return copAccessConfigMapper.insert(record);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return copAccessConfigMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CopAccessConfigDO record) {
        return copAccessConfigMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public CopAccessConfigDO selectByPrimaryKey(Long id) {
        return copAccessConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    @Deprecated
    public String selectSysParams(Long tenantId, Integer channelId) {
        return copAccessConfigMapper.selectSysParams(tenantId, channelId);
    }

    @Override
    public String selectAppSysParams(Long tenantId, Integer channelId, Long appId) {
        return copAccessConfigMapper.selectAppSysParams(tenantId, channelId, appId);
    }

    @Override
    public Long selectTenantId(Integer channelId, String tenantAppId) {
        return copAccessConfigMapper.selectTenantId(channelId, tenantAppId);
    }

    @Override
    public List<CopAccessConfigDO> findTenantChannelConfigApp(Long tenantId, Integer channelId) {
        return copAccessConfigMapper.selectByTenantChannelApp(tenantId, channelId);
    }

    @Override
    public List<CopAccessConfigDO> pageQueryTenantChannelConfig(CopAccessConfigListPageQueryDTO pageQuery) {
        return copAccessConfigMapper.selectByPageQuery(pageQuery);
    }

    @Override
    public CopAccessConfigDO findTenantChannelConfig(Long tenantId, Integer channelId) {
        return copAccessConfigMapper.selectByTenantChannel(tenantId, channelId);
    }

    @Override
    public boolean findTenantNeedContactSpec(BaseRequest baseRequest) {
        if (Objects.isNull(baseRequest)) {
            return false;
        }

        List<CopAccessConfigDO> tenantChannelConfigApps = findTenantChannelConfigApp(baseRequest.getTenantId(),
                baseRequest.getChannelId());
        List<ChannelStoreDO> channelStoreDOS = channelStoreMapper.selectChannelPoiCode(baseRequest.getTenantId(), baseRequest.getChannelId(),
                baseRequest.getStoreIdList());
        if (CollectionUtils.isEmpty(tenantChannelConfigApps) || CollectionUtils.isEmpty(channelStoreDOS)) {
            return false;
        }

        ChannelStoreDO channelStoreDO = channelStoreDOS.get(0);
        tenantChannelConfigApps = Fun.filter(tenantChannelConfigApps, copAccessConfigDO -> {
            return copAccessConfigDO.getAppId() == channelStoreDO.getAppId();
        });

        if (CollectionUtils.isEmpty(tenantChannelConfigApps)) {
            return false;
        }

        return tenantChannelConfigApps.get(0).getNeedContactSpec() == 1;
    }

    @Override
    public List<Integer> getChannelsByTenant(Long tenantId){
        if (tenantId <= 0) {
            return Lists.newArrayList();
        }
        return copAccessConfigMapper.getChannelsByTenant(tenantId);
    }

    @Override
    public String selectSysParams(String tenantAppId, Integer channelId) {
        return copAccessConfigMapper.selectElmSysParams(tenantAppId, channelId);
    }

    @Override
    public CopAccessConfigDO selectByTenantAppIdAndChannelId(String tenantAppId, Integer channelId) {
        return copAccessConfigMapper.selectByTenantAppIdAndChannelId(tenantAppId, channelId);
    }

    @Override
    public List<CopAccessConfigDO> selectByTenantAppIdChannelIdTenantIds(String tenantAppId, Integer channelId, List<Long> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            return Lists.newArrayList();
        }
        return copAccessConfigMapper.selectByTenantAppIdChannelIdTenantIds(tenantAppId, channelId, tenantIds);
    }

    @Override
    public CopAccessConfigDO selectByTenantIdAndChannelId(Long tenantId, Integer channelId) {
        return copAccessConfigMapper.selectByTenantIdAndChannelId(tenantId, channelId);
    }

    @Override
    public CopAccessConfigDO selectDouyinByTenantAppIdAndShopId(String tenantAppId, String shopId) {
        return copAccessConfigMapper.selectDouyinByTenantAppIdAndShopId(tenantAppId, shopId);
    }

    @Override
    public List<CopAccessConfigDO> selectDouyinByCopAccessConfigQueryDTOList(List<CopAccessConfigQueryDTO> dtos) {
        List<CopAccessConfigDO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(dtos)) {
            return result;
        }
        List<List<CopAccessConfigQueryDTO>> partition = Lists.partition(dtos, 100);
        for (List<CopAccessConfigQueryDTO> part : partition) {
            result.addAll(copAccessConfigMapper.selectDouyinByCopAccessConfigQueryDTOList(part));
        }

        return result;
    }

    @Override
    public CopAccessConfigDO selectYzByTenantAppIdAndShopId(String tenantAppId, String shopId) {
        return copAccessConfigMapper.selectYzByTenantAppIdAndShopId(tenantAppId, shopId);
    }

    @Override
    public List<CopAccessConfigDO> selectYzByCopAccessConfigQueryDTOList(List<CopAccessConfigQueryDTO> dtos) {
        List<CopAccessConfigDO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(dtos)) {
            return result;
        }
        List<List<CopAccessConfigQueryDTO>> partition = Lists.partition(dtos, 100);
        for (List<CopAccessConfigQueryDTO> part : partition) {
            result.addAll(copAccessConfigMapper.selectYzByCopAccessConfigQueryDTOList(part));
        }

        return result;
    }

    @Override
    public int updateSysParams(CopAccessConfigDO copAccessConfigDO) {
        //目前此接口仅提供给饿了么
        return copAccessConfigMapper.updateElmSysParams(copAccessConfigDO);
    }

    @Override
    public int updateWeChatSysParams(CopAccessConfigDO copAccessConfigDO) {
        return copAccessConfigMapper.updateWeChatSysParams(copAccessConfigDO);
    }
}
