package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Preconditions;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelMaterialSearchDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelMaterialSearchResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelMaterialUploadDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelMaterialUploadResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MaterialTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelMaterialDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialSearchRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialSearchResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialUploadResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialUploadResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelMaterialService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;

import lombok.extern.slf4j.Slf4j;

/**
 * 渠道素材服务实现
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Slf4j
@Service("dyChannelMaterialService")
public class DouyinChannelMaterialServiceImpl implements ChannelMaterialService {
    @Autowired
    private DouyinChannelGateService douyinChannelGateService;

    @Autowired
    private DouyinConverterService douyinConverterService;

    @Override
    public MaterialUploadResponse batchUploadMaterial(MaterialUploadRequest request) {
        ResultStatus resultStatus;
        MaterialUploadResult materialUploadResult = new MaterialUploadResult();
        MaterialUploadResponse materialUploadResponse = new MaterialUploadResponse();

        try {
            checkMaterialRequest(request, MaterialUploadRequest::getTenantId, MaterialUploadRequest::getChannelId,
                    MaterialUploadRequest::getMaterials, null);

            if (CollectionUtils.isEmpty(request.getMaterials())) {
                return MaterialUploadResponse.builder().resultStatus(ResultGenerator.genSuccessResult()).build();
            }

            BaseRequest baseRequest = request.buildBaseRequest();
            ChannelMaterialUploadDto channelMaterialUploadDto = douyinConverterService.materialUploadDtoMapping(request);
            MaterialTypeEnum materialTypeEnum = MaterialTypeEnum.findByDesc(request.getMaterialType());
            Preconditions.checkNotNull(materialTypeEnum, "invalid material type");
            channelMaterialUploadDto.getMaterials().forEach(materialDto -> materialDto.setMaterial_type(materialTypeEnum.getDesc()));
            ChannelResponseDTO<ChannelMaterialUploadResult> channelResponse;

            switch (materialTypeEnum) {
                case MATERIAL_TYPE_PHOTO:
                    channelResponse = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.UPLOAD_IMAGE_MATERIAL, baseRequest, channelMaterialUploadDto);
                    break;
                case MATERIAL_TYPE_VIDEO:
                    channelResponse = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.UPLOAD_VIDEO_MATERIAL, baseRequest, channelMaterialUploadDto);
                    break;
                default:
                    throw new IllegalArgumentException("unsupported material type");
            }

            if (channelResponse.isSuccess()) {
                materialUploadResult = buildUploadSuccessResult(channelResponse, materialTypeEnum.getDesc());
                resultStatus = ResultGenerator.genSuccessResult();
            }
            else {
                resultStatus = ResultGenerator.genResult(ResultCode.FAIL, channelResponse.getErrorMsg());
            }
        }
        catch (IllegalArgumentException e) {
            log.warn("DouyinChannelMaterialServiceImpl.batchUploadMaterial 参数校验异常, data:{}", request, e);
            resultStatus = ResultGenerator.genResult(ResultCode.INVALID_PARAM, e.getMessage());
        }
        catch (Exception e) {
            log.error("DouyinChannelMaterialServiceImpl.batchUploadMaterial 服务异常, data:{}", request, e);
            resultStatus = ResultGenerator.genResult(ResultCode.FAIL, e.getMessage());
        }

        materialUploadResponse.setResultStatus(resultStatus);
        materialUploadResponse.setMaterialUploadResult(materialUploadResult);
        return materialUploadResponse;
    }

    @Override
    public MaterialUploadResponse batchUploadMaterialByVirtualConfig(MaterialUploadRequest request) {
        ResultStatus resultStatus;
        MaterialUploadResult materialUploadResult = new MaterialUploadResult();
        MaterialUploadResponse materialUploadResponse = new MaterialUploadResponse();

        try {
            checkMaterialRequest(request, MaterialUploadRequest::getTenantId, MaterialUploadRequest::getChannelId,
                    MaterialUploadRequest::getMaterials, null);

            if (CollectionUtils.isEmpty(request.getMaterials())) {
                return MaterialUploadResponse.builder().resultStatus(ResultGenerator.genSuccessResult()).build();
            }

            BaseRequest baseRequest = request.buildBaseRequest();
            ChannelMaterialUploadDto channelMaterialUploadDto = douyinConverterService.materialUploadDtoMapping(request);
            MaterialTypeEnum materialTypeEnum = MaterialTypeEnum.findByDesc(request.getMaterialType());
            Preconditions.checkNotNull(materialTypeEnum, "invalid material type");
            channelMaterialUploadDto.getMaterials().forEach(materialDto -> materialDto.setMaterial_type(materialTypeEnum.getDesc()));
            ChannelResponseDTO<ChannelMaterialUploadResult> channelResponse;

            switch (materialTypeEnum) {
                case MATERIAL_TYPE_PHOTO:
                    channelResponse = douyinChannelGateService.sendPostByVirtualConfig(ChannelPostDouyinEnum.UPLOAD_IMAGE_MATERIAL, baseRequest,
                            channelMaterialUploadDto);
                    break;
                case MATERIAL_TYPE_VIDEO:
                    channelResponse = douyinChannelGateService.sendPostByVirtualConfig(ChannelPostDouyinEnum.UPLOAD_VIDEO_MATERIAL, baseRequest,
                            channelMaterialUploadDto);
                    break;
                default:
                    throw new IllegalArgumentException("unsupported material type");
            }

            if (channelResponse.isSuccess()) {
                materialUploadResult = buildUploadSuccessResult(channelResponse, materialTypeEnum.getDesc());
                resultStatus = ResultGenerator.genSuccessResult();
            }
            else {
                resultStatus = ResultGenerator.genResult(ResultCode.FAIL, channelResponse.getErrorMsg());
            }
        }
        catch (IllegalArgumentException e) {
            log.warn("DouyinChannelMaterialServiceImpl.batchUploadMaterialByVirtualConfig 参数校验异常, data:{}", request, e);
            resultStatus = ResultGenerator.genResult(ResultCode.INVALID_PARAM, e.getMessage());
        }
        catch (Exception e) {
            log.error("DouyinChannelMaterialServiceImpl.batchUploadMaterialByVirtualConfig 服务异常, data:{}", request, e);
            resultStatus = ResultGenerator.genResult(ResultCode.FAIL, e.getMessage());
        }

        materialUploadResponse.setResultStatus(resultStatus);
        materialUploadResponse.setMaterialUploadResult(materialUploadResult);
        return materialUploadResponse;
    }

    @Override
    public MaterialSearchResponse searchMaterial(MaterialSearchRequest request) {
        ResultStatus resultStatus;
        MaterialSearchResponse materialSearchResponse = MaterialSearchResponse.builder().total(0L).materialInfoList(Collections.emptyList()).build();

        try {
            checkMaterialRequest(request, MaterialSearchRequest::getTenantId, MaterialSearchRequest::getChannelId,
                    null, MaterialSearchRequest::getMaterialIdList);

            if (CollectionUtils.isEmpty(request.getMaterialIdList())) {
                materialSearchResponse.setResultStatus(ResultGenerator.genSuccessResult());
                return materialSearchResponse;
            }

            BaseRequest baseRequest = request.buildBaseRequest();
            ChannelMaterialSearchDto channelMaterialSearchDto = douyinConverterService.materialSearchDtoMapping(request);
            ChannelResponseDTO<ChannelMaterialSearchResult> channelResponse =
                    douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SEARCH_MATERIAL, baseRequest, channelMaterialSearchDto);

            if (channelResponse.isSuccess()) {
                buildSearchSuccessResult(channelResponse, materialSearchResponse);
                resultStatus = ResultGenerator.genSuccessResult();
            }
            else {
                resultStatus = ResultGenerator.genResult(ResultCode.FAIL, channelResponse.getErrorMsg());
            }
        }
        catch (IllegalArgumentException e) {
            log.warn("DouyinChannelMaterialServiceImpl.searchMaterial 参数校验异常, data:{}", request, e);
            resultStatus = ResultGenerator.genResult(ResultCode.INVALID_PARAM, e.getMessage());
        }
        catch (Exception e) {
            log.error("DouyinChannelMaterialServiceImpl.searchMaterial 服务异常, data:{}", request, e);
            resultStatus = ResultGenerator.genResult(ResultCode.FAIL, e.getMessage());
        }

        materialSearchResponse.setResultStatus(resultStatus);
        return materialSearchResponse;
    }

    @Override
    public MaterialSearchResponse searchMaterialByVirtualConfig(MaterialSearchRequest request) {
        ResultStatus resultStatus;
        MaterialSearchResponse materialSearchResponse = MaterialSearchResponse.builder().total(0L).materialInfoList(Collections.emptyList()).build();

        try {
            checkMaterialRequest(request, MaterialSearchRequest::getTenantId, MaterialSearchRequest::getChannelId,
                    null, MaterialSearchRequest::getMaterialIdList);

            if (CollectionUtils.isEmpty(request.getMaterialIdList())) {
                materialSearchResponse.setResultStatus(ResultGenerator.genSuccessResult());
                return materialSearchResponse;
            }

            BaseRequest baseRequest = request.buildBaseRequest();
            ChannelMaterialSearchDto channelMaterialSearchDto = douyinConverterService.materialSearchDtoMapping(request);
            ChannelResponseDTO<ChannelMaterialSearchResult> channelResponse =
                    douyinChannelGateService.sendPostByVirtualConfig(ChannelPostDouyinEnum.SEARCH_MATERIAL, baseRequest, channelMaterialSearchDto);

            if (channelResponse.isSuccess()) {
                buildSearchSuccessResult(channelResponse, materialSearchResponse);
                resultStatus = ResultGenerator.genSuccessResult();
            }
            else {
                resultStatus = ResultGenerator.genResult(ResultCode.FAIL, channelResponse.getErrorMsg());
            }
        }
        catch (IllegalArgumentException e) {
            log.warn("DouyinChannelMaterialServiceImpl.searchMaterialByVirtualConfig 参数校验异常, data:{}", request, e);
            resultStatus = ResultGenerator.genResult(ResultCode.INVALID_PARAM, e.getMessage());
        }
        catch (Exception e) {
            log.error("DouyinChannelMaterialServiceImpl.searchMaterialByVirtualConfig 服务异常, data:{}", request, e);
            resultStatus = ResultGenerator.genResult(ResultCode.FAIL, e.getMessage());
        }

        materialSearchResponse.setResultStatus(resultStatus);
        return materialSearchResponse;
    }

    private MaterialUploadResult buildUploadSuccessResult(ChannelResponseDTO<ChannelMaterialUploadResult> channelResponse,
                                                          String materialType) {
        ChannelMaterialUploadResult originResult = channelResponse.getCoreData();
        if (Objects.isNull(originResult)) {
            log.warn("materialService, channel return data is null, response:{}", channelResponse);
            return new MaterialUploadResult();
        }
        return DouyinConvertUtil.convertToMaterialUploadResult(originResult, materialType);
    }

    private void buildSearchSuccessResult(ChannelResponseDTO<ChannelMaterialSearchResult> channelResponse,
                                          MaterialSearchResponse materialSearchResponse) {
        ChannelMaterialSearchResult originResult = channelResponse.getCoreData();
        if (Objects.isNull(originResult)) {
            log.warn("materialService, channel return data is null, response:{}", channelResponse);
            return;
        }
        List<ChannelMaterialDto> materialInfoList = DouyinConvertUtil.convertToMaterialList(originResult.getMaterial_info_list());
        materialSearchResponse.setTotal(originResult.getTotal());
        materialSearchResponse.setMaterialInfoList(materialInfoList);
    }

    private <T> void checkMaterialRequest(T request,
                                          Function<T, Long> getTenantId,
                                          Function<T, Integer> getChannelId,
                                          Function<T, List<MaterialUploadRequest.Material>> getMaterials,
                                          Function<T, List<String>> getMaterialIdList) {
        // 基础参数校验
        Preconditions.checkNotNull(request, "request is null");
        Preconditions.checkNotNull(getTenantId.apply(request), "tenantId is null");
        Preconditions.checkNotNull(getChannelId.apply(request), "channelId is null");
        Preconditions.checkArgument(EnhanceChannelType.DY.getChannelId().equals(getChannelId.apply(request)), "unsupported channel");

        // 上传素材时检查素材列表
        generalCheckList(request, getMaterials, "materials is null", "upload materials size limit 50", Constant.BATCH_LIMIT_50_AMOUNT);
        // 搜索素材时检查素材Id列表
        generalCheckList(request, getMaterialIdList, "materialIdList is null", "search materials size limit 50", Constant.BATCH_LIMIT_50_AMOUNT);
    }

    private <T, R> void generalCheckList(T request, Function<T, List<R>> getList, String nullErrorMsg, String sizeErrorMsg, int limitSize) {
        if (request != null && getList != null) {
            List<R> list = getList.apply(request);
            Preconditions.checkNotNull(list, nullErrorMsg);
            Preconditions.checkArgument(list.size() <= limitSize, sizeErrorMsg);
        }
    }

}
