package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.qnh;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhBatchUpdateItemStatusParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhBatchUpdateSaleStatusParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhCommonResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhOfflineCategory;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhOfflineCategoryQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhOnlineCategory;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhOnlineCategoryQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhProduct;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhProductQueryId;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhProductQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhUpdateOnlinePriceParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhUpdateStatusInnerResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostQnhEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhOfflineCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhOnlineCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhProductDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhStoreProductDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhUpdateItemStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.QnhUpdateSaleStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhBatchUpdateItemStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhBatchUpdateSaleStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhOfflineCategoryListReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhOnlineCategoryListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhProductQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhStoreProductListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhUpdateOnlinePriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhBatchUpdateItemStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhBatchUpdateSaleStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhOfflineCategoryListResp;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhOnlineCategoryListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhProductQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhStoreProductListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhUpdateOnlinePriceResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.QnhProductService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.QnhConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.QnhConverterUtils;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wangyihao04
 * @Date: 2021-12-27 20:13
 * @Mail: <EMAIL>
 */
@SuppressWarnings("unchecked")
@Service
public class QnhProductServiceImpl implements QnhProductService {

    @Autowired
    private QnhChannelGateService qnhChannelGateService;

    @Autowired
    private QnhConverterService qnhConverterService;

    @Autowired
    private QnhStoreService qnhStoreService;

    @Override
    public QnhProductQueryResponse queryQnhProduct(QnhProductQueryRequest request) {

        QnhProductQueryParam queryParam = qnhConverterService.qnhProductQueryParamMapping(request);
        QnhCommonResponse<List<QnhProduct>> result = qnhChannelGateService.sendPost(request.getTenantId(),
                ChannelPostQnhEnum.QUERY_PRODUCT,
                queryParam);
        if (Objects.isNull(result) || result.fail()) {
            return genQnhProductQueryResponse(ResultCode.FAIL.getCode(), result.getMsg());
        }

        List<QnhProduct> qnhProducts = result.getStructuredData();

        List<QnhProductDTO> qnhProductDTOS = qnhConverterService.qnhProductDTOMapping(qnhProducts);
        QnhProductQueryResponse response = genQnhProductQueryResponse(ResultCode.SUCCESS.getCode(), result.getMsg());
        response.setData(qnhProductDTOS);
        response.setPageNo(result.getPageNo() == null ? request.getPageNo() : result.getPageNo());
        response.setPageSize(result.getPageSize() == null ? request.getPageSize() : result.getPageSize());
        response.setTotalPage(result.getTotalPage() == null ? 0 : result.getTotalPage());
        response.setTotal(result.getTotal() == null ? 0 : result.getTotal());
        return response;
    }

    private QnhProductQueryResponse genQnhProductQueryResponse(Integer code, String msg) {
        QnhProductQueryResponse response = new QnhProductQueryResponse();
        response.setCode(code);
        response.setMsg(msg);
        return response;
    }

    @Override
    public QnhUpdateOnlinePriceResponse updateOnlinePrice(QnhUpdateOnlinePriceRequest request) {
        QnhUpdateOnlinePriceParam updatePriceItem = qnhConverterService.qnhUpdateOnlinePriceParamMapping(request);
        updatePriceItem.setRegion_code(
                qnhStoreService.mappingQnhStoreIdByStoreId(request.getTenantId(), request.getStoreId()));
        QnhCommonResponse result = qnhChannelGateService.sendPost(request.getTenantId(),
                ChannelPostQnhEnum.UPDATE_ONLINE_PRICE,
                updatePriceItem);
        if (Objects.isNull(result) || !result.success()) {
            return genQnhUpdateOnlinePriceResponse(ResultCode.FAIL.getCode(),
                    result == null ? ResultCode.FAIL.getMsg() :
                            result.getMsg());
        } else {
            return genQnhUpdateOnlinePriceResponse(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
        }
    }

    private QnhUpdateOnlinePriceResponse genQnhUpdateOnlinePriceResponse(Integer code, String msg) {
        QnhUpdateOnlinePriceResponse response = new QnhUpdateOnlinePriceResponse();
        response.setCode(code);
        response.setMsg(msg);
        return response;
    }

    @Override
    public QnhBatchUpdateItemStatusResponse batchUpdateItemStatus(QnhBatchUpdateItemStatusRequest request) {
        Map<Long, String> qnhRegionCodeMap = qnhStoreService.mappingQnhStoreIdByStoreIds(request.getTenantId(), request
                .getUpdateParams().stream().map(QnhUpdateItemStatusDTO::getStoreId).collect(Collectors.toList()));
        QnhBatchUpdateItemStatusParam updateParam = QnhConverterUtils.convertBatchUpdateStatusParam(request,
                qnhRegionCodeMap);
        QnhCommonResponse<List<QnhUpdateStatusInnerResult>> result = qnhChannelGateService.sendPost(
                request.getTenantId(), ChannelPostQnhEnum.UPDATE_ITEM_STATUS, updateParam);
        if (Objects.isNull(result) || !result.success()) {
            return genQnhBatchUpdateItemStatusResponse(ResultCode.FAIL.getCode(),
                    result == null ? ResultCode.FAIL.getMsg() :
                            result.getMsg(), buildItemStatusErrorMsgMap(request.getUpdateParams()));
        } else {
            if (CollectionUtils.isNotEmpty(result.getStructuredData())) {
                List<QnhUpdateStatusInnerResult> failedResults = result.getStructuredData().stream()
                        .filter(e -> !e.success()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(failedResults)) {
                    return genQnhBatchUpdateItemStatusResponse(ResultCode.FAIL.getCode(), ResultCode.FAIL.getMsg(),
                            buildItemStatusErrorMsgMap(request.getUpdateParams(), failedResults));
                } else {
                    return genQnhBatchUpdateItemStatusResponse(ResultCode.SUCCESS.getCode(),
                            ResultCode.SUCCESS.getMsg(),
                            Collections.emptyMap());
                }
            } else {
                return genQnhBatchUpdateItemStatusResponse(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(),
                        Collections.emptyMap());
            }
        }
    }

    private Map<String, String> buildItemStatusErrorMsgMap(List<QnhUpdateItemStatusDTO> updateParams) {
        Map<String, String> errorMsgMap = Maps.newHashMap();
        for (QnhUpdateItemStatusDTO dto : updateParams) {
            errorMsgMap.put(dto.getSpuId(), dto.getSpuName() + "因为系统错误操作失败");
        }
        return errorMsgMap;
    }

    private Map<String, String> buildItemStatusErrorMsgMap(List<QnhUpdateItemStatusDTO> updateParams,
            List<QnhUpdateStatusInnerResult> failedResults) {
        Map<String, QnhUpdateItemStatusDTO> paramMap = updateParams.stream().collect(Collectors.toMap
                (QnhUpdateItemStatusDTO::getQnhId, e -> e));
        Map<String, String> errorMsgMap = Maps.newHashMap();
        for (QnhUpdateStatusInnerResult innerResult : failedResults) {
            QnhUpdateItemStatusDTO param = paramMap.get(innerResult.getQnhid());
            if (param != null) {
                errorMsgMap.put(param.getSpuId(), Strings.coalesceToEmpty(param.getSpuName()) + innerResult.getMsg());
            }
        }
        return errorMsgMap;
    }

    private QnhBatchUpdateItemStatusResponse genQnhBatchUpdateItemStatusResponse(Integer code, String msg,
            Map<String, String>
                    errorMsgMap) {
        QnhBatchUpdateItemStatusResponse response = new QnhBatchUpdateItemStatusResponse();
        response.setCode(code);
        response.setMsg(msg);
        response.setErrorMsgMap(errorMsgMap);
        return response;
    }

    @Override
    public QnhBatchUpdateSaleStatusResponse batchUpdateSaleStatus(QnhBatchUpdateSaleStatusRequest request) {
        Map<Long, String> qnhRegionCodeMap = qnhStoreService.mappingQnhStoreIdByStoreIds(request.getTenantId(), request
                .getUpdateParams().stream().map(QnhUpdateSaleStatusDTO::getStoreId).collect(Collectors.toList()));
        QnhBatchUpdateSaleStatusParam updateParam = QnhConverterUtils.convertBatchUpdateStatusParam(request,
                qnhRegionCodeMap);
        QnhCommonResponse<List<QnhUpdateStatusInnerResult>> result = qnhChannelGateService.sendPost(
                request.getTenantId(),
                ChannelPostQnhEnum.UPDATE_SALE_STATUS, updateParam);
        if (Objects.isNull(result) || !result.success()) {
            return genQnhBatchUpdateSaleStatusResponse(ResultCode.FAIL.getCode(),
                    result == null ? ResultCode.FAIL.getMsg() :
                            result.getMsg(), buildSaleStatusErrorMsgMap(request.getUpdateParams()));
        } else {
            if (CollectionUtils.isNotEmpty(result.getStructuredData())) {
                List<QnhUpdateStatusInnerResult> failedResults = result.getStructuredData().stream()
                        .filter(e -> !e.success()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(failedResults)) {
                    return genQnhBatchUpdateSaleStatusResponse(ResultCode.FAIL.getCode(), ResultCode.FAIL.getMsg(),
                            buildSaleStatusErrorMsgMap(request.getUpdateParams(), failedResults));
                } else {
                    return genQnhBatchUpdateSaleStatusResponse(ResultCode.SUCCESS.getCode(),
                            ResultCode.SUCCESS.getMsg(),
                            Collections.emptyMap());
                }
            } else {
                return genQnhBatchUpdateSaleStatusResponse(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(),
                        Collections.emptyMap());
            }
        }
    }

    private Map<String, String> buildSaleStatusErrorMsgMap(List<QnhUpdateSaleStatusDTO> updateParams) {
        Map<String, String> errorMsgMap = Maps.newHashMap();
        for (QnhUpdateSaleStatusDTO dto : updateParams) {
            errorMsgMap.put(dto.getSpuId(), dto.getSpuName() + "因为系统错误操作失败");
        }
        return errorMsgMap;
    }

    private Map<String, String> buildSaleStatusErrorMsgMap(List<QnhUpdateSaleStatusDTO> updateParams,
            List<QnhUpdateStatusInnerResult> failedResults) {
        Map<String, QnhUpdateSaleStatusDTO> paramMap = updateParams.stream().collect(Collectors.toMap
                (QnhUpdateSaleStatusDTO::getQnhId, e -> e));
        Map<String, String> errorMsgMap = Maps.newHashMap();
        for (QnhUpdateStatusInnerResult innerResult : failedResults) {
            QnhUpdateSaleStatusDTO param = paramMap.get(innerResult.getQnhid());
            if (param != null) {
                errorMsgMap.put(param.getSpuId(), Strings.coalesceToEmpty(param.getSpuName()) + innerResult.getMsg());
            }
        }
        return errorMsgMap;
    }

    private QnhBatchUpdateSaleStatusResponse genQnhBatchUpdateSaleStatusResponse(Integer code, String msg,
            Map<String, String>
                    errorMsgMap) {
        QnhBatchUpdateSaleStatusResponse response = new QnhBatchUpdateSaleStatusResponse();
        response.setCode(code);
        response.setMsg(msg);
        response.setErrorMsgMap(errorMsgMap);
        return response;
    }

    @Override
    public QnhStoreProductListResponse queryQnhStoreProduct(QnhStoreProductListRequest request) {
        QnhProductQueryParam queryParam = qnhConverterService.qnhProductQueryParamMapping(request);
        String qnhStoreCode = qnhStoreService.mappingQnhStoreIdByStoreId(request.getTenantId(), request.getStoreId());
        queryParam.setOrg_code(qnhStoreCode);
        if (CollectionUtils.isNotEmpty(request.getQnhIds())) {
            queryParam.setIds(
                    request.getQnhIds().stream().map(qnhId -> QnhProductQueryId.buildStoreQuery(qnhId, qnhStoreCode))
                            .collect(Collectors.toList()));
        }
        QnhCommonResponse result = qnhChannelGateService.sendPost(request.getTenantId(),
                ChannelPostQnhEnum.QUERY_PRODUCT,
                queryParam);
        if (Objects.isNull(result) || result.fail()) {
            return genQnhStoreProductListResponse(ResultCode.FAIL.getCode(), result.getMsg());
        }
        if (result.getStructuredData() == null) {
            return genQnhStoreProductListResponse(ResultCode.SUCCESS.getCode(), result.getMsg());
        }
        List<QnhStoreProductDTO> qnhProductDTOS = qnhConverterService.qnhStoreProductDTOMapping(
                (List<QnhProduct>) result
                        .getStructuredData());
        QnhStoreProductListResponse response = genQnhStoreProductListResponse(ResultCode.SUCCESS.getCode(),
                result.getMsg());
        response.setData(qnhProductDTOS);
        response.setTotalSize(result.getTotal());
        return response;
    }

    private QnhStoreProductListResponse genQnhStoreProductListResponse(Integer code, String msg) {
        QnhStoreProductListResponse response = new QnhStoreProductListResponse();
        response.setCode(code);
        response.setMsg(msg);
        return response;
    }

    @Override
    public QnhOnlineCategoryListResponse queryOnlineCategory(QnhOnlineCategoryListRequest request) {
        QnhOnlineCategoryQueryParam queryParam = qnhConverterService.qnhOnlineCategoryQueryParamMapping(request);
        QnhCommonResponse result = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum
                .QUERY_ONLINE_CATEGORY, queryParam);
        if (Objects.isNull(result) || result.fail()) {
            return genQnhOnlineCategoryListResponse(ResultCode.FAIL.getCode(), result.getMsg());
        }
        if (result.getStructuredData() == null) {
            return genQnhOnlineCategoryListResponse(ResultCode.SUCCESS.getCode(), result.getMsg());
        }
        List<QnhOnlineCategoryDTO> qnhOnlineCategoryDTOS = qnhConverterService.qnhOnlineCategoryDTOMapping(
                (List<QnhOnlineCategory>) result.getStructuredData());
        QnhOnlineCategoryListResponse response = genQnhOnlineCategoryListResponse(ResultCode.SUCCESS.getCode(),
                result.getMsg());
        response.setData(qnhOnlineCategoryDTOS);
        response.setTotalSize(result.getTotal());
        return response;
    }

    @Override
    public QnhOfflineCategoryListResp queryOfflineCategory(QnhOfflineCategoryListReq req) {
        QnhOfflineCategoryQueryParam param = new QnhOfflineCategoryQueryParam();
        param.setCategoryCode(req.getCategoryCode());
        param.setCategoryName(req.getCategoryName());
        param.setPageNo(req.getPageNo());
        param.setPageSize(req.getPageSize());

        QnhCommonResponse<List<QnhOfflineCategory>> response = qnhChannelGateService.sendPost(req.getTenantId(),
                ChannelPostQnhEnum.QUERY_OFFLINE_CATEGORY, param);
        if (response == null) {
            return QnhOfflineCategoryListResp.ofFail(ResultCode.FAIL.getCode(), "接口响应体为空");
        }
        if (response.fail()) {
            return QnhOfflineCategoryListResp.ofFail(ResultCode.FAIL.getCode(), response.getMsg());
        }
        if (response.getStructuredData() == null) {
            return QnhOfflineCategoryListResp.ofFail(ResultCode.SUCCESS.getCode(), response.getMsg());
        }

        List<QnhOfflineCategoryDTO> offlineCategories = qnhConverterService.convertOfflineCategories(
                response.getStructuredData());
        return QnhOfflineCategoryListResp.of(offlineCategories, response.getTotal());
    }

    private QnhOnlineCategoryListResponse genQnhOnlineCategoryListResponse(Integer code, String msg) {
        QnhOnlineCategoryListResponse response = new QnhOnlineCategoryListResponse();
        response.setCode(code);
        response.setMsg(msg);
        return response;
    }

}
