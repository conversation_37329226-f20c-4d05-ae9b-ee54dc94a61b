package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.squirrel.client.StoreKey;
import com.meituan.linz.boot.exception.ServiceRpcException;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.qnh.migrate.thrift.BaseMigrateThriftService;
import com.meituan.shangou.qnh.migrate.thrift.common.Status;
import com.meituan.shangou.qnh.migrate.thrift.dto.base.EntMtAppInfoDTO;
import com.meituan.shangou.qnh.migrate.thrift.dto.base.request.EntMtAppInfoQueryRequest;
import com.meituan.shangou.qnh.migrate.thrift.dto.base.request.EntMtAppInfoUpdateRequest;
import com.meituan.shangou.qnh.migrate.thrift.dto.base.response.QueryEntMtAppInfoResponse;
import com.meituan.shangou.saas.tenant.highlevelclient.QnhBizModes;
import com.meituan.shangou.saas.tenant.thrift.ChannelAccessConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.EmployeeThriftService;
import com.meituan.shangou.saas.tenant.thrift.TenantManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.BaseResponse;
import com.meituan.shangou.saas.tenant.thrift.common.enums.OpenApproveStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigContentDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.AddChannelAccessConfigRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.SubjectConfigListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.response.MeituanNameResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.request.AddTenantFromAppRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.request.UpdateMtManagerRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.CreateTenantResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelAppDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtAuthInfoRespDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtTokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.tenant.CreateAppInfoDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPoiCallbackTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MtIsvTokenTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TenantBizModeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.ChannelAppMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.ChannelPoiCallbackMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DxPushMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.message.ChannelPoiCallbackMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiCallbackService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelPoiServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.AuthCallbackThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelPoiThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.PoiChannelAppThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelAddAppCallbackRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiBindUnbindCallbackRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiCallbackRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiInfoDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.SetMtTokenCacheRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.EnumUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.common.PoiChannelConstant;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.TenantAppInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.TenantAppInfoQueryByAppRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.auth.request.MtIsvAuthCallbackRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.auth.response.MtAuthCallbackResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum.SUCCESS;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/24 19:43
 * @Description:
 */
@Slf4j
@Service
public class ChannelPoiCallbackServiceImpl implements ChannelPoiCallbackService {

    private static final String SYSTEM = "系统";

    @Autowired
    private ChannelPoiThriftServiceProxy channelPoiThriftServiceProxy;


    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Autowired
    private TenantManageThriftService tenantManageThriftService;

    @Autowired
    private MtBrandChannelPoiServiceImpl mtBrandChannelPoiService;

    @Autowired
    private ChannelThriftService.Iface channelThriftService;

    @Autowired
    private ChannelAccessConfigThriftService channelAccessConfigThriftService;

    @Autowired
    private ChannelAppMapper channelAppMapper;


    @Autowired
    private EmployeeThriftService employeeThriftService;

    @Autowired
    private ConfigThriftService configThriftService;

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Autowired
    private ChannelPoiCallbackMessageProducer channelPoiCallbackMessageProducer;

    @Autowired
    private BaseMigrateThriftService baseMigrateThriftService;


    @Autowired
    private DxPushMessageProducer dxPushMessageProducer;

    @Resource
    private QnhBizModes qnhBizModes;

    @Autowired
    private AuthCallbackThriftServiceProxy authCallbackThriftServiceProxy;
    
    @Autowired
    private PoiChannelAppThriftServiceProxy poiChannelAppThriftServiceProxy;


    /**
     * 同步渠道门店信息
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus syncChannelPoi(ChannelPoiCallbackRequest request) {
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        if (Objects.isNull(channelTypeEnum)) {
            log.error("syncChannelPoi, 未知渠道, request:{}", request);
            return ResultGenerator.genFailResult("");
        }
        //转换得到tenantID
        CopAccessConfigDO copAccessConfigDO = copAccessConfigService.selectByTenantAppIdAndChannelId(request.getTenantAppId(), channelTypeEnum.getCode());
        if (copAccessConfigDO == null) {
            return ResultGenerator.genSuccessResult();
        }
        Long tenantId = copAccessConfigDO.getTenantId();

        if (MccConfigUtil.getAsyncTenant().contains(-1L) || MccConfigUtil.getAsyncTenant().contains(tenantId)) {
            ChannelPoiCallbackMessage message = new ChannelPoiCallbackMessage();
            message.setAppId(copAccessConfigDO.getAppId());
            message.setTenantId(tenantId);
            message.setChannelPoiCode(request.getAppPoiCode());
            message.setChannelId(channelTypeEnum.getCode());
            message.setType(ChannelPoiCallbackTypeEnum.POI_SYNC.getType());
            message.setMessage(null);
            boolean send = channelPoiCallbackMessageProducer.sendMessageSync(message);
            if (!send) {
                log.error("syncChannelPoi channelPoiCallbackMessageProducer.sendMessageSync fail.msg:{}", message);
                return ResultGenerator.genFailResult("");
            }
            return ResultGenerator.genSuccessResult();
        } else {
            return channelPoiThriftServiceProxy.incrementAndSynTenantChannelStoreInfo(tenantId, channelTypeEnum.getCode()
                    , request.getAppPoiCode(), copAccessConfigDO.getAppId());
        }
    }

    @Override
    public ResultStatus unBindChannelPoi(ChannelPoiBindUnbindCallbackRequest request) {
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        if (Objects.isNull(channelTypeEnum)) {
            log.error("syncChannelPoi, 未知渠道, request:{}", request);
            return ResultGenerator.genFailResult("");
        }

        String poiInfo = request.getPoiInfo();
        try {
            poiInfo = URLDecoder.decode(poiInfo, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new ServiceRpcException("poiInfo解码失败");
        }
        ChannelPoiInfoDto poiInfoDto = JacksonUtils.parse(poiInfo, ChannelPoiInfoDto.class);
        String opType = request.getOpType();
        log.info("绑定解绑token消息{}", request);

        Long tenantId;
        Long appId;
        String appKey;

        String sysParams="";

        if (isSupportMultiTenant(request, poiInfoDto.getWmPoiId())) {
            // 一应用多租户
            TenantAppInfoDTO appInfoDTO = authPoiChannel(request, poiInfoDto);
            if (appInfoDTO == null){
                return ResultGenerator.genFailResult("poiChannel授权失败");
            }
            tenantId = appInfoDTO.getTenantId();
            appId = appInfoDTO.getQnhAppId();
            appKey = appInfoDTO.getAppKey();
        }else{
            // 一应用一租户
            //转换得到tenantID
            CopAccessConfigDO copAccessConfigDO = copAccessConfigService.selectByTenantAppIdAndChannelId(request.getTenantAppId(), channelTypeEnum.getCode());
            if (copAccessConfigDO == null) {
                return ResultGenerator.genSuccessResult();
            }
            tenantId = copAccessConfigDO.getTenantId();
            appId = copAccessConfigDO.getAppId();
            appKey = copAccessConfigDO.getTenantAppId();
            sysParams = copAccessConfigDO.getSysParams();
        }

        ChannelPoiCallbackMessage message = new ChannelPoiCallbackMessage();
        message.setAppId(appId);
        message.setTenantId(tenantId);
        message.setChannelPoiCode(poiInfoDto.getAppPoiCode());
        message.setChannelId(channelTypeEnum.getCode());
        message.setMessage(null);

        //门店解绑
        if ("2".equals(opType)) {
            List<Long> unBindPoiTenantId = MccConfigUtil.getUnBindPoiTenantId();
            if (unBindPoiTenantId.contains(tenantId) || unBindPoiTenantId.contains(-1L)) {
                return ResultGenerator.genSuccessResult();
            }

            if (MccConfigUtil.getAsyncTenant().contains(-1L) || MccConfigUtil.getAsyncTenant().contains(tenantId)) {
                // token需要在ocms channel 服务删除
                channelPoiThriftServiceProxy.deleteToke(tenantId, channelTypeEnum.getCode(), poiInfoDto.getAppPoiCode());
                channelPoiThriftServiceProxy.deleteToken(tenantId, channelTypeEnum.getCode(), poiInfoDto.getAppPoiCode(),appKey);
                message.setType(ChannelPoiCallbackTypeEnum.POI_UNBIND.getType());
                boolean send = channelPoiCallbackMessageProducer.sendMessageSync(message);
                if (!send) {
                    log.error("unBindChannelPoi channelPoiCallbackMessageProducer.sendMessageSync unbind fail.msg:{}", message);
                    return ResultGenerator.genFailResult("");
                }
                dxPushMessageProducer.pushPoiBindMessage(tenantId, appKey,channelTypeEnum.getCode(),poiInfoDto.getAppPoiCode(),"门店解绑应用");
                return ResultGenerator.genSuccessResult();
            } else {
                return channelPoiThriftServiceProxy.unBindChannelPoi(tenantId, channelTypeEnum.getCode(), poiInfoDto.getAppPoiCode());
            }

        }//token重推
        else if ("3".equals(opType)) {
            StoreKey storeKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + poiInfoDto.getAppPoiCode());
            try {
                String tokenString = URLDecoder.decode(request.getTokenInfo(), "UTF-8");
                Map<String, Object> tokenMap = JacksonUtils.parseMap(tokenString, String.class, Object.class);
                mtBrandChannelGateService.setAccessToken(storeKey, tokenMap, appKey, poiInfoDto.getAppPoiCode(), tenantId);
                dxPushMessageProducer.pushPoiBindMessage(tenantId,appKey,channelTypeEnum.getCode(),poiInfoDto.getAppPoiCode(),"门店重推Token成功");
            } catch (UnsupportedEncodingException e) {
                dxPushMessageProducer.pushPoiBindMessage(tenantId,appKey,channelTypeEnum.getCode(),poiInfoDto.getAppPoiCode(),"门店重推Token失败");
                throw new ServiceRpcException("tokenInfo解码失败");
            }
            return ResultGenerator.genSuccessResult();
        }//门店绑定
        else {
            if (StringUtils.isNotEmpty(sysParams) && channelTypeEnum.getCode() == ChannelTypeEnum.MEITUAN.getCode()) {
                Map<String, Object> sysParam = JSON.parseObject(sysParams);
                mtBrandChannelGateService.setToken(tenantId, channelTypeEnum.getCode(), poiInfoDto.getAppPoiCode(), sysParam, appKey);
            }
            if (MccConfigUtil.getAsyncTenant().contains(-1L) || MccConfigUtil.getAsyncTenant().contains(tenantId)) {
                message.setType(ChannelPoiCallbackTypeEnum.POI_BIND.getType());
                boolean send = channelPoiCallbackMessageProducer.sendMessageSync(message);
                if (!send) {
                    log.error("unBindChannelPoi channelPoiCallbackMessageProducer.sendMessageSync bind fail.msg:{}", message);
                    return ResultGenerator.genFailResult("");
                }
//                dxPushMessageProducer.pushPoiBindMessage(tenantId,copAccessConfigDO.getTenantAppId(),channelTypeEnum.getCode(),poiInfoDto.getAppPoiCode(),"门店绑定应用");
                return ResultGenerator.genSuccessResult();
            } else {
                return channelPoiThriftServiceProxy.incrementAndSynTenantChannelStoreInfo(tenantId,
                        channelTypeEnum.getCode(), poiInfoDto.getAppPoiCode(), appId);
            }
        }
    }

    /**
     * poi_channel授权
     *
     * @param request
     * @param poiInfoDto
     */
    private TenantAppInfoDTO authPoiChannel(ChannelPoiBindUnbindCallbackRequest request, ChannelPoiInfoDto poiInfoDto) {
        TenantAppInfoDTO appInfoDTO = null;
        try {
            // 调用poi_channel，关联门店和租户
            MtIsvAuthCallbackRequest mtIsvAuthCallbackRequest = new MtIsvAuthCallbackRequest();
            mtIsvAuthCallbackRequest.setAppKey(request.getTenantAppId());
            mtIsvAuthCallbackRequest.setOpType(request.getOpType());
            mtIsvAuthCallbackRequest.setWmPoiId(Long.valueOf(poiInfoDto.getWmPoiId()));
            mtIsvAuthCallbackRequest.setAppPoiCode(poiInfoDto.getAppPoiCode());
            if ("3".equals(request.getOpType())) {
                String tokenString = URLDecoder.decode(request.getTokenInfo(), "UTF-8");
                Map<String, Object> tokenMap = JacksonUtils.parseMap(tokenString, String.class, Object.class);
                MtIsvAuthCallbackRequest.Token token = MtAuthInfoRespDTO.convertToMtIsvAuthCallbackRequestTokenFromMap(tokenMap);
                mtIsvAuthCallbackRequest.setToken(token);
            }
            if (StringUtils.isNotBlank(request.getCustomParam())){
                mtIsvAuthCallbackRequest.setState(URLDecoder.decode(request.getCustomParam(), "UTF-8"));
            }
            MtAuthCallbackResponse response = authCallbackThriftServiceProxy.mtAuthCallback(mtIsvAuthCallbackRequest);

            if (response.getStatus().getCode() != 0 || response.getTenantAppInfo() == null){
                throw new BizException(response.getStatus().getMessage());
            }
            appInfoDTO = response.getTenantAppInfo();
        } catch (Exception e) {
            log.error("poiChannel授权失败, request={}", request, e);
        }
        return appInfoDTO;
    }

    private Boolean isSupportMultiTenant(ChannelPoiBindUnbindCallbackRequest request, Long wmPoiId) {
        Boolean supportMultiTenant = false;
        try{
            if ("1".equals(request.getOpType()) || "2".equals(request.getOpType()) || "4".equals(request.getOpType())) {
                // 绑定、解绑、修改app_poi_code操作会返回自定义参数
                if (StringUtils.isNotBlank(request.getCustomParam())) {
                    Map<String, Object> paramMap = JacksonUtils.parseMap(URLDecoder.decode(request.getCustomParam(), "UTF-8"), String.class, Object.class);
                    if (paramMap.containsKey("supportMultiTenant")) {
                        supportMultiTenant = (Integer) paramMap.get("supportMultiTenant") == 1;
                    }
                }
            }else {
                // 推token不返回自定参数
                TenantAppInfoDTO tenantAppInfoDTO = getTenantAppInfo(request, wmPoiId);
                supportMultiTenant = tenantAppInfoDTO != null && tenantAppInfoDTO.getQnhAppId() > PoiChannelConstant.MT_QNH_APP_ID_BEGIN;
            }

        }catch (Exception e){
            log.error("ChannelPoiCallbackService.isSupportMultiTenant 解析customParam失败", e);
        }
        return supportMultiTenant;
    }

    private TenantAppInfoDTO getTenantAppInfo(ChannelPoiBindUnbindCallbackRequest request, Long wmPoiId) {
        String subAppKey = String.valueOf(wmPoiId);
        TenantAppInfoQueryByAppRequest tenantAppInfoQueryByAppRequest = new TenantAppInfoQueryByAppRequest();
        tenantAppInfoQueryByAppRequest.setAppKey(request.getTenantAppId());
        tenantAppInfoQueryByAppRequest.setChannelId(ChannelTypeEnum.MEITUAN.getCode());
        tenantAppInfoQueryByAppRequest.setSubAppKeyList(Arrays.asList(String.valueOf(subAppKey)));
        List<TenantAppInfoDTO> tenantAppInfoDTOList = poiChannelAppThriftServiceProxy.queryTenantAppInfoListByApp(tenantAppInfoQueryByAppRequest);
        tenantAppInfoDTOList = Fun.filter(tenantAppInfoDTOList, tenantAppInfoDTO -> tenantAppInfoDTO.getSubAppKey().equals(subAppKey));
        if (CollectionUtils.isEmpty(tenantAppInfoDTOList)){
            log.info("isSupportMultiTenant 未获取到租户应用关联信息, tenantAppInfoQueryByAppRequest={}", tenantAppInfoQueryByAppRequest);
            return null;
        }
        TenantAppInfoDTO tenantAppInfoDTO = tenantAppInfoDTOList.get(0);
        return tenantAppInfoDTO;

    }

    @Override
    public ResultStatus applyApp(ChannelAddAppCallbackRequest request) {
        String createApInfo = request.getCreateApInfo();
        try {
            createApInfo = URLDecoder.decode(createApInfo, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new ServiceRpcException("createApInfo解码失败");
        }
        CreateAppInfoDto createAppInfoDto = JacksonUtils.parse(createApInfo, CreateAppInfoDto.class);
        if (StringUtils.isNotEmpty(createAppInfoDto.getTenantId())) {
            return ResultGenerator.genSuccessResult();
        }
        AddTenantFromAppRequest addTenantFromAppRequest = new AddTenantFromAppRequest();
        addTenantFromAppRequest.setBrandName(createAppInfoDto.getBrandName());
        addTenantFromAppRequest.setProvinceId(createAppInfoDto.getProvinceId());
        addTenantFromAppRequest.setCityId(createAppInfoDto.getCityId());
        addTenantFromAppRequest.setAreaId(createAppInfoDto.getAreaId());
        addTenantFromAppRequest.setApplierPhone(createAppInfoDto.getApplierPhone());
        addTenantFromAppRequest.setApplierName(createAppInfoDto.getApplierName());
        addTenantFromAppRequest.setAddress(createAppInfoDto.getAddress());
        addTenantFromAppRequest.setReviewer(createAppInfoDto.getReviewer());
        addTenantFromAppRequest.setApplyId(createAppInfoDto.getApplyId());
        addTenantFromAppRequest.setIsApprove(createAppInfoDto.getIsApprove());
        addTenantFromAppRequest.setBrandId(createAppInfoDto.getBrandId());
        addTenantFromAppRequest.setBAccountType(createAppInfoDto.getBAccountType());
        addTenantFromAppRequest.setWmPoiName(createAppInfoDto.getWmPoiName());
        BaseResponse baseResponse = tenantManageThriftService.applyApp(addTenantFromAppRequest);
        if (SUCCESS.getCode() != baseResponse.getStatus().getCode()) {
            return ResultGenerator.genResult(ResultCode.FAIL, "创建客户失败");
        }
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus applyAppCall(ChannelAddAppCallbackRequest request) {
        dxPushMessageProducer.pushPoiBindMessage("收到应用创建通知");
        String createApInfo = request.getCreateApInfo();
        try {
            createApInfo = URLDecoder.decode(createApInfo, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            dxPushMessageProducer.pushPoiBindMessage("应用创建推送解码失败");
            throw new ServiceRpcException("createApInfo解码失败");
        }
        CreateAppInfoDto createAppInfoDto = JacksonUtils.parse(createApInfo, CreateAppInfoDto.class);

        //系统审核逻辑处理
        if (SYSTEM.equals(createAppInfoDto.getReviewer())) {
            return processEntMtAppInfo(createAppInfoDto);
        }

        int isBaichuan = createAppInfoDto.getIsBaichuan();
        Long tenantId = 0L;
        Long tenantIdExist = copAccessConfigService.selectTenantId(ChannelType.MEITUAN.getValue(),
                createAppInfoDto.getNewAppId());
        if (tenantIdExist != null) {
            log.info("新应用已配置");
            dxPushMessageProducer.pushPoiBindMessage("新应用已配置");
            return ResultGenerator.genSuccessResult();
        }
        TenantBusinessModeEnum biz = null;
        if (0 == isBaichuan) {
            biz = TenantBusinessModeEnum.CONVENIENCE_STORE;
            AddTenantFromAppRequest addTenantFromAppRequest = new AddTenantFromAppRequest();
            addTenantFromAppRequest.setBrandName(createAppInfoDto.getBrandName());
            addTenantFromAppRequest.setProvinceId(createAppInfoDto.getProvinceId());
            addTenantFromAppRequest.setCityId(createAppInfoDto.getCityId());
            addTenantFromAppRequest.setAreaId(createAppInfoDto.getAreaId());
            addTenantFromAppRequest.setApplierPhone(createAppInfoDto.getApplierPhone());
            addTenantFromAppRequest.setApplierName(createAppInfoDto.getApplierName());
            addTenantFromAppRequest.setAddress(createAppInfoDto.getAddress());
            addTenantFromAppRequest.setReviewer(createAppInfoDto.getReviewer());
            addTenantFromAppRequest.setApplyId(createAppInfoDto.getApplyId());
            addTenantFromAppRequest.setIsApprove(createAppInfoDto.getIsApprove());
            addTenantFromAppRequest.setBrandId(createAppInfoDto.getBrandId());
            addTenantFromAppRequest.setBAccountType(createAppInfoDto.getBAccountType());
            addTenantFromAppRequest.setWmPoiName(createAppInfoDto.getWmPoiName());
            //创建租户
            CreateTenantResponse createTenantResponse = tenantManageThriftService.applyAppCall(addTenantFromAppRequest);
            if (SUCCESS.getCode() != createTenantResponse.getStatus().getCode()) {
                dxPushMessageProducer.pushPoiBindMessage("创建租户失败");
                return ResultGenerator.genResult(ResultCode.FAIL, "创建租户失败");
            }
            tenantId = createTenantResponse.getTenantId();
        } else {
            tenantId = Long.valueOf(createAppInfoDto.getTenantId());
            // 查询租户信息
            biz = qnhBizModes.qnhBizModeEnum(tenantId);
        }


        if (OpenApproveStatusEnum.REJECT.getKey() == createAppInfoDto.getIsApprove()) {
            return ResultGenerator.genSuccessResult();
        }
        UpdateMtManagerRequest updateMtManagerRequest = new UpdateMtManagerRequest();
        updateMtManagerRequest.setTenantIds(Arrays.asList(tenantId));
        updateMtManagerRequest.setMtManagerMis(createAppInfoDto.getReviewer());
        MeituanNameResponse meituanNameResponse = employeeThriftService.queryMeituanNameByMis(createAppInfoDto.getReviewer());
        if (SUCCESS.getCode() == meituanNameResponse.getStatus().getCode()) {
            updateMtManagerRequest.setMtManagerName(meituanNameResponse.getName());
            updateMtManagerRequest.setOptUser(createAppInfoDto.getReviewer());
            updateMtManagerRequest.setOptUserName(meituanNameResponse.getName());
            updateMtManagerRequest.setSkipAuth(true);
            tenantManageThriftService.updateMtManager(updateMtManagerRequest);
        }

        ResultStatus resultStatus = mtBrandChannelPoiService.appOnline(Integer.valueOf(createAppInfoDto.getNewAppId()));
        if (SUCCESS.getCode() != resultStatus.getCode()) {
            log.error("新应用上线失败" + resultStatus.getMsg());
            dxPushMessageProducer.pushPoiBindMessage("新应用上线失败");
        }
        resultStatus = mtBrandChannelPoiService.getAppSecret(Integer.valueOf(createAppInfoDto.getNewAppId()), createAppInfoDto.getAuthorizeCode());
        if (SUCCESS.getCode() != resultStatus.getCode()) {
            log.error("获取新应用密钥失败" + resultStatus.getMsg());
            dxPushMessageProducer.pushPoiBindMessage("获取新应用密钥失败");
            return ResultGenerator.genResult(ResultCode.FAIL, "获取新应用密钥失败");
        }
        String appSecret = resultStatus.getData();
        AddChannelAccessConfigRequest addChannelAppIdAndSecretRequest = new AddChannelAccessConfigRequest();
        addChannelAppIdAndSecretRequest.setChannelId(ChannelType.MEITUAN.getValue());
        addChannelAppIdAndSecretRequest.setAppId(createAppInfoDto.getNewAppId());
        addChannelAppIdAndSecretRequest.setSecret(appSecret);
        List<ChannelAppDO> channelAppDOS = channelAppMapper.selectByTenantIdAndChannelId(tenantId, ChannelType.MEITUAN.getValue());
        List<String> appNameSet = Fun.map(channelAppDOS, ChannelAppDO::getAppName);
        String appName = createAppInfoDto.getAppName();
        int i = 2;
        while (true) {
            if (!appNameSet.contains(appName)) {
                break;
            }
            appName = createAppInfoDto.getAppName() + i++;
        }
        addChannelAppIdAndSecretRequest.setChannelAppName(appName);
        String bAccountType = createAppInfoDto.getBAccountType();
        if ("200".equals(bAccountType)) {
            addChannelAppIdAndSecretRequest.setType(2);
        } else {
            addChannelAppIdAndSecretRequest.setType(1);
        }
        addChannelAppIdAndSecretRequest.setTenantId(tenantId);
        addChannelAppIdAndSecretRequest.setOptUser("system_open");
        List<CopAccessConfigDO> tenantChannelConfigApps = copAccessConfigService.findTenantChannelConfigApp(tenantId, ChannelType.MEITUAN.getValue());
        if (CollectionUtils.isEmpty(tenantChannelConfigApps)) {
//            List<ConfigContentDto> yiJianTenantChannelConfig = MccConfigUtil.getYiJianTenantChannelConfig();
            Map<String ,List<ConfigContentDto>> configMap = MccConfigUtil.getYiJianTenantChannelConfigV2();
            List<ConfigContentDto> config = configMap.get(biz.getKey());
            if (CollectionUtils.isEmpty(config)) {
                dxPushMessageProducer.pushPoiBindMessage(tenantId,appName,100,null,"业务线无渠道应用默认配置");
            }
            addChannelAppIdAndSecretRequest.setConfigs(config);
        } else {
            SubjectConfigListResponse subjectConfigListResponse = configThriftService.queryAllChannelConfigList(tenantId, tenantId, ChannelType.MEITUAN.getValue());
            List<ConfigContentDto> configs = subjectConfigListResponse.getConfigs();
            addChannelAppIdAndSecretRequest.setConfigs(configs);
        }

        BaseResponse response = null;
        try {
            response = channelAccessConfigThriftService.addChannelAccessConfig(addChannelAppIdAndSecretRequest);
        } catch (Exception e) {
            dxPushMessageProducer.pushPoiBindMessage("添加渠道应用失败");
            return ResultGenerator.genResult(ResultCode.FAIL, "添加渠道应用失败");
        }
        if (SUCCESS.getCode() != response.getStatus().code) {
            dxPushMessageProducer.pushPoiBindMessage("添加渠道应用失败");
            return ResultGenerator.genResult(ResultCode.FAIL, "添加渠道应用失败");
        }
        return ResultGenerator.genSuccessResult();
    }

    private ResultStatus processEntMtAppInfo(CreateAppInfoDto createAppInfoDto) {
        EntMtAppInfoQueryRequest entMtAppInfoQueryRequest = new EntMtAppInfoQueryRequest();
        entMtAppInfoQueryRequest.setAppId(createAppInfoDto.getNewAppId());
        QueryEntMtAppInfoResponse queryEntMtAppInfoResponse = baseMigrateThriftService.queryEntMtAppInfo(entMtAppInfoQueryRequest);
        if (!queryEntMtAppInfoResponse.getStatus().isSuccess()) {
            return ResultGenerator.genResult(ResultCode.FAIL, "查询美团应用信息失败");
        }

        //查询美团配置信息是否存在
        List<EntMtAppInfoDTO> entMtAppInfoList = queryEntMtAppInfoResponse.getEntMtAppInfoList();
        if (CollectionUtils.isEmpty(entMtAppInfoList)) {
            log.warn("查询美团配置信息为空,跳过更新秘钥");
            return ResultGenerator.genSuccessResult();
        }

        ResultStatus resultStatus = mtBrandChannelPoiService.getAppSecret(Integer.valueOf(createAppInfoDto.getNewAppId()), createAppInfoDto.getAuthorizeCode());
        if (SUCCESS.getCode() != resultStatus.getCode()) {
            log.error("获取新应用密钥失败" + resultStatus.getMsg());
            return ResultGenerator.genResult(ResultCode.FAIL, "获取新应用密钥失败");
        }

        EntMtAppInfoUpdateRequest entMtAppInfoUpdateRequest = new EntMtAppInfoUpdateRequest();
        entMtAppInfoUpdateRequest.setAppId(createAppInfoDto.getNewAppId());
        entMtAppInfoUpdateRequest.setAppSecret(resultStatus.getData());
        Status status = baseMigrateThriftService.updateEntMtAppInfo(entMtAppInfoUpdateRequest);
        if (!status.isSuccess()) {
            log.error("修改美团应用信息秘钥失败" + resultStatus.getMsg());
            return ResultGenerator.genResult(ResultCode.FAIL, "修改美团应用信息秘钥失败");
        }
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus setMtTokenCache(SetMtTokenCacheRequest request) {
        if (Objects.isNull(request) || request.getTenantId() == 0 || StringUtils.isBlank(request.getChannelPoiCode())
                || Objects.isNull(request.getMtTokenMessage())) {
            log.info("setMtTokenCache param error, request={}", request);
            return ResultGenerator.genFailResult("参数错误");
        }
        com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.MtTokenMessage requestMtTokenMessage = request.getMtTokenMessage();
        if (StringUtils.isBlank(requestMtTokenMessage.getAccessToken())
                || StringUtils.isBlank(requestMtTokenMessage.getRefreshToken())
                || requestMtTokenMessage.getExpires() == 0 || requestMtTokenMessage.getRefreshExpires() == 0) {
            log.info("setMtTokenCache param error, request={}", request);
            return ResultGenerator.genFailResult("token参数错误");
        }


        Long tenantId = request.getTenantId();
        String channelPoiCode = request.getChannelPoiCode();
        String appKey = request.getAppKey();
        
        
        
        MtTokenMessage mtTokenMessage = new MtTokenMessage();
        mtTokenMessage.setAccessToken(requestMtTokenMessage.getAccessToken());
        mtTokenMessage.setRefreshToken(requestMtTokenMessage.getRefreshToken());
        mtTokenMessage.setExpires(requestMtTokenMessage.getExpires());
        mtTokenMessage.setRefreshExpires(requestMtTokenMessage.getRefreshExpires());
        
        
        return channelPoiThriftServiceProxy.setMtToken(tenantId, channelPoiCode, mtTokenMessage,appKey);
    }
}
