package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhBatchUpdateItemStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhBatchUpdateSaleStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhOfflineCategoryListReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhOnlineCategoryListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhProductQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhStoreProductListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.QnhUpdateOnlinePriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhBatchUpdateItemStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhBatchUpdateSaleStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhOfflineCategoryListResp;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhOnlineCategoryListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhProductQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhStoreProductListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.resp.QnhUpdateOnlinePriceResponse;

/**
 * @Author: wangyihao04
 * @Date: 2021-12-27 20:14
 * @Mail: <EMAIL>
 */
public interface QnhProductService {

    QnhProductQueryResponse queryQnhProduct(QnhProductQueryRequest request);

    QnhUpdateOnlinePriceResponse updateOnlinePrice(QnhUpdateOnlinePriceRequest request);

    QnhBatchUpdateItemStatusResponse batchUpdateItemStatus(QnhBatchUpdateItemStatusRequest request);

    QnhBatchUpdateSaleStatusResponse batchUpdateSaleStatus(QnhBatchUpdateSaleStatusRequest request);

    QnhStoreProductListResponse queryQnhStoreProduct(QnhStoreProductListRequest request);

    QnhOnlineCategoryListResponse queryOnlineCategory(QnhOnlineCategoryListRequest request);

    QnhOfflineCategoryListResp queryOfflineCategory(QnhOfflineCategoryListReq req);

}
