package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.enums.OrderProductLabelEnum;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ELmActivityTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderProductDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ProductLabelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ActivityPromotionSplitUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ActivityDiscount;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.BillGetResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.BillOrderDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.CustomCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ElmBillDetailFee;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ElmBillFee;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.OrderActivitiesInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.OrderSkuDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ProductActivity;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ProductSubsidy;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ProductSubsidyDiscountDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.SkuQueryDetailResultDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhChannelOrderSettlementDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ElmExtraInfoDetailEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ElmFeeDetailEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelLeafStoreCategory;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelStoreCategory;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ActivityShareDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ChannelOrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GoodsActivityDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GoodsSharedActivityItem;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.SettlementExtraInfoDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.SettlementFeeDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils.getIntegerValue;

/***
 * author : <EMAIL> 
 * data : 2019/6/14 
 * time : 下午3:00
 **/
@Slf4j
public class ElmConverterUtil {

    private static final String SEND_IMMEDIATELY_IS_BOOKING_TRUE = "2";
    private static final String UNDERSCORE = "-";


    /***
     * 问题：饿百商品活动分摊上没有 商品所有优惠的原总价。只能取商品参与的某一个活动的活动原价。假设一个商品只能参加一个活动，这样做没有问题，假如一个商品参与了多个活动
     * 只取一个商品的原价不一定正确。
     * **/
    public static List<GoodsActivityDetailDTO> parseGoodsActivities(ChannelOrderDetailDTO channelOrderDetailDTO, List<OrderSkuDetail> skuDetails, List<OrderActivitiesInfo> discount) {

        Map<String/* baidu_product_id**/, ProductActivity> productActivityMap = buildProductActivityMap(discount);

        return skuDetails.stream().filter(e -> Objects.nonNull(e.getProduct_subsidy()))
                .map(skuDetail -> {
                    ProductActivity productActivity = productActivityMap.get(skuDetail.getBaidu_product_id());
                    ProductSubsidy subsidy = skuDetail.getProduct_subsidy();
                    GoodsActivityDetailDTO bean = new GoodsActivityDetailDTO();
                    bean.setCustomSkuId(skuDetail.getCustom_sku_id());
                    bean.setSkuCount(skuDetail.getProduct_amount());
                    bean.setTotalDiscount(subsidy.getDiscount());
                    bean.setChannelCost(subsidy.getBaidu_rate());
                    bean.setTenantCost(subsidy.getShop_rate());
                    bean.setAgentCost(subsidy.getAgent_rate());
                    bean.setLogisticsCost(subsidy.getLogistics_rate());
                    bean.setChannelJiFenCost(0);
                    List<GoodsSharedActivityItem> goodsSharedActivityItems = parseGoodActivityDetail(subsidy.getDiscount_detail(), productActivity, skuDetail);
                    bean.setGoodActivityDetail(goodsSharedActivityItems);
                    if (productActivity != null) {
                        //活动价格
                        bean.setActivityPrice(NumberUtils.toLong(productActivity.getNow_price()));
                        //原单价
                        bean.setOriginPrice(NumberUtils.toInt(productActivity.getOrig_price()));
                    }

                    return bean;
                }).collect(Collectors.toList());
    }

    private static List<GoodsSharedActivityItem> parseGoodActivityDetail(List<ProductSubsidyDiscountDetail> discount_detail, ProductActivity productActivity, OrderSkuDetail skuDetail) {
        if (discount_detail != null) {
            return discount_detail.stream().map(e -> {
                GoodsSharedActivityItem item = new GoodsSharedActivityItem();
                //商品优惠数量优先去优惠活动的，如果取不到取下单数量
                int count = productActivity != null && productActivity.getActivity_product_num() > 0 ? productActivity.getActivity_product_num() : skuDetail.getProduct_amount();
                item.setPromotionCount(count);
                item.setTenantCost(e.getShop_rate());
                item.setChannelCost(e.getBaidu_rate());
                item.setChannelPromotionType(e.getType());
                item.setActivityId(e.getActivity_id());
                item.setPromotionRemark("");
                return item;
            }).collect(Collectors.toList());
        }
        return null;

    }

    private static Map<String, ProductActivity> buildProductActivityMap(List<OrderActivitiesInfo> discount) {
        Map<String, ProductActivity> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(discount)) {
            discount.stream().filter(e -> e.getProducts() != null)
                    .forEach(e -> {
                        e.getProducts().stream().forEach(pa -> {
                            String baidu_product_id = pa.getBaidu_product_id();
                            ProductActivity old = map.put(baidu_product_id, pa);
                            if (old != null) {
                                log.info("同一商品参加不同的活动，new:{}, old:{}", pa, old);
                            }
                        });
                    });
        }
        return map;
    }


    /**
     * 构造以下键值对
     * 1) 活动商品信息: baidu_product_id-activity_id: {desc}
     * 2) 店铺级别的: key没有商品ID： activity_id: {activity_product_num, desc}
     */
    public static Map<String, ActivityDiscount> buildProductDiscountMap(List<OrderActivitiesInfo> discountList) {
        Map<String, ActivityDiscount> productDiscountMap = Maps.newHashMap();

        if (CollectionUtils.isEmpty(discountList)) {
            return productDiscountMap;
        }

        for (OrderActivitiesInfo discount : discountList) {
            List<ProductActivity> productList = discount.getProducts();

            if (CollectionUtils.isEmpty(productList)) {
                // 1）如果改活动没有商品参加，说明是店铺级的活动，不分摊到商品
                ActivityDiscount activityDiscount = new ActivityDiscount();

                activityDiscount.setActivity_id(discount.getActivity_id());
                activityDiscount.setShop_rate(discount.getShop_rate());
                activityDiscount.setBaidu_rate(discount.getBaidu_rate());
                activityDiscount.setType(discount.getType());
                activityDiscount.setDesc(discount.getDesc());

                productDiscountMap.put(discount.getActivity_id(), activityDiscount);
                // 继续处理下一条活动数据
                continue;
            }

            // 2）如果活动分摊到商品级别
            for (ProductActivity product : productList) {
                ActivityDiscount activityDiscount = new ActivityDiscount();

                activityDiscount.setActivity_id(discount.getActivity_id());
                activityDiscount.setShop_rate(discount.getShop_rate());
                activityDiscount.setBaidu_rate(discount.getBaidu_rate());
                activityDiscount.setType(discount.getType());
                activityDiscount.setDesc(discount.getDesc());
                activityDiscount.setActivity_product_num(product.getActivity_product_num());

                String key = product.getBaidu_product_id() + UNDERSCORE + discount.getActivity_id();


                if (productDiscountMap.get(key) == null) {
                    productDiscountMap.put(key, activityDiscount);
                } else {
                    // 多规格商品, 会出现 activity_id 和 baidu_product_id 一样，用activity_product_num较大的覆盖较小的
                    // 注意此时活动信息里面的shop_rate和baidu_rate可能不对，但是不影响多规格商品的分摊，因为多规格商品是商品级别的活动，
                    // 分摊时候只用到activity_product_num，整单级别的活动又不存在相同的key（activity_id）。
                    if (productDiscountMap.get(key).getActivity_product_num() < product.getActivity_product_num()) {
                        productDiscountMap.put(key, activityDiscount);
                    }
                }

            }
        }

        return productDiscountMap;
    }


    /**
     *
     */
    public static  List<ActivityShareDetailDTO> splitSkuPromotionFromActivityDiscount(List<OrderSkuDetail> skuDetailList,
                                                                                      Map<String, ActivityDiscount> productDiscountMap,
                                                                                      List<OrderProductDetailDTO> productDetailDTOList,
                                                                                      long tenantId) {
        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(skuDetailList)) {
            return activityShareDetailDTOList;
        }

        // 已经处理过的活动活动
        Set<ActivityDiscount> alreadyHandleActivitySet = new HashSet<>();
        log.info("解析活动分摊数据，productDiscountMap={},  skuDetailList={}", productDiscountMap, skuDetailList);
        // 1) 先处理已拆分到商品级的优惠
        for (OrderSkuDetail orderSkuDetail : skuDetailList) {
            if (orderSkuDetail.getProduct_subsidy() == null || CollectionUtils.isEmpty(orderSkuDetail.getProduct_subsidy().getDiscount_detail())) {
                // 该商品没有优惠
                continue;
            }

            // 该商品上的优惠
            List<ProductSubsidyDiscountDetail> discountDetailList = orderSkuDetail.getProduct_subsidy().getDiscount_detail();

            for (ProductSubsidyDiscountDetail discountDetail : discountDetailList) {
                String key = orderSkuDetail.getBaidu_product_id() + UNDERSCORE + discountDetail.getActivity_id();
                // 优先取商品级别的活动，取不到就取店铺级别的活动。
                ActivityDiscount activityDiscount = productDiscountMap.get(key) != null ? productDiscountMap.get(key) : productDiscountMap.get(discountDetail.getActivity_id());
                // 活动数量
                int activityProductNum = activityDiscount != null ? activityDiscount.getActivity_product_num() : 0;
                String promotionRemark = activityDiscount != null ? activityDiscount.getDesc() : "";

                // 商品数量
                Integer productAmount = orderSkuDetail.getProduct_amount();
                // 商品原价
                Integer productPrice = orderSkuDetail.getProduct_price();

                // 默认活动数量就是商品数量（满减分摊类型的优惠活动）
                // 多规格商品的活动数量取最大的，但是可能大于实际商品数量
                if (activityProductNum == 0 || activityProductNum > productAmount) {
                    activityProductNum = productAmount;
                }

                ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
                activityShareDetailDTO.setActivityId(discountDetail.getActivity_id());
                activityShareDetailDTO.setPromotionRemark(promotionRemark);
                activityShareDetailDTO.setChannelPromotionType(discountDetail.getType());
                // 跟 OrderProductDetailDTO 的 skuid 取值保持一致。
                activityShareDetailDTO.setSkuId(StringUtils.isNotBlank(orderSkuDetail.getCustomSkuSpecId()) ? orderSkuDetail.getCustomSkuSpecId() : orderSkuDetail.getCustom_sku_id());
                activityShareDetailDTO.setCustomSpu(orderSkuDetail.getCustom_sku_id());
                activityShareDetailDTO.setPromotionCount(activityProductNum);
                activityShareDetailDTO.setSkuCount(productAmount);
                activityShareDetailDTO.setTotalOriginPrice(productPrice * productAmount);

                // 单品优惠的需要乘以活动数量
                activityShareDetailDTO.setChannelCost(discountDetail.getBaidu_rate());
                activityShareDetailDTO.setTenantCost(discountDetail.getShop_rate());
                // 用以上的数据计算优惠后的总价
                activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() - activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());

                activityShareDetailDTOList.add(activityShareDetailDTO);
                alreadyHandleActivitySet.add(activityDiscount);
            }
        }

        // 分摊其他整单活动
        activityShareDetailDTOList.addAll(
                splitOtherActivityDiscount(productDetailDTOList, productDiscountMap, alreadyHandleActivitySet, tenantId)
        );

        return activityShareDetailDTOList;

    }


    /**
     * 有些特殊活动没有被分摊到商品上，就要我们来分摊。
     */
    private static List<ActivityShareDetailDTO> splitOtherActivityDiscount(List<OrderProductDetailDTO> productDetailDTOList,
                                                                           Map<String, ActivityDiscount> productDiscountMap,
                                                                           Set<ActivityDiscount> alreadyHandleSet, long tenantId) {
        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(productDetailDTOList)) {
            return activityShareDetailDTOList;
        }

        // 计算商品总的售价
        int totalSalePrice = productDetailDTOList.stream().mapToInt(ActivityPromotionSplitUtil::getSaleAmt).sum();

        // log.info("排序前skuDetailList={}", GsonUtil.toJSONString(productDetailDTOList));
        ActivityPromotionSplitUtil.sortProductList(tenantId, productDetailDTOList, ActivityPromotionSplitUtil::getSaleAmt,
                OrderProductDetailDTO::getQuantity, OrderProductDetailDTO::getSkuName);

        int totalQuantity = productDetailDTOList.stream().mapToInt(OrderProductDetailDTO::getQuantity).sum();

        // 1)
        for (ActivityDiscount activityDiscount : productDiscountMap.values()) {
            if (alreadyHandleSet.contains(activityDiscount)) {
                continue;
            }
            log.info("分摊特殊活动 activityDiscount={}", GsonUtil.toJSONString(activityDiscount));

            int poi_charge = getIntegerValue(activityDiscount.getShop_rate());
            int mt_charge = getIntegerValue(activityDiscount.getBaidu_rate());

            // 复制一份。累积减的时候使用，不能影响原值。
            int lastPoiPromotion = poi_charge;
            int lastPlatPromotion = mt_charge;

            for (int index = 0; index < productDetailDTOList.size(); index++) {
                OrderProductDetailDTO orderProductDetail = productDetailDTOList.get(index);

                ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();

                activityShareDetailDTO.setActivityId(activityDiscount.getActivity_id());
                activityShareDetailDTO.setChannelPromotionType(activityDiscount.getType());
                activityShareDetailDTO.setSkuId(orderProductDetail.getSkuId());
                activityShareDetailDTO.setCustomSpu(orderProductDetail.getCustomSpu());
                activityShareDetailDTO.setSkuCount(orderProductDetail.getQuantity());
                activityShareDetailDTO.setPromotionCount(orderProductDetail.getQuantity());
                activityShareDetailDTO.setPromotionRemark(activityDiscount.getDesc());
                int originPrice = orderProductDetail.getOriginalPrice() * orderProductDetail.getQuantity();
                activityShareDetailDTO.setTotalOriginPrice(originPrice);

                if (index == productDetailDTOList.size() - 1) {
                    // 尾差法：最后一个商品，取剩余的优惠。
                    activityShareDetailDTO.setChannelCost(lastPlatPromotion);
                    activityShareDetailDTO.setTenantCost(lastPoiPromotion);
                } else {
                    /*
                      参考下游 settlement 服务单头金额到明细的拆分方式
                      com.sankuai.meituan.shangou.empower.settlement.domain.finance.factory.FinanceCombinationFactory#splitHead2Detail
                     */
                    double percent = 0d;
                    if (totalSalePrice == 0) {
                        // 饿了么按售价的比例分摊，如果售价为 0，按数量拆分
                        percent = orderProductDetail.getQuantity() * 1.0 / totalQuantity;
                    } else {
                        percent = ActivityPromotionSplitUtil.getSaleAmt(orderProductDetail) * 1.0 / totalSalePrice;
                    }

                    int poiPromotion = Double.valueOf(poi_charge * percent).intValue();
                    int platItemPromotion = Double.valueOf(mt_charge * percent).intValue();
                    activityShareDetailDTO.setChannelCost(platItemPromotion);
                    activityShareDetailDTO.setTenantCost(poiPromotion);
                    lastPoiPromotion -= poiPromotion;
                    lastPlatPromotion -= platItemPromotion;
                }

                // 优惠的活动价格 = 原价 - 优惠。
                activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() -
                        activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());
                activityShareDetailDTOList.add(activityShareDetailDTO);
            }

        }

        return activityShareDetailDTOList;
    }

    public static String generateSettleOrderId(BillGetResult billGetResult) {
        StringBuilder sb = new StringBuilder();
        sb.append(billGetResult.getShop_id());
        sb.append("-");
        sb.append(billGetResult.getDate());
        return sb.toString();
    }


    /**
     * 解析饿了么账单中金额部分，放置在SettlementFeeDetail中
     *
     * @param elmBillFee 饿了么账单汇总，或，饿了么账单明细
     * @return
     */
    public static List<SettlementFeeDetail> parseElmSettlementFeeDetailList(ElmBillFee elmBillFee) {
        List<SettlementFeeDetail> settlementFeeDetailList = new ArrayList<>();

        for (ElmFeeDetailEnum value : ElmFeeDetailEnum.values()) {
            if (StringUtils.isBlank(value.getField())) {
                continue;
            }
            String fieldValue = ConverterUtils.getFieldValue(elmBillFee, value.getField());
            if (fieldValue != null) {
                settlementFeeDetailList.add(buildSettlementFeeDetail(fieldValue, value));
            }
        }
        return settlementFeeDetailList;
    }

    private static SettlementFeeDetail buildSettlementFeeDetail(String fieldValue, ElmFeeDetailEnum detailEnum) {
        long feeValue = Long.valueOf(fieldValue);
        SettlementFeeDetail settlementFeeDetail = new SettlementFeeDetail();
        settlementFeeDetail.setFeeKey(detailEnum.getCode());
        settlementFeeDetail.setFeeDesc(detailEnum.getDescription());
        settlementFeeDetail.setFeeValue(feeValue);
        return settlementFeeDetail;
    }

    public static List<SettlementFeeDetail> convertQnhSettlementFeeDetail(QnhChannelOrderSettlementDetail detail) {
        List<SettlementFeeDetail> settlementFeeDetailList = new ArrayList<>();

        for (ElmFeeDetailEnum detailEnum : ElmFeeDetailEnum.values()) {
            if (StringUtils.isBlank(detailEnum.getQnhField())) {
                continue;
            }
            String qnhField = ConverterUtils.getFieldValue(detail, detailEnum.getQnhField());
            if (qnhField != null) {
                settlementFeeDetailList.add(buildQnhSettlementFeeDetail(qnhField, detailEnum));
            }
        }
        return settlementFeeDetailList;
    }

    private static SettlementFeeDetail buildQnhSettlementFeeDetail(String fieldValue, ElmFeeDetailEnum detailEnum) {
        double feeValue = Double.parseDouble(fieldValue);
        SettlementFeeDetail settlementFeeDetail = new SettlementFeeDetail();
        settlementFeeDetail.setFeeKey(detailEnum.getCode());
        settlementFeeDetail.setFeeDesc(detailEnum.getDescription());
        settlementFeeDetail.setFeeValue(MoneyUtils.doubleHalfUpToLong(feeValue));
        return settlementFeeDetail;
    }

    /**
     * 解析饿了么账单明细中非金额部分，放置在SettlementExtraInfoDetail中
     *
     * @param elmBillDetailFee 饿了么账单明细
     * @return
     */
    public static List<SettlementExtraInfoDetail> parseElmSettlementExtraInfoDetailList(ElmBillDetailFee elmBillDetailFee) {
        List<SettlementExtraInfoDetail> extraInfoDetailList = new ArrayList<>();

        for (ElmExtraInfoDetailEnum value : ElmExtraInfoDetailEnum.values()) {
            String fieldValue = ConverterUtils.getFieldValue(elmBillDetailFee, value.getField());
            if (fieldValue != null) {
                extraInfoDetailList.add(buildSettlementExtraInfoDetail(fieldValue, value));
            }
        }
        return extraInfoDetailList;
    }

    private static SettlementExtraInfoDetail buildSettlementExtraInfoDetail(String fieldValue, ElmExtraInfoDetailEnum detailEnum) {
        SettlementExtraInfoDetail settlementFeeDetail = new SettlementExtraInfoDetail();
        settlementFeeDetail.setFeeKey(detailEnum.getCode());
        settlementFeeDetail.setFeeDesc(detailEnum.getDescription());
        settlementFeeDetail.setFeeValue(fieldValue);
        return settlementFeeDetail;
    }

    public static List<SettlementExtraInfoDetail> convertQnhSettlementExtraInfoDetail(QnhChannelOrderSettlementDetail detail) {
        List<SettlementExtraInfoDetail> extraInfoDetailList = new ArrayList<>();

        for (ElmExtraInfoDetailEnum value : ElmExtraInfoDetailEnum.values()) {
            if (StringUtils.isBlank(value.getQnhField())) {
                continue;
            }
            String qnhFieldValue = ConverterUtils.getFieldValue(detail, value.getQnhField());
            if (qnhFieldValue != null) {
                extraInfoDetailList.add(buildSettlementExtraInfoDetail(qnhFieldValue, value));
            }
        }
        return extraInfoDetailList;
    }

    public static String parseSettlementChannelOrderId(BillOrderDetail billOrderDetail) {
        StringBuilder sb = new StringBuilder();
        sb.append(billOrderDetail.getOrder_id());
        sb.append("-");
        sb.append(billOrderDetail.getTrade_create_time());
        return sb.toString();
    }

    public static long secondStringToMillis(String second) {
        try {
            Long.valueOf(second);
        } catch (NumberFormatException e) {
            log.warn("ElmConverterUtil.secondStringToMillis  解析时间错误:{}", second);
            return 0L;

        }
        return ConverterUtils.secondStringToMillis(second);
    }

    /**
     * 饿了么是否预订单判断
     *
     * @param sendImmediately
     * @return
     */
    public static boolean isBooking(String sendImmediately) {
        return StringUtils.equals(sendImmediately, SEND_IMMEDIATELY_IS_BOOKING_TRUE);
    }

    public static Integer extractProductWeight(OrderSkuDetail orderSkuDetail) {
        if (orderSkuDetail.getTotal_weight() == null) {
            return 0;
        }
        if (orderSkuDetail.getProduct_amount() == null || orderSkuDetail.getProduct_amount() == 0) {
            return 0;
        }
        return orderSkuDetail.getTotal_weight() / orderSkuDetail.getProduct_amount();
    }

    /**
     * 根据is_free_gift是否为赠品转换itemType
     * @param orderSkuDetail
     * @return itemType（0-正常商品，1-赠品）
     */
    public static Integer extractItemType(OrderSkuDetail orderSkuDetail) {
        if (orderSkuDetail.getIs_free_gift() == null) {
            return 0;
        }

        if (orderSkuDetail.getIs_free_gift() == 1) {
            return 1;
        }

        return 0;
    }

    private final static String PRIVACY_LABEL_DESC = "该商品是渠道定义的隐私商品，应渠道规则要求，为保护消费者隐私，订单中有任一商品属于隐私商品，打印小票时用户联需要隐藏所有商品信息（商家联和拣货联不受影响）";

    /**
     * 转换渠道商品标签
     * @param orderSkuDetail
     * @return
     */
    public static List<ProductLabelDTO> convertProductLabelList(OrderSkuDetail orderSkuDetail) {
        // 转换是否是隐私品 -- 注：隐私商品的是否使用，具体得看ocms服务buildItemModelList方法中的逻辑 -> com.sankuai.meituan.shangou.empower.ocms.service.order.impl.OrderCenterBizServiceImpl.buildItemModelList
        Integer is_privacy = orderSkuDetail.getIs_privacy();
        List<ProductLabelDTO> productLabelDTOList = new ArrayList<>(4);
        if (Objects.nonNull(is_privacy) && is_privacy == 1) {
            ProductLabelDTO productLabelDTO = new ProductLabelDTO();
            productLabelDTO.setType(OrderProductLabelEnum.PRIVACY_GOODS.getValue());
            productLabelDTO.setName(OrderProductLabelEnum.PRIVACY_GOODS.getDesc());
            productLabelDTO.setDesc(PRIVACY_LABEL_DESC);
            productLabelDTOList.add(productLabelDTO);
        }
        return productLabelDTOList;
    }

    public static String extractSkuProperty(List<OrderSkuDetail.ProductFeature> productFeatures) {
        String ret = "";
        if (CollectionUtils.isEmpty(productFeatures)) {
            return ret;
        }

        for (int i = 0; i < productFeatures.size(); i++) {
            ret = ret + Optional.ofNullable(productFeatures.get(i).getName()).orElse("默认")
                    + "-"
                    + Optional.ofNullable(productFeatures.get(i).getOption()).orElse("默认属性");
            if (i != productFeatures.size() - 1) {
                ret = ret + ",";
            }
        }

        return ret;
    }

    /**
     * 解析原始配送类型
     *
     * @param deliveryParty 订单配送方式
     * @return
     */
    public static int convertDeliveryType(String deliveryParty) {
        switch (deliveryParty) {
            case "2":
            case "6":
                return DistributeTypeEnum.SELF_DELIVERY.getValue();
            case "4":
                return DistributeTypeEnum.ELEM_ZONG_BAO.getValue();
            case "5":
                return DistributeTypeEnum.ELEM_PLATFORM.getValue();
            case "8":
                return DistributeTypeEnum.ELEM_DELIVERY.getValue();
            case "9":
                return DistributeTypeEnum.ELEM_HALF_DAY.getValue();
            case "10":
                return DistributeTypeEnum.ELEM_HOUR.getValue();
            default:
                return DistributeTypeEnum.UN_KNOWN.getValue();
        }
    }

    /**
     * 对单分类、多分类做兼容逻辑处理，优先用code，没有code用name
     *
     * @param skuInfoDTO
     * @return
     */
    public static String convert2MTCategoryName(SkuInfoDTO skuInfoDTO) {
        if (CollectionUtils.isEmpty(skuInfoDTO.getLeafStoreCategoryList())) {
            return skuInfoDTO.isSetChannelFrontCategory() ? null : skuInfoDTO.getFrontCategoryName();
        }
        if (skuInfoDTO.getLeafStoreCategoryList().size() == 1) {
            if (StringUtils.isEmpty(skuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryCode())) {
                return skuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryName();
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * elm取店内分类，elm只需要分类code
     * 如果是多分类结构，取第一个分类code
     *
     * @param skuInfoDTO
     * @return
     */
    public static String convert2ELMCategoryCode(SkuInfoDTO skuInfoDTO) {
        if (CollectionUtils.isEmpty(skuInfoDTO.getLeafStoreCategoryList())) {
            return skuInfoDTO.getChannelFrontCategory();
        }
        return skuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryCode();
    }

    /**
     * 转换饿了么分类列表
     *
     * @param spuInfoDTO
     * @return
     */
    public static List<CustomCategoryDTO> convert2ELMCategoryCode(SpuInfoDTO spuInfoDTO) {
        if (CollectionUtils.isEmpty(spuInfoDTO.getLeafStoreCategoryList())) {
            return null;
        }
        return spuInfoDTO.getLeafStoreCategoryList().stream().map(category -> {
            CustomCategoryDTO dto = new CustomCategoryDTO();
            dto.setCategory_id(category.getCategoryCode());
            return dto;
        }).collect(Collectors.toList());
    }

    public static List<SpuInfoDTO> convert2Spu(boolean isMedicineUMW, List<SkuQueryDetailResultDTO> channelSkuInfos, List<CatInfo> catInfoList) {
        if (CollectionUtils.isEmpty(channelSkuInfos)) {
            return Lists.newArrayList();
        }
        Map<String, CatInfo> cateInfoMap;
        if (CollectionUtils.isNotEmpty(catInfoList)) {
            cateInfoMap = catInfoList.stream().collect(Collectors.toMap(CatInfo::getCatId, Function.identity(), (v1, v2) -> v2));
        } else {
            cateInfoMap = Maps.newHashMap();
        }
        return channelSkuInfos.stream().map(channelSkuInfo -> buildSpuInfoDTO(isMedicineUMW, channelSkuInfo, cateInfoMap)).collect(Collectors.toList());
    }

    public static SpuInfoDTO buildSpuInfoDTO(boolean isMedicineUMW, SkuQueryDetailResultDTO skuQueryDetailResultDTO, Map<String, CatInfo> cateInfoMap) {
        SpuInfoDTO spuInfoDTO = new SpuInfoDTO();
        //渠道商品id
        spuInfoDTO.setChannelSpuId(skuQueryDetailResultDTO.getSku_id());
        //渠道商品自定义id
        spuInfoDTO.setCustomSpuId(skuQueryDetailResultDTO.getCustom_sku_id());
        //店内分类
        spuInfoDTO.setCategoryList(convertStoreCategory(skuQueryDetailResultDTO, cateInfoMap));
        //渠道类目
        spuInfoDTO.setChannelCategoryId(String.valueOf(skuQueryDetailResultDTO.getCate_id()));
        //卖点
        spuInfoDTO.setSellPoint(skuQueryDetailResultDTO.getSummary());
        if (StringUtils.isNotBlank(skuQueryDetailResultDTO.getCustom_cat_ids())) {
            spuInfoDTO.setFrontCategoryCodes(Lists.newArrayList(skuQueryDetailResultDTO.getCustom_cat_ids().split(",")));
        }
        //是否标品
        spuInfoDTO.setIsSp(skuQueryDetailResultDTO.getUpc_type());
        //末级店内分类
        if (CollectionUtils.isNotEmpty(skuQueryDetailResultDTO.getCustom_cat_list())) {
            List<ChannelLeafStoreCategory> collect = skuQueryDetailResultDTO.getCustom_cat_list().stream().map(channelCategory -> {
                ChannelLeafStoreCategory channelLeafStoreCategory = new ChannelLeafStoreCategory();
                channelLeafStoreCategory.setCategoryCode(channelCategory.getCustom_cat_id());
                channelLeafStoreCategory.setCategoryName(channelCategory.getCustom_cat_name());
                return channelLeafStoreCategory;
            }).collect(Collectors.toList());
            spuInfoDTO.setLeafStoreCategoryList(collect);
        }
        //商品名
        spuInfoDTO.setName(skuQueryDetailResultDTO.getName());
        //图片
        if (CollectionUtils.isNotEmpty(skuQueryDetailResultDTO.getPhotos())) {
            spuInfoDTO.setPictures(skuQueryDetailResultDTO.getPhotos().stream().map(SkuQueryDetailResultDTO.SkuPhotoDTO::getUrl).collect(Collectors.toList()));
        }
        //属性
        spuInfoDTO.setProperties(convertProperties(skuQueryDetailResultDTO.getSku_property()));
        //产地
        spuInfoDTO.setProductionArea(skuQueryDetailResultDTO.getProduction_addr3());
        //店内分类排序
        spuInfoDTO.setSequence(skuQueryDetailResultDTO.getRank());
        //规格类型
        if (CollectionUtils.isEmpty(skuQueryDetailResultDTO.getSku_spec())) {
            spuInfoDTO.setSpecType(1);
        } else {
            spuInfoDTO.setSpecType(2);
        }
        //sku
        spuInfoDTO.setSkus(convertSkus(isMedicineUMW, skuQueryDetailResultDTO));
        //上下架状态
        if (skuQueryDetailResultDTO.getStatus() == 0) {
            spuInfoDTO.setStatus(2);
        } else {
            spuInfoDTO.setStatus(1);
        }
        // 是否在活动中
        spuInfoDTO.setInActivity(skuQueryDetailResultDTO.getIs_in_activity() == 1);
        return spuInfoDTO;
    }

    private static List<ChannelStoreCategory> convertStoreCategory(SkuQueryDetailResultDTO skuQueryDetailResultDTO, Map<String, CatInfo> cateInfoMap) {
        if (CollectionUtils.isEmpty(skuQueryDetailResultDTO.getCustom_cat_list())) {
            return Lists.newArrayList();
        }
        List<ChannelStoreCategory> channelStoreCategories = Lists.newArrayList();
        skuQueryDetailResultDTO.getCustom_cat_list().forEach(channelCategory -> {
            if (cateInfoMap.containsKey(channelCategory.getCustom_cat_id())) {
                CatInfo catInfo = cateInfoMap.get(channelCategory.getCustom_cat_id());
                if (catInfo.getDepth() == 1) {
                    ChannelStoreCategory channelStoreCategory = new ChannelStoreCategory();
                    channelStoreCategory.setFirstCategoryCode(channelCategory.getCustom_cat_id());
                    channelStoreCategory.setFirstCategoryName(channelCategory.getCustom_cat_name());
                    channelStoreCategories.add(channelStoreCategory);
                } else {
                    ChannelStoreCategory channelStoreCategory = new ChannelStoreCategory();
                    channelStoreCategory.setSecondaryCategoryCode(channelCategory.getCustom_cat_id());
                    channelStoreCategory.setSecondaryCategoryName(channelCategory.getCustom_cat_name());
                    if (cateInfoMap.containsKey(catInfo.getParentId())) {
                        channelStoreCategory.setFirstCategoryCode(catInfo.getParentId());
                        channelStoreCategory.setFirstCategoryName(cateInfoMap.get(catInfo.getParentId()).getName());
                    }
                    channelStoreCategories.add(channelStoreCategory);
                }
            } else {
                log.warn("该商品无店内分类:{}", skuQueryDetailResultDTO.getName());
            }
        });
        return channelStoreCategories;
    }

    private static List<SkuInSpuInfoDTO> convertSkus(boolean isMedicineUMW, SkuQueryDetailResultDTO skuQueryDetailResultDTO) {
        List<SkuQueryDetailResultDTO.SkuQuerySpecResultDTO> channelSkus = skuQueryDetailResultDTO.getSku_spec();
        if (CollectionUtils.isEmpty(channelSkus)) {
            //表示该商品是单规格
            SkuInSpuInfoDTO skuInSpuInfoDTO = new SkuInSpuInfoDTO();
            skuInSpuInfoDTO.setChannelSkuId(skuQueryDetailResultDTO.getSku_id());
            skuInSpuInfoDTO.setCustomSkuId(skuQueryDetailResultDTO.getCustom_sku_id());
            //售卖单位
            skuInSpuInfoDTO.setUnit(skuQueryDetailResultDTO.getSale_unit());
            //最小起购数
            skuInSpuInfoDTO.setMinPurchaseQuantity(skuQueryDetailResultDTO.getMinimum());
            //价格
            skuInSpuInfoDTO.setPrice(BigDecimal.valueOf(skuQueryDetailResultDTO.getSale_price()).divide(BigDecimal.valueOf(100)).doubleValue());
            //库存
            skuInSpuInfoDTO.setStock(skuQueryDetailResultDTO.getLeft_num());
            //upc
            skuInSpuInfoDTO.setUpc(skuQueryDetailResultDTO.getUpc());
            if (StringUtils.isNotBlank(skuQueryDetailResultDTO.getWeight())) {
                skuInSpuInfoDTO.setWeight(Integer.parseInt(skuQueryDetailResultDTO.getWeight()));
            }
            return Lists.newArrayList(skuInSpuInfoDTO);
        } else {
            return channelSkus.stream().map(channelSku -> {
                SkuInSpuInfoDTO skuInSpuInfoDTO = new SkuInSpuInfoDTO();
                skuInSpuInfoDTO.setChannelSkuId(String.valueOf(channelSku.getSku_spec_id()));
                skuInSpuInfoDTO.setCustomSkuId(channelSku.getSku_spec_custom_id());
                //售卖单位
                skuInSpuInfoDTO.setUnit(skuQueryDetailResultDTO.getSale_unit());
                //最小起购数
                skuInSpuInfoDTO.setMinPurchaseQuantity(skuQueryDetailResultDTO.getMinimum());
                //价格
                skuInSpuInfoDTO.setPrice(BigDecimal.valueOf(channelSku.getSale_price()).divide(BigDecimal.valueOf(100)).doubleValue());
                skuInSpuInfoDTO.setStock(channelSku.getLeft_num());
                skuInSpuInfoDTO.setUpc(channelSku.getUpc());
                skuInSpuInfoDTO.setWeight(channelSku.getWeight());
                //规格名
                if (CollectionUtils.isNotEmpty(channelSku.getSpec_property())) {
                    SkuQueryDetailResultDTO.OpenApiPropValueDTO openApiPropValueDTO = channelSku.getSpec_property().get(0);
                    skuInSpuInfoDTO.setSpec(openApiPropValueDTO.getValue_text());
                    //判断是否是自定义规格
                    if (openApiPropValueDTO.getProp_id() == ProjectConstant.ELM_SELF_DEFINE_PROP_ID || (isMedicineUMW && openApiPropValueDTO.getProp_id() == ProjectConstant.ELM_MED_SELF_DEFINE_PROP_ID)) {
                        skuInSpuInfoDTO.setIsCustomSpec(1);
                    }
                }
                //销售属性
                if (CollectionUtils.isNotEmpty(channelSku.getSpec_property())) {
                    List<SaleAttrValueDTO> saleAttrValues = channelSku.getSpec_property()
                            .stream()
                            .map(specProp -> {
                                SaleAttrValueDTO saleAttrValueDTO = new SaleAttrValueDTO();
                                saleAttrValueDTO.setAttrId(specProp.getProp_id());
                                saleAttrValueDTO.setAttrName(specProp.getProp_text());
                                saleAttrValueDTO.setValueId(specProp.getValue_id());
                                saleAttrValueDTO.setValue(specProp.getValue_text());
                                saleAttrValueDTO.setPictureUrl(Optional.ofNullable(specProp.getPhoto_url())
                                        .filter(StringUtils::isNotBlank)
                                        .map(Collections::singletonList)
                                        .orElse(null));
                                return saleAttrValueDTO;
                            })
                            .collect(Collectors.toList());
                    skuInSpuInfoDTO.setSaleAttrValueList(saleAttrValues);
                }
                return skuInSpuInfoDTO;
            }).collect(Collectors.toList());
        }
    }

    public static String convertProperties(List<SkuQueryDetailResultDTO.CustomPropertyDTO> properties) {
        if (CollectionUtils.isEmpty(properties)) {
            return null;
        }
        List<StoreSkuPropertyInfo> propertyInfos = properties.stream().map(property -> {
            StoreSkuPropertyInfo storeSkuPropertyInfo = new StoreSkuPropertyInfo();
            storeSkuPropertyInfo.setPropertyName(property.getName());
            if (CollectionUtils.isNotEmpty(property.getDetail())) {
                List<String> values = property.getDetail().stream().map(SkuQueryDetailResultDTO.CustomPropertyDetail::getK).collect(Collectors.toList());
                storeSkuPropertyInfo.setPropertyValues(values);
            } else {
                storeSkuPropertyInfo.setPropertyValues(Lists.newArrayList());
            }
            return storeSkuPropertyInfo;
        }).collect(Collectors.toList());
        return JSON.toJSONString(propertyInfos);
    }

    /**
     * 不在计算订单优惠，在领域层做
     * @param channelOrderDetailDTO
     * @param skuDetails
     * @param discount
     */
    public static void extractPromotionFromActivities(ChannelOrderDetailDTO channelOrderDetailDTO, List<OrderSkuDetail> skuDetails, List<OrderActivitiesInfo> discount, Long tenantId) {
        for (OrderSkuDetail orderSkuDetail: skuDetails){
            if (orderSkuDetail.getProduct_subsidy() == null || CollectionUtils.isEmpty(orderSkuDetail.getProduct_subsidy().getDiscount_detail())){
                continue;
            }
            int platPromotion = 0;
            int poiPromotion = 0;
            int platItemPromotion = 0;
            int poiItemPromotion = 0;
            for (ProductSubsidyDiscountDetail subsidyDiscountDetail: orderSkuDetail.getProduct_subsidy().getDiscount_detail()){
                boolean tenantSupport = MccConfigUtil.checkTenantSupportElemItemPromotionTypeConfig(tenantId);
                boolean isItemPromotion = tenantSupport ?
                        ELmActivityTypeEnum.isItemPromotion(subsidyDiscountDetail.getType(), subsidyDiscountDetail.getActivity_type(), subsidyDiscountDetail.getActivity_child_type())
                        : ELmActivityTypeEnum.isItemPromotion(subsidyDiscountDetail.getType());
                if (isItemPromotion){
                    platItemPromotion += subsidyDiscountDetail.getBaidu_rate();
                    poiItemPromotion += subsidyDiscountDetail.getShop_rate();
                }else {
                    platPromotion += subsidyDiscountDetail.getBaidu_rate();
                    poiPromotion += subsidyDiscountDetail.getShop_rate();
                }
            }
            List<OrderProductDetailDTO> skus = channelOrderDetailDTO.getSkuDetails().stream()
                    .filter(sku -> Objects.equals(sku.getCustomSpu(), orderSkuDetail.getCustom_sku_id()) && Objects.equals(sku.getUpcCode(), orderSkuDetail.getUpc()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skus)){
                log.error("elem优惠分摊未匹配到商品信息{}", channelOrderDetailDTO);
                continue;
            }
            OrderProductDetailDTO targetSku = skus.stream().filter(v->Objects.equals(orderSkuDetail.getSub_biz_order_id(), v.getChannelItemId())).findFirst().orElse(skus.get(0));
            targetSku.setPlatPromotion(platPromotion);
            targetSku.setPoiPromotion(poiPromotion);
            targetSku.setPoiItemPromotion(poiItemPromotion);
            targetSku.setPlatItemPromotion(platItemPromotion);
            int salePrice = BigDecimal.valueOf((long) orderSkuDetail.getProduct_price() * orderSkuDetail.getProduct_amount() - poiItemPromotion - platItemPromotion)
                    .divide(BigDecimal.valueOf(orderSkuDetail.getProduct_amount()), 0, RoundingMode.HALF_UP).intValue();
            targetSku.setActualSalePrice(salePrice);
        }
    }

    public static String generateIdempotentId() {
        long time = System.currentTimeMillis();
        long random = RandomUtils.nextLong(0, 10000);

        return Long.toHexString(time) + "_" + Long.toHexString(random);
    }


    @Data
    public static class StoreSkuPropertyInfo {
        /**
         * 属性名称
         */
        @JSONField(name = "property_name")
        private String propertyName;

        /**
         * 属性值，不超过十个值
         */
        @JSONField(name = "values")
        private List<String> propertyValues;
    }
}
