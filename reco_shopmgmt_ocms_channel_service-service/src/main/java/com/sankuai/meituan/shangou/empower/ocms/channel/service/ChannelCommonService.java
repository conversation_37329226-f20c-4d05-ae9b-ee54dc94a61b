package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.channelconfig.SaveOrgProductAbilityRequest;

import java.util.Map;

/**
* @author: lijunbo03
* @Date: 2019/8/6
*/
public interface ChannelCommonService {

    ResultStatus auth(String url, Map<String, String> params, String requestIp, String originalUrl);

    default ResultStatus saveStoreProductAbility(SaveOrgProductAbilityRequest request){
        throw new UnsupportedOperationException();
    }
}
