package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.dianping.lion.common.util.JsonUtils;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.Strings;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.txd.TxdOfflineCategory;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.txd.TxdSkuInSpuStockDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpuStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOnlineStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdWeightFlagEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd.TxdConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ImageUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductChannelErrorMappingUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultSpuDataUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.CommonYesNoEnum;
import com.taobao.api.FileItem;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.internal.mapping.ApiField;
import com.taobao.api.request.*;
import com.taobao.api.request.AlibabaWdkStockPublishRequest.BatchStockPublishDto;
import com.taobao.api.request.AlibabaWdkStockPublishRequest.StockPublishDto;
import com.taobao.api.response.*;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator.genResult;

/**
 * <AUTHOR>
 * @date 2024/4/16
 */
@Slf4j
public class TxdConvertUtil {

    private static final String CATEGORY_ROOT_CODE = "0";
    public static final String TXD_DEFAULT_CATEGORY_CODE = "setup_default_1";
    public static final Integer LEFT = 1;
    /**
     * 最大分类深度
     */
    public static final Integer MAX_CATEGORY_LEVEL = 5;

    public static final Integer NOT_LEFT = 0;

    /**
     * 商品创建
     *
     * @param channelPoiId
     * @param spuList
     * @return
     */
    public static AlibabaWdkSkuAddRequest buildAddRequest(String channelPoiId, List<SpuInfoDTO> spuList) {
        AlibabaWdkSkuAddRequest aliRequest = new AlibabaWdkSkuAddRequest();
        List<AlibabaWdkSkuAddRequest.SkuDo> skuDos = Fun.map(spuList, spu -> buildSkuDo(channelPoiId, spu));
        List<AlibabaWdkSkuAddRequest.SkuDo> validSkuDos = Fun.filter(skuDos, Objects::nonNull);
        aliRequest.setParamList(validSkuDos);
        return aliRequest;
    }

    public static AlibabaWdkSkuAddRequest.SkuDo buildSkuDo(String channelPoiId, SpuInfoDTO spuInfoDTO) {

        if (spuInfoDTO == null) {
            return null;
        }
        AlibabaWdkSkuAddRequest.SkuDo skuDo = new AlibabaWdkSkuAddRequest.SkuDo();
        // 门店编码
        skuDo.setOuCode(channelPoiId);
        // 商品唯一编码
        skuDo.setSkuCode(spuInfoDTO.getCustomSpuId());
        // 商品名称
        skuDo.setSkuName(spuInfoDTO.getName());
        skuDo.setShortTitle(spuInfoDTO.getName());
        // 商家后台类目编码（同字段category_code），优先使用本字段
        if (StringUtils.isNotBlank(spuInfoDTO.getOfflineChannelCategoryCode())) {
            skuDo.setMerchantCatCode(spuInfoDTO.getOfflineChannelCategoryCode());
        }
        else {
            skuDo.setMerchantCatCode(TXD_DEFAULT_CATEGORY_CODE);
        }
        if (StringUtils.isNotBlank(spuInfoDTO.getChannelCategoryId())) {
            skuDo.setForestCateId(Long.valueOf(spuInfoDTO.getChannelCategoryId()));
        }
        // 图片 大于5张取前5张
        if (CollectionUtils.isNotEmpty(spuInfoDTO.getPictures())) {
            List<String> subList = spuInfoDTO.getPictures();
            if (spuInfoDTO.getPictures().size() > TxdConstant.DEFAULT_PICTURE_LIMIT) {
                subList = spuInfoDTO.getPictures().subList(0, TxdConstant.DEFAULT_PICTURE_LIMIT);
            }

            String urlStr = subList.stream().filter(StringUtils::isNotEmpty)
                    .collect(Collectors.joining(","));
            skuDo.setSkuPicUrls(urlStr);
        }
        // 上下架状态- 1上架  0下架
        skuDo.setAllowAppSale(TxdOnlineStatusEnum.ON_SALE.getCode());
        skuDo.setOnlineSaleFlag(spuInfoDTO.getStatus() == SpuStatusEnum.ON_LINE.getCode()
                ? TxdOnlineStatusEnum.ON_SALE.getCode()
                : TxdOnlineStatusEnum.OFF_SALE.getCode());
        // 产地
        skuDo.setProducerPlace(spuInfoDTO.getProductionArea());
        // 配送条件；填常温、冷藏、冷冻
        skuDo.setDeliveryStorage(spuInfoDTO.getDeliveryRequirement());
        // 存储条件：填常温、冷藏、冷冻；和配送条件保持一致
        skuDo.setStorage(spuInfoDTO.getDeliveryRequirement());
        // 设置商品详情为富文本格式，包括图片和文本内容。文描,（同字段txt_desc）；优先使用本字段
        skuDo.setRichText(buildRichText(spuInfoDTO));
        // 是否称重品 1：是  0：否（默认为0)
        if (spuInfoDTO.getIsSp() == 1) {
            skuDo.setWeightFlag(TxdWeightFlagEnum.NOT_WEIGHT_PRODUCT.getCode());
            // 均重，非称重品填1，A只有标品（非称重品）
            skuDo.setAvgWeight(TxdConstant.DEFAULT_AVG_WEIGHT);
            // 预扣款重量，非称重品填1，A只有标品（非称重品）
            skuDo.setPreMinusWeight(TxdConstant.DEFAULT_PRE_MINUS_WEIGHT);
            // 非称重品填1，A只有标品（非称重品）
            skuDo.setStepQuantity(TxdConstant.DEFAULT_STEP_QUANTITY);
        } else {
            skuDo.setWeightFlag(TxdWeightFlagEnum.WEIGHT_PRODUCT.getCode());
        }
        // 商品规格信息
        if (CollectionUtils.isNotEmpty(spuInfoDTO.getSkus())) {
            SkuInSpuInfoDTO skuInSpuInfoDTO = spuInfoDTO.getSkus().get(0);
            // 商品售价 （同字段sale_price），单位:元；优先使用本字段
            skuDo.setSkuPrice(String.valueOf(skuInSpuInfoDTO.getPrice()));
            skuDo.setMemberPrice(String.valueOf(skuInSpuInfoDTO.getPrice()));
            //条码
            skuDo.setBarcodes(skuInSpuInfoDTO.getUpc());
            String skuSaleUnit = getSkuSaleUnit(skuInSpuInfoDTO);
            // 商品销售单位
            skuDo.setSaleUnit(skuSaleUnit);
            // 设置商品库存单位
            skuDo.setInventoryUnit(skuSaleUnit);
            // 采购单位，与库存单位保持一致
            skuDo.setPurchaseUnit(skuSaleUnit);
            // 销售规格
            skuDo.setSaleSpec(getSkuSaleSpec(skuInSpuInfoDTO, skuSaleUnit));
            // 商品重量 单位g 必须为整数
            skuDo.setWeight(String.valueOf(skuInSpuInfoDTO.getWeight()));
            // 净含量
            skuDo.setNetContent(getSkuNetContent(skuInSpuInfoDTO, skuSaleUnit));
            // 起购量
            skuDo.setPurchaseQuantity(getSkuPurchaseQuantity(skuInSpuInfoDTO));
            // 副标题，用卖点对接
            skuDo.setSubTitle(spuInfoDTO.getSellPoint());
            // 淘鲜达合作商家默认填1
            skuDo.setPurchaseSpec(TxdConstant.DEFAULT_PURCHASE_SPEC);
            // 保质期，0代表不管理保质期
            skuDo.setPeriod(TxdConstant.DEFAULT_PERIOD);
            // 商品类型
            skuDo.setItemTypeNew(TxdConstant.DEFAULT_ITEM_TYPE);
            // 易碎品
            skuDo.setFragileFlag(TxdConstant.DEFAULT_FRAGILE_FLAG);
        }
        return skuDo;

    }

    public static ResultSpuData genAddSpuResult(Long storeId, List<SpuKey> spuKeys, TaobaoResponse commonResponse) {

        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (commonResponse == null) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        if (!commonResponse.isSuccess()) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    parseTxdErrorCode(commonResponse.getErrorCode()), commonResponse.getMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        AlibabaWdkSkuAddResponse response = (AlibabaWdkSkuAddResponse) commonResponse;
        if (response.getResult() == null) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        AlibabaWdkSkuAddResponse.ApiResults result = response.getResult();
        if (!BooleanUtils.isTrue(result.getSuccess())) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    parseTxdErrorCode(result.getErrCode()), result.getErrMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        if (CollectionUtils.isEmpty(result.getModels())) {
            return ProductResultDataUtils.combineSuccessDataList(resultSpuData, null, spuKeys);
        }

        List<ResultErrorSpu> errorDataList = resultSpuData.getErrorData();
        List<ResultSuccessSpu> successDataList = resultSpuData.getSucData();

        for (AlibabaWdkSkuAddResponse.ApiResult model : result.getModels()) {
            if (model == null) {
                continue;
            }
            if (BooleanUtils.isTrue(model.getSuccess())) {
                ResultSuccessSpu successSpu = new ResultSuccessSpu();
                successSpu.setSpuInfo(new SpuKey(model.getModel(), model.getModel(), null));
                successSpu.setStoreId(storeId);
                successSpu.setChannelId(EnhanceChannelType.TXD.getChannelId());
                successDataList.add(successSpu);
            } else {
                ResultErrorSpu errorSpu = new ResultErrorSpu();
                errorSpu.setSpuInfo(new SpuKey(model.getModel(), model.getModel(), null));

                errorSpu.setErrorCode(parseTxdErrorCode(model.getErrCode()));
                errorSpu.setErrorMsg(model.getErrMsg());

                ProductChannelUnifyErrorEnum errorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(
                        ChannelTypeEnum.TXD, model.getErrCode(), model.getErrMsg());
                errorSpu.setChannelUnifyError(errorEnum);

                errorSpu.setStoreId(storeId);
                errorSpu.setChannelId(EnhanceChannelType.TXD.getChannelId());
                errorDataList.add(errorSpu);
            }

        }
        return resultSpuData;
    }


    /**
     * 商品更新
     *
     * @param channelPoiId
     * @param spuStoreCategoryCodeDTOList
     * @return
     */
    public static AlibabaWdkSkuUpdateRequest buildUpdateOfflineCategoryRequest(String channelPoiId, List<SpuStoreCategoryCodeDTO> spuStoreCategoryCodeDTOList) {

        AlibabaWdkSkuUpdateRequest aliRequest = new AlibabaWdkSkuUpdateRequest();
        List<AlibabaWdkSkuUpdateRequest.SkuDo> skuDos = new ArrayList<>();
        spuStoreCategoryCodeDTOList.forEach(spuStoreCategoryCodeDTO -> {
            AlibabaWdkSkuUpdateRequest.SkuDo skuDo = new AlibabaWdkSkuUpdateRequest.SkuDo();
            skuDo.setOuCode(channelPoiId);
            skuDo.setSkuCode(spuStoreCategoryCodeDTO.getCustomSpuId());
            skuDo.setMerchantCatCode(spuStoreCategoryCodeDTO.getStoreCategoryCodeList().get(0));
            skuDos.add(skuDo);
        });
        aliRequest.setParamList(skuDos);
        return aliRequest;
    }

    public static AlibabaWdkSkuUpdateRequest buildUpdateUpcRequest(String channelPoiId, List<SpuUpcUpdateDto> upcUpdateDtos) {
        AlibabaWdkSkuUpdateRequest aliRequest = new AlibabaWdkSkuUpdateRequest();
        List<AlibabaWdkSkuUpdateRequest.SkuDo> skuDos = new ArrayList<>();
        upcUpdateDtos.forEach(upcUpdateDto -> {
            AlibabaWdkSkuUpdateRequest.SkuDo skuDo = new AlibabaWdkSkuUpdateRequest.SkuDo();
            skuDo.setOuCode(channelPoiId);
            skuDo.setSkuCode(upcUpdateDto.getCustomSpuId());
            skuDo.setBarcodes(String.join(",", upcUpdateDto.getUpcList()));
            skuDo.setBarcodeUpdateType((long) upcUpdateDto.getType());
            skuDos.add(skuDo);
        });
        aliRequest.setParamList(skuDos);
        return aliRequest;
    }

    public static AlibabaWdkSkuUpdateRequest buildUpdateRequestForDelete(String channelPoiId, Long tenantId, List<SpuInfoDeleteDTO> spuList) {

        AlibabaWdkSkuUpdateRequest aliRequest = new AlibabaWdkSkuUpdateRequest();
        List<AlibabaWdkSkuUpdateRequest.SkuDo> skuDos = Fun.map(spuList,
                spu -> {
                    AlibabaWdkSkuUpdateRequest.SkuDo skuDo = new AlibabaWdkSkuUpdateRequest.SkuDo();
                    skuDo.setOuCode(channelPoiId);
                    skuDo.setSkuCode(spu.getCustomSpuId());
                    if (spu.isMerchantSpuOffline() && MccConfigUtil.getTxdNeedUpdateProductCategoryTenants().contains(tenantId)) {
                        skuDo.setMerchantCatCode(TXD_DEFAULT_CATEGORY_CODE);
                    }
                    skuDo.setOnlineSaleFlag(TxdOnlineStatusEnum.OFF_SALE.getCode());
                    return skuDo;
                });
        List<AlibabaWdkSkuUpdateRequest.SkuDo> validSkuDos = Fun.filter(skuDos, Objects::nonNull);
        aliRequest.setParamList(validSkuDos);
        return aliRequest;
    }

    /**
     * 商品更新
     *
     * @param channelPoiId
     * @param spuList
     * @return
     */
    public static AlibabaWdkSkuUpdateRequest buildUpdateRequest(String channelPoiId, Long tenantId, List<SpuInfoDTO> spuList) {

        AlibabaWdkSkuUpdateRequest aliRequest = new AlibabaWdkSkuUpdateRequest();
        List<AlibabaWdkSkuUpdateRequest.SkuDo> skuDos = Fun.map(spuList,
                spu -> buildUpdateSkuDo(channelPoiId, tenantId, spu));
        List<AlibabaWdkSkuUpdateRequest.SkuDo> validSkuDos = Fun.filter(skuDos, Objects::nonNull);
        aliRequest.setParamList(validSkuDos);
        return aliRequest;
    }

    public static AlibabaWdkSkuUpdateRequest buildUpdatePriceRequest(String channelPoiCode, List<SkuPriceMultiChannelDTO> oneStoreParams) {
        AlibabaWdkSkuUpdateRequest aliRequest = new AlibabaWdkSkuUpdateRequest();
        List<AlibabaWdkSkuUpdateRequest.SkuDo> skuDoList = Fun.map(oneStoreParams, param -> {
            AlibabaWdkSkuUpdateRequest.SkuDo skuDo = new AlibabaWdkSkuUpdateRequest.SkuDo();
            skuDo.setOuCode(channelPoiCode);
            skuDo.setSkuCode(param.getSkuId());
            skuDo.setMemberPrice(String.valueOf(param.getPrice()));
            return skuDo;
        });
        aliRequest.setParamList(skuDoList);
        return aliRequest;
    }

    public static AlibabaWdkSkuUpdateRequest.SkuDo buildUpdateSkuDo(String channelPoiId, Long tenantId, SpuInfoDTO spuInfoDTO) {

        if (spuInfoDTO == null) {
            return null;
        }
        AlibabaWdkSkuUpdateRequest.SkuDo skuDo = new AlibabaWdkSkuUpdateRequest.SkuDo();

        // 门店编码
        skuDo.setOuCode(channelPoiId);
        // 商品唯一编码
        skuDo.setSkuCode(spuInfoDTO.getCustomSpuId());
        // 商品名称
        skuDo.setSkuName(spuInfoDTO.getName());

        skuDo.setShortTitle(spuInfoDTO.getName());
        Set<Long> needUpdateCategoryTenants = MccConfigUtil.getTxdNeedUpdateProductCategoryTenants();
        if (CollectionUtils.isNotEmpty(needUpdateCategoryTenants) && needUpdateCategoryTenants.contains(tenantId)) {
            // 商家后台类目编码（同字段category_code），优先使用本字段
            if (StringUtils.isNotBlank(spuInfoDTO.getOfflineChannelCategoryCode())) {
                skuDo.setMerchantCatCode(spuInfoDTO.getOfflineChannelCategoryCode());
            }
            else {
                skuDo.setMerchantCatCode(TXD_DEFAULT_CATEGORY_CODE);
            }
        }
        if (StringUtils.isNotBlank(spuInfoDTO.getChannelCategoryId())) {
            skuDo.setForestCateId(Long.valueOf(spuInfoDTO.getChannelCategoryId()));
        }
        // 图片 大于5张取前5张
        if (CollectionUtils.isNotEmpty(spuInfoDTO.getPictures())) {
            List<String> subList = spuInfoDTO.getPictures();
            if (spuInfoDTO.getPictures().size() > TxdConstant.DEFAULT_PICTURE_LIMIT) {
                subList = spuInfoDTO.getPictures().subList(0, TxdConstant.DEFAULT_PICTURE_LIMIT);
            }

            String urlStr = subList.stream().filter(StringUtils::isNotEmpty)
                    .collect(Collectors.joining(","));
            skuDo.setSkuPicUrls(urlStr);
        }

        // 上下架状态- 1上架  0下架
        skuDo.setAllowAppSale(TxdOnlineStatusEnum.ON_SALE.getCode());
        skuDo.setOnlineSaleFlag(spuInfoDTO.getStatus() == SpuStatusEnum.ON_LINE.getCode()
                ? TxdOnlineStatusEnum.ON_SALE.getCode()
                : TxdOnlineStatusEnum.OFF_SALE.getCode());
        // 产地
        skuDo.setProducerPlace(spuInfoDTO.getProductionArea());
        // 配送条件；填常温、冷藏、冷冻
        skuDo.setDeliveryStorage(spuInfoDTO.getDeliveryRequirement());
        // 存储条件：填常温、冷藏、冷冻；和配送条件保持一致
        skuDo.setStorage(spuInfoDTO.getDeliveryRequirement());
        // 设置商品详情为富文本格式，包括图片和文本内容。文描,（同字段txt_desc）；优先使用本字段
        skuDo.setRichText(buildRichText(spuInfoDTO));
        /* 更新渠道商品时不更新和称重品相关的字段
        // 是否称重品 1：是  0：否（默认为0)
        if (spuInfoDTO.getIsSp() == 1) {
            skuDo.setWeightFlag(TxdWeightFlagEnum.NOT_WEIGHT_PRODUCT.getCode());
            // 均重，非称重品填1，A只有标品（非称重品）
            skuDo.setAvgWeight(TxdConstant.DEFAULT_AVG_WEIGHT);
            // 预扣款重量，非称重品填1，A只有标品（非称重品）
            skuDo.setPreMinusWeight(TxdConstant.DEFAULT_PRE_MINUS_WEIGHT);
            // 非称重品填1，A只有标品（非称重品）
            skuDo.setStepQuantity(TxdConstant.DEFAULT_STEP_QUANTITY);
        } else {
            skuDo.setWeightFlag(TxdWeightFlagEnum.WEIGHT_PRODUCT.getCode());
        }*/
        // 商品规格信息
        if (CollectionUtils.isNotEmpty(spuInfoDTO.getSkus())) {
            SkuInSpuInfoDTO skuInSpuInfoDTO = spuInfoDTO.getSkus().get(0);
            // 商品售价 （同字段sale_price），单位:元；优先使用本字段
            if (skuInSpuInfoDTO.isSetPrice()) {
                skuDo.setSkuPrice(String.valueOf(skuInSpuInfoDTO.getPrice()));
                skuDo.setMemberPrice(String.valueOf(skuInSpuInfoDTO.getPrice()));
            }

            //条码,默认是覆盖主条码
            if (spuInfoDTO.isUnCoverTxdUpc()) {
                skuDo.setBarcodes(null);
            }
            else {
                skuDo.setBarcodes(skuInSpuInfoDTO.getUpc());
            }
            String skuSaleUnit = getSkuSaleUnit(skuInSpuInfoDTO);
            // 商品销售单位
            skuDo.setSaleUnit(skuSaleUnit);
            // 设置商品库存单位
            skuDo.setInventoryUnit(skuSaleUnit);
            // 采购单位，与库存单位保持一致
            skuDo.setPurchaseUnit(skuSaleUnit);
            // 销售规格
            skuDo.setSaleSpec(getSkuSaleSpec(skuInSpuInfoDTO, skuSaleUnit));
            // 商品重量 单位g 必须为整数
            skuDo.setWeight(String.valueOf(skuInSpuInfoDTO.getWeight()));
            // 净含量，按重量传
            skuDo.setNetContent(getSkuNetContent(skuInSpuInfoDTO, skuSaleUnit));
            // 起购量
            skuDo.setPurchaseQuantity(getSkuPurchaseQuantity(skuInSpuInfoDTO));
            // 副标题，用卖点对接
            skuDo.setSubTitle(spuInfoDTO.getSellPoint());
        }
        return skuDo;

    }


    public static String buildRichText(SpuInfoDTO spuInfoDTO){
        String textDesc = "";
        if (Objects.nonNull(spuInfoDTO.getDescription())) {
            textDesc = spuInfoDTO.getDescription();
        }
        String picDesc = "";
        if (CollectionUtils.isNotEmpty(spuInfoDTO.getPictureContents())) {
            picDesc = buildPicDetail(spuInfoDTO.getPictureContents());
        }
        return textDesc + "\n" + picDesc;
    }


    public   static String buildPicDetail(List<String> imageUrls) {
        if (CollectionUtils.isEmpty(imageUrls)) {
            return Strings.EMPTY;
        }
        StringBuilder rtfDetailBuilder = new StringBuilder();
        String baseStr = "<p><img src=\"%s\"/></p>";
        for (String imageUrl : imageUrls) {
            if (StringUtils.isNotEmpty(imageUrl)) {
                String outStr = String.format(baseStr, imageUrl);
                rtfDetailBuilder.append(outStr);
            }
        }
        return rtfDetailBuilder.toString();
    }

    public static ResultSpuData genUpdateSpuResult(Long storeId, List<SpuKey> spuKeys,
                                                   TaobaoResponse commonResponse) {



        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (commonResponse == null) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        if (!commonResponse.isSuccess()) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    parseTxdErrorCode(commonResponse.getErrorCode()), commonResponse.getMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        AlibabaWdkSkuUpdateResponse response = (AlibabaWdkSkuUpdateResponse) commonResponse;
        if (response.getResult() == null) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        AlibabaWdkSkuUpdateResponse.ApiResults result = response.getResult();
        if (!BooleanUtils.isTrue(result.getSuccess())) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    parseTxdErrorCode(result.getErrCode()), result.getErrMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        if (CollectionUtils.isEmpty(result.getModels())) {
            return ProductResultDataUtils.combineSuccessDataList(resultSpuData, null, spuKeys);
        }

        List<ResultErrorSpu> errorDataList = resultSpuData.getErrorData();
        List<ResultSuccessSpu> successDataList = resultSpuData.getSucData();

        for (AlibabaWdkSkuUpdateResponse.ApiResult model : result.getModels()) {
            if (model == null) {
                continue;
            }
            if (BooleanUtils.isTrue(model.getSuccess())) {
                ResultSuccessSpu successSpu = new ResultSuccessSpu();
                successSpu.setChannelResultInfo(model.getModel());
                successSpu.setSpuInfo(new SpuKey(model.getModel(), model.getModel(), null));
                successSpu.setStoreId(storeId);
                successSpu.setChannelId(EnhanceChannelType.TXD.getChannelId());
                successDataList.add(successSpu);
            } else {
                ResultErrorSpu errorSpu = new ResultErrorSpu();
                errorSpu.setSpuInfo(new SpuKey(model.getModel(), model.getModel(), null));

                errorSpu.setErrorCode(parseTxdErrorCode(model.getErrCode()));
                errorSpu.setErrorMsg(model.getErrMsg());

                ProductChannelUnifyErrorEnum errorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(
                        ChannelTypeEnum.TXD, model.getErrCode(), model.getErrMsg());
                errorSpu.setChannelUnifyError(errorEnum);

                errorSpu.setStoreId(storeId);
                errorSpu.setChannelId(EnhanceChannelType.TXD.getChannelId());
                errorDataList.add(errorSpu);
            }

        }
        return resultSpuData;
    }

    /**
     * 更新价格
     *
     * @param channelPoiId
     * @param skuPriceMultiChannelDTOs
     * @return
     */
    public static AlibabaCdcTxdBatchSkuPriceUpdateRequest buildPriceRequest(String channelPoiId,
                                                                            List<SkuPriceMultiChannelDTO> skuPriceMultiChannelDTOs) {

        AlibabaCdcTxdBatchSkuPriceUpdateRequest aliRequest = new AlibabaCdcTxdBatchSkuPriceUpdateRequest();
        List<AlibabaCdcTxdBatchSkuPriceUpdateRequest.SkuPriceDO> skuPriceDOS = Fun.map(skuPriceMultiChannelDTOs,
                skuPriceMultiChannelDTO -> {
                    SkuPriceDOExt skuPriceDO = new SkuPriceDOExt();
                    skuPriceDO.setStoreId(channelPoiId);
                    skuPriceDO.setSalePrice(String.valueOf(skuPriceMultiChannelDTO.getPrice()));
                    skuPriceDO.setMemberPrice(String.valueOf(skuPriceMultiChannelDTO.getPrice()));
                    skuPriceDO.setSkuCode(skuPriceMultiChannelDTO.getSkuId());
                    return skuPriceDO;
                });
        aliRequest.setParamList(skuPriceDOS);
        return aliRequest;
    }


    /**
     * 扩展增加会员价
     */
    @Setter
    @Getter
    public static class SkuPriceDOExt extends  AlibabaCdcTxdBatchSkuPriceUpdateRequest.SkuPriceDO {
        @ApiField("member_price")
        private String memberPrice;
    }

    public static ResultData genPriceResult(Long storeId, List<String> skuKeys,
                                            TaobaoResponse commonResponse) {

        ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
        if (commonResponse == null) {
            return ProductResultDataUtils.combineExceptionDataList(resultData,
                    ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg(), skuKeys,
                    storeId, ChannelTypeEnum.TXD);
        }

        if (!commonResponse.isSuccess()) {
            return ProductResultDataUtils.combineExceptionDataList(resultData,
                    parseTxdErrorCode(commonResponse.getErrorCode()), commonResponse.getMsg(), skuKeys, storeId, ChannelTypeEnum.TXD);
        }

        AlibabaCdcTxdBatchSkuPriceUpdateResponse response = (AlibabaCdcTxdBatchSkuPriceUpdateResponse) commonResponse;
        if (response.getResult() == null) {
            return ProductResultDataUtils.combineExceptionDataList(resultData,
                    ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg(), skuKeys,
                    storeId, ChannelTypeEnum.TXD);
        }

        AlibabaCdcTxdBatchSkuPriceUpdateResponse.ApiResults result = response.getResult();
        if (!BooleanUtils.isTrue(result.getSuccess())) {
            return ProductResultDataUtils.combineExceptionDataList(resultData, parseTxdErrorCode(result.getErrCode()),
                    result.getErrMsg(), skuKeys, storeId, ChannelTypeEnum.TXD);
        }

        if (CollectionUtils.isEmpty(result.getModels())) {
            return ProductResultDataUtils.combineSuccessDataList(resultData, null, skuKeys, storeId);
        }

        List<ResultErrorSku> errorDataList = resultData.getErrorData();
        List<ResultSuccessSku> successDataList = resultData.getSucData();

        for (AlibabaCdcTxdBatchSkuPriceUpdateResponse.ApiResult model : result.getModels()) {
            if (model == null) {
                continue;
            }
            if (BooleanUtils.isTrue(model.getSuccess())) {
                ResultSuccessSku successSku = new ResultSuccessSku();
                successSku.setChannelResultInfo(model.getModel());
                successSku.setSkuId(model.getModel());
                successSku.setChannelSkuId(model.getModel());
                successSku.setChannelSpuId(model.getModel());
                successSku.setChannelId(EnhanceChannelType.TXD.getChannelId());
                successSku.setStoreId(storeId);
                successDataList.add(successSku);
            } else {
                ResultErrorSku errorSku = new ResultErrorSku();

                errorSku.setChannelSkuId(model.getModel());
                errorSku.setSkuId(model.getModel());
                errorSku.setChannelSpuId(model.getModel());
                errorSku.setErrorCode(parseTxdErrorCode(model.getErrCode()));
                errorSku.setErrorMsg(model.getErrMsg());
                ProductChannelUnifyErrorEnum channelUnifiedError = ProductChannelErrorMappingUtils.getChannelUnifiedError(
                        ChannelTypeEnum.TXD, model.getErrCode(), model.getErrMsg());
                errorSku.setChannelUnifyError(channelUnifiedError);
                errorSku.setChannelId(EnhanceChannelType.TXD.getChannelId());
                errorSku.setStoreId(storeId);
                errorDataList.add(errorSku);
            }

        }
        return resultData;
    }

    /**
     * 更新上下架
     *
     * @param channelPoiId
     * @param spuInfoSellStatusDTO
     * @return
     */
    public static AlibabaCdcTxdBatchSkuStatusUpdateRequest buildStatusRequest(String channelPoiId,
                                                                              SpuInfoSellStatusDTO spuInfoSellStatusDTO) {

        AlibabaCdcTxdBatchSkuStatusUpdateRequest aliRequest = new AlibabaCdcTxdBatchSkuStatusUpdateRequest();
        List<AlibabaCdcTxdBatchSkuStatusUpdateRequest.SkuStatusDO> skuStatusDOS = Fun.map(
                spuInfoSellStatusDTO.getCustomSpuIds(), customSpuId -> {
                    AlibabaCdcTxdBatchSkuStatusUpdateRequest.SkuStatusDO skuStatusDO = new AlibabaCdcTxdBatchSkuStatusUpdateRequest.SkuStatusDO();
                    skuStatusDO.setStoreId(channelPoiId);
                    if (spuInfoSellStatusDTO.getSpuStatus() == SpuStatusEnum.ON_LINE.getCode()) {
                        skuStatusDO.setOnlineSaleFlag(TxdOnlineStatusEnum.ON_SALE.getCode());
                    } else {
                        skuStatusDO.setOnlineSaleFlag(TxdOnlineStatusEnum.OFF_SALE.getCode());
                    }
                    skuStatusDO.setSkuCode(customSpuId);
                    return skuStatusDO;
                });
        aliRequest.setParamList(skuStatusDOS);
        return aliRequest;
    }

    public static ResultSpuData genUpdateSpuStatusResult(Long storeId, List<SpuKey> spuKeys,
                                                         TaobaoResponse commonResponse) {

        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (commonResponse == null) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        if (!commonResponse.isSuccess()) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    parseTxdErrorCode(commonResponse.getErrorCode()), commonResponse.getMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        AlibabaCdcTxdBatchSkuStatusUpdateResponse response = (AlibabaCdcTxdBatchSkuStatusUpdateResponse) commonResponse;
        if (response.getResult() == null) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        AlibabaCdcTxdBatchSkuStatusUpdateResponse.ApiResults result = response.getResult();
        if (!BooleanUtils.isTrue(result.getSuccess())) {
            return ProductResultDataUtils.combineExceptionDataList(resultSpuData,
                    parseTxdErrorCode(result.getErrCode()), result.getErrMsg(), spuKeys, ChannelTypeEnum.TXD);
        }

        if (CollectionUtils.isEmpty(result.getModels())) {
            return ProductResultDataUtils.combineSuccessDataList(resultSpuData, null, spuKeys);
        }

        List<ResultErrorSpu> errorDataList = resultSpuData.getErrorData();
        List<ResultSuccessSpu> successDataList = resultSpuData.getSucData();

        for (AlibabaCdcTxdBatchSkuStatusUpdateResponse.ApiResult model : result.getModels()) {
            if (model == null) {
                continue;
            }
            if (BooleanUtils.isTrue(model.getSuccess())) {
                ResultSuccessSpu successSpu = new ResultSuccessSpu();
                successSpu.setChannelResultInfo(model.getModel());
                successSpu.setSpuInfo(new SpuKey(model.getModel(), model.getModel(), null));
                successSpu.setChannelId(EnhanceChannelType.TXD.getChannelId());
                successSpu.setStoreId(storeId);
                successDataList.add(successSpu);
            } else {
                ResultErrorSpu errorSpu = new ResultErrorSpu();
                errorSpu.setSpuInfo(new SpuKey(model.getModel(), model.getModel(), null));

                errorSpu.setErrorCode(parseTxdErrorCode(model.getErrCode()));
                errorSpu.setErrorMsg(model.getErrMsg());

                ProductChannelUnifyErrorEnum channelUnifiedError = ProductChannelErrorMappingUtils.getChannelUnifiedError(
                        ChannelTypeEnum.TXD, model.getErrCode(), model.getErrMsg());
                errorSpu.setChannelUnifyError(channelUnifiedError);


                errorSpu.setChannelId(EnhanceChannelType.TXD.getChannelId());
                errorSpu.setStoreId(storeId);
                errorDataList.add(errorSpu);
            }

        }
        return resultSpuData;
    }

    public static AlibabaAxChannelSkuStatusUpdateRequest buildSingleSpuOffSaleRequest(
            String channelPoiId, SpuInfoDeleteDTO spuDeleteDTO, String txdChannelCode) {
        AlibabaAxChannelSkuStatusUpdateRequest.ChannelSkuUpdateStatusReq req = new AlibabaAxChannelSkuStatusUpdateRequest.ChannelSkuUpdateStatusReq();
        req.setStoreId(channelPoiId);
        req.setSkuCode(spuDeleteDTO.getCustomSpuId());
        req.setChannelCode(txdChannelCode);
        req.setOnlineSaleFlag(TxdOnlineStatusEnum.OFF_SALE.getCode());
        AlibabaAxChannelSkuStatusUpdateRequest request = new AlibabaAxChannelSkuStatusUpdateRequest();
        request.setChannelSkuUpdateStatusReq(req);
        return request;
    }

    public static AlibabaAxChannelSkuStatusUpdateRequest buildSingleSpuStatusRequest(
            String channelPoiId, SingleSpuSellStatusDTO spuSellStatusDTO, String txdChannelCode) {
        long saleFlag = Objects.equals(SpuStatusEnum.ON_LINE.getCode(), spuSellStatusDTO.getSpuStatus())
                ? TxdOnlineStatusEnum.ON_SALE.getCode()
                : TxdOnlineStatusEnum.OFF_SALE.getCode();
        AlibabaAxChannelSkuStatusUpdateRequest.ChannelSkuUpdateStatusReq req = new AlibabaAxChannelSkuStatusUpdateRequest.ChannelSkuUpdateStatusReq();
        req.setStoreId(channelPoiId);
        req.setSkuCode(spuSellStatusDTO.getCustomSpuId());
        req.setChannelCode(txdChannelCode);
        req.setOnlineSaleFlag(saleFlag);
        AlibabaAxChannelSkuStatusUpdateRequest request = new AlibabaAxChannelSkuStatusUpdateRequest();
        request.setChannelSkuUpdateStatusReq(req);
        return request;
    }

    public static ResultSingleSpuData genUpdateSingleSpuStatusResult(Long storeId, SpuKey spuKey, TaobaoResponse commonResponse) {

        ResultSingleSpuData resultSpuData = ResultGenerator.genResultSingleSpuData(ResultCode.SUCCESS);
        if (!(commonResponse instanceof AlibabaAxChannelSkuStatusUpdateResponse)) {
            return ResultSpuDataUtils.combineExceptionData(resultSpuData, ChannelTypeEnum.TXD, storeId, spuKey,
                    ResultCode.CHANNEL_RESPONSE_NULL);
        }

        if (!commonResponse.isSuccess()) {
            return ResultSpuDataUtils.combineExceptionData(resultSpuData, ChannelTypeEnum.TXD, storeId, spuKey,
                    parseTxdErrorCode(commonResponse.getErrorCode()), commonResponse.getMsg());
        }

        AlibabaAxChannelSkuStatusUpdateResponse response = (AlibabaAxChannelSkuStatusUpdateResponse) commonResponse;
        AlibabaAxChannelSkuStatusUpdateResponse.ApiResult apiResult = response.getApiResult();
        if (!BooleanUtils.isTrue(apiResult.getSuccess())) {
            return ResultSpuDataUtils.combineExceptionData(resultSpuData, ChannelTypeEnum.TXD, storeId, spuKey,
                    parseTxdErrorCode(apiResult.getErrCode()), apiResult.getErrMsg());
        }

        if (BooleanUtils.isTrue(apiResult.getSuccess())) {
            ResultSuccessSpu successSpu = new ResultSuccessSpu()
                    .setSpuInfo(spuKey)
                    .setChannelId(EnhanceChannelType.TXD.getChannelId())
                    .setStoreId(storeId);
            resultSpuData.setSucData(successSpu);
            return resultSpuData;
        } else {
            return ResultSpuDataUtils.combineExceptionData(resultSpuData, ChannelTypeEnum.TXD, storeId, spuKey,
                    parseTxdErrorCode(apiResult.getErrCode()), apiResult.getErrMsg());
        }
    }

    /**
     * 上传图片
     *
     * @param pictureUploadDTO
     * @return
     */
    public static AlibabaWdkPictureUploadRequest buildPictureUploadRequest(PictureUploadDTO pictureUploadDTO) {
        AlibabaWdkPictureUploadRequest aliRequest = new AlibabaWdkPictureUploadRequest();
        FileItem fileItem = null;
        try {
            fileItem = new FileItem(pictureUploadDTO.getPictureName(),
                    ImageUtils.imageBytes(pictureUploadDTO.getUrl()));
        } catch (Exception e) {
            log.error("构建淘鲜达图片参数异常:{}", pictureUploadDTO, e);
            throw new RuntimeException(e);
        }
        aliRequest.setImg(fileItem);
        aliRequest.setImgInputTitle(pictureUploadDTO.getPictureName());
        aliRequest.setTitle(pictureUploadDTO.getPictureName());
        aliRequest.setPictureCategoryId(TxdConstant.DEFAULT_PICTURE_CATEGORY_ID);
        return aliRequest;
    }

    public static ResultData genPictureUploadResponse(TaobaoResponse commonResponse,
                                                      PictureUploadDTO pictureUploadDTO) {
        ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
        if (commonResponse == null) {
            ResultErrorSku resultErrorSku = new ResultErrorSku().setSkuId(pictureUploadDTO.getUid())
                    .setErrorCode(ResultCode.CHANNEL_RESPONSE_NULL.getCode())
                    .setErrorMsg(ResultCode.CHANNEL_RESPONSE_NULL.getMsg());
            resultData.getErrorData().add(resultErrorSku);
            return resultData;
        }

        if (!commonResponse.isSuccess()) {
            ResultErrorSku resultErrorSku = new ResultErrorSku().setSkuId(pictureUploadDTO.getUid())
                    .setErrorCode(parseTxdErrorCode(commonResponse.getErrorCode())).setErrorMsg(commonResponse.getMsg());
            resultData.getErrorData().add(resultErrorSku);
            return resultData;
        }

        AlibabaWdkPictureUploadResponse response = (AlibabaWdkPictureUploadResponse) commonResponse;

        if (response.getResult() == null) {
            ResultErrorSku resultErrorSku = new ResultErrorSku().setSkuId(pictureUploadDTO.getUid())
                    .setErrorCode(ResultCode.CHANNEL_RESPONSE_NULL.getCode())
                    .setErrorMsg(ResultCode.CHANNEL_RESPONSE_NULL.getMsg());
            resultData.getErrorData().add(resultErrorSku);
            return resultData;
        }

        AlibabaWdkPictureUploadResponse.ApiResult result = response.getResult();
        if (!BooleanUtils.isTrue(result.getSuccess())) {
            ResultErrorSku resultErrorSku = new ResultErrorSku().setSkuId(pictureUploadDTO.getUid())
                    .setErrorCode(parseTxdErrorCode(result.getErrCode())).setErrorMsg(result.getErrMsg());
            resultData.getErrorData().add(resultErrorSku);
            return resultData;
        }

        if (result.getModel() == null) {
            ResultErrorSku resultErrorSku = new ResultErrorSku().setSkuId(pictureUploadDTO.getUid())
                    .setErrorCode(ResultCode.CHANNEL_RESPONSE_NULL.getCode())
                    .setErrorMsg(ResultCode.CHANNEL_RESPONSE_NULL.getMsg());
            resultData.getErrorData().add(resultErrorSku);
            return resultData;
        }

        ResultSuccessSku resultSuccessSku = new ResultSuccessSku().setSkuId(pictureUploadDTO.getUid())
                .setChannelResultInfo(String.valueOf(result.getModel().getFullUrl()));
        resultData.getSucData().add(resultSuccessSku);
        return resultData;

    }

    /**
     * 查询商品
     *
     * @param channelPoiId
     * @param customSpuIds
     * @return
     */
    public static AlibabaWdkSkuQueryRequest buildQuerySpuRequest(String channelPoiId, List<String> customSpuIds) {
        AlibabaWdkSkuQueryRequest request = new AlibabaWdkSkuQueryRequest();
        AlibabaWdkSkuQueryRequest.SkuQueryDo skuQueryDo = new AlibabaWdkSkuQueryRequest.SkuQueryDo();
        skuQueryDo.setOuCode(channelPoiId);
        skuQueryDo.setSkuCodes(customSpuIds);
        request.setParam(skuQueryDo);
        return request;
    }

    /**
     * 查询商品
     *
     * @param channelPoiId
     * @param scrollId
     * @return
     */
    public static AlibabaWdkSkuStoreskuScrollQueryRequest buildQueryStoreSpuRequest(String channelPoiId, String scrollId) {
        AlibabaWdkSkuStoreskuScrollQueryRequest request = new AlibabaWdkSkuStoreskuScrollQueryRequest();
        request.setStoreId(channelPoiId);
        request.setScrollId(scrollId);
        return request;
    }


    public static GetSpuInfoResponse genQuerySpuResult(TaobaoResponse commonResponse) {

        GetSpuInfoResponse spuInfoResponse = new GetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());;
        if (commonResponse == null) {
            return spuInfoResponse.setStatus(genResult(ResultCode.CHANNEL_RESPONSE_NULL));
        }

        if (!commonResponse.isSuccess()) {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(parseTxdErrorCode(commonResponse.getCode()));
            resultStatus.setMsg(commonResponse.getMsg());
            return spuInfoResponse.setStatus(resultStatus);
        }

        AlibabaWdkSkuQueryResponse aliResponse = (AlibabaWdkSkuQueryResponse) commonResponse;
        if (aliResponse.getResult() == null) {
            return spuInfoResponse.setStatus(genResult(ResultCode.CHANNEL_RESPONSE_NULL));
        }

        AlibabaWdkSkuQueryResponse.ApiResults result = aliResponse.getResult();

        if (!BooleanUtils.isTrue(result.getSuccess())) {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(parseTxdErrorCode(result.getErrCode()));
            resultStatus.setMsg(result.getErrMsg());
            return spuInfoResponse.setStatus(resultStatus);
        }

        if (CollectionUtils.isEmpty(result.getModels())) {
            return spuInfoResponse.setStatus(ResultGenerator.genSuccessResult()).setSpuInfo(null);
        }


        List<SpuInfoDTO> spuInfoDTOS = new ArrayList<>();
        for (AlibabaWdkSkuQueryResponse.ApiResult model : result.getModels()) {
            if (model == null || model.getModel() == null) {
                continue;
            }
            if (!BooleanUtils.isTrue(model.getSuccess())) {
                log.warn("query txd product error:{}", model.getErrMsg());
                continue;
            }
            spuInfoDTOS.add(buildSpuInfo(model.getModel()));
        }
        if (CollectionUtils.isNotEmpty(spuInfoDTOS)) {
            spuInfoResponse.setSpuInfo(spuInfoDTOS.get(0));
        }
        return spuInfoResponse;
    }

    public static GetSpuInfosResponse genGetSpuInfosResult(AlibabaWdkSkuQueryResponse aliResponse) {

        GetSpuInfosResponse spuInfoResponse = new GetSpuInfosResponse().setStatus(ResultGenerator.genSuccessResult());
        if (aliResponse == null) {
            return spuInfoResponse.setStatus(genResult(ResultCode.CHANNEL_RESPONSE_NULL));
        }

        if (!aliResponse.isSuccess()) {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(parseTxdErrorCode(aliResponse.getCode()));
            resultStatus.setMsg(aliResponse.getMsg());
            return spuInfoResponse.setStatus(resultStatus);
        }

        if (aliResponse.getResult() == null) {
            return spuInfoResponse.setStatus(genResult(ResultCode.CHANNEL_RESPONSE_NULL));
        }

        AlibabaWdkSkuQueryResponse.ApiResults result = aliResponse.getResult();

        if (!BooleanUtils.isTrue(result.getSuccess())) {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(parseTxdErrorCode(result.getErrCode()));
            resultStatus.setMsg(result.getErrMsg());
            return spuInfoResponse.setStatus(resultStatus);
        }

        if (CollectionUtils.isEmpty(result.getModels())) {
            return spuInfoResponse.setStatus(ResultGenerator.genSuccessResult()).setSpuInfos(new ArrayList<>());
        }

        List<SpuInfoDTO> spuInfoDTOS = new ArrayList<>();
        for (AlibabaWdkSkuQueryResponse.ApiResult model : result.getModels()) {
            if (model == null || model.getModel() == null) {
                continue;
            }
            if (!BooleanUtils.isTrue(model.getSuccess())) {
                log.warn("query txd product error:{}", model.getErrMsg());
                continue;
            }
            spuInfoDTOS.add(buildSpuInfo(model.getModel()));
        }
        spuInfoResponse.setSpuInfos(spuInfoDTOS);
        return spuInfoResponse;
    }

    public static void genBatchQuerySpuResult(AlibabaWdkSkuQueryResponse aliResponse, BatchGetSpuInfoResponse spuInfoResponse) {
        if (aliResponse == null) {
            spuInfoResponse.setStatus(genResult(ResultCode.CHANNEL_RESPONSE_NULL));
            return;
        }

        if (!aliResponse.isSuccess()) {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(parseTxdErrorCode(aliResponse.getCode()));
            resultStatus.setMsg(aliResponse.getMsg());
            spuInfoResponse.setStatus(resultStatus);
            return;
        }

        if (aliResponse.getResult() == null) {
            spuInfoResponse.setStatus(genResult(ResultCode.CHANNEL_RESPONSE_NULL));
            return;
        }

        AlibabaWdkSkuQueryResponse.ApiResults result = aliResponse.getResult();

        if (!BooleanUtils.isTrue(result.getSuccess())) {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(parseTxdErrorCode(result.getErrCode()));
            resultStatus.setMsg(result.getErrMsg());
            spuInfoResponse.setStatus(resultStatus);
            return;
        }

        if (CollectionUtils.isEmpty(result.getModels())) {
            spuInfoResponse.setStatus(ResultGenerator.genSuccessResult()).setSpuInfos(new ArrayList<>());
            return;
        }

        List<SpuInfoDTO> spuInfoDTOS = new ArrayList<>();
        for (AlibabaWdkSkuQueryResponse.ApiResult model : result.getModels()) {
            if (model == null || model.getModel() == null) {
                continue;
            }
            if (!BooleanUtils.isTrue(model.getSuccess())) {
                log.warn("query txd product error:{}", model.getErrMsg());
                continue;
            }
            spuInfoDTOS.add(buildSpuInfo(model.getModel()));
        }
        spuInfoResponse.setSpuInfos(spuInfoDTOS);
    }

    public static BatchGetSpuInfoResponse genQueryStoreSpuResult(AlibabaWdkSkuStoreskuScrollQueryResponse aliResponse) {
        BatchGetSpuInfoResponse spuInfoResponse = new BatchGetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());

        if (aliResponse == null) {
            return spuInfoResponse.setStatus(genResult(ResultCode.CHANNEL_RESPONSE_NULL));
        }

        if (!aliResponse.isSuccess()) {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(parseTxdErrorCode(aliResponse.getCode()));
            resultStatus.setMsg(aliResponse.getMsg());
            return spuInfoResponse.setStatus(resultStatus);
        }

        if (aliResponse.getResult() == null) {
            return spuInfoResponse.setStatus(genResult(ResultCode.CHANNEL_RESPONSE_NULL));
        }

        AlibabaWdkSkuStoreskuScrollQueryResponse.ApiScrollPageResult pageResult = aliResponse.getResult();

        if (!BooleanUtils.isTrue(pageResult.getSuccess())) {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(parseTxdErrorCode(pageResult.getErrCode()));
            resultStatus.setMsg(pageResult.getErrMsg());
            return spuInfoResponse.setStatus(resultStatus);
        }

        PageInfo pageInfo = new PageInfo().setPageNum(Optional.ofNullable(pageResult.getPageCount()).orElse(0L).intValue())
                .setPageSize(Optional.ofNullable(pageResult.getPageSize()).orElse(20L).intValue())
                .setTotalNum(Optional.of(pageResult.getTotal()).orElse(0L).intValue());
        spuInfoResponse.setPageInfo(pageInfo);
        spuInfoResponse.setOffset(pageResult.getScrollId());

        if (CollectionUtils.isEmpty(pageResult.getModelList())) {
            return spuInfoResponse.setSpuInfos(new ArrayList<>());
        }

        List<SpuInfoDTO> spuInfoDTOS = new ArrayList<>();
        for (AlibabaWdkSkuStoreskuScrollQueryResponse.WdkOpenMerchantStoreSkuDo model : pageResult.getModelList()) {
            if (Objects.isNull(model)) {
                continue;
            }
            spuInfoDTOS.add(buildSpuInfo(model));
        }
        spuInfoResponse.setSpuInfos(spuInfoDTOS);
        return spuInfoResponse;
    }


    private static SpuInfoDTO buildSpuInfo(AlibabaWdkSkuStoreskuScrollQueryResponse.WdkOpenMerchantStoreSkuDo skuDo) {
        SpuInfoDTO spuInfoDTO = new SpuInfoDTO();
        spuInfoDTO.setName(skuDo.getSkuName());
        spuInfoDTO.setChannelSpuId(skuDo.getSkuCode());
        spuInfoDTO.setCustomSpuId(skuDo.getSkuCode());
        // 上下架状态
        if (skuDo.getOnlineSaleFlag() != null) {
            if (Objects.equals(TxdOnlineStatusEnum.ON_SALE.getCode(), skuDo.getOnlineSaleFlag())) {
                spuInfoDTO.setStatus(SpuStatusEnum.ON_LINE.getCode());
            }
            else {
                spuInfoDTO.setStatus(SpuStatusEnum.OFF_LINE.getCode());
            }
        }
        // 商品规格信息
        SkuInSpuInfoDTO skuInSpuInfoDTO = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO.setCustomSkuId(skuDo.getSkuCode());
        // 条码
        skuInSpuInfoDTO.setUpc(skuDo.getBarcode());
        spuInfoDTO.setSkus(Collections.singletonList(skuInSpuInfoDTO));
        return spuInfoDTO;
    }

    private static SpuInfoDTO buildSpuInfo(AlibabaWdkSkuQueryResponse.SkuDo skuDo) {
        SpuInfoDTO spuInfoDTO = new SpuInfoDTO();
        spuInfoDTO.setName(skuDo.getSkuName());
        spuInfoDTO.setChannelSpuId(skuDo.getSkuCode());
        spuInfoDTO.setCustomSpuId(skuDo.getSkuCode());
        // 商家后台类目
        if (!TXD_DEFAULT_CATEGORY_CODE.equals(skuDo.getMerchantCatCode())) {
            spuInfoDTO.setOfflineChannelCategoryCode(skuDo.getMerchantCatCode());
        }
        // 配送条件
        spuInfoDTO.setDeliveryRequirement(skuDo.getDeliveryWayName());
        // 产地
        spuInfoDTO.setProductionArea(skuDo.getProducerPlace());
        // 描述
        spuInfoDTO.setDescription(skuDo.getRichText());
        // 是否称重
        if (skuDo.getWeightFlag() != null) {
            if (Objects.equals(TxdWeightFlagEnum.WEIGHT_PRODUCT.getCode(), skuDo.getWeightFlag())) {
                spuInfoDTO.setIsSp(0);
            }
            else {
                spuInfoDTO.setIsSp(1);
            }
        }
        // 上下架状态
        if (skuDo.getOnlineSaleFlag() != null) {
            if (Objects.equals(TxdOnlineStatusEnum.ON_SALE.getCode(), skuDo.getOnlineSaleFlag())) {
                spuInfoDTO.setStatus(SpuStatusEnum.ON_LINE.getCode());
            }
            else {
                spuInfoDTO.setStatus(SpuStatusEnum.OFF_LINE.getCode());
            }

        }
        // 商品规格信息
        SkuInSpuInfoDTO skuInSpuInfoDTO = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO.setCustomSkuId(skuDo.getSkuCode());
        skuInSpuInfoDTO.setChannelSkuId(skuDo.getSkuCode());
        // 价格
        if (StringUtils.isNotEmpty(skuDo.getSalePrice())) {
            skuInSpuInfoDTO.setPrice(Double.parseDouble(skuDo.getSalePrice()));
        }

        // 条码
        skuInSpuInfoDTO.setUpc(skuDo.getBarcodes());
        // 售卖单位
        skuInSpuInfoDTO.setUnit(skuDo.getInventoryUnit());
        // 重量
        if (StringUtils.isNotEmpty(skuDo.getWeight())) {
            skuInSpuInfoDTO.setWeight(Integer.parseInt(skuDo.getWeight()));
        }
        spuInfoDTO.setSkus(Collections.singletonList(skuInSpuInfoDTO));
        return spuInfoDTO;
    }

    public static Integer parseTxdErrorCode(String errCode) {
        int code = ResultCode.FAIL.getCode();
        if (Objects.nonNull(errCode) && errCode.matches("\\d+")) {
            code = Integer.parseInt(errCode);
        }
        return code;
    }

    public static AlibabaWdkSkuCategoryAddRequest buildCategoryAddRequest(ChannelOfflineCategoryDTO channelOfflineCategoryDTO) {
        AlibabaWdkSkuCategoryAddRequest alibabaWdkSkuCategoryAddRequest = new AlibabaWdkSkuCategoryAddRequest();
        AlibabaWdkSkuCategoryAddRequest.CategoryDo categoryDo = new AlibabaWdkSkuCategoryAddRequest.CategoryDo();
        categoryDo.setCode(channelOfflineCategoryDTO.getChannelCode());
        categoryDo.setName(channelOfflineCategoryDTO.getName());
        if(StringUtils.isNotBlank(channelOfflineCategoryDTO.getParentChannelCode())
                && !channelOfflineCategoryDTO.getParentChannelCode().equals(CATEGORY_ROOT_CODE)) {
            categoryDo.setParentCode(channelOfflineCategoryDTO.getParentChannelCode());
        }
        categoryDo.setLeaf(channelOfflineCategoryDTO.getIsLeaf().equals(CommonYesNoEnum.YES.getCode()));
        alibabaWdkSkuCategoryAddRequest.setParam(categoryDo);
        return alibabaWdkSkuCategoryAddRequest;
    }

    public static ChannelOfflineCategoryCreateResponse genAddCategoryResult(TaobaoResponse commonResponse,
                                                                            ChannelOfflineCategoryDTO channelOfflineCategoryDTO) {
        ChannelOfflineCategoryCreateResponse channelOfflineCategoryCreateResponse = new ChannelOfflineCategoryCreateResponse();
        channelOfflineCategoryCreateResponse.setStatus(ChannelStatus.buildSuccess());

        if (commonResponse == null) {
            channelOfflineCategoryCreateResponse.setStatus(ChannelStatus.build(ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg()));
            return channelOfflineCategoryCreateResponse;
        }
        if (!commonResponse.isSuccess()) {
            channelOfflineCategoryCreateResponse.setStatus(ChannelStatus.build(parseTxdErrorCode(commonResponse.getErrorCode()), commonResponse.getMsg()));
            return channelOfflineCategoryCreateResponse;
        }

        AlibabaWdkSkuCategoryAddResponse addResponse = (AlibabaWdkSkuCategoryAddResponse) commonResponse;
        AlibabaWdkSkuCategoryAddResponse.ApiResult apiResult = addResponse.getResult();
        if (!apiResult.getSuccess()) {
            channelOfflineCategoryCreateResponse.setStatus(ChannelStatus.build(parseTxdErrorCode(apiResult.getErrCode()),
                    apiResult.getErrMsg()));
        }
        channelOfflineCategoryCreateResponse.setChannelOfflineCategoryCode(channelOfflineCategoryDTO.getChannelCode());
        return channelOfflineCategoryCreateResponse;
    }

    public static AlibabaWdkSkuCategoryUpdateRequest buildCategoryUpdateRequest(ChannelOfflineCategoryDTO channelOfflineCategoryDTO) {
        AlibabaWdkSkuCategoryUpdateRequest alibabaWdkSkuCategoryUpdateRequest = new AlibabaWdkSkuCategoryUpdateRequest();
        AlibabaWdkSkuCategoryUpdateRequest.CategoryDo categoryDo = new AlibabaWdkSkuCategoryUpdateRequest.CategoryDo();
        categoryDo.setCode(channelOfflineCategoryDTO.getChannelCode());
        categoryDo.setName(channelOfflineCategoryDTO.getName());
        alibabaWdkSkuCategoryUpdateRequest.setParam(categoryDo);
        return alibabaWdkSkuCategoryUpdateRequest;
    }

    public static ChannelOfflineCategoryResponse genUpdateCategoryResult(TaobaoResponse commonResponse) {
        ChannelOfflineCategoryResponse channelOfflineCategoryResponse = new ChannelOfflineCategoryResponse();
        channelOfflineCategoryResponse.setStatus(ChannelStatus.buildSuccess());


        if (commonResponse == null) {
            channelOfflineCategoryResponse.setStatus(ChannelStatus.build(ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg()));
            return channelOfflineCategoryResponse;
        }
        if (!commonResponse.isSuccess()) {
            channelOfflineCategoryResponse.setStatus(ChannelStatus.build(parseTxdErrorCode(commonResponse.getErrorCode()), commonResponse.getMsg()));
            return channelOfflineCategoryResponse;
        }

        AlibabaWdkSkuCategoryUpdateResponse updateResponse = (AlibabaWdkSkuCategoryUpdateResponse) commonResponse;
        AlibabaWdkSkuCategoryUpdateResponse.ApiResult apiResult = updateResponse.getResult();
        if (!apiResult.getSuccess()) {
            channelOfflineCategoryResponse.setStatus(ChannelStatus.build(parseTxdErrorCode(apiResult.getErrCode()),
                    apiResult.getErrMsg()));
        }
        return channelOfflineCategoryResponse;
    }

    public static AlibabaWdkSkuCategoryDeleteRequest buildDeleteRequest(String channelOfflineCategoryCode) {
        AlibabaWdkSkuCategoryDeleteRequest alibabaWdkSkuCategoryDeleteRequest = new AlibabaWdkSkuCategoryDeleteRequest();
        AlibabaWdkSkuCategoryDeleteRequest.CategoryDo categoryDo = new AlibabaWdkSkuCategoryDeleteRequest.CategoryDo();
        categoryDo.setCode(channelOfflineCategoryCode);
        alibabaWdkSkuCategoryDeleteRequest.setParam(categoryDo);
        return alibabaWdkSkuCategoryDeleteRequest;
    }

    public static ChannelOfflineCategoryResponse genDeleteCategoryResult(TaobaoResponse commonResponse) {
        ChannelOfflineCategoryResponse channelOfflineCategoryResponse = new ChannelOfflineCategoryResponse();
        channelOfflineCategoryResponse.setStatus(ChannelStatus.buildSuccess());

        if (commonResponse == null) {
            channelOfflineCategoryResponse.setStatus(ChannelStatus.build(ResultCode.CHANNEL_RESPONSE_NULL.getCode(), ResultCode.CHANNEL_RESPONSE_NULL.getMsg()));
            return channelOfflineCategoryResponse;
        }
        if (!commonResponse.isSuccess()) {
            channelOfflineCategoryResponse.setStatus(ChannelStatus.build(parseTxdErrorCode(commonResponse.getErrorCode()), commonResponse.getMsg()));
            return channelOfflineCategoryResponse;
        }


        AlibabaWdkSkuCategoryDeleteResponse deleteResponse = (AlibabaWdkSkuCategoryDeleteResponse) commonResponse;
        AlibabaWdkSkuCategoryDeleteResponse.ApiResult apiResult = deleteResponse.getResult();
        if (!apiResult.getSuccess()) {
            // 删除幂等
            if (MccConfigUtil.getTxdDeleteOfflineCategorySting().contains(apiResult.getErrMsg())) {
                return channelOfflineCategoryResponse;
            }
            channelOfflineCategoryResponse.setStatus(ChannelStatus.build(parseTxdErrorCode(apiResult.getErrCode()),
                    apiResult.getErrMsg()));
        }
        return channelOfflineCategoryResponse;
    }

    public static AlibabaWdkSkuCategoryQueryRequest buildQueryCategoryRequest(String channelOfflineCategoryCode) {
        AlibabaWdkSkuCategoryQueryRequest alibabaWdkSkuCategoryQueryRequest = new AlibabaWdkSkuCategoryQueryRequest();
        AlibabaWdkSkuCategoryQueryRequest.CategoryDo categoryDo = new AlibabaWdkSkuCategoryQueryRequest.CategoryDo();
        categoryDo.setCode(channelOfflineCategoryCode);
        alibabaWdkSkuCategoryQueryRequest.setParam(categoryDo);
        return alibabaWdkSkuCategoryQueryRequest;
    }

    public static List<ChannelOfflineCategoryDTO> genQueryCategoryResult(AlibabaWdkSkuCategoryQueryResponse commonResponse,
                                                                             Map<Long, ChannelOfflineCategoryDTO> codeToCategoryMap) {
        AlibabaWdkSkuCategoryQueryResponse.ApiResult apiResult = commonResponse.getResult();

        TxdOfflineCategory txdOfflineCategory = JsonUtils.fromJson(apiResult.getModel(), TxdOfflineCategory.class);
        List<TxdOfflineCategory> childCategories =
                Optional.ofNullable(txdOfflineCategory).map(TxdOfflineCategory::getChildCategories).orElse(null);
        List<ChannelOfflineCategoryDTO> childCategoryList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(childCategories)) {
            for (TxdOfflineCategory childCategory : childCategories) {
                if (TXD_DEFAULT_CATEGORY_CODE.equals(childCategory.getCode())) {
                    continue;
                }
                // 删除的为-1
                if (childCategory.getStatus() < 0) {
                    continue;
                }
                ChannelOfflineCategoryDTO channelOfflineCategoryDTO = buildChannelOfflineCategoryDTO(childCategory,
                        codeToCategoryMap.get(childCategory.getParentForestId()));
                codeToCategoryMap.put(childCategory.getForestId(), channelOfflineCategoryDTO);
                childCategoryList.add(channelOfflineCategoryDTO);
            }
        }

        return childCategoryList;
    }

    private static ChannelOfflineCategoryDTO buildChannelOfflineCategoryDTO(TxdOfflineCategory childCategory,
                                                                            ChannelOfflineCategoryDTO parentCategory) {
        ChannelOfflineCategoryDTO channelOfflineCategoryDTO = new ChannelOfflineCategoryDTO();
        channelOfflineCategoryDTO.setChannelCode(childCategory.getCode());
        channelOfflineCategoryDTO.setName(childCategory.getName());
        channelOfflineCategoryDTO.setParentChannelCode(Optional.ofNullable(parentCategory).map(ChannelOfflineCategoryDTO::getChannelCode).orElse(null));
        channelOfflineCategoryDTO.setIsLeaf(childCategory.isLeaf() ? LEFT : NOT_LEFT);
        channelOfflineCategoryDTO.setLevel(Optional.ofNullable(parentCategory).map(ChannelOfflineCategoryDTO::getLevel).orElse(0) + 1);
        return channelOfflineCategoryDTO;
    }

    public static AlibabaWdkStockPublishRequest buildStockRequest(List<TxdSkuInSpuStockDetail> txdSkuInSpuStockDetailList, String channelPoiId, String tenantAppId) {
        AlibabaWdkStockPublishRequest alibabaWdkStockPublishRequest = new AlibabaWdkStockPublishRequest();

        BatchStockPublishDto batchStockPublishDto = new BatchStockPublishDto();
        List<StockPublishDto> stockPublishDto = Lists.newArrayList();
        batchStockPublishDto.setBillNo(UUID.randomUUID().toString());
        batchStockPublishDto.setBillType(TxdConstant.defaultBillType);
        batchStockPublishDto.setOperator(TxdConstant.STOCK_OPERATOR);
        batchStockPublishDto.setPublishSource(tenantAppId);
        batchStockPublishDto.setShopCode(channelPoiId);
        batchStockPublishDto.setUpdateType(TxdConstant.defaultUpdateStockType);
        batchStockPublishDto.setUnBatchedOrderStockSubtracted(true);
        for (TxdSkuInSpuStockDetail data : txdSkuInSpuStockDetailList) {
            StockPublishDto dto = new StockPublishDto();
            dto.setSkuCode(data.getCustomSkuId());
            dto.setOrderNo(batchStockPublishDto.getBillNo()+"_"+data.getCustomSkuId());
            dto.setOrderType(TxdConstant.defaultStockOrderType);
            dto.setQuantity(String.valueOf(data.getStockQty()));
            stockPublishDto.add(dto);
        }
        batchStockPublishDto.setStockPublishDtos(stockPublishDto);
        alibabaWdkStockPublishRequest.setBatchStockPublishDto(batchStockPublishDto);
        return alibabaWdkStockPublishRequest;
    }

    public static ResultSpuData buildStockSpuResult(List<TxdSkuInSpuStockDetail> data, TaobaoResponse taobaoResponse) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (taobaoResponse == null) {
            throw new RuntimeException("数据为空");
        }

        if (!(taobaoResponse instanceof AlibabaWdkStockPublishResponse)) {
            throw new RuntimeException("返回结果类型错误");
        }

        AlibabaWdkStockPublishResponse response = (AlibabaWdkStockPublishResponse) taobaoResponse;
        data.forEach(sku -> {
            if (response.isSuccess()) {
                SpuKey spuKey = new SpuKey().setCustomSpuId(sku.getCustomSpuId()).setSkus(Lists.newArrayList(new SkuKey().setCustomSkuId(sku.getCustomSkuId())));
                resultData.getSucData().add(new ResultSuccessSpu().setStoreId(sku.getStoreId()).setSpuInfo(spuKey));
            } else {
                SpuKey spuKey = new SpuKey().setCustomSpuId(sku.getCustomSpuId()).setSkus(Lists.newArrayList(new SkuKey().setCustomSkuId(sku.getCustomSkuId())));
                resultData.getErrorData().add(new ResultErrorSpu().setStoreId(sku.getStoreId()).setSpuInfo(spuKey)
                        .setErrorCode(ResultCodeEnum.FAIL.getValue()).setErrorMsg(response.getMsg()));
            }
        });
        return resultData;
    }

    private static String getSkuSaleUnit(SkuInSpuInfoDTO skuDto) {
        // 售卖单位为空或为仅称重商品使用的单位,使用默认单位同步
        if (StringUtils.isBlank(skuDto.getUnit()) || TxdConstant.SUPPORT_WEIGHT_UNIT.contains(skuDto.getUnit())) {
            return TxdConstant.DEFAULT_UNIT;
        }
        return skuDto.getUnit();
    }
    private static String getSkuSaleSpec(SkuInSpuInfoDTO skuDto, String saleUnit) {
        return Optional.ofNullable(skuDto.getSpec())
                .filter(StringUtils::isNotEmpty)
                .orElseGet(() -> getSkuNetContent(skuDto, saleUnit));
    }

    private static long getSkuPurchaseQuantity(SkuInSpuInfoDTO skuDto) {
        return skuDto.isSetMinPurchaseQuantity()
                ? (long) skuDto.getMinPurchaseQuantity()
                : TxdConstant.DEFAULT_PURCHASE_QUANTITY;
    }

    private static String getSkuNetContent(SkuInSpuInfoDTO skuDto, String saleUnit) {
        return String.format("%s%s/%s", skuDto.getWeightForUnit(), skuDto.getWeightUnit(), saleUnit);
    }

}