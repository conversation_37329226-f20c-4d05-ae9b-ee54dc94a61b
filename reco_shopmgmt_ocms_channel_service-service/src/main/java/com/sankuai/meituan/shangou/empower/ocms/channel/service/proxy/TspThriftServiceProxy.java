package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.TimeUtil;
import com.sankuai.meituan.waimai.service.vp.exception.WmVpOpException;
import com.sankuai.meituan.waimai.service.vp.iface.WmVpQueryThriftService;
import com.sankuai.meituan.waimai.service.vp.vo.WmVpType;
import com.sankuai.meituan.waimai.service.vp.vo.param.QueryVpOrdersByPoiDaySeqParam;
import com.sankuai.meituan.waimai.service.vp.vo.result.WmVpOrderListVo;
import com.sankuai.meituan.waimai.service.vp.vo.result.WmVpOrderWithDetailsVo;
import com.sankuai.tsp.orderfulfillment.merchant.client.dto.req.poifulfill.PoiConfirmReqDTO;
import com.sankuai.tsp.orderfulfillment.merchant.client.dto.req.poifulfill.PoiSeqReqDTO;
import com.sankuai.tsp.orderfulfillment.merchant.client.dto.resp.poifulfill.PoiConfirmRespDTO;
import com.sankuai.tsp.orderfulfillment.merchant.client.dto.resp.poifulfill.PoiSeqRespDTO;
import com.sankuai.tsp.orderfulfillment.merchant.client.dto.resp.poifulfill.PoiSeqVo;
import com.sankuai.tsp.orderfulfillment.merchant.client.enums.FulfillmentSourceType;
import com.sankuai.tsp.orderfulfillment.merchant.client.enums.PoiFulfillResultCodeEnum;
import com.sankuai.tsp.orderfulfillment.merchant.client.service.PoiFulfillThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/12/23
 * @email liuyonggao@meituan
 */
@Slf4j
@Service
public class TspThriftServiceProxy {

    @Resource
    private PoiFulfillThriftService poiFulfillThriftService;
    @Resource
    private WmVpQueryThriftService.Iface wmVpQueryThriftService;

    @Resource
    private DrunkHorseOrderTransferService transferService;

    // 此处参数皆为外卖
    public boolean confirm2Tsp(Long orderViewId, ChannelPoiInfoDTO  channelPoiInfoDTO) {
        if (Objects.isNull(channelPoiInfoDTO)) {
            log.error("confirm2Tsp error:{}", orderViewId);
            return false;
        }
        PoiConfirmReqDTO poiConfirmReqDTO = new PoiConfirmReqDTO();
        poiConfirmReqDTO.setPoiId(Long.valueOf(channelPoiInfoDTO.getChannelPoiId()));
        poiConfirmReqDTO.setPoiName(channelPoiInfoDTO.getChannelPoiName());
        poiConfirmReqDTO.setOrderViewId(orderViewId);
        // 歪马业务码->190 WmVpType
        poiConfirmReqDTO.setBizCode(WmVpType.DRUNK_HORSE.getValue());
        poiConfirmReqDTO.setFulfillmentSourceType(FulfillmentSourceType.DRUNK_HORSE_PLATFORM);
        log.info("buyOrderConfirm poiConfirmReqDTO:{}", poiConfirmReqDTO);
        // 确认订单
        PoiConfirmRespDTO poiConfirmRespDTO = poiFulfillThriftService.confirm(poiConfirmReqDTO);
        log.info("buyOrderConfirm poiConfirmRespDTO:{}", poiConfirmRespDTO);
        if (Objects.nonNull(poiConfirmRespDTO)
                && Objects.equals(poiConfirmRespDTO.getResultCode(), PoiFulfillResultCodeEnum.SUCCESS)) {
            return true;
        }
        return false;
    }


    /**
     * 返回一个同http接口一样的data String，复用parse逻辑
     * @param orderViewId
     * @return
     * @throws WmVpOpException
     * @throws TException
     */
    public String getOrderDetail(Long orderViewId) throws WmVpOpException, TException {
        WmVpOrderListVo wmVpOrderListVo = null;
        try {
            List<Integer> types = new ArrayList<>();
            types.add(1);// 1：订单信息
            types.add(2);// 2：商品信息
            types.add(3);// 3：订单优惠信息
            types.add(4);// 4：订单流转信息
            types.add(10);// 10：订单支付信息

            log.info("SgOpenVpOrderBasicService getOrderDetail, bizCode:{}, orderViewId:{}, infoTypes:{}", orderViewId);
            wmVpOrderListVo = wmVpQueryThriftService.queryVpOrdersByViewIdsAndVpType(WmVpType.DRUNK_HORSE.getValue(), Collections.singletonList(orderViewId), types);
            log.info("SgOpenVpOrderBasicService getOrderDetail Success, order_view_id={}, wmVpOrderListVo={}", orderViewId, wmVpOrderListVo);
        } catch (Exception e) {
            log.error("SgOpenVpOrderBasicService getOrderDetail error, order_view_id={}, e={}", orderViewId, e);
            throw e;
        }
        if (wmVpOrderListVo == null || CollectionUtils.isEmpty(wmVpOrderListVo.getVp_order_list())) {
            log.info("SgOpenVpOrderBasicService getOrderDetail is null, order_view_id={}", orderViewId);
            return null;
        }

        WmVpOrderWithDetailsVo wmVpOrderWithDetailsVo = wmVpOrderListVo.getVp_order_list().get(0);
        if (wmVpOrderWithDetailsVo == null || wmVpOrderWithDetailsVo.getOrder() == null) {
            log.info("SgOpenVpOrderBasicService getOrderDetail is null, wmVpOrderListVo={}", wmVpOrderListVo);
            return null;
        }
        return transferService.getBuyOrderDetailLogistics(wmVpOrderWithDetailsVo);
    }


    // 此处参数皆为外卖
    public List<PoiSeqVo> batchGetLatestDaySeq(List<Long> wmPoiIds, String dateTime) {
        // 服务化请求入参
        PoiSeqReqDTO poiSeqReqDTO = new PoiSeqReqDTO();
        poiSeqReqDTO.setPoiIds(wmPoiIds);
        poiSeqReqDTO.setDate(dateTime);
        poiSeqReqDTO.setBizCode(WmVpType.DRUNK_HORSE.getValue());
        try {
            log.info("batchGetLatestDaySeq start, poiSeqReqDTO:{}", poiSeqReqDTO);
            PoiSeqRespDTO response = poiFulfillThriftService.getPoiSeqByPoiIds(poiSeqReqDTO);
            log.info("batchGetLatestDaySeq end, response:{}", response);

            if (Objects.nonNull(response) && Objects.nonNull(response.getPoiSeqVos())) {
                return response.getPoiSeqVos();
            }
        } catch (Exception e) {
            log.error("batchGetLatestDaySeq error", e);
        }
        return new ArrayList<>();
    }

    // 此处参数皆为外卖
    public Map<Integer, Long> batchGetOrderIdByDaySeq(Long wmPoiId, String dateTime, List<Integer> daySeqs) {
        try {
            QueryVpOrdersByPoiDaySeqParam param = new QueryVpOrdersByPoiDaySeqParam();
            param.setEndOrderTime(TimeUtil.transferDayEndTimestamp(dateTime));
            param.setStartOrderTime(TimeUtil.transferDayStartTimestamp(dateTime));
            param.setPoi_id(wmPoiId);
            param.setPoi_day_seq_list(daySeqs);
            param.setVp_type(WmVpType.DRUNK_HORSE.getValue());
            log.info("queryVpOrdersByPoiDaySeq start,request:{}", param);
            // 调用tsp服务化查询
            Map<Integer, Long> tspSetResult = wmVpQueryThriftService.queryVpOrdersByPoiDaySeq(param);
            log.info("queryVpOrdersByPoiDaySeq end,response:{}", tspSetResult);
            return tspSetResult;
        } catch (Exception e) {
            log.error("batchGetOrderIdByDaySeq error", e);
        }
        return new HashMap<>();
    }



}
