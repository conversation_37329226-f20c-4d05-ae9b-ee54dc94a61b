package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.RateLimit;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderAfterSaleRefundModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.*;
import com.meituan.shangou.saas.o2o.dto.response.BizAfterSaleCreateOrUpdateResponse;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderAfterSaleQueryResponse;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderCompensationUpdateResponse;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.MethodPerformance;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.MetricConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.BaseRhinoException;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelOrderThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import org.apache.thrift.TException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: 中台订单管理服务代理
 * @author: zhaolei12
 * @create: 2019/1/24 下午3:43
 */
@Rhino
@Service
public class ChannelOrderThriftServiceProxy {

    @Resource
    private ChannelOrderThriftService.Iface channelOrderThriftService;

    @Resource
    private CommonLogger log;

    @Resource
    private BizOrderThriftService bizOrderThriftService;

    /**
     * 推送订单信息变更通知
     */
    @Degrade(rhinoKey = "orderInfoChangeNotify", fallBackMethod = "orderInfoChangeNotifyFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "orderInfoChangeNotify", fallBackMethod = "orderInfoChangeNotifyFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public OrderResponse orderInfoChangeNotify(OrderInfoChangeRequest request) {
        try {
            OrderResponse orderResponse = channelOrderThriftService.orderInfoChangeNotify(request);
            log.info("ChannelOrderThriftServiceProxy.orderInfoChangeNotify, 调用订单服务推送订单信息变更返回结果, request:{}, orderResponse:{}",
                    request, orderResponse);
            if (Objects.isNull(orderResponse)) {
                log.error("ChannelOrderThriftServiceProxy.orderInfoChangeNotify, 中台订单模块订单信息变更通知服务异常, request:{}", request);
            }
            return orderResponse;
        } catch (TException e) {
            log.error("ChannelOrderThriftServiceProxy.orderInfoChangeNotify, 中台订单模块订单信息变更通知服务异常, request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    @SuppressWarnings("unused")
    public OrderResponse orderInfoChangeNotifyFallback(OrderInfoChangeRequest request) {
        log.warn("ChannelOrderThriftServiceProxy.orderInfoChangeNotifyFallback, 中台订单模块订单信息变更通知服务, request:{}", request);
        return null;
    }

    /**
     * 全部退款申请通知推送
     */
    @Degrade(rhinoKey = "orderAllRefundNotify", fallBackMethod = "orderAllRefundNotifyFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "orderAllRefundNotify", fallBackMethod = "orderAllRefundNotifyFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public OrderResponse orderAllRefundNotify(OrderAllRefundRequest request) {
        try {
            OrderResponse orderResponse = channelOrderThriftService.orderAllRefundNotify(request);
            log.info("ChannelOrderThriftServiceProxy.orderAllRefundNotify, 调用订单服务推送全部退款消息返回结果, request:{}, orderResponse:{}",
                    request, orderResponse);
            if (Objects.isNull(orderResponse)) {
                log.error("ChannelOrderThriftServiceProxy.orderAllRefundNotify, 中台订单模块全部退款消息推送服务异常, request:{}", request);
            }
            return orderResponse;
        } catch (TException e) {
            log.error("ChannelOrderThriftServiceProxy.orderAllRefundNotify, 中台订单模块全部退款消息推送服务异常, request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    @SuppressWarnings("unused")
    public OrderResponse orderAllRefundNotifyFallback(OrderAllRefundRequest request) {
        log.warn("ChannelOrderThriftServiceProxy.orderAllRefundNotify, 中台订单模块全部退款申请推送服务降级, request:{}", request);
        return null;
    }

    /**
     * 部分退款申请通知推送
     */
    @Degrade(rhinoKey = "orderPartRefundNotify", fallBackMethod = "orderPartRefundNotifyFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "orderPartRefundNotify", fallBackMethod = "orderPartRefundNotifyFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public OrderResponse orderPartRefundNotify(OrderPartRefundRequest request) {
        try {
            OrderResponse orderResponse = channelOrderThriftService.orderPartRefundNotify(request);
            log.info("ChannelOrderThriftServiceProxy.orderPartRefundNotify, 调用订单服务推送部分退款消息返回结果, request:{}, orderResponse:{}",
                    request, orderResponse);
            if (Objects.isNull(orderResponse)) {
                log.error("ChannelOrderThriftServiceProxy.orderPartRefundNotify, 中台订单模块部分退款消息推送服务异常, request:{}", request);
            }
            return orderResponse;
        } catch (TException e) {
            log.error("ChannelOrderThriftServiceProxy.orderPartRefundNotify, 中台订单模块部分退款消息推送服务异常, request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    @SuppressWarnings("unused")
    public OrderResponse orderPartRefundNotifyFallback(OrderPartRefundRequest request) {
        log.warn("ChannelOrderThriftServiceProxy.orderPartRefundNotifyFallback, 中台订单模块部分退款消息推送服务降级, request:{}", request);
        return null;
    }

    @Degrade(rhinoKey = "orderAllRefundGoodsNotify", fallBackMethod = "orderAllRefundGoodsNotifyFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "orderAllRefundGoodsNotify", fallBackMethod = "orderAllRefundGoodsNotifyFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public OrderResponse orderAllRefundGoodsNotify(OrderAllRefundGoodsRequest request) {
        try {
            OrderResponse orderResponse = channelOrderThriftService.orderAllRefundGoodsNotify(request);
            log.info("ChannelOrderThriftServiceProxy.orderAllRefundGoodsNotify, 调用订单服务推送全部退款消息返回结果, request:{}, orderResponse:{}",
                    request, orderResponse);
            if (Objects.isNull(orderResponse)) {
                log.error("ChannelOrderThriftServiceProxy.orderAllRefundGoodsNotify, 中台订单退货退款-模块全部退款消息推送服务异常, request:{}", request);
            }
            return orderResponse;
        } catch (TException e) {
            log.error("ChannelOrderThriftServiceProxy.orderAllRefundGoodsNotify, 中台订单模块退货退款-全部退款消息推送服务异常, request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    @SuppressWarnings("unused")
    public OrderResponse orderAllRefundGoodsNotifyFallback(OrderAllRefundGoodsRequest request) {
        log.warn("ChannelOrderThriftServiceProxy.orderAllRefundGoodsNotify, 中台订单模块退货退款-全部退款申请推送服务降级, request:{}", request);
        return null;
    }

    /**
     * 退货退款-部分退款申请通知推送
     */
    @Degrade(rhinoKey = "orderPartRefundGoodsNotify", fallBackMethod = "orderPartRefundGoodsNotifyFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "orderPartRefundGoodsNotify", fallBackMethod = "orderPartRefundGoodsNotifyFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public OrderResponse orderPartRefundGoodsNotify(OrderPartRefundGoodsRequest request) {
        try {
            OrderResponse orderResponse = channelOrderThriftService.orderPartRefundGoodsNotify(request);
            log.info("ChannelOrderThriftServiceProxy.orderPartRefundNotify, 调用订单服务推送部分退款消息返回结果, request:{}, orderResponse:{}",
                    request, orderResponse);
            if (Objects.isNull(orderResponse)) {
                log.error("ChannelOrderThriftServiceProxy.orderPartRefundNotify, 中台订单模块部分退款消息推送服务异常, request:{}", request);
            }
            return orderResponse;
        } catch (TException e) {
            log.error("ChannelOrderThriftServiceProxy.orderPartRefundNotify, 中台订单模块部分退款消息推送服务异常, request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    @SuppressWarnings("unused")
    public OrderResponse orderPartRefundGoodsNotifyFallback(OrderPartRefundGoodsRequest request) {
        log.warn("ChannelOrderThriftServiceProxy.orderPartRefundGoodsNotifyFallback, 中台订单模块部分退款消息推送服务降级, request:{}", request);
        return null;
    }

    /**
     * 订单状态变化通知推送
     */
    @Degrade(rhinoKey = "orderStatusChangeNotify", fallBackMethod = "orderStatusChangeNotifyFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "orderStatusChangeNotify", fallBackMethod = "orderStatusChangeNotifyFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public OrderStatusNotifyResponse orderStatusChangeNotify(OrderStatusChangeRequest request) {
        try {
            OrderStatusNotifyResponse orderResponse = channelOrderThriftService.orderStatusChangeNotify(request);
            log.info("ChannelOrderThriftServiceProxy.orderStatusChangeNotify, 调用订单服务推送订单状态返回结果 request:{}, orderResponse:{}",
                    request, orderResponse);
            if (Objects.isNull(orderResponse)) {
                log.error("ChannelOrderThriftServiceProxy.orderStatusChangeNotify, 中台订单模块订单状态变化通知服务异常, request:{}", request);
            }
            return orderResponse;
        } catch (TException e) {
            log.error("ChannelOrderThriftServiceProxy.orderStatusChangeNotify, 中台订单模块订单状态变化通知服务异常, request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    @SuppressWarnings("unused")
    public OrderResponse orderStatusChangeNotifyFallback(OrderStatusChangeRequest request) {
        log.warn("ChannelOrderThriftServiceProxy.orderStatusChangeNotifyFallback, 中台订单模块订单状态变化通知服务降级, request:{}", request);
        return null;
    }

    /**
     * 配送状态变化通知接口
     */
    @Degrade(rhinoKey = "deliveryStatusChangeNotify", fallBackMethod = "deliveryStatusChangeNotifyFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "deliveryStatusChangeNotify", fallBackMethod = "deliveryStatusChangeNotifyFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    @Retryable(value = BizException.class, maxAttempts = 3, backoff = @Backoff(delay = 300))
    public OrderResponse deliveryStatusChangeNotify(DeliveryStatusChangeRequest request) {
        try {
            OrderResponse orderResponse = channelOrderThriftService.deliveryStatusChangeNotify(request);
            log.info("ChannelOrderThriftServiceProxy.deliveryStatusChangeNotify, 调用订单服务推送配送状态返回结果, request:{}, orderResponse:{}, cost:{}ms",
                    request, orderResponse);
            if (Objects.isNull(orderResponse)) {
                log.error("ChannelOrderThriftServiceProxy.deliveryStatusChangeNotify, 中台订单模块配送状态变化通知服务异常, request:{}", request);
            }
            // 租户在配置内 && 京东渠道 && 配送状态 == WAIT_DISPATCH_RIDER && orderResponse == 200，则需要重试 --> 场景：生单过程中，配送信息更新失败
            if(MccConfigUtil.checkJdDeliveryVerifyOperationTimeTenant(request.getTenantId())
                    && ChannelTypeEnum.isJD2Home(request.getChannelType())
                    && Objects.equals(request.getDeliveryStatus(), DeliveryStatus.WAIT_DISPATCH_RIDER.getValue())
                    && Objects.nonNull(orderResponse) && Objects.equals(orderResponse.getCode(), 200)) {
                log.warn("京东订单配送状态更新失败，重试中，request:{}", JSON.toJSONString(request));
                throw new BizException(200, "京东订单配送状态更新失败，重试");
            }
            return orderResponse;
        } catch (TException e) {
            log.error("ChannelOrderThriftServiceProxy.deliveryStatusChangeNotify, 中台订单模块配送状态变化通知服务异常, request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    @SuppressWarnings("unused")
    public OrderResponse deliveryStatusChangeNotifyFallback(DeliveryStatusChangeRequest request) {
        log.warn("ChannelOrderThriftServiceProxy.deliveryStatusChangeNotifyFallback, 中台订单模块配送状态变化通知服务降级, request:{}", request);
        return null;
    }

    /**
     * 推送订单售后信息
     */
    @Degrade(rhinoKey = "orderAfterSaleNotify", fallBackMethod = "orderAfterSaleNotifyFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "orderAfterSaleNotify", fallBackMethod = "orderAfterSaleNotifyFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public BizAfterSaleCreateOrUpdateResponse orderAfterSaleNotify(BizAfterSaleCreateOrUpdateRequest request) {
        try {
            BizAfterSaleCreateOrUpdateResponse response = bizOrderThriftService.createOrUpdateAfterSale(request);
            log.info("ChannelOrderThriftServiceProxy.orderAfterSaleNotify, 调用订单服务推送售后信息消息返回结果, request:{}, response:{}",
                    request, response);
            if (Objects.isNull(response)) {
                log.error("ChannelOrderThriftServiceProxy.orderAfterSaleNotify, 调用订单服务推送售后信息异常, request:{}", request);
            }
            return response;
        } catch (TException e) {
            log.error("ChannelOrderThriftServiceProxy.orderAfterSaleNotify, 调用订单服务推送售后信息异常, request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    @SuppressWarnings("unused")
    public OrderResponse orderAfterSaleNotifyFallback(BizAfterSaleCreateOrUpdateRequest request) {
        log.warn("ChannelOrderThriftServiceProxy.orderAfterSaleNotifyFallback, 调用订单服务推送售后信息服务降级, request:{}", request);
        return null;
    }

    @Degrade(rhinoKey = "ChannelOrderThriftServiceProxy-queryOrderInfo", fallBackMethod = "queryOrderInfoFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "queryOrderInfo", fallBackMethod = "queryOrderInfo")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public Optional<BizOrderModel> queryOrderInfo(BizOrderQueryRequest request) {
        try {
            BizOrderQueryResponse response = bizOrderThriftService.query(request);
//            log.info("invoke queryOrderInfo, request = {} ,response = {}", request, response);
            if (Objects.isNull(response) || !response.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())) {
                return Optional.empty();
            }
            return Optional.of(response.getBizOrderModel());
        } catch (Exception e) {
            log.error("ChannelOrderThriftServiceProxy.orderAfterSaleNotify, 调用订单服务推送售后信息异常, request:{}", request, e);
            throw new BizException("查询订单失败");
        }
    }


    private Optional<BizOrderModel> queryOrderInfoFallback(BizOrderQueryRequest request) {
        log.warn("ChannelOrderThriftServiceProxy.queryOrderInfoFallback, 中台订单查询模块降级, request:{}", request);
        return Optional.empty();
    }

    @Degrade(rhinoKey = "ChannelOrderThriftServiceProxy-query4OrderId", fallBackMethod = "query4OrderIdFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "query4OrderId", fallBackMethod = "query4OrderIdFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public Optional<BizOrderModel> query4OrderId(long orderId) {
        try {
            BizOrderQueryResponse response = bizOrderThriftService.query4OrderId(orderId);
            log.info("invoke query4OrderId, orderId = {} ,response = {}", orderId, response);
            if (Objects.isNull(response) || !response.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())) {
                return Optional.empty();
            }
            return Optional.of(response.getBizOrderModel());
        } catch (Exception e) {
            log.error("ChannelOrderThriftServiceProxy.query4OrderId, 调用订单服务查询订单信息异常, orderId:{}", orderId, e);
            return Optional.empty();
        }
    }


    private Optional<BizOrderModel> query4OrderIdFallback(long orderId, Throwable t) {
        log.warn("ChannelOrderThriftServiceProxy.query4OrderIdFallback, 中台订单查询模块降级, request:{}", orderId, t);
        return Optional.empty();
    }


    @Degrade(rhinoKey = "ChannelOrderThriftServiceProxy-queryOrderAfterSale", fallBackMethod = "queryOrderAfterSaleFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "queryOrderAfterSale", fallBackMethod = "queryOrderAfterSaleFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public Optional<List<BizOrderAfterSaleRefundModel>> queryOrderAfterSale(BizOrderAfterSaleQueryRequest bizOrderAfterSaleQueryRequest) {
        try {
            BizOrderAfterSaleQueryResponse response = bizOrderThriftService.queryOrderAfterSale(bizOrderAfterSaleQueryRequest);
            log.info("invoke queryOrderAfterSale, request = {} ,response = {}", bizOrderAfterSaleQueryRequest, response);
            if (Objects.isNull(response) || !response.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())) {
                return Optional.empty();
            }
            return Optional.of(response.getBizOrderAfterSaleRefundModelList());
        } catch (Exception e) {
            log.error("ChannelOrderThriftServiceProxy.queryOrderAfterSale, 调用查询订单售后信息异常, request:{}", bizOrderAfterSaleQueryRequest, e);
            return Optional.empty();
        }
    }

    private Optional<BizOrderModel> queryOrderAfterSaleFallback(BizOrderAfterSaleQueryRequest bizOrderAfterSaleQueryRequest, Throwable t) {
        log.warn("ChannelOrderThriftServiceProxy.queryOrderAfterSaleFallback, 中台订单售后查询模块降级, request:{}", bizOrderAfterSaleQueryRequest, t);
        return Optional.empty();
    }

    /**
     * 更新订单赔付信息
     */
    @Degrade(rhinoKey = "updateOrderCompensation", fallBackMethod = "updateOrderCompensationFallback", isDegradeOnException = true,
            timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
            errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "updateOrderCompensation", fallBackMethod = "updateOrderCompensationFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public BizOrderCompensationUpdateResponse updateOrderCompensation(BizOrderCompensationUpdateV2Request request) {
        try {
            BizOrderCompensationUpdateResponse response = bizOrderThriftService.updateOrderCompensationV2(request);
            log.info("ChannelOrderThriftServiceProxy.updateOrderCompensation, 调用订单服务更新订单赔付信息返回结果, request:{}, response:{}",
                    request, response);
            if (Objects.isNull(response)) {
                log.error("ChannelOrderThriftServiceProxy.updateOrderCompensation,调用订单服务更新订单赔付信息异常, request:{}", request);
            }
            return response;
        } catch (TException e) {
            log.error("ChannelOrderThriftServiceProxy.updateOrderCompensation,调用订单服务更新订单赔付信息异常, request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    @SuppressWarnings("unused")
    public BizOrderCompensationUpdateResponse updateOrderCompensationFallback(BizOrderCompensationUpdateV2Request request) {
        log.warn("ChannelOrderThriftServiceProxy.updateOrderCompensationFallback, 调用订单服务更新订单赔付信息服务降级, request:{}", request);
        return null;
    }

}
