package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;

public interface SquirrelService {

    /**
     * 价格更新按照时间戳进行分布式锁过滤
     *
     * @param skuId
     * @param ts
     * @return true：可以更新 false：不需要更新
     */
    boolean priceCheckLock(String skuId, Long ts);

    /**
     * 库存更新按照时间戳进行分布式锁过滤
     *
     * @param skuId
     * @param ts
     * @return true：可以更新 false：不需要更新
     */
    boolean stockCheckLock(String skuId, Long ts);

    ResultData getDefaultPictureCache(long tenantId, int channelId);

    void setDefaultPictureCache(long tenantId, int channelId, ResultData resultData);

}
