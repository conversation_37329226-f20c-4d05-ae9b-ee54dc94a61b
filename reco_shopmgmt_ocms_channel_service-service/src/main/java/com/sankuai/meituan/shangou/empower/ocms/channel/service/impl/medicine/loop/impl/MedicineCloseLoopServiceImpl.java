package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.loop.impl;

import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.common.MedicineTenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.loop.MedicineCloseLoopService;
import groovy.util.logging.Slf4j;
import javafx.util.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MedicineCloseLoopServiceImpl implements MedicineCloseLoopService {

    @Autowired
    private MedicineTenantService medicineTenantService;

    @Override
    public boolean isUwmsCloseLoop(Long tenantId, Long storeId, MedicineCloseLoopTypeEnum typeEnum) {
        Map<Long, Set<Long>> tenantStoreIdMap = new HashMap<>();
        Set<Long> storeIds = new HashSet<>();
        storeIds.add(storeId);
        tenantStoreIdMap.put(tenantId, storeIds);

        Map<Long, Set<Long>> uwmsCloseLoopBatch = this.isUwmsCloseLoopBatch(tenantStoreIdMap, typeEnum);

        // 为空代表全部放行
        if (CollectionUtils.isEmpty(uwmsCloseLoopBatch)) {
            return false;
        }

        // 名单内的不放行
        return uwmsCloseLoopBatch.get(tenantId) != null && uwmsCloseLoopBatch.get(tenantId).contains(storeId);
    }

    @Override
    public Map<Long, Set<Long>> isUwmsCloseLoopBatch(Map<Long, Set<Long>> tenantStoreIdMap, MedicineCloseLoopTypeEnum typeEnum) {
        // 全放行
        if (CollectionUtils.isEmpty(tenantStoreIdMap) || typeEnum == null) {
            return Collections.emptyMap();
        }

        // 只判断医药租户
        List<Long> uwmsTenantIds = medicineTenantService.getAllUwmsTenantIds();
        Map<Long, Set<Long>> uwmsTenantStoreIdMap = new HashMap<>();
        Map<Long, Set<Long>> resultMap = new HashMap<>();

        for (Map.Entry<Long, Set<Long>> tenantStoreIdEntry : tenantStoreIdMap.entrySet()) {
            if (uwmsTenantIds.contains(tenantStoreIdEntry.getKey())) {
                uwmsTenantStoreIdMap.put(tenantStoreIdEntry.getKey(), tenantStoreIdEntry.getValue());
            }
        }

        if (isStaging()) {
            // st环境特殊处理，白名单机制，名单内门店可以上行，其余全部屏蔽上行
            Set<Long> uwmsTestPoiIds = getUwmsTestPoiIds();

            uwmsTenantStoreIdMap.forEach((k, v) -> {
                // 移除能够上行的门店id
                v.removeAll(uwmsTestPoiIds);

                if (!CollectionUtils.isEmpty(v)) {
                    resultMap.put(k, v);
                }
            });

            return resultMap;
        }

        // 其余环境全部黑名单机制，名单内不可上行，其余全部放行
        Set<Long> blockPoiIds = getBlockPoiIdsByType(typeEnum);

        uwmsTenantStoreIdMap.forEach((k, v) -> {
            // 取交集，判断哪些是被屏蔽上行的
            v.retainAll(blockPoiIds);

            if (!CollectionUtils.isEmpty(v)) {
                resultMap.put(k, v);
            }
        });

        return resultMap;
    }

    @Override
    public <T> Pair<Map<Long, List<T>>, Map<Long, List<T>>> filterBlockedItems(Map<Long, List<T>> sourceMap, MedicineCloseLoopTypeEnum typeEnum, Function<T, Long> storeIdFunc) {
        Map<Long, List<T>> filteredResultMap = new HashMap<>(); // 过滤完成之后的数据
        Map<Long, List<T>> filteredMap = new HashMap<>(); // 被过滤的数据
        Map<Long, Set<Long>> tenantStoreIdMap = new HashMap<>();

        for (Map.Entry<Long, List<T>> entry : sourceMap.entrySet()) {
            Long tenantId = entry.getKey();
            List<T> tenantSourceList = entry.getValue();

            tenantStoreIdMap.put(tenantId, tenantSourceList.stream().map(storeIdFunc).collect(Collectors.toSet()));
        }

        Map<Long, Set<Long>> blockedStoreIdMap = this.isUwmsCloseLoopBatch(tenantStoreIdMap, typeEnum);

        sourceMap.forEach((tenantId, sourceList) -> {
            if (!blockedStoreIdMap.containsKey(tenantId)) {
                filteredResultMap.put(tenantId, sourceList);
                return;
            }

            // 保留不在block门店范围内的数据
            List<T> copyDataList = new ArrayList<>(sourceList);
            List<T> filterResultList = sourceList.stream().filter(item -> !blockedStoreIdMap.get(tenantId).contains(storeIdFunc.apply(item))).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(filterResultList)) {
                // 部分或者全部没有被过滤
                filteredResultMap.put(tenantId, filterResultList);

                // 部分被过滤，记录被过滤掉的数据
                if (filterResultList.size() != sourceList.size()) {
                    copyDataList.removeAll(filterResultList);
                    filteredMap.put(tenantId, copyDataList);
                }
            } else {
                // 全部被过滤
                filteredMap.put(tenantId, sourceList);
            }
        });

        return new Pair<>(filteredResultMap, filteredMap);
    }

    private Set<Long> getBlockPoiIdsByType(MedicineCloseLoopTypeEnum typeEnum) {
        Set<Long> blockPoiIds = new HashSet<>();

        switch (typeEnum) {
            case ORDER_PICK_COMPLETE:
                // 在ocms中实现
                break;
            case STOCK:
                blockPoiIds = getStockCloseLoopPoiIds();
                break;
            case PRODUCT:
                blockPoiIds = getProductClosePushChannelPoiIds();
                break;
            default:
                break;
        }

        return blockPoiIds;
    }

    private boolean isStaging() {
        return Objects.equals(HostEnv.STAGING, ProcessInfoUtil.getHostEnv());
    }

    /**
     * 库存关闭渠道上行逻辑门店列表
     */
    private Set<Long> getStockCloseLoopPoiIds() {
        ConfigRepository configRepository = Lion.getConfigRepository("com.sankuai.shangouwarehouse.apptier.gaea");
        return new HashSet<>(configRepository.getList("close.stock.push.channel.poi.list", Long.class));
    }

    /**
     * 获取医药在st环境的测试门店
     */
    private Set<Long> getUwmsTestPoiIds() {
        ConfigRepository configRepository = Lion.getConfigRepository("com.sankuai.shangouwarehouse.apptier.gaea");
        return new HashSet<>(configRepository.getList("health.uwms.stage.poi.list", Long.class));
    }

    /**
     * 商品关闭渠道上行逻辑门店列表
     */
    private Set<Long> getProductClosePushChannelPoiIds() {
        ConfigRepository configRepository = Lion.getConfigRepository("com.sankuai.shangouwarehouse.apptier.gaea");
        return new HashSet<>(configRepository.getList("close.product.push.channel.poi.list", Long.class));
    }
}
