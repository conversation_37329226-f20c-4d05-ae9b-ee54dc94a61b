package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.google.common.collect.Maps;
import com.meituan.shangou.saas.common.enums.ChannelExtRefundTypeEnum;
import com.meituan.shangou.saas.order.query.constants.Constants;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JDFeeDetailEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SkuChangeTypeEunm;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpecTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GoodsSharedActivityItem;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/***
 * author : <EMAIL> 
 * data : 2019/6/13 
 * time : 下午7:21
 **/
@Slf4j
public class JddjConvertUtil {

    public static int calcTotalDiscount(JDGoodsSettlementInfo jdGoodsSettlementInfo) {
        if (CollectionUtils.isNotEmpty(jdGoodsSettlementInfo.getDiscountlist())) {
            return jdGoodsSettlementInfo.getDiscountlist().stream()
                    .mapToInt(e -> e.getSkuDiscountMoney() != null ? e.getSkuDiscountMoney().intValue() : 0)
                    .sum()
                    + jdGoodsSettlementInfo.getCostMoney().intValue() + jdGoodsSettlementInfo.getSaleMoney().intValue();//订单级别的优惠金额 + 商品纬度优惠金额
        }
        return 0;
    }

    public static int extractAfsPlatLogisticsPromotion(JddjAfterSaleDetail afterSaleDetail){
        return extractAfsLogisticsPromotion(afterSaleDetail, JddjAfsSkuDiscount::getPlatPayMoney);
    }

    public static int extractAfsPoiLogisticsPromotion(JddjAfterSaleDetail afterSaleDetail){
        return extractAfsLogisticsPromotion(afterSaleDetail, JddjAfsSkuDiscount::getVenderPayMoney);
    }

    public static int extractAfsLogisticsPromotion(JddjAfterSaleDetail afterSaleDetail, Function<JddjAfsSkuDiscount, Long> function){
        int result = 0;
        if (CollectionUtils.isEmpty(afterSaleDetail.getAfsDetailList())){
            return 0;
        }
        for (JddjAfsServiceDetail jddjAfsServiceDetail : afterSaleDetail.getAfsDetailList()){
            if (CollectionUtils.isEmpty(jddjAfsServiceDetail.getAfsSkuDiscountList())){
                continue;
            }
            Long reduce = jddjAfsServiceDetail.getAfsSkuDiscountList().stream().filter(v -> isAfsLogisticsDiscountType(v.getDiscountType()))
                    .map(function)
                    .filter(Objects::nonNull)
                    .reduce(0L, Long::sum);
            result += reduce.intValue();
        }
        return result;
    }


    public static int extractAfsPlatOrderPromotion(List<JddjAfsSkuDiscount> afsSkuDiscountList){
        return extractAfsOrderPromotion(afsSkuDiscountList, JddjAfsSkuDiscount::getPlatPayMoney);
    }

    public static double extractPartialRefundCount(JddjAfsServiceDetail jddjAfsServiceDetail){
        if (Objects.isNull(jddjAfsServiceDetail.getPartialRefundRatioShow()) || jddjAfsServiceDetail.getPartialRefundRatioShow() <= 0 || jddjAfsServiceDetail.getPartialRefundRatioShow() >= 1L){
            return 0L;
        }
        return BigDecimal.valueOf(jddjAfsServiceDetail.getSkuCount()).multiply(BigDecimal.valueOf(jddjAfsServiceDetail.getPartialRefundRatioShow())).setScale(3, RoundingMode.HALF_UP).doubleValue();
    }

    public static int extractAfsPoiOrderPromotion(List<JddjAfsSkuDiscount> afsSkuDiscountList){
        return extractAfsOrderPromotion(afsSkuDiscountList, JddjAfsSkuDiscount::getVenderPayMoney);
    }

    public static int extractAfsOrderPromotion(List<JddjAfsSkuDiscount> afsSkuDiscountList, Function<JddjAfsSkuDiscount, Long> function){
        if (CollectionUtils.isEmpty(afsSkuDiscountList)){
            return 0;
        }
        Long reduce = afsSkuDiscountList.stream().filter(v -> !isAfsLogisticsDiscountType(v.getDiscountType()))
                .map(function)
                .filter(Objects::nonNull)
                .reduce(0L, Long::sum);
        return reduce.intValue();
    }

    public static boolean isAfsLogisticsDiscountType(Integer discountType){
        return Arrays.asList(7,8,11).contains(discountType);
    }

    public static int calcTotalChannelCost(JDGoodsSettlementInfo jdGoodsSettlementInfo) {
        if (CollectionUtils.isNotEmpty(jdGoodsSettlementInfo.getDiscountlist())) {
            return jdGoodsSettlementInfo.getDiscountlist().stream()
                    .mapToInt(e -> e.getCostMoney() != null ? e.getCostMoney().intValue() : 0)
                    .sum();
        }
        return 0;
    }

    public static int calcTotalTenantCost(JDGoodsSettlementInfo jdGoodsSettlementInfo) {
        if (CollectionUtils.isNotEmpty(jdGoodsSettlementInfo.getDiscountlist())) {
            return jdGoodsSettlementInfo.getDiscountlist().stream()
                    .mapToInt(e -> e.getSaleMoney() != null ? e.getSaleMoney().intValue() : 0)
                    .sum();
        }
        return 0;
    }

    public static List<GoodsSharedActivityItem> convertGoodsSharedActivityItems(List<JDGoodsActivityDetail> discountlist, JDGoodsSettlementInfo jdGoodsSettlementInfo) {

        if (CollectionUtils.isNotEmpty(discountlist)) {

            //订单纬度活动
            List<GoodsSharedActivityItem> goodsSharedActivityItems = discountlist.stream().map(e -> {
                GoodsSharedActivityItem item = new GoodsSharedActivityItem();
                item.setActivityId(Optional.ofNullable(e.getOutActivityId()).orElse(""));
                item.setChannelPromotionType(String.valueOf(10000 * e.getPromotionType() + Optional.ofNullable(e.getPromotionDetailType()).orElse(0)));
                item.setChannelCost(e.getCostMoney());
                item.setTenantCost(e.getSaleMoney());
                item.setPromotionCount(jdGoodsSettlementInfo.getSkuCount());// 取售卖数量
                return item;
            }).collect(Collectors.toList());

            if (StringUtils.isNotBlank(jdGoodsSettlementInfo.getOutActivityId())) {
                GoodsSharedActivityItem goodsSharedActivityItem = new GoodsSharedActivityItem();//商品纬度
                goodsSharedActivityItem.setActivityId(jdGoodsSettlementInfo.getOutActivityId());
                goodsSharedActivityItem.setChannelPromotionType(String.valueOf(jdGoodsSettlementInfo.getPromotionType()));
                goodsSharedActivityItem.setChannelCost(jdGoodsSettlementInfo.getCostMoney());
                goodsSharedActivityItem.setTenantCost(jdGoodsSettlementInfo.getSaleMoney());
                goodsSharedActivityItem.setPromotionCount(jdGoodsSettlementInfo.getSkuCount());// 取售卖数量
                if (goodsSharedActivityItems == null)
                    goodsSharedActivityItems = Lists.newArrayList();
                goodsSharedActivityItems.add(goodsSharedActivityItem);//商品纬度活动
            }
            return goodsSharedActivityItems;
        }
        return Lists.newArrayList();
    }


    public static List<com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.SettlementFeeDetail> convertSettlementFeeDetailList(JddjSettleOrderDetailContent.JddjSettleDetailDaoJiaDailyDTO jddjSettleDetailDaoJiaDailyDTO) {
        List<com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.SettlementFeeDetail> settlementFeeDetailList = Lists.newArrayList();
        for (JDFeeDetailEnum value : JDFeeDetailEnum.values()) {

            String fieldValue = ConverterUtils.getFieldValue(jddjSettleDetailDaoJiaDailyDTO, value.getCode());
            if (fieldValue != null) {
                double feeValue = Double.parseDouble(fieldValue);
                com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.SettlementFeeDetail settlementFeeDetail = new com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.SettlementFeeDetail();
                settlementFeeDetail.setFeeValue(BigDecimal.valueOf(feeValue).multiply(BigDecimal.valueOf(value.getConvertPoint())).longValue());
                settlementFeeDetail.setFeeKey(value.getCode());
                settlementFeeDetail.setFeeDesc(value.getDescription());
                settlementFeeDetailList.add(settlementFeeDetail);
            }

        }
        return settlementFeeDetailList;
    }

    public static long convertSettlementFinishDate(Long settlementTime, Long settlementFinishDate) {
        if (Objects.nonNull(settlementFinishDate) && settlementFinishDate > 0) {
            return settlementFinishDate;
        }
        return settlementTime;
    }

    public static String convertToOrderId(JddjSettleOrderDetailContent.JddjSettleDetailDaoJiaDailyDTO jddjSettleDetailDaoJiaDailyDTO) {
        Long orderId = jddjSettleDetailDaoJiaDailyDTO.getOrderId();
        Integer orderTypeCode = jddjSettleDetailDaoJiaDailyDTO.getOrderTypeCode();
        Long relationId = jddjSettleDetailDaoJiaDailyDTO.getRelationId();

        // 	异常调整=1;正向订单 = 1001;
        // 	售后退货 = 1002;售后退款 = 1003;
        // 	售后退货申述 = 1004;售后退款申述 = 1005;售后换新申述 = 1006;
        // 	售后换新 = 1016;售后直赔 = 1017;

        // orderId:正向订单为订单号；售后单为售后单号
        // relationId:关联订单号 非正向单时才存在 如果此单为售后单，则关联单号为正向单。如果是售后申述单，则关联单号为售后单

        if (orderTypeCode == 1 || orderTypeCode == 1001) {
            return String.valueOf(orderId);

        } else if (orderTypeCode == 1002 || orderTypeCode == 1003 || orderTypeCode == 1016 || orderTypeCode == 1017) {
            return String.valueOf(relationId);
        } else if (orderTypeCode == 1004 || orderTypeCode == 1005 || orderTypeCode == 1006) {
            // 后续通过售后单号查订单号
            return String.valueOf(relationId);
        }

        return "未知京东结算单订单类型";
    }


    public static String convertToRefundId(JddjSettleOrderDetailContent.JddjSettleDetailDaoJiaDailyDTO jddjSettleDetailDaoJiaDailyDTO) {
        Long orderId = jddjSettleDetailDaoJiaDailyDTO.getOrderId();
        Integer orderTypeCode = jddjSettleDetailDaoJiaDailyDTO.getOrderTypeCode();
        // 	异常调整=1;正向订单 = 1001;
        // 	售后退货 = 1002;售后退款 = 1003;
        // 	售后退货申述 = 1004;售后退款申述 = 1005;售后换新申述 = 1006;
        // 	售后换新 = 1016;售后直赔 = 1017;
        // orderId:正向订单为订单号；售后单为售后单号
        // relationId:关联订单号 非正向单时才存在 如果此单为售后单，则关联单号为正向单。如果是售后申述单，则关联单号为售后单

        if (orderTypeCode == 1 || orderTypeCode == 1001) {
            return StringUtils.EMPTY;
        } else {
            return String.valueOf(orderId);
        }
    }


    public static Integer convertChannelSettleStatus(Integer settlementStatus) {
        Integer channelSettleStatus = null;
        if (Objects.nonNull(settlementStatus) && settlementStatus > 0) {
            channelSettleStatus = settlementStatus;
        }

        return channelSettleStatus;
    }

    /**
     * 结算状态代码 待打款(20002)、结算成功(20003)、结算失败(20004,20011)、驳回(20005)、打款失败(20009)、冻结中（20014）、部分结清（20015）	eg:	结算
     *
     * @param channelSettlementStatus
     * @return
     */

    public static boolean filterSettleStatus(Integer channelSettlementStatus) {
        List<Integer> jdFilterStateList = MccConfigUtil.getJDFilterStateList();
        //如果京东渠道状态为待结算 需要做过滤
        return jdFilterStateList.contains(channelSettlementStatus);
    }

    /**
     * jddj取店内分类，jddj只需要分类code
     * 如果是多分类结构，取第一个分类code
     *
     * @param skuInfoDTO
     * @return
     */
    public static String convert2JDDJCategoryCode(SkuInfoDTO skuInfoDTO) {
        if (CollectionUtils.isEmpty(skuInfoDTO.getLeafStoreCategoryList())) {
            return skuInfoDTO.getChannelFrontCategory();
        }
        return skuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryCode();
    }

    public static ChannelSkuSellStatusDTO convert2JDDJSellStatusRequestParam(SpuInfoDTO spuInfoDTO) {
        ChannelSkuSellStatusDTO channelSkuSellStatusDTO = new ChannelSkuSellStatusDTO();
        channelSkuSellStatusDTO.setOutStationNo("{}");
        channelSkuSellStatusDTO.setUserPin("system");

        List<StockVendibility> res = Lists.newArrayList();
        // 1-上架；2-下架
        boolean isSale = spuInfoDTO.getStatus() == 1;
        if (CollectionUtils.isNotEmpty(spuInfoDTO.getSkus())) {
            spuInfoDTO.getSkus().forEach(sku -> {
                StockVendibility info = new StockVendibility();
                info.setDoSale(isSale);
                info.setOutSkuId(sku.getCustomSkuId());
                res.add(info);
            });
        }
        channelSkuSellStatusDTO.setStockVendibilityList(res);
        return channelSkuSellStatusDTO;
    }

    public static SkuStockRequest conver2SkuStockRequestBySpus(BaseRequest request, List<SpuInfoDTO> spuInfoDTOs) {
        SkuStockRequest skuStockRequest = new SkuStockRequest();
        skuStockRequest.setBaseInfo(request);
        Long storeId;
        if (CollectionUtils.isNotEmpty(request.getStoreIdList())) {
            storeId = request.getStoreIdList().get(0);
        } else {
            throw new IllegalArgumentException("门店id为空");
        }
        if (CollectionUtils.isEmpty(spuInfoDTOs)) {
            throw new IllegalArgumentException("商品为空");
        }
        List<SkuStockDTO> skuStockDTOS = Lists.newArrayList();
        for (SpuInfoDTO spuInfoDTO : spuInfoDTOs) {
            if (CollectionUtils.isNotEmpty(spuInfoDTO.getSkus())) {
                spuInfoDTO.getSkus().forEach(sku -> {
                    SkuStockDTO skuStockDTO = new SkuStockDTO();
                    skuStockDTO.setChannelSkuId(sku.getChannelSkuId());
                    skuStockDTO.setSkuId(sku.getCustomSkuId());
                    skuStockDTO.setStoreId(storeId);
                    skuStockDTO.setStockQty(sku.getStock());
                    skuStockDTOS.add(skuStockDTO);
                });
            }
        }
        skuStockRequest.setSkuStockList(skuStockDTOS);
        return skuStockRequest;
    }

    public static SkuStockRequest Conver2SkuStockRequest(BaseRequest request, List<SkuInSpuInfoDTO> createSkus) {
        SkuStockRequest skuStockRequest = new SkuStockRequest();
        skuStockRequest.setBaseInfo(request);
        Long storeId;
        if (CollectionUtils.isNotEmpty(request.getStoreIdList())) {
            storeId = request.getStoreIdList().get(0);
        } else {
            throw new IllegalArgumentException("门店id为空");
        }
        List<SkuStockDTO> skuStockDTOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(createSkus)) {
            createSkus.forEach(sku -> {
                SkuStockDTO skuStockDTO = new SkuStockDTO();
                skuStockDTO.setChannelSkuId(sku.getChannelSkuId());
                skuStockDTO.setSkuId(sku.getCustomSkuId());
                skuStockDTO.setStoreId(storeId);
                skuStockDTO.setStockQty(sku.getStock());
                skuStockDTOS.add(skuStockDTO);
            });
        }
        skuStockRequest.setSkuStockList(skuStockDTOS);
        return skuStockRequest;
    }

    public static SkuPriceMultiChannelRequest conver2SkuPriceRequestBySpus(BaseRequest request, List<SpuInfoDTO> spuInfoDTOs) {
        Long storeId;
        if (CollectionUtils.isNotEmpty(request.getStoreIdList())) {
            storeId = request.getStoreIdList().get(0);
        } else {
            throw new IllegalArgumentException("门店id为空");
        }

        if (CollectionUtils.isEmpty(spuInfoDTOs)) {
            throw new IllegalArgumentException("商品列表为空");
        }

        SkuPriceMultiChannelRequest skuPriceMultiChannelRequest = new SkuPriceMultiChannelRequest();
        skuPriceMultiChannelRequest.setTenantId(request.getTenantId());
        List<SkuPriceMultiChannelDTO> skuPriceMultiChannelDTOS = Lists.newArrayList();

        for (SpuInfoDTO spuInfoDTO : spuInfoDTOs) {
            if (CollectionUtils.isNotEmpty(spuInfoDTO.getSkus())) {
                spuInfoDTO.getSkus().forEach(sku -> {
                    SkuPriceMultiChannelDTO skuPriceMultiChannelDTO = new SkuPriceMultiChannelDTO();
                    skuPriceMultiChannelDTO.setStoreId(storeId);
                    skuPriceMultiChannelDTO.setChannelId(request.getChannelId());
                    skuPriceMultiChannelDTO.setSkuId(sku.getCustomSkuId());
                    skuPriceMultiChannelDTO.setChannelSkuId(sku.getChannelSkuId());
                    skuPriceMultiChannelDTO.setPrice(sku.getPrice());
                    skuPriceMultiChannelDTO.setSpecType(spuInfoDTO.getSpecType());
                    skuPriceMultiChannelDTO.setChannelSpuId(spuInfoDTO.getChannelSpuId());
                    skuPriceMultiChannelDTO.setSpuId(spuInfoDTO.getSpuId());
                    skuPriceMultiChannelDTOS.add(skuPriceMultiChannelDTO);
                });
            }
        }
        skuPriceMultiChannelRequest.setParamList(skuPriceMultiChannelDTOS);
        return skuPriceMultiChannelRequest;
    }

    public static SkuPriceMultiChannelRequest Conver2SkuPriceRequest(BaseRequest request, SpuInfoDTO spuInfoDTO, List<SkuInSpuInfoDTO> createSkus) {

        Long storeId;
        if (CollectionUtils.isNotEmpty(request.getStoreIdList())) {
            storeId = request.getStoreIdList().get(0);
        } else {
            throw new IllegalArgumentException("门店id为空");
        }

        SkuPriceMultiChannelRequest skuPriceMultiChannelRequest = new SkuPriceMultiChannelRequest();
        skuPriceMultiChannelRequest.setTenantId(request.getTenantId());
        List<SkuPriceMultiChannelDTO> skuPriceMultiChannelDTOS = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(createSkus)) {
            createSkus.forEach(sku -> {
                SkuPriceMultiChannelDTO skuPriceMultiChannelDTO = new SkuPriceMultiChannelDTO();
                skuPriceMultiChannelDTO.setStoreId(storeId);
                skuPriceMultiChannelDTO.setChannelId(request.getChannelId());
                skuPriceMultiChannelDTO.setSkuId(sku.getCustomSkuId());
                skuPriceMultiChannelDTO.setChannelSkuId(sku.getChannelSkuId());
                skuPriceMultiChannelDTO.setPrice(sku.getPrice());
                skuPriceMultiChannelDTO.setSpecType(SpecTypeEnum.MULTI.getCode());
                skuPriceMultiChannelDTO.setChannelSpuId(spuInfoDTO.getChannelSpuId());
                skuPriceMultiChannelDTO.setSpuId(spuInfoDTO.getSpuId());
                skuPriceMultiChannelDTOS.add(skuPriceMultiChannelDTO);
            });
        }
        skuPriceMultiChannelRequest.setParamList(skuPriceMultiChannelDTOS);
        return skuPriceMultiChannelRequest;
    }

    public static SkuSellStatusInfoRequest Conver2SkuSellStatusInfoRequest(BaseRequest request, List<SpuKey> spuKeys, Integer status) {
        SkuSellStatusInfoRequest skuSellStatusInfoRequest = new SkuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple = new BaseRequestSimple();
        baseRequestSimple.setTenantId(request.getTenantId());
        baseRequestSimple.setChannelId(request.getChannelId());
        skuSellStatusInfoRequest.setBaseInfo(baseRequestSimple);
        Long storeId;
        if (CollectionUtils.isNotEmpty(request.getStoreIdList())) {
            storeId = request.getStoreIdList().get(0);
        } else {
            throw new IllegalArgumentException("门店id为空");
        }
        if (CollectionUtils.isEmpty(spuKeys)) {
            throw new IllegalArgumentException("商品为空");
        }
        SkuSellStatusInfoDTO skuSellStatusInfoDTO = new SkuSellStatusInfoDTO();
        List<SkuIdDTO> skuIdDTOS = Lists.newArrayList();

        skuSellStatusInfoDTO.setStoreId(storeId);
        skuSellStatusInfoDTO.setSkuStatus(status);

        for (SpuKey spuKey : spuKeys) {
            if (CollectionUtils.isNotEmpty(spuKey.getSkus())) {
                spuKey.getSkus().forEach(sku -> {
                    SkuIdDTO skuIdDTO = new SkuIdDTO();
                    skuIdDTO.setCustomSkuId(sku.getCustomSkuId());
                    skuIdDTOS.add(skuIdDTO);
                });
            }
        }

        skuSellStatusInfoDTO.setSkuId(skuIdDTOS);
        skuSellStatusInfoRequest.setParamList(Lists.newArrayList(skuSellStatusInfoDTO));
        return skuSellStatusInfoRequest;
    }

    public static SkuSellStatusInfoRequest Conver2SkuSellStatusInfoRequest(BaseRequestSimple request, List<SpuKey> spuKeyList, Long storeId, Integer status) {
        SkuSellStatusInfoRequest skuSellStatusInfoRequest = new SkuSellStatusInfoRequest();
        skuSellStatusInfoRequest.setBaseInfo(request);
        SkuSellStatusInfoDTO skuSellStatusInfoDTO = new SkuSellStatusInfoDTO();
        List<SkuIdDTO> skuIdDTOS = Lists.newArrayList();

        skuSellStatusInfoDTO.setStoreId(storeId);
        skuSellStatusInfoDTO.setSkuStatus(status);
        for (SpuKey spuKey : spuKeyList) {
            if (CollectionUtils.isNotEmpty(spuKey.getSkus())) {
                spuKey.getSkus().forEach(sku -> {
                    SkuIdDTO skuIdDTO = new SkuIdDTO();
                    skuIdDTO.setCustomSkuId(sku.getCustomSkuId());
                    skuIdDTOS.add(skuIdDTO);
                });
            }
        }
        skuSellStatusInfoDTO.setSkuId(skuIdDTOS);
        skuSellStatusInfoRequest.setParamList(Lists.newArrayList(skuSellStatusInfoDTO));
        return skuSellStatusInfoRequest;
    }

    public static ChannelSkuSellStatusDTO convert2JDDJSellStatusRequestParam(SpuKey spuKey, Integer sellStatus) {
        ChannelSkuSellStatusDTO channelSkuSellStatusDTO = new ChannelSkuSellStatusDTO();
        channelSkuSellStatusDTO.setOutStationNo("{}");
        channelSkuSellStatusDTO.setUserPin("system");

        List<StockVendibility> res = Lists.newArrayList();
        // 1-上架；2-下架
        boolean isSale = sellStatus == 1;
        if (CollectionUtils.isNotEmpty(spuKey.getSkus())) {
            spuKey.getSkus().forEach(sku -> {
                StockVendibility info = new StockVendibility();
                info.setDoSale(isSale);
                info.setOutSkuId(sku.getCustomSkuId());
                res.add(info);
            });
        }
        channelSkuSellStatusDTO.setStockVendibilityList(res);
        return channelSkuSellStatusDTO;
    }

    public static int parseLong2Int(Long value){
        return Optional.ofNullable(value).map(Long::intValue).orElse(0);
    }

    public static Map<String,String> parseExtend(JddjAfterSaleDetail afterSaleDetail){
        Map<String,String> extend = Maps.newHashMap();
        if(afterSaleDetail==null){
            return extend;
        }
        if(afterSaleDetail.getApplyDeal()!=null){
            extend.put("applyDeal",afterSaleDetail.getApplyDeal());
        }
       if(afterSaleDetail.getOrderSource()!=null){
           extend.put("orderSource",afterSaleDetail.getOrderSource().toString());
       }
        return extend;
    }

    public static Integer parseChannelExtRefundType(JddjAfterSaleDetail afterSaleDetail){
        if(afterSaleDetail.getExtMap()!=null) {
            if(Objects.equals(afterSaleDetail.getExtMap().getRefundReturn(), 1)) {
                // 1表示京东秒送先退款后退货流程
                return ChannelExtRefundTypeEnum.JDDJ_REFUND_RETURN_TYPE.getRefundType();
            } else if(Objects.equals(afterSaleDetail.getExtMap().getPickUpRefund(), 1)) {
                // 2表示京东秒送取件即退款
                return ChannelExtRefundTypeEnum.JDDJ_PICK_UP_REFUND_TYPE.getRefundType();
            }
        }
        return 0;
    }


}
