package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.ElmSelfDeliveryStatusCompensateMsg;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.delay.ElmSelfDeliveryStatusCompensateDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.cache.ElmSelfDeliveryStatusSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.UpdateDeliveryInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 饿了么自配送状态补偿服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class ElmSelfDeliveryStatusCompensateService {

    @Resource
    private ElmSelfDeliveryStatusSquirrelOperationService elmSquirrelService;

    @Resource(name = "elmChannelOrderService")
    private ElmChannelOrderServiceImpl elmChannelOrderService;

    @Resource
    private ElmSelfDeliveryStatusCompensateDelayMessageProducer elmSelfDeliveryStatusCompensateDelayMessageProducer;

    /**
     * 配送状态机，列表里的下标即顺序
     * 只有这4个状态需要补偿
    */
    private static final List<DeliveryStatusEnum> NEED_COMPENSATE_DELIVERY_STATUS_LIST =
            Lists.newLinkedList(Arrays.asList(DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_ARRIVED_SHOP,
                    DeliveryStatusEnum.RIDER_TAKEN_GOODS, DeliveryStatusEnum.DELIVERY_DONE));

    /**
     * @param: orderId 饿了么渠道订单ID
     * @param: currentDeliveryStatus 当前配送状态
     * @return 返回true代表需要补偿
    */
    public boolean isNeedCompensate(String channelOrderId, DeliveryStatusEnum currentDeliveryStatus,
                                    Optional<Integer> cacheDeliveryStatusOptional) {
        if (!NEED_COMPENSATE_DELIVERY_STATUS_LIST.contains(currentDeliveryStatus)) {
            return false;
        }

        if (cacheDeliveryStatusOptional.isPresent()) {
            DeliveryStatusEnum cacheDeliveryStatus = DeliveryStatusEnum.valueOf(cacheDeliveryStatusOptional.get());
            if (isCurrentDeliveryStatusBehindCacheDeliveryStatus(currentDeliveryStatus, cacheDeliveryStatus)) {
                return false;
            }
            if (isCurrentDeliveryStatusAfterOnePositionFromCacheDeliveryStatus(currentDeliveryStatus, cacheDeliveryStatus)) {
                elmSquirrelService.setDeliveryStatusToCache(channelOrderId, currentDeliveryStatus.getCode());
                return false;
            }
            // 否则需要触发补偿
            return true;
        } else {
            if (Objects.equals(DeliveryStatusEnum.RIDER_ASSIGNED, currentDeliveryStatus)) {
                elmSquirrelService.setDeliveryStatusToCache(channelOrderId, currentDeliveryStatus.getCode());
                return false;
            }
            // 否则需要触发补偿
            return true;
        }
    }

    public void compensateDelivery(UpdateDeliveryInfoRequest request, DeliveryStatusEnum currentDeliveryStatus, Optional<Integer> cacheDeliveryStatusOptional) {
        List<DeliveryStatusEnum> durationDeliveryStatusList = getDurationDeliveryStatusList(currentDeliveryStatus, cacheDeliveryStatusOptional);
        if (CollectionUtils.isEmpty(durationDeliveryStatusList)) {
            return;
        }

        log.info("ElmSelfDeliveryStatusCompensateService compensateDelivery, orderId is {}, currentDeliveryStatus is " +
                "{}, cacheDeliveryStatus is {}", request.getOrderId(), currentDeliveryStatus,
                cacheDeliveryStatusOptional.isPresent() ? cacheDeliveryStatusOptional.get() : StringUtils.EMPTY);
        // 消息延迟推送时间(min)
        int delayMinutes = 0;
        for (int index = 0; index < durationDeliveryStatusList.size(); index++) {
            DeliveryStatusEnum status = durationDeliveryStatusList.get(index);
            if (index == 0) {
                UpdateDeliveryInfoRequest updateDeliveryInfoRequest = updateDeliveryInfoRequestCopy(request, status);
                elmChannelOrderService.compensateDeliveryInfo(updateDeliveryInfoRequest);
                continue;
            }
            if (!Objects.equals(DeliveryStatusEnum.DELIVERY_DONE, status)) {
                delayMinutes += MccConfigUtil.getElmSelfDeliveryStatusCompensateUnfinishedDelayMinutes();
            } else {
                // 同步骑手送达状态，必须和骑手送出最少间隔5分钟
                delayMinutes += MccConfigUtil.getElmSelfDeliveryStatusCompensateFinishedDelayMinutes();
            }
            ElmSelfDeliveryStatusCompensateMsg msg = buildMsg(request, status);
            elmSelfDeliveryStatusCompensateDelayMessageProducer.sendDelayMessageInMinutes(msg, delayMinutes);
        }

        elmSquirrelService.setDeliveryStatusToCache(request.getOrderId(), currentDeliveryStatus.getCode());
    }

    private boolean isCurrentDeliveryStatusBehindCacheDeliveryStatus(DeliveryStatusEnum currentDeliveryStatus, DeliveryStatusEnum cacheDeliveryStatus) {
        return NEED_COMPENSATE_DELIVERY_STATUS_LIST.indexOf(currentDeliveryStatus)
                <= NEED_COMPENSATE_DELIVERY_STATUS_LIST.indexOf(cacheDeliveryStatus);
    }

    private boolean isCurrentDeliveryStatusAfterOnePositionFromCacheDeliveryStatus(DeliveryStatusEnum currentDeliveryStatus, DeliveryStatusEnum cacheDeliveryStatus) {
        return NEED_COMPENSATE_DELIVERY_STATUS_LIST.indexOf(currentDeliveryStatus) - NEED_COMPENSATE_DELIVERY_STATUS_LIST.indexOf(cacheDeliveryStatus) == 1;
    }

    private List<DeliveryStatusEnum> getDurationDeliveryStatusList(DeliveryStatusEnum currentDeliveryStatus, Optional<Integer> cacheDeliveryStatusOptional) {
        List<DeliveryStatusEnum> durationDeliveryStatusList = Lists.newArrayList();
        int currentDeliveryStatusIndex = NEED_COMPENSATE_DELIVERY_STATUS_LIST.indexOf(currentDeliveryStatus);

        if (cacheDeliveryStatusOptional.isPresent()) {
            DeliveryStatusEnum cacheDeliveryStatus = DeliveryStatusEnum.valueOf(cacheDeliveryStatusOptional.get());
            if (isCurrentDeliveryStatusBehindCacheDeliveryStatus(currentDeliveryStatus, cacheDeliveryStatus)) {
                return Collections.emptyList();
            }

            int cacheDeliveryStatusIndex = NEED_COMPENSATE_DELIVERY_STATUS_LIST.indexOf(cacheDeliveryStatus);
            for (int startIndex = cacheDeliveryStatusIndex + 1; startIndex <= currentDeliveryStatusIndex; ++startIndex) {
                durationDeliveryStatusList.add(NEED_COMPENSATE_DELIVERY_STATUS_LIST.get(startIndex));
            }
        } else {
            for (int startIndex = NumberUtils.INTEGER_ZERO; startIndex <= currentDeliveryStatusIndex; ++startIndex) {
                durationDeliveryStatusList.add(NEED_COMPENSATE_DELIVERY_STATUS_LIST.get(startIndex));
            }
        }

        return durationDeliveryStatusList;
    }

    private ElmSelfDeliveryStatusCompensateMsg buildMsg(UpdateDeliveryInfoRequest request, DeliveryStatusEnum status) {
        ElmSelfDeliveryStatusCompensateMsg msg = new ElmSelfDeliveryStatusCompensateMsg();
        msg.setTenantId(request.getTenantId());
        msg.setStoreId(request.getShopId());
        msg.setChannelId(request.getChannelId());
        msg.setChannelOrderId(request.getOrderId());
        msg.setDeliveryChannelId(request.getDeliveryChannelId());
        msg.setRiderName(request.getRiderName());
        msg.setRiderPhone(request.getRiderPhone());
        msg.setDeliveryStatus(status.getCode());
        return msg;
    }

   private UpdateDeliveryInfoRequest updateDeliveryInfoRequestCopy(UpdateDeliveryInfoRequest request, DeliveryStatusEnum status) {
       UpdateDeliveryInfoRequest updateDeliveryInfoRequest = new UpdateDeliveryInfoRequest();
       updateDeliveryInfoRequest.setTenantId(request.getTenantId());
       updateDeliveryInfoRequest.setShopId(request.getShopId());
       updateDeliveryInfoRequest.setChannelId(request.getChannelId());
       updateDeliveryInfoRequest.setOrderId(request.getOrderId());
       updateDeliveryInfoRequest.setDeliveryChannelId(request.getDeliveryChannelId());
       updateDeliveryInfoRequest.setRiderName(request.getRiderName());
       updateDeliveryInfoRequest.setRiderPhone(request.getRiderPhone());
       updateDeliveryInfoRequest.setStatus(status.getCode());
       return updateDeliveryInfoRequest;
   }

}
