package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine;

import com.google.common.collect.Maps;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryRequest;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.medicine.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTMedicineEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelOrderThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MtSignUtils;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.RHINO_UPTIMATE_SET;

/**
 * 美团请求公共服务接口
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:09
 **/
@Service
public class MtMedicineChannelGateService extends BaseChannelGateService {
    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Value("${mt.url.base}")
    private String baseUrl;

    @Resource
    private CommonLogger log;

    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private ChannelOrderThriftServiceProxy channelOrderThriftServiceProxy;

    @Override
    public Map<String, Object> sendGet(String url, String method, BaseRequest baseRequest, Object bizParam) {
        return sendGetApp(url, method, baseRequest, bizParam);
    }

    @Override
    public Map<String, Object> sendEncodedGet(String url, String method, BaseRequest baseRequest, Object bizParam) {
        return sendEncodedGetApp(url, method, baseRequest, bizParam);
    }

    @Override
    public <T> T sendPost(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
        return sendPostAppDto(postUrlEnum, baseRequest, bizParam);
    }

    @Override
    public Map<String, Object> sendPost(String url, String method, BaseRequest baseRequest, Object bizParam) {
        return sendPostApp(url, method, baseRequest, bizParam);
    }

    @Override
    public <T> T sendPost(ChannelPostInter postUrlEnum, Long tenantId, Integer channelId, Object bizParam, Long... storeId) {
        BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(channelId);
        if (storeId.length > 0) {
            baseRequest.setStoreIdList(Lists.newArrayList(storeId[0]));
        }

        return sendPostAppDto(postUrlEnum, baseRequest, bizParam);
    }

    @Override
    public Map<String, Object> sendEncodedGetApp(String url, String method, BaseRequest baseRequest, Object bizParam) {
        // 提前获取appId
        if (baseRequest.getAppId() <= 0) {
            baseRequest.setAppId(getAppIdByTenantId(baseRequest.getTenantId(), baseRequest.getChannelId()));
        }

        return super.sendEncodedGetApp(url, method, baseRequest, bizParam);
    }

    @Override
    public Map<String, Object> sendGetApp(String url, String method, BaseRequest baseRequest, Object bizParam) {
        // 提前获取appId
        if (baseRequest.getAppId() <= 0) {
            baseRequest.setAppId(getAppIdByTenantId(baseRequest.getTenantId(), baseRequest.getChannelId()));
        }

        return super.sendGetApp(url, method, baseRequest, bizParam);
    }

    @Override
    public Map<String, Object> sendPostApp(String url, String method, BaseRequest baseRequest, Object bizParam) {
        // 提前获取appId
        if (baseRequest.getAppId() <= 0) {
            baseRequest.setAppId(getAppIdByTenantId(baseRequest.getTenantId(), baseRequest.getChannelId()));
        }

        return super.sendPostApp(url, method, baseRequest, bizParam);
    }

    @Override
    public <T> T sendPostAppMapDto(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
        // 提前获取appId
        if (baseRequest.getAppId() <= 0) {
            baseRequest.setAppId(getAppIdByTenantId(baseRequest.getTenantId(), baseRequest.getChannelId()));
        }

        return super.sendPostAppMapDto(postUrlEnum, baseRequest, bizParam);
    }

    @Override
    public <T> T sendPostAppDto(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
        // 提前获取appId
        if (baseRequest.getAppId() <= 0) {
            baseRequest.setAppId(getAppIdByTenantId(baseRequest.getTenantId(), baseRequest.getChannelId()));
        }

        return super.sendPostAppMapDto(postUrlEnum, baseRequest, bizParam);
    }

    @Override
    public <T> T postToChannel(String channelPoiCode, String channelOnlinePoiCode, ChannelPostInter postUrlEnum, Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest) {
        replaceStoreId(bizParamMap, channelOnlinePoiCode);

        // 限频
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
        if (StringUtils.isNotBlank(appId) && postUrlEnum.requestLimited()) {
            long waitTime = clusterRateLimiter.tryAcquire(postUrlEnum.generateLimitedResourceKey(), appId, baseRequest.isAsyncInvoke());
            if (waitTime != 0) {
                if (RHINO_UPTIMATE_SET.contains(postUrlEnum)) {
                    log.info("MtyyChannelGateService.postToChannel appId:{} url:{} 未获取到令牌 抛出异常", appId, postUrlEnum.getUrl());
                    throw new InvokeChannelTooMuchException(waitTime);
                } else {
                    log.info("MtyyChannelGateService.postToChannel appId:{} url:{} 未获取到令牌 直接返回null", appId, postUrlEnum.getUrl());
                    return null;
                }
            }
        }

        String url = getPostUrl(postUrlEnum);
        Map<String, Object> postParams = generatePostParams(url, sysParam, bizParamMap);

        String resp;
        // 默认所有接口都解析部分失败错误信息，如有特殊接口不需要解析部分失败信息需在此处加下
        boolean parsePartFailedMsgFlag = true;
        ChannelPostMTMedicineEnum channelPostMTMedicineEnum = (ChannelPostMTMedicineEnum) postUrlEnum;
        switch (channelPostMTMedicineEnum) {
            case MEDICINE_DELETE:
            case MEDICINECAT_SAVE:
            case MEDICINECAT_UPDATE:
            case MEDICINECAT_DELETE:
            case MEDICINE_BATCH_ISSOLDOUT:
                parsePartFailedMsgFlag = false;
                break;
            default:
                break;
        }
        resp = postRequest(postUrlEnum, postParams, baseRequest);

        return dealPostResult(resp, postUrlEnum.getResultClass(), parsePartFailedMsgFlag);
    }

    @Override
    protected Map generatePostParams(String url, String method, Map<String, Object> sysParam, Map<String, Object> bizParam) {
        return generatePostParams(url, sysParam, bizParam);
    }

    private Map<String, Object> generatePostParams(String url, Map<String, Object> sysParam, Map<String, Object> bizParam) {
        sysParam.put("timestamp", String.valueOf(DateUtils.unixTime()));
        HashMap<String, Object> paramMap = Maps.newHashMap(sysParam);
        paramMap.putAll(bizParam);
        String paramForSig = MtSignUtils.concatParams(paramMap);
        String sig = MtSignUtils.getSig(url, paramForSig, (String) sysParam.get("secret"));
        paramMap.put("sig", sig);
        paramMap.remove("secret");

        return paramMap;
    }

    private void replaceStoreId(Map<String, Object> bizParamMap, String outStoreId) {
        if (StringUtils.isNotBlank(outStoreId)) {
            bizParamMap.put(Constant.FIELD_NAME_STOREID_MT, outStoreId);
        }
    }

    private <T> T dealPostResult(String resultJson, Class resultClass, boolean parsePartFailedMsgFlag) {
        String formatAfterResult = formatErrorMsg(resultJson);
        log.info("MtMedicineChannelGateService.dealPostResult 格式化渠道返回结果, result:{}, formatAfterResult:{}", resultJson, formatAfterResult);

        ChannelResponseDTO dto = ChannelResponseDTO.parseResult(formatAfterResult, resultClass, parsePartFailedMsgFlag);

        log.info("MtMedicineChannelGateService.dealPostResult 解析后渠道返回数据, channelResponseDTO:{}", dto);

        return (T) dto;
    }

    private Long getAppIdByTenantId(Long tenantId, Integer channelId) {
        List<CopAccessConfigDO> copAccessConfigDOS = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);

        if (CollectionUtils.isNotEmpty(copAccessConfigDOS)) {
            return copAccessConfigDOS.get(0).getAppId();
        }

        return 0L;
    }
//
//    @Override
//    public Map<String, Object> getAppChannelSysParams(Long tenantId, Integer channelId,
//                                                      Long appId, Long storeId) {
//        String sysParamJson = null;
//        List<CopAccessConfigDO> copAccessConfigDOS = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
//
//        Preconditions.checkArgument(
//                CollectionUtils.isNotEmpty(copAccessConfigDOS) && StringUtils.isNotBlank(copAccessConfigDOS.get(0).getSysParams()),
//                "copAccessConfig error tenantId=%s, channelId=%s, appId=%s", tenantId, channelId, appId);
//        Map<String, Object> sysParam = JSON.parseObject(copAccessConfigDOS.get(0).getSysParams());
//        return sysParam;
//    }


    @Override
    public String getPostUrl(ChannelPostInter postUrlEnum) {
        return baseUrl + postUrlEnum.getUrl();
        //return "https://waimaiopen.meituan.com/api/v1/medicine/list";
    }

    private String formatErrorMsg(String msg) {
        if (StringUtils.isBlank(msg)) {
            return Strings.EMPTY;
        }
        String finalMsg = msg;
        for (String formatMsg : MccConfigUtil.getMTMedicineFormatErrorMsg()) {
            finalMsg = finalMsg.replaceAll(formatMsg, Strings.EMPTY);
        }
        return finalMsg;
    }


}
