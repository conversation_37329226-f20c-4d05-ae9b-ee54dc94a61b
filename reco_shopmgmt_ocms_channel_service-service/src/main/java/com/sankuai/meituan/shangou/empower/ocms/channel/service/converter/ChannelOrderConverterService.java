
package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderAllRefundGoodsRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderAllRefundRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderPartRefundGoodsRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderPartRefundRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 饿了么实体转换统一接口类
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:09
 **/
@Mapper(componentModel = "spring", imports = {MoneyUtils.class, ConverterUtils.class})
public interface ChannelOrderConverterService {
    @Mappings({
        @Mapping(target = "channelOrderId", source = "orderId"),
        @Mapping(target = "timestamp", expression = "java(ConverterUtils.stringToMillis(request.getTimestamp()))"),
        @Mapping(target = "sponsor", constant = "3"), // 接口没定义字段
        @Mapping(target = "refundType", constant = "10"),
        @Mapping(expression = "java(ConverterUtils.codeToBool(request.getIsAppeal()))", target = "isAppeal"),
        @Mapping(target = "reason", source = "refundReason"),
        @Mapping(target = "refundApplyTime", expression = "java(ConverterUtils.stringToMillis(request.getRefundApplyTime()))")

    })
    OrderAllRefundRequest orderAllRefundNotify(OrderNotifyRequest request);

    @Mappings({
            @Mapping(target = "channelOrderId", source = "orderId"),
            @Mapping(target = "timestamp", expression = "java(ConverterUtils.stringToMillis(request.getTimestamp()))"),
            @Mapping(target = "sponsor", constant = "3"), // 接口没定义字段
            @Mapping(target = "refundType", constant = "10"),
            @Mapping(expression = "java(ConverterUtils.codeToBool(request.getIsAppeal()))", target = "isAppeal"),
            @Mapping(target = "reason", source = "refundReason"),
            @Mapping(target = "refundApplyTime", expression = "java(ConverterUtils.stringToMillis(request.getRefundApplyTime()))")

    })
    OrderAllRefundGoodsRequest orderAllRefundGoodsNotify(OrderNotifyRequest request);

    @Mappings({
        @Mapping(target = "channelOrderId", source = "orderId"),
        @Mapping(target = "timestamp", expression = "java(ConverterUtils.stringToMillis(request.getTimestamp()))"),
        @Mapping(target = "sponsor", constant = "3"), // 接口没定义字段
        @Mapping(target = "refundAmount", expression = "java(MoneyUtils.yuanToFen(request.getRefundPrice()))"),
        @Mapping(target = "orderPartRefundType", constant = "10"),
        @Mapping(expression = "java(ConverterUtils.codeToBool(request.getIsAppeal()))", target = "isAppeal"),
        @Mapping(target = "reason", source = "cancelReason")
    })
    OrderPartRefundRequest mtOrderPartRefundNotify(OrderNotifyRequest request);

    @Mappings({
            @Mapping(target = "channelOrderId", source = "orderId"),
            @Mapping(target = "timestamp", expression = "java(ConverterUtils.stringToMillis(request.getTimestamp()))"),
            @Mapping(target = "sponsor", constant = "3"), // 接口没定义字段
            @Mapping(target = "refundAmount", expression = "java(MoneyUtils.yuanToFen(request.getRefundPrice()))"),
            @Mapping(target = "orderPartRefundType", constant = "10"),
            @Mapping(expression = "java(ConverterUtils.codeToBool(request.getIsAppeal()))", target = "isAppeal"),
            @Mapping(target = "reason", source = "cancelReason")
    })
    OrderPartRefundGoodsRequest mtOrderPartRefundGoodsNotify(OrderNotifyRequest request);

    @Mappings({
        @Mapping(target = "channelOrderId", source = "orderId"),
        @Mapping(target = "timestamp", expression = "java(ConverterUtils.stringToMillis(request.getTimestamp()))"),
        @Mapping(target = "sponsor", constant = "3"), // 接口没定义字段
        @Mapping(target = "refundAmount", expression = "java(ConverterUtils.stringToInt(request.getRefundPrice()))"),
        @Mapping(target = "orderPartRefundType", constant = "10"),
        @Mapping(expression = "java(ConverterUtils.codeToBool(request.getIsAppeal()))", target = "isAppeal"),
        @Mapping(target = "reason", source = "cancelReason"),
        @Mapping(target = "refundApplyTime", expression = "java(ConverterUtils.stringToMillis(request.getRefundApplyTime()))"),
    })
    OrderPartRefundRequest orderPartRefundNotify(OrderNotifyRequest request);
}