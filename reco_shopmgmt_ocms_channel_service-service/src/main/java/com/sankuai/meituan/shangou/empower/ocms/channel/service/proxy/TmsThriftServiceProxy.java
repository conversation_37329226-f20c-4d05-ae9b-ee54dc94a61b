package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.ShopDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryOrderRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryShopDetailRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryOrderResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryShopDetailResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/5/20
 * @email jianglilin02@meituan
 */
@Slf4j
@Service
public class TmsThriftServiceProxy {

    @Resource
    private QueryDeliveryInfoThriftService queryDeliveryInfoThriftService;

    public Optional<List<TDeliveryOrder>> queryDeliveryOrderByOrderId(Long orderId){
        return queryDeliveryOrderByOrderId(orderId,true);
    }

    public Optional<List<TDeliveryOrder>> queryDeliveryOrderByOrderId(Long orderId,boolean masterOnly) {
        try {
            QueryDeliveryOrderResponse response = queryDeliveryInfoThriftService.queryDeliveryOrderByOrderId(new QueryDeliveryOrderRequest(orderId,masterOnly));
            log.info("invoke queryDeliveryOrderByOrderId,request = {},response = {}", orderId, response);
            if (!Objects.equals(response.getStatus().getCode(), ResponseCodeEnum.SUCCESS.getValue()) || CollectionUtils.isEmpty(response.getTDeliveryOrders())) {
                return Optional.empty();
            }
            return Optional.of(response.getTDeliveryOrders());
        } catch (Exception e) {
            log.error("invoke queryDeliveryOrderByOrderId error", e);
            throw new BizException("查询运单失败");
        }
    }

    public Optional<ShopDetail> queryShopDetail(String channelShopId) {
        try {
            QueryShopDetailResponse response = queryDeliveryInfoThriftService.queryShopDetail(new QueryShopDetailRequest(channelShopId));
            log.info("invoke queryShopDetail,request = {},response = {}", channelShopId, response);
            if (!Objects.equals(response.getStatus().getCode(), ResponseCodeEnum.SUCCESS.getValue()) || response.getShopDetails() == null) {
                return Optional.empty();
            }
            return Optional.of(response.getShopDetails());
        } catch (Exception e) {
            log.error("invoke queryShopDetail error", e);
            throw new BizException("查询渠道门店信息失败");
        }
    }

}
