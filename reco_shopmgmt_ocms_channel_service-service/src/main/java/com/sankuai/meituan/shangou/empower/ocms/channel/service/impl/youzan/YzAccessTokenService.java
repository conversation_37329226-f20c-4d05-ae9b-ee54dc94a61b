package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzan;

import java.math.BigInteger;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.TokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.core.client.core.DefaultYZClient;
import com.youzan.cloud.open.sdk.core.oauth.model.OAuthToken;
import com.youzan.cloud.open.sdk.core.oauth.token.TokenParameter;
import com.youzan.cloud.open.security.SecretClient;
import com.youzan.cloud.open.security.exception.DataSecurityException;

import lombok.extern.slf4j.Slf4j;

/**
 * 有赞token 获取工具类
 *
 * <AUTHOR>
 * @since 2021/06/03 16:53
 */
@Slf4j
@Service
public class YzAccessTokenService {


    private static final int RETRY_MAX = 3;

    private static final long SECONDS_MILLS = TimeUnit.SECONDS.toMillis(1);

    private static final String TOKEN_DELIMITER = "_token_";

    private static final TokenMessage UN_VALID = TokenMessage.builder().expires(0L).build();

    final LoadingCache<AppMessage, SecretClient> SECRET_CLIENT_CACHE = CacheBuilder.newBuilder()
            //本地最多缓存5000个应用
            .maximumSize(5000)
            //缓存20分钟实效
            .expireAfterAccess(10, TimeUnit.MINUTES)
            //加载token
            .build(new CacheLoader<AppMessage, SecretClient>() {
                @Override
                public SecretClient load(AppMessage key) {
                    try {
                        return new SecretClient(key.getClientId(), key.getClientSecret());
                    }
                    catch (DataSecurityException e) {
                        log.error("SecretClient init error,excption", e);
                        throw new BizException("SecretClient init error,excption:", e);
                    }
                }
            });

    @Autowired
    private DefaultYZClient yzClient;

    @Resource(name = "yzTokenRedisClient")
    private RedisStoreClient redisStoreClient;

    private static final String REDIS_TOKEN_CATEGORY = "yz-token";

    public String getAccessToken(AppMessage appMessage) {
        try {
            TokenMessage tokenMessage = getToken4Redis(appMessage);
            // redis查询结果为空
            if (tokenMessage == UN_VALID) {
                return refreshToken(appMessage).getToken();
            }
            if (tokenMessage.valid()) {
                return tokenMessage.getToken();
            }
            // token 过期获取新的token
            return refreshToken(appMessage).getToken();
        }
        catch (Exception e) {
            log.info("get token 4 youzan error, exception:", e);
        }
        throw new BizException("get youzan token error");
    }

    public SecretClient getClientSecret(AppMessage appMessage) {
        try {
            return SECRET_CLIENT_CACHE.get(appMessage);
        }
        catch (ExecutionException e) {
            log.info("get youzan SecretClient 4 cache error, exception:", e);
            throw new BizException("get youzan SecretClient error");
        }
    }

    public void clearClientSecret(AppMessage appMessage) {
        try {
            SECRET_CLIENT_CACHE.invalidate(appMessage);
        }
        catch (Exception e) {
            throw new BizException("remove youzan SecretClient error,exception:", e);
        }
    }

    /**
     * 查询有赞实时token、刷新缓存
     *
     * @param appMessage token相关参数
     * @return token信息
     */
    public TokenMessage refreshToken(AppMessage appMessage) {
        int retry = BigInteger.ZERO.intValue();
        while (++retry < RETRY_MAX) {
            try {
                return getAccessToken4Yz(appMessage);
            }
            catch (Exception e) {
                log.info("refresh youzan token error, retry:{}, exception:", retry, e);
            }
        }
        throw new BizException("get youzan token error");
    }

    /**
     * 直接刷新token到缓存
     *
     * @param grantId  有赞门店id
     * @param clientId 有赞 clientId
     * @param token    有赞 token
     * @param expires  有赞 token过期时间戳单位毫秒
     */
    public void refreshToken2Redis(String grantId, String clientId, String token, Long expires, Long timestamp) {
        AppMessage appMessage = AppMessage
                .builder()
                .grantId(grantId)
                .clientId(clientId)
                .build();
        TokenMessage tokenMessage = getToken4Redis(appMessage);
        // 过期时间比redis缓存中过期时间新才进行存储
        if(tokenMessage == null || tokenMessage.getTimestamp() == null || tokenMessage.getTimestamp() < timestamp){
            setToken2Redis(appMessage,
                    TokenMessage
                            .builder()
                            .token(token)
                            .refreshToken(StringUtils.EMPTY)
                            .expires(expires)
                            .timestamp(timestamp)
                            .build());
        }
    }

    public boolean checkToken(SDKException ex, AppMessage appMessage) {
        boolean tokenValid = ex.getCode() != null && MccConfigUtil.youzanTokenCodes().contains(ex.getCode().toString());
        if (tokenValid) {
            clearToken(appMessage);
        }
        return tokenValid;
    }

    private TokenMessage getToken4Redis(AppMessage appMessage) {
        int retry = BigInteger.ZERO.intValue();
        while (++retry <= RETRY_MAX) {
            try {
                String accessToken = redisStoreClient.get(new StoreKey(REDIS_TOKEN_CATEGORY,
                        String.valueOf(appMessage.getClientId()), TOKEN_DELIMITER, appMessage.getGrantId()));
                return StringUtils.isNotEmpty(accessToken) ? JSON.parseObject(accessToken, TokenMessage.class) : UN_VALID;
            }
            catch (Exception e) {
                log.info("get token 4 youzan error, retry:{}, exception:", retry, e);
            }
        }
        throw new BizException("get youzan token error");
    }

    /**
     * 查询有赞token、不刷新token
     *
     * @param appMessage token相关参数
     * @return token信息
     */
    public TokenMessage getAccessToken4Yz(AppMessage appMessage) {
        try {
            TokenParameter tokenParameter = TokenParameter
                    .self()
                    .clientId(appMessage.getClientId())
                    .clientSecret(appMessage.getClientSecret())
                    .grantId(appMessage.getGrantId())
                    // false 只要token未过期返回的都是同一个token
                    // true  表示刷新每次返回的都是不同的token,以最新的一个token为准
                    // 避免刷新旭日侧的token、强制设置为false、不进行token刷新
                    .refresh(false)
                    .build();
            log.info("request yz get token, request->{}", JSON.toJSONString(tokenParameter));
            OAuthToken token = yzClient.getOAuthToken(tokenParameter);
            log.info("response yz get token, response->{}", token);
            TokenMessage tokenMessage = TokenMessage.builder().expires(token.getExpires())
                    .refreshToken(Optional.ofNullable(token.getRefreshToken()).orElse(StringUtils.EMPTY))
                    .timestamp(System.currentTimeMillis())
                    .token(token.getAccessToken()).build();
            // 有赞token有效时间默认为7天
            // redis过期时间存储为有赞平台的过期时间
            // save token 2 redis
            setToken2Redis(appMessage, tokenMessage);
            return tokenMessage;
        }
        catch (SDKException e) {
            throw new BizException("get youzan token error", e);
        }

    }

    private void setToken2Redis(AppMessage appMessage, TokenMessage tokenMessage) {
        long expireSeconds = (tokenMessage.getExpires() - System.currentTimeMillis()) / SECONDS_MILLS;
        redisStoreClient.set(new StoreKey(REDIS_TOKEN_CATEGORY, appMessage.getClientId(), TOKEN_DELIMITER,
                appMessage.getGrantId()), JSON.toJSONString(tokenMessage), (int) expireSeconds);
    }

    private void clearToken(AppMessage appMessage) {
        try {
            redisStoreClient.delete(new StoreKey(REDIS_TOKEN_CATEGORY, String.valueOf(appMessage.getClientId()), TOKEN_DELIMITER,
                    appMessage.getGrantId()));
        }
        catch (Exception e) {
            throw new BizException("remove youzan token error,exception:", e);
        }
    }

}
