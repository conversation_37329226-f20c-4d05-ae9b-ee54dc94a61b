package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mock;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTMockEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.sankuai.sgfnqnh.tenant.task.client.thrift.mock.MockMtThriftService;
import com.sankuai.sgfnqnh.tenant.task.client.thrift.mock.request.MockBaseRequest;
import com.sankuai.sgfnqnh.tenant.task.client.thrift.mock.response.MockChannelPoiInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.LIST_PARTITION_NUM;
import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.RHINO_UPTIMATE_SET;

/**
 * @author:huchangwu
 * @date: 2019/1/16
 * @time: 下午4:42
 */
@Service("mockMtChannelPoiService")
public class MockMtChannelPoiServiceImpl implements ChannelPoiService {

    @Resource
    private MtChannelGateService mtChannelGateService;
    @Resource
    private MtConverterService mtConverterService;
    @Resource
    private ClusterRateLimiter clusterRateLimiter;
    @Resource
    private CommonLogger log;

    @Autowired
    private MockMtThriftService mockMtThriftService;

    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Override
    public GetPoiInfoResponse batchGetPoiInfo(BaseRequestSimple req) {
        return null;
//        GetPoiInfoResponse resp = new GetPoiInfoResponse();
////        Map<String, Object> sysParam = mtChannelGateService.getChannelSysParams(new BaseRequest().setChannelId(req.getChannelId()).setTenantId(req.getTenantId()));
////        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
////        /**
////         * 查询门店列表
////         */
////        // 限频控制
////        if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.BATCH_GET_POILIST, appId)) {
////            log.warn("Call blocking failed frequently, Continue");
////        }
//        String appId = getTenantAppId(req.getTenantId(),req.getChannelId());
//
//        MockBaseRequest baseRequest = new MockBaseRequest();
//        baseRequest.setAppId(Long.valueOf(appId));
//        List<String> totalShopIds = mockMtThriftService.getChannelPoiCodes(baseRequest);
//
//        /**
//         * 查询门店详情
//         */
//
//        List<List<String>> shopIds_partition = Lists.partition(totalShopIds, LIST_PARTITION_NUM);
//        List<PoiInfo> poiInfoList = shopIds_partition.parallelStream().map(shopIds -> {
//            List<MockChannelPoiInfo> mockRsp = mockMtThriftService.mgetPoiInfo(baseRequest, shopIds);
//            List<ChannelPoiInfo> channelPoiInfos = new ArrayList<>();
//            if (CollectionUtils.isNotEmpty(mockRsp)) {
//                mockRsp.forEach(x -> {
//                    ChannelPoiInfo tmp = new ChannelPoiInfo();
//                    BeanUtils.copyProperties(x, tmp);
//                    channelPoiInfos.add(tmp);
//                });
//            }
//            return mtConverterService.poiInfoListMapping(channelPoiInfos);
//        }).filter(result -> CollectionUtils.isNotEmpty(result)).flatMap(Collection::stream).collect(Collectors.toList());
//
//        resp.setStatus(ResultGenerator.genSuccessResult());
//        resp.setPoiInfoList(poiInfoList);
//        return resp;
    }

    private CopAccessConfigDO getTenantAppId(Long tenantId, Integer channelId) {
        List<CopAccessConfigDO> list = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(list)) {
            log.error("getTenantAppId t:{},c:{} no app", tenantId, channelId);
            return null;
        }
        return list.get(0);
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIds(BaseRequestSimple req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
//        Map<String, Object> sysParam = mtChannelGateService.getChannelSysParams(new BaseRequest().setChannelId(req.getChannelId()).setTenantId(req.getTenantId()));
//        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
//        /**
//         * 查询门店列表
//         */

        // 接口传入的appID不是渠道应用ID，而且自定义的那个，这里需要取查一下
        CopAccessConfigDO copAccessConfigDO = getTenantAppId(req.getTenantId(), req.getChannelId());
        String tenantAppId = copAccessConfigDO.getTenantAppId();
        // 限频控制
        if (StringUtils.isNotBlank(tenantAppId) && !clusterRateLimiter.tryAcquire(ChannelPostMTMockEnum.BATCH_GET_POILIST, tenantAppId)) {
            log.warn("Call blocking failed frequently, Continue");
        }

        MockBaseRequest baseRequest = new MockBaseRequest();
        baseRequest.setAppId(Long.valueOf(tenantAppId));
        List<String> totalShopIds = mockMtThriftService.getChannelPoiCodes(baseRequest);
        resp.setAppPoiCodeDTOList(new ArrayList<>());
        totalShopIds.forEach(x ->{
            resp.getAppPoiCodeDTOList().add(new AppPoiCodeDTO(x,copAccessConfigDO.getAppId()));
        });
        return resp.setStoreIds(totalShopIds);
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIdsBySysParam(BatchGetPoiIdsRequest req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        return resp;
    }

    @Override
    public GetPoiInfoResponse batchGetPoiDetails(BatchGetPoiDetailsRequest request) {
        GetPoiInfoResponse resp = new GetPoiInfoResponse();
//        Map<String, Object> sysParam = mtChannelGateService.getChannelSysParams(new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId()));
//        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
        CopAccessConfigDO copAccessConfigDO = getTenantAppId(request.getTenantId(), request.getChannelId());
        String tenantAppId = copAccessConfigDO.getTenantAppId();
        // 限频控制
        rateLimitManage(tenantAppId, ChannelPostMTMockEnum.BATCH_GET_POIDETAIL, request.isAsyncInvoke());


        MockBaseRequest baseRequest = new MockBaseRequest();
        baseRequest.setAppId(Long.valueOf(tenantAppId));

        List<List<String>> shopIds_partition = Lists.partition(request.getStoreIds(), LIST_PARTITION_NUM);
        List<PoiInfo> poiInfoList = Lists.newArrayList();
        try {
            poiInfoList = shopIds_partition.parallelStream().map(shopIds -> {

                List<MockChannelPoiInfo> mockRsp = mockMtThriftService.mgetPoiInfo(baseRequest, shopIds);
                List<PoiInfo> poiInfos = convert(mockRsp, copAccessConfigDO.getAppId());
                return poiInfos;
            }).filter(result -> CollectionUtils.isNotEmpty(result)).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (InvokeChannelTooMuchException e) {
            return resp.setStatus(new ResultStatus(ResultCodeEnum.TRIGGER_LIMIT.getValue(), "", String.valueOf(e.getWaitTimeMills())));
        }


        resp.setStatus(ResultGenerator.genSuccessResult());
        resp.setPoiInfoList(poiInfoList);
        return resp;
    }

    private List<PoiInfo> convert(List<MockChannelPoiInfo> mockRsp, Long appId) {
        if (CollectionUtils.isEmpty(mockRsp)) {
            return new ArrayList<>();
        }
        List<PoiInfo> rsp = new ArrayList<>();
        mockRsp.forEach(x -> {
            PoiInfo tmp = new PoiInfo();
            BeanUtils.copyProperties(x, tmp);
            tmp.setAppId(appId);
            rsp.add(tmp);
        });
        return rsp;
    }

    /**
     * 获取根据渠道的开放平台门店Id，查询渠道的外卖门店Id
     *
     * @param request
     * @return
     */
    @Override
    public GetPoiIdsResponse getPoiInnerId(BatchGetPoiDetailsRequest request) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        Map<String, Object> sysParam = mtChannelGateService.getChannelSysParams(new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId()));
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

        List<String> storeIdList = request.getStoreIds();
        if (CollectionUtils.isEmpty(storeIdList)) {
            throw new IllegalArgumentException("需传入门店的开放平台编码");
        }
        String appPoiCode = storeIdList.get(0);
        // 查询渠道门店的外卖平台门店ID
        List<String> wmPoiIds = Lists.newArrayList();

        request.getStoreIds().forEach(x -> wmPoiIds.add("wm_" + x));

        return resp.setStoreIds(wmPoiIds);
    }

    @Override
    public ResultStatus poiOpen(ChannelPoiIdRequest req) {

        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus poiClose(ChannelPoiIdRequest req) {

        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus promotionInfoUpdate(PoiPromotionInfoUpdateRequest req) {

        return ResultGenerator.genSuccessResult();
    }

    /**
     * 营业时间更新
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus shippingTimeUpdate(PoiShippingTimeUpdateRequest req) {
        return ResultGenerator.genSuccessResult();
    }


    /**
     * 使门店接受预订单
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus prebookStatusOpen(ChannelPoiIdRequest req) {
        // 构造业务参数
        HashMap<String, String> bizParam = Maps.newHashMapWithExpectedSize(2);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());
        bizParam.put(ProjectConstant.MT_PREBOOK, String.valueOf(ProjectConstant.YES));

        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(), null, false);

        return updateShopInfo(baseRequest, bizParam);
    }

    @Override
    public ResultStatus prebookStatusClose(ChannelPoiIdRequest req) {
        // 构造业务参数
        HashMap<String, String> bizParam = Maps.newHashMapWithExpectedSize(2);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());
        bizParam.put(ProjectConstant.MT_PREBOOK, String.valueOf(ProjectConstant.NO));

        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(), null, false);

        return updateShopInfo(baseRequest, bizParam);

    }

    /**
     * 更新门店的接受预定的日期范围
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus updatePrebookDays(PoiPrebookDaysUpdateRequest req) {
        // 构造业务参数
        HashMap<String, String> bizParam = Maps.newHashMapWithExpectedSize(2);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());
        bizParam.put(ProjectConstant.MT_PREBOOK_MIN_DAYS, String.valueOf(req.getPrebookMinDays()));
        bizParam.put(ProjectConstant.MT_PREBOOK_MAX_DAYS, String.valueOf(req.getPrebookMaxDays()));

        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(), null, false);

        return updateShopInfo(baseRequest, bizParam);
    }

    private ResultStatus updateShopInfo(BaseRequest baseRequest, Map<String, String> bizParam) {


        return ResultGenerator.genSuccessResult();
    }

    /**
     * 获取门店公告信息
     *
     * @param req
     * @return
     */
    @Override
    public GetPoiPromotionInfoResponse getPoiPromotionInfo(ChannelPoiIdRequest req) {
        GetPoiPromotionInfoResponse response = new GetPoiPromotionInfoResponse();

        BatchGetPoiDetailsRequest request = new BatchGetPoiDetailsRequest();

        request.setTenantId(req.getTenantId());
        request.setChannelId(req.getChannelId());
        request.setStoreIds(Arrays.asList(req.getChannelPoiCode()));

        GetPoiInfoResponse getPoiInfoResponse = batchGetPoiDetails(request);
        response.setStatus(getPoiInfoResponse.getStatus());
        if (CollectionUtils.isNotEmpty(getPoiInfoResponse.getPoiInfoList())) {
            response.setPromotionInfo(getPoiInfoResponse.getPoiInfoList().get(0).getPromotionInfo());
        }

        return response;
    }

    /**
     * 获取门店营业状态
     *
     * @param req
     * @return
     */
    @Override
    public ChannelPoiStatusResponse getPoiStatus(ChannelPoiIdRequest req) {
        ChannelPoiStatusResponse response = new ChannelPoiStatusResponse();

        BatchGetPoiDetailsRequest request = new BatchGetPoiDetailsRequest();

        request.setTenantId(req.getTenantId());
        request.setChannelId(req.getChannelId());
        request.setStoreIds(Arrays.asList(req.getChannelPoiCode()));

        GetPoiInfoResponse getPoiInfoResponse = batchGetPoiDetails(request);
        response.setStatus(getPoiInfoResponse.getStatus());
        if (CollectionUtils.isNotEmpty(getPoiInfoResponse.getPoiInfoList())) {
            response.setPoiStatus(getPoiInfoResponse.getPoiInfoList().get(0).getOpenLevel());
        }

        return response;
    }

    /**
     * 门店授权
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus poiAuth(ChannelPoiAuthRequest req) {
        return poiBindOrUnBind(req, 1);
    }

    /**
     * 门店解析授权
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus poiDeAuth(ChannelPoiAuthRequest req) {
        return poiBindOrUnBind(req, 2);
    }

    @Override
    public ResultStatus updatePoiAddress(PoiAddressUpdateRequest request) {
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public UpdateSaleafterAddressMessageResponse updateSaleafterAddressMessage(UpdateSaleafterAddressMessageRequest request) {
        return ResultGenerator.UpdateSafeAddressResultData(ResultCode.FAIL, "美团渠道暂不支持此功能");
    }

    @Override
    public ResultStatus poiHotlineUpdate(ChannelPoiHotlineRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能");
    }

    @Override
    public QueryPoiAuthDetailResponse queryPoiAuthDetailInfo(BaseChannelPoiRequestSimple request) {
        return new QueryPoiAuthDetailResponse().setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能"));
    }

    @Override
    public ResultStatus updatePoiAuthInfo(UpdatePoiAuthInfoRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能");
    }


    private ResultStatus poiBindOrUnBind(ChannelPoiAuthRequest req, int type) {


        return ResultGenerator.genSuccessResult();
    }


    private String convertShippingTime(List<ShippingTime> shippingTimes, Set<Integer> shippingDays) {
        String shippingTime = StringUtils.join(shippingTimes.stream()
                .map(s -> s.getStartTime() + "-" + s.getEndTime()).sorted().collect(Collectors.toList()), ",");
        StringBuilder dayShippingTime = new StringBuilder();
        if (CollectionUtils.isNotEmpty(shippingDays)) {
            for (int i = 0; i < 7; ++i) {
                if (shippingDays.contains(i)) {
                    dayShippingTime.append(shippingTime);
                }
                dayShippingTime.append(";");
            }
            dayShippingTime.deleteCharAt(dayShippingTime.length() - 1);
        } else {
            dayShippingTime.append(shippingTime);
        }
        return dayShippingTime.toString();
    }

    /**
     * 限频控制
     *
     * @param appId
     * @param postEnum
     * @param isAsync
     */
    private void rateLimitManage(String appId, ChannelPostMTMockEnum postEnum, Boolean isAsync) {
        if (StringUtils.isBlank(appId)) {
            return;
        }
        long waitTime = clusterRateLimiter.tryAcquire(postEnum, appId, isAsync);
        // 同步调用出现限频时，不进行限频优化
        if (waitTime != 0 && !isAsync) {
            log.warn("Call blocking failed frequently, Continue");
            return;
        }
        if (waitTime != 0) {
//            if (RHINO_UPTIMATE_SET.contains(postEnum)) {
//                log.info("MtChannelPoiService appId:{} url:{} 未获取到令牌 抛出异常", appId, postEnum.getUrl());
//                throw new InvokeChannelTooMuchException(waitTime);
//            } else {
            log.warn("Call blocking failed frequently, Continue");
//            }
        }
    }
}
