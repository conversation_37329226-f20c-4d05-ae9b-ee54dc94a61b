package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.meituan.shangou.saas.common.enums.OcmsRefundSponsorEnum;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryByViewOrderIdRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.RefundTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiSearchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiSearchResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.KeyConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.ChannelActivityTypeInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.FarmPaotuiCancelDeliveryInnerRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.FarmPaotuiCancelInnerResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ChannelStatusConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.YzConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.YzOrderConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.OrderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.PayStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.PayTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelOrderStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.json.GsonUtils;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanCloudSecretDecryptSingle;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanRetailOpenUpdateLocaldeliverystatus;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanTradeOpenPcOrderPromotion;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanCloudSecretDecryptSingleParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanCloudSecretDecryptSingleResult;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanRetailOpenUpdateLocaldeliverystatusParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanRetailOpenUpdateLocaldeliverystatusResult;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanTradeOpenPcOrderPromotionParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanTradeOpenPcOrderPromotionResult;
import com.youzan.cloud.open.sdk.gen.v3_0_0.api.*;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.*;
import com.youzan.cloud.open.sdk.gen.v3_0_1.api.YouzanTradeReturngoodsAgree;
import com.youzan.cloud.open.sdk.gen.v3_0_1.api.YouzanTradeReturngoodsRefuse;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanTradeReturngoodsAgreeParams;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanTradeReturngoodsAgreeResult;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanTradeReturngoodsRefuseParams;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanTradeReturngoodsRefuseResult;
import com.youzan.cloud.open.sdk.gen.v4_0_0.api.YouzanTradeGet;
import com.youzan.cloud.open.sdk.gen.v4_0_0.api.YouzanTradeSelffetchcodeGet;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanTradeGetParams;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanTradeGetResult;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanTradeSelffetchcodeGetParams;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanTradeSelffetchcodeGetResult;
import com.youzan.cloud.open.sdk.gen.v4_0_4.api.YouzanTradesSoldGet;
import com.youzan.cloud.open.sdk.gen.v4_0_4.model.YouzanTradesSoldGetParams;
import com.youzan.cloud.open.sdk.gen.v4_0_4.model.YouzanTradesSoldGetResult;
import com.youzan.cloud.open.security.SecretClient;
import com.youzan.cloud.open.security.secret.SecretServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.apache.commons.lang.builder.ToStringBuilder.reflectionToString;

/**
 * <AUTHOR>
 * @since 2021/06/03 16:28
 */
@Slf4j
@Service("yzToolChannelOrderService")
public class YzToolChannelOrderServiceImpl extends YouZanToolBaseService implements ChannelOrderService {

    private static final int SKU_PROPERTY_STEP = 2;
    private static final long MIN = TimeUnit.MINUTES.toMillis(1);
    public static final Integer YOU_ZAN = 501;

    private static final Long PAY_ON_DELIVERY = 9L;

    @Autowired
    private YzConverterService converterService;
    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private PoiThriftService poiThriftService;


    @Override
    public ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request) {
        return null;
    }

    @Override
    public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
        GetChannelOrderDetailResult result = new GetChannelOrderDetailResult();
        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getSotreId());
            //和有赞技术沟通，有赞订单token为门店维度，每个门店需要单独维护token
            YouzanTradeGetParams tradeGetParams = new YouzanTradeGetParams();
            tradeGetParams.setTid(request.getOrderId());
            YouzanTradeGetResult tradeGetResult = getResult4YouZan(appMessage, new YouzanTradeGet(tradeGetParams), YouzanTradeGetResult.class);
            decryptOrderMessage(appMessage, tradeGetResult.getData());
            log.info("YzToolChannelOrderServiceImpl.getChannelOrderDetail, request:{}, result:{}", request,
                    GsonUtils.toJSONString(tradeGetResult));
            ChannelOrderDetailDTO channelOrderDetailDTO = getChannelOrderDetailDTO(tradeGetResult.getData(), appMessage);
            channelOrderDetailDTO.setStoreId(request.getSotreId());
            return result.setStatus(ResultGenerator.genSuccessResult())
                    .setChannelOrderDetail(channelOrderDetailDTO);
        }
        catch (SDKException e) {
            log.error("search order detail error, request->{}, exception:", request, e);
            return result.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST));

        }
        catch (Exception e) {
            log.error("order detail error, exception:", e);
            return result.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST));
        }
    }

    @Override
    public ResultStatus preparationMealComplete(PreparationMealCompleteRequest request) {
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request) {
        GetOrderStatusResult result = new GetOrderStatusResult();
        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getStoreId());
            //和有赞技术沟通，有赞订单token为门店维度，每个门店需要单独维护token
            YouzanTradeGetParams tradeGetParams = new YouzanTradeGetParams();
            tradeGetParams.setTid(request.getOrderId());
            YouzanTradeGetResult tradeGetResult = getResult4YouZan(appMessage, new YouzanTradeGet(tradeGetParams), YouzanTradeGetResult.class);
            decryptOrderMessage(appMessage, tradeGetResult.getData());
            log.info("YzToolChannelOrderServiceImpl.getOrderStatus, request:{}, result:{}", request,
                    GsonUtils.toJSONString(tradeGetResult));
            ChannelOrderDetailDTO channelOrderDetailDTO = getChannelOrderDetailDTO(tradeGetResult.getData(), appMessage);

            OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
            orderStatusDTO.setOrderId(channelOrderDetailDTO.getChannelOrderId());
            orderStatusDTO.setStatus(channelOrderDetailDTO.getChannelOrderStatus());

            return result.setStatus(ResultGenerator.genSuccessResult())
                    .setOrderStatus(orderStatusDTO);
        } catch (SDKException e) {
            log.error("search order detail error, request->{}, exception:", request, e);
            return result.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST));

        } catch (Exception e) {
            log.error("order detail error, exception:", e);
            return result.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST));
        }
    }

    @Override
    public GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request) {
        GetOrderAfsApplyListResult ret = new GetOrderAfsApplyListResult();
        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getStoreId());

            List<String> afterSaleIds = new LinkedList<>();

            if (StringUtils.isBlank(request.getAfterSaleId())) {
                YouzanTradeRefundSearchParams params = new YouzanTradeRefundSearchParams();
                params.setTid(request.getChannelOrderId());
                YouzanTradeRefundSearchResult result = getResult4YouZan(appMessage, new YouzanTradeRefundSearch(params), YouzanTradeRefundSearchResult.class);

                if (!result.getSuccess() || result.getData() == null) {
                    return ret.setStatus(ResultGenerator.genResult(ResultCode.AFTERSALE_NOT_EXIST));
                }

                afterSaleIds = Optional.ofNullable(result.getData().getRefunds()).orElse(Collections.emptyList()).stream().map(item -> {
                    return item.getRefundId();
                }).collect(Collectors.toList());

            } else {
                afterSaleIds.add(request.getAfterSaleId());
            }

            List<OrderAfsApplyDTO> retData = new LinkedList<>();
            // 获取历史数据 和操作员类型
            Map<String, AfterSaleApplyBODto> afterSaleApplyMap = getAfterSaleApplyMap(request.getAfterSaleApplyBODto());
            int applyType = request.getApplyType();

            for (String afterSaleId : afterSaleIds) {
                YouzanTradeRefundGetParams params = new YouzanTradeRefundGetParams();
                params.setRefundId(afterSaleId);
                YouzanTradeRefundGetResult result = getResult4YouZan(appMessage, new YouzanTradeRefundGet(params), YouzanTradeRefundGetResult.class);

                if (!result.getSuccess() || result.getData() == null || StringUtils.isBlank(result.getData().getStatus())) {
                    return ret.setStatus(ResultGenerator.genResult(ResultCode.AFTERSALE_NOT_EXIST));
                }

                OrderAfsApplyDTO orderAfsApplyDTO = new OrderAfsApplyDTO();

                orderAfsApplyDTO.setChannelType(ChannelTypeEnum.YOU_ZAN.getCode());
                orderAfsApplyDTO.setChannelOrderId(result.getData().getTid());
                orderAfsApplyDTO.setAfterSaleId(result.getData().getRefundId());
                orderAfsApplyDTO.setRefundApplyTime(DateUtils.date2Millisecond(result.getData().getCreated()));
                orderAfsApplyDTO.setRefundPrice(MoneyUtils.yuanToFen(result.getData().getRefundFee()));
                if (result.getData().getReturnGoods()) {
                    orderAfsApplyDTO.setAfterSaleStatus(ChannelStatusConvertUtil.yzAfterSaleRefundGoodsTypeMapping(result.getData().getStatus()));
                } else {
                    orderAfsApplyDTO.setAfterSaleStatus(ChannelStatusConvertUtil.yzAfterSaleRefundAllRefundTypeMapping(result.getData().getStatus()));
                }

                orderAfsApplyDTO.setAfterSaleRecordStatus(ChannelStatusConvertUtil.yzAfsRecordMapping(result.getData().getStatus()));
                orderAfsApplyDTO.setChannelAfsStatus(result.getData().getStatus());
                orderAfsApplyDTO.setRefundAuditTime(DateUtils.date2Millisecond(result.getData().getModified()));

                // 用户描述
                String applyReasonDesc = result.getData().getDesc();

                String reason = YzOrderConverterUtil.covertToRefundReason(result.getData().getReason());

                if (!StringUtils.isBlank(applyReasonDesc)) {
                    reason = reason + "：" + applyReasonDesc;
                }

                orderAfsApplyDTO.setApplyReason(reason);

                if (!CollectionUtils.isEmpty(result.getData().getConsultMessage())) {
                    YouzanTradeRefundGetResult.YouzanTradeRefundGetResultConsultmessage cMessage = result.getData().getConsultMessage().get(result.getData().getConsultMessage().size() - 1);
                    if (!CollectionUtils.isEmpty(cMessage.getAttachment())) {
                        orderAfsApplyDTO.setRefundImgUrl(JSONArray.toJSONString(cMessage.getAttachment()));
                    }
                }

                // 客满介入状态： 1（客满未介入），2（客满介入中）。
                orderAfsApplyDTO.setIsAppeal(result.getData().getCsStatus() == 2);


                List<RefundProductDTO> afsProductList = converterService.convertRefundProductDTOs(result.getData().getRefundOrderItem());
                //获取退款方式 1仅退款 2退货退货 3换货
                Integer demand = result.getData().getDemand();
                // 查询订单，补充信息
                YouzanTradeGetParams tradeGetParams = new YouzanTradeGetParams();
                tradeGetParams.setTid(orderAfsApplyDTO.getChannelOrderId());
                YouzanTradeGetResult tradeGetResult = getResult4YouZan(appMessage, new YouzanTradeGet(tradeGetParams), YouzanTradeGetResult.class);
                decryptOrderMessage(appMessage, tradeGetResult.getData());
                log.info("YzToolChannelOrderServiceImpl.getOrderStatus, request:{}, result:{}", request,
                        GsonUtils.toJSONString(tradeGetResult));
                ChannelOrderDetailDTO channelOrderDetailDTO = getChannelOrderDetailDTO(tradeGetResult.getData(), appMessage);

                // 退款单中的包含的运费（元）
                String postage = result.getData().getRefundPostage();

                fillAfsProduct(channelOrderDetailDTO, afsProductList, MoneyUtils.nullableYuanToFen(postage), afterSaleApplyMap, applyType, orderAfsApplyDTO, demand);

                orderAfsApplyDTO.setAfsProductList(afsProductList);
                orderAfsApplyDTO.setServiceType(result.getData().getDemand());

                // 如果是用户行为直接跳过本次循环，类型已有值
                /*
                if(applyType == OcmsRefundSponsorEnum.CUSTOMER.getValue()){
                    retData.add(orderAfsApplyDTO);
                    log.info("有赞退单 orderAfsApplyDTO : {}", orderAfsApplyDTO);
                    continue;
                }
                log.info("退单 orderAfsApplyDTO : {}", orderAfsApplyDTO);
                if (checkAllRefund(channelOrderDetailDTO.getSkuDetails(), afsProductList)) {
                    orderAfsApplyDTO.setRefundType(RefundTypeEnum.ALL.getValue());
                } else {
                    orderAfsApplyDTO.setRefundType(RefundTypeEnum.PART.getValue());
                }
                */

                retData.add(orderAfsApplyDTO);
            }

            return ret.setStatus(ResultGenerator.genSuccessResult())
                    .setAfsApplyList(retData);
        } catch (SDKException e) {
            log.error("getOrderAfsApplyList error, request->{}, exception:", request, e);
            return ret.setStatus(ResultGenerator.genResult(ResultCode.AFTERSALE_NOT_EXIST));

        } catch (Exception e) {
            log.error("getOrderAfsApplyList error, exception:", e);
            return ret.setStatus(ResultGenerator.genResult(ResultCode.AFTERSALE_NOT_EXIST));
        }
    }

    private Map<String, AfterSaleApplyBODto> getAfterSaleApplyMap(List<AfterSaleApplyBODto> afterSaleApplyBODtoList) {
        if(CollectionUtils.isEmpty(afterSaleApplyBODtoList)){
            return new HashMap<>();
        }
        return afterSaleApplyBODtoList.stream().filter(item -> StringUtils.isNotBlank(item.getOrderItemId())).collect(Collectors.toMap(AfterSaleApplyBODto::getOrderItemId, AfterSaleApplyBODto->AfterSaleApplyBODto));
    }


    // 补充售后商品信息,
    private void fillAfsProduct(ChannelOrderDetailDTO channelOrderDetailDTO, List<RefundProductDTO> afsProductList, int freightRefundInFen, Map<String, AfterSaleApplyBODto> afterSaleApplyMap, int applyType, OrderAfsApplyDTO orderAfsApplyDTO, Integer demand) {
        if (afsProductList == null) {
            return;
        }

        Map<String, OrderProductDetailDTO> channelItemId2ProductDetail = channelOrderDetailDTO.getSkuDetails().stream().collect(Collectors.toMap(item -> {
            return item.getChannelItemId();
        }, part -> part));
        // 标记全单退类型 1 退款金额首次全单退； 2 退款剩余全部未退部分； 3 部分退 0 不处理 -1有问题
        log.info("有赞退单 channelOrderDetailDTO : {} ,有赞 afsProductList : {} ,有赞 afterSaleApplyMap : {} ,有赞 freightRefundInFen : {}", channelOrderDetailDTO, afsProductList, afterSaleApplyMap, freightRefundInFen);
        int refundTypeInt = getRefundTypeInt(channelOrderDetailDTO, afsProductList, afterSaleApplyMap, orderAfsApplyDTO);

        log.info("fillAfsProduct.refundTypeInt : {}", refundTypeInt);
        afsProductList.forEach(afsProduct -> {
            OrderProductDetailDTO orderProductDetailDTO = channelItemId2ProductDetail.get(afsProduct.getChannelOrderItemId());
            if (orderProductDetailDTO == null) {
                log.error("售后未能成功匹配订单商品，商品oid {}", afsProduct.getChannelOrderItemId());
                throw new RuntimeException("售后未能成功匹配订单商品");
            }

            afsProduct.setSkuId(orderProductDetailDTO.getChannelSkuId());
            afsProduct.setSkuName(orderProductDetailDTO.getSkuName());
            afsProduct.setSpec(orderProductDetailDTO.getSpecification());
            // 运费需要调整
            AfterSaleApplyBODto afterSaleApplyBODto = afterSaleApplyMap.get(afsProduct.getChannelOrderItemId());

            if(applyType == OcmsRefundSponsorEnum.CUSTOMER.getValue()){
                // 用户退单
                dealPartialRefundCountByUser(demand, afsProduct, orderProductDetailDTO, afterSaleApplyBODto, freightRefundInFen, channelOrderDetailDTO);
                afsProduct.setRefundCountDecimal(afsProduct.getPartialRefundCount());
                afsProduct.setCount(BigDecimal.valueOf(afsProduct.getRefundCountDecimal()).setScale(0,BigDecimal.ROUND_UP).intValue());
            }else {
                // 商家退单 添加channelOrderDetailDTO入参 用于计算是否是最后一个订单商品退款
                dealCountByStore(afsProduct, orderProductDetailDTO, afterSaleApplyBODto, refundTypeInt,channelOrderDetailDTO, freightRefundInFen);
            }
            dealPromotionAndFreight(afsProduct, afterSaleApplyBODto, orderProductDetailDTO, refundTypeInt, channelOrderDetailDTO, orderAfsApplyDTO, applyType, freightRefundInFen);

            /*if (afsProduct.getCount() == 0) {
                // 这里不一定准确，因为有赞单个商品的退款金额是可以商家自行填写的
                // 产品确认，向下取整

                // 退款金额-退款邮费 等于 该商品应退金额，除以该商品单价，近似算出退款件数
                BigDecimal countDecimal = new BigDecimal(afsProduct.getSkuRefundAmount() - freightRefundInFen).divide(new BigDecimal(orderProductDetailDTO.getSalePrice()));
                int count = countDecimal.setScale(0, BigDecimal.ROUND_DOWN).intValue(); // 向下取整

                if (count > orderProductDetailDTO.getQuantity()) {
                    count = orderProductDetailDTO.getQuantity();
                }

                afsProduct.setCount(count);
            }*/
        });
    }

    private void dealPromotionAndFreight(RefundProductDTO afsProduct, AfterSaleApplyBODto afterSaleApplyBODto, OrderProductDetailDTO orderProductDetailDTO, int refundTypeInt, ChannelOrderDetailDTO channelOrderDetailDTO, OrderAfsApplyDTO orderAfsApplyDTO, int applyType, int freightRefundInFen) {
        try {
            // 暂时不处理优惠
            afsProduct.setRefundPoiItemPromotion(0);
            afsProduct.setRefundPoiPromotion(0);
            // 商品重量
            long weight = orderProductDetailDTO.getWeight() * orderProductDetailDTO.getQuantity();
            // 实付金额
            int userTotalPayPrice = orderProductDetailDTO.getTotalPayAmtMinusDiscount();
            // 退单金额
            int skuRefundAmount = afsProduct.getSkuRefundAmount();
            //历史退单金额
            int oldPayAmt = 0;
            double oldWeight = 0d;
            if(Objects.nonNull(afterSaleApplyBODto)){
                oldPayAmt = afterSaleApplyBODto.getPayAmt();
                oldWeight = afterSaleApplyBODto.getWeight();

            }
            // 明细中的运费，包装费
            int orderFreight = channelOrderDetailDTO.getFreight();
            int orderPackageAmt = channelOrderDetailDTO.getPackageAmt();

            // 如果历史退款等于或者超过了实际商品支付金额，表示包装费或者运费多次退款，sku退款金额为0
            if (oldPayAmt >= orderProductDetailDTO.getTotalPayAmtMinusDiscount()) {
                // 商品sku退费为0 参与下面重量计算
                afsProduct.setSkuRefundAmount(0);
                // 设置运费退款金额
                orderAfsApplyDTO.setFreight(freightRefundInFen);
                // 设置包装费退款金额为 申请金额 - 运费退款金额
                orderAfsApplyDTO.setPayPackageFee(skuRefundAmount - freightRefundInFen);
                log.info("有赞退单 历史退款大于等于实际商品支付金额orderId:{}, afterSaleId:{}, skuRefundAmount: {}，处理后skuRefundAmount: {}", channelOrderDetailDTO.getChannelOrderId(), orderAfsApplyDTO.getAfterSaleId(), skuRefundAmount, afsProduct.getSkuRefundAmount());
            } else if ((afsProduct.getSkuRefundAmount() + oldPayAmt) >= orderProductDetailDTO.getTotalPayAmtMinusDiscount()) {
                // 历史金额 + 退单金额 >= 实际支付金额时, skuRefundAmount = 实际支付金额 - 历史退单金额
                afsProduct.setSkuRefundAmount(orderProductDetailDTO.getTotalPayAmtMinusDiscount() - oldPayAmt);
                // 设置运费退款金额
                orderAfsApplyDTO.setFreight(freightRefundInFen);
                // 设置包装费退款金额为 申请金额 + 历史退款金额 - 实际商品支付金额 - 运费退款金额
                orderAfsApplyDTO.setPayPackageFee(skuRefundAmount + oldPayAmt - orderProductDetailDTO.getTotalPayAmtMinusDiscount() - freightRefundInFen);
                log.info("有赞退单 历史金额加上退单金额大于等于实际支付金额时的orderId:{}, afterSaleId:{}, skuRefundAmount: {}，处理后skuRefundAmount: {}", channelOrderDetailDTO.getChannelOrderId(), orderAfsApplyDTO.getAfterSaleId(), skuRefundAmount, afsProduct.getSkuRefundAmount());
            }  else if(checkLast(afsProduct, channelOrderDetailDTO, freightRefundInFen)) {
                // 如果是最后一条，并且运费大于0， 说明是退货退款部分退中含有运费， 需要直接减去运费
                afsProduct.setSkuRefundAmount(skuRefundAmount - freightRefundInFen);
                // 设置运费退款金额
                orderAfsApplyDTO.setFreight(freightRefundInFen);
                // 设置包装费退款金额为0
                orderAfsApplyDTO.setPayPackageFee(0);
                log.info("有赞退单 最后一条，并且运费大于0 orderId:{}, afterSaleId:{}, skuRefundAmount: {}，处理后skuRefundAmount: {}", channelOrderDetailDTO.getChannelOrderId(), orderAfsApplyDTO.getAfterSaleId(), skuRefundAmount, afsProduct.getSkuRefundAmount());
            }
            if(orderAfsApplyDTO.getPayPackageFee() > orderPackageAmt){
                // 如果包装费超出了订单包装费，记录下
                log.info("有赞退单 包装费超出了订单包装费orderId:{}, afterSaleId:{}, payPackageFee: {}", channelOrderDetailDTO.getChannelOrderId(), orderAfsApplyDTO.getAfterSaleId(), orderAfsApplyDTO.getPayPackageFee());
            }
            // 退单重量 =（退款商品金额/正单开票金额）* 下单订单行重量
            double refundWeight = BigDecimal.valueOf(afsProduct.getSkuRefundAmount())
                    .multiply(BigDecimal.valueOf(weight))
                    .divide(BigDecimal.valueOf(orderProductDetailDTO.getSalePrice() * orderProductDetailDTO.getQuantity() - orderProductDetailDTO.getPlatPromotion() - orderProductDetailDTO.getPoiPromotion()),
                            0, RoundingMode.HALF_UP)
                    .doubleValue();
            afsProduct.setRefundWeight(refundWeight);
        }catch (Exception e){
            log.info("YzToolChannelOrderServiceImpl.dealPromotion error : ", e);
        }
    }

    private boolean isContainFreight(int applyType, int skuRefundAmount, int orderFreight, int orderPackageAmt, int oldPayAmt, int salePrice, int freightRefundInFen) {
        boolean isTrue = (applyType == OcmsRefundSponsorEnum.CUSTOMER.getValue() && freightRefundInFen > 0) || (applyType != OcmsRefundSponsorEnum.CUSTOMER.getValue() && (skuRefundAmount + oldPayAmt) == (salePrice + orderFreight + orderPackageAmt));
        return isTrue;
    }

    private void dealCountByStore(RefundProductDTO afsProduct, OrderProductDetailDTO orderProductDetailDTO, AfterSaleApplyBODto afterSaleApplyBODto, int refundTypeInt, ChannelOrderDetailDTO channelOrderDetailDTO, int freightRefundInFen) {
        // 商家行为时 第一次全部退
        try {
            BigDecimal oldRefundCountDecimal = new BigDecimal(0);
            if(refundTypeInt == YZAfterSaleRefundTypeEnum.FIRST_REFUND_ALL.getValue()){
                afsProduct.setCount(orderProductDetailDTO.getQuantity());
                afsProduct.setRefundCountDecimal(orderProductDetailDTO.getQuantity());
            }else if(refundTypeInt == YZAfterSaleRefundTypeEnum.TWO_REFUND_ALL.getValue()) {
                // 发生过部分退需要根据历史扣减 向上取整
                BigDecimal totalReturnNum = new BigDecimal(0);
                if(Objects.nonNull(afterSaleApplyBODto)){
                    totalReturnNum = new BigDecimal(StringUtils.isNotBlank(afterSaleApplyBODto.getCount()) ? afterSaleApplyBODto.getCount() : "0");
                    oldRefundCountDecimal = new BigDecimal(StringUtils.isNotBlank(afterSaleApplyBODto.getRefundCountDecimal()) ? afterSaleApplyBODto.getRefundCountDecimal() : "0");
                }
                afsProduct.setCount((new BigDecimal(orderProductDetailDTO.getQuantity()).subtract(totalReturnNum)).setScale(0, BigDecimal.ROUND_UP).intValue());
                afsProduct.setRefundCountDecimal((new BigDecimal(orderProductDetailDTO.getQuantity()).subtract(oldRefundCountDecimal)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            }else {
                //部分退时
                //判断是否是最后一个商品退款，只有渠道发起的最后一个商品退款会添加运费
                BigDecimal actualPrice=BigDecimal.valueOf(orderProductDetailDTO.getTotalPayAmtMinusDiscount()).divide(BigDecimal.valueOf(orderProductDetailDTO.getQuantity()), 0, BigDecimal.ROUND_HALF_UP);
                BigDecimal refundCountDecimal = new BigDecimal(afsProduct.getSkuRefundAmount()).divide(actualPrice, 2, BigDecimal.ROUND_HALF_UP);
                int oldPayAmt = 0;
                if(Objects.nonNull(afterSaleApplyBODto)){
                    oldPayAmt = afterSaleApplyBODto.getPayAmt();
                }

                // 如果历史退款等于或者超过了实际商品支付金额，表示包装费或者运费多次退款，数量设为0
                if (oldPayAmt >= orderProductDetailDTO.getTotalPayAmtMinusDiscount()) {
                    refundCountDecimal = BigDecimal.ZERO;
                    // 历史金额 + 退单金额 >= 实际支付金额时, 退单数量 = (实际支付金额 - 历史退单金额) / 实际单商品支付金额
                } else if ((afsProduct.getSkuRefundAmount() + oldPayAmt) >= orderProductDetailDTO.getTotalPayAmtMinusDiscount()) {
                    refundCountDecimal = (BigDecimal.valueOf(orderProductDetailDTO.getTotalPayAmtMinusDiscount()).subtract(BigDecimal.valueOf(oldPayAmt))).divide(actualPrice, 2, BigDecimal.ROUND_HALF_UP);
                } else if(checkLast(afsProduct, channelOrderDetailDTO, freightRefundInFen)) {
                    // 如果是最后一条，并且运费大于0， 说明是退货退款部分退中含有运费， 需要直接减去运费
                    refundCountDecimal = new BigDecimal(afsProduct.getSkuRefundAmount() - freightRefundInFen).divide(actualPrice, 2, BigDecimal.ROUND_HALF_UP);
                }
                afsProduct.setRefundCountDecimal(refundCountDecimal.doubleValue());
                afsProduct.setCount(refundCountDecimal.setScale(0, BigDecimal.ROUND_UP).intValue());
            }
            afsProduct.setPartialRefundCount(afsProduct.getCount());
        }catch (Exception e){
            log.info("YzToolChannelOrderServiceImpl.dealCountByStore error : ", e);
        }
    }

    private void dealPartialRefundCountByUser(Integer demand, RefundProductDTO afsProduct, OrderProductDetailDTO orderProductDetailDTO, AfterSaleApplyBODto afterSaleApplyBODto, int freightRefundInFen, ChannelOrderDetailDTO channelOrderDetailDTO) {
        try {
            // 退货退款直接取渠道返回的数量
            if(demand == YZAfterSaleRefundMethodEnum.REFUND_GOODS_MONEY.getValue()){
                afsProduct.setPartialRefundCount(afsProduct.getCount());
                return;
            }
            // 订单明细的实付金额
            int billValue = orderProductDetailDTO.getTotalPayAmtMinusDiscount();
            // 相等时 skuRefundAmount 退款包含运费时 渠道会放入运费
            int returnValue = afsProduct.getSkuRefundAmount();
            // 订单中的运费
            int freight = channelOrderDetailDTO.getFreight();
            // 订单中的包装费
            int packageAmt = channelOrderDetailDTO.getPackageAmt();
            // 本件商品总购买数量
            int quantity = orderProductDetailDTO.getQuantity();

            //退单金额和实际支付金额相等时
            if(returnValue == billValue){
                afsProduct.setPartialRefundCount(quantity);
                return;
            }
            // 退单金额 == 实际支付金额 + 运费 + 包装费时, 说明是这个订单的最后一笔退款
            if(returnValue == (billValue + freight + packageAmt)){
                afsProduct.setPartialRefundCount(quantity);
                return;
            }
            // 计算实际单价
            BigDecimal actualPrice = BigDecimal.valueOf(billValue).divide(BigDecimal.valueOf(quantity), 0, BigDecimal.ROUND_HALF_UP);
            // 累加已有的退单金额
            int totalReturnValue = 0;
            // 改订单第一次进入
            if(Objects.nonNull(afterSaleApplyBODto)){
                // 累加已有的退单金额
                totalReturnValue = afterSaleApplyBODto.getPayAmt();
                // 累加已有的退货数量
                double totalReturnNum = new BigDecimal(StringUtils.isNotBlank(afterSaleApplyBODto.getCount()) ? afterSaleApplyBODto.getCount() : "0").doubleValue();
                if((totalReturnValue + returnValue == billValue) || (totalReturnValue + returnValue == billValue + freight + packageAmt)){
                    double qty = new BigDecimal(quantity).subtract(BigDecimal.valueOf(totalReturnNum)).doubleValue();
                    if (qty < 0d) {
                        qty = 0d;
                    }
                    log.info("有赞数量统计 累计金额或者数量相等时 qty : {}", qty);
                    afsProduct.setPartialRefundCount(qty);
                    return;
                }
            }

            // 正常情况，使用退款金额计算退单数量
            afsProduct.setPartialRefundCount(new BigDecimal(returnValue).divide(actualPrice, 2, BigDecimal.ROUND_HALF_UP).doubleValue());
            // 如果历史退单金额超过了订单商品实付金额，相当于退的是运费或者包装费，退单数量设为0
            if(totalReturnValue >= billValue) {
                afsProduct.setPartialRefundCount(0d);
            } else if((returnValue + totalReturnValue) >= billValue) {
                // 如果申请退款金额 + 历史退单金额超过了订单商品实付金额，参与数量计算的商品退款金额为商品实付金额 - 历史退单金额
                afsProduct.setPartialRefundCount(new BigDecimal(billValue - totalReturnValue).divide(actualPrice, 2, BigDecimal.ROUND_HALF_UP).doubleValue());
            } else if(checkLast(afsProduct, channelOrderDetailDTO, freightRefundInFen)) {
                // 如果是最后一条，并且运费大于0， 说明是退货退款部分退中含有运费， 需要直接减去运费
                afsProduct.setPartialRefundCount(new BigDecimal(returnValue - freightRefundInFen).divide(actualPrice, 2, BigDecimal.ROUND_HALF_UP).doubleValue());
            }
        }catch (Exception e){
            afsProduct.setPartialRefundCount(0d);
            log.info("YzToolChannelOrderServiceImpl.dealPartialRefundCount error : ", e);
        }
    }

    private boolean checkLast(RefundProductDTO afsProduct,  ChannelOrderDetailDTO channelOrderDetailDTO, int freightRefundInFen){
        // 判断是否是最后一个
        return Objects.equals(channelOrderDetailDTO.getSkuDetails().get(channelOrderDetailDTO.getSkuDetails().size() - 1).getChannelItemId(), afsProduct.getChannelOrderItemId())
         && freightRefundInFen > 0;
    }

    private int getRefundTypeInt(ChannelOrderDetailDTO channelOrderDetailDTO, List<RefundProductDTO> afsProductList, Map<String, AfterSaleApplyBODto> afterSaleApplyMap, OrderAfsApplyDTO orderAfsApplyDTO) {
        try {
            int actualPayAmt = channelOrderDetailDTO.getActualPayAmt();

            int refundAmt = 0;
            for(RefundProductDTO dto : afsProductList){
                refundAmt += dto.getSkuRefundAmount();
            }

            // 一次性全单退
            if(refundAmt >= actualPayAmt){
                orderAfsApplyDTO.setRefundType(RefundTypeEnum.ALL.getValue());
                return YZAfterSaleRefundTypeEnum.FIRST_REFUND_ALL.getValue();
            }
            // 历史退单总金额 + 本次退款总金额 相等时证明是最后的退款数量 也是全单退
            int oldRefundAmt = 0;
            if(Objects.nonNull(afterSaleApplyMap) && afterSaleApplyMap.size() > 0){
                for (Map.Entry< String, AfterSaleApplyBODto> entry : afterSaleApplyMap.entrySet()){
                    AfterSaleApplyBODto value = entry.getValue();
                    oldRefundAmt += value.getPayAmt();
                    oldRefundAmt += value.getPayFreightAmt();
                }
            }
            if((oldRefundAmt + refundAmt) >= actualPayAmt){
                orderAfsApplyDTO.setRefundType(RefundTypeEnum.ALL.getValue());
                return YZAfterSaleRefundTypeEnum.TWO_REFUND_ALL.getValue();
            }
        }catch (Exception e){
            log.info("获取有赞退款类型 YzToolChannelOrderServiceImpl.getRefundTypeInt error : ", e);
        }
        // 部分退
        orderAfsApplyDTO.setRefundType(RefundTypeEnum.PART.getValue());
        return YZAfterSaleRefundTypeEnum.PART_REFUND.getValue();

    }


    // 判断全单退还是部分退
    private boolean checkAllRefund(List<OrderProductDetailDTO> productDetailDTOList, List<RefundProductDTO> afsProductList) {
        if (CollectionUtils.isEmpty(afsProductList)) {
            return false;
        }

        Map<String, RefundProductDTO> channelItemId2AfsProduct = afsProductList.stream().collect(Collectors.toMap(item -> {
            return item.getChannelOrderItemId();
        }, part -> part));


        for (OrderProductDetailDTO productDetailDTO : productDetailDTOList) {
            RefundProductDTO refundProductDTO = channelItemId2AfsProduct.get(productDetailDTO.getChannelItemId());
            if (refundProductDTO == null || refundProductDTO.getCount() != productDetailDTO.getQuantity()) {
                return false;
            }
        }

        return true;
    }


    private Long fetchRefundVersion(AppMessage appMessage, String refundId) throws SDKException {
        YouzanTradeRefundGetParams params = new YouzanTradeRefundGetParams();
        params.setRefundId(refundId);
        YouzanTradeRefundGetResult youzanTradeRefundGetResult = getResult4YouZan(appMessage, new YouzanTradeRefundGet(params), YouzanTradeRefundGetResult.class);

        if (!youzanTradeRefundGetResult.getSuccess() || youzanTradeRefundGetResult.getData() == null || StringUtils.isBlank(youzanTradeRefundGetResult.getData().getStatus())) {
            log.error("查询退款version失败");
            return null;
        }

        return youzanTradeRefundGetResult.getData().getVersion();
    }

    @Override
    public ResultStatus agreeRefund(AgreeRefundRequest request) {
        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getStoreId());


            YouzanTradeRefundAgreeParams youzanTradeRefundAgreeParams = new YouzanTradeRefundAgreeParams();
            youzanTradeRefundAgreeParams.setRefundId(request.getAfterSaleId());
            youzanTradeRefundAgreeParams.setVersion(fetchRefundVersion(appMessage, request.getAfterSaleId()));
            YouzanTradeRefundAgreeResult youzanTradeRefundAgreeResult = getResult4YouZan(appMessage, new YouzanTradeRefundAgree(youzanTradeRefundAgreeParams), YouzanTradeRefundAgreeResult.class);

            if (youzanTradeRefundAgreeResult.getSuccess()) {
                return ResultGenerator.genResult(ResultCode.SUCCESS);
            } else {
                return ResultGenerator.genResult(ResultCode.FAIL, youzanTradeRefundAgreeResult.getMessage());
            }
        } catch (SDKException e) {
            log.error("agreeRefund error, request->{}, exception:", request, e);
            return ResultGenerator.genResult(ResultCode.FAIL);

        } catch (Exception e) {
            log.error("agreeRefund error, exception:", e);
            return ResultGenerator.genResult(ResultCode.FAIL);
        }
    }

    @Override
    public ResultStatus rejectRefund(RejectRefundRequest request) {
        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getStoreId());

            YouzanTradeRefundRefuseParams youzanTradeRefundRefuseParams = new YouzanTradeRefundRefuseParams();
            youzanTradeRefundRefuseParams.setRefundId(request.getAfterSaleId());
            youzanTradeRefundRefuseParams.setVersion(fetchRefundVersion(appMessage, request.getAfterSaleId()));
            youzanTradeRefundRefuseParams.setRemark(StringUtils.isBlank(request.getReason()) ? "审核原因未填写" : request.getReason());
            YouzanTradeRefundRefuseResult youzanTradeRefundRefuseResult = getResult4YouZan(appMessage, new YouzanTradeRefundRefuse(youzanTradeRefundRefuseParams), YouzanTradeRefundRefuseResult.class);

            if (youzanTradeRefundRefuseResult.getSuccess()) {
                return ResultGenerator.genResult(ResultCode.SUCCESS);
            } else {
                return ResultGenerator.genResult(ResultCode.FAIL, youzanTradeRefundRefuseResult.getMessage());
            }
        } catch (SDKException e) {
            log.error("rejectRefund error, request->{}, exception:", request, e);
            return ResultGenerator.genResult(ResultCode.FAIL);

        } catch (Exception e) {
            log.error("rejectRefund error, exception:", e);
            return ResultGenerator.genResult(ResultCode.FAIL);
        }
    }

    @Override
    public ResultStatus refundGoods(RefundGoodsRequest request) {
        ResultStatus result = new ResultStatus();
        try {
            AfsReviewTypeEnum reviewType = AfsReviewTypeEnum.enumOf(request.getReviewType());

            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getStoreId());

            String reason = StringUtils.isBlank(request.getReason()) ? "审核原因未填写" : request.getReason();

            switch (reviewType) {
                case AGREE_REFUND_GOODS:
                    // 同意退货(一审同意)

                    PoiSearchRequest poiSearchRequest = new PoiSearchRequest();
                    poiSearchRequest.setTenantId(request.getTenantId());
                    poiSearchRequest.setPoiIdList(Lists.newArrayList(request.getStoreId()));
                    poiSearchRequest.setPageNum(1);
                    poiSearchRequest.setPageSize(1);

                    PoiSearchResponse poiSearchResponse = poiThriftService.search(poiSearchRequest);

                    if (CollectionUtils.isEmpty(poiSearchResponse.getPoiList()) || poiSearchResponse.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                        log.error("查询门店收货信息失败，request:{}, response:{}", poiSearchRequest, poiSearchResponse);
                        return result.setCode(ResultCode.FAIL.getCode()).setMsg("查询门店收货信息失败");
                    }

                    PoiInfoDto poiInfoDto = poiSearchResponse.getPoiList().get(0);

                    YouzanTradeReturngoodsAgreeParams youzanTradeReturngoodsAgreeParams = new YouzanTradeReturngoodsAgreeParams();

                    youzanTradeReturngoodsAgreeParams.setRefundId(request.getAfterSaleId());
                    youzanTradeReturngoodsAgreeParams.setVersion(fetchRefundVersion(appMessage, request.getAfterSaleId()));
                    // youzanTradeReturngoodsAgreeParams.setRemark(reason);

                    youzanTradeReturngoodsAgreeParams.setName(poiInfoDto.getManager());
                    youzanTradeReturngoodsAgreeParams.setMobile(poiInfoDto.getMobile());
                    youzanTradeReturngoodsAgreeParams.setAddress(poiInfoDto.getPoiAddress());

                    YouzanTradeReturngoodsAgreeResult youzanTradeReturngoodsAgreeResult = getResult4YouZan(appMessage, new YouzanTradeReturngoodsAgree(youzanTradeReturngoodsAgreeParams), YouzanTradeReturngoodsAgreeResult.class);

                    if (youzanTradeReturngoodsAgreeResult.getSuccess()) {
                        result.setCode(ResultCode.SUCCESS.getCode());
                    } else {
                        result.setCode(ResultCode.FAIL.getCode()).setMsg(youzanTradeReturngoodsAgreeResult.getMessage());
                    }

                    break;
                case REJECT_REFUND:
                    // 驳回退货/退款
                    if (request.getAuditStage() == AfsAuditStageEnum.FINAL_AUDIT.getValue()) {
                        //售后终审审审批驳回
                        YouzanTradeReturngoodsRefuseParams youzanTradeReturngoodsRefuseParams = new YouzanTradeReturngoodsRefuseParams();
                        youzanTradeReturngoodsRefuseParams.setRefundId(request.getAfterSaleId());
                        youzanTradeReturngoodsRefuseParams.setRemark(reason);
                        youzanTradeReturngoodsRefuseParams.setVersion(fetchRefundVersion(appMessage, request.getAfterSaleId()));

                        YouzanTradeReturngoodsRefuseResult youzanTradeRefundRefuseResult = getResult4YouZan(appMessage, new YouzanTradeReturngoodsRefuse(youzanTradeReturngoodsRefuseParams), YouzanTradeReturngoodsRefuseResult.class);

                        if (youzanTradeRefundRefuseResult.getSuccess()) {
                            result.setCode(ResultCode.SUCCESS.getCode());
                        } else {
                            result.setCode(ResultCode.FAIL.getCode()).setMsg(youzanTradeRefundRefuseResult.getMessage());
                        }

                    } else {
                        //售后初审审批驳回
                        YouzanTradeRefundRefuseParams youzanTradeRefundRefuseParams = new YouzanTradeRefundRefuseParams();
                        youzanTradeRefundRefuseParams.setRefundId(request.getAfterSaleId());
                        youzanTradeRefundRefuseParams.setVersion(fetchRefundVersion(appMessage, request.getAfterSaleId()));
                        youzanTradeRefundRefuseParams.setRemark(reason);
                        YouzanTradeRefundRefuseResult youzanTradeRefundRefuseResult = getResult4YouZan(appMessage, new YouzanTradeRefundRefuse(youzanTradeRefundRefuseParams), YouzanTradeRefundRefuseResult.class);

                        if (youzanTradeRefundRefuseResult.getSuccess()) {
                            result.setCode(ResultCode.SUCCESS.getCode());
                        } else {
                            result.setCode(ResultCode.FAIL.getCode()).setMsg(youzanTradeRefundRefuseResult.getMessage());
                        }
                    }
                    break;
                case AGREE_REFUND:
                    // 同意退款 (终审通过)
                    YouzanTradeRefundAgreeParams youzanTradeRefundAgreeParams = new YouzanTradeRefundAgreeParams();
                    youzanTradeRefundAgreeParams.setRefundId(request.getAfterSaleId());
                    youzanTradeRefundAgreeParams.setVersion(fetchRefundVersion(appMessage, request.getAfterSaleId()));
                    YouzanTradeRefundAgreeResult youzanTradeRefundAgreeResult = getResult4YouZan(appMessage, new YouzanTradeRefundAgree(youzanTradeRefundAgreeParams), YouzanTradeRefundAgreeResult.class);

                    if (youzanTradeRefundAgreeResult.getSuccess()) {
                        result.setCode(ResultCode.SUCCESS.getCode());
                    } else {
                        result.setCode(ResultCode.FAIL.getCode()).setMsg(youzanTradeRefundAgreeResult.getMessage());
                    }

                    break;
                default:
                    log.error("refundGoods reviewType not found!");
                    result.setCode(ResultCode.FAIL.getCode()).setMsg("没有对应的退货退款审核类型");
                    break;
            }

            return result;
        } catch (SDKException e) {
            log.error("refundGoods error, request->{}, exception:", request, e);
            return ResultGenerator.genResult(ResultCode.FAIL);
        } catch (Exception e) {
            log.error("refundGoods error, exception:", e);
            return ResultGenerator.genResult(ResultCode.FAIL);
        }
    }

    @Override
    public ResultStatus poiCancelOrder(PoiCancelOrderRequest request) {
        // 不支持，用部分退替代
        return null;
    }


    @Override
    public GetSelfFetchCodeResult checkSelfFetchCode(SelfFetchCodeGetRequest request){
        GetSelfFetchCodeResult ret = new GetSelfFetchCodeResult();
        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getStoreId());
            YouzanTradeSelffetchcodeGetParams youzanTradeSelffetchcodeGetParams = new YouzanTradeSelffetchcodeGetParams();
            youzanTradeSelffetchcodeGetParams.setCode(request.getCode());
            YouzanTradeSelffetchcodeGetResult youzanTradeSelffetchcodeGetResult = getResult4YouZan(appMessage, new YouzanTradeSelffetchcodeGet(youzanTradeSelffetchcodeGetParams), YouzanTradeSelffetchcodeGetResult.class);
            if (Objects.nonNull(youzanTradeSelffetchcodeGetResult.getResponse())) {
                GetSelfFetchCodeDTO getSelfFetchCodeDTO = new GetSelfFetchCodeDTO();
                getSelfFetchCodeDTO.setChannelOrderId(youzanTradeSelffetchcodeGetResult.getResponse().getTid());
                if(Objects.nonNull(youzanTradeSelffetchcodeGetResult.getResponse().getSelfFetchState())){
                    getSelfFetchCodeDTO.setSelfFetchStatus(youzanTradeSelffetchcodeGetResult.getResponse().getSelfFetchState());
                }
                return ret.setStatus(ResultGenerator.genSuccessResult())
                        .setGetSelfFetchCodeDTO(getSelfFetchCodeDTO);
            }
            if(Objects.nonNull(youzanTradeSelffetchcodeGetResult.getErrorResponse())){
                return ret.setStatus(ResultGenerator.genFailResult(youzanTradeSelffetchcodeGetResult.getErrorResponse().getMsg()));
            }
        }catch (SDKException e){
            log.info("checkSelfFetchCode error, request->{}, exception:", request, e);
            return ret.setStatus(ResultGenerator.genResult(ResultCode.FAIL));
        }catch (Exception e){
            log.info("checkSelfFetchCode error, exception:", e);
            return ret.setStatus(ResultGenerator.genResult(ResultCode.FAIL));
        }
        return ret.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST));
    }

    @Override
    public ResultStatus verifySelfFetchCode(VerifySelfFetchCodeRequest request) {
        ResultStatus result = new ResultStatus();
        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getStoreId());
            YouzanTradeSelffetchcodeApply youzanTradeSelffetchcodeApply = new YouzanTradeSelffetchcodeApply();
            YouzanTradeSelffetchcodeApplyParams tradeSelffetchcodeApplyParams = new YouzanTradeSelffetchcodeApplyParams();
            tradeSelffetchcodeApplyParams.setExtraInfo(request.getOperator());
            tradeSelffetchcodeApplyParams.setCode(request.getCode());
            youzanTradeSelffetchcodeApply.setAPIParams(tradeSelffetchcodeApplyParams);
            YouzanTradeSelffetchcodeApplyResult tradeSelffetchcodeGetResult = getResult4YouZan(appMessage, new YouzanTradeSelffetchcodeApply(tradeSelffetchcodeApplyParams), YouzanTradeSelffetchcodeApplyResult.class);
            // 核销失败，根据核销码查询订单是否已经核销
            if (Objects.nonNull(tradeSelffetchcodeGetResult.getErrorResponse())) {
                SelfFetchCodeGetRequest selfFetchCodeGetRequest = new SelfFetchCodeGetRequest();
                selfFetchCodeGetRequest.setChannelId(request.getChannelId());
                selfFetchCodeGetRequest.setCode(request.getCode());
                selfFetchCodeGetRequest.setStoreId(request.getStoreId());
                selfFetchCodeGetRequest.setTenantId(request.getTenantId());
                GetSelfFetchCodeResult getSelfFetchCodeResult = checkSelfFetchCode(selfFetchCodeGetRequest);
                if (getSelfFetchCodeResult.getStatus().equals(ResultCode.SUCCESS)
                        && Objects.nonNull(getSelfFetchCodeResult.getSelfFetchCodeDTO)
                        && Objects.equals(JDSelfFetchStatusEnum.VERIFY.getCode(), getSelfFetchCodeResult.getSelfFetchCodeDTO.getSelfFetchStatus())) {
                    return result.setCode(ResultCode.SUCCESS.getCode());
                }
                return result.setCode(ResultCode.FAIL.getCode()).setMsg(tradeSelffetchcodeGetResult.getErrorResponse().getMsg());
            }else if(Objects.nonNull(tradeSelffetchcodeGetResult.getResponse()) && tradeSelffetchcodeGetResult.getResponse().getIsSuccess()){
                return result.setCode(ResultCode.SUCCESS.getCode());
            }
        } catch (SDKException e) {
            log.info("checkSelfFetchCode error, request->{}, exception:", request, e);
        } catch (Exception e) {
            log.info("checkSelfFetchCode error, exception:", e);
        }
        return ResultGenerator.genResult(ResultCode.FAIL);
    }


    /**
     * 卖家标记签收
     * @param request
     * @return
     */
    @Override
    public ResultStatus markSignByOrderId(MarkSignByOrderIdRequest request) {
        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getStoreId());
            YouzanLogisticsOnlineMarksignParams logisticsOnlineMarksignParams = new YouzanLogisticsOnlineMarksignParams();
            logisticsOnlineMarksignParams.setTid(request.getOrderId());
            YouzanLogisticsOnlineMarksignResult logisticsOnlineMarksignResult = getResult4YouZan(appMessage, new YouzanLogisticsOnlineMarksign(logisticsOnlineMarksignParams), YouzanLogisticsOnlineMarksignResult.class);
            if(Objects.nonNull(logisticsOnlineMarksignResult.getResponse()) && BooleanUtils.isTrue(logisticsOnlineMarksignResult.getResponse().getIsSuccess())){
                return ResultGenerator.genResult(ResultCode.SUCCESS);
            }
        }catch (Exception e){
            log.info("checkSelfFetchCode error, exception:", e);
        }
        return ResultGenerator.genResult(ResultCode.FAIL);
    }

    @Override
    public ResultStatus poiPartRefundApply(PoiPartRefundRequest request) {
        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getStoreId());

            YouzanTradeRefundSearchParams youzanTradeRefundSearchParams = new YouzanTradeRefundSearchParams();
            youzanTradeRefundSearchParams.setTid(request.getOrderId());
            YouzanTradeRefundSearchResult youzanTradeRefundSearchResult = getResult4YouZan(appMessage, new YouzanTradeRefundSearch(youzanTradeRefundSearchParams), YouzanTradeRefundSearchResult.class);

            if (!youzanTradeRefundSearchResult.getSuccess() || youzanTradeRefundSearchResult.getData() == null) {
                log.error("查询有赞订单关联的售后单失败：{}", youzanTradeRefundSearchResult.getMessage());
                return ResultGenerator.genFailResult("查询订单关联的售后单失败：{}", youzanTradeRefundSearchResult.getMessage());
            }

            List<String> afterSaleIds = Optional.ofNullable(youzanTradeRefundSearchResult.getData().getRefunds()).orElse(Collections.emptyList()).stream().map(item -> {
                return item.getRefundId();
            }).collect(Collectors.toList());

            List<YouzanTradeRefundGetResult.YouzanTradeRefundGetResultData> allAfterSales = new ArrayList<>();

            for (String afterSaleId : afterSaleIds) {
                YouzanTradeRefundGetParams youzanTradeRefundGetParams = new YouzanTradeRefundGetParams();
                youzanTradeRefundGetParams.setRefundId(afterSaleId);
                YouzanTradeRefundGetResult youzanTradeRefundGetResult = getResult4YouZan(appMessage, new YouzanTradeRefundGet(youzanTradeRefundGetParams), YouzanTradeRefundGetResult.class);

                if (!youzanTradeRefundGetResult.getSuccess() || youzanTradeRefundGetResult.getData() == null || StringUtils.isBlank(youzanTradeRefundGetResult.getData().getStatus())) {
                    log.error("部分退款前查询有赞订单关联的售后单失败：{}", youzanTradeRefundSearchResult.getMessage());
                    return ResultGenerator.genFailResult("部分退款前查询关联售后单失败：{}", youzanTradeRefundSearchResult.getMessage());
                }

                allAfterSales.add(youzanTradeRefundGetResult.getData());
            }

            // 计算单个商品项 已经退款 和 正在退款 和 申诉中 的 总金额


            List<YouzanTradeRefundGetResult.YouzanTradeRefundGetResultData> refundingAndRefundedAfterSales = allAfterSales.stream()
                    .filter(
                            aftersale ->
                                    VALID_REFUND_STATUS.contains(aftersale.getStatus()) || aftersale.getCsStatus() == 2
                    )
                    .collect(Collectors.toList());

            // 已经退款和正在退款的金额
            Map<String, Long> refundingAndRefundedItems = refundingAndRefundedAfterSales.stream()
                    .flatMap(afterSale -> afterSale.getRefundOrderItem().stream())
                    .collect(Collectors.toMap(
                                    key -> key.getOid(),
                                    value -> value.getRefundFee(),
                                    (v1, v2) -> v1 + v2
                            )
                    );

            // 已退运费
            int refundedFreightInFen = 0;

            for (YouzanTradeRefundGetResult.YouzanTradeRefundGetResultData refundingAfterSale : refundingAndRefundedAfterSales) {
                refundedFreightInFen = MoneyUtils.nullableYuanToFen(refundingAfterSale.getRefundPostage());
                // 目前有赞只会在一个退款单里面有退运费
                if (refundedFreightInFen > 0) {
                    break;
                }
            }


            YouzanTradeRefundSellerActiveParams youzanTradeRefundSellerActiveParams = new YouzanTradeRefundSellerActiveParams();
            youzanTradeRefundSellerActiveParams.setTid(request.getOrderId());
            youzanTradeRefundSellerActiveParams.setDesc(StringUtils.isBlank(request.getReason()) ? "未填写主动退款原因" : request.getReason());

            List<YouzanTradeRefundSellerActiveParams.YouzanTradeRefundSellerActiveParamsItems> items = new ArrayList<>();

            AtomicReference<BigDecimal> refundTotalFee = new AtomicReference<>(new BigDecimal(0));

            request.getRefundProducts().forEach(refundProductInfoDTO -> {
                YouzanTradeRefundSellerActiveParams.YouzanTradeRefundSellerActiveParamsItems item = new YouzanTradeRefundSellerActiveParams.YouzanTradeRefundSellerActiveParamsItems();

                item.setOid(Long.valueOf(refundProductInfoDTO.getChannelOrderItemId()));

                // 金额校验

                // 已退金额（分）
                int refundedAmount = refundingAndRefundedItems.getOrDefault(refundProductInfoDTO.getChannelOrderItemId(), 0L).intValue();


                // 总付款金额（分）
                int totalPayAmtMinusDiscount = refundProductInfoDTO.getTotalPayAmtMinusDiscount();

                // 最多可退（分）
                int maxRefund = totalPayAmtMinusDiscount - refundedAmount;
                log.info("有赞 totalPayAmtMinusDiscount: {},refundedAmount: {}, maxRefund : {}", totalPayAmtMinusDiscount, refundedAmount, maxRefund);

                if (maxRefund > 0) {
                    int refundFee = refundProductInfoDTO.getRefundAmount();

                    if (refundFee > maxRefund) {
                        refundFee = maxRefund;
                    }

                    BigDecimal refundFeeInYuan = MoneyUtils.fenToYuan(refundFee);

                    refundTotalFee.set(refundTotalFee.get().add(refundFeeInYuan));

                    item.setRefundFee(refundFeeInYuan.toString());
                    items.add(item);
                }


            });


            // 需要退运费或者包装费，并且已经退的运费为0, 渠道优先退运费，运费未退，包装费肯定也未退
            if ((request.getFreight() > 0 || request.getPackageAmt() > 0) && refundedFreightInFen == 0) {
                // TODO lyt 赠品不能退运费


                // 总退费增加运费的退费
                BigDecimal freightInYuan = MoneyUtils.fenToYuan(request.getFreight());
                // 总退费增加包装费的退费
                BigDecimal packageAmtInYuan = MoneyUtils.fenToYuan(request.getPackageAmt());

                refundTotalFee.set(refundTotalFee.get().add(freightInYuan).add(packageAmtInYuan));

                if (CollectionUtils.isEmpty(items)) {
                    // 退款商品明细为空，那么就使用仅退运费模式
                    youzanTradeRefundSellerActiveParams.setRefundFeeType(1);
                } else {
                    // 将运费,包装费添加到最后一件退款商品上去
                    int lastItemIdx = items.size() - 1;
                    BigDecimal itemRefundAddFreightRefund = freightInYuan.add(packageAmtInYuan).add(new BigDecimal(items.get(lastItemIdx).getRefundFee()));
                    items.get(lastItemIdx).setRefundFee(itemRefundAddFreightRefund.toString());
                }

            }

            youzanTradeRefundSellerActiveParams.setRefundFee(refundTotalFee.toString());
            youzanTradeRefundSellerActiveParams.setItems(items);

            if (CollectionUtils.isEmpty(items)) {
                // 没有可退的商品，直接返回成功
                return ResultGenerator.genResult(ResultCode.SUCCESS);
            }

            YouzanTradeRefundSellerActiveResult youzanTradeRefundSellerActiveResult = getResult4YouZan(appMessage, new YouzanTradeRefundSellerActive(youzanTradeRefundSellerActiveParams), YouzanTradeRefundSellerActiveResult.class);

            if (youzanTradeRefundSellerActiveResult.getSuccess()) {
                return ResultGenerator.genResult(ResultCode.SUCCESS);
            } else {
                return ResultGenerator.genResult(ResultCode.FAIL, youzanTradeRefundSellerActiveResult.getMessage());
            }
        } catch (SDKException e) {
            log.error("poiPartRefundApply error, request->{}, exception:", request, e);
            return ResultGenerator.genResult(ResultCode.FAIL);

        } catch (Exception e) {
            log.error("poiPartRefundApply error, exception:", e);
            return ResultGenerator.genResult(ResultCode.FAIL);
        }
    }

    // 有效（通过或真正进行中）的售后单状态
    private static final Set<String> VALID_REFUND_STATUS = new HashSet<>();

    static {
        /*
        WAIT_SELLER_AGREE-买家已经申请退款，等待卖家同意;
        WAIT_BUYER_RETURN_GOODS-卖家已经同意退款，等待买家退货;
        WAIT_SELLER_CONFIRM_GOODS-买家已经退货，等待卖家确认收货;
        SELLER_REFUSE_BUYER-卖家拒绝退款;
        SELLER_REFUSE_BUYER_RETURN_GOODS-卖家未收到货,拒绝退款 ;
        SELLER_RETURN_GOODS-商家确认收货并发送换货; 目前不支持
        CLOSED-退款关闭;
        SUCCESS-退款成功;
         */
        VALID_REFUND_STATUS.add("WAIT_SELLER_AGREE");
        VALID_REFUND_STATUS.add("WAIT_BUYER_RETURN_GOODS");
        VALID_REFUND_STATUS.add("SELLER_REFUSE_BUYER_RETURN_GOODS"); // 卖家未收到货，买家可以重新上传快递单号
        VALID_REFUND_STATUS.add("SUCCESS");

    }


    @Override
    public CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request) {
        return null;
    }

    @Override
    public ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request) {
        return null;
    }

    @Override
    public PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request) {
        return null;
    }

    @Override
    public OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request) {
        return null;
    }

    @Override
    public GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request) {
        return null;
    }

    @Override
    public GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request) {
        return null;
    }

    /**
     * 注意：有赞仅同步订单发货，即骑手已取货
     * 由旭日系统去调用有赞订单发货接口去变更发货状态，我们暂时不调用（代码保留）
     */
    @Override
    public ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request) {
        log.info("不支持的配送状态更新:{}", request);
        return buildSuccessResultStatus();
//        try {
//            AppMessage appMessage = getAppMessage(request.getTenantId(), request.getStoreId());
//            String accessToken = accessTokenService.getAccessToken(appMessage);
//            int status = request.getStatus();
//            switch (status) {
//                case 50: {
//                    YouzanLogisticsOnlineConfirm youzanLogisticsOnlineConfirm = new YouzanLogisticsOnlineConfirm();
//                    YouzanLogisticsOnlineConfirmParams params = new YouzanLogisticsOnlineConfirmParams();
//                    params.setTid(request.getOrderId());
//                    youzanLogisticsOnlineConfirm.setAPIParams(params);
//                    log.info("begin invoke youzanLogisticsOnlineConfirm,param = {},accessToken = {}", params, accessToken);
//                    YouzanLogisticsOnlineConfirmResult result = yzClient.invoke(youzanLogisticsOnlineConfirm, new Token(accessToken), YouzanLogisticsOnlineConfirmResult.class);
//                    log.info("finish invoke youzanLogisticsOnlineConfirm,param = {},accessToken = {}, result = {}", params, accessToken, result);
//                    if (!result.getSuccess()) {
//                        return new ResultStatus().setCode(ResultCode.CHANNEL_SYSTEM_ERROR.getCode()).setMsg(ResultCode.CHANNEL_SYSTEM_ERROR.getMsg());
//                    }
//                    return buildSuccessResultStatus();
//                }
//                default: {
//                    log.warn("YzToolChannelOrderServiceImpl.updateOrderDeliveryStatus  无法处理配送状态:{}", status);
//                    //对于非法状态不做处理
//                    return buildSuccessResultStatus();
//                }
//            }
//        }
//        catch (SDKException e) {
//            log.error("YzToolChannelOrderServiceImpl.updateOrderDeliveryStatus throw SDKException", e);
//            return new ResultStatus().setCode(ResultCode.CHANNEL_SYSTEM_ERROR.getCode()).setMsg(ResultCode.CHANNEL_SYSTEM_ERROR.getMsg());
//        }
//        catch (Exception e) {
//            log.error("YzToolChannelOrderServiceImpl.updateOrderDeliveryStatus throw exception", e);
//            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg());
//        }
    }

    @Override
    public ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request) {
        try {
            log.info("updateDeliveryInfo, request:{}", request);
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getShopId());

            Long nowSeconds = System.currentTimeMillis() / 1000;

            YouzanRetailOpenUpdateLocaldeliverystatusParams youzanRetailOpenUpdateLocaldeliverystatusParams = new YouzanRetailOpenUpdateLocaldeliverystatusParams();

            youzanRetailOpenUpdateLocaldeliverystatusParams.setTransporterName(request.getRiderName());
            // youzanRetailOpenUpdateLocaldeliverystatusParams.setCancelReason("取消原因");
            youzanRetailOpenUpdateLocaldeliverystatusParams.setUpdateTime(nowSeconds.intValue());
            // youzanRetailOpenUpdateLocaldeliverystatusParams.setCancelFrom(1);
            youzanRetailOpenUpdateLocaldeliverystatusParams.setTransporterPhone(request.getRiderPhone());
            youzanRetailOpenUpdateLocaldeliverystatusParams.setTid(request.getOrderId());
            // youzanRetailOpenUpdateLocaldeliverystatusParams.setTransporterLng(String.valueOf(request.getLongitude()));
            // youzanRetailOpenUpdateLocaldeliverystatusParams.setTransporterLat(String.valueOf(request.getLongitude()));

            DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(request.getStatus());

            if (deliveryStatus == null) {
                log.error("不支持的配送状态:{}", request);
                return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg("不支持的配送状态" + request.getStatus());
            }

            int status = request.getStatus();
            switch (deliveryStatus) {
                // 有赞订单状态；待接单＝1；待取货＝2；配送中＝3；已完成＝4；已取消＝5；
                // 配送已创建
                case WAIT_DISPATCH_RIDER: {
                    youzanRetailOpenUpdateLocaldeliverystatusParams.setOrderStatus(1);
                    break;
                }
                case RIDER_ACCEPTED_ORDER: {
                    youzanRetailOpenUpdateLocaldeliverystatusParams.setOrderStatus(2);
                    break;
                }
                // 骑手已取餐 配送中
                case RIDER_TAKEN_MEAL: {
                    youzanRetailOpenUpdateLocaldeliverystatusParams.setOrderStatus(3);
                    break;
                }
                // 配送完成
                case DELIVERY_COMPLETED: {
                    youzanRetailOpenUpdateLocaldeliverystatusParams.setOrderStatus(4);
                    break;
                }
                case DELIVERY_CANCEL: {
                    youzanRetailOpenUpdateLocaldeliverystatusParams.setOrderStatus(5);
                    break;
                }

                default: {
                    log.warn("YzToolChannelOrderServiceImpl.updateDeliveryInfo  无法处理配送状态:{}", status);
                    //对于非法状态不做处理
                    return buildSuccessResultStatus();
                }
            }

            YouzanRetailOpenUpdateLocaldeliverystatusResult youzanRetailOpenUpdateLocaldeliverystatusResult = getResult4YouZan(appMessage, new YouzanRetailOpenUpdateLocaldeliverystatus(youzanRetailOpenUpdateLocaldeliverystatusParams), YouzanRetailOpenUpdateLocaldeliverystatusResult.class);

            if (!youzanRetailOpenUpdateLocaldeliverystatusResult.getSuccess()) {
                return new ResultStatus().setCode(ResultCode.CHANNEL_SYSTEM_ERROR.getCode()).setMsg(ResultCode.CHANNEL_SYSTEM_ERROR.getMsg());
            }

            return buildSuccessResultStatus();
        } catch (SDKException e) {
            log.error("YzToolChannelOrderServiceImpl.updateDeliveryInfo throw SDKException", e);
            return new ResultStatus().setCode(ResultCode.CHANNEL_SYSTEM_ERROR.getCode()).setMsg(ResultCode.CHANNEL_SYSTEM_ERROR.getMsg());
        } catch (Exception e) {
            log.error("YzToolChannelOrderServiceImpl.updateDeliveryInfo throw exception", e);
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg());
        }
    }

    @Override
    public ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request) {
        return updateDeliveryInfo(request);
    }

    @Override
    public ResultStatus selfDelivery(SelfDeliveryRequest request) {
        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), request.getStoreId());
            YouzanLogisticsOnlineLocalDistorderCancelParams youzanLogisticsOnlineLocalDistorderCancelParams = new YouzanLogisticsOnlineLocalDistorderCancelParams();
            youzanLogisticsOnlineLocalDistorderCancelParams.setDistId(request.getChannelDeliveryId());
            youzanLogisticsOnlineLocalDistorderCancelParams.setDeliveryChannel(StringUtils.isNotBlank(request.getDeliveryChannel())? Integer.parseInt(request.getDeliveryChannel()) : 0);
            youzanLogisticsOnlineLocalDistorderCancelParams.setTid(request.getOrderId());
            youzanLogisticsOnlineLocalDistorderCancelParams.setIsCloudTag(false);

            //查询取消原因
            YouzanLogisticsLocalCancelReasonParams youzanLogisticsLocalCancelReasonParams = new YouzanLogisticsLocalCancelReasonParams();
            youzanLogisticsLocalCancelReasonParams.setDeliveryChannel(StringUtils.isNotBlank(request.getDeliveryChannel())? Integer.parseInt(request.getDeliveryChannel()) : 0);
            YouzanLogisticsLocalCancelReasonResult youzanLogisticsLocalCancelReasonResult = getResult4YouZan(appMessage, new YouzanLogisticsLocalCancelReason(youzanLogisticsLocalCancelReasonParams), YouzanLogisticsLocalCancelReasonResult.class);
            if (!youzanLogisticsLocalCancelReasonResult.getSuccess() || CollectionUtils.isEmpty(youzanLogisticsLocalCancelReasonResult.getData())) {
                youzanLogisticsOnlineLocalDistorderCancelParams.setCancelReasonId(1);
            }
            youzanLogisticsOnlineLocalDistorderCancelParams.setCancelReasonId(youzanLogisticsLocalCancelReasonResult.getData().get(0).getId());

            YouzanLogisticsOnlineLocalDistorderCancelResult youzanLogisticsOnlineLocalDistorderCancelResult = getResult4YouZan(appMessage, new YouzanLogisticsOnlineLocalDistorderCancel(youzanLogisticsOnlineLocalDistorderCancelParams), YouzanLogisticsOnlineLocalDistorderCancelResult.class);
            if (!youzanLogisticsOnlineLocalDistorderCancelResult.getSuccess()) {
                return ResultGenerator.genFailResult(youzanLogisticsOnlineLocalDistorderCancelResult.getMessage());
            }
            return buildSuccessResultStatus();

        } catch (SDKException e) {
            log.error("YzToolChannelOrderServiceImpl.selfDelivery throw SDKException", e);
            return new ResultStatus().setCode(ResultCode.CHANNEL_SYSTEM_ERROR.getCode()).setMsg(ResultCode.CHANNEL_SYSTEM_ERROR.getMsg());
        } catch (Exception e) {
            log.error("YzToolChannelOrderServiceImpl.selfDelivery throw exception", e);
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg());
        }
    }

    @Override
    public ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request) {
        return new ResultStatus(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(), ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg(), null);
    }

    @Override
    public QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request) {
        return null;
    }

    @Override
    public ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request) {
        return null;
    }

    @Override
    public QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request) {
        QueryChannelOrderListResult ret = new QueryChannelOrderListResult();

        try {
            AppMessage appMessage = getSubAppMessage(request.getTenantId(), Long.valueOf(request.getShopId()));

            // 订单搜索接口，升级yz_open_id，该接口目前不支持返回buyer_id。 排序规则基于订单创建时间、更新时间和完成时间 创建时间可以获取指定时间内所有的订单。 更新时间可以获取最新更新订单，但是范围内订单是动态变化的。完成时间可以获取指定时间内完成的订单。 1.只有创建时间start_created和end_created，按创建时间排序 2.只有更新时间start_update和end_update，按更新时间排序 3.只有完成时间start_success和end_success，按完成时间排序 4.同时存在创建时间、更新时间，按更新时间排序 5.同时存在创建时间、完成时间，按完成时间排序 6.同时存在更新时间、完成时间，按完成时间排序 7.同时存在创建时间、更新时间、完成时间，按完成时间排序 8.如创建或更新或完成时间的开始和结束时间不是成对出现，条件无效 9.注意：查询2020年以前的订单数据请使用youzan.trades.sold.get.4.0.0接口查询，文档地址：https://doc.youzanyun.com/detail/API/0/157

            YouzanTradesSoldGetParams youzanTradesSoldGetParams = new YouzanTradesSoldGetParams();

            youzanTradesSoldGetParams.setNodeKdtId(Long.valueOf(request.getChannelPoiId()));
//            youzanTradesSoldGetParams.setExpressType("LOCAL_DELIVERY"); // 目前只支持同城订单
            youzanTradesSoldGetParams.setStartCreated(new Date(request.getStartTime()));
            youzanTradesSoldGetParams.setEndCreated(new Date(request.getEndTime()));
            youzanTradesSoldGetParams.setPageNo(request.getPage());
            youzanTradesSoldGetParams.setPageSize(request.getPageSize());


            YouzanTradesSoldGetResult youzanTradesSoldGetResult = getResult4YouZan(appMessage, new YouzanTradesSoldGet(youzanTradesSoldGetParams), YouzanTradesSoldGetResult.class);

            if (youzanTradesSoldGetResult.getSuccess()) {
                ret.setStatus(ResultGenerator.genSuccessResult());


                List<String> channelOrderIds = Optional.ofNullable(youzanTradesSoldGetResult.getData().getFullOrderInfoList()).orElse(Lists.emptyList())
                        .stream().filter(orderInfo->{
                            YouzanTradesSoldGetResult.YouzanTradesSoldGetResultOrderinfo yzOrderinfo = Optional
                                    .ofNullable(orderInfo.getFullOrderInfo().getOrderInfo()).orElse(null);
                            if (yzOrderinfo != null && StringUtils.isNotBlank(yzOrderinfo.getStatus())
                                    && !YZOrderStatusEnum.getUnCompensationStatusList()
                                            .contains(yzOrderinfo.getStatus())) {
                                Boolean isPay = Optional.ofNullable(yzOrderinfo.getOrderTags().getIsPayed())
                                        .orElse(false);
                                Long payType = yzOrderinfo.getPayType();
                                if (PAY_ON_DELIVERY.equals(payType) || BooleanUtils.toBoolean(isPay)) {
                                    return true;
                                }
                            }
                            return false;

                        }).map(item ->
                                item.getFullOrderInfo().getOrderInfo().getTid()
                        ).collect(Collectors.toList());

                Long total = youzanTradesSoldGetResult.getData().getTotalResults();
                ret.setTotal(total.intValue());
                ret.setPage(request.getPage());
                ret.setPageCount((total.intValue() + request.getPageSize() - 1) / request.getPageSize()); // 总页数（向上取整）
                ret.setChannelOrderIdList(channelOrderIds);
            } else {
                ret.setStatus(ResultGenerator.genResult(ResultCode.FAIL, youzanTradesSoldGetResult.getMessage()));
            }
        } catch (SDKException e) {
            log.error("queryChannelOrderList error, request->{}, exception:", request, e);
            ret.setStatus(ResultGenerator.genResult(ResultCode.FAIL));
        } catch (Exception e) {
            log.error("queryChannelOrderList error, exception:", e);
            ret.setStatus(ResultGenerator.genResult(ResultCode.FAIL));
        }

        return ret;
    }

    @Override
    public QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request) {
        return null;
    }

    @Override
    public ChannelOrderDetailDTO getOrderDetail4ChannelMessage(ChannelTypeEnum channelType, ChannelNotifyEnum channelNotify,
                                                               Long tenantId, String msg) {
        if (ChannelTypeEnum.YOU_ZAN != channelType) {
            return null;
        }
        TradeCommonMessage<String> tradMsg = JSON.parseObject(msg, new TypeReference<TradeCommonMessage<String>>() {
        });
        String message = UrlUtil.urlDecodeSafe(tradMsg.getMsg());
        YouzanTradeGetResult.YouzanTradeGetResultData createMsg = JSON.parseObject(message,
                new TypeReference<YouzanTradeGetResult.YouzanTradeGetResultData>() {
                });
        int status = OrderStatusConverter.yzOrderStatusMapping(tradMsg.getStatus());
        Long storeId = copChannelStoreService.selectChannelStoreId(tenantId, channelType.getCode(),
                String.valueOf(tradMsg.getChannelPoiCode()));
        // 隐私数据解密
        AppMessage appMessage = getSubAppMessage(tenantId, storeId);
        decryptOrderMessage(appMessage, createMsg);
        ChannelOrderDetailDTO channelOrderDetailDTO = getChannelOrderDetailDTO(createMsg, appMessage);
        channelOrderDetailDTO.setChannelOrderStatus(status);
        channelOrderDetailDTO.setStoreId(storeId);
        channelOrderDetailDTO.setOriginalPoiId(String.valueOf(tradMsg.getChannelPoiCode()));
        channelOrderDetailDTO.setChannelStoreName(tradMsg.getChannelStoreName());
        return channelOrderDetailDTO;
    }

    public void decryptOrderMessage(AppMessage appMessage,
                                    YouzanTradeGetResult.YouzanTradeGetResultData tradeGetResultData) {
        if (tradeGetResultData == null) {
            return;
        }
        YouzanTradeGetResult.YouzanTradeGetResultFullorderinfo fullOrderInfo = tradeGetResultData.getFullOrderInfo();
        if (fullOrderInfo == null) {
            return;
        }

        YouzanTradeGetResult.YouzanTradeGetResultAddressinfo addressInfo = fullOrderInfo.getAddressInfo();
        if (addressInfo != null) {
            addressInfo.setSelfFetchInfo(decrypt(appMessage, addressInfo.getSelfFetchInfo()));
            addressInfo.setReceiverName(decrypt(appMessage, addressInfo.getReceiverName()));
            addressInfo.setReceiverTel(decrypt(appMessage, addressInfo.getReceiverTel()));
            addressInfo.setDeliveryAddress(decrypt(appMessage, addressInfo.getDeliveryAddress()));
        }
        YouzanTradeGetResult.YouzanTradeGetResultBuyerinfo buyerInfo = fullOrderInfo.getBuyerInfo();
        if (buyerInfo != null) {
            buyerInfo.setBuyerPhone(decrypt(appMessage, buyerInfo.getBuyerPhone()));
            buyerInfo.setFansNickname(decrypt(appMessage, buyerInfo.getFansNickname()));
        }
        YouzanTradeGetResult.YouzanTradeGetResultInvoiceinfo invoiceInfo = fullOrderInfo.getInvoiceInfo();
        if (invoiceInfo != null) {
            invoiceInfo.setEmail(decrypt(appMessage, invoiceInfo.getEmail()));
        }
    }

    public ChannelOrderDetailDTO getChannelOrderDetailDTO(YouzanTradeGetResult.YouzanTradeGetResultData tradeGetResultData, AppMessage appMessage) {
        log.info("YzToolChannelOrderServiceImpl.getChannelOrderDetailDTO tradeGetResultData:{}", JSON.toJSONString(tradeGetResultData));
        ChannelOrderDetailDTO detail = converterService.orderDetailMapping(tradeGetResultData);
        log.info("YzToolChannelOrderServiceImpl.getChannelOrderDetailDTO detail:{}", detail);
        YouzanTradeGetResult.YouzanTradeGetResultOrderinfo orderInfo =
                Optional.ofNullable(tradeGetResultData).map(YouzanTradeGetResult.YouzanTradeGetResultData::getFullOrderInfo)
                        .map(YouzanTradeGetResult.YouzanTradeGetResultFullorderinfo::getOrderInfo).orElse(null);

        if (orderInfo == null) {
            throw new BizException(ResultCode.ORDER_NOT_EXIST.getCode(), "订单数据不完整 order info is null");
        }
        detail.setChannelOrderStatus(OrderStatusConverter.yzOrderStatusMapping(orderInfo.getStatus()));
        detail.setBaichuanStatus(BaichuanOrderStatusConverter.yzOrderStatusMapping(orderInfo.getStatus()));

        YouzanTradeGetResult.YouzanTradeGetResultPayinfo payInfo = tradeGetResultData.getFullOrderInfo().getPayInfo();
        if (payInfo == null && detail.getChannelOrderStatus() != ChannelOrderStatus.CANCELED.getValue()
                && detail.getChannelOrderStatus() >= ChannelOrderStatus.NEW_ORDER.getValue()) {
            throw new BizException(ResultCode.ORDER_NOT_EXIST.getCode(), "订单数据不完整 pay info is null");
        }
        if (payInfo != null) {
            detail.setActualPayAmt(MoneyUtils.yuanToFen(Optional.ofNullable(payInfo.getRealPayment())
                    .filter(StringUtils::isNotEmpty).orElse(payInfo.getPayment())));
            detail.setBizReceiveAmt(MoneyUtils.yuanToFen(payInfo.getPayment()));
            detail.setOriginalAmt(Math.addExact(MoneyUtils.nullableYuanToFen(payInfo.getTotalFee()),
                    MoneyUtils.nullableYuanToFen(payInfo.getPostFee())));
            detail.setFreight(MoneyUtils.nullableYuanToFen(payInfo.getPostFee()));
        }

        detail.setChannelOrderId(orderInfo.getTid());
        detail.setCreateTime(Optional.ofNullable(orderInfo.getCreated()).map(Date::getTime).orElse(System.currentTimeMillis()));
        // 渠道订单为终态设置订单完成时间
        detail.setCompletedTime((detail.getChannelOrderStatus() == ChannelOrderStatus.FINISHED.getValue()
                || detail.getChannelOrderStatus() == ChannelOrderStatus.CANCELED.getValue()) && detail.getActualPayAmt() > 0
                ? Optional.ofNullable(orderInfo.getSuccessTime()).map(Date::getTime).orElse(0L) : 0L);
        // 设置支付时间
        detail.setPayTime(Optional.ofNullable(tradeGetResultData.getFullOrderInfo())
                .map(e->e.getOrderInfo())
                .map(e->e.getPayTime())
                .map(Date::getTime)
                .orElse(0L));

        YouzanTradeGetResult.YouzanTradeGetResultInvoiceinfo invoiceInfo = tradeGetResultData.getFullOrderInfo().getInvoiceInfo();
        if (invoiceInfo != null) {
            detail.setIsNeedInvoice(true);
            OrderInvoiceDetailDTO orderInvoiceDetailDTO = new OrderInvoiceDetailDTO();
            orderInvoiceDetailDTO.setInvoiceTitle(invoiceInfo.getUserName());
            orderInvoiceDetailDTO.setTaxNo(invoiceInfo.getTaxpayerId());
            detail.setInvoiceDetail(orderInvoiceDetailDTO);
        }
        detail.setPayType(orderInfo.getPayType() != null && orderInfo.getPayType() != Constant.YOU_ZAN_CASH_PAY_CODE ?
                PayTypeEnum.ONLINE.getValue() : PayTypeEnum.CASH_DELIVERY.getValue());
        detail.setPayStatus(Optional.ofNullable(orderInfo.getOrderTags().getIsPayed())
                .orElse(false) ?
                PayStatusEnum.PAID.getValue() : PayStatusEnum.UNPAID.getValue());
        detail.setOriginalPoiId(String.valueOf(orderInfo.getNodeKdtId()));
        List<YouzanTradeGetResult.YouzanTradeGetResultOrders> products = tradeGetResultData.getFullOrderInfo().getOrders();
        if (CollectionUtils.isEmpty(products)) {
            throw new BizException(ResultCode.ORDER_NOT_EXIST.getCode(), "订单数据不完整 orders info is null");
        }
        List<OrderProductDetailDTO> productDetailDTOList = new ArrayList<>(products.size());
        List<OrderProductDetailDTO> giftProductDTO = new ArrayList<>();
        // 商家整单优惠
        Integer poiPromotion = 0;
        // 商家单品优惠
        Integer poiItemPromotion = 0;
        log.info("有赞商品明细数据,products:{}", products.toArray());
        for (YouzanTradeGetResult.YouzanTradeGetResultOrders product : products) {
            OrderProductDetailDTO orderProductDetailDTO = converterService.productMapping(product);
            log.info("有赞商品明细数据,商品行数据:{}", orderProductDetailDTO);
            int productPoiPromotion = Math.subtractExact(MoneyUtils.nullableYuanToFen(product.getTotalFee()),
                    MoneyUtils.nullableYuanToFen(product.getPayment()));

            log.info("有赞商品明细数据,商品行具体费用数据:totalFee:{}, payment:{}, price:{}, num:{}, productPoiPromotion:{}, weight:{}", product.getTotalFee(), product.getPayment(), product.getPrice(), product.getNum(), productPoiPromotion, product.getWeight());
            // 商品整单优惠
            orderProductDetailDTO.setPoiPromotion(productPoiPromotion);
            poiPromotion += productPoiPromotion;
            int productPoiItemPromotion = Math.subtractExact(Math.multiplyExact(MoneyUtils.nullableYuanToFen(product.getPrice()), product.getNum()), MoneyUtils.nullableYuanToFen(product.getTotalFee()));
            orderProductDetailDTO.setPoiItemPromotion(productPoiItemPromotion);

            poiItemPromotion += productPoiItemPromotion;


            orderProductDetailDTO.setWeight(StringUtils.isEmpty(product.getWeight()) ? 1000 : Long.parseLong(product.getWeight()));
            //规格
            String specification = orderProductDetailDTO.getSpecification();
            if (StringUtils.isNotEmpty(specification)) {
                List<String> specifications = JSON.parseObject(specification, new TypeReference<List<Specification>>() {
                }).stream().map(Specification::getSpecification).collect(Collectors.toList());
                orderProductDetailDTO.setSpecification(String.join(",", specifications));
            }
            //属性
            if (StringUtils.isNotEmpty(orderProductDetailDTO.getSkuProperty())) {
                List<SkuProperty> skuProperties = JSON.parseObject(orderProductDetailDTO.getSkuProperty(), new TypeReference<List<SkuProperty>>() {
                });
                if (skuProperties.size() % SKU_PROPERTY_STEP == 0) {
                    List<SkuProperty> realSkuProperties = new ArrayList<>(skuProperties.size() / SKU_PROPERTY_STEP);
                    for (int i = 0; i < skuProperties.size(); i++) {
                        SkuProperty skuProperty = skuProperties.get(i);
                        // 获取属性值、且跳过属性值对象遍历
                        skuProperty.setValue(skuProperties.get(++i).getValue());
                        realSkuProperties.add(skuProperty);
                    }
                    orderProductDetailDTO.setSkuProperty(realSkuProperties.stream()
                            .filter(v -> StringUtils.isNotEmpty(v.getName()) && StringUtils.isNotEmpty(v.getValue()))
                            .map(SkuProperty::getProperty).collect(Collectors.joining(",")));
                }
            }
            if (product.getIsPresent() != null && product.getIsPresent()) {
                giftProductDTO.add(orderProductDetailDTO);
            }
            /*
            if (product.getIsPresent() == null || !product.getIsPresent()) {
                productDetailDTOList.add(orderProductDetailDTO);
            }*/
            productDetailDTOList.add(orderProductDetailDTO);
        }
        detail.setPoiPromotion(poiPromotion);
        detail.setPoiItemPromotion(poiItemPromotion);
        detail.setSkuDetails(productDetailDTOList);
        setOrderActivities(tradeGetResultData, detail, productDetailDTOList, giftProductDTO);
        // 配送信息
        detail.setDeliveryDetail(setOrderDeliveryInfo(tradeGetResultData));
        // 预订单逻辑处理
        bookingDeal(detail, orderInfo);
        // 到店自提备注拼接
        if (ExpressTypeEnum.SELF_MENTION.getDesc().equals(detail.getDeliveryDetail().getDeliveryMethod())) {
            detail.setComment(StringUtils.isNotEmpty(detail.getComment()) ? detail.getComment().concat(" ").concat("到店自取") : "到店自取");
        }
        YouzanTradeGetResult.YouzanTradeGetResultBuyerinfo buyerInfo = tradeGetResultData.getFullOrderInfo().getBuyerInfo();
        //微信H5和微信小程序（有赞小程序和小程序插件）的订单会返回微信weixin_openid，
        // 三方App（有赞APP开店）的订单会返回open_user_id
        if (buyerInfo != null) {
            detail.setExtData(JSON.toJSONString(ImmutableMap.of("outerUserId", buyerInfo.getOuterUserId())));
            detail.setBuyerAccount(buyerInfo.getBuyerId()!=null ? String.valueOf(buyerInfo.getBuyerId()): null);
            detail.setBuyerNick(buyerInfo.getFansNickname());
        }
        if (detail.getChannelOrderStatus() > ChannelOrderStatus.NEW_ORDER.getValue() && detail.getActualPayAmt() > 0) {
            // 有赞订单详情没有订单确认时间、设置为支付时间后推1秒钟
            detail.setConfirmTime(Optional.ofNullable(orderInfo.getPayTime())
                    .map(time -> time.getTime() + TimeUnit.SECONDS.toMillis(1)).orElse(0L));
        }
        if (StringUtils.isNotBlank(orderInfo.getPayTypeStr())){
            detail.setChannelPayMode(orderInfo.getPayTypeStr());
        }else {
            detail.setChannelPayMode(KeyConstant.DEFAULT_PAY_MODE);
        }


        extractBuyerAndRemark(detail, tradeGetResultData.getFullOrderInfo());

        // 活动分摊信息
        setActivityShareDetailList(appMessage, detail);
        return detail;
    }

    /**
     * 设置费用等字段
     * @param detail
     * @param fullOrderInfo
     */
    private void extractBuyerAndRemark(ChannelOrderDetailDTO detail, YouzanTradeGetResult.YouzanTradeGetResultFullorderinfo fullOrderInfo){

        YouzanTradeGetResult.YouzanTradeGetResultOrderextra orderExtra = fullOrderInfo.getOrderInfo().getOrderExtra();
        YouzanTradeGetResult.YouzanTradeGetResultRemarkinfo remarkInfo = fullOrderInfo.getRemarkInfo();
        log.info("YzToolChannelOrderServiceImpl.extractBuyerAndRemark, orderExtra{}, remarkInfo:{}", JSON.toJSONString(orderExtra), JSON.toJSONString(remarkInfo));
        double packing = 0d;
        double ztPacking = 0d;
        double payable_logistics_value = detail.getFreight();
        try {
            String extraPrices = orderExtra.getExtraPrices();
            log.info("有赞订单 extraPrice:{}", extraPrices);
            if (StringUtils.isNotBlank(extraPrices)) {
                JSONArray extraPricesArr = JSONArray.parseArray(extraPrices);
                log.info("YzToolChannelOrderServiceImpl.extractBuyerAndRemark, extraPricesArr{}", extraPricesArr);
                if (extraPricesArr != null && extraPricesArr.size() > 0) {
                    for (int i = 0; i < extraPricesArr.size(); i++) {
                        JSONObject extraPricesObj = extraPricesArr.getJSONObject(i);
                        String extraDesc = extraPricesObj.getString("desc");
                        String extraName = extraPricesObj.getString("name");
                        double extraRealPay = extraPricesObj.getDoubleValue("realPay");
                        if (KeyConstant.PACKING.equals(extraDesc) || KeyConstant.PACKING.equals(extraName)
                        || KeyConstant.PACKING_SUB.equals(extraDesc) || KeyConstant.PACKING_SUB.equals(extraName)) {
                            packing += BigDecimal.valueOf(extraRealPay).doubleValue();
                        } else if (KeyConstant.LOGISTICS.equals(extraDesc) || KeyConstant.LOGISTICS.equals(extraName)) {
                            payable_logistics_value += BigDecimal.valueOf(extraRealPay).doubleValue();
                        } else if (KeyConstant.ZT_PACKING.equals(extraDesc) || KeyConstant.ZT_PACKING.equals(extraName)) {
                            ztPacking += BigDecimal.valueOf(extraRealPay).doubleValue();
                        }
                    }
                }
            }

            // 订单卖家备注
            String tradeMemo = remarkInfo.getTradeMemo();
            //测试设置买家备注
            log.info("有赞订单卖家备注 tradeMemo:{}", tradeMemo);
            if (BigDecimal.valueOf(packing).compareTo(BigDecimal.ZERO) == 0 && StringUtils.isNotEmpty(tradeMemo)) {
                packing = BigDecimal.valueOf(CommonUtils.matchPacking(tradeMemo.trim())).multiply(BigDecimal.valueOf(100)).doubleValue();
            }
            //包装费为0并且自提包装费大于0
            if (BigDecimal.valueOf(packing).compareTo(BigDecimal.ZERO) == 0 && BigDecimal.valueOf(ztPacking).compareTo(BigDecimal.ZERO) > 0) {
                detail.setPackageAmt(CommonUtils.roundMoney(ztPacking));
            } else {
                detail.setPackageAmt(CommonUtils.roundMoney(packing));
            }
            detail.setPayPackageAmt(detail.getPackageAmt());
            detail.setPoiPackageAmt(detail.getPackageAmt());

            detail.setFreight(CommonUtils.roundMoney(payable_logistics_value));

        } catch (Exception e) {
            log.info("获取有赞订单费用失败", e);
        }
    }

    private void bookingDeal(ChannelOrderDetailDTO detail, YouzanTradeGetResult.YouzanTradeGetResultOrderinfo orderInfo) {
        String reservationOrder = Optional.ofNullable(orderInfo.getOrderExtra()).
                map(YouzanTradeGetResult.YouzanTradeGetResultOrderextra::getIsReservationOrder).orElse(StringUtils.EMPTY);
        if (StringUtils.isEmpty(reservationOrder) && detail.getDeliveryDetail() != null) {
            //根据时间判断
            long intervalsTime = (detail.getDeliveryDetail().getArrivalTime() - detail.getCreateTime()) / MIN;
            int intervalsMin = MccConfigUtil.youzanDeliveryIntervalsMin();
            detail.setIsBooking(intervalsMin > 0 && intervalsTime > intervalsMin);
            return;
        }
        detail.setIsBooking(reservationOrder.equals("1"));
    }

    private OrderDeliveryDetailDTO setOrderDeliveryInfo(YouzanTradeGetResult.YouzanTradeGetResultData tradeGetResultData) {
        //地址信息完善
        //拼接规则delivery_address@delivery_province+delivery_city+delivery_district+delivery_address
        OrderDeliveryDetailDTO deliveryDetailDTO = new OrderDeliveryDetailDTO();
        Integer expressType = tradeGetResultData.getFullOrderInfo().getOrderInfo().getExpressType();
        ExpressTypeEnum expressTypeEnum = ExpressTypeEnum.codeOf(expressType);
        deliveryDetailDTO.setDeliveryMethod(expressTypeEnum.getDesc());
        deliveryDetailDTO.setIsSelfDelivery((expressTypeEnum == ExpressTypeEnum.KUAI_DI ||
                expressTypeEnum == ExpressTypeEnum.SAME_CITY_DELIVERY) ? 1 : 0);

        YouzanTradeGetResult.YouzanTradeGetResultAddressinfo addressInfo = tradeGetResultData.getFullOrderInfo().getAddressInfo();
        String address =
                new StringBuilder(StringUtils.EMPTY).append(Optional.ofNullable(addressInfo.getDeliveryAddress()).orElse(StringUtils.EMPTY))
                        .append("@#")
                        .append(Optional.ofNullable(addressInfo.getDeliveryProvince()).orElse(StringUtils.EMPTY))
                        .append(Optional.ofNullable(addressInfo.getDeliveryCity()).orElse(StringUtils.EMPTY))
                        .append(Optional.ofNullable(addressInfo.getDeliveryDistrict()).orElse(StringUtils.EMPTY))
                        .append(Optional.ofNullable(addressInfo.getDeliveryAddress()).orElse(StringUtils.EMPTY)).toString().trim();
        deliveryDetailDTO.setUserAddress(address);
        deliveryDetailDTO.setUserName(addressInfo.getReceiverName());
        deliveryDetailDTO.setUserPhone(addressInfo.getReceiverTel());
        //设置隐私号码
//        deliveryDetailDTO.setUserPrivacyPhone(addressInfo.getReceiverTel());
        deliveryDetailDTO.setUserPrivacyPhone("");
        deliveryDetailDTO.setArrivalTime(Optional.ofNullable(addressInfo.getDeliveryStartTime())
                .map(Date::getTime).orElse(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(30)));
        deliveryDetailDTO.setArrivalEndTime(Optional.ofNullable(addressInfo.getDeliveryEndTime()).map(Date::getTime)
                .filter(time -> time >= deliveryDetailDTO.getArrivalTime()).orElse(deliveryDetailDTO.getArrivalTime()));
        String addressExtra = addressInfo.getAddressExtra();
        if (StringUtils.isNotEmpty(addressExtra)) {
            AddressExtra addressExtObj = JSON.parseObject(addressExtra, AddressExtra.class);
            deliveryDetailDTO.setLatitude(Optional.ofNullable(addressExtObj.getLatitude()).orElse(0D));
            deliveryDetailDTO.setLongitude(Optional.ofNullable(addressExtObj.getLongitude()).orElse(0D));
        }

        // 配送方式为自提的时候 从self_fetch_info中获取user_time字段
        String selfFetchInfoJsonStr = addressInfo.getSelfFetchInfo();
        if(Objects.equals(ExpressTypeEnum.SELF_MENTION.getCode(), expressType) && StringUtils.isNotEmpty(selfFetchInfoJsonStr)){
            log.info("YzToolChannelOrderServiceImpl.setOrderDeliveryInfo selfFetchInfoJsonStr:{}", selfFetchInfoJsonStr);
            SelfFetchInfo selfFetchInfo = JSON.parseObject(selfFetchInfoJsonStr, SelfFetchInfo.class);
            List<Long> userTimeList = translateDateToTimeStamp(selfFetchInfo.getUserTime());
            if(userTimeList.size() == 2){
                deliveryDetailDTO.setArrivalTime(Optional.ofNullable(userTimeList.get(0)).orElse(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(30)));
                deliveryDetailDTO.setArrivalEndTime(Optional.ofNullable(userTimeList.get(1)).orElse(deliveryDetailDTO.getArrivalTime()));
            }
        }
        //有赞订单详情没有自提码，不设置自提码
        return deliveryDetailDTO;
    }

    private void setOrderActivities(YouzanTradeGetResult.YouzanTradeGetResultData tradeGetResultData,
                                    ChannelOrderDetailDTO detail, List<OrderProductDetailDTO> productDetailDTOList, List<OrderProductDetailDTO> giftProductDTO) {
        YouzanTradeGetResult.YouzanTradeGetResultOrderpromotion orderPromotion = tradeGetResultData.getOrderPromotion();
        detail.setActivities(new ArrayList<>());
        detail.setSkuSharedActivities(new ArrayList<>());
        if (orderPromotion == null) {
            return;
        }
        log.info("有赞活动原始信息,orderPromotion:{}", reflectionToString(orderPromotion.getOrder()));

        // 总活动金额=商品维度活动+订单维度活动金额
        int totalDis = Math.addExact(MoneyUtils.nullableYuanToFen(orderPromotion.getOrderDiscountFee()),
                MoneyUtils.nullableYuanToFen(orderPromotion.getItemDiscountFee()));
        detail.setTotalDiscount(totalDis);

        if (CollectionUtils.isNotEmpty(orderPromotion.getOrder())) {
            detail.setActivities(converterService.orderActivitiyInfosMapping(orderPromotion.getOrder()));
        }
        log.info("有赞活动信息,orderPromotion:{}", detail.getActivities());

        if (CollectionUtils.isNotEmpty(orderPromotion.getItem())) {
            for (YouzanTradeGetResult.YouzanTradeGetResultItem itemDis : orderPromotion.getItem()) {
                GoodsActivityDetailDTO goodsActivityDetailDTO = converterService.activityDetailMapping(itemDis);
                if (itemDis.getIsPresent() == null || !itemDis.getIsPresent()) {
                    OrderProductDetailDTO product =
                            setSkuInfo2ActivityInfoAndReturn(productDetailDTOList, itemDis.getOid(), goodsActivityDetailDTO);
                    goodsActivityDetailDTO.setTotalDiscount(Optional.ofNullable(itemDis.getPromotions())
                            .orElse(Collections.emptyList()).stream().mapToInt(promotion ->
                                    MoneyUtils.yuanToFen(promotion.getDiscountFee())).sum());
                    goodsActivityDetailDTO.setTenantCost(goodsActivityDetailDTO.getTotalDiscount());
                    if (product != null && Math.subtractExact(product.getOriginalPrice(),
                            goodsActivityDetailDTO.getTotalDiscount()) == product.getSalePrice()) {
                        // 设置商品活动价格=商品原价-商品优惠价格
                        goodsActivityDetailDTO.setActivityPrice(product.getSalePrice());
                        // 存在商品活动、将销售价格设置为原价、currentPrice会获取 activityPrice
                        product.setSalePrice(product.getOriginalPrice());
                    }
                    detail.getSkuSharedActivities().add(goodsActivityDetailDTO);
                }

                //赠品本次放订单明细，从优惠活动中剔除
                if (itemDis.getIsPresent() != null && itemDis.getIsPresent()) {
                    setSkuInfo2ActivityInfoAndReturn(giftProductDTO, itemDis.getOid(), goodsActivityDetailDTO);
                    OrderDiscountDetailDTO orderDiscountDetailDTO = new OrderDiscountDetailDTO();
                    OrderProductDetailDTO product = findProduct4List(giftProductDTO, itemDis.getOid());
                    goodsActivityDetailDTO.setTotalDiscount(Optional.ofNullable(itemDis.getPromotions())
                            .orElse(Collections.emptyList()).stream().mapToInt(promotion ->
                                    MoneyUtils.yuanToFen(promotion.getDiscountFee())).sum());
                    orderDiscountDetailDTO.setActDiscount(Optional.ofNullable(itemDis.getPromotions())
                            .orElse(Collections.emptyList()).stream().mapToInt(promotion ->
                                    MoneyUtils.yuanToFen(promotion.getDiscountFee())).sum());
                    orderDiscountDetailDTO.setBizCharge(goodsActivityDetailDTO.getTenantCost());
                    orderDiscountDetailDTO.setRemark("赠品");
                    ChannelGiftInfo giftInfo = new ChannelGiftInfo();
                    if (product != null) {
                        giftInfo.setName(product.getSkuName());
                        giftInfo.setSkuId(product.getSkuId());
                        giftInfo.setQuantity(product.getQuantity());
                        giftInfo.setSpu(product.getCustomSpu());
                        orderDiscountDetailDTO.setGiftInfo(giftInfo);
                        //detail.getActivities().add(orderDiscountDetailDTO);
                        log.info("赠品活动信息 goodsActivityDetailDTO: {}", JSON.toJSONString(goodsActivityDetailDTO));
                    }
                }
            }
        }
    }

    private OrderProductDetailDTO setSkuInfo2ActivityInfoAndReturn(List<OrderProductDetailDTO> productDetailDTOSList, String channelItemId,
                                                                   GoodsActivityDetailDTO goodsActivityDetailDTO) {
        OrderProductDetailDTO product = findProduct4List(productDetailDTOSList, channelItemId);
        if (product != null) {
            goodsActivityDetailDTO.setCustomSkuId(product.getSkuId());
            goodsActivityDetailDTO.setSkuCount(product.getQuantity());
        }
        return product;
    }

    private OrderProductDetailDTO findProduct4List(List<OrderProductDetailDTO> productDetailDTOSList, String channelItemId) {
        return productDetailDTOSList.stream().filter(product -> product.getChannelItemId().equals(channelItemId)).findAny().orElse(null);
    }


    private String decrypt(AppMessage appMessage, String message) {
        if (MccConfigUtil.useYzHttpDecrypt(appMessage.getClientId())) {
            return decryptByHttp(appMessage, message);
        }

        try {
            SecretClient secretClient = getClientSecret(appMessage);

            if (StringUtils.isEmpty(message)) {
                return message;
            }
            if (secretClient.isEncrypt(message)) {
                return secretClient.decrypt(Long.parseLong(appMessage.getGrantId()), message);
            }
            return message;
        }
        catch (Exception e) {
            log.error("decrypt message error, message:{},exception:", message, e);
            return message;
        }
    }

    private String decryptByHttp(AppMessage appMessage, String message) {
        if (StringUtils.isEmpty(message)) {
            return message;
        }

        SecretServer secretServer = new SecretServer(null);

        if (!secretServer.isEncrypt(message)) {
            return message;
        }

        YouzanCloudSecretDecryptSingleParams decryptSingleParams = new YouzanCloudSecretDecryptSingleParams();
        decryptSingleParams.setSource(message);

        YouzanCloudSecretDecryptSingleResult decryptSingleResult = null;
        try {
            decryptSingleResult = getResult4YouZan(appMessage, new YouzanCloudSecretDecryptSingle(decryptSingleParams), YouzanCloudSecretDecryptSingleResult.class);
        } catch (SDKException e) {
            throw new BizException("有赞数据解密出现异常", e);
        }

        if (decryptSingleResult == null || decryptSingleResult.getCode() != 200) {
            log.warn("有赞数据解密失败,{}", decryptSingleResult);
            throw new BizException("有赞数据解密失败");
        }

        return decryptSingleResult.getData();
    }

    private ResultStatus buildSuccessResultStatus() {
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ResultCode.SUCCESS.getCode());
        resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
        return resultStatus;
    }

    /**
     * 解析有赞的自提时间 参考格式为：{"user_time":"09月18日 08:01-09:01"}
     * @param timeString
     * @return 返回起止时间
     */
    public static List<Long> translateDateToTimeStamp(String timeString) {
        List<Long> timeStampList = new ArrayList<>();
        try {
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            String[] dateList = timeString.split("\\s+");
            if (dateList.length != 2) {
                return Collections.emptyList();
            }
            if(StringUtils.isNotBlank(timeString) && timeString.contains("年")){
                Arrays.stream(dateList[1].split("-")).forEach(item -> {
                    StringBuilder dateStr = new StringBuilder()
                            .append(dateList[0])
                            .append(" ")
                            .append(item);
                    long time = (new SimpleDateFormat("yyyy年MM月dd日 HH:mm")).parse(dateStr.toString(), new ParsePosition(0)).getTime();
                    if (time > 0) {
                        timeStampList.add(time);
                    }
                });
                log.info("YzToolChannelOrderServiceImpl.translateDateToTimeStamp timeStampList: {}",JSON.toJSONString(timeStampList));
                return timeStampList;
            }
            Arrays.stream(dateList[1].split("-")).forEach(item -> {
                StringBuilder dateStr = new StringBuilder().append(year)
                        .append("年")
                        .append(dateList[0])
                        .append(" ")
                        .append(item);
                long time = (new SimpleDateFormat("yyyy年MM月dd日 HH:mm")).parse(dateStr.toString(), new ParsePosition(0)).getTime();
                if (time > 0) {
                    timeStampList.add(time);
                }
            });
            return timeStampList;
        } catch (Exception e) {
            log.warn("parse youzan delivery time error timeString:{}", timeString, e);
        }
        return Collections.emptyList();
    }

    /**
     * 补充有赞订单活动分摊信息
     */
    private void setActivityShareDetailList(AppMessage appMessage, ChannelOrderDetailDTO orderDetail) {
        YouzanTradeOpenPcOrderPromotionParams promotionParams = new YouzanTradeOpenPcOrderPromotionParams();
        promotionParams.setOrderSharePromotion(Boolean.TRUE);
        promotionParams.setPostagePromotion(Boolean.TRUE);
        promotionParams.setGoodsLevelPromotion(Boolean.TRUE);
        promotionParams.setTid(orderDetail.getChannelOrderId());
        try {
            YouzanTradeOpenPcOrderPromotionResult promotionResult = getResult4YouZan(appMessage, new YouzanTradeOpenPcOrderPromotion(promotionParams), YouzanTradeOpenPcOrderPromotionResult.class);
            log.info("查询有赞订单优惠信息结果：{}", JSON.toJSONString(promotionResult));
            if (!promotionResult.getSuccess() || Objects.isNull(promotionResult.getData())) {
                return;
            }

            // orderProductDetail的channelSkuId对应有赞商品的itemId，需利用此id与优惠信息中的goodsId对应
            Map<String, OrderProductDetailDTO> orderProductDetailMap = new HashMap<>();
            for (OrderProductDetailDTO orderProductDetail : orderDetail.getSkuDetails()) {
                orderProductDetailMap.put(orderProductDetail.getChannelSkuId(), orderProductDetail);
            }
            Map<String, String> promotionTypeInfo = getPromotionTypeInfo();
            YouzanTradeOpenPcOrderPromotionResult.YouzanTradeOpenPcOrderPromotionResultData promotionResultData = promotionResult.getData();
            List<ActivityShareDetailDTO> activityShareDetailDTOList = promotionResultData.getGoods().stream()
                    // 排除订单商品列表中没有的和优惠信息为空的
                    .filter(item -> orderProductDetailMap.containsKey(String.valueOf(item.getGoodsId())) && CollectionUtils.isNotEmpty(item.getPromotions()))
                    .map(item -> {
                        OrderProductDetailDTO orderProductDetailDTO = orderProductDetailMap.get(String.valueOf(item.getGoodsId()));
                        // 每件商品可能有多个优惠，循环解析
                        return item.getPromotions().stream().map(promotion -> buildActivityShareDetailDTO(promotion, promotionTypeInfo, orderProductDetailDTO)).collect(Collectors.toList());
                    })
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            log.info("有赞订单活动分摊结果：{}", GsonUtils.toJSONString(activityShareDetailDTOList));
            orderDetail.setActivityShareDetailList(activityShareDetailDTOList);
        } catch (SDKException e) {
            log.error("查询有赞订单优惠信息SDK异常, 参数->{}, exception:", promotionParams, e);
        } catch (Exception e) {
            log.error("活动分摊异常, exception:", e);
        }
    }

    /**
     * 查询配置的有赞渠道优惠信息
     */
    private Map<String, String> getPromotionTypeInfo() {
        List<ChannelActivityTypeInfo> actTypeList = MccConfigUtil.getActivityDict();
        Map<String, String> promotionTypeInfo = new HashMap<>();
        actTypeList.stream().filter(item -> YOU_ZAN.equals(item.getChl())).forEach(item -> promotionTypeInfo.put(item.getName(), item.getCode()));
        return promotionTypeInfo;
    }

    private ActivityShareDetailDTO buildActivityShareDetailDTO(YouzanTradeOpenPcOrderPromotionResult.YouzanTradeOpenPcOrderPromotionResultPromotions promotion,
                                                               Map<String, String> promotionTypeInfo,
                                                               OrderProductDetailDTO productDetailDTO) {
        ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
        activityShareDetailDTO.setActivityId(Optional.ofNullable(promotion.getPromotionId()).map(String::valueOf).orElse(promotion.getPromotionTitle()))
                .setChannelPromotionType(Optional.ofNullable(promotionTypeInfo.get(promotion.getPromotionTypeName())).orElse(promotion.getPromotionTypeName()))
                .setSkuId(productDetailDTO.getSkuId())
                .setCustomSpu(productDetailDTO.getCustomSpu())
                .setSkuCount(productDetailDTO.getQuantity())
                .setPromotionCount(productDetailDTO.getQuantity())
                .setPromotionRemark(promotion.getPromotionTitle())
                .setChannelCost(0)
                .setTenantCost(Optional.ofNullable(promotion.getDecrease()).map(Long::intValue).orElse(0))
                .setTotalOriginPrice(productDetailDTO.getOriginalPrice() * productDetailDTO.getQuantity());
        activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() - activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());
        return activityShareDetailDTO;
    }
}
