package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ServicesLocator implements ApplicationContextAware {
    private static ApplicationContext ctx = null;

    /**
     * 查找Spring bean
     * @param name
     * @return
     */
    public static Object getBean(String name) {
        if (ctx == null) {
            log.error("ctx if null!");
            return null;
        }
        if (ctx.containsBean(name)) {
            return ctx.getBean(name);
        }
        name = name + "Impl";
        if (ctx.containsBean(name)) {
            return ctx.getBean(name);
        }
        return null;
    }

    /**
     * 获取实现某个接口的实现类集合
     * @param type
     * @return
     */
    public static Map<String, ?> getBeans(Class<?> type) {
        return ctx.getBeansOfType(type);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (ctx == null) {
            ctx = applicationContext;
        }
    }
}
