package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class TxdRefundCompleteMsg {

    @JSONField(name = "store_id")
    private String storeId;
    @JSONField(name = "order_status")
    private String orderStatus;
    @JSONField(name = "out_sub_order_id")
    private String outSubOrderId;
    @JSONField(name = "shop_id")
    private String shopId;
    @JSONField(name = "merchant_code")
    private String merchantCode;
    @JSONField(name = "out_main_refund_id")
    private String outMainRefundId;
    @JSONField(name = "order_from")
    private String orderFrom;
    @JSONField(name = "biz_sub_refund_id")
    private String bizSubRefundId;
}
