package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;


import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.StoreIdNotExistException;
import com.sankuai.sgfnqnh.poi.api.client.thrift.ChannelPoiThriftService;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.ChannelPoiBaseInfoDTO;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.request.RelQueryByAppKeyAndChannelPoiCodesRequest;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.request.RelationQueryByChannelPoiCodesRequest;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.response.PoiChannelPoiRelationResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/12
 * @email lishuzhou@meituan
 */

@Slf4j
@Service
public class PoiApiChannelPoiThriftServiceProxy {

    @Resource
    private ChannelPoiThriftService poiApiChannelPoiThriftService;

    public Optional<ChannelPoiBaseInfoDTO> getElmChannelPoiBaseInfoDTO(String appPoiCode, boolean useCache) {
        List<String> channelPoiCodes = new ArrayList<>();
        channelPoiCodes.add(appPoiCode);
        RelationQueryByChannelPoiCodesRequest request = new RelationQueryByChannelPoiCodesRequest(ChannelTypeEnum.ELEM.getCode(), channelPoiCodes, useCache);
        try {
            PoiChannelPoiRelationResponse response = poiApiChannelPoiThriftService.queryRelationByChannelPoiCodes(request);
            if (response == null || !response.getStatus().isSuccess()){
                log.info("PoiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO:饿了么ISV应用获取租户信息失败,渠道门店Id:{}", appPoiCode);
                return Optional.empty();
            }
            // 如果返回的渠道门店列表为空，该门店未接入牵牛花，不处理该请求
            if (CollectionUtils.isEmpty(response.getChannelPoiBaseInfoDTOList())) {
                log.info("PoiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO:饿了么ISV渠道门店未绑定租户,渠道门店Id:{}", appPoiCode);
                throw new StoreIdNotExistException();
            }

            // 如果返回的渠道门店列表不唯一，无法识别消息的所属牵牛花门店
            if (response.getChannelPoiBaseInfoDTOList().size() != NumberUtils.INTEGER_ONE) {
                log.info("PoiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO:渠道门店绑定租户不唯一,渠道门店Id:{},绑定租户数量:{}", appPoiCode, response.getChannelPoiBaseInfoDTOList().size());
                throw new StoreIdNotExistException();
            }

            return Optional.ofNullable(response.getChannelPoiBaseInfoDTOList().get(0));
        } catch (StoreIdNotExistException e) {
            throw e;
        } catch (Exception e) {
            log.error("PoiApiChannelPoiThriftServiceProxy.getElmChannelPoiBaseInfoDTO:饿了么ISV应用获取租户信息异常,渠道门店ID:{},exception:{}", appPoiCode, e);
            return Optional.empty();
        }
    }

    public Optional<ChannelPoiBaseInfoDTO> queryRelationByChannelPoiCode(ChannelTypeEnum channelType, String channelPoiCode) {
        List<ChannelPoiBaseInfoDTO> list = this.queryRelationByChannelPoiCodes(channelType, Collections.singletonList(channelPoiCode), true);
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        // 如果返回的渠道门店列表不唯一，无法识别消息的所属牵牛花门店
        if (list.size() != NumberUtils.INTEGER_ONE) {
            log.error("PoiApiChannelPoiThriftServiceProxy.queryRelationByChannelPoiCode:渠道门店绑定租户不唯一,channelType: {},渠道门店Id:{},绑定租户数量:{}", channelType, channelPoiCode, list.size());
            return Optional.empty();
        }
        return Optional.ofNullable(list.get(0));
    }

    public List<ChannelPoiBaseInfoDTO> queryRelationByChannelPoiCodes(ChannelTypeEnum channelType, List<String> channelPoiCodes, boolean useCache) {
        RelationQueryByChannelPoiCodesRequest request = new RelationQueryByChannelPoiCodesRequest(channelType.getCode(), channelPoiCodes, useCache);
        try {
            PoiChannelPoiRelationResponse response = poiApiChannelPoiThriftService.queryRelationByChannelPoiCodes(request);
            if (response == null || !response.getStatus().isSuccess()){
                log.error("PoiApiChannelPoiThriftServiceProxy.queryRelationByChannelPoiCodes失败，channelType: {}, 渠道门店Id: {}",channelType, channelPoiCodes);
                return Collections.emptyList();
            }
            // 如果返回的渠道门店列表为空，该门店未接入牵牛花，不处理该请求
            if (CollectionUtils.isEmpty(response.getChannelPoiBaseInfoDTOList())) {
                log.error("PoiApiChannelPoiThriftServiceProxy.queryRelationByChannelPoiCodes 渠道门店未绑定租户,channelType: {}, 渠道门店Id: {}", channelType, channelPoiCodes);
                return Collections.emptyList();
            }
            return response.getChannelPoiBaseInfoDTOList();
        } catch (StoreIdNotExistException e) {
            throw e;
        } catch (Exception e) {
            log.error("PoiApiChannelPoiThriftServiceProxy.queryRelationByChannelPoiCodes获取租户信息异常,channelType: {}, 渠道门店Id: {},exception:{}", channelType, channelPoiCodes, e);
            return Collections.emptyList();
        }
    }

    public List<ChannelPoiBaseInfoDTO> queryRelByAppKeyAndChannelPoiCodes(String appKey, List<String> channelPoiCodes, Integer channelId) {
        RelQueryByAppKeyAndChannelPoiCodesRequest request = new RelQueryByAppKeyAndChannelPoiCodesRequest(channelId,appKey,channelPoiCodes);
        try {
            PoiChannelPoiRelationResponse response = poiApiChannelPoiThriftService.queryRelByAppKeyAndChannelPoiCodes(request);
            if (response == null || !response.getStatus().isSuccess()){
                log.error("PoiApiChannelPoiThriftServiceProxy.queryRelationByChannelPoiCodes失败，channelId: {}, 渠道门店Id: {}，appKey:{}",channelId, channelPoiCodes,appKey);
                return Collections.emptyList();
            }
            // 如果返回的渠道门店列表为空，该门店未接入牵牛花，不处理该请求
            if (CollectionUtils.isEmpty(response.getChannelPoiBaseInfoDTOList())) {
                log.info("PoiApiChannelPoiThriftServiceProxy.queryRelationByChannelPoiCodes 渠道门店未绑定租户,channelId: {}, 渠道门店Id: {},appKey:{}", channelId, channelPoiCodes,appKey);
                return Collections.emptyList();
            }
            return response.getChannelPoiBaseInfoDTOList();
        } catch (Exception e) {
            log.error("PoiApiChannelPoiThriftServiceProxy.queryRelationByChannelPoiCodes获取租户信息异常,channelId: {}, 渠道门店Id: {},appKey:{},exception:{}", channelId, channelPoiCodes,appKey, e);
            return Collections.emptyList();
        }
    }

}
