package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.doudian.open.api.afterSale_Detail.data.*;
import com.doudian.open.api.afterSale_Detail.data.AfterSaleInfo;
import com.doudian.open.api.order_orderDetail.data.*;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.enums.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.douyin.MockDouYinPromotionInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderAfsApplyDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.RefundProductDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.NeverNpeUtil;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.ServiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class DouyinOrderConverterUtil {

    public static OrderAfsApplyDTO convertAfsApplyDTO(AfterSaleDetailData data) {
        OrderAfsApplyDTO orderAfsApplyDTO = new OrderAfsApplyDTO();
        OrderInfo orderInfo = data.getOrderInfo();
        ProcessInfo processInfo = data.getProcessInfo();
        AfterSaleInfo afterSaleInfo = NeverNpeUtil.exec(() -> data.getProcessInfo().getAfterSaleInfo());
        ArbitrateInfo arbitrateInfo = NeverNpeUtil.exec(() -> data.getProcessInfo().getArbitrateInfo());
        if (orderInfo == null || processInfo == null) {
            return orderAfsApplyDTO;
        }
        // mock抖音退单数据
        //mockDouYinRefundPromotion(afterSaleInfo);

        //基础信息
        orderAfsApplyDTO.setChannelType(ChannelTypeEnum.DOU_YIN.getCode());
        orderAfsApplyDTO.setChannelOrderId(orderInfo.getShopOrderId().toString());
        orderAfsApplyDTO.setAfterSaleId(afterSaleInfo.getAfterSaleId().toString());
        orderAfsApplyDTO.setRefundApplyTime(DateUtils.seconds2MS(afterSaleInfo.getApplyTime()));
        orderAfsApplyDTO.setRefundPrice(afterSaleInfo.getRefundTotalAmount().intValue());
        //退单类型
        if (afterSaleInfo.getAfterSaleType() == DouyinAfterSaleTypeEnum.RETURN_GOODS.getCode()) {
            orderAfsApplyDTO.setServiceType(ServiceTypeEnum.REFUND_GOODS.getCode());
        } else {
            orderAfsApplyDTO.setServiceType(ServiceTypeEnum.REFUND.getCode());
        }
        orderAfsApplyDTO.setChannelAfsStatus(afterSaleInfo.getAfterSaleStatus().toString());
        //退款审核时间
        orderAfsApplyDTO.setRefundAuditTime(afterSaleInfo.getUpdateTime());
        if (StringUtils.isNotBlank(afterSaleInfo.getReasonRemark())) {
            orderAfsApplyDTO.setApplyReason(afterSaleInfo.getReason() + ":" + afterSaleInfo.getReasonRemark());
        } else {
            orderAfsApplyDTO.setApplyReason(afterSaleInfo.getReason());
        }

        orderAfsApplyDTO.setRefundImgUrl(JSON.toJSONString(afterSaleInfo.getEvidence()));
        //仲裁信息
        if (Objects.isNull(arbitrateInfo)) {
            orderAfsApplyDTO.setIsAppeal(false);
        } else {
            if (DouyinArbitrateStatus.valueOfEnum(arbitrateInfo.getArbitrateStatus()) == DouyinArbitrateStatus.ARBITRATING || DouyinArbitrateStatus.valueOfEnum(arbitrateInfo.getArbitrateStatus()) == DouyinArbitrateStatus.ARBITRATE_NEGOTIATION) {
                orderAfsApplyDTO.setIsAppeal(true);
            } else {
                orderAfsApplyDTO.setIsAppeal(false);
            }
        }
        //退单商品明细信息--抖音没有给退款明细，只给了退款关联的正单商品信息
        orderAfsApplyDTO.setAfsProductList(reverseProductMapping(orderInfo, afterSaleInfo));
        //发货前退款或者是退款商品不止一个的情况为整单退
        if ((afterSaleInfo.getAfterSaleType() == 2 && afterSaleInfo.getApplyRole() == DouyinApplyRoleEnum.BUYER.getCode()) || (orderInfo.getSkuOrderInfos() != null && orderInfo.getSkuOrderInfos().size() > 1)) {
            orderAfsApplyDTO.setRefundType(RefundTypeEnum.ALL.getValue());
        }
        //操作人
        orderAfsApplyDTO.setApplyOpType(ChannelStatusConvertUtil.dyOpUserType(afterSaleInfo.getApplyRole()));
        //运费优惠
        orderAfsApplyDTO.setPlatLogisticsPromotion(Optional.ofNullable(afterSaleInfo.getPostDiscountReturnAmount()).orElse(0L).intValue()
                + Optional.ofNullable(NeverNpeUtil.toInt(afterSaleInfo.getKolPostDiscountReturnAmount())).orElse(0));
        orderAfsApplyDTO.setPoiLogisticsPromotion(afterSaleInfo.getShopPostDiscountReturnAmount().intValue());
        //运费
        orderAfsApplyDTO.setFreight(orderAfsApplyDTO.getPoiLogisticsPromotion()
                + orderAfsApplyDTO.getPlatLogisticsPromotion()
                + Optional.ofNullable(afterSaleInfo.getRefundPostAmount()).orElse(0L).intValue());
        //商品退款金额
        Long itemMoney = Optional.ofNullable(afterSaleInfo.getRefundTotalAmount()).orElse(0L) - Optional.ofNullable(afterSaleInfo.getRefundPostAmount()).orElse(0L) - Optional.ofNullable(afterSaleInfo.getRefundPackingChargeAmount()).orElse(0L);
        orderAfsApplyDTO.setItemRefundAmt(itemMoney.intValue());
        //包装费
        orderAfsApplyDTO.setPayPackageFee(afterSaleInfo.getRefundPackingChargeAmount().intValue());
        if(afterSaleInfo.getStatusDeadline() != null){
            orderAfsApplyDTO.setProcessDeadline(DateUtils.seconds2MS(afterSaleInfo.getStatusDeadline()));
        }
        return orderAfsApplyDTO;

    }

    private static void mockDouYinRefundPromotion(AfterSaleInfo afterSaleInfo){
        try {
            String mockDouYinPromotionInfoStr = MccConfigUtil.getMockDouYinRefundPromotionInfo();
            log.info("抖音促销退单信息mock字符串:{}", mockDouYinPromotionInfoStr);
            MockDouYinPromotionInfo mockDouYinPromotionInfo = JSON.parseObject(mockDouYinPromotionInfoStr, MockDouYinPromotionInfo.class);
            if (mockDouYinPromotionInfo == null) {
                return;
            }

            // 运费优惠
            if(Optional.ofNullable(mockDouYinPromotionInfo.getFreightDiscountPlat()).orElse(-1) > -1){
                afterSaleInfo.setPostDiscountReturnAmount(mockDouYinPromotionInfo.getFreightDiscountPlat().longValue());
            }
            if(Optional.ofNullable(mockDouYinPromotionInfo.getFreightDiscountPoi()).orElse(-1) > -1){
                afterSaleInfo.setShopPostDiscountReturnAmount(mockDouYinPromotionInfo.getFreightDiscountPoi().longValue());
            }
            // 政府补贴
            if(Optional.ofNullable(mockDouYinPromotionInfo.getGovernmentReducePlat()).orElse(-1) > -1){
                afterSaleInfo.setGovernmentChannelSubsidyAmount(mockDouYinPromotionInfo.getGovernmentReducePlat().longValue());
            }

            log.info("抖音退单促销信息mock成功后返回值:{}", JSON.toJSONString(afterSaleInfo));
        } catch (Exception e) {
            log.error("抖音退单促销信息mock失败 ", e);
        }

    }

    public static List<RefundProductDTO> reverseProductMapping(OrderInfo orderInfo, AfterSaleInfo afterSaleInfo) {
        List<RefundProductDTO> ret = new LinkedList<>();

        if (CollectionUtils.isEmpty(orderInfo.getSkuOrderInfos())) {
            return ret;
        }
        //退单关联的商品信息
        for (SkuOrderInfosItem skuOrderInfo : orderInfo.getSkuOrderInfos()) {
            RefundProductDTO refundProductDTO = new RefundProductDTO();
            refundProductDTO.setSkuId(NeverNpeUtil.toStr(skuOrderInfo.getSkuId()));
            refundProductDTO.setSkuName(skuOrderInfo.getProductName());
            //处理规格
            refundProductDTO.setSpec(obtainSkuSpecInfo(skuOrderInfo.getSkuSpec()));
            //多个商品是整单退的场景或者发货前退款
            if (afterSaleInfo.getAfterSaleType() == 2 || (orderInfo.getSkuOrderInfos() != null && orderInfo.getSkuOrderInfos().size() > 1)) {
                refundProductDTO.setCount(NeverNpeUtil.toInt(skuOrderInfo.getAfterSaleItemCount(), 0));
            } else {//部分退
                refundProductDTO.setCount(NeverNpeUtil.toInt(afterSaleInfo.getAfterSaleApplyCount(), 0));
            }


            refundProductDTO.setCustomSpu("");
            //商品金额
            Long itemMoney = Optional.ofNullable(afterSaleInfo.getRefundTotalAmount()).orElse(0L) - Optional.ofNullable(afterSaleInfo.getRefundPostAmount()).orElse(0L) - Optional.ofNullable(afterSaleInfo.getRefundPackingChargeAmount()).orElse(0L);
            refundProductDTO.setSkuRefundAmount(itemMoney.intValue());
            refundProductDTO.setChannelOrderItemId(NeverNpeUtil.toStr(skuOrderInfo.getSkuOrderId()));
            //商家优惠
            refundProductDTO.setRefundPoiPromotion(afterSaleInfo.getShopDiscountReturnAmount().intValue());
            //平台优惠 外加 政府补助金额
            refundProductDTO.setRefundPlatPromotion(
                    afterSaleInfo.getPlatformDiscountReturnAmount().intValue()
                    + Optional.ofNullable(afterSaleInfo.getGovernmentChannelSubsidyAmount()).orElse(0L).intValue()
            );
            ret.add(refundProductDTO);
            // 赠品处理
            if(CollectionUtils.isNotEmpty(skuOrderInfo.getGivenSkuDetails())) {
                for (GivenSkuDetailsItem givenSkuDetail : skuOrderInfo.getGivenSkuDetails()) {
                    RefundProductDTO refundGiftDTO = new RefundProductDTO();
                    refundGiftDTO.setFreeGift(true);
                    //refundGiftDTO.setSkuId(NeverNpeUtil.toStr(givenSkuDetail.getSkuId()));
                    refundGiftDTO.setSkuName(givenSkuDetail.getProductName());
                    refundGiftDTO.setCount(NeverNpeUtil.toInt(givenSkuDetail.getItemOrderNum(), 0));
                    refundGiftDTO.setCustomSpu("");
                    //商品金额
                    refundGiftDTO.setSkuRefundAmount(0);
                    refundGiftDTO.setChannelOrderItemId(NeverNpeUtil.toStr(givenSkuDetail.getSkuOrderId()));
                    //商家优惠
                    refundGiftDTO.setRefundPoiPromotion(0);
                    //平台优惠
                    refundGiftDTO.setRefundPlatPromotion(0);
                    ret.add(refundGiftDTO);
                }
            }
        }
        return ret;

    }

    public static String obtainSpecInfo(List<SpecItem> spec) {
        if (CollectionUtils.isEmpty(spec)) {
            return "";
        }
        StringBuilder specInfo = new StringBuilder();
        for (SpecItem item : spec) {
            specInfo.append(item.getName()).append(":").append(item.getValue()).append(",");
        }
        // specInfo.deleteCharAt(specInfo.length() - 1);
        specInfo.setCharAt(specInfo.length() - 1, ';');
        return specInfo.toString();
    }

    public static String obtainSkuSpecInfo(List<SkuSpecItem> spec) {
        if (CollectionUtils.isEmpty(spec)) {
            return "";
        }
        StringBuilder specInfo = new StringBuilder();
        for (SkuSpecItem item : spec) {
            specInfo.append(item.getName()).append(":").append(item.getValue()).append(",");
        }
        // specInfo.deleteCharAt(specInfo.length() - 1);
        specInfo.setCharAt(specInfo.length() - 1, ';');
        return specInfo.toString();
    }

    public static List<CouponInfoItem> convertCouponInfo(List<CouponInfoItem_6_6> couponInfoItem_6_6List) {
        if (CollectionUtils.isEmpty(couponInfoItem_6_6List)) {
            return Lists.newArrayList();
        }

        return couponInfoItem_6_6List.stream().map(item_6_6 -> {
            CouponInfoItem couponInfoItem = new CouponInfoItem();
            couponInfoItem.setCouponId(item_6_6.getCouponId());
            couponInfoItem.setCouponType(item_6_6.getCouponType());
            couponInfoItem.setCouponMetaId(item_6_6.getCouponMetaId());
            couponInfoItem.setCouponAmount(item_6_6.getCouponAmount());
            couponInfoItem.setCouponName(item_6_6.getCouponName());
            couponInfoItem.setShareDiscountCost(item_6_6.getShareDiscountCost());
            couponInfoItem.setExtraMap(item_6_6.getExtraMap());
            return couponInfoItem;
        }).collect(Collectors.toList());
    }

    public static List<FullDiscountInfoItem> convertFullDiscountInfo(List<FullDiscountInfoItem_6_6> fullDiscountInfoItem_6_6List) {
        if (CollectionUtils.isEmpty(fullDiscountInfoItem_6_6List)) {
            return Lists.newArrayList();
        }

        return fullDiscountInfoItem_6_6List.stream().map(item_6_6 -> {
            FullDiscountInfoItem fullDiscountInfoItem = new FullDiscountInfoItem();
            fullDiscountInfoItem.setCampaignId(item_6_6.getCampaignId());
            fullDiscountInfoItem.setCampaignType(item_6_6.getCampaignType());
            fullDiscountInfoItem.setShareDiscountCost(item_6_6.getShareDiscountCost());
            fullDiscountInfoItem.setCampaignName(item_6_6.getCampaignName());
            fullDiscountInfoItem.setCampaignAmount(item_6_6.getCampaignAmount());
            fullDiscountInfoItem.setCampaignSubType(item_6_6.getCampaignSubType());
            fullDiscountInfoItem.setExtraMap(item_6_6.getExtraMap());
            return fullDiscountInfoItem;
        }).collect(Collectors.toList());
    }

    public static boolean convertIsGift(String givenProductType) {
        // 没有赠品的这个字段可能为空
        return Objects.equals(givenProductType, "FREE");
    }
}
