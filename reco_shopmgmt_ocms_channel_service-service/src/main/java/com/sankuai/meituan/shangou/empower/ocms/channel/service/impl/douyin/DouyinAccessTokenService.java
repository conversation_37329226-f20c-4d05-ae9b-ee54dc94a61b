package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.doudian.open.api.token.AccessTokenData;
import com.doudian.open.api.token.AccessTokenResponse;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.AccessTokenBuilder;
import com.doudian.open.core.DefaultDoudianOpClient;
import com.doudian.open.core.DoudianOpConfig;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelVirtualAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinAppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DouyinResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.ChannelVirtualConfigMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.ChannelAppMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.AuthCallbackThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.sgfnqnh.poi.api.client.thrift.AppThriftService;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.application.request.AppTokenRequest;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.application.response.AppTokenResponse;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.auth.SetAccessConfigDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.auth.request.SetAccessConfigRequest;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @program: reco_shopmgmt_ocms_channel_service
 * @description: 抖音token获取工具类
 * @author: jinyi
 * @create: 2024-01-01 19:34
 **/
@Slf4j
@Service
public class DouyinAccessTokenService {

    private static final String REDIS_TOKEN_CATEGORY = "douyin-token";

    private static final String TOKEN_DELIMITER = "_token_";

    private static final int RETRY_MAX = 3;

    @Resource(name = "douyinTokenRedisClient")
    private RedisStoreClient redisStoreClient;

    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Autowired
    private AppThriftService appThriftService;

    @Autowired
    private ChannelAppMessageProducer channelAppMessageProducer;

    @Autowired
    private ChannelVirtualConfigMapper channelVirtualConfigMapper;

    @Autowired
    private AuthCallbackThriftServiceProxy authCallbackThriftServiceProxy;

    /**
     * 根据授权码获取token
     * 
     * @param appMessage
     * @return
     */
    private AccessToken getAccessToken(DouyinAppMessage appMessage) {
        DoudianOpConfig config = new DoudianOpConfig();
        config.setAppKey(appMessage.getAppKey());
        config.setAppSecret(appMessage.getSecret());
        AccessToken accessToken = AccessTokenBuilder.build(config, DefaultDoudianOpClient.getDefaultClient(),
                appMessage.getAuthCode());

        log.info("getAccessToken, appMessage:{}, token:{}", JacksonUtils.toJson(appMessage),
                JacksonUtils.toJson(accessToken));

        return accessToken;
    }

    /**
     * 查询授权回调生成的token
     *
     * @param appMessage
     * @return
     */
    private AccessToken getAccessTokenFromPoi(DouyinAppMessage appMessage) {
        AppTokenRequest appTokenRequest = new AppTokenRequest();
        appTokenRequest.setChannelId(EnhanceChannelType.DY.getChannelId());
        appTokenRequest.setAppKey(appMessage.getAppKey());
        appTokenRequest.setShopId(appMessage.getShopId());

        AppTokenResponse response = appThriftService.queryToken(appTokenRequest);
        log.info("getAccessTokenFromPoi, appMessage:{}, response:{}", JacksonUtils.toJson(appMessage),
                JacksonUtils.toJson(response));
        if (Objects.isNull(response) || Objects.isNull(response.getData())) {
            throw new BizException(ResultCode.FAIL.getCode(), "获取抖音虚拟账号token信息失败");
        }

        // 构建token数据
        AccessTokenData tokenData = new AccessTokenData();
        tokenData.setAccessToken(response.getData().getAccessToken());
        tokenData.setRefreshToken(response.getData().getRefreshToken());
        tokenData.setExpiresIn(response.getData().getAccessTokenExpireDate() - System.currentTimeMillis());
        tokenData.setShopId(appMessage.getShopId());

        AccessTokenResponse tokenResponse = new AccessTokenResponse();
        tokenResponse.setCode(DouyinResultCodeEnum.SUCCESS.getCode());
        tokenResponse.setData(tokenData);

        return AccessToken.wrapWithAppKey(tokenResponse, appMessage.getAppKey());
    }

    private void setToken2Redis(DouyinAppMessage appMessage, AccessToken accessToken, Long tenantId) {
        if (!accessToken.isSuccess()) {
            throw new BizException(ResultCode.FAIL.getCode(), accessToken.getMsg());
        }

        AccessTokenData tokenData = accessToken.getData();

        setToken2Redis(appMessage.getAppKey(), tokenData, tenantId);
    }

    public void setToken2Redis(String appKey, AccessTokenData tokenData, Long tenantId) {
        DouyinTokenMessage tokenMessage = DouyinTokenMessage.convertFromAccessTokenData(tokenData, tenantId);

        long expireSeconds = TimeUnit.DAYS.toSeconds(28);
        redisStoreClient.set(getRedisStoreKey(appKey, tokenData.getShopId()),
                JacksonUtils.toJson(tokenMessage), (int)expireSeconds);
    }

    /**
     * 删除token
     * @param appKey
     * @param shopId
     */
    public void deleteTokenFromRedis(String appKey, String shopId) {
        redisStoreClient.delete(getRedisStoreKey(appKey, shopId));
    }

    /**
     * 从 Redis 查询token
     * 
     * @param appKey
     * @param shopId
     * @return
     */
    public DouyinTokenMessage getTokenFromRedis(String appKey, String shopId) {
        for (int retry = 0; retry < RETRY_MAX; retry++) {
            try {
                String accessToken = redisStoreClient.get(getRedisStoreKey(appKey, shopId));
                if (StringUtils.isBlank(accessToken)) {
                    log.info("getTokenFromRedis, accessToken is null, retry:{}", retry);
                    return null;
                }
                return JacksonUtils.parse(accessToken, DouyinTokenMessage.class);
            } catch (Exception e) {
                log.error("douyin getTokenFromRedis error, retry:{}, exception:", retry, e);
            }
        }
        throw new BizException("douyin getTokenFromRedis error");
    }

    private StoreKey getRedisStoreKey(String appKey, String shopId) {
        return new StoreKey(REDIS_TOKEN_CATEGORY, appKey, TOKEN_DELIMITER, shopId);
    }

    public ResultStatus authCallBack(String appKey, String secret, String authCode) {
        DouyinAppMessage appMessage = DouyinAppMessage.builder().appKey(appKey).secret(secret).authCode(authCode)
                .build();
        AccessToken accessToken = getAccessToken(appMessage);
        log.info("douyin auth callback, appKey:{}, secret:{}, code:{}, token:{}", appKey, secret, authCode,
                JacksonUtils.toJson(accessToken));
        if (!DouyinResultCodeEnum.SUCCESS.getCode().equals(accessToken.getCode())) {
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg(accessToken.getMsg());
        }

        // 抖音官方测试门店，用于机器审核回调地址，不需要处理
        if (MccConfigUtil.getDouyinShopIdWhitelist().contains(accessToken.getShopId())){
            log.info("抖音官方测试门店，用于机器审核回调地址，不需要处理, shopId={}",accessToken.getShopId());
            return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
        }

        // token转发到poi-channel
        sendTokenToPoiChannel(appKey, secret, accessToken);
        

        // 根据shop_id + appKey查询租户id，并将租户信息放到redis中
        String shopId = accessToken.getShopId();
        CopAccessConfigDO copAccessConfigDO = copAccessConfigService.selectDouyinByTenantAppIdAndShopId(appKey, shopId);
        if (Objects.isNull(copAccessConfigDO)) {
            log.info("douyin auth callback，查询cop配置信息不存在，appKey={}, shopId={}", appKey, shopId);
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg("shopId对应的应用不存在");
        }

        setToken2Redis(appMessage, accessToken, copAccessConfigDO.getTenantId());

        // 发送mafka消息
        sendAuthCallbackMsg(copAccessConfigDO);


        return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
    }

    /**
     * token转发到poi-channel
     * @param appKey
     * @param secret
     * @param accessToken
     */
    private void sendTokenToPoiChannel(String appKey, String secret, AccessToken accessToken) {
        try{
            Long now = System.currentTimeMillis();

            SetAccessConfigDTO.Token token = SetAccessConfigDTO.Token.builder()
                    .accessToken(accessToken.getAccessToken())
                    .refreshToken(accessToken.getRefreshToken())
                    .accessTokenGrantDate(now)
                    .accessTokenExpireDate(now + accessToken.getExpireIn() * 1000)
                    .refreshTokenGrantDate(now)
                    .build();

            SetAccessConfigDTO setAccessConfigDTO = SetAccessConfigDTO.builder()
                    .channelId(ChannelTypeEnum.DOU_YIN.getCode())
                    .channelType(1)
                    .appKey(appKey)
                    .appName(appKey)
                    .secret(secret)
                    .subAppKey(accessToken.getShopId())
                    .token(token)
                    .type(2)
                    .build();
            authCallbackThriftServiceProxy.setAccessConfig(new SetAccessConfigRequest(setAccessConfigDTO));
        } catch (Exception e) {
            log.error("抖音token转发到poi-channel失败", e);
        }
    }

        /**
         *  发送授权回调消息
         * @param copAccessConfigDO
         */
    public void sendAuthCallbackMsg(CopAccessConfigDO copAccessConfigDO) {
        // 构建消息对象
        CopAccessConfigDO configDO = new CopAccessConfigDO();
        configDO.setId(copAccessConfigDO.getId());
        configDO.setAppId(copAccessConfigDO.getAppId());
        configDO.setAppName(copAccessConfigDO.getAppName());
        configDO.setChainProduct(copAccessConfigDO.getChainProduct());
        configDO.setChannelId(copAccessConfigDO.getChannelId());
        configDO.setNeedContactSpec(copAccessConfigDO.getNeedContactSpec());
        configDO.setStandard(copAccessConfigDO.getStandard());
        configDO.setSysParams(copAccessConfigDO.getSysParams());
        configDO.setTenantAppId(copAccessConfigDO.getTenantAppId());
        configDO.setTenantId(copAccessConfigDO.getTenantId());
        configDO.setValid(copAccessConfigDO.getValid());
        configDO.setCtime(copAccessConfigDO.getCtime());
        configDO.setUtime(copAccessConfigDO.getUtime());

        // 发送消息
        channelAppMessageProducer.sendAuthCallbackMsg(configDO);
    }

    /**
     * 保存抖音虚拟账号的Token
     *
     * @param tenantId        租户ID
     * @param virtualSysParam 虚拟系统参数
     * @return ResultStatus 操作结果状态
     */
    public ResultStatus saveDouYinVirtualToken(Long tenantId, String virtualSysParam) {
        Map<String, Object> sysParam = JSON.parseObject(virtualSysParam);
        String appKey = (String)sysParam.get(ProjectConstant.DOUYIN_APP_KEY);
        String secret = (String)sysParam.get(ProjectConstant.DOUYIN_SECRET);
        String shopId = (String)sysParam.get(ProjectConstant.DOUYIN_SHOP_ID);
        if (StringUtils.isAnyBlank(appKey, secret, shopId)) {
            log.error("租户渠道授权参数错误，appKey:{}|secret:{}|shopId:{}", appKey, secret, shopId);
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg("租户渠道授权参数错误");
        }
        // 1.获取抖音虚拟账号token
        DouyinAppMessage appVirtualMessage = DouyinAppMessage.builder().appKey(appKey).secret(secret).shopId(shopId).build();
        AccessToken accessToken = null;
        int retry = 0;
        do {
            try {
                accessToken = getAccessTokenFromPoi(appVirtualMessage);
            } catch (Exception e) {
                log.error("获取抖音虚拟账号token信息失败, appKey:{}, secret:{}, code:{}", appKey, secret, shopId, e);
                return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg("获取抖音虚拟账号token信息失败");
            }
            retry++;
        }
        while (retry < ProjectConstant.DOUYIN_GET_TOKEN_MAX_RETRY && accessToken == null);
        if (accessToken == null || accessToken.getData() == null || accessToken.getData().getAccessToken() == null ||
                accessToken.getData().getRefreshToken() == null) {
            log.error("获取抖音虚拟账号token信息失败, appKey:{}|secret:{}|code:{}|accessToken:{}|retry:{}", appKey, secret, shopId,
                    accessToken, retry);
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg("获取抖音虚拟账号token信息失败");
        }
        setToken2Redis(appVirtualMessage, accessToken, tenantId);
        return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
    }

    /**
     * 调用抖音接口刷新token
     *
     * @param appKey
     * @param secret
     * @param refreshToken
     * @return
     */
    public AccessToken refreshToken(String appKey, String secret, String refreshToken) {
        // config
        DoudianOpConfig config = new DoudianOpConfig();
        config.AddAppKeyAndSecret(appKey, secret);

        // accessToken
        AccessToken accessToken = new AccessToken();
        accessToken.setData(new AccessTokenData());
        accessToken.getData().setRefreshToken(refreshToken);

        // 调用抖音接口
        AccessToken newAccessToken = AccessTokenBuilder.refresh(config, DefaultDoudianOpClient.getDefaultClient(),
                accessToken, appKey);

        log.info("抖音token刷新结果，newAccessToken={}", JacksonUtils.toJson(newAccessToken));
        return newAccessToken;
    }

    public Map<String, Object> getDouyinVirtualSysParam(Long tenantId, Integer channelId) {
        // 查询保存的信息
        ChannelVirtualAccessConfigDO channelVirtualAccessConfigDO =
                channelVirtualConfigMapper.selectByTenantChannel(tenantId, channelId);
        if (Objects.isNull(channelVirtualAccessConfigDO) || StringUtils.isEmpty(channelVirtualAccessConfigDO.getVirtualSysParam())) {
            log.error("查询账号配置信息不存在，tenantId={}, channelId={}", tenantId, channelId);
            throw new BizException("查询账号配置信息不存在");
        }
        Map<String, Object> sysParam = JSON.parseObject(channelVirtualAccessConfigDO.getVirtualSysParam());
        String appKey = (String) sysParam.get(ProjectConstant.DOUYIN_APP_KEY);
        String secret = (String) sysParam.get(ProjectConstant.DOUYIN_SECRET);
        String shopId = (String) sysParam.get(ProjectConstant.DOUYIN_SHOP_ID);
        if (StringUtils.isAnyBlank(appKey, secret, shopId)) {
            log.error("租户渠道授权参数错误，appKey:{}|secret:{}|shopId:{}", appKey, secret, shopId);
            throw new BizException("租户渠道授权参数错误");
        }
        return sysParam;
    }
}
