package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSpuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelCategoryThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConcurrentUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductChannelErrorMappingUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultCodeUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultSpuDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import java.util.Arrays;
import java.util.HashMap;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator.genResult;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/4/29
 **/
@Slf4j
@Service("elmChannelSpuService")
public class ElmChannelSpuServiceImpl implements ChannelSpuService {

    private static ExecutorServiceTraceWrapper pushExecutor = new ExecutorServiceTraceWrapper(new ThreadPoolExecutor(10, 20, 5, TimeUnit.MINUTES, new ArrayBlockingQueue<>(100),
            new ThreadFactoryBuilder().setNameFormat("elm-store-spu-push" + "-%d").build(), new ThreadPoolExecutor.CallerRunsPolicy()));


    private static final int SPU_BATCH_MAX_COUNT = 50;
    private static final int SPU_BATCH_5_COUNT = 5;

    @Value("${elm.url.base}")
    private String elmUrlBase;
    @Value("${elm.url.skulist}")
    private String skuList;

    @Resource
    ElmChannelGateService elmChannelGateService;
    @Resource
    private ElmConverterService elmConverterService;

    @Resource
    ElmChannelSkuServiceImpl elmChannelSkuService;

    @Resource
    private CopChannelStoreService copChannelStoreService;
    @Resource
    private ClusterRateLimiter clusterRateLimiter;
    @Resource
    private BaseConverterService baseConverterService;
    @Resource
    private ChannelCategoryThriftServiceProxy channelCategoryThriftServiceProxy;

    @Resource
    private TenantService tenantService;

    @Override
    public ResultSpuData updateWeightBySpu(SpuWeightInfoRequest request) {
        return null;
    }

    @Override
    public ResultSpuData spuCreate(SpuInfoRequest request) {
        return spuCreateCommon(request, ChannelPostELMEnum.BATCH_SKU_CREATE);
    }

    @Override
    public ResultSpuData spuCreateForCleaner(SpuInfoRequest request) {
        return spuCreateCommon(request, ChannelPostELMEnum.BATCH_SKU_CREATE_FOR_CLEANER);
    }

    private ResultSpuData spuCreateCommon(SpuInfoRequest request, ChannelPostInter channelPostInter) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        ListUtils.listPartition(request.getParamList(), SPU_BATCH_MAX_COUNT).forEach(data -> {
            Map<String, SpuKey> upcSpuKeyMap = data.stream().collect(Collectors.toMap(this::convert2Upc, this::convert2Key,
                    (v1, v2) -> v2));

            Map<String, SpuKey> customSpuIdMap = Fun.toMapQuietly(data, SpuInfoDTO::getCustomSpuId, this::convert2Key);
            // 返回结果组装用标识
            List<SpuKey> bizKeyList = Lists.newArrayList(customSpuIdMap.values());
            try {
                BatchSpuCreateDTO createParam = new BatchSpuCreateDTO();
                boolean isConvenienceOrFlagshipStoreMode = tenantService.isConvenienceOrFlagshipStoreMode(request.getBaseInfo().getTenantId());
                boolean isErpSpuTenant = tenantService.isErpSpuTenant(request.getBaseInfo().getTenantId());
                boolean isMedicineUmWareHouse = tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId());
                createParam.setCreate_list(data.stream().map(spu -> elmConverterService.convert(spu, false, false,
                                request.getBaseInfo().getTenantId(),
                                channelCategoryThriftServiceProxy.getMultiSpecType(EnhanceChannelType.ELEM.getChannelId(),
                                        request.getBaseInfo().getTenantId(), spu.getChannelCategoryId()), true,
                                isConvenienceOrFlagshipStoreMode, isErpSpuTenant,isMedicineUmWareHouse))
                        .collect(Collectors.toList()));
                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(channelPostInter, request.getBaseInfo(), createParam);
                // 解析结果
                postResult.values().forEach(response -> {
                    ChannelResponseResult<ChannelSkuCreateBatchResult> createResult = response.getBody();
                    // 使用upc匹配成功的商品
                    if (createResult.getData() != null && CollectionUtils.isNotEmpty(createResult.getData().getResult_list())) {
                        createResult.getData().getResult_list().forEach(r -> {
                            SpuKey spuKey = Optional.ofNullable(customSpuIdMap.get(r.getCustom_sku_id())).orElse(upcSpuKeyMap.get(r.getUpc()));
                            if (spuKey == null) {
                                return;
                            }
                            //设置饿了么平台返回的末级渠道类目
                            spuKey.setCategoryId(r.getCat3_id());
                            //设置饿了么平台返回的规格id
                            if (CollectionUtils.isNotEmpty(r.getSku_spec_result())) {
                                Map<String, String> customSkuId2ChannelSkuId = r.getSku_spec_result().stream().collect(Collectors.toMap(SkuSpecResult::getSku_spec_custom_id, SkuSpecResult::getSku_spec_id));
                                spuKey.getSkus().forEach(skukey -> {
                                    skukey.setChannelSkuId(customSkuId2ChannelSkuId.get(skukey.getCustomSkuId()));
                                });
                            }
                            bizKeyList.remove(spuKey);
                            resultData.addToSucData(new ResultSuccessSpu().setSpuInfo(spuKey).setChannelResultInfo(r.getSku_id()));
                        });
                    }
                    // 填充失败列表
                    if (MccConfigUtil.getElmParseChannelResponseUpgradeSwitch()) {
                        fillErrorList(resultData, upcSpuKeyMap, bizKeyList, createResult);
                    } else {
                        bizKeyList.forEach(spuKey -> {
                            ResultErrorSpu errorSpu = new ResultErrorSpu().setSpuInfo(spuKey).setErrorMsg(createResult.getError())
                                    .setErrorCode(ResultCodeUtils.parseErrorCode(createResult.getErrno()));
                            resultData.addToErrorData(errorSpu);
                        });
                    }
                });
            } catch (IllegalArgumentException e) {
                log.error("ElmChannelSpuServiceImpl.spuCreate 参数校验失败, data:{}", data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);
            } catch (Exception e) {
                log.error("ElmChannelSpuServiceImpl.spuCreate 服务异常, data:{}", data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), bizKeyList);
            }
        });
        return resultData;
    }

    /**
     * 创建商品解析失败原因
     * elm失败商品非结构化
     * demo:【api】批量创建渠道商品失败:分类下商品超过2000【商品upc：6972995047343】;分类下商品超过2000【商品upc：6970962731202】;分类下商品超过2000【商品upc：6917751439567】;商品已存在, sku_id:16867513582217430【商品upc：6951895602111】
     * @param resultData
     * @param upcSpuKeyMap
     * @param bizKeyList
     * @param createResult
     */

    private void fillErrorList(ResultSpuData resultData, Map<String, SpuKey> upcSpuKeyMap, List<SpuKey> bizKeyList,
                               ChannelResponseResult<ChannelSkuCreateBatchResult> createResult) {

        if(CollectionUtils.isEmpty(bizKeyList) || StringUtils.isEmpty(createResult.getError())){
            return;
        }
        Map<String, String> errorMap = new HashMap<>();
        try {
            String error = createResult.getError();
            List<String> errorList = Arrays.stream(error.trim().split("(?<=】;)")).collect(Collectors.toList());
            for(String errorItem : errorList){
                String replaceError = errorItem.substring(errorItem.indexOf("【商品upc："));
                String upc = replaceError.substring(replaceError.indexOf("【商品upc：") + 7, replaceError.indexOf("】") );
                SpuKey spuKey = upcSpuKeyMap.get(upc);
                errorMap.put(spuKey.getCustomSpuId(), errorItem);
            }
        } catch (Exception e){
            log.error("elm fillErrorList failed, createResult:{} bizKeyList:{}", createResult, bizKeyList);
            // 失败打点
            MetricHelper.build().name("parseErrorList")
                    .tag("method", "spuCreate")
                    .tag("channel", String.valueOf(ChannelType.ELEM.getValue()))
                    .count();
        }

        // 填充失败列表
        bizKeyList.forEach(spuKey -> {
            String errorNo = createResult.getErrno() == null ? StringUtils.EMPTY : String.valueOf(createResult.getErrno());
            String msg;
            ProductChannelUnifyErrorEnum unifyErrorEnum = null;
            // 渠道错误解析准确才填充统一错误码
            if (errorMap.get(spuKey.getCustomSpuId()) == null){
                msg = createResult.getError();
            } else {
                msg = errorMap.get(spuKey.getCustomSpuId());
                // 填充统一渠道错误码
                unifyErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.ELEM, errorNo, msg);
            }
            ResultErrorSpu errorSpu = new ResultErrorSpu().setSpuInfo(spuKey).setErrorMsg(msg)
                    .setErrorCode(ResultCodeUtils.parseErrorCode(createResult.getErrno())).setChannelUnifyError(unifyErrorEnum);
            resultData.addToErrorData(errorSpu);
        });
    }

    public ResultSpuData singleCreateSpu(SpuInfoRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList()) || request.getParamListSize() != 1) {
            return resultData;
        }
        SpuInfoDTO data = request.getParamListIterator().next();
        SpuKey spuKey = convert2Key(data);

        try {
            // 业务参数转换
            boolean isConvenienceOrFlagshipStoreMode = tenantService.isConvenienceOrFlagshipStoreMode(request.getBaseInfo().getTenantId());
            boolean isErpSpuTenant = tenantService.isErpSpuTenant(request.getBaseInfo().getTenantId());
            boolean isMedicineUmWareHouse = tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId());
            ChannelSpuCreateOrUpdateDTO createParam = elmConverterService.convert(data, false, false,
                    request.getBaseInfo().getTenantId(),
                    channelCategoryThriftServiceProxy.getMultiSpecType(EnhanceChannelType.ELEM.getChannelId(),
                            request.getBaseInfo().getTenantId(), data.getChannelCategoryId()), true, isConvenienceOrFlagshipStoreMode, isErpSpuTenant, isMedicineUmWareHouse);

            // 调用渠道接口
            Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SKU_CREATE, request.getBaseInfo(), createParam);

            // 解析结果
            postResult.values().forEach(response -> {
                ChannelResponseResult<ChannelSkuCreateResponceResult> createResult = response.getBody();

                if (createResult != null && response.isSuccess() && createResult.getData() != null
                        && StringUtils.isNotBlank(createResult.getData().getChannelResultInfo())) {
                    ChannelSkuCreateResponceResult result = createResult.getData();
                    spuKey.setCategoryId(result.getCat3_id());
                    resultData.addToSucData(new ResultSuccessSpu().setSpuInfo(spuKey).setChannelResultInfo(result.getChannelResultInfo()));
                } else if (createResult != null) {
                    // 填充统一渠道错误码
                    String errnoStr = createResult.getErrno() == null ? StringUtils.EMPTY : String.valueOf(createResult.getErrno());
                    ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.ELEM, errnoStr, createResult.getError());
                    resultData.addToErrorData(new ResultErrorSpu().setSpuInfo(spuKey).setErrorCode(ResultCodeUtils.parseErrorCode(createResult.getErrno()))
                            .setErrorMsg(createResult.getError()).setChannelUnifyError(unifyErrorEnum));
                } else {
                    resultData.addToErrorData(new ResultErrorSpu().setSpuInfo(spuKey).setErrorCode(ResultCode.FAIL.getCode())
                            .setErrorMsg(ResultCode.FAIL.getMsg()));
                }
            });
        } catch (IllegalArgumentException e) {
            log.error("ElmChannelSpuServiceImpl.singleCreateSpu 参数校验失败, data:{}", data, e);
            ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), Lists.newArrayList(spuKey));
        } catch (Exception e) {
            log.error("ElmChannelSpuServiceImpl.singleCreateSpu 服务异常, data:{}", data, e);
            ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), Lists.newArrayList(spuKey));
        }
        return resultData;
    }


    /**
     * 指定字段更新，目前仅更新多规格自定义skuId，更新其他字段需要按需添加
     * @param request
     * @return
     */
    public ResultSpuData singleUpdateOptionalField(SpuInfoRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList()) || request.getParamListSize() != 1) {
            return resultData;
        }
        SpuInfoDTO data = request.getParamList().get(0);
        SpuKey spuKey = convert2Key(data);
        try {
            ChannelSpuCreateOrUpdateDTO dto = elmConverterService.convertForOptional(data, request.isOptByChannelSpuId());

            Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SKU_UPDATE, request.getBaseInfo(), dto);
            // 解析结果
            postResult.entrySet().forEach(resultEntry -> {
                long storeId = Optional.ofNullable(resultEntry.getKey()).orElse(0L);
                ChannelResponseDTO response = resultEntry.getValue();
                ChannelResponseResult<ChannelSkuCreateResponceResult> updateResult = response.getBody();

                if (updateResult != null && response.isSuccess() && updateResult.getData() != null) {
                    ChannelSkuCreateResponceResult result = updateResult.getData();
                    resultData.addToSucData(new ResultSuccessSpu().setSpuInfo(spuKey).setStoreId(storeId).setChannelResultInfo(result.getChannelResultInfo()));
                }
                else if (updateResult != null) {
                    // 填充统一渠道错误码
                    String errnoStr = updateResult.getErrno() == null ? StringUtils.EMPTY : String.valueOf(updateResult.getErrno());
                    ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.ELEM, errnoStr, updateResult.getError());
                    resultData.addToErrorData(new ResultErrorSpu().setStoreId(storeId).setSpuInfo(spuKey).setErrorCode(ResultCodeUtils.parseErrorCode(updateResult.getErrno()))
                            .setErrorMsg(updateResult.getError()).setChannelUnifyError(unifyErrorEnum));
                }
                else {
                    resultData.addToErrorData(new ResultErrorSpu().setStoreId(storeId).setSpuInfo(spuKey).setErrorCode(ResultCode.FAIL.getCode())
                            .setErrorMsg(ResultCode.FAIL.getMsg()));
                }
            });
        }catch  (IllegalArgumentException e) {
            log.error("ElmChannelSpuServiceImpl.singleUpdateOptionalField 参数校验失败, data:{}", data, e);
            ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), Lists.newArrayList(spuKey));
        } catch (Exception e) {
            log.error("ElmChannelSpuServiceImpl.singleUpdateOptionalField 服务异常, data:{}", data, e);
            ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), Lists.newArrayList(spuKey));
        }
        return resultData;
    }


    public ResultSpuData singleUpdateSpu(SpuInfoRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList()) || request.getParamListSize() != 1) {
            return resultData;
        }

        SpuInfoDTO data = request.getParamListIterator().next();
        SpuKey spuKey = convert2Key(data);

        try {
            // 医药无人仓-多规格商品更新场景，价格并不是必传
            boolean updateMustMultiSpecPrice = !tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId());
            boolean isConvenienceOrFlagshipStoreMode = tenantService.isConvenienceOrFlagshipStoreMode(request.getBaseInfo().getTenantId());
            boolean isErpSpuTenant = tenantService.isErpSpuTenant(request.getBaseInfo().getTenantId());
            boolean isMedicineUmWareHouse = !updateMustMultiSpecPrice;
            // 业务参数转换
            ChannelSpuCreateOrUpdateDTO updateParam = elmConverterService.convert(data, true,
                    request.isOptByChannelSpuId(), request.getBaseInfo().getTenantId(),
                    channelCategoryThriftServiceProxy.getMultiSpecType(EnhanceChannelType.ELEM.getChannelId(),
                            request.getBaseInfo().getTenantId(), data.getChannelCategoryId()), updateMustMultiSpecPrice,
                    isConvenienceOrFlagshipStoreMode, isErpSpuTenant, isMedicineUmWareHouse);

            // 调用渠道接口
            Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SKU_UPDATE, request.getBaseInfo(), updateParam);

            // 解析结果
            postResult.entrySet().forEach(resultEntry -> {
                long storeId = Optional.ofNullable(resultEntry.getKey()).orElse(0L);
                ChannelResponseDTO response = resultEntry.getValue();
                ChannelResponseResult<ChannelSkuCreateResponceResult> updateResult = response.getBody();

                if (updateResult != null && response.isSuccess() && updateResult.getData() != null) {
                    ChannelSkuCreateResponceResult result = updateResult.getData();
                    resultData.addToSucData(new ResultSuccessSpu().setSpuInfo(spuKey).setStoreId(storeId).setChannelResultInfo(result.getChannelResultInfo()));
                } else if (updateResult != null) {
                    // 填充统一渠道错误码
                    String errnoStr = updateResult.getErrno() == null ? StringUtils.EMPTY : String.valueOf(updateResult.getErrno());
                    ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.ELEM, errnoStr, updateResult.getError());
                    resultData.addToErrorData(new ResultErrorSpu().setStoreId(storeId).setSpuInfo(spuKey).setErrorCode(ResultCodeUtils.parseErrorCode(updateResult.getErrno()))
                            .setErrorMsg(updateResult.getError()).setChannelUnifyError(unifyErrorEnum));
                } else {
                    resultData.addToErrorData(new ResultErrorSpu().setStoreId(storeId).setSpuInfo(spuKey).setErrorCode(ResultCode.FAIL.getCode())
                            .setErrorMsg(ResultCode.FAIL.getMsg()));
                }
            });
        } catch (IllegalArgumentException e) {
            log.error("ElmChannelSpuServiceImpl.singleUpdateSpu 参数校验失败, data:{}", data, e);
            ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), Lists.newArrayList(spuKey));
        } catch (Exception e) {
            log.error("ElmChannelSpuServiceImpl.singleUpdateSpu 服务异常, data:{}", data, e);
            ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), Lists.newArrayList(spuKey));
        }
        return resultData;
    }

    private String convert2Upc(SpuInfoDTO spuInfoDTO) {
        return spuInfoDTO.getSkus().stream().map(SkuInSpuInfoDTO::getUpc).collect(Collectors.joining(","));
    }

    private SpuKey convert2Key(SpuInfoDTO spuInfoDTO) {
        SpuKey spuKey = new SpuKey().setCustomSpuId(spuInfoDTO.getCustomSpuId()).setChannelSpuId(spuInfoDTO.getChannelSpuId());
        List<SkuKey> skuKeys = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(spuInfoDTO.getSkus())) {
            spuInfoDTO.getSkus().forEach(skuInSpuInfoDTO -> {
                SkuKey skuKey = new SkuKey().setCustomSkuId(skuInSpuInfoDTO.getCustomSkuId());
                skuKeys.add(skuKey);
            });
            spuKey.setSkus(skuKeys);
        }
        return spuKey;
    }


    @Override
    public ResultSpuData upcCreate(SpuInfoRequest request) {
        return this.spuCreate(request);
    }

    @Override
    public ResultSpuData upcCreateForCleaner(SpuInfoRequest request) {
        return this.spuCreateForCleaner(request);
    }

    @Override
    public ResultSpuData updateBySpuOrUpc(SpuInfoRequest request) {
        return updateBySpuOrUpcCommon(request, ChannelPostELMEnum.BATCH_SKU_UPDATE);
    }

    @Override
    public ResultSpuData updateBySpuOrUpcForCleaner(SpuInfoRequest request) {
        return updateBySpuOrUpcCommon(request, ChannelPostELMEnum.BATCH_SKU_UPDATE_FOR_CLEANER);
    }

    private ResultSpuData updateBySpuOrUpcCommon(SpuInfoRequest request, ChannelPostInter channelPostInter) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        // 医药无人仓-多规格商品更新场景，价格并不是必传
        boolean updateMustMultiSpecPrice = !tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId());

        ListUtils.listPartition(request.getParamList(), SPU_BATCH_MAX_COUNT).forEach(data -> {
            // 返回结果组装用标识
            List<SpuKey> bizKeyList = data.stream().map(this::convert2Key).collect(Collectors.toList());
            Map<String, SpuKey> channelSpuIdSpuKeyMap = bizKeyList.stream().collect(Collectors.toMap(SpuKey::getChannelSpuId, Function.identity(), (v1, v2) -> v2));
            try {
                // 业务参数转换
                BatchSpuUpdateDTO updateParam = new BatchSpuUpdateDTO();
                boolean isConvenienceOrFlagshipStoreMode = tenantService.isConvenienceOrFlagshipStoreMode(request.getBaseInfo().getTenantId());
                boolean isErpSpuTenant =tenantService.isErpSpuTenant(request.getBaseInfo().getTenantId());
                boolean isMedicineUmWareHouse = tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId());

                updateParam.setUpdate_list(data.stream().map((spu -> elmConverterService.convert(spu, true,
                        request.isOptByChannelSpuId(), request.getBaseInfo().getTenantId(),
                        channelCategoryThriftServiceProxy.getMultiSpecType(EnhanceChannelType.ELEM.getChannelId(),
                                request.getBaseInfo().getTenantId(),
                                spu.getChannelCategoryId()), updateMustMultiSpecPrice, isConvenienceOrFlagshipStoreMode, isErpSpuTenant, isMedicineUmWareHouse))).collect(Collectors.toList()));
                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(channelPostInter, request.getBaseInfo(), updateParam);
                // 解析结果
                postResult.values().forEach(response -> {
                    ChannelResponseResult<ChannelSkuUpdateBatchResult> updateResult = response.getBody();
                    // 使用渠道商品ID匹配成功的商品
                    if (updateResult.getData() != null && CollectionUtils.isNotEmpty(updateResult.getData().getResult_list())) {
                        updateResult.getData().getResult_list().forEach(r -> {
                            SpuKey spuKey = channelSpuIdSpuKeyMap.get(r.getSku_id());
                            if (spuKey == null) {
                                return;
                            }
                            bizKeyList.remove(spuKey);
                            resultData.addToSucData(new ResultSuccessSpu().setSpuInfo(spuKey).setChannelResultInfo(r.getSku_id()));
                        });
                    }
                    // 解析失败列表
                    if (MccConfigUtil.getElmParseChannelResponseUpgradeSwitch()) {
                        fillErrorList(resultData, bizKeyList, updateResult);
                    } else {
                        bizKeyList.forEach(spuKey -> {
                            ResultErrorSpu errorSpu = new ResultErrorSpu().setSpuInfo(spuKey).setErrorMsg(updateResult.getError()).setErrorCode(ResultCodeUtils.parseErrorCode(updateResult.getErrno()));
                            resultData.addToErrorData(errorSpu);
                        });
                    }
                });

            } catch (IllegalArgumentException e) {
                log.error("ElmChannelSpuServiceImpl.updateBySpuOrUpc 参数校验失败, data:{}", data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);
            } catch (Exception e) {
                log.error("ElmChannelSpuServiceImpl.updateBySpuOrUpc 服务异常, data:{}", data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), bizKeyList);
            }
        });
        return resultData;
    }

    /**
     * 更新商品解析失败原因
     * elm失败商品非结构化
     * demo:【api】批量更新渠道商品失败:custom_sku_id不存在【商品id：1666010578525110358】;custom_sku_id不存在【商品id：1666010578525110111】
     * @param resultData
     * @param bizKeyList
     * @param updateResult
     */
    private void fillErrorList(ResultSpuData resultData, List<SpuKey> bizKeyList, ChannelResponseResult<ChannelSkuUpdateBatchResult> updateResult) {
        if(CollectionUtils.isEmpty(bizKeyList)){
            return;
        }
        Map<String, String> errorMap = new HashMap<>();
        try {
            String error = updateResult.getError();
            List<String> errorList = Arrays.stream(error.trim().split("(?<=】;)")).collect(Collectors.toList());
            for (String errorItem : errorList) {
                String replaceError = errorItem.substring(errorItem.indexOf("【商品id："));
                String customSpuId = replaceError.substring(replaceError.indexOf("【商品id：") + 6, replaceError.indexOf("】"));
                errorMap.put(customSpuId, errorItem);
            }
        } catch (Exception e){
            log.error("elm fillErrorList failed, updateResult:{} bizKeyList:{}", updateResult, bizKeyList);
            // 失败打点
            MetricHelper.build().name("parseErrorList")
                    .tag("method", "spuUpdate")
                    .tag("channel", String.valueOf(ChannelType.ELEM.getValue()))
                    .count();
        }
        // 填充失败列表
        bizKeyList.forEach(spuKey -> {
            String errorNo = updateResult.getErrno() == null ? StringUtils.EMPTY : String.valueOf(updateResult.getErrno());
            String msg;
            ProductChannelUnifyErrorEnum unifyErrorEnum = null;
            // 渠道错误解析准确才填充统一错误码
            if (errorMap.get(spuKey.getCustomSpuId()) == null){
                msg = updateResult.getError();
            } else {
                msg = errorMap.get(spuKey.getCustomSpuId());
                // 填充统一渠道错误码
                unifyErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.ELEM, errorNo, msg);
            }
            ResultErrorSpu errorSpu = new ResultErrorSpu().setSpuInfo(spuKey).setErrorMsg(msg)
                    .setErrorCode(ResultCodeUtils.parseErrorCode(updateResult.getErrno())).setChannelUnifyError(unifyErrorEnum);
            resultData.addToErrorData(errorSpu);
        });
    }


    @Override
    public ResultSpuData deleteSpu(SpuInfoDeleteRequest request) {
        try {
            BaseRequestSimple baseInfo = request.getBaseInfo();
            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
            // 按门店分组
            Map<Long, List<SpuInfoDeleteDTO>> storeDeleteMap = request.getParamList().stream().collect(Collectors.groupingBy(SpuInfoDeleteDTO::getStoreId));
            storeDeleteMap.forEach((storeId, list) -> {
                BaseRequest baseRequest = new BaseRequest().setTenantId(baseInfo.getTenantId()).setChannelId(baseInfo.getChannelId()).setStoreIdList(Lists.newArrayList(storeId));
                SkuInfoDeleteRequest deleteRequest = new SkuInfoDeleteRequest();
                deleteRequest.setBaseInfo(baseRequest);
                deleteRequest.setParamList(list.stream().map(e -> new SkuInfoDeleteDTO().setSkuId(e.getCustomSpuId())).collect(Collectors.toList()));
                ResultData skuResult = elmChannelSkuService.deleteSku(deleteRequest);
                ResultSpuData storeResultSpuData = convert2SpuResult(skuResult);
                ResultStatus status = storeResultSpuData.getStatus();
                if (status.getCode() != ResultCode.SUCCESS.getCode()) {
                    list.forEach(p -> {
                        // 填充统一渠道错误码
                        ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.ELEM, String.valueOf(status.getCode()), status.getMsg());
                        ResultErrorSpu errorSpu = new ResultErrorSpu().setStoreId(storeId).setErrorMsg(status.getMsg()).setErrorCode(status.getCode())
                                .setSpuInfo(new SpuKey().setCustomSpuId(p.getCustomSpuId())).setChannelUnifyError(unifyErrorEnum);
                        resultData.addToErrorData(errorSpu);
                    });
                    return;
                }
                if (CollectionUtils.isNotEmpty(storeResultSpuData.getSucData())) {
                    storeResultSpuData.getSucData().forEach(resultData::addToSucData);
                }
                if (CollectionUtils.isNotEmpty(storeResultSpuData.getErrorData())) {
                    storeResultSpuData.getErrorData().forEach(resultData::addToErrorData);
                }
            });
            return resultData;
        } catch (Exception e) {
            log.error("EleChannelSpuServiceImpl.deleteSpu, 批量删除商品SPU服务异常, request:{}", request, e);
        }
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量删除商品SPU失败");
    }

    @Override
    public ResultSpuData deleteCategoryAndSpu(BaseRequest request) {
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量删除分类和商品服务失败");
    }

    /**
     * 根据渠道SpuId删除渠道商品
     * @param request
     * @return
     */
    public ResultSpuData deleteSpuByChannelSpuId(SpuInfoDeleteByChannelSpuIdRequest request) {
        try {
            BaseRequestSimple baseInfo = request.getBaseInfo();
            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
            // 按门店分组
            Map<Long, List<SpuInfoDeleteByChannelSpuIdDTO>> storeDeleteMap = request.getParamList().stream().collect(Collectors.groupingBy(SpuInfoDeleteByChannelSpuIdDTO::getStoreId));
            storeDeleteMap.forEach((storeId, list) -> {
                BaseRequest baseRequest = new BaseRequest().setTenantId(baseInfo.getTenantId()).setChannelId(baseInfo.getChannelId()).setStoreIdList(Lists.newArrayList(storeId));
                SkuInfoDeleteRequest deleteRequest = new SkuInfoDeleteRequest();
                deleteRequest.setBaseInfo(baseRequest);
                deleteRequest.setParamList(list.stream().map(e -> new SkuInfoDeleteDTO().setSkuId(e.getChannelSpuId())).collect(Collectors.toList()));
                ResultData skuResult = elmChannelSkuService.deleteSkuByChannelSkuId(deleteRequest);
                ResultSpuData storeResultSpuData = convert2SpuResult(skuResult);
                ResultStatus status = storeResultSpuData.getStatus();
                if (status.getCode() != ResultCode.SUCCESS.getCode()) {
                    list.forEach(p -> {
                        ResultErrorSpu errorSpu = new ResultErrorSpu().setStoreId(storeId).setErrorMsg(status.getMsg()).setErrorCode(status.getCode()).setSpuInfo(new SpuKey().setChannelSpuId(p.getChannelSpuId()));
                        resultData.addToErrorData(errorSpu);
                    });
                    return;
                }
                if (CollectionUtils.isNotEmpty(storeResultSpuData.getSucData())) {
                    storeResultSpuData.getSucData().forEach(resultData::addToSucData);
                }
                if (CollectionUtils.isNotEmpty(storeResultSpuData.getErrorData())) {
                    storeResultSpuData.getErrorData().forEach(resultData::addToErrorData);
                }
            });
            return resultData;
        } catch (Exception e) {
            log.error("EleChannelSpuServiceImpl.deleteSpuByChannelSpuId, 根据channelSpuId批量删除商品SPU服务异常, request:{}", request, e);
        }
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "根据channelSpuId批量删除商品SPU失败");
    }

    @Override
    public ResultSpuData deleteSku(SkuInSpuInfoDeleteRequest request) {
        return ResultGenerator.genResultSpuData(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }

    @Override
    public ResultSpuData updateSpuSellStatus(SpuSellStatusInfoRequest request) {
        try {
            SkuSellStatusInfoRequest skuSellStatusInfoRequest = new SkuSellStatusInfoRequest();
            skuSellStatusInfoRequest.setBaseInfo(request.getBaseInfo());
            // 使用渠道ID更新
            if (request.isOptByChannelSpuId()) {
                skuSellStatusInfoRequest.setParamList(request.getParamList().stream().map(this::ofByChannelSpuId).collect(Collectors.toList()));
            } else {
                skuSellStatusInfoRequest.setParamList(request.getParamList().stream().map(this::of).collect(Collectors.toList()));
            }
            skuSellStatusInfoRequest.setOptByChannelSpuId(request.isOptByChannelSpuId());
            ResultData skuResult = elmChannelSkuService.updateSkuSellStatus(skuSellStatusInfoRequest);
            return convert2SpuResult(skuResult);
        } catch (Exception e) {
            log.error("ElmChannelSpuServiceImpl.updateSpuSellStatus, 批量修改商品上下架状态失败, request:{}", request, e);
        }
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量修改商品上下架状态失败");
    }

    /**
     * 修改spu售卖状态
     *
     * @param request
     * @return
     */
    public ResultSpuData updateSpuSellStatusByChannelSpuId(SpuSellStatusInfoByChannelSpuIdRequest request) {
        try {
            SkuSellStatusInfoRequest skuSellStatusInfoRequest = new SkuSellStatusInfoRequest();
            skuSellStatusInfoRequest.setBaseInfo(request.getBaseInfo());
            skuSellStatusInfoRequest.setParamList(request.getParamList().stream().map(this::ofSpuInfoSellStatusByChannelSpuIdDTO).collect(Collectors.toList()));
            ResultData skuResult = elmChannelSkuService.updateSkuSellStatusByChannelSkuId(skuSellStatusInfoRequest);
            return convert2SpuResultByChannelSpuId(skuResult);
        }
        catch (Exception e) {
            log.error("ElmChannelSpuServiceImpl.updateSpuSellStatusByChannelSpuId, 批量修改商品上下架状态失败, request:{}", request, e);
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量修改商品上下架状态失败");
        }
    }

    private ResultSpuData convert2SpuResult(ResultData skuResultData) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        resultData.setStatus(skuResultData.getStatus());
        if (CollectionUtils.isNotEmpty(skuResultData.getErrorData())) {
            skuResultData.getErrorData().forEach(errorData -> {
                SpuKey spuKey = new SpuKey().setCustomSpuId(errorData.getSkuId()).setChannelSpuId(errorData.getChannelSkuId());
                resultData.addToErrorData(new ResultErrorSpu()
                        .setStoreId(errorData.getStoreId())
                        .setSpuInfo(spuKey)
                        .setErrorCode(ResultCodeUtils.parseErrorCode(errorData.getErrorCode()))
                        .setErrorMsg(errorData.getErrorMsg())
                        .setChannelUnifyError(errorData.getChannelUnifyError()));
            });
        }
        if (CollectionUtils.isNotEmpty(skuResultData.getSucData())) {
            skuResultData.getSucData().forEach(sucData -> {
                SpuKey spuKey = new SpuKey().setCustomSpuId(sucData.getSkuId()).setChannelSpuId(sucData.getChannelSkuId());
                resultData.addToSucData(new ResultSuccessSpu()
                        .setStoreId(sucData.getStoreId())
                        .setSpuInfo(spuKey));
            });
        }
        return resultData;
    }

    /**
     * 解析结果（基于渠道商品编码）
     *
     * @see ElmChannelSpuServiceImpl#convert2SpuResult(ResultData)
     * @param skuResultData
     * @return
     */
    private ResultSpuData convert2SpuResultByChannelSpuId(ResultData skuResultData) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        resultData.setStatus(skuResultData.getStatus());
        if (CollectionUtils.isNotEmpty(skuResultData.getErrorData())) {
            skuResultData.getErrorData().forEach(errorData -> {
                SpuKey spuKey = new SpuKey().setChannelSpuId(errorData.getChannelSkuId());
                resultData.addToErrorData(new ResultErrorSpu()
                        .setStoreId(errorData.getStoreId())
                        .setSpuInfo(spuKey)
                        .setErrorCode(ResultCodeUtils.parseErrorCode(errorData.getErrorCode()))
                        .setErrorMsg(errorData.getErrorMsg()));
            });
        }
        if (CollectionUtils.isNotEmpty(skuResultData.getSucData())) {
            skuResultData.getSucData().forEach(sucData -> {
                SpuKey spuKey = new SpuKey().setChannelSpuId(sucData.getChannelSkuId());
                resultData.addToSucData(new ResultSuccessSpu()
                        .setStoreId(sucData.getStoreId())
                        .setSpuInfo(spuKey));
            });
        }
        return resultData;
    }

    private SkuSellStatusInfoDTO of(SpuInfoSellStatusDTO spuInfoSellStatusDTO) {
        SkuSellStatusInfoDTO skuSellStatusInfoDTO = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO.setStoreId(spuInfoSellStatusDTO.getStoreId());
        if (CollectionUtils.isNotEmpty(spuInfoSellStatusDTO.getCustomSpuIds())) {
            List<SkuIdDTO> skuIds = spuInfoSellStatusDTO.getCustomSpuIds().stream().map(customSpuId -> {
                SkuIdDTO skuIdDTO = new SkuIdDTO();
                skuIdDTO.setCustomSkuId(customSpuId);
                return skuIdDTO;
            }).collect(Collectors.toList());
            skuSellStatusInfoDTO.setSkuId(skuIds);
        }
        skuSellStatusInfoDTO.setSkuStatus(spuInfoSellStatusDTO.getSpuStatus());
        return skuSellStatusInfoDTO;
    }

    private SkuSellStatusInfoDTO ofByChannelSpuId(SpuInfoSellStatusDTO spuInfoSellStatusDTO) {
        SkuSellStatusInfoDTO skuSellStatusInfoDTO = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO.setStoreId(spuInfoSellStatusDTO.getStoreId());
        if (CollectionUtils.isNotEmpty(spuInfoSellStatusDTO.getChannelSpuIds())) {
            List<SkuIdDTO> skuIds = spuInfoSellStatusDTO.getChannelSpuIds().stream().map(channelSpuId -> {
                SkuIdDTO skuIdDTO = new SkuIdDTO();
                skuIdDTO.setSkuId(channelSpuId);
                return skuIdDTO;
            }).collect(Collectors.toList());
            skuSellStatusInfoDTO.setSkuId(skuIds);
        }
        skuSellStatusInfoDTO.setSkuStatus(spuInfoSellStatusDTO.getSpuStatus());
        return skuSellStatusInfoDTO;
    }
    /**
     * 基于渠道商品编码实现SpuInfoSellStatusByChannelSpuIdDTO -> SkuSellStatusInfoDTO
     *
     * @see ElmChannelSpuServiceImpl#of(SpuInfoSellStatusDTO)
     * @param spuInfoSellStatusByChannelSpuIdDTO
     * @return
     */
    private SkuSellStatusInfoDTO ofSpuInfoSellStatusByChannelSpuIdDTO(SpuInfoSellStatusByChannelSpuIdDTO spuInfoSellStatusByChannelSpuIdDTO) {
        SkuSellStatusInfoDTO skuSellStatusInfoDTO = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO.setStoreId(spuInfoSellStatusByChannelSpuIdDTO.getStoreId());

        if (CollectionUtils.isNotEmpty(spuInfoSellStatusByChannelSpuIdDTO.getChannelSpuIds())) {
            List<SkuIdDTO> skuIds = spuInfoSellStatusByChannelSpuIdDTO.getChannelSpuIds().stream().map(channelSpuId -> {
                SkuIdDTO skuIdDTO = new SkuIdDTO();
                skuIdDTO.setSkuId(channelSpuId);
                return skuIdDTO;
            }).collect(Collectors.toList());
            skuSellStatusInfoDTO.setSkuId(skuIds);
        }
        skuSellStatusInfoDTO.setSkuStatus(spuInfoSellStatusByChannelSpuIdDTO.getSpuStatus());
        return skuSellStatusInfoDTO;
    }

    @Override
    public ResultSpuData updateCustomSpuIdByOriginId(UpdateCustomSpuIdByOriginIdRequest request) {

        List<UpdateCustomSpuIdByOriginIdDTO> paramList = request.getParamList();
        if (CollectionUtils.isEmpty(paramList)){
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, "更新饿了么渠道自定义编码失败，参数为空，请核实");
        }

        UpdateCustomSpuIdByOriginIdDTO next = paramList.iterator().next();
        long storeId = next.getStoreId();
        BaseRequest baseRequestSimple = new BaseRequest()
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(storeId))
                .setTenantId(request.getBaseInfo().getTenantId());

        List<UpdateCustomSkuId> skuList = paramList.stream().map(param -> {
            UpdateCustomSkuId updateCustomSkuId = new UpdateCustomSkuId();
            updateCustomSkuId.setSku_id(param.getCustomSpuIdOrigin());
            updateCustomSkuId.setCustom_sku_id(param.getCustomSpuIdCurrent());
            return updateCustomSkuId;
        }).collect(Collectors.toList());

        UpdateCustomSkuId updateCustomSkuId = skuList.get(0);
        // 调用渠道接口更新 SPU
        Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.UPDATE_CUSTOM_SKU_ID, baseRequestSimple, updateCustomSkuId);

        ChannelResponseDTO response = postResult.get(storeId);
        if (response != null && !response.isSuccess()) {
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, "根据原商品编码更换新商品编码失败");
        }

        // 更新规格
        // sku_spec_custom_id


        List<SkuSpecDTO> specList = paramList.stream()
                .filter(x -> Objects.nonNull(x.getCustomSkuIdOrigin()))
                .map(param -> {
                    SkuSpecDTO skuSpecDTO = new SkuSpecDTO();
                    skuSpecDTO.setSku_spec_custom_id(param.getCustomSkuIdCurrent());
                    skuSpecDTO.setSku_spec_id(param.getCustomSkuIdOrigin());
                    return skuSpecDTO;
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(specList)){
            return ResultGenerator.genResultSpuData(ResultCode.SUCCESS, "更新 SKU ID 成功");
        }

        ChannelSpuCreateOrUpdateDTO updateParam = new ChannelSpuCreateOrUpdateDTO();

        updateParam.setSku_id(next.getCustomSpuIdOrigin());

        updateParam.setSku_spec(specList);

       // 查询 cat_property

        Map<String, Object> sysParam = elmChannelGateService.getSysParam(new BaseRequest()
                .setChannelId(request.getBaseInfo().getChannelId())
                .setTenantId(request.getBaseInfo().getTenantId())
                .setStoreIdList(Lists.newArrayList(storeId)));
        String appId = String.valueOf(sysParam.get(ProjectConstant.ELM_SOURCE));
        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SKU_CATEGORY_PROPERTY_QUERY, appId);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, "接口限流");
        }


        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo().getTenantId(),
                request.getBaseInfo().getChannelId(), Collections.singletonList(storeId));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return null;
        }
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(), storeId);
        ChannelStoreDO channelStoreDO = channelStoreDOMap.get(channelStoreKey);

        Map<String, String> bizParam = Maps.newHashMap();
        bizParam.put("custom_sku_id",updateCustomSkuId.getCustom_sku_id());
        bizParam.put("sku_id", updateCustomSkuId.getSku_id());
        bizParam.put("shop_id", channelStoreDO.getChannelOnlinePoiCode());

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(storeId));
        Map<String, Object> categoryPropertyInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, ChannelPostELMEnum.SKU_CATEGORY_PROPERTY_QUERY.getUrl(), baseRequest, bizParam);
        // 超时时，会返回 null
        if (Objects.isNull(categoryPropertyInfoMap)) {
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, ResultCode.FAIL.getMsg());
        }
        JSONObject categoryPropertyInfoMapBody = (JSONObject) categoryPropertyInfoMap.get(ProjectConstant.BODY);
        if ((int) categoryPropertyInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, categoryPropertyInfoMapBody.getString(ProjectConstant.ERROR));
        }
        JSONObject categoryPropertyInfoMapData = categoryPropertyInfoMapBody.getJSONObject(ProjectConstant.DATA);
        if (Objects.isNull(categoryPropertyInfoMapData)) {
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, "查询类目属性失败");
        }
        ChannelSkuCategoryProperty categoryPropertyInfo = categoryPropertyInfoMapData.toJavaObject(ChannelSkuCategoryProperty.class);
        List<ChannelSkuCategoryProperty.PropValueDTO> itemPropValues = categoryPropertyInfo.getItemPropValues();
        List<PropValueDTO> propertyList = Fun.map(itemPropValues, each -> {
            PropValueDTO spuCategoryPropertyInfo = new PropValueDTO();
            spuCategoryPropertyInfo.setPropId(Long.parseLong(each.getPropId()));
            spuCategoryPropertyInfo.setPropText(each.getPropText());
            spuCategoryPropertyInfo.setValueId(Long.parseLong(each.getValueId()));
            spuCategoryPropertyInfo.setValueText(each.getValueText());
            return spuCategoryPropertyInfo;
        });
        updateParam.setCat_property(propertyList);
        postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SKU_UPDATE, baseRequestSimple, updateParam);
        response = postResult.get(storeId);
        if (response != null && !response.isSuccess()) {
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, "根据原商品编码更换新商品编码失败");
        }
        return ResultGenerator.genResultSpuData(ResultCode.SUCCESS, "更新 规格 ID 成功");
    }

    @Override
    public ResultSpuData updateCustomSpuIdByNameAndSpec(UpdateCustomSpuIdByNameAndSpecRequest request) {
        return ResultGenerator.genResultSpuData(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }

    /**
     * 查询渠道商品
     * 渠道接口文档：https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.list-3?aopApiCategory=item_manage&type=item_all
     *
     * @param request
     * @return
     */
    @Override
    public GetSpuInfoResponse getSpuInfo(GetSpuInfoRequest request) {
        GetSpuInfoResponse response = new GetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        ChannelStoreDO channelStoreDO = getChannelStore(request);
        if (Objects.isNull(channelStoreDO)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL));
        }

        Map<String, Object> sysParam = elmChannelGateService.getSysParam(new BaseRequest()
                .setChannelId(request.getBaseInfo().getChannelId())
                .setTenantId(request.getBaseInfo().getTenantId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId())));
        String appId = String.valueOf(sysParam.get(ProjectConstant.ELM_SOURCE));

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostELMEnum.BATCH_GET_SKUINFO, appId);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY, ProjectConstant.TRIGGER_LIMIT_MSG));
        }
        Map<String, String> bizParam = Maps.newHashMap();
        bizParam.put("page", Constant.ONE_STR);
        bizParam.put("pageSize",Constant.ONE_STR);
        bizParam.put("shop_id", channelStoreDO.getChannelOnlinePoiCode());
        bizParam.put("include_cate_info", Constant.ONE_STR);
        if (StringUtils.isNotBlank(request.getSpuId())) {
            bizParam.put("sku_id", request.getSpuId());
        }
        if (StringUtils.isNotBlank(request.getCustomSpuId())) {
            bizParam.put("custom_sku_id", request.getCustomSpuId());
        }
        if (StringUtils.isNotBlank(request.getUpc())) {
            bizParam.put("upc", request.getUpc());
        }
        if (StringUtils.isNotBlank(request.getChannelSpuId())) {
            bizParam.put("sku_id", request.getChannelSpuId());
        }
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));
        Map<String, Object> skuInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, skuList, baseRequest, bizParam);
        // 超时时，会返回 null
        if (Objects.isNull(skuInfoMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, ResultCode.FAIL.getMsg()));
        }

        JSONObject skuInfoMapBody = (JSONObject) skuInfoMap.get(ProjectConstant.BODY);
        if ((int) skuInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }
        JSONObject skuInfoMapData = skuInfoMapBody.getJSONObject(ProjectConstant.DATA);
        if (Objects.isNull(skuInfoMapData) || StringUtils.isBlank(skuInfoMapData.getString("list"))) {
            return response;
        }
        List<SkuQueryDetailResultDTO> channelSkuInfos = JSON.parseArray(skuInfoMapData.getString("list"), SkuQueryDetailResultDTO.class);
        if (CollectionUtils.isEmpty(channelSkuInfos)) {
            return response;
        }
        GetCategoryResponse categoryResponse = new GetCategoryResponse();
        if(MccConfigUtil.NeedQueryChannelStoreCategory()){
            CatRequest catRequest = new CatRequest();
            catRequest.setBaseInfo(request.getBaseInfo());
            catRequest.setStoreId(request.getStoreId());
            categoryResponse = elmChannelSkuService.batchGetChannelStoreCategoryInfo(catRequest);
        }
        Map<String, CatInfo> cateInfoMap = Optional.ofNullable(categoryResponse).map(GetCategoryResponse::getCatInfoList)
                .map(catInfos -> catInfos.stream().collect(Collectors.toMap(CatInfo::getCatId, Function.identity(), (p1, p2) -> p2)))
                .orElse(Maps.newHashMap());

        // 查询业态
        boolean isMedicineUMW = tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId());
        return response.setSpuInfo(ElmConverterUtil.buildSpuInfoDTO(isMedicineUMW, channelSkuInfos.get(0), cateInfoMap));
    }

    /**
     * 根据牵牛花门店ID查询渠道门店信息
     */
    private ChannelStoreDO getChannelStore(GetSpuInfoRequest request) {
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo().getTenantId(),
                request.getBaseInfo().getChannelId(), Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return null;
        }
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                request.getStoreId());
        return channelStoreDOMap.get(channelStoreKey);
    }

    @Override
    public GetSpuInfosResponse getSpuInfoList(GetSpuInfosRequest request) {
        return null;
    }


    public BatchGetSpuInfoByOffsetResponse batchGetSpuInfoByOffset(BatchGetSpuInfoByOffsetRequest request) {
        BatchGetSpuInfoByOffsetResponse response = new BatchGetSpuInfoByOffsetResponse().setStatus(ResultGenerator.genSuccessResult());

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                request.getStoreId());
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSpuInfos(Collections.emptyList());
        }
        // 构造请求参数
        Map<String, String> bizParam = Maps.newHashMap();
        String shopId = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        //根据饿了么要求，page始终为1
        bizParam.put("page", String.valueOf(1));
        bizParam.put("shop_id", shopId);
        bizParam.put("include_cate_info", Constant.ONE_STR);
        bizParam.put("sku_id_offset", String.valueOf(request.getSkuIdOffset()));
        bizParam.put("pagesize",String.valueOf(request.getPageSize()));

        Map<String, Object> sysParam = elmChannelGateService.getSysParam(new BaseRequest()
                .setChannelId(request.getBaseInfo().getChannelId())
                .setTenantId(request.getBaseInfo().getTenantId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId())));
        String appId = String.valueOf(sysParam.get(ProjectConstant.ELM_SOURCE));

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostELMEnum.BATCH_GET_SKUINFO, appId);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY)).setSpuInfos(Collections.emptyList());
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));

        Map<String, Object> skuInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, skuList, baseRequest, bizParam);
        // 超时时，会返回 null
        if (Objects.isNull(skuInfoMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, ResultCode.FAIL.getMsg()));
        }

        JSONObject skuInfoMapBody = (JSONObject) skuInfoMap.get(ProjectConstant.BODY);
        if ((int) skuInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }

        JSONObject skuInfoMapData = skuInfoMapBody.getJSONObject(ProjectConstant.DATA);
        // 组装数据
        List<SkuQueryDetailResultDTO> channelSkuInfos = JSON.parseArray(skuInfoMapData.getString("list"), SkuQueryDetailResultDTO.class);
        if (CollectionUtils.isEmpty(channelSkuInfos)) {
            return response.setSpuInfos(Collections.emptyList());
        }
        //查询店内分类
        CatRequest catRequest = new CatRequest();
        catRequest.setBaseInfo(request.getBaseInfo());
        catRequest.setStoreId(request.getStoreId());
        GetCategoryResponse categoryResponse = elmChannelSkuService.batchGetChannelStoreCategoryInfo(catRequest);

        // 查询业态
        boolean isMedicineUMW = tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId());

        long skuIdOffset = Long.parseLong(skuInfoMapData.getString("sku_id_offset"));
        List<SpuInfoDTO> spuInfoDTOs = ElmConverterUtil.convert2Spu(isMedicineUMW, channelSkuInfos, categoryResponse.getCatInfoList());
        return response.setStatus(ResultGenerator.genSuccessResult()).setSpuInfos(spuInfoDTOs).setSkuIdOffset(skuIdOffset);
    }

    //饿了么商品信息转换为牵牛花spu信息


    @Override
    public BatchGetSpuInfoResponse batchGetSpuInfo(BatchGetSpuInfoRequest request) {
        BatchGetSpuInfoResponse response = new BatchGetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                request.getStoreId());
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSpuInfos(Collections.emptyList());
        }
        // 构造请求参数
        Map<String, String> bizParam = Maps.newHashMap();
        String shopId = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        //根据饿了么要求，page始终为1
        String offset = request.getOffset();

        bizParam.put("page", StringUtils.isNotBlank(offset) ? String.valueOf(1) : String.valueOf(request.getPageNum()));
        bizParam.put("shop_id", shopId);
        bizParam.put("include_cate_info", Constant.ONE_STR);
        bizParam.put("sku_id_offset", offset);
        bizParam.put("pagesize",String.valueOf(request.getPageSize()));
        if ((request.isSetUnCategory() || request.getUnCategory() > 0)) {
            bizParam.put("get_uncate", "1");
        }

        Map<String, Object> sysParam = elmChannelGateService.getSysParam(new BaseRequest()
                .setChannelId(request.getBaseInfo().getChannelId())
                .setTenantId(request.getBaseInfo().getTenantId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId())));
        String appId = String.valueOf(sysParam.get(ProjectConstant.ELM_SOURCE));

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostELMEnum.BATCH_GET_SKUINFO, appId);
        if (!tryAcquire) {
            log.warn("ElmChannelSpuServiceImpl.batchGetSpuInfo 触发限流，tenantId: {}, storeId: {}", request.getBaseInfo().getTenantId(), request.getStoreId());
            return response.setStatus(ResultGenerator.genResult(ResultCode.TRIGGER_LIMIT)).setSpuInfos(Collections.emptyList());
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));
        Map<String, Object> skuInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, skuList, baseRequest, bizParam);
        // 超时时，会返回 null
        if (Objects.isNull(skuInfoMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, ResultCode.FAIL.getMsg()));
        }

        JSONObject skuInfoMapBody = (JSONObject) skuInfoMap.get(ProjectConstant.BODY);
        if ((int) skuInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }

        JSONObject skuInfoMapData = skuInfoMapBody.getJSONObject(ProjectConstant.DATA);
        // 组装数据
        List<SkuQueryDetailResultDTO> channelSkuInfos = JSON.parseArray(skuInfoMapData.getString("list"), SkuQueryDetailResultDTO.class);

        GetCategoryResponse categoryResponse = new GetCategoryResponse();
        // 仅在需要时查询店内分类，节约资源
        if(request.needCategory){
            CatRequest catRequest = new CatRequest();
            catRequest.setBaseInfo(request.getBaseInfo());
            catRequest.setStoreId(request.getStoreId());
            categoryResponse = elmChannelSkuService.batchGetChannelStoreCategoryInfo(catRequest);
        }
        // 查询业态
        boolean isMedicineUMW = tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId());
        List<SpuInfoDTO> spuInfoDTOs = ElmConverterUtil.convert2Spu(isMedicineUMW, channelSkuInfos, categoryResponse.getCatInfoList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(skuInfoMapData.getInteger("page"));
        pageInfo.setTotalPage(skuInfoMapData.getInteger("pages"));
        pageInfo.setTotalNum(skuInfoMapData.getInteger("total"));

        return response.setStatus(ResultGenerator.genSuccessResult())
                .setSpuInfos(spuInfoDTOs)
                .setPageInfo(pageInfo)
                .setOffset(skuInfoMapData.getString("sku_id_offset"));
    }

    @Override
    public BatchGetSpuInfoResponse getSpuInfosByCategory(GetSpuInfosByCategoryRequest request) {
        BatchGetSpuInfoResponse response = new BatchGetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                request.getStoreId());
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSpuInfos(Collections.emptyList());
        }
        // 构造请求参数
        Map<String, String> bizParam = Maps.newHashMap();
        String shopId = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();

        bizParam.put("page", String.valueOf(request.getPageNum()));
        bizParam.put("shop_id", shopId);
        bizParam.put("category_id", request.getChannelCategoryId());
        bizParam.put("pagesize",String.valueOf(request.getPageSize()));

        Map<String, Object> sysParam = elmChannelGateService.getSysParam(new BaseRequest()
                .setChannelId(request.getBaseInfo().getChannelId())
                .setTenantId(request.getBaseInfo().getTenantId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId())));
        String appId = String.valueOf(sysParam.get(ProjectConstant.ELM_SOURCE));

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostELMEnum.BATCH_GET_SKUINFO_BY_CATEGORY, appId);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY)).setSpuInfos(Collections.emptyList());
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));
        Map<String, Object> skuInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, ChannelPostELMEnum.BATCH_GET_SKUINFO_BY_CATEGORY.getUrl(), baseRequest, bizParam);
        // 超时时，会返回 null
        if (Objects.isNull(skuInfoMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, ResultCode.FAIL.getMsg()));
        }

        JSONObject skuInfoMapBody = (JSONObject) skuInfoMap.get(ProjectConstant.BODY);
        if ((int) skuInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }

        JSONObject skuInfoMapData = skuInfoMapBody.getJSONObject(ProjectConstant.DATA);
        // 组装数据
        List<SkuQueryDetailResultDTO> channelSkuInfos = JSON.parseArray(skuInfoMapData.getString("list"), SkuQueryDetailResultDTO.class);

        //查询店内分类
        CatRequest catRequest = new CatRequest();
        catRequest.setBaseInfo(request.getBaseInfo());
        catRequest.setStoreId(request.getStoreId());
        GetCategoryResponse categoryResponse = elmChannelSkuService.batchGetChannelStoreCategoryInfo(catRequest);
        // 查询业态
        boolean isMedicineUMW = tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId());
        List<SpuInfoDTO> spuInfoDTOs = ElmConverterUtil.convert2Spu(isMedicineUMW, channelSkuInfos, categoryResponse.getCatInfoList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(skuInfoMapData.getInteger("page"));
        pageInfo.setTotalPage(skuInfoMapData.getInteger("pages"));
        pageInfo.setTotalNum(skuInfoMapData.getInteger("total"));

        return response.setStatus(ResultGenerator.genSuccessResult())
                .setSpuInfos(spuInfoDTOs)
                .setPageInfo(pageInfo);
    }

    @Override
    public GetHeadQuarterSpuInfoResponse batchGetHeadQuarterSpuInfo(BasePageRequest request) {
        return null;
    }

    @Override
    public QueryAuditStatusResponse getAuditStatus(QueryAuditStatusRequest request) {
        return null;
    }

    @Override
    public RecommendChannelCategoryQueryResponse queryRecommendChannelCategory(RecommendChannelCategoryQueryRequest request) {
        return null;
    }

    @Override
    public SkuId2AppFoodCodeResponse queryAppFoodCodeBySkuId(QueryAppFoodCodeBySkuIdRequest request) {
        return null;
    }

    @Override
    public ResultStatus updateCustomSpuStoreCategory(UpdateCustomSpuStoreCategoryRequest request) {
        ResultStatus status = ResultGenerator.genResult(ResultCode.SUCCESS);
        ChannelSpuCategoryMapDTO updateParam = elmConverterService.convert2ChannelSpuCategoryMapDTO(request);
        // 调用渠道接口
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));
        Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SKU_CATEGORY_MAPPING,
                baseRequest, updateParam);
        ChannelResponseDTO response = postResult.get(request.getStoreId());
        if (response != null && !response.isSuccess()) {
            return ResultGenerator.genResult(ResultCode.FAIL, response.getErrorMsg());
        }
        return status;
    }

    @Override
    public ResultSpuData batchUpdateSpuStoreCategoryCode(BatchUpdateCustomSpuStoreCategoryRequest request) {
        List<ProductStoreCategoryDTO> productStoreCategoryList = request.getProductStoreCategoryList();
        if (CollectionUtils.isEmpty(productStoreCategoryList)) {
            return ResultGenerator.genResultSpuData(ResultCode.INVALID_PARAM, "商品列表不能为空");
        }

        List<ResultSuccessSpu> resultSuccessSpus = new CopyOnWriteArrayList<>();
        List<ResultErrorSpu> resultErrorSpus = new CopyOnWriteArrayList<>();

        List<Callable<ChannelResponseDTO>> callableList = Lists.newArrayList();
        //循环调用接口
        productStoreCategoryList.forEach(productStoreCategoryDTO ->  callableList.add(() -> {
            ChannelSpuCategoryMapDTO updateParam = elmConverterService.convert2ChannelSpuCategoryMapDTO(productStoreCategoryDTO);
            // 调用渠道接口
            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(request.getBaseInfo().getTenantId())
                    .setChannelId(request.getBaseInfo().getChannelId())
                    .setStoreIdList(Collections.singletonList(request.getStoreId()));
            Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SKU_CATEGORY_MAPPING, baseRequest, updateParam);
            ChannelResponseDTO response = postResult.get(request.getStoreId());
            if (response != null && !response.isSuccess()) {
                ResultErrorSpu resultErrorSpu = new ResultErrorSpu();
                resultErrorSpu.setErrorMsg(response.getErrorMsg());
                resultErrorSpu.setErrorCode(ResultCode.FAIL.getCode());
                resultErrorSpu.setChannelId(ChannelTypeEnum.ELEM.getCode());
                SpuKey spuKey = new SpuKey();
                spuKey.setCustomSpuId(productStoreCategoryDTO.getCustomSpuId());
                resultErrorSpu.setSpuInfo(spuKey);
                resultErrorSpu.setStoreId(request.getStoreId());
                resultErrorSpus.add(resultErrorSpu);
            } else {
                ResultSuccessSpu resultSuccessSpu = new ResultSuccessSpu();
                SpuKey spuKey = new SpuKey();
                spuKey.setCustomSpuId(productStoreCategoryDTO.getCustomSpuId());
                resultSuccessSpu.setSpuInfo(spuKey);
                resultSuccessSpu.setStoreId(request.getStoreId());
                resultSuccessSpu.setChannelId(ChannelTypeEnum.ELEM.getCode());
                resultSuccessSpus.add(resultSuccessSpu);
            }
            return response;
        }));
        // 并发执行
        ConcurrentUtils.concurrentExecute(pushExecutor, callableList, "批量修改商品店内分类失败");

        ResultSpuData resultSpuData = new ResultSpuData();
        resultSpuData.setSucData(resultSuccessSpus);
        resultSpuData.setErrorData(resultErrorSpus);
        if (CollectionUtils.isEmpty(resultErrorSpus)) {
            resultSpuData.setStatus(genResult(ResultCode.SUCCESS));
        } else {
            resultSpuData.setStatus(genResult(ResultCode.FAIL));
        }
        return resultSpuData;
    }

    @Override
    public SensitiveWordCheckResponse checkSensitiveWords(SensitiveWordCheckRequest request) {
        ResultStatus resultStatus = ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE);
        return new SensitiveWordCheckResponse(resultStatus);
    }

    @Override
    public QueryNormAuditDelSpuResponse queryNormAuditDelSpuInfo(QueryNormAuditDelSpuRequest request) {
        return new QueryNormAuditDelSpuResponse()
                .setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setAuditStatusList(Collections.emptyList());
    }

    @Override
    public QueryChannelSpuIdResponse queryChannelSpuId(QueryChannelSpuIdRequest request) {
        return null;
    }

    @Override
    public ResultSpuData updateStoreCategory(SpuStoreCategoryInfoRequest request) {
        return null;
    }

    @Override
    public ProductSequenceBatchSetResponse batchSetSequence(ProductSequenceBatchSetRequest request) {
        return null;
    }

    @Override
    public QueryQualityProblemResponse queryQualityProblem(QueryQualityProblemRequest request) {
        return null;
    }

    @Override
    public ResultStatus updateOptionFieldBySpu(UpdateSpuOptionFieldRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest();
            baseRequest.setTenantId(request.getBaseInfo().getTenantId());
            baseRequest.setChannelId(request.getBaseInfo().getChannelId());
            baseRequest.setStoreIdList(Lists.newArrayList(request.getStoreId()));
            // 调用渠道接口
            int multiSpecType = channelCategoryThriftServiceProxy.getMultiSpecType(request.getBaseInfo().getChannelId(),
                    request.getBaseInfo().getTenantId(), request.getChannelCategoryId());

            Map<Long, ChannelResponseDTO> postResult = elmChannelGateService
                    .sendPostAppMapDto(ChannelPostELMEnum.SKU_UPDATE, baseRequest, ElemSpuOptionUpdateDTO.of(request,
                            multiSpecType));

            if(postResult == null){
                return ResultGenerator.genResult(ResultCode.RESULT_PARSE, "返回值为空");
            }
            ChannelResponseDTO responseDTO = postResult.get(request.getStoreId());
            if(responseDTO == null || responseDTO.getBody() == null){
                return ResultGenerator.genResult(ResultCode.RESULT_PARSE, "返回值为空");
            }
            if(responseDTO.isSuccess()){
                return ResultGenerator.genSuccessResult();
            }
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(responseDTO.getBody().getErrno());
            resultStatus.setMsg(responseDTO.getBody().getError());
            return resultStatus;
        } catch (IllegalArgumentException e) {
            log.error("ElmChannelSpuServiceImpl.updateOptionFieldBySpu 参数校验失败", e);
            return ResultGenerator.genResult(ResultCode.INVALID_PARAM);
        } catch (Exception e) {
            log.error("ElmChannelSpuServiceImpl.updateOptionFieldBySpu 服务异常", e);
        }
        return ResultGenerator.genResult(ResultCode.FAIL);
    }

    /**
     * 仅用于IM发送商品卡片时spu和itemId的互转
     * @param request
     * @return
     * @throws SDKException
     */

    public Long getSpuItemId(GetSpuInfoRequest request) {
        log.info("查询饿了么商品的itemId, request:{}", request);
        ChannelStoreDO channelStoreDO = getChannelStore(request);
        if (Objects.isNull(channelStoreDO)) {
            log.error("未获取到渠道门店");
            return null;
        }

        Map<String, Object> sysParam = elmChannelGateService.getSysParam(new BaseRequest()
                .setChannelId(request.getBaseInfo().getChannelId())
                .setTenantId(request.getBaseInfo().getTenantId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId())));
        String appId = String.valueOf(sysParam.get(ProjectConstant.ELM_SOURCE));

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostELMEnum.BATCH_GET_SKUINFO, appId);
        if (!tryAcquire) {
            log.error("获取当前执行权失败");
            return null;
        }
        Map<String, String> bizParam = Maps.newHashMap();
        bizParam.put("page", Constant.ONE_STR);
        bizParam.put("pageSize",Constant.ONE_STR);
        bizParam.put("shop_id", channelStoreDO.getChannelOnlinePoiCode());
        if (StringUtils.isNotBlank(request.getSpuId())) {
            bizParam.put("sku_id", request.getSpuId());
        }
        if (StringUtils.isNotBlank(request.getCustomSpuId())) {
            bizParam.put("custom_sku_id", request.getCustomSpuId());
        }
        if (StringUtils.isNotBlank(request.getUpc())) {
            bizParam.put("upc", request.getUpc());
        }
        if (StringUtils.isNotBlank(request.getChannelSpuId())) {
            bizParam.put("sku_id", request.getChannelSpuId());
        }
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));
        Map<String, Object> skuInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, skuList, baseRequest, bizParam);
        if (Objects.isNull(skuInfoMap)) {
            log.error("查询超时");
            return null;
        }
        JSONObject skuInfoMapBody = (JSONObject) skuInfoMap.get(ProjectConstant.BODY);
        if ((int) skuInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            log.error("查询返回失败, errono:{}", skuInfoMapBody.get(ProjectConstant.ERRNO));
            return null;
        }
        JSONObject skuInfoMapData = skuInfoMapBody.getJSONObject(ProjectConstant.DATA);
        if (Objects.isNull(skuInfoMapData) || StringUtils.isBlank(skuInfoMapData.getString("list"))) {
            log.warn("没有查询到对应饿了么商品");
            return null;
        }
        List<SkuQueryDetailResultDTO> channelSkuInfos = JSON.parseArray(skuInfoMapData.getString("list"), SkuQueryDetailResultDTO.class);
        if (CollectionUtils.isEmpty(channelSkuInfos)) {
            log.error("反序列化失败！");
            return null;
        }
        return channelSkuInfos.get(0).getItemId();
    }

    /**
     * 查询商品渠道类目属性
     * @param request
     * @return
     */
    @Override
    public GetSpuCategoryPropertyResponse getSpuCategoryPropertyInfo(GetSpuInfoRequest request) {
        GetSpuCategoryPropertyResponse response = new GetSpuCategoryPropertyResponse().setStatus(ResultGenerator.genSuccessResult());
        ChannelStoreDO channelStoreDO = getChannelStore(request);
        if (Objects.isNull(channelStoreDO)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL));
        }
        Map<String, Object> sysParam = elmChannelGateService.getSysParam(new BaseRequest()
                .setChannelId(request.getBaseInfo().getChannelId())
                .setTenantId(request.getBaseInfo().getTenantId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId())));
        String appId = String.valueOf(sysParam.get(ProjectConstant.ELM_SOURCE));
        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SKU_CATEGORY_PROPERTY_QUERY, appId);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY, ProjectConstant.TRIGGER_LIMIT_MSG));
        }
        Map<String, String> bizParam = Maps.newHashMap();
        bizParam.put("shop_id", channelStoreDO.getChannelOnlinePoiCode());
        if (StringUtils.isNotBlank(request.getCustomSpuId())) {
            bizParam.put("custom_sku_id", request.getCustomSpuId());
        }
        if (StringUtils.isNotBlank(request.getUpc())) {
            bizParam.put("upc", request.getUpc());
        }
        if (StringUtils.isNotBlank(request.getChannelSpuId())) {
            bizParam.put("sku_id", request.getChannelSpuId());
        }
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));
        Map<String, Object> categoryPropertyInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, ChannelPostELMEnum.SKU_CATEGORY_PROPERTY_QUERY.getUrl(), baseRequest, bizParam);
        // 超时时，会返回 null
        if (Objects.isNull(categoryPropertyInfoMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, ResultCode.FAIL.getMsg()));
        }
        JSONObject categoryPropertyInfoMapBody = (JSONObject) categoryPropertyInfoMap.get(ProjectConstant.BODY);
        if ((int) categoryPropertyInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, categoryPropertyInfoMapBody.getString(ProjectConstant.ERROR)));
        }
        JSONObject categoryPropertyInfoMapData = categoryPropertyInfoMapBody.getJSONObject(ProjectConstant.DATA);
        if (Objects.isNull(categoryPropertyInfoMapData)) {
            return response;
        }
        ChannelSkuCategoryProperty categoryPropertyInfo = categoryPropertyInfoMapData.toJavaObject(ChannelSkuCategoryProperty.class);
        List<ChannelSkuCategoryProperty.PropValueDTO> itemPropValues = categoryPropertyInfo.getItemPropValues();
        List<SpuCategoryPropertyInfo> propertyList = Fun.map(itemPropValues, each -> {
            SpuCategoryPropertyInfo spuCategoryPropertyInfo = new SpuCategoryPropertyInfo();
            spuCategoryPropertyInfo.setPropId(each.getPropId());
            spuCategoryPropertyInfo.setPropText(each.getPropText());
            spuCategoryPropertyInfo.setValueId(each.getValueId());
            spuCategoryPropertyInfo.setValueText(each.getValueText());
            spuCategoryPropertyInfo.setSaleProp(BooleanUtils.isTrue(each.getSaleProp()));
            return spuCategoryPropertyInfo;
        });
        return response.setStatus(ResultGenerator.genSuccessResult()).setPropertyList(propertyList);
    }

    @Override
    public ResultStatus submitAppealInfo(SubmitAppealInfoRequest request) {
        return null;
    }
}
