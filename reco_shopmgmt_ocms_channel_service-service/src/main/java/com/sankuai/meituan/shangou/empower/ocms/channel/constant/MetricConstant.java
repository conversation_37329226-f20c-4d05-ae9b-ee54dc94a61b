package com.sankuai.meituan.shangou.empower.ocms.channel.constant;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON>12
 * @create: 2019-02-25 13:44
 */
public class MetricConstant {

    public static final String HTTP_NAME = "HTTP接口性能统计";
    public static final String HTTP_TAG_URL = "URL";

    public static final String DEPENDENT_SERVICE = "依赖服务性能统计";
    public static final String ORDER_UP_SERVICE = "订单上行服务性能统计";
    public static final String COMMON_DOWN_SERVICE = "通用下行服务性能统计";
    public static final String ORDER_DOWN_SERVICE = "订单下行服务性能统计";
    public static final String MEMBER_DOWN_SERVICE = "会员下行服务性能统计";
    public static final String SKU_SERVICE = "商品服务性能统计(SKU)";
    public static final String SPU_SERVICE = "商品服务性能统计(SPU)";
    public static final String CATEGORY_SERVICE = "品类服务性能统计";
    public static final String BRAND_SERVICE = "品牌服务性能统计";

    public static final String CATEGORY_BRAND_SERVICE = "类目品牌服务性能统计";
    public static final String PRICE_SERVICE = "价格服务性能统计";
    public static final String STOCK_SERVICE = "库存服务性能统计";
    public static final String BASE_DATA_SERVICE = "基础数据服务性能统计";
    public static final String ACCESST_TOKEN_DOWN_SERVICE = "权限令牌下行服务性能统计";
    public static final String SERVICE = "SERVICE";
    public static final String METHOD = "METHOD";
    public static final String ACTIVITY_SERVICE = "促销服务性能统计";
    public static final String ACTIVITY_SERVICE_COUNT = "促销服务调用量统计";
    public static final String COUNT = "COUNT";
    public static final String TOTAL = "TOTAL";
    public static final String FAIL = "FAIL";
    public static final String PROMOTION = "促销服务性能统计";
    public static final String SKUNOTIFY = "非中台渠道商品更新统计";
    public static final String COMMENT_SERVICE = "评价服务性能统计";
    public static final String SKUNOTIFY_GETDETAIL = "美团渠道获取单个商品详情失败统计";

    public static final String ERP_CHANNEL_CALL_BACK = "有ERP渠道回调统计";
    public static final String CHANNEL = "channel";
    public static final String TYPE = "type";

    public static final String OCTO_SERVICE_DETAIL = "OctoServiceDetail";

    public static final String OPEN_URL = "OpenUrl";

    public static final String UNKNOWN_NAME = "UnknownName";

    public static final String PRODUCT_CALLBACK_CHANGE_FIELD = "product.callback.change.field";

    public interface ChannelErrorMetric {
        String ALL_KEY = "channel.api.call";
        String ERROR_KEY = "channel.api.error";
        String TAG_CHANNEL = "channel";
        String TAG_API = "api";
        String TAG_ERROR = "error";
        String TAG_TENANT_ID = "tenantId";
        String ERR_REQUEST = "接口异常";
    }

}
