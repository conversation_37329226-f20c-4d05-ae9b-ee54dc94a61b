
package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.HandleNewSupplyDyDeliveryChangeMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.SplitChannelBatchOrderNotifyMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.order.OrderSplitNotifyMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelBatchOrderCallbackService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.Message;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class ChannelBatchOrderCallbackServiceImpl implements ChannelBatchOrderCallbackService {

    @Resource
    private HandleNewSupplyDyDeliveryChangeMessageProducer handleNewSupplyDyDeliveryChangeMessageProducer;

    @Resource
    private SplitChannelBatchOrderNotifyMessageProducer splitChannelBatchOrderNotifyMessageProducer;

    @Override
    public void sendOrderMq(Message message, DynamicChannelType channelType, String appid) {
        OrderSplitNotifyMessage orderSplitNotifyMessage = new OrderSplitNotifyMessage(message,
                channelType.getChannelCode(), appid);
        splitChannelBatchOrderNotifyMessageProducer.sendMessageSync(orderSplitNotifyMessage);
    }
        
    @Override
    public void sendTmsDyDeliveryCallbackMq(Message message) {
        try {
            String data = message.getData();
            if (StringUtils.isEmpty(data)) {
                log.error("sendTmsDyDeliveryCallbackMq data is empty");
                return;
            }

            handleNewSupplyDyDeliveryChangeMessageProducer.sendMessageSync(data);
        } catch (Exception e) {
            log.error("sendTmsDyDeliveryCallbackMq error", e);
        }
    }

}
