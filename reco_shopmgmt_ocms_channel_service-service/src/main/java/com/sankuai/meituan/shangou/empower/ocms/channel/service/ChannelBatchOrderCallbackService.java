package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.Message;

/**
 * @description: 订单回调接口
 * @author: z<PERSON><PERSON><PERSON>12
 * @create: 2019/1/28 上午10:19
 */
public interface ChannelBatchOrderCallbackService {

    void sendOrderMq(Message message, DynamicChannelType channelType, String appid);

    void sendTmsDyDeliveryCallbackMq(Message message);
}
