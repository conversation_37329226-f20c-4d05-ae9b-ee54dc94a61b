package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ChannelConfigDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TenantTypeEnum;

/**
 * <AUTHOR>
 * @date 2023-05-23
 */
public interface TenantService {
    /**
     * 获取租户规格类型
     * @param tenantId
     * @return true 多规格，false 单规格
     */
    boolean isSpuGray(Long tenantId);

    /**
     * 是否为erp租户
     * @param tenantId
     * @return
     */
    boolean isErpSpuTenant(Long tenantId);

    /**
     * 判断租户类型
     * @param tenantId
     * @return 1 单规格租户，2 ERP租户，3 非ERP租户
     */
    TenantTypeEnum getTenantTypeEnum(Long tenantId);

    /**
     * 是否是医药无人仓租户
     */
    boolean isMedicineAdultUnmanWarehouse(Long tenantId);

    boolean isNoErpBTenant(Long tenantId);

    /**
     * 判断是否便利店或旗舰店业务
     */
    boolean isConvenienceOrFlagshipStoreMode(Long tenantId);

    /**
     * 判断租户渠道的【包装袋费用是否商家收取】配置
     * 
     * @param tenantId 租户ID
     * @param dynamicChannelType 渠道类型
     * @return true 表示商家收取包装费
     */
    boolean isMerchantsChargePackageFee(Long tenantId, DynamicChannelType dynamicChannelType);

    ChannelConfigDto queryTenantChannelConfig(long tenantId, long subjectId, int channelId, ConfigItemEnum configItemEnum);
}
