package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.dianping.zebra.util.StringUtils;
import com.github.rholder.retry.RetryException;
import com.google.common.base.Charsets;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.MetricConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTenantSysParams;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JdSkuNotifyParamInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.AbnormalSourceType;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelNotifyEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DouyinCallBackMessageTagEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MtAppealResultEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MtAppealTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TenantTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.DiffSkuContents;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.DiffSkus;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.SpuUpdateInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.SpuViolationDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.AppealInfoCallbackMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.ChannelAbnormalCallbackMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.ChannelAbnormalCallbackProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.ChannelLabelData;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.ChannelSpuCallbackProducerWrapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.ChannelSpuTagCallbackProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DouyinChannelCallbackProducerWrapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DouyinSpuCallbackForwardProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.ErpChannelSpuCallbackMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.MTSpuAppealInfoCallbackProducerWrapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.MTSpuQualityCallbackProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.MTSpuQualityCallbackProducerWrapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.QualityProblemCallbackMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.SpuAbnormalEffectEntity;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.SpuChangeCallbackMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.SpuChangeCallbackProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.SpuTagCallbackMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.SpuChangeCallbackProducerWrapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.message.DouyinCallbackMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.message.DouyinMaterialChangeInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.message.DouyinProductChangeInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.message.MaterialChangeMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.message.ProductChangeMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.ProductCallbackSpuUpdateInfoExt;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCallBackService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.DouyinChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.tenant.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.route.RouteServiceFactory;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelStoreCategory;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSkuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuAppealInfoNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuManagerAbnormalChangeRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuTagNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuViolationNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.EnumUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.RetryUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.SkuCallbackMonitors;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuNotifyCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuNotifySkuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SpuUpdateNotifyDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SpuUpdateNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.SkuNotifyType;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSpuMessageCallBackDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ChannelSpuMessageCallBackTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ProductFieldsEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.ChannelSpuMessageCallBackRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ChannelCallbackThriftService;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.Message;


import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.MetricConstant.CHANNEL;
import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.MetricConstant.ERP_CHANNEL_CALL_BACK;
import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.MetricConstant.TYPE;
import static com.sankuai.meituan.shangou.empower.ocms.channel.utils.CommonUtils.isUrlEncodedChinese;


/**
 * @author: chenzhiyang
 * @date: 2019/6/10
 * @time: 8:37 PM
 */

@Service("channelCallBackServiceImpl")
public class ChannelCallBackServiceImpl  implements ChannelCallBackService{

    /**
     * 主档商品消息时门店id默认值
     */
    private static final Long TENANT_PRODUCT_STORE_ID = -1L;
    @Resource
    private RouteServiceFactory routeServiceFactory;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private ChannelCallbackThriftService channelCallbackThriftService; //ocms处理商品服务

    @Autowired
    private ChannelSpuCallbackProducerWrapper callbackProducerWrapper;
    @Autowired
    private ChannelAbnormalCallbackProducer channelAbnormalCallbackProducer;

    @Resource
    private CommonLogger log;
    @Autowired
    TenantRemoteService tenantRemoteService;
    @Autowired
    SpuChangeCallbackProducerWrapper spuChangeCallbackProducerWrapper;
    @Autowired
    MTSpuQualityCallbackProducerWrapper mtSpuQualityCallbackProducerWrapper;

    @Autowired
    private MTSpuAppealInfoCallbackProducerWrapper mtSpuAppealInfoCallbackProducerWrapper;

    @Autowired
    private ChannelSpuTagCallbackProducer channelSpuTagCallbackProducer;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private DouyinChannelCallbackProducerWrapper douyinChannelCallbackProducerWrapper;
    @Autowired
    private DouyinChannelCommonService douyinChannelCommonService;
    @Autowired
    private DouyinSpuCallbackForwardProducer douyinSpuCallbackForwardProducer;

    @Override
    public ResultStatus skuNotify(SkuNotifyRequest request) {
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        if (Objects.isNull(channelTypeEnum)) {
            log.error("ChannelCallBackServiceImpl.skuNotify, 渠道回调商品消息 未知渠道编码, request:{}", request);
            return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
        }
        ChannelNotifyEnum notifyEnum = EnumUtil.getEnumByAbbrev(request.getAction(), ChannelNotifyEnum.class);
        if (Objects.isNull(notifyEnum)) {
            log.error("ChannelCallBackServiceImpl.skuNotify, 渠道回调商品消息 未知操作, request:{}", request);
            return ResultGenerator.genResult(ResultCode.CHANNEL_NOTIFY_UNKNOWN).setData(ProjectConstant.NG);
        }
        //1.区分渠道channelCode
        //2.区分消息action（新增商品消息 修改商品消息）需要枚举
        //3.调用中台商品模块
        //4.返回处理结果（不同渠道要求的code不同，美团识别data，饿百京东识别code）
        switch (channelTypeEnum) {
            case MEITUAN:
            case MT_DRUNK_HORSE:
                return mtNotifyHandler(channelTypeEnum, notifyEnum, request);
            case JD2HOME:
                return jddjNotifyHandler(channelTypeEnum, notifyEnum, request);
            default:
                return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
        }
    }

    private ResultStatus mtNotifyHandler(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, SkuNotifyRequest request) {
        //1.区分消息action（订单状态消息、配送状态消息、订单修改消息等）需要枚举
        switch (notifyEnum) {
            case MT_NEW_SKU_NOTIFY :
                return createSpuNotify(channelTypeEnum, notifyEnum, request);
            case MT_UPDATE_SKU_NOTIFY:
                return updateSpuNotify(channelTypeEnum, notifyEnum, request);
            case MT_DELETE_SKU_NOTIFY:
                return deleteSpuNotify(channelTypeEnum, notifyEnum, request);
            default:
                return ResultGenerator.genResult(ResultCode.CHANNEL_NOTIFY_UNKNOWN).setData(ProjectConstant.NG);
        }
    }

    private ResultStatus jddjNotifyHandler(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, SkuNotifyRequest request) {
        // 参数判空
        if (StringUtils.isBlank(request.getData())) {
            return ResultGenerator.genFailResult("data 为空");
        }
        // 解析参数
        JdSkuNotifyParamInfo paramInfo = JSON.parseObject(urlDecode(request.getData()), JdSkuNotifyParamInfo.class);
        if(Objects.isNull(paramInfo)){
            log.error("jddjNotifyHandler 参数解析异常，request:{}", request);
            return ResultGenerator.genFailResult("参数解析异常");
        }
        // 区分消息action
        switch (notifyEnum) {
            case JDDJ_NEW_SKU_NOTIFY :
                return createJddjNewSKuNotify(channelTypeEnum, request, paramInfo);
            case JDDJ_STOCK_IS_HAVE:
                return createStockIsHaveNotify(channelTypeEnum, request, paramInfo);
            case JDDJ_MANAGE_REASON:
                return jddjManageReason(channelTypeEnum, request, paramInfo);
            default:
                return ResultGenerator.genResult(ResultCode.CHANNEL_NOTIFY_UNKNOWN).setData(ProjectConstant.NG);
        }
    }


    @Override
    public void douyinNotify(Message message, DouyinCallBackMessageTagEnum callBackMessageTagEnum, String appId){

        if(message == null){
           return;
        }

        if (Objects.isNull(message.getData())) {
            log.info("抖音回调消息体为空:msgId:{},typeE:{}", message.getMsg_id(), callBackMessageTagEnum.getDesc());
            return;
        }

        switch (callBackMessageTagEnum){
            case PRODUCT_CHANGE:
                douyinProductChangeNotify(message, appId);
                break;
            case MATERIAL_AUDIT:
                douyinMaterialAuditNotify(message, appId);
                break;
            default:
                break;
        }
    }


    @Override
    public void sendDouyinCallbackMq(Message message, String appId) {
        DouyinCallbackMessage douyinCallbackMessage = new DouyinCallbackMessage();
        douyinCallbackMessage.setMessage(message);
        douyinCallbackMessage.setAppKey(appId);
        douyinSpuCallbackForwardProducer.sendMessage(douyinCallbackMessage);
    }


    /**
     * 抖音商品变更消息
     */
    private void douyinProductChangeNotify(Message message, String appId){
        DouyinProductChangeInfo douyinProductChangeInfo = null;
        try {
            douyinProductChangeInfo = JacksonUtils.parse(message.getData(), DouyinProductChangeInfo.class);
        } catch (Exception e) {
            log.error("解析抖音消息异常:{},error:{}", JacksonUtils.toJson(message), e.getMessage(), e);
        }

        if (douyinProductChangeInfo != null) {
            if (douyinProductChangeInfo.getShop_id() == null) {
                log.info("抖音商品回调消息shop_id为空:{}", message.getData());
                return;
            }
            Long tenantId = getTenantId(String.valueOf(douyinProductChangeInfo.getShop_id()), appId);
            if (tenantId == null) {
                return;
            }
            ProductChangeMessage changeMessage = ProductChangeMessage.of(douyinProductChangeInfo);
            changeMessage.setTenantId(tenantId);

            String partKey = String.format("%d_%d_%d", changeMessage.getTenantId(), changeMessage.getStoreId(),
                    changeMessage.getChannelId());
            douyinChannelCallbackProducerWrapper.sendProductMessage(changeMessage, partKey);
        }
    }


    private Long getTenantId(String shopId, String appKey) {
        try {
            return douyinChannelCommonService.getTenantIdByHeadShopId(shopId, appKey);
        } catch (Exception e) {
            log.error("获取租户信息失败shopId:{}, appKey:{}", shopId, appKey, e);
            return null;
        }
    }


    /**
     * 抖音素材审核消息
     */
    private void douyinMaterialAuditNotify(Message message,  String appId){
        DouyinMaterialChangeInfo douyinMaterialChangeInfo = null;
        try {
            douyinMaterialChangeInfo = JacksonUtils.parse(message.getData(), DouyinMaterialChangeInfo.class);
        } catch (Exception e) {
            log.error("解析抖音消息异常:{},error:{}", JacksonUtils.toJson(message), e.getMessage(), e);
        }

        if (douyinMaterialChangeInfo != null) {

            if (douyinMaterialChangeInfo.getShop_id() == null) {
                log.info("抖音素材回调消息shop_id为空:{}", message.getData());
                return;
            }
            Long tenantId = getTenantId(String.valueOf(douyinMaterialChangeInfo.getShop_id()), appId);
            if (tenantId == null) {
                return;
            }
            MaterialChangeMessage changeMessage = MaterialChangeMessage.of(douyinMaterialChangeInfo);
            changeMessage.setTenantId(tenantId);
            String partKey = String.format("%d_%d", changeMessage.getTenantId(), changeMessage.getChannelId());
            douyinChannelCallbackProducerWrapper.sendMaterialMessage(changeMessage, partKey);
        }
    }


    private String urlDecode(String urlParam) {
        if (StringUtils.isNotBlank(urlParam)) {
            try {
                urlParam = URLDecoder.decode(urlParam, Charsets.UTF_8.displayName());
            }
            catch (Exception e) {
                log.warn("Exception:" + Charsets.UTF_8.displayName() + e.getMessage());
            }
        }
        return urlParam;
    }

    /**
     * 京东商品治理消息转发
     * @param channelTypeEnum
     * @param request
     * @param paramInfo
     * @return
     */
    private ResultStatus jddjManageReason(ChannelTypeEnum channelTypeEnum, SkuNotifyRequest request, JdSkuNotifyParamInfo paramInfo) {
        // 根据渠道编码和渠道APPID获取租户ID和牵牛花appId
        CopAccessConfigDO copAccessConfigDO = getCopAccessConfigDOParam(channelTypeEnum.getCode(), request.getTenantAppId());
        if (Objects.isNull(copAccessConfigDO)) {
            log.info("ChannelCallBackServiceImpl.createJddjNewSKuNotify, return [租户校验不通过],copAccessConfigDO:{} request:{}", copAccessConfigDO, request);
            return ResultGenerator.genFailResult("租户校验不通过");
        }
        long tenantId = copAccessConfigDO.getTenantId();
        int appId = copAccessConfigDO.getAppId().intValue();
        // 校验租户
        if (tenantId == ProjectConstant.UNKNOW_TENANT_ID) {
            log.info("ChannelCallBackServiceImpl.jddjManageReason, return [租户校验不通过],tenantId:{} request:{}", tenantId, request);
            return ResultGenerator.genSuccessResult();
        }
        // 发送商品违规消息
        ChannelSpuMessageCallBackDTO callBackDTO = new ChannelSpuMessageCallBackDTO();
        callBackDTO.setCustomSpuId(paramInfo.getOutBillId());
        // channelSkuId将替换channelSpuId字段，需求全量后可删除channelSpuId赋值代码
        callBackDTO.setChannelSpuId(paramInfo.getBillId());
        callBackDTO.setChannelSkuId(paramInfo.getBillId());
        callBackDTO.setStoreId(TENANT_PRODUCT_STORE_ID);
        callBackDTO.setSourceCode(AbnormalSourceType.PUNISH_CALLBACK.getCode());
        callBackDTO.setAppId(appId);
        ChannelSpuMessageCallBackRequest callBackRequest = new ChannelSpuMessageCallBackRequest();
        callBackRequest.setTenantId(tenantId);
        callBackRequest.setChannelId(channelTypeEnum.getCode());
        callBackRequest.setChannelSpuMessageCallBackTypeEnum(ChannelSpuMessageCallBackTypeEnum.VIOLATION);
        callBackRequest.setChannelSpuMessageCallBackDTOS(Arrays.asList(callBackDTO));
        log.info("ChannelCallBackServiceImpl.jddjManageReason, 转发商品治理消息 request:{}", callBackRequest);
        sendCallbackMessage(callBackRequest);
        return ResultGenerator.genSuccessResult();
    }

    /**
     * 京东商品新增消息转发
     * @param channelTypeEnum
     * @param request
     * @param paramInfo
     * @return
     */
    private ResultStatus createJddjNewSKuNotify(ChannelTypeEnum channelTypeEnum, SkuNotifyRequest request, JdSkuNotifyParamInfo paramInfo) {
        // 根据渠道编码和渠道APPID获取租户ID和牵牛花appId
        CopAccessConfigDO copAccessConfigDO = getCopAccessConfigDOParam(channelTypeEnum.getCode(), request.getTenantAppId());
        if (Objects.isNull(copAccessConfigDO)) {
            log.info("ChannelCallBackServiceImpl.createJddjNewSKuNotify, return [租户校验不通过],copAccessConfigDO:{} request:{}", copAccessConfigDO, request);
            return ResultGenerator.genFailResult("租户校验不通过");
        }
        long tenantId = copAccessConfigDO.getTenantId();
        int appId = copAccessConfigDO.getAppId().intValue();
        // 校验租户
        if (tenantId == ProjectConstant.UNKNOW_TENANT_ID) {
            log.info("ChannelCallBackServiceImpl.createJddjNewSKuNotify, return [租户校验不通过],tenantId:{} request:{}", tenantId, request);
            return ResultGenerator.genSuccessResult();
        }

        // 发送创建商品消息
        ChannelSpuMessageCallBackDTO callBackDTO = new ChannelSpuMessageCallBackDTO();
        callBackDTO.setCustomSpuId(paramInfo.getOutBillId());
        callBackDTO.setChannelSpuId(paramInfo.getBillId());
        callBackDTO.setOperationSource(paramInfo.getRemark());
        callBackDTO.setStoreId(TENANT_PRODUCT_STORE_ID);
        callBackDTO.setAppId(appId);
        ChannelSpuMessageCallBackRequest callBackRequest = new ChannelSpuMessageCallBackRequest();
        callBackRequest.setTenantId(tenantId);
        callBackRequest.setChannelId(channelTypeEnum.getCode());
        callBackRequest.setChannelSpuMessageCallBackTypeEnum(ChannelSpuMessageCallBackTypeEnum.CREATE);
        callBackRequest.setChannelSpuMessageCallBackDTOS(Arrays.asList(callBackDTO));
        log.info("ChannelCallBackServiceImpl.createJddjNewSKuNotify, 转发新增商品消息 request:{}", callBackRequest);
        sendCallbackMessage(callBackRequest);
        return ResultGenerator.genSuccessResult();
    }
    /**
     * 京东门店商品库存上下架状态消息
     * @param channelTypeEnum
     * @param request
     * @param paramInfo
     * @return
     */
    private ResultStatus createStockIsHaveNotify(ChannelTypeEnum channelTypeEnum, SkuNotifyRequest request, JdSkuNotifyParamInfo paramInfo) {

        // 根据渠道编码和渠道APPID获取租户ID
        long tenantId = getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId());
        // 根据租户ID、渠道编码、渠道门店编码获取内部租户ID
        ChannelStoreDO channelStoreDO = copChannelStoreService.selectChannelStore(tenantId, channelTypeEnum.getCode(), paramInfo.getStationNo());
        long storeId = channelStoreDO.getStoreId();
        // 校验租户门店
        if (tenantId == ProjectConstant.UNKNOW_TENANT_ID || storeId == ProjectConstant.UNKNOW_STORE_ID) {
            log.info(
                    "ChannelCallBackServiceImpl.createStockIsHaveNotify, return [租户 门店 校验不通过],tenantId:{} storeId:{} request: {} paramInfo:{}",
                    tenantId, storeId, request, paramInfo);
            return ResultGenerator.genSuccessResult();
        }

        // 发送上下架消息
        ChannelSpuMessageCallBackDTO callBackDTO = new ChannelSpuMessageCallBackDTO();
        callBackDTO.setStoreId(storeId);
        callBackDTO.setAppId(channelStoreDO.getIntAppId());
        callBackDTO.setCustomSpuId(paramInfo.getOutBillId());
        callBackDTO.setChannelSpuId(paramInfo.getBillId());
        callBackDTO.setOperationSource(paramInfo.getOperSource());
        List<ProductFieldsEnum> changedFields = Lists.newArrayList();
        changedFields.add(ProductFieldsEnum.SALE_STATUS);
        callBackDTO.setChangedFields(changedFields);
        // channelSkuId将替换channelSpuId字段，需求全量后可删除channelSpuId赋值代码
        callBackDTO.setChannelSpuId(paramInfo.getSkuId());
        callBackDTO.setChannelSkuId(paramInfo.getSkuId());
        ChannelSpuMessageCallBackRequest callBackRequest = new ChannelSpuMessageCallBackRequest();
        callBackRequest.setTenantId(tenantId);
        callBackRequest.setChannelId(channelTypeEnum.getCode());
        callBackRequest.setChannelSpuMessageCallBackTypeEnum(ChannelSpuMessageCallBackTypeEnum.UPDATE);
        callBackRequest.setChannelSpuMessageCallBackDTOS(Arrays.asList(callBackDTO));
        log.info("ChannelCallBackServiceImpl.createStockIsHaveNotify, 转发上下架状态变化消息 request:{}", callBackRequest);
        sendCallbackMessage(callBackRequest);
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus skuViolationNotify(SkuViolationNotifyRequest request) {
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        if (Objects.isNull(channelTypeEnum)) {
            log.error("ChannelCallBackServiceImpl.skuViolationNotify, 渠道回调商品消息 未知渠道编码, request:{}", request);
            return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
        }
        ChannelNotifyEnum notifyEnum = EnumUtil.getEnumByAbbrev(request.getAction(), ChannelNotifyEnum.class);
        if (Objects.isNull(notifyEnum)) {
            log.error("ChannelCallBackServiceImpl.skuViolationNotify, 渠道回调商品消息 未知操作, request:{}", request);
            return ResultGenerator.genResult(ResultCode.CHANNEL_NOTIFY_UNKNOWN).setData(ProjectConstant.NG);
        }
        switch (channelTypeEnum) {
            case MEITUAN:
            case MT_DRUNK_HORSE:
                return spuViolationHandler(channelTypeEnum, notifyEnum, request);
            default:
                return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
        }
    }

    @Override
    public ResultStatus skuTagNotify(SkuTagNotifyRequest request) {
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        if (Objects.isNull(channelTypeEnum)) {
            log.error("ChannelCallBackServiceImpl.skuManagerAbnormalChange, 渠道回调商品消息 未知渠道编码, request:{}", request);
            return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
        }

        long tenantId = getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId());
        if (tenantId == ProjectConstant.UNKNOW_TENANT_ID) {
            log.warn("ChannelCallBackServiceImpl.skuManagerAbnormalChange, return [租户校验不通过], tenantId : {}", tenantId);
            return ResultGenerator.genFailResult("tenant app id 无效");
        }
        long storeId = copChannelStoreService.selectChannelStoreId(tenantId, channelTypeEnum.getCode(), request.getAppPoiCode());
        if (storeId == ProjectConstant.UNKNOW_STORE_ID) {
            log.info("ChannelCallBackServiceImpl.skuManagerAbnormalChange, return [门店 校验不通过],storeId:{}", storeId);
            return ResultGenerator.genFailResult("门店 校验不通过");
        }

        String labelDataJson = URLDecoder.decode(request.getLabelData());
        ChannelLabelData labelData = JacksonUtils.parse(labelDataJson, ChannelLabelData.class);

        channelSpuTagCallbackProducer.sendMessage(buildSpuTagMessage(tenantId, storeId, channelTypeEnum.getCode(), request, labelData), storeId);

        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }

    private SpuTagCallbackMessage buildSpuTagMessage(long tenantId, long storeId, int channelId, SkuTagNotifyRequest request, ChannelLabelData labelData) {
        SpuTagCallbackMessage message = new SpuTagCallbackMessage();

        message.setTenantId(tenantId);
        message.setStoreId(storeId);
        message.setChannelId(channelId);
        message.setCustomSpuId(request.getAppSpuCode());
        message.setLabelId(labelData.getLabel_code());
        message.setLabelName(labelData.getLabel_name());
        message.setLabelType(labelData.getLabel_type());
        message.setLabelSource(labelData.getLabel_source());

        return message;
    }

    @Override
    public ResultStatus skuManagerAbnormalChange(SkuManagerAbnormalChangeRequest request) {
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        if (Objects.isNull(channelTypeEnum)) {
            log.error("ChannelCallBackServiceImpl.skuManagerAbnormalChange, 渠道回调商品消息 未知渠道编码, request:{}", request);
            return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
        }
        if(ChannelTypeEnum.MEITUAN.equals(channelTypeEnum)){
            return spuManagerAbnormalChangeHandler(channelTypeEnum,request);
        }else{
            return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
        }
    }

    @Override
    public ResultStatus skuAppealInfoNotify(SkuAppealInfoNotifyRequest request) {
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        if (Objects.isNull(channelTypeEnum)) {
            log.error("ChannelCallBackServiceImpl.skuAppealInfoNotify, 渠道回调商品申诉消息 未知渠道编码, request:{}", request);
            return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
        }
        if (ChannelTypeEnum.MEITUAN.equals(channelTypeEnum)) {
            return handleSkuAppealInfo(EnhanceChannelType.MT.getChannelId(), request);
        } else {
            return ResultGenerator.genResult(ResultCode.CHANNEL_CODE_INVALID).setData(ProjectConstant.NG);
        }
    }

    private ResultStatus handleSkuAppealInfo(Integer channelId, SkuAppealInfoNotifyRequest request) {
        long tenantId = getTenantIdParam(channelId, request.getTenantAppId());
        if (tenantId == ProjectConstant.UNKNOW_TENANT_ID) {
            log.warn("ChannelCallBackServiceImpl.skuAppealInfoNotify, return [租户校验不通过], tenantId : {}", tenantId);
            return ResultGenerator.genFailResult("tenant app id 无效");
        }
        // 根据租户ID、渠道编码、渠道门店编码获取内部租户ID
        long storeId = copChannelStoreService.selectChannelStoreId(tenantId, channelId, request.getAppPoiCode());
        // 校验租户门店
        if (storeId == ProjectConstant.UNKNOW_STORE_ID) {
            log.info("ChannelCallBackServiceImpl.skuAppealInfoNotify, return [门店 校验不通过],storeId:{}", storeId);
            return ResultGenerator.genFailResult("门店 校验不通过");
        }

        // 校验申诉结果是否符合要求
        if (!MtAppealResultEnum.isValidValue(request.getAppealResult())) {
            log.info("ChannelCallBackServiceImpl.skuAppealInfoNotify, return [申诉结果 校验不通过],appealResult:{}",
                    request.getAppealResult());
            return ResultGenerator.genFailResult("申诉结果变更值 校验不通过");
        }

        // 校验申诉类型是否符合要求
        if (!MtAppealTypeEnum.isValidValue(request.getAppealType())) {
            log.info("ChannelCallBackServiceImpl.skuAppealInfoNotify, return [申诉类型 校验不通过],appealType:{}",
                    request.getAppealType());
            return ResultGenerator.genFailResult("申诉类型值 校验不通过");
        }

        // 发送门店商品申诉信息消息
        AppealInfoCallbackMessage appealInfoCallbackMessage = new AppealInfoCallbackMessage();
        appealInfoCallbackMessage.setTenantId(tenantId);
        appealInfoCallbackMessage.setChannelId(channelId);
        appealInfoCallbackMessage.setCustomerSpuId(request.getAppSpuCode());
        appealInfoCallbackMessage.setStoreId(storeId);
        appealInfoCallbackMessage.setAppealResult(request.getAppealResult());
        appealInfoCallbackMessage.setAppealType(request.getAppealType());
        mtSpuAppealInfoCallbackProducerWrapper.sendMessage(appealInfoCallbackMessage, request.getAppSpuCode());
        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }

    private ResultStatus spuManagerAbnormalChangeHandler(ChannelTypeEnum channelTypeEnum,SkuManagerAbnormalChangeRequest request){
        String qualityInfo = URLDecoder.decode(request.getQualityInfo());
        // 解析商品问题数据
        List<SpuAbnormalEffectEntity> spuAbnormalEffects = JSON.parseArray(qualityInfo,SpuAbnormalEffectEntity.class);
        long tenantId = getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId());
        if (tenantId == ProjectConstant.UNKNOW_TENANT_ID) {
            log.warn("ChannelCallBackServiceImpl.skuManagerAbnormalChange, return [租户校验不通过], tenantId : {}", tenantId);
            return ResultGenerator.genFailResult("tenant app id 无效");
        }
        // 根据租户ID、渠道编码、渠道门店编码获取内部租户ID
        long storeId = copChannelStoreService.selectChannelStoreId(tenantId, channelTypeEnum.getCode(), request.getAppPoiCode());
        // 校验租户门店
        if (storeId == ProjectConstant.UNKNOW_STORE_ID) {
            log.info("ChannelCallBackServiceImpl.skuManagerAbnormalChange, return [门店 校验不通过],storeId:{}", storeId);
            return ResultGenerator.genFailResult("门店 校验不通过");
        }

        // 非erp租户发送消息
        if (MccConfigUtil.pushOldAbnormalCallbackTopicSwitch() && !tenantRemoteService.isErpSpuTenant(tenantId)) {
            ChannelAbnormalCallbackMessage message = new ChannelAbnormalCallbackMessage();
            message.setTenantId(tenantId);
            message.setChannelId(channelTypeEnum.getCode());
            message.setStoreId(storeId);
            // 判断是否需要URL解码
            String appSpuCode = request.getAppSpuCode();
            if (appSpuCode != null && isUrlEncodedChinese(appSpuCode)) {
                try {
                    // 尝试进行URL解码
                    message.setCustomSpuId(URLDecoder.decode(appSpuCode,Charsets.UTF_8.displayName()));
                } catch (Exception e) {
                    // 如果解码失败，使用原始值
                    log.error("URL解码失败，使用原始值:{}，异常信息:{}",appSpuCode,e);
                    message.setCustomSpuId(appSpuCode);
                }
            } else {
                // 不需要解码的情况，直接使用原值
                message.setCustomSpuId(appSpuCode);
            }
            message.setSpuAbnormalEffects(spuAbnormalEffects);
            message.setUtime(request.getUtime());
            String partKey = String.format("%d_%d_%s", storeId, channelTypeEnum.getCode(), request.getAppSpuCode());
            channelAbnormalCallbackProducer.sendMessage(message,partKey);
//            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }

        sendAbnormalCallback(channelTypeEnum, request, spuAbnormalEffects, tenantId, storeId);


        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }

    private void sendAbnormalCallback(ChannelTypeEnum channelTypeEnum, SkuManagerAbnormalChangeRequest request, List<SpuAbnormalEffectEntity> spuAbnormalEffects, long tenantId, long storeId) {
        ErpChannelSpuCallbackMessage callbackMessage = new ErpChannelSpuCallbackMessage();
        callbackMessage.setTenantId(tenantId);
        callbackMessage.setChannelId(channelTypeEnum.getCode());
        callbackMessage.setAbnormalSourceType(AbnormalSourceType.QUALITY_PROBLEM.getCode());
        QualityProblemCallbackMessage qualityProblemCallbackMessage = new QualityProblemCallbackMessage();
        qualityProblemCallbackMessage.setUTime(request.getUtime());
        qualityProblemCallbackMessage.setCustomerSpuId(request.getAppSpuCode());
        qualityProblemCallbackMessage.setStoreId(storeId);
        qualityProblemCallbackMessage.setAbnormalEffectList(spuAbnormalEffects);
        callbackMessage.setMtSpuChangeCallbackMessage(JacksonUtils.toJson(qualityProblemCallbackMessage));
        mtSpuQualityCallbackProducerWrapper.sendMessage(callbackMessage, request.getAppSpuCode());
    }

    private ResultStatus spuViolationHandler(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, SkuViolationNotifyRequest request){
        switch (notifyEnum) {
            case MT_WAENING_SKU_NOTIFY:
            case MT_PUNISH_SKU_NOTIFY:
                return spuViolation(channelTypeEnum, notifyEnum, request);
            default:
                return ResultGenerator.genResult(ResultCode.CHANNEL_NOTIFY_UNKNOWN).setData(ProjectConstant.NG);
        }
    }

    /**
     * 推送商品违规预警信息
     * @param channelTypeEnum
     * @param notifyEnum
     * @param request
     * @return
     */
    private ResultStatus spuViolation(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, SkuViolationNotifyRequest request){
        log.info("ChannelCallBackServiceImpl.spuViolation, 渠道回调商品消息 request:{}", request);

        // 根据渠道编码和渠道APPID获取租户ID
        long tenantId = getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId());
        if (tenantId == ProjectConstant.UNKNOW_TENANT_ID) {
            log.warn("ChannelCallBackServiceImpl.spuViolation, return [租户校验不通过], tenantId : {}", tenantId);
            return ResultGenerator.genFailResult("tenant app id 无效");
        }
        //获取 tenantId  storeId  channelId  customSpuId
        SpuViolationDto spuViolationDto = new SpuViolationDto();
        //校验相关参数并解析
        ResultStatus checkStatus = checkRelationParam(request, spuViolationDto);
        if(checkStatus.getCode() != ResultCode.SUCCESS.getCode()){
            log.warn("ChannelCallBackServiceImpl.spuViolation, return [相关参数校验失败], msg : {}", checkStatus.getMsg());
            return checkStatus;
        }
        // 根据租户ID、渠道编码、渠道门店编码获取内部租户ID
        ChannelStoreDO channelStoreDO = copChannelStoreService.selectChannelStore(tenantId, channelTypeEnum.getCode(), spuViolationDto.getPoiInfo().getAppPoiCode());
        long storeId = channelStoreDO.getStoreId();
        // 校验租户门店
        if (storeId == ProjectConstant.UNKNOW_STORE_ID) {
            log.info("ChannelCallBackServiceImpl.spuViolation, return [门店 校验不通过],storeId:{}", storeId);
            return ResultGenerator.genFailResult("门店 校验不通过");
        }

        ChannelSpuMessageCallBackDTO createSpuDTO = new ChannelSpuMessageCallBackDTO();
        createSpuDTO.setCustomSpuId(spuViolationDto.getSpuData().getAppSpuCode());
        createSpuDTO.setStoreId(storeId);
        createSpuDTO.setAppId(channelStoreDO.getIntAppId());
        createSpuDTO.setRuleId(spuViolationDto.getRuleInfo().getRuleId());
        createSpuDTO.setSourceCode(AbnormalSourceType.PUNISH_CALLBACK.getCode());
        ChannelSpuMessageCallBackRequest violationSpuRequest = new ChannelSpuMessageCallBackRequest();
        violationSpuRequest.setTenantId(tenantId);
        violationSpuRequest.setChannelId(channelTypeEnum.getCode());
        violationSpuRequest.setChannelSpuMessageCallBackTypeEnum(ChannelSpuMessageCallBackTypeEnum.VIOLATION);
        violationSpuRequest.setChannelSpuMessageCallBackDTOS(Arrays.asList(createSpuDTO));
        //发送
        sendAbnormalMessage(violationSpuRequest);

        sendCallbackMessageByMq(violationSpuRequest);

        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }

    /**
     * 校验相关参数并解析
     * @param request
     * @return
     */
    private ResultStatus checkRelationParam(SkuViolationNotifyRequest request, SpuViolationDto spuViolationDto){
        if(Objects.isNull(request.getPoiInfo())){
            return ResultGenerator.genFailResult("门店相关信息为空");
        }
        String poiInfoStr = URLDecoder.decode(request.getPoiInfo());
        spuViolationDto.setPoiInfo(JSON.parseObject(poiInfoStr, SpuViolationDto.PoiInfo.class));

        if(Objects.isNull(request.getSpuData())){
            return ResultGenerator.genFailResult("商品信息为空");
        }
        String spuDataStr = URLDecoder.decode(request.getSpuData());
        spuViolationDto.setSpuData(JSON.parseObject(spuDataStr, SpuViolationDto.SpuData.class));

        if(Objects.isNull(request.getRuleInfo())){
            return ResultGenerator.genFailResult("规则相关信息为空");
        }
        String RuleInfoStr = URLDecoder.decode(request.getRuleInfo());
        spuViolationDto.setRuleInfo(JSON.parseObject(RuleInfoStr, SpuViolationDto.RuleInfo.class));

        return ResultGenerator.genSuccessResult();
    }

    private ResultStatus deleteSpuNotify(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, SkuNotifyRequest request) {
        // 删除的商品目前只需要通知做不一致对比
        log.info("ChannelCallBackServiceImpl.deleteSpuNotify, 渠道回调商品消息 request:{}", request);
        if(request.getData() == null){
            return ResultGenerator.genFailResult("data 为空");
        }

        String content = URLDecoder.decode(URLDecoder.decode(request.getData()));
        log.info("ChannelCallBackServiceImpl.deleteSpuNotify, 解码后渠道回调商品消息 content:{}", content);

        // 根据渠道编码和渠道APPID获取租户ID
        long tenantId = getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId());
        if (tenantId == ProjectConstant.UNKNOW_TENANT_ID) {
            log.warn("ChannelCallBackServiceImpl.deleteSpuNotify, return [租户校验不通过], tenantId : {}", tenantId);
            return ResultGenerator.genFailResult("tenant app id 无效");
        }

        JSONArray jsonArray = JSON.parseArray(content);
        jsonArray.forEach(item-> {
            //解析数据
            try {
                SpuUpdateInfo spuUpdateInfo = JSON.parseObject(((JSONObject)item).toJSONString(), SpuUpdateInfo.class);
                if (spuUpdateInfo != null) {
                    // 根据租户ID、渠道编码、渠道门店编码获取内部租户ID
                    ChannelStoreDO channelStoreDO = copChannelStoreService.selectChannelStore(tenantId, channelTypeEnum.getCode(), spuUpdateInfo.getAppPoiCode());
                    long storeId = channelStoreDO.getStoreId();
                    // 校验租户门店
                    if (storeId == ProjectConstant.UNKNOW_STORE_ID) {
                        log.info("ChannelCallBackServiceImpl.deleteSpuNotify, return [门店 校验不通过],tenantId:{} storeId:{}", tenantId, storeId);
                        return;
                    }
                    if (StringUtils.isBlank(spuUpdateInfo.getCustomSpuId())) {
                        log.info("ChannelCallBackServiceImpl.deleteSpuNotify, return [app_food_code为空 校验不通过]");
                        return;
                    }
                    ChannelSpuMessageCallBackDTO createSpuDTO = new ChannelSpuMessageCallBackDTO();
                    createSpuDTO.setCustomSpuId(spuUpdateInfo.getCustomSpuId());
                    createSpuDTO.setStoreId(storeId);
                    createSpuDTO.setAppId(channelStoreDO.getIntAppId());
                    createSpuDTO.setSourceCode(AbnormalSourceType.DELETE_SPU_CHANNEL_CALLBACK.getCode());
                    createSpuDTO.setDeleteReason(spuUpdateInfo.getDeleteReason());
                    ChannelSpuMessageCallBackRequest deleteSpuRequest = new ChannelSpuMessageCallBackRequest();
                    deleteSpuRequest.setTenantId(tenantId);
                    deleteSpuRequest.setChannelId(channelTypeEnum.getCode());
                    deleteSpuRequest.setChannelSpuMessageCallBackTypeEnum(ChannelSpuMessageCallBackTypeEnum.DELETE);
                    deleteSpuRequest.setChannelSpuMessageCallBackDTOS(Arrays.asList(createSpuDTO));
                    log.info("ChannelCallBackServiceImpl.deleteSpuNotify, 调用product-biz商品删除服务 request:{}", deleteSpuRequest);
                    sendCallbackMessage(deleteSpuRequest);
                    SkuCallbackMonitors.monitorDelete((JSONObject)item);
                }
            } catch (Exception e) {
                log.error("parse sku delete info error, object : {}", item, e);
            }

        });
        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }

    private ResultStatus createSpuNotify(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, SkuNotifyRequest request){
        log.info("ChannelCallBackServiceImpl.createSpuNotify, 渠道回调商品消息 request:{}", request);
        if(request.getData() == null){
            return ResultGenerator.genFailResult("data 为空");
        }

        //此回调接口需支持POST请求方式，美团通过此接口向商家系统推送的信息，商家签名验证时需使用解码2次。
        String content = URLDecoder.decode(URLDecoder.decode(request.getData()));
        log.info("ChannelCallBackServiceImpl.createSpuNotify, 解码后渠道回调商品消息 content:{}", content);
        // 历史原因，开放平台推送的数据不是json格式需要处理成json
        JSONArray jsonArray = JSON.parseArray(content);
        List<ChannelSpuMessageCallBackDTO> channelSpuMessageCallBackDTOS = Lists.newArrayList();
        jsonArray.forEach(item-> {
            JSONObject object = (JSONObject) item;
            String appPoiCode = object.getString("app_poi_code");
            String customSpuId = object.getString("app_food_code");
            Integer isCombination = object.getInteger("is_combination");
            if (StringUtils.isBlank(appPoiCode) || StringUtils.isBlank(customSpuId)) {
                // 校验字段
                log.info("ChannelCallBackServiceImpl.createSpuNotify, return [appPoiCode or app_food_code is blank]");
                return;
            }
            // 根据渠道编码和渠道APPID获取租户ID
            long tenantId = getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId());
            // 根据租户ID、渠道编码、渠道门店编码获取内部租户ID
            ChannelStoreDO channelStoreDO = copChannelStoreService.selectChannelStore(tenantId, channelTypeEnum.getCode(), appPoiCode);
            long storeId = channelStoreDO.getStoreId();
            // 校验租户门店
            if (tenantId == ProjectConstant.UNKNOW_TENANT_ID || storeId == ProjectConstant.UNKNOW_STORE_ID) {
                log.info("ChannelCallBackServiceImpl.createSpuNotify, return [租户 门店 校验不通过],tenantId:{} storeId:{}", tenantId, storeId);
                return;
            }
            ChannelSpuMessageCallBackDTO createSpuDTO = new ChannelSpuMessageCallBackDTO();
            createSpuDTO.setCustomSpuId(customSpuId);
            createSpuDTO.setStoreId(storeId);
            createSpuDTO.setAppId(channelStoreDO.getIntAppId());
            createSpuDTO.setIsCombination(isCombination);
            ChannelSpuMessageCallBackRequest createSpuRequest = new ChannelSpuMessageCallBackRequest();
            createSpuRequest.setTenantId(tenantId);
            createSpuRequest.setChannelId(channelTypeEnum.getCode());
            createSpuRequest.setChannelSpuMessageCallBackTypeEnum(ChannelSpuMessageCallBackTypeEnum.CREATE);
            createSpuRequest.setChannelSpuMessageCallBackDTOS(Arrays.asList(createSpuDTO));
            log.info("ChannelCallBackServiceImpl.createSpuNotify, 调用product-biz商品新增服务 request:{}", createSpuRequest);
            sendCallbackMessage(createSpuRequest);
            SkuCallbackMonitors.monitorCreate((JSONObject)item);
        });
        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }

    private ResultStatus updateSpuNotify(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, SkuNotifyRequest request){
        log.info("ChannelCallBackServiceImpl.updateSpuNotify, 渠道回调商品消息 request:{}", request);
        if(request.getData() == null){
            return ResultGenerator.genFailResult("data 为空");
        }
        String content = URLDecoder.decode(URLDecoder.decode(request.getData()));
        log.info("ChannelCallBackServiceImpl.updateSpuNotify, 解码后渠道回调商品消息 content:{}", content);

        // 根据渠道编码和渠道APPID获取租户ID
        long tenantId = getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId());
        if (tenantId == ProjectConstant.UNKNOW_TENANT_ID) {
            log.warn("ChannelCallBackServiceImpl.updateSpuNotify, return [租户校验不通过], tenantId : {}", tenantId);
            return ResultGenerator.genFailResult("tenant app id 无效");
        }

        SpuUpdateNotifyRequest spuUpdateNotifyRequest = new SpuUpdateNotifyRequest();
        spuUpdateNotifyRequest.setTenantId(tenantId);
        spuUpdateNotifyRequest.setChannelId(channelTypeEnum.getCode());
        JSONArray jsonArray = JSON.parseArray(content);
        List<SpuUpdateNotifyDTO> spuUpdateNotifyDTOList = Lists.newArrayList();
        jsonArray.forEach(item-> {
            //解析数据
            try {
                SpuUpdateInfo spuUpdateInfo = JSON.parseObject(((JSONObject)item).toJSONString(), SpuUpdateInfo.class);
                if (spuUpdateInfo != null) {
                    //无sku id丢弃
                    if (StringUtils.isBlank(spuUpdateInfo.getCustomSpuId())){
                        return;
                    }
                    // 根据租户ID、渠道编码、渠道门店编码获取内部租户ID
                    ChannelStoreDO channelStoreDO = copChannelStoreService.selectChannelStore(tenantId, channelTypeEnum.getCode(), spuUpdateInfo.getAppPoiCode());
                    long storeId = channelStoreDO.getStoreId();
                    // 校验租户门店
                    if (storeId == ProjectConstant.UNKNOW_STORE_ID) {
                        log.info("ChannelCallBackServiceImpl.updateSpuNotify, return [门店 校验不通过],tenantId:{} storeId:{}", tenantId, storeId);
                        return;
                    }

                    //目前只处理审核状态变化的情况
                    ChannelSpuMessageCallBackDTO channelSpuMessageCallBackDTO = spuUpdateInfo.toSpuUpdateNotifyDTO(storeId, channelStoreDO.getIntAppId());
                    if (channelSpuMessageCallBackDTO == null) {
                        return;
                    }
                    // erp租户填充扩展字段和属性变更打点
                    changeFiledCatAndFillExt4Erp(spuUpdateInfo, tenantId, channelTypeEnum, channelSpuMessageCallBackDTO);

                    ChannelSpuMessageCallBackRequest updateSpuRequest = new ChannelSpuMessageCallBackRequest();
                    updateSpuRequest.setTenantId(tenantId);
                    updateSpuRequest.setChannelId(channelTypeEnum.getCode());
                    updateSpuRequest.setChannelSpuMessageCallBackTypeEnum(ChannelSpuMessageCallBackTypeEnum.UPDATE);
                    updateSpuRequest.setChannelSpuMessageCallBackDTOS(Arrays.asList(channelSpuMessageCallBackDTO));
                    log.info("ChannelCallBackServiceImpl.updateSpuNotify, 调用product-biz商品更新服务 request:{}", updateSpuRequest);
                    sendCallbackMessage(updateSpuRequest);
                    SkuCallbackMonitors.monitorUpdate((JSONObject)item);
                }
            } catch (Exception e) {
                log.error("parse sku update info error, object : {}", item, e);
            }

        });
        return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
    }

    /**
     * erp租户填充扩展字段和属性变更打点
     *
     * @param spuUpdateInfo
     * @param tenantId
     * @param channelTypeEnum
     */
    private void changeFiledCatAndFillExt4Erp(SpuUpdateInfo spuUpdateInfo, long tenantId, ChannelTypeEnum channelTypeEnum, ChannelSpuMessageCallBackDTO channelSpuMessageCallBackDTO) {
        try {
            if (tenantService.getTenantTypeEnum(tenantId) != TenantTypeEnum.ERP) {
                return;
            }
            if (spuUpdateInfo.getDiffContents() == null) {
                return;
            }
            List<DiffSkus> diffSkusList = spuUpdateInfo.getDiffContents().getDiffSkusList();
            if (CollectionUtils.isEmpty(diffSkusList)) {
                return;
            }
            // 填充扩展字段
            ProductCallbackSpuUpdateInfoExt spuUpdateInfoExt = spuUpdateInfo.getExt4Erp();
            if (spuUpdateInfoExt != null) {
                channelSpuMessageCallBackDTO.setExt(JacksonUtils.toJson(spuUpdateInfoExt));
            }
            // 店内码变更打点
            for (DiffSkus diffSku : diffSkusList) {
                DiffSkuContents skuDiffContents = diffSku.getDiffContents();
                if (skuDiffContents == null) {
                    continue;
                }
                //新增或删除规格或更新店内码打点
                if (skuDiffContents.getSkuId() != null && ("null".equals(skuDiffContents.getSkuId().getResult(String.class)) || "null".equals(
                        skuDiffContents.getSkuId().getOrigin(String.class)) || skuDiffContents.getSkuId().isChanged(String.class))) {
                    MetricHelper.build().name(MetricConstant.PRODUCT_CALLBACK_CHANGE_FIELD).tag("channel", channelTypeEnum.getAbbrev())
                            .tag("type", ProductFieldsEnum.SPEC.getFieldDesc())
                            .tag("tenantId", String.valueOf(tenantId))
                            .count();
                    log.error("商品更新回调-商品店内码可能发生变化，请重点关注 spuUpdateInfo:{} tenantId:{} channelType:{}", spuUpdateInfo,
                            tenantId, channelTypeEnum.getAbbrev());
                }
            }
        } catch (Exception e) {
            log.warn("商品更新回调 changeFiledCat error spuUpdateInfo:{}", spuUpdateInfo, e);
        }
    }
    /**
     * 发送商家端商品修改回调消息
     *
     * @param request
     */
    private void sendCallbackMessage(ChannelSpuMessageCallBackRequest request) {
        if (CollectionUtils.isEmpty(request.getChannelSpuMessageCallBackDTOS())) {
            log.warn("变更SPU列表为空 request:{}", request);
            return;
        }

        MetricHelper.build().name(ERP_CHANNEL_CALL_BACK)
                .tag(TYPE, request.getChannelSpuMessageCallBackTypeEnum() == null ?
                        "null" : String.valueOf(request.getChannelSpuMessageCallBackTypeEnum()))
                .tag(CHANNEL, String.valueOf(request.getChannelId())).count();
        sendAbnormalMessage(request);

        boolean enable = MccConfigUtil.enableSyncSpuWithMq();
        if (enable) {
            sendCallbackMessageByMq(request);
        } else {
            sendCallbackMessageByThrift(request);
        }
    }

    private void sendAbnormalMessage(ChannelSpuMessageCallBackRequest request) {
        // 异常商品不处理商品新增消息
        if (ChannelSpuMessageCallBackTypeEnum.CREATE.equals(request.getChannelSpuMessageCallBackTypeEnum())){
            return;
        }
        log.info("商家端商品变更使用MQ方式同步");
        ErpChannelSpuCallbackMessage erpChannelSpuCallbackMessage =  new ErpChannelSpuCallbackMessage();
        SpuChangeCallbackMessage message = new SpuChangeCallbackMessage();
        message.setTenantId(request.getTenantId());
        message.setChannelId(request.getChannelId());
        message.setSyncType(request.getChannelSpuMessageCallBackTypeEnum());
        try {
            request.getChannelSpuMessageCallBackDTOS()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(SpuChangeCallbackMessage.SpuUpdateInnerMessage::new)
                    .collect(Collectors.groupingBy(SpuChangeCallbackMessage.SpuUpdateInnerMessage::getStoreId))
                    .forEach((storeId, innerMessages) -> {
                        message.setInnerMessageList(innerMessages);
                        erpChannelSpuCallbackMessage.setTenantId(message.getTenantId());
                        erpChannelSpuCallbackMessage.setChannelId(message.getChannelId());
                        erpChannelSpuCallbackMessage.setMtSpuChangeCallbackMessage(JacksonUtils.toJson(message));
                        Long partKey = Objects.nonNull(storeId) && storeId > 0 ? storeId : message.getTenantId();
                        spuChangeCallbackProducerWrapper.sendMessage(erpChannelSpuCallbackMessage, partKey);
                    });
        }
        catch (Exception e) {
            log.error("异常商品消息发送失败, request:{}", request, e);
        }
    }

    /**
     * @deprecated
     * Thrift接口方式通知商品同步
     * @param request
     */
    private void sendCallbackMessageByThrift(ChannelSpuMessageCallBackRequest request) {
        log.info("商家端商品变更使用thrift方式同步");
        CommonResponse response = channelCallbackThriftService.onChannelSpuMessageCallBack(request);
        log.info("商品同步结果 response:{}", response);
    }

    /**
     * MQ消息方式通知商品同步，租户-门店维度发送
     *
     * @param request
     */
    private void sendCallbackMessageByMq(ChannelSpuMessageCallBackRequest request) {
        log.info("商家端商品变更使用MQ方式同步");

        SpuChangeCallbackMessage message = new SpuChangeCallbackMessage();
        message.setTenantId(request.getTenantId());
        message.setChannelId(request.getChannelId());
        message.setSyncType(request.getChannelSpuMessageCallBackTypeEnum());

        request.getChannelSpuMessageCallBackDTOS()
                .stream()
                .filter(Objects::nonNull)
                .map(SpuChangeCallbackMessage.SpuUpdateInnerMessage::new)
                .collect(Collectors.groupingBy(SpuChangeCallbackMessage.SpuUpdateInnerMessage::getStoreId))
                .forEach((storeId, innerMessages) -> {
                    message.setInnerMessageList(innerMessages);

                    String partKey = String.format("%d_%d_%d", message.getTenantId(), storeId, message.getChannelId());
                    callbackProducerWrapper.sendMessage(message, partKey);
                });
    }

    private GetSkuInfoResponse getSkuInfoDetail(long tenantId, int channelId, long storeId, String customSkuId) {
        try {
            return RetryUtils.executeWithCostomRetryStrategy(4, () -> {
                BaseRequestSimple baseRequestSimple = new BaseRequestSimple().setTenantId(tenantId).setChannelId(channelId);
                GetSkuInfoRequest request = new GetSkuInfoRequest().setBaseInfo(baseRequestSimple).setStoreId(storeId).setCustomSkuId(customSkuId);
                log.info("ChannelCallBackServiceImpl.getSkuInfoDetail, 获取单个商品详情参数 request:{}", request);
                GetSkuInfoResponse response = routeServiceFactory.selectChannelSkuService(request.getBaseInfo().getChannelId(), tenantId).getSkuInfo(request);
                log.info("ChannelCallBackServiceImpl.getSkuInfoDetail, 获取单个商品详情返回值 response:{}", response);
                return response;
            }, 400, a -> a.retryIfResult(input -> Objects.isNull(input) || input.getStatus().getCode() != ResultCode.SUCCESS.getCode() || Objects.isNull(input.getSkuInfo())).retryIfException());
        } catch (ExecutionException e) {
            log.info("ChannelCallBackServiceImpl.getSkuInfoDetail,重试异常:{}", e.getMessage(), e);
            return null;
        } catch (RetryException e) {
            log.info("ChannelCallBackServiceImpl.getSkuInfoDetail,达到最大重试次数4次",e);
            return null;
        }
    }

    /**
     * 查询数据库获取租户ID
     */
    private Long getTenantIdParam(int channelId, String tenantAppId) {
        Long tenantId = copAccessConfigService.selectTenantId(channelId, tenantAppId);
        if (Objects.isNull(tenantId)) {
            log.warn("ChannelOrderCallbackServiceImpl.getTenantIdParam, 未获取到渠道租户ID, channelId:{}, tenantAppId:{}",
                    channelId, tenantAppId);
            return ProjectConstant.UNKNOW_TENANT_ID;
        }
        return tenantId;
    }

    private CopAccessConfigDO getCopAccessConfigDOParam(int channelId, String tenantAppId) {
        CopAccessConfigDO copAccessConfigDO = copAccessConfigService.selectByTenantAppIdAndChannelId(tenantAppId, channelId);
        if (Objects.isNull(copAccessConfigDO)) {
            log.warn("ChannelOrderCallbackServiceImpl.getTenantIdParam, 未获取到渠道租户配置, channelId:{}, tenantAppId:{}",
                    channelId, tenantAppId);
            return null;
        }
        return copAccessConfigDO;
    }

    /**
     * 转换成ocms的skunotify请求对象
     * @param tenantId
     * @param channelId
     * @param storeId
     * @param operateType  操作类型  SkuNotifyType.ADD:新增
     * @param skuInfoDTO
     * @return
     */
    private SkuNotifySkuInfoDTO convert2OcmsSkuNotifyDto(Long tenantId, int channelId, Long storeId,SkuNotifyType operateType,SkuInfoDTO skuInfoDTO){
        SkuNotifySkuInfoDTO sku = new SkuNotifySkuInfoDTO();
        sku.setTenantId(tenantId);
        sku.setChannelId(channelId);
        sku.setStoreId(storeId);
        sku.setOperateType(operateType);

        sku.setSkuId(skuInfoDTO.getSkuId());
        sku.setName(skuInfoDTO.getName());
        sku.setFrontCategory(skuInfoDTO.getFrontCategory());
        sku.setFrontCategoryName(skuInfoDTO.getFrontCategoryName());
        sku.setCategory(skuInfoDTO.getCategory());
        sku.setBrand(skuInfoDTO.getBrand());
        sku.setMinPurchaseQuantity(skuInfoDTO.getMinPurchaseQuantity());
        sku.setSpec(skuInfoDTO.getSpec());
        sku.setWeight(skuInfoDTO.getWeight());
        sku.setBoxPrice(skuInfoDTO.getBoxPrice());
        sku.setBoxQuantity(skuInfoDTO.getBoxQuantity());
        sku.setSkuStatus(skuInfoDTO.getSkuStatus());
        sku.setPrice(skuInfoDTO.getPrice());
        sku.setStock(skuInfoDTO.getStock());
        sku.setUpc(skuInfoDTO.getUpc());
        sku.setChannelFrontCategory(skuInfoDTO.getChannelFrontCategory());
        sku.setChannelFrontCategoryName(skuInfoDTO.getChannelFrontCategoryName());
        sku.setUnit(skuInfoDTO.getUnit());
        sku.setSourceType(skuInfoDTO.getSourceType());
        sku.setCategoryFirst(skuInfoDTO.getCategoryFirst());
        sku.setCategorySecond(skuInfoDTO.getCategorySecond());
        sku.setBrandName(skuInfoDTO.getBrandName());
        sku.setProductionArea(skuInfoDTO.getProductionArea());
        sku.setSequence(skuInfoDTO.getSequence());
        sku.setIsSp(skuInfoDTO.getIsSp());
        sku.setCustomSkuId(skuInfoDTO.getCustomSkuId());
        sku.setChannelSkuId(skuInfoDTO.getChannelSkuId());
        sku.setAvailableTimes(skuInfoDTO.getAvailableTimes());
        sku.setDescription(skuInfoDTO.getDescription());
        sku.setProperties(skuInfoDTO.getProperties());
        sku.setIsSpecialty(skuInfoDTO.getIsSpecialty());

        List<String> pictures = new ArrayList<>();
        sku.setPictures(pictures);
        for(String picture:skuInfoDTO.getPictures()){
            pictures.add(picture);
        }

        List<SkuNotifyCategoryDTO> categoryList = new ArrayList<>();
        sku.setCategoryList(categoryList);
        for(ChannelStoreCategory channelStoreCategory:skuInfoDTO.getCategoryList()){
            SkuNotifyCategoryDTO category = new SkuNotifyCategoryDTO();
            category.setFirstCategoryCode(channelStoreCategory.getFirstCategoryCode());
            category.setFirstCategoryName(channelStoreCategory.getFirstCategoryName());
            category.setSecondaryCategoryCode(channelStoreCategory.getSecondaryCategoryCode());
            category.setSecondaryCategoryName(channelStoreCategory.getSecondaryCategoryName());
            categoryList.add(category);
        }

        return sku;
    }
}
