package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.tenant.highlevelclient.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelSkuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelSkuInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelSkuPropUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelStoreCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ElmChannelBaseResponseDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.UpdateCustomSkuId;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SkuStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSkuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuInfoByOffsetRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuInfoByOffsetResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChangeCustomSkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelStoreCategory;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSkuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuIdDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuSellStatusInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuSellStatusInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSkuIdDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode.TRIGGER_LIMIT;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum.ELEM;
import static com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum.RATE_LIMIT;

/**
 * @description: 饿了么渠道商品内部服务接口
 * @author: chenhaie
 * @create: 2019/1/7 下午5:27
 */
@Service("elmChannelSkuService")
public class ElmChannelSkuServiceImpl implements ChannelSkuService {

    @Value("${elm.url.base}")
    private String elmUrlBase;

    @Value("${elm.url.skulist}")
    private String skuList;

    @Value("${elm.url.frontCatList}")
    private String frontCatList;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private ElmConverterService elmConverterService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CommonLogger log;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Override
    public ResultData skuCreate(SkuInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        request.getParamList().forEach(data -> {
            try {
                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SKU_CREATE, request.getBaseInfo(), elmConverterService.skuCreateMapping(data));

                // 组装返回结果
                ResultDataUtils.combineResultData(resultData, postResult, data.getSkuId());

                // 获取渠道商品编码
                parseChannelSkuId(resultData);

            } catch (IllegalArgumentException e) {
                log.error("ElmChannelSkuServiceImpl.skuCreate 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getSkuId());

            } catch (Exception e) {
                log.error("ElmChannelSkuServiceImpl.skuCreate 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getSkuId());
            }
        });

        return resultData;
    }

    private void parseChannelSkuId(ResultData resultData) {
        List<ResultSuccessSku> sucData = resultData.getSucData();
        sucData.forEach(resultSuccessSku -> {
            if (StringUtils.isNotBlank(resultSuccessSku.getChannelResultInfo())) {
                resultSuccessSku.setChannelSkuId(resultSuccessSku.getChannelResultInfo());
            }
        });
    }


    @Override
    public ResultData upcCreate(SkuInfoRequest request) {
        return skuCreate(request);
    }

    @Override
    public ResultData updateSku(SkuInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        request.getParamList().forEach(data -> {
            Map<Long, ChannelResponseDTO> postResult;
            try {
                // 更新操作不设置价格
                data.setPriceIsSet(false);
                // 更新操作不设置库存
                if (MccConfigUtil.elmUpdateSkuWithOutStockSwitch()) {
                    data.setStockIsSet(false);
                }

                // 调用渠道接口
                postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SKU_UPDATE, request.getBaseInfo(), elmConverterService.skuCreateMapping(data));

                // 组装返回结果
                ResultDataUtils.combineResultData(resultData, postResult, data.getSkuId());

            } catch (IllegalArgumentException e) {
                log.error("ElmChannelSkuServiceImpl.updateSku 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getSkuId());

            } catch (Exception e) {
                log.error("ElmChannelSkuServiceImpl.updateSku 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getSkuId());
            }
        });
        return resultData;
    }

    @Override
    public ResultData deleteSku(SkuInfoDeleteRequest request) {
        try {
            BaseRequest baseInfo = request.getBaseInfo();
            ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
            baseInfo.getStoreIdList().forEach(storeId -> {
                BaseRequest baseRequest = new BaseRequest()
                        .setTenantId(request.getBaseInfo().getTenantId())
                        .setChannelId(request.getBaseInfo().getChannelId())
                        .setStoreIdList(Lists.newArrayList(storeId));
                List<String> skuList = request.getParamList().stream().map(SkuInfoDeleteDTO::getSkuId)
                        .collect(Collectors.toList());
                List<List<String>> subList = ListUtils.listPartition(skuList, MccConfigUtil.getElmSkuDeleteBatch());
                subList.forEach(data -> {
                    ChannelSkuDeleteDTO channelSkuDeleteDTO = new ChannelSkuDeleteDTO();
                    channelSkuDeleteDTO.setCustom_sku_id(StringUtils.join(data, ","));
                    Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SKU_DELETE, baseRequest, channelSkuDeleteDTO);
                    log.info("ElmChannelSkuServiceImpl.deleteSkuExecutor, ELM删除商品接口返回数据 channelId:{}, storeId:{}, s:{}, result:{}",
                            baseInfo.getChannelId(), storeId, data, postResult);
                    // 组装返回结果，是否使用新的解析方法
                    if (MccConfigUtil.getElmParseChannelResponseUpgradeSwitch()) {
                        ProductResultDataUtils.elmCombineResultDataList(postResult, resultData, skuList, ElmChannelBaseResponseDetail.class, true, false, Collections.emptyMap());
                    } else {
                        ResultBuilderUtil.buildSkuDelResult(baseInfo.getChannelId(), storeId, data, postResult, null, resultData);
                    }
                    try {
                        Thread.sleep(200);
                    } catch (Exception e) {
                        log.error("ElmChannelSkuServiceImpl.deleteSku, request:{}", request, e);
                    }
                });
            });
            return resultData;
        } catch (Exception e) {
            log.error("ElmChannelSkuServiceImpl.deleteSku, request:{}", request, e);
        }
        return ResultGenerator.genResultData(ResultCode.FAIL, "ELM批量删除商品失败");
    }

    public ResultData deleteSkuByChannelSkuId(SkuInfoDeleteRequest request) {
        try {
            BaseRequest baseInfo = request.getBaseInfo();
            ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
            baseInfo.getStoreIdList().forEach(storeId -> {
                BaseRequest baseRequest = new BaseRequest()
                        .setTenantId(request.getBaseInfo().getTenantId())
                        .setChannelId(request.getBaseInfo().getChannelId())
                        .setStoreIdList(Lists.newArrayList(storeId));
                List<String> skuList = request.getParamList().stream().map(SkuInfoDeleteDTO::getSkuId)
                        .collect(Collectors.toList());
                List<List<String>> subList = ListUtils.listPartition(skuList, MccConfigUtil.getElmSkuDeleteBatch());
                subList.forEach(skuIds -> {
                    ChannelSkuDeleteDTO channelSkuDeleteDTO = new ChannelSkuDeleteDTO();
                    channelSkuDeleteDTO.setSku_id(StringUtils.join(skuIds, ","));
                    Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SKU_DELETE, baseRequest, channelSkuDeleteDTO);
                    log.info("ElmChannelSkuServiceImpl.deleteSkuByChannelSkuId, ELM删除商品接口返回数据 channelId:{}, storeId:{}, skuIds:{}, result:{}",
                            baseInfo.getChannelId(), storeId, skuIds, postResult);
                    ResultBuilderUtil.buildSkuDelResult(baseInfo.getChannelId(), storeId, skuIds, postResult, null, resultData);
                    try {
                        Thread.sleep(200);
                    } catch (Exception e) {
                        log.error("ElmChannelSkuServiceImpl.deleteSkuByChannelSkuId, request:{}", request, e);
                    }
                });
            });
            return resultData;
        } catch (Exception e) {
            log.error("ElmChannelSkuServiceImpl.deleteSkuByChannelSkuId, request:{}", request, e);
        }
        return ResultGenerator.genResultData(ResultCode.FAIL, "ELM根据channelSkuId批量删除商品失败");
    }

    @Override
    public ResultData pictureUpload(PictureUploadRequest request) {
        ResultData resultData = ResultDataUtils.newSynchronizedResultData(ChannelResultStatusEnum.SUCCESS);

        List<CopAccessConfigDO> copAccessConfigDOS = copAccessConfigService.findTenantChannelConfigApp(request.getBaseInfo().getTenantId(),
                request.getBaseInfo().getChannelId());
        if (CollectionUtils.isEmpty(copAccessConfigDOS)) {
            return ResultDataUtils.newSynchronizedResultData(ChannelResultStatusEnum.UNDEAL_ERROR);
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(request.getBaseInfo().getStoreIdList())
                .setAppId(copAccessConfigDOS.get(0).getAppId());

        request.getParamList().parallelStream().forEach(data -> {
            try {
                // 调用渠道接口
                ChannelResponseDTO postResult = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.PICTURE_UPLOAD, baseRequest, elmConverterService.pictureUploadMapping(data));

                // 组装返回结果
                ResultDataUtils.combineResultData(resultData, postResult, data.getUid());

            } catch (IllegalArgumentException e) {
                log.error("ElmChannelSkuServiceImpl.pictureUpload 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getUid());

            } catch (Exception e) {
                log.error("ElmChannelSkuServiceImpl.pictureUpload 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getUid());
            }
        });

        return resultData;
    }

    @Override
    public PictureUploadResult getPictureUploadStatus(PictureUploadStatusRequest request) {
        return null;
    }

    @Override
    public ResultData updateSkuSellStatus(SkuSellStatusInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        request.getParamList().forEach(data -> {
            List<String> skuIds;
            // 区分使用商家自定义商品ID还是渠道商品ID更新
            if (request.isOptByChannelSpuId()) {
                skuIds = data.getSkuId().stream().map(SkuIdDTO::getSkuId).collect(Collectors.toList());
            } else {
                skuIds = data.getSkuId().stream().map(SkuIdDTO::getCustomSkuId).collect(Collectors.toList());
            }

            try {
                BaseRequest baseRequest = new BaseRequest()
                        .setTenantId(request.getBaseInfo().getTenantId())
                        .setChannelId(request.getBaseInfo().getChannelId())
                        .setStoreIdList(Lists.newArrayList(data.getStoreId()));

                // 调用渠道接口 上架/下架
                ChannelPostELMEnum postUrlEnum = data.getSkuStatus() == SkuStatusEnum.OFF_LINE.getCode() ? ChannelPostELMEnum.SKU_OFFLINE : ChannelPostELMEnum.SKU_ONLINE;
                ChannelSkuPropUpdateDTO channelSkuPropUpdateDTO;
                // 区分使用商家自定义商品ID还是渠道商品ID更新
                if (request.isOptByChannelSpuId()) {
                    channelSkuPropUpdateDTO = elmConverterService.updateSellStatusByChannelSkuId(data);
                } else {
                    channelSkuPropUpdateDTO = elmConverterService.updateSkuSellStatus(data);
                }
                Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(postUrlEnum, baseRequest, channelSkuPropUpdateDTO);

                // 组装返回结果，是否使用新的解析方法
                if (MccConfigUtil.getElmParseChannelResponseUpgradeSwitch()) {
                    ProductResultDataUtils.elmCombineResultDataList(postResult, resultData, skuIds, ElmChannelBaseResponseDetail.class, false, request.isOptByChannelSpuId(), Collections.emptyMap());
                } else {
                    ResultDataUtils.elmCombineResultDataList(postResult, resultData, skuIds, ElmChannelBaseResponseDetail.class);
                }
            } catch (IllegalArgumentException e) {
                log.error("ElmChannelSkuServiceImpl.updateSkuSellStatus 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds);
            } catch (Exception e) {
                log.error("ElmChannelSkuServiceImpl.updateSkuSellStatus 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds);
            }
        });
        return resultData;
    }


    /**
     * 修改sku售卖状态（基于渠道商品编码）
     *
     * @param request
     * @return
     */
    public ResultData updateSkuSellStatusByChannelSkuId(SkuSellStatusInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }

        for (SkuSellStatusInfoDTO SkuSellStatusInfoDTO : request.getParamList()) {
            List<String> channelSkuIds = SkuSellStatusInfoDTO.getSkuId().stream().map(SkuIdDTO::getSkuId).collect(Collectors.toList());
            try {
                BaseRequest baseRequest = new BaseRequest()
                        .setTenantId(request.getBaseInfo().getTenantId())
                        .setChannelId(request.getBaseInfo().getChannelId())
                        .setStoreIdList(Lists.newArrayList(SkuSellStatusInfoDTO.getStoreId()));

                // 调用渠道接口进行上架/下架
                ChannelPostELMEnum postUrlEnum = SkuSellStatusInfoDTO.getSkuStatus() == SkuStatusEnum.OFF_LINE.getCode() ? ChannelPostELMEnum.SKU_OFFLINE : ChannelPostELMEnum.SKU_ONLINE;
                Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(postUrlEnum, baseRequest, elmConverterService.updateSkuSellStatusByChannelSkuId(SkuSellStatusInfoDTO));

                // 组装返回结果（注意原有的其它方法都是基于商家商品编码解析，所以新提了个方法支持渠道商品的解析，下面的catch处理同理）
                ResultDataUtils.elmCombineResultDataListByChannelSkuId(postResult, resultData, channelSkuIds, ElmChannelBaseResponseDetail.class);
            }
            catch (IllegalArgumentException e) {
                log.error("ElmChannelSkuServiceImpl.updateSkuSellStatusByChannelSkuId 参数校验失败, data:{}", SkuSellStatusInfoDTO, e);
                ResultDataUtils.combineExceptionDataListByChannelSpuId(resultData, e.getMessage(), channelSkuIds);
            }
            catch (Exception e) {
                log.error("ElmChannelSkuServiceImpl.updateSkuSellStatusByChannelSkuId 服务异常, data:{}", SkuSellStatusInfoDTO, e);
                ResultDataUtils.combineExceptionDataListByChannelSpuId(resultData, ChannelResultStatusEnum.UNDEAL_ERROR.getDesc(), channelSkuIds);
            }
        }
        return resultData;
    }

    @Override
    public ResultData updateCustomSkuId(UpdateCustomSkuIdRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        request.getParamList().forEach(data -> {
            try {
                BaseRequest baseRequest = new BaseRequest()
                        .setTenantId(request.getBaseInfo().getTenantId())
                        .setChannelId(request.getBaseInfo().getChannelId())
                        .setStoreIdList(Lists.newArrayList(data.getStoreId()));
                UpdateCustomSkuId updateCustomSkuId = elmConverterService.updateCustomSkuIdMapping(data);

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.UPDATE_CUSTOM_SKU_ID, baseRequest, updateCustomSkuId);

                // 组装返回结果
                ResultDataUtils.combineResultData(resultData, postResult, data.getCustomSkuId());

            } catch (IllegalArgumentException e) {
                log.error("ElmChannelSkuServiceImpl.updateCustomSkuId 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getCustomSkuId());

            } catch (Exception e) {
                log.error("ElmChannelSkuServiceImpl.updateCustomSkuId 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getCustomSkuId());
            }
        });
        return resultData;
    }


    @Override
    public GetSkuInfoResponse getSkuInfo(GetSkuInfoRequest request) {
        GetSkuInfoResponse response = new GetSkuInfoResponse().setStatus(ResultGenerator.genSuccessResult());

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                request.getStoreId());
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSkuInfo(null);
        }
        // 构造请求参数
        Map<String, String> bizParam = Maps.newHashMap();
        String shopId = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        bizParam.put("shop_id", shopId);
        if (StringUtils.isNotBlank(request.getChannelSkuId())) {
            bizParam.put("sku_id", request.getChannelSkuId());
        }
        if (StringUtils.isNotBlank(request.getCustomSkuId())) {
            bizParam.put("custom_sku_id", request.getCustomSkuId());
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));

        Map<String, Object> skuInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, skuList, baseRequest, bizParam);
        JSONObject skuInfoMapBody = (JSONObject) skuInfoMap.get(ProjectConstant.BODY);
        if ((int) skuInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }

        List<SkuInfoDTO> skuInfoDTOS = Lists.newArrayList();
        JSONObject skuInfoMapData = skuInfoMapBody.getJSONObject(ProjectConstant.DATA);

        if (skuInfoMapData == null){
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSkuInfo(null);
        }
        // 组装数据
        List<ChannelSkuInfo> channelSkuInfos = JSON.parseArray(skuInfoMapData.getString("list"), ChannelSkuInfo.class);

        // 异常情况处理
        if (CollectionUtils.isEmpty(channelSkuInfos)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSkuInfo(null);
        }

        skuInfoDTOS.addAll(elmConverterService.channelSkuInfoListMapping(channelSkuInfos));
        //查询商品多分类列表字段赋值，保证对外提供的多分类查询字段有值有值
        //elm渠道统一放到一级分类里边。
        skuInfoDTOS.forEach(item -> {
            List<ChannelStoreCategory> channelStoreCategoryList = Lists.newArrayList();
            item.setCategoryList(channelStoreCategoryList);
            ChannelStoreCategory channelStoreCategory = new ChannelStoreCategory();
            channelStoreCategory.setFirstCategoryCode(item.getChannelFrontCategory());
            channelStoreCategory.setFirstCategoryName(item.getChannelFrontCategoryName());
            channelStoreCategoryList.add(channelStoreCategory);
        });

        return response.setStatus(ResultGenerator.genSuccessResult()).setSkuInfo(skuInfoDTOS.get(0));
    }

    @Override
    // https://open-be.ele.me/dev/api/doc/v3/#api-Sku-sku_list
    public BatchGetSkuInfoResponse batchGetSkuInfo(BatchGetSkuInfoRequest request) {
        BatchGetSkuInfoResponse response = new BatchGetSkuInfoResponse().setStatus(ResultGenerator.genSuccessResult());

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                request.getStoreId());
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSkuInfos(Collections.emptyList());
        }
        // 构造请求参数
        Map<String, String> bizParam = Maps.newHashMap();
        String shopId = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        bizParam.put("page", String.valueOf(request.getPageNum()));
        bizParam.put("pagesize", String.valueOf(request.getPageSize()));
        bizParam.put("shop_id", shopId);
        bizParam.put("include_cate_info", Constant.ONE_STR);

        Map<String, Object> sysParam = elmChannelGateService.getSysParam(new BaseRequest()
                .setChannelId(request.getBaseInfo().getChannelId())
                .setTenantId(request.getBaseInfo().getTenantId())
                .setStoreIdList(com.google.common.collect.Lists.newArrayList(request.getStoreId())));
        String appId = String.valueOf(sysParam.get(ProjectConstant.ELM_SOURCE));

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostELMEnum.BATCH_GET_SKUINFO, appId);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY, ProjectConstant.TRIGGER_LIMIT_MSG)).setSkuInfos(Collections.emptyList());
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));

        Map<String, Object> skuInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, skuList, baseRequest, bizParam);
        JSONObject skuInfoMapBody = (JSONObject) skuInfoMap.get(ProjectConstant.BODY);
        if ((int) skuInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }

        List<SkuInfoDTO> skuInfoDTOS = Lists.newArrayList();
        JSONObject skuInfoMapData = skuInfoMapBody.getJSONObject(ProjectConstant.DATA);
        // 组装数据
        List<ChannelSkuInfo> channelSkuInfos = JSON.parseArray(skuInfoMapData.getString("list"), ChannelSkuInfo.class);
        if (CollectionUtils.isEmpty(channelSkuInfos)) {
            return response.setSkuInfos(Collections.emptyList());
        }
        // 分页数据构造
        String total = skuInfoMapData.getString("total"); // 商品总数
        String page = skuInfoMapData.getString("page"); // 页码
        String pages = skuInfoMapData.getString("pages"); // 总页数

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(Integer.parseInt(page));
        pageInfo.setTotalPage(Integer.parseInt(pages));
        pageInfo.setPageSize(request.getPageSize());
        pageInfo.setTotalNum(Integer.parseInt(total));

        skuInfoDTOS.addAll(elmConverterService.channelSkuInfoListMapping(channelSkuInfos));
        //查询商品多分类列表字段赋值，保证对外提供的多分类查询字段有值有值
        //elm渠道统一放到一级分类里边。
        if (CollectionUtils.isNotEmpty(skuInfoDTOS)) {
            skuInfoDTOS.forEach(item -> {
                List<ChannelStoreCategory> channelStoreCategoryList = Lists.newArrayList();
                item.setCategoryList(channelStoreCategoryList);
                ChannelStoreCategory channelStoreCategory = new ChannelStoreCategory();
                channelStoreCategory.setFirstCategoryCode(item.getChannelFrontCategory());
                channelStoreCategory.setFirstCategoryName(item.getChannelFrontCategoryName());
                channelStoreCategoryList.add(channelStoreCategory);
            });
        }
        return response.setStatus(ResultGenerator.genSuccessResult()).setSkuInfos(skuInfoDTOS).setPageInfo(pageInfo);
    }


    //通过游标的方式批量获取sku，用于全量查询门店商品的场景
    public BatchGetSkuInfoByOffsetResponse batchGetSkuInfoByOffset(BatchGetSkuInfoByOffsetRequest request) {
        BatchGetSkuInfoByOffsetResponse response = new BatchGetSkuInfoByOffsetResponse().setStatus(ResultGenerator.genSuccessResult());

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                request.getStoreId());
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSkuInfos(Collections.emptyList());
        }
        // 构造请求参数
        Map<String, String> bizParam = Maps.newHashMap();
        String shopId = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        //根据饿了么要求，page始终为1
        bizParam.put("page", String.valueOf(1));
        bizParam.put("shop_id", shopId);
        bizParam.put("include_cate_info", Constant.ONE_STR);
        bizParam.put("sku_id_offset", String.valueOf(request.getSkuIdOffset()));

        Map<String, Object> sysParam = elmChannelGateService.getSysParam(new BaseRequest()
                .setChannelId(request.getBaseInfo().getChannelId())
                .setTenantId(request.getBaseInfo().getTenantId())
                .setStoreIdList(com.google.common.collect.Lists.newArrayList(request.getStoreId())));
        String appId = String.valueOf(sysParam.get(ProjectConstant.ELM_SOURCE));

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostELMEnum.BATCH_GET_SKUINFO, appId);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY, ProjectConstant.TRIGGER_LIMIT_MSG)).setSkuInfos(Collections.emptyList());
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));

        Map<String, Object> skuInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, skuList, baseRequest, bizParam);
        JSONObject skuInfoMapBody = (JSONObject) skuInfoMap.get(ProjectConstant.BODY);
        if ((int) skuInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, skuInfoMapBody.getString(ProjectConstant.ERROR)));
        }

        List<SkuInfoDTO> skuInfoDTOS = Lists.newArrayList();
        JSONObject skuInfoMapData = skuInfoMapBody.getJSONObject(ProjectConstant.DATA);
        // 组装数据
        List<ChannelSkuInfo> channelSkuInfos = JSON.parseArray(skuInfoMapData.getString("list"), ChannelSkuInfo.class);
        if (CollectionUtils.isEmpty(channelSkuInfos)) {
            return response.setSkuInfos(Collections.emptyList());
        }

        int skuIdOffset = Integer.parseInt(skuInfoMapData.getString("sku_id_offset"));
        skuInfoDTOS.addAll(elmConverterService.channelSkuInfoListMapping(channelSkuInfos));
        //查询商品多分类列表字段赋值，保证对外提供的多分类查询字段有值有值
        //elm渠道统一放到一级分类里边。
        if (CollectionUtils.isNotEmpty(skuInfoDTOS)) {
            skuInfoDTOS.forEach(item -> {
                List<ChannelStoreCategory> channelStoreCategoryList = Lists.newArrayList();
                item.setCategoryList(channelStoreCategoryList);
                ChannelStoreCategory channelStoreCategory = new ChannelStoreCategory();
                channelStoreCategory.setFirstCategoryCode(item.getChannelFrontCategory());
                channelStoreCategory.setFirstCategoryName(item.getChannelFrontCategoryName());
                channelStoreCategoryList.add(channelStoreCategory);
            });
        }
        return response.setStatus(ResultGenerator.genSuccessResult()).setSkuInfos(skuInfoDTOS).setSkuIdOffset(skuIdOffset);
    }


    @Override
    public GetCategoryResponse batchGetChannelStoreCategoryInfo(CatRequest request) {
        GetCategoryResponse response = new GetCategoryResponse().setStatus(ResultGenerator.genSuccessResult());
        if (request == null || request.getBaseInfo() == null)
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "拉取饿了么前台分类失败"));
        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                request.getStoreId());
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(),
                        Collections.singletonList(request.getStoreId()));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setCatInfoList(Collections.emptyList());
        }
        // 构造请求参数
        Map<String, String> bizParam = Maps.newHashMap();
        String shopId = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        bizParam.put("shop_id", shopId);

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));
        // 限流
        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.BATCH_GET_CATEGORYINFO, String.valueOf(request.getBaseInfo().getTenantId()))) {
            log.info("ElmChannelSkuServiceImpl.batchGetChannelStoreCategoryInfo 被限流");
            return response.setStatus(ResultGenerator.genResult(ResultCode.TRIGGER_LIMIT));
        }

        Map<String, Object> skuInfoMap = elmChannelGateService.sendPostApp(elmUrlBase, frontCatList, baseRequest, bizParam);
        JSONObject skuInfoMapBody = (JSONObject) skuInfoMap.get(ProjectConstant.BODY);
        if ((int) skuInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            String msg = skuInfoMapBody.getString(ProjectConstant.ERROR);
            ProductChannelUnifyErrorEnum errorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ELEM, null, msg);
            boolean openRateLimit = MccConfigUtil.isElmOpenRateLimit();
            if (openRateLimit) {
                boolean isRateLimited = Objects.equals(errorEnum,RATE_LIMIT);
                if(isRateLimited){
                    return response.setStatus(ResultGenerator.genResult(TRIGGER_LIMIT, msg));
                }
            }
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, msg));
        }

        List<CatInfo> catInfos = Lists.newArrayList();
        List<ChannelStoreCategoryDTO> channelStoreCategoryDTOS = Lists.newArrayList();

        JSONObject catInfoMapData;
        try {
            catInfoMapData = skuInfoMapBody.getJSONObject(ProjectConstant.DATA);
        } catch (Exception e) {
            return response.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(Lists.emptyList());
        }
        if (catInfoMapData == null) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL)).setCatInfoList(Collections.emptyList());
        }

        JSONArray catInfoJSONArray = catInfoMapData.getJSONArray("categorys");
        // 组装数据
        catInfoJSONArray.forEach(item -> {
            genChildCategoryInfo((JSONObject) item, channelStoreCategoryDTOS, "0", "", 1);
        });
        catInfos.addAll(elmConverterService.channelStoreCategoryDTOMapping(channelStoreCategoryDTOS));
        return response.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(catInfos);
    }

    @Override
    public ResultData changeCustomSkuId(ChangeCustomSkuIdRequest request) {
        UpdateCustomSkuIdRequest updateCustomSkuIdRequest = new UpdateCustomSkuIdRequest();
        updateCustomSkuIdRequest.setBaseInfo(request.getBaseInfo());
        List<UpdateCustomSkuIdDTO> paramList = request.getParamList().stream().map(item ->
                elmConverterService.convert2UpdateCustomSkuIdDTO(item)).collect(Collectors.toList());
        updateCustomSkuIdRequest.setParamList(paramList);
        return updateCustomSkuId(updateCustomSkuIdRequest);
    }

    private void genChildCategoryInfo(JSONObject jsonObject, List<ChannelStoreCategoryDTO> channelStoreCategoryDTOS,
                                      String parentId, String parentName, int depth) {
        JSONArray catInfoJSONArray = (JSONArray) jsonObject.get(ProjectConstant.CHILDREN);

        ChannelStoreCategoryDTO channelStoreCategoryDTO = new ChannelStoreCategoryDTO();
        channelStoreCategoryDTO.setCategoryId(jsonObject.getString("category_id"));
        channelStoreCategoryDTO.setCategoryName(jsonObject.getString("name"));
        channelStoreCategoryDTO.setSequence(jsonObject.getInteger("rank"));
        channelStoreCategoryDTO.setParentId(parentId);
        channelStoreCategoryDTO.setParentName(parentName);
        channelStoreCategoryDTO.setDepth(depth);
        channelStoreCategoryDTO.setCustomCategoryId(jsonObject.getString("shop_custom_id"));
        channelStoreCategoryDTOS.add(channelStoreCategoryDTO);


        if (CollectionUtils.isNotEmpty(catInfoJSONArray)) {
            // 组装数据
            for (Object item : catInfoJSONArray
            ) {
                int childDepth = depth + 1;
                genChildCategoryInfo((JSONObject) item, channelStoreCategoryDTOS, channelStoreCategoryDTO.getCategoryId(),
                        channelStoreCategoryDTO.getCategoryName(), childDepth);
            }
        }
    }

}
