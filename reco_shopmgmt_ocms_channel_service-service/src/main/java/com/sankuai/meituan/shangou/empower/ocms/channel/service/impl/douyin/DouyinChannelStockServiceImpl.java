package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinChannelResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinSkuInSpuStockDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinSkuStockInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinSkuStockInfo.DyParams;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelStockService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 抖音渠道商品库存内部服务接口
 *
 * <AUTHOR>
 * @date 2023/12/26 14:34
 */
@Service("dyChannelStockService")
public class DouyinChannelStockServiceImpl implements ChannelStockService {

    public static final int STOCK_UPDATE_MAX_COUNT = 50;

    @Resource
    private CommonLogger logger;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private DouyinChannelGateService douyinChannelGateService;

    @Override
    public ResultSpuData updateStockBySpu(SpuStockRequest request) {
        //logger.info("dyChannelStockServiceImpl.updateStockBySpu,开始 request:{}", JacksonUtils.toJson(request));

        if (CollectionUtils.isEmpty(request.getParamList())) {
            return ResultGenerator.genResultSpuData(ResultCode.INVALID_PARAM, "同步参数列表为空");
        }

        boolean isRetry = request.isRetryFlag();
        BaseRequestSimple baseInfo = request.getBaseInfo();
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        try {
            List<SpuStockDTO> dySpuStockList = request.getParamList();

            //获取storeId的Set
            Set<Long> dyStoreIdSet = new HashSet<>();
            dySpuStockList.forEach(dySpuStock -> dyStoreIdSet.add(dySpuStock.getStoreId()));

            Map<Long, ChannelStoreDO> channelStoreHashMap = new HashMap<>();
            dyStoreIdSet.forEach(dyStoreId -> {
                ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(baseInfo.getTenantId(), baseInfo.channelId, dyStoreId);
                if (channelStoreDO != null) {
                    channelStoreHashMap.put(dyStoreId, channelStoreDO);
                }
            });
            logger.info("channelStoreHashMap的值为:{}", JacksonUtils.toJson(channelStoreHashMap));

            List<DouyinSkuInSpuStockDetail> douyinSkuInSpuStockDetailList = new ArrayList<>();
            for (SpuStockDTO spuStockDTO : dySpuStockList) {
                ChannelStoreDO channelStoreDO = channelStoreHashMap.get(spuStockDTO.getStoreId());
                if (channelStoreDO == null) {
                    logger.warn("dyChannelStockServiceImpl.updateStockBySpu,storeId:{}为空，渠道门店信息获取失败",spuStockDTO.getStoreId());
                    continue;
                }

                List<SkuInSpuStockDTO> skuStockInfo = spuStockDTO.getSkuStockInfo();
                if (CollectionUtils.isNotEmpty(skuStockInfo)) {
                    for (SkuInSpuStockDTO skuInSpuStockDTO : skuStockInfo) {
                        DouyinSkuInSpuStockDetail douyinSkuInSpuStockDetail = new DouyinSkuInSpuStockDetail();
                        douyinSkuInSpuStockDetail.setChannelStoreId(channelStoreDO.getChannelPoiCode());
                        douyinSkuInSpuStockDetail.setStoreId(spuStockDTO.getStoreId());
                        douyinSkuInSpuStockDetail.setChannelSpuId(spuStockDTO.getChannelSpuId());
                        douyinSkuInSpuStockDetail.setChannelSkuId(skuInSpuStockDTO.getChannelSkuId());
                        douyinSkuInSpuStockDetail.setCustomSpuId(spuStockDTO.getCustomSpuId());
                        douyinSkuInSpuStockDetail.setCustomSkuId(skuInSpuStockDTO.getCustomSkuId());
                        douyinSkuInSpuStockDetail.setStockQty(skuInSpuStockDTO.getStockQty());
                        douyinSkuInSpuStockDetail.setSpecType(skuInSpuStockDTO.getSpecType());
                        douyinSkuInSpuStockDetailList.add(douyinSkuInSpuStockDetail);
                    }
                }
            }

            ListUtils.listPartition(douyinSkuInSpuStockDetailList, STOCK_UPDATE_MAX_COUNT)
                    .forEach(douyinSkuInSpuStockDetail -> batchSyncStockCommon(baseInfo, douyinSkuInSpuStockDetail, resultData, isRetry));
        } catch (Exception e) {
            logger.warn("dyChannelStockServiceImpl.updateStockBySpu, 批量修改商品库存服务异常, request:{}", JacksonUtils.toJson(request), e);
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, "dyChannelStockServiceImpl.updateStockBySpu 批量修改商品库存失败");
        }

        logger.info("dyChannelStockServiceImpl.updateStockBySpu,结束 resultData:{}", JacksonUtils.toJson(resultData));
        return resultData;
    }

    private void batchSyncStockCommon(BaseRequestSimple baseInfo, List<DouyinSkuInSpuStockDetail> douyinSkuInSpuStockDetail,
                                      ResultSpuData resultData, boolean isRetry) {
        boolean retrySwitch = MccConfigUtil.douyinChannelStockRetryBySkuSwitch();
        if (retrySwitch && isRetry) {
            logger.info("douyin batchSyncStockCommon retry by single sku.");
            batchSyncStockForRetry(baseInfo, douyinSkuInSpuStockDetail, resultData);
        } else {
            logger.info("douyin batchSyncStockCommon run by batch sku.");
            batchSyncStock(baseInfo, douyinSkuInSpuStockDetail, resultData);
        }
    }

    private void batchSyncStockForRetry(BaseRequestSimple baseInfo, List<DouyinSkuInSpuStockDetail> douyinSkuInSpuStockDetail,
                                        ResultSpuData resultData) {
        ResultSpuData temporaryResult = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        try {
            // 业务参数转换
            List<DyParams> skuStockInfoList = buildSpuStock(douyinSkuInSpuStockDetail);
            if (CollectionUtils.isEmpty(skuStockInfoList)) {
                return;
            }

            Map<String, DouyinSkuInSpuStockDetail> sourceMap = douyinSkuInSpuStockDetail.stream()
                    .collect(Collectors.toMap(DouyinSkuInSpuStockDetail::getChannelSkuId, Function.identity(), (o1, o2) -> o2));

            // 抖音重试任务优化：因为抖音批量同步时，只要有一个sku失败会导致整体失败，导致一批重试任务永远无法重试成功
            // 所以这里启用优化：抖音的重试推送，单个sku推送一次，并且累计到最终返回结果中.
            int interval = MccConfigUtil.getDouyinStockRetryInterval();
            List<DouyinSkuStockInfo> reqList = new ArrayList<>();
            skuStockInfoList.forEach(info -> {
                DouyinSkuStockInfo douyinSkuStockInfo = new DouyinSkuStockInfo();
                douyinSkuStockInfo.setItems(Lists.newArrayList(info));
                reqList.add(douyinSkuStockInfo);
            });

            for (DouyinSkuStockInfo req : reqList) {
                if (interval > 0) {
                    Thread.sleep(interval);
                }

                DouyinSkuInSpuStockDetail detail = sourceMap.get(String.valueOf(req.getItems().get(0).getSku_id()));
                List<DouyinSkuInSpuStockDetail> tempList = (null == detail) ? Lists.newArrayList() : Lists.newArrayList(detail);
                try {
                    // 调用渠道接口
                    logger.info("dyChannelStockServiceImpl.updateStockBySpu HTTP请求 start, baseRequestSimple:{}, douyinSkuStockInfo:{},",
                            JacksonUtils.toJson(baseInfo), JacksonUtils.toJson(req));
                    ChannelResponseDTO postResult = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SKU_SYNC_STOCK_BATCHMULTIPRODUCTS,
                            baseInfo, null, req);
                    logger.info("dyChannelStockServiceImpl.updateStockBySpu HTTP请求 end, baseRequestSimple:{}, douyinSkuStockInfo:{}, postResult:{}",
                            JacksonUtils.toJson(baseInfo), JacksonUtils.toJson(req), JacksonUtils.toJson(postResult));

                    // 组装返回结果
                    installPartResultSpuData(temporaryResult, postResult, tempList);
                } catch (InvokeChannelTooMuchException e) {
                    logger.warn("dyChannelStockServiceImpl single 触发限频, req:{}, douyinSkuInSpuStockDetail:{}",
                            JacksonUtils.toJson(req), JacksonUtils.toJson(tempList), e);
                    installFailResultSpuData(temporaryResult, tempList, "调用渠道同步库存异常");
                } catch (Exception se) {
                    logger.error("dyChannelStockServiceImpl single 同步渠道库存异常, req:{}, douyinSkuInSpuStockDetail:{}",
                            JacksonUtils.toJson(req), JacksonUtils.toJson(tempList), se);
                    installFailResultSpuData(temporaryResult, tempList,"调用渠道同步库存异常");
                }
            }

            resultData.getSucData().addAll(temporaryResult.getSucData());
            resultData.getErrorData().addAll(temporaryResult.getErrorData());
        } catch (Exception e) {
            logger.error("dyChannelStockServiceImpl retry 同步渠道库存异常, baseInfo:{}, douyinSkuInSpuStockDetail:{}", JacksonUtils.toJson(baseInfo), JacksonUtils.toJson(douyinSkuInSpuStockDetail), e);
            installFailResultSpuData(resultData, douyinSkuInSpuStockDetail,"调用渠道同步库存异常");
        }
    }

    private void batchSyncStock(BaseRequestSimple baseInfo, List<DouyinSkuInSpuStockDetail> douyinSkuInSpuStockDetail, ResultSpuData resultData) {

        ResultSpuData temporaryResult = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        try {

            // 业务参数转换
            List<DyParams> skuStockInfoList = buildSpuStock(douyinSkuInSpuStockDetail);
            if (CollectionUtils.isEmpty(skuStockInfoList)) {
                return;
            }
            DouyinSkuStockInfo douyinSkuStockInfo = new DouyinSkuStockInfo();
            douyinSkuStockInfo.setItems(skuStockInfoList);

            // 调用渠道接口
            logger.info("dyChannelStockServiceImpl.updateStockBySpu HTTP请求 start, baseRequestSimple:{}, douyinSkuStockInfo:{},", JacksonUtils.toJson(baseInfo), JacksonUtils.toJson(douyinSkuStockInfo));
            ChannelResponseDTO postResult = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SKU_SYNC_STOCK_BATCHMULTIPRODUCTS, baseInfo, null, douyinSkuStockInfo);
            logger.info("dyChannelStockServiceImpl.updateStockBySpu HTTP请求 end, baseRequestSimple:{}, douyinSkuStockInfo:{}, postResult:{}", JacksonUtils.toJson(baseInfo), JacksonUtils.toJson(douyinSkuStockInfo), JacksonUtils.toJson(postResult));

            // 组装返回结果
            installPartResultSpuData(temporaryResult, postResult, douyinSkuInSpuStockDetail);

            resultData.getSucData().addAll(temporaryResult.getSucData());
            resultData.getErrorData().addAll(temporaryResult.getErrorData());

        } catch (Exception e) {
            logger.error("dyChannelStockServiceImpl 同步渠道库存异常, baseInfo:{},douyinSkuInSpuStockDetail:{}", JacksonUtils.toJson(baseInfo), JacksonUtils.toJson(douyinSkuInSpuStockDetail), e);
            installFailResultSpuData(resultData, douyinSkuInSpuStockDetail,"调用渠道同步库存异常");
        }
    }

    private List<DyParams> buildSpuStock(List<DouyinSkuInSpuStockDetail> douyinSkuInSpuStockDetail) {
        if (CollectionUtils.isEmpty(douyinSkuInSpuStockDetail)) {
            return new ArrayList<>();
        }
        List<DyParams> list = new ArrayList<>();

        for (DouyinSkuInSpuStockDetail skuInSpuStockDetail : douyinSkuInSpuStockDetail) {
            // 判断非空
            DyParams douyinSkuStockInfo = updateSpuStock(skuInSpuStockDetail);
            if (douyinSkuStockInfo!=null){
                list.add(douyinSkuStockInfo);
            }
        }
        return list;
    }

    private DyParams updateSpuStock(DouyinSkuInSpuStockDetail skuInSpuStockDetail) {
        if (skuInSpuStockDetail == null) {
            return null;
        }
        DyParams dyParams = new DyParams();
        dyParams.setStock_num((long) skuInSpuStockDetail.getStockQty());
        dyParams.setProduct_id(Long.parseLong(skuInSpuStockDetail.getChannelSpuId()));
        dyParams.setOut_warehouse_id(skuInSpuStockDetail.getChannelStoreId());
        dyParams.setSku_id(Long.parseLong(skuInSpuStockDetail.getChannelSkuId()));
        return dyParams;
    }

    private void installFailResultSpuData(ResultSpuData resultData, List<DouyinSkuInSpuStockDetail> douyinSkuInSpuStockDetail, String errMsg) {
        if (CollectionUtils.isEmpty(douyinSkuInSpuStockDetail)) {
            return;
        }
        Map<String, List<DouyinSkuInSpuStockDetail>> spuMap = douyinSkuInSpuStockDetail.stream().collect(Collectors.groupingBy(DouyinSkuInSpuStockDetail::getChannelSpuId));
        spuMap.forEach((spuId, detailList) -> {
            SpuKey spuKey = new SpuKey().setCustomSpuId(detailList.get(0).getCustomSpuId());
            List<SkuKey> skuKeys = Lists.newArrayList();
            long storeId=0;
            for (DouyinSkuInSpuStockDetail it : detailList) {
                skuKeys.add(new SkuKey().setCustomSkuId(it.getCustomSkuId()));
                storeId=it.getStoreId();
            }
            spuKey.setSkus(skuKeys);
            resultData.getErrorData().add(new ResultErrorSpu().setStoreId(storeId).setSpuInfo(spuKey)
                    .setErrorCode(ResultCodeEnum.FAIL.getValue()).setErrorMsg(errMsg));
        });
    }

    private void installPartResultSpuData(ResultSpuData resultData,ChannelResponseDTO postResult, List<DouyinSkuInSpuStockDetail> douyinSkuInSpuStockDetail) {

        if (postResult == null || CollectionUtils.isEmpty(douyinSkuInSpuStockDetail)) {
            throw new IllegalStateException("数据为空");
        }

        if (!postResult.isSuccess()) {
            installFailResultSpuData(resultData, douyinSkuInSpuStockDetail, postResult.getAggregateErrorMsg());
            return;
        }

        boolean dyResultFlag = MccConfigUtil.douyinChannelStockResultFlag();
        if (dyResultFlag){
            installSuccessResultSpuData(resultData,douyinSkuInSpuStockDetail);
            return;
        }

        Set<String> sucSkuSet = Sets.newHashSet();
        Map<String, String> failSkuMap = Maps.newHashMap();

        if (postResult.getCoreData() != null && postResult.getCoreData() instanceof DouyinChannelResult) {
            DouyinChannelResult aggRest = (DouyinChannelResult) postResult.getCoreData();
            aggRest.getResults().forEach(item -> {

                if (item.isSuccess()) {
                    sucSkuSet.add(String.valueOf(item.getSku_id()));
                } else {
                    failSkuMap.put(String.valueOf(item.getSku_id()), item.getChannelResultInfo());
                }
            });
        }
        List<DouyinSkuInSpuStockDetail> sucSkuDetails = Lists.newArrayList();
        douyinSkuInSpuStockDetail.forEach(sku -> {

            if (sucSkuSet.contains(sku.getChannelSkuId())) {
                sucSkuDetails.add(sku);
            } else if (failSkuMap.containsKey(sku.getChannelSkuId())) {
                SpuKey spuKey = new SpuKey().setCustomSpuId(sku.getCustomSpuId()).setSkus(Lists.newArrayList(new SkuKey().setCustomSkuId(sku.getCustomSkuId())));
                resultData.getErrorData().add(new ResultErrorSpu().setStoreId(sku.getStoreId()).setSpuInfo(spuKey)
                        .setErrorCode(ResultCodeEnum.FAIL.getValue()).setErrorMsg(failSkuMap.get(sku.getChannelSkuId())));
            } else {
                SpuKey spuKey = new SpuKey().setCustomSpuId(sku.getCustomSpuId()).setSkus(Lists.newArrayList(new SkuKey().setCustomSkuId(sku.getCustomSkuId())));
                resultData.getErrorData().add(new ResultErrorSpu().setStoreId(sku.getStoreId()).setSpuInfo(spuKey)
                        .setErrorCode(ResultCodeEnum.FAIL_ALLOW_RETRY.getValue()).setErrorMsg("系统未同步，请重试"));
            }
        });

        Map<String, List<DouyinSkuInSpuStockDetail>> spuMap = sucSkuDetails.stream().collect(Collectors.groupingBy(DouyinSkuInSpuStockDetail::getChannelSpuId));

        spuMap.forEach((spuId, detailList) -> {
            SpuKey spuKey = new SpuKey().setCustomSpuId(detailList.get(0).getCustomSpuId());
            List<SkuKey> skuKeys = Lists.newArrayList();
            long storeId=0;
            for (DouyinSkuInSpuStockDetail it : detailList) {
                skuKeys.add(new SkuKey().setCustomSkuId(it.getCustomSkuId()));
                storeId=it.getStoreId();
            }
            spuKey.setSkus(skuKeys);
            resultData.getSucData().add(new ResultSuccessSpu().setStoreId(storeId).setSpuInfo(spuKey));
        });

    }

    private void installSuccessResultSpuData(ResultSpuData resultData, List<DouyinSkuInSpuStockDetail> douyinSkuInSpuStockDetail) {

        Map<String, List<DouyinSkuInSpuStockDetail>> spuMap = douyinSkuInSpuStockDetail.stream().collect(Collectors.groupingBy(DouyinSkuInSpuStockDetail::getChannelSpuId));

        spuMap.forEach((spuId, detailList) -> {
            SpuKey spuKey = new SpuKey().setCustomSpuId(detailList.get(0).getCustomSpuId());
            List<SkuKey> skuKeys = Lists.newArrayList();
            long storeId=0;
            for (DouyinSkuInSpuStockDetail it : detailList) {
                skuKeys.add(new SkuKey().setCustomSkuId(it.getCustomSkuId()));
                storeId=it.getStoreId();
            }
            spuKey.setSkus(skuKeys);
            resultData.getSucData().add(new ResultSuccessSpu().setStoreId(storeId).setSpuInfo(spuKey));
        });
    }


    @Override
    public ResultData updateStockMultiChannel(SkuStockMultiChannelRequest request) {
        logger.error("DouyinChannelStockServiceImpl 不支持的更新方式 updateStockMultiChannel: " + JacksonUtils.toJson(request));
        return null;
    }

    @Override
    public BatchGetStockInfoResponse batchGetStockInfo(BatchGetStockInfoRequest request) {
        logger.error("DouyinChannelStockServiceImpl 不支持的更新方式 batchGetStockInfo: " + JacksonUtils.toJson(request));
        return null;
    }
}
