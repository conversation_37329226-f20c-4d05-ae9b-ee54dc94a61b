package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.model.OrderBaseModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryByViewOrderIdRequest;
import com.meituan.shangou.saas.o2o.dto.request.OrderQueryBaseRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderBaseResponse;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.OrderCommonService;
import com.sankuai.sgfnqnh.poi.api.client.thrift.ChannelPoiThriftService;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.ChannelPoiBaseInfoDTO;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.response.PoiChannelPoiRelationResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class OrderCommonServiceImpl implements OrderCommonService {

    @Resource
    private BizOrderThriftService bizOrderService;

    @Resource(name = "channelStoreThriftService")
    private ChannelPoiThriftService channelPoiThriftService;

    @Override
    public BizOrderModel queryOrderByViewId(Long tenantId, Long storeId, String viewOrderId,
            DynamicOrderBizType orderBizType) {
        try {
            BizOrderQueryByViewOrderIdRequest request = new BizOrderQueryByViewOrderIdRequest(orderBizType.getValue(),
                    viewOrderId, tenantId, storeId);
            // log.info("BizOrderThriftService.queryByViewOrderId begin, req={}", request);
            BizOrderQueryResponse response = bizOrderService.queryByViewOrderId(request);
            log.info("BizOrderThriftService.queryByViewOrderId req={}, resp={}", request, response);
            if (response != null && response.getBizOrderModel() != null) {
                return response.getBizOrderModel();
            }
            return null;

        } catch (Exception e) {
            log.error("BizOrderThriftService.queryByOrderId failed tenantId:{},orderId:{}", tenantId, viewOrderId, e);
            return null;
        }
    }

    @Override
    public OrderBaseModel queryOrderBaseByViewId(Long tenantId, Long storeId, String viewOrderId,
            DynamicOrderBizType orderBizType) {
        try {
            OrderQueryBaseRequest request = OrderQueryBaseRequest.builder()
                    .tenantId(tenantId)
                    .viewOrderId(viewOrderId)
                    .orderBizType(orderBizType.getValue())
                    .fromTairFirst(true)
                    .build();
            // log.info("BizOrderThriftService.queryOrderBase4ViewOrderId begin, req={}", request);
            BizOrderBaseResponse response = bizOrderService.queryOrderBase4ViewOrderId(request);
            log.info("BizOrderThriftService.queryOrderBase4ViewOrderId req={}, resp={}", request, response);
            if (response != null && response.getModel() != null) {
                return response.getModel();
            }
            return null;

        } catch (Exception e) {
            log.error("BizOrderThriftService.queryOrderBase4ViewOrderId failed tenantId:{}, orderId:{}", tenantId,
                    viewOrderId, e);
            return null;
        }
    }

    @Override
    @Nullable
    public ChannelPoiBaseInfoDTO queryTenantInfoByChannelPoiInfo(DynamicChannelType dynamicChannelType,
            String channelPoiCode) {
        try {
            log.info("queryTenantInfoByChannelPoiInfo req: {}", channelPoiCode);
            PoiChannelPoiRelationResponse poiChannelPoiRelationResponse = channelPoiThriftService
                    .queryPoiChannelPoiRelationByChannelPoiCodes(dynamicChannelType.getChannelId(),
                            Collections.singletonList(channelPoiCode));

            log.info("queryTenantInfoByChannelPoiInfo resp: {}", poiChannelPoiRelationResponse);
            List<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTOList = poiChannelPoiRelationResponse.getChannelPoiBaseInfoDTOList();
            if (CollectionUtils.isEmpty(channelPoiBaseInfoDTOList)) {
                return null;
            }
            return channelPoiBaseInfoDTOList.get(0);
        } catch (Exception e) {
            log.error("queryPoiChannelPoiRelationByChannelPoiCodes error: ", e);
        }
        return null;
    }
}
