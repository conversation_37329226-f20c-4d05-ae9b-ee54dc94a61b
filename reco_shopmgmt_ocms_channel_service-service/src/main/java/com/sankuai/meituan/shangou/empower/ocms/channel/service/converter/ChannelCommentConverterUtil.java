package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ChannelCommentLevelRuleInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentListQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentReplyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.CommentLevelEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 评价转换工具
 *
 * <AUTHOR>
 */
public class ChannelCommentConverterUtil {

    private ChannelCommentConverterUtil() {

    }

    /**
     * 评价分与评价级别映射关系
     */
    private static final Map<Integer, String> DEFAULT_MT_ORDER_SCORE_TO_COMMENT_LEVEL_MAP = new HashMap<>();
    private static final Map<Integer, String> DEFAULT_ELM_ORDER_SCORE_TO_COMMENT_LEVEL_MAP = new HashMap<>();
    private static final Map<Integer, String> DEFAULT_JDDJ_ORDER_SCORE_TO_COMMENT_LEVEL_MAP = new HashMap<>();
    private static final Map<Integer, String> DEFAULT_YOUZAN_ORDER_SCORE_TO_COMMENT_LEVEL_MAP = new HashMap<>();
    private static final List<ChannelCommentLevelRuleInfo> DEFAULT_COMMENT_LEVEL_RULES = new ArrayList();

    static {

        // 美团订单评分与评价级别映射关系
        DEFAULT_MT_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(1, CommentLevelEnum.BAD_COMMENT.name());
        DEFAULT_MT_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(2, CommentLevelEnum.BAD_COMMENT.name());
        DEFAULT_MT_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(3, CommentLevelEnum.COMMON_COMMENT.name());
        DEFAULT_MT_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(4, CommentLevelEnum.GOOD_COMMENT.name());
        DEFAULT_MT_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(5, CommentLevelEnum.GOOD_COMMENT.name());

        // 饿了么订单评分与评价级别映射关系
        DEFAULT_ELM_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(1, CommentLevelEnum.BAD_COMMENT.name());
        DEFAULT_ELM_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(2, CommentLevelEnum.BAD_COMMENT.name());
        DEFAULT_ELM_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(3, CommentLevelEnum.COMMON_COMMENT.name());
        DEFAULT_ELM_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(4, CommentLevelEnum.GOOD_COMMENT.name());
        DEFAULT_ELM_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(5, CommentLevelEnum.GOOD_COMMENT.name());

        // 京东到家订单评分与评价级别映射关系
        DEFAULT_JDDJ_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(1, CommentLevelEnum.BAD_COMMENT.name());
        DEFAULT_JDDJ_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(2, CommentLevelEnum.BAD_COMMENT.name());
        DEFAULT_JDDJ_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(3, CommentLevelEnum.COMMON_COMMENT.name());
        DEFAULT_JDDJ_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(4, CommentLevelEnum.GOOD_COMMENT.name());
        DEFAULT_JDDJ_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(5, CommentLevelEnum.GOOD_COMMENT.name());

        // 有赞订单评分与评价级别映射关系
        DEFAULT_YOUZAN_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(1, CommentLevelEnum.BAD_COMMENT.name());
        DEFAULT_YOUZAN_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(2, CommentLevelEnum.BAD_COMMENT.name());
        DEFAULT_YOUZAN_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(3, CommentLevelEnum.COMMON_COMMENT.name());
        DEFAULT_YOUZAN_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(4, CommentLevelEnum.GOOD_COMMENT.name());
        DEFAULT_YOUZAN_ORDER_SCORE_TO_COMMENT_LEVEL_MAP.put(5, CommentLevelEnum.GOOD_COMMENT.name());



        // 渠道与各渠道订单评分及评价级别映射关系
        DEFAULT_COMMENT_LEVEL_RULES.add(new ChannelCommentLevelRuleInfo(ChannelTypeEnum.MEITUAN.getCode(), DEFAULT_MT_ORDER_SCORE_TO_COMMENT_LEVEL_MAP, "美团外卖：好评 4～5星；中评 3星；差评 1～2星"));
        DEFAULT_COMMENT_LEVEL_RULES.add(new ChannelCommentLevelRuleInfo(ChannelTypeEnum.ELEM.getCode(), DEFAULT_ELM_ORDER_SCORE_TO_COMMENT_LEVEL_MAP, "饿了么：好评 4～5星；中评3 星；差评 1～2星"));
        DEFAULT_COMMENT_LEVEL_RULES.add(new ChannelCommentLevelRuleInfo(ChannelTypeEnum.JD2HOME.getCode(), DEFAULT_JDDJ_ORDER_SCORE_TO_COMMENT_LEVEL_MAP, "京东到家：好评 4～5星；中评 3星；差评 1～2星"));
        DEFAULT_COMMENT_LEVEL_RULES.add(new ChannelCommentLevelRuleInfo(ChannelTypeEnum.YOU_ZAN.getCode(), DEFAULT_YOUZAN_ORDER_SCORE_TO_COMMENT_LEVEL_MAP, "有赞：好评 4～5星；中评 3星；差评 1～2星"));
    }


    public static Map<String, Object> buildMtCommentQueryBizParam(CommentListQueryRequest request, ChannelStoreDO channelStoreDO) {
        Map<String, Object> bizParam = Maps.newHashMap();

        String startTime = DateUtils.formatDate(DateUtils.parse(request.getStartTime(),
                DateUtils.DefaultLongFormat), DateUtils.YYYYMMDD_Format);
        String endTime = DateUtils.formatDate(DateUtils.parse(request.getEndTime(),
                DateUtils.DefaultLongFormat), DateUtils.YYYYMMDD_Format);

        bizParam.put(ProjectConstant.APP_POI_CODE, channelStoreDO.getChannelOnlinePoiCode());
        bizParam.put(ProjectConstant.COMMENT_START_TIME, startTime);
        bizParam.put(ProjectConstant.COMMENT_END_TIME, endTime);
        bizParam.put(ProjectConstant.COMMENT_MT_PAGE_OFFSET, (request.getPageNum() - 1) * request.getPageSize());
        bizParam.put(ProjectConstant.COMMENT_MT_PAGE_SIZE, request.getPageSize());
        if (StringUtils.isNotEmpty(request.getReplyStatus())) {
            bizParam.put(ProjectConstant.COMMENT_MT_REPLY_STATUS, Integer.parseInt(request.getReplyStatus()));
        }
        return bizParam;
    }

    public static Map<String, Object> buildMtCommentReplyBizParam(CommentReplyRequest request, ChannelStoreDO channelStoreDO) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.APP_POI_CODE, channelStoreDO.getChannelOnlinePoiCode());
        bizParam.put(ProjectConstant.COMMENT_ID, request.getChannelCommentId());
        bizParam.put(ProjectConstant.COMMENT_REPLY, request.getReplyContent());
        return bizParam;
    }


    public static Map<String, Object> buildElmCommentQueryBizParam(CommentListQueryRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        long startTime = DateUtils.date2TimeStamp(request.getStartTime(), DateUtils.DefaultLongFormat);
        long endTime = DateUtils.date2TimeStamp(request.getEndTime(), DateUtils.DefaultLongFormat);

        bizParam.put(ProjectConstant.COMMENT_START_TIME, startTime);
        bizParam.put(ProjectConstant.COMMENT_END_TIME, endTime);
        bizParam.put(ProjectConstant.COMMENT_ELM_PAGE, request.getPageNum());
        if (StringUtils.isNotEmpty(request.getReplyStatus())) {
            bizParam.put(ProjectConstant.COMMENT_ELM_REPLY_STATUS, Integer.parseInt(request.getReplyStatus()));
        }
        return bizParam;
    }

    public static Map<String, Object> buildElmCommentReplyBizParam(CommentReplyRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.COMMENT_ID, request.getChannelCommentId());
        bizParam.put(ProjectConstant.COMMENT_REPLY_CONTENT, request.getReplyContent());
        return bizParam;
    }

    public static Map<String, Object> buildJddjCommentQueryBizParam(CommentListQueryRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.COMMENT_ORDER_ID, request.getChannelCommentId());
        return bizParam;
    }

    public static Map<String, Object> buildJddjCommentReplyBizParam(CommentReplyRequest request, ChannelStoreDO channelStoreDO) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.COMMENT_ORDER_ID, request.getChannelCommentId());
        bizParam.put(ProjectConstant.COMMENT_JJDJ_STORE_ID, channelStoreDO.getChannelPoiCode());
        bizParam.put(ProjectConstant.COMMENT_REPLY_CONTENT, request.getReplyContent());
        bizParam.put(ProjectConstant.COMMENT_JJDJ_REPLY_PIN, request.getReplyUser());
        return bizParam;
    }

    public static String calculateCommentLevel(Long tenantId, Integer channelId, Integer orderScore, Date commentTime) {

        String commentLevel = CommentLevelEnum.NONE.name();

        Map<Integer, String> orderScoreToCommentLevelMap = getScore2CommentLevelMap(tenantId, channelId, commentTime);
        if (MapUtils.isNotEmpty(orderScoreToCommentLevelMap)) {
            commentLevel = orderScoreToCommentLevelMap.get(orderScore);
        }
        return commentLevel;
    }

    public static Map<Integer, String> getScore2CommentLevelMap(Long tenantId, Integer channelId, Date commentTime) {

        List<ChannelCommentLevelRuleInfo> commentLevelRules = getCommentLevelRules(tenantId);
        if (CollectionUtils.isEmpty(commentLevelRules)) {
            return Collections.emptyMap();
        }

        Map<Integer, Map<Integer, String>> channelScoreCommentLevelMap = commentLevelRules.stream()
                .collect(Collectors.toMap(ruleInfo -> ruleInfo.getChannelId(),
                        ruleInfo -> ruleInfo.getEffectiveScore2CommentLevelMap(commentTime)));

        return channelScoreCommentLevelMap.get(channelId);
    }

    public static List<ChannelCommentLevelRuleInfo> getCommentLevelRules(Long tenantId) {

        // 查询租户评分规则配置
        List<ChannelCommentLevelRuleInfo> commentLevelRuleConfigList =
                MccConfigUtil.getTenantCommentLevelRuleConfig(tenantId);

        if (CollectionUtils.isEmpty(commentLevelRuleConfigList)) {
            // 查询渠道评分规则配置
            commentLevelRuleConfigList = MccConfigUtil.getCommentLevelRuleConfig();
        }

        // 如果未对评分规则动态配置, 则返回默认规则
        if (CollectionUtils.isEmpty(commentLevelRuleConfigList)) {
            return DEFAULT_COMMENT_LEVEL_RULES;
        }

        return commentLevelRuleConfigList;
    }

    public static List<String> getCommentLevelRuleDescriptions(Long tenantId) {

        List<ChannelCommentLevelRuleInfo> commentLevelRules = getCommentLevelRules(tenantId);

        if (CollectionUtils.isEmpty(commentLevelRules)) {
            return Collections.emptyList();
        }

        Date now = new Date();
        List<String> commentLevelRuleDescList = new ArrayList<>();
        for (ChannelCommentLevelRuleInfo commentLevelRule : commentLevelRules) {
            commentLevelRuleDescList.add(commentLevelRule.getEffectiveRuleDesc(now));
        }

        return commentLevelRuleDescList;
    }


}
