/**
 * meituan.com Inc. Copyright (c) 2010-2024 All Rights Reserved.
 */
package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.health;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryRequest;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MaltFarmAggRiderTransferDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.OrderRiderTransferRiderNotifyDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.PaoTuiRiderTransferDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelOrderThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.TmsThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.LogisticsStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DeliveryOrderCheckWrapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.EnumUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.FarmPaoTuiSignUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * </p>
 * <AUTHOR>
 * @version 渠道骑手转单:ChannelRiderTransferRiderService.java v1.0 2024/6/11 12:30 wb_wenke Exp $
 */
@Service("healthChannelRiderTransferRiderService")
public class HealthChannelRiderTransferRiderService {

    @Resource
    private CommonLogger log;

    @Resource
    private TenantService tenantService;

    @Resource
    private ChannelOrderThriftServiceProxy channelOrderThriftServiceProxy;

    @Resource
    private TmsThriftServiceProxy tmsThriftServiceProxy;

    @Resource
    private DeliveryOrderCheckWrapper deliveryOrderCheckWrapper;

    @Resource
    private BizOrderThriftService bizOrderThriftServiceClient;

    @Autowired
    @Qualifier("healthChannelOrderService")
    private ChannelOrderService healthChannelOrderService;

    public ChannelOrderService getHealthChannelOrderService() {
        return healthChannelOrderService;
    }

    /**
     * 根据订单ID获取运单 （包含灰度逻辑）
     * @param channelTypeEnum
     * @param request
     * @param tenantId
     * @param isFillRiderPhone 是否填充骑手电话号码
     * @return
     */
    public MaltFarmAggRiderTransferDTO getMaltFarmAggRiderTransferDTO(ChannelTypeEnum channelTypeEnum, OrderNotifyRequest request, Long tenantId, Boolean isFillRiderPhone) {
        try {
            // 2 非无人仓租户直接返回不处理
            if (!tenantService.isMedicineAdultUnmanWarehouse(tenantId)) {
                return null;
            }
            // 2.1 如果开启灰度 并且 租户ID为灰度租户 执行后续骑手转骑手消息逻辑 否则不处理  不灰度直接全部放量
            if (MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayTenantSwitch()) {
                List<Long> grayTenants = MccConfigUtil.getDeliveryPaotuiRiderTransferTiderGrayTenantIds();
                // 开启了租户灰度 并且 当前租户ID不是灰度租户ID 直接返回
                if (!grayTenants.contains(tenantId)) {
                    return null;
                }
            }

            // 3. 获取订单查询参数
            BizOrderQueryRequest bizOrderQueryRequest = getBizOrderQueryRequest(channelTypeEnum, request.getOrderId(), tenantId);
            Optional<BizOrderModel> bizOrderModelOpt = channelOrderThriftServiceProxy.queryOrderInfo(bizOrderQueryRequest);
            if (!bizOrderModelOpt.isPresent()) {
                log.info("doRiderTransferRider bizOrderModelOpt is not present");
                return null;
            }
            // 3.1 增加门店灰度逻辑
            Long shopId = bizOrderModelOpt.get().getShopId();
            if (MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayPoiSwitch()) {
                List<Long> grayPoiId = MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayPoiIds();
                // 如果开启了门店灰度 并且 门店ID不是灰度的门店ID直接返回
                if (!grayPoiId.contains(shopId)) {
                    return null;
                }
            }

            Optional<List<TDeliveryOrder>> tDeliveryOrders = tmsThriftServiceProxy.queryDeliveryOrderByOrderId(bizOrderModelOpt.get().getOrderId());
            if (!tDeliveryOrders.isPresent()) {
                log.info("doRiderTransferRider tDeliveryOrders is not present");
                return null;
            }
            boolean isValid = deliveryOrderCheckWrapper.checkValidPaoTuiOrder(tDeliveryOrders.get());
            if (!isValid) {
                log.info("doRiderTransferRider tDeliveryOrders is not valid");
                return null;
            }

            TDeliveryOrder deliveryOrder = tDeliveryOrders.get().stream().filter(TDeliveryOrder::getActiveStatus).findFirst().orElse(tDeliveryOrders.get().stream().max(
                Comparator.comparing(TDeliveryOrder::getId)).get());
            List<DeliveryPlatformEnum> platformEnumList = deliveryOrderCheckWrapper.getDeliveryPlatform(tDeliveryOrders.get());
            if(platformEnumList.contains(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM)){
                // 4. 构建跑腿的对象
                PaoTuiRiderTransferDTO paoTuiRiderTransferDTO = getPaoTuiRiderTransferByOrderNotifyRequest(request, tenantId, shopId, isFillRiderPhone);

                // 5. 返回需要调用麦芽田的对象
                return buildMaltFarmAggRiderTransferDTO(paoTuiRiderTransferDTO, deliveryOrder);
            }else if(platformEnumList.contains(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM)){
                // 青云消息 青云跑腿逻辑暂不对接
                return null;
            }
            return null;
        } catch (Exception e) {
            log.info("channelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO exception", e);
            Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, "channelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO exception");
            throw e;
        }
    }

    /**
     * 构建麦芽田回到接口参数
     * @param paoTuiRiderTransferDTO
     * @param deliveryOrder
     * @return
     */
    private MaltFarmAggRiderTransferDTO buildMaltFarmAggRiderTransferDTO(PaoTuiRiderTransferDTO paoTuiRiderTransferDTO, TDeliveryOrder deliveryOrder) {
        String orderId = String.valueOf(deliveryOrder.getOrderId());
        if(StringUtils.isNotEmpty(deliveryOrder.getDeliveryOrderId())){
            orderId = deliveryOrder.getDeliveryOrderId();
        }
        MaltFarmAggRiderTransferDTO maltFarmAggRiderTransferDTO = MaltFarmAggRiderTransferDTO.builder()
            // 我们的运单id就是给他们的orderId
            .orderId(orderId)
            // 同麦芽田商量后决定传递固定值10：已抢单 牵牛花的美团跑腿比较特殊回调的状态流转分别为：0 待接单 10 已抢单 15 已到店 20 派送中 40 已完成 100 已取消
            .status(10)
            .dispatcherName(paoTuiRiderTransferDTO.getRiderName())
            .dispatcherMobile(paoTuiRiderTransferDTO.getRiderPhone())
            .timestamp(paoTuiRiderTransferDTO.getTimestamp().intValue())
            .build();
        maltFarmAggRiderTransferDTO.setSign(
            FarmPaoTuiSignUtils.generateSignatureFromRequest(maltFarmAggRiderTransferDTO));
        return maltFarmAggRiderTransferDTO;
    }

    /**
     * 获取订单查询参数
     *
     * @param channelTypeEnum
     * @param wmOrderIdView
     * @return
     */
    private BizOrderQueryRequest getBizOrderQueryRequest(ChannelTypeEnum channelTypeEnum, String wmOrderIdView, Long tenantId) {
        BizOrderQueryRequest bizOrderQueryRequest = new BizOrderQueryRequest();
        // 也可以使用getTenantIdParamByChannelPoiCode(ChannelTypeEnum.MT_MEDICINE.getCode(), paoTuiRiderTransferDTO.getAppPoiCode());获取租户ID
        bizOrderQueryRequest.setTenantId(tenantId);
        bizOrderQueryRequest.setShopId(0L);
        bizOrderQueryRequest.setViewOrderId(wmOrderIdView);
        bizOrderQueryRequest.setFromMaster(true);
        bizOrderQueryRequest.setOrderBizType(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue());
        return bizOrderQueryRequest;
    }

    /**
     * 根据通知参数转换为 跑腿对象参数
     * @param request 请求参数
     * @param tenantId 租户ID
     * @param storeId 门店ID
     * @param isFillRiderPhoneFlag 是否填充 骑手电话号码
     * @return
     */
    public PaoTuiRiderTransferDTO getPaoTuiRiderTransferByOrderNotifyRequest(OrderNotifyRequest request, Long tenantId, Long storeId, Boolean isFillRiderPhoneFlag) {
        long timestamp = System.currentTimeMillis();
        if(StringUtils.isNotBlank(request.getTimestamp())){
            timestamp = Long.parseLong(request.getTimestamp());
        }
        OrderRiderTransferRiderNotifyDTO riderNotifyBody = JSONObject.parseObject(request.getBody(), OrderRiderTransferRiderNotifyDTO.class);
        String appPoiCode = riderNotifyBody.getApp_poi_code();
        String wmOrderIdView = riderNotifyBody.getWm_order_id_view();
        String riderName = riderNotifyBody.getRider_name();
        // 由于平台该接口返回的riderPhone为空 所以需要单独请求接口获取对应订单骑手的电话号码
        String riderPhone = StringUtils.defaultString(riderNotifyBody.getRider_phone(), "");
        if(StringUtils.isBlank(appPoiCode)){
            log.info("app_poi_code is empty");
            Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, "跑腿接口参数异常。app_poi_code is empty");
            throw new BizException("app_poi_code is empty");
        }
        if (StringUtils.isBlank(wmOrderIdView) || StringUtils.isBlank(riderName)) {
            log.info("wmOrderIdView, riderName is empty");
            Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, "跑腿接口参数异常。wmOrderIdView, riderName is empty");
            throw new BizException("wmOrderIdView, riderName cannot be empty");
        }
        // 骑手电话为空 需要单独查询订单骑手隐私号 通过lion手动控制是否需要走填充电话逻辑
        if(isFillRiderPhoneFlag && MccConfigUtil.getMtRiderTransferRiderFillRiderPhoneSwitch()){
            // 由于骑手转派目前不返回骑手电话字段 所有需要单独查询订单骑手隐私号
            riderPhone = getRiderPrivacyPhoneByOrderLogisticsStatusInfo(request, tenantId, storeId, wmOrderIdView, riderName);
        }
        return PaoTuiRiderTransferDTO.builder()
            .appPoiCode(appPoiCode)
            .wmOrderIdView(wmOrderIdView)
            .riderName(riderName)
            .riderPhone(riderPhone)
            .timestamp(timestamp)
            .build();
    }

    /**
     * 根据APPID，订单ID等查询骑手隐私号码
     * @param request 请求参数
     * @param tenantId 租户ID
     * @param storeId 门店ID
     * @param wmOrderIdView 订单ID
     * @param newRiderName 当前转派通知中的骑手姓名 用于交验 如果和查询到的订单骑手名称不一致抛异常
     * @return
     */
    private String getRiderPrivacyPhoneByOrderLogisticsStatusInfo(OrderNotifyRequest request, Long tenantId, Long storeId, String wmOrderIdView, String newRiderName) {
        // 如果发现主从延迟严重 设置睡眠时间
        if (null != MccConfigUtil.getRiderPrivacyPhoneThreadSleepMillis() && MccConfigUtil.getRiderPrivacyPhoneThreadSleepMillis() > 0) {
            try {
                Thread.sleep(MccConfigUtil.getRiderPrivacyPhoneThreadSleepMillis());
            } catch (Exception e) {
                log.info("getRiderPrivacyPhoneByorderLogisticsStatusInfo sleep error", e);
            }
        }
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        GetLogisticsStatusRequest logisticsStatusRequest = new GetLogisticsStatusRequest();
        logisticsStatusRequest.setChannelId(channelTypeEnum.getCode());
        logisticsStatusRequest.setTenantId(tenantId);
        logisticsStatusRequest.setStoreId(storeId);
        logisticsStatusRequest.setOrderId(wmOrderIdView);
        GetLogisticsStatusResult result = getHealthChannelOrderService().getLogisticsStatus(logisticsStatusRequest);
        ResultStatus resultStatus = result.getStatus();
        if (resultStatus.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new BizException("查询订单获取骑手隐私号失败，转单逻辑异常。异常原因：" + resultStatus.getMsg());
        }
        // 调用接口拿到的最新骑手信息
        LogisticsStatusDTO logisticsStatusDTO = result.getLogisticsStatus();
        if (null == logisticsStatusDTO) {
            throw new BizException("查询订单获取骑手隐私号失败，转单逻辑异常。未获取到logisticsStatusDTO数据");
        }
        // 最新骑手名称为空
        if(StringUtils.isBlank(logisticsStatusDTO.getRiderName())){
            throw new BizException("查询订单获取到的最新骑手名称为空，转单逻辑异常。");
        }
        // 最新骑手电话为空
        if(StringUtils.isBlank(logisticsStatusDTO.getRiderPhone())){
            throw new BizException("查询订单获取到的最新骑手联系电话为空，转单逻辑异常。");
        }
        // 如果订单骑手名称与转单骑手名称不一致抛异常(如果订单骑手名称与转单骑手名称一致，但是电话号码不一致，由于主从延迟取到了旧电话号码，该情况代码逻辑无法判断 不处理)
        if (StringUtils.isBlank(newRiderName) || !logisticsStatusDTO.getRiderName().trim().equals(newRiderName.trim())) {
            Cat.logEvent(ProjectConstant.MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR, "查询订单获取到的最新骑手名称与转单骑手名称不一致，转单逻辑异常。");
            throw new BizException("查询订单获取到的最新骑手名称与转单骑手名称不一致，转单逻辑异常。订单骑手名称：" + logisticsStatusDTO.getRiderName().trim() + ", 转单消息骑手名称：" + newRiderName);
        }
        return logisticsStatusDTO.getRiderPhone();
    }
}