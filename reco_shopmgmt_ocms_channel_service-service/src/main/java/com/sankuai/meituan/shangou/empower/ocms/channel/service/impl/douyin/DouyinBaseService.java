package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.doudian.open.core.AccessToken;
import com.doudian.open.core.AccessTokenBuilder;
import com.doudian.open.core.DoudianOpConfig;
import com.doudian.open.core.DoudianOpRequest;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.service.inf.kms.client.Kms;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTenantSysParams;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.DouyinConfigDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.DouyinChannelCommonService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;


/**
 * @program: reco_shopmgmt_ocms_channel_service
 * @description: 抖音基础服务工具类
 * @author: jinyi
 * @create: 2024-01-26 18:56
 **/
@Slf4j
@Service
public class DouyinBaseService {

    // ocms的appkey
    private static final String OCMS_APP_KEY = "com.sankuai.shangou.empower.ocms";

//    @Autowired
//    private DefaultYZClient yzClient;
//    @Autowired
//    private YzToolAccessTokenService accessTokenService;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    DouyinChannelCommonService douyinChannelCommonService;

//    @Resource
//    private ClusterRateLimiter clusterRateLimiter;

//    final LoadingCache<AppMessage, SecretClient> SECRET_CLIENT_CACHE = CacheBuilder.newBuilder()
//            //本地最多缓存5000个应用
//            .maximumSize(5000)
//            //缓存20分钟实效
//            .expireAfterAccess(10, TimeUnit.MINUTES)
//            //加载token
//            .build(new CacheLoader<AppMessage, SecretClient>() {
//                @Override
//                public SecretClient load(AppMessage key) {
//                    try {
//                        return new SecretClient(key.getClientId(), key.getClientSecret());
//                    }
//                    catch (DataSecurityException e) {
//                        throw new BizException("SecretClient init error,excption:", e);
//                    }
//                }
//            });

    /**
     * 有赞基础调用
     * @param appMessage  应用信息用于换取token
     * @param api         请求参数及接口
     * @param clz         返回结果
     * @param <T>
     * @return
     * @throws SDKException
     */
//    public <T> T getResult4YouZan(AppMessage appMessage,API api, Class<T> clz) throws SDKException {
//        Stopwatch stopwatch = Stopwatch.createStarted();
//        try {
//            log.info("YouZanToolBaseService request appMessage{}, api{}", appMessage, JSON.toJSONString(api));
//            T result = yzClient.invoke(api, new Token(accessTokenService.getAccessToken(appMessage)), clz);
//            log.info("YouZanToolBaseService api: {} response: {}", api.getName(), JSON.toJSONString(result));
//            return result;
//        }
//        catch (SDKException sdkEx) {
//            throw sdkEx;
//        } finally {
//            long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
//            MetricUtils.performance(MetricConstant.HTTP_NAME, MetricUtils.genUrlTagMap(api.getName()), elapsed);
//            log.info("有赞渠道发送API请求的api:{}, cost={}", api.getName(), elapsed);
//        }
//    }

    /**
     * 有赞基础调用，本接口会进行限频操作，使用该接口，先查看当前接口是否存在限频配置
     * @param appMessage  应用信息用于换取token
     * @param api         请求参数及接口
     * @param clz         返回结果
     * @param <T>
     * @return
     * @throws SDKException
     */
//    public <T> T getResult4YouZanByRhino(ChannelPostYZEnum postYZEnum, AppMessage appMessage, API api, Class<T> clz) throws SDKException {
//        // 限频控制
//        if (MccConfigUtil.isGrayYzRhinoConfig() && !clusterRateLimiter.tryAcquire(postYZEnum, appMessage.getGrantId())) {
//            log.warn("当前有赞请求触发限频，请稍后重试！api [{}].", api.getName());
//            throw new BizException(ResultCode.TRIGGER_LIMIT.getCode(), "当前有赞请求接口过于频繁，请稍后重试！");
//        }
//        return getResult4YouZan(appMessage, api, clz);
//    }

//    /**
//     * 连锁总店 AppMessage
//     * @param tenantId
//     * @return
//     */
//    public AppMessage getMainAppMessage(Long tenantId) {
//        CopAccessConfigDO tenantChannelConfig = copAccessConfigService.findTenantChannelConfig(tenantId, ChannelTypeEnum.YOU_ZAN.getCode());
//        if (tenantChannelConfig == null) {
//            throw new BizException(ResultCode.CHANNEL_APP_ID_INVALID.getCode(), "租户未绑定");
//        }
//        String sysParamJson = tenantChannelConfig.getSysParams();
//        JSONObject sysParam = JSON.parseObject(sysParamJson);
//        return AppMessage.builder()
//                .clientId(tenantChannelConfig.getTenantAppId())
//                .grantId(sysParam.getString(ProjectConstant.YZ_GRANT_ID))
//                .clientSecret(sysParam.getString(ProjectConstant.SECRET))
//                .build();
//    }

    /**
     * AppMessage
     * @param tenantId
     * @param storeId
     * @return
     */
    public AccessToken getAccessToken(Long tenantId, Long storeId) {
        return AccessTokenBuilder.parse("ebaa2730-1642-446a-8f7c-b447b204ca3b");

//        CopAccessConfigDO tenantChannelConfig = copAccessConfigService.findTenantChannelConfig(tenantId, ChannelTypeEnum.YOU_ZAN.getCode());
//        if (tenantChannelConfig == null) {
//            throw new BizException(ResultCode.CHANNEL_APP_ID_INVALID.getCode(), "租户未绑定");
//        }
//        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(tenantId, ChannelTypeEnum.DOU_YIN.getCode(), storeId);
//        if (channelStore == null) {
//            throw new BizException(ResultCode.CHANNEL_POI_CODE_INVALID.getCode(), "门店未绑定");
//        }
//        return AccessTokenBuilder.parse(JSON.parseObject(tenantChannelConfig.getSysParams()).getString(ProjectConstant.SECRET));
    }

    public AccessToken prepareRequestConfigAndToken(DoudianOpRequest<?> request, long tenantId) {
        // Map<String, String> tenantInfo = Lion.getConfigRepository().getMap("config.douyin.testPoiInfo");
        DouyinTenantSysParams sysParams = null;
        try {
            sysParams = douyinChannelCommonService.getTenantSysParamsByTenantId(tenantId);
            log.info("获取租户信息成功({}), req: {}", tenantId, request);
        } catch (Exception e) {
            log.error("获取抖音租户系统参数为空({})", tenantId, e);
        }
        if (sysParams == null) {
            log.error("获取抖音租户系统参数为空({})", tenantId);
            throw new BizException(ResultCode.INVALID_PARAM.getCode(), "获取抖音租户系统参数为空");
        }
        DoudianOpConfig config = new DoudianOpConfig();
        config.setAppKey(sysParams.getAppKey());
        config.setAppSecret(sysParams.getSecret());
        request.setConfig(config);
        return AccessTokenBuilder.parse(sysParams.getAccessToken());
    }

    public DouyinConfigDto getRequestConfigAndToken(Long tenantId) {
        DouyinTenantSysParams sysParams = null;
        try {
            sysParams = douyinChannelCommonService.getTenantSysParamsByTenantId(tenantId);
        } catch (Exception e) {
            log.error("获取抖音租户系统参数为空({})", tenantId, e);
        }
        if (sysParams == null) {
            log.error("获取抖音租户系统参数为空({})", tenantId);
            throw new BizException(ResultCode.INVALID_PARAM.getCode(), "获取抖音租户系统参数为空");
        }
        DoudianOpConfig config = new DoudianOpConfig();
        config.setAppKey(sysParams.getAppKey());
        config.setAppSecret(sysParams.getSecret());
        return DouyinConfigDto.builder().doudianOpConfig(config)
                .accessToken(AccessTokenBuilder.parse(sysParams.getAccessToken())).build();
    }

    /**
     * 通过appKey获取secret
     *
     * @param appKey
     * @return
     */
    public String getSecretByAppKey(String appKey) {
        if (StringUtils.isBlank(appKey)) {
            return null;
        }

        // 跨服务访问kms，需要开启主机验证
        String appKeySecretJson = Kms.getByNameWithoutErrorLog(OCMS_APP_KEY,
                ProjectConstant.DOUYIN_APPKEY_SECRET_KMS_KEY);
        if (StringUtils.isBlank(appKeySecretJson)) {
            log.info("抖音渠道的appKey、secret未在kms配置");
            return null;
        }
        Map<String, String> appKeySecretMap = JacksonUtils.parseMap(appKeySecretJson, String.class, String.class);
        String secret = appKeySecretMap.get(appKey);
        if (secret == null) {
            log.info("getSecretByAppKey 查找secret未配置，appKey={}", appKey);
        }

        return secret;
    }

}
