package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import scala.util.parsing.combinator.testing.Str;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;

@Service
@Slf4j
public class NewSupplyOrderCacheService {
    @Resource(name = "redisSgNewSupplyOfc")
    private RedisStoreClient redisStoreClient;

    // 需要与orderbiz中的同字段值相同
    private static final String CHANGE_ORDER_LOCK_CATEGORY = "new_supply_change_order_lock";

    // 需要与orderbiz中的同字段值相同
    private static final String CHANGE_ORDER_CHANGE_KEY = "_change_key";



    public Boolean setChangeOrderNxValue(String key, String value, int expireTime) {
        try {
            StoreKey storeKey = new StoreKey(CHANGE_ORDER_LOCK_CATEGORY, key);
            return redisStoreClient.setnx(storeKey, value, expireTime);
        } catch (Exception e) {
            log.error("newSupply setChangeOrderNxValue redis set error", e);
        }
        return false;
    }

    // 需要与orderbiz中的取值方法相对应
    public void setChangeOrderChangeValue(String channelOrderId, Integer channelId, String value, int expireTime) {
        try {
            StoreKey storeKey = new StoreKey(CHANGE_ORDER_LOCK_CATEGORY, channelOrderId + "_" + channelId + CHANGE_ORDER_CHANGE_KEY);
            redisStoreClient.setnx(storeKey, value, expireTime);
        } catch (Exception e) {
            log.error("newSupply setChangeOrderJsonValue redis set error", e);

        }
    }

    public void setValue(StoreKey storeKey, String value, int expireTime) {
        try {
            redisStoreClient.set(storeKey, value, expireTime);
        } catch (Exception e) {
            log.error("newSupply order redis set error", e);

        }
    }

    public Boolean isExistStoreKey(StoreKey storeKey) {
        try {
            return redisStoreClient.exists(storeKey);
        } catch (Exception e) {
            log.error("newSupply order redis isExistStoreKey error", e);
            return false;
        }
    }
}
