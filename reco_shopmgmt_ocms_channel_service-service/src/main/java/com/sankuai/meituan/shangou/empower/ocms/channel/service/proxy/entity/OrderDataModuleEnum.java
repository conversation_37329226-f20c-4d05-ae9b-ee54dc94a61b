package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

public enum OrderDataModuleEnum {
    Detail(0,"订单商品详情"),
    Extras(1,"订单优惠信息"),
    SkuBenefitDetail(2,"商品优惠详情"),
    UserMemberInfo(3,"订单用户会员信息"),
    PoiReceiveDetail(4,"订单纬度的商家对账信息"),
    PoiReceiveDetailYuan(5,"订单纬度的商家对账信息(元)"),
    RecipientAddress(6,"订单收货人地址"),
    LogisticsCode(7,"订单配送方式"),
    OpenUid(8,"开放平台用户id"),
    Food(9,"部分退款商品信息"),
    LogisticsInfo(10,"退货退款物流信息"),
    OrderInfo(11,"部分订单基本信息(包括订单优惠信息、订单商品详情、门店信息等)"),
    ProductSkuInfo(12,"sku信息"),
    ProductSpuInfo(13,"spu信息"),
    ProductInfo(14,"商品"),
    Replace2OriginPrice(15,"替换折扣价为原价");

    OrderDataModuleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
