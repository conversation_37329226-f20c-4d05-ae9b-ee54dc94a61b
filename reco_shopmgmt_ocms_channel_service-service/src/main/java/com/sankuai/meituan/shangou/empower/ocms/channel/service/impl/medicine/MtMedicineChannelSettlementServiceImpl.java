package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelSettlementServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 美团医药结算业务接口实现
 *
 * <AUTHOR>
 * @since 2021/3/1
 */
@Service("mtMedicineChannelSettlementService")
public class MtMedicineChannelSettlementServiceImpl extends MtChannelSettlementServiceImpl {

    @Autowired
    private MtMedicineChannelGateService mtMedicineChannelGateService;

    @Override
    public BaseChannelGateService getChannelGateService() {
        return mtMedicineChannelGateService;
    }
}