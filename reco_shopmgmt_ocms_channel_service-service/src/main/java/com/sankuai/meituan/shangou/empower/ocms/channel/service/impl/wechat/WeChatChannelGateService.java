package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.wechat;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostWeiXinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.HttpClientUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * 请求微信开放平台
 * <AUTHOR>
 */
@Slf4j
@Service
public class WeChatChannelGateService extends BaseChannelGateService {

	@Value("${weixin.url.base}")
	private String baseUrl;

	@Override
	protected <T> T postToChannel(String channelPoiCode, String channelOnlinePoiCode, ChannelPostInter postUrlEnum, Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest) {
		return null;
	}

	@Override
	public Map<String, Object> sendPost(String url, String method, BaseRequest baseRequest, Object bizParam, Map<String, Object> systemParam) {
		// 参数校验
		validateSendPost(url, method, baseRequest, bizParam);

		// 参数实体转换为Map
		Map<String, Object> bizParamMap = null;
		try {
			if (bizParam != null) {
				if (bizParam instanceof Map) {
					bizParamMap = (Map<String, Object>) bizParam;
				} else {
					bizParamMap = ConverterUtils.convertToMap(bizParam, baseRequest.getChannelId());
				}
			}
		} catch (Exception e) {
			log.error("BaseChannelGateService.sendPost error", e);
		}

		// 拼接参数
		Map<String, Object> postParam = generatePostParams(url, method, systemParam, bizParamMap);
		postParam.remove("access_token");

		// post请求需要拼接token作为url
		try {
			url += URLEncoder.encode("" + systemParam.get("access_token"), Charsets.UTF_8.displayName());
		} catch (UnsupportedEncodingException e) {
			log.error("url encode error", e);
		}

		String result = HttpClientUtil.postJson(url,
			MccConfigUtil.getHttpConnectionTimeOut(), MccConfigUtil.getHttpSocketTimeOut(), JSON.toJSONString(postParam));
		log.info("WeChatChannelGateService.postUrl(), http post, url:{}, baseRequest:{}, postParam:{}, result:{}", url, baseRequest, postParam, result);
		return JSON.parseObject(result);
	}

	@Override
	public Map<String, Object> sendGet(String url, String method, BaseRequest baseRequest, Object bizParam) {
		// 参数校验
		validateSendPost(url, method, baseRequest, bizParam);

		// 参数实体转换为Map
		Map<String, Object> bizParamMap = null;
		if (!(bizParam instanceof Map)) {
			try {
				bizParamMap = ConverterUtils.getProperty(bizParam);
			} catch (Exception e) {
				log.error("sendGet 获取bizParamMap异常", e);
			}
		} else {
			bizParamMap = (Map<String, Object>) bizParam;
		}

		// 获取系统参数
		Map<String, Object> sysParam = getChannelSysParams(baseRequest);

		// 拼接参数
		Map<String, String> postParam = generatePostParams(url, method, sysParam, bizParamMap);

		// 请求
		return getUrl(url, baseRequest, postParam);
	}

	@Override
	protected Map generatePostParams(String url, String method, Map<String, Object> sysParam, Map<String, Object> bizParam) {
		if (isUpdateToken(url)){
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("appid", sysParam.get("appid"));
			paramMap.put("secret", sysParam.get("secret"));
			paramMap.put("grant_type", "client_credential");
			return paramMap;
		}
		return generatePostParams(sysParam, bizParam);
	}

	private boolean isUpdateToken(String url) {
		return StringUtils.contains(url, ChannelPostWeiXinEnum.UPDATE_TOKEN.getUrl());
	}

	private Map<String, Object> generatePostParams(Map<String, Object> sysParam, Map<String, Object> bizParam) {
		HashMap<String, Object> paramMap = new HashMap<>(sysParam);
		paramMap.remove("appid");
		paramMap.remove("secret");
		paramMap.putAll(bizParam);
		return paramMap;
	}

	@Override
	public String getPostUrl(ChannelPostInter postUrlEnum) {
		return baseUrl + postUrlEnum.getUrl();
	}

}
