package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.tenant;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.meituan.shangou.saas.tenant.highlevelclient.QnhBizModes;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.TenantThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.QnhTenantMigrateDto;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.TenantBaseDto;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.request.OldQnhSearchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.QnhTenantMigrateListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.TenantBaseResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.tenant.HasErpBo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TenantBizModeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.sgfnqnh.poi.api.client.thrift.ChannelPoiThriftService;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.ChannelPoiBaseInfoDTO;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.response.PoiChannelPoiRelationResponse;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2022-03-23 17:29
 */
@Service
@Slf4j
@Setter
@Rhino
public class TenantRemoteService {
    @Resource
    private ConfigThriftService configThriftService;

    @Resource
    private TenantThriftService tenantThriftService;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private ChannelPoiThriftService channelStoreThriftService;

    @Resource
    private QnhBizModes qnhBizModes;

    private final Cache<String, Object> LOCAL_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();
    private static final String ERP_KEY = "ERP";

    public TenantBizModeEnum getTenantBizMode(Long tenantId) {
        try {
            if (tenantId == null || tenantId <= NumberUtils.LONG_ZERO) {
                return TenantBizModeEnum.NOT_EXIST;
            }
            String bizMode = qnhBizModes.qnhBizMode(tenantId);
            return TenantBizModeEnum.codeOf(bizMode);
        }
        catch (Exception e) {
            throw new ChannelBizException("search tenant biz mode error, tenantId:" + tenantId);
        }

    }

    public boolean isErpSpuTenant(Long tenantId) {
        String key = String.format("%s_%d", ERP_KEY, tenantId);
        try {
            Object result = LOCAL_CACHE.get(key, () -> queryTenantHasErp(tenantId));
            return (boolean) result;
        } catch (ExecutionException e) {
            log.error("获取ERP租户配置异常 tenantId:{}", tenantId, e);
            throw new IllegalStateException("获取ERP租户配置异常", e);
        }
    }
    public boolean queryTenantHasErp(long tenantId) {
        ConfigDto configDto = queryTenantConfig(tenantId, tenantId, ConfigItemEnum.HAS_ERP);
        if (configDto == null || StringUtils.isBlank(configDto.getConfigContent())) {
            return false;
        }
        return ConfigItemEnum.HAS_ERP.isMainConfigYesStr(configDto.getConfigContent());
    }
    public ConfigDto queryTenantConfig(long tenantId, long subjectId, ConfigItemEnum configItemEnum) {
        ConfigQueryRequest request = new ConfigQueryRequest();
        request.setTenantId(tenantId);
        request.setSubjectId(subjectId);
        request.setConfigId(configItemEnum.getConfigId());
        TenantConfigResponse response;
        try {
            response = configThriftService.queryTenantConfig(request);
            log.info("查询租户配置 request:{},response:{}", request, response);
        } catch (Exception e) {
            log.error("查询租户配置异常 request:{}", request, e);
            throw new BizException("查询租户配置异常", e);
        }

        if (response == null || response.getStatus() == null) {
            throw new BizException("未返回结果");
        }

        if (response.getStatus().getCode() != 0) {
            throw new BizException(response.getStatus().getMessage());
        }

        return response.getConfig();
    }


    public boolean isErpTenant(Long tenantId) {
        try {
            String config = queryConfig(tenantId, tenantId, ConfigItemEnum.HAS_ERP.getKey());
            if (StringUtils.isBlank(config)) {
                return false;
            }
            HasErpBo hasErpBo = com.meituan.linz.boot.util.JacksonUtils.parse(config, HasErpBo.class);
            if (hasErpBo == null) {
                return false;
            }
            return hasErpBo.checkHasErp();
        }catch (Exception e){
            return false;
        }

    }

    /**
     * 查询租户配置
     * @param tenantId
     * @param subjectId
     * @param configId
     * @return
     */
    public String queryConfig(Long tenantId, Long subjectId, Integer configId) {
        try {
            ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
            configQueryRequest.setTenantId(tenantId);
            configQueryRequest.setSubjectId(subjectId);
            configQueryRequest.setConfigId(configId);
            TenantConfigResponse response = configThriftService.queryTenantConfig(configQueryRequest);
            log.info("TenantRemoteService.queryConfig 查询租户配置 request:{}, response:{}", configQueryRequest, response);
            return ConverterUtils.nonNullConvert(response.getConfig(), ConfigDto::getConfigContent);
        }catch (Exception e){
            log.error("search tenant config info exception,tenantId:{}, ", tenantId, e);
            return null;
        }


    }

    public List<QnhTenantMigrateDto> queryTenantByOldQnhCondition(OldQnhSearchRequest request) {
        try {
            QnhTenantMigrateListResponse response = tenantThriftService.queryTenantByOldQnhCondition(request);
            if (response == null || response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                log.error("queryTenantByOldQnhCondition结果为空或code不为0, request={}, response={}",
                        com.meituan.linz.boot.util.JacksonUtils.toJson(request),
                        com.meituan.linz.boot.util.JacksonUtils.toJson(response));
                throw new BizException(StatusCodeEnum.FAIL.getCode(),
                        "tenantThriftService.queryTenantByOldQnhCondition");
            }
            return response.getTenantList();
        } catch (Exception e) {
            log.error("queryTenantByOldQnhCondition error, request = {}, ", request, e);
            return new ArrayList<>();
        }
    }

    public TenantBaseDto queryTenantBaseInfo(Long tenantId) {
        try {
            TenantBaseResponse response = tenantThriftService.getTenantBase(tenantId);
            if (response == null || response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                log.error("getTenantBase结果为空或code不为0, request={}, response={}", tenantId,response);
                throw new BizException(StatusCodeEnum.FAIL.getCode(),
                        "tenantThriftService.queryTenantByOldQnhCondition");
            }
            return response.getTenantBaseDto();
        } catch (Exception e) {
            log.error("getTenantBase error, request = {}, ", tenantId, e);
            return null;
        }
    }

    /**
     * 查询数据库获取租户ID
     */
    public Long getTenantIdParam(int channelId, String tenantAppId) {
        Long tenantId = copAccessConfigService.selectTenantId(channelId, tenantAppId);
        Long qnhTenantId = MccConfigUtil.getQnhTenantId(tenantAppId);
        if (qnhTenantId != 0L) {
            log.info("tenantId:{},qnhTenantId:{}", tenantId, qnhTenantId);
            tenantId = qnhTenantId;
        }
        if (Objects.isNull(tenantId)) {
            log.error("ChannelOrderCallbackServiceImpl.getTenantIdParam, 未获取到渠道租户ID, channelId:{}, tenantAppId:{}",
                    channelId, tenantAppId);
            return 0L;
        }
        return tenantId;
    }

    /**
     * 查询数据库获取租户ID
     */
    public Long getTenantIdParamByChannelPoiCode(int channelId, String channelPoiCode) {
        ChannelStoreDO channelStoreDO = copChannelStoreService.selectByChannelPoiCode(channelId, channelPoiCode);

        if (Objects.isNull(channelStoreDO) || Objects.isNull(channelStoreDO.getTenantId())) {
            log.error("copChannelStoreService.selectByChannelPoiCode, 未获取到渠道租户ID, channelId:{}, channelPoiCode:{}",
                    channelId, channelPoiCode);
            return 0L;
        }
        return channelStoreDO.getTenantId();
    }

    /**
     * 适用于高频查询渠道门店信息的场景，基础运营侧的实现是从缓存查
     */
    @Degrade(
            rhinoKey = "TenantRemoteService.batchQueryPoiChannelPoiRelationByPoiIds",
            fallBackMethod = "batchQueryPoiChannelPoiRelationByPoiIdsFallback", timeoutInMilliseconds = 2000
    )
    public List<ChannelPoiBaseInfoDTO> batchQueryPoiChannelPoiRelationByPoiIds(Integer channelId, Long tenantId, List<Long> poiIds) {
        try {
            PoiChannelPoiRelationResponse response = channelStoreThriftService.queryPoiChannelPoiRelationByPoiIds(channelId, tenantId, poiIds);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                log.error("batchQueryPoiChannelPoiRelationByPoiIds, response or status is null, channelId:{}, tenantId:{}, poiIds:{}", channelId, tenantId, poiIds);
                return Collections.emptyList();
            }
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                log.error("batchQueryPoiChannelPoiRelationByPoiIds, response failed, channelId:{}, tenantId:{}, poiIds:{}", channelId, tenantId, poiIds);
                return Collections.emptyList();
            }

            return response.getChannelPoiBaseInfoDTOList();
        } catch (Exception e) {
            log.error("batchQueryPoiChannelPoiRelationByPoiIds error, channelId:{}, tenantId:{}, poiIds:{}", channelId, tenantId, poiIds);
            return Collections.emptyList();
        }
    }

    public List<ChannelPoiBaseInfoDTO> batchQueryPoiChannelPoiRelationByPoiIdsFallback(Integer channelId, Long tenantId, List<Long> poiIds) {
        log.error("batchQueryPoiChannelPoiRelationByPoiIdsFallback, channelId:{}, tenantId:{}, poiIds:{}", channelId, tenantId, poiIds);
        return Collections.emptyList();
    }

    @Degrade(
            rhinoKey = "TenantRemoteService.queryTenantAddNonSaleGiftToDetailSwitch",
            fallBackMethod = "queryTenantAddNonSaleGiftToDetailSwitchFallback", timeoutInMilliseconds = 2000
    )
    public boolean queryTenantAddNonSaleGiftToDetailSwitch(long tenantId) {
        try {
            ConfigDto configDto = queryTenantConfig(tenantId, tenantId, ConfigItemEnum.DISPLAY_NON_SALE_GIFTS_IN_ORDER);
            if (configDto != null && StringUtils.isNotBlank(configDto.getConfigContent())) {
                // 匹配为No则认为不需要加入，返回false，其余都为true
                return !ConfigItemEnum.DISPLAY_NON_SALE_GIFTS_IN_ORDER.isMainConfigNoStr(configDto.getConfigContent());
            }
        } catch (Exception e) {
            log.error("查询租户非在售商品加入订单详情配置出错 tenantId:{}", tenantId, e);
        }
        // 默认为true
        return true;
    }

    public boolean queryTenantAddNonSaleGiftToDetailSwitchFallback(long tenantId) {
        log.error("queryTenantAddNonSaleGiftToDetailSwitchFallback, tenantId:{}", tenantId);
        // 默认为true
        return true;
    }

}
