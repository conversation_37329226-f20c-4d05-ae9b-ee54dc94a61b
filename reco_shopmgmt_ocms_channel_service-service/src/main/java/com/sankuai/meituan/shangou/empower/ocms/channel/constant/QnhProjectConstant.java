package com.sankuai.meituan.shangou.empower.ocms.channel.constant;

public final class QnhProjectConstant {
    private QnhProjectConstant() {
    }

    //订单号
    public static final String ORDER_ID = "channel_sheetno";
    //单据状态
    public static final String STATUS = "status";
    //拣货状态
    public static final String PICKUP_STATUS = "pickup_status";
    //单据类型：1（订单）2（退货单)/退款类型（1：整单退，2：部分退）
    public static final String TYPE = "type";
    //退单号
    public static final String RETURN_ORDER_ID = "return_order_sn";
    //渠道编码
    public static final String CHANNEL_KEYWORD = "channel_keyword";
    //审核结果
    public static final String IS_AGREE = "is_agree";
    //门店编码
    public static final String REGION_CODE = "region_code";
    //备注
    public static final String REMARK = "remark";
    //原因
    public static final String REASON = "reason";
    //原因code
    public static final String REASON_CODE = "reason_code";
    //物品主键
    public static final String ITEM_ID = "id";
    //sku id
    public static final String SKU_ID = "item_code";
    //spu id
    public static final String SPU_ID = "spu_code";
    //售价
    public static final String SALE_PRICE = "sale_price";
    //实拣重量
    public static final String PRACTICAL_WEIGHT = "practical_weight";
    //商品明细
    public static final String ITEMS = "items";
    //商品明细
    public static final String ITEM = "item";
    //退单方式（40：退货退款，10：仅退款）
    public static final String WAY = "way";
    //商品数量
    public static final String SALE_QTY = "sale_qty";
    //商品条码
    public static final String UPC = "barcode";
    //配送状态
    public static final String DELIVERY_STATUS = "delivery_status";
    //配送员
    public static final String DELIVERY_MAN = "delivery_man";
    //配送员手机号
    public static final String DELIVERY_PHONE = "delivery_phone";
    //经度（高德坐标系）
    public static final String LNG = "lng";
    //纬度（高德坐标系）
    public static final String LAT = "lat";
    //页码
    public static final String PAGE_NO = "page_no";
    //每页条数
    public static final String PAGE_SIZE = "page_size";
    //开始时间
    public static final String START_TIME = "start_time";
    //结束时间
    public static final String END_TIME = "end_time";
    //库存
    public static final String STOCK = "stock";
    //牵牛花id
    public static final String QNH_ID = "qnhid";
    //同item_code
    public static final String QNH_SKU_ID = "skuid";

    public static final String ACTIVITY_GIFT_DESC = "赠品";

    public static final String THRID_CARRIER_ORDER_ID = "third_carrier_order_id";

    public static final String LOGISTICS_PROVIDER_CODE = "logistics_provider_code";


    /**
     * 业务身份key
     * **/
    public static final String BUSINESS_KEY = "QNH_BIZ_MODE";
}
