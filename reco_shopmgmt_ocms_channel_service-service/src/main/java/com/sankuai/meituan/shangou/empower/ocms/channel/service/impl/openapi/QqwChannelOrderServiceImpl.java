package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.openapi;

import com.dianping.cat.util.StringUtils;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ChannelOrderIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderListReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderSearchResponseV2;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSDeliveryInfoVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderStatusLog;
import com.meituan.shangou.saas.order.management.client.enums.RecoBusinessIdEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchThriftServiceV2;
import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.KeyConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.openapi.OpenResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.openapi.OrderStatusParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.openapi.SelfDeliveryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.AfsAuditStageEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.AfsReviewTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.CreateAggDeliveryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.CreateAggDeliveryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelOrderThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelOrderStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.sgopen.push.domain.QnhOpenPushSystemParam;
import com.sankuai.meituan.shangou.sgopen.push.domain.QnhPushResponse;
import com.sankuai.meituan.shangou.sgopen.push.enums.QnhOpenOperatorEnum;
import com.sankuai.meituan.shangou.sgopen.push.enums.QnhOpenPushTypeEnum;
import com.sankuai.meituan.shangou.sgopen.push.service.QnhOpenPushThriftService;
import com.sankuai.meituan.shangou.sgopen.push.utils.ParamConverter;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("qqwChannelOrderService")
public class QqwChannelOrderServiceImpl implements ChannelOrderService {
    @Resource
    private OcmsOrderSearchThriftServiceV2 orderSearchThriftServiceV2;

    @Resource
    private QnhOpenPushThriftService qnhOpenPushThriftService;

    @Resource
    private CommonLogger log;

    @Resource
    private MccConfigUtil mccConfigUtil;

    private static String OPEN_PUSH_SWITCH_CONFIG = "open_push_switch_config";



    @Override
    public ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_STATUS_UPDATE.getCode());
        qnhOpenPushSystemParam.setTenantId(request.getTenantId());
        qnhOpenPushSystemParam.setStoreId(request.getStoreId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());

        OrderStatusParam orderStatusParam = OrderStatusParam.builder()
                .channel_sheetno(request.orderId)
                .status("4").poi_id(request.getStoreId()).build();

        log.info("poiConfirmOrder, sysParam: {}, data:{}", qnhOpenPushSystemParam, orderStatusParam);

        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)
                || mccConfigUtil.isMockUpOp(request.getTenantId(), request.getChannelId(), "poiConfirmOrder")) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }

        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(orderStatusParam)));

            if (qnhPushResponse == null) {
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }

            if (qnhPushResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }
        } catch (Exception e) {
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
        GetChannelOrderDetailResult result = new GetChannelOrderDetailResult();
        try {
            OcmsOrderSearchResponseV2 ocmsOrderSearchResponse = orderSearchThriftServiceV2.orderList(
                    buildOrderQueryRequest(
                            request.getOrderId(),
                            request.getChannelId(),
                            request.getSotreId(),
                            request.getTenantId()
                    ));
            OCMSOrderVO ocmsOrderVO = ocmsOrderSearchResponse.getOcmsOrderList().stream().findAny().orElseThrow(()-> StatusCodeEnum.ORDER_NOT_FOUNT.toBizException());
            return result.setStatus(ResultGenerator.genSuccessResult()).setChannelOrderDetail(buildChannelOrderDetailDTO(ocmsOrderVO));
        } catch (TException e) {
            log.error("getChannelOrderDetial error:{}",e.getMessage());
            result.setStatus(ResultGenerator.genFailResult(e.getMessage()));
        } catch (BizException e){
            log.error("getChannelOrderDetail error:{}",e.getMessage());
            result.setStatus(ResultGenerator.genFailResult(e.getMessage()));
        }
       return result;
    }

    private ChannelOrderDetailDTO buildChannelOrderDetailDTO(OCMSOrderVO ocmsOrderVO) {
        ChannelOrderDetailDTO channelOrderDetailDTO = new ChannelOrderDetailDTO();
        channelOrderDetailDTO.setChannelId(ocmsOrderVO.getOrderBizType() >= 2000 ? ocmsOrderVO.getOrderBizType() : ChannelTypeEnum.QUAN_QIU_WA.getCode());
        channelOrderDetailDTO.setChannelOrderId(ocmsOrderVO.getViewOrderId());
        OrderDeliveryDetailDTO deliveryDetailDTO = new OrderDeliveryDetailDTO();

        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        if(ocmsDeliveryInfoVO!=null){
            deliveryDetailDTO.setUserPhone(ocmsDeliveryInfoVO.getUserPhone());
            deliveryDetailDTO.setUserPhoneIsValid(StringUtils.isNotEmpty(ocmsDeliveryInfoVO.getUserPhone()));
        }

        channelOrderDetailDTO.setDeliveryDetail(deliveryDetailDTO);

        Integer orderStatus = ocmsOrderVO.getOrderStatus();

        orderStatus = filterate2GetStatus(orderStatus, ocmsOrderVO);

        if (orderStatus == OrderStatusEnum.REFUND_APPLIED.getValue()) {
            throw new BizException("订单当前状态查找失败");
        }

        channelOrderDetailDTO.setStatus(orderStatusBiz2Mid(orderStatus));

        return channelOrderDetailDTO;
    }

    @Override
    public ResultStatus preparationMealComplete(PreparationMealCompleteRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_STATUS_UPDATE.getCode());
        qnhOpenPushSystemParam.setTenantId(request.getTenantId());
        qnhOpenPushSystemParam.setStoreId(request.getStoreId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());

        OrderStatusParam orderStatusParam = OrderStatusParam.builder()
                .channel_sheetno(request.orderId)
                .status("6").poi_id(request.getStoreId()).build();

        log.info("preparationMealComplete, sysParam: {}, data:{}", qnhOpenPushSystemParam, orderStatusParam);

        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)
                || mccConfigUtil.isMockUpOp(request.getTenantId(), request.getChannelId(), "preparationMealComplete")) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }

        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(orderStatusParam)));

            if (qnhPushResponse == null) {
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }

            if (qnhPushResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }
        } catch (Exception e) {
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request) {
        GetOrderStatusResult result = new GetOrderStatusResult();

        GetChannelOrderDetailRequest getChannelOrderDetailRequest = new GetChannelOrderDetailRequest();

        getChannelOrderDetailRequest.setTenantId(request.getTenantId());
        getChannelOrderDetailRequest.setChannelId(request.getChannelId());
        getChannelOrderDetailRequest.setOrderId(request.getOrderId());
        getChannelOrderDetailRequest.setSotreId(request.getStoreId());
        getChannelOrderDetailRequest.setAppId(request.getAppId());

        try {
            GetChannelOrderDetailResult channelOrderDetail = getChannelOrderDetail(getChannelOrderDetailRequest);
            if (channelOrderDetail != null && channelOrderDetail.getChannelOrderDetail() != null) {
                int status = channelOrderDetail.getChannelOrderDetail().getStatus();

                OrderStatusDTO orderStatusDTO = new OrderStatusDTO();

                orderStatusDTO.setOrderId(request.getOrderId());
                orderStatusDTO.setStatus(status);

                result.setStatus(ResultGenerator.genSuccessResult()).setOrderStatus(orderStatusDTO);
            }
        } catch (Exception e) {
            result.setStatus(ResultGenerator.genFailResult(e.getMessage()));
            log.error("getOrderStatus error:{}", e.getMessage());
        }

        return result;
    }

    @Override
    public GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request) {
        return null;
    }

    @Override
    public ResultStatus agreeRefund(AgreeRefundRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        String status;
        //todo 状态参数传递
        if (!StringUtils.isBlank(request.afterSaleId) && request.afterSaleId.startsWith(KeyConstant.OPEN_API_AFTER_SALE_ID_PREFIX)){
            qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_STATUS_UPDATE.getCode());
            status = "15";
        }else {
            qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_RETURN_APPLY_UPDATE.getCode());
            status = "6";
        }
        qnhOpenPushSystemParam.setTenantId(request.getTenantId());
        qnhOpenPushSystemParam.setStoreId(request.getStoreId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());

        OrderStatusParam orderStatusParam = OrderStatusParam.builder()
                .channel_sheetno(request.orderId)
                .return_order_sn(request.afterSaleId)
                .poi_id(request.getStoreId())
                .status(status).build();

        log.info("agreeRefund, sysParam: {}, data:{}", qnhOpenPushSystemParam, orderStatusParam);

        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }

        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(orderStatusParam)));

            if (qnhPushResponse == null) {
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }

            if (qnhPushResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }
        } catch (Exception e) {
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public ResultStatus rejectRefund(RejectRefundRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_RETURN_APPLY_UPDATE.getCode());
        qnhOpenPushSystemParam.setTenantId(request.getTenantId());
        qnhOpenPushSystemParam.setStoreId(request.getStoreId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());

        OrderStatusParam orderStatusParam = OrderStatusParam.builder()
                .channel_sheetno(request.orderId)
                .return_order_sn(request.afterSaleId)
                .status("4")
                .poi_id(request.getStoreId())
                .build();
        if (request.getAfterSaleId().startsWith("AFS-")){
            qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_STATUS_UPDATE.getCode());
            OcmsOrderSearchResponseV2 ocmsOrderSearchResponse = null;
            try {
                String status = getOpenApiStatus(request);
                orderStatusParam.setStatus(status);
            } catch (Exception e) {
                log.error("查询订单状态error e:{}",e);
            }

        }
        log.info("rejectRefund, sysParam: {}, data:{}", qnhOpenPushSystemParam, orderStatusParam);

        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }

        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(orderStatusParam)));

            if (qnhPushResponse == null) {
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }

            if (qnhPushResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }
        } catch (Exception e) {
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    private String getOpenApiStatus(RejectRefundRequest request) throws TException {
        OcmsOrderSearchResponseV2 ocmsOrderSearchResponse;
        ocmsOrderSearchResponse = orderSearchThriftServiceV2.orderList(
                buildOrderQueryRequest(
                        request.getOrderId(),
                        request.getChannelId(),
                        request.getStoreId(),
                        request.getTenantId()
                ));

        OCMSOrderVO ocmsOrderVO = ocmsOrderSearchResponse.getOcmsOrderList().stream().findAny().orElseThrow(()-> StatusCodeEnum.ORDER_NOT_FOUNT.toBizException());
        Integer orderStatus = ocmsOrderVO.getOrderStatus();

        orderStatus = filterate2GetStatus(orderStatus, ocmsOrderVO);
        if (ocmsOrderVO.getOcmsDeliveryInfoVO() != null){
            return convertOpenApiStatus(orderStatus,
                    ocmsOrderVO.getOcmsDeliveryInfoVO().getDeliveryStatus(),
                    ocmsOrderVO.getOcmsDeliveryInfoVO().getDistributeStatus());
        }
        return null;
    }

    private static Integer filterate2GetStatus(Integer orderStatus, OCMSOrderVO ocmsOrderVO) {
        if (orderStatus == OrderStatusEnum.REFUND_APPLIED.getValue()) {
            // 先排除掉source=22并且target==22的状态日志，取时间最新的一条
            Optional<OrderStatusLog> statusInLog = Optional.ofNullable(ocmsOrderVO.getOrderStatusLogList()).orElse(Collections.emptyList()).stream()
                    .filter(
                            e -> {
                                return !(e.getSourceStatus() == OrderStatusEnum.REFUND_APPLIED.getValue() && e.getTargetStatus() == OrderStatusEnum.REFUND_APPLIED.getValue());
                            }
                    ).sorted(Comparator.comparing(OrderStatusLog::getUpdateTime).reversed())
                    .findFirst();

            if (statusInLog.isPresent()) {
                // 如果target!=22认为target是最新的状态
                if (statusInLog.get().getTargetStatus() != OrderStatusEnum.REFUND_APPLIED.getValue()) {
                    orderStatus = statusInLog.get().getTargetStatus();
                } else {
                    orderStatus = statusInLog.get().getSourceStatus();
                }
            }
        }
        return orderStatus;
    }

    @Override
    public ResultStatus refundGoods(RefundGoodsRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_RETURN_APPLY_UPDATE.getCode());
        qnhOpenPushSystemParam.setTenantId(request.getTenantId());
        qnhOpenPushSystemParam.setStoreId(request.getStoreId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());

        AfsReviewTypeEnum reviewType = AfsReviewTypeEnum.enumOf(request.getReviewType());

        String status = "";

        switch (reviewType) {
            case AGREE_REFUND_GOODS:
                // 一审通过
                status = "2";
                break;
            case REJECT_REFUND:
                if (request.getAuditStage() == AfsAuditStageEnum.FINAL_AUDIT.getValue()) {
                    // 二审驳回
                    status = "4";
                } else {
                    // 一审驳回
                    status = "4";
                }
                break;
            case AGREE_REFUND:
                // 二审通过
                status = "5";
                break;
            default:
                log.error("QqwChannelOrderServiceImpl refundGoods reviewType ERROR! 不支持的退货退款状态");
                return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + "不支持的退货退款状态");
        }

        OrderStatusParam orderStatusParam = OrderStatusParam.builder()
                .channel_sheetno(request.orderId)
                .return_order_sn(request.afterSaleId)
                .status(status).poi_id(request.getStoreId()).build();

        log.info("refundGoods, sysParam: {}, data:{}", qnhOpenPushSystemParam, orderStatusParam);

        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }

        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(orderStatusParam)));
            if (qnhPushResponse == null) {
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }

            if (qnhPushResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }

        } catch (Exception e) {
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public ResultStatus poiCancelOrder(PoiCancelOrderRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_CANCEL_APPLY.getCode());
        qnhOpenPushSystemParam.setTenantId(request.getTenantId());
        qnhOpenPushSystemParam.setStoreId(request.getStoreId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());

        OrderStatusParam orderStatusParam = OrderStatusParam.builder()
                .channel_sheetno(request.orderId)
                .status("15").poi_id(request.getStoreId()).build();

        log.info("poiCancelOrder, sysParam: {}, data:{}", qnhOpenPushSystemParam, orderStatusParam);

        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }

        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(orderStatusParam)));
            if (qnhPushResponse == null) {
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }
            //解析渠道返回的处理结果
            handleOpenResult(request.getTenantId(), request.getChannelId(), qnhPushResponse.getData());
            if (qnhPushResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }

        } catch (ChannelBizException e) {
            return ResultGenerator.genFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("poiCancelOrder error", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public ResultStatus poiPartRefundApply(PoiPartRefundRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_RETURN_APPLY.getCode());
        qnhOpenPushSystemParam.setTenantId(request.getTenantId());
        qnhOpenPushSystemParam.setStoreId(request.getStoreId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());

        OrderStatusParam orderStatusParam = OrderStatusParam.builder()
                .channel_sheetno(request.orderId)
                .apply_sheetno(request.getAfterSaleId())
                .poi_id(request.getStoreId())
                .build();

        log.info("poiPartRefundApply, sysParam: {}, data:{}", qnhOpenPushSystemParam, orderStatusParam);

        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }

        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(orderStatusParam)));

            if (qnhPushResponse == null) {
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }
            //解析渠道返回的处理结果
            handleOpenResult(request.getTenantId(), request.getChannelId(), qnhPushResponse.getData());
            if (qnhPushResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }
        } catch (ChannelBizException e) {
            return ResultGenerator.genFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("poiPartRefundApply error", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request) {
        return null;
    }

    @Override
    public ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request) {
        return null;
    }

    @Override
    public PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request) {
        return null;
    }

    @Override
    public OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request) {
        return null;
    }

    @Override
    public GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request) {
        return null;
    }

    @Override
    public GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request) {
        return null;
    }

    @Override
    public ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_STATUS_UPDATE.getCode());
        qnhOpenPushSystemParam.setTenantId(request.getTenantId());
        qnhOpenPushSystemParam.setStoreId(request.getStoreId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());

        DeliveryStatus deliveryStatusEnum = DeliveryStatus.findByValue(request.getStatus());
        String deliveryStatus = "";
        switch (deliveryStatusEnum) {
            case RIDER_TAKEN_MEAL:
                deliveryStatus = "9";
                break;
            case DELIVERY_COMPLETED:
                deliveryStatus = "11";
                break;
            default:
                // 其他状态不同步
                return ResultGenerator.genSuccessResult();
        }

        OrderStatusParam orderStatusParam = OrderStatusParam.builder()
                .channel_sheetno(request.orderId)
                .status(deliveryStatus).poi_id(request.getStoreId()).build();

        log.info("updateOrderDeliveryStatus, sysParam: {}, data:{}", qnhOpenPushSystemParam, orderStatusParam);

        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)
                || mccConfigUtil.isMockUpOp(request.getTenantId(), request.getChannelId(), "updateOrderDeliveryStatus")) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }

        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(orderStatusParam)));

            if (qnhPushResponse == null) {
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }

            if (qnhPushResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }
        } catch (Exception e) {
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_STATUS_UPDATE.getCode());
        qnhOpenPushSystemParam.setTenantId(request.getTenantId());
        qnhOpenPushSystemParam.setStoreId(request.getShopId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());

        DeliveryStatus deliveryStatusEnum = DeliveryStatus.findByValue(request.getStatus());

        String deliveryStatus = "";
        switch (deliveryStatusEnum) {
            case RIDER_TAKEN_MEAL:
                deliveryStatus = "9";
                break;
            case DELIVERY_COMPLETED:
                deliveryStatus = "11";
                break;
            default:
                // 其他状态不同步
                return ResultGenerator.genSuccessResult();
        }

        OrderStatusParam orderStatusParam = OrderStatusParam.builder()
                .channel_sheetno(request.orderId)
                .status(deliveryStatus).poi_id(request.getShopId()).build();

        log.info("updateDeliveryInfo, sysParam: {}, data:{}", qnhOpenPushSystemParam, orderStatusParam);

        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)
                || mccConfigUtil.isMockUpOp(request.getTenantId(), request.getChannelId(), "updateDeliveryInfo")) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }

        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(orderStatusParam)));

            if (qnhPushResponse == null) {
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }

            if (qnhPushResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }
        } catch (Exception e) {
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public ResultStatus verifySelfFetchCode(VerifySelfFetchCodeRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_STATUS_UPDATE.getCode());
        qnhOpenPushSystemParam.setTenantId(request.tenantId);
        qnhOpenPushSystemParam.setStoreId(request.getStoreId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());
        OrderStatusParam orderStatusParam = OrderStatusParam.builder()
                .channel_sheetno(request.orderId)
                .status("11").poi_id(request.getStoreId()).build();
        log.info("verifySelfFetchCode, sysParam:{},data:{}",qnhOpenPushSystemParam,orderStatusParam);
        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }
        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(orderStatusParam)));

            if (qnhPushResponse == null) {
                log.error("核验自提码,请求开放平台返回NULL viewOrderId:{}",request.getOrderId());
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }
            if (qnhPushResponse.getCode() == 0){
                return ResultGenerator.genSuccessResult();
            } else {
                log.error("核验自提码,请求开放平台失败,err msg:{}",qnhPushResponse.getMsg());
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }
        } catch (Exception e){
            log.error("核验自提码错误 {}",e.getMessage());
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request) {
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus selfDelivery(SelfDeliveryRequest request) {
        QnhOpenPushSystemParam qnhOpenPushSystemParam = new QnhOpenPushSystemParam();
        qnhOpenPushSystemParam.setChannelId(request.getChannelId());
        qnhOpenPushSystemParam.setPushCode(QnhOpenPushTypeEnum.ORDER_SELF_DELIVERY.getCode());
        qnhOpenPushSystemParam.setTenantId(request.getTenantId());
        qnhOpenPushSystemParam.setStoreId(request.getStoreId());
        qnhOpenPushSystemParam.setSync(true);
        qnhOpenPushSystemParam.setOperatorCode(QnhOpenOperatorEnum.EMPOWER_ORDER.getCode());

        SelfDeliveryParam selfDeliveryParam = SelfDeliveryParam.builder()
                .channel_sheetno(request.getOrderId())
                .self_delivery_sheetno(request.getDeliveryOrderId()).poi_id(request.getStoreId()).build();

        log.info("selfDelivery, sysParam: {}, data:{}", qnhOpenPushSystemParam, selfDeliveryParam);

        if (!ConfigUtilAdapter.getBoolean(OPEN_PUSH_SWITCH_CONFIG, false)) {
            log.info("mock成功");
            return ResultGenerator.genSuccessResult();
        }

        if (MccConfigUtil.isOpenApiFallbackSwitch()) {
            log.error("openapi selfDeliveryFallback, orderId:{}", request.getChannelDeliveryId());
            return ResultGenerator.genFailResult("当前处于降级模式，不允许转单");
        }

        try {
            QnhPushResponse qnhPushResponse = qnhOpenPushThriftService.executePush(qnhOpenPushSystemParam, handleDataParam(request.getTenantId(), ParamConverter.objectToMapIntrospector(selfDeliveryParam)));

            if (qnhPushResponse == null) {
                return ResultGenerator.genFailResult("请求开放平台返回NULL");
            }

            if (qnhPushResponse.getCode() == 0) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genFailResult(qnhPushResponse.getMsg());
            }
        } catch (Exception e) {
            log.error("self delivery error", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request) {
        return new ResultStatus(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(), ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg(), null);
    }

    @Override
    public QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request) {
        return null;
    }

    @Override
    public ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request) {
        return null;
    }

    @Override
    public QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request) {
        return null;
    }

    @Override
    public QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request) {
        return null;
    }

    private OcmsOrderListReq buildOrderQueryRequest(String viewOrderId, int channelId, Long shopId, Long tenantId) {
        OcmsOrderListReq orderListReq = new OcmsOrderListReq();
        orderListReq.setShopId(shopId);
        orderListReq.setChannelIdList(Lists.newArrayList(channelId));
        orderListReq.setChannelOrderId(viewOrderId);
        if(StringUtils.isNotEmpty(viewOrderId)
            && MccConfigUtil.useQueryMngByViewOrderIdTenantSwitch(tenantId)
        ){
            // 查询条件不为空并且使用根据订单ID查询订单列表的开关打开时，设置订单查询条件
            ChannelOrderIdCondition channelOrderIdCondition = new ChannelOrderIdCondition();
            channelOrderIdCondition.setChannelId(channelId);
            channelOrderIdCondition.setChannelOrderId(viewOrderId);
            orderListReq.setOrderReqList(Collections.singletonList(channelOrderIdCondition));
            orderListReq.setUseOrderListQuery(true);
        } else {
            long now = System.currentTimeMillis();
            orderListReq.setBeginCreateTime(now - MccConfigUtil.privateOrderQueryTimeRange());
            orderListReq.setEndCreateTime(now);
        }
        orderListReq.setTenantId(tenantId);
        orderListReq.setPage(1);
        orderListReq.setPageSize(10);
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        return orderListReq;
    }

    private static final Map<Integer, Integer> BIZ_ORDER_STATUS_CHANNEL_ORDER_STATUS = new HashMap<>();

    static {
        BIZ_ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.SUBMIT.getValue(), ChannelOrderStatus.NEW_ORDER.getValue());
        BIZ_ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.MERCHANT_CONFIRMED.getValue(), ChannelOrderStatus.BIZ_CONFIRMED.getValue());
        BIZ_ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.PICKING.getValue(), ChannelOrderStatus.FULFILLMENT.getValue());
        BIZ_ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.COMPLETED.getValue(), ChannelOrderStatus.FINISHED.getValue());
        BIZ_ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.REFUND_APPLIED.getValue(), ChannelOrderStatus.CANCEL_APPLIED.getValue());
        BIZ_ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.CANCELED.getValue(), ChannelOrderStatus.CANCELED.getValue());
        BIZ_ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.LOCKED.getValue(), ChannelOrderStatus.LOCKED.getValue());

    }

    public static String convertOpenApiStatus(Integer orderStatus,Integer deliverStatus,Integer distributeSatus){
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderStatus);
        switch (orderStatusEnum){
            case SUBMIT:
                return "1";//用户已提交订单
            case MERCHANT_CONFIRMED:
                return "4";//商家已确认
            case PICKING:
                if(distributeSatus>DistributeStatusEnum.RIDER_TAKE_GOODS.getValue()){
                    return "9";//已配送
                }
                if(Objects.equals(deliverStatus, DeliveryStatusEnum.PICKED.getValue())){
                    return "6";//拣货完成
                }
                return "4";
            case COMPLETED:
                return "11";//已签收
            case REFUND_APPLIED:
                return "14";//待取消
            case CANCELED:
                return "15";//订单待取消
            default:
                return "0";
        }
    }

    public Integer orderStatusBiz2Mid(Integer orderBizOrderStatus) {
        Integer channelOrderStatus = BIZ_ORDER_STATUS_CHANNEL_ORDER_STATUS.get(orderBizOrderStatus);
        if (channelOrderStatus == null) {
            log.error("订单状态转换失败，channelOrderStatus:{}", channelOrderStatus);
        }
        return channelOrderStatus;
    }

    /**
     * 获取开放平台-渠道返回结果信息
     *
     * @param tenantId
     * @param channelId
     * @param result 渠道返回结果
     * @return
     */
    private void handleOpenResult(Long tenantId, Integer channelId, String result) {
        if (StringUtils.isBlank(result)) {
            return;
        }
        // 根据配置判断渠道的租户是否需要解析开发平台返回的数据
        if (!MccConfigUtil.checkOpenResultConvert(tenantId, channelId)) {
            return;
        }
        OpenResult openResult = OpenResult.convertOpenResult(result);
        if (Objects.isNull(openResult)) {
            return;
        }
        if (!Objects.equals(openResult.getCode(), ResultCode.SUCCESS.getCode())
                && StringUtils.isNotEmpty(openResult.getMsg())) {
            throw new ChannelBizException(openResult.getMsg());
        }
    }

    /**
     * 处理data参数，新增参数需要注意部分商家验证签名存在问题，需要排除部分参数
     *
     * @param tenantId
     * @param dataParamMap data参数的map
     */
    private Map<String, String> handleDataParam(Long tenantId, Map<String, String> dataParamMap) {
        try {
            if (MapUtils.isEmpty(dataParamMap)) {
                return dataParamMap;
            }
            // 获取租户需要排除的推送字段列表
            List<String> keyList = MccConfigUtil.openDataParamExcludeFieldsConfig(tenantId);
            if (CollectionUtils.isEmpty(keyList)) {
                return dataParamMap;
            }
            // 创建一个新的Map副本
            Map<String, String> resultMap = new HashMap<>(dataParamMap);
            for (String key : keyList) {
                resultMap.remove(key);
            }
            return resultMap;
        } catch (Exception e) {
            log.error("tenantId:{} dataParamMap:{} handleDataParam error", tenantId, dataParamMap, e);
        }
        return dataParamMap;
    }
}
