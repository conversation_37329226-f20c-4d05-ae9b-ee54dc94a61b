package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.dianping.lion.client.Lion;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.meituan.shangou.saas.common.enums.OcmsRefundSponsorEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.drunkhorse.AfterSaleCommonOpScenario;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelOrderDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtOrderAfsApplyDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.OrderSkuDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DrunkHorseAuditTypeUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderUserType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021-10-11
 * @desc 歪马
 */
@Slf4j
public class DrunkHorseConverterUtil {

    private static final int CUSTOMER = 1;
    private static final int CALL_CENTER = 2;
    private static final int SYSTEM = 3;
    private static final int PEISONG = 4;
    private static final int TENANT = 5;
    private static final int OPEN_PLATFORM = 6;

    private static final int SELF_DELIVERY = 1;

    private static final String SKU_SAIL_PROPERTY = "2";
    public static final Map<String, String> defaultOpScenarioMap = ImmutableMap.of("505", "商家超时未处理");

    public static int translateSponsor(int opType, int applyOpScenario) {
        if (applyOpScenario == AfterSaleCommonOpScenario.SYSTEM_APPLY_FOR_ORDER_CANCEL) {
            return OcmsRefundSponsorEnum.RISK.getValue();
        }

        switch (opType) {
            case CUSTOMER:
                return OcmsRefundSponsorEnum.CUSTOMER.getValue();
            case CALL_CENTER:
                return OcmsRefundSponsorEnum.CALL_CENTER.getValue();
            case SYSTEM:
                return OcmsRefundSponsorEnum.SYSTEM.getValue();
            case PEISONG:
                return OcmsRefundSponsorEnum.PEISONG.getValue();
            case TENANT:
                return OcmsRefundSponsorEnum.TENANT.getValue();
            case OPEN_PLATFORM:
                return OcmsRefundSponsorEnum.TENANT.getValue();
            default:
                return OcmsRefundSponsorEnum.UNKNOWN.getValue();

        }
    }

    public static long orderCreateTime(ChannelOrderDetail channelOrderDetail) {
        if (channelOrderDetail != null) {
            if (channelOrderDetail.getCtime() != null) {
                return TimeUnit.SECONDS.toMillis(channelOrderDetail.getCtime());
            } else if (channelOrderDetail.getUtime() != null) {
                return TimeUnit.SECONDS.toMillis(channelOrderDetail.getUtime());
            }
        }
        return System.currentTimeMillis();
    }


    public static long convertTime(int time) {
        return time * 1000L;
    }

    /**
     * 解析是否为商家自配送
     */
    public static int isSelfDelivery() {
        return SELF_DELIVERY;
    }

    /**
     * 解析原始配送类型
     */
    public static int convertDeliveryType() {
        return DistributeTypeEnum.SELF_DELIVERY.getValue();

    }

    /**
     * 解析原始配送类型
     */
    public static int orderUserType(Integer is_vip) {
        if (is_vip == null) {
            return OrderUserType.COMMON.getValue();
        }
        if (is_vip == 1) {
            return OrderUserType.MEMBER.getValue();
        } else if (is_vip == 0) {
            return OrderUserType.COMMON.getValue();
        } else {
            log.error("是否会员用户返回异常， is_vip:{}", is_vip);
        }
        return OrderUserType.COMMON.getValue();
    }

    public static Integer convertAuditType(MtOrderAfsApplyDTO orderAfsApplyDTO) {
        return DrunkHorseAuditTypeUtils.getAuditTypeFromRefundDetail(orderAfsApplyDTO);
    }

    public static void compensateSailProperty(OrderSkuDetail skuDetail){
        log.info("调整前, 商品:{}, am:{}, property:{}", skuDetail. getFood_name(), skuDetail.getFood_property_type(), skuDetail.getFood_property());
        List<String> amList = Splitter.on(",").splitToList(skuDetail.getFood_property_type());
        List<String> propertyList = Splitter.on(",").splitToList(skuDetail.getFood_property());
        int size = Math.min(amList.size(), propertyList.size());
        List<String> afterPropertyList = new ArrayList<>();
        Iterator<String> amIterator = amList.iterator();
        int index = 0;
        while (amIterator.hasNext() && index < size){
            String am = amIterator.next();
            String property = propertyList.get(index);
            if (StringUtils.equals(SKU_SAIL_PROPERTY, am)){
                skuDetail.setSpec(property);
            }else{
                afterPropertyList.add(property);
            }
            index++;
        }
        skuDetail.setFood_property(Joiner.on(",").join(afterPropertyList));
        log.info("调整后, 商品:{}, am:{}, property:{}", skuDetail. getFood_name(), skuDetail.getFood_property_type(), skuDetail.getFood_property());
    }

    public static void compensateSailProperties(List<OrderSkuDetail> skuDetails) {
        if (!Lion.getConfigRepository().getBooleanValue("drunkhorse.compensate.switch", true)){
            return;
        }
        if (CollectionUtils.isEmpty(skuDetails)){
            return;
        }
        skuDetails.forEach(skuDetail ->{
            if (StringUtils.isBlank(skuDetail.getSpec())
                    && StringUtils.isNotBlank(skuDetail.getFood_property_type())
                    && StringUtils.isNotBlank(skuDetail.getFood_property())){
                try {
                    compensateSailProperty(skuDetail);
                }catch (Exception e){
                    log.error("解析商品销售属性失败", e);
                }

            }
        });
    }
}
