package com.sankuai.meituan.shangou.empower.ocms.channel.service.cache;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Rhino
@Slf4j
public class ElmSelfDeliveryStatusSquirrelOperationService extends SquirrelOperateService {

    private static final String CATEGORY_NAME = "elm_self_delivery_status";

    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    @Degrade(rhinoKey = "ElmSelfDeliveryStatusSquirrelOperationService-getDeliveryStatusFromCache", fallBackMethod = "getDeliveryStatusFromCacheFallBack", timeoutInMilliseconds = 2000)
    public Optional<Integer> getDeliveryStatusFromCache(String channelOrderId) {
        return get(channelOrderId, Integer.class);
    }

    @Degrade(rhinoKey = "ElmSelfDeliveryStatusSquirrelOperationService-setDeliveryStatusToCache", fallBackMethod = "setDeliveryStatusToCacheFallBack", timeoutInMilliseconds = 2000)
    public void setDeliveryStatusToCache(String channelOrderId, Integer deliveryStatus) {
        set(channelOrderId, deliveryStatus, TimeUtil.toSeconds(MccConfigUtil.getElmSelfDeliveryStatusExpireDays()));
    }

    public Optional<Integer> getDeliveryStatusFromCacheFallBack(String channelOrderId) {
        log.warn("ElmSelfDeliveryStatusSquirrelOperationService getDeliveryStatusFromCacheFallBack degrade");
        return Optional.empty();
    }

    public void setDeliveryStatusToCacheFallBack(String channelOrderId, Integer deliveryStatus) {
        log.warn("ElmSelfDeliveryStatusSquirrelOperationService setDeliveryStatusToCacheFallBack degrade");
    }

}
