package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.StringUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelNotifyEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.medicine.mt.MtMedicineCreateSkuInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.medicine.mt.MtMedicineDeleteSkuInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.medicine.mt.MtMedicineOperationSkuBase;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.medicine.mt.MtMedicineUpdateSkuInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.MedicineSkuChangeCallbackProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.medicine.MedicineSkuChangeCallbackMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.MedicineChannelCallbackRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.MedicineChannelCallbackResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.MedicineChannelCallBackService;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.EnumUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MedicineSkuCallbackMonitors;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ChannelSpuMessageCallBackTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 医药的渠道商品回调
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
@Slf4j
@Service("medicineChannelCallbackServiceImpl")
public class MedicineChannelCallbackServiceImpl implements MedicineChannelCallBackService {
    private static final String AUTO_STOCK = "商品实时库存";
    private static final String AUDIT = "奥丁审核";

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Autowired
    private MedicineSkuChangeCallbackProducer medicineSkuChangeCallbackProducer;

    @Override
    public MedicineChannelCallbackResponse skuNotify(MedicineChannelCallbackRequest request) {
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);

        if (Objects.isNull(channelTypeEnum)) {
            log.error("MedicineChannelCallBackServiceImpl.skuNotify, 医药渠道回调商品消息 未知渠道编码, request:{}", request);
            return MedicineChannelCallbackResponse.buildResponse(
                    ResultCode.CHANNEL_CODE_INVALID.getCode(),
                    ResultCode.CHANNEL_CODE_INVALID.getMsg(),
                    ProjectConstant.NG
            );
        }

        ChannelNotifyEnum notifyEnum = EnumUtil.getEnumByAbbrev(request.getAction(), ChannelNotifyEnum.class);

        if (Objects.isNull(notifyEnum)) {
            log.error("MedicineChannelCallBackServiceImpl.skuNotify, 医药渠道回调商品消息 未知操作, request:{}", request);
            return MedicineChannelCallbackResponse.buildResponse(
                    ResultCode.CHANNEL_NOTIFY_UNKNOWN.getCode(),
                    ResultCode.CHANNEL_NOTIFY_UNKNOWN.getMsg(),
                    ProjectConstant.NG
            );
        }

        //1.区分渠道channelCode
        //2.区分消息action（新增商品消息 修改商品消息）需要枚举
        //3.调用中台商品模块
        //4.返回处理结果（不同渠道要求的code不同，美团识别data，饿百京东识别code）
        switch (channelTypeEnum) {
            case MT_MEDICINE:
                return mtNotifyHandler(channelTypeEnum, notifyEnum, request);
            default:
                return MedicineChannelCallbackResponse.buildResponse(
                        ResultCode.CHANNEL_CODE_INVALID.getCode(),
                        ResultCode.CHANNEL_CODE_INVALID.getMsg(),
                        ProjectConstant.NG
                );
        }
    }

    private MedicineChannelCallbackResponse mtNotifyHandler(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, MedicineChannelCallbackRequest request) {
        switch (notifyEnum) {
            //1.区分消息action（订单状态消息、配送状态消息、订单修改消息等）需要枚举
            case MT_NEW_SKU_NOTIFY:
            case MT_UPDATE_SKU_NOTIFY:
            case MT_DELETE_SKU_NOTIFY:
                return handleMtSkuChangeNotify(channelTypeEnum, notifyEnum, request);
            default:
                log.error("MedicineChannelCallbackServiceImpl.mtNotifyHandler, 医药渠道回调商品消息 美团渠道消息类型错误, request: {}", request);
                return MedicineChannelCallbackResponse.buildResponse(
                        ResultCode.CHANNEL_NOTIFY_UNKNOWN.getCode(),
                        ResultCode.CHANNEL_NOTIFY_UNKNOWN.getMsg(),
                        ProjectConstant.NG
                );

        }
    }

    private MedicineChannelCallbackResponse handleMtSkuChangeNotify(ChannelTypeEnum channelTypeEnum, ChannelNotifyEnum notifyEnum, MedicineChannelCallbackRequest request) {
        log.info("MedicineChannelCallbackServiceImpl.handleMtSkuChangeNotify, 医药渠道回调商品消息 content: {}", request);

        if (request.getData() == null) {
            return MedicineChannelCallbackResponse.buildResponse(
                    ResultCode.FAIL.getCode(),
                    ResultCode.FAIL.getMsg(),
                    ProjectConstant.NG
            );
        }

        //此回调接口需支持POST请求方式，美团通过此接口向商家系统推送的信息。
        String content = null;

        try {
            content = URLDecoder.decode(request.getData(), "UTF-8");
        } catch (Exception e) {
            content = "[]";
            log.error("MedicineChannelCallbackServiceImpl.handleMtSkuChangeNotify, 医药渠道回调商品消息 解析消息失败 request: {}, exception: ", request, e);
        }

        long tenantId = getTenantIdParam(channelTypeEnum.getCode(), request.getTenantAppId());

        if (tenantId == ProjectConstant.UNKNOW_TENANT_ID) {
            log.warn("MedicineChannelCallbackServiceImpl.handleMtSkuChangeNotify, 医药渠道回调商品消息 租户id获取失败 request: {}", request);
            return MedicineChannelCallbackResponse.buildResponse(
                    ResultCode.FAIL.getCode(),
                    ResultCode.FAIL.getMsg(),
                    ProjectConstant.NG
            );
        }

        JSONArray jsonArray = JSON.parseArray(content);

        jsonArray.forEach(item -> {
            JSONObject jsonObject = (JSONObject) item;

            try {
                MedicineSkuChangeCallbackMessage skuChangeMessage = new MedicineSkuChangeCallbackMessage();
                MtMedicineOperationSkuBase operationSkuBase = null;
                Long operationTime = System.currentTimeMillis();
                String operationType = null;

                if (ChannelNotifyEnum.MT_NEW_SKU_NOTIFY.equals(notifyEnum)) {
                    operationSkuBase = JSON.parseObject(jsonObject.toJSONString(), MtMedicineCreateSkuInfo.class);

                    skuChangeMessage.setOpPlatform(((MtMedicineCreateSkuInfo) operationSkuBase).getOpPlatform());
                    skuChangeMessage.setMessageType(ChannelSpuMessageCallBackTypeEnum.CREATE.getValue());
                    // 创建时间
                    operationTime = operationSkuBase.getCtime();
                    operationType = "CREATE_SKU";
                } else if (ChannelNotifyEnum.MT_UPDATE_SKU_NOTIFY.equals(notifyEnum)) {
                    operationSkuBase = JSON.parseObject(jsonObject.toJSONString(), MtMedicineUpdateSkuInfo.class);

                    skuChangeMessage.setOpPlatform(((MtMedicineUpdateSkuInfo) operationSkuBase).getOpPlatform());
                    skuChangeMessage.setReason(((MtMedicineUpdateSkuInfo) operationSkuBase).getOpReason());
                    skuChangeMessage.setMessageType(ChannelSpuMessageCallBackTypeEnum.UPDATE.getValue());

                    // 因为售卖导致的自动库存扣减和审核触发不进行操作
                    if (AUTO_STOCK.equals(skuChangeMessage.getReason()) || AUDIT.equals(skuChangeMessage.getOpName())) {
                        return;
                    }

                    // 更新时间
                    operationTime = operationSkuBase.getUtime();
                    operationType = "UPDATE_SKU";
                } else if (ChannelNotifyEnum.MT_DELETE_SKU_NOTIFY.equals(notifyEnum)) {
                    operationSkuBase = JSON.parseObject(jsonObject.toJSONString(), MtMedicineDeleteSkuInfo.class);

                    skuChangeMessage.setOpPlatform(((MtMedicineDeleteSkuInfo) operationSkuBase).getOpPlatform());
                    skuChangeMessage.setReason(((MtMedicineDeleteSkuInfo) operationSkuBase).getDeleteReason());
                    skuChangeMessage.setMessageType(ChannelSpuMessageCallBackTypeEnum.DELETE.getValue());
                    // 删除时间
                    operationTime = operationSkuBase.getUtime();
                    operationType = "DELETE_SKU";
                } else {
                    log.warn("MedicineChannelCallbackServiceImpl.handleMtSkuChangeNotify, 医药渠道回调商品消息 未知消息类型 request: {}, item: {}", request, jsonObject.toJSONString());
                    return;
                }

                long storeId = copChannelStoreService.selectChannelStoreId(tenantId, channelTypeEnum.getCode(), operationSkuBase.getAppPoiCode());

                if (storeId == ProjectConstant.UNKNOW_STORE_ID) {
                    log.warn("MedicineChannelCallbackServiceImpl.handleMtSkuChangeNotify, 医药渠道回调商品消息 门店id获取失败 request: {}, item: {}", request, jsonObject.toJSONString());
                    return;
                }

                skuChangeMessage.setCustomSkuId(operationSkuBase.getCustomSkuId());
                skuChangeMessage.setTenantId(tenantId);
                skuChangeMessage.setStoreId(storeId);
                skuChangeMessage.setOpName(operationSkuBase.getOpName());
                skuChangeMessage.setUpc(operationSkuBase.getUpc());
                skuChangeMessage.setChannelId(channelTypeEnum.getCode());

                // TODO 待开放平台修改后修改判断方式
                // 两者都为空的时候，大概率为组合商品，这里不做组合商品相关操作
                if (StringUtils.isEmpty(skuChangeMessage.getUpc()) && StringUtils.isEmpty(skuChangeMessage.getCustomSkuId())) {
                    return;
                }

                // 发送异步处理消息，打点
                sendCallbackMessageByMq(skuChangeMessage);
                MedicineSkuCallbackMonitors.monitor(operationType, operationTime);
            } catch (Exception e) {
                log.info("MedicineChannelCallbackServiceImpl.handleMtSkuChangeNotify, 医药渠道回调商品消息 未知错误处理消息失败 " +
                        "request: {}, item: {}", request, item, e);
            }
        });

        return MedicineChannelCallbackResponse.buildSuccessResponse(ProjectConstant.OK);
    }

    /**
     * 查询数据库获取租户ID
     */
    private Long getTenantIdParam(int channelId, String tenantAppId) {
        Long tenantId = copAccessConfigService.selectTenantId(channelId, tenantAppId);
        if (Objects.isNull(tenantId)) {
            log.warn("ChannelOrderCallbackServiceImpl.getTenantIdParam, 未获取到渠道租户ID, channelId:{}, tenantAppId:{}",
                    channelId, tenantAppId);
            return ProjectConstant.UNKNOW_TENANT_ID;
        }
        return tenantId;
    }

    private void sendCallbackMessageByMq(MedicineSkuChangeCallbackMessage message) {
        String partKey = String.format("%d_%d_%d", message.getTenantId(), message.getStoreId(), message.getChannelId());
        medicineSkuChangeCallbackProducer.sendMessage(message, partKey);
    }
}
