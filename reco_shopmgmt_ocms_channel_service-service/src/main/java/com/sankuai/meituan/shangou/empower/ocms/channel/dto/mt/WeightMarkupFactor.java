package com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-09-25
 * @email <EMAIL>
 */
@Data
public class WeightMarkupFactor {

    //超限重量， 1.单位kg 2.正数且最多支持1位小数 3.最大支持传1000 4.多个规则之间不能重复
    private double weight;

    //加价步长 1.单位kg 2.正数且最多支持1位小数 3.最大支持传1000
    private double step;

    //加价金额， 1.单位元 2.正数且最多支持1位小数 3.最大支持传10000
    private double markupNum;

}
