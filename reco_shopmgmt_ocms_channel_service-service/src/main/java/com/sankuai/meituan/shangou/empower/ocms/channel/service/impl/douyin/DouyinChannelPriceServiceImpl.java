package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPriceService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSingleData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSkuPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 抖音商品价格内部服务接口
 *
 * <AUTHOR>
 * @create 2023-12-20 下午4:40
 **/
@Slf4j
@Service("dyChannelPriceService")
public class DouyinChannelPriceServiceImpl implements ChannelPriceService {
    @Autowired
    private DouyinChannelGateService douyinChannelGateService;

    @Override
    public ResultData updatePrice(SkuPriceRequest request) {
        return null;
    }

    @Override
    public ResultData updatePriceMultiChannel(SkuPriceMultiChannelRequest request) {
        return updatePriceMultiChannelCommon(request, ChannelPostJDDJEnum.PRICE_UPDATE);
    }

    @Override
    public ResultData updatePriceMultiChannelForCleaner(SkuPriceMultiChannelRequest request) {
        return updatePriceMultiChannelCommon(request, ChannelPostJDDJEnum.PRICE_UPDATE_FOR_CLEANER);
    }

    private ResultData updatePriceMultiChannelCommon(SkuPriceMultiChannelRequest request, ChannelPostInter channelPostInter) {
        return null;
    }

    @Override
    public ResultSingleData updateSingleSkuPrice(SingleSkuPriceRequest request) {
        ResultSingleData resultData = ResultGenerator.genResultSingleData(ResultCode.SUCCESS);
        SkuPriceDTO skuPriceDTO = request.getParam();
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
        Preconditions.checkArgument(StringUtils.isNotBlank(skuPriceDTO.getChannelSkuId()), "channelSkuId is blank");

        try {
            ChannelResponseDTO result = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SKU_EDIT_PRICE,
                    baseRequest, DouyinConvertUtil.editSkuPriceMapping(skuPriceDTO));
            Preconditions.checkNotNull(result, "Edit sku price result is null");

            if (result.isSuccess()) {
                ResultSuccessSku resultSuccessSku = new ResultSuccessSku().setChannelId(request.getChannelId()).setStoreId(request.getStoreId())
                        .setChannelSkuId(skuPriceDTO.getChannelSkuId())
                        .setSkuId(skuPriceDTO.getSkuId())
                        .setChannelResultInfo(result.getMsg());
                resultData.setSucData(resultSuccessSku);
            } else {
                ResultErrorSku resultErrorSku = new ResultErrorSku().setStoreId(-1).setSkuId(skuPriceDTO.getSkuId()).setChannelUnifyError(result.getUnifyUnifiedErrorEnum()).setErrorMsg(Optional.of(result.getSub_msg()).orElse(result.getMsg()));
                resultData.setErrorData(resultErrorSku);
            }
        } catch (IllegalArgumentException e) {
            log.error("DouyinChannelPriceServiceImpl.updateSingleSkuPrice 参数校验失败, data:{}", skuPriceDTO, e);
            ResultDataUtils.combineExceptionData(resultData, e.getMessage(), skuPriceDTO.getSkuId());
        } catch (Exception e) {
            log.error("DouyinChannelPriceServiceImpl.updateSingleSkuPrice 服务异常, data:{}", skuPriceDTO, e);
            ResultDataUtils.combineExceptionData(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), skuPriceDTO.getSkuId());
        }

        return resultData;
    }

    @Override
    public List<ChannelStorePriceInfo> batchGetPriceInfo(BaseRequest request, List<Long> skuIds) {
        return null;
    }

}
