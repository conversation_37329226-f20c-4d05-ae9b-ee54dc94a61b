package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import java.util.HashMap;
import java.util.Map;

public class YzOrderConverterUtil {

    /**
     * 售后申请原因，(仅退款-已收到货申请原因) 0:商家主动退款;1: 买/卖双方协商一致;2: 买错/多买/不想要;3: 商品质量问题;4: 未收到货品';5: 做工粗糙/有瑕疵;11: 质量问题;12: 拍错/多拍/不喜欢;13: 商品描述不符;14: 假货;15: 商家发错货;16: 商品破损/少件;17: 其他;18: 仅退款(收到货)-退运费;19: 协商一致退款;20: 快递一直未送达;21: 未按约定时间发货;22: 拍错/不想要;23: 计划有变无法使用;24: 商家降价; (仅退款-未收到货申请原因) 25:非正品 51: 买错/多买/不想要;52: 快递无记录;53: 少货/空包裹;54: 未按约定时间发货;55: 快递一直未送达;56: 其他;57: 拍错/多拍/不喜欢;58: (退货退款-申请原因)协商一致退款; 59: 同城订单商家手动拒单;60:仅退款(未收到货) -地址/电话填错了 ;61:仅退款(未收到货) -送达时间选错了;101: 商品破损/少件;102: 商家发错货;103: 商品描述不符;104: 拍错/多拍/不喜欢;105: 质量问题;106: 假货;107: 其他;108: 做工粗糙/有瑕疵;109: 非正品;110: (系统退款原因) 未按约定时间发货;111:退货退款-退运费;112:7天无理由退货;201: 返现退款;202:203:拼团订单扣库存失败退款; 酒店拒单退款;204: 订单关闭退款;205: 代付过期退款;206: 超付退款;207: 外卖拒单退款;208: 拼团未成团退款;209: 团购返现退款;210:美业退款;211: 订单少付退款;212: 小程序拼团退款;213:送礼子订单未被领取退款;214:超付+本金组合退款;215:送礼社群版到期自动退款;216:上云商家一键退款类型;217:群团购团长审核不通过退款;218:群团购未成团退款;219:会员卡发卡失败退款;220:同城订单超时未接单自动关单;221:差价退款;222:重复购买退款;223:教育线上重复购买退款;301:换货-商家发错商品;302:换货-大小/颜色/型号不合适;303:换货-商品损坏;304:换货-其他;401:酒店退款-行程取消;402:酒店退款-信息填错;403:酒店退款-前台价格更优惠;404:酒店退款-其他渠道更便宜;405:酒店退款-对酒店环境不满意;406:酒店退款-卖家告诉满房;407:酒店退款-买多了/买错了/计划有变;408:酒店退款-想要预约的日期预约不上;409:酒店退款-直接预订的价格比使用预售券更优惠;410:酒店退款-预约时联系不上商家;411:酒店退款-商品信息与实际信息不符;412:酒店退款-其他.
     */

    private static Map<Integer/*原因编码*/, String/*原因文案*/> YZ_AFTER_SALE_REASON = new HashMap<>();

    static {

        // (仅退款-已收到货申请原因)
        YZ_AFTER_SALE_REASON.put(0, "商家主动退款");
        YZ_AFTER_SALE_REASON.put(1, "买/卖双方协商一致");
        YZ_AFTER_SALE_REASON.put(2, "买错/多买/不想要");
        YZ_AFTER_SALE_REASON.put(3, "商品质量问题");
        YZ_AFTER_SALE_REASON.put(4, "未收到货品");
        YZ_AFTER_SALE_REASON.put(5, "做工粗糙/有瑕疵");
        YZ_AFTER_SALE_REASON.put(11, "质量问题");
        YZ_AFTER_SALE_REASON.put(12, "拍错/多拍/不喜欢");
        YZ_AFTER_SALE_REASON.put(13, "商品描述不符");
        YZ_AFTER_SALE_REASON.put(14, "假货");
        YZ_AFTER_SALE_REASON.put(15, "商家发错货");
        YZ_AFTER_SALE_REASON.put(16, "商品破损/少件");
        YZ_AFTER_SALE_REASON.put(17, "其他");
        YZ_AFTER_SALE_REASON.put(18, "仅退款(收到货)-退运费");
        YZ_AFTER_SALE_REASON.put(19, "协商一致退款");
        YZ_AFTER_SALE_REASON.put(20, "快递一直未送达");
        YZ_AFTER_SALE_REASON.put(21, "未按约定时间发货");
        YZ_AFTER_SALE_REASON.put(22, "拍错/不想要");
        YZ_AFTER_SALE_REASON.put(23, "计划有变无法使用");
        YZ_AFTER_SALE_REASON.put(24, "商家降价");

        // (仅退款-未收到货申请原因)
        YZ_AFTER_SALE_REASON.put(25, "非正品");
        YZ_AFTER_SALE_REASON.put(51, "买错/多买/不想要");
        YZ_AFTER_SALE_REASON.put(52, "快递无记录");
        YZ_AFTER_SALE_REASON.put(53, "少货/空包裹");
        YZ_AFTER_SALE_REASON.put(54, "未按约定时间发货");
        YZ_AFTER_SALE_REASON.put(55, "快递一直未送达");
        YZ_AFTER_SALE_REASON.put(56, "其他");
        YZ_AFTER_SALE_REASON.put(57, "拍错/多拍/不喜欢");

        // (退货退款-申请原因)
        YZ_AFTER_SALE_REASON.put(58, "协商一致退款");
        YZ_AFTER_SALE_REASON.put(59, "同城订单商家手动拒单");

        YZ_AFTER_SALE_REASON.put(60, "仅退款(未收到货) -地址/电话填错了");
        YZ_AFTER_SALE_REASON.put(61, "仅退款(未收到货) -送达时间选错了");
        YZ_AFTER_SALE_REASON.put(101, "商品破损/少件");
        YZ_AFTER_SALE_REASON.put(102, "商家发错货");
        YZ_AFTER_SALE_REASON.put(103, "商品描述不符");
        YZ_AFTER_SALE_REASON.put(104, "拍错/多拍/不喜欢");
        YZ_AFTER_SALE_REASON.put(105, "质量问题");
        YZ_AFTER_SALE_REASON.put(106, "假货");
        YZ_AFTER_SALE_REASON.put(107, "其他");
        YZ_AFTER_SALE_REASON.put(108, "做工粗糙/有瑕疵");
        YZ_AFTER_SALE_REASON.put(109, "非正品");
        YZ_AFTER_SALE_REASON.put(110, "(系统退款原因) 未按约定时间发货");
        YZ_AFTER_SALE_REASON.put(111, "退货退款-退运费");
        YZ_AFTER_SALE_REASON.put(112, "7天无理由退货");
        YZ_AFTER_SALE_REASON.put(201, "返现退款");
        YZ_AFTER_SALE_REASON.put(202, "拼团订单扣库存失败退款");
        YZ_AFTER_SALE_REASON.put(203, "酒店拒单退款");
        YZ_AFTER_SALE_REASON.put(204, "订单关闭退款");
        YZ_AFTER_SALE_REASON.put(205, "代付过期退款");
        YZ_AFTER_SALE_REASON.put(206, "超付退款");
        YZ_AFTER_SALE_REASON.put(207, "外卖拒单退款");
        YZ_AFTER_SALE_REASON.put(208, "拼团未成团退款");
        YZ_AFTER_SALE_REASON.put(209, "团购返现退款");
        YZ_AFTER_SALE_REASON.put(210, "美业退款");

        YZ_AFTER_SALE_REASON.put(211, "订单少付退款");
        YZ_AFTER_SALE_REASON.put(212, "小程序拼团退款");
        YZ_AFTER_SALE_REASON.put(213, "送礼子订单未被领取退款");
        YZ_AFTER_SALE_REASON.put(214, "超付+本金组合退款");
        YZ_AFTER_SALE_REASON.put(215, "送礼社群版到期自动退款");
        YZ_AFTER_SALE_REASON.put(216, "上云商家一键退款类型");
        YZ_AFTER_SALE_REASON.put(217, "群团购团长审核不通过退款");
        YZ_AFTER_SALE_REASON.put(218, "群团购未成团退款");
        YZ_AFTER_SALE_REASON.put(219, "会员卡发卡失败退款");
        YZ_AFTER_SALE_REASON.put(220, "同城订单超时未接单自动关单");
        YZ_AFTER_SALE_REASON.put(221, "差价退款");
        YZ_AFTER_SALE_REASON.put(222, "重复购买退款");
        YZ_AFTER_SALE_REASON.put(223, "教育线上重复购买退款");
        YZ_AFTER_SALE_REASON.put(301, "换货-商家发错商品");
        YZ_AFTER_SALE_REASON.put(302, "换货-大小/颜色/型号不合适");
        YZ_AFTER_SALE_REASON.put(303, "换货-商品损坏");
        YZ_AFTER_SALE_REASON.put(304, "换货-其他");
        YZ_AFTER_SALE_REASON.put(401, "酒店退款-行程取消");
        YZ_AFTER_SALE_REASON.put(402, "酒店退款-信息填错");
        YZ_AFTER_SALE_REASON.put(403, "酒店退款-前台价格更优惠");
        YZ_AFTER_SALE_REASON.put(404, "酒店退款-其他渠道更便宜");
        YZ_AFTER_SALE_REASON.put(405, "酒店退款-对酒店环境不满意");
        YZ_AFTER_SALE_REASON.put(406, "酒店退款-卖家告诉满房");
        YZ_AFTER_SALE_REASON.put(407, "酒店退款-买多了/买错了/计划有变");
        YZ_AFTER_SALE_REASON.put(408, "酒店退款-想要预约的日期预约不上");
        YZ_AFTER_SALE_REASON.put(409, "酒店退款-直接预订的价格比使用预售券更优惠");
        YZ_AFTER_SALE_REASON.put(410, "酒店退款-预约时联系不上商家");
        YZ_AFTER_SALE_REASON.put(411, "酒店退款-商品信息与实际信息不符");
        YZ_AFTER_SALE_REASON.put(412, "酒店退款-其他");

    }


    public static String covertToRefundReason(Integer yzReasonType) {
        return YZ_AFTER_SALE_REASON.getOrDefault(yzReasonType, "未识别" + yzReasonType);
    }
}
