package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

/**
 * <AUTHOR>
 * 描述:
 * 收货人地址详情
 */
public class SgOpenRecipientAddressDetail {

    /**
     * 一级行政区(省级行政区):省、自治区、直辖市、特别行政区
     */
    private String province;

    /**
     * 二级行政区(地级行政区):地级市、地区、自治州、盟
     */
    private String city;

    /**
     * 三级行政区(县级行政区):县、县级市、市辖区、自治县、旗、自治旗、矿区、林区、特区
     */
    private String area;

    /**
     * 四级行政区(乡级行政区):乡、民族乡、镇、街道
     */
    private String town;

    /**
     * 详细地址
     */
    private String detail_address;

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getDetail_address() {
        return detail_address;
    }

    public void setDetail_address(String detail_address) {
        this.detail_address = detail_address;
    }

    @Override
    public String toString() {
        return "WmOpenRecipientAddressDetail{" +
                "province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", area='" + area + '\'' +
                ", town='" + town + '\'' +
                ", detail_address='" + detail_address + '\'' +
                '}';
    }
}