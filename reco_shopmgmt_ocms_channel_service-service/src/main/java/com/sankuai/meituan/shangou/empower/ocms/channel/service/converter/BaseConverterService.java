package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * 饿了么实体转换统一接口类
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:09
 **/
@Mapper(componentModel = "spring", uses ={MappingConverterUtils.class}, imports = {ConverterUtils.class})
public interface BaseConverterService {

    @Mappings({
    })
    BaseRequest baseRequest(BaseRequestSimple param);

    @Mappings({
    })
    BaseRequestSimple baseSimpleRequest(BaseRequest param);

    @Mappings({
    })
    BaseRequest baseRequest(ChannelPoiCategoryRequest param);

    @Mappings({
            @Mapping(target = "level", constant = "2"),
    })
    CategoryInfoDTO degradeCategory(CategoryInfoDegradeDTO data);

    @Mappings({
            @Mapping(target = "channelCategoryCode", source = "channelCode"),
    })
    CategoryInfoDeleteDTO degradeCategoryDel(CategoryInfoDegradeDTO data);

    @Mappings({
            @Mapping(target = "channelCategoryCode", source = "channelCode"),
    })
    CategoryInfoDeleteDTO degradeCategory(CategoryPoiResult data);
    List<CategoryInfoDeleteDTO> degradeCategory(List<CategoryPoiResult> data);

    @Mappings({
            @Mapping(target = "code", source = "skuId"),
            @Mapping(target = "channelCode", source = "channelResultInfo"),
            @Mapping(target = "resultCode", constant = "0"),
    })
    CategoryPoiResult toUpdateCategoryResponse(ResultSuccessSku resultData);
    List<CategoryPoiResult> toUpdateCategoryResponseSuc(List<ResultSuccessSku> resultData);

    @Mappings({
            @Mapping(target = "code", source = "skuId"),
            @Mapping(target = "msg", source = "errorMsg"),
            @Mapping(target = "resultCode", constant = "1"),
    })
    CategoryPoiResult toUpdateCategoryResponse(ResultErrorSku resultData);
    List<CategoryPoiResult> toUpdateCategoryResponseFail(List<ResultErrorSku> resultData);

    @Mappings({
            @Mapping(target = "data", source = "errorData"),
    })
    UpdateCategoryResponse toUpdateCategoryResponse(ResultData resultData);


    @Mappings({
    })
    CategoryInfoDTO adjustCategoryLevel(CategoryLevelAdjustDTO data);

    @Mappings({
            @Mapping(target = "channelCategoryCode", source = "channelCode"),
            @Mapping(target = "parentCode", source = "oldParentCode"),
            @Mapping(target = "channelParentCategoryCode", source = "oldChannelParentCode"),
    })
    CategoryInfoDeleteDTO adjustCategoryLevelDel(CategoryLevelAdjustDTO data);

    @Mappings({
            @Mapping(target = "level", constant = "1"),
            @Mapping(target = "parentCode", ignore = true),
            @Mapping(target = "channelParentCode", ignore = true),
            @Mapping(target = "parentName", ignore = true),
    })
    CategoryInfoDTO upgradeCategoryLevel(CategoryLevelAdjustDTO data);


    @Mappings({
            @Mapping(target = "level", constant = "2"),
    })
    CategoryInfoDTO degradeCategoryLevel(CategoryLevelAdjustDTO data);
}