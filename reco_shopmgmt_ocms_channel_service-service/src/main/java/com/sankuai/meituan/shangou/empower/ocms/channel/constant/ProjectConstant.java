package com.sankuai.meituan.shangou.empower.ocms.channel.constant;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;

public final class ProjectConstant {

    /**
     * Rhino
     */
    public static final long RHINO_TIMEOUT = 100000;
    public static final float RHINO_ERROR_PERCENTAGE = 50f;

    /**
     * Common
     */
    public static final long UNKNOW_TENANT_ID = 0L;
    public static final long UNKNOW_STORE_ID = -1L;
    public static final int LIST_PARTITION_NUM = 50;
    public static final long CHANNEL_JD2HOME_GLOBAL_STORE_ID = 0L;
    // 狗东默认品牌ID（其他品牌）
    public static final String JDDJ_DEFAULT_BRAND_ID = "35247";
    // 京东SPU更新规格不存错误码
    public static final String JDDJ_UPDATE_SPU_SKU_NOT_EXIST_CODE = "11004";
    // 京东SKU恢复规格规格存在错误
    public static final String JDDJ_UPDATE_SPU_SKU_ROLLBACK_EXIST_MSG = "非删除状态商品，无需恢复";
    // 京东SKU删除规格不存错误码
    public static final Integer JDDJ_DELETE_SKU_NOT_EXIST_CODE = 1009001;
    // 京东SPU下架或删除 多规格商品不存在错误码
    public static final String JDDJ_OFFSALE_OR_DELETE_SPU_NOT_EXIST_CODE = "11035";
    // 京东图片裁剪后缀
    public static final String JDDJ_IMAGE_CUT_SUFFIX = "@800w_800h_1e_1c";
    public static final Pattern JDDJ_SKU_EXIST_MSG_PATTERN = Pattern.compile("^商家skuId已存在:(\\d+)$");

    public static final String SUCCESS_CODE = "0";

    public static final int YES = 1;
    public static final int NO = 0;

    public static final String RESULT = "result";
    public static final String CODE = "code";
    public static final String DATA = "data";
    public static final String EXTRA_INFO = "extra_info";
    public static final String TOTAL_COUNT = "total_count";
    public static final String NG = "ng";
    public static final String OK = "ok";
    public static final String MSG = "msg";
    public static final String STATUS = "status";
    public static final String CHILDREN = "children";

    public static final String ERRNO = "errno";
    public static final String ERROR = "error";
    public static final String ERROR_LIST = "error_list";
    public static final String BODY = "body";
    public static final String SUCCESS_LIST = "success_list";

    public static final String ORDER_ID = "order_id";
    public static final String DISPATCH_ORDER_ID = "dispatch_order_id";

    public static final String REFUND_ID = "refund_id";

    public static final String ORDER_IDS = "order_ids";
    public static final String ORDER_ID_HUMP_ = "orderId";
    public static final String APP_POI_CODES = "app_poi_codes";
    public static final String SHOP_ID = "shop_id";
    public static final String SHOP_NAME = "storeName";

    public static final String SHOP_LIST = "shop_list";

    public static final String BAIDU_SHOP_ID = "baidu_shop_id";

    public static final String THIRD_PARTY_SHOP_ID = "thirdPartyShopId";

    public static final String STORE_NO = "StoreNo";
    public static final String STATION_NO = "stationNo";
    public static final String OPERATOR = "operator";
    public static final Object OUT_SYSTEM_ID = "outSystemId";
    public static final String DEPTH = "depth";
    public static final String PARENT_ID = "parent_id";
    public static final String ID = "id";
    public static final String FIELDS = "fields";
    public static final String BRAND_NAME = "brandName";
    public static final String PRODUCT_NAME = "productName";
    public static final String PAGE_NO = "pageNo";
    public static final String PAGE_SIZE = "pageSize";
    public static final String COUNT = "count";
    public static final String ORDER_REASON = "reason";
    public static final String ORDER_REASON_CODE = "reason_code";
    public static final String FOOD_DATA = "food_data";
    //美团医药查询审核状态接口返回值
    public static final String MT_YY_DATA = "hits";
    public static final String CLOSE_STATUS = "closeStatus";
    public static final String STORE_NOTICE = "storeNotice";
    public static final String PART_REFUND_TYPE = "part_refund_type";
    public static final String SECRET = "secret";
    public static final String REFUND_TOTAL = "refund_total";

    public static final String ELM_PROMOTION_INFO = "content";
    public static final String ELM_SHIPPING_TIME = "business_time";
    public static final String ELM_NEW_SHIPPING_TIME = "business_time2";
    public static final String ELM_HOTLINE = "service_phone";

    public static final String ELM_BUSINESS_STATUS = "shop_busstatus";
    public static final String ELM_SOURCE = "source";

    public static final String ELM_GRANT_TYPE = "grant_type";
    public static final String ELM_NEED_REFRESH_TOKEN = "need_refresh_token";
    public static final String ELM_CLIENT_ID = "client_id";
    public static final String ELM_CLIENT_SECRET = "client_secret";
    public static final String ELM_CODE = "code";
    public static final String MONEY = "money";

    public static final int MT_CATEGORY_MAX_LENGTH = 8;
    public static final int ELM_CAT_MIN_DEPTH = 1;
    public static final int ELM_CAT_MAX_DEPTH = 3;
    public static final int ELM_CAT_MIN_SORT = 1;
    public static final int ELM_CAT_MAX_SORT = 100000;
    public static final int JDDJ_BRAND_VALID = 2;
    public static final int JDDJ_CATEGORY_VALID = 1;
    public static final int MT_WEIGHT_REFUND_TYPE = 3;

    public static final String JDDJ_APP_KEY = "app_key";

    public static final String TYPE = "type";
    public static final String APP_POI_CODE = "app_poi_code";

    public static final String DOUYIN_APP_KEY = "app_key";

    public static final String DOUYIN_SECRET = "secret";

    public static final String DOUYIN_SHOP_ID = "shop_id";

    public static final String DOUYIN_ACCESS_TOKEN = "access_token";

    /**
     * kms key，存储抖音渠道的appKey、secret
     */
    public static final String DOUYIN_APPKEY_SECRET_KMS_KEY = "douyin_appkey_secret";

    public static final String OCMS_APP_KEY = "com.sankuai.shangou.empower.ocms";


    public static final String TXD_APP_KEY = "app_key";
    public static final String TXD_SESSION_KEY = "sessionKey";

    /**
     * 抖音授权码
     */
    public static final String DOUYIN_AUTH_COADE = "auth_code";
    public static final int DOUYIN_GET_TOKEN_MAX_RETRY= 3;
    public static final String MT_ACCESS_TOKEN = "access_token";
    public static final String MT_ACCESS_TOKEN_CACHE = "mt_access_token_cache";
    public static final String APP_POI_ID = "wm_poi_id";
    public static final String CATEGORY_CODE = "category_code";
    public static final String CATEGORY_NAME = "category_name";
    public static final String SEQUENCE = "sequence";
    public static final String CATEGORY_CODE_ORIGIN = "category_code_origin";
    public static final String CATEGORY_NAME_ORIGIN = "category_name_origin";
    public static final String CATEGORY_SEQUENCE_DATA = "category_sequence_data";
    public static final String SMART_SWITCH = "smart_switch";
    public static final String TOP_FLAG = "top_flag";
    public static final String WEEKS_TIME = "weeks_time";
    public static final String PERIOD = "period";
    public static final String SECONDARY_CATEGORY_CODE = "secondary_category_code";
    public static final String SECONDARY_CATEGORY_NAME = "secondary_category_name";
    public static final String MEDICINE_SECONDARY_CATEGORY_CODE = "second_category_code";
    public static final String MEDICINE_SECONDARY_CATEGORY_NAME = "second_category_name";
    public static final String MEDICINE_SECONDARY_SEQUENCE = "second_sequence";
    public static final String MT_APP_ID_KEY = "app_id";
    public static final String MT_PROMOTION_INFO = "promotion_info";
    public static final String MT_SHIPPING_TIME = "shipping_time";

    public static final String MT_HOTLINE = "phone";
    public static final String MT_ADDRESS = "address";
    public static final String MT_LATITUDE = "latitude";
    public static final String MT_LONGITUDE = "longitude";
    public static final String MT_PREBOOK = "pre_book";
    public static final String MT_PREBOOK_MIN_DAYS = "pre_book_min_days";
    public static final String MT_PREBOOK_MAX_DAYS = "pre_book_max_days";
    public static final String MT_AREA = "area";
    public static final String MT_MIN_PRICE = "min_price";
    public static final String MT_SHIPPING_FEE = "shipping_fee";
    public static final String MT_APP_SHIPPING_CODE = "app_shipping_code";

    public static final String MT_SHIPPING_ID = "mt_shipping_id";
    public static final String MT_TYPE = "type";
    public static final String TIME_RANGE = "time_range";
    public static final String MT_POI_PAGE_NUM = "page_num";
    public static final String MT_POI_PAGE_SIZE = "page_size";
    public static final String SHIPPING_DATA = "shipping_data";
    public static final String UPC_CODE = "upc_code";
    public static final String NAME = "name";
    public static final String MT_KEYWORD = "keyword";
    public static final String MT_ATTR_ID = "attr_id";
    public static final String MT_NEW_APP_ID_KEY = "new_app_id";
    public static final String MT_AUTHORIZE_CODE = "authorize_code";

    public static final String TAG_ID = "tag_id";
    public static final String MT_GENERAL_ATTRS = "general_attrs";
    public static final String MT_WEIGHT_FOR_UNIT = "weight_for_unit";;
    public static final String MT_WEIGHT_UNIT = "weight_unit";

    public static final String REFUND_PRICE = "refund_price";

    //美团按金额灵活退类型常量
    public static final String MT_AMOUNT_REFUND_TYPE = "5";

    public static final String MT_APP_SPU_CODE = "app_spu_code";

    /**
     * weixin相关字段常量
     */
    public static final Integer SUCCESS = 0;
    public static final Integer FAIL = 1;
    public static final String OPEN_ID = "openid";
    public static final String USER_LIST = "user_list";

    public static final String ACTION_NAME = "action_name";
    public static final String ACTION_INFO = "action_info";
    public static final String EXPIRE_SECONDS = "expire_seconds";

    public static final String YZ_GRANT_ID = "grant_id";
    public static final String YZ_CLIENT_ID = "client_id";

    public static final String SPU_DATA = "spu_data";
    public static final String DATE_TIME = "date_time";
    public static final String DAY_SEQS = "day_seqs";

    public static final String PARENT_CATEGORY_CODE_ZERO = "0";

    /**
     * 抖音相关字段常量
     */
    public static final String DOUYIN_OPERATE_TYPE = "operate_type";
    public static final String DOUYIN_CATEGORY_ID = "category_id";
    public static final String DOUYIN_CATEGORY_NAME = "category_name";
    public static final String DOUYIN_PARENT_CATEGORY_ID = "parent_category_id";
    public static final String DOUYIN_RANK = "rank";
    public static final String DOUYIN_TEMP_ID = "tmp_id";

    /**
     * 抖音店内分类不存在的错误码
     */
    public static final String DOUYIN_CATEGORY_NOT_EXISTS_ERROR_CODE = "isv.business-failed:20125";

    public static final String DOUYIN_SEQUENCE = "sequence";
    /**
     * 抖音店内分类最大排序
     */
    public static final int DOUYIN_CATEGORY_MAX_RANK = 50000;
    public static final int DOUYIN_CATEGORY_MAX_LENGTH = 16;

    /**
     * jddj内部字段
     */
    public static final String JDDJ_FIELD_ID = "ID";
    public static final String JDDJ_FIELD_CATEGORY_NAME = "CATEGORY_NAME";
    public static final String JDDJ_FIELD_PID = "PID";
    public static final String JDDJ_FIELD_CATEGORY_LEVEL = "CATEGORY_LEVEL";
    public static final String JDDJ_FIELD_CATEGORY_STATUS = "CATEGORY_STATUS";
    public static final String JDDJ_FIELD_CATEGORY_LEAF = "LEAF";
    public static final String JDDJ_FIELD_CHECK_UPC_STATUS = "CHECK_UPC_STATUS";

    public static final String JDDJ_FIELD_BRAND_ID = "BRAND_ID";
    public static final String JDDJ_FIELD_BRAND_NAME = "BRAND_NAME";
    public static final String JDDJ_FIELD_BRAND_STATUS = "BRAND_STATUS";
    public static final String JDDJ_FIELD_BRAND = "brand";
    public static final String JDDJ_FIELD_CATEGORY = "category";
    public static final String JDDJ_FIELD_BRANDID = "brandId";
    public static final String JDDJ_FIELD_CATEGORYID = "categoryId";

    //创建订单
    public static final String JDDJ_NEW_ORDER_STATUS = "41000";
    //确认订单
    public static final String JDDJ_BIZ_CONFIRM_STATUS = "32000";

    public static final String IMG_DATA = "img_data";

    public static final String DEFAULT_PICTURE_CACHE = "channel_default_picture_cache";
    public static final String MT_ERROR_RESPONSE_ERROR_LIST_KEY = "error_list";
    public static final String MT_SUCCESS_MAP_KEY = "success_map";

    public static final String ACT_DELETE_EXIST_MSG = "活动ID不存在";

    /**
     * 商品消息通知
     * opAppKey目前的返回值：
     * 商家端_移动端
     * 蜜蜂
     * 先富
     * 商家端_PC端
     * C端
     * 商家app端
     * pc端
     * 其他端
     */
    public static final String SKU_NOTIFY_MOBILE = "商家端_移动端";
    public static final String SKU_NOTIFY_PC = "商家端_PC端";
    public static final String SKU_NOTIFY_APP = "商家app端";
    /**
     * 评价字段
     */
    public static final String COMMENT_START_TIME = "start_time";
    public static final String COMMENT_END_TIME = "end_time";
    public static final String COMMENT_ID = "comment_id";
    public static final String COMMENT_REPLY = "reply";
    public static final String COMMENT_REPLY_CONTENT = "content";
    public static final String COMMENT_ORDER_ID = "orderId";
    public static final String COMMENT_MT_PAGE_OFFSET = "pageoffset";
    public static final String COMMENT_MT_PAGE_SIZE = "pagesize";
    public static final String COMMENT_MT_REPLY_STATUS = "replyStatus";
    public static final String COMMENT_ELM_PAGE = "page";
    public static final String COMMENT_ELM_REPLY_STATUS = "reply_status";
    public static final String COMMENT_ELM_COMMENT_LIST = "comment_list";
    public static final String COMMENT_JJDJ_STORE_ID = "storeId";
    public static final String COMMENT_JJDJ_REPLY_PIN = "replyPin";

    /**
     * 评价渠道回复状态：未回复、已回复、回复失败
     */
    public static final String CHANNEL_COMMENT_REPLY_STATUS_NOT_REPLY = "0";
    public static final String CHANNEL_COMMENT_REPLY_STATUS_REPLIED = "1";
    public static final String CHANNEL_COMMENT_REPLY_STATUS_REPLY_FAILED = "2";

    /**
     * 美团评价回复状态，0:未回复，1:已回复
     */
    public static final String COMMENT_MT_REPLY_STATUS_0 = "0";
    public static final String COMMENT_MT_REPLY_STATUS_1 = "1";

    /**
     * 京东到家评价回复状态，1:未审核, 2：通过, 3：不通过
     */
    public static final Integer COMMENT_JDDJ_REPLY_STATUS_1 = 1;
    public static final Integer COMMENT_JDDJ_REPLY_STATUS_2 = 2;
    public static final Integer COMMENT_JDDJ_REPLY_STATUS_3 = 3;

    /**
     * 踩赞商品
     */
    public static final Integer COMMENT_JDDJ_PRAISE_ITEM = 1;
    public static final Integer COMMENT_JDDJ_CRITIC_ITEM = 2;

    /**
     * 评价模块，thrift接口中整数类型NULL值使用-1标识
     */
    public static final int COMMENT_THRIFT_INT_VALUE_NULL = -1;

    public static final String OTHER_REASON = "其他原因";

    /**
     * 英文逗号限定符
     **/
    public static final String ENGLISH_COMMA_DELIMITER = ",";

    /**
     * 删除渠道商品，幂等处理错误码
     */
    public static final String DELETE_CHANNEL_SKU_IDEMPOTENT_CODE = "delete_channel_sku_idempotent_code";

    /**
     * 删除渠道商品，幂等处理错误文案
     */
    public static final String DELETE_CHANNEL_SKU_IDEMPOTENT_MSG = "delete_channel_sku_idempotent_msg";

    public static final int CATEGORY_MAX_LENGTH = 8;
    public static final String CATEGORY_MAX_LENGTH_MSG = "分类名称不能超过8字符，请修改后重试";

    public static final String PERSONALIZED_RATE_LIMITER_CONF_FILE = "personalized_rate_limiter.conf";
    public static final String APP_KEY = "com.sankuai.shangou.empower.ocmschannel";

    /**
     * 克重退差试算参数
     **/
    public static final String APP_FOOD_CODE = "app_food_code";
    /**
     * 克重退差试算参数
     **/
    public static final String SKU_ID = "sku_id";
    /**
     * 克重退差试算参数
     **/
    public static final String ACTUAL_WEIGHT = "actual_weight";
    /**
     * 克重退差试算参数
     **/
    public static final String ITEM_ID = "item_id";
    /**
     * 克重退差试算参数
     **/
    public static final String RESULT_CODE = "result_code";
    /**
     * 标识哪些接口做了限频优化. 这里用一个set来标识哪些接口做了优化， 主要是因为简单。如果通过ChannelPostInter加
     * 个变量来标识，会导致所有枚举类都要改动。
     * 如果后续迁移接口较多，则推荐在如果通过ChannelPostInter加接口来区分
     */
    public static final Set<ChannelPostInter> RHINO_UPTIMATE_SET = new HashSet<ChannelPostInter>() {{
        add(ChannelPostMTEnum.PRICE_UPDATE);
        add(ChannelPostJDDJEnum.PRICE_UPDATE);
        add(ChannelPostELMEnum.PRICE_UPDATE);
        add(ChannelPostMTEnum.BATCH_GET_POIDETAIL);
        add(ChannelPostJDDJEnum.SHOP_DETAIL);
        add(ChannelPostELMEnum.GET_POIDETAIL);
        add(ChannelPostMTEnum.GET_POI_INNER_ID);
    }};

    /**
     * 闪购商品审核系统OPNAME
     */
    public static final String SHANGOU_PRODUCT_AUDIT_SYSTEM_OP_NAME = "闪购商品审核系统";

    /**
     * 麦芽田跑腿无效回调
     */
    public static final String FARM_PAO_TUI_INVALID_CALLBACK = "invalid";

    public static final String WECHAT_ACCESS_TOKEN = "access_token";
    /**
     * 微信公众号appID
     **/
    public static final String WECHAT_APP_ID_KEY = "appid";

    /**
     * 规格不可售标志
     */
    public static final Integer SKU_NONE_SELL_FLAG = 2;

    //麦芽田配送状态打点
    public static final String MALT_FARM_STATUS_CAT_STR = "MALT_FARM_STATUS";

    //麦芽田跑腿状态打点
    public static final String MALT_FARM_PAOTUI_STATUS_CAT_STR = "MALT_FARM_PAOTUI_STATUS";

    // 麦芽田骑手转派埋点
    public static final String MALT_FARM_RIDER_TRANSFER_RIDER_CAT_STR = "MALT_FARM_RIDER_TRANSFER_RIDER";

    //给麦芽田同步跑腿消息打点
    public static final String MALT_FARM_PAOTUI_POST_CAT_STR = "MALT_FARM_PAOTUI_POST";

    //给麦芽田同步跑腿锁单2.0解锁消息打点
    public static final String MALT_FARM_PAOTUI_LOCK_ORDER_V2_POST_CAT_STR = "MALT_FARM_PAOTUI_LOCK_ORDER_V2_POST";

    //麦芽田发跑腿打点
    public static final String MALT_FARM_PAOTUI_LAUNCH_CAT_STR = "MALT_FARM_PAOTUI_LAUNCH";

    //麦芽田取消跑腿打点
    public static final String MALT_FARM_PAOTUI_CANCEL_CAT_STR = "MALT_FARM_PAOTUI_CANCEL";

    //麦芽田跑腿骑手位置
    public static final String MALT_FARM_PAOTUI_LOCATION_CAT_STR = "MALT_FARM_PAOTUI_LOCATION";

    // 饿了么拣货完成距离接单不足1min
    public static final String ELM_PREPARATION_MEAL_COMPLETE_ILLEGAL_CAT_STR = "ELM_PREPARATION_MEAL_COMPLETE_ILLEGAL";

    //给麦芽田同步订单信息变更失败打点
    public static final String MALT_FARM_SYNC_ORDER_CHANGE_FAILED_CAT_STR = "MALT_FARM_SYNC_ORDER_CHANGE_FAILED";

    //给麦芽田同步订单信息变更，极端场景下订单信息修改同步到麦芽田侧时配送状态已经改变
    public static final String MALT_FARM_SYNC_ORDER_CHANGE_DELIVERY_STATUS_ALREADY_CHANGED_CAT_STR = "MALT_FARM_SYNC_ORDER_CHANGE_DELIVERY_STATUS_ALREADY_CHANGED";

    //饿了渠道规格自定义ID
    public final static Long ELM_SELF_DEFINE_PROP_ID = 168606316L;

    //饿了么渠道医药类目的规格ID
    public static final Long ELM_MED_SELF_DEFINE_PROP_ID = 253529750L;

    //青云智送取消跑腿打点
    public static final String DAP_PAOTUI_CANCEL_CAT_STR = "DAP_PAOTUI_CANCEL";

    //青云智送跑腿骑手位置
    public static final String DAP_PAOTUI_LOCATION_CAT_STR = "DAP_PAOTUI_LOCATION";

    //青云查询门店信息
    public static final String DAP_SHOP_DETAIL_CAT_STR = "DAP_SHOP_DETAIL";


    //青云智送配送状态打点
    public static final String DAP_STATUS_CAT_STR = "DAP_STATUS";

    public static final String DAP_ERROR_CAT_STR = "DAP_ERROR";

    //给青云同步跑腿消息打点
    public static final String DAP_PAOTUI_POST_CAT_STR = "DAP_PAOTUI_POST";

    // 京东渠道同步接口打点type
    public static final String JDDJ_UPDATE_DELIVERY_INFO_CAT_TYPE = "JDDJ_UPDATE_DELIVERY_INFO_CAT_TYPE";

    public static final String JDDJ_UPDATE_RIDER_INFO_CAT_TYPE = "JDDJ_UPDATE_RIDER_INFO_CAT_TYPE";

    // 京东渠道同步骑手信息变更接口打点type
    public static final String JDDJ_RIDER_INFO_CHANGE_CAT_TYPE = "JDDJ_RIDER_INFO_CHANGE_CAT_TYPE";

    //开放平台渠道特殊属性值为空的code
    public final static Integer PLATFORM_CHANNEL_SPECIAL_ATTR_EMPTY_VALUE_CODE = 6000;

    public static final String NO_RESPONSE = "渠道返回信息为空";

    public static final String EQUAL_STR = "=";

    public static final String AND_STR = "&";

    public static final String PROMOTION_ID = "promotionId";

    public static final String VENDER_PAY_MONEY = "venderPayMoney";

    public static final String PLAT_PAY_MONEY = "platPayMoney";


    // 连字符
    public static final String HYPHEN = "-";
    public static final String JD_SINGLE_DEFAULT_PAGE_INFO = "1";

    public static final String JD_DEFAULT_FILTER_DELETE = "0";

    public static final String ASTERISK = "*";

    //麦芽田跑腿降级状态打点
    public static final String MALT_FARM_PAOTUI_STATUS_FALLBACK_CAT_STR = "MALT_FARM_PAOTUI_STATUS_FALLBACK";

    //青云跑腿降级状态打点
    public static final String DAP_FARM_PAOTUI_STATUS_FALLBACK_CAT_STR = "DAP_FARM_PAOTUI_STATUS_FALLBACK";

    public static final String TRIGGER_LIMIT_MSG = "调用频繁，请稍后再试。";

    // 淘鲜达渠道同步自配送骑手坐标打点
    public static final String TXD_UPDATE_RIDER_INFO_CAT_TYPE = "TXD_UPDATE_RIDER_INFO_CAT_TYPE";

    // 淘鲜达渠道同步骑手信息变更打点
    public static final String TXD_RIDER_INFO_CHANGE_CAT_TYPE = "TXD_RIDER_INFO_CHANGE_CAT_TYPE";

    //用户逆向退货运费，返货预估运费
    public static final String PRE_REFUND_GOODS_SHIPPING_FEE = "preReturnFreight";
    //用户逆向退货运费，返回真实运费
    public static final String USER_REFUND_GOODS_SHIPPING_FEE = "returnFreight";
    //配送方式，1：自配 2：三方配送
    public static final String DELIVERY_METHOD = "fulfillment_code";
    // 聚合配送查询变更后履约信息
    public static final String QUERY_CHANGED_FULFILLMENT_INFO = "queryChangedFulfillmentInfo";

    // 淘鲜达
    public static final String TXD_SUCCESS_TRUE = "true";

    public static final String TXD_FAILED_DATA = "false";

    public static final String TXD_SUCCESS_MSG = "SUCCESS";
    // 抖音平台配送消息过滤
    public static final String DY_DELIVERY_CHANGE_FILTER = "DY_DELIVERY_CHANGE_FILTER";

    public static final String DAP_LAUNCH_DELIVERY_CAT_STR = "DAP_LAUNCH_DELIVERY";

    public static final String MALT_LAUNCH_DELIVERY_CAT_STR = "MALT_LAUNCH_DELIVERY";

    public static final String MT_TOKEN_PROCESS_TYPE = "MT_TOKEN_PROCESS_TYPE";


    private ProjectConstant() {
    }

    /**
     * 商家自配送下，可以同步骑手轨迹的配送状态
     */
    public static final Set<DeliveryStatus> SELF_DELIVERY_LOCATION_SYNC_SET = new HashSet<DeliveryStatus>() {{
        add(DeliveryStatus.RIDER_ACCEPTED_ORDER);
        add(DeliveryStatus.RIDER_ARRIVE_SHOP);
        add(DeliveryStatus.RIDER_TAKEN_MEAL);
    }};
}
