package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.tenant;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ChannelConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ChannelConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.TenantSwitchGetRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantChannelConfigResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantSwitchGetResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TenantBizModeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TenantTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-05-23
 */
@Service
@Slf4j
public class TenantServiceImpl implements TenantService {

    private static final String SPU_GRAY_KEY = "SPU_GRAY";
    private static final String ERP_KEY = "ERP";

    @Autowired
    private TenantRemoteService tenantRemoteService;

    private final Cache<String, Object> LOCAL_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    @Autowired
    private ConfigThriftService configThriftService;

    @Override
    public boolean isSpuGray(Long tenantId) {
        String key = String.format("%s_%d", SPU_GRAY_KEY, tenantId);

        try {
            Object result = LOCAL_CACHE.get(key, () -> getSpuGrayFlagWithoutCache(tenantId));
            return ((boolean) result);
        } catch (ExecutionException e) {
            log.error("获取租户灰度信息异常 tenantId:{}", tenantId, e);
            throw new ChannelBizException("获取SPU租户配置出错", e);
        }
    }

    @Override
    public boolean isErpSpuTenant(Long tenantId) {
        String key = String.format("%s_%d", ERP_KEY, tenantId);
        try {
            Object result = LOCAL_CACHE.get(key, () -> queryTenantHasErp(tenantId));
            return (boolean) result;
        } catch (ExecutionException e) {
            log.error("获取ERP租户配置异常 tenantId:{}", tenantId, e);
            throw new ChannelBizException("获取ERP租户配置异常", e);
        }
    }

    @Override
    public TenantTypeEnum getTenantTypeEnum(Long tenantId) {

        TenantTypeEnum typeEnum = TenantTypeEnum.NO_ERP;;
        try {
            if (!isSpuGray(tenantId)){
                //单规格
                typeEnum = TenantTypeEnum.SINGLE;
            } else {
                //若为多规格，则查询租户erp类型
                if (isErpSpuTenant(tenantId)) {
                    typeEnum = TenantTypeEnum.ERP;
                } else if (isMedicineAdultUnmanWarehouse(tenantId)) {
                    typeEnum = TenantTypeEnum.MEDICINE_ADULT_UNMAN_WAREHOUSE;
                } else {
                    typeEnum = TenantTypeEnum.NO_ERP;
                }
            }
        } catch (Exception e){
            log.error("租户类型查询异常 tenantId={}",tenantId,e);
        }
        return typeEnum;
    }

    /**
     * 是否是医药成人无人仓业务租户
     */
    @Override
    public boolean isMedicineAdultUnmanWarehouse(Long tenantId) {
        try {
            return Objects.equals(TenantBizModeEnum.MEDICINE_ADULT_UNMAN_WAREHOUSE, tenantRemoteService.getTenantBizMode(tenantId));
        } catch (Exception e) {
            log.error("TenantServiceImpl.isMedicineAdultUnmanWarehouse 查询是否是医药成人无人仓租户失败, tenantId: {}, error: ", tenantId, e);
            throw e;
        }
    }

    @Override
    public boolean isNoErpBTenant(Long tenantId) {
        TenantBizModeEnum bizModeEnum =  tenantRemoteService.getTenantBizMode(tenantId);
        return Objects.equals(TenantBizModeEnum.MEDICINE_ADULT_UNMAN_WAREHOUSE, bizModeEnum)
                || Objects.equals(TenantBizModeEnum.CONVENIENCE_STORE, bizModeEnum)
                || Objects.equals(TenantBizModeEnum.MTSG_FLAGSHIPSTORE, bizModeEnum)
                || Objects.equals(TenantBizModeEnum.CAI_DA_QUAN, bizModeEnum);
    }

    /**
     * 直接调用接口获取租户灰度标志
     * @param tenantId
     * @return
     */
    private boolean getSpuGrayFlagWithoutCache(Long tenantId) {
        try {
            Map<String, String> configs = queryTenantSwitch(tenantId, Lists.newArrayList(SPU_GRAY_KEY));
            String grayValue = configs.getOrDefault(SPU_GRAY_KEY, "");
            return "1".equals(grayValue);
        } catch (Exception e) {
            log.error("获取租户灰度标志异常 tenantId:{}", tenantId, e);
            throw new ChannelBizException("获取SPU租户配置出错", e);
        }

    }

    public Map<String, String> queryTenantSwitch(Long tenantId, List<String> switchKeyList) {

        if (CollectionUtils.isEmpty(switchKeyList)) {
            return Collections.emptyMap();
        }

        // 查询租户开关配置
        TenantSwitchGetRequest request = new TenantSwitchGetRequest();
        request.setTenantId(tenantId);
        request.setSwitchKey(switchKeyList);

        TenantSwitchGetResponse response = configThriftService.getTenantSwitch(request);

        if (response == null || response.getStatus() == null) {
            throw new ChannelBizException("查询租户开关配置错误：未返回结果");
        }
        if (response.getStatus().getCode() != 0) {
            throw new ChannelBizException("查询租户开关配置错误："+response.getStatus().getMessage());
        }

        return response.getSwitchValue();
    }

    public boolean queryTenantHasErp(long tenantId) {
        ConfigDto configDto = queryTenantConfig(tenantId, tenantId, ConfigItemEnum.HAS_ERP);
        if (configDto == null || StringUtils.isBlank(configDto.getConfigContent())) {
            return false;
        }
        return ConfigItemEnum.HAS_ERP.isMainConfigYesStr(configDto.getConfigContent());
    }

    public ConfigDto queryTenantConfig(long tenantId, long subjectId, ConfigItemEnum configItemEnum) {
        ConfigQueryRequest request = new ConfigQueryRequest();
        request.setTenantId(tenantId);
        request.setSubjectId(subjectId);
        request.setConfigId(configItemEnum.getConfigId());
        TenantConfigResponse response;
        try {
            response = configThriftService.queryTenantConfig(request);
            log.info("查询租户配置 request:{},response:{}", request, response);
        } catch (Exception e) {
            log.error("查询租户配置异常 request:{}", request, e);
            throw new ChannelBizException("查询租户配置异常", e);
        }

        if (response == null || response.getStatus() == null) {
            throw new ChannelBizException("未返回结果");
        }

        if (response.getStatus().getCode() != 0) {
            throw new ChannelBizException(response.getStatus().getMessage());
        }

        return response.getConfig();
    }

    @Override
    public boolean isConvenienceOrFlagshipStoreMode(Long tenantId) {

        TenantBizModeEnum bizMode = tenantRemoteService.getTenantBizMode(tenantId);
        if (Objects.isNull(bizMode)) {
            throw new BizException("未获取到租户的业务模式");
        }

        return (TenantBizModeEnum.CONVENIENCE_STORE == bizMode || TenantBizModeEnum.MTSG_FLAGSHIPSTORE == bizMode);
    }

    @Override
    public boolean isMerchantsChargePackageFee(Long tenantId, DynamicChannelType dynamicChannelType) {
        try {
            ChannelConfigQueryRequest request = new ChannelConfigQueryRequest();
            request.setTenantId(tenantId);
            request.setConfigId(ConfigItemEnum.CHARGING_MERCHANT_BAG_FEES.getKey());
            request.setSubjectId(tenantId);
            request.setChannelId(dynamicChannelType.getChannelId());
            TenantChannelConfigResponse response = configThriftService.queryTenantChannelConfig(request);
            if (response.getStatus() != null && response.getStatus().getCode() == StatusCodeEnum.SUCCESS.getCode()
                    && response.getChannelConfig() != null && response.getChannelConfig().getConfigContent() != null) {
                return ConfigItemEnum.CHARGING_MERCHANT_BAG_FEES
                        .isMainConfigYesStr(response.getChannelConfig().getConfigContent());
            }
        } catch (Exception e) {
            log.error("租户[{}] 渠道[{}] queryMerchantsChargePackageFeeConfig error", tenantId,
                    dynamicChannelType.getDesc(), e);
        }
        return false;
    }

    @Override
    public ChannelConfigDto queryTenantChannelConfig(long tenantId, long subjectId, int channelId, ConfigItemEnum configItemEnum) {
        ChannelConfigQueryRequest request = new ChannelConfigQueryRequest();
        request.setTenantId(tenantId);
        request.setSubjectId(subjectId);
        request.setConfigId(configItemEnum.getConfigId());
        request.setChannelId(channelId);
        TenantChannelConfigResponse response;
        try {
            response = configThriftService.queryTenantChannelConfig(request);
            log.info("查询租户渠道配置 request:{}, response:{}", request, response);
        }
        catch (Exception e) {
            log.error("查询租户渠道配置异常 request:{}", request, e);
            throw new IllegalStateException("查询租户渠道配置异常");
        }

        if (response == null || response.getStatus() == null) {
            log.error("查询租户渠道配置无响应 request={}, response={}", request, response);
            throw new IllegalStateException("查询租户渠道配置无响应");
        }

        if (response.getStatus().getCode() != 0) {
            throw new IllegalStateException(response.getStatus().getMessage());
        }
        return response.getChannelConfig();
    }

}
