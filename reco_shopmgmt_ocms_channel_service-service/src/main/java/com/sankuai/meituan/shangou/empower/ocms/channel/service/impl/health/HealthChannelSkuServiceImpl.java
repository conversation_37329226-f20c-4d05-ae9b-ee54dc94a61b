package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.health;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSkuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelSkuServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 美团渠道商品内部服务接口
 *
 * @author: chenhaie
 * @create: 2019/1/7 下午5:27
 */
@Service("healthChannelSkuService")
public class HealthChannelSkuServiceImpl extends MtBrandChannelSkuServiceImpl implements ChannelSkuService {

}
