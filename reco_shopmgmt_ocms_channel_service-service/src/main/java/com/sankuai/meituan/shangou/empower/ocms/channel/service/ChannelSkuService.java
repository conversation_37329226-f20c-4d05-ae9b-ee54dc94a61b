package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;

/**
 * @description: 渠道商品品类内部服务接口，各渠道父接口
 * @author: ch<PERSON><PERSON>e
 * @create: 2019/1/7 下午5:27
 */
public interface ChannelSkuService {
    /**
     * 按sku创建商品
     *
     * @param request
     * @return
     */
    ResultData skuCreate(SkuInfoRequest request);

    /**
     * 按upc创建商品
     *
     * @param request
     * @return
     */
    ResultData upcCreate(SkuInfoRequest request);

    /**
     * 更新商品
     *
     * @param request
     * @return
     */
    ResultData updateSku(SkuInfoRequest request);

    /**
     * 删除商品
     *
     * @param request
     * @return
     */
    ResultData deleteSku(SkuInfoDeleteRequest request);

    /**
     * 图片上传
     *
     * @param request
     * @return
     */
    ResultData pictureUpload(PictureUploadRequest request);

    /**
     * 商品图片上传结果查询
     *
     * @param request
     * @return
     */
    PictureUploadResult getPictureUploadStatus(PictureUploadStatusRequest request);

    /**
     * 商品上架/下架
     *
     * @param request
     * @return
     */
    ResultData updateSkuSellStatus(SkuSellStatusInfoRequest request);

    /**
     * 商品上架/下架（用于清洗工具大批量处理）
     *
     * @param request
     * @return
     */
    default ResultData updateSkuSellStatusForCleaner(SkuSellStatusInfoRequest request) {
        return null;
    }

    /**
     * 更新商品customSkuId
     *
     * @param request
     * @return
     */
    ResultData updateCustomSkuId(UpdateCustomSkuIdRequest request);

    /**
     * 批量拉取商品
     * @param request
     * @return
     */
    BatchGetSkuInfoResponse batchGetSkuInfo(BatchGetSkuInfoRequest request);

    /**
     * 获取单个商品信息
     * @param request
     * @return
     */
    GetSkuInfoResponse getSkuInfo(GetSkuInfoRequest request);

    /**
     * 批量拉取前台分类
     * @param request
     * @return
     */
    GetCategoryResponse batchGetChannelStoreCategoryInfo(CatRequest request);

    /**
     * 更换商品customSkuId（美团使用根据原商品编码更换新商品编码）
     * （美团retail/updateAppFoodCodeByOrigin 根据原商品编码更换新商品编码）
     *
     * @param request
     * @return
     */
    ResultData changeCustomSkuId(ChangeCustomSkuIdRequest request);

    /**
     * 根据图片URL批量上传图片
     * @param request 图片URL列表
     * @return 图片URL和图片ID的映射关系列表
     */
    default BatchUploadPictureByUrlsResponse batchUploadImageUrls(PictureUploadRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    /**
     * 获取商品违规信息
     * @param request 图片URL列表
     * @return 图片URL和图片ID的映射关系列表
     */
    default BatchGetViolateSkuResponse batchGetViolateSku(BatchGetViolateSkuRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    /**
     * 批量更换商品customSkuId（美团平台根据原商品编码更换新商品编码）,
     * 当前接口仅实现美团平台批量操作，且为数据清洗任务开发接口
     *
     * @param request
     * @return
     */
    default ResultData batchChangeCustomSkuIdForCleanJob(CustomSkuIdChangeRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }


    default BatchGetSkuSaleInfoResponse batchGetSkuSaleInfo(BatchGetSkuSaleInfoRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }
}
