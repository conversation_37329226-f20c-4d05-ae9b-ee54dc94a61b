package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.MtReturnDuringDeliveryRequest;

/**
 * @description: 配送中返货渠道回调
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2024/4/17 上午11:19
 */
public interface ChannelReturnDuringDeliveryCallbackService {

    /**
     * 配送中返货-美团返货单状态推送-渠道回调
     * @param request
     * @return
     */
    ResultStatus mtReturnDeliveryNotify(MtReturnDuringDeliveryRequest request);
}
