package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.OrderDeliveryInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.PartRefundDetailInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusResult;

/**
 * @description: 渠道订单配送业务接口
 * @author: zhaolei12
 * @create: 2019/1/28 上午10:19
 */
public interface ChannelDeliveryService {

    OrderDeliveryInfo getOrderDeliveryInfo(long tenantId, String orderId, Long appId, long storeId);

    // 饿了么接口改造，不再使用
    @Deprecated
    PartRefundDetailInfo getRefundRecordForElm(long tenantId, String orderId, Long appId);

    /**
     * 返回渠道原始的配送状态
     *
     * @param tenantId 租户
     * @param orderId  订单
     * @param storeId  门店
     * @return 配送状态
     */
    GetLogisticsStatusResult getMtOrderLogisticsStatus(long tenantId, String orderId, long storeId);

}
