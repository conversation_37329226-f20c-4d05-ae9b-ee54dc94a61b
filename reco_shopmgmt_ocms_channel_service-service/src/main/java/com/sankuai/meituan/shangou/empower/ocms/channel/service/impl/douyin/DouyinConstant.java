package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

public class DouyinConstant {
    /**
     * 抖音上架状态
     */
    public static final int DOUYIN_ONSALE_STATUS = 0;

    /**
     * 重复创建门店商品的错误信息。
     */
    public static String SUB_PRODUCT_DUPLICATED_ERROR = "重复创建渠道商品";

    /**
     * 商品重复操作的错误信息。
     */
    public static String DUPLICATE_OPERATION = "操作重复";

    /**
     * 商品已经删除。
     */
    public static String PRODUCT_DELETED = "删除";

    /**
     * 主档商品删除后，子商品删除（下架操作）返回的错误信息
     */
    public static String SUB_PRODUCT_NOT_FOUND= "获取子商品信息失败";
    /**
     * 查询商品信息，如果商品不存在，返回的错误信息
     */
    public static String STORE_PRODUCT_NOT_EXIST= "商品不存在";

    public static String TENANT_PRODUCT_DELETED = "商品操作重复，当前状态：删除";

    /**
     * 全国库存
     */
    public static final int SKU_TYPE_0 = 0;

    /**
     * 区域库存
     */
    public static final int SKU_TYPE_1 = 1;


    public static final String WEIGHT = "weight";

    public static final String WEIGHT_UNIT = "g";

    public static final String SPEC_HEAD_NAME = "规格";


    public final static long DOU_YIN_DEFAULT_BRAND_ID = 596120136L;


    public final static String NO_BRAND = "无品牌";

}
