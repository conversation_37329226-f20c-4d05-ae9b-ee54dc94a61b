package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.meituan.shangou.saas.tenant.highlevelclient.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelSpuUpdateStoreCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.SpuResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JddjFixStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JddjMultiSpuCreateStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JddjSpuCreateUnifyStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpecTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSkuResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.TenantChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.UpdateCustomSkuIdDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.UpdateCustomSkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.UpdateCustomSpuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.CustomChannelSkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.CustomSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.SaleAttrRelationDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.SaleAttrValueDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.UpdateCustomSpuIdDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.UpdateProductStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSkuListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleUpdateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuUpdateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.UpdateCustomSkuIdResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.UpdateCustomSpuIdResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelMerchantSpuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjSpuConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConcurrentUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductChannelErrorMappingUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultCodeUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.JDDJ_UPDATE_SPU_SKU_NOT_EXIST_CODE;
import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.JDDJ_UPDATE_SPU_SKU_ROLLBACK_EXIST_MSG;
import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.SUCCESS_CODE;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/17
 **/
@Slf4j
@Service("jddjChannelMerchantSpuService")
public class JddjChannelMerchantSpuServiceImpl implements ChannelMerchantSpuService {

    private static ExecutorServiceTraceWrapper pushExecutor = new ExecutorServiceTraceWrapper(new ThreadPoolExecutor(5, 10, 5, TimeUnit.MINUTES, new ArrayBlockingQueue<>(5),
            new ThreadFactoryBuilder().setNameFormat("jddj-merchant-spu-push" + "-%d").build(), new ThreadPoolExecutor.CallerRunsPolicy()));

    @Resource
    private BaseConverterService baseConverterService;
    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Autowired
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private JddjConverterService jddjConverterService;

    @Resource(name = "jddjProductThreadPool")
    private ExecutorService jddjProductThreadPool;

    @Autowired
    private  JddjChannelAppIdUtils jddjChannelAppIdUtils;

    @Autowired
    private TenantService tenantService;

    @Override
    public MerchantSpuCreateResponse createSpu(MerchantSpuCreateRequest request) {
        MerchantSpuCreateResponse response = new MerchantSpuCreateResponse();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
        baseRequest.setAppId(appId);
        List<MerchantSpuResult> spuResultList = Lists.newArrayList();

        List<Future<MerchantSpuResult>> futureList = request.getParamList().stream()
                .map(spuDTO -> jddjProductThreadPool.submit(() -> createSpu(spuDTO, baseRequest)))
                .collect(Collectors.toList());

        futureList.forEach(future -> {
            try {
                MerchantSpuResult result = future.get(10L, TimeUnit.SECONDS);
                spuResultList.add(result);
            } catch (Exception e) {
                log.warn("创建京东总部商品未知异常", e);
                throw new BizException("并行创建京东总部商品异常:" + e.getMessage());
            }
        });

        //记录操作结果
        response.setSpuResultList(spuResultList);
        response.setStatus(ChannelStatus.buildSuccess());
        return response;
    }

    private MerchantSpuResult createSpu(MerchantSpuDTO spu, BaseRequest baseRequest) {
        if (SpecTypeEnum.SINGLE.getCode() == spu.getSpecType()) {
            // 创建单规格
            return createSingleSpu(spu, baseRequest);
        } else {
            // 创建多规格
            return createMultiSpu(spu, baseRequest);
        }
    }

    /**
     * 创建单规格商品
     *
     * @param data
     * @param baseRequest
     * @return
     */
    private MerchantSpuResult createSingleSpu(MerchantSpuDTO data, BaseRequest baseRequest) {
        MerchantSpuResult result = new MerchantSpuResult();
        result.setCustomSpuId(data.getCustomSpuId());
        try {
            ChannelResponseDTO<SkuNewResult> response = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SKU_CREATE_NEW, baseRequest, JddjSpuConverterService.convert2Single(data));
            if (!response.isSuccess()) {
                Integer unifyError = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(response.getErrno()), response.getErrorMsg());
                return result.buildResult(ResultCodeUtils.parseErrorCode(response.getErrno()), response.getErrorMsg(), unifyError);
            }
            SkuNewResult createResult = response.getCoreData();

            if (!createResult.success()) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                    .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(createResult.getResultCode()), createResult.fetchFailMsg());
                return result.buildResult(createResult.getResultCode(), createResult.fetchFailMsg(), unifyError);
            }
            result.buildResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
            result.setChannelSpuId(createResult.getSkuId());
            result.setSkuResultList(Lists.newArrayList(new CustomChannelSkuKey(createResult.getOutSkuId(), createResult.getSkuId())));
            result.setChannelResultInfo(createResult.fetchFailMsg());
        } catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.createSingleSpu 服务异常, data:{}", data, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return result;
    }



    /**
     * 创建多规格商品
     *
     * @param data
     * @param baseRequest
     * @return
     */
    private MerchantSpuResult createMultiSpu(MerchantSpuDTO data, BaseRequest baseRequest) {
        MerchantSpuResult result = new MerchantSpuResult();
        result.setCustomSpuId(data.getCustomSpuId());
        try {
            ChannelResponseDTO<SpuCreateResult> response = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_CREATE, baseRequest, JddjSpuConverterService.convert2Spu(data));
            if (!response.isSuccess()) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                    .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(response.getErrno()), response.getErrorMsg());
                return result.buildResult(Integer.parseInt(response.getErrno()), response.getErrorMsg(), unifyError);
            }
            SpuCreateResult createResult = response.getCoreData();
            result.buildResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
            result.setChannelSpuId(createResult.getSuperId());
            result.setSkuResultList(createResult.getSkuMainParterResponseList().stream().map(sku -> new CustomChannelSkuKey(sku.getOutSkuId(), sku.getSkuId())).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.createMultiSpu 服务异常, data:{}", data, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return result;
    }


    @Override
    public MerchantSpuUpdateResponse updateSpu(MerchantSpuUpdateRequest request) {
        MerchantSpuUpdateResponse response = new MerchantSpuUpdateResponse();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
        baseRequest.setAppId(appId);
        List<MerchantSpuResult> spuResultList = Lists.newArrayList();
        //按规格类型进行更新
        request.getParamList().forEach(spu -> {
            if (SpecTypeEnum.SINGLE.getCode() == spu.getSpecType()) {
                // 更新单规格
                spuResultList.add(updateSingleSpu(spu, baseRequest));
            } else {
                // 更新多规格
                spuResultList.add(updateMultiSpu(spu, baseRequest));
            }
        });
        //记录操作结果
        response.setSpuResultList(spuResultList);
        response.setStatus(ChannelStatus.buildSuccess());
        return response;
    }


    /**
     * 更新单规格商品
     *
     * @param data
     * @param baseRequest
     * @return
     */
    private MerchantSpuResult updateSingleSpu(MerchantSpuDTO data, BaseRequest baseRequest) {
        MerchantSpuResult result = new MerchantSpuResult();
        result.setCustomSpuId(data.getCustomSpuId());
        try {
            ChannelResponseDTO<SkuNewResult> response = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SKU_UPDATE_NEW, baseRequest, JddjSpuConverterService.convert2SingleUpdate(data));
            if (!response.isSuccess()) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(response.getErrno()), response.getErrorMsg());
                return result.buildResult(ResultCodeUtils.parseErrorCode(response.getErrno()), response.getErrorMsg(), unifyError);
            }
            SkuNewResult updateResult = response.getCoreData();
            if (!updateResult.success()) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                    .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(updateResult.getResultCode()), updateResult.fetchFailMsg());
                return result.buildResult(updateResult.getResultCode(), updateResult.fetchFailMsg(), unifyError);
            }
            result.buildResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
        } catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.updateSingleSpu 服务异常, data:{}", data, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return result;
    }

    /**
     * 更新多规格商品
     *
     * @param data
     * @param baseRequest
     * @return
     */
    private MerchantSpuResult updateMultiSpu(MerchantSpuDTO data, BaseRequest baseRequest) {
        MerchantSpuResult result = new MerchantSpuResult();
        result.setCustomSpuId(data.getCustomSpuId());
        try {
            // 1.添加增量销售属性值
            if (CollectionUtils.isNotEmpty(data.getSaleAttrRelationsList())) {
                // 查询已存在的销售属性
                Map<String, String> param = Maps.newHashMap();
                param.put("outSuperId", data.getCustomSpuId());
                ChannelResponseDTO<List<SaleAttrValueRelationDTO>> attrsResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_GET_SALE_ATTR, baseRequest, param);
                if (!attrsResponse.isSuccess()) {
                    return result.buildResult(Integer.parseInt(attrsResponse.getErrno()), attrsResponse.getErrorMsg());
                }
                if (CollectionUtils.isEmpty(attrsResponse.getCoreData())) {
                    return result.buildResult(ResultCode.FAIL.getCode(), "多规格商品销售属性不存在");
                }
                List<ChannelSpuSaleAttrAddDTO> attrAddList = JddjSpuConverterService.convert2SaleAttrAdd(data, attrsResponse.getCoreData());
                List<Callable<ChannelResponseDTO<DataResult>>> callableList = Lists.newArrayList();
                attrAddList.forEach(dto -> callableList.add(() -> jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_APPEND_SALE_ATTR, baseRequest, dto)));
                List<ChannelResponseDTO<DataResult>> responseList = ConcurrentUtils.concurrentExecute(pushExecutor, callableList, "追加多规格商品销售属性失败");
                for (ChannelResponseDTO<DataResult> response : responseList) {
                    if (!response.isSuccess()) {
                        return result.buildResult(Integer.parseInt(response.getErrno()), response.getErrorMsg());
                    }
                }
            }
            // 2.更新spu的基本信息
            ChannelResponseDTO<DataResult> spuUpdateResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_UPDATE_BASE, baseRequest, JddjSpuConverterService.convert2SpuBase(data));
            if (!spuUpdateResponse.isSuccess()) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(spuUpdateResponse.getErrno()), spuUpdateResponse.getErrorMsg());
                return result.buildResult(Integer.parseInt(spuUpdateResponse.getErrno()), spuUpdateResponse.getErrorMsg(), unifyError);
            }
            // 3.恢复已删除的规格
            List<ChannelSpuSkuRollBackDTO> rollBackList = JddjSpuConverterService.convert2SkuRollBack(data);
            if (CollectionUtils.isNotEmpty(rollBackList)) {
                List<Callable<ChannelResponseDTO<DataResult>>> callableList = Lists.newArrayList();
                rollBackList.forEach(dto -> callableList.add(() -> jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_ROLLBACK_SKU, baseRequest, dto)));
                List<ChannelResponseDTO<DataResult>> responseList = ConcurrentUtils.concurrentExecute(pushExecutor, callableList, "恢复多规格商品规格失败");
                for (ChannelResponseDTO<DataResult> response : responseList) {
                    // 恢复已删除的规格幂等处理
                    if (response.getDataResponse() != null && JDDJ_UPDATE_SPU_SKU_ROLLBACK_EXIST_MSG.equals(response.getDataResponse().getMsg())) {
                        continue;
                    }
                    if (!response.isSuccess()) {
                        return result.buildResult(Integer.parseInt(response.getErrno()), response.getErrorMsg());
                    }
                }
            }
            // 4.追加新增规格
            List<ChannelSpuSkuCreateDTO> createSkuList = JddjSpuConverterService.convert2SpuSkuAppend(data);
            if (CollectionUtils.isNotEmpty(createSkuList)) {
                List<Callable<ChannelResponseDTO<SpuCreateResult>>> createSkuCallableList = Lists.newArrayList();
                createSkuList.forEach(dto -> createSkuCallableList.add(() -> doAppendSku(baseRequest, dto)));
                //执行新增规格,解析返回结果
                List<CustomChannelSkuKey> skuResultList = Lists.newArrayList();
                List<ChannelResponseDTO<SpuCreateResult>> responseList = ConcurrentUtils.concurrentExecute(pushExecutor, createSkuCallableList, "多规格商品新增规格失败");
                for (ChannelResponseDTO<SpuCreateResult> response : responseList) {
                    if (!response.isSuccess()) {
                        //填充统一渠道错误码
                        Integer unifyError = ProductChannelErrorMappingUtils
                                .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(response.getErrno()), response.getErrorMsg());
                        return result.buildResult(Integer.parseInt(response.getErrno()), response.getErrorMsg(), unifyError);
                    }
                    SpuCreateResult createResult = response.getCoreData();
                    if (createResult != null && CollectionUtils.isNotEmpty(createResult.getSkuMainParterResponseList())) {
                        skuResultList.addAll(createResult.getSkuMainParterResponseList().stream().map(sku -> new CustomChannelSkuKey(sku.getOutSkuId(), sku.getSkuId())).collect(Collectors.toList()));
                    }
                }
                result.setSkuResultList(skuResultList);
            }
            List<Callable<ChannelResponseDTO<DataResult>>> updateSkuCallableList = Lists.newArrayList();
            // 5.更新已存在、恢复及删除的规格
            List<ChannelSpuSkuUpdateDTO> updateSkuList = JddjSpuConverterService.convert2SpuSkuUpdate(data);
            if (CollectionUtils.isNotEmpty(updateSkuList)) {
                updateSkuList.forEach(dto -> updateSkuCallableList.add(() -> {
                    ChannelResponseDTO<DataResult> response = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_SKU_UPDATE_BASE, baseRequest, dto);
                    // 删除操作规格不存在幂等处理
                    if (dto.getFixedStatus() == JddjFixStatusEnum.DELETE.getCode() && JDDJ_UPDATE_SPU_SKU_NOT_EXIST_CODE.equals(response.getErrno())) {
                        response.getDataResponse().setCode(SUCCESS_CODE);
                    }
                    return response;
                }));
            }
            List<ChannelResponseDTO<DataResult>> updateResponseList = ConcurrentUtils.concurrentExecute(pushExecutor, updateSkuCallableList, "多规格商品规格更新失败");
            for (ChannelResponseDTO<DataResult> response : updateResponseList) {
                if (!response.isSuccess()) {
                    //填充统一渠道错误码
                    Integer unifyError = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(response.getErrno()), response.getErrorMsg());
                    return result.buildResult(Integer.parseInt(response.getErrno()), response.getErrorMsg(), unifyError);
                }
            }
            result.buildResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
        } catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.updateMultiSpu 服务异常, data:{}", data, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return result;
    }

    private ChannelResponseDTO<SpuCreateResult> doAppendSku(BaseRequest baseRequest, ChannelSpuSkuCreateDTO dto) {
        ChannelResponseDTO<SpuCreateResult> skuAppendResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_APPEND_SKU, baseRequest, dto);
        //京东渠道返回规格已存在时，视为新增成功，返回已存在的外键
        Optional<String> existSkuId = isChannelSkuExists(skuAppendResult);
        if (existSkuId.isPresent()) {
            OutSkuKey existOutkey = new OutSkuKey();
            existOutkey.setSkuId(existSkuId.get());
            existOutkey.setOutSkuId(dto.getOutSkuId());
            SpuCreateResult spuResult = new SpuCreateResult();
            spuResult.setOutSpuId(dto.getOutSuperId());
            spuResult.setSkuMainParterResponseList(Collections.singletonList(existOutkey));
            ChannelResponseResult<SpuCreateResult> respResult = new ChannelResponseResult<>();
            respResult.setCode(SUCCESS_CODE);
            respResult.setResultData(spuResult);
            ChannelResponseDTO<SpuCreateResult> skuExistSuccessResp = new ChannelResponseDTO<>();
            skuExistSuccessResp.setSuccess(true);
            skuExistSuccessResp.setCode(SUCCESS_CODE);
            skuExistSuccessResp.setDataResponse(respResult);
            return skuExistSuccessResp;
        }
        return skuAppendResult;
    }

    private Optional<String> isChannelSkuExists(ChannelResponseDTO<SpuCreateResult> skuAppendResult) {
        if (skuAppendResult.isSuccess()) {
            return Optional.empty();
        }
        ChannelResponseResult<SpuCreateResult> dataResp = skuAppendResult.getDataResponse();
        return Optional.ofNullable(skuAppendResult.getDataResponse())
                .map(resp -> resp.getMsg())
                .filter(StringUtils::isNotBlank)
                .map(msg -> ProjectConstant.JDDJ_SKU_EXIST_MSG_PATTERN.matcher(msg))
                .filter(matcher -> matcher.find())
                .map(matcher -> matcher.group(1));
    }


    @Override
    public MerchantSpuSingleUpdateResponse singleUpdateSpuStoreCategory(UpdateProductStoreCategoryRequest request){
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        baseRequest.setAppId(request.getBaseInfo().getAppId());
        MerchantSpuSingleUpdateResponse response = new MerchantSpuSingleUpdateResponse();
        MerchantSpuResult result = new MerchantSpuResult();
        try {
            if (SpecTypeEnum.SINGLE.getCode() == request.getSpecType()) {
                ChannelNewSkuUpdateDTO single = new ChannelNewSkuUpdateDTO();
                single.setOutSkuId(request.getCustomSpuId());
                single.setShopCategories(request.getChannelStoreCategoryList());
                single.setFixedStatus(null);
                single.setIsSale(null);
                single.setIfViewDesc(null);
                single.setSellCities(null);
                ChannelResponseDTO<SkuNewResult> singleResp = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SKU_UPDATE_NEW,
                        baseRequest, single);
                if (!singleResp.isSuccess()) {
                    result.buildResult(ResultCodeUtils.parseErrorCode(singleResp.getErrno()), singleResp.getErrorMsg(), null);
                }
                else if (singleResp.getCoreData() != null && !singleResp.getCoreData().success()) {
                    result.buildResult(singleResp.getCoreData().getResultCode(), singleResp.getCoreData().fetchFailMsg(), null);
                }
                else {
                    result.buildResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
                }
            }
            else {
                ChannelSpuBaseUpdateDTO multi = new ChannelSpuBaseUpdateDTO();
                multi.setOutSuperId(request.getCustomSpuId());
                multi.setShopCategories(request.getChannelStoreCategoryList());
                multi.setFixedStatus(null);
                multi.setIsSale(null);
                multi.setIfViewDesc(null);
                multi.setSellCities(null);
                ChannelResponseDTO<String> multiResp = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_UPDATE_BASE,
                        baseRequest, multi);

                if (!multiResp.isSuccess()) {
                    result.buildResult(ResultCodeUtils.parseErrorCode(multiResp.getErrno()), multiResp.getErrorMsg(), null);
                }
                else {
                    result.buildResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.singleUpdateSpuStoreCategory 服务异常, data:{}", request, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        response.setSpuResult(result);
        response.setStatus(ChannelStatus.buildSuccess());
        return response;
    }

    @Override
    public MerchantSpuSingleDeleteResponse singleDeleteSpu(MerchantSpuSingleDeleteRequest request) {
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        MerchantSpuSingleDeleteResponse response = new MerchantSpuSingleDeleteResponse();
        MerchantSpuResult result = new MerchantSpuResult();
        CustomSpuDeleteDTO deleteDTO = new CustomSpuDeleteDTO();
        deleteDTO.setCustomSpuId(request.getCustomSpuId());
        deleteDTO.setChannelSpuId(request.getChannelSpuId());
        deleteDTO.setSpecType(request.getSpecType());
        try {
            Preconditions.checkArgument(Objects.nonNull(deleteDTO.getSpecType()), "specType is null");
            Preconditions.checkArgument(StringUtils.isNotBlank(deleteDTO.getCustomSpuId()), "customSpuId is blank");
            if (SpecTypeEnum.SINGLE.getCode() == deleteDTO.getSpecType()) {
                // 单规格删除
                result = deleteSingleSpu(deleteDTO, baseRequest);
            }
            else {
                // 多规格删除
                result = deleteMultiSpu(deleteDTO, baseRequest);
            }
        } catch (IllegalArgumentException e) {
            log.warn("JddjChannelMerchantSpuServiceImpl.singleDeleteSpu, 参数校验失败, request:{}", request, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.singleDeleteSpu 服务异常, data:{}", request, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        response.setSpuResult(result);
        response.setStatus(ChannelStatus.buildSuccess());
        return response;
    }


    @Override
    public MerchantSpuDeleteResponse deleteSpu(MerchantSpuDeleteRequest request) {
        MerchantSpuDeleteResponse response = new MerchantSpuDeleteResponse();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
        baseRequest.setAppId(appId);
        List<MerchantSpuResult> spuResultList = Lists.newArrayList();
        //按规格类型进行删除
        request.getParamList().forEach(spu -> {
            if (SpecTypeEnum.SINGLE.getCode() == spu.getSpecType()) {
                // 创建单规格
                spuResultList.add(deleteSingleSpu(spu, baseRequest));
            } else {
                // 创建多规格
                spuResultList.add(deleteMultiSpu(spu, baseRequest));
            }
        });
        //记录操作结果
        response.setSpuResultList(spuResultList);
        response.setStatus(ChannelStatus.buildSuccess());
        return response;
    }

    @Override
    public MerchantSpuDetailResponse getSpuDetail(MerchantSpuDetailRequest request){
        MerchantSpuDetailResponse result = new MerchantSpuDetailResponse();
        try {
            MerchantSpuDTO merchantSpuDTO = getChannelMerchantSpu(request);
            merchantSpuDTO.setSkuList(getChannelMerchantSku(request));

            result.setStatus(ChannelStatus.buildSuccess());
            result.setMerchantSpuDTO(merchantSpuDTO);
        } catch (Exception e) {
            String message = String.format("JddjChannelMerchantSpuServiceImpl.getSpuDetail 服务异常, request:%s,message:%s",request,e.getMessage());
            log.error(message, e);
            result.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), e.getMessage()));
        }
        return result;
    }

    @Override
    public MerchantSpuDetailResponse getSpuDetailSingle(MerchantSpuDetailRequest request) {
        MerchantSpuDetailResponse result = new MerchantSpuDetailResponse();
        result.setStatus(ChannelStatus.buildSuccess());
        try {
            if (request == null || request.getBaseInfo() == null){
                result.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), "拉取京东到家商品信息失败"));
                return result;
            }
            BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
            int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
            baseRequest.setAppId(appId);
            // 请求线上渠道
            ChannelResponseDTO<JDSkuInfoListResult> jdSkuInfoListResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.BATCH_GET_SKUINFO_NEW, baseRequest, JddjSpuConverterService.convert2SingleDetail(request));
            if (jdSkuInfoListResult == null) {
                result.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), ProjectConstant.NO_RESPONSE));
                return result;
            }
            if (!jdSkuInfoListResult.isSuccess() || !jdSkuInfoListResult.getDataResponse().isSuccess() || jdSkuInfoListResult.getCoreData() == null) {
                result.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), jdSkuInfoListResult.getMsg()));
                return result;
            }

            if (jdSkuInfoListResult.getCoreData().getResult() == null) {
                return result;
            }
            List<JDChannelSkuInfo> jdChannelSkuInfoList = JacksonUtils.parseList(jdSkuInfoListResult.getCoreData().getResult(), JDChannelSkuInfo.class);
            // 返回空列表
            if (CollectionUtils.isEmpty(jdChannelSkuInfoList)) {
                return result;
            }
            MerchantSpuDTO spuInfoDTOList = JDChannelSkuInfo.toMerchantSpuDTO(jdChannelSkuInfoList.get(0));
            result.setMerchantSpuDTO(spuInfoDTOList);
        } catch (BizException e) {
            String message = String.format("JddjChannelMerchantSpuServiceImpl.getSpuDetailSingle 业务服务异常, request:%s,message:%s",request,e.getMessage());
            log.error(message, e);
            result.setStatus(ChannelStatus.build(e.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            String message = String.format("JddjChannelMerchantSpuServiceImpl.getSpuDetailSingle 服务异常, request:%s,message:%s",request,e.getMessage());
            log.error(message, e);
            result.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), e.getMessage()));
        }
        return result;
    }

    /**
     * 获取渠道商品spu
     * @param request
     * @return
     */
    private MerchantSpuDTO getChannelMerchantSpu(MerchantSpuDetailRequest request){

        MerchantSpuDTO merchantSpuDTO = new MerchantSpuDTO();
        Map<String,String> param = new HashMap();
        param.put("outSuperId",request.getCustomSpuId());
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
        baseRequest.setAppId(appId);
        ChannelResponseDTO<GetSpuInfoResponseResult> postResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_GET_INFO, baseRequest, param);
        if (!postResult.isSuccess()) {
            throw new RuntimeException(postResult.getErrorMsg());
        }
        GetSpuInfoResponseResult getSpuInfoResponseResult = postResult.getCoreData();
        merchantSpuDTO.setCustomSpuId(getSpuInfoResponseResult.getOutSuperId());
        merchantSpuDTO.setChannelCategoryId(getSpuInfoResponseResult.getCategoryId().toString());
        merchantSpuDTO.setName(getSpuInfoResponseResult.getSuperName());

        Map<String,List<String>> saleAttrValueMap = getSpuInfoResponseResult.getSaleAttrValueRelation().stream().collect(
                Collectors.groupingBy(SaleAttrValueRelationDTO::getSaleAttrName,
                        Collectors.mapping(SaleAttrValueRelationDTO::getSaleAttrValueName,Collectors.toList())));
        merchantSpuDTO.setSaleAttrRelationsList(saleAttrValueMap.entrySet().stream().map(entry->{
            SaleAttrRelationDTO saleAttrRelationDTO = new SaleAttrRelationDTO();
            saleAttrRelationDTO.setSaleAttrName(entry.getKey());
            saleAttrRelationDTO.setSaleAttrValueNameList(entry.getValue());
            return saleAttrRelationDTO;
        }).collect(Collectors.toList()));
        return merchantSpuDTO;
    }

    @Override
    public MerchantSkuListResponse getMerchantSkuList(MerchantSpuDetailRequest request) {
        MerchantSkuListResponse result = new MerchantSkuListResponse();
        try {
            result.setStatus(ChannelStatus.buildSuccess());
            result.setSkuList(getChannelMerchantSku(request));
        } catch (Exception e) {
            String message = String.format("JddjChannelMerchantSpuServiceImpl.getMerchantSkuList 服务异常, request:%s,message:%s",request,e.getMessage());
            log.error(message, e);
            result.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), e.getMessage()));
        }
        return result;
    }

    /**
     * 获取渠道商品的sku列表
     *
     * @param request
     * @return
     */
    private List<MerchantSkuDTO> getChannelMerchantSku(MerchantSpuDetailRequest request) {
        List<MerchantSkuDTO> merchantSkus = new ArrayList();

        GetSkuParam getSkuParam = new GetSkuParam();
        GetSkuParam getSkuParam1 = new GetSkuParam();
        if (request.isOptByChannelSpuId()) {
            getSkuParam1.setSpuId(request.getChannelSpuId());
        } else {
            getSkuParam1.setOutSuperId(request.getCustomSpuId());
        }
        getSkuParam.getRequestList().add(getSkuParam1);
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
        baseRequest.setAppId(appId);
        ChannelResponseDTO<QuerySkuListResponseResult> skusResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_GET_SKU_LIST, baseRequest, getSkuParam);
        if (!skusResponse.isSuccess()) {
            throw new RuntimeException(skusResponse.getErrorMsg());
        }

        String data = skusResponse.getData();
        QuerySkuListResponseResult querySkuListResponseResult = JSON.parseObject(data, QuerySkuListResponseResult.class);
        List<QuerySkuListResponseResult.SpuSkuResponse> spuSkuResponses;
        if (request.isOptByChannelSpuId()) {
            spuSkuResponses = querySkuListResponseResult.getResult().get(request.getChannelSpuId());
        } else {
            spuSkuResponses = querySkuListResponseResult.getResult().get(request.getCustomSpuId());
        }
        if (CollectionUtils.isNotEmpty(spuSkuResponses)) {
            merchantSkus = spuSkuResponses.stream().map(sku -> {
                MerchantSkuDTO merchantSkuDTO = new MerchantSkuDTO();
                merchantSkuDTO.setCustomSkuId(sku.getOutSkuId());
                merchantSkuDTO.setFixedStatus(sku.getFixedStatus());
                merchantSkuDTO.setSpecValueName(sku.getSkuName());
                merchantSkuDTO.setAttrValueList(sku.getSaleAttrValueList().stream().map(s -> {
                    SaleAttrValueDTO saleAttrValueDTO = new SaleAttrValueDTO();
                    saleAttrValueDTO.setSaleAttrName(s.getSaleAttrName());
                    saleAttrValueDTO.setSaleAttrValue(s.getSaleAttrValueName());
                    return saleAttrValueDTO;
                }).collect(Collectors.toList()));
                BigDecimal weight = sku.getWeight();
                if (weight != null){
                    // 千克转克（舍去小数位）
                    BigDecimal transferWeight = weight.multiply(new BigDecimal(1000));
                    merchantSkuDTO.setWeight(transferWeight.intValue());
                }
                if (sku.getSkuPrice() != null){
                    merchantSkuDTO.setSuggestSalePrice(sku.getSkuPrice());
                }
                merchantSkuDTO.setUpc(sku.getUpcCode());
                if (sku.getSkuId() != null) {
                    merchantSkuDTO.setChannelSkuId(sku.getSkuId().toString());
                }
                return merchantSkuDTO;
            }).collect(Collectors.toList());
        }
        return merchantSkus;
    }

    @Data
    private class GetSkuParam{
        private List<GetSkuParam> requestList = new ArrayList();

        private String outSuperId;
        private String spuId;
    }

    /**
     * 创建单规格商品
     *
     * @param data
     * @param baseRequest
     * @return
     */
    private MerchantSpuResult deleteSingleSpu(CustomSpuDeleteDTO data, BaseRequest baseRequest) {
        MerchantSpuResult result = new MerchantSpuResult();
        result.setCustomSpuId(data.getCustomSpuId());
        try {
            // ChannelResponseDTO<SkuNewResult> deleteResponse = jddjChannelGateService.sendPost(ChannelPostJDDJEnum.SKU_UPDATE_NEW, baseRequest, JddjSpuConverterService.convert2SingleDelete(data));
            ChannelResponseDTO<SkuNewResult> deleteResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SKU_UPDATE_NEW, baseRequest, JddjSpuConverterService.convert2SingleDelete(data));
            if (!deleteResponse.isSuccess()) {
                return result.buildResult(ResultCodeUtils.parseErrorCode(deleteResponse.getErrno()), deleteResponse.getErrorMsg());
            }
            SkuNewResult deleteResult = deleteResponse.getCoreData();
            boolean ignoreRpcError = false;
            if (deleteResult != null) {
                String resultCode = Objects.nonNull(deleteResult.getResultCode()) ? String.valueOf(deleteResult.getResultCode()) : "-1";
                ignoreRpcError = MccConfigUtil.ignoreRpcError(ChannelTypeEnum.JD2HOME.getCode(), ChannelPostJDDJEnum.SPU_UPDATE_BASE.getUrl(), resultCode, deleteResult.getFailedDetail());
            }
            if (!deleteResult.success() && !ignoreRpcError) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(deleteResult.getResultCode()), deleteResult.getResultMsg());
                return result.buildResult(deleteResult.getResultCode(), deleteResult.fetchFailMsg(), unifyError);
            }
            result.buildResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
        } catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.deleteSingleSpu 服务异常, data:{}", data, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return result;
    }

    /**
     * 创建单规格商品
     *
     * @param data
     * @param baseRequest
     * @return
     */
    private MerchantSpuResult deleteMultiSpu(CustomSpuDeleteDTO data, BaseRequest baseRequest) {
        if (tenantService.isErpSpuTenant(baseRequest.getTenantId())) {
            return deleteMultiSpuForErpTenant(data, baseRequest);
        }
        MerchantSpuResult result = new MerchantSpuResult();
        result.setCustomSpuId(data.getCustomSpuId());
        try {
            //先下架，后能删除
            ChannelResponseDTO<DataResult> offSaleResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_UPDATE_BASE, baseRequest, JddjSpuConverterService.convert2MultiOffSale(data));
            boolean ignoreRpcError = false;
            if (offSaleResponse.getDataResponse() != null) {
                ignoreRpcError = MccConfigUtil.ignoreRpcError(ChannelTypeEnum.JD2HOME.getCode(), ChannelPostJDDJEnum.SPU_UPDATE_BASE.getUrl(), offSaleResponse.getDataResponse().getCode(), offSaleResponse.getDataResponse().getMsg());
            }
            if (!offSaleResponse.isSuccess() && (offSaleResponse.getDataResponse() == null
                    || !ignoreRpcError)) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(offSaleResponse.getErrno()), offSaleResponse.getErrorMsg());
                return result.buildResult(Integer.parseInt(offSaleResponse.getErrno()), offSaleResponse.getErrorMsg(), unifyError);
            }
            // 京东的修改接口在1s内不能调用超过一次，此处临时增加一个sleep, 后续应该将先下架再删除的逻辑移到业务层，通过业务限流来控制。
            TimeUnit.SECONDS.sleep(1);

            ChannelResponseDTO<DataResult> deleteResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_UPDATE_BASE, baseRequest, JddjSpuConverterService.convert2MultiDelete(data));
            if (deleteResponse.getDataResponse() != null) {
                ignoreRpcError = MccConfigUtil.ignoreRpcError(ChannelTypeEnum.JD2HOME.getCode(), ChannelPostJDDJEnum.SPU_UPDATE_BASE.getUrl(), deleteResponse.getDataResponse().getCode(), deleteResponse.getDataResponse().getMsg());
            }
            if (!deleteResponse.isSuccess() && (deleteResponse.getDataResponse() == null
                    || !ignoreRpcError)) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(offSaleResponse.getErrno()), offSaleResponse.getErrorMsg());
                return result.buildResult(Integer.parseInt(deleteResponse.getErrno()), deleteResponse.getErrorMsg(), unifyError);
            }
            result.buildResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
        } catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.deleteMultiSpu 服务异常, data:{}", data, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return result;
    }

    private MerchantSpuResult deleteMultiSpuForErpTenant(CustomSpuDeleteDTO data, BaseRequest baseRequest) {
        MerchantSpuResult result = new MerchantSpuResult();
        result.setCustomSpuId(data.getCustomSpuId());
        try {
            //先下架，后能删除
            ChannelResponseDTO<DataResult> offSaleResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_UPDATE_BASE, baseRequest, JddjSpuConverterService.convert2MultiOffSale(data));
            boolean ignoreRpcError = false;
            if (offSaleResponse.getDataResponse() != null) {
                ignoreRpcError = MccConfigUtil.ignoreRpcError(ChannelTypeEnum.JD2HOME.getCode(), ChannelPostJDDJEnum.SPU_UPDATE_BASE.getUrl(), offSaleResponse.getDataResponse().getCode(), offSaleResponse.getDataResponse().getMsg());
            }
            if (!offSaleResponse.isSuccess() && (offSaleResponse.getDataResponse() == null
                    || !ignoreRpcError)) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(offSaleResponse.getErrno()), offSaleResponse.getErrorMsg());
                return result.buildResult(Integer.parseInt(offSaleResponse.getErrno()), offSaleResponse.getErrorMsg(), unifyError);
            }

            // 京东的修改接口在1s内不能调用超过一次，此处临时增加一个sleep, 后续应该将先下架再删除的逻辑移到业务层，通过业务限流来控制。
            TimeUnit.SECONDS.sleep(1);

            // 解绑京东渠道SPU的商家编码
            String superId = data.getChannelSpuId();
            String deprecatedOutSpuerId = UUID.randomUUID().toString().replace("-", "");
            ChannelSpuUpdateOutSuperIdDTO updateOutSuperIdDTO = new ChannelSpuUpdateOutSuperIdDTO();
            updateOutSuperIdDTO.addUpdateRequest(Long.parseLong(superId), deprecatedOutSpuerId);
            ChannelResponseDTO<DataResult> updateResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_UPDATE_SUPER_ID, baseRequest, updateOutSuperIdDTO);
            if (updateResponse.getDataResponse() != null) {
                ignoreRpcError = MccConfigUtil.ignoreRpcError(ChannelTypeEnum.JD2HOME.getCode(), ChannelPostJDDJEnum.SPU_UPDATE_SUPER_ID.getUrl(), updateResponse.getDataResponse().getCode(), updateResponse.getDataResponse().getMsg());
            }
            if (!updateResponse.isSuccess() && (updateResponse.getDataResponse() == null
                    || !ignoreRpcError)) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(updateResponse.getErrno()), updateResponse.getErrorMsg());
                return result.buildResult(Integer.parseInt(updateResponse.getErrno()), updateResponse.getErrorMsg(), unifyError);
            }

            // 京东的修改接口在1s内不能调用超过一次，此处临时增加一个sleep, 后续应该将先下架再删除的逻辑移到业务层，通过业务限流来控制。
            TimeUnit.SECONDS.sleep(1);

            ChannelSpuBaseUpdateDTO deleteRequest = JddjSpuConverterService.convert2MultiDelete(data);
            deleteRequest.setOutSuperId(deprecatedOutSpuerId);
            ChannelResponseDTO<DataResult> deleteResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_UPDATE_BASE, baseRequest, deleteRequest);
            if (deleteResponse.getDataResponse() != null) {
                ignoreRpcError = MccConfigUtil.ignoreRpcError(ChannelTypeEnum.JD2HOME.getCode(), ChannelPostJDDJEnum.SPU_UPDATE_BASE.getUrl(), deleteResponse.getDataResponse().getCode(), deleteResponse.getDataResponse().getMsg());
            }
            if (!deleteResponse.isSuccess() && (deleteResponse.getDataResponse() == null
                    || !ignoreRpcError)) {
                //填充统一渠道错误码
                Integer unifyError = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(offSaleResponse.getErrno()), offSaleResponse.getErrorMsg());
                return result.buildResult(Integer.parseInt(deleteResponse.getErrno()), deleteResponse.getErrorMsg(), unifyError);
            }
            result.buildResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
        } catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.deleteMultiSpuForErpTenant 服务异常, data:{}", data, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return result;
    }


    @Override
    public MerchantSpuCreateResponse getSpuCreateStatus(MerchantSpuDetailRequest request) {
        MerchantSpuCreateResponse response = new MerchantSpuCreateResponse();
        MerchantSpuResult result = new MerchantSpuResult();
        //按规格类型查询创建状态
        if ( Objects.nonNull(request.getSpecType()) && SpecTypeEnum.SINGLE.getCode() == request.getSpecType()) {
            // 单规格创建状态
            result = getSingleSpuStatus(request);
        }
        if ( Objects.nonNull(request.getSpecType()) && SpecTypeEnum.MULTI.getCode() == request.getSpecType()) {
            // 多规格创建状态
            result = getMultiSpuStatus(request);
        }
        // 接口报错直接返回
        if (result.getChannelResultInfo() != null) {
            //记录操作结果
            response.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), result.getChannelResultInfo()));
            return response;
        }
        //记录操作结果
        response.setSpuResultList(Lists.newArrayList(result));
        response.setStatus(ChannelStatus.buildSuccess());
        return response;
    }

    @Override
    public UpdateCustomSpuIdResponse updateCustomSpuId(UpdateCustomSpuIdRequest request) {
        UpdateCustomSpuIdResponse response = new UpdateCustomSpuIdResponse();
        response.setStatus(ChannelStatus.buildSuccess());

        //透传多应用id
        BaseRequest baseRequest = convertBaseRequest(request.getBaseInfo());

        List<MerchantSpuResult> spuResultList = new ArrayList<>();
        // 分批处理
        Lists.partition(request.getParamList(), ProjectConstant.LIST_PARTITION_NUM).forEach(subList -> {
            try {
                ChannelSpuUpdateOutSuperIdDTO updateOutSuperIdDTO = new ChannelSpuUpdateOutSuperIdDTO();
                for (UpdateCustomSpuIdDTO updateCustomSpuIdDTO : subList) {
                    updateOutSuperIdDTO.addUpdateRequest(Long.parseLong(updateCustomSpuIdDTO.getChannelSpuId()), updateCustomSpuIdDTO.getCustomSpuId());
                }
                ChannelResponseDTO<DataResult> updateResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_UPDATE_SUPER_ID, baseRequest, updateOutSuperIdDTO);
                log.info("/djapi/pms/batchUpdateOutSuperId updateOutSuperIdDTO: {}, updateResponse: {}", updateOutSuperIdDTO, updateResponse);
                if (!updateResponse.isSuccess()) {
                    //填充统一渠道错误码
                    Integer unifyError = ProductChannelErrorMappingUtils
                            .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(updateResponse.getErrno()), updateResponse.getErrorMsg());
                    addSpuResult(spuResultList, subList, Integer.parseInt(updateResponse.getErrno()), updateResponse.getErrorMsg(), unifyError);
                }else{
                    addSpuResult(spuResultList, subList, ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null);
                }
            } catch (Exception e) {
                log.error("JddjChannelMerchantSpuServiceImpl.updateCustomSpuId 执行异常, request:{}, subList: {}", request, subList, e);
                addSpuResult(spuResultList, subList, ResultCode.FAIL.getCode(), e.getMessage(), null);
            }
        });
        response.setSpuResultList(spuResultList);
        return response;
    }

    private BaseRequest convertBaseRequest(TenantChannelRequest request) {
        BaseRequestSimple requestSimple = request.convert2Base();
        Optional.ofNullable(request)
                .map(TenantChannelRequest::getAppId)
                .ifPresent(requestSimple::setAppId);
        BaseRequest baseRequest = baseConverterService.baseRequest(requestSimple);
        return baseRequest;
    }

    @Override
    public UpdateCustomSkuIdResponse updateCustomSkuId(UpdateCustomSkuIdRequest request) {
        UpdateCustomSkuIdResponse response = new UpdateCustomSkuIdResponse();
        response.setStatus(ChannelStatus.buildSuccess());

        BaseRequest baseRequest = convertBaseRequest(request.getBaseInfo());

        List<MerchantSkuResult> spuResultList = new ArrayList<>();
        // 分批处理
        Lists.partition(request.getParamList(), ProjectConstant.LIST_PARTITION_NUM).forEach(subList -> {
            try {
                ChannelSkuUpdateOutSkuIdDTO updateOutSuperIdDTO = new ChannelSkuUpdateOutSkuIdDTO();
                for (UpdateCustomSkuIdDTO updateCustomSpuIdDTO : subList) {
                    updateOutSuperIdDTO.addUpdateRequest(Long.parseLong(updateCustomSpuIdDTO.getChannelSkuId()), updateCustomSpuIdDTO.getCustomSkuId());
                }
                ChannelResponseDTO<DataResult> updateResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.UPDATE_CUSTOM_SKU_ID, baseRequest, updateOutSuperIdDTO);
                log.info("/djapi/pms/sku/batchUpdateOutSkuId updateOutSuperIdDTO: {}, updateResponse: {}", updateOutSuperIdDTO, updateResponse);
                if (!updateResponse.isSuccess()) {
                    //填充统一渠道错误码
                    Integer unifyError = ProductChannelErrorMappingUtils
                            .getChannelUnifiedError(baseRequest.getChannelId(), String.valueOf(updateResponse.getErrno()), updateResponse.getErrorMsg());
                    addSkuResult(spuResultList, subList, Integer.parseInt(updateResponse.getErrno()), updateResponse.getErrorMsg(), unifyError);
                }else{
                    addSkuResult(spuResultList, subList, ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null);
                }
            } catch (Exception e) {
                log.error("JddjChannelMerchantSpuServiceImpl.updateCustomSkuId 执行异常, request:{}, subList: {}", request, subList, e);
                addSkuResult(spuResultList, subList, ResultCode.FAIL.getCode(), e.getMessage(), null);
            }
        });
        response.setSpuResultList(spuResultList);
        return response;
    }

    private static void addSkuResult(List<MerchantSkuResult> spuResultList, List<UpdateCustomSkuIdDTO> subList, Integer code, String msg, Integer unifyCode) {
        for (UpdateCustomSkuIdDTO updateCustomSpuIdDTO : subList) {
            MerchantSkuResult merchantSkuResult = new MerchantSkuResult();
            merchantSkuResult.buildResult(code, msg, unifyCode);
            merchantSkuResult.setChannelSkuId(updateCustomSpuIdDTO.getChannelSkuId());
            merchantSkuResult.setCustomSkuId(updateCustomSpuIdDTO.getCustomSkuId());
            spuResultList.add(merchantSkuResult);
        }
    }

    private static void addSpuResult(List<MerchantSpuResult> spuResultList, List<UpdateCustomSpuIdDTO> subList, Integer code, String msg, Integer unifyCode) {
        for (UpdateCustomSpuIdDTO updateCustomSpuIdDTO : subList) {
            MerchantSpuResult merchantSpuResult = new MerchantSpuResult();
            merchantSpuResult.buildResult(code, msg, unifyCode);
            merchantSpuResult.setChannelSpuId(updateCustomSpuIdDTO.getChannelSpuId());
            merchantSpuResult.setCustomSpuId(updateCustomSpuIdDTO.getCustomSpuId());
            spuResultList.add(merchantSpuResult);
        }
    }

    /**
     * 获取创建状态-单规格
     * 接口文档：https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=0a97930f0a7147d5a9feeb7bffb8fd8d
     *
     * @param request
     * @return
     */
    public MerchantSpuResult getSingleSpuStatus(MerchantSpuDetailRequest request) {
        MerchantSpuResult result = new MerchantSpuResult();
        result.setCustomSpuId(request.getCustomSpuId());
        try {
            ChannelSkuStatusDTO channelSkuStatusDTO = new ChannelSkuStatusDTO();
            channelSkuStatusDTO.setOutSkuId(request.getCustomSpuId());
            BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
            int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
            baseRequest.setAppId(appId);
            ChannelResponseDTO<SkuNewResult> channelResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SKU_GET_STATUS, baseRequest, channelSkuStatusDTO);
            if (!channelResponse.isSuccess()) {
                result.setChannelResultInfo(channelResponse.getErrorMsg());
                return result;
            }
            SkuNewResult createResult = channelResponse.getCoreData();
            result.setChannelSpuId(createResult.getSkuId());
            result.setSkuResultList(Lists.newArrayList(new CustomChannelSkuKey(createResult.getOutSkuId(), createResult.getSkuId())));
            result.setResultCode(createResult.getResultCode());
            result.setResultMsg(createResult.getResultMsg());
        } catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.getSingleSpuStatus 服务异常, data:{}", request, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return result;
    }


    /**
     * 获取创建状态-多规格
     * 接口文档：https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=448ba8ee82eb40958318bb84f2efc9c2
     *
     * @param request
     * @return
     */
    public MerchantSpuResult getMultiSpuStatus(MerchantSpuDetailRequest request) {
        MerchantSpuResult result = new MerchantSpuResult();
        result.setCustomSpuId(request.getCustomSpuId());
        try {
            ChannelSpuStatusDTO channelSpuStatusDTO = new ChannelSpuStatusDTO();
            channelSpuStatusDTO.setOutSuperId(request.getCustomSpuId());
            BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
            int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
            baseRequest.setAppId(appId);
            ChannelResponseDTO<SpuMainStatusResponse> channelResponse = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.SPU_GET_STATUS, baseRequest, channelSpuStatusDTO);
            if (!channelResponse.isSuccess()) {
                result.setChannelResultInfo(channelResponse.getErrorMsg());
                return result;
            }
            SpuMainStatusResponse createResult = channelResponse.getCoreData();
            result.setCustomSpuId(request.getCustomSpuId());
            if (createResult == null) {
                result.setResultCode(unifySpuStatusResultCode(createResult));
                return result;
            }
            result.setChannelSpuId(createResult.getSuperId());
            if (CollectionUtils.isNotEmpty(createResult.getSkuMainStatusResponseList())) {
                List<CustomChannelSkuKey> skuResultList = createResult.getSkuMainStatusResponseList().stream()
                        .map(skuMainStatusResponse -> new CustomChannelSkuKey(skuMainStatusResponse.getSkuId(), skuMainStatusResponse.getOutSkuId()))
                        .collect(Collectors.toList());
                result.setSkuResultList(skuResultList);
            }
            result.setResultCode(unifySpuStatusResultCode(createResult));
            result.setResultMsg(channelResponse.getMsg());
        } catch (Exception e) {
            log.error("JddjChannelMerchantSpuServiceImpl.getMultiSpuStatus 服务异常, data:{}", request, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return result;
    }

    /**
     * 统一获取创建状态返回码
     * 背景：多规格和单规格返回码不统一
     * 多规格创建状态：1、创建成功（商家可以使用） 2、创建中（不可用，需要再进行查询） 3、创建失败
     * 单规格创建状态：1009000、创建中  100006、没有查到商品 100001、创建成功
     *
     * @param spuMainStatusResponse
     */
    private int unifySpuStatusResultCode(SpuMainStatusResponse spuMainStatusResponse) {
        // 没有查询到商品
        if (spuMainStatusResponse == null) {
            return JddjSpuCreateUnifyStatusEnum.SPU_NOT_EXIST.getCode();
        }
        Integer createStatus = spuMainStatusResponse.getStatus();
        // 创建成功
        if (Objects.equals(JddjMultiSpuCreateStatusEnum.CREATE_SUCCESS.getCode(), createStatus)) {
            return JddjSpuCreateUnifyStatusEnum.CREATE_SUCCESS.getCode();
        }
        // 创建中
        if (Objects.equals(JddjMultiSpuCreateStatusEnum.CREATING.getCode(), createStatus)) {
            return JddjSpuCreateUnifyStatusEnum.CREATING.getCode();
        }
        return spuMainStatusResponse.getStatus();
    }
}
