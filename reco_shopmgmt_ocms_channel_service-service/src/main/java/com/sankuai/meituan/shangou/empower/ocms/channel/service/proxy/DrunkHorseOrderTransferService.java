package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.OrderDataStatusUtil;
import com.sankuai.meituan.waimai.service.vp.constants.enums.WmVpOrderStatusEnum;
import com.sankuai.meituan.waimai.service.vp.iface.WmVpActivityThriftService;
import com.sankuai.meituan.waimai.service.vp.vo.WmVpType;
import com.sankuai.meituan.waimai.service.vp.vo.result.*;
import com.sankuai.meituan.waimai.thrift.activity.constants.WmActivityType;
import com.sankuai.meituan.waimai.thrift.activity.domain.ChargeSideInfo;
import com.sankuai.meituan.waimai.thrift.activity.exception.OrderRefundException;
import com.sankuai.meituan.waimai.thrift.activity.iface.WmActivityOrderThriftIface;
import com.sankuai.meituan.waimai.thrift.activity.util.DischargeUtil;
import com.sankuai.meituan.waimai.thrift.activity.vo.*;
import com.sankuai.meituan.waimai.thrift.command.WmProductPlatformQuerySpusCommand;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmProductSkuCView;
import com.sankuai.meituan.waimai.thrift.domain.WmProductSpuCView;
import com.sankuai.meituan.waimai.thrift.result.WmProductPlatformQuerySpusResult;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import com.sankuai.meituan.waimai.thrift.service.WmProductPlatformQueryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 闪购订单查询服务
 * （借鉴开放平台代码，暂不做处理）
 * <AUTHOR>
 * @date 2021/9/13 7:18 下午
 */
@Service
@Slf4j
public class DrunkHorseOrderTransferService {

    @Resource
    private WmProductPlatformQueryThriftService.Iface wmProductPlatformQueryThriftService;
    @Resource
    private WmVpActivityThriftService.Iface wmVpActivityThriftService;
    @Autowired
    private WmActivityOrderThriftIface.Iface wmActivityOrderNewThriftIface;
    @Autowired
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;

    /**
     * 拼装buy订单详细信息
     *
     * @param wmVpOrderWithDetailsVo
     * @return
     */
    public String getBuyOrderDetailLogistics(WmVpOrderWithDetailsVo wmVpOrderWithDetailsVo) {
        if ( wmVpOrderWithDetailsVo == null || wmVpOrderWithDetailsVo.getOrder() == null) {
            log.info("SgOpenOrderQueryService getBuyOrderDetailLogistics, orderId = null");
            return null;
        }
        WmVpOrderVo wmVpOrderVo = wmVpOrderWithDetailsVo.getOrder();
        Long orderId = wmVpOrderVo.getId();
        // 获取订单信息
        SgAppOrder sgAppOrder = getSgAppBuyOrderByWmVpOrderResult(wmVpOrderWithDetailsVo, WmVpType.DRUNK_HORSE);

        SgOrderAndLogisticsVo orderAndLogisticsVo = new SgOrderAndLogisticsVo();
        orderAndLogisticsVo.setOrder_id(orderId);
        orderAndLogisticsVo.setStatus(wmVpOrderVo.getStatus());

        orderAndLogisticsVo.setIs_pre_sale_order(sgAppOrder.getIs_pre_sale_order());
        orderAndLogisticsVo.setApp_order_code(sgAppOrder.getApp_order_code());
        orderAndLogisticsVo.setApp_poi_code(sgAppOrder.getApp_poi_code());
        orderAndLogisticsVo.setWm_poi_id(sgAppOrder.getWm_poi_id());
        orderAndLogisticsVo.setWm_poi_name(sgAppOrder.getWm_poi_name());
        orderAndLogisticsVo.setWm_poi_address(sgAppOrder.getWm_poi_address());
        orderAndLogisticsVo.setWm_poi_phone(sgAppOrder.getWm_poi_phone());
        orderAndLogisticsVo.setRecipient_address(sgAppOrder.getRecipient_address());
        orderAndLogisticsVo.setRecipient_address_detail(sgAppOrder.getRecipient_address_detail());
        orderAndLogisticsVo.setRecipient_phone(sgAppOrder.getRecipient_phone());
        orderAndLogisticsVo.setRecipient_name(sgAppOrder.getRecipient_name());
        orderAndLogisticsVo.setTotal(sgAppOrder.getTotal());
        orderAndLogisticsVo.setCaution(sgAppOrder.getCaution());
        orderAndLogisticsVo.setRemark(sgAppOrder.getRemark());
        orderAndLogisticsVo.setShipper_phone(sgAppOrder.getShipper_phone() == null ? "" : sgAppOrder.getShipper_phone());
        // 订单状态转换
        orderAndLogisticsVo.setStatus(convertBuyOrderStatus(sgAppOrder.getStatus()));
        orderAndLogisticsVo.setIs_third_shipping(sgAppOrder.getIs_third_shipping() == null ? 0 : sgAppOrder.getIs_third_shipping());
        orderAndLogisticsVo.setCtime(sgAppOrder.getCtime());
        orderAndLogisticsVo.setUtime(sgAppOrder.getUtime());
        orderAndLogisticsVo.setDetail(JSONObject.toJSONString(sgAppOrder.getDetail()));
        orderAndLogisticsVo.setDay_seq(sgAppOrder.getDay_seq());
        orderAndLogisticsVo.setExtras(JSONObject.toJSONString(sgAppOrder.getExtras_buy()));
        orderAndLogisticsVo.setLatitude(sgAppOrder.getLatitude());
        orderAndLogisticsVo.setLongitude(sgAppOrder.getLongitude());
        orderAndLogisticsVo.setWm_order_id_view(sgAppOrder.getWm_order_id_view());
        orderAndLogisticsVo.setOriginal_price(sgAppOrder.getOriginal_price());
        orderAndLogisticsVo.setCity_id(sgAppOrder.getCity_id());
        orderAndLogisticsVo.setDelivery_time(sgAppOrder.getDelivery_time());
        orderAndLogisticsVo.setEstimate_arrival_time(sgAppOrder.getEstimate_arrival_time());
        orderAndLogisticsVo.setIs_poi_first_order(sgAppOrder.is_poi_first_order());
        orderAndLogisticsVo.setIs_brand_first_order(sgAppOrder.getIs_brand_first_order());
        orderAndLogisticsVo.setUser_id(sgAppOrder.getUser_id());
        orderAndLogisticsVo.setOpenuid(sgAppOrder.getUser_id());
        orderAndLogisticsVo.setTotal_weight(sgAppOrder.getTotal_weight());
        // 用户实付金额
        orderAndLogisticsVo.setTotal(wmVpOrderVo.getTotal());
        orderAndLogisticsVo.setOrder_phone_number(sgAppOrder.getOrder_phone_number());
        orderAndLogisticsVo.setOrder_id(wmVpOrderVo.getWm_vp_order_view_id());
        orderAndLogisticsVo.setSku_benefit_detail(sgAppOrder.getSku_benefit_detail());
        orderAndLogisticsVo.setOrder_completed_time(sgAppOrder.getOrder_completed_time());
        orderAndLogisticsVo.setOrder_cancel_time(sgAppOrder.getOrder_cancel_time());
        orderAndLogisticsVo.setOrder_confirm_time(sgAppOrder.getOrder_confirm_time());
        orderAndLogisticsVo.setLogistics_completed_time(sgAppOrder.getLogistics_completed_time());
        orderAndLogisticsVo.setLogistics_fetch_time(sgAppOrder.getLogistics_fetch_time());
        orderAndLogisticsVo.setOrder_pay_time(sgAppOrder.getOrder_pay_time());
        orderAndLogisticsVo.setTrace_no(sgAppOrder.getTrade_no());
        orderAndLogisticsVo.setRecipient_gender(sgAppOrder.getRecipient_gender());
        orderAndLogisticsVo.setDiscount_shipping_fee(wmVpOrderVo.getExpress_fee());
        orderAndLogisticsVo.setResult("ok");
        orderAndLogisticsVo.setPri_phone(wmVpOrderVo.getPri_phone());
        orderAndLogisticsVo.setPrivacy_phone(wmVpOrderVo.getPrivacy_phone());
        orderAndLogisticsVo.setBackup_privacy_phones(wmVpOrderVo.getBackup_privacy_phones());
        orderAndLogisticsVo.setIncmp_code(OrderDataStatusUtil.getDegradeStatus());
        orderAndLogisticsVo.setIncmp_modules(OrderDataStatusUtil.getDegradeModules());
        orderAndLogisticsVo.setIs_vip(sgAppOrder.getIs_vip());
        OrderDataStatusUtil.remove();
        return JSON.toJSONString(orderAndLogisticsVo);
    }

    /**
     * 获取订单信息
     *
     * @param wmVpOrderWithDetailsVo
     * @param bizCode
     * @return
     */
    private SgAppOrder getSgAppBuyOrderByWmVpOrderResult(WmVpOrderWithDetailsVo wmVpOrderWithDetailsVo, WmVpType bizCode) {
        if (wmVpOrderWithDetailsVo == null) {
            return null;
        }

        // 基础信息设置
        SgAppOrder wao = changeWmOrderToAppBuyOrder(wmVpOrderWithDetailsVo);
        if (wao == null) {
            return null;
        }
        // 订单主档数据
        WmVpOrderVo wmVpOrderVo = wmVpOrderWithDetailsVo.getOrder();
        try {
            //设置门店信息
            long wmPoiId = wao.getWm_poi_id();
            WmPoiAggre wmPoiAggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(wmPoiId, ImmutableSet.of(
                    WmPoiFieldQueryConstant.WM_POI_FIELD_SOURCE_ID,
                    WmPoiFieldQueryConstant.WM_POI_FIELD_ADDRESS,
                    WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
                    WmPoiFieldQueryConstant.WM_POI_FIELD_CALL_CENTER,
                    WmPoiFieldQueryConstant.WM_POI_FIELD_CITY_LOCATION_ID
            ));

            if (wmPoiAggre == null) {
                return null;
            }
            // wmOrderId 非viewId
            long wm_order_id = wmVpOrderVo.getId();
            wao.setShipping_type(0);
            // 目前默认自配
            wao.setIs_third_shipping(1);
            wao.setWm_poi_address(wmPoiAggre.getAddress());
            wao.setWm_poi_name(wmPoiAggre.getName());
            wao.setWm_poi_phone(wmPoiAggre.getCall_center());
            wao.setSource_id(wmPoiAggre.getSource_id());
            wao.setLocation_city_id(wmPoiAggre.getCity_location_id());
            // 处理订单相关节点时间
            buildOrderTime(wao, wmVpOrderWithDetailsVo);
            // 订单详情-商品信息 detail
            List<SgAppOrderFood> sgBuyOrderVps = getSgBuyOrderDetails(wmVpOrderWithDetailsVo, wm_order_id, wmPoiId);
            wao.setDetail(sgBuyOrderVps);
            // 订单优惠信息 extras
            getSgBuyOrderExtra(wao, wmVpOrderWithDetailsVo);
            // 商品优惠详情 sku_benefit_detail
            getBuyOrderSkuBenefitDetail(wao, wmVpOrderWithDetailsVo, bizCode);
            // todo 这地方应该是没必要的
            //            WmOrderAuditLogUtil.wmOrderAuditLog(Lists.newArrayList(), poiApp.getApp_id());
        } catch (Exception e) {
            log.error("openapi获取订单详细出现错误，wm_order_id={},错误信息={}", wao.getOrder_id(), e.getMessage(), e);
            OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.OrderInfo);
        }

        return wao;
    }


    /**
     * （借鉴开放平台代码，暂不做处理）
     * 获取商品优惠信息
     *
     * @param sgAppOrder
     * @param wmVpOrderWithDetailsVo 订单详情
     * @param bizCode
     */
    public void getBuyOrderSkuBenefitDetail(SgAppOrder sgAppOrder, WmVpOrderWithDetailsVo wmVpOrderWithDetailsVo, WmVpType bizCode) {
        Long orderId = sgAppOrder.getOrder_id();
        Long wm_order_id_view = sgAppOrder.getWm_order_id_view();
        List<OrderSkuDetail> wmOrderSkuDetailResultList = null;
        long wmPoiId = sgAppOrder.getWm_poi_id();
        List<SgAppOrderSkuBenefitDetail> detailList = new ArrayList<>();
        try {
            // 组装请求参数
            RefundParam refundParam = new RefundParam();
            refundParam.setOrderId(orderId);
            refundParam.setBizType(bizCode.getValue());
            // 商品活动分摊信息
            boolean isHaveActInfo = setApportionInfoBySku(refundParam, orderId, bizCode);
            if (!isHaveActInfo) {
                log.info("该buy订单没有商品活动分摊信息,orderId:{},bizCode:{}", orderId, bizCode);
                return;
            }
            // 设置入参的商品信息
            setRefundParamItems(refundParam, wmVpOrderWithDetailsVo);
            // 入参的优惠信息
            setRefundParamExtras(refundParam, wmVpOrderWithDetailsVo, orderId, wmPoiId);
            //查询优惠信息
            log.info("getSkuInfoWithOrderActivity refundParam:{}", refundParam);
            // orderId、items、extras、apportionInfoBySku 必填
            wmOrderSkuDetailResultList = wmActivityOrderNewThriftIface.getSkuInfoWithOrderActivity(refundParam);
            log.info("getSkuInfoWithOrderActivity, orderId={}, wm_order_id_view={},  wmOrderSkuDetailResultList={}",
                    orderId, wm_order_id_view, wmOrderSkuDetailResultList);
        } catch (OrderRefundException orderRefundException) {
            log.warn("getBuyOrderSkuBenefitDetail orderRefundException error, orderId={}, wm_order_id_view={}",
                    orderId, wm_order_id_view, orderRefundException);
            OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.SkuBenefitDetail);
        } catch (TException e) {
            log.warn("getBuyOrderSkuBenefitDetail TException error, orderId={}, wm_order_id_view={}}",
                    orderId, wm_order_id_view, e);
            OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.SkuBenefitDetail);
        } catch (Exception e1) {
            log.warn("getBuyOrderSkuBenefitDetail Exception error, orderId={}, wm_order_id_view={}",
                    orderId, wm_order_id_view, e1);
            OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.SkuBenefitDetail);
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(wmOrderSkuDetailResultList)) {
            log.info("wmOrderSkuDetailResultList is empty,orderId:{}", orderId);
            return;
        }
        // 设置skuIdList
        List<Long> skuIdList = new ArrayList<>();
        for (OrderSkuDetail vpOrderSkuDetail : wmOrderSkuDetailResultList) {
            if (vpOrderSkuDetail == null) {
                continue;
            }
            skuIdList.add(vpOrderSkuDetail.getSkuid());
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(skuIdList)) {
            log.error("查询wmOrderSkuDetailResultList为空，订单id: {}", orderId);
            return;
        }
        // 反查商品服务
        WmProductPlatformQuerySpusCommand command = new WmProductPlatformQuerySpusCommand();
        command.setWmPoiId(wmPoiId);
        command.setSkuIds(skuIdList);
        command.setNeedDeleted(true);
        log.info("getBuyOrderSkuBenefitDetail 查询优惠字段，wmPoiId:{},wmOrderId:{},command:{}", wmPoiId, orderId, JSONUtil.toJSONString(command));
        WmProductPlatformQuerySpusResult wmOrderSpus = null;
        try {
            wmOrderSpus = wmProductPlatformQueryThriftService.getWmProductSpusByCommand(command);
        } catch (Exception e) {
            log.warn("getBuyOrderSkuBenefitDetail 查询优惠字段异常，wmPoiId:{},wmOrderId:{},command:{}", wmPoiId, orderId, JSONUtil.toJSONString(command), e);
            OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.SkuBenefitDetail);
        }

        log.info("getBuyOrderSkuBenefitDetail 查询优惠字段，商品详情，wmPoiId:{},wmOrderId:{},wmOrderSpus:{}", wmPoiId, orderId, JSONUtil.toJSONString(wmOrderSpus));
        if (wmOrderSpus == null) {
            log.error("getBuyOrderSkuBenefitDetail 查询优惠字段,商品信息为空，wmPoiId:{},wmOrderId:{}", wmPoiId, orderId);
            return;
        }

        Map<Long, WmProductSpuCView> skuIdSpuMap = new HashMap<>();
        Map<Long, WmProductSkuCView> skuIdSkuMap = new HashMap<>();

        if (wmOrderSpus != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(wmOrderSpus.getSpus())) {
            for (WmProductSpuCView spu : wmOrderSpus.getSpus()) {
                if (spu != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(spu.getSkuList())) {
                    for (WmProductSkuCView sku : spu.getSkuList()) {
                        skuIdSpuMap.put(sku.getId(), spu);
                        skuIdSkuMap.put(sku.getId(), sku);
                    }
                }
            }
        }

        for (OrderSkuDetail wmOrderSkuDetailResult : wmOrderSkuDetailResultList) {
            if (wmOrderSkuDetailResult == null) {
                continue;
            }
            WmProductSpuCView spu = skuIdSpuMap.get(wmOrderSkuDetailResult.getSkuid());
            WmProductSkuCView sku = skuIdSkuMap.get(wmOrderSkuDetailResult.getSkuid());
            SgAppOrderSkuBenefitDetail wmAppOrderSkuBenefitDetail = new SgAppOrderSkuBenefitDetail();
            wmAppOrderSkuBenefitDetail.setApp_food_code("");
            wmAppOrderSkuBenefitDetail.setSku_id("");
            // 商品中心设置app_spu_code & sku_id
            if (spu == null) {
                OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.ProductSpuInfo);
            } else if (StringUtils.isNotBlank(spu.getSource_food_code())) {
                wmAppOrderSkuBenefitDetail.setApp_food_code(spu.getSource_food_code());
            }
            if (sku == null) {
                OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.ProductSkuInfo);
            } else if (StringUtils.isNotBlank(sku.getSource_food_code())) {
                wmAppOrderSkuBenefitDetail.setSku_id(sku.getSource_food_code());
            }
            if (sku != null) {
                wmAppOrderSkuBenefitDetail.setUpc(sku.getUpc_code());
            }
            if (spu != null && StringUtils.isNotBlank(spu.getName())) {
                wmAppOrderSkuBenefitDetail.setName(spu.getName());
            }
            wmAppOrderSkuBenefitDetail.setCount(wmOrderSkuDetailResult.getTotalNumber());
            wmAppOrderSkuBenefitDetail.setOriginPrice(wmOrderSkuDetailResult.getOriginPrice());
            wmAppOrderSkuBenefitDetail.setTotalOriginPrice(wmOrderSkuDetailResult.getTotalOriginPrice());
            wmAppOrderSkuBenefitDetail.setActivityPrice(wmOrderSkuDetailResult.getActivityPrice());
            wmAppOrderSkuBenefitDetail.setTotalActivityPrice(wmOrderSkuDetailResult.getTotalActivityPrice());
            wmAppOrderSkuBenefitDetail.setTotalReducePrice(wmOrderSkuDetailResult.getTotalReducePrice());
            wmAppOrderSkuBenefitDetail.setBoxNumber(wmOrderSkuDetailResult.getBoxNumber());
            wmAppOrderSkuBenefitDetail.setBoxPrice(wmOrderSkuDetailResult.getBoxPrice());
            wmAppOrderSkuBenefitDetail.setTotalBoxPrice(wmOrderSkuDetailResult.getTotalBoxPrice());
            wmAppOrderSkuBenefitDetail.setTotalMtCharge(wmOrderSkuDetailResult.getTotalMtCharge());
            wmAppOrderSkuBenefitDetail.setTotalPoiCharge(wmOrderSkuDetailResult.getTotalPoiCharge());
            // sku参与活动详情
            List<SgAppOrderActDetail> sgAppOrderActDetailList = convertExtraDetail(wmOrderSkuDetailResult);
            wmAppOrderSkuBenefitDetail.setWmAppOrderActDetails(sgAppOrderActDetailList);
            detailList.add(wmAppOrderSkuBenefitDetail);
        }
        sgAppOrder.setSku_benefit_detail(JSON.toJSONString(detailList));
    }


    /**
     * 数据处理
     *
     * @param wmVpOrderWithDetailsVo
     * @return
     */
    public SgAppOrder changeWmOrderToAppBuyOrder(WmVpOrderWithDetailsVo wmVpOrderWithDetailsVo) {
        // 订单主档信息
        WmVpOrderVo wmVpOrderVo = wmVpOrderWithDetailsVo.getOrder();
        if (wmVpOrderVo == null) {
            log.info("wmVpOrderVo is null");
            return null;
        }
        SgAppOrder sgAppOrder = new SgAppOrder();
        sgAppOrder.setOrder_id(wmVpOrderVo.getId());
        sgAppOrder.setWm_order_id_view(wmVpOrderVo.getWm_vp_order_view_id());
        sgAppOrder.setCtime((long) wmVpOrderVo.getOrder_time());
        sgAppOrder.setUtime((long) wmVpOrderVo.getUtime());
        // 订单预计送达时间
        sgAppOrder.setDelivery_time((long) wmVpOrderVo.getEstimate_arrival_time());
        sgAppOrder.setApp_order_code("");
        // 备注
        sgAppOrder.setCaution(wmVpOrderVo.getRemark());
        // 收货人姓名
        sgAppOrder.setRecipient_name(wmVpOrderVo.getRecipient_name());
        // 收货人电话
        sgAppOrder.setRecipient_phone(wmVpOrderVo.getRecipient_phone());
        // 配送员联系电话
        sgAppOrder.setShipper_phone(wmVpOrderVo.getCourier_phone() == null ? "" : wmVpOrderVo.getCourier_phone());
        sgAppOrder.setRemark(wmVpOrderVo.getRemark());
        // 见枚举@link com.sankuai.meituan.waimai.service.vp.constants.enums.WmVpOrderStatusEnum
        sgAppOrder.setStatus(wmVpOrderVo.getStatus());
        sgAppOrder.setTotal(wmVpOrderVo.getTotal());
        sgAppOrder.setOriginal_price(wmVpOrderVo.getOriginal_price());
        sgAppOrder.setWm_poi_id(wmVpOrderVo.getPoi_id());
        sgAppOrder.setCity_id(Long.valueOf(wmVpOrderVo.getCity_id()));
        // 组装ext字段
        buildOrderExtField(wmVpOrderVo, sgAppOrder);
        // 设置支付信息
        buildBuyOrderTradeNo(wmVpOrderWithDetailsVo.getPayRecords(), wmVpOrderVo.getWm_vp_order_view_id(), sgAppOrder);
        // 用户id
        sgAppOrder.setUser_id(wmVpOrderVo.getUser_id());
        return sgAppOrder;
    }

    /**
     * 添加ext字段
     * recipient_address、latitude、longitude、day_seq、is_poi_first_order、是否品牌新客
     *
     * @param wmVpOrderVo
     * @param sgAppOrder
     */
    private void buildOrderExtField(WmVpOrderVo wmVpOrderVo, SgAppOrder sgAppOrder) {
        String vpOrderExt = wmVpOrderVo.getOrder_ext();
        log.info("buildOrderExtField vpOrderExt:{}", vpOrderExt);
        if (StringUtils.isBlank(vpOrderExt)) {
            return;
        }
        try {
            SgBuyOrderExt sgBuyOrderExt = JSONObject.parseObject(vpOrderExt, SgBuyOrderExt.class);
            log.info("buildOrderExtField sgBuyOrderExt:{}", sgBuyOrderExt);
            if (sgBuyOrderExt == null) {
                return;
            }
            // 推单序列号
            sgAppOrder.setDay_seq(sgBuyOrderExt.getPerformExt() == null ? null : sgBuyOrderExt.getPerformExt().getDaySeq());
            if (sgBuyOrderExt.getGInfo() != null) {
                // 订单用户是否第一次在此门店下单：true-是，false-否。
                sgAppOrder.setIs_poi_first_order(sgBuyOrderExt.getGInfo().getSfo());
                // 是否品牌新客
                sgAppOrder.setIs_brand_first_order(sgBuyOrderExt.getGInfo().isFirst_order() ? 1 : 0);
                // 是否预订单
                sgAppOrder.setIs_pre_sale_order(sgBuyOrderExt.getGInfo().getIsPreOrder() == 0 ? false : true);
                sgAppOrder.setIs_vip(sgBuyOrderExt.getGInfo().getVip());
            }
            if (sgBuyOrderExt.getAddressInfo() != null) {
                SgBuyOrderExt.AddressInfo addressInfo = sgBuyOrderExt.getAddressInfo();
                // recipient_address订单人收货地址，例：色金拉 (色金拉)@#西藏自治区林芝市墨脱县色金拉
                // f5（f6）@f1+f2+f3+f5 f6为空时不加括号
                StringBuilder recipientAddressSb = new StringBuilder(addressInfo.getF5())
                        .append(StringUtils.isNotBlank(addressInfo.getF6()) ? "（" + addressInfo.getF6() + "）" : "")
                        .append("@#").append(addressInfo.getF1())
                        .append(addressInfo.getF2())
                        .append(addressInfo.getF3())
                        .append(addressInfo.getF5());
                sgAppOrder.setRecipient_address(recipientAddressSb.toString());
                Integer longitude = Integer.parseInt(addressInfo.getF7());
                Integer latitude = Integer.parseInt(addressInfo.getF8());
                // 经度
                sgAppOrder.setLongitude((double) longitude / 1000000);
                // 纬度
                sgAppOrder.setLatitude((double) latitude / 1000000);
            }
            if (sgBuyOrderExt.getBizInfo() != null) {
                // 收件人性别
                sgAppOrder.setRecipient_gender(sgBuyOrderExt.getBizInfo().getRecipientGender());
            }
        } catch (Exception e) {
            log.warn("buildOrderExtField error,vpOrderExt:{}", vpOrderExt, e);
        }
    }

    /**
     * 订单支付信息设置
     *
     * @param wmVpPayRecordResVos
     * @param orderViewId
     * @param sgAppOrder
     */
    private void buildBuyOrderTradeNo(List<WmVpPayRecordResVo> wmVpPayRecordResVos, Long orderViewId, SgAppOrder sgAppOrder) {
        // 订单支付信息
        log.info("buildBuyOrderPayFlow wmVpPayRecordResVos:{}", wmVpPayRecordResVos);
        if (CollectionUtils.isEmpty(wmVpPayRecordResVos)) {
            return;
        }
        for (WmVpPayRecordResVo recordResVo : wmVpPayRecordResVos) {
            if (recordResVo == null) {
                continue;
            }
            // status 支付状态 0-等待支付结果 10-支付成功 20-已退款
            if (recordResVo.getStatus() == 10 || recordResVo.getStatus() == 20) {
                // 支付时间
                sgAppOrder.setOrder_pay_time(Long.valueOf(recordResVo.getPay_time()));
                // 支付流水号
                sgAppOrder.setTrade_no(recordResVo.getTrade_no());
                break;
            }
        }
    }

    /**
     * 获取订单详情
     *
     * @param wmVpOrderWithDetailsVo
     * @param wmOrderId
     * @param wmPoiId
     * @return
     */
    public List<SgAppOrderFood> getSgBuyOrderDetails(WmVpOrderWithDetailsVo wmVpOrderWithDetailsVo, Long wmOrderId, Long wmPoiId) {
        // 订单商品信息列表
        List<WmVpOrderDetailVo> wmVpOrderDetailVos = wmVpOrderWithDetailsVo.getDetails();
        log.info("订单详情：wmPoiId:{},wmOrderId:{},detail:{}", wmPoiId, wmOrderId, JSONUtil.toJSONString(wmVpOrderDetailVos));
        if (CollectionUtils.isEmpty(wmVpOrderDetailVos)) {
            log.warn("查询订单详情为空：wmPoiId:{},wmOrderId:{}", wmPoiId, wmOrderId);
            return Collections.emptyList();
        }
        List<SgAppOrderFood> wmAppOrderFoods = new ArrayList<>();
        List<Long> orderSkuIdList = new ArrayList<>();
        for (WmVpOrderDetailVo orderDetail : wmVpOrderDetailVos) {
            if (orderDetail == null) {
                continue;
            }
            orderSkuIdList.add(orderDetail.getSku_id());
        }

        if (CollectionUtils.isEmpty(orderSkuIdList)) {
            log.error("查询订单商品详情为skuidList为空，wmPoiId:{},wmOrderId:{},detail:{}", wmPoiId, wmOrderId, JSONUtil.toJSONString(wmVpOrderDetailVos));
            return Collections.emptyList();
        }

        WmProductPlatformQuerySpusCommand command = new WmProductPlatformQuerySpusCommand();
        command.setWmPoiId(wmPoiId);
        command.setSkuIds(orderSkuIdList);
        command.setNeedDeleted(true);
        log.info("WmAppOrderService getOrderDetails 查询buy订单商品信息入参，wmPoiId:{},wmOrderId:{},command:{}", wmPoiId, wmOrderId, JSONUtil.toJSONString(command));
        WmProductPlatformQuerySpusResult wmOrderSpus = null;
        try {
            // 查询商品信息
            wmOrderSpus = wmProductPlatformQueryThriftService.getWmProductSpusByCommand(command);
        } catch (Exception e) {
            log.error("WmAppOrderService getOrderDetails 查询订单商品信息异常，wmPoiId:{},wmOrderId:{},command:{}", wmPoiId, wmOrderId, JSONUtil.toJSONString(command), e);
            OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.ProductSpuInfo);
        }
        log.info("WmAppOrderService getOrderDetails 查询订单商品信息结果，wmPoiId:{},wmOrderId:{},wmOrderSpus:{}", wmPoiId, wmOrderId, JSONUtil.toJSONString(wmOrderSpus));
        Map<Long, WmProductSpuCView> skuIdSpuMap = new HashMap<>();
        Map<Long, WmProductSkuCView> skuIdSkuMap = new HashMap<>();

        if (wmOrderSpus != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(wmOrderSpus.getSpus())) {
            for (WmProductSpuCView spu : wmOrderSpus.getSpus()) {
                if (spu != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(spu.getSkuList())) {
                    for (WmProductSkuCView sku : spu.getSkuList()) {
                        if (sku == null) {
                            continue;
                        }
                        skuIdSpuMap.put(sku.getId(), spu);
                        skuIdSkuMap.put(sku.getId(), sku);
                    }
                }
            }
        }
        for (WmVpOrderDetailVo orderDetail : wmVpOrderDetailVos) {
            SgAppOrderFood wmAppOrderFood = new SgAppOrderFood();
            WmProductSpuCView spu = skuIdSpuMap.get(orderDetail.getSku_id());
            WmProductSkuCView sku = skuIdSkuMap.get(orderDetail.getSku_id());
            // 商品中心查询 app_spu_code & sku_id
            if (spu == null) {
                OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.ProductSpuInfo);
            } else if (StringUtils.isNotBlank(spu.getSource_food_code())) {
                wmAppOrderFood.setApp_food_code(spu.getSource_food_code());
            }
            if (sku == null) {
                OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.ProductSkuInfo);
            } else if (StringUtils.isNotBlank(sku.getSource_food_code())) {
                wmAppOrderFood.setSku_id(sku.getSource_food_code());
            }
            // 重量字段
            if (sku != null) {
                wmAppOrderFood.setWeight(sku.getWeight());
                wmAppOrderFood.setUpc(sku.getUpc_code());
                wmAppOrderFood.setWeight_for_unit(SgOpenRetailWeightUnitEnum.transformWeightWithWeightUnit(sku.getWeight(), SgOpenRetailWeightUnitEnum.findByUnit(sku.getWeight_unit())));
                wmAppOrderFood.setWeight_unit(sku.getWeight_unit());
                wmAppOrderFood.setPicture(sku.getPicture());
            }

            // item_id商品行id
            wmAppOrderFood.setItem_id(orderDetail.getDetail_id());
            // todo 用不到
//            wmAppOrderFood.setMt_spu_id(mtSpuId);
//            wmAppOrderFood.setMt_sku_id(mtSkuId);
//            wmAppOrderFood.setMt_tag_id(mtTagId);
            wmAppOrderFood.setFood_name(orderDetail.getSpu_name());
            wmAppOrderFood.setPrice((float) orderDetail.getPrice());
            wmAppOrderFood.setActual_price((float) orderDetail.getPrice());
            wmAppOrderFood.setOriginal_price((float) orderDetail.getOriginal_price());
            wmAppOrderFood.setQuantity(orderDetail.getCount());
            wmAppOrderFood.setUnit(orderDetail.getUnit());
            wmAppOrderFood.setFood_discount(1f);
            wmAppOrderFood.setFood_property(StringUtils.join(orderDetail.getAttrValues(), ","));
            wmAppOrderFood.setSpec(orderDetail.getSpec());
            wmAppOrderFood.setWm_food_sku_id(orderDetail.getSku_id());  // 注:不能将此属性推送给三方
            wmAppOrderFood.setCart_id(orderDetail.getCart_id());
            wmAppOrderFoods.add(wmAppOrderFood);
        }
        log.info("WmAppOrderService.getOrderDetailsNew.wmAppOrderFoods:{}", JSON.toJSONString(wmAppOrderFoods));
        return wmAppOrderFoods;
    }

    /**
     * 设置订单优惠信息
     *
     * @param wao
     * @param wmVpOrderWithDetailsVo
     */
    public void getSgBuyOrderExtra(SgAppOrder wao, WmVpOrderWithDetailsVo wmVpOrderWithDetailsVo) {
        // 订单优惠信息列表
        List<WmVpOrderExtraResVo> wmOrderExtraResults = wmVpOrderWithDetailsVo.getWmOrderExtraRessults();
        // 订单优惠信息
        List<SgAppBuyOrderExtra> wmAppOrderExtras = new ArrayList<>();
        try {
            log.info("订单id={}，订单extra={}", wao.getOrder_id(), wmVpOrderWithDetailsVo);
            double orderTotalUserCharge = 0;//所有活动 用户承担的总和
            for (WmVpOrderExtraResVo wmVpOrderExtraResVo : wmOrderExtraResults) {
                SgAppBuyOrderExtra appExtra = new SgAppBuyOrderExtra();
                appExtra.setReduce_fee((float) wmVpOrderExtraResVo.getReduce_fee());
                appExtra.setRemark(wmVpOrderExtraResVo.getRemark());
                appExtra.setType(wmVpOrderExtraResVo.getType());
                //加入批量设置活动id
                appExtra.setAct_policy_id(wmVpOrderExtraResVo.getWm_act_policy_id());
                // 赠品信息

                if (!StringUtils.isBlank(wmVpOrderExtraResVo.getExt())) {
                    // 活动扩展信息
                    SgAppBuyOrderExtraActExtendMsg actExtendMsg = getOrderExtraActExtendMsg(wao.getWm_order_id_view(), wao.getWm_poi_id(), wmVpOrderExtraResVo.getExt(),
                            wmVpOrderExtraResVo.getDischarge_detail(), wmVpOrderExtraResVo.getType());
                    appExtra.setAct_extend_msg(actExtendMsg);
                }
                ChargeSideInfo chargeSideInfo = DischargeUtil.transform(wmVpOrderExtraResVo.getDischarge_detail());
                // 认为除商家承担外都是美团承担
                appExtra.setMt_charge((float) chargeSideInfo.getMt_charge());
                appExtra.setPoi_charge((float) chargeSideInfo.getPoi_charge());
                log.info("活动id解析, orderId: {}, orderExtra: {}, appExtra: {}, chargeSideInfo: {}, wmPoiId: {}", wao.getOrder_id(), wmVpOrderExtraResVo,
                        appExtra, chargeSideInfo, wao.getWm_poi_id());
                wmAppOrderExtras.add(appExtra);
            }
            if (wmAppOrderExtras.isEmpty()) {
                wmAppOrderExtras = Collections.emptyList();
            }
            wao.setExtras_buy(wmAppOrderExtras);

        } catch (Exception e) {
            log.error("open平台，getSgBuyOrderExtra出错，订单id: {}", wao.getOrder_id(), e);
            OrderDataStatusUtil.degradeOrderStatus(OrderDataModuleEnum.Extras);
        }
    }

    /**
     * 获取订单优惠信息中的活动扩展信息
     *
     * @param wm_poi_id
     * @param discharge_detail
     * @param type
     * @return
     */
    private SgAppBuyOrderExtraActExtendMsg getOrderExtraActExtendMsg(Long orderViewId, Long wm_poi_id, String ext, String discharge_detail, int type) {
        log.info("getOrderExtraActExtendMsg, order_view_id:{}, wm_poi_id={}, ext={}, type={}, discharge_detail:{}", orderViewId, wm_poi_id, ext, type, discharge_detail);

        if (org.apache.commons.lang3.StringUtils.isBlank(discharge_detail)) {
            return null;
        }
        try {
            SgAppBuyOrderExtra.SgBuyOrderExtraResult sgBuyOrderExtraResult = JSONObject.parseObject(ext, SgAppBuyOrderExtra.SgBuyOrderExtraResult.class);
            // 样例：{"s_a":{"cl":{"v":"14010|1|1;1|1|1"},"at":2,"ct":1,"sw":1},"g_i_i":49,"g_s_i":130079615,"sku_id":130079615,"g_n":1}
            JSONObject dischargeDetailJO = JSON.parseObject(discharge_detail);
            if (dischargeDetailJO == null) {
                return null;
            }
            SgAppBuyOrderExtraActExtendMsg giftsActExtendMsg = new SgAppBuyOrderExtraActExtendMsg();
            // 类型留口，暂不支持满赠
            if (type == WmActivityType.FULL_DONATION.getValue()) {
                //满赠 赠品数量
                String d_e_c = dischargeDetailJO.getString("d_e_c");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(d_e_c)) {
                    giftsActExtendMsg.setGift_num(Integer.parseInt(d_e_c));
                }
            } else if (type == WmActivityType.DRUNK_HORSE_BUY_DONATION.getValue()) {
                //买赠 赠品数量
                String g_n = dischargeDetailJO.getString("g_n");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(g_n)) {
                    giftsActExtendMsg.setGift_num(Integer.parseInt(g_n));
                }
                //买赠 主品sku
                String skuId = dischargeDetailJO.getString("sku_id");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(skuId)) {
                    setWmAppMsgActExtendMsgNew(wm_poi_id, Long.parseLong(skuId), 1, giftsActExtendMsg);
                }
            } else {
                return null;
            }
            //赠品skuId
            String g_s_i = dischargeDetailJO.getString("g_s_i");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(g_s_i)) {
                long skuId = Long.parseLong(g_s_i);
                setWmAppMsgActExtendMsgNew(wm_poi_id, skuId, 2, giftsActExtendMsg);

            }
            giftsActExtendMsg.setGifts_name(sgBuyOrderExtraResult.getGiftName());

            return giftsActExtendMsg;
        } catch (Exception e) {
            log.warn("getOrderExtraActExtendMsg 出错，ext={}", ext, e);
            return null;
        }
    }

    /**
     * 转换为app 商品优惠详情对象
     *
     * @param wm_poi_id
     * @param skuId
     * @param type      1:主品，2：赠品
     * @return
     */
    private void setWmAppMsgActExtendMsgNew(long wm_poi_id, Long skuId, int type, SgAppBuyOrderExtraActExtendMsg giftsActExtendMsg) {
        log.info("getWmAppMsgBySkuId,wm_poi_id={},skuId={}", wm_poi_id, skuId);

        WmProductPlatformQuerySpusCommand command = new WmProductPlatformQuerySpusCommand();
        command.setWmPoiId(wm_poi_id);
        command.setSkuIds(Lists.newArrayList(skuId));
        command.setNeedDeleted(true);
        log.info("AddOrderExtraActExtendMsgProcessor setWmAppMsgActExtendMsgNew 查询商品信息，wmPoiId:{},skuId:{},command:{}", wm_poi_id,skuId, JSONUtil.toJSONString(command));
        WmProductPlatformQuerySpusResult wmOrderSpus = null;
        try {
            wmOrderSpus = wmProductPlatformQueryThriftService.getWmProductSpusByCommand(command);
        } catch (Exception e) {
            log.error("AddOrderExtraActExtendMsgProcessor setWmAppMsgActExtendMsgNew 查询订单商品信息异常，wmPoiId:{},skuId:{},wmOrderSpus:{}", wm_poi_id,skuId,JSONUtil.toJSONString(wmOrderSpus),e);
        }

        Map<Long, WmProductSpuCView> skuIdSpuMap = new HashMap<>();
        Map<Long, WmProductSkuCView> skuIdSkuMap = new HashMap<>();

        if(wmOrderSpus!=null && org.apache.commons.collections.CollectionUtils.isNotEmpty(wmOrderSpus.getSpus())){
            for(WmProductSpuCView spu : wmOrderSpus.getSpus()){
                if(spu!=null && org.apache.commons.collections.CollectionUtils.isNotEmpty(spu.getSkuList())){
                    for(WmProductSkuCView sku : spu.getSkuList()){
                        skuIdSpuMap.put(sku.getId(),spu);
                        skuIdSkuMap.put(sku.getId(),sku);
                    }
                }
            }
        }

        WmProductSpuCView spu = skuIdSpuMap.get(skuId);
        WmProductSkuCView sku = skuIdSkuMap.get(skuId);
        String app_food_code = spu==null?"":spu.getSource_food_code();

        if (type == 1) {
            giftsActExtendMsg.setApp_food_code(app_food_code);
            giftsActExtendMsg.setSku_id(sku==null?"":sku.getSource_food_code());
        } else if (type == 2) {
            giftsActExtendMsg.setGifts_app_food_code(app_food_code);
            giftsActExtendMsg.setGifts_sku_id(sku==null?"":sku.getSource_food_code());
        }
    }

    /**
     * 转换订单状态
     * 订单状态，返回订单当前的状态。目前平台的订单状态参考值有：
     * 1-用户已提交订单；2-向商家推送订单；4-商家已确认；8-订单已完成；9-订单已取消。
     *
     * @param buyOrderStatus
     * @return
     */
    public int convertBuyOrderStatus(int buyOrderStatus) {
        int status = -1;
        if (WmVpOrderStatusEnum.SUBMIT.getCode() == buyOrderStatus) {
            // 1-用户已提交订单
            status = 1;
        } else if (WmVpOrderStatusEnum.PAYED.getCode() == buyOrderStatus) {
            // 2-向商家推送订单
            status = 2;
        } else if (WmVpOrderStatusEnum.CANCELED.getCode() == buyOrderStatus) {
            // 9-订单已取消
            status = 9;
        } else if (WmVpOrderStatusEnum.CONFIRM.getCode() == buyOrderStatus) {
            // 4-商家已确认
            status = 4;
        } else if (WmVpOrderStatusEnum.PERFORM.getCode() == buyOrderStatus) {
            // 完成发货
            // 4-商家已确认
            status = 4;
        } else if (WmVpOrderStatusEnum.FINISHED.getCode() == buyOrderStatus) {
            // 8-订单已完成
            status = 8;
        } else if (WmVpOrderStatusEnum.ILLEGAL_STATUS.getCode() == buyOrderStatus) {

        }
        return status;
    }



    /**
     * 设置订单相关时间节点
     *
     * @param wao
     * @param wmVpOrderWithDetailsVo
     */
    public void buildOrderTime(SgAppOrder wao, WmVpOrderWithDetailsVo wmVpOrderWithDetailsVo) {
        if (wmVpOrderWithDetailsVo == null) {
            return;
        }
        List<WmVpOrderStatusHisResult> wmVpOrderStatusHisResultList = wmVpOrderWithDetailsVo.getStatusHisResults();
        if (CollectionUtils.isEmpty(wmVpOrderStatusHisResultList)) {
            return;
        }
        // 按照ctime正序排列
        Collections.sort(wmVpOrderStatusHisResultList, Comparator.comparingInt(WmVpOrderStatusHisResult::getCtime));
        int orderConfirmTime = 0;// 订单确认时间
        int orderCancelTime = 0;// 订单取消时间
        int orderCompletedTime = 0;// 订单完成时间
        int logisticsFetchTime = 0;// 骑手取餐时间 对应骑手状态 20-骑手已取货
        int logisticsCompletedTime = 0;// 骑手完成配送时间 40-骑手已送达
        // 循环操作找到时间
        for (WmVpOrderStatusHisResult result : wmVpOrderStatusHisResultList) {
            if (result == null) {
                continue;
            }
            if (orderConfirmTime == 0 && result.getStatus() == WmVpOrderStatusEnum.CONFIRM.getCode()) {
                orderConfirmTime = result.getCtime();
            }
            if (orderCancelTime == 0 && result.getStatus() == WmVpOrderStatusEnum.CANCELED.getCode()) {
                orderCancelTime = result.getCtime();
            }
            if (orderCompletedTime == 0 && result.getStatus() == WmVpOrderStatusEnum.FINISHED.getCode()) {
                orderCompletedTime = result.getCtime();
            }
            if (logisticsFetchTime == 0 && result.getLogistics_status() == 20) {
                logisticsFetchTime = result.getCtime();
            }
            if (logisticsCompletedTime == 0 && result.getLogistics_status() == 40) {
                logisticsCompletedTime = result.getCtime();
            }
        }
        wao.setOrder_cancel_time(Long.valueOf(orderCancelTime));
        wao.setOrder_completed_time(Long.valueOf(orderCompletedTime));
        wao.setOrder_confirm_time(Long.valueOf(orderConfirmTime));
        wao.setLogistics_completed_time(logisticsCompletedTime);
        wao.setLogistics_fetch_time(logisticsFetchTime);
    }

    /**
     * 设置商品活动分摊信息入参
     *
     * @param refundParam
     * @param orderId
     */
    private boolean setApportionInfoBySku(RefundParam refundParam, Long orderId, WmVpType bizCode) throws Exception {
        log.info("queryAllSkuActivities orderId:{},bizCode:{}", orderId, bizCode);
        String allSkuActivities = wmVpActivityThriftService.queryAllSkuActivities(orderId, bizCode.getValue());
        log.info("queryAllSkuActivities orderId:{},bizCode:{},allSkuActivities:{}", orderId, bizCode, allSkuActivities);
        if (StringUtils.isBlank(allSkuActivities)) {
            log.info("allSkuActivities is empty,orderId:{}", orderId);
            return false;
        }
        refundParam.setApportionInfoBySku(JSONArray.parseArray(allSkuActivities, ItemApportionInfo.class));
        return true;
    }

    /**
     * 设置入参的商品信息
     *
     * @param refundParam
     * @param wmVpOrderWithDetailsVo
     */
    private void setRefundParamItems(RefundParam refundParam, WmVpOrderWithDetailsVo wmVpOrderWithDetailsVo) {
        List<WmVpOrderDetailVo> wmVpOrderDetailVos = wmVpOrderWithDetailsVo.getDetails();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(wmVpOrderDetailVos)) {
            return;
        }
        // 入参的商品信息
        List<CountActivityItem> items = new ArrayList<>();
        for (WmVpOrderDetailVo wmVpOrderDetailVo : wmVpOrderDetailVos) {
            if (wmVpOrderDetailVo == null) {
                continue;
            }
            CountActivityItem item = new CountActivityItem();
            item.setId(wmVpOrderDetailVo.getSku_id());
            item.setName(wmVpOrderDetailVo.getSku_name());
            item.setOriginalPrice(wmVpOrderDetailVo.getOriginal_price());
            item.setCount(wmVpOrderDetailVo.getCount());
            item.setBoxPrice(0);
            item.setBoxCount(0);
            item.setCartId(0);
            item.setActivityPrice(wmVpOrderDetailVo.getPrice());
            item.setAttrIdsI64(wmVpOrderDetailVo.getAttrIdsI64());
            items.add(item);
        }
        log.info("setRefundParamItems wmVpOrderDetailVos:{},items:{}", wmVpOrderDetailVos, items);
        refundParam.setItems(items);
    }

    /**
     * 设置入参的优惠信息
     *
     * @param refundParam
     * @param wmVpOrderWithDetailsVo
     */
    private void setRefundParamExtras(RefundParam refundParam, WmVpOrderWithDetailsVo wmVpOrderWithDetailsVo, Long orderId, long wmPoiId) {
        List<WmVpOrderExtraResVo> wmVpOrderExtraResVos = wmVpOrderWithDetailsVo.getWmOrderExtraRessults();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(wmVpOrderExtraResVos)) {
            return;
        }
        List<CountActivityExtra> extras = new ArrayList<>();
        for (WmVpOrderExtraResVo wmVpOrderExtraResVo : wmVpOrderExtraResVos) {
            if (wmVpOrderExtraResVo == null) {
                continue;
            }
            CountActivityExtra extra = new CountActivityExtra();
            extra.setId(wmVpOrderExtraResVo.getId());
            extra.setWm_order_id(orderId);
            extra.setWm_order_id(wmPoiId);
            extra.setReduce_fee(wmVpOrderExtraResVo.getReduce_fee());
            // type=344为赠品，需设置gift_name
            if (wmVpOrderExtraResVo.getType() == 344) {
                JSONObject jsonObject = JSON.parseObject(wmVpOrderExtraResVo.getExt());
                extra.setGift_name(jsonObject == null ? null : jsonObject.getString("giftName"));
            }
            extra.setCtime(wmVpOrderExtraResVo.getCtime());
            extra.setUtime(wmVpOrderExtraResVo.getUtime());
            extra.setWm_act_poi_id(0);
            extra.setRemark(wmVpOrderExtraResVo.getRemark());
            // 设置Wm_act_policy_id
            extra.setWm_act_policy_id(wmVpOrderExtraResVo.getWm_act_policy_id());
            extra.setType(wmVpOrderExtraResVo.getType());
            extra.setDischarge_detail(wmVpOrderExtraResVo.getDischarge_detail());
            extras.add(extra);
        }
        log.info("setRefundParamExtras wmVpOrderExtraResVos:{},extras:{}", wmVpOrderExtraResVos, extras);
        refundParam.setExtras(extras);
    }


    /**
     * 设置sku extra detail
     *
     * @param orderSkuDetail
     * @return
     */
    private List<SgAppOrderActDetail> convertExtraDetail(OrderSkuDetail orderSkuDetail){
        //处理活动详情
        List<OrderActivityDetail> orderActivityDetails = orderSkuDetail.getActivities();
        log.info("服务化查询活动详情信息,orderActivityDetails: {}", orderActivityDetails);
        List<SgAppOrderActDetail> wmAppOrderActDetailList = new ArrayList<>();

        if (org.apache.commons.collections.CollectionUtils.isEmpty(orderActivityDetails)) {
            return wmAppOrderActDetailList;
        }
        for (OrderActivityDetail orderActivityDetail : orderActivityDetails) {
            if (orderActivityDetail == null) {
                continue;
            }
            SgAppOrderActDetail wmAppOrderActDetail = new SgAppOrderActDetail();
            //处理活动id（门店级别营销活动id、商品级别营销活动id均通过item_id字段对外展示；非营销活动此处没有活动id）
            long originActivityId = orderActivityDetail.getOriginActivityId();
            long originActivityItemId = orderActivityDetail.getOriginActivityItemId();
            Long batchActivityId = getProductBatchIdFromOrderDischargeDetail(orderActivityDetail.getDischargeDetail());
            if (batchActivityId != null && batchActivityId > 0) {//批次级别营销活动
                wmAppOrderActDetail.setAct_id(batchActivityId);
            } else if (originActivityItemId > 0) {//商品级别营销活动
                wmAppOrderActDetail.setAct_id(originActivityItemId);
            } else if (originActivityId > 0) {//门店级别营销活动
                wmAppOrderActDetail.setAct_id(originActivityId);
            }
            //其余字段透传
            wmAppOrderActDetail.setType(orderActivityDetail.getType());
            wmAppOrderActDetail.setRemark(orderActivityDetail.getRemark());
            wmAppOrderActDetail.setMtCharge(orderActivityDetail.getMtCharge());
            wmAppOrderActDetail.setPoiCharge(orderActivityDetail.getPoiCharge());
            wmAppOrderActDetail.setCount(orderActivityDetail.getCount());
            wmAppOrderActDetailList.add(wmAppOrderActDetail);
        }
        return wmAppOrderActDetailList;
    }


    /**
     * 获取订单活动信息中的活动批次id
     *
     * @param dischargeDetail 订单中的优惠信息
     * @return java.lang.Long
     */
    private Long getProductBatchIdFromOrderDischargeDetail(String dischargeDetail) {

        if (StringUtils.isEmpty(dischargeDetail)) {
            log.warn("获取订单的dischargeDetailString失败,dischargeDetail:{}", dischargeDetail);
            return null;
        }
        OrderDischargeDetail orderDischargeDetail;
        try {
            orderDischargeDetail = JSONObject.parseObject(dischargeDetail, OrderDischargeDetail.class);
        } catch (Exception e) {
            log.error("转换订单的dischargeDetail异常, dischargeDetail:{}, e.getMessage:{}", dischargeDetail, e.getMessage(), e);
            return null;
        }

        if (orderDischargeDetail == null) {
            return null;
        }

        return orderDischargeDetail.getUuid();
    }
}
