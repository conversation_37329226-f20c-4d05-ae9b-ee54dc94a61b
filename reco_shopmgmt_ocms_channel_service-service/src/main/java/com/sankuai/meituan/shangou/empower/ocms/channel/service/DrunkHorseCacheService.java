package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.shangou.saas.common.enums.AuditTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.DrunkHorseWrongPoiMapping;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class DrunkHorseCacheService {

    @Resource(name = "drunkHorseRedisClient")
    private RedisStoreClient redisStoreClient;

    private static final String USER_APPLY_REFUND = "user_apply_refund";

    private static final String MT_MERCHANTS_OVER_TIME = "mt_merchants_over_time";

    private static final String MT_WX_MALL_POI_ORDER = "drunk_horse_cross_order";
    private static final String AUDIT_TYPE = "auditType";

    public void setUserApplyRefund(String viewOrderId, String refundId) {
        try {
            StoreKey key = new StoreKey(USER_APPLY_REFUND, viewOrderId, refundId);
            redisStoreClient.set(key, viewOrderId);
        } catch (Exception ex) {
            log.error("drunkHorse redis setUserApplyRefund error", ex);

        }
    }

    /**
     * 设置团测微商城门店下单异常订单映射
     * **/
    public boolean setPoiCrossOrderMapping(String viewOrderId, DrunkHorseWrongPoiMapping wrongPoiMapping){
        try {
            StoreKey key = new StoreKey(MT_WX_MALL_POI_ORDER, viewOrderId);
            return redisStoreClient.hsetnx(key, viewOrderId, wrongPoiMapping);
        } catch (Exception ex) {
            log.error("setWrongPoiMapping error", ex);
        }
        return false;
    }

    public DrunkHorseWrongPoiMapping getCrossOrderMapping(String viewOrderId){
        if (StringUtils.isBlank(viewOrderId)){
            return null;
        }
        try {
            StoreKey key = new StoreKey(MT_WX_MALL_POI_ORDER, viewOrderId);
            return redisStoreClient.hget(key, viewOrderId);
        } catch (Exception ex) {
            log.error("getWrongPoiMapping error", ex);
        }
        return null;
    }

    public Boolean isExistUserApplyRefund(String viewOrderId, String refundId) {
        try {
            StoreKey key = new StoreKey(USER_APPLY_REFUND, viewOrderId, refundId);
            return redisStoreClient.exists(key);
        } catch (Exception ex) {
            log.error("drunkHorse redis setUserApplyRefund error", ex);
            return false;
        }
    }

    public void setMtMerchantsOverTimeAudit(String viewOrderId) {
        try {
            StoreKey key = new StoreKey(MT_MERCHANTS_OVER_TIME, viewOrderId);
            redisStoreClient.set(key, viewOrderId);
        } catch (Exception ex) {
            log.error("drunkHorse redis setMtMerchantsOverTimeAudit error", ex);
        }
    }


    public void setAuditType(String viewOrderId, Integer auditType) {
        try {
            StoreKey key = new StoreKey(AUDIT_TYPE, viewOrderId);
            redisStoreClient.set(key, auditType);
        } catch (Exception ex) {
            log.error("drunkHorse redis setAuditType error", ex);
        }
    }

    public Integer getAuditType(String viewOrderId) {
        try {
            StoreKey key = new StoreKey(AUDIT_TYPE, viewOrderId);
            return redisStoreClient.get(key);
        } catch (Exception ex) {
            log.error("drunkHorse redis getAuditType error", ex);
            return AuditTypeEnum.UNKNOWN_AUDIT.getCode();
        }
    }







}
