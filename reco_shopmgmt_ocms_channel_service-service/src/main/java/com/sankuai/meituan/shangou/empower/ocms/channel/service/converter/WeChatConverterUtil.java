package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.weixin.BatchWeChatUserInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.weixin.OpenIdInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.weixin.WeChatGetTicketRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.weixin.WeChatSendTemplateMessageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.weixin.WeChatUserInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.WeChatActionNameEnum;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 微信开放平台数据转换工具
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/04
 */
public class WeChatConverterUtil {

	public static Map<String, Object> buildWeChatQueryUserInfoBizParam(WeChatUserInfoRequest request) {
		Map<String, Object> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.OPEN_ID, request.getOpenId());
		return bizParam;
	}

	public static Map<String, Object> buildWeChatBatchQueryUserInfoBizParam(List<String> openIdList) {
		Map<String, Object> bizParam = Maps.newHashMap();
		List<OpenIdInfo> openIdInfos = openIdList.stream()
				.map(openId ->
					new OpenIdInfo(openId, "zh_CN"))
				.collect(Collectors.toList());

		bizParam.put(ProjectConstant.USER_LIST, openIdInfos);
		return bizParam;
	}


    public static Map<String, Object> buildWeChatGetTicketInfoBizParam(WeChatGetTicketRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        if (request.getActionType() == null || request.getActionType() == WeChatActionNameEnum.LIMIT.getCode()) {
            bizParam.put(ProjectConstant.ACTION_NAME, WeChatActionNameEnum.LIMIT.getActionName());
            bizParam.put(ProjectConstant.ACTION_INFO, JSON.parseObject(buildActionInfo(request.getSceneId())));
            return bizParam;
        }
        WeChatActionNameEnum actionType = WeChatActionNameEnum.EnumOf(request.getActionType());
        if (actionType == null) {
            return bizParam;
        }
        bizParam.put(ProjectConstant.ACTION_NAME, actionType.getActionName());
        bizParam.put(ProjectConstant.EXPIRE_SECONDS, request.getExpireSeconds());
        //采用整型scene_id
        if (actionType.equals(WeChatActionNameEnum.TEMP)){
            bizParam.put(ProjectConstant.ACTION_INFO, JSON.parseObject(buildActionInfo(request.getSceneId())));
        }
        //采用字符串型scene_str
        if (actionType.equals(WeChatActionNameEnum.TEMP_STR)){
            bizParam.put(ProjectConstant.ACTION_INFO, JSON.parseObject(buildActionInfoStr(request.getSceneStr())));
        }

        return bizParam;
    }

	public static Map<String, Object> buildWeChatSendTemplateMessageBizParam(WeChatSendTemplateMessageRequest request) {
		Map<String, Object> data = JsonUtils.fromJson(JsonUtils.toJson(request), new TypeReference<Map<String, Object>>(){});
		data.remove("tenantId");
		return data;
	}

	private static String buildActionInfo(Integer sceneId) {
		StringBuilder sb = new StringBuilder();
		sb.append("{\"scene\": {\"scene_id\": ");
		sb.append(sceneId);
		sb.append("}}");
		return sb.toString();
	}

    private static String buildActionInfoStr(String sceneStr) {
        StringBuilder sb = new StringBuilder();
        sb.append("{\"scene\": {\"scene_str\":\" ");
        sb.append(sceneStr);
        sb.append("\"");
        sb.append("}}");
        return sb.toString();
    }
}
