package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.RHINO_UPTIMATE_SET;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ChannelErrorCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtAuthInfoRespDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MtResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;

/**
 * 美团请求公共服务接口
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:09
 **/
@Service("mtBrandChannelGateService")
public class MtBrandChannelGateService extends BaseChannelGateService {
    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Value("${mt.url.base}")
    private String baseUrl ;

    @Resource
    private CommonLogger log;

    /**
     * 传入门店id返回map结构、如果确定是返回dto请使用sendPostReturnDto、
     * 确定是多门店请求返回结果是map请使用sendPostAppMapDto
     *
     * @param postUrlEnum 请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam 请求业务参数
     * @return
     */
    @Deprecated
    @Override
    public <T> T sendPost(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
        if (CollectionUtils.isEmpty(baseRequest.getStoreIdList())) {
            return sendPostReturnDto(postUrlEnum, baseRequest, bizParam);
        }
        return super.sendPostAppMapDto(postUrlEnum, baseRequest, bizParam);
    }

    /**
     * 返回非map结构
     */
    public <T> T sendPostReturnDto(ChannelPostInter postUrlEnum, Long tenantId, Integer channelId, Object bizParam, Long storeId) {
        BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(channelId);
        if (storeId > NumberUtils.LONG_ZERO) {
            baseRequest.setStoreIdList(Lists.newArrayList(storeId));
        }

        return sendPostReturnDto(postUrlEnum, baseRequest, bizParam);
    }

    /**
     * 传入门店id也返回对象、订单相关service大部分是返回对象
     * 多品牌各渠道发送请求公共方法门店维度 post
     *
     * @param postUrlEnum 请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam    请求业务参数
     * @return
     */
    public <T> T sendPostReturnDto(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
            return super.sendPostAppDto(postUrlEnum, baseRequest, bizParam);
    }

    /**
     * 各渠道发送请求公共方法
     *
     * @param url      请求URL
     * @param bizParam 请求业务参数
     * @return
     */
    @Override
    public Map<String, Object> sendPost(String url, String method, BaseRequest baseRequest, Object bizParam) {
        if (CollectionUtils.isNotEmpty(baseRequest.getStoreIdList()) && baseRequest.getStoreIdList().get(0) != null) {

            String sysParam = MccConfigUtil.getQnhSysCodeStr(baseRequest.getTenantId(), baseRequest.getStoreIdList().get(0));

            if (StringUtils.isNotEmpty(sysParam)) {

                log.info("sysParam:{}", sysParam);
                Map<String, Object> systemParam = JSON.parseObject(sysParam);

                if (MapUtils.isNotEmpty(systemParam)) {
                    return sendPost(url, method, baseRequest, bizParam, systemParam);
                }
            }
        }
        return super.sendPostApp(url, method, baseRequest, bizParam);
    }

    @Override
    public <T> T postToChannel(String channelPoiCode, String channelOnlinePoiCode, ChannelPostInter postUrlEnum,
                               Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest) {
        replaceStoreId(bizParamMap, channelOnlinePoiCode, postUrlEnum);

        // 限频
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
        if (StringUtils.isNotBlank(appId) && postUrlEnum.requestLimited()) {
            long waitTime = clusterRateLimiter.tryAcquire(postUrlEnum.generateLimitedResourceKey(), appId, baseRequest.isAsyncInvoke());
            if (waitTime != 0) {
                if (RHINO_UPTIMATE_SET.contains(postUrlEnum)) {
                    log.info("MtBrandChannelGateService.postToChannel appId:{} url:{} 未获取到令牌 抛出异常", appId, postUrlEnum.getUrl());
                    throw new InvokeChannelTooMuchException(waitTime);
                }
                else {
                    if (MccConfigUtil.getNeedThrowExceptionWhenTriggerLimitForOpenApi().contains(postUrlEnum.getUrl())) {
                        throw new InvokeChannelTooMuchException(0);
                    }
                    if (MccConfigUtil.getChannelLimiterErrorPoster().contains(postUrlEnum)) {
                        log.error("MtBrandChannelGateService.postToChannel 触发限流 appId:{} url:{} ", appId, postUrlEnum.getUrl());
                        return (T) ChannelResponseDTO.defaultLimitErrorResponse();
                    }
                    return null;
                }
            }
        }

        String url = getPostUrl(postUrlEnum);
        Map<String, Object> postParams = generatePostParams(url, null, sysParam, bizParamMap);

        String resp;

        // 默认所有接口都解析部分失败错误信息，如有特殊接口不需要解析部分失败信息需在此处加下
        boolean parsePartFailedMsgFlag = true;
        ChannelPostMTEnum channelPostMTEnum = (ChannelPostMTEnum) postUrlEnum;
        switch (channelPostMTEnum) {
            case PICTURE_UPLOAD:
                parsePartFailedMsgFlag = false;
                resp = postRequest(postUrlEnum, postParams, Lists.newArrayList((String) postParams.get("img_name")), baseRequest);
                break;
            case VIDEO_UPLOAD:
                resp = postRequest(postUrlEnum, postParams, Lists.newArrayList((String) postParams.get("video_name")),
                        baseRequest);
                break;
            default:
                resp = postRequest(postUrlEnum, postParams, baseRequest);
                break;
        }

        if (channelPostMTEnum == ChannelPostMTEnum.UPDATE_CUSTOM_SKU_ID) {
            return dealPostResult4UpdateCustomSkuId(resp, postUrlEnum.getResultClass());
        }

        ChannelResponseDTO result = dealPostResult(resp, postUrlEnum.getResultClass(), parsePartFailedMsgFlag);
        channelResponseMetric(ChannelTypeEnum.MEITUAN, postUrlEnum, baseRequest, result, ChannelResponseDTO::getResponseError);
        if (result != null) {
            // 图片接口不打印请求参数
            if (ChannelPostMTEnum.PICTURE_UPLOAD == postUrlEnum) {
                log.info("【渠道原始日志】, operate:{}, url:{}, success:{},request:{}, result:{}",
                        postUrlEnum.getUrlShortName(),
                        postUrlEnum.getUrl(),
                        parseSuccess(result),
                        baseRequest,
                        resp);
            }
            else {
                log.info("【渠道原始日志】, operate:{}, url:{}, success:{}, isErp:{}, request:{}, postParams:{}, result:{}",
                        postUrlEnum.getUrlShortName(),
                        postUrlEnum.getUrl(),
                        parseSuccess(result),
                        baseRequest.isErpTenant(),
                        baseRequest,
                        postParams,
                        resp);
            }
        }

        if (Objects.nonNull(result) && Objects.nonNull(result.getError()) &&
                MtResultCodeEnum.TRIGGER_FLOW_CONTROL.getCodeStr().equals(result.getErrno())) {
            if (RHINO_UPTIMATE_SET.contains(postUrlEnum)) {
                log.warn("推送美团渠道触发限频，返回客户端重试,result {}", result);
                throw new InvokeChannelTooMuchException(0);
            }
        }
        return (T) result;
    }

    private Boolean parseSuccess(ChannelResponseDTO result) {
        if (result == null || result.getResult_code() == null) {
            return false;
        }

        return ChannelErrorCode.MT.SUCCESS_INTEGER.equals(result.getResult_code());
    }

    /***
     * 对get的参数进行encode
     * 因为商家退款接口输入特殊字符导致接口调用失败
     * 注意：底层getUrl没有对param进行encode，在这里先对param进行encode，如果后续httpClient的assemble方法也进行了encode，这里需要放开
     * ****/
    @Override
    public Map<String, Object> sendEncodedGet(String url, String method, BaseRequest baseRequest, Object bizParam) {
        return sendEncodedGetApp(url, method, baseRequest, bizParam);
    }

    @Override
    public Map<String, Object> sendGet(String url, String method, BaseRequest baseRequest, Object bizParam) {
        if (CollectionUtils.isNotEmpty(baseRequest.getStoreIdList()) && baseRequest.getStoreIdList().get(0) != null) {

            String sysParam = MccConfigUtil.getQnhSysCodeStr(baseRequest.getTenantId(), baseRequest.getStoreIdList().get(0));

            if (StringUtils.isNotEmpty(sysParam)) {

                log.info("sysParam:{}", sysParam);
                Map<String, Object> systemParam = JSON.parseObject(sysParam);

                if (MapUtils.isNotEmpty(systemParam)) {
                    return sendGet(url, method, baseRequest, systemParam, bizParam);
                }
            }
        }

        return super.sendGetApp(url, method, baseRequest, bizParam);
    }

    public Map<String, Object> sendGet(String url, String method,BaseRequest baseRequest,
                                       Map<String, Object> sysParam, Object bizParam) {
        // 参数实体转换为Map
        Map<String, Object> bizParamMap = null;
        if (!(bizParam instanceof Map)) {
            try {
                bizParamMap = ConverterUtils.getProperty(bizParam);
            }
            catch (Exception e) {
                log.error("sendGet 获取bizParamMap异常", e);
            }
        }
        else {
            bizParamMap = (Map<String, Object>) bizParam;
        }
        // 拼接参数
        Map<String, String> postParam = generatePostParams(url, method, sysParam, bizParamMap);

        // 请求
        return getUrl(url, baseRequest, postParam);
    }


    private <T> T dealPostResult4UpdateCustomSkuId(String resp, Class resultClass) {
        ChannelResponseDTO dto = ChannelResponseDTO.parseResult4UpdateCustomSkuId(resp, resultClass);
        return (T) dto;
    }

    @Override
    protected Map generatePostParams(String url, String method, Map<String, Object> sysParam, Map<String, Object> bizParam) {
        sysParam.put("timestamp", String.valueOf(DateUtils.unixTime()));
        HashMap<String, Object> paramMap = Maps.newHashMap(sysParam);
        paramMap.putAll(bizParam);
        String paramForSig = MtSignUtils.concatParamsContainsEmpty(paramMap);
        String sig = MtSignUtils.getSig(url, paramForSig, (String) sysParam.get("secret"));
        paramMap.put("sig", sig);
        paramMap.remove("secret");

        return paramMap;
    }

    public static final String MT_MULTI_STORE = "app_poi_codes";

    private void replaceStoreId(Map<String, Object> bizParamMap, String outStoreId, ChannelPostInter postInter) {
        if (StringUtils.isNotBlank(outStoreId)) {
            if (ChannelPostMTEnum.VIDEO_UPLOAD == postInter) {
                bizParamMap.put(MT_MULTI_STORE, outStoreId);
            } else {
                bizParamMap.put(Constant.FIELD_NAME_STOREID_MT, outStoreId);
            }
        }
    }

    private ChannelResponseDTO dealPostResult(String resultJson, Class resultClass, boolean isUpdatePrice) {
        return ChannelResponseDTO.parseResult(resultJson, resultClass, isUpdatePrice);
    }

    @Override
    public String getPostUrl(ChannelPostInter postUrlEnum) {
        return getBaseUrl() + postUrlEnum.getUrl();
    }

    public <T> T sendPost(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Long storeId, Object bizParam) {
        if (storeId != null) {
            baseRequest.setStoreIdList(Lists.newArrayList(storeId));
        }

        return sendPost(postUrlEnum, baseRequest, bizParam);
    }

     public String getBaseUrl() {
        return baseUrl;
    }


    /**
     * 快捷授权接口（直接获取access_token接口）
     * @return
     */
    public MtAuthInfoRespDTO fastGetToken(Map<String, Object> params){
        String url = getPostUrl(ChannelPostMTEnum.OAUTH_AUTHORIZE);
        Map<String, String> postParamToken = generatePostParams(url, null,
                params, new HashMap<>());
        Map<String, Object> tokenMap = getUrl(url, new BaseRequest(), postParamToken);

        if (MapUtils.isNotEmpty(tokenMap) && 0 == (Integer) tokenMap.get("status")) {
            return MtAuthInfoRespDTO.convertFromMap(tokenMap);
        } else {
            log.error("快捷授权接口失败 postMTEnum:{} params:{}", ChannelPostMTEnum.OAUTH_AUTHORIZE, tokenMap);
            throw new BizException("快捷授权接口失败");
        }
    }

    /**
     * 通过refresh_token刷新access_token的接口
     * @return
     */
    public MtAuthInfoRespDTO refreshToken(Map<String, Object> params){
        String url = getPostUrl(ChannelPostMTEnum.OAUTH_TOKEN);
        Map<String, Object> postParamToken = generatePostParams(url, null,
                params, new HashMap<>());
        Map<String, Object> tokenMap = postUrl(url, new BaseRequest(), postParamToken);

        if (MapUtils.isNotEmpty(tokenMap) && 0 == (Integer) tokenMap.get("status")) {
            MtAuthInfoRespDTO.convertFromMap(tokenMap);
        } else {
            log.error("美团token刷新失败 postMTEnum:{} params:{}", ChannelPostMTEnum.OAUTH_TOKEN, tokenMap);
            throw new BizException("美团token刷新失败");
        }
        return MtAuthInfoRespDTO.convertFromMap(tokenMap);
    }

}