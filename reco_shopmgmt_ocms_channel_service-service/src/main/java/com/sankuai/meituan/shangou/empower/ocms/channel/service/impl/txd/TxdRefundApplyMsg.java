package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class TxdRefundApplyMsg {

    @JSO<PERSON>ield(name = "refundFee")
    private Integer refundFee;
    @JSONField(name = "merchantCode")
    private String merchantCode;
    @JSONField(name = "reverseType")
    private Integer reverseType;
    @JSONField(name = "refundReason")
    private String refundReason;
    @JSONField(name = "outOrderId")
    private String outOrderId;
    @JSONField(name = "orderFrom")
    private Integer orderFrom;
    @JSONField(name = "subRefundOrders")
    private List<TxdSubRefundOrdersDTO> subRefundOrders;
    @JSONField(name = "storeId")
    private String storeId;
    @JSONField(name = "refundDeliveryFee")
    private Integer refundDeliveryFee;
    @JSONField(name = "refundId")
    private String refundId;
    @JSONField(name = "refundPictures")
    private String refundPictures;

    @JSONField(name = "remarks")
    private String remarks;
}
