package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.onelimiter.LimitResult;
import com.dianping.rhino.onelimiter.OneLimiter;
import com.dianping.rhino.onelimiter.OneLimiterConstants;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.Uninterruptibles;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccFileConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.RateLimitRandomWaitTimeUtil;
import com.sankuai.meituan.shangou.saas.common.collection.ListUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * @Title: RhinoClusterRateLimiter
 * @Description: 基于rhino统一限流器实现集群限频 需要在rhino后台配置对应key和限频策略
 * <AUTHOR>
 * @Date 2019/6/26 19:28
 */
@Service
public class RhinoClusterRateLimiter implements ClusterRateLimiter {
    private static final OneLimiter ONE_LIMITER = Rhino.newOneLimiter();
    private static final long UNLIMITED = -1L;
    @Resource
    private CommonLogger log;
    private Random random = new Random();

    /**
     * 获取请求令牌 并且 返回需要等待的时间
     *
     * @param limitedResourceKey 资源标识
     * @param uuid               请求标识
     * @param isAsync             是否异步请求
     * @return -1: 无限等待；0：表示通过限流 ；其他代表需要等待的毫秒数
     */
    @Override
    public long tryAcquire(String limitedResourceKey, String uuid, Boolean isAsync) {
        // 是否个性化限频
        if (MccFileConfigUtil.isPersonalizedRateLimit(limitedResourceKey, uuid)) {
            limitedResourceKey = generatePersonalizedLimitedResourceKey(limitedResourceKey, uuid);
        }
        // 同步调用需要有重试;异步调用一次不成功则计算需要等待的时间
        if (isAsync) {
            Map<String, String> params = Maps.newHashMap();
            params.put(OneLimiterConstants.UUID, uuid);
            LimitResult result = ONE_LIMITER.run(limitedResourceKey, params);
            log.info("RhinoClusterRateLimiter.tryAcquire limitedKey:{} uuid:{} result:{} ", limitedResourceKey, uuid, result);
            // 这里等待时间实际上和租户没关系，理论上只和渠道有关系，暂时设置一个配置值，但是不和渠道关联
            return  result.isPass() ? 0L : randomWaitTime(limitedResourceKey);
        } else {
            boolean pass = tryAcquireLimitKeyWithRetry(limitedResourceKey, uuid);
            return pass ? 0L : UNLIMITED;
        }
    }

    private long randomWaitTime(String limitedResourceKey) {
        Long waitTime = RateLimitRandomWaitTimeUtil.getWaitTime(limitedResourceKey);
        if (Objects.nonNull(waitTime)) {
            return waitTime;
        }

        String waitString = MccConfigUtil.getLimiterRandomWaitTime();
        List<Long> waitList = ListUtil.longListFromString(waitString, ",");
        long start = 5000;
        int step = 5000;
        // 配置不合理，用默认值让流程继续下去
        if (waitList.size() == 2 && waitList.get(0).compareTo(waitList.get(1)) < 0) {
            start = waitList.get(0);
            step = waitList.get(1).intValue() - waitList.get(0).intValue();
        }
        long rd = random.nextInt(step);
        return (start + rd);
    }

    @Override
    public boolean tryAcquire(String limitedResourceKey, String uuid) {
        return tryAcquire(limitedResourceKey, uuid, false) == 0l;
    }

    private String generatePersonalizedLimitedResourceKey(String limitedResourceKey, String uuid) {
        return uuid + "-" + limitedResourceKey;
    }

    /**
     * 按限频key 获取令牌 如rhino管理端未配置返回true
     *
     * @param limitedKey
     * @param uuid
     * @return
     */
    private boolean tryAcquireLimitKeyWithRetry(String limitedKey, String uuid) {
        try {
            int tryTimes = 1;
            Map<String, String> params = Maps.newHashMap();
            params.put(OneLimiterConstants.UUID, uuid);
            LimitResult result = ONE_LIMITER.run(limitedKey, params);
            if (result.isPass()) {
                return true;
            }
            RhinoRateLimitedResult rhinoRateLimitedResult = RhinoRateLimitedResult.parse(result.getMsg());
            int retry = rhinoRateLimitedResult.getRetry();
            long stepMillis = rhinoRateLimitedResult.getStepMillis();
            while (retry-- > 0) {
                Uninterruptibles.sleepUninterruptibly(stepMillis, TimeUnit.MILLISECONDS);
                tryTimes++;
                result = ONE_LIMITER.run(limitedKey, params);
                log.info("RhinoClusterRateLimiter.tryAcquireLimitKeyWithRetry limitedKey:{} uuid:{} result:{} try:{}", limitedKey, uuid, result, tryTimes);
                if (result.isPass()) {
                    return true;
                }
            }
            log.warn("RhinoClusterRateLimiter.tryAcquireLimitKeyWithRetry final fail. limitedKey:{} uuid:{} try:{}", limitedKey, uuid, tryTimes);
            return false;
        } catch (Exception e) {
            log.error("RhinoClusterRateLimiter.tryAcquireLimitKeyWithRetry error. limitedKey:{} uuid:{}", limitedKey, uuid, e);
            return true;//异常时不限制
        }
    }

    @Override
    public boolean tryAcquire(ChannelPostInter channelPostInter, String uuid) {
        if (channelPostInter == null) {
            log.warn("tryAcquire illegal request. channelPostInter is null. uuid:{}", uuid);
            return true;
        }
        return !channelPostInter.requestLimited() || tryAcquire(channelPostInter.generateLimitedResourceKey(), uuid);
    }

    @Override
    public long tryAcquire(ChannelPostInter channelPostInter, String uuid, Boolean isAsync) {
        if (channelPostInter == null) {
            log.warn("tryAcquire illegal request. channelPostInter is null. uuid:{}", uuid);
            return 0L;
        }
        if (!channelPostInter.requestLimited()) {
            return 0L;
        }
        return tryAcquire(channelPostInter.generateLimitedResourceKey(), uuid, isAsync);
    }

    @Data
    @Slf4j
    static class RhinoRateLimitedResult {
        private long stepMillis = 1000L;
        private int retry = 0;
        private String msg = "调用过于频繁，请稍后再试";

        static RhinoRateLimitedResult parse(String content) {
            if (StringUtils.isBlank(content)) {
                return new RhinoRateLimitedResult();
            }
            try {
                return JSON.parseObject(content, RhinoRateLimitedResult.class);
            } catch (Exception e) {
                log.error("RhinoRateLimitedResult.parse error. content:{}", content, e);
                return new RhinoRateLimitedResult();
            }
        }
    }

}
