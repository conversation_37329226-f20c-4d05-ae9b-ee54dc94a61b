package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.UpdateCustomSkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.UpdateCustomSpuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.BatchGetFreightTemplateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.UpdateProductStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.BatchFreightTemplateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSkuListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleUpdateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuUpdateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.UpdateCustomSkuIdResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.UpdateCustomSpuIdResponse;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/17
 * 渠道总部商品服务
 **/
public interface ChannelMerchantSpuService {

    /**
     * 创建总部渠道商品
     *
     * @param request
     * @return
     */
    MerchantSpuCreateResponse createSpu(MerchantSpuCreateRequest request);

    /**
     * 更新总部渠道商品
     *
     * @param request
     * @return
     */
    MerchantSpuUpdateResponse updateSpu(MerchantSpuUpdateRequest request);

    /**
     * 删除总部渠道商品
     *
     * @param request
     * @return
     */
    MerchantSpuDeleteResponse deleteSpu(MerchantSpuDeleteRequest request);

    /**
     * 获取总部渠道商品详情
     *
     * @param request
     * @return
     */
    MerchantSpuDetailResponse getSpuDetail(MerchantSpuDetailRequest request);

    /**
     * 获取总部渠道商品单规格详情
     *
     * @param request
     * @return
     */
    MerchantSpuDetailResponse getSpuDetailSingle(MerchantSpuDetailRequest request);


    /**
     * 获取总部商品规格列表
     * @param request
     * @return
     */
    default MerchantSkuListResponse getMerchantSkuList(MerchantSpuDetailRequest request){
        return null;
    }

    /**
     * 获取商品创建状态
     * @param request
     * @return
     */
    MerchantSpuCreateResponse getSpuCreateStatus(MerchantSpuDetailRequest request);



    /**
     * 单个创建总部渠道商品
     *
     * @param request
     * @return
     */
    default MerchantSpuSingleCreateResponse singleCreateSpu(MerchantSpuSingleCreateRequest request) {
        return null;
    }

    /**
     * 更新总部渠道商品
     *
     * @param request
     * @return
     */
    default MerchantSpuSingleUpdateResponse singleUpdateSpu(MerchantSpuSingleUpdateRequest request) {
        return null;
    }
    
    
    default MerchantSpuSingleDeleteResponse singleDeleteSpu(MerchantSpuSingleDeleteRequest request) {
        return null;
    }


    /**
     * 更新商品店内分类
     * @param request
     * @return
     */
    default MerchantSpuSingleUpdateResponse singleUpdateSpuStoreCategory(UpdateProductStoreCategoryRequest request) {
        return null;
    }


    /**
     * 获取运费模板
     * @param request
     * @return
     */
    default BatchFreightTemplateResponse getFreightTemplateList(BatchGetFreightTemplateRequest request) {
        return null;
    }

    default UpdateCustomSpuIdResponse updateCustomSpuId(UpdateCustomSpuIdRequest request){
        return null;
    }

    default UpdateCustomSkuIdResponse updateCustomSkuId(UpdateCustomSkuIdRequest request){
        return null;
    }
}

