package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.application;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelAppService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.DouyinChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.application.AppInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class ChannelAppServiceImpl implements ChannelAppService {

    @Resource
    private DouyinChannelCommonService douyinChannelCommonService;

    @Override
    public AppInfoDTO queryAppInfoByTenant(Long tenantId, Integer channelId) {
        if(channelId == ChannelType.DOU_YIN.getValue()) {
            return douyinChannelCommonService.queryAppInfoByTenant(tenantId, channelId);
        }
        log.info("当前查询非抖音渠道，暂不处理");
        return null;
    }
}
