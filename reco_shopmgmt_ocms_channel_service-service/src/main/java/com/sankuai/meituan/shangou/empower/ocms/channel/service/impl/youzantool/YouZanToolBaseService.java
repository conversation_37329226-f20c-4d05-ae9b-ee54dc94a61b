package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import com.google.common.base.Preconditions;
import com.google.common.base.Stopwatch;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.TokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostYZEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.HttpClientUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MetricUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.api.API;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.core.client.auth.Token;
import com.youzan.cloud.open.sdk.core.client.core.DefaultYZClient;
import com.youzan.cloud.open.security.SecretClient;
import com.youzan.cloud.open.security.exception.DataSecurityException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 11/29/22 10:41 AM
 */
@Slf4j
public class YouZanToolBaseService {

    @Autowired
    private DefaultYZClient yzClient;
    @Autowired
    private YzToolAccessTokenService accessTokenService;

    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    final LoadingCache<AppMessage, SecretClient> SECRET_CLIENT_CACHE = CacheBuilder.newBuilder()
            //本地最多缓存5000个应用
            .maximumSize(5000)
            //缓存20分钟实效
            .expireAfterAccess(10, TimeUnit.MINUTES)
            //加载token
            .build(new CacheLoader<AppMessage, SecretClient>() {
                @Override
                public SecretClient load(AppMessage key) {
                    try {
                        return new SecretClient(key.getClientId(), key.getClientSecret());
                    }
                    catch (DataSecurityException e) {
                        throw new BizException("SecretClient init error,excption:", e);
                    }
                }
            });

    /**
     * 有赞基础调用
     * @param appMessage  应用信息用于换取token
     * @param api         请求参数及接口
     * @param clz         返回结果
     * @param <T>
     * @return
     * @throws SDKException
     */
    public <T> T getResult4YouZan(AppMessage appMessage,API api, Class<T> clz) throws SDKException {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            log.info("YouZanToolBaseService request appMessage{}, api{}", appMessage, JSON.toJSONString(api));
            T result = yzClient.invoke(api, new Token(accessTokenService.getAccessToken(appMessage)), clz);
            log.info("YouZanToolBaseService api: {} response: {}", api.getName(), JSON.toJSONString(result));
            metricRequestCount(api, result);
            return result;
        }
        catch (SDKException sdkEx) {
            metricRequestErrorCount(api, sdkEx.getCode());
            throw sdkEx;
        } finally {
            long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
            HttpClientUtil.metricRequestTime(getHost(api), api.getName(), elapsed);
            log.info("有赞渠道发送API请求的api:{}, cost={}", api.getName(), elapsed);
        }
    }

    public <T> T getAccessToken4YouZan(AppMessage appMessage,API api, Class<T> clz) throws SDKException {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            log.info("YouZanToolBaseService request appMessage{}, api{}", appMessage, JSON.toJSONString(api));
            TokenMessage accessToken4Yz = accessTokenService.getAccessToken4Yz(appMessage);
            T result = yzClient.invoke(api, new Token(accessToken4Yz.getToken()), clz);
            log.info("YouZanToolBaseService api: {} response: {}", api.getName(), JSON.toJSONString(result));
            return result;
        }
        catch (SDKException sdkEx) {
            metricRequestErrorCount(api, sdkEx.getCode());
            throw sdkEx;
        } finally {
            long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
            HttpClientUtil.metricRequestTime(getHost(api), api.getName(), elapsed);
            log.info("有赞渠道发送API请求的api:{}, cost={}", api.getName(), elapsed);
        }
    }

    /**
     * 有赞基础调用，本接口会进行限频操作，使用该接口，先查看当前接口是否存在限频配置
     * @param appMessage  应用信息用于换取token
     * @param api         请求参数及接口
     * @param clz         返回结果
     * @param <T>
     * @return
     * @throws SDKException
     */
    public <T> T getResult4YouZanByRhino(ChannelPostYZEnum postYZEnum, AppMessage appMessage, API api, Class<T> clz) throws SDKException {
        // 限频控制
        if (MccConfigUtil.isGrayYzRhinoConfig() && !clusterRateLimiter.tryAcquire(postYZEnum, appMessage.getGrantId())) {
            log.warn("当前有赞请求触发限频，请稍后重试！api [{}].", api.getName());
            throw new BizException(ResultCode.TRIGGER_LIMIT.getCode(), "当前有赞请求接口过于频繁，请稍后重试！");
        }
        return getResult4YouZan(appMessage, api, clz);
    }

    /**
     * 连锁总店 AppMessage
     * @param tenantId
     * @return
     */
    public AppMessage getMainAppMessage(Long tenantId) {
        CopAccessConfigDO tenantChannelConfig = copAccessConfigService.findTenantChannelConfig(tenantId, ChannelTypeEnum.YOU_ZAN.getCode());
        if (tenantChannelConfig == null) {
            throw new BizException(ResultCode.CHANNEL_APP_ID_INVALID.getCode(), "租户未绑定");
        }
        String sysParamJson = tenantChannelConfig.getSysParams();
        JSONObject sysParam = JSON.parseObject(sysParamJson);
        return AppMessage.builder()
                .clientId(tenantChannelConfig.getTenantAppId())
                .grantId(sysParam.getString(ProjectConstant.YZ_GRANT_ID))
                .clientSecret(sysParam.getString(ProjectConstant.SECRET))
                .build();
    }

    /**
     * 连锁分店 AppMessage
     * @param tenantId
     * @param storeId
     * @return
     */
    public AppMessage getSubAppMessage(Long tenantId, Long storeId) {
        CopAccessConfigDO tenantChannelConfig = copAccessConfigService.findTenantChannelConfig(tenantId, ChannelTypeEnum.YOU_ZAN.getCode());
        if (tenantChannelConfig == null) {
            throw new BizException(ResultCode.CHANNEL_APP_ID_INVALID.getCode(), "租户未绑定");
        }
        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(), storeId);
        if (channelStore == null) {
            throw new BizException(ResultCode.CHANNEL_POI_CODE_INVALID.getCode(), "门店未绑定");
        }
        return AppMessage.builder()
                .clientId(tenantChannelConfig.getTenantAppId())
                .grantId(channelStore.getChannelPoiCode())
                .clientSecret(JSON.parseObject(tenantChannelConfig.getSysParams()).getString(ProjectConstant.SECRET))
                .build();
    }


    public SecretClient getClientSecret(AppMessage appMessage) {
        try {
            return SECRET_CLIENT_CACHE.get(appMessage);
        }
        catch (ExecutionException e) {
            log.info("get youzan SecretClient 4 cache error, exception:", e);
            throw new BizException("get youzan SecretClient error");
        }
    }

    public Map<Long, ChannelStoreDO> queryChannelStoreInfo(Long tenantId, int channelId, List<Long> storeIds) {
        Map<String, ChannelStoreDO> pois = copChannelStoreService.getChannelPoiCode(tenantId, channelId, storeIds);
        Preconditions.checkNotNull(pois, ChannelResultStatusEnum.VALIDATE_TELNET_ERROR.getDesc(), storeIds.toString());
        Preconditions.checkArgument(pois.size() == storeIds.size(), ChannelResultStatusEnum.VALIDATE_TELNET_ERROR.getDesc(), storeIds.toString());
        pois.values().forEach(t -> {
            Preconditions.checkArgument(StringUtils.isNotBlank(t.getChannelOnlinePoiCode()), ChannelResultStatusEnum.VALIDATE_TELNET_ERROR.getDesc(), t.getStoreId());
        });

        return pois.values().stream().collect(Collectors.toMap(ChannelStoreDO::getStoreId, v -> v, (s1, s2) -> s1));
    }

    private String getHost(API api){
        try {
            if(api == null){
                return "open.youzanyun.com";
            }
            return Optional
                    .ofNullable(api.getGateway())
                    .filter(StringUtils::isNotBlank)
                    .orElse("https://open.youzanyun.com")
                    .replaceAll("https://", "");
        } catch (Exception e) {
            return "open.youzanyun.com";
        }
    }

    private <T> Pair<Integer, Boolean> getResultStatus(T result) {
        try {
            if(result == null){
                return null;
            }
            Class<?> clz = result.getClass();
            Field codeFiled = ReflectionUtils.findField(clz, "code");
            Field successFiled = ReflectionUtils.findField(clz, "success");
            // 非标result不进行埋点
            if (codeFiled == null || successFiled == null) {
                return null;
            }
            if(!codeFiled.isAccessible()){
                codeFiled.setAccessible(true);
            }
            if(!successFiled.isAccessible()){
                successFiled.setAccessible(true);
            }
            Object code = ReflectionUtils.getField(codeFiled, result);
            Object success = ReflectionUtils.getField(successFiled, result);
            return Pair.of(getCode(code), getSuccess(success));
        } catch (Exception e) {
            log.error("parse result error, exception:", e);
            return Pair.of(-1, false);
        }
    }

    private Integer getCode(Object value) {
        if(value == null){
            return -1;
        }
        if(value instanceof Integer){
            return (Integer) value;
        }
        if(value instanceof String
                && StringUtils.isNumeric((String) value)){
            return Integer.parseInt((String) value);
        }
        return -1;
    }

    private Boolean getSuccess(Object value) {
        if(value == null){
            return false;
        }
        if(value instanceof Boolean){
            return (Boolean) value;
        }
        return true;
    }

    private <T> void metricRequestCount(API api, T result){
        try {
            if(result == null){
                return;
            }
            Pair<Integer, Boolean> resultStatus = getResultStatus(result);
            if(resultStatus == null){
                return;
            }

            Map<String, String> tag = new HashMap<>();
            tag.put("url", api.getName());
            tag.put("status", Optional.ofNullable(resultStatus.getKey()).orElse(-1).toString());
            tag.put("result", Optional.ofNullable(resultStatus.getValue()).orElse(false) ? "ok" : "fail");
            MetricUtils.count(getHost(api).concat("[result]"), tag, 1);
        } catch (Exception e) {
            log.error("metricRequestCount error, exception:", e);
        }
    }

    private void metricRequestErrorCount(API api, Integer code){
        try {
            Map<String, String> tag = new HashMap<>();
            tag.put("url", api.getName());
            tag.put("status", Optional.ofNullable(code).orElse(-1).toString());
            tag.put("result", "fail");
            MetricUtils.count(getHost(api).concat("[result]"), tag, 1);
        } catch (Exception e) {
            log.error("metricRequestErrorCount error, exception:", e);
        }
    }
}
