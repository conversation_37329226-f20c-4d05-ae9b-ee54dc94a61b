package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.qnh;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhSkuSpuMappingInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.CustomProductKeyDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.request.ChannelSkuMappingByCustomProductKeysQueryRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.request.ChannelSkuMappingBySkuIdsQueryRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.response.ChannelSkuMappingQueryResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.service.ChannelProductMappingQueryThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.QnhIdMappingByItemCodeQueryCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.QnhIdMappingQueryCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.QnhIdMappingResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerQnhIdMappingThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QnhSkuSpuConvertService {

    @Resource
    private EmpowerQnhIdMappingThriftService.Iface empowerQnhIdMappingThriftService;

    @Resource
    private ChannelProductMappingQueryThriftService productMappingQueryThriftService;

    public List<QnhSkuSpuMappingInfo> queryFnInfoByQnhSkuList(long tenantId, List<String> qnhSkuList) {
        if (CollectionUtils.isEmpty(qnhSkuList)) {
            return Collections.emptyList();
        }
        List<String> skuList = qnhSkuList.stream().filter(new Predicate<String>() {
            @Override
            public boolean test(String s) {
                return StringUtils.isNotEmpty(s);
            }
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuList)){
            return Collections.emptyList();
        }
        QnhIdMappingByItemCodeQueryCommand command = new QnhIdMappingByItemCodeQueryCommand();
        command.setMerchantId(tenantId);
        command.setQnhItemCodes(skuList);
        QnhIdMappingResult qnhIdMappingResult = null;
        try {
            qnhIdMappingResult = empowerQnhIdMappingThriftService.querySkuQnhIdMappingByItemCodes(command);
            log.info("queryFnInfoByQnhSkuList command:{},result:{}", command, qnhIdMappingResult);
        } catch (Exception e) {
            log.error("queryFnInfoByQnhSkuList error command:{}", command, e);
            throw new ChannelBizException("获取百川数据失败", e);
        }
        if (qnhIdMappingResult == null || qnhIdMappingResult.getStatus().getCode() != NumberUtils.INTEGER_ZERO) {
            throw new ChannelBizException("获取百川数据失败");
        }
        if (CollectionUtils.isEmpty(qnhIdMappingResult.getQnhIdMappingList())) {
            return Collections.emptyList();
        }
        List<QnhSkuSpuMappingInfo> mappingInfoList = new ArrayList<>();
        qnhIdMappingResult.getQnhIdMappingList().forEach(info -> {

            QnhSkuSpuMappingInfo mappingInfo = new QnhSkuSpuMappingInfo();
            mappingInfo.setQnhSkuId(info.getQnhCode());
            mappingInfo.setSkuId(info.getSkuId());
            mappingInfo.setSpuId(info.getSpuId());
            mappingInfoList.add(mappingInfo);
        });
        return mappingInfoList;
    }

    public List<QnhSkuSpuMappingInfo> queryCustomInfoByFnSkuList(long tenantId, long storeId, Integer channelId, List<String> fnSkuList) {
        if (CollectionUtils.isEmpty(fnSkuList)) {
            return Collections.emptyList();
        }
        List<String> skuList = fnSkuList.stream().filter(new Predicate<String>() {
            @Override
            public boolean test(String s) {
                return StringUtils.isNotEmpty(s);
            }
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuList)){
            return Collections.emptyList();
        }
        ChannelSkuMappingBySkuIdsQueryRequest skuIdsQueryRequest = new ChannelSkuMappingBySkuIdsQueryRequest();
        skuIdsQueryRequest.setTenantId(tenantId);
        skuIdsQueryRequest.setStoreId(storeId);
        skuIdsQueryRequest.setChannelId(channelId);
        skuIdsQueryRequest.setSkuIds(skuList);
        skuIdsQueryRequest.setHasInvalid(true);
        ChannelSkuMappingQueryResponse response = null;
        try {
            response = productMappingQueryThriftService.queryChannelSkuMappingBySkuIds(skuIdsQueryRequest);
            log.info("queryCustomInfoByFnSkuList skuIdsQueryRequest:{},response:{}", skuIdsQueryRequest, response);
        } catch (Exception e) {
            log.error("queryCustomInfoByFnSkuList error skuIdsQueryRequest:{}", skuIdsQueryRequest, e);
            throw new ChannelBizException("获取渠道数据失败", e);
        }
        if (response == null || response.getCode() != NumberUtils.INTEGER_ZERO.intValue()) {
            throw new ChannelBizException("获取渠道数据失败");
        }
        if (CollectionUtils.isEmpty(response.getChannelSkuMappingDTOList())) {
            return Collections.emptyList();
        }
        List<QnhSkuSpuMappingInfo> mappingInfoList = new ArrayList<>();
        response.getChannelSkuMappingDTOList().forEach(mapping -> {
            QnhSkuSpuMappingInfo mappingInfo = new QnhSkuSpuMappingInfo();
            mappingInfo.setCustomSkuId(mapping.getCustomSkuId());
            mappingInfo.setCustomSpuId(mapping.getCustomSpuId());
            mappingInfo.setSkuId(mapping.getSkuId());
            mappingInfo.setSpuId(mapping.getSpuId());
            mappingInfoList.add(mappingInfo);
        });
        return mappingInfoList;
    }

    public List<QnhSkuSpuMappingInfo> queryFnInfoByCustomProductKey(long tenantId, long storeId, Integer channelId, List<CustomProductKeyDTO> customProductKeyDTOList) {
        if (CollectionUtils.isEmpty(customProductKeyDTOList)) {
            return Collections.emptyList();
        }
        ChannelSkuMappingByCustomProductKeysQueryRequest request = new ChannelSkuMappingByCustomProductKeysQueryRequest();
        request.setTenantId(tenantId);
        request.setChannelId(channelId);
        request.setStoreId(storeId);
        request.setCustomProductKeyDTOList(customProductKeyDTOList);
        ChannelSkuMappingQueryResponse response = null;
        try {
            response = productMappingQueryThriftService.queryChannelSkuMappingByCustomProductKeys(request);
            log.info("queryChannelSkuMappingByCustomProductKeys request:{},response:{}", request, response);
        } catch (Exception e) {
            log.error("queryChannelSkuMappingByCustomProductKeys error request:{}", request, e);
            throw new ChannelBizException("获取牵牛花渠道商品数据失败", e);
        }
        if (response == null || response.getCode() != NumberUtils.INTEGER_ZERO.intValue()) {
            throw new ChannelBizException("获取牵牛花渠道商品数据失败");
        }
        if (CollectionUtils.isEmpty(response.getChannelSkuMappingDTOList())) {
            return Collections.emptyList();
        }
        List<QnhSkuSpuMappingInfo> mappingInfoList = new ArrayList<>();

        response.getChannelSkuMappingDTOList().forEach(mapping -> {
            QnhSkuSpuMappingInfo mappingInfo = new QnhSkuSpuMappingInfo();
            mappingInfo.setCustomSpuId(mapping.getCustomSpuId());
            mappingInfo.setCustomSkuId(mapping.getCustomSkuId());
            mappingInfo.setSpuId(mapping.getSpuId());
            mappingInfo.setSkuId(mapping.getSkuId());
            mappingInfoList.add(mappingInfo);
        });
        return mappingInfoList;
    }

    public List<QnhSkuSpuMappingInfo> queryQnhInfoByFnSkuList(long tenantId,List<String> fnSkuList) {
        if (CollectionUtils.isEmpty(fnSkuList)) {
            return Collections.emptyList();
        }
        List<String> skuList = fnSkuList.stream().filter(new Predicate<String>() {
            @Override
            public boolean test(String s) {
                return StringUtils.isNotEmpty(s);
            }
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuList)){
            return Collections.emptyList();
        }
        QnhIdMappingQueryCommand command = new QnhIdMappingQueryCommand();
        command.setFnSkuIds(skuList);
        command.setMerchantId(tenantId);
        QnhIdMappingResult qnhIdMappingResult = null;
        try {
            qnhIdMappingResult = empowerQnhIdMappingThriftService.queryQnhIdMapping(command);
            log.info("queryQnhInfoByFnSkuList request:{},response:{}", command, qnhIdMappingResult);
        } catch (TException e) {
            log.error("queryQnhInfoByFnSkuList error request:{}", command, e);
            throw new ChannelBizException("获取牵牛花数据失败", e);
        }
        if (qnhIdMappingResult == null || qnhIdMappingResult.getStatus().getCode() != NumberUtils.INTEGER_ZERO) {
            throw new ChannelBizException("获取牵牛花数据失败");
        }
        if (CollectionUtils.isEmpty(qnhIdMappingResult.getQnhIdMappingList())) {
            return Collections.emptyList();
        }
        List<QnhSkuSpuMappingInfo> mappingInfoList = new ArrayList<>();
        qnhIdMappingResult.getQnhIdMappingList().forEach(info -> {

            QnhSkuSpuMappingInfo mappingInfo = new QnhSkuSpuMappingInfo();
            mappingInfo.setQnhSkuId(info.getQnhCode());
            mappingInfo.setSkuId(info.getSkuId());
            mappingInfo.setSpuId(info.getSpuId());
            mappingInfo.setQnhUpc(info.getQnhUpc());
            mappingInfo.setQnhId(info.getQnhId());
            mappingInfoList.add(mappingInfo);
        });
        return mappingInfoList;
    }

}
