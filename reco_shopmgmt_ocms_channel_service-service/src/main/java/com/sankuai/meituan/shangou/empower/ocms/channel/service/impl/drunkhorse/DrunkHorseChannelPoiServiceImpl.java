package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelPoiServiceImpl;

/**
 * <AUTHOR>
 * @since 2021/10/14
 */
@Service("mtDrunkHorseChannelPoiService")
public class DrunkHorseChannelPoiServiceImpl extends MtChannelPoiServiceImpl implements ChannelPoiService {
    @Autowired
    @Qualifier("mtDrunkHorseChannelGateService")
    private MtChannelGateService mtChannelGateService;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.shoplist}")
    private String shoplist;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.shopget}")
    private String shopget;

    @Value("${mt_drunkhorse.url.base}")
    private String mtUrlBase;

    @Override
    public MtChannelGateService getMtChannelGateService() {
        return mtChannelGateService;
    }

    @Override
    public String getShoplist() {
        return shoplist;
    }

    @Override
    public String getShopget() {
        return shopget;
    }

    @Override
    public String getMtUrlBase() {
        return mtUrlBase;
    }
}
