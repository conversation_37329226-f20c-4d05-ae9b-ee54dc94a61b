package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelGateService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 歪马专用美团请求公共服务接口
 *
 * <AUTHOR>
 * @create 2023-05-18
 **/
@Service("mtDrunkHorseBrandChannelGateService")
public class DrunkHorseBrandChannelGateService extends MtBrandChannelGateService {
    @Value("${mt_drunkhorse.url.base}")
    private String baseUrl;

    @Override
    public String getBaseUrl() {
        return baseUrl;
    }
}