package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.meituan.shangou.saas.dto.model.PartRefundProductInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.drunkhorse.DrunkHorsePartRefundGoodsDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RefundSku;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 美团实体转换统一接口类
 *
 * <AUTHOR>
 * @create 2021-10-11 下午8:09
 **/

@Mapper(componentModel = "spring", uses = {MappingConverterUtils.class}, imports = {
        SpuFoodDataInfo.class,ChannelSpuInfoDTO.class,
        ConverterUtils.class,SkuPriceInfo.class,SkuStockInfo.class,ChannelStoreCategory.class,
        ChannelSkuCreateDTO.class,SkuFoodDataInfo.class, MoneyUtils.class,
        ChannelStatusConvertUtil.class, StringUtils.class,DateUtils.class,
        BigDecimal.class, Objects.class, DrunkHorseConverterUtil.class, OrderStatusConverter.class,
        Map.Entry.class, SpuPriceInfo.class, ActivityUtils.class})
public interface DrunkHorseConverterService {

    @Mappings({
            @Mapping(source = "order_id", target = "channelOrderId"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(channelOrderDetail.getTotal()))", target = "actualPayAmt"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(channelOrderDetail.getOriginal_price()))", target = "originalAmt"),
            @Mapping(expression = "java(DrunkHorseConverterUtil.orderCreateTime(channelOrderDetail))", target = "createTime"),
//            @Mapping(expression = "java(ConverterUtils.codeToBool(channelOrderDetail.getHas_invoiced()))", target = "isNeedInvoice"),
            @Mapping(source = "city_id", target = "cityId"),
            @Mapping(constant = "2", target = "payType"),
            @Mapping(constant = "0", target = "packageAmt"), // 目前TSP要求业务方写死0，不能使用package_bag_money
            @Mapping(source = "pay_status", target = "payStatus"),
            @Mapping(expression = "java(OrderStatusConverter.mtOrderStatusMapping(channelOrderDetail.getStatus()))", target = "status"),
            @Mapping(expression = "java(OrderStatusConverter.mtOrderStatusMapping(channelOrderDetail.getStatus()))", target = "channelOrderStatus"),
            @Mapping(source = "operator", target = "operator"),
            @Mapping(source = "is_pre_sale_order", target = "isBooking"),
            @Mapping(source = "day_seq", target = "orderSerialNumber"),
            @Mapping(source = "caution", target = "comment"),
            @Mapping(source = "wm_poi_name", target = "channelStoreName"),
            @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getOrder_pay_time()))", target = "payTime"),
            @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getOrder_confirm_time()))", target = "confirmTime"),
            @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getOrder_cancel_time()))", target = "cancelTime"),
            @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getOrder_completed_time()))", target = "completedTime"),
            @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getLogistics_fetch_time()))", target = "logisticFetchTime"),
            @Mapping(expression = "java(ConverterUtils.secondsToMillis(channelOrderDetail.getLogistics_completed_time()))", target = "logisticCompletedTime"),
            @Mapping(target = "dispatcherName", source = "logistics_dispatcher_name"),
            @Mapping(target = "dispatcherPhone", source = "logistics_dispatcher_mobile"),
            @Mapping(source = "user_id", target = "userId"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(channelOrderDetail.getTotal()))", target = "bizReceiveAmt"),
            @Mapping(target = "downFlag", expression = "java(channelOrderDetail.getIncmp_code() == -1 ? 1 : 0)"),
            @Mapping(target = "degradeModules", source = "incmp_modules"),
            @Mapping(target = "freight", expression = "java(MoneyUtils.yuanToFen(channelOrderDetail.getDiscount_shipping_fee()))"),
            @Mapping(target = "orderUserType", expression = "java(DrunkHorseConverterUtil.orderUserType(channelOrderDetail.getIs_vip()))"),
            @Mapping(target = "positioningAddress", source = "positioning_address")
    })
    ChannelOrderDetailDTO channelOrderDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
            @Mapping(source = "sku_id", target = "skuId"),
            @Mapping(source = "food_name", target = "skuName"),
            @Mapping(source = "spec", target = "specification"),
            @Mapping(source = "app_food_code", target = "customSpu"),
            @Mapping(source = "quantity", target = "quantity"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderSkuDetail.getActual_price()))", target = "salePrice"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderSkuDetail.getOriginal_price()))", target = "originalPrice"),
//            @Mapping(source = "box_num", target = "packageCount"),
            @Mapping(constant = "0", target = "packagePrice"),
            @Mapping(constant = "0", target = "packageFee"),
            @Mapping(source = "unit", target = "unit"),
            @Mapping(source = "item_id", target = "itemId"),
            @Mapping(source = "food_property", target = "skuProperty"),
            @Mapping(source = "picture", target = "skuPicUrls"),
            @Mapping(source = "weight",target = "weight"),
            @Mapping(source = "is_consignment", target = "consignmentProduct"),
            @Mapping(source = "consignment_agent_id", target = "consignmentAgentId")

    })
    OrderProductDetailDTO orderSkuDetailMapping(OrderSkuDetail orderSkuDetail);
    List<OrderProductDetailDTO> orderSkuDetailListMapping(List<OrderSkuDetail> orderSkuDetailList);

    @Mappings({
            @Mapping(source = "act_detail_id", target = "activityId"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderActivitiesInfo.getReduce_fee()))", target = "actDiscount"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderActivitiesInfo.getMt_charge()))", target = "channelCharge"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderActivitiesInfo.getPoi_charge()))", target = "bizCharge"),
            @Mapping(source = "remark", target = "remark"),
            @Mapping(source = "item_id", target = "itemId"),
            @Mapping(target = "giftInfo", expression = "java(convertChannelGiftInfo(orderActivitiesInfo.getAct_extend_msg()))"),
            @Mapping(source = "sub_type", target = "subType")
            })
    OrderDiscountDetailDTO orderActivitieInfoMapping(OrderActivitiesInfo orderActivitiesInfo);
    List<OrderDiscountDetailDTO> orderActivitieInfoListMapping(List<OrderActivitiesInfo> orderActivitiesInfoList);

    @Mappings({
            @Mapping(source = "gifts_sku_id", target = "skuId"),
            @Mapping(source = "gifts_app_food_code", target = "customSpu"),
            @Mapping(source = "gifts_name", target = "skuName"),
            @Mapping(source = "gift_num", target = "quantity"),
            @Mapping(target = "itemType", constant = "1"),
            @Mapping(target = "salePrice", constant = "0")
    })
    OrderProductDetailDTO orderGiftInfoMapping(ActExtendMsg actExtendMsg);
    List<OrderProductDetailDTO> orderGiftInfoListMapping(List<ActExtendMsg> actExtendMsgList);

    @Mappings({
            @Mapping(source = "sku_id", target = "mainSkuId"),
            @Mapping(source = "gifts_sku_id", target = "skuId"),
            @Mapping(source = "gifts_name", target = "name"),
            @Mapping(source = "gift_num", target = "quantity"),
//            @Mapping(source = "gifts_app_food_code", target = "spu")
    })
    ChannelGiftInfo convertChannelGiftInfo(ActExtendMsg actExtendMsg);
    List<ChannelGiftInfo> convertChannelGiftInfos(List<ActExtendMsg> actExtendMsgList);

    @Mappings({
            @Mapping(source = "recipient_address", target = "userAddress"),
            @Mapping(source = "recipient_phone", target = "userPhone"),
            @Mapping(source = "recipient_name", target = "userName"),
            @Mapping(expression = "java((channelOrderDetail.getEstimate_arrival_time() > 0 ? channelOrderDetail.getEstimate_arrival_time() : channelOrderDetail.getDelivery_time()) * 1000)", target = "arrivalTime"),
            @Mapping(expression = "java((channelOrderDetail.getEstimate_arrival_time() > 0 ? channelOrderDetail.getEstimate_arrival_time() : channelOrderDetail.getDelivery_time()) * 1000)", target = "arrivalEndTime"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.drunkHorseDeliveryMethodMapping())", target = "deliveryMethod"),
            @Mapping(expression = "java(!StringUtils.contains(channelOrderDetail.getRecipient_phone(), \"*\"))", target = "userPhoneIsValid"),
            @Mapping(source = "latitude", target = "latitude"),
            @Mapping(source = "longitude", target = "longitude"),
            @Mapping(expression = "java(DrunkHorseConverterUtil.isSelfDelivery())", target = "isSelfDelivery"),
            @Mapping(expression = "java(DrunkHorseConverterUtil.convertDeliveryType())", target = "originalDeliveryType")
    })
    OrderDeliveryDetailDTO deliveryDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
        @Mapping(source = "invoice_title", target = "invoiceTitle"),
        @Mapping(source = "taxpayer_id", target = "taxNo")
    })
    OrderInvoiceDetailDTO invoiceDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "food_data",expression = "java(SkuFoodDataInfo.toFoodDatas(data.getSkuId()))"),
            @Mapping(target = "sell_status",expression = "java(ChannelSkuCreateDTO.mapIs_sold_out(data.getSkuStatus()))"),
    })
    ChannelSkuSellStatusDTO updateSkuSellStatus(SkuSellStatusInfoDTO data);

    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "sku_id",source = "skuId"),
            @Mapping(target = "app_food_code",source = "customSkuId"),
            @Mapping(target = "name", source = "skuName"),
            @Mapping(target = "spec", source = "spec"),
            @Mapping(target = "category_name", source = "categoryName"),
    })
    UpdateCustomSkuId updateCustomSkuId(UpdateCustomSkuIdDTO data);

    @Mappings({
            @Mapping(target = "order_id", source = "orderId"),
            @Mapping(target = "reason", source = "reason"),
            @Mapping(target = "refund_id", source = "afterSaleId"),
    })
    ChannelOrderCancelRelDTO agreeRefund(AgreeRefundRequest request);


    @Mappings({
            @Mapping(target = "order_id", source = "orderId"),
            @Mapping(target = "reason", source = "reason"),
            @Mapping(target = "refund_id", source = "afterSaleId"),
    })
    ChannelOrderCancelRelDTO rejectRefund(RejectRefundRequest request);


    @Mappings({
        @Mapping(target = "skuName", source = "food_name"),
        @Mapping(target = "sku", source = "sku_id"),
        @Mapping(target = "customSpuId", source = "app_food_code"),
        @Mapping(target = "upc", source = "upc"),
        @Mapping(target = "count", source = "count"),
        @Mapping(target = "skuRefundAmount", expression = "java(channelPartRefundSkuInfo.getCount()==0?MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getRefund_price()):MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getRefund_price() * channelPartRefundSkuInfo.getCount()))"),//商品退款金额是单价，需要转成退款总价
        @Mapping(target = "foodPrice", expression = "java(MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getFood_price()))"),
        @Mapping(target = "boxPrice", expression = "java(MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getBox_price()))"),
        @Mapping(target = "boxNum", source = "box_num"),
        @Mapping(target = "refundWeight",source ="refunded_weight" )
    })
    RefundSku partRefundSkuInfo(ChannelPartRefundSkuInfo channelPartRefundSkuInfo);
    List<RefundSku> partRefundSkuInfoList(List<ChannelPartRefundSkuInfo> channelPartRefundSkuInfoList);

    @Mappings({
            @Mapping(target = "skuName", source = "name"),
            @Mapping(target = "customSpuId", source = "app_spu_code"),
            @Mapping(target = "customSkuId", source = "sku_id"),
            @Mapping(target = "count", source = "count"),
            @Mapping(target = "skuRefundAmount", expression = "java(MoneyUtils.yuanToFen(drunkHorsePartRefundGoodsDTO.getCount() == 0 ? drunkHorsePartRefundGoodsDTO.getRefund_price() : drunkHorsePartRefundGoodsDTO.getRefund_price() * drunkHorsePartRefundGoodsDTO.getCount()))"),//商品退款金额是单价，需要转成退款总价
            @Mapping(target = "foodPrice", expression = "java(MoneyUtils.yuanToFen(drunkHorsePartRefundGoodsDTO.getPrice()))"),
            @Mapping(target = "spec", source = "spec"),
            @Mapping(target = "itemId", source = "item_id")

    })
    PartRefundProductInfo partRefundSpuEventInfo(DrunkHorsePartRefundGoodsDTO drunkHorsePartRefundGoodsDTO);
    List<PartRefundProductInfo> partRefundSpuEventInfoList(List<DrunkHorsePartRefundGoodsDTO> drunkHorsePartRefundGoodsDTOS);

    @Mappings({
            @Mapping(target = "channelPromotionType", expression = "java(String.valueOf(wmAppOrderActDetail.getType()))"),
            @Mapping(target = "promotionRemark", source = "remark"),
            @Mapping(target = "channelCost", expression = "java(MoneyUtils.yuanToFen(wmAppOrderActDetail.getMtCharge()))"),
            @Mapping(target = "tenantCost", expression = "java(MoneyUtils.yuanToFen(wmAppOrderActDetail.getPoiCharge()))"),
            @Mapping(target = "activityId", source = "act_id"),
            @Mapping(target = "promotionCount", source = "count"),
            @Mapping(target = "actConfActivityId", expression = "java(ActivityUtils.translateActId(wmAppOrderActDetail.getAct_id(), wmAppOrderActDetail.getAct_conf_type()))"),
            @Mapping(target = "actConfType", source = "act_conf_type")
    })
    GoodsSharedActivityItem convertGoodsSharedActivityItem(WmAppOrderActDetail wmAppOrderActDetail);
    List<GoodsSharedActivityItem> convertGoodsSharedActivityItems(List<WmAppOrderActDetail> wmAppOrderActDetails);


    @Mappings({
            @Mapping(target = "customSpu", source = "app_food_code"),
            @Mapping(target = "customSkuId", source = "sku_id"),
            @Mapping(target = "skuCount", source = "count"),
            @Mapping(target = "totalDiscount", expression = "java(MoneyUtils.yuanToFen(skuBenefitDetail.getTotalReducePrice()))"),
            @Mapping(target = "originPrice", expression = "java(MoneyUtils.yuanToFen(skuBenefitDetail.getOriginPrice()))"),
            @Mapping(target = "activityPrice", expression = "java(MoneyUtils.yuanToFen(skuBenefitDetail.getActivityPrice()))"),
            @Mapping(target = "channelCost", expression = "java(MoneyUtils.yuanToFen(skuBenefitDetail.getTotalMtCharge()))"),
            @Mapping(target = "tenantCost", expression = "java(MoneyUtils.yuanToFen(skuBenefitDetail.getTotalPoiCharge()))"),
            @Mapping(target = "agentCost", constant = "0"),
            @Mapping(target = "logisticsCost", constant = "0"),
            @Mapping(target = "channelJiFenCost", constant = "0"),
            @Mapping(target = "channelPromotionType", constant = "0"),
            @Mapping(target = "goodActivityDetail", expression = "java(convertGoodsSharedActivityItems(skuBenefitDetail.getWmAppOrderActDetails()))")

    })
    GoodsActivityDetailDTO convertGoodsActivityDetail(SkuBenefitDetail skuBenefitDetail);
    List<GoodsActivityDetailDTO> convertGoodsActivityDetails(List<SkuBenefitDetail> skuBenefitDetails);



//    @Mappings({
//            @Mapping(target = "skuName", source = "food_name"),
//            @Mapping(target = "customSpu", source = "app_food_code"),
//            @Mapping(target = "skuId", source = "sku_id"),
//            @Mapping(target = "count", source = "count"),
//            @Mapping(target = "skuRefundAmount", expression = "java(MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getCount() == 0 ? channelPartRefundSkuInfo.getRefund_price() : channelPartRefundSkuInfo.getRefund_price() * channelPartRefundSkuInfo.getCount()))"),//商品退款金额是单价，需要转成退款总价
//            @Mapping(target = "foodPrice", expression = "java(MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getFood_price()))"),
//            @Mapping(target = "boxPrice", expression = "java(MoneyUtils.yuanToFen(channelPartRefundSkuInfo.getBox_price()))"),
//            @Mapping(target = "boxNum", source = "box_num"),
//            @Mapping(target = "refundWeight",source = "refunded_weight"),
//            @Mapping(target = "spec", source = "spec")
//    })
//    RefundProductDTO convertRefundProductDTO(ChannelPartRefundSkuInfo channelPartRefundSkuInfo);
//
//    List<RefundProductDTO> convertPartRefundProductDTOs(List<ChannelPartRefundSkuInfo> channelPartRefundSkuInfos);

    @Mappings({
            @Mapping(target = "skuName", source = "name"),
            @Mapping(target = "customSpu", source = "app_spu_code"),
            @Mapping(target = "skuId", source = "sku_id"),
            @Mapping(target = "count", source = "count"),
            @Mapping(target = "skuRefundAmount", expression = "java(MoneyUtils.yuanToFen(drunkHorsePartRefundGoodsDTO.getCount() == 0 ? drunkHorsePartRefundGoodsDTO.getRefund_price() : drunkHorsePartRefundGoodsDTO.getRefund_price() * drunkHorsePartRefundGoodsDTO.getCount()))"),//商品退款金额是单价，需要转成退款总价
            @Mapping(target = "foodPrice", expression = "java(MoneyUtils.yuanToFen(drunkHorsePartRefundGoodsDTO.getPrice()))"),
            @Mapping(target = "spec", source = "spec"),
            @Mapping(target = "itemId", source = "item_id")

    })
    RefundProductDTO partRefundSpuInfo(DrunkHorsePartRefundGoodsDTO drunkHorsePartRefundGoodsDTO);
    List<RefundProductDTO> partRefundSpuInfoList(List<DrunkHorsePartRefundGoodsDTO> drunkHorsePartRefundGoodsDTOS);


    @Mappings({
            @Mapping(constant = "700", target = "channelType"),
            @Mapping(source = "order_id", target = "channelOrderId"),
            @Mapping(source = "refund_id", target = "afterSaleId"),
            @Mapping(source = "refund_status", target = "refundStatus"),
            @Mapping(source = "refund_flow", target = "refundFlow"),
            @Mapping(source = "apply_op_type", target = "applyOpType"),
            @Mapping(expression = "java(DrunkHorseConverterUtil.convertTime(drunkHorseOrderAfsApplyDTO.getCtime()))", target = "refundApplyTime"),
            @Mapping(expression = "java(DrunkHorseConverterUtil.convertTime(drunkHorseOrderAfsApplyDTO.getRefund_audit_time()))", target = "refundAuditTime"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(drunkHorseOrderAfsApplyDTO.getMoney()))", target = "refundPrice"),
            @Mapping(expression = "java(OrderStatusConverter.dhAfterSaleTypeMapping(drunkHorseOrderAfsApplyDTO.getRefund_type()))", target = "refundType"),
            @Mapping(expression = "java(partRefundSpuInfoList(drunkHorseOrderAfsApplyDTO.getSpu_data()))", target = "afsProductList"),
            @Mapping(source = "apply_reason", target = "applyReason"),
            @Mapping(source = "res_reason", target = "resReason"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.dhAfterSaleStatusMapping(drunkHorseOrderAfsApplyDTO.getRefund_status()))", target = "afterSaleStatus"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.dhAfterSaleRecordStatusMapping(drunkHorseOrderAfsApplyDTO.getRefund_status()))", target = "afterSaleRecordStatus"),
            @Mapping(expression = "java(DrunkHorseConverterUtil.convertAuditType(drunkHorseOrderAfsApplyDTO))", target = "auditType")
    })
    OrderAfsApplyDTO convertAfsApplyDTO(MtOrderAfsApplyDTO drunkHorseOrderAfsApplyDTO);

    List<OrderAfsApplyDTO> convertAfsApplyDTOs(List<MtOrderAfsApplyDTO> afsApplyDTOs);



    @Mappings({
            @Mapping(target = "skuName", source = "name"),
            @Mapping(target = "skuId", source = "sku_id"),
            @Mapping(target = "count", source = "count"),
            @Mapping(target = "appFoodCode",source = "app_spu_code"),
            @Mapping(target = "itemId",source = "item_id"),
            @Mapping(target = "customSpu",source = "app_spu_code"),
            @Mapping(target = "refundPrice", expression = "java(MoneyUtils.yuanToFen(drunkHorsePartRefundGoodsDTO.getRefund_price() ))")

    })
    PartRefundGoodDetailDTO convertPartRefundGoodDetailDTO(DrunkHorsePartRefundGoodsDTO drunkHorsePartRefundGoodsDTO);

    List<PartRefundGoodDetailDTO> convertPartRefundGoodDetailDTOs(List<DrunkHorsePartRefundGoodsDTO> drunkHorsePartRefundGoodsDTOS);

    @Mappings({
            @Mapping(target = "app_poi_code", source = "storeId"),
            @Mapping(target = "app_food_code_origin", source = "orgCustomSkuId"),
            @Mapping(target = "app_food_code", source = "customSkuId"),
            @Mapping(target = "sku_id_origin", source = "orgChannelSkuId"),
            @Mapping(target = "sku_id", source = "channelSkuId")
    })
    ChangeCustomSkuId changeCustomSkuId(ChangeCustomSkuIdDTO data);




}
