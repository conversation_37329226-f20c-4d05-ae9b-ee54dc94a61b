package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.util.CollectionUtils;
import com.doudian.open.gson.reflect.TypeToken;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.meituan.mtrace.Tracer;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryByViewOrderIdRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.meituan.shangou.saas.order.platform.enums.BoolTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderProductLabelEnum;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ChannelConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantChannelConfigResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.KeyConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.LionConfigKeyConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.MtAfsFieldCompareMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.MtAfsFieldCompareMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.MtBrandPrepareCompleteMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.message.MtBrandPrepareCompleteMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.FarmPaotuiCancelDeliveryInnerRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.FarmPaotuiCancelInnerResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.RiderPointBatchSyncRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.MTOrderDaySeqRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.MTOrderDaySeqResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.MTOrderSeqResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.FarmPaoTuiInnerThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.MtChannelOrderFormParser;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.common.MedicineTenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mock.MockOrderChannelUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.tenant.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelPoiThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.FavoritesStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.OrderDegradeModuleEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.TenantCancelOrderReasonEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.SUCCESS_LIST;
import static com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils.getIntegerValue;
import static com.sankuai.meituan.shangou.empower.ocms.channel.utils.PhoneNumberUtils.isValidPrivacyNumber;

@Service("mtBrandChannelOrderService")
public class MtBrandChannelOrderServiceImpl implements ChannelOrderService, MtChannelOrderFormParser {
	private static final int CALCULATE_SUCCESS = 1;

	private static final int COMPENSATION_ORDER_ID_MAX_COUNT = 20;


	@Resource
	private MtConverterService mtConverterService;

	@Resource
	public MtBrandChannelGateService mtBrandChannelGateService;

	@Resource
	private CopChannelStoreService copChannelStoreService;

	@Resource
	private ChannelPoiThriftServiceProxy channelPoiThriftServiceProxy;

	@Resource
	private FarmPaoTuiInnerThriftService paoTuiInnerThriftService;

	@Resource
	private BizOrderThriftService bizOrderThriftServiceClient;

	@Resource
	private ConfigThriftService configThriftService;

	@Resource
	private CommonLogger log;

	@Resource
	private TenantRemoteService tenantRemoteService;

	@Resource
	private MockOrderChannelUtil mockOrderChannelUtil;

	@Resource
	private MedicineTenantService medicineTenantService;

	@Resource
	private HealthChannelOrderSensitiveHelper healthChannelOrderSensitiveHelper;

	@Resource
	private MtBrandChannelOrderSensitiveHelper mtBrandChannelOrderSensitiveHelper;

	@Resource
	private MtAfsFieldCompareMessageProducer mtAfsFieldCompareMessageProducer;

	@Value("${mt.url.base}" + "${mt.url.orderDetail}")
	private String orderDetailUrl;
	@Value("${mt.url.base}" + "${mt.url.preparationMealComplete}")
	private String preparationMealCompleteUrl;
	@Value("${mt.url.base}" + "${mt.url.orderStatus}")
	private String orderStatusUrl;
	@Value("${mt.url.base}" + "${mt.url.orderConfirm}")
	private String orderConfirmsUrl;
	@Value("${mt.url.base}" + "${mt.url.orderCancel}")
	private String orderCancelUrl;
	@Value("${mt.url.settlementBaseV1}" + "${mt.url.billList}")
	private String settlementBillListUrl;

	@Value("${mt.url.base}" + "${mt.url.billList}")
	private String settlementBaseBillListUrl;

	@Value("${mt.url.base}" + "${mt.url.orderAfsApplyList}")
	private String orderAfsApplyListUrl;


	@Value("${mt.url.base}" + "${mt.url.logisticsStatus}")
	private String logisticStatusUrl;

	@Value("${mt.url.settlementBaseV1}" + "${mt.url.orderDelivering}")
	private String orderDeliveringUrl;

	@Value("${mt.url.settlementBaseV1}" + "${mt.url.orderArrived}")
	private String orderArrivedUrl;

	@Value("${mt.url.settlementBaseV1}" + "${mt.url.orderLogisticsSync}")
	private String orderLogisticsSyncUrl;

	@Value("${mt.url.base}" + "${mt.url.orderLogisticsChangePoiSelf}")
	private String orderLogisticsChangePoiSelfUrl;

	@Value("${mt.url.base}" + "${mt.url.orderDelivering}")
	private String innerNetOrderDeliveringUrl;

	@Value("${mt.url.base}" + "${mt.url.orderArrived}")
	private String innerNetOrderArrivedUrl;

	@Value("${mt.url.base}" + "${mt.url.orderLogisticsSync}")
	private String innerNetOrderLogisticsSyncUrl;

	@Value("${mt.url.settlementBaseV1}" + "${mt.url.deliveringRiderPointSync}")
	private String deliveringRiderPointSyncUrl;

	@Value("${mt.url.settlementBaseV1}" + "${mt.url.deliveryCompletedRiderPointSync}")
	private String deliveryCompletedRiderPointSyncUrl;

	@Value("${mt.url.base}" + "${mt.url.orderGetPartRefundFoods}")
	private String orderGetPartRefundFoods;

	@Value("${mt.url.base}" + "${mt.url.batchFetchAbnormalOrder}")
	private String batchFetchAbnormalOrder;

	@Value("${mt.url.base}" + "${mt.url.weightRefundCalculate}")
	private String weightRefundCalculate;

	@Value("${mt.url.base}" + "${mt.url.reviewAfterSales}")
	private String reviewAfterSalesUrl;

	@Value("${mt.url.base}" + "${mt.url.getUnitPartRefundFoods}")
	private String getUnitPartRefundFoods;

	@Value("${mt.url.base}" + "${mt.url.getUnitPartRefundFoodsV2}")
	private String getUnitPartRefundFoodsV2;

	@Value("${mt.url.base}" + "${mt.url.getOrderIdByDaySeq}")
	private String getOrderIdByDaySeq;

	@Value("${mt.url.base}" + "${mt.url.getOrderDaySeq}")
	private String getOrderDaySeq;

	@Value("${mt.url.base}" + "${mt.url.getAfterSaleOrders}")
	private String getAfterSaleOrders;

	@Value("${mt.url.base}" + "${mt.url.getOrderIdByPage}")
	private String getOrderIdByPage;

	@Value("${mt.url.base}" + "${mt.url.getAmountPartRefundFoods}")
	private String getAmountPartRefundFoods;

	@Value("${mt.url.base}" + "${mt.url.getReturnDuringDelivery}")
	private String getReturnDuringDelivery;

	@Value("${mt.url.base}" + "${mt.url.returnGoodsAudit}")
	private String returnGoodsAudit;

	@Value("${mt.url.base}" + "${mt.url.getCompensateInfo}")
	private String getCompensateInfo;

	@Value("${mt.url.base}" + "${mt.url.getOrderOtherRefundDetail}")
	private String getOrderOtherRefundDetail;

	@Value("${mt.url.base}" + "${mt.url.getOrderPrivacyPhone}")
	private String getOrderPrivacyPhone;

	@Value("${mt.url.base}" + "${mt.url.getOrderRefundGoodsFee}")
	private String getOrderRefundGoodsFee;


	@Resource
	private MtBrandPrepareCompleteMessageProducer prepareCompleteMessageProducer;

	@Override
	public ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request) {
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
        addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		Map<String, Object> getResult = mtBrandChannelGateService.sendGet(orderConfirmsUrl, null, baseRequest, bizParam);
		log.info("MtChannelOrderServiceImpl.poiConfirmOrder, request:{}, getResult:{}", request, getResult);
		if (MapUtils.isEmpty(getResult)) {
			return ResultGenerator.genFailResult("调用渠道商家确认订单失败");
		}
		ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
		if (Objects.isNull(channelResponseDTO)) {
			return ResultGenerator.genFailResult("调用渠道商家确认订单返回数据解析失败");
		}
		if (channelResponseDTO.isSuccess()) {
			return ResultGenerator.genSuccessResult();
		}
		return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
	}

	@Override
	public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
		GetChannelOrderDetailResult orderDetailResult = new GetChannelOrderDetailResult();
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setAppId(request.getAppId());
		addStoreId2BaseRequest(request.getSotreId(), baseRequest);
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		Map<String, Object> resultMap;
		if (Tracer.isTest() && MccConfigUtil.orderPressTestEnable()){
			resultMap = mockOrderChannelUtil.mockMtOrderDetail(request);
		}else {
			resultMap = mtBrandChannelGateService.sendGet(orderDetailUrl, null, baseRequest, bizParam);
		}
		log.info("MtChannelOrderServiceImpl.getChannelOrderDetail, request:{}, resultMap:{}", request, resultMap);
		if (CollectionUtils.isEmpty(resultMap)) {
			return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单详情失败"));
		}
		return orderDetailAnalysis(request.getTenantId(), request.getChannelId(), request.getAppId(), request.getSotreId(), resultMap, orderDetailResult);
	}


	@Override
	public GetChannelReturnDuringDeliveryResult getChannelReturnDuringDelivery(GetChannelReturnDuringDeliveryRequest request) {
		GetChannelReturnDuringDeliveryResult result = new GetChannelReturnDuringDeliveryResult();
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setAppId(request.getAppId());
		addStoreId2BaseRequest(request.getSotreId(), baseRequest);
		if (StringUtils.isBlank(request.getDispatchOrderId())) {
			return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "配送单ID不可为空"));
		}
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		bizParam.put(ProjectConstant.DISPATCH_ORDER_ID, request.getDispatchOrderId());
		bizParam.put("app_poi_code", request.getChannelPoiId());
		try {
			Map<String, Object> resultMap = mtBrandChannelGateService.sendPost(getReturnDuringDelivery, null, baseRequest, bizParam);
			log.info("MtChannelOrderServiceImpl.getChannelReturnDuringDeliveryResult, request:{}, resultMap:{}",
					request, resultMap);
			if (MapUtils.isEmpty(resultMap)
					|| Objects.isNull(resultMap.get(SUCCESS_LIST))) {
				return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取返货单详情失败"));
			}

			JSONArray jsonArray = (JSONArray) resultMap.get(SUCCESS_LIST);
			if (org.apache.commons.collections4.CollectionUtils.isEmpty(jsonArray)) {
				log.info("MtChannelOrderServiceImpl.getChannelReturnDuringDeliveryResult 未查询到返货单, request:{}, resultMap:{}",
						request, resultMap);
				return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "未查询到返货单"));
			}
			List<ChannelReturnDuringDeliveryInfo> successList = jsonArray.toJavaList(ChannelReturnDuringDeliveryInfo.class);
			return result.setStatus(ResultGenerator.genSuccessResult())
					.setChannelReturnDuringDeliveryDTO(parseChannelReturnDuringDeliveryDTO(successList.get(0)));
		} catch (Exception e) {
			log.error("MtChannelOrderServiceImpl.getChannelReturnDuringDeliveryResult 调用渠道获取返货单详情异常, request:{}", request, e);
			return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取返货单详情失败"));
		}
	}

	@Override
	public ResultStatus returnGoodsAudit(ChannelReturnGoodsAuditRequest request) {

		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map<String, Object> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getViewOrderId());
		bizParam.put(ProjectConstant.DISPATCH_ORDER_ID, request.getDispatchOrderId());
		bizParam.put("result", request.getAuditResult());
		if(request.getAuditResult() == 0){
			bizParam.put("reject_reason_code", request.getRejectReasonCode());
			bizParam.put("reject_other_reason", request.getRejectOtherReason());
		}
		try {
			Map<String, Object> resultMap = mtBrandChannelGateService.sendPost(returnGoodsAudit, null, baseRequest, bizParam);
			log.info("MtChannelOrderServiceImpl.returnGoodsAudit, request:{}, resultMap:{}",
					request, resultMap);
			if (MapUtils.isEmpty(resultMap)
					|| Objects.isNull(resultMap.get(ProjectConstant.RESULT_CODE)) || (Integer) resultMap.get(ProjectConstant.RESULT_CODE) !=1) {
				return new ResultStatus(ResultCode.FAIL.getCode(), ResultCode.FAIL.getMsg(), null);
			}
			return new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null);


		} catch (Exception e) {
			log.error("MtChannelOrderServiceImpl.returnGoodsAudit 调用渠道获取返货单详情异常, request:{}", request, e);
			return new ResultStatus(ResultCode.FAIL.getCode(), ResultCode.FAIL.getMsg(), null);
		}

	}

	@Override
	public RefundAddressChangeFeeDTO getRefundAddressChangeFeeInfo(GetRefundAddressChangeFeeRequest request) {

		try {
			if(Objects.isNull(request)){
				log.info("MtChannelOrderServiceImpl.getRefundAddressChangeFeeInfo request is null");
				return null;
			}
			BaseRequest baseRequest = new BaseRequest()
					.setChannelId(request.getChannelId())
					.setTenantId(request.getTenantId())
					.setStoreIdList(request.getStoreIdList())
					.setAppId(request.getAppId());

			Map params = Maps.newHashMap();
			params.put("order_id", request.getOrderId());
			params.put("type", GetRefundAddressTypeEnum.REFUND_UPDATE_ADDRESS_FEE.getValue());
//			log.info("MtChannelOrderServiceImpl.getRefundAddressChangeFeeInfo request params: {}", params);
			Map addressChangeFeeResultMap = mtBrandChannelGateService.sendGet(getOrderOtherRefundDetail, null, baseRequest, params);
			log.info("MtChannelOrderServiceImpl.getRefundAddressChangeFeeInfo addressChangeFeeResultMap: {}", addressChangeFeeResultMap);
			if(Objects.isNull(addressChangeFeeResultMap)){
				return null;
			}
			JSONArray jsonArray = (JSONArray) addressChangeFeeResultMap.get(SUCCESS_LIST);
			if (CollectionUtils.isEmpty(jsonArray)) {
				log.info("MtChannelOrderServiceImpl.getRefundAddressChangeFeeInfo 未查询到地址变更退费, request:{}, addressChangeFeeResultMap:{}",
						request, addressChangeFeeResultMap);
				return null;
			}
			List<RefundAddressChangeFeeInfoDTO> successList = jsonArray.toJavaList(RefundAddressChangeFeeInfoDTO.class);

			RefundAddressChangeFeeInfoDTO infoDTO = successList.get(0);
			if(Objects.isNull(infoDTO)){
				log.info("转换 infoDTO is null");
				return null;
			}
			//校验是否是同一笔订单
			if(Long.parseLong(request.getOrderId()) != infoDTO.getOrder_id()){
				log.info("地址变更退费查询viewOrderId 与 返回viewOrderId 不一致");
				return null;
			}
			RefundAddressChangeFeeDTO refundAddressChangeFeeDTO = new RefundAddressChangeFeeDTO();
			refundAddressChangeFeeDTO.setOrderId(infoDTO.getOrder_id());
			refundAddressChangeFeeDTO.setMoney(infoDTO.getMoney());
			refundAddressChangeFeeDTO.setType(infoDTO.getType());
			refundAddressChangeFeeDTO.setRefundTime(infoDTO.getRefund_time());
			return refundAddressChangeFeeDTO;
		} catch (Exception e) {
			log.error("MtChannelOrderServiceImpl.getRefundAddressChangeFeeInfo is error viewOrderId: {}, e= ", request.getOrderId(), e);
		}
		return null;
	}

	private ChannelReturnDuringDeliveryDTO parseChannelReturnDuringDeliveryDTO(ChannelReturnDuringDeliveryInfo channelReturnDuringDeliveryInfo) {
		ChannelReturnDuringDeliveryDTO channelReturnDuringDeliveryDTO = new ChannelReturnDuringDeliveryDTO();
		if (Objects.isNull(channelReturnDuringDeliveryInfo)) {
			return channelReturnDuringDeliveryDTO;
		}
		channelReturnDuringDeliveryDTO.setDispatchOrderId(String.valueOf(channelReturnDuringDeliveryInfo.getDispatch_order_id()));
		if (Objects.nonNull(channelReturnDuringDeliveryInfo.getRefund_id())) {
			channelReturnDuringDeliveryDTO.setRefundId(String.valueOf(channelReturnDuringDeliveryInfo.getRefund_id()));
		}
		channelReturnDuringDeliveryDTO.setOpRole(channelReturnDuringDeliveryInfo.getOp_role());
		channelReturnDuringDeliveryDTO.setStatus(channelReturnDuringDeliveryInfo.getStatus());
		channelReturnDuringDeliveryDTO.setDeliveryTime(String.valueOf(channelReturnDuringDeliveryInfo.getDelivery_time()));
		channelReturnDuringDeliveryDTO.setTime(String.valueOf(channelReturnDuringDeliveryInfo.getTime()));
		channelReturnDuringDeliveryDTO.setRejectReason(channelReturnDuringDeliveryInfo.getReject_reason());
		channelReturnDuringDeliveryDTO.setPoiCheckResult(Optional.ofNullable(channelReturnDuringDeliveryInfo.getPoi_check_result()).orElse(0));
		return channelReturnDuringDeliveryDTO;
	}

	@Override
	public MTOrderDaySeqResponse getOrderIdByDaySeq(MTOrderDaySeqRequest request) {
		Map<String,Object> bizParam = Maps.newHashMap();
		bizParam.put("date_time", request.getDateTime());
		bizParam.put("day_seq_start", request.getDaySeqStart());
		bizParam.put("day_seq_end", request.getDaySeqEnd());
		bizParam.put("app_poi_code", request.getChannelPoiId());

		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setAppId(request.getAppId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		String url = getOrderIdByDaySeq;
		log.info("MtChannelOrderServiceImpl.getOrderIdByDaySeq  调用美团接口 request:{},  param:{}", request, bizParam);
		Map result = mtBrandChannelGateService.sendPost(url, null, baseRequest, bizParam);
		log.info("MtChannelOrderServiceImpl.getOrderIdByDaySeq  调用美团接口返回:{}", result);
		MTOrderDaySeqResponse resp = new MTOrderDaySeqResponse();
		if (MapUtils.isEmpty(result) || result.get(ProjectConstant.DATA) == null) {
			resp.setCode(ResultCode.FAIL.getCode());
			resp.setMsg("调用渠道查询异常订单列表失败");
			return resp;
		}
		if ("ng".equals(result.get(ProjectConstant.DATA))) {
			resp.setCode(ResultCode.FAIL.getCode());
			resp.setMsg(JSON.parseObject(String.valueOf(result.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG));
			return resp;
		}
		JSONObject data = JSON.parseObject(String.valueOf(result.get(ProjectConstant.DATA)));
		if(StringUtils.equals(data.getString(ProjectConstant.RESULT),ProjectConstant.OK)){
			List<String> orderIdList = data.getJSONArray(ProjectConstant.ORDER_IDS).toJavaList(String.class);
			resp.setCode(ResultCode.SUCCESS.getCode());
			resp.setOrderIds(orderIdList);
			return resp;
		}
		resp.setCode(ResultCode.FAIL.getCode());
		resp.setMsg("未知异常");
		return resp;
	}
	@Override
	public MTOrderSeqResponse getOrderSeq(MTOrderDaySeqRequest request){
		Map<String,Object> bizParam = Maps.newHashMap();
		bizParam.put("app_poi_code", request.getChannelPoiId());
		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setAppId(request.getAppId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		String url = getOrderDaySeq;
		log.info("MtChannelOrderServiceImpl.getOrderDaySeq  调用美团接口 request:{},  param:{}", request, bizParam);
		Map result = mtBrandChannelGateService.sendPost(url, null, baseRequest, bizParam);
		log.info("MtChannelOrderServiceImpl.getOrderDaySeq  调用美团接口返回:{}", result);
		MTOrderSeqResponse resp = new MTOrderSeqResponse();
		if (MapUtils.isEmpty(result) || result.get(ProjectConstant.DATA) == null) {
			resp.setCode(ResultCode.FAIL.getCode());
			resp.setMsg("调用渠道查询异常订单列表失败");
			return resp;
		}
		if ("ng".equals(result.get(ProjectConstant.DATA))) {
			resp.setCode(ResultCode.FAIL.getCode());
			resp.setMsg(JSON.parseObject(String.valueOf(result.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG));
			return resp;
		}
		JSONObject data = JSON.parseObject(String.valueOf(result.get(ProjectConstant.DATA)));
		if(StringUtils.equals(data.getString(ProjectConstant.RESULT),ProjectConstant.OK)){

			resp.setCode(ResultCode.SUCCESS.getCode());
			resp.setSeq(data.getInteger("day_seq"));
			return resp;
		}
		resp.setCode(ResultCode.FAIL.getCode());
		resp.setMsg("未知异常");
		return resp;
	}

	@Override
	public QueryAfsOrderListResult queryAfsOrderList(QueryChannelAfsOrderListRequest request) {
		Map<String,Object> bizParam = Maps.newHashMap();
		bizParam.put("start_time", request.getStartTime());
		bizParam.put("end_time", request.getEndTime());
		bizParam.put("page_size", request.getPageSize());
		if(request.getExtraInfo()!=null&&request.getExtraInfo().get("vernier")!=null){
			bizParam.put("vernier", request.getExtraInfo().get("vernier"));
		}
		bizParam.put("app_poi_code", request.getChannelPoiId());

		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setAppId(request.getAppId());
		addStoreId2BaseRequest(request.getShopId(), baseRequest);
		String url = getAfterSaleOrders;
		log.info("MtChannelOrderServiceImpl.queryAfsOrderList  调用美团接口 request:{},  param:{}", request, bizParam);
		Map result = mtBrandChannelGateService.sendGet(url, null, baseRequest, bizParam);
		log.info("MtChannelOrderServiceImpl.queryAfsOrderList  调用美团接口返回:{}", result);
		QueryAfsOrderListResult resp = new QueryAfsOrderListResult();
		if (MapUtils.isEmpty(result) || result.get(ProjectConstant.DATA) == null) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询订单列表失败"));
		}
		if ("ng".equals(result.get(ProjectConstant.DATA))) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(result.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
		}
		resp.setStatus(ResultGenerator.genSuccessResult())
				.setChannelAfsOrderIdList(transformAfsOrder((JSONArray) result.get(ProjectConstant.DATA))).setExtraInfo(transformExtraInfo((JSONObject) result.get("extra_info")));
		return resp;

	}

	private Map<String, String> transformExtraInfo(JSONObject extra_info) {
		Map<String,String> result = Maps.newHashMap();
		if(StringUtils.isNotEmpty(extra_info.getString("vernier"))){
			result.put("vernier",extra_info.getString("vernier"));
		}
		if(StringUtils.isNotEmpty(extra_info.getString("has_next"))){
			result.put("has_next",extra_info.getString("has_next"));
		}
		log.info("transformExtraInfo-result:{}",result);
		return result;
	}

	private List<AfsOrderInfo> transformAfsOrder(JSONArray jsonArray) {
		if(CollectionUtils.isEmpty(jsonArray)){
			return Lists.newArrayList();
		}
		List<AfsOrderInfo> result = jsonArray.toJavaList(JSONObject.class).stream().map(entry -> {
			AfsOrderInfo afsOrderInfo = new AfsOrderInfo();
			afsOrderInfo.setChannelAfsOrderId(entry.getString(ProjectConstant.REFUND_ID));
			afsOrderInfo.setChannelOrderId(entry.getString(ProjectConstant.ORDER_ID));
			return afsOrderInfo;
		}).collect(Collectors.toList());
		return result;
	}

	private static final Gson gson = new Gson();

	public static <T> List<T> deserializeList(String json, Class<T> clazz) {
		Type listType = TypeToken.getParameterized(List.class, clazz).getType();
		return gson.fromJson(json, listType);
	}

	public static <T> Set<T> deserializeSet(String json, Class<T> clazz) {
		Type listType = TypeToken.getParameterized(Set.class, clazz).getType();
		return gson.fromJson(json, listType);
	}

	@Override
	public ChannelOrderDetailDTO parseOrderForm(long tenantId, int channelId, Map<String, String> orderDetailMap) {
		if (orderDetailMap == null) {
			return null;
		}
		ChannelOrderDetail channelOrderDetail = new ChannelOrderDetail();
		for (Map.Entry<String, String> entry : orderDetailMap.entrySet()) {
			try {
				String property = entry.getKey();
				String propertyValue = entry.getValue();
				Field field = ChannelOrderDetailFieldCache.getField(property);
				if (Objects.isNull(field) || StringUtils.isBlank(propertyValue)) {
					continue;
				}
				Type genericType = field.getGenericType();
				if (genericType instanceof ParameterizedType) {
					ParameterizedType parameterizedType = (ParameterizedType) genericType;
					Type[] typeArguments = parameterizedType.getActualTypeArguments();
					Type rawType = parameterizedType.getRawType();
					boolean isList = rawType instanceof Class && List.class.isAssignableFrom((Class<?>) rawType);
					boolean isSet = rawType instanceof Class && Set.class.isAssignableFrom((Class<?>) rawType);
					if (typeArguments.length > 0 && isList) {
						Class<?> typeArgument = (Class<?>) typeArguments[0];
						Object deserializedValue = deserializeList(propertyValue, typeArgument);
						field.set(channelOrderDetail, deserializedValue);
						continue;
					} else if (typeArguments.length > 0 && isSet) {
						Class<?> typeArgument = (Class<?>) typeArguments[0];
						Object deserializedValue = deserializeSet(propertyValue, typeArgument);
						field.set(channelOrderDetail, deserializedValue);
						continue;
					} else {
						log.error("数据解析失败,{},{}", property, propertyValue);
						log.error("美团渠道消息解析,仅支持List和基本类型以及String,{}", orderDetailMap);
						return null;
					}
				}
				BeanUtils.setProperty(channelOrderDetail, property, propertyValue);
			}catch (Exception e) {
				log.error("解析渠道消息订单详情错误,orderDetailMap:{}", orderDetailMap, e);
				return null;
			}
		}
//		log.info("解析渠道信息,{}", channelOrderDetail);
		return orderDetail2ChannelOrderDetailDto(tenantId, channelId, channelOrderDetail);
	}


	@Override
	public ChannelOrderDetailDTO parseOrderForm(long tenantId, int channelId, String orderDetailForm) {
		ChannelOrderDetail channelOrderDetail = null;
		channelOrderDetail = getChannelOrderDetailFromStr(orderDetailForm);
		if (Objects.isNull(channelOrderDetail)) {
			return null;
		}
		return orderDetail2ChannelOrderDetailDto(tenantId, channelId, channelOrderDetail);

	}
	
	public ChannelOrderDetail getChannelOrderDetailFromStr(String orderDetailForm) {
		ChannelOrderDetail channelOrderDetail;
		if (StringUtils.startsWith(orderDetailForm, "{") && StringUtils.endsWith(orderDetailForm, "}")) {
			//json 格式
			channelOrderDetail = JSON.parseObject(orderDetailForm, ChannelOrderDetail.class);
		} else {
			//表单格式
			channelOrderDetail = new ChannelOrderDetail();
			String pairs[] = StringUtils.split(orderDetailForm, "&");
			for (String pair : pairs) {
				String kv[] = StringUtils.split(pair, "=");
				if (kv.length == 2) {
					String key = kv[0];
					String value = kv[1];
					try {
						if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
							BeanUtils.setProperty(channelOrderDetail, UrlUtil.urlDecodeSafe(key), UrlUtil.urlDecodeSafe(value));
						}
					} catch (Exception e) {
						log.error("解析form表单错误,target值无法赋值,key:{}, value:{}", key, value, e);
					}
				}
			}
		}
		return channelOrderDetail;
	}


	private ChannelOrderDetailDTO orderDetail2ChannelOrderDetailDto(long tenantId, int channelId, ChannelOrderDetail channelOrderDetail){
		try {
			return doOrderDetail2ChannelOrderDetailDto(tenantId, channelId, channelOrderDetail);
		}catch (Exception e){
			log.error("订单映射异常", e);
			throw new IllegalArgumentException("订单映射异常");
		}
	}

	public ChannelOrderDetailDTO doOrderDetail2ChannelOrderDetailDto(long tenantId, int channelId,
		ChannelOrderDetail channelOrderDetail) {
		//返回数据映射
		ChannelOrderDetailDTO channelOrderDetailDTO = mtConverterService.channelOrderDetailMapping(channelOrderDetail);
		if (StringUtils.isNotBlank(channelOrderDetail.getDay_seq_num())) {
			String daySeqNum = channelOrderDetail.getDay_seq_num();
			channelOrderDetailDTO.setOrderSerialNumberStr(daySeqNum);
			if (StringUtils.isBlank(channelOrderDetail.getDay_seq())) { // 如果老字段被下掉了，用新字段解析一下
				try {
					String daySeq = daySeqNum.replace("-", "");
					if (NumberUtils.isDigits(daySeq)) {
						channelOrderDetailDTO.setOrderSerialNumber(Long.parseLong(daySeq));
					} else {
						log.warn("订单{}的流水号格式有误: {}", channelOrderDetail.getOrder_id(), daySeqNum);
					}
				} catch (Exception e) {
					log.warn("订单{}的流水号格式有误: {}", channelOrderDetail.getOrder_id(), daySeqNum, e);
				}
			}
		}
		if(CollectionUtils.isNotEmpty(channelOrderDetail.getOrder_tag_list())){
			channelOrderDetailDTO.setIsFastOrder(channelOrderDetail.getOrder_tag_list().contains(MtOrderTagEnum.FAST_DELIVERY.getCode()) ? BoolTypeEnum.YES.getValue() : BoolTypeEnum.NO.getValue());
			channelOrderDetailDTO.setMtFamousTavern(channelOrderDetail.getOrder_tag_list().contains(MtOrderTagEnum.MT_FAMOUS_TAVERN.getCode()));
			channelOrderDetailDTO.setMtFacaiWine(channelOrderDetail.getOrder_tag_list().contains(MtOrderTagEnum.FA_CAI_WINE.getCode()));
			channelOrderDetailDTO.setFourWheelDelivery(channelOrderDetail.getOrder_tag_list().contains(MtOrderTagEnum.FOUR_WHEEL_DELIVERY.getCode()));
		}
		// 重新获取备注
		convertComment(tenantId, channelOrderDetailDTO, channelOrderDetail.getStock_out_info());

		channelOrderDetailDTO.setStockOutInfo(channelOrderDetail.getStock_out_info());
		channelOrderDetailDTO.setBaichuanStatus(BaichuanOrderStatusConverter.mtOrderStatusMapping(channelOrderDetail.getStatus()));
		//计算总优惠金额
		channelOrderDetailDTO.setTotalDiscount(channelOrderDetailDTO.getOriginalAmt() - channelOrderDetailDTO.getActualPayAmt());
		//门店信息
		channelOrderDetailDTO.setStoreId(copChannelStoreService.selectChannelStoreId(tenantId, channelId, channelOrderDetail.getApp_poi_code()));
		//商家应收金额
		PoiReceiveDetail poiReceiveDetail = JSON.parseObject(channelOrderDetail.getPoi_receive_detail(), PoiReceiveDetail.class);
		if (Objects.nonNull(poiReceiveDetail)) {
			channelOrderDetailDTO.setBizReceiveAmt(poiReceiveDetail.getWmPoiReceiveCent().intValue());
			channelOrderDetailDTO.setPlatformFee(poiReceiveDetail.getFoodShareFeeChargeByPoi().intValue());

			//商家对账信息相关的额外信息
			if(StringUtils.isNotEmpty(poiReceiveDetail.getReconciliationExtras())){
				ReconciliationExtras reconciliationExtras = JSON.parseObject(poiReceiveDetail.getReconciliationExtras(), ReconciliationExtras.class);
				if(Objects.nonNull(reconciliationExtras)){
					channelOrderDetailDTO.setPerformanceServiceFee(MoneyUtils.yuanToFen(reconciliationExtras.getPerformanceServiceFee()));
				}
			}
		}

		//配送信息
		if (0 == channelOrderDetail.getDelivery_time()) {
			channelOrderDetailDTO.setIsBooking(false);
		}
		channelOrderDetailDTO.setDeliveryDetail(mtConverterService.deliveryDetailMapping(channelOrderDetail));
		// 美团隐私解密失败也降级
		if (Objects.nonNull(channelOrderDetail.getMtDecryptDownFlag()) && channelOrderDetail.getMtDecryptDownFlag() == 1) {
			channelOrderDetailDTO.setDownFlag(1);
			channelOrderDetailDTO.getDegradeModules().add(OrderDegradeModuleEnum.MT_USER_SENSITIVE_DATA.getValue());
		}
		//开票信息
		channelOrderDetailDTO.setInvoiceDetail(mtConverterService.invoiceDetailMapping(channelOrderDetail));
		//商品信息
		List<OrderSkuDetail> orderSkuDetailList = JSON.parseArray(channelOrderDetail.getDetail(), OrderSkuDetail.class);
		if (BusinessIdTracer.isDrunkHorseTenant(tenantId)){
			/**
			 * 现象：歪马外卖渠道订单部分商品规格为空
			 * 原因：外卖订单历史逻辑：当一个销售商品有多个销售规格的时候就是这个mode=2，spec就不会返回。导致了现在歪马订单列表和订单详情以及小票展示规格字段，多销售规格的商品规格字段为空。
			 * 所以规格取值逻辑应该为：一个销售商品有可选规格时，不取这个商品的规格字段spec，改去用户选的销售属性。具体实现是先取规格，规格为空改取销售属性。
			 * **/
			DrunkHorseConverterUtil.compensateSailProperties(orderSkuDetailList);
		}
		List<OrderProductDetailDTO> orderProductDetailAllList = new ArrayList<>(mtConverterService.orderSkuDetailListMapping(orderSkuDetailList));
		// 处理下详情label信息主要用于mock和白名单
		dealOrderProductDetailAllListLabel(orderProductDetailAllList, tenantId);
		channelOrderDetailDTO.setSkuDetails(orderProductDetailAllList);

		List<OrderProductDetailDTO> orderProductDetailDTOSFromAct = Lists.newArrayList();
		//活动信息
		if (isJsonArray(channelOrderDetail.getExtras())){
			List<OrderActivitiesInfo> orderActivitiesInfoList = JSON.parseArray(channelOrderDetail.getExtras(), OrderActivitiesInfo.class);
			//过滤空对象
			//美团下发的对象中有如下格式：[{"mt_charge":0.0,"poi_charge":1.0,"reduce_fee":1.0,"remark":"满2.0元减1.0元","type":2},{}]
			//需要过滤掉最后的{}
			orderActivitiesInfoList = orderActivitiesInfoList.stream()
					.filter(item -> Objects.nonNull(item.getReduce_fee()) && Objects.nonNull(item.getMt_charge()) && Objects.nonNull(item.getPoi_charge()))
					.collect(Collectors.toList());
			extractPromotionFromActivities(tenantId, channelOrderDetailDTO, orderActivitiesInfoList, channelOrderDetail.getLogistics_code());
			List<OrderDiscountDetailDTO> orderDiscountDetailDTOS = mtConverterService.orderActivitieInfoListMapping(orderActivitiesInfoList);
//			log.info("orderDetail2ChannelOrderDetailDto 活动信息:{}", orderDiscountDetailDTOS);

			// 非歪马租户需将线上赠品从促销信息移除并放入订单明细；若关闭灰度开关则只将ERP租户的促销信息移除并放入订单明细
			if (CollectionUtils.isNotEmpty(orderDiscountDetailDTOS)) {
                if ((MccConfigUtil.getGiftProductToOrderDetailSwitch(tenantId) && !BusinessIdTracer.isDrunkHorseTenant(tenantId))
                        || (!MccConfigUtil.getGiftProductToOrderDetailSwitch(tenantId) && tenantRemoteService.isErpTenant(tenantId))
                        || (BusinessIdTracer.isDrunkHorseTenant(tenantId) && MccConfigUtil.dhAddGiftToItemSwitch(tenantId))
				) {
					orderDiscountDetailDTOS.forEach(item -> item.setGiftInfo(null));
					orderProductDetailDTOSFromAct = extractGiftFromPromotion(orderActivitiesInfoList, tenantId);
                }
            }

//			log.info("orderDetail2ChannelOrderDetailDto 处理后的活动信息:{}", orderDiscountDetailDTOS);
			channelOrderDetailDTO.setActivities(orderDiscountDetailDTOS);
		}
		// 活动分摊信息
		String goodsActivities = channelOrderDetail.getSku_benefit_detail();
		if (StringUtils.isNotBlank(goodsActivities)) {
			List<SkuBenefitDetail> skuBenefitDetails = JSON.parseArray(goodsActivities, SkuBenefitDetail.class);
			channelOrderDetailDTO.setSkuSharedActivities(mtConverterService.convertGoodsActivityDetails(skuBenefitDetails));
			if (MccConfigUtil.promotionSplitV2Switch(tenantId)) {
				extractSkuPromotionFromSkuBenefitV2(channelOrderDetailDTO.getSkuDetails(), skuBenefitDetails, tenantId);
			} else if (MccConfigUtil.promotionSplitV3Switch(tenantId)) {
				try {
					extractSkuPromotionFromSkuBenefitV3(channelOrderDetailDTO.getSkuDetails(), skuBenefitDetails, tenantId);
				}catch (Exception e){
					log.error("优惠分摊出错, tenantId:{}", tenantId, e);
					extractSkuPromotionFromSkuBenefit(channelOrderDetailDTO.getSkuDetails(), skuBenefitDetails);
				}
			}else {
				extractSkuPromotionFromSkuBenefit(channelOrderDetailDTO.getSkuDetails(), skuBenefitDetails);
			}

			// 美团已经拆分到商品明细级别优惠：
			List<ActivityShareDetailDTO> activityShareDetailDTOList = splitSkuPromotionFromSkuBenefitCatchException(channelOrderDetailDTO.getSkuSharedActivities(), tenantId);
			if (CollectionUtils.isEmpty(channelOrderDetailDTO.getActivityShareDetailList())) {
				// 集合为空的情况下才直接set
				channelOrderDetailDTO.setActivityShareDetailList(activityShareDetailDTOList);
			} else {
				channelOrderDetailDTO.getActivityShareDetailList().addAll(activityShareDetailDTOList);
			}
		}

		// 美团未拆分的订单级别优惠，如运费：
		List<ActivityShareDetailDTO> activityShareDetailDTOList = splitSkuLogisticPromotionFromSkuBenefitCatchException(channelOrderDetailDTO, channelOrderDetailDTO.getActivities(), tenantId);
		if (CollectionUtils.isEmpty(channelOrderDetailDTO.getActivityShareDetailList())) {
			// 集合为空的情况下才直接set
			channelOrderDetailDTO.setActivityShareDetailList(activityShareDetailDTOList);
		} else {
			channelOrderDetailDTO.getActivityShareDetailList().addAll(activityShareDetailDTOList);
		}

		//赠品添加到商品明细, 需要在活动分摊完之后加入到商品列表中
		if (CollectionUtils.isNotEmpty(orderProductDetailDTOSFromAct)) {
			orderProductDetailAllList.addAll(orderProductDetailDTOSFromAct);
		}
//		log.info("orderDetail2ChannelOrderDetailDto 商品信息:{}", channelOrderDetailDTO.getSkuDetails());

		String userMemberInfoStr = channelOrderDetail.getUser_member_info();
		if (StringUtils.isNotBlank(userMemberInfoStr)) {
			log.info("orderDetail2ChannelOrderDetailDto 订单会员信息不为空 :{} ", channelOrderDetail);
			ChannelOrderDetail.UserMemberInfo userMemberInfo = JSON.parseObject(userMemberInfoStr, ChannelOrderDetail.UserMemberInfo.class);
			if (Objects.nonNull(userMemberInfo)) {
				channelOrderDetailDTO.setMemberCardCode(userMemberInfo.getCard_code());
			}
		}

		// 设置用户隐私号
		try {
			channelOrderDetailDTO.getDeliveryDetail().setUserPrivacyPhone(MtConverterUtil.getUserPrivacyPhone(channelOrderDetail));
			channelOrderDetailDTO.getDeliveryDetail().setBackUpUserPrivacyPhone(JSON.parseArray(channelOrderDetail.getBackup_recipient_phone(), String.class));

			//埋点
//			recordSkuIdAppFoodCodeNotEqual(tenantId, channelOrderDetailDTO.getStoreId(), channelOrderDetail, orderSkuDetailList);
			//埋点订单降级
			recordOrderDowngrade(tenantId, channelOrderDetailDTO.getStoreId(), channelOrderDetail);

		} catch (Exception ex) {
			log.error("设置用户隐私号异常", ex);
		}

		// 首先取门店基础服务的id、不满足条件再获取APP方门店id
		channelOrderDetailDTO.setOriginalPoiId(Objects.nonNull(channelOrderDetail.getWm_poi_id())
				&& channelOrderDetail.getWm_poi_id() > BigInteger.ZERO.longValue()
				? channelOrderDetail.getWm_poi_id().toString() : channelOrderDetail.getApp_poi_code());
		// 设置收藏状态
		channelOrderDetailDTO.setFavoritesStatus(Optional.ofNullable(channelOrderDetail.getIs_favorites())
				.map(favorites -> favorites ? FavoritesStatusEnum.ALREADY : FavoritesStatusEnum.NOT)
				.orElse(FavoritesStatusEnum.UNKNOWN));

		//运费返还:快速达 4011、及时达4012  光速达 4001配送方式，且当租户配置为返还运费时，履约服务费扣减配送费
        if (MtBrandConverterUtil.checkLogisticsCodeRebackFreight(channelOrderDetail.getLogistics_code())
                && MccConfigUtil.checkMtQikeRebackFreightTenantId(tenantId)) {
			log.info("运费返还，订单id:{} ,返还运费:{}", channelOrderDetailDTO.getChannelOrderId(), channelOrderDetailDTO.getFreight());
			channelOrderDetailDTO.setRebackFreight(channelOrderDetailDTO.getFreight());
//			channelOrderDetailDTO.setPerformanceServiceFee(channelOrderDetailDTO.getPerformanceServiceFee() - channelOrderDetailDTO.getFreight());
		}
		channelOrderDetailDTO.setChannelPayMode(KeyConstant.WECHAT_PAY_MODE);
		channelOrderDetailDTO.setAppPoiCode(channelOrderDetail.getApp_poi_code());
		channelOrderDetailDTO.setDeliveryPosition(channelOrderDetail.getDelivery_position());
		//美团名酒馆集合店联系方式处理
		buildGatherPhones(channelOrderDetailDTO.getDeliveryDetail(), channelOrderDetail.getGather_wm_poi_phones());
		return channelOrderDetailDTO;
	}

	private void dealOrderProductDetailAllListLabel(List<OrderProductDetailDTO> orderProductDetailAllList, Long tenantId){
		if(tenantId == null) {
			return;
		}
		try {
			// lion处理白名单
			if (!MccConfigUtil.getMtFreshPromiseLabelSaveTenantSwitch(tenantId)) {
				// 移除新鲜承诺标签
				for (OrderProductDetailDTO orderProductDetailDTO : orderProductDetailAllList) {
					if (CollectionUtils.isNotEmpty(orderProductDetailDTO.getChannelLabelList())) {
						orderProductDetailDTO.getChannelLabelList()
								.removeIf(label -> label.getType() == OrderProductLabelEnum.COMPENSATION_FOR_PROMISE.getValue());
					}
					// 清空保质期字段
					orderProductDetailDTO.setProductPickingDateRange(null);
				}
			} else {
				// mock处理
				//mockMtOrderProductDetailLabel(orderProductDetailAllList, tenantId);
			}
		} catch (Exception e) {
			log.error("dealOrderProductDetailAllList error", e);
		}
	}

	// mock新鲜承诺代码
	private void mockMtOrderProductDetailLabel(List<OrderProductDetailDTO> orderProductDetailAllList, Long tenantId){
		if(!MccConfigUtil.getMtFreshPromiseLabelSaveTenantMockSwitch(tenantId)) {
			return;
		}
		Calendar calendarStart = Calendar.getInstance();
		calendarStart.add(Calendar.DAY_OF_MONTH, -1);
		Calendar calendarEnd = Calendar.getInstance();
		calendarEnd.add(Calendar.DAY_OF_MONTH, 1);
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		orderProductDetailAllList.get(0).setProductPickingDateRange(new ProductPickingDateRangeDTO(){{
			setProductionStartDate(simpleDateFormat.format(calendarStart.getTime()));
			setProductionEndDate(simpleDateFormat.format(calendarEnd.getTime()));
		}});
		if(orderProductDetailAllList.get(0).getChannelLabelList() == null){
			orderProductDetailAllList.get(0).setChannelLabelList(new ArrayList<>());
		}
		orderProductDetailAllList.get(0).getChannelLabelList().add(new ProductLabelDTO(){{
			setType(OrderProductLabelEnum.COMPENSATION_FOR_PROMISE.getValue());
			setName(OrderProductLabelEnum.COMPENSATION_FOR_PROMISE.getDesc());
			setDesc(OrderProductLabelEnum.COMPENSATION_FOR_PROMISE.getDesc() + "的描述");
		}});
	}

	// mock代码
	void mockMtOrderPromotion(ChannelOrderDetailDTO channelOrderDetailDTO,long tenantId){
		MockMtOrderAndRefundDTO mockMtOrderAndRefundDTO = MccConfigUtil.mockMtOrderAndRefund(tenantId);
		if(mockMtOrderAndRefundDTO != null){
			if(mockMtOrderAndRefundDTO.getOrderPlatPackagePromotion() != null
			&& mockMtOrderAndRefundDTO.getOrderPlatPackagePromotion() > 0
			){
				channelOrderDetailDTO.setPlatPackagePromotion(mockMtOrderAndRefundDTO.getOrderPlatPackagePromotion());
			}
			if(mockMtOrderAndRefundDTO.getOrderPlatLogisticsPromotion() != null
			&& mockMtOrderAndRefundDTO.getOrderPlatLogisticsPromotion() > 0
			){
				channelOrderDetailDTO.setPlatLogisticsPromotion(mockMtOrderAndRefundDTO.getOrderPlatLogisticsPromotion());
			}
		}
	}

	// 缺货信息连接词
	private final static String STOCK_OUT_INFO_CONJUNCTIONS = "如遇缺货";
	/**
	 * 转换备注
	 * @param tenantId 租户id
	 * @param channelOrderDetailDTO 订单详情
	 * @param stockOutInfo 缺货信息
	 */
	private void convertComment(long tenantId, ChannelOrderDetailDTO channelOrderDetailDTO, String stockOutInfo){
		try {
			Map<String, String> commentConfigMap = MccConfigUtil.getSupportMtSimpleCommentConfig();
			if (CollectionUtils.isEmpty(commentConfigMap)) {
				return;
			}
			String blackTenants = commentConfigMap.get("blackTenants");
			// 在黑名单内
			if (StringUtils.isNotBlank(blackTenants) && MccConfigUtil.checkTenantInMtSimpleCommentConfig(tenantId, blackTenants)) {
				return;
			}
			String whiteTenants = commentConfigMap.get("whiteTenants");
			// 白名单为空 || 不在白名单内
			if (StringUtils.isEmpty(whiteTenants) || !MccConfigUtil.checkTenantInMtSimpleCommentConfig(tenantId, whiteTenants)) {
				return;
			}

			String withNoStockInfo = commentConfigMap.get("noStockInfoTenants");
			// 租户备注不拼接缺货信息
			if (StringUtils.isNotEmpty(withNoStockInfo) && MccConfigUtil.checkTenantInMtSimpleCommentConfig(tenantId, withNoStockInfo)) {
				channelOrderDetailDTO.setComment(
						Optional.ofNullable(channelOrderDetailDTO.getNewComment()).orElse(StringUtils.EMPTY));
				return;
			}

            // 缺货信息中，不存在 "如遇缺货"
            if (StringUtils.isNotBlank(stockOutInfo) && !stockOutInfo.contains(STOCK_OUT_INFO_CONJUNCTIONS)) {
                stockOutInfo = "【" + STOCK_OUT_INFO_CONJUNCTIONS + "】：" + stockOutInfo;
            }

            if (StringUtils.isEmpty(stockOutInfo)) {
                stockOutInfo = "";
            }
            // 拼接备注
            channelOrderDetailDTO.setComment(Optional.ofNullable(channelOrderDetailDTO.getNewComment()).orElse(StringUtils.EMPTY)
                            + stockOutInfo);
		}catch (Exception e){
			log.warn("转换备注异常！stockOutInfo:{}, channelOrderDetailDTO:{}", stockOutInfo,JSON.toJSONString(channelOrderDetailDTO), e);
		}
	}

	private List<OrderProductDetailDTO> extractGiftFromPromotion(List<OrderActivitiesInfo> orderActivitiesInfoList, Long tenantId) {
		List<OrderProductDetailDTO> giftProductDetail = Lists.newArrayList();
		if (CollectionUtils.isEmpty(orderActivitiesInfoList)){
			return giftProductDetail;
		}
		//赠品活动
		List<OrderActivitiesInfo> actExtendMsgList = orderActivitiesInfoList.stream().filter(v->MtActivityTypeEnum.isGiftPromotion(v.getType())).collect(Collectors.toList());
		log.info("orderDetail2ChannelOrderDetailDto 赠品信息:{}", actExtendMsgList);
		if (CollectionUtils.isEmpty(actExtendMsgList)){
			return giftProductDetail;
		}
		for (OrderActivitiesInfo giftOrderActivitiesInfo: actExtendMsgList){
			if(Objects.isNull(giftOrderActivitiesInfo.getAct_extend_msg()) || giftOrderActivitiesInfo.getAct_extend_msg().getGift_num() == 0){
				continue;
			}
			OrderProductDetailDTO orderProductDetailDTO = mtConverterService.orderGiftInfoMapping(giftOrderActivitiesInfo.getAct_extend_msg());
			int poiCharge = MoneyUtils.yuanToFen(giftOrderActivitiesInfo.getPoi_charge());
			orderProductDetailDTO.setPoiItemPromotion(0);
			orderProductDetailDTO.setOriginalPrice(0);

			JSONObject extJSON = new JSONObject();
			if (orderProductDetailDTO.isSetExtData()) {
				try{
					String extData = orderProductDetailDTO.getExtData();
					extJSON = JSON.parseObject(extData);
				} catch (Exception e) {
					log.error("解析ext异常，detail：{}", JSON.toJSONString(orderProductDetailDTO), e);
				}
			}
			// 添加线上赠品标（giftType  :  0 (平台赠品)），标志打在明细的扩展字段
			extJSON.put("giftType", 0);
			//需要将赠品的 mainSkuId 存入extData
			String mainSkuId = giftOrderActivitiesInfo.getAct_extend_msg().getSku_id();
			if(StringUtils.isNotBlank(mainSkuId)) {
				extJSON.put("mainSkuId", mainSkuId);
			}
			// 如果赠品的skuId和customSpu都为空，则为非在售赠品，需要进行非在售赠品打标处理
			if (StringUtils.isEmpty(orderProductDetailDTO.getSkuId()) && StringUtils.isEmpty(orderProductDetailDTO.getCustomSpu())) {
				// 如果开关为否，则不加入非在售赠品到订单详情中
				if(!tenantRemoteService.queryTenantAddNonSaleGiftToDetailSwitch(tenantId)) {
					continue;
				} else {
					extJSON.put("nonSaleGift", Boolean.TRUE);
				}
			}
			orderProductDetailDTO.setExtData(extJSON.toJSONString());
			giftProductDetail.add(orderProductDetailDTO);
		}

		return giftProductDetail;
	}



	@Data
	public static class ItemActDetail{
		private int count;
		private long actId;
		private int platPromotion;
		private int poiPromotion;
		List<WmAppOrderActDetail> wmAppOrderActDetails;
		private boolean hadBenefit;

		public static List<ItemActDetail> toItemActDetailList(List<WmAppOrderActDetail> itemActDetailList){
			if (CollectionUtils.isEmpty(itemActDetailList)){
				return Lists.newArrayList();
			}
			List<ItemActDetail> rs = Lists.newArrayList();
			Map<Long, List<WmAppOrderActDetail>> actid2Map = itemActDetailList.stream().collect(Collectors.groupingBy(WmAppOrderActDetail::getAct_id));
			for (Map.Entry<Long, List<WmAppOrderActDetail>> entry : actid2Map.entrySet()){
				ItemActDetail itemActDetail = new ItemActDetail();
				itemActDetail.setActId(entry.getKey());
				itemActDetail.setWmAppOrderActDetails(entry.getValue());
				int count = 0;
				int platPromotion =0;
				int poiPromotion =0;
				for (WmAppOrderActDetail wmAppOrderActDetail : entry.getValue()){
					count += wmAppOrderActDetail.getCount();
					platPromotion += MoneyUtils.yuanToFen(wmAppOrderActDetail.getMtCharge()) * wmAppOrderActDetail.getCount();
					poiPromotion += MoneyUtils.yuanToFen(wmAppOrderActDetail.getPoiCharge()) * wmAppOrderActDetail.getCount();
				}
				itemActDetail.setCount(count);
				itemActDetail.setPlatPromotion(platPromotion);
				itemActDetail.setPoiPromotion(poiPromotion);
				rs.add(itemActDetail);
			}
			return rs;
		}

		public static List<ItemActDetail> buildItemActDetails(List<WmAppOrderActDetail> itemActDetailList) {
			if (CollectionUtils.isEmpty(itemActDetailList)){
				return Lists.newArrayList();
			}
			List<ItemActDetail> rs = Lists.newArrayList();
			for (WmAppOrderActDetail actDetail : itemActDetailList){
				ItemActDetail itemActDetail = new ItemActDetail();
				itemActDetail.setActId(actDetail.getAct_id());
				itemActDetail.setCount(actDetail.getCount());
				itemActDetail.setPlatPromotion(MoneyUtils.yuanToFen(actDetail.getMtCharge()) * actDetail.getCount());
				itemActDetail.setPoiPromotion(MoneyUtils.yuanToFen(actDetail.getPoiCharge()) * actDetail.getCount());
				rs.add(itemActDetail);
			}
			return rs;
		}
	}

	@Slf4j
	public static class SkuBenefit{
		List<WmAppOrderActDetail> actDetailList;
		List<WmAppOrderActDetail> itemActDetailList;
		List<OrderProductDetailDTO> skuDetails;
		int totalPlatPromotion;
		int totalPoiPromotion;
		long tenantId;

		public SkuBenefit(long tenantId, SkuBenefitDetail skuBenefitDetail, List<OrderProductDetailDTO> skuDetails) {
			actDetailList = Optional.ofNullable(skuBenefitDetail).map(SkuBenefitDetail::getWmAppOrderActDetails).orElse(Lists.newArrayList());
			itemActDetailList = actDetailList.stream()
					.filter(act -> MtActivityTypeEnum.isItemPromotion(act.getType()))
					.collect(Collectors.toList());
			this.skuDetails = skuDetails;
			totalPlatPromotion = MoneyUtils.yuanToFen(skuBenefitDetail.getTotalMtCharge());
			totalPoiPromotion = MoneyUtils.yuanToFen(skuBenefitDetail.getTotalPoiCharge());
			this.tenantId = tenantId;
		}

		public void benefit(){
			if (CollectionUtils.isEmpty(actDetailList) || CollectionUtils.isEmpty(skuDetails)){
				return;
			}
			if(skuDetails.size() == 1){
				OrderProductDetailDTO orderProductDetailDTO = skuDetails.get(0);
				int totalPlatItemPromotion = 0;
				int totalPoiItemPromotion = 0;
				for (WmAppOrderActDetail wmAppOrderActDetail : itemActDetailList){
					totalPlatItemPromotion += MoneyUtils.yuanToFen(wmAppOrderActDetail.getMtCharge()) * wmAppOrderActDetail.getCount();
					totalPoiItemPromotion += MoneyUtils.yuanToFen(wmAppOrderActDetail.getPoiCharge()) * wmAppOrderActDetail.getCount();
				}
				orderProductDetailDTO.setPlatItemPromotion(totalPlatItemPromotion);
				orderProductDetailDTO.setPoiItemPromotion(totalPoiItemPromotion);
				orderProductDetailDTO.setPlatPromotion(totalPlatPromotion - totalPlatItemPromotion);
				orderProductDetailDTO.setPoiPromotion(totalPoiPromotion - totalPoiItemPromotion);
				return;
			}
			// 单品优惠是否已匹配赋值
			boolean isSetItemPromotion = false;
			//开关
			if(MccConfigUtil.getNewSetItemPromotionSwitch(tenantId)) {
				isSetItemPromotion = priorityMatchMoreItemPromotion(itemActDetailList, skuDetails);
			}
			log.info("benefit.isSetItemPromotion: {}", isSetItemPromotion);
			//单品分摊
			List<ItemActDetail> itemActDetails = ItemActDetail.toItemActDetailList(itemActDetailList);
			//最后兜底方案
			for (OrderProductDetailDTO productDetailDTO : skuDetails){
				//已经赋值单品优惠，无需再次赋值
				if(isSetItemPromotion) {
					continue;
				}
				//无单品优惠
				if(Objects.equals(productDetailDTO.getOriginalPrice(), productDetailDTO.getSalePrice())){
					continue;
				}
				ItemActDetail targetItemActDetail = null;
				for (ItemActDetail itemActDetail : itemActDetails){
					if (itemActDetail.isHadBenefit()) {
						continue;
					}
					if (itemActDetail.getCount() == productDetailDTO.getQuantity()) {
						targetItemActDetail = itemActDetail;
						itemActDetail.setHadBenefit(true);
						break;
					}
				}
				if (Objects.nonNull(targetItemActDetail)){
					productDetailDTO.setPoiItemPromotion(targetItemActDetail.getPoiPromotion());
					productDetailDTO.setPlatItemPromotion(targetItemActDetail.getPlatPromotion());
				}else {
					//错误
				}
			}
			//整单分摊
			int totalPoiOrderPromotion = totalPoiPromotion - itemActDetails.stream().map(ItemActDetail::getPoiPromotion).reduce(0, Integer::sum);
			int totalPlatOrderPromotion = totalPlatPromotion -  itemActDetails.stream().map(ItemActDetail::getPlatPromotion).reduce(0, Integer::sum);
			int lastPoiPromotion = totalPoiOrderPromotion;
			int lastPlatPromotion = totalPlatOrderPromotion;

			//数量分摊
			int totalQuantity = skuDetails.stream().mapToInt(OrderProductDetailDTO::getQuantity).sum();
			int totalShare = skuDetails.stream().map(v->v.getOriginalPrice() * v.getQuantity() - v.getPoiItemPromotion() - v.getPlatItemPromotion()).reduce(0, Integer::sum);

			for (int index = 0; index < skuDetails.size(); index++){
				OrderProductDetailDTO productDetailDTO = skuDetails.get(index);
				if (index == skuDetails.size() - 1){
					productDetailDTO.setPoiPromotion(lastPoiPromotion);
					productDetailDTO.setPlatPromotion(lastPlatPromotion);
				}else {
					BigDecimal percent;
					if (MccConfigUtil.splitOrderPromotionBySalePrice(tenantId)) {
						int saleMoney = productDetailDTO.getOriginalPrice() * productDetailDTO.getQuantity() - productDetailDTO.getPoiItemPromotion() - productDetailDTO.getPlatItemPromotion();
						percent = BigDecimal.valueOf(saleMoney).divide(BigDecimal.valueOf(totalShare),4, RoundingMode.HALF_UP);

					}else {
						percent = BigDecimal.valueOf(productDetailDTO.quantity).divide(BigDecimal.valueOf(totalQuantity),4, RoundingMode.HALF_UP);
					}
					int poiPromotion = BigDecimal.valueOf(totalPoiOrderPromotion).multiply(percent).setScale(0, RoundingMode.HALF_UP).intValue();
					int platItemPromotion = BigDecimal.valueOf(totalPlatOrderPromotion).multiply(percent).setScale(0, RoundingMode.HALF_UP).intValue();
					productDetailDTO.setPoiPromotion(poiPromotion);
					productDetailDTO.setPlatPromotion(platItemPromotion);
					lastPoiPromotion -= poiPromotion;
					lastPlatPromotion -= platItemPromotion;
				}
			}

		}

		private boolean priorityMatchMoreItemPromotion(List<WmAppOrderActDetail> itemActDetailList, List<OrderProductDetailDTO> skuDetails) {
			try {
				//第一优先：含有优惠的同一商品明细行和活动明细一一匹配
				boolean isSetItemPromotion = setItemPromotionByEveryPromotionProductCount(itemActDetailList, skuDetails);
				log.info("priorityMatchMoreItemPromotion isSetItemPromotion: {}, itemActDetailList: {}, skuDetails: {}", isSetItemPromotion, itemActDetailList, skuDetails);
				//第二优先：按同一商品明细数量之和与同一活动聚合汇总后的数据匹配
				if(!isSetItemPromotion) {
					List<ItemActDetail> itemActDetails = ItemActDetail.toItemActDetailList(itemActDetailList);
					//第二优先：按该活动对应所有商品累加数量匹配聚合后的活动数量
					return setItemPromotionPriorityMatchingProductCount(itemActDetails, skuDetails);
				}
				return true;

			} catch (Exception e) {
				log.info("MtBrandChannelOrderServiceImpl.priorityMatchMoreItemPromotion is error e= ", e);
			}
			return false;
		}

		private boolean setItemPromotionByEveryPromotionProductCount(List<WmAppOrderActDetail> itemActDetailList, List<OrderProductDetailDTO> skuDetails) {
			boolean isMatch = actDetailAndPromotionProductIsMatch(itemActDetailList, skuDetails);
			log.info("setItemPromotionByEveryPromotionProductCount isMatch: {}", isMatch);

			if(!isMatch) {
				return false;
			}
			//执行一一匹配逻辑
			List<ItemActDetail> itemActDetails = ItemActDetail.buildItemActDetails(itemActDetailList);
			for (OrderProductDetailDTO productDetailDTO : skuDetails){
				//无单品优惠
				if(Objects.equals(productDetailDTO.getOriginalPrice(), productDetailDTO.getSalePrice())){
					continue;
				}
				ItemActDetail targetItemActDetail = null;
				for (ItemActDetail itemActDetail : itemActDetails){
					if (itemActDetail.isHadBenefit()) {
						continue;
					}
					if (itemActDetail.getCount() == productDetailDTO.getQuantity()) {
						targetItemActDetail = itemActDetail;
						itemActDetail.setHadBenefit(true);
						break;
					}
				}
				if (Objects.nonNull(targetItemActDetail)){
					productDetailDTO.setPoiItemPromotion(targetItemActDetail.getPoiPromotion());
					productDetailDTO.setPlatItemPromotion(targetItemActDetail.getPlatPromotion());
				}
			}
			return true;

		}

		private boolean actDetailAndPromotionProductIsMatch(List<WmAppOrderActDetail> itemActDetailList, List<OrderProductDetailDTO> skuDetails) {
			List<Integer> productCounts = skuDetails.stream().filter(item -> !Objects.equals(item.getOriginalPrice(), item.getSalePrice()))
					.map(OrderProductDetailDTO::getQuantity).collect(Collectors.toList());
			for (WmAppOrderActDetail actDetail : itemActDetailList) {
				if(CollectionUtils.isEmpty(productCounts)) {
					return false;
				}
				boolean matchResult = matchCount(productCounts, actDetail.getCount());
				if(!matchResult) {
					return false;
				}
			}
			return productCounts.size() == 0;
		}

		private boolean matchCount(List<Integer> productCounts, int count) {
			for (int index = 0; index < productCounts.size(); index++) {
				if(count == productCounts.get(index)) {
					productCounts.remove(index);
					return true;
				}
			}
			//未匹配上
			return false;
		}

		/**
		 * 同一商品多行，明细单品赋值，优先按商品总数量和活动数量匹配
		 */
		private boolean setItemPromotionPriorityMatchingProductCount(List<ItemActDetail> itemActDetails, List<OrderProductDetailDTO> skuDetails) {
			Integer productAllCount = skuDetails.stream().map(OrderProductDetailDTO::getQuantity).reduce(0, Integer::sum);
			ItemActDetail targetItemActDetail = null;
			for (ItemActDetail itemActDetail : itemActDetails){
				if (itemActDetail.isHadBenefit()) {
					continue;
				}
				if (itemActDetail.getCount() == productAllCount) {
					targetItemActDetail = itemActDetail;
					itemActDetail.setHadBenefit(true);
					break;
				}
			}
			if(Objects.isNull(targetItemActDetail)) {
				return false;
			}
			log.info("extractSkuPromotionFromSkuBenefitV2.setItemPromotionPriorityMatchingProductCount");
			int poiItemPromotionAll = targetItemActDetail.getPoiPromotion();
			int platItemPromotionAll = targetItemActDetail.getPlatPromotion();
			int recordPoiItemPromotion = 0;
			int recordPlatItemPromotion = 0;

			for (int index = 0; index < skuDetails.size(); index++) {
				OrderProductDetailDTO productDetailDTO = skuDetails.get(index);
				//最后一件
				if(index == skuDetails.size()-1) {
					productDetailDTO.setPoiItemPromotion(poiItemPromotionAll - recordPoiItemPromotion);
					productDetailDTO.setPlatItemPromotion(platItemPromotionAll - recordPlatItemPromotion);
					return true;
				}
				int poiItemPromotion = BigDecimal.valueOf(poiItemPromotionAll).multiply(BigDecimal.valueOf(productDetailDTO.getQuantity())).divide(BigDecimal.valueOf(productAllCount), 0, BigDecimal.ROUND_HALF_UP).intValue();
				int platItemPromotion = BigDecimal.valueOf(platItemPromotionAll).multiply(BigDecimal.valueOf(productDetailDTO.getQuantity())).divide(BigDecimal.valueOf(productAllCount), 0, BigDecimal.ROUND_HALF_UP).intValue();
				productDetailDTO.setPoiItemPromotion(poiItemPromotion);
				productDetailDTO.setPlatItemPromotion(platItemPromotion);
				recordPoiItemPromotion += poiItemPromotion;
				recordPlatItemPromotion += platItemPromotion;
			}
			return true;
		}

	}

	private void extractSkuPromotionFromSkuBenefitV2(List<OrderProductDetailDTO> skuDetails, List<SkuBenefitDetail> skuBenefitDetails, Long tenantId) {
		log.info("extractSkuPromotionFromSkuBenefitV2");
		if (CollectionUtils.isEmpty(skuBenefitDetails)){
			return;
		}
		for (SkuBenefitDetail skuBenefitDetail : skuBenefitDetails){
			if (CollectionUtils.isEmpty(skuBenefitDetail.getWmAppOrderActDetails())){
				continue;
			}
			List<OrderProductDetailDTO> targetSkuDetails = skuDetails.stream()
					.filter(sku -> Objects.equals(sku.getSkuId(), skuBenefitDetail.getSku_id()) && Objects.equals(sku.getCustomSpu(), skuBenefitDetail.getApp_food_code()))
					.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(targetSkuDetails)){
				continue;
			}

			SkuBenefit skuBenefit = new SkuBenefit(tenantId, skuBenefitDetail, targetSkuDetails);
			skuBenefit.benefit();
		}
	}

//	private void extractSkuPromotionFromSkuBenefitV3(List<OrderProductDetailDTO> skuDetails, List<SkuBenefitDetail> skuBenefitDetails) {
//		log.info("extractSkuPromotionFromSkuBenefitV3");
//		if (CollectionUtils.isEmpty(skuBenefitDetails)){
//			return;
//		}
//		for (SkuBenefitDetail skuBenefitDetail : skuBenefitDetails){
//			if (CollectionUtils.isEmpty(skuBenefitDetail.getSgAppOrderSkuBenefitDetailByItems())){
//				continue;
//			}
//			Map<Long, OrderProductDetailDTO> itemId2Product = skuDetails.stream().collect(Collectors.toMap(OrderProductDetailDTO::getItemId, Function.identity(), (v1, v2) -> v2));
//			Map<Long, List<SgAppOrderSkuBenefitDetailByItem>> itemId2Promotion = skuBenefitDetail.getSgAppOrderSkuBenefitDetailByItems().stream().collect(Collectors.groupingBy(SgAppOrderSkuBenefitDetailByItem::getItemId));
//			for (Map.Entry<Long, OrderProductDetailDTO> entry : itemId2Product.entrySet()){
//				List<SgAppOrderSkuBenefitDetailByItem> promotionDetails = itemId2Promotion.get(entry.getKey());
//				if (CollectionUtils.isEmpty(promotionDetails)){
//					continue;
//				}
//				int poiPromotion = 0;
//				int poiItemPromotion = 0;
//				int platItemPromotion = 0;
//				int platPromotion = 0;
//				for (SgAppOrderSkuBenefitDetailByItem promotionDetail : promotionDetails){
//					if (CollectionUtils.isEmpty(promotionDetail.getItemWmAppOrderActDetails())){
//						continue;
//					}
//					for (ItemWmAppOrderActDetail itemWmAppOrderActDetail : promotionDetail.getItemWmAppOrderActDetails()){
//						if (MtActivityTypeEnum.isItemPromotion(itemWmAppOrderActDetail.getType())){
//							poiItemPromotion += MoneyUtils.yuanToFen(itemWmAppOrderActDetail.getPoiCharge());
//							platItemPromotion += MoneyUtils.yuanToFen(itemWmAppOrderActDetail.getMtCharge());
//						}else {
//							poiPromotion += MoneyUtils.yuanToFen(itemWmAppOrderActDetail.getPoiCharge());
//							platPromotion += MoneyUtils.yuanToFen(itemWmAppOrderActDetail.getMtCharge());
//						}
//					}
//				}
//				entry.getValue().setPoiPromotion(poiPromotion);
//				entry.getValue().setPlatPromotion(platPromotion);
//				entry.getValue().setPoiItemPromotion(poiItemPromotion);
//				entry.getValue().setPlatItemPromotion(platItemPromotion);
//			}
//		}
//	}

	private void extractSkuPromotionFromSkuBenefitV3(List<OrderProductDetailDTO> skuDetails, List<SkuBenefitDetail> skuBenefitDetails, Long tenantId) {
		log.info("extractSkuPromotionFromSkuBenefitV3");
		if (CollectionUtils.isEmpty(skuBenefitDetails)){
			return;
		}
		for (SkuBenefitDetail skuBenefitDetail : skuBenefitDetails){
			if (CollectionUtils.isEmpty(skuBenefitDetail.getWmAppOrderActDetails())){
				continue;
			}
			List<OrderProductDetailDTO> targetSkuDetails = skuDetails.stream()
					.filter(sku -> Objects.equals(sku.getSkuId(), skuBenefitDetail.getSku_id()) && Objects.equals(sku.getCustomSpu(), skuBenefitDetail.getApp_food_code()))
					.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(targetSkuDetails)){
				//todo找不到匹配商品明细，出错
				continue;
			}
			int totalPlatPromotion = MoneyUtils.yuanToFen(skuBenefitDetail.getTotalMtCharge());
			int totalPoiPromotion = MoneyUtils.yuanToFen(skuBenefitDetail.getTotalPoiCharge());

			List<WmAppOrderActDetail> itemActDetails = skuBenefitDetail.getWmAppOrderActDetails().stream()
					.filter(act -> MtActivityTypeEnum.isItemPromotion(act.getType()))
					.collect(Collectors.toList());
			//单品优惠
			Set<WmAppOrderActDetail> usedItemActDetails = new HashSet<>();
			for (OrderProductDetailDTO productDetailDTO : targetSkuDetails){
				if (CollectionUtils.isEmpty(itemActDetails)){
					productDetailDTO.setActualSalePrice(productDetailDTO.getOriginalPrice());
					continue;
				}
				//无单品优惠
				if(Objects.equals(productDetailDTO.getOriginalPrice(), productDetailDTO.getSalePrice())){
					continue;
				}
				if (targetSkuDetails.size() == 1){
					int platItemPromotion = 0;
					int poiItemPromotion = 0;
					for (WmAppOrderActDetail itemActDetail : itemActDetails){
						platItemPromotion += MoneyUtils.yuanToFen(itemActDetail.getMtCharge()) * itemActDetail.getCount();
						poiItemPromotion += MoneyUtils.yuanToFen(itemActDetail.getPoiCharge()) * itemActDetail.getCount();
					}
					productDetailDTO.setPlatItemPromotion(platItemPromotion);
					productDetailDTO.setPoiItemPromotion(poiItemPromotion);
					totalPlatPromotion -= platItemPromotion;
					totalPoiPromotion -= poiItemPromotion;
				}else {
					//一个sku只能参加一个单品优惠
					for (WmAppOrderActDetail itemActDetail : itemActDetails){
						if (usedItemActDetails.contains(itemActDetail)){
							continue;
						}
						if (itemActDetail.getCount() == productDetailDTO.getQuantity()) {
							int actualSalePrice = productDetailDTO.getOriginalPrice() - MoneyUtils.yuanToFen(itemActDetail.getMtCharge()) - MoneyUtils.yuanToFen(itemActDetail.getPoiCharge());
							int platItemPromotion = MoneyUtils.yuanToFen(itemActDetail.getMtCharge()) * itemActDetail.getCount();
							int poiItemPromotion = MoneyUtils.yuanToFen(itemActDetail.getPoiCharge()) * itemActDetail.getCount();
							productDetailDTO.setPlatItemPromotion(platItemPromotion);
							productDetailDTO.setPoiItemPromotion(poiItemPromotion);
							productDetailDTO.setActualSalePrice(actualSalePrice);
							totalPlatPromotion -= platItemPromotion;
							totalPoiPromotion -= poiItemPromotion;
							usedItemActDetails.add(itemActDetail);
							break;
						}
					}
				}
			}
			// 复制一份。累积减的时候使用，不能影响原值。
			int lastPoiPromotion = totalPoiPromotion;
			int lastPlatPromotion = totalPlatPromotion;

			int totalQuantity = targetSkuDetails.stream().mapToInt(OrderProductDetailDTO::getQuantity).sum();
			int totalShare = targetSkuDetails.stream().map(v->v.getOriginalPrice() * v.getQuantity() - v.getPoiItemPromotion() - v.getPlatItemPromotion()).reduce(0, Integer::sum);

			//整单优惠
			for (int index = 0; index < targetSkuDetails.size(); index++){
				OrderProductDetailDTO productDetailDTO = targetSkuDetails.get(index);
				if (index == targetSkuDetails.size() - 1){
					productDetailDTO.setPoiPromotion(lastPoiPromotion);
					productDetailDTO.setPlatPromotion(lastPlatPromotion);
				}else {
					BigDecimal percent;
					if (MccConfigUtil.splitOrderPromotionBySalePrice(tenantId)) {
						int saleMoney = productDetailDTO.getOriginalPrice() * productDetailDTO.getQuantity() - productDetailDTO.getPoiItemPromotion() - productDetailDTO.getPlatItemPromotion();
						percent = BigDecimal.valueOf(saleMoney).divide(BigDecimal.valueOf(totalShare),4, RoundingMode.HALF_UP);
					}else {
						percent = BigDecimal.valueOf(productDetailDTO.getQuantity()).divide(BigDecimal.valueOf(totalQuantity),4, RoundingMode.HALF_UP);
					}
					int poiPromotion = BigDecimal.valueOf(totalPoiPromotion).multiply(percent).setScale(0, RoundingMode.HALF_UP).intValue();
					int platItemPromotion = BigDecimal.valueOf(totalPlatPromotion).multiply(percent).setScale(0, RoundingMode.HALF_UP).intValue();
					productDetailDTO.setPoiPromotion(poiPromotion);
					productDetailDTO.setPlatPromotion(platItemPromotion);
					lastPoiPromotion -= poiPromotion;
					lastPlatPromotion -= platItemPromotion;
				}
			}
		}
	}



	private void extractSkuPromotionFromSkuBenefit(List<OrderProductDetailDTO> skuDetails, List<SkuBenefitDetail> skuBenefitDetails) {
		if (CollectionUtils.isEmpty(skuBenefitDetails)){
			return;
		}
		for (SkuBenefitDetail skuBenefitDetail : skuBenefitDetails){
			if (CollectionUtils.isEmpty(skuBenefitDetail.getWmAppOrderActDetails())){
				continue;
			}
			List<OrderProductDetailDTO> targetSkuDetails = skuDetails.stream()
					.filter(sku -> Objects.equals(sku.getSkuId(), skuBenefitDetail.getSku_id()) && Objects.equals(sku.getCustomSpu(), skuBenefitDetail.getApp_food_code()))
					.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(targetSkuDetails)){
				//todo找不到匹配商品明细，出错
				continue;
			}
			int totalPlatPromotion = MoneyUtils.yuanToFen(skuBenefitDetail.getTotalMtCharge());
			int totalPoiPromotion = MoneyUtils.yuanToFen(skuBenefitDetail.getTotalPoiCharge());

			List<WmAppOrderActDetail> itemActDetails = skuBenefitDetail.getWmAppOrderActDetails().stream()
					.filter(act -> MtActivityTypeEnum.isItemPromotion(act.getType()))
					.collect(Collectors.toList());
			//单品优惠
			for (OrderProductDetailDTO productDetailDTO : targetSkuDetails){
				if (CollectionUtils.isEmpty(itemActDetails)){
					productDetailDTO.setActualSalePrice(productDetailDTO.getOriginalPrice());
					continue;
				}
				//无单品优惠
				if(Objects.equals(productDetailDTO.getOriginalPrice(), productDetailDTO.getSalePrice())){
					continue;
				}
				//一个sku只能参加一个单品优惠
				WmAppOrderActDetail itemActDetail = itemActDetails.get(0);
				if (itemActDetail.getCount() == productDetailDTO.getQuantity() ){
					int actualSalePrice = productDetailDTO.getOriginalPrice() - MoneyUtils.yuanToFen(itemActDetail.getMtCharge()) - MoneyUtils.yuanToFen(itemActDetail.getPoiCharge());
					int platItemPromotion = MoneyUtils.yuanToFen(itemActDetail.getMtCharge()) * productDetailDTO.getQuantity();
					int poiItemPromotion = MoneyUtils.yuanToFen(itemActDetail.getPoiCharge()) * productDetailDTO.getQuantity();
					productDetailDTO.setPlatItemPromotion(platItemPromotion);
					productDetailDTO.setPoiItemPromotion(poiItemPromotion);
					productDetailDTO.setActualSalePrice(actualSalePrice);
					totalPlatPromotion -= platItemPromotion;
					totalPoiPromotion -= poiItemPromotion;
					break;
				}

			}

			// 复制一份。累积减的时候使用，不能影响原值。
			int lastPoiPromotion = totalPoiPromotion;
			int lastPlatPromotion = totalPlatPromotion;

			//整单优惠
			for (int index = 0; index < targetSkuDetails.size(); index++){
				OrderProductDetailDTO productDetailDTO = targetSkuDetails.get(index);
				if (index == targetSkuDetails.size() - 1){
					productDetailDTO.setPoiPromotion(lastPoiPromotion);
					productDetailDTO.setPlatPromotion(lastPlatPromotion);
				}else {
					BigDecimal percent = BigDecimal.valueOf(productDetailDTO.quantity).divide(BigDecimal.valueOf(skuBenefitDetail.getCount()),2, RoundingMode.HALF_UP);
					int poiPromotion = BigDecimal.valueOf(totalPoiPromotion).multiply(percent).setScale(0, RoundingMode.HALF_UP).intValue();
					int platItemPromotion = BigDecimal.valueOf(totalPlatPromotion).multiply(percent).setScale(0, RoundingMode.HALF_UP).intValue();
					productDetailDTO.setPoiPromotion(poiPromotion);
					productDetailDTO.setPlatPromotion(platItemPromotion);
					lastPoiPromotion -= poiPromotion;
					lastPlatPromotion -= platItemPromotion;
				}
			}
		}
	}


	/**
	 * 捕捉异常，避免影响订单入库。
	 */
	private List<ActivityShareDetailDTO> splitSkuPromotionFromSkuBenefitCatchException(List<GoodsActivityDetailDTO> skuBenefitDetails, long tenantId) {
		try {
			if (!MccConfigUtil.getActivityShareSwitch()) {
				log.info("活动优惠拆分功能控制为false");
				return new ArrayList<>();
			}
//			log.info("meituan活动分摊 splitSkuPromotionFromSkuBenefitCatchException, skuBenefitDetails={}", skuBenefitDetails);
			List<ActivityShareDetailDTO> shareDetailDTOS = splitSkuPromotionFromSkuBenefit(skuBenefitDetails, tenantId);
			log.info("meituan活动分摊 splitSkuPromotionFromSkuBenefitCatchException 结果shareDetailDTOS={}", shareDetailDTOS);
			return shareDetailDTOS;
		} catch (Exception ex) {
			log.error("活动分摊异常", ex);
			return Lists.emptyList();
		}
	}

	/**
	 * 美团已经分摊好每个商品参与的活动优惠，但这里是商品维度，即一个商品sku只有一条数据，这条数据下挂着多个活动。
	 * 这里需要将数据拆成 商品sku + 活动 维度的，例如一个商品A参加2个活动E和F，将生成 A+E、A+F 2条数据。
	 */
	private List<ActivityShareDetailDTO> splitSkuPromotionFromSkuBenefit(List<GoodsActivityDetailDTO> skuBenefitDetails, long tenantId) {
		if (CollectionUtils.isEmpty(skuBenefitDetails)){
			return Lists.emptyList();
		}

		List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();

		// 外层循环是商品维度
		for (GoodsActivityDetailDTO skuBenefitDetail : skuBenefitDetails){
			if (CollectionUtils.isEmpty(skuBenefitDetail.getGoodActivityDetail())){
				// 该商品没有分摊优惠
				continue;
			}

			boolean flag = MccConfigUtil.handleMtMultiItemPromotion(tenantId);
			// 内层循环商品参加的每一个活动
			for (GoodsSharedActivityItem wmAppOrderActDetail : skuBenefitDetail.getGoodActivityDetail()) {
				ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
				activityShareDetailDTO.setChannelPromotionType(wmAppOrderActDetail.getChannelPromotionType());
				activityShareDetailDTO.setSkuId(skuBenefitDetail.getCustomSkuId());
				activityShareDetailDTO.setCustomSpu(skuBenefitDetail.getCustomSpu());
				activityShareDetailDTO.setSkuCount(getIntegerValue(skuBenefitDetail.getSkuCount()));
				activityShareDetailDTO.setTotalOriginPrice(getIntegerValue(skuBenefitDetail.getOriginPrice() * skuBenefitDetail.getSkuCount()));
				// 单品优惠取值
				if (MtActivityTypeEnum.isItemPromotion(Integer.parseInt(wmAppOrderActDetail.getChannelPromotionType()))) {
					if (flag) {
						continue;
					}
					// 参加活动的商品数量
					int activitySkuQuantity = wmAppOrderActDetail.getPromotionCount();
					activityShareDetailDTO.setPromotionCount(activitySkuQuantity);
					activityShareDetailDTO.setPromotionRemark("单品直降");
					// 单品优惠的需要乘以活动数量
					activityShareDetailDTO.setChannelCost(getIntegerValue(wmAppOrderActDetail.getChannelCost() * activitySkuQuantity));
					activityShareDetailDTO.setTenantCost(getIntegerValue(wmAppOrderActDetail.getTenantCost() * activitySkuQuantity));
				} else {
					activityShareDetailDTO.setPromotionCount(getIntegerValue(skuBenefitDetail.getSkuCount()));
					// 活动名称取美团渠道返回的remark
					activityShareDetailDTO.setPromotionRemark(wmAppOrderActDetail.getPromotionRemark());
					// 整单优惠已经分摊好，不需要计算
					activityShareDetailDTO.setChannelCost(getIntegerValue(wmAppOrderActDetail.getChannelCost()));
					activityShareDetailDTO.setTenantCost(getIntegerValue(wmAppOrderActDetail.getTenantCost()));
				}

				activityShareDetailDTO.setActivityId(StringUtils.isNotBlank(wmAppOrderActDetail.getActivityId()) ? wmAppOrderActDetail.getActivityId() : activityShareDetailDTO.getPromotionRemark());

				// 用以上的数据计算优惠后的总价
				activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() - activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());

				// 美团无整合营销字段，以下代码注释掉
				// activityShareDetailDTO.setPlatMarketPromotion();
				// activityShareDetailDTO.setPoiMarketPromotion();
				// activityShareDetailDTO.setSupplierMarketPromotion();

				activityShareDetailDTOList.add(activityShareDetailDTO);
			}

			if (flag) {
				log.info("美团渠道单品活动处理, skuBenefitDetail:{}, tenantId:{}", GsonUtil.toJSONString(skuBenefitDetail), tenantId);
				List<ActivityShareDetailDTO> itemPromtionActivityList = handleDuplicateItemPromotion(skuBenefitDetail);
				log.info("美团渠道单品活动处理, itemPromotionActivityList={}", GsonUtil.toJSONString(itemPromtionActivityList));
				activityShareDetailDTOList.addAll(itemPromtionActivityList);
			}
		}

		return activityShareDetailDTOList;
	}

	/**
	 * 重复的单品活动需要聚合
	 */
	private List<ActivityShareDetailDTO> handleDuplicateItemPromotion(GoodsActivityDetailDTO skuBenefitDetail) {
		List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
		List<GoodsSharedActivityItem> itemPromotionList = skuBenefitDetail.getGoodActivityDetail()
				.stream()
				.filter(activity ->
						MtActivityTypeEnum.isItemPromotion(Integer.parseInt(activity.getChannelPromotionType())))
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(itemPromotionList)) {
			return activityShareDetailDTOList;
		}


		Map<String, List<GoodsSharedActivityItem>> activityGroupMap = itemPromotionList.stream()
				.collect(Collectors.groupingBy(GoodsSharedActivityItem::getActivityId));
		for (Map.Entry<String, List<GoodsSharedActivityItem>> entry : activityGroupMap.entrySet()) {
			ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
			activityShareDetailDTO.setChannelPromotionType(entry.getValue().get(0).getChannelPromotionType());
			activityShareDetailDTO.setSkuId(skuBenefitDetail.getCustomSkuId());
			activityShareDetailDTO.setCustomSpu(skuBenefitDetail.getCustomSpu());
			activityShareDetailDTO.setSkuCount(getIntegerValue(skuBenefitDetail.getSkuCount()));
			activityShareDetailDTO.setTotalOriginPrice(getIntegerValue(skuBenefitDetail.getOriginPrice() * skuBenefitDetail.getSkuCount()));
			activityShareDetailDTO.setActivityId(entry.getKey());
			activityShareDetailDTO.setPromotionRemark("单品直降");

			int totalChannelCost = 0;
			int totalTenantCost = 0;
			int totalPromotionCount = 0;

			for (GoodsSharedActivityItem item : entry.getValue()) {
				totalChannelCost += getIntegerValue(item.getChannelCost() * item.getPromotionCount());
				totalTenantCost += getIntegerValue(item.getTenantCost() * item.getPromotionCount());
				totalPromotionCount += item.getPromotionCount();
			}

			activityShareDetailDTO.setChannelCost(totalChannelCost);
			activityShareDetailDTO.setTenantCost(totalTenantCost);
			activityShareDetailDTO.setPromotionCount(totalPromotionCount);
			activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() - totalChannelCost - totalTenantCost);

			activityShareDetailDTOList.add(activityShareDetailDTO);
		}

		return activityShareDetailDTOList;
	}


	/**
	 * 捕捉异常，避免影响订单入库。
	 */
	private List<ActivityShareDetailDTO> splitSkuLogisticPromotionFromSkuBenefitCatchException(ChannelOrderDetailDTO channelOrderDetailDTO, List<OrderDiscountDetailDTO> orderDiscountDetailDTOList, long tenantId) {
		try {
			if (!MccConfigUtil.getActivityShareSwitch()) {
				log.info("活动优惠拆分功能控制为false");
				return Lists.emptyList();
			}
//			log.info("meituan活动分摊 splitSkuLogisticPromotionFromSkuBenefitCatchException, orderDiscountDetailDTOList={}", GsonUtils.toJSONString(orderDiscountDetailDTOList));
			List<ActivityShareDetailDTO> shareDetailDTOS = splitSkuLogisticPromotionFromSkuBenefit(channelOrderDetailDTO, orderDiscountDetailDTOList, tenantId);
//			log.info("meituan活动分摊 splitSkuLogisticPromotionFromSkuBenefitCatchException 结果shareDetailDTOS={}", GsonUtils.toJSONString(shareDetailDTOS));
			return shareDetailDTOS;
		} catch (Exception ex) {
			log.error("活动分摊异常", ex);
			return Lists.emptyList();
		}
	}

	/**
	 * 美团没有分摊运费优惠到商品级别。
	 * 这里需要将数据拆成 商品sku + 活动 维度的，例如一个商品A参加2个活动E和F，将生成 A+E、A+F 2条数据。
	 */
	private List<ActivityShareDetailDTO> splitSkuLogisticPromotionFromSkuBenefit(ChannelOrderDetailDTO channelOrderDetailDTO, List<OrderDiscountDetailDTO> orderDiscountDetailDTOList, long tenantId) {
		if (CollectionUtils.isEmpty(orderDiscountDetailDTOList)){
			return Lists.emptyList();
		}
		List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
		// 外层循环是活动维度
		for (OrderDiscountDetailDTO orderDiscountDetailDTO : orderDiscountDetailDTOList) {
			// 不是运费，不分摊。
			if (!MtActivityTypeEnum.isLogisticPromotion(Integer.parseInt(orderDiscountDetailDTO.getType()))) {
				continue;
			}
			activityShareDetailDTOList.addAll(splitSkuLogisticPromotion(channelOrderDetailDTO, orderDiscountDetailDTO, tenantId));
		}

		return activityShareDetailDTOList;
	}

	/**
	 * 美团没有分摊运费优惠到商品级别。
	 * 这里需要将数据拆成 商品sku + 活动 维度的，例如一个商品A参加2个活动E和F，将生成 A+E、A+F 2条数据。
	 */
	private List<ActivityShareDetailDTO> splitSkuLogisticPromotion(ChannelOrderDetailDTO channelOrderDetailDTO, OrderDiscountDetailDTO orderDiscountDetailDTO, long tenantId) {
		List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();

		// allSkuDetailList 包含了赠品
		List<OrderProductDetailDTO> allSkuDetailList = channelOrderDetailDTO.getSkuDetails();

		if (CollectionUtils.isEmpty(allSkuDetailList)) {
			log.info("活动分摊没有商品明细allSkuDetailList={}", allSkuDetailList);
			return activityShareDetailDTOList;
		}

		// 过滤赠品
		List<OrderProductDetailDTO> skuDetailList = allSkuDetailList.stream().filter(detailDTO -> detailDTO.getItemType() != 1).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(skuDetailList)) {
			log.info("活动分摊没有商品明细skuDetailList={}", skuDetailList);
			return activityShareDetailDTOList;
		}

		// 渠道给的单位是元，这里之前已经转为分
		int poi_charge = getIntegerValue(orderDiscountDetailDTO.getBizCharge());
		int mt_charge = getIntegerValue(orderDiscountDetailDTO.getChannelCharge());
        // 复制一份。累积减的时候使用，不能影响原值。
		int lastPoiPromotion = poi_charge;
		int lastPlatPromotion = mt_charge;

		// 优惠都为0，不分摊
		if (poi_charge <= 0 && mt_charge <= 0) {
			return activityShareDetailDTOList;
		}

		// 计算商品总的实际价格，即优惠后。 SalePrice 来自美团渠道订单详情的 actual_price
		int totalActualSalePrice = skuDetailList.stream().mapToInt(ActivityPromotionSplitUtil::getSaleAmt).sum();

		// log.info("排序前skuDetailList={}", skuDetailList);
		ActivityPromotionSplitUtil.sortProductList(tenantId, skuDetailList, ActivityPromotionSplitUtil::getSaleAmt,
				OrderProductDetailDTO::getQuantity, OrderProductDetailDTO::getSkuName);

		// 商品总数量
		int totalQuantity = skuDetailList.stream().mapToInt(OrderProductDetailDTO::getQuantity).sum();

		// 遍历商品明细
		for (int index = 0; index < skuDetailList.size(); index++) {
			OrderProductDetailDTO skuDetail = skuDetailList.get(index);
			ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();

			activityShareDetailDTO.setChannelPromotionType(orderDiscountDetailDTO.getType());
			// skuid 来自美团渠道订单详情的 skuid
			activityShareDetailDTO.setSkuId(skuDetail.getSkuId());
			activityShareDetailDTO.setCustomSpu(skuDetail.getCustomSpu());
			activityShareDetailDTO.setSkuCount(skuDetail.getQuantity());
			//
			activityShareDetailDTO.setPromotionCount(skuDetail.getQuantity());
			activityShareDetailDTO.setPromotionRemark(orderDiscountDetailDTO.getRemark());
			// 运费没有活动ID
			activityShareDetailDTO.setActivityId(StringUtils.isNotBlank(orderDiscountDetailDTO.getActivityId()) ? orderDiscountDetailDTO.getActivityId() : orderDiscountDetailDTO.getRemark());
			activityShareDetailDTO.setTotalOriginPrice(skuDetail.getOriginalPrice() * skuDetail.getQuantity());
			int actualSalePrice = ActivityPromotionSplitUtil.getSaleAmt(skuDetail);

			if (index == skuDetailList.size() - 1) {
				// 尾差法：最后一个商品，取剩余的优惠。
				activityShareDetailDTO.setChannelCost(lastPlatPromotion);
				activityShareDetailDTO.setTenantCost(lastPoiPromotion);
			} else {
				/*
				  参考下游 settlement 服务单头金额到明细的拆分方式
				  com.sankuai.meituan.shangou.empower.settlement.domain.finance.factory.FinanceCombinationFactory#splitHead2Detail
 				 */
				double percent = 0d;
				if (totalActualSalePrice == 0) {
					// 如果优惠后价格为0，说明所有商品优惠后价格为0，按数量拆分
					percent = skuDetail.getQuantity() * 1.0 / totalQuantity;
				} else {
					// 美团按优惠后的价格比例分摊
					percent = actualSalePrice * 1.0 / totalActualSalePrice;
				}

				int poiPromotion = Double.valueOf(poi_charge * percent).intValue();
				int platItemPromotion = Double.valueOf(mt_charge * percent).intValue();
				activityShareDetailDTO.setChannelCost(platItemPromotion);
				activityShareDetailDTO.setTenantCost(poiPromotion);
				lastPoiPromotion -= poiPromotion;
				lastPlatPromotion -= platItemPromotion;
			}

			// activityShareDetailDTO.setPlatMarketPromotion();
			// activityShareDetailDTO.setPoiMarketPromotion();
			// activityShareDetailDTO.setSupplierMarketPromotion();

			// 运费优惠不影响活动价格。单品优惠的活动价格 = 原价 - 优惠。运费优惠不这么计算
			activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice());
			// activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() - activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());
			activityShareDetailDTOList.add(activityShareDetailDTO);
		}

		return activityShareDetailDTOList;
	}

	/**
	 * todo订单级别整单、单品是否有计算必要
	 * @param tenantId
	 * @param channelOrderDetailDTO
	 * @param orderActivitiesInfoList
	 * @param logisticsCode 渠道配送类型
	 */
	private void extractPromotionFromActivities(long tenantId, ChannelOrderDetailDTO channelOrderDetailDTO, List<OrderActivitiesInfo> orderActivitiesInfoList, String logisticsCode) {
		int platPromotion = 0;
		int poiPromotion = 0;
		int platItemPromotion = 0;
		int poiItemPromotion = 0;
		int platLogisticPromotion = 0;
		int poiLogisticPromotion = 0;
		int platPackagePromotion = 0;
		int poiPackagePromotion = 0;
		if (CollectionUtils.isNotEmpty(orderActivitiesInfoList)){
            boolean useNewPromotion = MccConfigUtil.checkExtrasNewPromotionTenantId(tenantId);
			for (OrderActivitiesInfo orderActivitiesInfo : orderActivitiesInfoList){
				if (orderActivitiesInfo.getType() == null){
					continue;
				}
				int type = orderActivitiesInfo.getType();
				int mtCharge = Optional.ofNullable(orderActivitiesInfo.getMt_charge()).map(MoneyUtils::yuanToFen).orElse(0);
				int poiCharge = Optional.ofNullable(orderActivitiesInfo.getPoi_charge()).map(MoneyUtils::yuanToFen).orElse(0);
				//承担优惠分摊到商品的金额
				int skuMtCharge = Optional.ofNullable(orderActivitiesInfo.getSku_mt_charge()).map(MoneyUtils::yuanToFen).orElse(0);
				int skuPoiCharge = Optional.ofNullable(orderActivitiesInfo.getSku_poi_charge()).map(MoneyUtils::yuanToFen).orElse(0);
                //承担优惠分摊到打包费的金额
                int packageBagMtCharge = Optional.ofNullable(orderActivitiesInfo.getPackage_bag_mt_charge()).map(MoneyUtils::yuanToFen).orElse(0);
                int packageBagPoiCharge = Optional.ofNullable(orderActivitiesInfo.getPackage_bag_poi_charge()).map(MoneyUtils::yuanToFen).orElse(0);
                //承担优惠分摊到配送费的金额
                int shippingFeeMtCharge = Optional.ofNullable(orderActivitiesInfo.getShipping_fee_mt_charge()).map(MoneyUtils::yuanToFen).orElse(0);
                int shippingFeePoiCharge = Optional.ofNullable(orderActivitiesInfo.getShipping_fee_poi_charge()).map(MoneyUtils::yuanToFen).orElse(0);
				if (MtActivityTypeEnum.isItemPromotion(type)){
					platItemPromotion += mtCharge;
					poiItemPromotion += poiCharge;
                    if (useNewPromotion) {
                        //单品优惠中，如果有分摊到包装费优惠金额上，则累计包装费
                        platPackagePromotion += packageBagMtCharge;
                        poiPackagePromotion += packageBagPoiCharge;
                        //单品优惠中，如果有分摊到配送费优惠金额上，则累计配送费
                        platLogisticPromotion += shippingFeeMtCharge;
                        poiLogisticPromotion += shippingFeePoiCharge;
						//从mtCharge和poiCharge减去分摊到其他优惠上的金额
						platItemPromotion -= (packageBagMtCharge + shippingFeeMtCharge);
						poiItemPromotion -= (packageBagPoiCharge + shippingFeePoiCharge);
                    }
				}else if (MtActivityTypeEnum.isLogisticPromotion(type)){
					platLogisticPromotion += mtCharge;
					poiLogisticPromotion += poiCharge;
                    if (useNewPromotion) {
                        //配送费优惠中，如果有分摊到包装费优惠金额上，则累计包装费
                        platPackagePromotion += packageBagMtCharge;
                        poiPackagePromotion += packageBagPoiCharge;
						//从mtCharge和poiCharge减去分摊到其他优惠上的金额
                        platLogisticPromotion -= (packageBagMtCharge + skuMtCharge);
                        poiLogisticPromotion -= (packageBagPoiCharge + skuPoiCharge);
                    }
				}else if (MtActivityTypeEnum.isPackagePromotion(type)){
					platPackagePromotion += mtCharge;
					poiPackagePromotion += poiCharge;
                    if (useNewPromotion) {
                        //包装费优惠中，如果有分摊到配送费优惠金额上，则累计配送费
                        platLogisticPromotion += shippingFeeMtCharge;
                        poiLogisticPromotion += shippingFeePoiCharge;
						//从mtCharge和poiCharge减去分摊到其他优惠上的金额
                        platPackagePromotion -= (shippingFeeMtCharge + skuMtCharge);
                        poiPackagePromotion -= (shippingFeePoiCharge + skuPoiCharge);;
                    }
				}else {
					platPromotion += mtCharge;
					poiPromotion += poiCharge;
                    if (useNewPromotion) {
                        //整单优惠中，如果有分摊到包装费优惠金额上，则累计包装费
                        platPackagePromotion += packageBagMtCharge;
                        poiPackagePromotion += packageBagPoiCharge;
                        //整单优惠中，如果有分摊到配送费优惠金额上，则累计配送费
                        platLogisticPromotion += shippingFeeMtCharge;
                        poiLogisticPromotion += shippingFeePoiCharge;
						//从mtCharge和poiCharge减去分摊到其他优惠上的金额
						platPromotion -= (packageBagMtCharge + shippingFeeMtCharge);
						poiPromotion -= (packageBagPoiCharge + shippingFeePoiCharge);
                    }
				}
			}
		}
		channelOrderDetailDTO.setPlatPromotion(platPromotion);
		channelOrderDetailDTO.setPoiPromotion(poiPromotion);
		channelOrderDetailDTO.setPlatItemPromotion(platItemPromotion);
		channelOrderDetailDTO.setPoiItemPromotion(poiItemPromotion);
		channelOrderDetailDTO.setPlatLogisticsPromotion(platLogisticPromotion);
		channelOrderDetailDTO.setPoiLogisticsPromotion(poiLogisticPromotion);
		channelOrderDetailDTO.setPlatPackagePromotion(platPackagePromotion);
		channelOrderDetailDTO.setPoiPackagePromotion(poiPackagePromotion);

		// mock 代码
		/*mockMtOrderPromotion(channelOrderDetailDTO, tenantId);
		platPackagePromotion = channelOrderDetailDTO.getPlatPackagePromotion();*/

		//包装费分摊
		if (platformChargePackageFee(tenantId)){
			channelOrderDetailDTO.setPlatPackageAmt(channelOrderDetailDTO.getPackageAmt() - platPackagePromotion);
			channelOrderDetailDTO.setPoiPackageAmt(-poiPackagePromotion);
		}else {
			channelOrderDetailDTO.setPlatPackageAmt(-platPackagePromotion);
			channelOrderDetailDTO.setPoiPackageAmt(channelOrderDetailDTO.getPackageAmt() - poiPackagePromotion);
		}
		channelOrderDetailDTO.setPayPackageAmt(channelOrderDetailDTO.getPackageAmt() - poiPackagePromotion - platPackagePromotion);


		if (Objects.equals(channelOrderDetailDTO.getDeliveryDetail().getIsSelfDelivery(), 0)) {
            if (MtBrandConverterUtil.checkLogisticsCodeRebackFreight(logisticsCode)
                    && !MccConfigUtil.checkMtQikeRebackFreightTenantId(tenantId)) {
				//平台企客配送 运费返还:快速达 4011、及时达4012 光速达 4001且租户不需要返还运费，商家运费收入=应收配送费-运费商家优惠，按照商家自配送的方式计算
				channelOrderDetailDTO.setPoiLogisticsIncome(channelOrderDetailDTO.getFreight() - poiLogisticPromotion);
            } else {
                channelOrderDetailDTO.setPoiLogisticsIncome(-poiLogisticPromotion);
            }
        }else {
			channelOrderDetailDTO.setPoiLogisticsIncome(channelOrderDetailDTO.getFreight() - poiLogisticPromotion);
		}
	}

	private boolean platformChargePackageFee(Long tenantId){
		try {
			ChannelConfigQueryRequest request = new ChannelConfigQueryRequest();
			request.setTenantId(tenantId);
			request.setConfigId(ConfigItemEnum.CHARGING_MERCHANT_BAG_FEES.getKey());
			request.setSubjectId(tenantId);
			request.setChannelId(ChannelTypeEnum.MEITUAN.getCode());
			TenantChannelConfigResponse response = configThriftService.queryTenantChannelConfig(request);
			if (response.getStatus() != null && response.getStatus().getCode() == StatusCodeEnum.SUCCESS.getCode()
					&& response.getChannelConfig() != null && response.getChannelConfig().getConfigContent() != null) {
				return ConfigItemEnum.CHARGING_MERCHANT_BAG_FEES.isMainConfigNoStr(response.getChannelConfig().getConfigContent());
			}
		} catch (Exception e) {
			log.error("queryJdIntegratedMarketingConfig error", e);
		}
		return false;
	}


	private void recordOrderDowngrade(long tenantId, long storeId, ChannelOrderDetail channelOrderDetail) {
		if (channelOrderDetail.hasDownGrade()) {
			log.info("[美团订单降级], tenantId:{}, storeId:{}, order:{}, incmp:{}", tenantId, storeId, channelOrderDetail.getOrder_id(), channelOrderDetail.getInvoice_title());
			MetricHelper.build().name("order.downgrade").tag("channel", "mt").count();
		}

		Optional.ofNullable(channelOrderDetail.getIncmp_modules()).map(List::stream).orElse(Stream.empty()).forEach(incmp -> {
			MetricHelper.build().name("order.downgrade.mt.module").tag("module", String.valueOf(incmp)).count();
		});
	}

	private void recordSkuIdAppFoodCodeNotEqual(long tenantId, Long storeId, ChannelOrderDetail channelOrderDetail, List<OrderSkuDetail> orderSkuDetailList) {
		Optional.ofNullable(orderSkuDetailList).map(List::stream).orElse(Stream.empty()).forEach(skuDetail -> {
			if (!StringUtils.equals(skuDetail.getApp_food_code(), skuDetail.getSku_id())) {
				log.info("[appFoodCodeVSSkuId不等], tenantId:{}, storeId:{}, order:{}, skuDetail:{}", tenantId, storeId, channelOrderDetail.getOrder_id(), skuDetail);
			}
		});
	}

	private GetChannelOrderDetailResult orderDetailAnalysis(long tenantId, int channelId, long appId, long storeId, Map<String, Object> result,
	                                                        GetChannelOrderDetailResult orderDetailResult) {
		//返回数据反序列化
		ChannelOrderDetailResponse orderDetailResponse = JSON.parseObject(JSON.toJSONString(result),
				ChannelOrderDetailResponse.class);
		if (Objects.isNull(orderDetailResponse) || StringUtils.isBlank(orderDetailResponse.getData())) {
			return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "订单详情数据反序列化失败"));
		}
		//返回状态校验
		if (ProjectConstant.NG.equals(orderDetailResponse.getData())) {
			MtErrorStatus error = orderDetailResponse.getError();
			if (Objects.isNull(error)) {
				return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST));
			}
			return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.ORDER_NOT_EXIST, error.getMsg()));
		}
		//返回数据映射
		if (MccConfigUtil.isDecryptSensitiveInfoTenant(tenantId)) {
			String orderDetailResponseDataString = mtBrandChannelOrderSensitiveHelper.pushInfoDecodeSensitiveInfo(tenantId, channelId, appId, storeId, orderDetailResponse.getData());
			if (StringUtils.isNotBlank(orderDetailResponseDataString)) {
				orderDetailResponse.setData(orderDetailResponseDataString);
			}
		}
		ChannelOrderDetailDTO channelOrderDetailDTO = parseOrderForm(tenantId, channelId, orderDetailResponse.getData());
		return orderDetailResult.setStatus(ResultGenerator.genSuccessResult())
				.setChannelOrderDetail(channelOrderDetailDTO);
	}

    /**
     * 构建订单详情中商家电话和售后电话
     *
     * @param orderDeliveryDetailDTO
     * @param gatherWmPoiPhones
     */
    private void buildGatherPhones(OrderDeliveryDetailDTO orderDeliveryDetailDTO,
            List<ChannelOrderDetail.GatherWmPoiPhones> gatherWmPoiPhones) {
        if (Objects.isNull(orderDeliveryDetailDTO) || CollectionUtils.isEmpty(gatherWmPoiPhones)) {
            return;
        }
        try {
            Map<String, String> mtGatherPhoneType = MccConfigUtil.getMtGatherPhoneType();
            String gatherPoiPhoneKey = mtGatherPhoneType.get("gatherPoiPhone");
            String gatherAfterSalesPhoneKey = mtGatherPhoneType.get("gatherAfterSalesPhone");
            String gatherPreSalePhoneKey = mtGatherPhoneType.get("gatherPreSalePhone");
            // 保存每种类型的其中一个号码
            gatherWmPoiPhones.forEach(gatherPhones -> {
                String gatherPoiPhoneType = gatherPhones.getGather_poi_phone_type();
                if (StringUtils.equals(gatherPoiPhoneType, gatherPoiPhoneKey)) {
                    orderDeliveryDetailDTO.setGatherPoiPhone(gatherPhones.getGather_wm_poi_phone());
                } else if (StringUtils.equals(gatherPoiPhoneType, gatherAfterSalesPhoneKey)) {
                    orderDeliveryDetailDTO.setGatherAfterSalesPhone(gatherPhones.getGather_wm_poi_phone());
                } else if (StringUtils.equals(gatherPoiPhoneType, gatherPreSalePhoneKey)) {
                    orderDeliveryDetailDTO.setGatherPreSalePhone(gatherPhones.getGather_wm_poi_phone());
                } else {
                    // 自定义电话的类型是用户输入的
                    orderDeliveryDetailDTO.setGatherCustomPhone(gatherPhones.getGather_wm_poi_phone());
                }
            });
        } catch (Exception e) {
            log.error("orderDeliveryDetailDTO:{}, gatherWmPoiPhones:{}, buildGatherPhones error",
                    orderDeliveryDetailDTO, gatherWmPoiPhones, e);
        }
    }

	@Override
	public GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request) {
		GetOrderStatusResult resp = new GetOrderStatusResult();
		OrderStatusDTO orderStatusDTO = new OrderStatusDTO().setOrderId(request.getOrderId());
		resp.setOrderStatus(orderStatusDTO);

		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setAppId(request.getAppId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map param = Maps.newHashMap();
		param.put("order_id", request.getOrderId());
		Map viewStatusMap = mtBrandChannelGateService.sendGet(orderStatusUrl, null, baseRequest, param);
		if (MapUtils.isEmpty(viewStatusMap)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询订单状态失败"));
		}
		if ("ng".equals(viewStatusMap.get(ProjectConstant.DATA))) {
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(viewStatusMap.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
		} else {
			Integer mtOrderStatus = JSON.parseObject(String.valueOf(viewStatusMap.get(ProjectConstant.DATA))).getInteger(ProjectConstant.STATUS);
			if (mtOrderStatus == null) {
				return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单状态为空"));
			}
			resp.setStatus(ResultGenerator.genSuccessResult())
					.getOrderStatus().setStatus(OrderStatusConverter.mtOrderStatusMapping(mtOrderStatus));
		}
		return resp;
	}

	@Override
	public ResultStatus agreeRefund(AgreeRefundRequest request) {
		try {
			log.info("MtBrandChannelOrderServiceImpl.agreeRefund request:{}", request);
			//美团仅退款审核接口调整走新接口（退货退款接口）
			if(MccConfigUtil.mtOnlyRefundAuditAdjustSwitch(request.getTenantId()) && !BusinessIdTracer.isDrunkHorseTenant(request.getTenantId())){
				return refundGoods(MTChannelChangeParamUtil.reviewAfterSalesAgree(request));
			}
			//如果是含有返货的仅退款
			if(Boolean.TRUE.equals(request.isIsReturnGoods())){
				return reviewAfterSales(request);
			}

			ChannelResponseDTO resultData = mtBrandChannelGateService.sendPostReturnDto(ChannelPostMTEnum.ORDER_AGREEREFUND, request.getTenantId(),
					request.getChannelId(), mtConverterService.agreeRefund(request), request.getStoreId());

			return dealResult(resultData);
		} catch (Exception e) {
			log.error("elmChannelOrderService agreeRefund ERROR!", e);
			return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
		}
	}

	private ResultStatus reviewAfterSales(AgreeRefundRequest request) {
		log.info("MtBrandChannelOrderServiceImpl.reviewAfterSales request: {}", request);
		RefundGoodsRequest refundParams = new RefundGoodsRequest();
		refundParams.setTenantId(request.getTenantId());
		refundParams.setStoreId(request.getStoreId());
		refundParams.setOrderId(request.getOrderId());
		refundParams.setIsReturnGoods(request.isIsReturnGoods());
		//只会审核通过的情况走该逻辑
		refundParams.setReason(request.getReason());
		refundParams.setReviewType(AfsReviewTypeEnum.AGREE_REFUND.getValue());
		return refundGoods(refundParams);

	}

	private ResultStatus dealResult(ChannelResponseDTO resultData) {
		if (ProjectConstant.NG.equals(resultData.getData())) {
			return ResultGenerator.genFailResult(resultData.getErrorMsg());
		}
		return ResultGenerator.genSuccessResult();
	}

	@Override
	public ResultStatus rejectRefund(RejectRefundRequest request) {
		try {
			log.info("MtBrandChannelOrderServiceImpl.rejectRefund request:{}", request);
			//美团仅退款审核接口调整走新接口（退货退款接口）,歪马租户维持老逻辑
			if(MccConfigUtil.mtOnlyRefundAuditAdjustSwitch(request.getTenantId()) && !BusinessIdTracer.isDrunkHorseTenant(request.getTenantId())){
				return refundGoods(MTChannelChangeParamUtil.reviewAfterSalesReject(request));
			}
			ChannelResponseDTO resultData = mtBrandChannelGateService.sendPostReturnDto(ChannelPostMTEnum.ORDER_REJECTREFUND, request.getTenantId(),
					request.getChannelId(), mtConverterService.rejectRefund(request), request.getStoreId());

			return dealResult(resultData);
		} catch (Exception e) {
			log.error("elmChannelOrderService rejectRefund ERROR!", e);
			return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
		}
	}

	@Override
	public ResultStatus refundGoods(RefundGoodsRequest request) {
		GetLogisticsStatusResult resp = new GetLogisticsStatusResult();
			BaseRequest baseRequest = new BaseRequest()
					.setChannelId(ChannelTypeEnum.MEITUAN.getCode())
					.setTenantId(request.getTenantId());
			if(request.getStoreId() > NumberUtils.LONG_ZERO){
				baseRequest.setStoreIdList(ImmutableList.of(request.getStoreId()));
			}
			Map<String, Object> param = new HashMap<>();
			param.put("wm_order_id_view", request.getOrderId());
			param.put("reject_reason_code", request.getReasonCode());
			param.put("review_type", request.getReviewType());
			//拒绝时需要校验拒绝原因参数
		    String checkReasonParamIsErrorTips = MTChannelChangeParamUtil.checkReasonParam(request.getReason(), String.valueOf(request.getReasonCode()), request.getReviewType(), request.getTenantId());
		    if(StringUtils.isNotBlank(checkReasonParamIsErrorTips)){
		    	return ResultGenerator.genResult(ResultCodeEnum.INVALID_PARAM_CUSTOM_TIPS, checkReasonParamIsErrorTips);
		    }
			if(StringUtils.isNotBlank(request.getReason())){
				String reason = request.getReason().replace("\n", "").replace("\r", "").replaceAll(" ", "");
				param.put("reject_other_reason", reason);
			}
			// 需要返货时
			if(Boolean.TRUE.equals(request.isIsReturnGoods())){
				param.put("is_return_goods", ReturnGoodsEnum.RETURN_GOODS.getValue());
			}
			Map<String, Object> getResult = mtBrandChannelGateService.sendGet(reviewAfterSalesUrl, null, baseRequest, param);

			log.info("MtChannelOrderServiceImpl.refundGoods, request:{}, resultMap:{}", request, getResult);
		if (MapUtils.isEmpty(getResult)) {
			return ResultGenerator.genFailResult("调用渠道退货退款审核接口失败");
		}
		ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
		if (Objects.isNull(channelResponseDTO)) {
			return ResultGenerator.genFailResult("调用渠道退货退款审核接口返回数据解析失败");
		}
		if (channelResponseDTO.isSuccess()) {
			return ResultGenerator.genSuccessResult();
		}
		return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
	}

	@Override
	public ResultStatus poiCancelOrder(PoiCancelOrderRequest request) {
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
		if(request.getStoreId() > NumberUtils.LONG_ZERO){
			baseRequest.setStoreIdList(ImmutableList.of(request.getStoreId()));
		}
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		bizParam.put(ProjectConstant.ORDER_REASON, request.getReason());
		TenantCancelOrderReasonEnum tenantCancelOrderReasonEnum = TenantCancelOrderReasonEnum.findByValue(request.getReason_code());
		int mtCancelReason = OrderRefundCodeConventer.mtRefundCodeMapping(tenantCancelOrderReasonEnum);
		bizParam.put(ProjectConstant.ORDER_REASON_CODE, String.valueOf(mtCancelReason));
		Map<String, Object> getResult = mtBrandChannelGateService.sendEncodedGet(orderCancelUrl, null, baseRequest, bizParam);
		log.info("MtChannelOrderServiceImpl.poiCancelOrder, request:{}, getResult:{}", request, getResult);
		if (MapUtils.isEmpty(getResult)) {
			return ResultGenerator.genFailResult("调用渠道商家取消订单失败");
		}
		ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
		if (Objects.isNull(channelResponseDTO)) {
			return ResultGenerator.genFailResult("调用渠道商家取消订单返回数据解析失败");
		}
		if (channelResponseDTO.isSuccess()) {
			return ResultGenerator.genSuccessResult();
		}
		return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
	}

	@Override
	public CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request) {
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map<String, Object> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		bizParam.put(ProjectConstant.APP_FOOD_CODE, request.getAppFoodCode());
		bizParam.put(ProjectConstant.ACTUAL_WEIGHT, request.getActualWeight());
		bizParam.put(ProjectConstant.SKU_ID, request.getSkuId());
		if(StringUtils.isNotBlank(request.getItemId())){
			bizParam.put(ProjectConstant.ITEM_ID, request.getItemId());
		}
		Map<String, Object> resultMap = mtBrandChannelGateService.sendGet(weightRefundCalculate, null, baseRequest, bizParam);

		log.info("poiPartRefundCalculate, request:{}, getResult:{}", request, resultMap);
		if (MapUtils.isEmpty(resultMap)) {
			return buildCalculateResult(ResultGenerator.genResult(ResultCode.UNKNOWN_ERROR), null);
		}
		if (Objects.nonNull(resultMap.get(ProjectConstant.RESULT_CODE))
				&& CALCULATE_SUCCESS == (int) resultMap.get(ProjectConstant.RESULT_CODE)
				&& Objects.nonNull(resultMap.get(ProjectConstant.DATA))) {

			ChannelWeightRefundDTO dto =
					JSON.parseObject(resultMap.get(ProjectConstant.DATA).toString(), ChannelWeightRefundDTO.class);
			return buildCalculateResult(ResultGenerator.genSuccessResult(), dto);

		} else {
			String msg = ResultCode.UNKNOWN_ERROR.getMsg();
			if (Objects.nonNull(resultMap.get(ProjectConstant.MT_ERROR_RESPONSE_ERROR_LIST_KEY))) {
				List<ChannelResponseDTO.ChannelResponseError> errors = JSON.parseArray(resultMap.get(ProjectConstant.MT_ERROR_RESPONSE_ERROR_LIST_KEY).toString(), ChannelResponseDTO.ChannelResponseError.class);
				if (CollectionUtils.isNotEmpty(errors)) {
					msg = errors.get(0).getMsg();
				}
			}
			return buildCalculateResult(ResultGenerator.genFailResult(msg), null);
		}
	}

	@Override
	public ResultStatus poiPartRefundApply(PoiPartRefundRequest request) {
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map<String, Object> bizParam = Maps.newHashMap();
		//克重退款
		if (ProjectConstant.MT_WEIGHT_REFUND_TYPE == request.getPartRefundType()) {
			bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
			bizParam.put(ProjectConstant.ORDER_REASON, request.getReason());
			bizParam.put(ProjectConstant.PART_REFUND_TYPE, request.getPartRefundType());
			List<?> refundProducts = request.getRefundProducts().stream().map(MtRefundFoodDomain::fromDTO).map(refundProductInfoDTO -> {
				Map<String, Object> map = Maps.newHashMap();
				map.put("actual_weight", refundProductInfoDTO.getActualWeight());
				map.put("app_food_code", refundProductInfoDTO.getAppFoodCode());

				if (StringUtils.isNotEmpty(refundProductInfoDTO.getSku_Id())) {
					map.put("sku_id", refundProductInfoDTO.getSku_Id());
				}

				if (refundProductInfoDTO.getItemId() != 0) {
					// 有item_id就不传其他参数了
					map.put("item_id", refundProductInfoDTO.getItemId());
				}

				return map;
			}).collect(Collectors.toList());
			bizParam.put(ProjectConstant.FOOD_DATA, JSON.toJSONString(refundProducts));
		}
		//部件退款
		else {
			bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
			bizParam.put(ProjectConstant.ORDER_REASON, request.getReason());

			List<?> refundProducts = request.getRefundProducts().stream().map(MtRefundFoodDomain::fromDTO).map(refundProductInfoDTO -> {
				Map<String, Object> map = Maps.newHashMap();
				map.put("count", refundProductInfoDTO.getCount());
				map.put("app_food_code", refundProductInfoDTO.getAppFoodCode());

				if (StringUtils.isNotEmpty(refundProductInfoDTO.getSku_Id())) {
					map.put("sku_id", refundProductInfoDTO.getSku_Id());
				}

				if (refundProductInfoDTO.getItemId() != 0) {
					// 有item_id就不传其他参数了
					map.put("item_id", refundProductInfoDTO.getItemId());
				}
				return map;
			}).collect(Collectors.toList());
			bizParam.put(ProjectConstant.FOOD_DATA, JSON.toJSONString(refundProducts));
		}
		ChannelResponseDTO channelResponseDTO = mtBrandChannelGateService.sendPostReturnDto(ChannelPostMTEnum.POI_PART_REFUND_APPLY, baseRequest,
				bizParam);

		log.info("MtChannelOrderServiceImpl.poiPartRefundApply, request:{}, getResult:{}", request, channelResponseDTO);
		if (Objects.isNull(channelResponseDTO)) {
			return ResultGenerator.genFailResult("商家发起部分退款失败,返回结果为空");
		}
		if (channelResponseDTO.isSuccess()) {
			return ResultGenerator.genSuccessResult();
		}
		return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());


	}

	@Override
	public ResultStatus preparationMealComplete(PreparationMealCompleteRequest request) {
		return preparationMealCompleteWithMQ(request, true);
	}
	public ResultStatus preparationMealCompleteWithMQ(PreparationMealCompleteRequest request, boolean sendMQ) {
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map<String, Object> params = new HashMap<>();
		params.put("order_id", request.getOrderId());
		Map<String, Object> resultMap = mtBrandChannelGateService.sendGet(preparationMealCompleteUrl, null, baseRequest, params);
		log.info("MtChannelOrderServiceImpl.preparationMealComplete, request:{}, resultMap:{}", request, resultMap);
		if (Objects.isNull(resultMap)) {
			return ResultGenerator.genResult(ResultCode.FAIL, "调用渠道出餐完成上报失败");
		}
		ChannelOrderDetailResponse orderDetailResponse = JSON.parseObject(JSON.toJSONString(resultMap), ChannelOrderDetailResponse.class);
		if (Objects.isNull(orderDetailResponse) || StringUtils.isBlank(orderDetailResponse.getData())) {
			return ResultGenerator.genResult(ResultCode.FAIL, "数据反序列化失败");
		}
		//返回状态校验
		if (ProjectConstant.NG.equals(orderDetailResponse.getData())) {
			MtErrorStatus error = orderDetailResponse.getError();
			if (Objects.nonNull(error) && ResultCode.REPEAT_MEAL_COMPLETE.getCode() == error.getCode()) {
				//重复出餐和xx秒内不能出餐都是808状态码
				//xx秒内不能出餐走异步mq消费重试
				if (checkCompletePickNeedRetry(error.getMsg())) {
					if (sendMQ) {
						log.info("发送拣货完成重试MQ");
						prepareCompleteMessageProducer.sendMessage(buildPrepareCompleteMessage(request));
						return ResultGenerator.genSuccessResult();
					} else {
						//MQ重试期间返回失败状态码，避免重复发送MQ
						return ResultGenerator.genFailResult(error.getMsg());
					}
				}
				return ResultGenerator.genResult(ResultCode.MEAL_COMPLETE);
			}
			return ResultGenerator.genFailResult(error.getMsg());
		}
		return ResultGenerator.genSuccessResult();
	}

	private boolean checkCompletePickNeedRetry(String errorMsg){
		List<String> completePickNeedRetryMsgs = MccConfigUtil.completePickNeedRetryMsg();
		for (String completePickNeedRetryMsg : completePickNeedRetryMsgs) {
			if (errorMsg.contains(completePickNeedRetryMsg)) {
				return true;
			}
		}
		return false;
	}

	private MtBrandPrepareCompleteMessage buildPrepareCompleteMessage(PreparationMealCompleteRequest request) {
		return MtBrandPrepareCompleteMessage.builder()
				.channelId(request.getChannelId())
				.tenantId(request.getTenantId())
				.shopId(request.getStoreId())
				.orderId(request.getOrderId())
				.build();
	}


	@Override
	public ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request) {
		return ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE);
	}

	@Override
	public PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request) {
		PoiAdjustOrderResult result = new PoiAdjustOrderResult();
		return result.setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
	}

	@Override
	public OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request) {
		OrderShouldSettlementResult result = new OrderShouldSettlementResult();
		return result.setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
	}

	@Override
	public GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request) {
		GetChannelOrderDetailRequest orderDetailRequest = new GetChannelOrderDetailRequest();
		orderDetailRequest.setTenantId(request.getTenantId());
		orderDetailRequest.setChannelId(request.getChannelId());
		orderDetailRequest.setOrderId(request.getOrderId());
		GetChannelOrderDetailResult orderDetailResult = getChannelOrderDetail(orderDetailRequest);
		GoodsSettlementResult result = new GoodsSettlementResult();
		if (Objects.nonNull(orderDetailResult) && orderDetailResult.getStatus().getCode() == ResultCode.SUCCESS.getCode()) {
			List<GoodsActivityDetailDTO> goodsActivityDetailDTOList = orderDetailResult.getChannelOrderDetail().getSkuSharedActivities();
			result.setStatus(ResultGenerator.genSuccessResult());
			result.setGoodSettlementInfos(goodsActivityDetailDTOList);
		} else {
			result.setStatus(orderDetailResult.getStatus());
		}
		return result;
	}

	@Override
	public GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request) {
		GetLogisticsStatusResult resp = new GetLogisticsStatusResult();


		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map param = Maps.newHashMap();
		param.put("order_id", request.getOrderId());
		Map viewStatusMap = mtBrandChannelGateService.sendGet(logisticStatusUrl, null, baseRequest, param);
		if (MapUtils.isEmpty(viewStatusMap)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询订单状态失败"));
		}
		if ("ng".equals(viewStatusMap.get(ProjectConstant.DATA))) {
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(viewStatusMap.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
		} else {
			MtLogisticsStatus data = JSON.parseObject(String.valueOf(viewStatusMap.get(ProjectConstant.DATA)), MtLogisticsStatus.class);

			if (data == null) {
				return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单状态为空"));
			}
			LogisticsStatusDTO orderStatusDTO = mtConverterService.convertLogisticsStatus(data);
			orderStatusDTO.setOrderId(request.getOrderId());
			resp.setStatus(ResultGenerator.genSuccessResult())
					.setLogisticsStatus(orderStatusDTO);
		}
		return resp;
	}


	@Override
	public GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request) {
		GetOrderAfsApplyListResult resp = new GetOrderAfsApplyListResult();

		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelType())
				.setTenantId(request.getTenantId())
                .setAppId(request.getAppId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map param = Maps.newHashMap();
		param.put("wm_order_id_view", request.getChannelOrderId());
		Map resultMap = mtBrandChannelGateService.sendGet(orderAfsApplyListUrl, null, baseRequest, param);
		//查询地址变更退费
		String refundAddressChangeFee = getRefundAddressChangeFee(baseRequest, request.getChannelOrderId());
		if (MapUtils.isEmpty(resultMap)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询订单售后申请失败"));
		}
		if ("ng".equals(resultMap.get(ProjectConstant.DATA))) {
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(resultMap.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
		} else {
			boolean isUseSuccessList = MccConfigUtil.checkSupportMtChangeAfsApplyListField(request.getTenantId());
			String dataSource = isUseSuccessList ? ProjectConstant.SUCCESS_LIST : ProjectConstant.DATA;
			List<MtOrderAfsApplyDTO> afsApplyDTOs = JSON.parseArray(String.valueOf(resultMap.get(dataSource)), MtOrderAfsApplyDTO.class);
			List<OrderAfsApplyDTO> orderAfsApplyList = mtConverterService.convertAfsApplyDTOs(afsApplyDTOs);
			if (StringUtils.isNotBlank(request.getAfterSaleId())) {
				orderAfsApplyList = orderAfsApplyList.stream().filter(e -> StringUtils.equals(e.getAfterSaleId(), request.getAfterSaleId())).collect(Collectors.toList());
			}
			// 字段比较
			if(isUseSuccessList){
				compareAfsField(request.getTenantId(), request.getAfterSaleId(), resultMap, orderAfsApplyList);
			}
			if(!MccConfigUtil.getMtUseRefundNewTrackTenantsSwitch(request.getTenantId())){
				// 去除优惠数据
				clearRefundPromotion(orderAfsApplyList);
			}
			// mock 代码
			//mockMtRefundDeductUserShippingFee(orderAfsApplyList, request.getTenantId());
			resp.setStatus(ResultGenerator.genSuccessResult()).setAfsApplyList(orderAfsApplyList);
			if(StringUtils.isNotBlank(refundAddressChangeFee)){
				resp.setAddressChangeFee(refundAddressChangeFee);
			}
		}
		return resp;
	}

	// mock代码
	void mockMtRefundDeductUserShippingFee(List<OrderAfsApplyDTO> orderAfsApplyList,long tenantId){
		MockMtOrderAndRefundDTO mockMtOrderAndRefundDTO = MccConfigUtil.mockMtOrderAndRefund(tenantId);
		if(mockMtOrderAndRefundDTO != null
				&& mockMtOrderAndRefundDTO.getDeductUserShippingFee() != null
				&& mockMtOrderAndRefundDTO.getDeductUserShippingFee() > 0
		){
			for (OrderAfsApplyDTO orderAfsApplyDTO : orderAfsApplyList) {
				if(orderAfsApplyDTO.getRefundType() == 1){
					orderAfsApplyDTO.setCostCustomerFreightFee(mockMtOrderAndRefundDTO.getDeductUserShippingFee());
				}
			}
		}
	}

	/**
	 * 只比较过滤后的数据
	 * @param resultMap
	 * @param successList
	 */
	public void compareAfsField(Long tenantId, String afterSaleId, Map resultMap, List<OrderAfsApplyDTO> successList){
		try {
			Map<String, List<String>> configMap = getMtAfsApplyListFieldCompareConfigByTenant(tenantId);
			// 租户不支持
			if(MapUtils.isEmpty(configMap)){
				return;
			}
			// data数据转换
			List<MtOrderAfsApplyDTO> afsApplyDTOs = JSON.parseArray(String.valueOf(resultMap.get(ProjectConstant.DATA)), MtOrderAfsApplyDTO.class);
			List<OrderAfsApplyDTO> dataList = mtConverterService.convertAfsApplyDTOs(afsApplyDTOs);
			// 数据过滤
			if (StringUtils.isNotBlank(afterSaleId)) {
				dataList = dataList.stream().filter(e -> StringUtils.equals(e.getAfterSaleId(), afterSaleId)).collect(Collectors.toList());
			}
			// 数据比较
			mtAfsFieldCompareMessageProducer.sendMessage(new MtAfsFieldCompareMessage(JSON.toJSONString(dataList), JSON.toJSONString(successList), configMap.get("ignoreFieldList"), configMap.get("compareItemKeyList")));
		}catch (Exception e){
			log.error("compareAfsField is error! resultMap:{}", JSON.toJSONString(resultMap), e);
		}
	}

	/**
	 * 获取租户比对字段配置
	 * {"tenantIdList":["1000094","1000099"],"ignoreFieldList":["firstAutoNegoType"],"compareItemKeyList":["afterSaleId"]}
	 * @param tenantId
	 * @return
	 */
	private Map<String, List<String>> getMtAfsApplyListFieldCompareConfigByTenant(Long tenantId){
		try{
			if(tenantId == null || tenantId <= 0L){
				return null;
			}
			String configStr = MccConfigUtil.getMtAfsApplyListFieldCompareConfig();
			if(StringUtils.isEmpty(configStr)){
				return null;
			}
			Map<String, List<String>> configMap = JSON.parseObject(configStr, new TypeReference<Map<String, List<String>>>(){}.getType());
			if(MapUtils.isEmpty(configMap)){
				return null;
			}
			List<String> tenantIds = configMap.get("tenantIdList");
			boolean isSupportTenant = CollectionUtils.isNotEmpty(tenantIds) && (org.apache.commons.lang.StringUtils.equalsIgnoreCase("ALL", tenantIds.get(0)) || tenantIds.contains(String.valueOf(tenantId)));
			return isSupportTenant ? configMap : null;
		}catch (Exception e) {
			log.error("获取租户比对字段配置异常，不影响主流程！", e);
		}
		return null;

	}


	/**
	 * 清空优惠信息
	 * @param orderAfsApplyList
	 */
	private void clearRefundPromotion(List<OrderAfsApplyDTO> orderAfsApplyList){
		// 旧逻辑由自己计算
		if(CollectionUtils.isNotEmpty(orderAfsApplyList)){
			for (OrderAfsApplyDTO orderAfsApplyDTO : orderAfsApplyList) {
				if(CollectionUtils.isNotEmpty(orderAfsApplyDTO.getAfsProductList())){
					for (RefundProductDTO refundProductDTO : orderAfsApplyDTO.getAfsProductList()) {
						refundProductDTO.setRefundPlatOrderPromotion(0);
						refundProductDTO.setRefundPoiOrderPromotion(0);
						refundProductDTO.setRefundPlatItemPromotion(0);
						refundProductDTO.setRefundPoiItemPromotion(0);
					}
				}
			}
		}
	}

	private String getRefundAddressChangeFee(BaseRequest baseRequest, String channelOrderId) {
		try {
			GetRefundAddressChangeFeeRequest changeFeeRequest = new GetRefundAddressChangeFeeRequest();
			changeFeeRequest.setChannelId(baseRequest.getChannelId());
			changeFeeRequest.setOrderId(channelOrderId);
			changeFeeRequest.setAppId(baseRequest.getAppId());
			changeFeeRequest.setTenantId(baseRequest.getTenantId());
			changeFeeRequest.setStoreIdList(baseRequest.getStoreIdList());

			RefundAddressChangeFeeDTO refundAddressChangeFeeInfo = getRefundAddressChangeFeeInfo(changeFeeRequest);
			if(Objects.nonNull(refundAddressChangeFeeInfo) && StringUtils.isNotBlank(refundAddressChangeFeeInfo.getMoney())){
				return refundAddressChangeFeeInfo.getMoney();
			}
		} catch (Exception e) {
			log.error("MtChannelOrderServiceImpl.getRefundAddressChangeFee is error viewOrderId: {}, e= ", channelOrderId, e);
		}
		return null;
	}

	@Override
	public ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request) {
		return new ResultStatus()
				.setCode(ResultCode.SUCCESS.getCode())
				.setMsg(ResultCode.SUCCESS.getMsg());
	}

	@Override
	public ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request) {
		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
		addStoreId2BaseRequest(request.getShopId(), baseRequest);
		ResultStatus resultStatus = new ResultStatus();
		Map<String, Object> param = buildBusinessParam(request);
		if (Objects.isNull(param)) {
			log.warn("更新配送信息条件不足，不更新，直接返回成功:{}", request);
			resultStatus.setCode(ResultCode.SUCCESS.getCode());
			resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
			return resultStatus;
		}
		String url = innerNetOrderLogisticsSyncUrl;
		//跟账单接口的Base URL保持一致
		boolean switchMTBillInnerNetUrl = MccConfigUtil.switchMTBillInnerNetUrl();
		if (!switchMTBillInnerNetUrl) {
			url = orderLogisticsSyncUrl;
		}
		log.info("MtChannelOrderServiceImpl.updateDeliveryInfo  调用美团接口 baseRequest:{}   param:{}", baseRequest, param);
		Map result = mtBrandChannelGateService.sendPost(url, null, baseRequest, param);
		log.info("MtChannelOrderServiceImpl.updateDeliveryInfo  调用美团接口返回:{}", result);

		if (Objects.isNull(result)) {
			resultStatus.setCode(ResultCode.FAIL.getCode());
			resultStatus.setMsg(ResultCode.FAIL.getMsg());
		} else if (ProjectConstant.NG.equals(result.get(ProjectConstant.DATA))) {
			updateDeliveryInfoNG(result, resultStatus);
		} else {
			resultStatus.setCode(ResultCode.SUCCESS.getCode());
			resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
		}
		return resultStatus;
	}

	/**
	 * 更新配送信息NG处理
	 */
	private void updateDeliveryInfoNG(Map result, ResultStatus resultStatus) {
		try {
			resultStatus.setCode(ResultCode.FAIL.getCode());
			resultStatus.setMsg(ResultCode.FAIL.getMsg());
			Object errList = result.get(ProjectConstant.ERROR_LIST);
			if (errList == null) {
				return;
			}

			List<MtErrorStatus> mtErrorStatusList = JSON.parseArray(String.valueOf(errList), MtErrorStatus.class);
			if (CollectionUtils.isEmpty(mtErrorStatusList)) {
				return;
			}

			List<String> mtLogisticsFilterCodes = MccConfigUtil.getMtLogisticsFilterCodes();
			if (CollectionUtils.isEmpty(mtLogisticsFilterCodes)) {
				return;
			}
			for (MtErrorStatus mtErrorStatus : mtErrorStatusList) {
				if (mtLogisticsFilterCodes.contains(String.valueOf(mtErrorStatus.getCode()))) {
					resultStatus.setCode(ResultCode.SUCCESS.getCode());
					resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
					return;
				}
			}
		} catch (Exception e) {
			log.info("MtChannelOrderServiceImpl.updateDeliveryInfoNG  error:{}", e);
		}
	}

	@Override
	public ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request) {
		return updateDeliveryInfo(request);
	}

	@Override
	public ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request) {
		return new ResultStatus(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(), ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg(), null);
	}

	@Override
	public ResultStatus selfDelivery(SelfDeliveryRequest request) {

		BizOrderModel bizOrderModel=queryOrderInfo(request.getTenantId(),request.getStoreId(),request.getOrderId());
		if(bizOrderModel==null){
			return ResultGenerator.genFailResult("查询订单为空");
		}

		if(bizOrderModel.getDeliveryModel()!=null && bizOrderModel.getDeliveryModel().getOriginalDistributeType()!=null && bizOrderModel.getDeliveryModel().getOriginalDistributeType()== DistributeTypeEnum.ZONG_BAO.getValue()){
			FarmPaotuiCancelDeliveryInnerRequest cancelDeliveryInnerRequest=new FarmPaotuiCancelDeliveryInnerRequest(request.getOrderId(),request.getTenantId(),bizOrderModel.getShopId());
			FarmPaotuiCancelInnerResponse response = paoTuiInnerThriftService.cancelDelivery(cancelDeliveryInnerRequest);
			if(response.getCode() == 0){
				return ResultGenerator.genSuccessResult();
			}
			if(MccConfigUtil.checkSelfErrorMsgToSuc(response.getMsg())){
				return ResultGenerator.genSuccessResult();
			}
			return ResultGenerator.genFailResult(response.getMsg());
		}

		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
		addStoreId2BaseRequest(bizOrderModel.getShopId(), baseRequest);
		Map param = Maps.newHashMap();
		param.put("order_id", request.getOrderId());
		log.info("MtChannelOrderServiceImpl.selfDelivery  调用美团接口 baseRequest:{}   param:{}", baseRequest, param);
		Map result = mtBrandChannelGateService.sendPost(orderLogisticsChangePoiSelfUrl, null, baseRequest, param);
		log.info("MtChannelOrderServiceImpl.selfDelivery  调用美团接口返回:{}", result);

		if (MapUtils.isEmpty(result)) {
			return ResultGenerator.genFailResult("调用渠道切换商家自配送失败");
		}
		ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(result), ChannelResponseDTO.class);
		if (Objects.isNull(channelResponseDTO)) {
			return ResultGenerator.genFailResult("调用渠道切换商家自配送失败解析失败");
		}
		if (channelResponseDTO.isSuccess()) {
			return ResultGenerator.genSuccessResult();
		}
		if(MccConfigUtil.checkSelfErrorMsgToSuc(channelResponseDTO.getErrorMsg())){
			return ResultGenerator.genSuccessResult();
		}
		return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
	}

	private BizOrderModel queryOrderInfo(Long tenantId,Long storeId, String viewOrderId){
		try {
			BizOrderQueryByViewOrderIdRequest request = new BizOrderQueryByViewOrderIdRequest(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue(), viewOrderId,tenantId,storeId);
			log.info("BizOrderThriftService.queryByViewOrderId begin, request={}", request);
			BizOrderQueryResponse response = bizOrderThriftServiceClient.queryByViewOrderId(request);
			if(response.getBizOrderModel()!=null){
				return response.getBizOrderModel();
			}
			return null;

		} catch (Exception e) {
			log.error("Call BizOrderThriftService.queryByOrderId failed tenantId:{},orderId:{}",tenantId,viewOrderId,e);
			return null;
		}
	}

	@Override
	public QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request) {
		return null;
	}

	@Override
	public ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request) {
		Map param = Maps.newHashMap();
		param.put("order_id", request.getOrderId());
		BaseRequest baseRequest = new BaseRequest()
				.setTenantId(request.getTenantId())
				.setChannelId(request.getChannelId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		String url = orderGetPartRefundFoods;
		log.info("MtChannelOrderServiceImpl.queryChannelPartRefundGoodsDetail  调用美团接口 baseRequest:{}   param:{}", baseRequest, param);
		Map result = mtBrandChannelGateService.sendGet(url, null, baseRequest, param);
		log.info("MtChannelOrderServiceImpl.queryChannelPartRefundGoodsDetail  调用美团接口返回:{}", result);
		ChannelPartRefundGoodsResult resp = new ChannelPartRefundGoodsResult();
		if (MapUtils.isEmpty(result)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询订单部分退款商品失败"));
		}
		if ("ng".equals(result.get(ProjectConstant.DATA))) {
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(result.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
		} else {
			List<MtPartRefundGoodsDTO> partRefundGoodsDTOList = JSON.parseArray(String.valueOf(result.get(ProjectConstant.DATA)), MtPartRefundGoodsDTO.class);

			if (CollectionUtils.isEmpty(partRefundGoodsDTOList)) {
				return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单部分退款商品详情为空"));
			}

			List<PartRefundGoodDetailDTO> partRefundGoodDetailDTOList = mtConverterService.convertPartRefundGoodDetailDTOs(partRefundGoodsDTOList);
			resp.setStatus(ResultGenerator.genSuccessResult())
					.setPartRefundGoodsList(partRefundGoodDetailDTOList);
		}
		return resp;
	}

	@Override
	public QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request) {
		Map param = Maps.newHashMap();
		param.put("start_time", request.getStartTime()/1000);
		param.put("end_time", request.getEndTime()/1000);
		if(StringUtils.isNotEmpty(request.getVernier())){
			param.put("vernier", request.getVernier());
		}
		param.put("page_size", request.getPageSize());
		param.put("app_poi_code", request.getChannelPoiId());
		BaseRequest baseRequest = new BaseRequest()
				.setTenantId(request.getTenantId())
				.setChannelId(request.getChannelId());
		addStoreId2BaseRequest(request.getShopId(), baseRequest);
		String url = getOrderIdByPage;
		log.info("MtBrandChannelOrderServiceImpl.queryChannelOrderList  调用美团接口 baseRequest:{}   param:{}", baseRequest, param);
		Map result = mtBrandChannelGateService.sendPost(url, null, baseRequest, param);
		log.info("MtBrandChannelOrderServiceImpl.queryChannelOrderList  调用美团接口返回:{}", result);
		QueryChannelOrderListResult queryChannelOrderListResult = new QueryChannelOrderListResult();
		if (MapUtils.isEmpty(result)) {
			return queryChannelOrderListResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "批量查询门店订单号"));
		}
		OrderIdsByPageDTO orderIdsByPageDTO = JSON.parseObject(JSON.toJSONString(result), OrderIdsByPageDTO.class);
		if (orderIdsByPageDTO.getResult_code() == null || orderIdsByPageDTO.getResult_code() != 1) {
			return queryChannelOrderListResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "批量查询门店订单号"));
		}

		if (orderIdsByPageDTO.getSuccess_map() != null) {
			queryChannelOrderListResult.setVernier(orderIdsByPageDTO.getSuccess_map().getVernier());
			queryChannelOrderListResult.setHasMore(orderIdsByPageDTO.getSuccess_map().getHasMore());
		}
		if(CollectionUtils.isNotEmpty(orderIdsByPageDTO.getSuccess_list())){
			queryChannelOrderListResult.setChannelOrderIdList(orderIdsByPageDTO.getSuccess_list().stream().map(e->e.getOrder_id().toString()).collect(Collectors.toList()));
		}
		queryChannelOrderListResult.setStatus(ResultGenerator.genSuccessResult());

		return queryChannelOrderListResult;
	}

	@Override
	public QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request) {
		Map bizParam = Maps.newHashMap();
		bizParam.put("start_time", request.getStartTime());
		bizParam.put("end_time", request.getEndTime());
		bizParam.put("type", request.getType());
		bizParam.put("offset", request.getOffset());
		bizParam.put("limit", request.getLimit());
		BaseRequest baseRequest = new BaseRequest()
				.setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId())
				.setAppId(request.getAppId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		String url = batchFetchAbnormalOrder;
		log.info("MtChannelOrderServiceImpl.queryChannelAbnormalOrderList  调用美团接口 request:{},  param:{}", request, bizParam);
		Map result = mtBrandChannelGateService.sendPost(url, null, baseRequest, bizParam);
		log.info("MtChannelOrderServiceImpl.queryChannelAbnormalOrderList  调用美团接口返回:{}", result);
		QueryChannelAbnormalOrderResult resp = new QueryChannelAbnormalOrderResult();
		if (MapUtils.isEmpty(result)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询异常订单列表失败"));
		}
		if ("ng".equals(result.get(ProjectConstant.DATA))) {
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(result.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
		} else {
			List<String> abnormalOrderList = JSON.parseArray(String.valueOf(result.get(ProjectConstant.DATA)), String.class);
			resp.setStatus(ResultGenerator.genSuccessResult())
					.setChannelOrderIdList(abnormalOrderList);
		}
		return resp;
	}

	@Override
	public WeightPartRefundGoodsResult queryWeightPartRefundGoodsDetail(WeightPartRefundGoodsRequest request){
		WeightPartRefundGoodsResult resp = new WeightPartRefundGoodsResult();
		//获取渠道订单
		GetChannelOrderDetailRequest detailRequest = new GetChannelOrderDetailRequest();
		detailRequest.setChannelId(request.getChannelId());
		detailRequest.setTenantId(request.getTenantId());
		detailRequest.setOrderId(request.getOrderId());
		detailRequest.setSotreId(request.getStoreId());
		GetChannelOrderDetailResult channelOrderDetailResult = getChannelOrderDetail(detailRequest);
		if (channelOrderDetailResult == null || channelOrderDetailResult.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道订单不存在"));
		}

		//判断是否只有一种商品，且商品数量为1，如果是这种场景，走另外的接口
		if (checkOneItemCount(channelOrderDetailResult)) {
			return queryWeightRefundGoodsDetail(request);
		}

		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		// 目前克重退差查询商品接口使用的是查询可被部分退款的商品详情，没有使用克重退款商品查询接口。
		Map<String, Object> getResult = mtBrandChannelGateService.sendGet(getUnitPartRefundFoods, null, baseRequest, bizParam);
		log.info("MtChannelOrderServiceImpl.queryWeightPartRefundGoodsDetail, request:{}, getResult:{}", request, getResult);
		if (MapUtils.isEmpty(getResult)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询克重退差商品失败"));
		}
		ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
		if (Objects.isNull(channelResponseDTO)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询克重退差商品失败"));
		}

		if ("ng".equals(getResult.get(ProjectConstant.DATA))) {
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(getResult.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
		} else {
			List<MtUnitPartRefundFoodsDTO> resultList = JSON.parseArray(String.valueOf(getResult.get(ProjectConstant.DATA)), MtUnitPartRefundFoodsDTO.class);

			if (CollectionUtils.isEmpty(resultList)) {
				return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单克重退差商品为空"));
			}
			List<MtUnitPartRefundFoodsDTO> unitPartRefundGoodsDTOList = resultList.stream().filter(item -> item.getIs_refund_difference() > 0).collect(Collectors.toList());
			List<WeightPartRefundGoodsDTO> weightPartRefundGoodDTOList = mtConverterService.convertWeightPartRefundGoodDTOs(unitPartRefundGoodsDTOList);
			return resp.setStatus(ResultGenerator.genSuccessResult()).setChannelOrderIdList(weightPartRefundGoodDTOList);
		}
		return resp;
	}

	@Override
	public ChannelOrderMoneyRefundItemResult queryChannelOrderMoneyRefundItemList(ChannelOrderMoneyRefundItemRequest request) {
		Map<String, Object> param = Maps.newHashMap();
		param.put(ProjectConstant.ORDER_ID, request.getOrderId());
		BaseRequest baseRequest = new BaseRequest()
				.setTenantId(request.getTenantId())
				.setChannelId(request.getChannelId())
				.setStoreIdList(Lists.newArrayList(request.getStoreId()));
		log.info("MtChannelOrderServiceImpl.queryChannelOrderMoneyRefundItemList  调用美团接口 baseRequest:{}   param:{}", baseRequest, param);
		Map<String, Object> result = mtBrandChannelGateService.sendGet(getAmountPartRefundFoods, null, baseRequest, param);
		log.info("MtChannelOrderServiceImpl.queryChannelPartRefundGoodsDetail  调用美团接口返回:{}", result);
		ChannelOrderMoneyRefundItemResult resp = new ChannelOrderMoneyRefundItemResult();
		if (MapUtils.isEmpty(result)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询订单部分退款商品失败"));
		}
		if (!Objects.equals(result.get(ProjectConstant.RESULT_CODE), 1)) {
			String errMsg = "";
			Object errList = result.get(ProjectConstant.ERROR_LIST);
			MtErrorStatus errObj = JSON.parseArray(String.valueOf(errList != null ? errList : ""), MtErrorStatus.class)
					.stream().findFirst().orElse(null);
			if (errObj != null) {
				errMsg = errObj.getMsg();
			}
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, errMsg));
		} else {
			Object successList = result.get(SUCCESS_LIST);
			List<MtEcommercePartRefundGoodsDTO> ecommercePartRefundGoodsDTOS = JSON.parseArray(String.valueOf(successList != null ? successList : ""), MtEcommercePartRefundGoodsDTO.class);
			if (CollectionUtils.isEmpty(ecommercePartRefundGoodsDTOS)) {
				return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单部分退款商品详情为空"));
			}
			//查找支持金额退的商品
			for (MtEcommercePartRefundGoodsDTO dto : ecommercePartRefundGoodsDTOS) {
				if (StringUtils.isBlank(dto.getPart_refund_types())) {
					continue;
				}
				List<String> partRefundTypes = Arrays.asList(dto.getPart_refund_types().split(","));
				//不支持金额退返回的可退数量设置为0
				if (!partRefundTypes.contains(ProjectConstant.MT_AMOUNT_REFUND_TYPE)) {
					dto.setCount(0);
				}
			}
			if (CollectionUtils.isEmpty(ecommercePartRefundGoodsDTOS)) {
				return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单部分退款商品详情为空"));
			}
			List<ChannelOrderMoneyRefundItemDTO> resultList = mtConverterService.convertMoneyCheckRefundItemDTOs(ecommercePartRefundGoodsDTOS);
			resp.setMoneyRefundItemDTOList(resultList);
			resp.setStatus(ResultGenerator.genSuccessResult());
		}
		return resp;
	}

	@Override
	public ResultStatus moneyRefund(MoneyRefundRequest request) {
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
				.setTenantId(request.getTenantId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map<String, Object> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID,request.getOrderId());
		bizParam.put(ProjectConstant.ORDER_REASON,request.getReasonRemarks());
		bizParam.put(ProjectConstant.PART_REFUND_TYPE,5);
		List<?> refundProducts = request.getRefundProductInfoList().stream().map(i -> {
			Map<String, Object> map = Maps.newHashMap();
			map.put(ProjectConstant.SKU_ID,i.getCustomSkuId());
			map.put(ProjectConstant.ITEM_ID,i.getChannelOrderItemId());
			map.put(ProjectConstant.COUNT,i.getCount());
			map.put(ProjectConstant.REFUND_PRICE,MoneyUtils.fenToYuan(i.getRefundAmount()));
			map.put(ProjectConstant.MT_APP_SPU_CODE,i.getCustomSpu());
			return map;
		}).collect(Collectors.toList());
		bizParam.put(ProjectConstant.FOOD_DATA,JSON.toJSONString(refundProducts));
		ChannelResponseDTO channelResponseDTO = mtBrandChannelGateService.sendPostReturnDto(ChannelPostMTEnum.POI_PART_REFUND_APPLY, baseRequest,
				bizParam);
		log.info("MtChannelOrderServiceImpl.poiPartRefundApply, request:{}, getResult:{}", request, channelResponseDTO);
		if (Objects.isNull(channelResponseDTO)) {
			return ResultGenerator.genFailResult("商家发起金额部分退款失败,返回结果为空");
		}
		if (channelResponseDTO.isSuccess()) {
			return ResultGenerator.genSuccessResult();
		}
		return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
	}

	private boolean checkOneItemCount(GetChannelOrderDetailResult channelOrderDetailResult) {
		ChannelOrderDetailDTO detailDTO = channelOrderDetailResult.getChannelOrderDetail();
		//美团的赠品不在商品行中，而且活动list里，例外：有erp的商家，赠品会在商品行里
		if (CollectionUtils.isNotEmpty(detailDTO.getSkuDetails())) {
			List<OrderProductDetailDTO> orderProductDetailDTOS =
					detailDTO.getSkuDetails().stream().filter(product -> product.getSalePrice() != 0).collect(Collectors.toList());
			if (orderProductDetailDTOS.size() == 1) {
				OrderProductDetailDTO orderProductDetailDTO = detailDTO.getSkuDetails().get(0);
				if (orderProductDetailDTO.getQuantity() == 1) {
					return true;
				}
			}
		}
		return false;
	}

	private WeightPartRefundGoodsResult queryWeightRefundGoodsDetail(WeightPartRefundGoodsRequest request) {
		WeightPartRefundGoodsResult resp = new WeightPartRefundGoodsResult();
		BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
		addStoreId2BaseRequest(request.getStoreId(), baseRequest);
		Map<String, String> bizParam = Maps.newHashMap();
		bizParam.put(ProjectConstant.ORDER_ID, request.getOrderId());
		Map<String, Object> getResult = mtBrandChannelGateService.sendGet(getUnitPartRefundFoodsV2, null, baseRequest, bizParam);
		log.info("MtChannelOrderServiceImpl.queryWeightRefundGoodsDetail, request:{}, getResult:{}", request, getResult);
		if (MapUtils.isEmpty(getResult)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询单件克重退差商品失败"));
		}
		ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
		if (Objects.isNull(channelResponseDTO)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询单件克重退差商品失败"));
		}

		if ("ng".equals(getResult.get(ProjectConstant.DATA))) {
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(getResult.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
		} else {
			List<MtUnitPartRefundFoodsV2DTO> resultList = JSON.parseArray(String.valueOf(getResult.get(ProjectConstant.DATA)), MtUnitPartRefundFoodsV2DTO.class);
			if (CollectionUtils.isEmpty(resultList)) {
				return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单单件克重退差商品为空"));
			}
			List<MtUnitPartRefundFoodsV2DTO> unitPartRefundFoodsV2DTOList = resultList.stream().filter(item -> item.getSupport_refund_type() < 2).collect(Collectors.toList());
			List<WeightPartRefundGoodsDTO> weightPartRefundGoodDTOList = mtConverterService.convertWeightRefundGoodDTOs(unitPartRefundFoodsV2DTOList);
			return resp.setStatus(ResultGenerator.genSuccessResult()).setChannelOrderIdList(weightPartRefundGoodDTOList);
		}
		return resp;
	}

	private String getPoiPhoneNum(Long tenantId,Long storeId){

		PoiDetailInfoDTO poiDetailInfoDTO = channelPoiThriftServiceProxy.queryPoiDetailInfoByPoiId(tenantId,storeId);
		if(poiDetailInfoDTO == null){
			return "";
		}

		return poiDetailInfoDTO.getPoiPhoneNum();
	}

	private Map<String, Object> buildBusinessParam(UpdateDeliveryInfoRequest request) {
		Map<String, Object> param = Maps.newHashMap();
		param.put("order_id", request.getOrderId());
		if ((StringUtils.isEmpty(request.getRiderName()) || StringUtils.isEmpty(request.getRiderPhone()))) {
			// 解除新供给不同步「骑手待接单」的限制(隔离歪马、医药)
			if (!Objects.equals(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(), request.getStatus())
					|| isDHTenantOrUwmsTenant(request.getTenantId())) {
				if (cancelDelivery(request)){
					log.warn("美团更新配送信息，无骑手姓名和电话");
					return null;
				}
			}
		}
		param.put("courier_name", Optional.ofNullable(request.getRiderName()).orElse(""));
		param.put("courier_phone", Optional.ofNullable(request.getRiderPhone()).orElse(""));
		param.put("phone_type", 0);
		//配送取消时，如果骑手电话为空就回传空
		if (StringUtils.isNotBlank(request.getRiderPhone())) {
			if (MccConfigUtil.usePrivacyPhoneSwitch()) {
				if(!PhoneNumberUtils.isValidElevenNumberMobileNumber(request.getRiderPhone())){
					param.put("phone_type", 1);
					if (isValidPrivacyNumber(request.getRiderPhone())) {
						param.put("courier_phone", Optional.ofNullable(request.getRiderPhone()).orElse(""));
					} else {
						String invalidPrivacySymbol = MccConfigUtil.invalidPrivacySymbol();
						String phoneNumber = request.getRiderPhone();
						phoneNumber = phoneNumber.replaceAll(invalidPrivacySymbol, "_");
						if (isValidPrivacyNumber(phoneNumber)) {
							param.put("courier_phone", phoneNumber);
						} else {
							String poiPhone=getPoiPhoneNum(request.getTenantId(),request.getShopId());
							param.put("courier_phone", poiPhone);
						}

					}
				}
			} else {
				if(MccConfigUtil.deliveryPhoneSwitch()){
					if(!PhoneNumberUtils.isValidElevenNumberMobileNumber(request.getRiderPhone())){
						String poiPhone=getPoiPhoneNum(request.getTenantId(),request.getShopId());
						if(StringUtils.isNotEmpty(poiPhone)){
							param.put("courier_phone", poiPhone);
						}
					}
				}
			}
		}
		try {
			param.put("logistics_status", convertDeliveryStatus(request.getStatus()));
		} catch (ChannelBizException e) {
			log.warn("美团渠道订单更新配送信配送状态转换失败 status:{}", request.getStatus(), e);
			return null;
		}

		param.put("latitude", String.valueOf(request.getLatitude()));
		param.put("longitude", String.valueOf(request.getLongitude()));
		if (request.getDeliveryOrderId() != null && MccConfigUtil.getMtThirdCarrierOrderIdSwitch()){
			param.put("third_carrier_order_id", request.getDeliveryOrderId());
		}
		Integer mtDeliveryChannelCode = DeliveryChannelCodeConvert.getMtDeliveryChannelCode(request.getDeliveryChannelId());
		if(mtDeliveryChannelCode != null){
			param.put("logistics_provider_code", mtDeliveryChannelCode);
		}
		return param;
	}

	private boolean cancelDelivery(UpdateDeliveryInfoRequest request) {
		if (!MccConfigUtil.getCancelDeliverySwitch()) {
			return true;
		}
		if (MccConfigUtil.getDHTenantIdList().contains(String.valueOf(request.getTenantId()))) {
			return true;
		}
		if (!MccConfigUtil.getDeliveryStatusList().contains(String.valueOf(request.getStatus()))) {
			return true;
		}
		return false;
	}

	private boolean isDHTenantOrUwmsTenant(Long tenantId) {
		if (MccConfigUtil.getDHTenantIdList().contains(String.valueOf(tenantId))) {
			return true;
		}
		try {
			return medicineTenantService.getAllUwmsTenantIds().contains(tenantId);
		} catch (Exception e) {
			log.error("medicineTenantService.getAllUwmsTenantIds error: ", e);
			return false;
		}
	}

	private int convertDeliveryStatus(int status) {
		DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(status);
		if (Objects.isNull(deliveryStatus)) {
			log.warn("美团渠道无法处理配送状态:{}", status);
			throw new ChannelBizException("美团渠道无法处理配送状态:" + status);
		}
		switch (deliveryStatus) {
			//已创建配送包裹
			case WAIT_DISPATCH_RIDER:
				return 1;
			//骑手已接单
			case RIDER_ACCEPTED_ORDER:
				return 10;
			//骑手已到店
			case RIDER_ARRIVE_SHOP:
				return 15;
			//骑手已取餐
			case RIDER_TAKEN_MEAL:
				return 20;
			//骑手已送达
			case DELIVERY_COMPLETED:
				return 40;
			//配送单已取消
			case DELIVERY_CANCEL:
				return 100;
			//无法处理，抛出异常
			default:
				throw new ChannelBizException("美团渠道无法处理配送状态:" + status);
		}
	}

	private CalculateRefundResult buildCalculateResult(ResultStatus status, ChannelWeightRefundDTO dto) {
		CalculateRefundResult result = new CalculateRefundResult();
		result.setStatus(status);
		if (Objects.nonNull(dto)) {
			CalculateRefundDTO calculateRefundDTO = new CalculateRefundDTO();
			calculateRefundDTO.setAppFoodCode(dto.getApp_food_code());
			calculateRefundDTO.setRefundMoney(dto.getRefund_money().doubleValue());
			calculateRefundDTO.setSkuId(dto.getSku_id());
			result.setCalculateRefundDTO(calculateRefundDTO);
		}
		return result;
	}

	private boolean isJsonArray(String str) {
		return StringUtils.startsWith(str ,"[") && StringUtils.endsWith(str, "]");

	}

	public void addStoreId2BaseRequest(long storeId, BaseRequest baseRequest){
		if(storeId <= NumberUtils.LONG_ZERO){
			return;
		}
		baseRequest.setStoreIdList(ImmutableList.of(storeId));
	}

	/**
	 * 查询订单赔付信息列表
	 * @param request
	 * @return
	 */
	@Override
	public QueryCompensationOrderListResult queryCompensateOrderList(PoiBatchCompensationRequest request) {
		QueryCompensationOrderListResult queryCompensationOrderListResult = new QueryCompensationOrderListResult();
		List<OrderCompensationDTO> orderCompensationDTOList= Lists.newArrayList();
		try {
			com.google.common.collect.Lists.partition(request.getOrderViewIds(), COMPENSATION_ORDER_ID_MAX_COUNT).forEach(datas -> {
				Map<String, Object> param = Maps.newHashMap();
				param.put("orderViewIds", datas);
				param.put("app_poi_code", request.getChannelPoiId());
				param.put("compensate_type", request.getCompensateType());
				BaseRequest baseRequest = new BaseRequest()
						.setTenantId(request.getTenantId())
						.setAppId(request.getAppId())
						.setChannelId(request.getChannelId());
				addStoreId2BaseRequest(request.getStoreId(), baseRequest);
				log.info("MtBrandChannelOrderServiceImpl.queryOrderCompensateInfo  调用赔付美团接口 baseRequest:{}   param:{}", baseRequest, param);
				Map result = mtBrandChannelGateService.sendGet(getCompensateInfo, null, baseRequest, param);
				if (MapUtils.isEmpty(result)) {
					return;
				}
				log.info("MtBrandChannelOrderServiceImpl.queryChannelOrderList  调用赔付美团接口:{}", JSON.toJSONString(result));
				CompensationOrderResultDTO compensationOrderResultDTO = JSON.parseObject(JSON.toJSONString(result), CompensationOrderResultDTO.class);
				if (compensationOrderResultDTO.getResult_code() == null || compensationOrderResultDTO.getResult_code() != 1) {
					return;
				}
				if(CollectionUtils.isNotEmpty(compensationOrderResultDTO.getSuccess_list())){
					orderCompensationDTOList.addAll(mtConverterService.compensationOrderMappings(compensationOrderResultDTO.getSuccess_list()));
				}
			});
		}catch (Exception e){
			log.error("MtBrandChannelOrderServiceImpl.queryCompensateOrderList error", e);
			queryCompensationOrderListResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单赔付信息异常"));
			return queryCompensationOrderListResult;
		}
		queryCompensationOrderListResult.setOrderCompensationDTOList(orderCompensationDTOList);
		queryCompensationOrderListResult.setStatus(ResultGenerator.genSuccessResult());
		return queryCompensationOrderListResult;
	}

	@Override
	public GetOrderPrivacyPhoneResult queryOrderPrivacyPhone(GetOrderPrivacyPhoneRequest request) {
		Map<String, Object> param = Maps.newHashMap();
		param.put(ProjectConstant.ORDER_ID, request.getOrderId());
		BaseRequest baseRequest = new BaseRequest()
				.setTenantId(request.getTenantId())
				.setChannelId(request.getChannelId())
				.setStoreIdList(Lists.newArrayList(request.getStoreId()));
		log.info("MtChannelOrderServiceImpl.queryOrderPrivacyPhone  调用美团接口 baseRequest:{}   param:{}", baseRequest, param);
		Map<String, Object> result = mtBrandChannelGateService.sendGet(getOrderPrivacyPhone, null, baseRequest, param);
		log.info("MtChannelOrderServiceImpl.queryOrderPrivacyPhone  调用美团接口返回:{}", result);
		GetOrderPrivacyPhoneResult resp = new GetOrderPrivacyPhoneResult();
		if (MapUtils.isEmpty(result)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询订单隐私号失败"));
		}
		if (!Objects.equals(result.get(ProjectConstant.RESULT_CODE), 1)) {
			String errMsg = "";
			Object errList = result.get(ProjectConstant.ERROR_LIST);
			MtErrorStatus errObj = JSON.parseArray(String.valueOf(errList != null ? errList : ""), MtErrorStatus.class)
					.stream().findFirst().orElse(null);
			if (errObj != null) {
				errMsg = errObj.getMsg();
			}
			resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, errMsg));
		} else {
			JSONObject successMap = (JSONObject) result.get(ProjectConstant.MT_SUCCESS_MAP_KEY);
			if (CollectionUtils.isEmpty(successMap)  || StringUtils.isEmpty(successMap.getString("privacy_phone"))) {
				return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单隐私号为空"));
			}
			resp.setPrivacyPhone(successMap.getString("privacy_phone"));
			successMap.getObject("privacy_phone", String.class);
			resp.setStatus(ResultGenerator.genSuccessResult());
		}
		return resp;
	}

	@Override
	public GetOrderRefundGoodsFeeResult queryOrderRefundGoodsFee(GetOrderRefundGoodsFeeRequest request) {
		Map<String, Object> param = Maps.newHashMap();
		param.put(ProjectConstant.ORDER_ID, request.getOrderId());
		BaseRequest baseRequest = new BaseRequest()
				.setTenantId(request.getTenantId())
				.setChannelId(request.getChannelId())
				.setStoreIdList(Lists.newArrayList(request.getStoreId()));

		log.info("MtChannelOrderServiceImpl.queryOrderRefundGoodsFee  调用美团接口 request:{},  param:{}", request, param);
		Map<String, Object> result = mtBrandChannelGateService.sendPost(getOrderRefundGoodsFee, null, baseRequest, param);
		log.info("MtChannelOrderServiceImpl.queryOrderRefundGoodsFee  调用美团接口返回:{}", result);

		GetOrderRefundGoodsFeeResult resp = new GetOrderRefundGoodsFeeResult();
		if (MapUtils.isEmpty(result)) {
			return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道退单商家反货运费失败"));
		}
		// mock 代码
		//mockMtRefundGoodsFee(result, request.getTenantId());
		if (!Objects.equals(result.get(ProjectConstant.RESULT_CODE), 1)) {
			String errMsg = "";
			int errCoe = 0;
			Object errList = result.get(ProjectConstant.ERROR_LIST);
			MtErrorStatus errObj = JSON.parseArray(String.valueOf(errList != null ? errList : ""), MtErrorStatus.class)
					.stream().findFirst().orElse(null);
			if (errObj != null) {
				errMsg = errObj.getMsg();
				errCoe = errObj.getCode();
			}
			// 700 反货单不存在，1102 非商家责任，商家无需支付费用，直接返回成功，金额0
			if(Objects.equals(errCoe, 700) || Objects.equals(errCoe, 1102)){
				resp.setTotalReturnGoodsFee("0");
				resp.setStatus(ResultGenerator.genSuccessResult());
			} else {
				resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, errMsg));
			}
		} else {
			JSONArray successList = (JSONArray) result.get(SUCCESS_LIST);
			if (CollectionUtils.isEmpty(successList)) {
				return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询退单商家反货运费为空"));
			}
			BigDecimal totalReturnGoodsFee = BigDecimal.ZERO;
			for (int i = 0 ; i < successList.size(); i++){
				if(successList.getJSONObject(i) != null){
					totalReturnGoodsFee = totalReturnGoodsFee.add(
							Optional.ofNullable(successList.getJSONObject(i)
									.getBigDecimal("return_goods_fee")).orElse(BigDecimal.ZERO));
				}
			}
			resp.setTotalReturnGoodsFee(totalReturnGoodsFee.toString());
			resp.setStatus(ResultGenerator.genSuccessResult());
		}
		return resp;
	}

	// mock代码
	void mockMtRefundGoodsFee(Map<String, Object> result,long tenantId){
		MockMtOrderAndRefundDTO mockMtOrderAndRefundDTO = MccConfigUtil.mockMtOrderAndRefund(tenantId);
		if(mockMtOrderAndRefundDTO != null
				&& StringUtils.isNotEmpty(mockMtOrderAndRefundDTO.getRefundGoodsFee())
		){
			result.put(ProjectConstant.RESULT_CODE, 1);
			JSONArray jsonObjectList = new JSONArray();
			jsonObjectList.add(new JSONObject(){{
				put("return_goods_fee", mockMtOrderAndRefundDTO.getRefundGoodsFee());
			}});
			result.put(SUCCESS_LIST, jsonObjectList);
		}
	}

	@Override
	public DecryptUserSensitiveDataResponse decryptUserSensitiveData(DecryptUserSensitiveDataRequest request) {
// 参数检查
		DecryptUserSensitiveDataResponse response = new DecryptUserSensitiveDataResponse();

		ResultStatus resultStatus = new ResultStatus();
		resultStatus.setCode(ResultCode.CHANNEL_ORDER_QUERY_FAILED.getCode());
		resultStatus.setMsg(ResultCode.CHANNEL_ORDER_QUERY_FAILED.getMsg());
		response.setStatus(resultStatus);

		GetChannelOrderDetailRequest getChannelOrderDetailRequest = new GetChannelOrderDetailRequest();
		getChannelOrderDetailRequest.setTenantId(request.getTenantId());
		getChannelOrderDetailRequest.setChannelId(request.getChannelId());
		getChannelOrderDetailRequest.setSotreId(request.getStoreId());
		getChannelOrderDetailRequest.setOrderId(request.getOrderId());
		GetChannelOrderDetailResult channelDetailResult = getChannelOrderDetail(getChannelOrderDetailRequest);
		if (channelDetailResult == null || channelDetailResult.getStatus().getCode() != 0) {
			return response;
		}

		OrderDeliveryDetailDTO deliveryDetail = channelDetailResult.getChannelOrderDetail().getDeliveryDetail();

		if (isOrderFinishedOrCanceled(channelDetailResult)) {
			resultStatus = new ResultStatus();
			resultStatus.setCode(-1);
			resultStatus.setMsg("order finished or canceled");
			response.setStatus(resultStatus);
			log.info("订单已完成或已取消，不进行隐私解密, orderId:{}", request.getOrderId());
			return response;
		}
		DecryptUserSensitiveDataDTO userSensitiveDataDTO = new DecryptUserSensitiveDataDTO();
		if (!StringUtils.equals(deliveryDetail.getUserName(), MccConfigUtil.getDefaultMtSensitiveInfo("recipient_name"))){
			userSensitiveDataDTO.setName(deliveryDetail.getUserName());
		}
		if (!StringUtils.equals(deliveryDetail.getUserPhone(), MccConfigUtil.getDefaultMtSensitiveInfo("recipient_phone"))) {
			userSensitiveDataDTO.setPhone(deliveryDetail.getUserPhone());
		}
		if (!StringUtils.equals(deliveryDetail.getUserAddress(), MccConfigUtil.getDefaultMtSensitiveInfo("recipient_address"))) {
			userSensitiveDataDTO.setAddress(deliveryDetail.getUserAddress());
		}
		resultStatus.setCode(ResultCode.SUCCESS.getCode());
		resultStatus.setMsg(ResultCode.SUCCESS.getMsg());

		response.setUserSensitiveDataDTO(userSensitiveDataDTO);
		response.setStatus(resultStatus);
		return response;
	}

	private boolean isOrderFinishedOrCanceled(GetChannelOrderDetailResult channelDetailResult) {
		ChannelOrderDetailDTO channelOrderDetail = channelDetailResult.getChannelOrderDetail();
		if (Objects.isNull(channelOrderDetail)) {
			return true;
		}
		return channelOrderDetail.getStatus() == 8 || channelOrderDetail.getStatus() == 9;
	}

	@Override
	@SuppressWarnings("all")
	public boolean batchSyncRiderPoint(RiderPointBatchSyncRequest request) {
		//美团骑手轨迹回传一次性条数
		int size = MccConfigUtil.getLionIntValue(LionConfigKeyConstant.MT_RIDER_POINT_SYNC_SIZE, 50);
		//组装配送中轨迹同步参数
		List<Map<String, Object>> list = RiderPointConvertUtils.buildMtChanelSyncRiderPointParams(request, size);
		//组装基础参数
		BaseRequest baseRequest = RiderPointConvertUtils.buildPointSyncBaseRequest(request);
		AtomicBoolean flag = new AtomicBoolean(true);
		for (Map<String, Object> param : list) {
			try {
				Map<String, Object> result = mtBrandChannelGateService.sendPost(deliveringRiderPointSyncUrl, null, baseRequest, param);
				if (MapUtils.isEmpty(result)
						|| Objects.isNull(result.get(ProjectConstant.RESULT_CODE)) || (Integer) result.get(ProjectConstant.RESULT_CODE) != 1) {
					flag.set(false);
				}
			} catch (Exception e) {
				log.error("配送中骑手点位回传异常 param:{}", param, e);
				flag.set(false);
			}
		}
		return flag.get();
	}

	@Override
	@SuppressWarnings("all")
	public boolean batchSyncRiderAllPoint(RiderPointBatchSyncRequest request) {
		//美团骑手轨迹回传一次性条数
		int size = MccConfigUtil.getLionIntValue(LionConfigKeyConstant.MT_RIDER_ALL_POINT_SYNC_SIZE, 5400);
		//组装配送中轨迹同步参数
		List<Map<String, Object>> list = RiderPointConvertUtils.buildMtChanelSyncRiderPointParams(request, size);
		//组装基础参数
		BaseRequest baseRequest = RiderPointConvertUtils.buildPointSyncBaseRequest(request);
		AtomicBoolean flag = new AtomicBoolean(true);
		for (Map<String, Object> param : list) {
			try {
				Map<String, Object> result = mtBrandChannelGateService.sendPost(deliveryCompletedRiderPointSyncUrl, null, baseRequest, param);
				if (MapUtils.isEmpty(result)
						|| Objects.isNull(result.get(ProjectConstant.RESULT_CODE)) || (Integer) result.get(ProjectConstant.RESULT_CODE) != 1) {
					flag.set(false);
				}
			} catch (Exception e) {
				log.error("配送完成骑手点位回传异常 param:{}", param, e);
				flag.set(false);
			}
		}
		return flag.get();
	}

}
