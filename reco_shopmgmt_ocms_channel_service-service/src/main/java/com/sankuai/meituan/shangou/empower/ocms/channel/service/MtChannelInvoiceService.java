package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.invoice.ApplyBlueInvoiceReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.invoice.ApplyRedInvoiceReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.invoice.UploadInvoiceReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.invoice.CreateInvoiceSgHandReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.invoice.RedFlushForSgHandReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;

public interface MtChannelInvoiceService {
    /**
     * 订单发票上传  https://tscc.meituan.com/home/<USER>/market/10236
     * @param req 发票信息
     */
    ResultStatus uploadBlueInvoice(UploadInvoiceReq req);

    /**
     * 订单发票红冲结果回传 https://tscc.meituan.com/home/<USER>/market/10237
     * @param req 发票信息
     */
    ResultStatus uploadRedInvoice(UploadInvoiceReq req);

    /**
     * 开票申请 https://tscc.meituan.com/home/<USER>/market/10234
     * @param req 发票信息
     * @return ResultStatus
     */
    ResultStatus applyBlueInvoice(CreateInvoiceSgHandReq req);
    /**
     * 红冲申请 https://tscc.meituan.com/home/<USER>/market/10235
     * @param req 发票信息
     *  @return ResultStatus
     */
    ResultStatus applyRedInvoice(RedFlushForSgHandReq req);
}
