package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPriceService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductChannelErrorMappingUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Preconditions;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 饿了么渠道商品价格内部服务接口
 *
 * <AUTHOR>
 * @create 2019-01-09 下午4:40
 **/
@Service("elmChannelPriceService")
public class ElmChannelPriceServiceImpl implements ChannelPriceService {
    public static final int PRICE_UPDATE_MAX_COUNT = 100;

    @Resource
    private ElmConverterService elmConverterService;

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CommonLogger log;

    @Override
    public ResultData updatePrice(SkuPriceRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        BaseRequest baseRequest = getBaseRequest(request);

        // 分页调用
        ListUtils.listPartition(request.getParamList(), PRICE_UPDATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuPriceDTO::getSkuId).collect(Collectors.toList());

                // 业务参数组装
                ChannelPriceUpdateDTO postData = elmConverterService.updatePrice(new SkuPriceRequest().setParamList(data));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.PRICE_UPDATE, baseRequest, postData);

                // 组装返回结果
                ResultDataUtils.combineResultDataList(resultData, postResult, bizKeyList);

            } catch (IllegalArgumentException e) {
                log.error("ElmChannelPriceServiceImpl.updatePrice 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);

            } catch (Exception e) {
                log.error("ElmChannelPriceServiceImpl.updatePrice 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList);
            }
        });
        return resultData;
    }


    @Override
    public ResultData updatePriceMultiChannel(SkuPriceMultiChannelRequest request) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(request.getParamList()), "多渠道多门店价格信息不能为空");
        final ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long storeId = request.getParamList().get(0).getStoreId();
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getTenantId())
                .setChannelId(request.getParamList().get(0).getChannelId())
                .setStoreIdList(Lists.newArrayList(storeId))
                .setAsyncInvoke(request.isAsyncInvoke());
        // 按规格类型分组处理
        Map<Integer, List<SkuPriceMultiChannelDTO>> specTypePriceList = request.getParamList().stream().collect(Collectors.groupingBy(SkuPriceMultiChannelDTO::getSpecType));
        specTypePriceList.forEach((specType, paramList) -> {
            if (CollectionUtils.isEmpty(paramList)) {
                return;
            }
            if (specType == 0 || SpecTypeEnum.SINGLE.getCode() == specType) {
                // 分批调用
                // 区分使用商家自定义商品ID还是渠道商品ID更新
                List<String> skuIds;

                //  商家自定义商品ID和牵牛花 SKUID 的映射关系, 单规格映射 customSpuId -> skuId
                Map<String, String> customSpuIdMapping = new HashMap<>();
                if (request.isOptByChannelSpuId()) {
                    // 因为牵牛花elm的channelSkuId未维护，所以使用channelSpuId
                    skuIds = paramList.stream().map(SkuPriceMultiChannelDTO::getChannelSpuId).collect(Collectors.toList());
                } else {
                    customSpuIdMapping.putAll(
                            paramList.stream()
                            .filter(param -> StringUtils.isNotBlank(param.getCustomSpuId()))
                            .collect(Collectors.toMap(SkuPriceMultiChannelDTO::getCustomSpuId, SkuPriceMultiChannelDTO::getSkuId, (k1, k2) -> k1))
                    );
                    skuIds = paramList.stream().map(SkuPriceMultiChannelDTO::getSkuId).collect(Collectors.toList());
                }
                try {
                    // 业务参数转换
                    ChannelPriceUpdateDTO postData;
                    if (request.isOptByChannelSpuId()) {
                        postData = elmConverterService.updatePriceMultiChannelSingelSpecByChannelSkuId(new SkuPriceMultiChannelRequest().setParamList(paramList));
                    } else {
                        postData = elmConverterService.updatePriceMultiChannelSingelSpec(new SkuPriceMultiChannelRequest().setParamList(paramList));
                    }

                    // 调用渠道接口
                    Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.PRICE_UPDATE, baseRequest, postData);

                    // 返回信息重组
                    if (MccConfigUtil.getElmParseChannelResponseUpgradeSwitch()) {
                        ProductResultDataUtils.elmCombineResultDataList(postResult, resultData, skuIds, ElmChannelBaseResponseDetail.class,
                                false, request.isOptByChannelSpuId(), customSpuIdMapping);
                    } else {
                        // 组装返回结果
                        installResultData(postResult, resultData, skuIds);
                    }
                } catch (IllegalArgumentException e) {
                    log.error("ElmChannelPriceServiceImpl.updatePriceMultiChannel 参数校验失败, paramList:{}", paramList, e);
                    ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds, storeId);
                } catch (InvokeChannelTooMuchException e) {
                    resultData.setStatus(new ResultStatus(ResultCodeEnum.TRIGGER_LIMIT.getValue(), ProjectConstant.TRIGGER_LIMIT_MSG, String.valueOf(e.getWaitTimeMills())));
                    ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds, storeId);
                } catch (Exception e) {
                    log.error("ElmChannelPriceServiceImpl.updatePriceMultiChannel 服务异常, paramList:{}", paramList, e);
                    ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds, storeId);
                }
            } else if (SpecTypeEnum.MULTI.getCode() == specType) {
                // 按渠道商品分组规格改价请求
                Map<String, List<SkuPriceMultiChannelDTO>> channelSpuPriceList = paramList.stream().collect(Collectors.groupingBy(SkuPriceMultiChannelDTO::getChannelSpuId));
                channelSpuPriceList.forEach((channelSpuId, data) -> {
                    List<String> skuIds = data.stream().map(SkuPriceMultiChannelDTO::getSkuId).collect(Collectors.toList());
                    //  商家自定义商品ID和牵牛花 SKUID 的映射关系, 多规格映射 customSkuId -> skuId
                    Map<String, String> customSkuIdMapping = data.stream()
                            .filter(param -> StringUtils.isNotBlank(param.getCustomSkuId()))
                            .collect(Collectors.toMap(SkuPriceMultiChannelDTO::getCustomSkuId, SkuPriceMultiChannelDTO::getSkuId, (k1, k2) -> k1));

                    try {
                        ChannelSpecPriceUpdateDTO postData = ChannelSpecPriceUpdateDTO.build(channelSpuId, data);
                        // 调用渠道接口
                        Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SPEC_PRICE_UPDATE, baseRequest, postData);
                        // 解析结果
                        if (MccConfigUtil.getParseMultiChannelResponseUpgradeSwitch()) {
                            ProductResultDataUtils.elmCombineResultDataList(postResult, resultData, skuIds, ElmChannelBaseResponseDetail.class, false, request.isOptByChannelSpuId(), customSkuIdMapping);
                        } else {
                            postResult.values().forEach(response -> {
                                if (response.isSuccess()) {
                                    resultData.getSucData().addAll(data.stream().map(this::convert2Success).collect(Collectors.toList()));
                                } else {
                                    ChannelResponseResult result = response.getBody();
                                    resultData.getErrorData().addAll(data.stream().map(m -> convert2Error(m).setErrorCode(result.getErrno()).setErrorMsg(result.getError())).collect(Collectors.toList()));
                                }
                            });
                        }
                    } catch (IllegalArgumentException e) {
                        log.error("ElmChannelPriceServiceImpl.updatePrice 参数校验失败, data:{}", data, e);
                        ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds);
                    } catch (Exception e) {
                        log.error("ElmChannelPriceServiceImpl.updatePrice 服务异常, data:{}", data, e);
                        ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds);
                    }
                });
            }
        });
        return resultData;
    }

    private ResultSuccessSku convert2Success(SkuPriceMultiChannelDTO dto) {
        return new ResultSuccessSku().setStoreId(dto.getStoreId())
                .setSkuId(dto.getSkuId())
                .setChannelId(dto.getChannelId())
                .setChannelSkuId(dto.getChannelSkuId())
                .setChannelSpuId(dto.getChannelSpuId())
                .setSpuId(dto.getSpuId());
    }

    private ResultErrorSku convert2Error(SkuPriceMultiChannelDTO dto) {
        return new ResultErrorSku().setStoreId(dto.getStoreId())
                .setSkuId(dto.getSkuId())
                .setChannelId(dto.getChannelId())
                .setChannelSkuId(dto.getChannelSkuId())
                .setChannelSpuId(dto.getChannelSpuId())
                .setSpuId(dto.getSpuId());
    }

    @Override
    public List<ChannelStorePriceInfo> batchGetPriceInfo(BaseRequest request, List<Long> skuIds) {
        return null;
    }


    /**
     * 组装结果数据
     *
     * @param postResult
     * @param resultData
     * @param skuIds
     */
    @SuppressWarnings("unchecked")
    private void installResultData(Map<Long, ChannelResponseDTO> postResult, ResultData
            resultData, List<String> skuIds) {
        Preconditions.checkArgument(MapUtils.isNotEmpty(postResult), "installResultData fail, post result is empty");
        postResult.forEach((storeId, channelResponseDTO) -> {
            ChannelPriceResponseData channelPriceResponseData = parseErrorMsg(channelResponseDTO);
            if (channelPriceResponseData != null) {
                installResultDataForPartSuccess(storeId, channelPriceResponseData, resultData);
            } else {
                skuIds.forEach(skuId -> ResultDataUtils.addToResultData(storeId, channelResponseDTO, resultData, skuId));
            }
        });
    }

    /**
     * 解析错误信息 部分成功数据 通过errorMsg解析获取channelSkuId
     *
     * @param channelResponseDTO
     * @return
     */
    private ChannelPriceResponseData parseErrorMsg(ChannelResponseDTO channelResponseDTO) {
        if (parseErrorMsgUnnecessary(channelResponseDTO)) {
            return null;
        }
        String channelResponseData = channelResponseDTO.getErrorMsg();
        ChannelPriceResponseData channelPriceResponseData = null;
        try {
            channelPriceResponseData = JSON.parseObject(channelResponseData, ChannelPriceResponseData.class);
        } catch (Exception e) {
            log.warn("ElmChannelPriceServiceImpl.reinstallErrorData error, errorMsg:{} ", channelResponseData, e);
        }
        if (channelPriceResponseData == null || channelPriceResponseData.invalid()) {
            return null;
        }
        return channelPriceResponseData;
    }

    /**
     * 组装部分成功结果
     *
     * @param storeId
     * @param channelPriceResponseData
     * @param resultData
     */
    private void installResultDataForPartSuccess(long storeId, ChannelPriceResponseData
            channelPriceResponseData, ResultData resultData) {
        if (channelPriceResponseData == null || channelPriceResponseData.invalid()) {
            return;
        }
        if (CollectionUtils.isNotEmpty(channelPriceResponseData.getSuccessList())) {
            channelPriceResponseData.getSuccessList().forEach(successDetail -> {
                ResultSuccessSku resultSuccessSku = new ResultSuccessSku();
                resultSuccessSku.setSkuId(successDetail.getCustomSkuId());
                resultSuccessSku.setChannelSkuId(successDetail.getSkuId());
                resultSuccessSku.setChannelId(ChannelTypeEnum.ELEM.getCode());
                resultSuccessSku.setStoreId(storeId);
                resultData.addToSucData(resultSuccessSku);
            });
        }

        if (CollectionUtils.isNotEmpty(channelPriceResponseData.getErrorList())) {
            channelPriceResponseData.getErrorList().forEach(errorDetail -> {
                ResultErrorSku resultErrorSku = new ResultErrorSku();
                resultErrorSku.setSkuId(errorDetail.getCustomSkuId());
                resultErrorSku.setChannelSkuId(errorDetail.getSkuId());
                resultErrorSku.setChannelId(ChannelTypeEnum.ELEM.getCode());
                resultErrorSku.setStoreId(storeId);
                resultErrorSku.setErrorCode(errorDetail.getErrorCode());
                resultErrorSku.setErrorMsg(errorDetail.getErrorMsg());
                //填充统一渠道错误码
                ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.ELEM,
                    String.valueOf(errorDetail.getErrorCode()), errorDetail.getErrorMsg());
                resultErrorSku.setChannelUnifyError(unifyErrorEnum);
                resultData.addToErrorData(resultErrorSku);
            });
        }
    }

    private boolean parseErrorMsgUnnecessary(ChannelResponseDTO channelResponseDTO) {
        return channelResponseDTO == null || !StringUtils.equals(channelResponseDTO.getErrno(), ElmResultCodeEnum.UPDATE_PRICE_PART_SUCCESS.getCodeStr())
                || StringUtils.isBlank(channelResponseDTO.getErrorMsg()) || !channelResponseDTO.getErrorMsg().startsWith("{");
    }
}
