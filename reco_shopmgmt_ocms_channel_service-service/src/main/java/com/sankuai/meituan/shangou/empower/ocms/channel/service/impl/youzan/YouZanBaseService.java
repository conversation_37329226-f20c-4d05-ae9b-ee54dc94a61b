package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzan;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.TokenUnValidException;
import com.youzan.cloud.open.sdk.api.API;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.core.client.auth.Token;
import com.youzan.cloud.open.sdk.core.client.core.DefaultYZClient;

/**
 * <AUTHOR>
 * @since 6/18/21 10:41 AM
 */
public class YouZanBaseService {

    @Autowired
    private DefaultYZClient yzClient;
    @Autowired
    private YzAccessTokenService accessTokenService;

    public <T> T getResult4YouZanWithRetry(AppMessage appMessage,
                                           API api, Class<T> clz) throws SDKException {
        try {
            return getResult4YouZan(appMessage, api, clz);
        }
        catch (TokenUnValidException e) {
            // token失效重试请求
            return getResult4YouZan(appMessage, api, clz);
        }
    }

    public <T> T getResult4YouZan(AppMessage appMessage,
                                  API api, Class<T> clz) throws SDKException {
        try {
            return yzClient.invoke(api, new Token(accessTokenService.getAccessToken(appMessage)), clz);
        }
        catch (SDKException sdkEx) {
            if (accessTokenService.checkToken(sdkEx, appMessage)) {
                throw new TokenUnValidException("token un valid", sdkEx);
            }
            throw sdkEx;
        }
    }
    public DefaultYZClient getYzClient() {
        return yzClient;
    }

    public YzAccessTokenService getAccessTokenService() {
        return accessTokenService;
    }
}
