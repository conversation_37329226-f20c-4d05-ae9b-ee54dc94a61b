package com.sankuai.meituan.shangou.empower.ocms.channel.validator;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoByOffsetRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteByChannelSpuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: luokai14
 * @Date: 2022/7/15 11:56 上午
 * @Mail: <EMAIL>
 */
@Slf4j
public class ElmParamValidator {
    public static void elmBasicValidate(BatchGetSpuInfoByOffsetRequest request) {
        Preconditions.checkNotNull(request, ResultCode.INVALID_REQUEST.getMsg());
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            Preconditions.checkArgument(tenantId > 0, "tenantId 不合法");
            int channelId = request.getBaseInfo().getChannelId();
            Preconditions.checkArgument(ChannelTypeEnum.ELEM.getCode() == channelId, "channelId不合法");
            int pageSie = request.getPageSize();
            Preconditions.checkArgument(pageSie > 0 && pageSie < 101, "pageSize 取值必须在1-100之间");
        } catch (Exception e) {
            log.error("ElmParamValidator.elmBasicValidate 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    public static void eleDeleteSpuByChannelSpuIdValidate(SpuInfoDeleteByChannelSpuIdRequest request) {
        Preconditions.checkNotNull(request, ResultCode.INVALID_REQUEST.getMsg());
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            Preconditions.checkArgument(tenantId > 0, "tenantId 不合法");
            int channelId = request.getBaseInfo().getChannelId();
            Preconditions.checkArgument(ChannelTypeEnum.ELEM.getCode() == channelId, "channelId不合法");
        } catch (Exception e) {
            log.error("ElmParamValidator.eleDeleteSpuByChannelSpuIdValidate 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    public static void elmSinglePushSpuValidate(SpuInfoRequest request) {
        Preconditions.checkNotNull(request, ResultCode.INVALID_REQUEST.getMsg());
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            Preconditions.checkArgument(tenantId > 0, "tenantId 不合法");
            int channelId = request.getBaseInfo().getChannelId();
            Preconditions.checkArgument(ChannelTypeEnum.ELEM.getCode() == channelId, "channelId不合法");
            Preconditions.checkArgument(request.getParamListSize() == 1,"商品数量不合法，只允许传入单个商品");
        } catch (Exception e) {
            log.error("ElmParamValidator.elmSinglePushSpuValidate 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }
}
