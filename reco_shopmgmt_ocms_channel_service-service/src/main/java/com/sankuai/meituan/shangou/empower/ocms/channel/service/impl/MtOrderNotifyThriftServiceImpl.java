package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.MtOrderNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.MtOrderNotifyV2Request;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.MtOrderNotifyResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MtOrderNotifyThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.PaoTuiLockStatusNotifyRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.PaoTuiLockStatusNotifyV2Request;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.PaoTuiLockStatusNotifyResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description: 美团订单解锁信息推送
 */
@Slf4j
@Service
public class MtOrderNotifyThriftServiceImpl implements MtOrderNotifyThriftService {

    //美团平台推送消息新接口，成功返回{"result_code":1};失败返回{"result_code":3}
    private static final Integer SUCCESS = 1;

    private static final Integer FAIL = 3;

    @Resource
    private DeliveryOperationThriftService deliveryOperationThriftService;
    @Override
    public MtOrderNotifyResponse orderUnlockNotify(MtOrderNotifyRequest request) {
        try {
            log.info("orderUnlockNotify request:{}", request.getOrderId());
            PaoTuiLockStatusNotifyResponse paoTuiLockStatusNotifyResponse = deliveryOperationThriftService.paoTuiLockStatusNotify(new PaoTuiLockStatusNotifyRequest(request.getOrderId()));
            log.info("orderUnlockNotify response:{}", paoTuiLockStatusNotifyResponse);
            if (Objects.isNull(paoTuiLockStatusNotifyResponse) || paoTuiLockStatusNotifyResponse.getStatus().getCode() != 0) {
                return new MtOrderNotifyResponse(FAIL);
            }
            return new MtOrderNotifyResponse(SUCCESS);
        } catch (BizException e) {
            log.error("doOrderUnlockNotify throw BizException", e);
            return new MtOrderNotifyResponse(FAIL);
        } catch (Exception e) {
            log.error("doOrderUnlockNotify throw Exception", e);
            return new MtOrderNotifyResponse(FAIL);
        }
    }

    @Override
    public MtOrderNotifyResponse orderUnlockNotifyV2(MtOrderNotifyV2Request request) {
        try {
            log.info("orderUnlockNotifyV2 request:{}", request);
            PaoTuiLockStatusNotifyResponse response = deliveryOperationThriftService.paoTuiLockStatusNotifyV2(new PaoTuiLockStatusNotifyV2Request(String.valueOf(request.getOrderId()), request.getDeliveryAvailable()));
            log.info("orderUnlockNotifyV2 response:{}", response);
            if (Objects.isNull(response) || response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return new MtOrderNotifyResponse(FAIL);
            }
            return new MtOrderNotifyResponse(SUCCESS);
        } catch (BizException e) {
            log.error("orderUnlockNotifyV2 throw BizException", e);
            return new MtOrderNotifyResponse(FAIL);
        } catch (Exception e) {
            log.error("orderUnlockNotifyV2 throw Exception", e);
            return new MtOrderNotifyResponse(FAIL);
        }
    }
}