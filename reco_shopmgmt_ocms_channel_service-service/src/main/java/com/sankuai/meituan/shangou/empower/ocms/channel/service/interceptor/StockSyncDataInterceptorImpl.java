package com.sankuai.meituan.shangou.empower.ocms.channel.service.interceptor;

import com.google.common.collect.Lists;
import com.meituan.dorado.rpc.meta.RpcResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 库存数据同步拦截器
 * <AUTHOR>
 * @Date 2020-05-11
 */
@Service("StockSyncDataInterceptorImpl")
public class StockSyncDataInterceptorImpl extends UpstreamSyncDataInterceptBase<ResultErrorSku> {

    private static final String UPDATE_STOCK = "updateStock";
    private static final String UPDATE_STOCK_MULTI_CHANNEL = "updateStockMultiChannel";

    private static final List<String> METHODS = new ArrayList<>();
    static {
        METHODS.add(UPDATE_STOCK);
        METHODS.add(UPDATE_STOCK_MULTI_CHANNEL);
    }

    @Override
    protected List<String> getErrorSkuIds(int channelId, long storeId, InterceptInfo interceptInfo){
        // UPDATE_STOCK_MULTI_CHANNEL 方法每个门店下的skuId列表不一样，需要特殊处理
        if(interceptInfo.getMethodName().equals(UPDATE_STOCK_MULTI_CHANNEL)){
            return interceptInfo.errorSkuIds.get(createKey(channelId, storeId));
        }
        else {
            return interceptInfo.commonErrorSkuIds;
        }
    }

    @Override
    protected long getStoreIdBy(Object requestData, InterceptInfo interceptInfo) {
        long storeId;
        switch (interceptInfo.getMethodName()){
            case UPDATE_STOCK_MULTI_CHANNEL:
                storeId = ((SkuStockMultiChannelDTO)requestData).getStoreId();
                break;
            default:
                storeId = super.getStoreIdBy(requestData, interceptInfo);
                break;
        }
        return storeId;
    }

    @Override
    protected List<String> getAffectMethods() {
        return METHODS;
    }

    @Override
    protected void parseArgument(Object argument, InterceptInfo interceptInfo){
        switch (interceptInfo.getMethodName()){
            case UPDATE_STOCK_MULTI_CHANNEL:
                SkuStockMultiChannelRequest multiChannelRequest = (SkuStockMultiChannelRequest)argument;
                interceptInfo.tenantId = multiChannelRequest.tenantId;
                interceptInfo.requestDataList = multiChannelRequest.skuStockList;
                //interceptInfo.channelId = multiChannelRequest.skuStockList
                multiChannelRequest.skuStockList.stream()
                        .collect(Collectors.groupingBy(SkuStockMultiChannelDTO::getChannelId,
                                Collectors.mapping(SkuStockMultiChannelDTO::getStoreId, Collectors.toSet())))
                        .forEach((channelId, storeIdList) -> interceptInfo.getChannelStoreIds().put(channelId, ListUtils.newArrayListOrDefault(storeIdList)));
                multiChannelRequest.skuStockList.stream()
                        .collect(Collectors.groupingBy(item -> createKey(item.getChannelId(),item.getStoreId()),
                                Collectors.mapping(SkuStockMultiChannelDTO::getSkuId, Collectors.toList())))
                        .forEach((key, skuIds) -> interceptInfo.errorSkuIds.put(key, skuIds));
                break;
            default:
                SkuPriceRequest request = (SkuPriceRequest)argument;
                interceptInfo.tenantId = request.getTenantId();
                interceptInfo.requestDataList = Lists.newArrayList(request.getStoreId());
                interceptInfo.channelStoreIds.put(request.getChannelId(), Lists.newArrayList(request.getStoreId()));
                interceptInfo.commonErrorSkuIds = request.paramList.stream().map(SkuPriceDTO::getSkuId).collect(Collectors.toList());
                break;
        }
    }

    @Override
    protected List<ResultErrorSku> getErrorDataList(RpcResult result) {
        return ((ResultData)result.getReturnVal()).getErrorData();
    }

    @Override
    protected ResultErrorSku createResultErrorSku(int channelId, long storeId, String skuId, InterceptInfo interceptInfo){
        ResultStatus status = createCustomResultStatus(channelId, storeId, interceptInfo);
        return new ResultErrorSku().setStoreId(storeId).setSkuId(skuId).setChannelId(channelId)
                .setErrorMsg(status.getMsg())
                .setErrorCode(status.getCode());
    }

    @Override
    protected RpcResult createCustomResult(Class returnType, InterceptInfo interceptInfo) {
        RpcResult result = super.createCustomResult(returnType, interceptInfo);
        result.setReturnVal(ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS));
        return result;
    }
}
