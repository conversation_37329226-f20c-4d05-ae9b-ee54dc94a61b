package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.type.TypeReference;

import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 12/3/21 12:01 PM
 */
@Slf4j
public class DeliveryChannelCodeConvert {

    public static Integer DEFAULT_AVL=10017;
    public static Integer ELM_DEFAULT_AVL=99999;

    /**
     * 商家使用的快递公司，code及快递的枚举值：
     * 10001-顺丰
     * 10002-达达
     * 10003-闪送
     * 10004-蜂鸟
     * 10005 UU跑腿
     * 10006 快跑者
     * 10007 极客快送
     * 10008-点我达
     * 10009 同达
     * 10010-生活半径
     * 10011 邻趣
     * 10012 趣送
     * 10013 快服务
     * 10014 菜鸟新配盟
     * 10015 商家自建配送
     * 10016 风先生
     * 10017-其他
     * 10032-美团跑腿
     */

    @Data
    public static class MtDeliveryChannelProvider {
        /**
         * 赋能tms配送渠道code
         */
        private Integer code;

        private String name;
        /**
         * 美团平台对应的配送渠道code
         */
        private Integer mtCode;
    }


    public static Integer getMtDeliveryChannelCode(Integer tmsCode) {
        try {
            if (tmsCode == null) {
                return null;
            }
            String mtDeliveryChannelCodes = MccConfigUtil.getMtDeliveryChannelCodes();
            if (StringUtils.isEmpty(mtDeliveryChannelCodes)) {
                return DEFAULT_AVL;
            }
            List<MtDeliveryChannelProvider> mtDeliveryChannelProviders = JacksonUtils.fromJson(mtDeliveryChannelCodes,
                    new TypeReference<List<MtDeliveryChannelProvider>>() {
                    });
            if (CollectionUtils.isEmpty(mtDeliveryChannelProviders)) {
                return DEFAULT_AVL;
            }
            Integer mtCode = mtDeliveryChannelProviders
                    .stream()
                    .filter(provider -> tmsCode.equals(provider.getCode()))
                    .findAny()
                    .map(MtDeliveryChannelProvider::getMtCode)
                    .orElse(null);
            if (mtCode == null) {
                log.warn("convert 2 mt delivery is null tmsCode:{}", tmsCode);
                return DEFAULT_AVL;
            }
            return mtCode;
        }
        catch (Exception e) {
            log.error("convert 2 mt delivery channel code error:", e);
            return DEFAULT_AVL;
        }


    }

    public static Integer getQnhOrderMtDeliveryChannelCode(Integer tmsCode) {
        try {
            if (tmsCode == null) {
                return null;
            }
            String mtDeliveryChannelCodes = MccConfigUtil.getQnhMtDeliveryChannelCodes();
            if (StringUtils.isEmpty(mtDeliveryChannelCodes)) {
                return DEFAULT_AVL;
            }
            List<MtDeliveryChannelProvider> mtDeliveryChannelProviders = JacksonUtils.fromJson(mtDeliveryChannelCodes,
                    new TypeReference<List<MtDeliveryChannelProvider>>() {
                    });
            if (CollectionUtils.isEmpty(mtDeliveryChannelProviders)) {
                return DEFAULT_AVL;
            }
            Integer mtCode = mtDeliveryChannelProviders
                    .stream()
                    .filter(provider -> tmsCode.equals(provider.getCode()))
                    .findAny()
                    .map(MtDeliveryChannelProvider::getMtCode)
                    .orElse(null);
            if (mtCode == null) {
                log.warn("convert 2 mt delivery is null tmsCode:{}", tmsCode);
                return DEFAULT_AVL;
            }
            return mtCode;
        }
        catch (Exception e) {
            log.error("convert 2 mt delivery channel code error:", e);
            return DEFAULT_AVL;
        }

    }


    @Data
    public static class ElmDeliveryChannelProvider {
        /**
         * 赋能tms配送渠道code
         */
        private List<Integer> codeList;

        private List<String> name;
        /**
         * 饿了么平台对应的配送渠道code
         */
        private Integer elmCode;
    }


    public static Integer getElmDeliveryChannelCode(Integer tmsCode) {
        try {
            if (tmsCode == null) {
                return null;
            }
            String elmDeliveryChannelCodes = MccConfigUtil.getElmDeliveryChannelCodes();
            if (StringUtils.isEmpty(elmDeliveryChannelCodes)) {
                return ELM_DEFAULT_AVL;
            }
            log.info("读取到elmDeliveryChannelCodes{}", elmDeliveryChannelCodes);
            List<ElmDeliveryChannelProvider> elmDeliveryChannelProviders = JacksonUtils.fromJson(elmDeliveryChannelCodes,
                    new TypeReference<List<ElmDeliveryChannelProvider>>() {
                    });
            if (CollectionUtils.isEmpty(elmDeliveryChannelProviders)) {
                return ELM_DEFAULT_AVL;
            }
            log.info("从mcc配置中读取到的elmDeliveryChannelProviders{}", elmDeliveryChannelProviders);
            Integer elmCode = elmDeliveryChannelProviders
                    .stream()
                    .filter(provider -> provider.getCodeList().contains(tmsCode))
                    .findAny()
                    .map(ElmDeliveryChannelProvider::getElmCode)
                    .orElse(null);
            if (elmCode == null) {
                log.warn("convert elm delivery is null tmsCode:{}", tmsCode);
                return ELM_DEFAULT_AVL;
            }
            return elmCode;
        }
        catch (Exception e) {
            log.error("convert elm delivery channel code error:", e);
            return ELM_DEFAULT_AVL;
        }


    }


}
