package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member.MemberCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member.MemberCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member.MemberQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member.MemberQueryResponse;

/**
 * @description: 会员回调接口
 * @author: xiaolei10
 * @create: 2019/7/2下午3:19
 */
public interface ChannelMemberCallbackService {


    /**
     * 会员查询
     * @param request
     * @return
     */
    MemberQueryResponse getMember(MemberQueryRequest request);

    /**
     * 会员创建
     * @param request
     * @return
     */
    MemberCreateResponse createMember(MemberCreateRequest request);
}
