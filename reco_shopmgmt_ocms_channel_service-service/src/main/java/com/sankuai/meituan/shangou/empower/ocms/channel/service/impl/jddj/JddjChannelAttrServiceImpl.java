package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import static com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant.BATCH_LIMIT_50_AMOUNT;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelAttrDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelPriceUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelAttrService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;

@Service("jddjChannelAttrService")
public class JddjChannelAttrServiceImpl implements ChannelAttrService {

    private static final int JDDJ_CUSTOMIZE_ATTRIBUTE_TYPE = 2;

    @Resource
    private JddjConverterService jddjConverterService;

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private CommonLogger log;

    @Override
    public ResultData updateAttr(SpuInfoRequest request) {
        return updateAttrCommon(request, ChannelPostJDDJEnum.MANAGE_STORE_GOODS_ATTRIBUTE);
    }

    @Override
    public ResultData updateAttrForCleaner(SpuInfoRequest request) {
        return updateAttrCommon(request, ChannelPostJDDJEnum.MANAGE_STORE_GOODS_ATTRIBUTE_FOR_CLEANER);
    }

    private ResultData updateAttrCommon(SpuInfoRequest request, ChannelPostJDDJEnum channelPostJDDJEnum) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long storeId = request.getBaseInfo().getStoreIdList().get(0);
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getBaseInfo().getTenantId()).setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(storeId)).setAsyncInvoke(request.getBaseInfo().isAsyncInvoke());
        log.info("updateAttr request:{}",request);
        List<ChannelAttrDTO> channelAttrDTOList = getUpdateChannelAttrDTOList(request,storeId);

        for (ChannelAttrDTO data : channelAttrDTOList) {
            List<String> bizKeyList = null;
            try {
                Map<Long, ChannelResponseDTO> postResult = jddjChannelGateService.sendPostAppMapDto(channelPostJDDJEnum, baseRequest,
                        data);
                ProductResultDataUtils.combinePartResultData(resultData, postResult, bizKeyList, true, ChannelTypeEnum.JD2HOME);
            }catch (IllegalArgumentException e) {
                log.error("JddjChannelPriceServiceImpl.updatePriceMultiChannel 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList, storeId);
            } catch (InvokeChannelTooMuchException e) {
                log.warn("JddjChannelPriceServiceImpl.updatePriceMultiChannel 调用渠道触发限流, data:{}",
                        data);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.TRIGGER_LIMIT, bizKeyList, storeId);
            } catch (Exception e) {
                log.error("JddjChannelPriceServiceImpl.updatePriceMultiChannel 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList, storeId);
            }
        }
        return resultData;
    }

    private List<ChannelAttrDTO> getUpdateChannelAttrDTOList(SpuInfoRequest request, long storeId) {
        List<ChannelAttrDTO> channelAttrDTOList = Lists.newArrayList();

        Map<String,List<SpuInfoDTO>> propertiesMap =
                request.getParamList().stream().collect(Collectors.groupingBy(SpuInfoDTO::getProperties));
        propertiesMap.forEach((properties, spuInfoDTOList) -> {
            List<Long> channelSkuList =
                    spuInfoDTOList.stream().flatMap(spuInfoDTO -> spuInfoDTO.getSkus().stream())
                            .map(SkuInSpuInfoDTO::getMerchantChannelSkuId)
                            .filter(NumberUtils::isDigits)
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
            List<List<Long>> channeSkuList = Lists.partition(channelSkuList,BATCH_LIMIT_50_AMOUNT);
            channeSkuList.stream().forEach(list->{
                ChannelAttrDTO channelAttrDTO = new ChannelAttrDTO();
                channelAttrDTO.setSkuIdList(channelSkuList);
                channelAttrDTO.setAttributeType(JDDJ_CUSTOMIZE_ATTRIBUTE_TYPE);
                channelAttrDTO.setStationNo(String.valueOf(storeId));
                channelAttrDTO.setOperatePin("牵牛花");
                channelAttrDTO.setAttributeValue(properties);
                channelAttrDTOList.add(channelAttrDTO);
            });
        });
        return channelAttrDTOList;
    }
}
