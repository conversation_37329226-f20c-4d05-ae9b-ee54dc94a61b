package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiSignRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiSignResponse;

/**
 * 渠道鉴权想过接口
 * @author:chenji<PERSON>hui05
 * @date: 2023/07/10
 * @time: 下午4:00
 */
public interface ChannelPoiAuthService {

    /**
     * 获取签名
     * @param request
     * @return
     */
    ChannelPoiSignResponse getChannelSign(ChannelPoiSignRequest request);
}
