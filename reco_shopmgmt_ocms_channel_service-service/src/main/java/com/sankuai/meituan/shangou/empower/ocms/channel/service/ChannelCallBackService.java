package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DouyinCallBackMessageTagEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuAppealInfoNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuManagerAbnormalChangeRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuTagNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuViolationNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.Message;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * @create 2019-06-10 8:35 PM
 **/
public interface ChannelCallBackService {

    /**
     * 商品新增或修改推送
     * @param request
     * @return
     */
    ResultStatus skuNotify(SkuNotifyRequest request);

    /**
     * 商品违规 预警/处罚
     * @param request
     * @return
     */
    ResultStatus skuViolationNotify(SkuViolationNotifyRequest request);

    /**
     * 商品违规 预警/处罚
     * @param request
     * @return
     */
    ResultStatus skuTagNotify(SkuTagNotifyRequest request);

    /**
     * 商品管家 质量异常推送
     * @param request
     * @return
     */
    ResultStatus skuManagerAbnormalChange(SkuManagerAbnormalChangeRequest request);

    /**
     * 商品申诉状态推送
     * @param request
     * @return
     */
    ResultStatus skuAppealInfoNotify(SkuAppealInfoNotifyRequest request);


    void douyinNotify(Message message, DouyinCallBackMessageTagEnum callBackMessageTagEnum, String appId);


    void sendDouyinCallbackMq(Message message, String appId);
}
