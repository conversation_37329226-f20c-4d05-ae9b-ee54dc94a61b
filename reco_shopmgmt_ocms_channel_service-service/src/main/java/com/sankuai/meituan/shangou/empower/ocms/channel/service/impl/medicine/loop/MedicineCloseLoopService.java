package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.loop;

import javafx.util.Pair;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

public interface MedicineCloseLoopService {
    /**
     * 过滤被屏蔽的数据
     * @param sourceMap 需要过滤的数据列表，格式为 租户id -> 该租户被过滤的数据列表，需要包含门店id
     * @param typeEnum 过滤类型
     * @param storeIdFunc 数据列表获取门店id方法
     * @return key: 过滤完成之后的数据 value: 被过滤掉的数据
     * @param <T> 数据信息
     */
    <T> Pair<Map<Long, List<T>>, Map<Long, List<T>>> filterBlockedItems(Map<Long, List<T>> sourceMap, MedicineCloseLoopTypeEnum typeEnum, Function<T, Long> storeIdFunc);

    public static enum MedicineCloseLoopTypeEnum {
        /**
         * 屏蔽商品上行
         */
        PRODUCT,
        /**
         * 屏蔽捡货完成上行，在ocms实现
         */
        ORDER_PICK_COMPLETE,
        /**
         * 屏蔽库存上行
         */
        STOCK
    }

    /**
     * 单租户判断是否需要屏蔽上行
     */
    boolean isUwmsCloseLoop(Long tenantId, Long storeId, MedicineCloseLoopTypeEnum typeEnum);

    /**
     * 多租户判断是否需要屏蔽上行
     * @return key为需要屏蔽上行的租户id列表，value为租户下被屏蔽的门店id列表
     */
    Map<Long, Set<Long>> isUwmsCloseLoopBatch(Map<Long/*租户id*/, Set<Long>/*门店id列表*/> tenantStoreIdMap, MedicineCloseLoopTypeEnum typeEnum);
}
