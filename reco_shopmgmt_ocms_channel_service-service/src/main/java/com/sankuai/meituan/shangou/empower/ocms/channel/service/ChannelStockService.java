package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetStockInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetStockInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStockRequest;

/**
 * @description: 渠道商品品类内部服务接口，各渠道父接口
 * @author: chenhaie
 * @create: 2019/1/7 下午5:27
 */
public interface ChannelStockService {

    /**
     * 更新商品库存(SPU)
     *
     * @param request
     * @return
     */
    ResultSpuData updateStockBySpu(SpuStockRequest request);

    /**
     * 多渠道更新商品库存接口
     *
     * @param request
     * @return
     */
    ResultData updateStockMultiChannel(SkuStockMultiChannelRequest request);

    /**
     * 批量拉取库存信息
     * @param request
     * @return
     */
    BatchGetStockInfoResponse batchGetStockInfo(BatchGetStockInfoRequest request);
}
