package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.util.Preconditions;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelVirtualAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.VirtualAccessConfigListPageQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.ObjectStringParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.RateLimitLevelEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.ChannelVirtualConfigMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DouyinJsonUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DouyinSignUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DouyinSignUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;

import okhttp3.HttpUrl;

/**
 * @program: reco_shopmgmt_ocms_channel_service
 * @description: douyin请求公共服务接口
 * @author: jinyi
 * @create: 2023-12-28 20:44
 **/
@Service("douyinChannelGateService")
public class DouyinChannelGateService extends BaseChannelGateService {
    private final static String API_VERSION = "2";

    private final static String SIGN_METHOD = "hmac-sha256";

    @Value("${douyin.url.base}")
    private String baseUrl;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private CommonLogger log;
    @Autowired
    private DouyinAccessTokenService douyinAccessTokenService;

    @Autowired
    private ChannelVirtualConfigMapper channelVirtualConfigMapper;


    @Override
    protected Map generatePostParams(String url, String method, Map<String, Object> sysParam, Map<String, Object> bizParam) {
        return generatePostParams(method, sysParam, bizParam);
    }

    @Override
    public <T> T sendPost(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
        // 获取系统参数
        Map<String, Object> sysParam = getChannelSysParams(baseRequest.getTenantId(), baseRequest.getChannelId());

        return sendPost(postUrlEnum, baseRequest, bizParam, sysParam);
    }

    @Override
    public <T> T sendPostByVirtualConfig(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
        // 获取系统参数
        Map<String, Object> sysParam = getChannelVirtualSysParam(baseRequest.getTenantId(), baseRequest.getChannelId());

        return sendPost(postUrlEnum, baseRequest, bizParam, sysParam);
    }

    public <T> T sendPostByVirtualSysParam(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam, Map<String, Object> sysParam) {
        return sendPost(postUrlEnum, baseRequest, bizParam, sysParam);
    }


    private <T> T sendPost(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam, Map<String, Object> sysParam) {
        // 参数校验
        validateSendPost(postUrlEnum, bizParam);

        // 业务参数实体转换为Map
        Map<String, Object> bizParamMap = convertToMap(bizParam);

        // 基础数据拉取时，没有门店数据直接返回结果对象
        if (CollectionUtils.isEmpty(baseRequest.getStoreIdList())) {
            return postToChannel(postUrlEnum, sysParam, bizParamMap, baseRequest);
        }

        // 有门店信息接口调用时，多门店请求，返回以门店id为key的map
        Map<Long, T> res = Maps.newHashMap();
        Map<String, Object> finalBizParamMap = bizParamMap;
        Map<String, ChannelStoreDO> pois = getTenantPoiInfo(baseRequest);
        baseRequest.getStoreIdList().forEach(storeId -> {
            String channelStoreKey = KeyUtils.genChannelStoreKey(baseRequest.getTenantId(), baseRequest.getChannelId(), storeId);
            T object = postToChannel(pois.get(channelStoreKey).getChannelPoiCode(), pois.get(channelStoreKey).getChannelOnlinePoiCode(), postUrlEnum, sysParam, finalBizParamMap, baseRequest);
            res.put(storeId, object);
        });

        return (T) res;
    }


    private   Map<String, Object> convertToMap(Object object) {
        HashMap resultMap = new HashMap();

        try {
            if (object instanceof Map) {
                return (Map<String, Object>) object;
            }

            Field[] fields = object.getClass().getDeclaredFields();
            Field[] arr$ = fields;
            int len$ = fields.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                Field field = arr$[i$];
                //解决jacoco 问题
                if (field.isSynthetic()){
                    continue;
                }
                field.setAccessible(true);
                Object obj = field.get(object);
                String key = field.getName();

                if (obj == null) {
                    continue;
                }

                if (obj instanceof byte[]) {
                    resultMap.put(key, obj);
                } else if (obj instanceof List) {
                    if (((List)obj).size() > 0) {
                        resultMap.put(key, JSON.toJSONString(obj));
                    }
                } else if (obj instanceof ObjectStringParam) {
                    resultMap.put(key, JSON.toJSONString(obj));
                } else if (obj instanceof Map) {
                    resultMap.put(key, JSON.toJSONString(obj));
                } else {
                    resultMap.put(key, String.valueOf(obj));
                }

            }

            return resultMap;
        } catch (Exception var10) {
            throw new RuntimeException("对象转换异常！");
        }
    }

    private void validateSendPost(ChannelPostInter postUrlEnum, Object bizParam) {
        Preconditions.checkArgument(StringUtils.isNotBlank(postUrlEnum.getUrl()), "sendPost url is blank");
        Preconditions.checkNotNull(postUrlEnum.getResultClass(), "sendPost ResultClass is null");
        Preconditions.checkNotNull(bizParam, "sendPost bizParam is blank");
    }

    @Override
    public <T> T postToChannel(String channelPoiCode, String channelOnlinePoiCode, ChannelPostInter postUrlEnum, Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest) {
        if (StringUtils.isNotBlank(channelPoiCode) || StringUtils.isNotBlank(channelOnlinePoiCode)) {
            if (bizParamMap.containsKey("store_id")) {
                bizParamMap.put("store_id", channelOnlinePoiCode);
            }
        }
        String method = postUrlEnum.getUrlShortName();
        String url = addSignParam(baseUrl + postUrlEnum.getUrl(), method, sysParam, bizParamMap);
        // 调用
        String uuid = channelOnlinePoiCode;
        if (postUrlEnum.getRateLimitLevel() == RateLimitLevelEnum.APP) {
            Object obj = sysParam.get("shop_id");
            if (obj != null)
                uuid = String.valueOf(obj);
        }
        // 限频

        if (MccConfigUtil.getChannelLimiterErrorPoster().contains(postUrlEnum)) {
            if (postUrlEnum.requestLimited()) {
                boolean pass = clusterRateLimiter.tryAcquire(postUrlEnum, uuid);
                if (!pass) {
                    log.info("DouYinChannelGateService.postToChannel appId:{} url:{} 未获取到令牌 直接返回null", uuid,
                            postUrlEnum.getUrl());
                    return (T)ChannelResponseDTO.defaultLimitErrorResponse();
                }
            }
        }
        
        // 调用
        ChannelResponseDTO dto = dealPostResult(postRequestByJson(url, bizParamMap, Collections.emptyMap(), baseRequest), postUrlEnum);
        channelResponseMetric(ChannelTypeEnum.DOU_YIN, postUrlEnum, baseRequest, dto, ChannelResponseDTO::getResponseError);
        if (Objects.isNull(dto)) {
            log.warn("DouyinChannelGateService.postToChannel 执行post请求结果转换ChannelResponseDTO为null 直接返回null");
            // 请求结果为null,说明发生未知错误，需要重试
            dto = ChannelResponseDTO.defaultUnknownErrorResponse();
        }
        log.info("【渠道原始日志】, operate:{}, url:{}, success:{}, isErp:{}, request:{}, postParams:{}, result:{}",
                postUrlEnum.getUrlShortName(),
                postUrlEnum.getUrl(),
                dto.isSuccess(),
                baseRequest.isErpTenant(),
                baseRequest,
                JacksonUtils.toJson(bizParamMap),
                JacksonUtils.toJson(dto));
        return (T) dto;
    }


    /**
     * url后面添加签名参数
     * @param url
     * @param method
     * @param systemParam
     * @param bizParamMap
     * @return
     */
    private String addSignParam(String url, String method, Map<String, Object> systemParam, Map<String, Object> bizParamMap) {
        String appKey = (String) systemParam.get(ProjectConstant.DOUYIN_APP_KEY);
        String secret = (String) systemParam.get(ProjectConstant.DOUYIN_SECRET);
        String shopId = (String) systemParam.get(ProjectConstant.SHOP_ID);
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);

        DouyinTokenMessage tokenMessage = douyinAccessTokenService.getTokenFromRedis(appKey, shopId);
        if (Objects.isNull(tokenMessage)) {
            log.info("DouyinChannelGateService generatePostParams 获取token不存在，appkey={}, shopId={}", appKey, shopId);
            throw new BizException("douyin getTokenFromRedis 失败");
        }
        // url后添加请求参数
        return HttpUrl.parse(url)
                .newBuilder()
                .addQueryParameter("method", method)
                .addQueryParameter("app_key", appKey)
                .addQueryParameter("access_token", tokenMessage.getAccessToken())
                .addQueryParameter("timestamp", timestamp)
                .addQueryParameter("v", API_VERSION)
                .addQueryParameter("sign", DouyinSignUtil.getSign(appKey, secret, method, DouyinJsonUtil.toJson(bizParamMap), timestamp, API_VERSION))
                .addQueryParameter("sign_method", SIGN_METHOD)
                .build()
                .toString();
    }

    private ChannelResponseDTO dealPostResult(String resultJson, ChannelPostInter channelPostInter) {
        ChannelResponseDTO dto = ChannelResponseDTO.parseResult(resultJson, channelPostInter);
        return dto;
    }




    private Map<String, Object> generatePostParams(String method, Map<String, Object> sysParam,
                                                   Map<String, Object> bizParam) {
        String appKey = (String)sysParam.get(ProjectConstant.DOUYIN_APP_KEY);
        String secret = (String)sysParam.get(ProjectConstant.DOUYIN_SECRET);
        String shopId = (String)sysParam.get(ProjectConstant.SHOP_ID);
        DouyinTokenMessage tokenMessage = douyinAccessTokenService.getTokenFromRedis(appKey, shopId);
        if (Objects.isNull(tokenMessage)) {
            log.info("DouyinChannelGateService generatePostParams 获取token不存在，appkey={}, shopId={}", appKey, shopId);
            throw new BizException("douyin getTokenFromRedis 失败");
        }
        long timeStamp = DateUtils.unixTime();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("method", method);
        paramMap.put("app_key", appKey);
        paramMap.put("access_token", tokenMessage.getAccessToken());
        paramMap.put("param_json", JacksonUtils.toJson(bizParam));
        paramMap.put("timestamp", String.valueOf(timeStamp));
        paramMap.put("v", API_VERSION);
        paramMap.put("sign", DouyinSignUtils.getSign(appKey, secret, method, timeStamp, bizParam));
        paramMap.put("sign_method", SIGN_METHOD);
        return paramMap;
    }

    @Override
    protected String getPostUrl(ChannelPostInter postUrlEnum) {
        return baseUrl + postUrlEnum.getUrl();
    }

    @Override
    protected Map<String, Object> getChannelVirtualSysParam(Long tenantId, Integer channelId){
        try {
            return douyinAccessTokenService.getDouyinVirtualSysParam(tenantId, channelId);
        }catch (BizException e){
            log.error("获取抖音虚拟信息账号失败", e);
            return null;
        }
    }

    @Override
    public ChannelVirtualAccessConfigDO selectByTenantIdAndChannelId(Long tenantId, Integer channelId) {
        return channelVirtualConfigMapper.selectByTenantChannel(tenantId, channelId);
    }

    @Override
    public List<ChannelVirtualAccessConfigDO> pageQueryTenantChannelConfig(VirtualAccessConfigListPageQueryDTO pageQuery) {
        return channelVirtualConfigMapper.selectByPageQuery(pageQuery);
    }

}
