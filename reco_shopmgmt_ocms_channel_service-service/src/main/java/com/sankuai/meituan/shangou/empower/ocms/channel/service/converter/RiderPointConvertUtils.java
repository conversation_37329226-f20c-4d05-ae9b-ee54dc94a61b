package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.DapRiderPointCallbackRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.DapRiderPointInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.RiderPointBatchSyncRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.RiderPointInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryOrderResponse;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/31
 */
@Slf4j
public class RiderPointConvertUtils {


    /**
     * 封装配送中骑手点位回传参数
     *
     * @param request 具体参数
     * @param size    点位分割个数
     */
    public static List<Map<String, Object>> buildMtChanelSyncRiderPointParams(RiderPointBatchSyncRequest request, int size) {
        try {
            List<RiderPointInfoRequest> riderPointInfoRequestList = request.getRiderPointInfoRequestList();
            // 按50个一组,
            List<List<RiderPointInfoRequest>> partition = Lists.partition(riderPointInfoRequestList, size);

            List<Map<String, Object>> params = Lists.newArrayList();
            for (List<RiderPointInfoRequest> riderPointInfoRequests : partition) {
                Map<String, Object> param = Maps.newHashMap();
                param.put("order_id", request.getChannelOrderId());
                param.put("third_carrier_order_id", request.getDeliveryOrderId());
                param.put("third_logistics_id", DeliveryChannelCodeConvert.getMtDeliveryChannelCode(request.getDeliveryChannelId()));
                //骑手轨迹
                List<Map<String, Object>> pointTrace = riderPointInfoRequests.stream().map(item -> {
                    Map<String, Object> riderPointInfo = Maps.newHashMap();
                    riderPointInfo.put("rider_name", item.getRiderName());
                    riderPointInfo.put("latitude", item.getLatitude());
                    riderPointInfo.put("longitude", item.getLongitude());
                    riderPointInfo.put("back_flow_time", item.getBackFlowTime());
                    return riderPointInfo;
                }).collect(Collectors.toList());

                param.put("rider_track_points", JSON.toJSONString(pointTrace));
                params.add(param);
            }
            return params;
        } catch (Exception e) {
            log.error("buildDeliveringRiderPointParams error", e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建正在配送的骑手轨迹
     */
    public static BaseRequest buildPointSyncBaseRequest(RiderPointBatchSyncRequest request) {
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setChannelId(request.getChannelId());
        baseRequest.setTenantId(request.getTenantId());
        baseRequest.setOrderId(request.getChannelOrderId());
        baseRequest.setStoreIdList(Collections.singletonList(request.getStoreId()));
        return baseRequest;
    }

    /**
     * 构建正在配送的骑手轨迹
     */
    public static RiderPointBatchSyncRequest convertDapToSyncReq(DapRiderPointCallbackRequest request, QueryDeliveryOrderResponse queryDeliveryOrderResponse) {

        try {
            List<TDeliveryOrder> list = queryDeliveryOrderResponse.getTDeliveryOrders();
            // 根据配送单id查询配送单信息
            TDeliveryOrder tDeliveryOrder = list.stream()
                    .filter(item -> Objects.equals(item.getChannelDeliveryId(), request.getDeliveryId()))
                    .findAny().orElse(null);
            if (Objects.isNull(tDeliveryOrder)) {
                log.info("配送单不存在");
                return null;
            }
            //是否开启骑手轨迹上传配置
            if (!MccConfigUtil.checkOpenRiderSyncV2(tDeliveryOrder.getStoreId(), tDeliveryOrder.getTenantId())) {
                log.info("骑手轨迹批量上传功能未开启");
                return null;
            }
            DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(tDeliveryOrder.getOrderBizType());
            if (Objects.isNull(channelType)) {
                log.info("渠道不存在");
                return null;
            }
            //请求参数封装
            RiderPointBatchSyncRequest req = new RiderPointBatchSyncRequest();
            req.setTenantId(tDeliveryOrder.getTenantId());
            req.setChannelId(channelType.getChannelId());
            req.setStoreId(tDeliveryOrder.getStoreId());
            req.setOrderId(tDeliveryOrder.getOrderId());
            req.setChannelOrderId(tDeliveryOrder.getChannelOrderId());
            req.setDeliveryOrderId(tDeliveryOrder.getId());
            req.setDeliveryChannelId(tDeliveryOrder.getDeliveryChannel());
            List<RiderPointInfoRequest> riderPointInfoRequestList = request.buildJsonPoints().stream().map(item -> {
                RiderPointInfoRequest riderPointInfoRequest = new RiderPointInfoRequest();
                riderPointInfoRequest.setRiderName(item.getName());
                riderPointInfoRequest.setLatitude(BigDecimal.valueOf(item.getLat()).divide(BigDecimal.valueOf(1000_000), 6, RoundingMode.HALF_UP).doubleValue());
                riderPointInfoRequest.setLongitude(BigDecimal.valueOf(item.getLng()).divide(BigDecimal.valueOf(1000_000), 6, RoundingMode.HALF_UP).doubleValue());
                riderPointInfoRequest.setBackFlowTime(item.getBackFlowTime());
                return riderPointInfoRequest;
            }).collect(Collectors.toList());
            req.setRiderPointInfoRequestList(riderPointInfoRequestList);
            return req;
        } catch (Exception e) {
            log.error("convertDapToSyncReq error", e);
            return null;
        }
    }


}
