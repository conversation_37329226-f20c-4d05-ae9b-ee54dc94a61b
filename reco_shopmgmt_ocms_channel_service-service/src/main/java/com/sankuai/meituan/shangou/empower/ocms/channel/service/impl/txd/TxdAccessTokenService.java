package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.txd.TxdTokenResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.TopAuthTokenCreateRequest;
import com.taobao.api.response.TopAuthTokenCreateResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @program: reco_shopmgmt_ocms_channel_service
 * @description: 淘鲜达授权服务
 * @author: jinyi
 * @create: 2024-04-29 16:32
 **/
@Slf4j
@Service
public class TxdAccessTokenService {


    /**
     * 淘鲜达创建token
     * @param appKey
     * @param secret
     * @param code
     * @return
     */
    public TxdTokenResult createAccessToken(String appKey, String secret, String code){
        TopAuthTokenCreateRequest req = new TopAuthTokenCreateRequest();
        req.setCode(code);

        TaobaoClient client = new DefaultTaobaoClient(MccConfigUtil.getTxdHttpsUrl(), appKey, secret);

        try {
            TopAuthTokenCreateResponse response = client.execute(req);
            if (Objects.isNull(response) || !response.isSuccess() || StringUtils.isBlank(response.getTokenResult())){
                log.error("TxdAccessTokenService.createAccessToken 获取token失败, msg={}", response.getMessage());
            }
            log.info("TxdAccessTokenService.createAccessToken token={}", response.getTokenResult());
            TxdTokenResult tokenResult = JacksonUtils.parse(response.getTokenResult(), TxdTokenResult.class);
            return tokenResult;
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }
}
