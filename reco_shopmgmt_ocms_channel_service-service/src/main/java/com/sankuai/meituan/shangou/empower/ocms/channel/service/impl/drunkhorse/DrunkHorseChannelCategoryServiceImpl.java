package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCategoryService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSkuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelCategoryServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/01/03
 */
@Service("mtDrunkHorseChannelCategoryService")
public class DrunkHorseChannelCategoryServiceImpl extends MtChannelCategoryServiceImpl implements ChannelCategoryService {

    @Autowired
    @Qualifier("mtDrunkHorseChannelGateService")
    private MtChannelGateService mtChannelGateService;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.getSpTagIds}")
    private String getSpTagIds;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.categoryCreate}")
    private String categoryCreate;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.categoryAttrList}")
    private String getCategoryAttrList;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.categoryAttrValueList}")
    private String getCategoryAttrValueList;

    @Autowired
    @Qualifier("mtDrunkHorseChannelSkuService")
    private ChannelSkuService mtChannelSkuService;


    @Override
    public CategorySmartSortQueryResponse queryStoreCategorySmartSort(CategorySmartSortQueryRequest request) {

        CatRequest catRequest = new CatRequest();
        BaseRequestSimple simple = new BaseRequestSimple();
        simple.setChannelId(request.getChannelId());
        simple.setTenantId(request.getTenantId());
        catRequest.setBaseInfo(simple);
        catRequest.setStoreId(request.getStoreId());
        GetCategoryResponse getCategoryResponse = getMtChannelSkuService().batchGetChannelStoreCategoryInfo(catRequest);
        if (getCategoryResponse.getCatInfoList() == null){
            return new CategorySmartSortQueryResponse().setStatus(ResultGenerator.genResult(ResultCode.SUCCESS))
                    .setData(Collections.emptyList());
        }

        List<CategorySmartSortDTO> data = new ArrayList<>();
        getCategoryResponse.getCatInfoList().forEach(cat -> {
            CategorySmartSortDTO dto = new CategorySmartSortDTO();
            dto.setChannelCategoryCode(cat.getCatId());
            dto.setSmartSort(cat.getSmartSort() == Constant.VALUE_SMART_SORT_SWITCH_ON);
            dto.setName(cat.getName());
            data.add(dto);
        });
        return new CategorySmartSortQueryResponse().setStatus(ResultGenerator.genResult(ResultCode.SUCCESS))
                .setData(data);
    }

    @Override
    public GetCategoryResponse queryStoreCategoryList(CatRequest request) {
        return null;
    }
    @Override
    public MtChannelGateService getMtChannelGateService() {
        return mtChannelGateService;
    }

    @Override
    public String getGetSpTagIds() {
        return getSpTagIds;
    }

    @Override
    public String getCategoryCreate() {
        return categoryCreate;
    }

    @Override
    public String getGetCategoryAttrList() {
        return getCategoryAttrList;
    }

    @Override
    public String getGetCategoryAttrValueList() {
        return getCategoryAttrValueList;
    }

    @Override
    public ChannelSkuService getMtChannelSkuService() {
        return mtChannelSkuService;
    }
}
