package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ChannelErrorCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.AuthInfoRespDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelPoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelResponseResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ShopBatchGetRespDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ShopIdListRespDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.PoiChannelAppThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseChannelPoiRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiDetailsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiIdsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.AppPoiCodeDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiAuthRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiHotlineRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.GetPoiIdsResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.GetPoiInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.GetPoiPromotionInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiAddressUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiAuthDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiPrebookDaysUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiPromotionInfoUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiShippingTimeUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.QueryPoiAuthDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ShippingTime;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.UpdatePoiAuthInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.UpdateSaleafterAddressMessageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.UpdateSaleafterAddressMessageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.CoordinateTransformUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.common.PoiChannelConstant;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.AppInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByAppIdRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByTenantRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.RHINO_UPTIMATE_SET;
import static org.apache.curator.shaded.com.google.common.base.Preconditions.checkNotNull;

/**
 * @author:huchangwu
 * @date: 2019/1/17
 * @time: 下午2:46
 */
@Service("elmChannelPoiService")
public class ElmChannelPoiServiceImpl implements ChannelPoiService {

    @Resource
    private ElmConverterService elmConverterService;
    @Resource
    private ElmChannelGateService elmChannelGateService;
    @Resource
    private BaseConverterService baseConverterService;
    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Autowired
    private PoiChannelAppThriftServiceProxy poiChannelAppThriftServiceProxy;

    @Resource
    private CommonLogger log;

    @Value("${elm.url.base}")
    private String baseUrl;

//    shoplist接口下线
//    @Value("${elm.url.shoplist}")
//    private String shoplist;

    @Value("${elm.url.shopIdList}")
    private String shopIdList;

    @Value("${elm.url.shopBatchGet}")
    private String shopBatchGet;

    @Value("${elm.url.shopget}")
    private String shopget;

    @Value("${elm.url.skushopauthget}")
    private String skushopauthget;

    @Value("${elm.url.skushopauthcontrol}")
    private String skushopauthcontrol;

    /**
     * shop.id.list 接口分页查询 的page_size
     */
    private static final int SHOP_ID_LIST_PAGE_SIZE = 100;

    /**
     * common.token.getuser 接口分页查询 的page_size
     * @param req
     * @return
     */
    private static final int COMMON_TOKEN_GETUSER_PAGE_SIZE = 50;

    private static ThreadPoolExecutor channelIdExecutor = Rhino.newThreadPool("elm_get_channel_id_thread_pool",
                    DefaultThreadPoolProperties.Setter()
                            .withCoreSize(10).withMaxSize(20).withBlockingQueue(new SynchronousQueue<>())
                            .withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy())
                            .withThreadFactory(new ThreadFactoryBuilder().setNameFormat("elm_get_channel_id_thread_pool" + "-%d").build()))
            .getExecutor();



    @Override
    public GetPoiInfoResponse batchGetPoiInfo(BaseRequestSimple req) {
        GetPoiInfoResponse resp = new GetPoiInfoResponse();

        long tenantId = req.getTenantId();
        int channelId = req.getChannelId();
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOList)) {
            log.warn("获取租户品牌数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取租户品牌数据失败"));
        }

        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.BATCH_GET_POIINFO, String.valueOf(tenantId))) {
            log.warn("查询门店列表 获取令牌失败 不阻塞流程 直接调用接口， request{}", req);
        }

        List<PoiInfo> poiInfoListInAllApps = new ArrayList<>();

        List<String> elmIsvAppKeyList = MccConfigUtil.getElmIsvAppKeyList();

        // 按渠道/品牌应用维度进行查询
        for (CopAccessConfigDO copAccessConfigDO : copAccessConfigDOList) {
            //双写数据，判断是否属于饿了么isv应用，属于则忽略cop数据
            if (elmIsvAppKeyList.contains(copAccessConfigDO.getTenantAppId())){
                continue;
            }

            long appId = copAccessConfigDO.getAppId() == null ? 1L : copAccessConfigDO.getAppId();
            BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);

            List<String> shopIds = getShopIdList(baseRequest, null);

            // 查询门店详情
            for (String shopId : shopIds) {
                Map<String, Object> shopIdMap = new HashMap<>();
                shopIdMap.put(ProjectConstant.SHOP_ID, shopId);
                if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.GET_POIDETAIL, String.valueOf(tenantId))) {
                    log.warn("查询门店详情 获取令牌失败 不阻塞流程 直接调用接口， request{}, shopId:{}", req, shopId);
                }
                Map<String, Object> poiInfoMap = elmChannelGateService.sendPostApp(baseUrl, shopget, baseRequest, shopIdMap);
                JSONObject poiInfoJsonObject = (JSONObject) poiInfoMap.get(ProjectConstant.BODY);
                if ((int) poiInfoJsonObject.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
                    // 一个应用查询失败，直接全部失败
                    String errorMsg = poiInfoJsonObject.getString(ProjectConstant.ERROR);
                    log.error("查询门店详情失败 tenantId: {} - channelId: {} - appId: {} - shopId: {} 错误信息：{}", tenantId, channelId, appId, shopId, errorMsg);
                    return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店详情失败：" + errorMsg));
                }
                JSONObject poiJsonObject = poiInfoJsonObject.getJSONObject(ProjectConstant.DATA);
                ChannelPoiInfo channelPoiInfo = poiJsonObject.toJavaObject(ChannelPoiInfo.class);
                PoiInfo poiInfo = elmConverterService.poiInfoMapping(channelPoiInfo);
                CoordinateTransformUtil.CoordinatePoint coordinatePoint = CoordinateTransformUtil.bd09ToGcj02(poiInfo.getLongitude(), poiInfo.getLatitude());
                poiInfo.setLongitude(coordinatePoint.getLongitude());
                poiInfo.setLatitude(coordinatePoint.getLatitude());

                poiInfoListInAllApps.add(poiInfo);
            }

        }

        if (poiInfoListInAllApps.isEmpty()) {
            log.warn("查询门店列表为空 tenantId: {} - channelId: {}", tenantId, channelId);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表为空"));
        }
        resp.setStatus(ResultGenerator.genSuccessResult());
        resp.setPoiInfoList(poiInfoListInAllApps);
        return resp;
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIds(BaseRequestSimple req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        long tenantId = req.getTenantId();
        int channelId = req.getChannelId();

        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.BATCH_GET_POIINFO, String.valueOf(tenantId))) {
            log.warn("查询门店列表 获取令牌失败 不阻塞流程 直接调用接口， request{}", req);
        }

        List<String> totalShopIdsInAllApps = new ArrayList<>();
        List<AppPoiCodeDTO> appPoiCodeDTOList = new ArrayList<>();

        //开关控制新老逻辑
        if (MccConfigUtil.batchQueryChannelPoiIdList(tenantId)) {
            List<AppPoiCodeDTO> appPoiCodeDTOS = batchGetPoiIdsNew(tenantId, channelId);
            appPoiCodeDTOList.addAll(appPoiCodeDTOS);
        }else {
            batchGetPoiIdsForSoftwareApp(tenantId, channelId,totalShopIdsInAllApps,appPoiCodeDTOList);
            batchGetPoiIdsForIsv(tenantId, channelId,totalShopIdsInAllApps,appPoiCodeDTOList);
        }

        if (totalShopIdsInAllApps.isEmpty() && appPoiCodeDTOList.isEmpty()) {
            log.warn("查询门店列表为空 tenantId: {} - channelId: {}", tenantId, channelId);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表为空"));
        }

        // 门店id去重，去重逻辑：ISV应用 > 软件应用
        List<AppPoiCodeDTO> distinctList = distinctAppPoiCodeDTOList(appPoiCodeDTOList);
        List<String> distinctShopIds = Fun.map(distinctList, AppPoiCodeDTO::getAppPoiCode);

        log.info("batchGetPoiIds 最终获取门店的总数量:{}",distinctShopIds.size());

        resp.setStoreIds(distinctShopIds);
        resp.setAppPoiCodeDTOList(distinctList);
        return resp;
    }

    private List<AppPoiCodeDTO> batchGetPoiIdsNew(Long tenantId, Integer channelId){
        List<AppPoiCodeDTO> appPoiCodeDTOList = new ArrayList<>();
        List<List<AppPoiCodeDTO>> result = new ArrayList<>();
        try {

            result = Arrays.asList(
                    batchGetPoiIdsForSoftwareApp(tenantId, channelId),
                    batchGetPoiIdsForIsv(tenantId, channelId)
            ).parallelStream().map(task -> {
                try {
                    return task;
                } catch (Exception e) {
                    log.error("并行流 执行批量获取门店ID任务失败", e);
                    throw new BizException("并行流 执行批量获取门店ID任务失败");
                }
            }).collect(Collectors.toList());
        } catch (InterruptedException e) {
            // 捕获线程中断异常
            Thread.currentThread().interrupt();
            log.error("batchGetPoiIdsNew 执行批量获取门店ID任务失败 Thread was interrupted", e);
            throw new BizException("执行批量获取门店ID任务发生异常");
        } catch (Exception e) {
            log.error("batchGetPoiIdsNew 执行批量获取门店ID任务发生异常", e);
            throw new BizException("执行批量获取门店ID任务发生异常");
        }
        result.forEach(appPoiCodeDTOList::addAll);
        return appPoiCodeDTOList;
    }

    private List<AppPoiCodeDTO> distinctAppPoiCodeDTOList(List<AppPoiCodeDTO> appPoiCodeDTOList){
        // 过滤掉空值
        appPoiCodeDTOList = Fun.filter(appPoiCodeDTOList, k-> Objects.nonNull(k) || StringUtils.isNotBlank(k.getAppPoiCode()) || k.getAppId() > 0);

        // 当有重复 appPoiCode 时，保留较大的 appId
        Map<String, AppPoiCodeDTO> poiCodeAppIdMap = appPoiCodeDTOList.stream()
                .collect(Collectors.toMap(
                        k -> k.getAppPoiCode(),
                        v -> v,
                        (v1, v2) -> v1.getAppId() > v2.getAppId() ? v1 : v2
                ));

        return new ArrayList<>(poiCodeAppIdMap.values());
    }


    /**
     * ISV应用获取渠道门店id
     * @param tenantId
     * @param channelId
     * @param totalShopIdsInAllApps
     * @param appPoiCodeDTOList
     */
    private void batchGetPoiIdsForIsv(long tenantId, int channelId, List<String> totalShopIdsInAllApps, List<AppPoiCodeDTO> appPoiCodeDTOList){
        AppInfoQueryByTenantRequest request= new AppInfoQueryByTenantRequest();
        request.setTenantId(tenantId);
        request.setChannelIdList(Arrays.asList(channelId));
        List<AppInfoDTO> appInfoDTOList = poiChannelAppThriftServiceProxy.queryAppInfoByTenant(request);
        if (CollectionUtils.isEmpty(appInfoDTOList)) {
            log.warn("获取租户应用数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            return;
        }

        for (AppInfoDTO appInfoDTO : appInfoDTOList) {
            try{
                String appKey = appInfoDTO.getAppKey();
                String secret = appInfoDTO.getSecret();
                String accessToken = appInfoDTO.getAccessToken();

                // 查询平台门店id
                List<String> baiduShopIds = getAuthBaiduShopIds(appKey, secret, accessToken);
                // 平台门店id -> 第三方门店id
                List<String> shopIds = getShopIdListByBaiduShopIdsForIsv(appKey, secret, accessToken, baiduShopIds);

                if (CollectionUtils.isNotEmpty(shopIds)) {
                    totalShopIdsInAllApps.addAll(shopIds);
                    // appPoiCode 关联 appId 进行透出
                    appPoiCodeDTOList.addAll(Fun.map(shopIds, shopId -> new AppPoiCodeDTO(shopId, appInfoDTO.getQnhAppId())));
                }
            }catch (Exception e){
                log.warn("batchGetPoiIdsForIsv 查询第三方门店id失败, appInfoDTO={}", appInfoDTO);
            }
        }
    }

    /**
     * ISV应用获取渠道门店id
     * @param tenantId
     * @param channelId
     */
    private List<AppPoiCodeDTO> batchGetPoiIdsForIsv(long tenantId, int channelId) throws ExecutionException, InterruptedException {
        List<AppPoiCodeDTO> appPoiCodeDTOList = new ArrayList<>();
        AppInfoQueryByTenantRequest request= new AppInfoQueryByTenantRequest();
        request.setTenantId(tenantId);
        request.setChannelIdList(Arrays.asList(channelId));
        List<AppInfoDTO> appInfoDTOList = poiChannelAppThriftServiceProxy.queryAppInfoByTenant(request);
        if (CollectionUtils.isEmpty(appInfoDTOList)) {
            log.warn("获取租户应用数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            return appPoiCodeDTOList;
        }

        for (AppInfoDTO appInfoDTO : appInfoDTOList) {

            String appKey = appInfoDTO.getAppKey();
            String secret = appInfoDTO.getSecret();
            String accessToken = appInfoDTO.getAccessToken();

            // 查询平台门店id
            List<String> baiduShopIds = getAuthBaiduShopIdsNewForIsv(appKey, secret, accessToken);

            // 平台门店id -> 第三方门店id
            List<String> shopIds = getShopIdListByBaiduShopIdsForIsvNew(appKey, secret, accessToken, baiduShopIds);

            if (CollectionUtils.isNotEmpty(shopIds)) {
                // appPoiCode 关联 appId 进行透出
                appPoiCodeDTOList.addAll(Fun.map(shopIds, shopId -> new AppPoiCodeDTO(shopId, appInfoDTO.getQnhAppId())));
            }

        }
        return appPoiCodeDTOList;
    }

    /**
     * 获取授权平台门店id列表
     * @param appKey
     * @param secret
     * @param accessToken
     * @return
     */
    private List<String> getAuthBaiduShopIds(String appKey, String secret, String accessToken) {
        List<String> baiduShopIdList = new ArrayList<>();

        int page = 1;
        int pageSize = COMMON_TOKEN_GETUSER_PAGE_SIZE;
        boolean hasMore = true;

        do {
            Map<String, Object> sysParam = elmChannelGateService.buildIsvSysParam(appKey, secret, accessToken);
            Map<String, Object> bizParam = new HashMap<>();
            bizParam.put("page", page);
            bizParam.put("pageSize", pageSize);

            try {
                ChannelResponseDTO channelResponseDTO = elmChannelGateService.postToChannel(ChannelPostELMEnum.COMMON_TOKEN_GETUSER, sysParam, bizParam);
                ChannelResponseResult<AuthInfoRespDTO> channelResponseResult = channelResponseDTO.getBody();
                if (!channelResponseDTO.isSuccess() || Objects.isNull(channelResponseResult.getData()) || Objects.isNull(channelResponseResult.getData().getOpenid())) {
                    log.error("获取饿了么授权信息失败 sysParam={},  bizParam={}, channelResponseDTO={}", sysParam, bizParam, channelResponseDTO);
//                throw new BizException("获取饿了么授权信息失败");
                    break;
                }
                List<String> platformShopIdList = Fun.map(channelResponseResult.getData().getAuthShopList(), AuthInfoRespDTO.AuthShopInfo::getPlatformShopId);
                baiduShopIdList.addAll(platformShopIdList);

                if ("1".equals(channelResponseResult.getData().getType()) || "2".equals(channelResponseResult.getData().getType())){
                    log.info("饿了么散店和连锁子店分页参数不生效 sysParam={},  bizParam={}，page:{}", sysParam, bizParam,page);
                    break;
                }

                int total = channelResponseResult.getData().getTotal();
                hasMore = total > pageSize * page;
            }catch (Exception e){
                log.error("获取饿了么授权信息失败 sysParam={},  bizParam={}", sysParam, bizParam);
                break;
            }

            page++;
            if (page > 1000) { // TODO 兜底逻辑，怕饿了么那边有莫名奇妙的问题导致死循环
                log.error("获取饿了么授权信息失败 page 分页太多,sysParam:{},bizParam:{}",sysParam,bizParam);
                break;
            }
        }while (hasMore);
        return baiduShopIdList;
    }

    /**
     * 获取授权平台门店id列表
     * @param appKey
     * @param secret
     * @param accessToken
     * @return
     */
    private List<String> getAuthBaiduShopIdsNewForIsv(String appKey, String secret, String accessToken) throws ExecutionException, InterruptedException {
        List<String> baiduShopIdList = new ArrayList<>();

        int page = 1;
        int pageSize = COMMON_TOKEN_GETUSER_PAGE_SIZE;
        Map<String, Object> sysParam = elmChannelGateService.buildIsvSysParam(appKey, secret, accessToken);
        Map<String, Object> bizParam = new HashMap<>();
        bizParam.put("page", page);
        bizParam.put("pageSize", pageSize);

        Map<String, Object> sysParamCopy = Optional.ofNullable(sysParam).map(HashMap::new).orElse(null);
        long startTime = System.currentTimeMillis();
        ChannelResponseDTO<AuthInfoRespDTO> channelResponseDTO = elmChannelGateService.postToChannel(ChannelPostELMEnum.COMMON_TOKEN_GETUSER, sysParamCopy, bizParam);
        long endTime = System.currentTimeMillis();
        log.info("ISV:耗时日志：getAuthBaiduShopIdsNew 耗时:{},返回结果:{}", endTime - startTime,channelResponseDTO);
        ChannelResponseResult<AuthInfoRespDTO> channelResponseResult = channelResponseDTO.getBody();
        if (!channelResponseDTO.isSuccess() || Objects.isNull(channelResponseResult.getData()) || Objects.isNull(channelResponseResult.getData().getOpenid())) {
            log.error("获取饿了么授权信息失败 sysParam={},  bizParam={}, channelResponseDTO={}", sysParam, bizParam, channelResponseDTO);
            return baiduShopIdList;
        }
        List<String> platformShopIdList = Fun.map(channelResponseResult.getData().getAuthShopList(), AuthInfoRespDTO.AuthShopInfo::getPlatformShopId);
        baiduShopIdList.addAll(platformShopIdList);

        if ("1".equals(channelResponseResult.getData().getType()) || "2".equals(channelResponseResult.getData().getType())) {
            log.info("饿了么散店和连锁子店分页参数不生效 sysParam={},  bizParam={}，page:{}", sysParam, bizParam, page);
            return baiduShopIdList;
        }

        int total = channelResponseResult.getData().getTotal();
        //total总数大于分页数，需要分页查询
        if (total > pageSize) {
            int pageCount = total % pageSize == 0 ? total/pageSize : total/pageSize +1;
            List<Integer> pageList = IntStream.range(2, pageCount + 1).boxed().collect(Collectors.toList());
            log.info("饿了么分页查询门店信息，pageList:{}",pageList);
            int batchNumber = MccConfigUtil.queryElmShopBatchGetNumber();
            List<List<Integer>> pagePartition = Lists.partition(pageList, batchNumber);
            for (List<Integer> pageNoList : pagePartition) {
                List<Future<List<String>>> tasks = new ArrayList<>();
                for (Integer pageNo : pageNoList) {
                    tasks.add(channelIdExecutor.submit(() -> getBaiduShopIdsForIsv(pageNo, pageSize, sysParam)));
                }
                //等待batchSize个线程执行完成
                for (Future<List<String>> task : tasks) {
                    baiduShopIdList.addAll(task.get());
                }
                tasks.clear();
            }
        }
        return baiduShopIdList;
    }


    private List<String> getBaiduShopIdsForIsv(int page, int pageSize, Map<String, Object> sysParam) {
        Map<String, Object> sysParamCopy = Optional.ofNullable(sysParam).map(HashMap::new).orElse(null);
        List<String> baiduShopIdList = new ArrayList<>();
        Map<String, Object> bizParam = new ConcurrentHashMap<>();
        bizParam.put("page", page);
        bizParam.put("pageSize", pageSize);
        long startTime = System.currentTimeMillis();
        ChannelResponseDTO<AuthInfoRespDTO> channelResponseDTO = elmChannelGateService.postToChannel(ChannelPostELMEnum.COMMON_TOKEN_GETUSER, sysParamCopy, bizParam);
        long endTime = System.currentTimeMillis();
        log.info("ISV:耗时日志:饿了么分页查询门店信息,{}，page:{},耗时:{},返回结果:{}",ChannelPostELMEnum.COMMON_TOKEN_GETUSER.getUrl(),page,endTime - startTime,channelResponseDTO);
        ChannelResponseResult<AuthInfoRespDTO> channelResponseResult = channelResponseDTO.getBody();
        if (!channelResponseDTO.isSuccess() || Objects.isNull(channelResponseResult.getData()) || Objects.isNull(channelResponseResult.getData().getOpenid())) {
            log.error("获取饿了么授权信息失败 sysParam={},  bizParam={}, channelResponseDTO={}", sysParamCopy, bizParam, channelResponseDTO);
            return baiduShopIdList;
        }
        List<String> platformShopIdList = Fun.map(channelResponseResult.getData().getAuthShopList(), AuthInfoRespDTO.AuthShopInfo::getPlatformShopId);
        baiduShopIdList.addAll(platformShopIdList);

        if ("1".equals(channelResponseResult.getData().getType()) || "2".equals(channelResponseResult.getData().getType())) {
            log.info("饿了么散店和连锁子店分页参数不生效 sysParam={},  bizParam={}，page:{}", sysParamCopy, bizParam, page);
            return baiduShopIdList;
        }
        return baiduShopIdList;
    }

    /**
     * ISV应用-查询平台门店id列表
     * @param appKey
     * @param secret
     * @param accessToken
     * @return
     */
    private List<String> getBaiduShopIdsForIsv(String appKey, String secret, String accessToken){
        List<String> baiduShopIdList = new ArrayList<>();

        int page = 1;
        int pageSize = SHOP_ID_LIST_PAGE_SIZE;
        boolean hasMore = true;

        do {
            Map<String, Object> bizParam = new HashMap<>();
            bizParam.put("page", page);
            bizParam.put("page_size", pageSize);

            // 查询平台门店id，https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.id.list-3?aopApiCategory=shop_all&type=api_menu
            Map<String, Object> sysParam = elmChannelGateService.buildIsvSysParam(appKey, secret, accessToken);
            ChannelResponseDTO channelResponseDTO = elmChannelGateService.postToChannel(ChannelPostELMEnum.SHOP_ID_LIST, sysParam, bizParam);
            if (!channelResponseDTO.isSuccess()){
                log.error("ISV应用查询平台门店id列表失败，sysParam={}, errMsg={}", sysParam, channelResponseDTO.getErrorMsg());
                throw new BizException("ISV应用查询平台门店id列表失败");
            }
            ChannelResponseResult<ShopIdListRespDTO> channelResponseResult = channelResponseDTO.getBody();
            List<String> batchBaiduShopIdList = Fun.map(channelResponseResult.getData().getShop_list(), ShopIdListRespDTO.ShopIdListDetailRespDTO::getBaidu_shop_id);
            baiduShopIdList.addAll(batchBaiduShopIdList);

            int total = channelResponseResult.getData().getTotal();
            hasMore = total > pageSize * page;
            page++;
        }while (hasMore);

        return baiduShopIdList;
    }

    /**
     * ISV应用-根据平台门店id查询第三方门店id
     * @param appKey
     * @param secret
     * @param accessToken
     * @param baiduShopIdList
     * @return
     */
    private List<String> getShopIdListByBaiduShopIdsForIsv(String appKey, String secret, String accessToken, List<String> baiduShopIdList){
        List<String> shopIdList = new ArrayList<>();

        List<List<String>> baiduShopIdBatchList = ListUtils.partition(baiduShopIdList, SHOP_ID_LIST_PAGE_SIZE);

        for (List<String> baiduShopIdBatch : baiduShopIdBatchList) {
            Map<String, Object> bizParam = new HashMap<>();
            bizParam.put("platform_shop_id_list", baiduShopIdBatch);
            // https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.batch.get-3?aopApiCategory=shop_all&type=api_menu
            Map<String, Object> sysParam = elmChannelGateService.buildIsvSysParam(appKey, secret, accessToken);
            ChannelResponseDTO channelResponseDTO = elmChannelGateService.postToChannel(ChannelPostELMEnum.SHOP_BATCH_GET, sysParam, bizParam);
            if (!channelResponseDTO.isSuccess()){
                log.error("ISV应用-根据平台门店id查询第三方门店id失败，sysParam={}, bizParam={}, errMsg={}", sysParam, bizParam, channelResponseDTO.getErrorMsg());
                throw new BizException("ISV应用-根据平台门店id查询第三方门店id");
            }
            List<ShopBatchGetRespDTO> shopBatchGetRespDTOList = JacksonUtils.parse(
                    (String)channelResponseDTO.getBody().getData(), new TypeReference<List<ShopBatchGetRespDTO>>() {});
            List<String> batchShopIdList = Fun.map(shopBatchGetRespDTOList, ShopBatchGetRespDTO::getThirdPartyShopId);
            shopIdList.addAll(batchShopIdList);
        }
        return shopIdList;
    }

    /**
     * ISV应用-根据平台门店id查询第三方门店id
     * @param appKey
     * @param secret
     * @param accessToken
     * @param baiduShopIdList
     * @return
     */
    private List<String> getShopIdListByBaiduShopIdsForIsvNew(String appKey, String secret, String accessToken, List<String> baiduShopIdList) throws ExecutionException, InterruptedException {
        List<String> shopIdList = new ArrayList<>();

        int batchNumber = MccConfigUtil.queryElmShopBatchGetNumber();

        List<List<String>> pageSizeBatchList = ListUtils.partition(baiduShopIdList,  SHOP_ID_LIST_PAGE_SIZE);
        List<List<List<String>>> batchNumberBatchList = ListUtils.partition(pageSizeBatchList, batchNumber);


        for (List<List<String>> batchNumberList : batchNumberBatchList) {
            List<Future<List<String>>> tasks = new ArrayList<>();
            for (List<String> baiduShopIds : batchNumberList) {
                Map<String, Object> sysParam = elmChannelGateService.buildIsvSysParam(appKey, secret, accessToken);
                tasks.add(channelIdExecutor.submit(() -> getShopIdListByBaiduShopIdsForIsv(sysParam, baiduShopIds)));
            }
            for (Future<List<String>> task : tasks) {
                shopIdList.addAll(task.get());
            }
            tasks.clear();
        }
        return shopIdList;
    }

    private List<String> getShopIdListByBaiduShopIdsForIsv(Map<String, Object> sysParam,List<String> baiduShopIds){
        Map<String, Object> bizParam = new HashMap<>();
        bizParam.put("platform_shop_id_list", baiduShopIds);
        // https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.batch.get-3?aopApiCategory=shop_all&type=api_menu

        long startTime = System.currentTimeMillis();
        ChannelResponseDTO channelResponseDTO = elmChannelGateService.postToChannel(ChannelPostELMEnum.SHOP_BATCH_GET, sysParam, bizParam);
        long endTime = System.currentTimeMillis();
        log.info("ISV:耗时日志:饿了么分页查询门店信息,{}，耗时:{},返回结果:{}",ChannelPostELMEnum.SHOP_BATCH_GET.getUrl(),endTime - startTime,channelResponseDTO);
        if (!channelResponseDTO.isSuccess()){
            log.error("ISV应用-根据平台门店id查询第三方门店id失败，sysParam={}, bizParam={}, errMsg={}", sysParam, bizParam, channelResponseDTO.getErrorMsg());
            throw new BizException("ISV应用-根据平台门店id查询第三方门店id失败");
        }
        List<ShopBatchGetRespDTO> shopBatchGetRespDTOList = JacksonUtils.parse(
                (String)channelResponseDTO.getBody().getData(), new TypeReference<List<ShopBatchGetRespDTO>>() {});
        return Fun.map(shopBatchGetRespDTOList, ShopBatchGetRespDTO::getThirdPartyShopId);
    }

    /**
     * 软件应用获取渠道门店id
     * @param tenantId
     * @param channelId
     * @param totalShopIdsInAllApps
     * @param appPoiCodeDTOList
     */
    private void batchGetPoiIdsForSoftwareApp(long tenantId, int channelId, List<String> totalShopIdsInAllApps, List<AppPoiCodeDTO> appPoiCodeDTOList) {
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOList)) {
            log.warn("获取租户品牌数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            return;
        }
        List<String> elmIsvAppKeyList = MccConfigUtil.getElmIsvAppKeyList();

        // 按渠道/品牌应用维度进行查询
        for (CopAccessConfigDO copAccessConfigDO : copAccessConfigDOList) {
            //双写数据，判断是否属于饿了么isv应用，属于则忽略cop数据
            if (elmIsvAppKeyList.contains(copAccessConfigDO.getTenantAppId())){
                continue;
            }
            long appId = copAccessConfigDO.getAppId() == null ? 1L : copAccessConfigDO.getAppId();
            BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);

            List<String> shopIds = getShopIdList(baseRequest, null);

            if (CollectionUtils.isNotEmpty(shopIds)) {
                totalShopIdsInAllApps.addAll(shopIds);
                // appPoiCode 关联 appId 进行透出
                for (String appPoiCode : shopIds) {
                    AppPoiCodeDTO appPoiCodeDTO = new AppPoiCodeDTO(appPoiCode, appId);
                    appPoiCodeDTOList.add(appPoiCodeDTO);
                }
            }
        }
    }

    /**
     * 软件应用获取渠道门店id
     * @param tenantId
     * @param channelId
     */
    private List<AppPoiCodeDTO> batchGetPoiIdsForSoftwareApp(long tenantId, int channelId) {
        List<AppPoiCodeDTO> appPoiCodeDTOList = new ArrayList<>();
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOList)) {
            log.warn("获取租户品牌数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            return appPoiCodeDTOList;
        }
        List<String> elmIsvAppKeyList = MccConfigUtil.getElmIsvAppKeyList();

        // 按渠道/品牌应用维度进行查询
        for (CopAccessConfigDO copAccessConfigDO : copAccessConfigDOList) {
            //双写数据，判断是否属于饿了么isv应用，属于则忽略cop数据
            if (elmIsvAppKeyList.contains(copAccessConfigDO.getTenantAppId())){
                continue;
            }
            long appId = copAccessConfigDO.getAppId() == null ? 1L : copAccessConfigDO.getAppId();
            BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);

            List<String> shopIds = getShopIdListNewForSoftwareApp(baseRequest, null);

            if (CollectionUtils.isNotEmpty(shopIds)) {
                // appPoiCode 关联 appId 进行透出
                for (String appPoiCode : shopIds) {
                    AppPoiCodeDTO appPoiCodeDTO = new AppPoiCodeDTO(appPoiCode, appId);
                    appPoiCodeDTOList.add(appPoiCodeDTO);
                }
            }
        }
        return appPoiCodeDTOList;
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIdsBySysParam(BatchGetPoiIdsRequest req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        Map<String, Object> sysParam = JSON.parseObject(req.getSysParams());
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setTenantId(req.getTenantId());
        baseRequest.setChannelId(req.getChannelId());

        List<String> shopIds = getShopIdList(baseRequest, sysParam);

        return resp.setStoreIds(shopIds);
    }

    @Override
    public GetPoiInfoResponse batchGetPoiDetails(BatchGetPoiDetailsRequest request) {
        GetPoiInfoResponse resp = new GetPoiInfoResponse();

        long tenantId = request.getTenantId();
        int channelId = request.getChannelId();
        List<String> appPoiCodeList = request.getStoreIds();
        if (CollectionUtils.isEmpty(appPoiCodeList)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "需传入门店的开放平台编码"));
        }

        List<PoiInfo> poiInfoListInAllApps = new ArrayList<>();

        // 根据是否传入 appId 进行处理。
        // 注：当初始化拉取信息时，一定需要传入 appId，因为此时渠道门店信息表中还没有数据，也就不能根据 appPoiCode 去关联了
        if (request.isSetAppId()) {
            try {
                long appId = request.getAppId();
                List<PoiInfo> poiInfoList = getPoiInfoListByAppId(request, appPoiCodeList, appId);
                if (CollectionUtils.isNotEmpty(poiInfoList)) {
                    poiInfoListInAllApps.addAll(poiInfoList);
                }
            } catch (InvokeChannelTooMuchException e) {
                return resp.setStatus(new ResultStatus(ResultCodeEnum.TRIGGER_LIMIT.getValue(), "", String.valueOf(e.getWaitTimeMills())));
            }
        } else {
            // 根据 appPoiCode 获取 appId
            Map<String, Long> poiCodeAppIdMapping = poiCode2AppId(tenantId, channelId, appPoiCodeList);

            // 按照 appId 将 appPoiCode 分组
            Map<Long, List<String>> appPoiCodeGroupByAppIdMap = new HashMap<>();
            for (Map.Entry<String, Long> entry : poiCodeAppIdMapping.entrySet()) {
                String appPoiCode = entry.getKey();
                Long appId = entry.getValue();
                List<String> list = appPoiCodeGroupByAppIdMap.getOrDefault(appId, new ArrayList<>());
                list.add(appPoiCode);
                appPoiCodeGroupByAppIdMap.put(appId, list);
            }

            // 按照 appId 维度进行查询
            for (Map.Entry<Long, List<String>> entry : appPoiCodeGroupByAppIdMap.entrySet()) {
                long appId = entry.getKey();
                List<String> appPoiCodeListInAppId = entry.getValue();
                try {
                    List<PoiInfo> poiInfoList = getPoiInfoListByAppId(request, appPoiCodeListInAppId, appId);
                    if (CollectionUtils.isNotEmpty(poiInfoList)) {
                        poiInfoListInAllApps.addAll(poiInfoList);
                    }
                } catch (InvokeChannelTooMuchException e) {
                    return resp.setStatus(new ResultStatus(ResultCodeEnum.TRIGGER_LIMIT.getValue(), "", String.valueOf(e.getWaitTimeMills())));
                }
            }
        }

        if (poiInfoListInAllApps.isEmpty()) {
            log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appPoiCodeList: {}", tenantId, channelId, appPoiCodeList);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表为空"));
        }
        resp.setStatus(ResultGenerator.genSuccessResult());
        resp.setPoiInfoList(poiInfoListInAllApps);
        return resp;
    }

    /**
     * 根据appId和渠道门店编码获取门店详情
     * @param request
     * @param appPoiCodeList
     * @param appId appId必传
     * @return
     */
    private List<PoiInfo> getPoiInfoListByAppId(BatchGetPoiDetailsRequest request, List<String> appPoiCodeList, Long appId) {
        List<PoiInfo> poiInfoList = new ArrayList<>();

        long tenantId = request.getTenantId();
        int channelId = request.getChannelId();
        // 软件应用走老的门店详情获取逻辑，isv应用需要从poiChannel查询应用信息
        if(appId < PoiChannelConstant.ELM_ISV_QNH_APP_ID_BEGIN){
            // 软件应用
            poiInfoList = getPoiInfoInApp(tenantId, channelId, appId, appPoiCodeList, request.isAsyncInvoke());
        }else {
            // ISV应用
            poiInfoList = getPoiInfoIForIsv(tenantId, channelId, appId, appPoiCodeList, request.isAsyncInvoke());
        }
        return poiInfoList;
    }


    /**
     * 获取租户、渠道、poiCode 维度下，poiCode 到 appId 的映射信息
     * 当从渠道门店基础信息表中获取不到关联数据，且租户只关联一个品牌时，进行兜底
     *
     * @param tenantId    租户id
     * @param channelId   渠道id
     * @param poiCodeList 商家门店编码列表
     * @return
     */
    private Map<String, Long> poiCode2AppId(long tenantId, int channelId, List<String> poiCodeList) {
        Map<String, Long> poiCodeAppIdMapping = copChannelStoreService.getPoiCodeAppIdMapping(tenantId, channelId, poiCodeList);
        if (poiCodeAppIdMapping == null || poiCodeAppIdMapping.isEmpty() || poiCodeAppIdMapping.size() != poiCodeList.size()) {
            // 如果在渠道门店基础表中不存在数据，这里进行兜底
            // 或者存在部分数据时也进行兜底
            List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
            if (CollectionUtils.isNotEmpty(copAccessConfigDOList) &&  copAccessConfigDOList.size() == 1){
                Long appId = copAccessConfigDOList.get(0).getAppId();
                return poiCodeList.stream().collect(Collectors.toMap(poiCode -> poiCode, poiCode -> appId));
            }else if (CollectionUtils.isEmpty(copAccessConfigDOList)){
                List<AppInfoDTO> appInfoDTOList = poiChannelAppThriftServiceProxy.queryAppInfoByTenant(new AppInfoQueryByTenantRequest(tenantId, Arrays.asList(channelId)));
                if (CollectionUtils.isNotEmpty(appInfoDTOList) &&  appInfoDTOList.size() == 1){
                    Long appId = appInfoDTOList.get(0).getQnhAppId();
                    return poiCodeList.stream().collect(Collectors.toMap(poiCode -> poiCode, poiCode -> appId));
                }else {
                    throw new IllegalArgumentException("租户关联的品牌数为空或超过一个，不能获取对应密钥信息");
                }
            }else {
                throw new IllegalArgumentException("租户关联的品牌数为空或超过一个，不能获取对应密钥信息");
            }

        } else {
            return poiCodeAppIdMapping;
        }
    }

    private Long poiCode2AppId(long tenantId, int channelId, String poiCode) {
        Map<String, Long> map = poiCode2AppId(tenantId, channelId, Collections.singletonList(poiCode));
        return map.get(poiCode);
    }

    /**
     * 根据appId获取渠道门店信息
     *
     * @param tenantId 租户id
     * @param channelId 渠道di
     * @param appId 品牌应用id
     * @param appPoiCodeListInAppId 渠道商家门店编码列表
     * @param isAsyncInvoke 是否异步，限流用参数
     * @return
     */
    private List<PoiInfo> getPoiInfoIForIsv(long tenantId, int channelId, Long appId, List<String> appPoiCodeListInAppId, boolean isAsyncInvoke) {
        List<PoiInfo> poiInfoList = new ArrayList<>();
        BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);

        // 拉取渠道门店时，渠道门店未和中台门店绑定，因此只能通过appId获取应用信息
        if (Objects.isNull(appId)){
            log.warn("getPoiInfoIForIsv appId为空");
            return new ArrayList<>();
        }

        // 限频检查
        rateLimitManage(String.valueOf(tenantId), ChannelPostELMEnum.GET_POIDETAIL, isAsyncInvoke);

        AppInfoQueryByAppIdRequest request = new AppInfoQueryByAppIdRequest();
        request.setTenantId(tenantId);
        request.setChannelId(channelId);
        request.setQnhAppId(appId);
        AppInfoDTO appInfoDTO = poiChannelAppThriftServiceProxy.queryAppInfoByAppId(request);
        if (Objects.isNull(appInfoDTO)){
            log.warn("getPoiInfoIForIsv appInfoDTO为空，tenantId={}，channelId={}, appId={}", tenantId, channelId, appId);
            return new ArrayList<>();
        }

        for (String shopId : appPoiCodeListInAppId) {
            Map<String, Object> sysParam = elmChannelGateService.buildIsvSysParam(appInfoDTO.getAppKey(),
                    appInfoDTO.getSecret(), appInfoDTO.getAccessToken());
            Map<String, Object> bizParam = new HashMap<>();
            bizParam.put(ProjectConstant.SHOP_ID, shopId);

            Map poiInfoMap = elmChannelGateService.sendPost(baseUrl, shopget, baseRequest, bizParam, sysParam);
            JSONObject poiInfoJsonObject = (JSONObject) poiInfoMap.get(ProjectConstant.BODY);
            if ((int) poiInfoJsonObject.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
                String errorMsg = poiInfoJsonObject.getString(ProjectConstant.ERROR);
                log.error("查询门店详情失败 tenantId: {} - channelId: {} - appId: {} - shopId: {} 错误信息：{}", tenantId, channelId, appId, shopId, errorMsg);
                throw new RuntimeException("查询门店详情失败：" + errorMsg);
            }
            JSONObject poiJsonObject = poiInfoJsonObject.getJSONObject(ProjectConstant.DATA);
            String businessTime2String = formatBusinessTime2((Map<String, Object>) poiJsonObject.get("business_time2"));

            ChannelPoiInfo channelPoiInfo = poiJsonObject.toJavaObject(ChannelPoiInfo.class);
            PoiInfo poiInfo = elmConverterService.poiInfoMapping(channelPoiInfo);
            CoordinateTransformUtil.CoordinatePoint coordinatePoint = CoordinateTransformUtil.bd09ToGcj02(poiInfo.getLongitude(), poiInfo.getLatitude());
            poiInfo.setLongitude(coordinatePoint.getLongitude());
            poiInfo.setLatitude(coordinatePoint.getLatitude());
            poiInfo.setHotline(channelPoiInfo.getService_phone());
            // 将businessTime2String赋值给poiInfo
            poiInfo.setShippingTime(businessTime2String);
            poiInfoList.add(poiInfo);
        }
        fillAppId(poiInfoList, appId, appInfoDTO.getAppKey());
        return poiInfoList;
    }

    /**
     * 获取某个品牌下的渠道应用信息
     *
     * @param tenantId              租户id
     * @param channelId             渠道id
     * @param appId                 品牌应用id
     * @param appPoiCodeListInAppId 渠道商家门店编码列表
     * @param isAsyncInvoke         是否异步，限流用参数
     * @return 渠道应用信息列表
     */
    private List<PoiInfo> getPoiInfoInApp(long tenantId, int channelId, long appId, List<String> appPoiCodeListInAppId, boolean isAsyncInvoke) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);
        // 每个 appId 维度查询一次即可
        Map<String, Object> sysParam = elmChannelGateService.getSysParam(baseRequest);
        String appKey = Optional.ofNullable(sysParam).map(k->(String)k.getOrDefault("source", "")).orElse("");
        List<PoiInfo> poiInfoList = new ArrayList<>();

        for (String shopId : appPoiCodeListInAppId) {
            Map<String, Object> shopIdMap = new HashMap<>();
            shopIdMap.put(ProjectConstant.SHOP_ID, shopId);
            // 限频检查
            rateLimitManage(String.valueOf(tenantId), ChannelPostELMEnum.GET_POIDETAIL, isAsyncInvoke);

            // sysParam 每次会被改写，因此每次新建一个 Map
            Map<String, Object> sysParamTmp = new HashMap<>(sysParam);
            Map poiInfoMap = elmChannelGateService.sendPost(baseUrl, shopget, baseRequest, shopIdMap, sysParamTmp);
            JSONObject poiInfoJsonObject = (JSONObject) poiInfoMap.get(ProjectConstant.BODY);
            if ((int) poiInfoJsonObject.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
                String errorMsg = poiInfoJsonObject.getString(ProjectConstant.ERROR);
                log.error("查询门店详情失败 tenantId: {} - channelId: {} - appId: {} - shopId: {} 错误信息：{}", tenantId, channelId, appId, shopId, errorMsg);
                throw new RuntimeException("查询门店详情失败：" + errorMsg);
            }
            JSONObject poiJsonObject = poiInfoJsonObject.getJSONObject(ProjectConstant.DATA);
            String businessTime2String = formatBusinessTime2((Map<String, Object>) poiJsonObject.get("business_time2"));

            ChannelPoiInfo channelPoiInfo = poiJsonObject.toJavaObject(ChannelPoiInfo.class);
            PoiInfo poiInfo = elmConverterService.poiInfoMapping(channelPoiInfo);
            CoordinateTransformUtil.CoordinatePoint coordinatePoint = CoordinateTransformUtil.bd09ToGcj02(poiInfo.getLongitude(), poiInfo.getLatitude());
            poiInfo.setLongitude(coordinatePoint.getLongitude());
            poiInfo.setLatitude(coordinatePoint.getLatitude());
            poiInfo.setHotline(channelPoiInfo.getService_phone());
            // 将businessTime2String赋值给poiInfo
            poiInfo.setShippingTime(businessTime2String);
            poiInfoList.add(poiInfo);
        }
        fillAppId(poiInfoList, appId, appKey);
        return poiInfoList;
    }

    /**
     * 在 PoiInfo 中填充 appId 信息
     *
     * @param poiInfos
     * @param appId
     */
    private void fillAppId(List<PoiInfo> poiInfos, long appId, String appKey) {
        if (CollectionUtils.isEmpty(poiInfos)) {
            return;
        }
        for (PoiInfo poiInfo : poiInfos) {
            poiInfo.setAppId(appId);
            poiInfo.setAppKey(appKey);
        }
    }

    @Override
    public GetPoiIdsResponse getPoiInnerId(BatchGetPoiDetailsRequest request) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        return resp.setStatus(ResultGenerator.genFailResult("饿了么不支持该功能"));
    }

    @Override
    public ResultStatus poiOpen(ChannelPoiIdRequest request) {
        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SHOP_OPEN, String.valueOf(request.getTenantId()))) {
            log.warn("查询门店列表 获取令牌失败 不阻塞流程 直接调用接口， request{}", request);
        }
        return poiChangeBusinessMode(request, "shop.open");
    }

    @Override
    public ResultStatus poiClose(ChannelPoiIdRequest request) {
        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SHOP_CLOSE, String.valueOf(request.getTenantId()))) {
            log.warn("查询门店列表 获取令牌失败 不阻塞流程 直接调用接口， request{}", request);
        }
        return poiChangeBusinessMode(request, "shop.close");
    }

    @Override
    public ResultStatus promotionInfoUpdate(PoiPromotionInfoUpdateRequest req) {
        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SHOP_PROMOTION_SET, String.valueOf(req.getTenantId()))) {
            log.warn("查询门店列表 获取令牌失败 不阻塞流程 直接调用接口， request{}", req);
        }

        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.SHOP_ID, req.getChannelPoiCode());
        bizParam.put(ProjectConstant.ELM_PROMOTION_INFO, req.getPromotionInfo());

        BaseRequest baseRequest = toBaseRequest(req.getTenantId(), req.getChannelId(), req.getStoreId(), req.getChannelPoiCode());
        Map<String, Object> poiOpenResp = elmChannelGateService.sendPostApp(baseUrl, "shop.announcement.set", baseRequest, bizParam);

        JSONObject poiOpenRespJson = (JSONObject) poiOpenResp.get(ProjectConstant.BODY);
        if ((int) poiOpenRespJson.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return ResultGenerator.genResult(ResultCode.FAIL, poiOpenRespJson.getString(ProjectConstant.ERROR));
        }

        return ResultGenerator.genSuccessResult();
    }

    /**
     * 营业时间更新
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus shippingTimeUpdate(PoiShippingTimeUpdateRequest req) {
        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SHOP_UPDATE, String.valueOf(req.getTenantId()))) {
            log.warn("查询门店列表 获取令牌失败 不阻塞流程 直接调用接口， request{}", req);
        }

        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.SHOP_ID, req.getChannelPoiCode());
        Map<String, Object> shippingTimeParam = convertShippingTime(req.getShippingTimes(), req.getShippingDays());
        bizParam.put(ProjectConstant.ELM_NEW_SHIPPING_TIME, shippingTimeParam);

        BaseRequest baseRequest = toBaseRequest(req.getTenantId(), req.getChannelId(), req.getStoreId(), req.getChannelPoiCode());
        Map<String, Object> poiOpenResp = elmChannelGateService.sendPostApp(baseUrl, "shop.update", baseRequest, bizParam);

        JSONObject poiOpenRespJson = (JSONObject) poiOpenResp.get(ProjectConstant.BODY);
        if ((int) poiOpenRespJson.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return ResultGenerator.genResult(ResultCode.FAIL, poiOpenRespJson.getString(ProjectConstant.ERROR));
        }

        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus prebookStatusOpen(ChannelPoiIdRequest req) {
        return ResultGenerator.genFailResult("饿了么暂不支持预订单设置");
    }

    @Override
    public ResultStatus prebookStatusClose(ChannelPoiIdRequest req) {
        return ResultGenerator.genFailResult("饿了么暂不支持预订单设置");
    }

    /**
     * 更新门店的接受预定的日期范围
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus updatePrebookDays(PoiPrebookDaysUpdateRequest req) {
        return ResultGenerator.genFailResult("饿了么暂不支持预订单接受日期范围设置");
    }

    /**
     * 获取门店公告信息
     *
     * @param req
     * @return
     */
    @Override
    public GetPoiPromotionInfoResponse getPoiPromotionInfo(ChannelPoiIdRequest req) {

        GetPoiPromotionInfoResponse response = new GetPoiPromotionInfoResponse();
        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SHOP_PROMOTION_GET, String.valueOf(req.getTenantId()))) {
            log.warn("查询门店列表 获取令牌失败 不阻塞流程 直接调用接口， request{}", req);
        }

        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.SHOP_ID, req.getChannelPoiCode());

        BaseRequest baseRequest = toBaseRequest(req.getTenantId(), req.getChannelId(), req.getStoreId(),req.getAppId(), req.getChannelPoiCode());

        Map<String, Object> poiOpenResp = elmChannelGateService.sendPostApp(baseUrl, "shop.announcement.get", baseRequest, bizParam);

        JSONObject body = (JSONObject)poiOpenResp.get(ProjectConstant.BODY);

        if ((int) body.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, body.get(ProjectConstant.ERROR)));
        }

        JSONObject data = (JSONObject)body.get(ProjectConstant.DATA);
        response.setPromotionInfo(data.getString(ProjectConstant.ELM_PROMOTION_INFO));
        response.setStatus(ResultGenerator.genSuccessResult());

        return response;
    }

    /**
     * 获取门店营业状态
     *
     * @param req
     * @return
     */
    @Override
    public ChannelPoiStatusResponse getPoiStatus(ChannelPoiIdRequest req) {
        ChannelPoiStatusResponse response = new ChannelPoiStatusResponse();
        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SHOP_BUSINESS_GET, String.valueOf(req.getTenantId()))) {
            log.warn("查询门店列表 获取令牌失败 不阻塞流程 直接调用接口， request{}", req);
        }

        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.SHOP_ID, req.getChannelPoiCode());
        bizParam.put("platformFlag", "1");

        BaseRequest baseRequest = toBaseRequest(req.getTenantId(), req.getChannelId(), req.getStoreId(), req.getChannelPoiCode());
        Map<String, Object> poiOpenResp = elmChannelGateService.sendPostApp(baseUrl, "shop.busstatus.get", baseRequest, bizParam);

        JSONObject body = (JSONObject)poiOpenResp.get(ProjectConstant.BODY);

        if ((int) body.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, body.get(ProjectConstant.ERROR)));
        }

        JSONObject data = (JSONObject)body.get(ProjectConstant.DATA);
        response.setPoiStatus(data.getIntValue(ProjectConstant.ELM_BUSINESS_STATUS));
        response.setStatus(ResultGenerator.genSuccessResult());

        return response;
    }

    /**
     * 门店授权
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genFailResult("饿了么渠道不支持授权");
    }

    /**
     * 门店解析授权
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiDeAuth(ChannelPoiAuthRequest request) {
        return ResultGenerator.genFailResult("饿了么渠道不支持解除授权");
    }

    @Override
    public ResultStatus poiHotlineUpdate(ChannelPoiHotlineRequest req) {
        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SHOP_UPDATE, String.valueOf(req.getTenantId()))) {
            log.warn("查询门店列表 获取令牌失败 不阻塞流程 直接调用接口， request{}", req);
        }

        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.SHOP_ID, req.getChannelPoiCode());
        bizParam.put(ProjectConstant.ELM_HOTLINE, req.getHotline());
        //增加storeId
        BaseRequest baseRequest = toBaseRequest(req.getTenantId(), req.getChannelId(), req.getStoreId(), req.getChannelPoiCode());
        Map<String, Object> poiOpenResp = elmChannelGateService.sendPostApp(baseUrl, "shop.update", baseRequest, bizParam);

        JSONObject poiOpenRespJson = (JSONObject) poiOpenResp.get(ProjectConstant.BODY);
        if ((int) poiOpenRespJson.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return ResultGenerator.genResult(ResultCode.FAIL, poiOpenRespJson.getString(ProjectConstant.ERROR));
        }

        return ResultGenerator.genSuccessResult();
    }

    @Override
    public QueryPoiAuthDetailResponse queryPoiAuthDetailInfo(BaseChannelPoiRequestSimple request) {
        QueryPoiAuthDetailResponse response = new QueryPoiAuthDetailResponse();

        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).
                setChannelId(request.getChannelId()).setStoreIdList(Lists.newArrayList(request.getPoiId()));

        Map<String, Object> sysParam = elmChannelGateService.getSysParam(baseRequest);
        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(request.getTenantId(), request.getChannelId(), request.getPoiId());

        if (channelStore == null) {
            return response.setStatus(ResultGenerator.genResult(ResultCodeEnum.FAIL, "关联渠道门店不存在"));
        }

        Map<String, Object> shopIdMap = new HashMap<>();
        shopIdMap.put(ProjectConstant.SHOP_ID, channelStore.getChannelOnlinePoiCode());
        Map<String, Object> sysParamTmp = new HashMap<>(sysParam);
        Map poiAuthInfoMap = elmChannelGateService.sendPost(baseUrl, skushopauthget, baseRequest, shopIdMap, sysParamTmp);

        JSONObject poiAuthInfoJsonObject = (JSONObject) poiAuthInfoMap.get(ProjectConstant.BODY);
        if ((int) poiAuthInfoJsonObject.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            String errorMsg = poiAuthInfoJsonObject.getString(ProjectConstant.ERROR);
            log.error("查询门店商品编辑权限详情失败 request: {} 错误信息：{}", request, errorMsg);
            response.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, errorMsg));
            return response;
        }

        response.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));
        JSONObject authTypeInfoJsonObject = poiAuthInfoJsonObject.getJSONObject(ProjectConstant.DATA);
        if (authTypeInfoJsonObject != null && authTypeInfoJsonObject.containsKey("cusCatAuthTypeDtoList")) {
            JSONArray authTypeArrayJsonObj = authTypeInfoJsonObject.getJSONArray("cusCatAuthTypeDtoList");
            List<PoiAuthDetailDTO> poiAuthDetailDTOList = authTypeArrayJsonObj.toJavaList(PoiAuthDetailDTO.class);
            response.setPoiAuthDetailList(poiAuthDetailDTOList);
        }

        return response;
    }

    /**
     * 根据接口文档和系统设计，需要storeName参数，在ocmschannel不好处理，所以选择由外部传参
     * https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.shop.auth.control-3?aopApiCategory=item_manage&type=item_all
     */
    @Override
    public ResultStatus updatePoiAuthInfo(UpdatePoiAuthInfoRequest request) {
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
                .setChannelId(request.getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()))
                .setAppId(request.getAppId());
        Map<String, Object> shopIdMap = new HashMap<>();
        Map<String, Object> sysParam = elmChannelGateService.getAppChannelSysParams(request.getTenantId(), request.getChannelId(), baseRequest.getAppId(), request.getStoreId());
//        String poiAuthInfoJsonStr = JSONObject.toJSONString(request.getPoiAuthDetailList());

        shopIdMap.put("cusCatAuthTypeDtoList", request.getPoiAuthDetailList());
        shopIdMap.put(ProjectConstant.SHOP_ID, request.getChannelPoiCode());
        shopIdMap.put(ProjectConstant.SHOP_NAME, request.getChannelPoiName());

        // 限频检查
        rateLimitManage(String.valueOf(request.getChannelId()), ChannelPostELMEnum.UPDATE_POI_AUTH, false);

        // sysParam 每次会被改写，因此每次新建一个 Map
        Map<String, Object> sysParamTmp = new HashMap<>(sysParam);
        Map poiAuthUpdateInfoMap = elmChannelGateService.sendPost(baseUrl, skushopauthcontrol, baseRequest, shopIdMap, sysParamTmp);
        JSONObject poiAuthUpdateMap = (JSONObject) poiAuthUpdateInfoMap.get(ProjectConstant.BODY);
        if (Objects.equals(poiAuthUpdateMap.getString(ProjectConstant.ERROR), "参数错误-散店无需设置店铺权限") || Objects.equals(poiAuthUpdateMap.getString(ProjectConstant.ERROR), "成功")) {
            // 分别对应饿了么单店以及零售开启结果，根据对方开放平台回复，不能使用errno来判断是否成功，必须通过error中的msg来判断
            return ResultGenerator.genResult(ResultCode.SUCCESS);
        }

        if ((int) poiAuthUpdateMap.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            String errorMsg = poiAuthUpdateMap.getString(ProjectConstant.ERROR);
            log.error("更新连锁管品门店编辑权限失败 request: {} 错误信息：{}", request, errorMsg);
            return ResultGenerator.genResult(ResultCodeEnum.FAIL, errorMsg);
        }

        return ResultGenerator.genResult(ResultCode.SUCCESS);
    }

    @Override
    public ResultStatus updatePoiAddress(PoiAddressUpdateRequest request) {
        return ResultGenerator.genFailResult("饿了么渠道不支持");
    }

    @Override
    public UpdateSaleafterAddressMessageResponse updateSaleafterAddressMessage(UpdateSaleafterAddressMessageRequest request) {
        return ResultGenerator.UpdateSafeAddressResultData(ResultCode.FAIL, "饿了么渠道暂不支持此功能");
    }


    public Map<String,String> shippingTimeToMap(ShippingTime shippingTime) {
        Map<String,String> shippingTimeMap = Maps.newHashMap();
        shippingTimeMap.put("start", shippingTime.getStartTime());
        shippingTimeMap.put("end", shippingTime.getEndTime());
        return shippingTimeMap;
    }

    private BaseRequest toBaseRequest(long tenantId, int channelId, long storeId, long appId, String channelPoiCode){
        BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(channelId);
        if (storeId > 0) {
            baseRequest.setStoreIdList(Lists.newArrayList(storeId));
        }else if (appId > 0){
            baseRequest.setAppId(appId);
        }else if (StringUtils.isNotBlank(channelPoiCode)){
            Long qnhAppId = poiCode2AppId(tenantId, channelId, channelPoiCode);
            if (Objects.nonNull(qnhAppId)) {
                baseRequest.setAppId(qnhAppId);
            }
        } else{
            log.warn("未传入 storeId、appId， 并且 channelPoiCode 转化 BaseRequest 无效,storeId:{},appId:{},channelPoiCode:{}", storeId,appId,channelPoiCode);
        }
        return baseRequest;
    }

    /**
     * 根据 storeId 或者 channelPoiCode 转化 BaseRequest
     */
    private BaseRequest toBaseRequest(long tenantId, int channelId, long storeId, String channelPoiCode) {
        BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(channelId);
        if (storeId > 0) {
            baseRequest.setStoreIdList(Lists.newArrayList(storeId));
        } else if (StringUtils.isNotBlank(channelPoiCode)) {
            Long appId = poiCode2AppId(tenantId, channelId, channelPoiCode);
            if (Objects.nonNull(appId)) {
                baseRequest.setAppId(appId);
            }
        } else {
            log.warn("根据 storeId 或者 channelPoiCode 转化 BaseRequest 无效");
        }
        return baseRequest;
    }


    private ResultStatus poiChangeBusinessMode(ChannelPoiIdRequest req, String cmd) {


        Map<String, Object> shopIdMap = new HashMap<>();
        shopIdMap.put(ProjectConstant.SHOP_ID, req.getChannelPoiCode());

        BaseRequest baseRequest = toBaseRequest(req.getTenantId(), req.getChannelId(), req.getStoreId(), req.getChannelPoiCode());
        Map<String, Object> poiOpenResp = elmChannelGateService.sendPostApp(baseUrl, cmd, baseRequest, shopIdMap);

        JSONObject poiOpenRespJson = (JSONObject) poiOpenResp.get(ProjectConstant.BODY);
        if ((int) poiOpenRespJson.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            int status = (int)poiOpenRespJson.get(ProjectConstant.ERRNO);
            if (checkBusinessModeStatus(status)) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genResult(ResultCode.FAIL, poiOpenRespJson.getString(ProjectConstant.ERROR));
            }
        }

        return ResultGenerator.genSuccessResult();
    }


    /**
     * 检查错误码,如果是已开店再开店或已闭店再闭店,则直接返回成功
     * @param status
     * @return
     */
    private boolean checkBusinessModeStatus(int status) {
        if (ChannelErrorCode.ElmErrorCode.STORE_ALREADY_OPEN == status) {
            return true;
        } else if (ChannelErrorCode.ElmErrorCode.STORE_ALREADY_CLOSED == status) {
            return true;
        }
        return false;
    }

    /**
     * 限频控制
     * @param appId
     * @param postEnum
     * @param isAsync
     */
    private void rateLimitManage(String appId, ChannelPostELMEnum postEnum, Boolean isAsync) {
        long waitTime = clusterRateLimiter.tryAcquire(postEnum, appId, isAsync);
        // 同步调用出现限频时，不进行限频优化
        if (waitTime != 0 && !isAsync) {
            log.warn("ElmChannelPoiService appId:{} url:{} 获取令牌失败 不阻塞流程 直接调用接口", appId, postEnum.getUrl());
            return;
        }
        if (waitTime !=0) {
            if (RHINO_UPTIMATE_SET.contains(postEnum)) {
                log.info("ElmChannelPoiService appId:{} url:{} 获取令牌失败  抛出异常", appId, postEnum.getUrl());
                throw new InvokeChannelTooMuchException(waitTime);
            } else {
                log.warn("ElmChannelPoiService appId:{} url:{} 获取令牌失败 不阻塞流程 直接调用接口", appId, postEnum.getUrl());
            }
        }
    }

    /**
     * 查询 第三方门店id列表
     *
     * @param baseRequest
     * @return
     */
    private List<String> getShopIdList(BaseRequest baseRequest, Map<String, Object> sysParam) {
        // 底层会修改sysParam，需要提前备份出来
        Map<String, Object> sysParamCopy = Optional.ofNullable(sysParam).map(k -> new HashMap<>(k)).orElse(null);

        // 查询平台门店id
        List<String> baiduShopIds = getBaiduShopIds(baseRequest, sysParam);

        // 平台门店id -> 第三方门店id
        List<String> shopIds = getShopIdListByBatchSize(baseRequest, sysParamCopy, baiduShopIds,
                SHOP_ID_LIST_PAGE_SIZE);

        return shopIds;
    }

    /**
     * 查询 第三方门店id列表
     *
     * @param baseRequest
     * @return
     */
    private List<String> getShopIdListNewForSoftwareApp(BaseRequest baseRequest, Map<String, Object> sysParam) {
        // 底层会修改sysParam，需要提前备份出来
        Map<String, Object> sysParamCopy = Optional.ofNullable(sysParam).map(HashMap::new).orElse(null);

        // 查询平台门店id
        List<String> baiduShopIds = getBaiduShopIdsNewForSoftwareApp(baseRequest, sysParamCopy);

        // 平台门店id -> 第三方门店id
        return getShopIdListByBatchSizeNewForSoftwareApp(baseRequest, sysParamCopy, baiduShopIds);
    }

    /**
     * 分批查询，根据 平台门店id 查询 第三方门店id
     *
     * @param baseRequest
     * @param baiduShopIds
     * @param batchSize
     * @return
     */
    private List<String> getShopIdListByBatchSize(BaseRequest baseRequest, Map<String, Object> sysParam,
                                                  List<String> baiduShopIds, int batchSize) {
        List<String> shopIdList = new ArrayList<>();

        List<String> tmpBaiduShopIds = new ArrayList<>();

        for (String baiduShopId : baiduShopIds) {
            tmpBaiduShopIds.add(baiduShopId);

            if (tmpBaiduShopIds.size() >= batchSize) {
                shopIdList.addAll(getShopIdListByBaiduShopIds(baseRequest, sysParam, tmpBaiduShopIds));
                tmpBaiduShopIds.clear();
            }
        }

        if (CollectionUtils.isNotEmpty(tmpBaiduShopIds)) {
            shopIdList.addAll(getShopIdListByBaiduShopIds(baseRequest, sysParam, tmpBaiduShopIds));
            tmpBaiduShopIds.clear();
        }
        return shopIdList;
    }

    /**
     * 分批查询，根据 平台门店id 查询 第三方门店id
     *
     * @param baseRequest
     * @param baiduShopIds
     * @return
     */
    private List<String> getShopIdListByBatchSizeNewForSoftwareApp(BaseRequest baseRequest, Map<String, Object> sysParam,
                                                                   List<String> baiduShopIds) {
        List<String> shopIdList = new ArrayList<>();
        int batchGetNumber = MccConfigUtil.queryElmShopBatchGetNumber();
        List<List<String>> partition = Lists.partition(baiduShopIds, SHOP_ID_LIST_PAGE_SIZE);

        List<List<List<String>>> batchSizeBaiduShopIdList = Lists.partition(partition, batchGetNumber);

        for (List<List<String>> batchPartition : batchSizeBaiduShopIdList) {
            List<Future<List<String>>> tasks = new ArrayList<>();
            for (List<String> baiduShopIdList : batchPartition) {
                tasks.add(channelIdExecutor.submit(() -> getShopIdListByBaiduShopIds(baseRequest, sysParam, baiduShopIdList)));
            }
            //等待batchGetNumber个线程执行完成
            for (Future<List<String>> task : tasks) {
                try {
                    shopIdList.addAll(task.get());
                } catch (Exception e) {
                    log.error("根据 平台门店id 查询 第三方门店id失败", e);
                    throw new BizException("根据 平台门店id 查询 第三方门店id失败");
                }
            }
            tasks.clear();
        }
        return shopIdList;
    }


    /**
     * 根据 平台门店id 查询 第三方门店id
     *
     * @param baseRequest
     * @param baiduShopIds
     * @return
     */
    private List<String> getShopIdListByBaiduShopIds(BaseRequest baseRequest, Map<String, Object> sysParam,
                                                     List<String> baiduShopIds) {
        long tenantId = baseRequest.getTenantId();
        int channelId = baseRequest.getChannelId();
        long appId = baseRequest.getAppId();
        Map<String, Object> bizParam = new HashMap<>();
        bizParam.put("platform_shop_id_list", baiduShopIds);

        long startTime = System.currentTimeMillis();
        Map<String, Object> shopListMap = sendPost(baseRequest, sysParam, bizParam, shopBatchGet);
        log.info("软件应用:耗时日志:shop.batch.get 耗时：{},返回结果:{}" , (System.currentTimeMillis() - startTime),shopListMap);

        JSONObject shopIdJsonObject = (JSONObject)shopListMap.get(ProjectConstant.BODY);
        if ((int)shopIdJsonObject.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            // 查询失败，抛出异常
            String errorMsg = shopIdJsonObject.getString(ProjectConstant.ERROR);
            log.error("查询门店列表失败 tenantId: {} - channelId: {} - appId: {} 错误信息：{}", tenantId, channelId, appId,
                    errorMsg);
            throw new BizException("查询门店列表失败");
        }
        JSONArray shopJsonArray = shopIdJsonObject.getJSONArray(ProjectConstant.DATA);
        if (shopJsonArray == null || shopJsonArray.isEmpty()) {
            log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
            return new ArrayList<>();
        }

        List<String> shopIdList = new ArrayList<>();
        shopJsonArray
                .forEach(json -> shopIdList.add(((JSONObject)json).getString(ProjectConstant.THIRD_PARTY_SHOP_ID)));
        return shopIdList;
    }

    private Map<String, Object> sendPost(BaseRequest baseRequest, Map<String, Object> sysParam,
                                         Map<String, Object> bizParam, String method) {
        Map<String, Object> shopListMap;
        if (sysParam == null) {
            shopListMap = elmChannelGateService.sendPostApp(baseUrl, method, baseRequest, bizParam);
        } else {
            // 底层会修改sysParam，多次调用会验签失败
            Map<String, Object> sysParamCopy = Optional.ofNullable(sysParam).map(k -> new HashMap<>(k)).orElse(null);
            shopListMap = elmChannelGateService.sendPost(baseUrl, method, baseRequest, bizParam, sysParamCopy);
        }
        return shopListMap;
    }

    /**
     * 查询 平台门店id， 查询失败时抛出异常
     *
     * @param baseRequest
     * @return
     */
    private List<String> getBaiduShopIds(BaseRequest baseRequest, Map<String, Object> sysParam) {
        List<String> baiduShopIds = new ArrayList<>();

        long tenantId = baseRequest.getTenantId();
        int channelId = baseRequest.getChannelId();
        long appId = baseRequest.getAppId();

        int page = 1;
        int pageSize = SHOP_ID_LIST_PAGE_SIZE;
        boolean hasMore = true;
        do {
            Map<String, Object> bizParam = new HashMap<>();
            bizParam.put("page", page);
            bizParam.put("page_size", pageSize);

            // 查询平台门店id，https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.id.list-3?aopApiCategory=shop_all&type=api_menu
            Map<String, Object> shopListMap = sendPost(baseRequest, sysParam, bizParam, shopIdList);

            JSONObject shopIdJsonObject = (JSONObject)shopListMap.get(ProjectConstant.BODY);
            if ((int)shopIdJsonObject.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
                // 查询失败，抛出异常
                String errorMsg = shopIdJsonObject.getString(ProjectConstant.ERROR);
                log.error("查询门店列表失败 tenantId: {} - channelId: {} - appId: {} 错误信息：{}", tenantId, channelId, appId,
                        errorMsg);
                throw new BizException("查询门店列表失败");
            }

            JSONObject dataJsonObject = shopIdJsonObject.getJSONObject(ProjectConstant.DATA);
            if (dataJsonObject == null || dataJsonObject.isEmpty()) {
                // 查询data为空，本次查询失败，返回空列表
                log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
                return new ArrayList<>();
            }

            int total = dataJsonObject.getInteger("total");

            JSONArray shopJsonArray = dataJsonObject.getJSONArray(ProjectConstant.SHOP_LIST);
            if (shopJsonArray == null || shopJsonArray.isEmpty()) {
                // 查询shop_list为空，本次查询失败，返回空列表
                log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
                return new ArrayList<>();
            }

            shopJsonArray
                    .forEach(json -> baiduShopIds.add(((JSONObject)json).getString(ProjectConstant.BAIDU_SHOP_ID)));
            hasMore = total > pageSize * page;
            page++;
        } while (hasMore);
        return baiduShopIds;
    }

    /**
     * 查询 平台门店id， 查询失败时抛出异常
     *
     * @param baseRequest
     * @return
     */
    private List<String> getBaiduShopIdsNewForSoftwareApp(BaseRequest baseRequest, Map<String, Object> sysParam) {
        List<String> baiduShopIds = new ArrayList<>();

        long tenantId = baseRequest.getTenantId();
        int channelId = baseRequest.getChannelId();
        long appId = baseRequest.getAppId();
        int page = 1;
        int pageSize = SHOP_ID_LIST_PAGE_SIZE;

        Map<String, Object> bizParam = new HashMap<>();
        bizParam.put("page", page);
        bizParam.put("page_size", pageSize);

        // 查询平台门店id，https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.id.list-3?aopApiCategory=shop_all&type=api_menu
        long startTime = System.currentTimeMillis();
        Map<String, Object> shopListMap = sendPost(baseRequest, sysParam, bizParam, shopIdList);
        long endTime = System.currentTimeMillis();
        JSONObject shopIdJsonObject = (JSONObject)shopListMap.get(ProjectConstant.BODY);
        log.info("耗时日志:software查询门店列表{},耗时：{}，返回结果:{}","shop.id.list", endTime - startTime,shopIdJsonObject);
        if ((int)shopIdJsonObject.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            // 查询失败，抛出异常
            String errorMsg = shopIdJsonObject.getString(ProjectConstant.ERROR);
            log.error("查询门店列表失败 tenantId: {} - channelId: {} - appId: {} 错误信息：{}", tenantId, channelId, appId,
                    errorMsg);
            throw new BizException("查询门店列表失败");
        }

        JSONObject dataJsonObject = shopIdJsonObject.getJSONObject(ProjectConstant.DATA);
        if (dataJsonObject == null || dataJsonObject.isEmpty()) {
            // 查询data为空，本次查询失败，返回空列表
            log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
            return new ArrayList<>();
        }

        JSONArray shopJsonArray = dataJsonObject.getJSONArray(ProjectConstant.SHOP_LIST);
        if (shopJsonArray == null || shopJsonArray.isEmpty()) {
            // 查询shop_list为空，本次查询失败，返回空列表
            log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
            return new ArrayList<>();
        }

        shopJsonArray.forEach(json -> baiduShopIds.add(((JSONObject)json).getString(ProjectConstant.BAIDU_SHOP_ID)));

        int total = dataJsonObject.getInteger("total");
        if (total > pageSize) {
            //不止一页,计算总页数
            int pageCount = total % pageSize == 0 ? total/pageSize : total/pageSize +1;
            List<Integer> pageList = IntStream.range(2, pageCount + 1).boxed().collect(Collectors.toList());

            int batchNumber = MccConfigUtil.queryElmShopBatchGetNumber();
            List<List<Integer>> pagePartitionByBatchNumber = Lists.partition(pageList, batchNumber);

            for (List<Integer> pagePartition : pagePartitionByBatchNumber) {
                List<Future<List<String>>> tasks = new ArrayList<>();
                for (Integer pageNo : pagePartition) {
                    tasks.add(channelIdExecutor.submit(
                            () -> getBaiduShopIdsForSoftwareApp(pageNo, pageSize, baseRequest, sysParam, tenantId, channelId, appId))
                    );
                }
                for (Future<List<String>> task : tasks) {
                    try {
                        baiduShopIds.addAll(task.get());
                    } catch (Exception e) {
                        log.error("获取门店列表失败", e);
                        throw new RuntimeException("获取门店列表失败", e);
                    }
                }
                tasks.clear();
            }
        }
        return baiduShopIds;
    }


    private List<String> getBaiduShopIdsForSoftwareApp(Integer page,Integer pageSize,BaseRequest baseRequest,Map<String, Object> sysParam,Long tenantId,Integer channelId,Long appId){
        List<String> baiduShopIdsNew = new ArrayList<>();
        Map<String, Object> param = new HashMap<>();
        param.put("page", page);
        param.put("page_size", pageSize);

        // 查询平台门店id，https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.id.list-3?aopApiCategory=shop_all&type=api_menu
        long startTime = System.currentTimeMillis();
        Map<String, Object> shopListMapNew = sendPost(baseRequest, sysParam, param, shopIdList);
        long endTime = System.currentTimeMillis();
        log.info("耗时日志:software查询门店列表{},耗时：{}，返回结果:{}","shop.id.list", endTime - startTime,shopListMapNew);
        JSONObject shopIdJsonObjectNew = (JSONObject) shopListMapNew.get(ProjectConstant.BODY);
        if ((int)shopIdJsonObjectNew.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            // 查询失败，抛出异常
            String errorMsg = shopIdJsonObjectNew.getString(ProjectConstant.ERROR);
            log.error("查询门店列表失败 tenantId: {} - channelId: {} - appId: {} 错误信息：{}", tenantId, channelId, appId,
                    errorMsg);
            throw new BizException("查询门店列表失败");
        }

        JSONObject dataJsonObjectNew = shopIdJsonObjectNew.getJSONObject(ProjectConstant.DATA);
        if (dataJsonObjectNew == null || dataJsonObjectNew.isEmpty()) {
            // 查询data为空，本次查询失败，返回空列表
            log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
            return new ArrayList<>();
        }
        JSONArray shopJsonArrayNew = dataJsonObjectNew.getJSONArray(ProjectConstant.SHOP_LIST);
        if (shopJsonArrayNew == null || shopJsonArrayNew.isEmpty()) {
            // 查询shop_list为空，本次查询失败，返回空列表
            log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
            return new ArrayList<>();
        }
        shopJsonArrayNew.forEach(json -> baiduShopIdsNew.add(((JSONObject)json).getString(ProjectConstant.BAIDU_SHOP_ID)));
        return baiduShopIdsNew;
    }

    private Map<String, Object> convertShippingTime(List<ShippingTime> shippingTimes, Set<Integer> shippingDays) {
        Map<String, Object> businessTime2 = new HashMap<>();
        List<Map<String, Object>> normalBusinessTimeList = new ArrayList<>();
        Map<String, Object> businessHour = new HashMap<>();
        Map<String, Object> businessHourValue = new HashMap<>();

        // 将shippingDays转换为weeks
        if (CollectionUtils.isNotEmpty(shippingDays)) {
            // shippingDays的值需要+1才能对应到weeks的值，例如shippingDay = 0对应week = 1
            List<Integer> weeks = shippingDays.stream()
                .map(day -> day + 1)
                .collect(Collectors.toList());
            businessHourValue.put("weeks", weeks);
        } else {
            businessHourValue.put("weeks", Lists.newArrayList(1, 2, 3, 4, 5, 6, 7));
        }

        // 判断是否全天营业
        boolean is24Hours = false;
        if (CollectionUtils.isNotEmpty(shippingTimes) && shippingTimes.size() == 1) {
            ShippingTime shippingTime = shippingTimes.get(0);
            if ("00:00".equals(shippingTime.getStartTime()) && "23:59".equals(shippingTime.getEndTime())) {
                is24Hours = true;
            }
        }

        // 设置营业类型：1-24小时营业，2-自定义
        businessHourValue.put("type", is24Hours ? 1 : 2);

        // 将shippingTimes转换为ranges
        if (CollectionUtils.isNotEmpty(shippingTimes)) {
            List<Map<String, Object>> ranges = new ArrayList<>();
            for (ShippingTime shippingTime : shippingTimes) {
                Map<String, Object> range = new HashMap<>();

                // 直接使用start_time和end_time
                range.put("start_time", shippingTime.getStartTime());
                range.put("end_time", shippingTime.getEndTime());

                ranges.add(range);
            }
            businessHourValue.put("ranges", ranges);
        }

        businessHour.put("business_hour", businessHourValue);
        normalBusinessTimeList.add(businessHour);
        businessTime2.put("normal_business_time_list", normalBusinessTimeList);
        return businessTime2;
    }

    public static String formatBusinessTime2(Map<String, Object> businessTime2) {
        if (businessTime2 == null) {
            return "";
        }

        // 获取normal_business_time_list
        List<Map<String, Object>> normalBusinessTimeList = (List<Map<String, Object>>) businessTime2.get("normal_business_time_list");
        if (normalBusinessTimeList == null || normalBusinessTimeList.isEmpty()) {
            return "";
        }

        Map<String, Object> businessHour = normalBusinessTimeList.get(0);
        Map<String, Object> businessHourValue = (Map<String, Object>) businessHour.get("business_hour");
        if (businessHourValue == null) {
            return "";
        }

        // 获取type,weeks和ranges
        int type = (int) businessHourValue.get("type");
        List<Integer> weeks = (List<Integer>) businessHourValue.get("weeks");
        List<Map<String, Object>> ranges = (List<Map<String, Object>>) businessHourValue.get("ranges");
        
        if (type == 1) {
            // 处理全天营业的情况
            if (weeks != null) {
                // 构建营业时间字符串
                StringBuilder[] weekTimeBuilder = new StringBuilder[7];
                for (int i = 0; i < 7; i++) {
                    weekTimeBuilder[i] = new StringBuilder();
                }
            
                // 对于全天营业，时间段为00:00-23:59
                for (Integer week : weeks) {
                    // 饿了么的week是1-7，对应周一到周日，而数组索引是0-6
                    int index = week - 1;
                    if (index >= 0 && index < 7) {
                        weekTimeBuilder[index].append("00:00-23:59");
                    }
                }

                // 拼接最终结果
                return Arrays.stream(weekTimeBuilder)
                        .map(StringBuilder::toString)
                        .collect(Collectors.joining(";"));
            }
        }

        if (weeks == null || ranges == null || ranges.isEmpty()) {
            return "";
        }

        // 构建营业时间字符串
        StringBuilder[] weekTimeBuilder = new StringBuilder[7];
        for (int i = 0; i < 7; i++) {
            weekTimeBuilder[i] = new StringBuilder();
        }

        // 格式化每个时间段
        String timeRangeStr = ranges.stream()
                .map(range -> range.get("start_time") + "-" + range.get("end_time"))
                .collect(Collectors.joining(","));

        // 将时间段分配给对应的星期
        for (Integer week : weeks) {
            // 饿了么的week是1-7，对应周一到周日，而数组索引是0-6
            int index = week - 1;
            if (index >= 0 && index < 7) {
                weekTimeBuilder[index].append(timeRangeStr);
            }
        }

        // 拼接最终结果
        return Arrays.stream(weekTimeBuilder)
                .map(StringBuilder::toString)
                .collect(Collectors.joining(";"));
    }

}
