package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDrunkHorseEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MtResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.*;

/**
 * 歪马送酒请求公共服务接口
 *
 * <AUTHOR>
 * @create 2021/10/11 上午10:22
 **/
@Service
public class DrunkHorseChannelGateService extends BaseChannelGateService {
    // 美团开放平台签名错误码
    private static final int SIGN_ERROR_CODE = 703;
    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Value("${drunkhorse.url.base}")
    private String baseUrl;

    @Resource
    private CommonLogger log;

    public <T> T postToChannel(String channelPoiCode, String channelOnlinePoiCode, ChannelPostInter postUrlEnum,
                               Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest, boolean sigV2) {
        replaceStoreId(bizParamMap, channelOnlinePoiCode);

        // 限频
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
        if (StringUtils.isNotBlank(appId) && postUrlEnum.requestLimited()) {
            long waitTime = clusterRateLimiter.tryAcquire(postUrlEnum.generateLimitedResourceKey(), appId, baseRequest.isAsyncInvoke());
            if (waitTime != 0) {
                if (RHINO_UPTIMATE_SET.contains(postUrlEnum)) {
                    log.info("DhChannelGateService.postToChannel appId:{} url:{} 未获取到令牌 抛出异常", appId, postUrlEnum.getUrl());
                    throw new InvokeChannelTooMuchException(waitTime);
                } else {
                    log.info("DhChannelGateService.postToChannel appId:{} url:{} 未获取到令牌 直接返回null", appId, postUrlEnum.getUrl());
                    return null;
                }
            }
        }

        String url = getPostUrl(postUrlEnum);
        Map<String, Object> postParams = generatePostParams(url, sysParam, bizParamMap, sigV2);

        String resp;

        // 默认所有接口都解析部分失败错误信息，如有特殊接口不需要解析部分失败信息需在此处加下
        boolean parsePartFailedMsgFlag = true;
        resp = postRequest(postUrlEnum, postParams, baseRequest);

        ChannelResponseDTO result = dealPostResult(resp, postUrlEnum.getResultClass(), parsePartFailedMsgFlag);

        if (Objects.nonNull(result) && Objects.nonNull(result.getError()) &&
                MtResultCodeEnum.TRIGGER_FLOW_CONTROL.getCodeStr().equals(result.getErrno())){
            if (RHINO_UPTIMATE_SET.contains(postUrlEnum)){
                log.warn("推送美团歪马渠道触发限频，返回客户端重试,result {}", result);
                throw new InvokeChannelTooMuchException(0);
            }
        }

        return (T) result;

    }

    @Override
    public <T> T postToChannel(String channelPoiCode, String channelOnlinePoiCode, ChannelPostInter postUrlEnum,
                               Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest) {
        boolean sigV2Flag = MccConfigUtil.sigContainsEmptyFlag();
        ChannelResponseDTO responseDTO = postToChannel(channelPoiCode, channelOnlinePoiCode, postUrlEnum, sysParam, bizParamMap, baseRequest,
                sigV2Flag);
        Integer code = Optional.ofNullable(responseDTO.getError()).map(ChannelResponseDTO.ChannelResponseError::getCode).orElse(-1);
        // 新签名且签名错误进行重试一次
        if(sigV2Flag && code == SIGN_ERROR_CODE){
            return postToChannel(channelPoiCode, channelOnlinePoiCode, postUrlEnum, sysParam, bizParamMap, baseRequest, false);
        }
        return (T) responseDTO;

    }


    /***
     * 对get的参数进行encode
     * 因为商家退款接口输入特殊字符导致接口调用失败
     * 注意：底层getUrl没有对param进行encode，在这里先对param进行encode，如果后续httpClient的assemble方法也进行了encode，这里需要放开
     * ****/
    @Override
    public Map<String, Object> sendEncodedGet(String url, String method, BaseRequest baseRequest, Object bizParam) {
        boolean sigV2Flag = MccConfigUtil.sigContainsEmptyFlag();
        Map<String, Object> result = sendEncodedGet(url, method, baseRequest, bizParam, sigV2Flag);
        int code = parseErrorCode(result);
        if(sigV2Flag && code == SIGN_ERROR_CODE){
            return sendEncodedGet(url, method, baseRequest, bizParam, false);
        }
        return result;
    }

    public Map<String, Object> sendEncodedGet(String url, String method, BaseRequest baseRequest, Object bizParam, boolean sigV2) {

        // 参数校验
        validateSendPost(url, method, baseRequest, bizParam);

        // 参数实体转换为Map
        Map<String, Object> bizParamMap = null;
        if (!(bizParam instanceof Map)) {
            try {
                bizParamMap = ConverterUtils.getProperty(bizParam);
            } catch (Exception e) {
                log.error("sendGet 获取bizParamMap异常", e);
            }
        } else {
            bizParamMap = (Map<String, Object>) bizParam;
        }

        // 获取系统参数
        Map<String, Object> sysParam = getChannelSysParams(baseRequest);

        // 拼接参数
        Map<String, String> postParam = generatePostParams(url, method, sysParam, bizParamMap, sigV2);

        //encode value
        if (bizParamMap != null){
            bizParamMap.forEach((key, value)->{
                if (value instanceof String){
                    postParam.put(key, UrlUtil.urlEnCodeSafe((String) value));
                }
            });
        }
        // 请求
        return getUrl(url, baseRequest, postParam);
    }

    @Override
    public Map<String, Object> sendPost(String url, String method, BaseRequest baseRequest, Object bizParam, Map<String, Object> systemParam) {
        boolean sigV2Flag = MccConfigUtil.sigContainsEmptyFlag();
        Map<String, Object> result = sendPost(url, method, baseRequest, bizParam, systemParam, sigV2Flag);
        int code = parseErrorCode(result);
        if(sigV2Flag && code == SIGN_ERROR_CODE){
            return sendPost(url, method, baseRequest, bizParam, systemParam, false);
        }
        return result;
    }

    /**
     * 各渠道发送请求公共方法
     *
     * @param url      请求URL
     * @param bizParam 请求业务参数
     * @return
     */
    public Map<String, Object> sendPost(String url, String method, BaseRequest baseRequest, Object bizParam, Map<String, Object> systemParam, boolean sigV2) {

        // 参数校验
        validateSendPost(url, method, baseRequest, bizParam);

        // 参数实体转换为Map
        Map<String, Object> bizParamMap = null;
        try {
            if (bizParam != null) {
                if (bizParam instanceof Map) {
                    bizParamMap = (Map<String, Object>) bizParam;
                } else {
                    bizParamMap = ConverterUtils.convertToMap(bizParam, baseRequest.getChannelId());
                }
            }
        } catch (Exception e) {
            log.error("BaseChannelGateService.sendPost error", e);
        }

        // 拼接参数
        Map<String, Object> postParam = generatePostParams(url, method, systemParam, bizParamMap, sigV2);

        // 请求
        return postUrl(url, baseRequest, postParam);
    }

    @Override
    public Map<String, Object> sendGet(String url, String method, BaseRequest baseRequest, Object bizParam) {
        boolean sigV2Flag = MccConfigUtil.sigContainsEmptyFlag();
        Map<String, Object> result = sendGet(url, method, baseRequest, bizParam, sigV2Flag);
        int code = parseErrorCode(result);
        if(sigV2Flag && code == SIGN_ERROR_CODE){
            return sendGet(url, method, baseRequest, bizParam, false);
        }
        return result;
    }

    public Map<String, Object> sendGet(String url, String method, BaseRequest baseRequest, Object bizParam, boolean sigV2) {

        // 参数校验
        validateSendPost(url, method, baseRequest, bizParam);

        // 参数实体转换为Map
        Map<String, Object> bizParamMap = null;
        if (!(bizParam instanceof Map)) {
            try {
                bizParamMap = ConverterUtils.getProperty(bizParam);
            } catch (Exception e) {
                log.error("sendGet 获取bizParamMap异常", e);
            }
        } else {
            bizParamMap = (Map<String, Object>) bizParam;
        }

        // 获取系统参数
        Map<String, Object> sysParam = getChannelSysParams(baseRequest);

        // 拼接参数
        Map<String, String> postParam = generatePostParams(url, method, sysParam, bizParamMap, sigV2);

        // 请求
        return getUrl(url, baseRequest, postParam);
    }


    private <T> T dealPostResult4UpdateCustomSkuId(String resp, Class resultClass) {
        ChannelResponseDTO dto = ChannelResponseDTO.parseResult4UpdateCustomSkuId(resp, resultClass);
        return (T) dto;
    }

    @Override
    protected Map generatePostParams(String url, String method, Map<String, Object> sysParam, Map<String, Object> bizParam) {
        return generatePostParams(url, sysParam, bizParam, false);
    }

    protected Map generatePostParams(String url, String method, Map<String, Object> sysParam, Map<String, Object> bizParam, boolean sigV2) {
        return generatePostParams(url, sysParam, bizParam, sigV2);
    }

    private Map<String, Object> generatePostParams(String url, Map<String, Object> sysParam, Map<String, Object> bizParam,
                                                   boolean containsEmpty) {
        sysParam.put("timestamp", String.valueOf(DateUtils.unixTime()));
        HashMap<String, Object> paramMap = Maps.newHashMap(sysParam);
        paramMap.putAll(bizParam);
        String paramForSig = containsEmpty ? MtSignUtils.concatParamsContainsEmpty(paramMap) : MtSignUtils.concatParams(paramMap);
        String sig = MtSignUtils.getSig(url, paramForSig, (String) sysParam.get("secret"));
        paramMap.put("sig", sig);
        paramMap.remove("secret");

        return paramMap;
    }

    private void replaceStoreId(Map<String, Object> bizParamMap, String outStoreId) {
        if (StringUtils.isNotBlank(outStoreId)) {
            bizParamMap.put(Constant.FIELD_NAME_STOREID_MT, outStoreId);
        }
    }

    private ChannelResponseDTO dealPostResult(String resultJson, Class resultClass, boolean isUpdatePrice) {
       return ChannelResponseDTO.parseResult(resultJson, resultClass, isUpdatePrice);
    }

    @Override
    public String getPostUrl(ChannelPostInter postUrlEnum) {
        return baseUrl + postUrlEnum.getUrl();
    }


    private int parseErrorCode(Map<String, Object> result){
        if(MapUtils.isEmpty(result) || !result.containsKey(ERROR)){
            return -1;
        }
        Object error = result.get(ERROR);
        if(error instanceof Map){
            Object code = ((Map) error).get(CODE);
            return code != null ? NumberUtils.toInt(code.toString(), -1) : -1;
        }
        return -1;
    }
}