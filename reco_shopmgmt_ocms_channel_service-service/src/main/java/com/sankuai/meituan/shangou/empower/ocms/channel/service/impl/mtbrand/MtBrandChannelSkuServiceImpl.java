package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MtResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSkuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.MtChannelResultEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.SourceType;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator.genResult;

/**
 * 美团渠道商品内部服务接口
 *
 * @author: chenhaie
 * @create: 2019/1/7 下午5:27
 */
@Service("mtBrandChannelSkuService")
public class MtBrandChannelSkuServiceImpl implements ChannelSkuService {
    public static final int SKU_CREATE_MAX_COUNT = 200;

    public static final int UPC_CREATE_MAX_COUNT = 50;

    @Value("${mt.url.base}" + "${mt.url.skulist}")
    private String skuList;

    @Value("${mt.url.base}" + "${mt.url.skuDetail}")
    private String skuDetail;

    @Value("${mt.url.base}" + "${mt.url.frontCatList}")
    private String frontCatList;

    @Value("${mt.url.base}" + "${mt.url.batchUploadImageUrl}")
    private String batchUploadImageUrl;

    @Value("${mt.url.base}" + "${mt.url.batchUpdateAppSpuCodeByOriginUrl}")
    private String batchChangeCustomSkuIdUrl;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private MtConverterService mtConverterService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private CommonLogger log;

    @Resource(name = "mtPictureUploadThreadPool")
    private ExecutorService mtPictureUploadThreadPool;

    @Resource(name = "mtBrandChannelSpuService")
    private MtBrandChannelSpuServiceImpl mtBrandChannelSpuService;

    /**
     * 美团创建商品有批量接口，开放平台限制一次最多200条，一批同时成功或者同时失败
     *
     * @param request 接口虽然支持多门店语义（多个门店创建同一批SKU); 当前使用场景storeIdList仅使用了一个门店id
     * @return
     */
    @Override
    public ResultData skuCreate(SkuInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        // 分页调用
        ListUtils.listPartition(request.getParamList(), SKU_CREATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuInfoDTO::getSkuId).collect(Collectors.toList());

                // 业务参数转换
                List<ChannelSkuCreateDTO> paramList = Lists.newArrayList();
                data.forEach(param -> {
                    ChannelSkuCreateDTO channelSkuCreateDTO = mtConverterService.skuCreateMapping(param);
                    // 设置基础名称
                    channelSkuCreateDTO.setBase_name(param.getName());
                    if (StringUtils.isEmpty(channelSkuCreateDTO.getProduct_name_supplement())) {
                        channelSkuCreateDTO.setProduct_name_supplement("EMPTY_VALUE");
                    }
                    channelSkuCreateDTO.setSkus(Lists.newArrayList(mtConverterService.skuInfoMapping(param)));
                    paramList.add(channelSkuCreateDTO);
                });

                //由于thrift中力荐是int类型，如果想往渠道传null的话，需要进行一个特殊的转换
                paramList.stream().filter(Objects::nonNull).forEach(item -> {
                    if (item.getIs_specialty() == -1) {
                        item.setIs_specialty(null);
                    }
                });

                ChannelSkuUpdateDTO dto = mtConverterService.updatePrice(JSON.toJSONString(paramList));

                // 调用渠道接口 内部会循环storeIdList(多个门店创建同样的SKU)，并填充app_poi_code
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.SKU_CREATE, request.getBaseInfo(), dto);

                // 组装返回结果
                ResultDataUtils.combinePartResultData(resultData, postResult, bizKeyList);

            } catch (IllegalArgumentException e) {
                log.error("MtChannelSkuServiceImpl.skuCreate 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);

            } catch (Exception e) {
                log.error("MtChannelSkuServiceImpl.skuCreate 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList);
            }
        });

        return resultData;
    }

    /**
     * @param request 接口虽然支持多门店语义（多个门店创建同一批SPU); 当前使用场景storeIdList仅使用了一个门店id
     * @return
     */
    @Override
    public ResultData upcCreate(SkuInfoRequest request) {
        return upcCreate(request, false);
    }

    private ResultData upcCreate(SkuInfoRequest request, boolean uploadPic) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        ListUtils.listPartition(request.getParamList(), UPC_CREATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuInfoDTO::getSkuId).collect(Collectors.toList());

                // 业务参数转换
                List<ChannelUpcCreateDTO> foodData = Lists.newArrayList();
                data.forEach(obj -> {
                    ChannelUpcCreateDTO channelUpcCreateDTO = mtConverterService.upcCreateMapping(obj);
                    if (!uploadPic) {
                        channelUpcCreateDTO.setPicture(Strings.EMPTY);
                    }
                    channelUpcCreateDTO.setStandard_sku(mtConverterService.standardSkuMapping(obj));
                    foodData.add(channelUpcCreateDTO);
                });
                //由于thrift中力荐是int类型，如果想往渠道传null的话，需要进行一个特殊的转换
                foodData.stream().filter(Objects::nonNull).forEach(item -> {
                    if (item.getIs_specialty() == -1) {
                        item.setIs_specialty(null);
                    }
                });

                // 调用渠道接口 内部会循环storeIdList(多个门店创建同样的SPU)，并填充app_poi_code
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.UPC_CREATE, request.getBaseInfo(), new ChannelUpcCreateDTO(foodData));

                // 结果组装
                ResultDataUtils.combinePartResultData(resultData, postResult, bizKeyList);

            } catch (IllegalArgumentException e) {
                log.error("MtChannelSkuServiceImpl.upcCreate 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);

            } catch (Exception e) {
                log.error("MtChannelSkuServiceImpl.upcCreate 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList);
            }
        });

        return resultData;
    }

    /**
     * @param request 接口虽然支持多门店语义（多个门店创建同一批SPU); 当前使用场景storeIdList仅使用了一个门店id
     * @return
     */
    @Override
    public ResultData updateSku(SkuInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }

        // 更新操作不设置价格
        request.getParamList().stream().filter(Objects::nonNull).forEach(item -> item.setPriceIsSet(false));
        // 更新操作不设置库存
        if (MccConfigUtil.mtUpdateSkuWithOutStockSwitch()) {
            request.getParamList().stream().filter(Objects::nonNull).forEach(item -> item.setStockIsSet(false));
        }

        List<SkuInfoDTO> skuCreateList = request.getParamList().stream().filter(item -> item.getSourceType() != SourceType.UpcCreate.getValue()).collect(Collectors.toList());

        List<SkuInfoDTO> upcCreateList = request.getParamList().stream().filter(item -> item.getSourceType() == SourceType.UpcCreate.getValue()).collect(Collectors.toList());
        // 根据sku创建的商品进行更新
        if (CollectionUtils.isNotEmpty(skuCreateList)) {

            SkuInfoRequest skuUpdateRequest = new SkuInfoRequest().setBaseInfo(request.getBaseInfo()).setParamList(skuCreateList);

            resultData = this.skuCreate(skuUpdateRequest);
        }
        // 根据upc创建的商品进行更新
        if (CollectionUtils.isNotEmpty(upcCreateList)) {

            SkuInfoRequest upcUpdateRequest = new SkuInfoRequest().setBaseInfo(request.getBaseInfo()).setParamList(upcCreateList);

            ResultData upcUpdateResultData = this.upcCreate(upcUpdateRequest);

            resultData.sucData.addAll(upcUpdateResultData.sucData);

            resultData.errorData.addAll(upcUpdateResultData.errorData);
        }
        // 对sku更新失败的的商品重试进行upc更新
        retryUpcUpdate(resultData, request.getBaseInfo(), request.paramList);

        return resultData;
    }

    /**
     * 修改按upc新增只能按upc修改问题临时方案
     * 现在没有标识区分商品是通过sku还是upc接口新增，通过sku修改接口修改返回错误信息是
     * "此商品为标品，不允许更新"时，再调用upc更新接口更新
     *
     * @param resultData
     * @param baseInfo
     * @param data
     */
    private void retryUpcUpdate(ResultData resultData, BaseRequest baseInfo, List<SkuInfoDTO> data) {
        if (CollectionUtils.isEmpty(resultData.getErrorData())) {
            return;
        }

        Map<String, SkuInfoDTO> appleMap = data.stream().collect(Collectors.toMap(SkuInfoDTO::getSkuId, a -> a, (k1, k2) -> k1));
        SkuInfoRequest request = new SkuInfoRequest().setBaseInfo(baseInfo).setParamList(Lists.newArrayList());
        Iterator<ResultErrorSku> itr = resultData.getErrorData().iterator();
        while (itr.hasNext()) {
            ResultErrorSku item = itr.next();
            if (MccConfigUtil.getMTUpcUpdateErrMgs().equals(item.getErrorMsg())) {
                request.getParamList().add(appleMap.get(item.getSkuId()));
                itr.remove();
            }
            ;
        }

        if (CollectionUtils.isNotEmpty(request.getParamList())) {
            ResultData res = this.upcCreate(request, true);
            resultData.getSucData().addAll(res.getSucData());
            resultData.getErrorData().addAll(res.getErrorData());
        }
    }

    /**
     * @param request 多门店接口；内部逐门店串行
     * @return
     */
    @Override
    public ResultData deleteSku(SkuInfoDeleteRequest request) {
        try {
            BaseRequest baseInfo = request.getBaseInfo();
            ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
            // 逐门店串行
            baseInfo.getStoreIdList().forEach(storeId ->
                    // 逐个SPU删除
                    request.getParamList().forEach(skuInfoDeleteDTO -> {
                        ChannelSkuDeleteDTO channelSkuDeleteDTO = mtConverterService.skuDeleteMapping(skuInfoDeleteDTO);

                        BaseRequest storeRequest = new BaseRequest()
                                .setTenantId(baseInfo.getTenantId())
                                .setChannelId(baseInfo.getChannelId())
                                .setStoreIdList(Lists.newArrayList(storeId));

                        Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.SKU_DELETE, storeRequest, channelSkuDeleteDTO);
                        if (Objects.isNull(postResult)) {
                            ResultBuilderUtil.resultAnalysis(baseInfo.getChannelId(), storeId, skuInfoDeleteDTO.getSkuId(), "调用渠道接口失败", resultData);
                        }
                        ResultBuilderUtil.resultAnalysis(baseInfo.getChannelId(), storeId, skuInfoDeleteDTO.getSkuId(), postResult, resultData);
                    }));
            return resultData;
        } catch (Exception e) {
            log.error("MtChannelSkuServiceImpl.deleteSku, MT批量删除商品服务异常, request:{}", request, e);
        }
        return ResultGenerator.genResultData(ResultCode.FAIL, "批量删除商品失败");
    }

    /**
     * @param request 接口虽然支持多门店语义（多个门店创建同一批SPU); 当前使用场景storeIdList仅使用了一个门店id
     * @return
     */
    @Override
    public ResultData pictureUpload(PictureUploadRequest request) {
        ResultData resultData = ResultDataUtils.newSynchronizedResultData(ChannelResultStatusEnum.SUCCESS);

        boolean useCustomThreadPool = MccConfigUtil.mtUploadPictureUseCustomThreadPool();

        if (!useCustomThreadPool) {
            request.getParamList().parallelStream().forEach(data -> {
                pictureUpload(request, data, resultData);
            });
        } else {
            List<Future<Void>> futureList = new ArrayList<>(request.getParamList().size());
            for (PictureUploadDTO data : request.getParamList()) {
                Future<Void> future = mtPictureUploadThreadPool.submit(() -> {
                    pictureUpload(request, data, resultData);
                    return null;
                });
                futureList.add(future);
            }
            for (Future<Void> future : futureList) {
                try {
                    future.get(10L, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error("MtChannelSkuServiceImpl.pictureUpload 等待结果异常, request:{}", request, e);
                }
            }
        }

        return resultData;
    }

    private void pictureUpload(PictureUploadRequest request, PictureUploadDTO data, ResultData resultData) {
        try {
            // url 2 byte
            if (StringUtils.isNotBlank(data.getUrl())) {
                SsrfCheckUtils.checkImageUrl(data.getUrl());
                data.setPictureData(ImageUtils.imageBytes(data.getUrl()));
            }
            // 调用渠道接口
            Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.PICTURE_UPLOAD, request.getBaseInfo(), mtConverterService.pictureUploadMapping(data));

            // 结果组装
            postResult.forEach((k, v) -> {
                if (v == null){
                    resultData.getErrorData().add(new ResultErrorSku().setChannelId(request.getBaseInfo().getChannelId()).setStoreId(k).setSkuId(data.getUid()).setErrorMsg("调用渠道接口失败"));
                    return;
                }
                if (v.getError() == null) {
                    resultData.getSucData().add(new ResultSuccessSku().setChannelId(request.getBaseInfo().getChannelId()).setStoreId(k).setSkuId(data.getUid()).setChannelResultInfo(v.getData()));
                } else {
                    resultData.getErrorData().add(new ResultErrorSku().setChannelId(request.getBaseInfo().getChannelId()).setStoreId(k).setSkuId(data.getUid()).setErrorMsg(v.getErrorMsg()));
                }
            });

        } catch (IllegalArgumentException e) {
            log.error("MtChannelSkuServiceImpl.pictureUpload 参数校验失败, data:{}", data, e);
            ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getUid());

        } catch (Exception e) {
            log.error("MtChannelSkuServiceImpl.pictureUpload 服务异常, data:{}", data, e);
            ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getUid());
        }
    }

    @Override
    public PictureUploadResult getPictureUploadStatus(PictureUploadStatusRequest request) {
        return null;
    }

    /**
     * @param request 多门店接口；内部逐门店串行
     * @return
     */
    @Override
    public ResultData updateSkuSellStatus(SkuSellStatusInfoRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        // 逐门店执行
        request.getParamList().forEach(data -> {
            List<String> skuIds = data.getSkuId().stream().map(SkuIdDTO::getCustomSkuId).collect(Collectors.toList());
            try {
                BaseRequest storeRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(data.getStoreId()));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.SKU_SELL_STATIS, storeRequest, mtConverterService.updateSkuSellStatus(data));

                // 结果组装
                ResultDataUtils.combinePartResultData(resultData, postResult, skuIds);

            } catch (IllegalArgumentException e) {
                log.error("MtChannelSkuServiceImpl.updateSkuSellStatus 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds);

            } catch (Exception e) {
                log.error("MtChannelSkuServiceImpl.updateSkuSellStatus 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds);
            }
        });

        return resultData;
    }

    /**
     * @param request 多门店接口；内部逐门店串行
     * @return
     */
    @Override
    public ResultData updateCustomSkuId(UpdateCustomSkuIdRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        // 逐门店执行
        request.getParamList().forEach(data -> {
            try {
                long storeId = data.getStoreId();

                UpdateCustomSkuId updateCustomSkuId = mtConverterService.updateCustomSkuId(data);
                if (StringUtils.isEmpty(updateCustomSkuId.getSpec())) {
                    updateCustomSkuId.setSpec(null);
                }
                String shopId = updateCustomSkuId.getApp_food_code();
                boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.UPDATE_CUSTOM_SKU_ID, shopId);
                if (!tryAcquire) {
                    // 获取当前执行权失败，返回可重试
                    // storeId=1000215, skuId=946552, errorMsg=不存在此菜品
                    String format = MessageFormat.format(ResultCode.FAIL_ALLOW_RETRY.getMsg(), "根据原商品编码更换新商品编码限頻");
                    resultData.getErrorData().add(new ResultErrorSku(storeId, data.getCustomSkuId(),
                            format, channelId, ResultCode.FAIL_ALLOW_RETRY.getCode(), data.getSkuId(), ProductChannelUnifyErrorEnum.RATE_LIMIT));
                    return;
                }

                BaseRequest storeRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.UPDATE_CUSTOM_SKU_ID, storeRequest, updateCustomSkuId);

                // 组装返回结果
                ChannelResponseDTO channelResponseDTO = postResult.get(storeId);
                if (channelResponseDTO.isSuccess()) {
                    ResultSuccessSku successSku = new ResultSuccessSku(storeId, data.getCustomSkuId(), ResultCode.SUCCESS.getMsg(), channelId, data.getSkuId());
                    resultData.getSucData().add(successSku);
                } else {
                    ResultErrorSku resultErrorSku = new ResultErrorSku(storeId, data.getCustomSkuId(),
                            channelResponseDTO.getErrorMsg(), channelId, ResultCode.FAIL.getCode(), data.getSkuId(), null);
                    resultData.getErrorData().add(resultErrorSku);
                }
                // 结果组装
                //ResultDataUtils.combinePartResultData(resultData, postResult, Lists.newArrayList(data.getCustomSkuId()));
            } catch (IllegalArgumentException e) {
                log.error("MtChannelSkuServiceImpl.updateCustomSkuId 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), Lists.newArrayList(data.getCustomSkuId()));

            } catch (Exception e) {
                log.error("MtChannelSkuServiceImpl.updateCustomSkuId 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, Lists.newArrayList(data.getCustomSkuId()));
            }
        });

        return resultData;
    }

    /**
     * @param request 单门店接口
     * @return
     */
    @Override
    public GetSkuInfoResponse getSkuInfo(GetSkuInfoRequest request) {
        GetSkuInfoResponse response = new GetSkuInfoResponse().setStatus(ResultGenerator.genSuccessResult());

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();
        long storeId = request.getStoreId();

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL));
        }

        // 构造参数
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        String appFoodCode = request.getCustomSkuId();
        bizParam.put("app_poi_code", appPoiCode);
        bizParam.put("app_food_code", appFoodCode);

        //限频处理
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.GET_SKU_DETAIL, appId);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY)).setSkuInfo(null);
        }

        // 请求线上渠道
        Map<String, Object> skuInfoMap;
        if (MccConfigUtil.isSpuQueryUseUrlEncode(tenantId)) {
            skuInfoMap = mtBrandChannelGateService.sendEncodedGet(skuDetail, null, baseRequest, bizParam);
        } else {
            skuInfoMap = mtBrandChannelGateService.sendGet(skuDetail, null, baseRequest, bizParam);
        }
        if (MapUtils.isEmpty(skuInfoMap) || Objects.isNull(skuInfoMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(skuInfoMap.get(ProjectConstant.DATA))) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团商品失败"));
        }
        JSONObject item = (JSONObject) skuInfoMap.get(ProjectConstant.DATA);
        // 组装数据
        ChannelSkuCreateDTO channelSkuCreateDTO = new ChannelSkuCreateDTO();
        // 异常情况处理，可能存在没有sku和店内分类信息，过滤
        if (StringUtils.isBlank(item.getString("skus")) || StringUtils.isBlank(item.getString("category_list"))) {
            log.warn("MtChannelSku.getSkuInfo request:{} skus or category_list is empty json:{}", request, item);
            return response.setStatus(ResultGenerator.genSuccessResult()).setSkuInfo(null);
        }
        List<ChannelSkuInfoDTO> channelSkuInfoDTOS = JSONArray.parseArray((item.get("skus")).toString(), ChannelSkuInfoDTO.class);
        List<ChannelShopCategoryDTO> channelShopCategoryDTOS = JSONArray.parseArray((item.get("category_list")).toString(), ChannelShopCategoryDTO.class);
        // 异常情况处理，可能存在没有sku信息情况
        if (CollectionUtils.isEmpty(channelSkuInfoDTOS) || CollectionUtils.isEmpty(channelShopCategoryDTOS)) {
            log.warn("MtChannelSku.getSkuInfo request:{} skus or category_list is empty json:{}", request, item);
            return response.setStatus(ResultGenerator.genSuccessResult()).setSkuInfo(null);
        }

        channelSkuCreateDTO.setCategory_list(channelShopCategoryDTOS);
        channelSkuCreateDTO.setSkus(channelSkuInfoDTOS);
        channelSkuCreateDTO.setApp_poi_code(appPoiCode);
        channelSkuCreateDTO.setApp_food_code(item.getString("app_food_code"));
        channelSkuCreateDTO.setName(item.getString("name"));
        channelSkuCreateDTO.setCategory_code(item.getString("secondary_category_code"));
        channelSkuCreateDTO.setCategory_name(item.getString("secondary_category_name"));
        // 美团不是返回末级分类，而是固定的一级分类 二级分类用两个字段
        if (StringUtils.isBlank(channelSkuCreateDTO.getCategory_name())) {
            channelSkuCreateDTO.setCategory_code(item.getString("category_code"));
            channelSkuCreateDTO.setCategory_name(item.getString("category_name"));
        }
        // TODO 原有SKU上面的分类字段会被废弃 实际有效的是分类列表 美团支持多分类但是中台只能处理一个分类
        if (!CollectionUtils.isEmpty(channelShopCategoryDTOS)) {
            channelSkuCreateDTO.setCategory_code(channelShopCategoryDTOS.get(0).getSecondary_category_code());
            channelSkuCreateDTO.setCategory_name(channelShopCategoryDTOS.get(0).getSecondary_category_name());
            // 美团不是返回末级分类，而是固定的一级分类 二级分类用两个字段
            if (StringUtils.isBlank(channelSkuCreateDTO.getCategory_name())) {
                channelSkuCreateDTO.setCategory_code(channelShopCategoryDTOS.get(0).getCategory_code());
                channelSkuCreateDTO.setCategory_name(channelShopCategoryDTOS.get(0).getCategory_name());
            }
        }
        channelSkuCreateDTO.setTag_id(item.getString("tag_id"));
        channelSkuCreateDTO.setZh_name(item.getString("zh_name"));
        channelSkuCreateDTO.setOrigin_name(item.getString("origin_name"));
        channelSkuCreateDTO.setPicture(item.getString("picture"));
        channelSkuCreateDTO.setIs_sold_out(item.getInteger("is_sold_out"));
        channelSkuCreateDTO.setPrice(item.getFloat("price"));
        channelSkuCreateDTO.setMin_order_count(item.getInteger("min_order_count"));
        channelSkuCreateDTO.setBox_price(item.getFloat("box_price"));
        channelSkuCreateDTO.setBox_num(item.getFloat("box_num"));
        channelSkuCreateDTO.setUnit(item.getString("unit"));
        channelSkuCreateDTO.setDescription(item.getString("description"));
        channelSkuCreateDTO.setSequence(item.getInteger("sequence"));
        channelSkuCreateDTO.setIsSp(item.getInteger("isSp"));
        channelSkuCreateDTO.setProduct_name(item.getString("product_name"));
        channelSkuCreateDTO.setFlavour(item.getString("flavour"));
        channelSkuCreateDTO.setDescription(((JSONObject) item).getString("description"));
        channelSkuCreateDTO.setIs_specialty(((JSONObject) item).getInteger("is_specialty"));
        channelSkuCreateDTO.setProperties(((JSONObject) item).getString("properties"));

        channelSkuCreateDTO.setAudit_status(((JSONObject) item).getInteger("audit_status")); // 商品审核状态
        channelSkuCreateDTO.setIs_complete(((JSONObject) item).getInteger("is_complete"));// 信息是否完整

        // 基础名称，补充语完善
        channelSkuCreateDTO.setBase_name(((JSONObject)item).getString("base_name"));
        channelSkuCreateDTO.setProduct_name_supplement(((JSONObject)item).getString("product_name_supplement"));
        channelSkuCreateDTO.setProduct_name_supplement_seq(((JSONObject)item).getInteger("product_name_supplement_seq"));

        SkuInfoDTO skuInfoDTO = mtConverterService.skuCreateDTOMapping(channelSkuCreateDTO);
        setBoxInfo(channelSkuCreateDTO, skuInfoDTO);// 设置包装盒信息
        return response.setStatus(ResultGenerator.genSuccessResult()).setSkuInfo(skuInfoDTO);
    }

    /**
     * @param request 单门店接口
     * @return
     */
    @Override
    public BatchGetSkuInfoResponse batchGetSkuInfo(BatchGetSkuInfoRequest request) {
        BatchGetSkuInfoResponse response = new BatchGetSkuInfoResponse().setStatus(ResultGenerator.genSuccessResult());

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();
        long storeId = request.getStoreId();

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setSkuInfos(Collections.emptyList());
        }

        // 构造参数
        int pageSize = request.getPageSize();
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        bizParam.put("offset", String.valueOf((request.getPageNum() - 1) * pageSize));
        bizParam.put("limit", String.valueOf(pageSize));
        bizParam.put("app_poi_code", appPoiCode);

        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.BATCH_GET_SKUINFO, appPoiCode);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY)).setSkuInfos(Collections.emptyList());
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Lists.newArrayList(storeId));
        // 请求线上渠道
        Map skuInfoMap = mtBrandChannelGateService.sendGet(skuList, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(skuInfoMap) || Objects.isNull(skuInfoMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(skuInfoMap.get(ProjectConstant.DATA))) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团商品失败"));
        }

        List<SkuInfoDTO> skuInfoDTOS = Lists.newArrayList();
        JSONArray skuInfoJSONArray = (JSONArray) skuInfoMap.get(ProjectConstant.DATA);
        JSONObject extraInfo = (JSONObject) skuInfoMap.get(ProjectConstant.EXTRA_INFO);
        int totalCount = extraInfo != null && null != extraInfo.get(ProjectConstant.TOTAL_COUNT) ? (int) extraInfo.get(ProjectConstant.TOTAL_COUNT) : 0;
        int totalPage = totalCount % request.getPageSize() == 0 ? totalCount / request.getPageSize() : (totalCount / request.getPageSize()) + 1;
        PageInfo pageInfo = new PageInfo(request.getPageNum(), request.getPageSize(), totalPage, totalCount);

        // 组装数据
        skuInfoJSONArray.forEach(item -> {
            ChannelSkuCreateDTO channelSkuCreateDTO = new ChannelSkuCreateDTO();
            // 异常情况处理，可能存在没有sku和店内分类信息，过滤
            if (StringUtils.isBlank(((JSONObject) item).getString("skus")) || StringUtils.isBlank(((JSONObject) item).getString("category_list"))) {
                log.warn("MtChannelSku.batchGetSkuInfo request:{} skus or category_list is empty json:{}", request, item);
                return;
            }
            List<ChannelSkuInfoDTO> channelSkuInfoDTOS = JSONArray.parseArray((((JSONObject) item).get("skus")).toString(), ChannelSkuInfoDTO.class);
            List<ChannelShopCategoryDTO> channelShopCategoryDTOS = JSONArray.parseArray((((JSONObject) item).get("category_list")).toString(), ChannelShopCategoryDTO.class);
            if (CollectionUtils.isEmpty(channelSkuInfoDTOS) || CollectionUtils.isEmpty(channelShopCategoryDTOS)) {
                log.warn("MtChannelSku.batchGetSkuInfo request:{} skus or category_list is empty json:{}", request, item);
                return;
            }
            channelSkuCreateDTO.setSkus(channelSkuInfoDTOS);
            channelSkuCreateDTO.setCategory_list(channelShopCategoryDTOS);
            channelSkuCreateDTO.setApp_poi_code(appPoiCode);
            channelSkuCreateDTO.setApp_food_code(((JSONObject) item).getString("app_food_code"));
            channelSkuCreateDTO.setName(((JSONObject) item).getString("name"));
            channelSkuCreateDTO.setCategory_code(((JSONObject) item).getString("secondary_category_code"));
            channelSkuCreateDTO.setCategory_name(((JSONObject) item).getString("secondary_category_name"));
            // 美团不是返回末级分类，而是固定的一级分类 二级分类用两个字段
            if (StringUtils.isBlank(channelSkuCreateDTO.getCategory_name())) {
                channelSkuCreateDTO.setCategory_code(((JSONObject) item).getString("category_code"));
                channelSkuCreateDTO.setCategory_name(((JSONObject) item).getString("category_name"));
            }
            // TODO 原有SKU上面的分类字段会被废弃 实际有效的是分类列表 美团支持多分类但是中台只能处理一个分类
            if (!CollectionUtils.isEmpty(channelShopCategoryDTOS)) {
                channelSkuCreateDTO.setCategory_code(channelShopCategoryDTOS.get(0).getSecondary_category_code());
                channelSkuCreateDTO.setCategory_name(channelShopCategoryDTOS.get(0).getSecondary_category_name());
                // 美团不是返回末级分类，而是固定的一级分类 二级分类用两个字段
                if (StringUtils.isBlank(channelSkuCreateDTO.getCategory_name())) {
                    channelSkuCreateDTO.setCategory_code(channelShopCategoryDTOS.get(0).getCategory_code());
                    channelSkuCreateDTO.setCategory_name(channelShopCategoryDTOS.get(0).getCategory_name());
                }
            }
            channelSkuCreateDTO.setTag_id(((JSONObject) item).getString("tag_id"));
            channelSkuCreateDTO.setZh_name(((JSONObject) item).getString("zh_name"));
            channelSkuCreateDTO.setOrigin_name(((JSONObject) item).getString("origin_name"));
            channelSkuCreateDTO.setPicture(((JSONObject) item).getString("picture"));
            channelSkuCreateDTO.setIs_sold_out(((JSONObject) item).getInteger("is_sold_out"));
            channelSkuCreateDTO.setPrice(((JSONObject) item).getFloat("price"));
            channelSkuCreateDTO.setMin_order_count(((JSONObject) item).getInteger("min_order_count"));
            channelSkuCreateDTO.setBox_price(((JSONObject) item).getFloat("box_price"));
            channelSkuCreateDTO.setBox_num(((JSONObject) item).getFloat("box_num"));
            channelSkuCreateDTO.setUnit(((JSONObject) item).getString("unit"));
            channelSkuCreateDTO.setSequence(((JSONObject) item).getInteger("sequence"));
            channelSkuCreateDTO.setIsSp(((JSONObject) item).getInteger("isSp"));
            channelSkuCreateDTO.setProduct_name(((JSONObject) item).getString("product_name"));
            channelSkuCreateDTO.setFlavour(((JSONObject) item).getString("flavour"));
            channelSkuCreateDTO.setVideo_id(((JSONObject) item).getLong("video_id"));
            channelSkuCreateDTO.setVideo_url_mp4(((JSONObject) item).getString("video_url_mp4"));
            channelSkuCreateDTO.setDescription(((JSONObject) item).getString("description"));
            channelSkuCreateDTO.setIs_specialty(((JSONObject) item).getInteger("is_specialty"));
            channelSkuCreateDTO.setProperties(((JSONObject) item).getString("properties"));

            channelSkuCreateDTO.setAudit_status(((JSONObject) item).getInteger("audit_status")); // 商品审核状态
            channelSkuCreateDTO.setIs_complete(((JSONObject) item).getInteger("is_complete"));// 信息是否完整

            // 基础名称，补充语完善
            channelSkuCreateDTO.setBase_name(((JSONObject)item).getString("base_name"));
            channelSkuCreateDTO.setProduct_name_supplement(((JSONObject)item).getString("product_name_supplement"));
            channelSkuCreateDTO.setProduct_name_supplement_seq(((JSONObject)item).getInteger("product_name_supplement_seq"));

            SkuInfoDTO skuInfoDTO = mtConverterService.skuCreateDTOMapping(channelSkuCreateDTO);
            setBoxInfo(channelSkuCreateDTO, skuInfoDTO);// 设置包装盒信息
            skuInfoDTOS.add(skuInfoDTO);
        });
        return response.setStatus(ResultGenerator.genSuccessResult()).setSkuInfos(skuInfoDTOS).setPageInfo(pageInfo);
    }

    private void setBoxInfo(ChannelSkuCreateDTO channelSkuCreateDTO, SkuInfoDTO skuInfoDTO) {
        ChannelSkuInfoDTO channelSkuInfoDTO = channelSkuCreateDTO.getSkus().get(0);
        if (channelSkuInfoDTO != null) {
            // 包装盒设置
            if (StringUtils.isNotBlank(channelSkuInfoDTO.getBox_num())) {
                skuInfoDTO.setBoxQuantity(Double.valueOf(channelSkuInfoDTO.getBox_num()).intValue());
            } else if (StringUtils.isNotBlank(channelSkuInfoDTO.getLadder_box_num())) {
                skuInfoDTO.setBoxQuantity(Double.valueOf(channelSkuInfoDTO.getLadder_box_num()).intValue());
            } else {
                skuInfoDTO.setBoxQuantity(1);
            }

            // 包装盒价格
            if (StringUtils.isNotBlank(channelSkuInfoDTO.getBox_price())) {
                skuInfoDTO.setBoxPrice(Double.valueOf(channelSkuInfoDTO.getBox_price()));
            } else if (StringUtils.isNotBlank(channelSkuInfoDTO.getLadder_box_price())) {
                skuInfoDTO.setBoxPrice(Double.valueOf(channelSkuInfoDTO.getLadder_box_price()));
            } else {
                skuInfoDTO.setBoxPrice(0.0D);
            }
        }
    }

    /**
     * 查询店内分类
     *
     * @param request 单门店接口
     * @return
     */
    @Override
    public GetCategoryResponse batchGetChannelStoreCategoryInfo(CatRequest request) {
        GetCategoryResponse response = new GetCategoryResponse().setStatus(ResultGenerator.genSuccessResult());
        if (request == null || request.getBaseInfo() == null) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "拉取美团前台分类失败"));
        }

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();
        long storeId = request.getStoreId();

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setCatInfoList(Collections.emptyList());
        }
        // 构造参数
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        bizParam.put("app_poi_code", appPoiCode);

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        //租户维度限频处理
        Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.BATCH_GET_CATEGORYINFO, appId);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            log.info("ChannelSkuThriftService.batchGetChannelStoreCategoryInfo 获取当前执行权失败，返回重试");
            return response.setStatus(ResultGenerator.genResult(ResultCode.TRIGGER_LIMIT)).setCatInfoList(Collections.emptyList());
        }

        // 请求线上渠道
        Map skuInfoMap = mtBrandChannelGateService.sendGet(frontCatList, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(skuInfoMap) || Objects.isNull(skuInfoMap.get(ProjectConstant.DATA))
                || ProjectConstant.NG.equals(skuInfoMap.get(ProjectConstant.DATA))) {
            Integer errorCode = Optional.ofNullable(skuInfoMap)
                    .map(skuInfo -> (JSONObject)skuInfo.get(ProjectConstant.ERROR))
                    .map(error -> error.getInteger(ProjectConstant.CODE))
                    .orElse(null);
            String errorMessage = Optional.ofNullable(skuInfoMap)
                    .map(skuInfo -> (JSONObject)skuInfo.get(ProjectConstant.ERROR))
                    .map(error -> error.getString(ProjectConstant.MSG))
                    .orElse("获取美团前台分类失败");
            if (Objects.equals(ResultCodeEnum.MT_STORE_NOT_EXIST.getValue(), errorCode)) {
                return response.setStatus(ResultGenerator.genResult(ResultCodeEnum.MT_STORE_NOT_EXIST, errorMessage));
            } else {
                return response.setStatus(ResultGenerator.genResult(ResultCodeEnum.FAIL, errorMessage));
            }
        }
        List<CatInfo> catInfos = Lists.newArrayList();
        List<ChannelStoreCategoryDTO> channelStoreCategoryDTOS = Lists.newArrayList();
        JSONArray catInfoJSONArray = (JSONArray) skuInfoMap.get(ProjectConstant.DATA);
        // 组装数据
        catInfoJSONArray.forEach(item -> {
            genChildCategoryInfo((JSONObject) item, channelStoreCategoryDTOS, "0", "", 1);
        });
        catInfos.addAll(mtConverterService.channelStoreCategoryDTOMapping(channelStoreCategoryDTOS));
        return response.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(catInfos);
    }

    /**
     * @param request 多门店接口；内部逐门店串行
     * @return
     */
    @Override
    public ResultData changeCustomSkuId(ChangeCustomSkuIdRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        // 逐门店串行
        request.getParamList().forEach(data -> {
            try {
                long storeId = data.getStoreId();
                BaseRequest storeRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                ChangeCustomSkuId changeCustomSkuId = mtConverterService.changeCustomSkuId(data);

                // 调用渠道接口
                Map<Long, ChannelResponseDTO<String>> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CHANGE_CUSTOM_SKU_ID, storeRequest, changeCustomSkuId);

                // 组装返回结果
                ChannelResponseDTO<String> channelResponseDTO = postResult.get(data.getStoreId());
                if (channelResponseDTO.isSuccess()) {
                    ResultSuccessSku successSku = new ResultSuccessSku(data.getStoreId(), data.getCustomSkuId(), ResultCode.SUCCESS.getMsg(), channelId, data.getChannelSkuId());
                    resultData.getSucData().add(successSku);
                } else {
                    boolean toUpdateChannelSkuIdExists = MccConfigUtil.toUpdateChannelSkuIdExists(channelId, channelResponseDTO.getErrorMsg());
                    if (toUpdateChannelSkuIdExists && changeChannelSkuIdOnly(data) && isChannelSkuIdUpdatedSuccess(request, data)) {
                        log.info("更新货号失败，查询平台货号已更新成功，认为成功 data:{}", data);
                        ResultSuccessSku successSku = new ResultSuccessSku(data.getStoreId(), data.getCustomSkuId(), ResultCode.SUCCESS.getMsg(), channelId, data.getChannelSkuId());
                        resultData.getSucData().add(successSku);
                    } else {
                        ResultErrorSku resultErrorSku = new ResultErrorSku(data.getStoreId(), data.getCustomSkuId(),
                                channelResponseDTO.getErrorMsg(), channelId, ResultCode.FAIL.getCode(), data.getChannelSkuId(), null);
                        resultData.getErrorData().add(resultErrorSku);
                    }
                }
            } catch (IllegalArgumentException e) {
                log.error("MtChannelSkuServiceImpl.changeCustomSkuId 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), Lists.newArrayList(data.getCustomSkuId()));

            } catch (Exception e) {
                log.error("MtChannelSkuServiceImpl.changeCustomSkuId 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, Lists.newArrayList(data.getCustomSkuId()));
            }
        });

        return resultData;
    }

    private boolean isChannelSkuIdUpdatedSuccess(ChangeCustomSkuIdRequest request, ChangeCustomSkuIdDTO data) {
        GetSkuInfoRequest getSkuInfoRequest = new GetSkuInfoRequest();
        getSkuInfoRequest.setBaseInfo(request.getBaseInfo());
        getSkuInfoRequest.setCustomSkuId(data.getOrgCustomSkuId());
        getSkuInfoRequest.setStoreId(data.getStoreId());
        GetSkuInfoResponse getSkuInfoResponse = getSkuInfo(getSkuInfoRequest);
        log.info("更新货号失败后，查询当前货号信息data:{} 结果：{}", data, getSkuInfoResponse);
        return getSkuInfoResponse != null && getSkuInfoResponse.getSkuInfo() != null &&
                StringUtils.equals(data.getChannelSkuId(), getSkuInfoResponse.getSkuInfo().getSkuId());
    }

    private boolean changeChannelSkuIdOnly(ChangeCustomSkuIdDTO data) {
        return StringUtils.isBlank(data.getCustomSkuId()) && StringUtils.isNotBlank(data.getChannelSkuId()) && StringUtils.isNotBlank(data.getOrgChannelSkuId());
    }

    private void genChildCategoryInfo(JSONObject jsonObject, List<ChannelStoreCategoryDTO> channelStoreCategoryDTOS,
                                      String parentId, String parentName, int depth) {
        JSONArray catInfoJSONArray = (JSONArray) jsonObject.get(ProjectConstant.CHILDREN);

        ChannelStoreCategoryDTO channelStoreCategoryDTO = new ChannelStoreCategoryDTO();
        channelStoreCategoryDTO.setCategoryId(jsonObject.getString("code"));
        channelStoreCategoryDTO.setCategoryName(jsonObject.getString("name"));
        channelStoreCategoryDTO.setSequence(jsonObject.getInteger("sequence"));
        channelStoreCategoryDTO.setParentId(parentId);
        channelStoreCategoryDTO.setParentName(parentName);
        channelStoreCategoryDTO.setDepth(depth);
        channelStoreCategoryDTO.setSmartSort(jsonObject.getInteger("smart_switch"));
        channelStoreCategoryDTO.setCustomCategoryId(channelStoreCategoryDTO.getCategoryId());
        channelStoreCategoryDTOS.add(channelStoreCategoryDTO);


        if (CollectionUtils.isNotEmpty(catInfoJSONArray)) {
            // 组装数据
            for (Object item : catInfoJSONArray) {
                int childDepth = depth + 1;
                genChildCategoryInfo((JSONObject) item, channelStoreCategoryDTOS, channelStoreCategoryDTO.getCategoryId(),
                        channelStoreCategoryDTO.getCategoryName(), childDepth);
            }
        }
    }

    @Override
    public BatchUploadPictureByUrlsResponse batchUploadImageUrls(PictureUploadRequest request) {
        BatchUploadPictureByUrlsResponse response = new BatchUploadPictureByUrlsResponse().setStatus(ResultGenerator.genSuccessResult());
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return response;
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(request.getBaseInfo().getStoreIdList());

        Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.PICTURE_UPLOAD_BY_URL, appId);
        if (!tryAcquire) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY));
        }

        Map<String, String> bizParam = Maps.newHashMap();
        bizParam.put("mt_urls", request.getParamList().stream().map(PictureUploadDTO::getUrl).distinct()
                .collect(Collectors.joining(",")));

        Map<String, Object> resultMap = mtBrandChannelGateService.sendPost(batchUploadImageUrl, null, baseRequest, bizParam);

        buildResponse(response, resultMap);
        return response;
    }

    private void buildResponse(BatchUploadPictureByUrlsResponse response,
                               Map<String, Object> resultMap) {
        if (MapUtils.isEmpty(resultMap)) {
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团渠道图片ID失败"));
            return;
        }
        if (ProjectConstant.NG.equals(resultMap.get(ProjectConstant.DATA))) {
            JSONObject item = (JSONObject) resultMap.get(ProjectConstant.ERROR);
            if (Objects.isNull(item) || !item.containsKey(ProjectConstant.CODE)) {
                response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团渠道图片ID失败"));
                return;
            }
            Integer errCode = item.getInteger(ProjectConstant.CODE);
            if (Objects.equals(MtResultCodeEnum.SIGN_ERROR.getCode(), errCode)) {
                response.setStatus(ResultGenerator.genResult(ResultCode.INVALID_PARAM, "获取美团渠道图片ID失败"));
                return;
            } else if (Objects.equals(MtResultCodeEnum.TRIGGER_FLOW_CONTROL.getCode(), errCode)) {
                response.setStatus(ResultGenerator.genResult(ResultCode.TRIGGER_LIMIT, "获取美团渠道图片ID失败"));
                return;
            }
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团渠道图片ID失败"));
            return ;
        }

        Integer resultCode = (Integer) resultMap.get(ProjectConstant.RESULT_CODE);
        if (Objects.isNull(resultCode)
                || Objects.equals(MtChannelResultEnum.SYSTEM_ERROR.getValue(), resultCode)) {
            response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团渠道图片ID失败"));
            return;
        }

        response.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));
        JSONArray successJSONArray = (JSONArray) resultMap.get(ProjectConstant.SUCCESS_LIST);
        if (Objects.nonNull(successJSONArray) && !successJSONArray.isEmpty()) {
            List<PictureUrl2IdSuccessDTO> successDTOS = Lists.newArrayList();
            successJSONArray.forEach(item -> {
                PictureUrl2IdSuccessDTO pictureUrl2IdSuccessDTO = new PictureUrl2IdSuccessDTO();
                pictureUrl2IdSuccessDTO.setPictureUrl(((JSONObject) item).getString("mt_url"));
                pictureUrl2IdSuccessDTO.setPictureId(((JSONObject) item).getString("image_id"));
                successDTOS.add(pictureUrl2IdSuccessDTO);
            });
            response.setSuccessList(successDTOS);
        }

        JSONArray errorJSONArray = (JSONArray) resultMap.get(ProjectConstant.ERROR_LIST);
        if (Objects.nonNull(errorJSONArray) && !errorJSONArray.isEmpty()) {
            List<PictureUrl2IdErrorDTO> errorDTOS = Lists.newArrayList();
            errorJSONArray.forEach(item -> {
                PictureUrl2IdErrorDTO pictureUrl2IdErrorDTO = new PictureUrl2IdErrorDTO();
                pictureUrl2IdErrorDTO.setPictureUrl(((JSONObject) item).getString("mt_url"));
                pictureUrl2IdErrorDTO.setCode(((JSONObject) item).getInteger("code"));
                pictureUrl2IdErrorDTO.setMsg(((JSONObject) item).getString("msg"));
                errorDTOS.add(pictureUrl2IdErrorDTO);
            });
            response.setErrorList(errorDTOS);
        }
    }

    @Override
    public ResultData batchChangeCustomSkuIdForCleanJob(CustomSkuIdChangeRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();
            long storeId = request.getBaseInfo().getPoiId();

            // 获取渠道门店编码
            String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
            Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
            if (MapUtils.isEmpty(channelStoreDOMap)) {
                ResultDataUtils.combineChangeExceptionDataList(resultData, "获取门店信息异常", request.getParamList());
                return resultData;
            }

            BaseRequest baseInfo = new BaseRequest();
            baseInfo.setTenantId(request.getBaseInfo().getTenantId());
            baseInfo.setChannelId(request.getBaseInfo().getChannelId());
            baseInfo.setStoreIdList(Lists.newArrayList(request.getBaseInfo().getPoiId()));

            Map<String, Object> bizParam = Maps.newHashMap();
            String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
            bizParam.put("app_poi_code", appPoiCode);
            bizParam.put("spu_data", JSON.toJSONString(mtConverterService.changeCustomSkuIdInfoMapping(request.getParamList())));

            //限频处理
            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseInfo);
            String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
            boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.GET_SPU_DETAIL, appId);

            if (!tryAcquire) {
                // 限频导致失败
                resultData.setStatus(genResult(ResultCode.FAIL_ALLOW_RETRY, ProjectConstant.TRIGGER_LIMIT_MSG));
                ResultDataUtils.combineChangeExceptionDataList(resultData, ProjectConstant.TRIGGER_LIMIT_MSG, request.getParamList());
                return resultData;
            }

            Map<String, Object> changeCustomSkuIdResMap =
                    mtBrandChannelGateService.sendPost(batchChangeCustomSkuIdUrl, null, baseInfo, bizParam);
            parseBatchCustomSkuIdResponse(resultData, request, changeCustomSkuIdResMap);
        } catch (IllegalArgumentException e) {
            log.error("MtChannelSkuServiceImpl.batchChangeCustomSkuIdForCleanJob 参数校验失败, request:{}", request, e);
            ResultDataUtils.combineChangeExceptionDataList(resultData, e.getMessage(), request.getParamList());

        } catch (Exception e) {
            log.error("MtChannelSkuServiceImpl.batchChangeCustomSkuIdForCleanJob 服务异常, request:{}", request, e);
            ResultDataUtils.combineChangeExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR.getDesc(), request.getParamList());
        }

        return resultData;
    }

    private void parseBatchCustomSkuIdResponse(ResultData resultData, CustomSkuIdChangeRequest request,
                                               Map<String, Object> responseMap) {
        // result_code: 1-全部操作成功；2-部分成功，失败的数据存在error_list字段中；3-全部操作失败，失败的数据存在error_list字段中
        if (MapUtils.isNotEmpty(responseMap) && (int) responseMap.get(ProjectConstant.RESULT_CODE) == 1) {
            return;
        }

        List<String> customSpuIdList;
        // 若返回无结果时，则实时查询变更数据是否成功进行替换
        if (isAllFailed(responseMap)) {
            customSpuIdList = request.getParamList().stream()
                    .map(CustomSkuIdChangeDTO::getOrgCustomSpuId)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> customSkuIdList = queryAllCustomSkuId(request.getBaseInfo(), customSpuIdList);
            List<CustomSkuIdChangeDTO> updateFailedDtoList = request.getParamList().stream()
                    .filter(changeDto -> CollectionUtils.isEmpty(customSkuIdList) || !customSkuIdList.contains(changeDto.getCustomSkuId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(updateFailedDtoList)) {
                resultData.setStatus(genResult(ResultCode.FAIL));
                ResultDataUtils.combineChangeExceptionDataList(resultData, ProjectConstant.NO_RESPONSE, updateFailedDtoList);
            }
            return;
        }

        List<ChannelSkuIdChangeResponseErr> responseErrList =
                JSONArray.parseArray(responseMap.get(ProjectConstant.ERROR_LIST).toString(), ChannelSkuIdChangeResponseErr.class);
        Map<String, CustomSkuIdChangeDTO> channelSkuIdMap = Fun.toMap(request.getParamList(), CustomSkuIdChangeDTO::getOrgCustomSkuId);
        customSpuIdList =  responseErrList.stream()
                .map(errInfo -> channelSkuIdMap.get(errInfo.getSku_id()).getOrgCustomSpuId())
                .distinct()
                .collect(Collectors.toList());
        List<String> customSkuIdList = queryAllCustomSkuId(request.getBaseInfo(), customSpuIdList);
        List<ResultErrorSku> resultErrorSkus = responseErrList.stream()
                .map(errInfo -> {
                    CustomSkuIdChangeDTO changeDTO = channelSkuIdMap.get(errInfo.getSku_id());
                    return customSkuIdList.contains(changeDTO.getCustomSkuId()) ? null : new ResultErrorSku()
                            .setSkuId(errInfo.getSku_id())
                            .setChannelSkuId(changeDTO.getCustomSkuId())
                            .setChannelSpuId(errInfo.getApp_spu_code())
                            .setErrorMsg(errInfo.getMsg());
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(resultErrorSkus)) {
            resultData.getErrorData().addAll(resultErrorSkus);
            resultData.setStatus(genResult(ResultCode.FAIL));
        }
    }

    private boolean isAllFailed(Map<String, Object> responseMap) {
        if (MapUtils.isEmpty(responseMap) || Objects.isNull(responseMap.get(ProjectConstant.ERROR_LIST))) {
            return true;
        }

        // {"data":"ng","error_list":[{"msg":"签名验证错误","code":703}],"result_code":4,"error":{"msg":"签名验证>      错误","code":703}}
        if (Objects.nonNull(responseMap.get(ProjectConstant.DATA))
                && ProjectConstant.NG.equals(responseMap.get(ProjectConstant.DATA).toString())) {
            return true;
        }

        if (Objects.isNull(responseMap.get(ProjectConstant.ERROR_LIST))) {
           return false;
        }

        List<ChannelSkuIdChangeResponseErr> responseErrList =
                JSONArray.parseArray(responseMap.get(ProjectConstant.ERROR_LIST).toString(), ChannelSkuIdChangeResponseErr.class);
        for (ChannelSkuIdChangeResponseErr responseErr : responseErrList) {
            if (StringUtils.isBlank(responseErr.getSku_id())) {
                return true;
            }
        }

        return false;
    }

    private List<String> queryAllCustomSkuId(BaseChannelPoiRequestSimple channelPoiInfo, List<String> customSpuIdList) {
        BaseRequestSimple baseInfo = new BaseRequestSimple();
        baseInfo.setTenantId(channelPoiInfo.getTenantId());
        baseInfo.setChannelId(channelPoiInfo.getChannelId());

        GetSpuInfosRequest spuInfoRequest = new GetSpuInfosRequest();
        spuInfoRequest.setBaseInfo(baseInfo);
        spuInfoRequest.setStoreId(channelPoiInfo.getPoiId());
        spuInfoRequest.setCustomSpuIds(customSpuIdList);
        try {
            GetSpuInfosResponse response = mtBrandChannelSpuService.getSpuInfoList(spuInfoRequest);
            if (CollectionUtils.isEmpty(response.getSpuInfos())) {
                log.info("未查询到指定商品的商品详情，request {}.", response);
                return Collections.emptyList();
            }

            return response.getSpuInfos().stream()
                    .map(SpuInfoDTO::getSkus)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(List::stream)
                    .map(SkuInSpuInfoDTO::getCustomSkuId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量获取商品详情异常，结果按失败处理。request {}.", spuInfoRequest, e);
        }
        return Collections.emptyList();
    }

}
