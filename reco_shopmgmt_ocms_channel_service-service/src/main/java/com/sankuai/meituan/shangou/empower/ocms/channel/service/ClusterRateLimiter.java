package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;

import java.util.concurrent.TimeUnit;

/**
 * @Title: ClusterRateLimiter
 * @Description:
 * <AUTHOR>
 * @Date 2019/6/26 16:47
 */
public interface ClusterRateLimiter {
    /**
     * 获取请求令牌 并且 返回需要等待的时间
     * @param limitedResourceKey 资源标识
     * @param uuid 请求标识
     * @param isAsync  是否异步请求
     * @return -1: 无限等待；0：表示通过限流 ；其他代表需要等待的毫秒数
     */
    long tryAcquire(String limitedResourceKey, String uuid, Boolean isAsync);
    /**
     * 获取请求令牌 根据rhino管理端配置执行重试策略
     *
     * @param limitedResourceKey 资源标识
     * @param uuid 请求标识
     * @return
     */
    boolean tryAcquire(String limitedResourceKey, String uuid);

    /**
     * 获取请求令牌 根据rhino管理端配置执行重试策略
     *
     * @param channelPostInter 渠道接口枚举
     * @param uuid 请求标识
     * @return
     */
    boolean tryAcquire(ChannelPostInter channelPostInter, String uuid);

    /**
     * 获取请求令牌 并且 返回需要等待的时间
     * @param channelPostInter 渠道接口枚举
     * @param uuid 请求标识
     * @param isAsync  是否异步请求
     * @return -1: 无限等待；0：表示通过限流 ；其他代表需要等待的毫秒数
     */
    long tryAcquire(ChannelPostInter channelPostInter, String uuid, Boolean isAsync);
}
