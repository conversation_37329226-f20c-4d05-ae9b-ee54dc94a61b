package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.request.ChannelCategoryMultiSpecRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.response.ChannelCategoryMultiSpecResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.service.ChannelCategoryThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.MultiSpecTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * product platform渠道类目服务代理
 *
 * <AUTHOR>
 * @date 2023/11/20 20:37
 */
@Slf4j
@Service
public class ChannelCategoryThriftServiceProxy {
    @Resource
    private ChannelCategoryThriftService platformChannelCategoryThriftService;

    /**
     * 获取渠道类目的多规格的类型。默认返回多规格。
     */
    public int getMultiSpecType(Integer channelId, Long tenantId, String channelCategoryId) {
        ChannelCategoryMultiSpecRequest req = new ChannelCategoryMultiSpecRequest();
        req.setChannelId(channelId);
        req.setTenantId(tenantId);
        req.setChannelCategoryId(channelCategoryId);
        int multiSpecType = MultiSpecTypeEnum.COMMON.getCode();
        ChannelCategoryMultiSpecResponse res = null;
        try {
            res = platformChannelCategoryThriftService.queryMultiSpecType(req);
            if (Objects.isNull(res) || Objects.isNull(res.getMultiSpecType())) {
                log.warn("channelCategoryThriftService.queryMultiSpecType 返回结果为null，返回默认值（多规格）! request={}, response={}", req, res);
                return multiSpecType;
            }
            multiSpecType = res.getMultiSpecType();
            log.info("channelCategoryThriftService.queryMultiSpecType 成功! request={}, response={}", req, res);
        } catch (Exception e) {
            log.error("channelCategoryThriftService.queryMultiSpecType 异常，返回默认值（多规格）! request={}, response={}", req, res, e);
        }
        return multiSpecType;
    }
}