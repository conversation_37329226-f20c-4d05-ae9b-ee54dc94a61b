package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.alibaba.fastjson.JSON;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.BalanceBillListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.CheckBillResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JdBalanceBillParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JdCheckBillParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoSortDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChangeCustomSkuIdDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelActivityInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ImgHandleQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuSellStatusInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoSellStatusByChannelSkuIdDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSkuIdDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ViolateSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RefundSku;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 京东到家实体转换统一接口类
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:09
 **/
@Mapper(componentModel = "spring", imports = {ConverterUtils.class,ChannelSkuCreateDTO.class, StockVendibility.class, DateUtils.class, MoneyUtils.class, JddjOrderConvertUtil.class, JSON.class, OrderStatusConverter.class, BigDecimal.class, ChannelPoiHotlineUtil.class})
public interface JddjConverterService {
    @Mappings({
            @Mapping(target = "outSkuId", source = "skuId"),
            @Mapping(target = "skuId", constant = ""),
            @Mapping(target = "skuName", source = "name"),
            @Mapping(target = "shopCategories", expression = "java(JddjConvertUtil.convert2JDDJCategoryCode(param))"),
            @Mapping(target = "categoryId", expression = "java(ChannelSkuCreateDTO.toCategoryId(param.getCategoryFirst(), param.getCategorySecond(), param.getCategory()))"),
            @Mapping(target = "brandId", source = "brand"),
            @Mapping(target = "weight", expression = "java((param.isSetWeight() && param.getWeight() > 0) ?Double.valueOf(param.getWeight()/1000).floatValue() : 1)"),
            @Mapping(target = "images", source = "pictures"),
            @Mapping(target = "fixedStatus", expression = "java(param.isSetSkuStatus() && param.getSkuStatus() != 2 ? param.getSkuStatus() : 1)"),
            @Mapping(target = "skuPrice", expression = "java(param.isSetPrice()?Double.valueOf(param.getPrice()*100).longValue():null)"),
            @Mapping(target = "isSale", expression = "java(param.isSetSkuStatus() && param.getSkuStatus() == 1 ? true : false)"),
            @Mapping(target = "upcCode", source = "upc")
    })
    ChannelSkuCreateDTO skuCreateMapping(SkuInfoDTO param);

    @Mappings({
            @Mapping(target = "skuId", source = "skuId"),
            @Mapping(target = "customSkuId", source = "outSkuId"),
            @Mapping(target = "category", source = "categoryId"),
            @Mapping(target = "frontCategory", source = "shopCategories"),
            @Mapping(target = "channelFrontCategory", source = "shopCategories"),
            @Mapping(target = "weight", source = "weight"),
            @Mapping(target = "upc", source = "upcCode"),
            @Mapping(target = "price", expression = "java(MoneyUtils.fenToYuan(skuCreateDTO.getSkuPrice().intValue()).doubleValue())"),
            @Mapping(target = "skuStatus", expression = "java(skuCreateDTO.getFixedStatus() == 1 ? 1 : 2)"),
            @Mapping(target = "name", source = "skuName"),
            @Mapping(target = "brand", source = "brandId"),
//            @Mapping(target = "isSp", constant = "1"),
            @Mapping(target = "unit", constant = "1"),
            @Mapping(target = "spec", constant = ""),
            @Mapping(target = "sourceType", constant = "3"),
    })
    SkuInfoDTO skuCreateDTOMapping(ChannelSkuCreateDTO skuCreateDTO);
    List<SkuInfoDTO> skuCreateDTOsMapping(List<ChannelSkuCreateDTO> skuDTOs);

    @Mappings({
            @Mapping(target = "uniqueCode", source = "upc"),
            @Mapping(target = "outSku", source = "skuId"),
            @Mapping(target = "jdPrice", source = "price"),
            @Mapping(target = "shopCategoryId", expression = "java(Long.parseLong(JddjConvertUtil.convert2JDDJCategoryCode(param)))"),
            @Mapping(target = "isSale", constant = "true"),
    })
    ChannelUpcCreateDTO upcCreateMapping(SkuInfoDTO param);

    List<ChannelUpcCreateDTO> upcCreateMapping(List<SkuInfoDTO> param);

    @Mappings({
            @Mapping(target = "catId", source = "categoryId"),
            @Mapping(target = "name", source = "categoryName"),
            @Mapping(target = "sequence", source = "sequence"),
            @Mapping(target = "depth", source = "depth"),
            @Mapping(target = "parentId", source = "parentId"),
    })
    CatInfo channelStoreCategoryDTOMapping(ChannelStoreCategoryDTO data);
    List<CatInfo> channelStoreCategoryDTOMapping(List<ChannelStoreCategoryDTO> data);

    @Mappings({
            @Mapping(target = "id", source = "channelCategoryCode"),
            @Mapping(target = "moveProduct", source = "forceDelete")
    })
    ChannelCategoryDeleteDTO deleteCategory(CategoryInfoDeleteDTO data);

    @Mappings({
            @Mapping(target = "pid", source = "channelCategoryCode"),
            @Mapping(target = "childIds", expression = "java(ConverterUtils.skuCategoryCode(data.getSortItemList()))"),
    })
    ChannelCategorySortDTO sortCategory(CategoryInfoSortDTO data);

    @Mappings({
            @Mapping(target = "appPoiCode", source = "outSystemId"),
            @Mapping(target = "name", source = "stationName"),
            @Mapping(target = "province", source = "provinceName"),
            @Mapping(target = "city", source = "cityName"),
            @Mapping(target = "county", source = "countyName"),
            @Mapping(target = "address", source = "stationAddress"),
            @Mapping(target = "longitude", source = "lng"),
            @Mapping(target = "latitude", source = "lat"),
            @Mapping(target = "invoiceSupport", source = "supportInvoice"),
            @Mapping(target = "openLevel", source = "closeStatus"),
            @Mapping(target = "isOnline", source = "yn"),
            @Mapping(target = "closeTime",expression = "java(ConverterUtils.getJdCloseTime(channelPoiInfo.getServiceTimeEnd1(), channelPoiInfo.getServiceTimeEnd2()))"),
            @Mapping(target = "channelPoiCode",source = "stationNo"),
            @Mapping(target = "promotionInfo", source = "storeNotice"),
            @Mapping(target = "shippingTime", expression = "java(ConverterUtils.getJdShippingTime(channelPoiInfo.getServiceTimeStart1(), " +
                    "channelPoiInfo.getServiceTimeEnd1(), channelPoiInfo.getServiceTimeStart2(), channelPoiInfo.getServiceTimeEnd2()))"),
            @Mapping(target = "hotline", expression = "java(ChannelPoiHotlineUtil.getHotline(channelPoiInfo.getPhone(),channelPoiInfo.getMobile()))"),
    })
    PoiInfo poiInfoMapping(ChannelPoiInfo channelPoiInfo);

    @Mappings({
            @Mapping(target = "outSkuId", source = "skuId"),
            @Mapping(target = "fixedStatus", constant = "4")
    })
    ChannelSkuDeleteDTO skuDeleteMapping(SkuInfoDeleteDTO skuInfoDeleteDTO);

    @Mappings({
            @Mapping(target = "skuIds", source = "channelSkuIdList")
    })
    ChannelSkuPicUploadStatusDTO pictureUploadStatusMapping(PictureUploadStatusDTO pictureUploadStatusDTO);

    @Mappings({
            @Mapping(target = "channelSkuId", source = "skuId"),
            @Mapping(target = "handleStatus", source = "handleStatus"),
            @Mapping(target = "handleStatusDesc", source = "handleStatusStr"),
            @Mapping(target = "sourceImgUrl", source = "sourceImgUrl"),
            @Mapping(target = "remark", source = "handleRemark"),
            @Mapping(target = "errorMsg", source = "handleErrLog"),
            @Mapping(target = "skuImgSort", source = "skuImgSort"),
            @Mapping(target = "isMain", source = "isMain"),
            @Mapping(target = "imgType", source = "imgType"),
    })
    ImgHandleQueryDTO imgHandleQueryResultMapping(ImgHandleQueryResult imgHandleQueryResult);

    List<ImgHandleQueryDTO> imgHandleQueryResultMappingList(List<ImgHandleQueryResult> imgHandleQueryResultList);

    @Mappings({
        @Mapping(target = "channelSkuId", source = "skuId")
    })
    ViolateSkuDTO convertViolateSku2DTO(OpenSkuManageReasonResult openViolateSku);
    List<ViolateSkuDTO> convertViolateSkuList2DTO(List<OpenSkuManageReasonResult> openViolateSku);

    @Mappings({
            @Mapping(target = "outSkuId", source = "skuId"),
            @Mapping(target = "price", expression = "java(ConverterUtils.priceConvert(skuPriceDTO.getPrice()))"),
    })
    SkuPriceInfo updatePrice(SkuPriceDTO skuPriceDTO);

    List<SkuPriceInfo> updatePrice(List<SkuPriceDTO> skuPriceDTO);

    @Mappings({
            @Mapping(target = "outStationNo", constant = "{}"),
            @Mapping(target = "skuPriceInfoList", source = "paramList"),
    })
    ChannelPriceUpdateDTO updatePrice(SkuPriceRequest request);

    @Mappings({
            @Mapping(target = "outSkuId", source = "skuId"),
            @Mapping(target = "price", expression = "java(ConverterUtils.priceConvert(skuPriceDTO.getPrice()))"),
    })
    SkuPriceInfo updatePrice(SkuPriceMultiChannelDTO skuPriceDTO);

    List<SkuPriceInfo> updatePriceM(List<SkuPriceMultiChannelDTO> skuPriceDTO);

    @Mappings({
            @Mapping(target = "outStationNo", constant = "{}"),
            @Mapping(target = "skuPriceInfoList", source = "paramList"),
    })
    ChannelPriceUpdateDTO updatePriceMultiChannel(SkuPriceMultiChannelRequest request);

    @Mappings({
            @Mapping(target = "outStationNo", constant = "{}"),
            @Mapping(target = "userPin", constant = "system"),
            @Mapping(target = "skuStockList", source = "skuStockList"),
    })
    ChannelStockUpdateDTO updateStock(SkuStockRequest skuStockRequest);

    @Mappings({
            @Mapping(target = "outSkuId", source = "customSkuId"),
            @Mapping(target = "stockQty", source = "stockQty"),
    })
    SkuStockInfo updateSpuStock(SkuInSpuStockDetail skuStockDetail);
    List<SkuStockInfo> updateSpuStock(List<SkuInSpuStockDetail> skuStockDetailList);

    @Mappings({
            @Mapping(target = "outSkuId", source = "skuId"),
            @Mapping(target = "stockQty", source = "stockQty"),
    })
    SkuStockInfo updateStock(SkuStockDTO skuStockRequest);
    List<SkuStockInfo> updateStock(List<SkuStockDTO> skuStockRequest);

    @Mappings({
            @Mapping(target = "outSkuId", source = "skuId"),
            @Mapping(target = "stockQty", source = "stockQty"),
    })
    SkuStockInfo updatePriceMultiChannel(SkuStockMultiChannelDTO request);
    List<SkuStockInfo> updatePriceMultiChannel(List<SkuStockMultiChannelDTO> request);
    @Mappings({
            @Mapping(target = "outStationNo", constant = "{}"),
            @Mapping(target = "userPin", constant = "system"),
            @Mapping(target = "skuStockList", source = "skuStockList"),
    })
    ChannelStockUpdateDTO updateStockMultiChannel(SkuStockMultiChannelRequest request);

    @Mappings({
        @Mapping(source = "orderId", target = "orderId")
    })
    ChannelOrderDetailParam channelOrderDetailParamMapping(GetChannelOrderDetailRequest request);

    @Mappings({
        @Mapping(source = "orderId", target = "channelOrderId"),
        @Mapping(source = "orderBuyerPayableMoney", target = "actualPayAmt"),
        @Mapping(source = "orderTotalMoney", target = "originalAmt"),
        @Mapping(constant = "0", target = "bizReceiveAmt"),
        @Mapping(source = "orderReceivableFreight", target = "freight"),
        @Mapping(expression = "java(ConverterUtils.dateToMills(channelOrderDetail.getOrderStartTime()))", target = "createTime"),
        @Mapping(expression = "java(ConverterUtils.dateToMills(channelOrderDetail.getOrderPurchaseTime()))", target = "payTime"),
        @Mapping(source = "orderInvoiceOpenMark", target = "isNeedInvoice"),
        @Mapping(source = "orderNum", target = "orderSerialNumber"),
        @Mapping(source = "packagingMoney", target = "packageAmt"),
        @Mapping(expression = "java(channelOrderDetail.getOrderPayType() == 1 ? 1 : 2)", target = "payType"),
        @Mapping(expression = "java(JddjOrderConvertUtil.isYuYueDelivery(channelOrderDetail))", target = "isBooking"),
        @Mapping(expression = "java(JddjOrderConvertUtil.isOpeningDelivery(channelOrderDetail))", target = "isOpeningDelivery"),
        @Mapping(source = "orderBuyerRemark", target = "comment"),
        @Mapping(source = "deliveryStationName", target = "channelStoreName"),
        @Mapping(source = "venderVipCardId", target = "memberCardCode"),
        @Mapping(expression = "java(ConverterUtils.dateToMills(channelOrderDetail.getOrderCancelTime()))", target = "cancelTime"),
        @Mapping(expression = "java(ConverterUtils.dateToMills(channelOrderDetail.getDeliveryConfirmTime()))", target = "completedTime"),
        @Mapping(expression = "java(ConverterUtils.dateToMills(channelOrderDetail.getDeliveryConfirmTime_begin()))", target = "logisticFetchTime"),
        @Mapping(expression = "java(ConverterUtils.dateToMills(channelOrderDetail.getDeliveryConfirmTime_end()))", target = "logisticCompletedTime"),
        @Mapping(target = "dispatcherName", constant = ""),//京东订单详情暂时不传骑手姓名
        @Mapping(target = "dispatcherPhone", constant = ""),//京东订单详情暂时不传骑手电话
        @Mapping(source = "platformPointsDeductionMoney", target = "scoreDeduction"),//积分抵扣
        @Mapping(source = "packagingMoney", target = "platPackageAmt"),//平台包装费
            @Mapping(source = "orderDiscountMoney", target = "totalOrderPromotion"),//订单级别优惠商品金额
        @Mapping(source = "packagingMoney", target = "payPackageAmt"),//用户支付包装费
        @Mapping(source = "userTip", target = "customerLogisticsTips"),//用户给配送员加小费
        @Mapping(source = "tips", target = "poiLogisticsTips"),//商家给配送员加小费
        @Mapping(source = "buyerPin", target = "buyerAccount") // 买家账号
    })
    ChannelOrderDetailDTO channelOrderDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
            @Mapping(source = "skuIdIsv", target = "skuId"),
            @Mapping(source = "skuName", target = "skuName"),
            @Mapping(source = "skuId", target = "channelSkuId"),
            @Mapping(source = "skuCount", target = "quantity"),
            @Mapping(expression = "java(ConverterUtils.kilogramToGram(orderSkuDetail.getSkuWeight()))", target = "unitCount"),
            @Mapping(source = "skuJdPrice", target = "salePrice"),
            @Mapping(source = "skuStorePrice", target = "originalPrice"),
            @Mapping(source = "skuCostumeProperty", target = "specification"),
            @Mapping(expression = "java(JddjOrderConvertUtil.convertSkuProperty(orderSkuDetail.getSkuCostumeProperty()))", target = "skuProperty"),
            @Mapping(constant = "0", target = "packageCount"),
            @Mapping(constant = "0", target = "packagePrice"),
            @Mapping(source = "canteenMoney", target = "packageFee"),
            @Mapping(source = "upcCode", target = "upcCode"),
            @Mapping(expression = "java(ConverterUtils.kilogramToGram(orderSkuDetail.getSkuWeight()))", target = "weight"),
            @Mapping(expression = "java(JddjOrderConvertUtil.extractItemType(orderSkuDetail))", target = "itemType"),
            @Mapping(source = "platformIntegralDeductMoney", target = "platformIntegralDeductMoney"),
            @Mapping(source = "supplyName", target = "channelItemId")
    })
    OrderProductDetailDTO orderSkuDetailMapping(OrderSkuDetail orderSkuDetail);
    List<OrderProductDetailDTO> orderSkuDetailListMapping(List<OrderSkuDetail> orderSkuDetailList);

    @Mappings({
        @Mapping(source = "discountCode", target = "activityId"),
        @Mapping(source = "discountPrice", target = "actDiscount"),
        @Mapping(source = "platPayMoney", target = "channelCharge"),
        @Mapping(source = "venderPayMoney", target = "bizCharge")
    })
    OrderDiscountDetailDTO orderActivitieInfoMapping(OrderActivitiesInfo orderActivitiesInfo);
    List<OrderDiscountDetailDTO> orderActivitieInfoListMapping(List<OrderActivitiesInfo> orderActivitiesInfoList);

    @Mappings({
            @Mapping(source = "buyerFullAddress", target = "userAddress"),
            @Mapping(source = "buyerMobile", target = "userPhone"),
            @Mapping(source = "buyerFullName", target = "userName"),
            @Mapping(expression = "java(ConverterUtils.dateToMills(channelOrderDetail.getOrderPreStartDeliveryTime()))", target = "arrivalTime"),
            @Mapping(expression = "java(ConverterUtils.dateToMills(channelOrderDetail.getOrderPreEndDeliveryTime()))", target = "arrivalEndTime"),
            @Mapping(expression = "java(ConverterUtils.dateToMills(channelOrderDetail.getPickDeadline()))", target = "pickUpEndTime"),
            @Mapping(source = "deliveryCarrierName", target = "deliveryMethod"),
            @Mapping(expression = "java(!ConverterUtils.isTimePassed(channelOrderDetail.getMiddleNumBindingTime()))", target = "userPhoneIsValid"),
            @Mapping(source = "buyerLng", target = "longitude"),
            @Mapping(source = "buyerLat", target = "latitude"),
            @Mapping(expression = "java(JddjOrderConvertUtil.isSelfDelivery(channelOrderDetail.getDeliveryCarrierNo()))", target = "isSelfDelivery"),
            @Mapping(expression = "java(JddjOrderConvertUtil.userPrivacyPhone(channelOrderDetail.getLastFourDigitsOfBuyerMobile()))", target = "userPrivacyPhone"),
            @Mapping(expression = "java(JddjOrderConvertUtil.convertDeliveryType(channelOrderDetail.getDeliveryCarrierNo()))", target = "originalDeliveryType")
    })
    OrderDeliveryDetailDTO deliveryDetailMapping(ChannelOrderDetail channelOrderDetail);

    @Mappings({
        @Mapping(source = "invoiceFormType", target = "invoiceType"),
        @Mapping(source = "invoiceTitle", target = "invoiceTitle"),
        @Mapping(source = "invoiceDutyNo", target = "taxNo"),
        @Mapping(source = "invoiceMoney", target = "invoiceMoney")
    })
    OrderInvoiceDetailDTO invoiceDetailMapping(OrderInvoiceDetail orderInvoiceDetail);

    @Mappings({
            @Mapping(target = "orderId", source = "orderId"),
            @Mapping(target = "isAgreed", constant = "true"),
            @Mapping(target = "operator", constant = "system"),
            @Mapping(target = "remark", source = "reason"),
    })
    ChannelOrderCancelRelDTO agreeRefund(AgreeRefundRequest request);

    @Mappings({
            @Mapping(target = "orderId", source = "orderId"),
            @Mapping(target = "isAgreed", constant = "false"),
            @Mapping(target = "operator", constant = "system"),
            @Mapping(target = "remark", source = "reason"),
    })
    ChannelOrderCancelRelDTO rejectRefund(RejectRefundRequest request);

    @Mappings({
        @Mapping(target = "orderId", source = "orderId"),
        @Mapping(target = "operator", source = "operator")
    })
    PreparationMealCompleteDTO mealCompleteMapping(PreparationMealCompleteRequest request);

    default ChannelSkuSellStatusDTO updateSkuSellStatus(List<SkuSellStatusInfoDTO> datas) {
        ChannelSkuSellStatusDTO sellStatusDTO = new ChannelSkuSellStatusDTO();
        sellStatusDTO.setUserPin("system");
        sellStatusDTO.setOutStationNo("{}");
        sellStatusDTO.setStockVendibilityList(StockVendibility.toStockVendibility(datas));
        return sellStatusDTO;
    }
    default ChannelSkuSellStatusByChannelSkuIdDTO updateSkuSellStatus(String channelStoreId,
                                                                      List<SpuInfoSellStatusByChannelSkuIdDTO> paramList){
        ChannelSkuSellStatusByChannelSkuIdDTO channelSkuSellStatusByChannelSkuIdDTO = new ChannelSkuSellStatusByChannelSkuIdDTO();
        List<ChannelSkuSellStatusChannelSkuIdDTO> listBaseStockCenterRequest = Fun.map(paramList, skuDto -> {
            ChannelSkuSellStatusChannelSkuIdDTO channelSkuSellStatusChannelSkuIdDTO = new ChannelSkuSellStatusChannelSkuIdDTO();
            channelSkuSellStatusChannelSkuIdDTO.setStationNo(channelStoreId);
            channelSkuSellStatusChannelSkuIdDTO.setSkuId(skuDto.getChannelSkuId());
            channelSkuSellStatusChannelSkuIdDTO.setDoSale(skuDto.getSaleStatus());
            return channelSkuSellStatusChannelSkuIdDTO;
        });
        channelSkuSellStatusByChannelSkuIdDTO.setListBaseStockCenterRequest(listBaseStockCenterRequest);

        return channelSkuSellStatusByChannelSkuIdDTO;
    }

    @Mappings({
            @Mapping(target = "skuId", source = "skuId"),
            @Mapping(target = "outSkuId", source = "customSkuId"),
    })
    UpdateCustomSkuId updateCustomSkuIdMapping(UpdateCustomSkuIdDTO param);

    @Mappings({
        @Mapping(target = "outInfoId", expression = "java(String.valueOf(channelActivityInfo.getActivityId()))"),
        @Mapping(target = "promotionName", source = "activityName"),
        @Mapping(target = "advertising", source = "advertising"),
        @Mapping(target = "beginDate", expression = "java(ConverterUtils.secondToString(channelActivityInfo.getStartTime()))"),
        @Mapping(target = "endDate", expression = "java(ConverterUtils.secondToString(channelActivityInfo.getEndTime()))"),
        @Mapping(target = "promotionType", expression = "java(JddjActivityConvertUtil.getPromotionType(channelActivityInfo.getActivityType()))"),
        @Mapping(target = "traceId", constant = "oacc-112233"),
        @Mapping(target = "timeStamp", expression = "java(ConverterUtils.millisToString(0))")
    })
    ChannelActivityMainInfo channelActivityMainInfo(ChannelActivityInfo channelActivityInfo);

    @Mappings({
        @Mapping(target = "outInfoId", expression = "java(String.valueOf(channelActivityInfo.getActivityId()))"),
        @Mapping(target = "infoId", expression = "java(channelActivityInfo.getChannelActivityId())"),
        @Mapping(target = "limitDevice", expression = "java(ConverterUtils.boolToInt(channelActivityInfo.isLimitDevice()))"),
        @Mapping(target = "limitPin", expression = "java(ConverterUtils.boolToInt(channelActivityInfo.isLimitPin()))"),
        @Mapping(target = "limitDaily", expression = "java(ConverterUtils.boolToInt(channelActivityInfo.isLimitDaily()))"),
        @Mapping(target = "limitCount", source = "limitCount"),
        @Mapping(target = "traceId", constant = "oacc-112233"),
        @Mapping(target = "timeStamp", expression = "java(ConverterUtils.secondToString(0))")
    })
    ChannelActivityRuleInfo channelActivityRuleInfo(ChannelActivityInfo channelActivityInfo);

    @Mappings({
        @Mapping(target = "outSkuId", source = "skuId"),
        @Mapping(target = "outStationNo", source = "storeId"),
        @Mapping(target = "promotionPrice", source = "promotionPrice"),
        @Mapping(target = "limitSkuCount", source = "limitSkuCount")
    })
    ChannelActivitySkuInfo channelActivitySkuInfo(ChannelActivityInfo channelActivityInfo);
    List<ChannelActivitySkuInfo> channelActivitySkuInfoList(List<ChannelActivityInfo> channelActivityInfoList);

    @Mappings({
        @Mapping(target = "outInfoId", expression = "java(String.valueOf(channelActivityInfo.getActivityId()))"),
        @Mapping(target = "infoId", expression = "java(channelActivityInfo.getChannelActivityId())"),
        @Mapping(target = "traceId", constant = "oacc-112233"),
        @Mapping(target = "timeStamp", expression = "java(ConverterUtils.secondToString(0))")
    })
    ChannelActivityItemInfo channelActivityItemInfo(ChannelActivityInfo channelActivityInfo);

    @Mappings({
        @Mapping(target = "outSkuId", source = "skuId"),
        @Mapping(target = "outStationNo", source = "storeId")
    })
    ChannelActivityCancelSkuInfo channelActivityCancelSkuInfo(ChannelActivityInfo channelActivityInfo);
    List<ChannelActivityCancelSkuInfo> channelActivityCancelSkuInfoList(List<ChannelActivityInfo> channelActivityInfoList);

    @Mappings({
        @Mapping(target = "outInfoId", expression = "java(String.valueOf(channelActivityInfo.getActivityId()))"),
        @Mapping(target = "infoId", expression = "java(channelActivityInfo.getChannelActivityId())"),
        @Mapping(target = "traceId", constant = "oacc-112233"),
        @Mapping(target = "timeStamp", expression = "java(ConverterUtils.secondToString(0))")
    })
    ChannelActivityCancelItemInfo channelActivityCancelItemInfo(ChannelActivityInfo channelActivityInfo);

    @Mappings({
        @Mapping(target = "infoId", expression = "java(channelActivityInfo.getChannelActivityId())"),
        @Mapping(target = "endDate", expression = "java(ConverterUtils.secondToString(channelActivityInfo.getEndTime()))"),
        @Mapping(target = "traceId", constant = "oacc-112233"),
        @Mapping(target = "timeStamp", expression = "java(ConverterUtils.secondToString(0))")
    })
    ActivityTimeChangeInfo activityTimeChangeInfo(ChannelActivityInfo channelActivityInfo);

    @Mappings({
        @Mapping(target = "outSkuId", source = "skuId"),
        @Mapping(target = "outStationNo", source = "storeId"),
        @Mapping(target = "limitSkuCount", source = "limitSkuCount")
    })
    AcitvitySkuCountChangeInfo activitySkuCountChangeInfo(ChannelActivityInfo channelActivityInfo);
    List<AcitvitySkuCountChangeInfo> activitySkuCountChangeInfoList(List<ChannelActivityInfo> channelActivityInfoList);

    @Mappings({
        @Mapping(target = "outInfoId", expression = "java(String.valueOf(channelActivityInfo.getActivityId()))"),
        @Mapping(target = "infoId", expression = "java(channelActivityInfo.getChannelActivityId())"),
        @Mapping(target = "traceId", constant = "oacc-112233"),
        @Mapping(target = "timeStamp", expression = "java(ConverterUtils.secondToString(0))")
    })
    AcitvityCountChangeInfo activityCountChangeInfo(ChannelActivityInfo channelActivityInfo);

    @Mappings({
            @Mapping(target = "isAgreed", source = "isAgreed", defaultValue = "true"),
    })
    PoiConfirmOrderDTO poiConfirmOrderMapping(PoiConfirmOrderRequest request);


    @Mappings({
            @Mapping(target = "operateTime", expression = "java(ConverterUtils.secondToString(request.getOperateTime()))")
    })
    PoiReceiveRefundGoodsDTO poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request);


    @Mappings({
            @Mapping(target = "selfPickCode", source = "code"),
            @Mapping(target = "orderId", source = "orderId"),
            @Mapping(target = "operPin", source = "operator"),
    })
    JDverifySelfFetchCodeParam verifySelfFetchCodeMapping(VerifySelfFetchCodeRequest data);


    @Mappings({
            @Mapping(target = "operPin", source = "optUser"),
            @Mapping(target = "remark", source = "reason"),
            @Mapping(target = "oaosAdjustDTOList", expression = "java(JddjOrderConvertUtil.adjustDtos(request.getModifyProducts()))")
    })
    PoiAdjustOrderDTO poiAdjustOrder(PoiAdjustOrderRequest request);


    @Mappings({
            @Mapping(target = "skuId", source = "outSkuId"),
            @Mapping(target = "salePrice", expression = "java(Integer.parseInt(jdAfterAdjustProductDTO.getSkuJdPrice()))")
    })
    OrderProductInfoAfterAdjustDTO afterAdjustProductInfo(JdAfterAdjustProductDTO jdAfterAdjustProductDTO);

    @Mappings({
            @Mapping(target = "products", source = "oaList"),
            @Mapping(target = "orderTotalMoney", expression = "java(coreData.getOrderTotalMoney())"),
            @Mapping(target = "orderDiscountMoney", expression = "java(coreData.getOrderDiscountMoney())"),
            @Mapping(target = "orderFreightMoney", expression = "java(coreData.getOrderFreightMoney())"),
            @Mapping(target = "orderBuyerPayableMoney", expression = "java(coreData.getOrderBuyerPayableMoney())")
    })
    AfterAdjustOrderInfo afterAdjustOrderInfo(JdAfterAdjustOrderInfoDTO coreData);

    @Mappings({
            @Mapping(source = "orderId", target = "orderId")
    })
    JDOrderShouldSettlementDetailParam shouldSettlementMapping(OrderShouldSettlementInfoRequest request);

    @Mappings({
            @Mapping(source = "orderId", target = "orderId")
    })
    JDGoodsSettlementDetailParam goodsSettlementMapping(GoodsSettlementInfoRequest request);

    @Mappings({
            @Mapping(target = "customSkuId", expression = "java(customSkuMap.get(jdGoodsSettlementInfo.getSkuId()))"),//设置customSkuId
            @Mapping(target = "totalDiscount", expression = "java(JddjConvertUtil.calcTotalDiscount(jdGoodsSettlementInfo))"),
            @Mapping(target = "originPrice", expression = "java(jdGoodsSettlementInfo.getPdjPrice())"),//原价
            @Mapping(target = "activityPrice", expression = "java(jdGoodsSettlementInfo.getPromotionPrice())"),//京东的销售价
            @Mapping(target = "channelCost", expression = "java(JddjConvertUtil.calcTotalChannelCost(jdGoodsSettlementInfo) + jdGoodsSettlementInfo.getCostMoney())"),//订单级别平台承担金额 + 商品纬度平台承担金额
            @Mapping(target = "tenantCost", expression = "java(JddjConvertUtil.calcTotalTenantCost(jdGoodsSettlementInfo) + jdGoodsSettlementInfo.getSaleMoney())"),//订单级别商家承担金额 + 商品纬度平台承担金额
            @Mapping(target = "agentCost", constant = "0"),
            @Mapping(target = "logisticsCost", constant = "0"),
            @Mapping(target = "hasPromotion", expression = "java(!Integer.valueOf(1).equals(jdGoodsSettlementInfo.getPromotionType()))"),
            @Mapping(target = "channelJiFenCost", expression = "java(jdGoodsSettlementInfo.getPlatformIntegralDeductMoney())"),
            @Mapping(target = "channelPromotionType", expression = "java(String.valueOf(jdGoodsSettlementInfo.getPromotionType()))"),
            @Mapping(target = "goodActivityDetail", expression = "java(JddjConvertUtil.convertGoodsSharedActivityItems(jdGoodsSettlementInfo.getDiscountlist(), jdGoodsSettlementInfo))"),//订单纬度分摊信息
            @Mapping(target = "channelItemId", expression = "java(jdGoodsSettlementInfo.getSkuUuid())")//商品行标示
    })
    GoodsActivityDetailDTO convertGoodsActivityDetail(JDGoodsSettlementInfo jdGoodsSettlementInfo, Map<Long, String> customSkuMap);

    @Mappings({
            @Mapping(target = "skuName", source = "wareName"),
            @Mapping(target = "sku", source = "skuIdIsv"),
            @Mapping(target = "upc", source = "upcCode"),
            @Mapping(target = "count", source = "skuCount"),
            @Mapping(expression = "java(JddjConvertUtil.extractPartialRefundCount(detail))", target = "partialRefundCount"),
            @Mapping(target = "skuRefundAmount", source = "cashMoney"),
            @Mapping(target = "foodPrice", expression = "java(detail.getPayPrice())"),
            @Mapping(target = "boxPrice", expression = "java(detail.getMealBoxMoney() / detail.getSkuCount())"),  //mealBoxMoney : 该商品餐盒费金额 * 申请数量
            @Mapping(target = "boxNum", constant = "0"),
            @Mapping(target = "customSpuId", source = "skuIdIsv")
    })
    RefundSku partRefundSkuInfo(JddjAfsServiceDetail detail);
    List<RefundSku> partRefundSkuInfoList(List<JddjAfsServiceDetail> afsDetailList);

    @Mappings({
            @Mapping(source = "orderId", target = "serviceOrder"),
            @Mapping(constant = "1", target = "approveType"),//审核结果类型（1：退款 2：退货 3：驳回）
            @Mapping(source = "reason", target = "rejectReason"),
            @Mapping(constant = "system", target = "optPin")
    })
    JddjApproveAfterSaleDTO agreeAfterSaleRefund(AgreeRefundRequest request);

    @Mappings({
            @Mapping(source = "orderId", target = "serviceOrder"),
            @Mapping(constant = "3", target = "approveType"),//审核结果类型（1：退款 2：退货 3：驳回）
            @Mapping(source = "reason", target = "rejectReason"),
            @Mapping(constant = "system", target = "optPin")
    })
    JddjApproveAfterSaleDTO rejectAfterSaleRefund(RejectRefundRequest request);

    @Mappings({
            @Mapping(source = "afterSaleId", target = "serviceOrder"),
            @Mapping(constant = "2", target = "approveType"),//审核结果类型（1：退款 2：退货 3：驳回）
            @Mapping(source = "reason", target = "rejectReason"),
            @Mapping(constant = "system", target = "optPin")
    })
    JddjApproveAfterSaleDTO agreeAfterSaleRefund(RefundGoodsRequest request);

    @Mappings({
            @Mapping(source = "afterSaleId", target = "serviceOrder"),
            @Mapping(constant = "3", target = "approveType"),//审核结果类型（1：退款 2：退货 3：驳回）
            @Mapping(source = "reason", target = "rejectReason"),
            @Mapping(constant = "system", target = "optPin")
    })
    JddjApproveAfterSaleDTO rejectAfterSaleRefund(RefundGoodsRequest request);

    @Mappings({
            @Mapping(source = "afterSaleId", target = "afsServiceOrder"),
            @Mapping(constant = "tenant", target = "pin")
    })
    JddjReceiveAfterSaleGoodsDTO receiveAfterSaleRefundGoods(RefundGoodsRequest request);

    @Mappings({
            @Mapping(expression = "java(request.getOrderId())", target = "orderId"),
            @Mapping(expression = "java(request.getOperatorId())", target = "pin"),
            @Mapping(expression = "java(String.valueOf(request.getReasonCode()))", target = "questionTypeCode"),
            @Mapping(expression = "java(request.getReason())", target = "questionDesc"),
            @Mapping(constant = "", target = "questionPic"),
            @Mapping(constant = "", target = "customerName"),
            @Mapping(constant = "", target = "customerMobilePhone"),
            @Mapping(constant = "", target = "address"),
            @Mapping(expression = "java(JddjOrderConvertUtil.convertPartRefundSku(request, channelOrderDetail))", target = "skuList")
    })
    PoiCommitAfterSaleDTO poiRefundMapping(PoiPartRefundRequest request, ChannelOrderDetail channelOrderDetail);

    @Mappings({
            @Mapping(expression = "java(request.getOrderId())", target = "orderId"),
            @Mapping(constant = "system", target = "pin"),
            // todo需要产品指定退款原因
            @Mapping(constant = "207", target = "questionTypeCode"),
            @Mapping(constant = "", target = "questionDesc"),
            @Mapping(constant = "", target = "questionPic"),
            @Mapping(constant = "", target = "customerName"),
            @Mapping(constant = "", target = "customerMobilePhone"),
            @Mapping(constant = "", target = "address"),
            @Mapping(expression = "java(JddjOrderConvertUtil.convertMoneyRefundSku(request, channelOrderDetail))", target = "skuList")
    })
    PoiCommitAfterSaleDTO moneyRefundMapping(MoneyRefundRequest request, ChannelOrderDetail channelOrderDetail);

    @Mappings({
            @Mapping(source = "reason_code", target = "reasonCode")
    })
    PoiPartRefundRequest convertCancel2PartRefund(PoiCancelOrderRequest request);


    @Mappings({
            @Mapping(source = "pageNo", target = "pageNum"),
            @Mapping(expression = "java(DateUtils.formatDateMouthYear(request.getAccountTimeStart()))", target = "accountTimeStart"),
            @Mapping(expression = "java(DateUtils.formatDateMouthYear(request.getAccountTimeEnd()))", target = "accountTimeEnd"),
            @Mapping(expression = "java(JddjConvertUtil.convertChannelSettleStatus(request.getSettlementStatus()))", target = "settleStatus")

    })
    JDSettlementListParam convertChannelSettlementPageRequset(ChannelSettlementPageRequest request);

    @Mappings({
            @Mapping(source = "pageNum", target = "pageNum"),
            @Mapping(source = "pageSize", target = "pageSize"),
            @Mapping(source = "pages", target = "totalPage"),
            @Mapping(source = "total", target = "totalNum")
    })
    com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo convertPageInfo(JddjSettleOrderListContent.JddjSettleOrderPage settleOrderList);

    @Mappings({
            @Mapping(constant = "300", target = "channelId"),
            @Mapping(source = "settleOrderId", target = "settleOrderId"),
            @Mapping(source = "settleStatusCode", target = "settleStatus"),
            @Mapping(source = "settleStatus", target = "settleStatusDesc"),
            @Mapping(expression = "java(DateUtils.seconds2MS(jddjSettleOrder.getFinishTime()))", target = "settleFinishDate"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(jddjSettleOrder.getSettleMoney()))", target = "settleAmount"),
            @Mapping(source = "memo", target = "remark"),
            @Mapping(expression = "java(DateUtils.seconds2MS(jddjSettleOrder.getAccountTimeStart()))", target = "accountTimeStart"),
            @Mapping(expression = "java(DateUtils.seconds2MS(jddjSettleOrder.getAccountTimeEnd()))", target = "accountTimeEnd"),
            @Mapping(source = "payMethodCode", target = "payMethod"),
            @Mapping(source = "payMethod", target = "payMethodDesc"),
            @Mapping(expression = "java(jddjSettleOrder.getAccountContent().getAccountNo())", target = "accountNo"),
            @Mapping(expression = "java(convertSettlementAccountInfo(jddjSettleOrder.getAccountContent()))", target = "accountInfo"),
            @Mapping(source = "outFlowId", target = "outFlowId"),

    })
    ChannelSettlementDTO convertChannelSettlement(JddjSettleOrderListContent.JddjSettleOrder jddjSettleOrder);

    @Mappings({
            @Mapping(source = "bankSubBranch", target = "bankSubName")
    })
    SettlementAccountInfo convertSettlementAccountInfo(JddjSettleOrderListContent.JddjSettleOrderAccountContent accountContent);

    @Mappings({
            @Mapping(expression = "java(convertPageInfo(settleOrderList))", target = "pageInfo"),
            @Mapping(expression = "java(convertChannelSettlementList(settleOrderList.getResult()))", target = "channelSettlementDtoList")
    })
    ChannelSettlementPageDTO convertChannelSettlementPage(JddjSettleOrderListContent.JddjSettleOrderPage settleOrderList);


    @Mappings({
            @Mapping(source = "pageNo", target = "pageNum"),
            @Mapping(source = "settlementId", target = "settleOrderId")
    })
    JDSettlementOrderDetailParam convertChannelSettlementDetailByIdRequest(ChannelOrderSettlementByIdRequest request);

    default List<ChannelSettlementDTO> convertChannelSettlementList(List<JddjSettleOrderListContent.JddjSettleOrder> jddjSettleOrders) {
        if (CollectionUtils.isEmpty(jddjSettleOrders)) {
            return Collections.emptyList();
        }
        List<ChannelSettlementDTO> list = new ArrayList<>();
        for (JddjSettleOrderListContent.JddjSettleOrder jddjSettleOrder : jddjSettleOrders) {
            if (JddjConvertUtil.filterSettleStatus(jddjSettleOrder.getSettleStatusCode())){
                continue;
            }
            list.add(convertChannelSettlement(jddjSettleOrder));
        }
        return list;
    }

    @Mappings({
            @Mapping(source = "pageNum", target = "pageNum"),
            @Mapping(source = "pageSize", target = "pageSize"),
            @Mapping(source = "pages", target = "totalPage"),
            @Mapping(source = "total", target = "totalNum")
    })
    com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo convertPageInfo(JddjSettleOrderDetailContent.SettleOrderPage settleDetails);

    @Mappings({
            @Mapping(target = "channelId", constant = "300"),
            @Mapping(target = "channelSettleOrderId", source = "settleOrderId"),
            @Mapping(target = "orderChargeType", source = "orderTypeCode"),
            @Mapping(target = "orderChargeTypeDesc", source = "orderType"),
            @Mapping(target = "settleMilliAmt", expression = "java(BigDecimal.valueOf(MoneyUtils.yuanToFen(jddjSettleDetailDaoJiaDailyDTO.getWaitSettleAmount())).multiply(BigDecimal.valueOf(100)).longValue())"),
            @Mapping(target = "orderTime", source = "businessStartTime"),
            @Mapping(target = "settlementFinishDate", expression = "java(JddjConvertUtil.convertSettlementFinishDate(jddjSettleDetailDaoJiaDailyDTO.getSettleFinishTime(),jddjSettleDetailDaoJiaDailyDTO.getCreateTime()))"),
            @Mapping(target = "settlementFeeDetailList", expression = "java(JddjConvertUtil.convertSettlementFeeDetailList" + "" + "(jddjSettleDetailDaoJiaDailyDTO))"),
            @Mapping(target = "channelOrderId", expression = "java(JddjConvertUtil.convertToOrderId(jddjSettleDetailDaoJiaDailyDTO))"),
            @Mapping(target = "rawSettleStatus", source = "status"),
            @Mapping(target = "rawSettleStatusDesc", constant = "已完成"),
            @Mapping(target = "settlementDate", source = "createTime"),
            @Mapping(target = "channelShopId", source = "stationId"),
            @Mapping(target = "refundId", expression = "java(JddjConvertUtil.convertToRefundId(jddjSettleDetailDaoJiaDailyDTO))"),
    })
    ChannelOrderSettlementDTO convertChannelSettlementDetail(JddjSettleOrderDetailContent.JddjSettleDetailDaoJiaDailyDTO jddjSettleDetailDaoJiaDailyDTO);

    List<ChannelOrderSettlementDTO> convertChannelSettlementDetails(List<JddjSettleOrderDetailContent.JddjSettleDetailDaoJiaDailyDTO> jddjSettleDetailDaoJiaDailyDTOS);



    @Mappings({
            @Mapping(expression = "java(convertPageInfo(settleDetails))", target = "pageInfo"),
            @Mapping(expression = "java(convertChannelSettlementDetails(settleDetails.getResult()))", target = "channelOrderSettlementDtoList")
    })
    ChannelOrderSettlementPageDTO convertChannelOrderSettlementPage(JddjSettleOrderDetailContent.SettleOrderPage settleDetails);

    @Mappings({
            @Mapping(source = "skuIdIsv", target = "skuId"),
            @Mapping(source = "wareName", target = "skuName"),
            @Mapping(source = "skuCount", target = "count"),
            @Mapping(expression = "java(JddjConvertUtil.extractPartialRefundCount(afsDetail))", target = "partialRefundCount"),
            @Mapping(source = "cashMoney", target = "skuRefundAmount"),
            @Mapping(source = "skuSpecification", target = "spec"),
            @Mapping(source = "platPayMoney", target = "refundPlatItemPromotion"),
            @Mapping(source = "venderPayMoney", target = "refundPoiItemPromotion"),
            @Mapping(source = "mealBoxMoney", target = "boxAmt"),
            @Mapping(expression = "java(afsDetail.getPayPrice())", target = "foodPrice"),
            @Mapping(expression = "java(JddjConvertUtil.extractAfsPlatOrderPromotion(afsDetail.getAfsSkuDiscountList()))", target = "refundPlatOrderPromotion"),
            @Mapping(source = "partialRefundRatioShow", target = "partialRefundRatioShow"),
            @Mapping(expression = "java(JddjConvertUtil.extractAfsPoiOrderPromotion(afsDetail.getAfsSkuDiscountList()))", target = "refundPoiOrderPromotion")

    })
    RefundProductDTO convertRefundProductDTO(JddjAfsServiceDetail afsDetail);


    List<RefundProductDTO> convertRefundProductDTOs(List<JddjAfsServiceDetail> afsDetails);

    @Mappings({
            @Mapping(constant = "300", target = "channelType"),
            @Mapping(source = "orderId", target = "channelOrderId"),
            @Mapping(source = "afsServiceOrder", target = "afterSaleId"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.jddjAfterSaleStatusMapping(afterSaleDetail.getAfsServiceState().toString(),afterSaleDetail.getApplyDeal(),afterSaleDetail.getExtMap()))", target = "afterSaleStatus"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.jddjAfsRecordMapping(afterSaleDetail.getAfsServiceState().toString()))", target = "afterSaleRecordStatus"),
            @Mapping(expression = "java(convertRefundProductDTOs(afterSaleDetail.getAfsDetailList()))", target = "afsProductList"),
            @Mapping(source = "createTime", target = "refundApplyTime"),
            @Mapping(source = "approvedDate", target = "refundAuditTime"),
            @Mapping(constant = "0", target = "commission"),
            @Mapping(constant = "1", target = "commissionType"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.parseLong2Int(afterSaleDetail.getPlatformIntegralDeductMoney()))", target = "scoreDeduction"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.jddjAfsRefundType(afterSaleDetail.getIsLastApply()))", target = "refundType"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.parseLong2Int(afterSaleDetail.getCashMoney()))", target = "itemRefundAmt"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.parseLong2Int(afterSaleDetail.getSelfPickPayMoney()))", target = "selfPickServiceFee"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.parseLong2Int(afterSaleDetail.getPackagingMoney()))", target = "originalPackageFee"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.parseLong2Int(afterSaleDetail.getVirtualMoney()))", target = "refundPromotion"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.parseLong2Int(afterSaleDetail.getOrderFreightMoney()))", target = "freight"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.jddjAfsFreightMapping(afterSaleDetail))", target = "afsFreight"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.jddjAfsRefundPriceMapping(afterSaleDetail))", target = "refundPrice"),
            @Mapping(expression = "java(JddjConvertUtil.extractAfsPlatLogisticsPromotion(afterSaleDetail))", target = "platLogisticsPromotion"),
            @Mapping(expression = "java(JddjConvertUtil.extractAfsPoiLogisticsPromotion(afterSaleDetail))", target = "poiLogisticsPromotion"),
            @Mapping(expression = "java(JddjConvertUtil.parseExtend(afterSaleDetail))", target = "extend"),
            @Mapping(expression = "java(JddjConvertUtil.parseChannelExtRefundType(afterSaleDetail))", target = "channelExtRefundType"),
            @Mapping(expression = "java(ChannelStatusConvertUtil.jddjIsAppeal(afterSaleDetail.getAfsServiceState().toString(),afterSaleDetail.getDutyAssume(),afterSaleDetail.getApproveType()))", target = "isAppeal")

    })
    OrderAfsApplyDTO convertOrderAfsApplyDTO(JddjAfterSaleDetail afterSaleDetail);

    @Mappings({
        @Mapping(target = "skuId", source = "channelSkuId"),
        @Mapping(target = "customSkuId", source = "customSkuId"),
    })
    UpdateCustomSkuIdDTO convert2UpdateCustomSkuIdDTO(ChangeCustomSkuIdDTO param);

    @Mappings({
            @Mapping(target = "orderId", source = "orderId"),
            @Mapping(target = "updatePin", source = "operator")
    })
    SelfDeliveryDTO selfDeliveryMapping(SelfDeliveryRequest request);


    /**
     * 京东分页查询对账单接口的 thrift 入参 转换为 接口入惨
     * @param request
     * @return
     */
    @Mappings({
            @Mapping(source = "shopIds", target = "shopIds"),
            @Mapping(source = "orderIds", target = "orderIds")
    })
    JdBalanceBillParam convertBalanceBillByBalanceBillRequest(ChannelBalanceBillPageRequest request);

    /**
     * 京东分页查询对账单接口 的 账单列表转换为 thrift
     * @param balanceBillDTOList 京东分页查询对账单接口 获取的账单列表
     * @return
     */
    List<BalanceBillDTO> convertBalanceBillDTOs(List<BalanceBillListResponse.BalanceBillDTO> balanceBillDTOList);

    @Mappings({
            @Mapping(source = "orderId", target = "orderId")
    })
    BalanceBillDTO convertBalanceBillDTO(BalanceBillListResponse.BalanceBillDTO balanceBillDTO);
    /**
     * 京东 查询订单计费明细接口的 thrift 入参 转换为 接口入惨
     * @param request
     * @return
     */
    @Mappings({
            @Mapping(source = "orderIds", target = "orderIds")
    })
    JdCheckBillParam convertCheckBillByCheckBillRequest(ChannelCheckBillPageRequest request);

    /**
     * 京东查询订单计费明细接口 的 明细列表转换为 thrift
     * @param billSearchResultList 京东查询订单计费明细接口 获取的明细列表
     * @return
     */
    List<BillSearchResult> convertCheckBills(List<CheckBillResponse.BillSearchResult> billSearchResultList);

    @Mappings({
            @Mapping(source = "orderId", target = "orderId")
    })
    BillSearchResult convertCheckBill(CheckBillResponse.BillSearchResult billSearchResult);

    /**
     * 京东分页查询对账单接口的 thrift入参 转换为 接口入参
     */
    @Mappings({
            @Mapping(source = "shopIds", target = "shopIds"),
            @Mapping(source = "orderIds", target = "orderIds")
    })
    JdBalanceBillParam convertBalanceBillByJDBalanceBillPageRequest(JDBalanceBillPageRequest request);

    /**
     * 京东分页查询对账单接口的 接口出参 转换为 thrift出参
     */
    @Mappings({
            @Mapping(source = "code", target = "code"),
            @Mapping(source = "msg", target = "msg"),
            @Mapping(expression = "java(convertJDBalanceBillData(jdBalanceBillResult.getData()))", target = "data")
    })
    com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.JDBalanceBillResult convertJDBalanceBillResult(com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult jdBalanceBillResult);

    /**
     * 京东分页查询对账单接口的 接口出参 转换为 thrift出参
     */
    @Mappings({
            @Mapping(source = "code", target = "code"),
            @Mapping(source = "msg", target = "msg"),
            @Mapping(expression = "java(convertJDBalanceBillListContent(jdBalanceBillData.getContent()))", target = "content")
    })
    JDBalanceBillData convertJDBalanceBillData(com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult.JDBalanceBillData jdBalanceBillData);

    /**
     * 京东分页查询对账单接口的 接口出参 转换为 thrift出参
     */
    @Mappings({
            @Mapping(source = "summaryDueAmount", target = "summaryDueAmount"),
            @Mapping(source = "summaryGoodsBill", target = "summaryGoodsBill"),
            @Mapping(source = "currentDueAmount", target = "currentDueAmount"),
            @Mapping(source = "currentGoodsBill", target = "currentGoodsBill"),
            @Mapping(expression = "java(convertJDBalanceBillSettleOrderPage(jdBalanceBillListContent.getBillList()))", target = "billList")
    })
    JDBalanceBillListContent convertJDBalanceBillListContent(com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult.JDBalanceBillListContent jdBalanceBillListContent);

    /**
     * 京东分页查询对账单接口的 接口出参 转换为 thrift出参
     */
    @Mappings({
            @Mapping(source = "pageNum", target = "pageNum"),
            @Mapping(source = "pageSize", target = "pageSize"),
            @Mapping(source = "total", target = "total"),
            @Mapping(source = "startRow", target = "startRow"),
            @Mapping(expression = "java(convertJDBalanceBillDTOs(jdBalanceBillSettleOrderPage.getResult()))", target = "result")
    })
    JDBalanceBillSettleOrderPage convertJDBalanceBillSettleOrderPage(com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult.JDBalanceBillSettleOrderPage jdBalanceBillSettleOrderPage);

    /**
     * 京东分页查询对账单接口的 接口出参 转换为 thrift出参
     */
    List<JDBalanceBillDTO> convertJDBalanceBillDTOs(List<com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult.JDBalanceBillDTO> jdBalanceBillDTO);

    /**
     * 京东分页查询对账单接口的 接口出参 转换为 thrift出参
     */
    @Mappings({
            @Mapping(source = "id", target = "id"),
            @Mapping(source = "orgCode", target = "orgCode"),
            @Mapping(source = "orgName", target = "orgName"),
            @Mapping(source = "stationId", target = "stationId")
    })
    JDBalanceBillDTO convertJDBalanceBillDTO(com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.bill.JDBalanceBillResult.JDBalanceBillDTO jdBalanceBillDTO);
}
