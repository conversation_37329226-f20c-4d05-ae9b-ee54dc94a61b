package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreBaseInfoDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.PoiCodeAppIdDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.ChannelStoreBaseInfoMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.ChannelStoreMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;

/**
 * @description:
 * @author: zhaolei12
 * @create: 2019-02-20 13:48
 */
@Service
public class CopChannelStoreServiceImpl implements CopChannelStoreService {

    @Resource
    private ChannelStoreMapper channelStoreMapper;

    @Resource
    private ChannelStoreBaseInfoMapper channelStoreBaseInfoMapper;

    @Resource
    private CommonLogger log;

    @Override
    public Map<String, ChannelStoreDO> getChannelPoiCode(long tenantId, int channelId, List<Long> storeIdList) {
        Map<String, ChannelStoreDO> channelStoreMap = Maps.newHashMapWithExpectedSize(storeIdList.size());
        try {
            List<ChannelStoreDO> channelStoreDOList = channelStoreMapper
                    .selectChannelPoiCode(tenantId, channelId, storeIdList);
            channelStoreDOList.forEach(channelStoreDO -> {
                if (Objects.isNull(channelStoreDO)) {
                    return;
                }
                String channelStoreKey = KeyUtils
                        .genChannelStoreKey(channelStoreDO.getTenantId(), channelStoreDO.getChannelId(),
                                channelStoreDO.getStoreId());
                channelStoreMap.put(channelStoreKey, channelStoreDO);
            });
        } catch (Exception e) {
            log.error(
                    "CopChannelStoreServiceImpl.getChannelPoiCode, 查询租户渠道信息异常, tenantId:{}, channelId:{}, storeIdList:{}",
                    tenantId, channelId, storeIdList, e);
        } finally {
//            log.info(
//                    "CopChannelStoreServiceImpl.getChannelPoiCode, 查询租户渠道信息返回数据, tenantId:{}, channelId:{}, "
//                            + "storeIdList:{}, channelStoreMap:{}",
//                    tenantId, channelId, storeIdList, channelStoreMap);
        }
        return channelStoreMap;
    }

    @Override
    public ChannelStoreDO getChannelStore(Long tenantId, Integer channelId, Long storeId) {
        Preconditions.checkArgument(tenantId != null, "租户id不能为空");
        Preconditions.checkArgument(channelId != null, "渠道id不能为空");
        Preconditions.checkArgument(storeId != null, "门店id不能为空");
        String channelOnlinePoiCode= MccConfigUtil.getQnhChannelStoreId(tenantId,storeId,channelId);
        if(StringUtils.isNotEmpty(channelOnlinePoiCode)){
            ChannelStoreDO channelStoreDO=new ChannelStoreDO();
            channelStoreDO.setTenantId(tenantId);
            channelStoreDO.setChannelId(channelId);
            channelStoreDO.setStoreId(storeId);
            channelStoreDO.setChannelPoiCode(channelOnlinePoiCode);
            channelStoreDO.setChannelOnlinePoiCode(channelOnlinePoiCode);
            return channelStoreDO;
        }
        List<ChannelStoreDO> channelStoreDOList = channelStoreMapper
                .selectChannelPoiCode(tenantId, channelId, Arrays.asList(storeId));
        return CollectionUtils.isNotEmpty(channelStoreDOList) ? channelStoreDOList.get(0) : null;
    }

    /**
     * 查询渠道门店信息（如果查询结果为空，带默认返回数据）
     * @param tenantId
     * @param channelId
     * @param channelPoiCode
     * @return
     */
    @Override
    public ChannelStoreDO selectChannelStore(long tenantId, int channelId, String channelPoiCode) {
        ChannelStoreDO channelStoreDO = innerSelectChannelStore(tenantId, channelId, channelPoiCode, false);
        if (channelStoreDO == null){
            channelStoreDO = new ChannelStoreDO();
            channelStoreDO.setStoreId(ProjectConstant.UNKNOW_STORE_ID);
        }

        return channelStoreDO;
    }

    @Override
    public Long selectChannelStoreId(long tenantId, int channelId, String channelPoiCode) {
        ChannelStoreDO channelStoreDO = innerSelectChannelStore(tenantId, channelId, channelPoiCode, false);
        return channelStoreDO == null ? ProjectConstant.UNKNOW_STORE_ID : channelStoreDO.getStoreId();
    }

    @Override
    public Long selectChannelStoreIdIgnoreValid(long tenantId, int channelId, String channelPoiCode) {
        ChannelStoreDO channelStoreDO = innerSelectChannelStore(tenantId, channelId, channelPoiCode, true);
        return channelStoreDO == null ? ProjectConstant.UNKNOW_STORE_ID : channelStoreDO.getStoreId();
    }

    @Override
    public <T> Map<String, ChannelStoreDO> getChannelPoiCode(BaseRequestSimple baseInfo, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        try {
            Method method = list.get(0).getClass().getMethod("getStoreId");

            List storeIds = list.stream().map(item -> {
                try {
                    return method.invoke(item);
                } catch (Exception e) {
                    log.info("CopChannelStoreServiceImpl.getChannelPoiCode.invoke, 查询租户渠道信息异常 item:{}", item);
                    throw new IllegalArgumentException("method getStoreId invoke fail");
                }
            }).filter(x -> x != null).collect(Collectors.toList());
            Map<String, ChannelStoreDO> channelStoreDOMap = getChannelPoiCode(baseInfo.getTenantId(), baseInfo.getChannelId(), storeIds);
            return channelStoreDOMap;
        } catch (Exception e) {
            log.info("CopChannelStoreServiceImpl.getChannelPoiCode, 查询租户渠道信息异常 baseInfo:{}, list:{}", baseInfo, list);
            throw new IllegalArgumentException("parse storeId failed");
        }

    }

    @Override
    public List<ChannelStoreBaseInfoDO> selectAllDisableSyncDataStoreBaseInfos(long tenantId, int channelId){
        try {
            return channelStoreBaseInfoMapper.selectAllDisableSyncDataStoreBaseInfos(tenantId, channelId);
        } catch (Exception e) {
            log.error("CopChannelStoreServiceImpl.selectEnableSyncDataStoreBaseInfos, 查询租户渠道所有停用同步数据门店列表异常, tenantId:{}, channelId:{}",
                    tenantId, channelId, e);
        }

        return Lists.newArrayList();
    }

    @Override
    public List<PoiCodeAppIdDO> selectAllByPoiCode(long tenantId, int channelId, List<String> poiCodeList) {
        if (CollectionUtils.isEmpty(poiCodeList)) {
            throw new IllegalArgumentException("poiCodeList cannot be empty!");
        }
        try {
            return channelStoreBaseInfoMapper.selectAllByPoiCode(tenantId, channelId, poiCodeList);
        } catch (Exception e) {
            log.error("CopChannelStoreServiceImpl.selectAllByPoiCode, 查询租户渠道渠道门店基础信息异常, tenantId:{}, channelId:{}, poiCode:{}",
                    tenantId, channelId, poiCodeList, e);
        }

        return Lists.newArrayList();
    }

    @Override
    public List<PoiCodeAppIdDO> selectAllByChannelPoiCode(long tenantId, int channelId, List<String> poiCodeList) {
        if (CollectionUtils.isEmpty(poiCodeList)) {
            throw new IllegalArgumentException("poiCodeList cannot be empty!");
        }
        try {
            return channelStoreBaseInfoMapper.selectAllByChannelPoiCode(tenantId, channelId, poiCodeList);
        } catch (Exception e) {
            log.error("CopChannelStoreServiceImpl.selectAllByPoiCode, 查询租户渠道渠道门店基础信息异常, tenantId:{}, channelId:{}, poiCode:{}",
                    tenantId, channelId, poiCodeList, e);
        }

        return Lists.newArrayList();
    }

    @Override
    public Map<String, Long> getPoiCodeAppIdMapping(long tenantId, int channelId, List<String> poiCodeList) {
        List<PoiCodeAppIdDO> channelStoreBaseInfos = this.selectAllByPoiCode(tenantId, channelId, poiCodeList);
        return channelStoreBaseInfos.stream().collect(Collectors.toMap(PoiCodeAppIdDO::getPoiCode, PoiCodeAppIdDO::getAppId));
    }

    @Override
    public ChannelStoreDO selectByChannelPoiCode(Integer channelId, String channelPoiCode) {
        Preconditions.checkArgument(channelId != null, "渠道id不能为空");
        Preconditions.checkArgument(StringUtils.isNotEmpty(channelPoiCode), "渠道门店id不能为空");
        ChannelStoreDO channelStoreDO = channelStoreMapper.selectByChannelPoiCode(channelId, channelPoiCode);
        return channelStoreDO;
    }

    private ChannelStoreDO innerSelectChannelStore(long tenantId, int channelId, String channelPoiCode, boolean ignoreValid) {
        ChannelStoreDO channelStoreDO = null;
        try {
            if(ignoreValid) {
                channelStoreDO = channelStoreMapper.selectChannelStoreIdIgnoreValid(tenantId, channelId, channelPoiCode);
            }else {
                channelStoreDO = channelStoreMapper.selectChannelStoreId(tenantId, channelId, channelPoiCode);
            }
        } catch (Exception e) {
            log.error(
                    "CopChannelStoreServiceImpl.selectChannelStoreId, 查询租户渠道信息异常, tenantId:{}, channelId:{}, "
                            + "channelPoiCode:{}",
                    tenantId, channelId, channelPoiCode, e);
        } finally {
//            log.info(
//                    "CopChannelStoreServiceImpl.selectChannelStoreId, 查询租户渠道信息返回数据, tenantId:{}, channelId:{}, "
//                            + "channelPoiCode:{}, channelStoreDO:{}",
//                    tenantId, channelId, channelPoiCode, channelStoreDO);
        }
        return channelStoreDO;
    }
}
