package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.qnh;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.QnhProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhChannelOrderSettlementDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhChannelOrderSettlementPageInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhCommonResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostQnhEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MTSettleStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.QnhChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.QnhChannelOrderSettlementDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.QnhChannelOrderSettlementPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.QnhChannelOrderSettlementPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.SettlementFeeDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;

import lombok.extern.slf4j.Slf4j;

/**
 * 牵牛花结算业务接口实现
 *
 * <AUTHOR>
 * @since 2022/3/1
 */
@Slf4j
@Service
public class QnhSettlementService {

    @Autowired
    private QnhChannelGateService qnhChannelGateService;

    @Autowired
    private QnhStoreService qnhStoreService;

    public QnhChannelOrderSettlementPageResponse getChannelOrderSettlementList(QnhChannelOrderSettlementPageRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.PAGE_NO, request.getPageNo());
        bizParam.put(QnhProjectConstant.PAGE_SIZE, request.getPageSize());
        bizParam.put(QnhProjectConstant.CHANNEL_KEYWORD, QnhChannelTypeEnum.valueOfEnum(request.getChannelId()).getKeyword());
        bizParam.put(QnhProjectConstant.REGION_CODE, qnhStoreService.mappingQnhStoreIdByStoreId(request.getTenantId(), request.getShopId()));
        bizParam.put(QnhProjectConstant.START_TIME, request.getAccountTimeStart() / 1000);
        bizParam.put(QnhProjectConstant.END_TIME, request.getAccountTimeEnd() / 1000);
        QnhCommonResponse<QnhChannelOrderSettlementPageInfo> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.PULL_CHANNEL_ORDER_SETTLEMENT, bizParam);
        log.info("通过牵牛花拉取渠道账单, request:{}, qnhCommonResponse:{}", request, JacksonUtils.toJson(qnhCommonResponse));
        QnhChannelOrderSettlementPageResponse resp = new QnhChannelOrderSettlementPageResponse();
        if (qnhCommonResponse == null) {
            return resp.setResultStatus(ResultGenerator.genResult(ResultCode.FAIL, "通过牵牛花拉取渠道账单失败"));
        }
        if (!qnhCommonResponse.success()) {
            return resp.setResultStatus(ResultGenerator.genResult(ResultCode.FAIL, qnhCommonResponse.getMsg()));
        }
        QnhChannelOrderSettlementPageInfo pageInfo = qnhCommonResponse.getStructuredData();
        if (pageInfo == null) {
            return resp.setResultStatus(ResultGenerator.genResult(ResultCode.FAIL, "通过牵牛花拉取渠道账单为空"));
        }

        return convertToResponse(pageInfo, request.getChannelId());
    }

    private QnhChannelOrderSettlementPageResponse convertToResponse(QnhChannelOrderSettlementPageInfo pageInfo, Integer channelId) {
        QnhChannelOrderSettlementPageResponse response = new QnhChannelOrderSettlementPageResponse();
        response.setTotalCount((int)pageInfo.getCount());

        // 构造渠道账单数据
        response.setChannelOrderSettlementDtoList(Fun.map(pageInfo.getRows(), row -> convertToChannelOrderSettlement(row, channelId)));
        response.setResultStatus(ResultGenerator.genSuccessResult());
        return response;
    }

    private QnhChannelOrderSettlementDTO convertToChannelOrderSettlement(QnhChannelOrderSettlementDetail detail, Integer channelId) {
        QnhChannelOrderSettlementDTO settlementDTO = new QnhChannelOrderSettlementDTO();
        settlementDTO.setChannelId(channelId);
        settlementDTO.setChannelOrderId(detail.getChannel_sheetno());
        settlementDTO.setOrderTime(detail.getOrder_time() * 1000);
        settlementDTO.setOrderChargeType(detail.getCharge_type());
        settlementDTO.setOrderChargeTypeDesc(detail.getCharge_type_desc());
        settlementDTO.setOutSettlementId(detail.getOut_settlement_id());
        settlementDTO.setSettlementDate(detail.getSettle_time() * 1000);
        settlementDTO.setDeliveryType(detail.getDelivery_type());
        settlementDTO.setSettlementFinishDate(detail.getSettle_time() * 1000);
        settlementDTO.setRefundId(detail.getRefund_id());
        settlementDTO.setRefundTime(detail.getRefund_time() * 1000);
        settlementDTO.setChargeFeeType(detail.getCharge_fee_type());
        settlementDTO.setOrderFinishTime(detail.getComplete_time() * 1000);
        settlementDTO.setOrderIndex(detail.getOrder_index());
        // 结算金额明细
        List<SettlementFeeDetail> feeDetails = Collections.emptyList();
        if (channelId.equals(ChannelType.MEITUAN.getValue())) {
            settlementDTO.setSettlementAmt(MoneyUtils.doubleHalfUpToLong(detail.getSettlement_amt()));
            MTSettleStatusEnum statusEnum = MtConverterUtil.convertMtSettleStatus(detail.getAccount_state());
            settlementDTO.setRawSettleStatus(String.valueOf(statusEnum.getCode()));
            settlementDTO.setRawSettleStatusDesc(statusEnum.getDesc());
            // 商家承担成本的商品优惠分摊明细
            settlementDTO.setSkuBenefitDetailList(MtConverterUtil.convertSkuBenefitDetailList(detail.getSku_benefit_details()));
            // 商家承担配送费活动分摊明细
            settlementDTO.setOrderSkuShippingDetailList(MtConverterUtil.convertSkuShippingDetailList(detail.getSku_shipping_details()));
            // 费用明细
            feeDetails = MtConverterUtil.convertQnhSettlementFeeDetail(detail);
        }
        else if (channelId.equals(ChannelType.ELEM.getValue())) {
            settlementDTO.setSettlementAmt(MoneyUtils.doubleHalfUpToLong(detail.getAmount()));
            // 饿了么额外信息
            settlementDTO.setSettlementExtraInfoDetailList(ElmConverterUtil.convertQnhSettlementExtraInfoDetail(detail));
            // 费用明细
            feeDetails = ElmConverterUtil.convertQnhSettlementFeeDetail(detail);
        }
        settlementDTO.setSettlementFeeDetailList(feeDetails);
        return settlementDTO;
    }

}