package com.sankuai.meituan.shangou.empower.ocms.channel.validator;

import com.google.common.base.Preconditions;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelNotifyEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ValidateFieldEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.EnumUtil;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * @description:
 * @author: zhaolei12
 * @create: 2019/1/30 下午3:16
 */
@Slf4j
public class OrderParamValidator {

    private OrderParamValidator() {}

    public static <T> void orderBasicValidate(T request) {
        Preconditions.checkNotNull(request, ResultCode.INVALID_REQUEST.getMsg());
        try {
            long tenantId = (long) request.getClass().getMethod(ValidateFieldEnum.TENANT_ID.getAbbrev()).invoke(request);
            Preconditions.checkArgument(tenantId > 0, "tenantId 不合法");
            int channelId = (int) request.getClass().getMethod(ValidateFieldEnum.CHANNEL_ID.getAbbrev()).invoke(request);
            Preconditions.checkArgument(ChannelTypeEnum.abbrevMap.containsKey(channelId) || channelId >= 2000, "channelId为空或不合法");
            String orderId = (String) request.getClass().getMethod(ValidateFieldEnum.ORDER_ID.getAbbrev()).invoke(request);
            Preconditions.checkArgument(StringUtils.isNotBlank(orderId), "orderId不能为空");
        } catch (Exception e) {
            log.error("OrderParamValidator.orderBasicValidate 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    public static <T> void orderBasicWithoutChannelValidate(T request) {
        Preconditions.checkNotNull(request, ResultCode.INVALID_REQUEST.getMsg());
        try {
            long tenantId = (long) request.getClass().getMethod(ValidateFieldEnum.TENANT_ID.getAbbrev()).invoke(request);
            Preconditions.checkArgument(tenantId > 0, "tenantId 不合法");
            String orderId = (String) request.getClass().getMethod(ValidateFieldEnum.ORDER_ID.getAbbrev()).invoke(request);
            Preconditions.checkArgument(StringUtils.isNotBlank(orderId), "orderId不能为空");
        } catch (Exception e) {
            log.error("OrderParamValidator.orderBasicValidate 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    public static <T> void orderCallbackBasicValidate(T request) {
        Preconditions.checkNotNull(request, ResultCode.INVALID_REQUEST.getMsg());
        try {
            String channelCode = String.valueOf(request.getClass().getMethod(ValidateFieldEnum.CHANNEL_CODE.getAbbrev()).invoke(request));
            Preconditions.checkArgument(Objects.nonNull(EnumUtil.getEnumByAbbrev(channelCode, ChannelTypeEnum.class)), "channelCode 为空或不合法");
            // 有赞同城配送包裹状态变更的消息体无client_id字段，需跳过tenantAppId校验，https://doc.youzanyun.com/detail/MSG/357
            if (!ChannelTypeEnum.YOU_ZAN.getAbbrev().equals(channelCode) || !ChannelNotifyEnum.YZ_DELIVERY_TAKE_OUT_ORDER_UPDATE.getAbbrev().equals(String.valueOf(request.getClass().getMethod(ValidateFieldEnum.ACTION.getAbbrev()).invoke(request)))) {
                String tenantAppId = String.valueOf(request.getClass().getMethod(ValidateFieldEnum.TENANT_APP_ID.getAbbrev()).invoke(request));
                Preconditions.checkArgument(StringUtils.isNotBlank(tenantAppId), "tenantAppId 为空或不合法");
            }
        } catch (Exception e) {
            log.error("OrderParamValidator.orderCallbackBasicValidate 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    public static <T> void confirmOrderValidate(T request) {
        orderCallbackBasicValidate(request);
        try {
            String status = String.valueOf(request.getClass().getMethod(ValidateFieldEnum.STATUS.getAbbrev()).invoke(request));
            Preconditions.checkArgument(StringUtils.isNotBlank(status), "status 订单状态不能为空");
        } catch (Exception e) {
            log.error("OrderParamValidator.confirmOrderValidate 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    public static <T> void orderBasicWithoutOrderIdValidate(T request) {
        Preconditions.checkNotNull(request, ResultCode.INVALID_REQUEST.getMsg());
        try {
            long tenantId = (long) request.getClass().getMethod(ValidateFieldEnum.TENANT_ID.getAbbrev()).invoke(request);
            Preconditions.checkArgument(tenantId > 0, "tenantId 不合法");
            int channelId = (int) request.getClass().getMethod(ValidateFieldEnum.CHANNEL_ID.getAbbrev()).invoke(request);
            Preconditions.checkArgument(ChannelTypeEnum.abbrevMap.containsKey(channelId) || channelId >= 2000, "channelId为空或不合法");
        } catch (Exception e) {
            log.error("OrderParamValidator.orderBasicWithoutOrderIdValidate 异常", e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

}
