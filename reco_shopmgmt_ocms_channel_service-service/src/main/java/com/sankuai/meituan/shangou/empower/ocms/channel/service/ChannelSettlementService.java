package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.*;

/**
 * 结算业务接口
 *
 * <AUTHOR>
 * @since 2021/2/28
 */
public interface ChannelSettlementService {

    /**
     * 根据账期区间，分页查询渠道结算单汇总
     *
     * @param request 饿了么 pageSize 固定为20 因此在请求中 pageSize 无效
     * @return
     */
    ChannelSettlementPageResponse getChannelSettlementList(ChannelSettlementPageRequest request);

    /**
     * 根据渠道结算单号，分页查询渠道结算明细
     *
     * @param request
     * @return
     */
    ChannelSettlementAndDetailResponse getChannelOrderSettlementListById(ChannelOrderSettlementByIdRequest request);

    /**
     * 根据账期区间，分页查询渠道结算单明细
     *
     * @param request
     * @return
     */
    ChannelOrderSettlementPageResponse getChannelOrderSettlementList(ChannelOrderSettlementPageRequest request);

    /**
     * 分页查询对账单接口
     * @param request
     * @return
     */
    ChannelBalanceBillPageResponse getBalanceBillList(ChannelBalanceBillPageRequest request);

    /**
     * 查询订单计费明细接口
     * @param request
     * @return
     */
    ChannelCheckBillResponse getCheckBillList(ChannelCheckBillPageRequest request);

    /**
     * 分页查询对账单接口
     * 透传，不做额外的处理
     * @param request
     * @return
     */
    JDBalanceBillPageResponse getBalanceBill(JDBalanceBillPageRequest request);

    /**
     * 分页查询对账单接口
     * 透传，不做额外的处理
     * @param request
     * @return
     */
    DouyinBalanceBillPageResponse getDouyinBalanceBill(DouyinBalanceBillPageRequest request);
}