package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelActivityService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ActTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ItemOpTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.SettingTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultBuilderUtil;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description:
 * @author: zhaolei12
 * @create: 2019-03-06 16:22
 */
@Service("mtBrandChannelActivityService")
public class MtBrandChannelActivityServiceImpl implements ChannelActivityService {

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private MtConverterService mtConverterService;

    @Resource(name = "mtBrandChannelCommonService")
    private MtBrandChannelCommonServiceImpl mtBrandChannelBaseService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private CommonLogger log;

    @Value("${mt.url.base}" + "${mt.url.actCanModifyPrice}")
    private String actCanModifyPrice;

    public final static String NG = "ng";

    @Resource(name = "mtActivityThreadPool")
    private ExecutorService executorService;

    /**
     * @param activityList 支持多门店，内部单门店串行执行
     * @return
     */
    @Override
    public ActivityResponse saveActivity(ActTypeEnum actType, BaseRequest baseRequest, List<ChannelActivityInfo> activityList) {
        ActivityResponse activityResponse = ResultBuilderUtil.genActivityResponse(ResultGenerator.genSuccessResult());
        try {
            //按门店分组（接口为单门店多商品）
            Map<Long, List<ChannelActivityInfo>> map = activityList.stream()
                    .collect(Collectors.groupingBy(ChannelActivityInfo::getStoreId));

            long tenantId = baseRequest.getTenantId();
            int channelId = baseRequest.getChannelId();

            //门店串行调用
            map.forEach((storeId, activityInfoList) -> {
                BaseRequest storeRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                switch (actType) {
                    case CREATE:
                        createActivity(storeRequest, activityInfoList, activityResponse);
                        break;
                    case EDIT:
                        editActivity(storeRequest, activityInfoList, activityResponse);
                        break;
                    default:
                        break;
                }
            });
            return activityResponse;
        } catch (Exception e) {
            log.error("MtChannelActivityServiceImpl.saveActivity, actType:{}, baseRequest:{}, activityList:{}", actType, baseRequest, activityList, e);
        }
        return activityResponse.setStatus(ResultGenerator.genFailResult("渠道网关保存服务异常"));
    }

    /**
     * 编辑活动，判断（新增、修改）、删除商品
     */
    private void editActivity(BaseRequest baseRequest, List<ChannelActivityInfo> activityInfoList, ActivityResponse response) {
        Map<ItemOpTypeEnum, List<ChannelActivityInfo>> editMap = activityInfoList.stream()
                .collect(Collectors.groupingBy(ChannelActivityInfo::getItemOpType));
        editMap.forEach((itemOpType, groupActivityList) -> {
            switch (itemOpType) {
                case ADD:
                    groupActivityList.forEach(activityInfo -> activityInfo.setChannelActivityId(Strings.EMPTY));
                    createActivity(baseRequest, groupActivityList, response);
                    break;
                case UPDATE:
                    createActivity(baseRequest, groupActivityList, response);
                    break;
                case DELETE:
                    delActivityItem(baseRequest, groupActivityList, response);
                    break;
                default:
                    break;
            }
        });
    }

    /**
     * 调用渠道接口创建、更新活动（单门店）
     *
     * @param baseRequest 门店id必须传入
     */
    private void createActivity(BaseRequest baseRequest, List<ChannelActivityInfo> activityInfoList, ActivityResponse response) {
        // 渠道接口
        Map<String, ChannelActDataInfoDTO> actDataInfoMap = queryPoiActivity(baseRequest);

        List<ChannelActivityInfo> existActivityInfoList = Lists.newArrayList();
        if (MapUtils.isNotEmpty(actDataInfoMap)) {
            activityInfoList.forEach(channelActivityInfo -> {
                if (actDataInfoMap.containsKey(channelActivityInfo.getSkuId())) {
                    ResultBuilderUtil.activitySuccessResultAnalysis(baseRequest, actDataInfoMap.get(channelActivityInfo.getSkuId()).getSkuId(),
                            channelActivityInfo.getSkuId(), response);
                    existActivityInfoList.add(channelActivityInfo);
                }
            });
        }
        activityInfoList.removeAll(existActivityInfoList);
        if (CollectionUtils.isEmpty(activityInfoList)) {
            return;
        }
        List<String> skuList = Lists.newArrayList();
        activityInfoList.forEach(channelActivityInfo -> {
            skuList.add(channelActivityInfo.getSkuId());
            if (SettingTypeEnum.DISCOUNT_PRICE == channelActivityInfo.getSettingType()) {
                channelActivityInfo.setDiscountCoefficient(0.1);
            } else if (SettingTypeEnum.DISCOUNT_COEFFICIENT == channelActivityInfo.getSettingType()) {
                channelActivityInfo.setPromotionPrice(1);
            }
        });
        ChannelActivityParamInfo activityParamInfo = new ChannelActivityParamInfo();
        activityParamInfo.setAct_data(mtConverterService.channelActDataInfoList(activityInfoList));

        // 渠道接口
        Map<Long, ChannelResponseDTO<String>> postResult = mtBrandChannelGateService
                .sendPost(ChannelPostMTEnum.ACT_BATCH_SAVE, baseRequest, activityParamInfo);
        //返回数据结果解析
        log.info("MtChannelActivityServiceImpl.createActivity, 渠道创建活动返回数据, postResult:{}", postResult);
        ResultBuilderUtil.activityResultAnalysis(baseRequest, skuList, postResult, "调用渠道创建活动失败", response);
    }

    /**
     * 查询门店折扣商品（用于创建活动幂等处理）
     */
    private Map<String, ChannelActDataInfoDTO> queryPoiActivity(BaseRequest baseRequest) {
        Map<String, ChannelActDataInfoDTO> actDataInfoMap = Maps.newHashMap();
        if (MccConfigUtil.getActRetailDiscountSwitch()) {
            return actDataInfoMap;
        }
        List<ChannelActDataInfoDTO> channelActDataInfoList = mtBrandChannelBaseService.queryPoiActivity(baseRequest);
        channelActDataInfoList.forEach(channelActDataInfo -> actDataInfoMap.put(channelActDataInfo.getSkuId(), channelActDataInfo));
        return actDataInfoMap;
    }

    /**
     * 调用渠道接口删除活动商品（单门店）
     *
     * @param baseRequest 门店ID必须传入
     */
    private void delActivityItem(BaseRequest baseRequest, List<ChannelActivityInfo> activityInfoList, ActivityResponse response) {
        Map<String, String> activityMap = activityInfoList.stream().filter(a -> StringUtils.isNotBlank(a.getChannelActivityId())).collect(
                Collectors.toMap(ChannelActivityInfo::getChannelActivityId, ChannelActivityInfo::getSkuId, (k1, k2) -> k1));
        ChannelActivityDiscountDeleteDTO activityDeleteList = ChannelActivityDiscountDeleteDTO.builder()
                .item_ids(StringUtils.join(activityMap.keySet(), ",")).build();
        if (MapUtils.isEmpty(activityMap)) {
            activityInfoList.forEach(activityInfo -> {
                ResultBuilderUtil.activityDelErrorResultAnalysis(baseRequest, activityInfo.getChannelActivityId(),
                        activityInfo.getSkuId(), "活动编码不能为空", response, true);
            });
            return;
        }

        // 渠道接口
        Map<Long, ChannelResponseDTO<String>> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.ACT_BATCH_DELETE, baseRequest, activityDeleteList);
        log.info("MtChannelActivityServiceImpl.delActivityItem, 渠道删除活动返回数据, postResult:{}", postResult);
        if (Objects.isNull(postResult)) {
            ResultBuilderUtil.activityDelErrorResultAnalysis(baseRequest, activityMap, "调用渠道删除活动商品失败", response, true);
            return;
        }
        ChannelResponseDTO<String> channelResponseDTO = postResult.get(baseRequest.getStoreIdList().get(0));
        if (channelResponseDTO.isSuccess()) {
            ResultBuilderUtil.activityDelSuccessResultAnalysis(baseRequest, activityMap, response);
            return;
        }
        ChannelActErrResult actErrResult = JSON.parseObject(channelResponseDTO.getErrorMsg(), ChannelActErrResult.class);
        if (Objects.isNull(actErrResult) || CollectionUtils.isEmpty(actErrResult.getError_list())) {
            ResultBuilderUtil.activityDelSuccessResultAnalysis(baseRequest, activityMap, response);
        } else {
            actErrResult.getError_list().forEach(responseErr ->
                    ResultBuilderUtil.activityDelErrorResultAnalysis(baseRequest, responseErr.getAct_id(), activityMap.get(responseErr.getAct_id()),
                            responseErr.getError_msg(), response, false));
        }
    }

    /**
     * @param channelPoiParamInfoList 只支持多门店，内部单门店串行执行
     * @return
     */
    @Override
    public ActivityResponse batchDelete(BaseRequest baseRequest, List<ChannelPoiParamInfo> channelPoiParamInfoList) {
        ActivityResponse response = new ActivityResponse();
        List<ActivityResultDataInfo> errorResultDataInfoList = Collections.synchronizedList(new ArrayList<>());
        List<ActivityResultDataInfo> successResultDataInfoList = Collections.synchronizedList(new ArrayList<>());
        ResultStatus resultStatus = ResultGenerator.genSuccessResult();

        // 单门店串行
        channelPoiParamInfoList.forEach(poiParamInfo ->
                batchDeleteActivity(baseRequest, errorResultDataInfoList, successResultDataInfoList, poiParamInfo)
        );

        response.setSuccessData(successResultDataInfoList).setErrorData(errorResultDataInfoList).setStatus(resultStatus);
        return response;
    }

    /**
     * @param request 仅支持单门店
     * @return
     */
    @Override
    public QueryActivityResponse queryActivityList(BaseRequest request) {
        List<ChannelActDataInfoDTO> channelActDataInfoList = mtBrandChannelBaseService.queryPoiActivity(request);
        return new QueryActivityResponse().setStatus(ResultGenerator.genSuccessResult()).setActList(channelActDataInfoList);
    }

    /**
     * @param request 单门店接口
     * @return
     */
    @Override
    public QuerySkuActivityInfoResponse querySkuActivityInfos(QuerySkuActivityInfoRequest request) {

        QuerySkuActivityInfoResponse response = new QuerySkuActivityInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        ChannelSkuActAllDetail actAllDetail = mtBrandChannelBaseService.querySkuActivityInfoList(request);
        if (CollectionUtils.isNotEmpty(actAllDetail.getActivityDetails())) {
            response.setSkuActivityInfos(mtConverterService.channelSkuActDataInfoListMapping(actAllDetail.getActivityDetails()));
        }
        if (MapUtils.isNotEmpty(actAllDetail.getSkuErrMap())) {
            response.getStatus().setMsg(JSON.toJSONString(actAllDetail.getSkuErrMap()));
        }

        if (CollectionUtils.isEmpty(actAllDetail.getActivityDetails()) &&
                MapUtils.isNotEmpty(actAllDetail.getSkuErrMap())) {
            response.getStatus().setCode(ResultCode.FAIL.getCode());
        }

        return response;
    }

    /**
     * 一租户多品牌后，渠道交互sercet在品牌维度，因此单次请求渠道只能是同品牌下门店
     *
     * @param request 多门店接口
     * @param count
     * @return
     */
    @Override
    public QueryCanModifyPriceResponse querySpuCanModifyPrice(QueryCanModifyPriceRequest request, Integer count) {
        QueryCanModifyPriceResponse response = new QueryCanModifyPriceResponse();
        response.setStatus(ResultGenerator.genFailResult("默认失败"));
        try {
            Cat.logEvent(getClass().getSimpleName(), "querySpuCanModifyPrice");

            long tenantId = request.getTenantId();
            int channelId = request.getChannelId();

            //StoreCustomSpuKey: 一个门店及下挂的spuids
            List<StoreCustomSpuKey> customSpuKeys = request.getCustomSpuIds();
            if (CollectionUtils.isEmpty(customSpuKeys)) {
                return response.setStatus(ResultGenerator.genSuccessResult());
            }

            List<Long> storeIds = customSpuKeys.stream()
                    .map(StoreCustomSpuKey::getStoreId)
                    .distinct()
                    .collect(Collectors.toList());

            Map<String, ChannelStoreDO> storeKeyMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, storeIds);

            if (org.apache.commons.collections4.MapUtils.isEmpty(storeKeyMap)) {
                return response.setStatus(ResultGenerator.genFailResult("调用门店服务未返回结果"));
            }

            //渠道门店id与牵牛花门店id映射关系
            Map<String/*渠道商家门店编码*/, ChannelStoreDO> channelOnlinePoiMap = storeKeyMap.values().stream()
                    .collect(Collectors.toMap(ChannelStoreDO::getChannelOnlinePoiCode, Function.identity(), (o1, o2) -> o2));


            Table<Long/*品牌AppId*/, ChannelStoreDO/*门店信息*/, StoreCustomSpuKey/*门店下的CustomSpuId集合*/> appCustomSpuTable = HashBasedTable.create();
            customSpuKeys.stream()
                    .forEach(item -> {
                        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, item.getStoreId());
                        ChannelStoreDO channelStoreDO = storeKeyMap.get(channelStoreKey);
                        if (Objects.isNull(channelStoreDO)) {
                            log.error("查询门店不存在对应的渠道门店 channelStoreKey = {}", channelStoreKey);
                            throw new IllegalStateException("查询门店不存在对应的渠道门店");
                        }

                        //品牌在牵牛花对应的id
                        Long appId = channelStoreDO.getAppId();
                        appCustomSpuTable.put(appId, channelStoreDO, item);
                    });

            //循环每个品牌：一个品牌一次渠道调用
            List<Future<CanModifyPriceResult>> futures = Lists.newArrayList();
            for (Long appId : appCustomSpuTable.rowKeySet()) {
                List<StoreSpuParam> storeSpuParams = Lists.newArrayList();
                Map<ChannelStoreDO/*门店信息*/, StoreCustomSpuKey/*门店下的CustomSpuId集合*/> storeCustomSpuMap = appCustomSpuTable.row(appId);
                if (MapUtils.isEmpty(storeCustomSpuMap)) {
                    continue;
                }

                //循环每个门店
                storeCustomSpuMap.forEach((channelStoreDO, storeCustomSpuKey) -> {
                    if (CollectionUtils.isNotEmpty(storeCustomSpuKey.getCustomSpuIds())) {
                        StoreSpuParam storeSpuParam = new StoreSpuParam();
                        storeSpuParam.setApp_poi_code(channelStoreDO.getChannelOnlinePoiCode());
                        storeSpuParam.setApp_food_codes(Lists.newArrayList(storeCustomSpuKey.getCustomSpuIds()));
                        storeSpuParams.add(storeSpuParam);
                    }
                });

                String food_data = JSON.toJSONString(storeSpuParams);
                CanModifyPriceDTO priceDTO = new CanModifyPriceDTO();
                priceDTO.setFood_data(food_data);

                //线程池加速
                Future<CanModifyPriceResult> future = executorService.submit(() -> {
                    BaseRequest baseRequest = new BaseRequest()
                            .setTenantId(tenantId)
                            .setChannelId(channelId)
                            //多门店场景传入品牌对应的Appid, 底层会查询品牌对应的secret，再与渠道交互
                            .setAppId(appId)
                            .setAsyncInvoke(false);
                    //ele接口需要传第二个method
                    Map<String, Object> resultString = mtBrandChannelGateService.sendEncodedGet(actCanModifyPrice, null, baseRequest, priceDTO);

                    CanModifyPriceResult channelResult = JSON.parseObject(JSON.toJSONString(resultString), CanModifyPriceResult.class);

                    return channelResult;
                });
                futures.add(future);
            }

            //收集渠道查询结果
            for (Future<CanModifyPriceResult> future : futures) {
                CanModifyPriceResult channelResult = future.get(10L, TimeUnit.SECONDS);
                response = buildQueryCanModifyPriceResponse(response, channelResult, channelOnlinePoiMap);
            }
        } catch (Exception e) {
            log.error("查询SPU是否可以改价异常", e);
            response.setStatus(ResultGenerator.genFailResult("查询SPU是否可以改价异常" +
                    Optional.ofNullable(e.getMessage()).orElse("")));
        }
        return response;
    }

    /**
     * 将渠道结果装入到response中，由于存在单次多品牌并发请求渠道，可能存在部分品牌成功，部分品牌失败
     *
     * @param response 要求非空
     * @param channelResult
     * @param channelOnlinePoiMap
     * @return
     */
    private QueryCanModifyPriceResponse buildQueryCanModifyPriceResponse(@NonNull QueryCanModifyPriceResponse response,
                                                                         CanModifyPriceResult channelResult,
                                                                         Map<String, ChannelStoreDO> channelOnlinePoiMap) {
        if (Objects.isNull(channelResult)) {
            return response;
        }

        //全部成功
        if (channelResult.getResult_code() == 1) {
            QueryCanModifyPriceResponse tempResponse = parseSuccess(channelOnlinePoiMap, channelResult);

            if (CollectionUtils.isEmpty(tempResponse.getStoreSpuCanModifyPriceInfos())) {
                return response;
            }

            if (CollectionUtils.isEmpty(response.getStoreSpuCanModifyPriceInfos())) {
                response.setStoreSpuCanModifyPriceInfos(tempResponse.getStoreSpuCanModifyPriceInfos());
            } else {
                response.getStoreSpuCanModifyPriceInfos().addAll(tempResponse.getStoreSpuCanModifyPriceInfos());
            }

            //更新状态为成功
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                response.setStatus(ResultGenerator.genSuccessResult());
            }
        } else {
            //填入最新一次渠道错误信息
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, channelResult.getError()));
            }

            //填入渠道报错spu
            List<StoreSpuErrorInfo> errorInfos = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(channelResult.getError_list())) {
                errorInfos = channelResult.getError_list().stream()
                        .map(it -> {
                            Long storeId = Optional.ofNullable(channelOnlinePoiMap.get(it.getApp_poi_code()))
                                    .map(ChannelStoreDO::getStoreId)
                                    .orElse(-1L);
                            return new StoreSpuErrorInfo(storeId,
                                    Optional.ofNullable(it.getApp_food_code()).orElse(""),
                                    Optional.ofNullable(it.getMsg()).orElse(""));
                        })
                        .collect(Collectors.toList());
            } else {
                //将渠道error封装到特殊StoreSpuErrorInfo中
                errorInfos.add(new StoreSpuErrorInfo(-1, "报错参考errorMsg", channelResult.getError()));
            }

            if (CollectionUtils.isEmpty(response.getErrorSpuInfos())) {
                response.setErrorSpuInfos(errorInfos);
            } else {
                response.getErrorSpuInfos().addAll(errorInfos);
            }
        }
        return response;
    }

    private QueryCanModifyPriceResponse parseSuccess(Map<String, ChannelStoreDO> channelOnlinePoiMap, CanModifyPriceResult channelResult) {
        Preconditions.checkArgument(channelResult.getResult_code() == 1);
        List<StoreSuccessInfo> storeSuccessInfos = JSON.parseArray(channelResult.getData(), StoreSuccessInfo.class);
        List<StoreSpuCanModifyPriceInfo> storeSpuCanModifyPriceInfos = Optional.ofNullable(storeSuccessInfos)
                .map(List::stream).orElse(Stream.empty())
                .map(storeSuccessInfo -> {
                    Long storeId = Optional.ofNullable(channelOnlinePoiMap.get(storeSuccessInfo.getApp_poi_code()))
                            .map(ChannelStoreDO::getStoreId)
                            .orElseThrow(() -> new IllegalStateException("门店映射出错"));
                    List<SpuCanModifyPriceInfo> spuCanModifyPriceInfos = Optional
                            .ofNullable(storeSuccessInfo.getResult_list())
                            .map(List::stream).orElse(Stream.empty())
                            .map(spuSuccessInfo ->
                                    new SpuCanModifyPriceInfo(spuSuccessInfo.getApp_food_code(),
                                            spuSuccessInfo.getCan_modify_price() > 0 ? false : true)//这里开放平台定义的接口问题，can_modify_price:1是不能改价。
                            ).collect(Collectors.toList());
                    return new StoreSpuCanModifyPriceInfo().setStoreId(storeId)
                            .setSpuCanModifyPriceInfos(spuCanModifyPriceInfos);
                }).collect(Collectors.toList());
        QueryCanModifyPriceResponse response = new QueryCanModifyPriceResponse();
        response.setStoreSpuCanModifyPriceInfos(storeSpuCanModifyPriceInfos);
        response.setStatus(ResultGenerator.genSuccessResult());
        return response;
    }

    @Data
    static class StoreSuccessInfo {
        String app_poi_code;
        List<SpuSuccessInfo> result_list;
    }

    @Data
    static class SpuSuccessInfo {
        String app_food_code;
        Integer can_modify_price;
    }

    /**
     * @param poiParamInfo 单门店
     */
    private void batchDeleteActivity(BaseRequest baseRequest, List<ActivityResultDataInfo> errorResultDataInfoList, List<ActivityResultDataInfo> successResultDataInfoList, ChannelPoiParamInfo poiParamInfo) {
        try {
            ChannelActivityDiscountDeleteDTO activityDiscountDeleteDTO = ChannelActivityDiscountDeleteDTO.builder()
                    .app_poi_code(String.valueOf(poiParamInfo.getStoreId()))
                    .item_ids(StringUtils.join(poiParamInfo.getChannelActivityIdList(), ','))
                    .build();

            BaseRequest storeRequest = new BaseRequest()
                    .setTenantId(baseRequest.getTenantId())
                    .setChannelId(poiParamInfo.getChannelId())
                    .setStoreIdList(Lists.newArrayList(poiParamInfo.getStoreId()));

            // 渠道接口
            Map<Long, ChannelResponseDTO<String>> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.ACTIVITY_DISCOUNT_BATCH_DELETE,
                    storeRequest, activityDiscountDeleteDTO);

            if (MapUtils.isEmpty(postResult)) {
                ResultStatus errorStatus = ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg());
                errorResultDataInfoList.addAll(buildResultDataInfoList(poiParamInfo, errorStatus));
                return;
            }
            ChannelResponseDTO<String> channelResponseDTO = postResult.get(poiParamInfo.getStoreId());
            if (channelResponseDTO == null) {
                ResultStatus errorStatus = ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg());
                errorResultDataInfoList.addAll(buildResultDataInfoList(poiParamInfo, errorStatus));
                return;
            }
            if (!channelResponseDTO.isSuccess()) {
                ResultStatus errorStatus = ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg());
                errorResultDataInfoList.addAll(buildResultDataInfoList(poiParamInfo, errorStatus, channelResponseDTO.getErrorMsg()));
                return;
            }

            successResultDataInfoList.addAll(buildResultDataInfoList(poiParamInfo, ResultGenerator.genSuccessResult()));
            return;
        } catch (IllegalArgumentException e) {
            log.error("batch delete error. msg:{}, param:{}", e.getMessage(), baseRequest, e);
            ResultStatus errorStatus = ResultGenerator.genFailResult(e.getMessage());
            errorResultDataInfoList.addAll(buildResultDataInfoList(poiParamInfo, errorStatus));
            return;
        } catch (Exception e) {
            log.error("batch delete error. msg:{}, param:{}", e.getMessage(), baseRequest, e);
            ResultStatus errorStatus = ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg());
            errorResultDataInfoList.addAll(buildResultDataInfoList(poiParamInfo, errorStatus));
            return;
        }
    }

    /**
     * 将批量结果打平
     *
     * @param paramInfo
     * @param resultStatus
     * @return
     */
    private List<ActivityResultDataInfo> buildResultDataInfoList(ChannelPoiParamInfo paramInfo, ResultStatus resultStatus) {
        List<ActivityResultDataInfo> resultDataInfoList = new ArrayList<>();
        if (paramInfo == null || CollectionUtils.isEmpty(paramInfo.getChannelActivityIdList())) {
            return resultDataInfoList;
        }

        for (String activityId : paramInfo.getChannelActivityIdList()) {
            resultDataInfoList.add(
                    new ActivityResultDataInfo()
                            .setChannelId(paramInfo.getChannelId())
                            .setStoreId(paramInfo.getStoreId())
                            .setChannelActivityId(activityId)
                            .setData(resultStatus)
            );
        }
        return resultDataInfoList;
    }

    private List<ActivityResultDataInfo> buildResultDataInfoList(ChannelPoiParamInfo paramInfo, ResultStatus resultStatus, String errorMsgJson) {
        List<ActivityResultDataInfo> resultDataInfoList = this.buildResultDataInfoList(paramInfo, resultStatus);
        if (CollectionUtils.isEmpty(resultDataInfoList) || StringUtils.isBlank(errorMsgJson)) {
            return resultDataInfoList;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Map<String/** error_list */, List<MTActivityErrorResult>> errorMsgMap = objectMapper.readValue(errorMsgJson, new TypeReference<Map<String, List<MTActivityErrorResult>>>() {
            });
            if (MapUtils.isEmpty(errorMsgMap)) {
                return resultDataInfoList;
            }

            List<MTActivityErrorResult> activityErrorMsgResult = errorMsgMap.get(ProjectConstant.MT_ERROR_RESPONSE_ERROR_LIST_KEY);
            if (CollectionUtils.isEmpty(activityErrorMsgResult)) {
                return resultDataInfoList;
            }
            Map<String, String> errorActivityMsgMap = activityErrorMsgResult.stream().collect(Collectors.toMap(error -> error.getAct_id().toString(), MTActivityErrorResult::getError_msg));
            resultDataInfoList.forEach(info -> {
                String msg = errorActivityMsgMap.get(info.getChannelActivityId());
                if (StringUtils.isNotBlank(msg) && msg.equals(ProjectConstant.ACT_DELETE_EXIST_MSG)) {
                    info.setData(new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(msg));
                } else if (StringUtils.isNotBlank(msg)) {
                    info.setData(new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg(msg));
                }
            });
        } catch (IOException e) {
            log.error("json analysis error. msg:{} , param:{}", e.getMessage(), errorMsgJson, e);
        }

        return resultDataInfoList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode
    static class MTActivityErrorResult {
        private Long act_id;
        private String error_msg;
    }
}
