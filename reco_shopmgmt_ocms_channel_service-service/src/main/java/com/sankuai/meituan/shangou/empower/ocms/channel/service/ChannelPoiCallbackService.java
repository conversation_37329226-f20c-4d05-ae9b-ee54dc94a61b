package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelAddAppCallbackRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiBindUnbindCallbackRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiCallbackRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.SetMtTokenCacheRequest;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/24 19:28
 * @Description:
 */
public interface ChannelPoiCallbackService {


    /**
     * 同步渠道门店信息
     * @param request
     * @return
     */
    ResultStatus syncChannelPoi(ChannelPoiCallbackRequest request);

    /**
     * 同步解绑渠道门店信息
     * @param request
     * @return
     */
    ResultStatus unBindChannelPoi(ChannelPoiBindUnbindCallbackRequest request);


    /**
     * 应用市场申请信息推送
     * @param request
     * @return
     */
    ResultStatus applyApp(ChannelAddAppCallbackRequest request);

    /**
     * 应用市场审核结果推送
     * @param request
     * @return
     */
    ResultStatus applyAppCall(ChannelAddAppCallbackRequest request);

    /**
     * 设置美团token到缓存
     * @param request
     * @return
     */
    ResultStatus setMtTokenCache(SetMtTokenCacheRequest request);
}
