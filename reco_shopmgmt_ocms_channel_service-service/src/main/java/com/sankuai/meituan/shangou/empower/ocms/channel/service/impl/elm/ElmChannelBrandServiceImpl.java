package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelBrandService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.BrandStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BrandInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetBrandResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.RecommendBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.RecommendBrandResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: chenzhiyang
 * @date: 2019/4/8
 * @time: 3:41 PM
 */
@Service("elmChannelBrandService")
public class ElmChannelBrandServiceImpl implements ChannelBrandService {

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CommonLogger log;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Override
    public GetBrandResponse batchGetBrand(GetBrandRequest req) {
        GetBrandResponse response = new GetBrandResponse().setStatus(ResultGenerator.genSuccessResult());
        long tenantId = req.getBaseInfo().getTenantId();
        int channelId = req.getBaseInfo().getChannelId();
        long storeId = req.getStoreId();
        try {
            List<CopAccessConfigDO> copAccessConfigDOS = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
            if (CollectionUtils.isEmpty(copAccessConfigDOS)) {
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取租户品牌数据失败"));
            }

            Map<String, Object> brandMap = new HashMap<>();
            if (StringUtils.isNumeric(req.getCategoryId())) {
                brandMap.put("cat3_id", Long.parseLong(req.getCategoryId()));
            }
            brandMap.put("keyword", req.getBrandName());
            brandMap.put("page", req.getPageNum());

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId))
                    .setAppId(copAccessConfigDOS.get(0).getAppId());

            ChannelResponseDTO<String> result = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.BRAND_LIST, baseRequest, brandMap);
            if (Objects.isNull(result)) {
                return response.setStatus(ResultGenerator.genFailResult("获取品牌信息失败！"));
            }
            if (!result.isSuccess()) {
                return response.setStatus(ResultGenerator.genFailResult("获取品牌信息失败！" + result.getErrorMsg()));
            }
            Map brandResultMap = JSON.parseObject(result.getCoreData());
            int totalNum = (int) brandResultMap.get("count");
            int pageNum = (int) brandResultMap.get("page_num");
            int pageSize = (int) brandResultMap.get("page_size");
            int totalPage = (int) brandResultMap.get("max_page_num");
            JSONArray brandJSONArray = (JSONArray) brandResultMap.get("detail");
            PageInfo page = new PageInfo(pageNum, pageSize, totalPage, totalNum);
            List<BrandInfo> brandInfoList = brandJSONArray.toJavaList(BrandInfo.class);
            if(CollectionUtils.isNotEmpty(brandInfoList)){
                brandInfoList.forEach( b -> b.setBrandStatus(BrandStatusEnum.VALID.getValue()));
            }
            response.setBrandInfoList(brandInfoList);
            response.setPageInfo(page);
        } catch (Exception e) {
            response.setStatus(ResultGenerator.genFailResult("获取品牌信息失败！"));
        }
        return response;
    }

    @Override
    public RecommendBrandResponse getRecommendBrand(RecommendBrandRequest req){
        RecommendBrandResponse recommendBrandResponse = new RecommendBrandResponse();
        recommendBrandResponse.setStatus(ChannelStatus.build(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(),ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg()));
        return recommendBrandResponse;
    }
}
