package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mock;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/9/21
 **/
@Component
public class MockOrderChannelUtil {
    public Map<String, Object> mockMtOrderDetail(GetChannelOrderDetailRequest request){
        String orderDetailJsonString = MccConfigUtil.getMtOrderDetailResult();
        orderDetailJsonString = orderDetailJsonString.replace("orderId", request.getOrderId());
        return JSON.parseObject(orderDetailJsonString);
    }
}
