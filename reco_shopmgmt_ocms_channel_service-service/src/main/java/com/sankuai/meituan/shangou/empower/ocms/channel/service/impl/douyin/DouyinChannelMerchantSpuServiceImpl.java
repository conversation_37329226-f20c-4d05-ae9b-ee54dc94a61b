package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelGetProductDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelProductDetailResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelSpuUpdateStoreCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.FreightDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.FreightListResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.FreightTemplateQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.SpuResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.CustomChannelSkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.BatchGetFreightTemplateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.UpdateProductStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.BatchFreightTemplateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleUpdateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuUpdateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelMerchantSpuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultCodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Author: luokai14
 * @Date: 2023/12/18 3:20 下午
 * @Mail: <EMAIL>
 */
@Slf4j
@Service("dyChannelMerchantSpuService")
public class DouyinChannelMerchantSpuServiceImpl implements ChannelMerchantSpuService {

    @Autowired
    private DouyinChannelGateService douyinChannelGateService;

    @Autowired
    private BaseConverterService baseConverterService;


    @Override
    public MerchantSpuCreateResponse createSpu(MerchantSpuCreateRequest request) {
        return null;
    }

    @Override
    public MerchantSpuUpdateResponse updateSpu(MerchantSpuUpdateRequest request) {
        return null;
    }

    @Override
    public MerchantSpuDeleteResponse deleteSpu(MerchantSpuDeleteRequest request) {
        return null;
    }

    @Override
    public MerchantSpuDetailResponse getSpuDetail(MerchantSpuDetailRequest request) {

        Preconditions.checkArgument(StringUtils.isNotBlank(request.getCustomSpuId()) || StringUtils.isBlank(request.getChannelSpuId()),
                "channelSpuId和customSpuId不能都为空");
        MerchantSpuDetailResponse result = new MerchantSpuDetailResponse();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        try {

            ChannelGetProductDetailDTO param = new ChannelGetProductDetailDTO();
            if (Objects.nonNull(request.getChannelSpuId())){
                param.setProduct_id(Long.valueOf(request.getChannelSpuId()));
            }
            param.setOut_product_id(request.getCustomSpuId());
            // 调用渠道接口
            ChannelResponseDTO<ChannelProductDetailResult> channelResponse =
                    douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SPU_DETAIL, baseRequest, param);

            if (!channelResponse.isSuccess()) {
                result.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), channelResponse.getAggregateErrorMsg()));
            } else {
                if(channelResponse.getCode() != null){
                    result.setMerchantSpuDTO(channelResponse.getCoreData().toMerchantSpuDTO());
                }
                result.setStatus(ChannelStatus.buildSuccess());
            }
        } catch (Exception e) {
            String message = String.format("DouyinChannelMerchantSpuServiceImpl.getSpuDetail 服务异常, request:%s,message:%s",request,e.getMessage());
            log.error(message, e);
            result.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), e.getMessage()));
        }
        return result;
    }

    @Override
    public MerchantSpuDetailResponse getSpuDetailSingle(MerchantSpuDetailRequest request) {
        return getSpuDetail(request);
    }

    @Override
    public MerchantSpuCreateResponse getSpuCreateStatus(MerchantSpuDetailRequest request) {
        return null;
    }
    
    @Override
    public MerchantSpuSingleCreateResponse singleCreateSpu(MerchantSpuSingleCreateRequest request) {
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        MerchantSpuSingleCreateResponse response = new MerchantSpuSingleCreateResponse();
        MerchantSpuResult result = new MerchantSpuResult();
        try {
            ChannelResponseDTO<SpuResult> channelResponse = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SPU_CREATE,
                    baseRequest, DouyinConvertUtil.buildMerchantSpuCreateDTO(request.getMerchantSpuDTO(), baseRequest));
            if (!channelResponse.isSuccess()) {
                result = buildFailResult(channelResponse);
            } else {
                result = buildSuccessResult(channelResponse);
            }
        } catch (Exception e) {
            log.error("DouyinChannelMerchantSpuServiceImpl.singleCreateSpu 服务异常, data:{}", request, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        response.setSpuResult(result);
        response.setStatus(ChannelStatus.buildSuccess());
        return response;
    }
    


    @Override
    public MerchantSpuSingleUpdateResponse singleUpdateSpu(MerchantSpuSingleUpdateRequest request) {
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        MerchantSpuSingleUpdateResponse response = new MerchantSpuSingleUpdateResponse();
        MerchantSpuResult result = new MerchantSpuResult();
        try {
            ChannelResponseDTO<SpuResult> channelResponse = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SPU_UPDATE,
                    baseRequest, DouyinConvertUtil.buildMerchantSpuCreateDTO(request.getMerchantSpuDTO(), baseRequest));
            if (!channelResponse.isSuccess()) {
                result = buildFailResult(channelResponse);
            } else {
                result = buildSuccessResult(channelResponse);
            }
        } catch (Exception e) {
            log.error("DouyinChannelMerchantSpuServiceImpl.singleUpdateSpu 服务异常, data:{}", request, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        response.setSpuResult(result);
        response.setStatus(ChannelStatus.buildSuccess());
        return response;
    }

    @Override
    public MerchantSpuSingleDeleteResponse singleDeleteSpu(MerchantSpuSingleDeleteRequest request){
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        MerchantSpuSingleDeleteResponse response = new MerchantSpuSingleDeleteResponse();
        MerchantSpuResult result = new MerchantSpuResult();
        try {
            ChannelSpuDeleteDTO channelSpuDeleteDTO = new ChannelSpuDeleteDTO();
            channelSpuDeleteDTO.setProduct_id(Long.valueOf(request.getChannelSpuId()));
            if (StringUtils.isNotEmpty(request.getCustomSpuId())) {
                channelSpuDeleteDTO.setOut_product_id(Long.valueOf(request.getCustomSpuId()));
            }
            channelSpuDeleteDTO.setDelete_forever(request.getDeleteForever());
            ChannelResponseDTO<SpuResult> channelResponse = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SPU_DELETE, baseRequest, channelSpuDeleteDTO);
            if (!deleteSuccess(channelResponse)) {
                result = buildFailResult(channelResponse);
            } else {
                result = buildSuccessResult(channelResponse);
            }
        } catch (Exception e) {
            log.error("DouyinChannelMerchantSpuServiceImpl.singleDeleteSpu 服务异常, data:{}", request, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        response.setSpuResult(result);
        response.setStatus(ChannelStatus.buildSuccess());
        return response;
    }
    
    private boolean deleteSuccess(ChannelResponseDTO<SpuResult> channelResponse){
        if (channelResponse.isSuccess()) {
            return true;
        }

        // 删除幂等处理
        if (channelResponse.getSub_msg() != null
                && channelResponse.getSub_msg().contains(DouyinConstant.TENANT_PRODUCT_DELETED)) {
            return true;
        }

        return false;
    }


    @Override
    public MerchantSpuSingleUpdateResponse singleUpdateSpuStoreCategory(UpdateProductStoreCategoryRequest request){
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        MerchantSpuSingleUpdateResponse response = new MerchantSpuSingleUpdateResponse();
        MerchantSpuResult result = new MerchantSpuResult();
        try {
            ChannelResponseDTO<SpuResult> channelResponse = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SPU_PARTIAL_UPDATE, baseRequest, ChannelSpuUpdateStoreCategoryDTO.of(request.getChannelSpuId(), request.getChannelStoreCategoryList()));
            if (!channelResponse.isSuccess()) {
                result = buildFailResult(channelResponse);
            } else {
                result = buildSuccessResult(channelResponse);
            }
        } catch (Exception e) {
            log.error("DouyinChannelMerchantSpuServiceImpl.singleUpdateSpuStoreCategory 服务异常, data:{}", request, e);
            result.buildResult(ResultCode.FAIL.getCode(), e.getMessage());
        }
        response.setSpuResult(result);
        response.setStatus(ChannelStatus.buildSuccess());
        return response;
    }


    /**
     * 获取售后服务模板列表
     */
    @Override
    public BatchFreightTemplateResponse getFreightTemplateList(BatchGetFreightTemplateRequest request){

        BatchFreightTemplateResponse response = new BatchFreightTemplateResponse();
        response.setStatus(ChannelStatus.buildSuccess());

        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo().convert2Base());
        FreightTemplateQueryParam param = new FreightTemplateQueryParam();
        param.setName(request.getName());
        param.setSize(String.valueOf(request.getPageSize()));
        param.setPage(String.valueOf(request.getPageNum()));
        try {
            ChannelResponseDTO<FreightListResult> channelResponse = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.GET_FREIGHT_TEMPLATE_LIST, baseRequest, param);
            if (!channelResponse.isSuccess()) {
                response.setStatus(ChannelStatus.build(channelResponse.getCode(), channelResponse.getAggregateErrorMsg()));
            } else {
                if (channelResponse.getCoreData() != null && CollectionUtils.isNotEmpty(channelResponse.getCoreData().getList())) {
                    List<FreightDTO> validFreights = Fun.filter(channelResponse.getCoreData().getList(),
                            freight -> freight.getTemplate() != null);
                    response.setFreightTemplateDTOList(Fun.map(validFreights, freight-> freight.getTemplate().toDTO()));
                }
            }
        }
        catch (Exception e) {
            log.error("DouyinChannelMerchantSpuServiceImpl.getFreightTemplateList 服务异常, data:{}", request, e);
            response.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), e.getMessage()));
        }
        return response;
    }


    private MerchantSpuResult buildFailResult(ChannelResponseDTO<SpuResult> channelResponse) {
        MerchantSpuResult result = new MerchantSpuResult();
        result.buildResult(ResultCodeUtils.parseErrorCode(channelResponse.getCode()), channelResponse.getAggregateErrorMsg(), channelResponse.getUnifyUnifiedError());
        return result;
    }


    private MerchantSpuResult buildSuccessResult(ChannelResponseDTO<SpuResult> channelResponse){
        MerchantSpuResult result = new MerchantSpuResult();
        result.buildResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
        SpuResult spuResult = channelResponse.getCoreData();
        
        if (spuResult == null) {
            return result;
        }
        result.setChannelSpuId(spuResult.getProduct_id());
        result.setChannelResultInfo(spuResult.getProduct_id());
        if (CollectionUtils.isEmpty(spuResult.getSku())) {
            return result;
        }
        // 设置sku信息
        List<CustomChannelSkuKey> customChannelSkuKeys = Fun.map(spuResult.getSku(), skuCreateResult -> new CustomChannelSkuKey(skuCreateResult.getOuter_sku_id(), skuCreateResult.getSku_id()));
        result.setSkuResultList(Lists.newArrayList(customChannelSkuKeys));
        return result;
    }
}
