// Copyright (C) 2019 Meituan
// All rights reserved
package com.sankuai.meituan.shangou.empower.ocms.channel.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2019/11/18 下午3:19
 **/
public class InvokeChannelTooMuchException extends RuntimeException {
    private long waitTimeMills;
    public InvokeChannelTooMuchException(long waitTimeMills) {
        super();
        this.waitTimeMills = waitTimeMills;
    }

    public long getWaitTimeMills() {
        return waitTimeMills;
    }
}
