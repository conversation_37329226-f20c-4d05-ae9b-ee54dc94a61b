package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.YzSkuPriceAndStockUpdateInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostYZEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.YzConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.tenant.TenantRemoteService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemDetailGet;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemUpdateBranchSku;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemDetailGetParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemDetailGetResult;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemUpdateBranchSkuResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode.INVALID_PARAM;

/**
 * @Author: wangyihao04
 * @Date: 2022/11/29 10:41 AM
 * @Mail: <EMAIL>
 */
@Slf4j
@Service
public class YzToolSpuCommonService extends YouZanToolBaseService {
    /**
     * 获取总部商品详情
     * @param tenantId
     * @param itemNo
     * @return
     * @throws SDKException
     */

    @Autowired
    TenantRemoteService tenantRemoteService;

    public YouzanItemDetailGetResult.YouzanItemDetailGetResultData getSpuDetail(Long tenantId, Long itemNo) throws SDKException {
        AppMessage appMessage = getMainAppMessage(tenantId);
        YouzanItemDetailGet itemDetailGet = buildYouzanItemDetailGet(null, itemNo);
        YouzanItemDetailGetResult result = getResult4YouZanByRhino(ChannelPostYZEnum.QUERY_SPU_DETAIL, appMessage, itemDetailGet, YouzanItemDetailGetResult.class);
        log.info("查询有赞商品详情:tenantId {}, itemNo {}, result {}.", tenantId, itemNo, JSON.toJSONString(result));
        if (!result.getSuccess()){
            throw new BizException(String.format("查询有赞总部商品详情失败,  %s", result.getMessage()));
        }
        return result.getData();
    }

    /**
     * 目前erp走单规格，非erp走多规格
     * @param tenantId
     * @return
     */
    public boolean needYzSingeSpec(long tenantId) {
        return tenantRemoteService.isErpSpuTenant(tenantId);
    }


    /**
     * 获取网店商品详情
     * @param tenantId
     * @param storeId
     * @param itemNo
     * @return
     * @throws SDKException
     */
    public YouzanItemDetailGetResult.YouzanItemDetailGetResultData
    getStoreSpuDetail(Long tenantId, Long storeId, Long itemNo) throws SDKException {
        AppMessage appMessage = getMainAppMessage(tenantId);

        YouzanItemDetailGet itemDetailGet = buildYouzanItemDetailGet(getYzChannelPoiId(tenantId, storeId), itemNo);
        YouzanItemDetailGetResult result = getResult4YouZanByRhino(ChannelPostYZEnum.QUERY_SPU_DETAIL, appMessage,
                itemDetailGet, YouzanItemDetailGetResult.class);
        log.info("查询有赞商品详情:tenantId {}, storeId {}, itemNo {}, result {}.", tenantId, storeId, itemNo, JSON.toJSONString(result));
        if (!result.getSuccess()){
            throw new BizException(String.format("查询有赞网店商品详情失败, %s", result.getMessage()));
        }
        return result.getData();
    }

    /**
     * 获取有赞的网店id
     * @param tenantId
     * @param storeId
     * @return
     */
    protected Long getYzChannelPoiId(Long tenantId, Long storeId) {
        Map<Long, ChannelStoreDO> channelStoreDOMap = queryChannelStoreInfo(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(), Lists.newArrayList(storeId));
        if (!channelStoreDOMap.containsKey(storeId)) {
            throw new BizException(ResultCode.CHANNEL_POI_CODE_INVALID.getCode(), "未绑定渠道门店");
        }
        String channelPoiCode = channelStoreDOMap.get(storeId).getChannelPoiCode();
        if (StringUtils.isBlank(channelPoiCode)){
            throw new BizException(ResultCode.CHANNEL_POI_CODE_INVALID.getCode(), "渠道门店为空");
        }
        if (!StringUtils.isNumeric(channelPoiCode)){
            throw new BizException(ResultCode.CHANNEL_POI_CODE_INVALID.getCode(), "有赞渠道门店必须为数值类型");
        }
        return Long.valueOf(channelPoiCode);
    }


    /**
     *
     * @param kdtId
     * @param itemNo
     * @return
     */
    private YouzanItemDetailGet buildYouzanItemDetailGet(Long kdtId, @NotNull Long itemNo) {
        Preconditions.checkArgument(itemNo != null , "有赞商品id必填");
        YouzanItemDetailGetParams itemDetailGetParams = new YouzanItemDetailGetParams();
        //设置
        itemDetailGetParams.setItemId(itemNo);
        itemDetailGetParams.setNodeKdtId(kdtId);

        YouzanItemDetailGet itemDetailGet = new YouzanItemDetailGet();
        itemDetailGet.setAPIParams(itemDetailGetParams);
        return itemDetailGet;
    }

    public YouzanItemUpdateBranchSkuResult updatePriceAndStockInfo(Long tenantId, Long storeId,
                                                                   List<YzSkuPriceAndStockUpdateInfo> priceAndStockUpdateInfos
    ) throws SDKException {
        AppMessage appMessage = getMainAppMessage(tenantId);
        Map<Long, ChannelStoreDO> storeDOMap = queryChannelStoreInfo(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(), Lists.newArrayList(storeId));
        Preconditions.checkArgument(storeDOMap.containsKey(storeId), "门店未绑定有赞渠道");

        YouzanItemUpdateBranchSku youzanItemUpdateBranchSku = YzConverterUtil.skuPriceAndStockUpdateToYzAPI(priceAndStockUpdateInfos,
                    storeDOMap.get(storeId).getChannelPoiCode());
        YouzanItemUpdateBranchSkuResult result = getResult4YouZanByRhino(ChannelPostYZEnum.UPDATE_SPU_STOCK_AND_PRICE,
                appMessage, youzanItemUpdateBranchSku, YouzanItemUpdateBranchSkuResult.class);
        if (!result.getSuccess()) {
            throw new BizException(result.getCode(), String.format("有赞网店商品更新价格库存服务失败, %s", result.getMessage()));
        }

        log.info("门店商品价格 & 库存更新操作记录：youzanItemUpdateBranchSku {}, result {}.", youzanItemUpdateBranchSku, JSON.toJSONString(result));

        return result;
    }

    public YouzanItemUpdateBranchSkuResult updatePriceAndStockInfoNoSku(Long tenantId, Long storeId,
                                                                   List<YzSkuPriceAndStockUpdateInfo> priceAndStockUpdateInfos

    ) throws SDKException {
        AppMessage appMessage = getMainAppMessage(tenantId);
        Map<Long, ChannelStoreDO> storeDOMap = queryChannelStoreInfo(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(), Lists.newArrayList(storeId));
        Preconditions.checkArgument(storeDOMap.containsKey(storeId), "门店未绑定有赞渠道");

        YouzanItemUpdateBranchSku youzanItemUpdateBranchSku = YzConverterUtil.skuPriceAndStockUpdateToYzAPINoSku(priceAndStockUpdateInfos,
                storeDOMap.get(storeId).getChannelPoiCode());

        YouzanItemUpdateBranchSkuResult result = getResult4YouZanByRhino(ChannelPostYZEnum.UPDATE_SPU_STOCK_AND_PRICE,
                appMessage, youzanItemUpdateBranchSku, YouzanItemUpdateBranchSkuResult.class);
        if (!result.getSuccess()) {
            throw new BizException(result.getCode(), String.format("有赞网店商品更新价格库存服务失败, %s", result.getMessage()));
        }

        log.info("门店商品价格 & 库存更新操作记录：youzanItemUpdateBranchSku {}, result {}.", youzanItemUpdateBranchSku, JSON.toJSONString(result));

        return result;
    }

    public YouzanItemUpdateBranchSkuResult updateStockInfo(Long tenantId, Long storeId,
                                                                   List<YzSkuPriceAndStockUpdateInfo> priceAndStockUpdateInfos)throws SDKException {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(priceAndStockUpdateInfos), INVALID_PARAM.getMsg());
        priceAndStockUpdateInfos.forEach(it -> Preconditions.checkArgument(Objects.isNull(it.getPrice()) && Objects.nonNull(it.getStockQty()), INVALID_PARAM.getMsg()));
        AppMessage appMessage = getMainAppMessage(tenantId);
        Map<Long, ChannelStoreDO> storeDOMap = queryChannelStoreInfo(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(), Lists.newArrayList(storeId));
        Preconditions.checkArgument(storeDOMap.containsKey(storeId), "门店未绑定有赞渠道");
        YouzanItemUpdateBranchSku youzanItemUpdateBranchSku = YzConverterUtil.skuPriceAndStockUpdateToYzAPI(priceAndStockUpdateInfos, storeDOMap.get(storeId).getChannelPoiCode());
        YouzanItemUpdateBranchSkuResult result = getResult4YouZanByRhino(ChannelPostYZEnum.UPDATE_SPU_STOCK_AND_PRICE,
            appMessage, youzanItemUpdateBranchSku, YouzanItemUpdateBranchSkuResult.class);
        if (!result.getSuccess()){
            throw new BizException(result.getMessage());
        }

        log.info("门店商品价格 & 库存更新操作记录：youzanItemUpdateBranchSku {}, result {}.", youzanItemUpdateBranchSku, JSON.toJSONString(result));

        return result;
    }

    public YouzanItemUpdateBranchSkuResult updateStockInfoNoSku(Long tenantId, Long storeId,
            List<YzSkuPriceAndStockUpdateInfo> priceAndStockUpdateInfos)throws SDKException {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(priceAndStockUpdateInfos), INVALID_PARAM.getMsg());
        priceAndStockUpdateInfos.forEach(it -> Preconditions.checkArgument(Objects.isNull(it.getPrice()) && Objects.nonNull(it.getStockQty()), INVALID_PARAM.getMsg()));
        AppMessage appMessage = getMainAppMessage(tenantId);
        Map<Long, ChannelStoreDO> storeDOMap = queryChannelStoreInfo(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(), Lists.newArrayList(storeId));
        Preconditions.checkArgument(storeDOMap.containsKey(storeId), "门店未绑定有赞渠道");

        YouzanItemUpdateBranchSku youzanItemUpdateBranchSku = YzConverterUtil.skuPriceAndStockUpdateToYzAPINoSku(priceAndStockUpdateInfos,
                storeDOMap.get(storeId).getChannelPoiCode());
        YouzanItemUpdateBranchSkuResult result = getResult4YouZanByRhino(ChannelPostYZEnum.UPDATE_SPU_STOCK_AND_PRICE,
                appMessage, youzanItemUpdateBranchSku, YouzanItemUpdateBranchSkuResult.class);
        if (!result.getSuccess()){
            throw new BizException(result.getMessage());
        }

        log.info("门店商品价格 & 库存更新操作记录：updateStockInfoNoSku {}, result {}.", youzanItemUpdateBranchSku, JSON.toJSONString(result));

        return result;
    }

}
