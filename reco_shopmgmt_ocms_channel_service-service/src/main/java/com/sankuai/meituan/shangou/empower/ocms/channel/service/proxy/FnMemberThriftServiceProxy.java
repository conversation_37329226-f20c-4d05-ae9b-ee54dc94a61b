package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.RateLimit;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.MethodPerformance;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.MetricConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.BaseRhinoException;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.request.mcard.AddMemberElectronicCardRequest;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.request.mcard.MemberCardInfoQueryRequest;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.response.mcard.AddCardResponse;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.response.mcard.MemberCardInfoQueryResponse;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.service.MemberThriftService;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 赋能会员服务代理
 * @Author：<EMAIL>
 * @Date: 2019/7/2 4:35 PM
 */
@Rhino
@Service
public class FnMemberThriftServiceProxy {
    //@Autowired
    private MemberThriftService memberThriftService;

    @Resource
    private CommonLogger log;

    @Degrade(rhinoKey = "getMemberCardInfo", fallBackMethod = "getMemberCardInfoFallback", isDegradeOnException = true,
        timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
        errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "getMemberCardInfo", fallBackMethod = "getMemberCardInfoFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public MemberCardInfoQueryResponse getMemberCardInfo(MemberCardInfoQueryRequest request){

        try {
            MemberCardInfoQueryResponse response = memberThriftService.getMemberCardInfo(request);
            log.info("FnMemberThriftServiceProxy.getMemberCardInfo, request:{}, response:{}",
                request, response);
            return response;
        } catch (TException e) {
            log.error("FnMemberThriftServiceProxy.getMemberCardInfo, 服务异常，request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    public MemberCardInfoQueryResponse getMemberCardInfoFallback(MemberCardInfoQueryRequest request){
        log.warn("FnMemberThriftServiceProxy.getMemberCardInfo 降级，request:{}",request);
        return null;
    }


    @Degrade(rhinoKey = "createMemberCard", fallBackMethod = "createMemberCardFallback", isDegradeOnException = true,
        timeoutInMilliseconds = ProjectConstant.RHINO_TIMEOUT,
        errorThresholdPercentage = ProjectConstant.RHINO_ERROR_PERCENTAGE)
    @RateLimit(rhinoKey = "createMemberCard", fallBackMethod = "createMemberCardFallback")
    @MethodPerformance(MetricConstant.DEPENDENT_SERVICE)
    public AddCardResponse createMemberCard(AddMemberElectronicCardRequest request){

        try {
            AddCardResponse response = memberThriftService.addMemberAndElectronicCard(request);
            log.info("FnMemberThriftServiceProxy.createMemberCard, request:{}, response:{}",
                request, response);
            return response;
        } catch (TException e) {
            log.error("FnMemberThriftServiceProxy.createMemberCard, 服务异常，request:{}", request, e);
            throw new BaseRhinoException();
        }
    }

    public AddCardResponse createMemberCardFallback(AddMemberElectronicCardRequest request){
        log.warn("FnMemberThriftServiceProxy.createMemberCard 降级，request:{}",request);
        return null;
    }
}