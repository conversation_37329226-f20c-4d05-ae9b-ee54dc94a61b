package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpecTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStoreStockInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelStockService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultSpuDataUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Preconditions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 饿了么渠道商品库存内部服务接口
 *
 * <AUTHOR>
 * @create 2019-01-09 下午4:41
 **/
@Service("elmChannelStockService")
public class ElmChannelStockServiceImpl implements ChannelStockService {
    public static final int PRICE_UPDATE_MAX_COUNT = 100;
    public static final long INIT_SKU_OFFSET_ID = 0L;
    public static final long OFFSET_MODEL_DEFAULT_PAGE = 1;

    @Value("${elm.url.base}")
    private String elmUrlBase;

    @Value("${elm.url.skulist}")
    private String skuList;

    @Resource
    private ElmConverterService elmConverterService;

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CommonLogger log;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private BaseConverterService baseConverterService;

    @Override
    public ResultSpuData updateStockBySpu(SpuStockRequest request) {
        if(Objects.isNull(request.getParamList())){
            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
            return ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.VALIDATE_ERROR, "");
        }

        // 多规格租户下面的单规格商品更新库存
        SkuStockMultiChannelRequest skuStockMultiChannelRequest = new SkuStockMultiChannelRequest();
        skuStockMultiChannelRequest.setTenantId(request.getBaseInfo().getTenantId());

        // 多规格租户下面的多规格商品更新库存
        SpuStockRequest spuStockRequest = new SpuStockRequest();
        spuStockRequest.setBaseInfo(request.getBaseInfo());

        List<SpuStockDTO> spuStockDTOList = new ArrayList<>();
        List<SkuStockMultiChannelDTO> skuStockMultiChannelList = new ArrayList<>();

        request.getParamList().forEach(data->{
            List<SkuInSpuStockDTO> skuInSpuStockDTOList = new ArrayList<>();
            data.getSkuStockInfo().forEach(item->{
                int specType = item.getSpecType();
                if(specType == SpecTypeEnum.MULTI.getCode()){
                    skuInSpuStockDTOList.add(item);
                }else if(specType == SpecTypeEnum.SINGLE.getCode() || specType == SpecTypeEnum.OLD_SINGLE.getCode()){
                    skuStockMultiChannelList.add(new SkuStockMultiChannelDTO()
                            .setChannelId(request.getBaseInfo().getChannelId())
                            .setStoreId(data.getStoreId())
                            .setChannelSkuId(item.getCustomSkuId())
                            .setSkuId(item.getCustomSkuId())
                            .setOperator(item.getOperator())
                            .setStockQty(item.getStockQty())
                            .setTimestamp(item.getTimestamp()));
                }
            });

            // 多规格商品信息
            if(CollectionUtils.isNotEmpty(skuInSpuStockDTOList)) {
                spuStockDTOList.add(new SpuStockDTO().setStoreId(data.getStoreId())
                        .setCustomSpuId(data.getCustomSpuId())
                        .setSkuStockInfo(skuInSpuStockDTOList)
                );
            }
        });
        // 多规格商品requset
        spuStockRequest.setParamList(spuStockDTOList);

        // 单规格商品request
        skuStockMultiChannelRequest.setSkuStockList(skuStockMultiChannelList);

        return prepareResult(skuStockMultiChannelRequest, spuStockRequest);
    }

    private ResultSpuData prepareResult(SkuStockMultiChannelRequest skuStockMultiChannelRequest, SpuStockRequest spuStockRequest) {

        ResultSpuData resultMultiSpuData = new ResultSpuData();
        ResultSpuData resultSingleSpecData = new ResultSpuData();
        if (CollectionUtils.isNotEmpty(spuStockRequest.getParamList())) {
            //log.info("ElmChannelStockServiceImpl.updateStockBySpu 多规格商品上行同步请求详情，spuStockRequest:{}", spuStockRequest);
            resultMultiSpuData = updateMultiSpecStockBySpu(spuStockRequest);
        }

        if (CollectionUtils.isNotEmpty(skuStockMultiChannelRequest.getSkuStockList())) {
            //log.info("ElmChannelStockServiceImpl.updateStockBySpu 单规格商品上行同步请求详情，skuStockMultiChannelRequest:{}", skuStockMultiChannelRequest);
            resultSingleSpecData = updateSingleSpecStock(skuStockMultiChannelRequest);
        }

        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        if (CollectionUtils.isNotEmpty(resultMultiSpuData.getErrorData())) {
            resultData.getErrorData().addAll(resultMultiSpuData.getErrorData());
        }

        if (CollectionUtils.isNotEmpty(resultMultiSpuData.getSucData())) {
            resultData.getSucData().addAll(resultMultiSpuData.getSucData());
        }

        if (CollectionUtils.isNotEmpty(resultSingleSpecData.getErrorData())) {
            resultData.getErrorData().addAll(resultSingleSpecData.getErrorData());
        }

        if (CollectionUtils.isNotEmpty(resultSingleSpecData.getSucData())) {
            resultData.getSucData().addAll(resultSingleSpecData.getSucData());
        }

        log.info("ElmChannelStockServiceImpl.prepareResult 单规格商品上行同步请求详情结果，resultData:{}", resultData);
        return resultData;
    }

    public ResultSpuData updateMultiSpecStockBySpu(SpuStockRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        BaseRequestSimple baseInfo = request.getBaseInfo();

        request.getParamList().forEach(data -> {
            List<SpuKey> bizKeyList = buildSpuKeyList(data);
            long storeId = data.getStoreId();
            try {
                BaseRequest baseRequest = new BaseRequest().setTenantId(baseInfo.getTenantId()).setChannelId(baseInfo.getChannelId()).setStoreIdList(Lists.newArrayList(storeId));

                // 业务参数转换
                List<ChannelSpecStockDetail> specStockList = elmConverterService.updateSpuStock(data.getSkuStockInfo());

                // 发送请求
                ChannelSpuStockUpdateDTO postData = ChannelSpuStockUpdateDTO.builder()
                        .shop_id(String.valueOf(storeId))
                        .custom_sku_id(data.getCustomSpuId())
                        .spec_stock_list(specStockList)
                        .build();
                Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.SPEC_STOCK_UPDATE, baseRequest, postData);
                log.info("ElmChannelStockServiceImpl.updateStockBySpu 多规格, baseRequest:{}, postData:{}, postResult:{}", baseRequest, postData, postResult);

                // 组装返回结果
                boolean isErpType = Objects.nonNull(request.getBaseInfo()) ? request.getBaseInfo().isErpTenant() : false;
                ResultSpuDataUtils.combinePartResultData(resultData, postResult, bizKeyList, ChannelTypeEnum.ELEM, isErpType);
            } catch (IllegalArgumentException e) {
                log.warn("ElmChannelStockServiceImpl.updateStockBySpu 多规格参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.VALIDATE_ERROR.getDesc(), storeId, bizKeyList);
            } catch (InvokeChannelTooMuchException e) {
                log.warn("ElmChannelStockServiceImpl.updateStockBySpu 多规格触发限频, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, "触发限频", storeId, bizKeyList);
            } catch (Exception e) {
                log.warn("ElmChannelStockServiceImpl.updateStockBySpu 多规格服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, "服务异常，请重试", storeId, bizKeyList);
            }
        });

        return resultData;
    }

    private List<SpuKey> buildSpuKeyList(SpuStockDTO data) {
        // 返回结果组装用标识
        List<SpuKey> bizKeyList = new ArrayList<>();
        SpuKey spuKey = new SpuKey().setCustomSpuId(data.getCustomSpuId()).setChannelSpuId(data.getChannelSpuId());
        List<SkuKey> skuKeys = new ArrayList<>();
        data.getSkuStockInfo().forEach(skuStock -> {
            SkuKey skuKey = new SkuKey().setCustomSkuId(skuStock.getCustomSkuId()).setChannelSkuId(skuStock.getChannelSkuId());
            skuKeys.add(skuKey);
        });
        spuKey.setSkus(skuKeys);
        bizKeyList.add(spuKey);
        return bizKeyList;
    }

    public ResultSpuData updateSingleSpecStock(SkuStockMultiChannelRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        // 按照storeid拆分
        Map<Long, List<SkuStockMultiChannelDTO>> skuStockMultiMap = request.getSkuStockList().stream()
                .collect(Collectors.groupingBy(SkuStockMultiChannelDTO::getStoreId));


        for (Map.Entry<Long, List<SkuStockMultiChannelDTO>> entry : skuStockMultiMap.entrySet()) {
            List<SkuStockMultiChannelDTO> skuStockList = entry.getValue();
            long storeId = entry.getKey();

            BaseRequest baseRequest = new BaseRequest().setTenantId(request.tenantId).setChannelId(request.skuStockList.get(0).channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            // 分页调用
            ListUtils.listPartition(skuStockList, PRICE_UPDATE_MAX_COUNT).forEach(data -> {
                // 返回结果组装用标识
                List<String> bizKeyList = data.stream().map(SkuStockMultiChannelDTO::getSkuId).collect(Collectors.toList());
                try {
                    // 业务参数转换
                    ChannelStockUpdateDTO postData = elmConverterService.updateStockMultiChannel(new SkuStockMultiChannelRequest().setSkuStockList(data));
                    // 调用渠道接口
                    Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.STOCK_UPDATE, baseRequest, postData);
                    log.info("ElmChannelStock.updateStockMultiChannel 单规格 postResult:{}", postResult);
                    // 组装返回结果
                    installResultData(resultData, postResult, bizKeyList);

                } catch (IllegalArgumentException e) {
                    log.warn("ElmChannelStockServiceImpl.updateStockMultiChannel 单规格参数校验失败, data:{}", data, e);
                    ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.VALIDATE_ERROR, storeId, bizKeyList);

                } catch (InvokeChannelTooMuchException e) {
                    log.warn("ElmChannelStockServiceImpl.updateStockMultiChannel 单规格触发限频, data:{}", data, e);
                    ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.TRIGGER_LIMIT, storeId, bizKeyList);
                } catch (Exception e) {
                    log.warn("ElmChannelStockServiceImpl.updateStockMultiChannel 单规格服务异常, data:{}", data, e);
                    ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, storeId, bizKeyList);
                }
            });
        }
        return resultData;
    }

    @Override
    public ResultData updateStockMultiChannel(SkuStockMultiChannelRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long storeId = request.skuStockList.get(0).storeId;
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.tenantId).setChannelId(request.skuStockList.get(0).channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        // 分页调用
        ListUtils.listPartition(request.skuStockList, PRICE_UPDATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuStockMultiChannelDTO::getSkuId).collect(Collectors.toList());

                // 业务参数转换
                ChannelStockUpdateDTO postData = elmConverterService.updateStockMultiChannel(new SkuStockMultiChannelRequest().setSkuStockList(data));
                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = elmChannelGateService.sendPostAppMapDto(ChannelPostELMEnum.STOCK_UPDATE, baseRequest, postData);
                log.info("ElmChannelStock.updateStockMultiChannel postResult:{}", postResult);
                // 组装返回结果
                if (MccConfigUtil.isELMStockParseResult()) {
                    installResultData(resultData, postResult, bizKeyList);
                } else {
                    ResultDataUtils.combineResultDataList(resultData, postResult, bizKeyList);
                }

            } catch (IllegalArgumentException e) {
                log.warn("ElmChannelStockServiceImpl.updateStockMultiChannel 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList, storeId);

            } catch (InvokeChannelTooMuchException e) {
                log.warn("ElmChannelStockServiceImpl.updateStockMultiChannel 触发限频, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList, storeId);
            } catch (Exception e) {
                log.warn("ElmChannelStockServiceImpl.updateStockMultiChannel 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList, storeId);
            }
        });
        return resultData;
    }

    @Override
    public BatchGetStockInfoResponse batchGetStockInfo(BatchGetStockInfoRequest request) {
        BatchGetStockInfoResponse response = new BatchGetStockInfoResponse().setQueryType(QueryTypeEnum.ID_OFFSET.getCode());
        // 获取渠道门店编码
        ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(), request.getStoreId());
        if (Objects.isNull(channelStoreDO) || StringUtils.isBlank(channelStoreDO.getChannelOnlinePoiCode())) {
            throw new IllegalArgumentException("channel store is not exist,storeId:" + request.getStoreId());
        }

        // 构造请求参数并请求
        Map<String, Object> bizParam = buildQueryStockParam(channelStoreDO.getChannelOnlinePoiCode(), request.getPageSize(), getSkuIdOffset(request));
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        baseRequest.setStoreIdList(com.google.common.collect.Lists.newArrayList(request.getStoreId()));
        ChannelResponseDTO<String> result = elmChannelGateService.sendPostAppDto(ChannelPostELMEnum.BATCH_GET_SKUINFO, baseRequest, bizParam);
        if (!result.isSuccess()) {
            throw new ChannelBizException("query elm sku stock error:" + result.getErrorMsg());
        }

        // 组装sku数据
        Map skuInfoMapData = JSON.parseObject(result.getCoreData());
        if (Objects.isNull(skuInfoMapData.get("list"))) {
            return response.setSkuStocks(Collections.emptyList()).setStatus(ResultGenerator.genSuccessResult());
        }

        JSONArray skuJSONArray = (JSONArray) skuInfoMapData.get("list");
        List<ChannelStockInfo> channelStockInfoList = skuJSONArray.toJavaList(ChannelStockInfo.class);

        if (org.apache.commons.collections.CollectionUtils.isEmpty(channelStockInfoList)) {
            return response.setSkuStocks(Collections.emptyList()).setStatus(ResultGenerator.genSuccessResult());
        }

        //将渠道原始返回对象转换成目标DTO
        List<ChannelSkuStockDTO> skuStockDTOS = convert2TargetDTO(channelStockInfoList);

        //构建分页信息、offset信息
        setPageInfo(skuInfoMapData, request.getPageSize(), response);

        return response.setSkuStocks(skuStockDTOS).setStatus(ResultGenerator.genSuccessResult());
    }

    /**
     * 将渠道原始返回对象转换成目标DTO
     *
     * @param channelStockInfoList
     * @return
     */
    private List<ChannelSkuStockDTO> convert2TargetDTO(List<ChannelStockInfo> channelStockInfoList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(channelStockInfoList)) {
            return Collections.emptyList();
        }

        List<ChannelSkuStockDTO> skuStockDTOS = Lists.newArrayList();
        for (ChannelStockInfo channelStockInfo : channelStockInfoList) {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(channelStockInfo.getSku_spec())) {
                ChannelSkuStockDTO skuStockDTO = new ChannelSkuStockDTO();
                skuStockDTO.setStockQty(channelStockInfo.getLeft_num());
                skuStockDTO.setCustomSpuId(channelStockInfo.getCustom_sku_id());
                skuStockDTO.setCustomSkuId(channelStockInfo.getCustom_sku_id());
                skuStockDTOS.add(skuStockDTO);
            } else {
                for (SkuQueryDetailResultDTO.SkuQuerySpecResultDTO specResultDTO : channelStockInfo.getSku_spec()) {
                    ChannelSkuStockDTO skuStockDTO = new ChannelSkuStockDTO();
                    skuStockDTO.setStockQty(specResultDTO.getLeft_num());
                    skuStockDTO.setCustomSpuId(channelStockInfo.getCustom_sku_id());
                    skuStockDTO.setCustomSkuId(specResultDTO.getSku_spec_custom_id());
                    skuStockDTOS.add(skuStockDTO);
                }
            }
        }
        return skuStockDTOS;
    }

    /**
     * 构建分页信息、offset信息
     *
     * @param skuInfoMapData
     * @param pageSize
     * @param response
     */
    private void setPageInfo(Map skuInfoMapData, int pageSize, BatchGetStockInfoResponse response) {
        if (Objects.isNull(skuInfoMapData) || Objects.isNull(skuInfoMapData.get("sku_id_offset"))) {
            throw new ChannelBizException("elm post response data error");
        }

        Long skuIdOffset = (Long) skuInfoMapData.get("sku_id_offset");
        Integer totalCount = Optional.ofNullable(skuInfoMapData.get("total")).map(v -> (Integer) v).orElse(0);
        Integer totalPage = Optional.ofNullable(skuInfoMapData.get("pages")).map(v -> (Integer) v).orElse(0);
        Integer page = Optional.ofNullable(skuInfoMapData.get("page")).map(v -> (Integer) v).orElse(0);
        response.setSkuIdOffset(skuIdOffset.toString());
        response.setPageInfo(new PageInfo(page, pageSize, totalPage, totalCount));
    }

    /**
     * 构建查询库存请求参数
     * @param channelOnlinePoiCode
     * @param pageSize
     * @param skuIdOffset
     * @return
     */
    private Map<String, Object> buildQueryStockParam(String channelOnlinePoiCode, int pageSize, Long skuIdOffset) {
        Map<String, Object> bizParam = Maps.newHashMap();
        //根据饿了么要求，游标模式page始终为1
        bizParam.put("page", OFFSET_MODEL_DEFAULT_PAGE);
        bizParam.put("pagesize", pageSize);
        bizParam.put("shop_id", channelOnlinePoiCode);
        bizParam.put("sku_id_offset", skuIdOffset);
        return bizParam;
    }

    /**
     * 获取商品id游标，当请求的SkuIdOffset为空，则表示为请求第一页
     * @param request
     * @return
     */
    private Long getSkuIdOffset(BatchGetStockInfoRequest request) {
        return StringUtils.isBlank(request.getSkuIdOffset()) ? INIT_SKU_OFFSET_ID : Long.valueOf(request.getSkuIdOffset());
    }

    /**
     * 组装结果数据
     *
     * @param postResult
     * @param resultData
     * @param skuIds
     */
    @SuppressWarnings("unchecked")
    private void installResultData(ResultData resultData, Map<Long, ChannelResponseDTO> postResult, List<String> skuIds) {
        Preconditions.checkArgument(MapUtils.isNotEmpty(postResult), "Stock.installResultData fail, post result is empty");
        postResult.forEach((storeId, channelResponseDTO) -> {
            // msg转换为model,部分成功解析
            if (!channelResponseDTO.isSuccess() && channelResponseDTO.getMsg().startsWith("{")) {
                ChannelStockResponseData channelStockResponseData = JSON.parseObject(channelResponseDTO.getMsg(), ChannelStockResponseData.class);;

                // 成功的数据
                if (channelStockResponseData != null && CollectionUtils.isNotEmpty(channelStockResponseData.getSuccessList())) {
                    channelStockResponseData.getSuccessList().forEach(successDetail -> {
                        String customSkuIdAndStockStr = successDetail.getCustomSkuId();
                        String customSkuId = customSkuIdAndStockStr.split(":")[0];
                        ResultSuccessSku resultSuccessSku = new ResultSuccessSku();
                        resultSuccessSku.setChannelSkuId(successDetail.getSkuId());
                        resultSuccessSku.setChannelId(ChannelTypeEnum.ELEM.getCode());
                        resultSuccessSku.setSkuId(customSkuId);
                        resultSuccessSku.setStoreId(storeId);
                        resultData.addToSucData(resultSuccessSku);
                    });
                }

                // 失败的数据
                if (channelStockResponseData != null &&  CollectionUtils.isNotEmpty(channelStockResponseData.getErrorList())) {
                    channelStockResponseData.getErrorList().forEach(errorDetail -> {
                        String customSkuIdAndStockStr = errorDetail.getCustomSkuId();
                        String customSkuId = customSkuIdAndStockStr.split(":")[0];

                        ResultErrorSku resultErrorSku = new ResultErrorSku();
                        resultErrorSku.setChannelSkuId(errorDetail.getSkuId());
                        resultErrorSku.setSkuId(customSkuId);
                        resultErrorSku.setChannelId(ChannelTypeEnum.ELEM.getCode());
                        resultErrorSku.setStoreId(storeId);
                        resultErrorSku.setErrorCode(errorDetail.getErrorCode());
                        resultErrorSku.setErrorMsg(errorDetail.getErrorMsg());
                        resultData.addToErrorData(resultErrorSku);
                    });
                }
            } else {

                skuIds.forEach(skuId -> ResultDataUtils.addToResultData(storeId, channelResponseDTO, resultData, skuId));
            }
        });
    }


    /**
     * 组装结果数据
     *
     * @param postResult
     * @param resultData
     * @param skuIds
     */
    @SuppressWarnings("unchecked")
    private void installResultData(ResultSpuData resultData, Map<Long, ChannelResponseDTO> postResult, List<String> skuIds) {
        Preconditions.checkArgument(MapUtils.isNotEmpty(postResult), "Stock.installResultData fail, post result is empty");
        postResult.forEach((storeId, channelResponseDTO) -> {
            // msg转换为model,部分成功解析
            if (!channelResponseDTO.isSuccess() && channelResponseDTO.getMsg().startsWith("{")) {
                ChannelStockResponseData channelStockResponseData = JSON.parseObject(channelResponseDTO.getMsg(), ChannelStockResponseData.class);;

                // 成功的数据
                if (channelStockResponseData != null && CollectionUtils.isNotEmpty(channelStockResponseData.getSuccessList())) {
                    channelStockResponseData.getSuccessList().forEach(successDetail -> {
                        String customSkuIdAndStockStr = successDetail.getCustomSkuId();
                        String customSkuId = customSkuIdAndStockStr.split(":")[0];
                        ResultSuccessSpu resultSuccessSpu = new ResultSuccessSpu();
                        resultSuccessSpu.setChannelId(ChannelTypeEnum.ELEM.getCode());
                        resultSuccessSpu.setStoreId(storeId);

                        SpuKey spuKey = new SpuKey();
                        List<SkuKey> skuKeyList = new ArrayList<>();
                        spuKey.setChannelSpuId(customSkuId);
                        spuKey.setCustomSpuId(customSkuId);
                        skuKeyList.add(new SkuKey().setChannelSkuId(customSkuId).setCustomSkuId(customSkuId));
                        spuKey.setSkus(skuKeyList);
                        resultSuccessSpu.setSpuInfo(spuKey);
                        resultData.addToSucData(resultSuccessSpu);
                    });
                }

                // 失败的数据
                if (channelStockResponseData != null &&  CollectionUtils.isNotEmpty(channelStockResponseData.getErrorList())) {
                    channelStockResponseData.getErrorList().forEach(errorDetail -> {
                        String customSkuIdAndStockStr = errorDetail.getCustomSkuId();
                        String customSkuId = customSkuIdAndStockStr.split(":")[0];

                        ResultErrorSpu resultErrorSpu = new ResultErrorSpu();
                        resultErrorSpu.setChannelId(ChannelTypeEnum.ELEM.getCode());
                        resultErrorSpu.setStoreId(storeId);
                        resultErrorSpu.setErrorCode(errorDetail.getErrorCode());
                        resultErrorSpu.setErrorMsg(errorDetail.getErrorMsg());

                        SpuKey spuKey = new SpuKey();
                        List<SkuKey> skuKeyList = new ArrayList<>();
                        spuKey.setChannelSpuId(customSkuId);
                        spuKey.setCustomSpuId(customSkuId);
                        skuKeyList.add(new SkuKey().setChannelSkuId(customSkuId).setCustomSkuId(customSkuId));
                        spuKey.setSkus(skuKeyList);
                        resultErrorSpu.setSpuInfo(spuKey);

                        resultData.addToErrorData(resultErrorSpu);
                    });
                }
            } else {

                skuIds.forEach(skuId -> ResultDataUtils.addToResultData(storeId, channelResponseDTO, resultData, skuId));
            }
        });
    }
}
