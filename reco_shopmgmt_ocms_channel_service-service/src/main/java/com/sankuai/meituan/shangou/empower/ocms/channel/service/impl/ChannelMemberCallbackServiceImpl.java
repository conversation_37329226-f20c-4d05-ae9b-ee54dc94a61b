package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelMemberCallbackService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.FnMemberThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.EnumUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.saas.common.enums.ChannelEnum;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.common.CardIdTypeEnum;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.common.CardStatusEnum;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.common.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.common.SexEnum;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.request.mcard.AddMemberElectronicCardRequest;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.request.mcard.MemberCardInfoQueryRequest;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.response.mcard.AddCardResponse;
import com.sankuai.meituan.shangou.saas.crm.mif.thrift.response.mcard.MemberCardInfoQueryResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * @Author：<EMAIL>
 * @Date: 2019/7/2 3:15 PM
 */
@Service("channelMemberCallbackServiceImpl")
public class ChannelMemberCallbackServiceImpl  implements ChannelMemberCallbackService {

    public static final  int FN_MEMBER_NOT_EXIST_ERROR_CODE = 101;

    public static final  int SG_MEMBER_NOT_EXIST_STAUTS_CODE = 4;

    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Autowired
    private FnMemberThriftServiceProxy fnMemberThriftServiceProxy;

    @Resource
    private CommonLogger log;

    /**
     * 会员查询
     */
    @Override
    public MemberQueryResponse getMember(MemberQueryRequest request) {


            ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
            if (Objects.isNull(channelTypeEnum)) {
                log.error("getMember, 未知渠道, request:{}", request);
                return new MemberQueryResponse().setCode(ResultCode.CHANNEL_CODE_INVALID.getCode()).setMsg(ResultCode.CHANNEL_CODE_INVALID.getMsg());
            }
            //转换得到tenantID
            Long tenantId = copAccessConfigService.selectTenantId(channelTypeEnum.getCode(),request.getTenantAppId());
            if(tenantId == null){
                log.error("getMember: channel {},tenantAppCode {} 未查到tenantId",request.getChannelCode(),request.getTenantAppId());
                return new MemberQueryResponse().setCode(ResultCode.CHANNEL_APP_ID_INVALID.getCode()).setMsg(ResultCode.CHANNEL_APP_ID_INVALID.getMsg());

            }
            MemberCardInfoQueryResponse memberCardInfo = fnMemberThriftServiceProxy.getMemberCardInfo(buildMemberQueryRequest(tenantId,request));
            return convertResult(memberCardInfo,channelTypeEnum,tenantId);



    }

    private MemberQueryResponse convertResult(MemberCardInfoQueryResponse memberCardInfo, ChannelTypeEnum channelTypeEnum, long tenantId) {


        MemberQueryResponse res = new MemberQueryResponse();
        if(memberCardInfo == null){
            return res.setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg());
        }
        if(memberCardInfo.getCode() == ResultCodeEnum.SUCCESS){
            MemberInfoDTO memberInfo = new MemberInfoDTO();
            memberInfo.setBirthday(memberCardInfo.getData().getMemberInfo().getBirthday()== null ? null:
                DateUtils.formatDate( new Date(DateUtils.date2TimeStamp(memberCardInfo.getData().getMemberInfo().getBirthday()+"",DateUtils.YYYYMMDD_Format)),DateUtils.YYYY_MM_DD_Format));
            memberInfo.setCardCode(memberCardInfo.getData().getCardInfo().getCardCode());
            memberInfo.setGender(memberCardInfo.getData().getMemberInfo().getSex() == null ? 0 : memberCardInfo.getData().getMemberInfo().getSex().getValue());

            memberInfo.setIdentityCard(memberCardInfo.getData().getMemberInfo().getIdNo());
            memberInfo.setMobile(memberCardInfo.getData().getMemberInfo().getMobile());
            memberInfo.setName(memberCardInfo.getData().getMemberInfo().getMemberName());
            memberInfo.setMemberId(memberCardInfo.getData().getMemberInfo().getMemberId()+"");

            String channelPoiCode = getChannelPoiCode(tenantId,channelTypeEnum,memberCardInfo.getData().getCardInfo().getIssueStoreId());
            if(StringUtils.isEmpty(channelPoiCode)){
                return res.setCode(ResultCode.CHANNEL_POI_CODE_INVALID.getCode()).setMsg(ResultCode.CHANNEL_POI_CODE_INVALID.getMsg());
            }
            memberInfo.setRegisterPoiCode(channelPoiCode);
            memberInfo.setRegisterTime(memberCardInfo.getData().getCardInfo().getIssueTime() == null ? null : (int)(memberCardInfo.getData().getCardInfo().getIssueTime()/1000));
            memberInfo.setStatusCode(getCardStatus(memberCardInfo.getData().getCardInfo().getCardStatus()));
            //memberInfo.setTotal_score(response.getData().getScore()+"");

            return res.setCode(ResultCode.SUCCESS.getCode()).setData(memberInfo);
        }else{

            if(memberCardInfo.getStatus() == FN_MEMBER_NOT_EXIST_ERROR_CODE){
                //会员若不存在，结果返回成功，但data中的卡状态设置为4，代表非会员
                MemberInfoDTO memberInfo = new MemberInfoDTO();
                memberInfo.setStatusCode(SG_MEMBER_NOT_EXIST_STAUTS_CODE);
                res.setCode(ResultCode.SUCCESS.getCode()).setData(memberInfo);
            }else{
                res.setCode(ResultCode.FAIL.getCode()).setMsg(memberCardInfo.getMsg());
            }
            return res;
        }


    }

    private int getCardStatus(CardStatusEnum cardStatus) {

        switch (cardStatus){
            case ACTIVATED:
                return 1;
            case CANCEL:
                return 3;
            case UNACTIVATED:
                return 4;
            default:
                return 2;
        }
    }

    private String getChannelPoiCode(long tenantId,ChannelTypeEnum channelTypeEnum,long stroreId){
        Map<String, ChannelStoreDO> storeDOMap = copChannelStoreService.getChannelPoiCode(tenantId,channelTypeEnum.getCode(), Lists.newArrayList(stroreId));

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId,channelTypeEnum.getCode(),
            stroreId);
        ChannelStoreDO channelStoreDO =  storeDOMap.get(channelStoreKey);
        if(channelStoreDO != null){
            return channelStoreDO.getChannelOnlinePoiCode();
        }
        return null;
    }


    private MemberCardInfoQueryRequest buildMemberQueryRequest(Long tenantId, MemberQueryRequest request) {

        MemberCardInfoQueryRequest dto = new MemberCardInfoQueryRequest();
        dto.setTenantId(tenantId);
        if(!StringUtils.isEmpty(request.getMobile())){
            dto.setCardIdType(CardIdTypeEnum.PHONE);
            dto.setCardIdValue(request.getMobile());
        }else{
            dto.setCardIdType(CardIdTypeEnum.CARD_CODE);
            dto.setCardIdValue(request.getCardCode());
        }
        return dto;
    }

    /**
     * 会员创建
     */
    @Override
    public MemberCreateResponse createMember(MemberCreateRequest request) {
        ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByAbbrev(request.getChannelCode(), ChannelTypeEnum.class);
        if (Objects.isNull(channelTypeEnum)) {
            log.error("createMember, 未知渠道, request:{}", request);
            return new MemberCreateResponse().setCode(ResultCode.CHANNEL_CODE_INVALID.getCode()).setMsg(ResultCode.CHANNEL_CODE_INVALID.getMsg());
        }

        //转换得到tenantID
        Long tenantId = copAccessConfigService.selectTenantId(channelTypeEnum.getCode(),request.getTenantAppId());
        if(tenantId == null){
            log.error("createMember: channel {},tenantAppCode {} 未查到tenantId",request.getChannelCode(),request.getTenantAppId());
            return new MemberCreateResponse().setCode(ResultCode.CHANNEL_APP_ID_INVALID.getCode()).setMsg(ResultCode.CHANNEL_APP_ID_INVALID.getMsg());
        }

        long storeId = copChannelStoreService.selectChannelStoreId(tenantId,channelTypeEnum.getCode(),request.getAppPoiCode());
        if(storeId < 0 ){
            log.error("createMember:tenantId {},chahnnel {},appPoiCode {},未找到storeId",tenantId,request.getChannelCode(),request.getAppPoiCode());
            return new MemberCreateResponse().setCode(ResultCode.CHANNEL_POI_CODE_INVALID.getCode()).setMsg(ResultCode.CHANNEL_POI_CODE_INVALID.getMsg());
        }

        AddCardResponse response = fnMemberThriftServiceProxy.createMemberCard(buildMemberCreateRequest(tenantId, storeId,request,channelTypeEnum));

        return convertCreateResult(response, channelTypeEnum, tenantId);
    }

    private MemberCreateResponse convertCreateResult(AddCardResponse response, ChannelTypeEnum channelTypeEnum, Long tenantId) {

        MemberCreateResponse res = new MemberCreateResponse();
        if(response == null){
            return res.setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg());
        }

        if(response.getCode() == ResultCodeEnum.SUCCESS){
            MemberCreateResultDTO createResultDTO = new MemberCreateResultDTO();
            createResultDTO.setCardCode(response.getData().getCardCode());
            createResultDTO.setMemberId(response.getData().getMemberId()+"");
            createResultDTO.setRegisterTime(response.getData().getRegisterTime() == null ? null : (int)(response.getData().getRegisterTime()/1000));
            createResultDTO.setStatusCode(getCardStatus(response.getData().getCardStatus()));

            return res.setCode(ResultCode.SUCCESS.getCode()).setData(createResultDTO);
        }else{
            return res.setCode(ResultCode.FAIL.getCode()).setMsg(response.getMsg());
        }
    }

    private AddMemberElectronicCardRequest buildMemberCreateRequest(Long tenantId, long storeId, MemberCreateRequest request, ChannelTypeEnum channelTypeEnum) {

        AddMemberElectronicCardRequest addRequest = new AddMemberElectronicCardRequest();
        addRequest.setTenantId(tenantId);
        addRequest.setMobile(request.getMobile());

        try{
            if(!StringUtils.isEmpty(request.getBirthday())){
                addRequest.setBirthday(Integer.parseInt(DateUtils.formatDate(new Date(DateUtils.date2TimeStamp(request.getBirthday(),DateUtils.YYYY_MM_DD_Format)*1000),DateUtils.YYYYMMDD_Format)));
            }
            addRequest.setSex(SexEnum.findByValue(request.getGender()));
        }catch (Exception e){
            throw new IllegalArgumentException(ResultCode.INVALID_PARAM.getMsg());
        }

        addRequest.setMemberName(request.getName());
        addRequest.setPoiId(storeId);
        if(channelTypeEnum == ChannelTypeEnum.MEITUAN){
            addRequest.setChannel(ChannelEnum.MTSG);
        }else{
            //目前理论上不会走到此处，仅为保证逻辑完整性，暂时抛异常，后续其他渠道接入 此处需扩展
            throw new IllegalArgumentException(ResultCode.INVALID_PARAM.getMsg());
        }
        return addRequest;
    }


}