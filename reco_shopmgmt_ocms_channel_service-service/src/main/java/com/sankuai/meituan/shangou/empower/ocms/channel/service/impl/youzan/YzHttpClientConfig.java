package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzan;

import java.math.BigInteger;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.X509TrustManager;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.youzan.cloud.open.sdk.core.HttpConfig;
import com.youzan.cloud.open.sdk.core.client.core.DefaultYZClient;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import sun.security.ssl.SSLSocketFactoryImpl;

/**
 * <AUTHOR>
 * @since 2021-06-03 17:30
 */
@Configuration
public class YzHttpClientConfig {

    /**
     * socket超时时间
     */
    private static final int DEFAULT_SOCKET_TIMEOUT = 15;
    private static final int DEFAULT_KEEP_ALIVE_DURATION = 30;

    @Bean
    public DefaultYZClient client() throws Exception {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(DEFAULT_SOCKET_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_SOCKET_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_SOCKET_TIMEOUT, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(MccConfigUtil.getHttpMaxTotal(),
                        DEFAULT_KEEP_ALIVE_DURATION, TimeUnit.MINUTES))
                .hostnameVerifier((hostname, session) -> true)
                .sslSocketFactory(new SSLSocketFactoryImpl(), new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {

                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {

                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[BigInteger.ZERO.intValue()];
                    }
                });
        HttpConfig httpConfig = HttpConfig.builder().OkHttpClientBuilder(builder).build();
        return new DefaultYZClient(httpConfig);
    }
}
