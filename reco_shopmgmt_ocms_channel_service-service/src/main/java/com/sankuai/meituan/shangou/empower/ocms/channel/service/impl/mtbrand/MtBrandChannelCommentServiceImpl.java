package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtCommentInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MtResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCommentService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ChannelCommentConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.ChannelCommentDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentListQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentListQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentReplyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentReplyResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;

/**
 * 美团渠道评价内部服务接口
 *
 * <AUTHOR>
 */
@Service("mtBrandChannelCommentService")
public class MtBrandChannelCommentServiceImpl implements ChannelCommentService {

    @Value("${mt.url.base}" + "${mt.url.commentQuery}")
    private String commentQueryUrl;

    @Value("${mt.url.base}" + "${mt.url.commentReply}")
    private String commentReplyUrl;

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private CommonLogger log;

    @Override
    public CommentListQueryResponse queryCommentList(CommentListQueryRequest request) {
        CommentListQueryResponse response = new CommentListQueryResponse();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
                .setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getStoreId()));

        // 校验渠道门店信息, 如果渠道门店信息不存在, 直接返回
        ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(request.getTenantId(),
                request.getChannelId(), request.getStoreId());
        if (channelStoreDO == null) {
            log.warn("渠道门店未启用, {}", KeyUtils.genChannelStoreKey(request.getTenantId(), request.getChannelId(),
                    request.getStoreId()));
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道门店未启用"));
        }

        // 构建评价查询业务参数
        Map<String, Object> bizParam = ChannelCommentConverterUtil.buildMtCommentQueryBizParam(request, channelStoreDO);

        // 调用评价查询接口
        Map<String, Object> resultMap = mtBrandChannelGateService.sendGet(commentQueryUrl, null, baseRequest, bizParam);

        // 解析评价查询结果
        if (MapUtils.isNotEmpty(resultMap)) {
            ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(resultMap), ChannelResponseDTO.class);
            String data = channelResponseDTO.getData();
            if (ProjectConstant.NG.equals(channelResponseDTO.getData())) {
                return response.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg()));
            } else {
                List<MtCommentInfo> mtCommentInfoList = JSON.parseArray(data, MtCommentInfo.class);
                List<ChannelCommentDTO> channelCommentDTOList = Optional.of(mtCommentInfoList).map(List::stream)
                        .orElse(Stream.empty()).map(mtCommentInfo -> mtCommentInfo.convertToChannelCommentDTO(request.getTenantId())).collect(Collectors.toList());
                return response.setStatus(ResultGenerator.genSuccessResult()).setCommentDTOList(channelCommentDTOList);
            }
        } else {
            return response.setStatus(ResultGenerator.genFailResult("调用美团评价查询接口失败"));
        }
    }

    @Override
    public CommentReplyResponse reply(CommentReplyRequest request) {
        CommentReplyResponse response = new CommentReplyResponse();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
                .setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getStoreId()));

        // 校验渠道门店信息, 如果渠道门店信息不存在, 直接返回
        ChannelStoreDO channelStoreDO = copChannelStoreService
                .getChannelStore(request.getTenantId(), request.getChannelId(), request.getStoreId());
        if (channelStoreDO == null) {
            log.warn("渠道门店未启用, {}", KeyUtils.genChannelStoreKey(request.getTenantId(), request.getChannelId(),
                    request.getStoreId()));
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道门店未启用"));
        }

        // 构建回复评价业务参数
        Map<String, Object> bizParam = ChannelCommentConverterUtil.buildMtCommentReplyBizParam(request, channelStoreDO);

        // 调用评价回复接口
        Map<String, Object> resultMap = mtBrandChannelGateService.sendPost(commentReplyUrl, null, baseRequest, bizParam);

        // 解析评价回复结果
        ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(resultMap), ChannelResponseDTO.class);
        if (ProjectConstant.NG.equals(channelResponseDTO.getData())) {
            // 超期或已经提交过2次, 回复之前中台已经判断过期时间, 此处的错误暂时都以已提交过2次处理
            if (MtResultCodeEnum.COMMENT_ALREADY_REPLIED.getCode() == channelResponseDTO.getError().getCode()) {
                return response.setStatus(ResultGenerator.genResult(ResultCode.CHANNEL_COMMENT_ALREADY_REPLIED, channelResponseDTO.getErrorMsg()));
            }
            return response.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg()));
        } else {
            return response.setStatus(ResultGenerator.genSuccessResult());
        }
    }

}
