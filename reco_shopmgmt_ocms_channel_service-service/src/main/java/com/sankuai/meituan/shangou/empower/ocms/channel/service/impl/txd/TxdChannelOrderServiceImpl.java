package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.doudian.open.utils.JsonUtil;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.model.OrderBaseModel;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ChannelConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantChannelConfigResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdDeliveryTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdRefundApplyReasonEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdTenantModeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.helper.TxdHttpHelper;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.txd.TxdOrderBaseInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.txd.TxdOrderTradeAttributes;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.OrderCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.tenant.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.JsonPathUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.StrUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.ServiceTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.AfterSaleRecordStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderPartRefundType;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.hash.MD5Util;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.ChannelPoiBaseInfoDTO;
import com.taobao.api.ApiException;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.request.AlibabaAelophyOrderDelivererChangeRequest;
import com.taobao.api.request.AlibabaAelophyOrderDesensitizephoneGetRequest;
import com.taobao.api.request.AlibabaAelophyOrderGetRequest;
import com.taobao.api.request.AlibabaAelophyOrderLogisticsTraceCallbackRequest;
import com.taobao.api.request.AlibabaAelophyOrderWorkCallbackRequest;
import com.taobao.api.request.AlibabaAxWarehouseOutboundCallbackRequest;
import com.taobao.api.request.AlibabaTclsAelophyRefundAgreeRequest;
import com.taobao.api.request.AlibabaTclsAelophyRefundCsapplyNewRequest;
import com.taobao.api.request.AlibabaTclsAelophyRefundCsapplyrenderRequest;
import com.taobao.api.request.AlibabaTclsAelophyRefundDisagreeRequest;
import com.taobao.api.request.AlibabaWdkOrderListRequest;
import com.taobao.api.request.AlibabaWdkOrderRefundGetRequest;
import com.taobao.api.request.AlibabaWdkOrderStcodeAcceptRequest;
import com.taobao.api.request.AlibabaWdkOrderStcodeQueryRequest;
import com.taobao.api.response.AlibabaAelophyOrderDelivererChangeResponse;
import com.taobao.api.response.AlibabaAelophyOrderDesensitizephoneGetResponse;
import com.taobao.api.response.AlibabaAelophyOrderGetResponse;
import com.taobao.api.response.AlibabaAelophyOrderLogisticsTraceCallbackResponse;
import com.taobao.api.response.AlibabaAelophyOrderWorkCallbackResponse;
import com.taobao.api.response.AlibabaAxWarehouseOutboundCallbackResponse;
import com.taobao.api.response.AlibabaTclsAelophyRefundAgreeResponse;
import com.taobao.api.response.AlibabaTclsAelophyRefundCsapplyNewResponse;
import com.taobao.api.response.AlibabaTclsAelophyRefundCsapplyrenderResponse;
import com.taobao.api.response.AlibabaTclsAelophyRefundDisagreeResponse;
import com.taobao.api.response.AlibabaWdkOrderListResponse;
import com.taobao.api.response.AlibabaWdkOrderRefundGetResponse;
import com.taobao.api.response.AlibabaWdkOrderStcodeAcceptResponse;
import com.taobao.api.response.AlibabaWdkOrderStcodeQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.meituan.shangou.saas.tenant.thrift.common.constants.TenantConfigConstants.V_SINGLE_MODEL;
import static com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum.INTERFACE_MODEL;
import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode.TXD_REPORT_RIDER_INFO_CHANGE_ERROR;
import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode.TXD_REPORT_RIDER_LOCATION_ERROR;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdDeliveryTypeEnum.MERCHANT_DELIVERY;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum.ACCEPTED;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum.PACKAGED;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum.PACKAGE_FINISH;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum.PICKED;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum.PICK_FINISH;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum.REJECTED;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum.SHIPPING;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum.SIGN;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdRefundApplyReasonEnum.REASON_480052;
import static com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.TxdOrderConvertUtil.convertFromChannelOrder;
import static com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.TxdOrderConvertUtil.getShopCardFee;
import static java.util.Optional.ofNullable;

@Slf4j
@Service("txdChannelOrderService")
public class TxdChannelOrderServiceImpl implements ChannelOrderService {

    private static final String ORDER_LIST_SUCCESS_CODE = "HM02008888888001";
    private static final long DEFAULT_DOUBLE_MODE_ORDER_FROM = 31L;
    private static final long DEFAULT_SINGLE_MODE_ORDER_FROM = 4L;

    @Resource
    private TxdHttpHelper httpHelper;


    @Resource
    private TenantRemoteService tenantRemoteService;
    
    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private ConfigThriftService configThriftService;

    /**
     * 自配送同步淘鲜达，淘鲜达侧默认公司运力名称
    */
    public static final String DEFAULT_DELIVERY_COMPANY = "OTHER";

    @Override
    public ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request) {
        try {
            TxdOrderBaseInfo baseInfo = TxdOrderBaseInfo.fromPoiConfirmOrderRequest(request);
            AlibabaAelophyOrderWorkCallbackResponse resp = reportOrderStatus(baseInfo, ACCEPTED);
            if (isResponseSuccess(resp)) {
                return ResultGenerator.genSuccessResult();
            }
            try {
                queryOrderFromChannel(baseInfo);
            } catch (Exception e) {
                log.warn("queryOrderFromChannel error: ", e);;
            }
            return ResultGenerator.genFailResult(getErrMsg(resp), resp);
        } catch (Exception e) {
            log.error("poiConfirmOrder error, request:{}", request, e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    private <T extends TaobaoResponse, R> boolean isResponseSuccess(T resp, Function<T, R> apiResultGetter,
        Function<R, Boolean> successGetter) {
        if (resp == null || !resp.isSuccess()) {
            return false;
        }
        R apiResult = apiResultGetter.apply(resp);
        return apiResult != null && BooleanUtils.isTrue(successGetter.apply(apiResult));
    }

    // AlibabaAelophyOrderWorkCallbackResponse
    private boolean isResponseSuccess(AlibabaAelophyOrderWorkCallbackResponse resp) {
        return isResponseSuccess(resp,
                AlibabaAelophyOrderWorkCallbackResponse::getApiResult,
                AlibabaAelophyOrderWorkCallbackResponse.TopBaseResult::getSuccess);
    }

    // AlibabaTclsAelophyRefundCsapplyNewResponse
    private boolean isResponseSuccess(AlibabaTclsAelophyRefundCsapplyNewResponse resp) {
        return isResponseSuccess(resp,
                AlibabaTclsAelophyRefundCsapplyNewResponse::getApiResult,
                AlibabaTclsAelophyRefundCsapplyNewResponse.ApiResult::getSuccess);
    }

    // AlibabaAelophyOrderLogisticsTraceCallbackResponse
    private boolean isResponseSuccess(AlibabaAelophyOrderLogisticsTraceCallbackResponse resp) {
        return isResponseSuccess(resp,
                AlibabaAelophyOrderLogisticsTraceCallbackResponse::getApiResult,
                AlibabaAelophyOrderLogisticsTraceCallbackResponse.TopBaseResult::getSuccess);
    }

    // AlibabaAelophyOrderDelivererChangeResponse
    private boolean isResponseSuccess(AlibabaAelophyOrderDelivererChangeResponse resp) {
        return isResponseSuccess(resp,
                AlibabaAelophyOrderDelivererChangeResponse::getApiResult,
                AlibabaAelophyOrderDelivererChangeResponse.TopBaseResult::getSuccess);
    }

    // AlibabaTclsAelophyRefundCsapplyrenderResponse
    private boolean isResponseSuccess(AlibabaTclsAelophyRefundCsapplyrenderResponse resp) {
        return isResponseSuccess(resp,
                AlibabaTclsAelophyRefundCsapplyrenderResponse::getApiResult,
                AlibabaTclsAelophyRefundCsapplyrenderResponse.ApiResult::getSuccess);
    }

    // AlibabaWdkOrderStcodeAcceptResponse
    private boolean isResponseSuccess(AlibabaWdkOrderStcodeAcceptResponse resp) {
        return isResponseSuccess(resp,
                AlibabaWdkOrderStcodeAcceptResponse::getApiResult,
                AlibabaWdkOrderStcodeAcceptResponse.ApiResult::getSuccess);
    }

    // AlibabaWdkOrderStcodeQueryResponse
    private boolean isResponseSuccess(AlibabaWdkOrderStcodeQueryResponse resp) {
        return isResponseSuccess(resp,
                AlibabaWdkOrderStcodeQueryResponse::getApiResult,
                AlibabaWdkOrderStcodeQueryResponse.ApiResult::getSuccess);
    }


    private String getErrMsg(AlibabaAelophyOrderWorkCallbackResponse resp) {
        if (resp == null) {
            return "接口返回为空";
        }
        String msg = resp.getMsg();
        AlibabaAelophyOrderWorkCallbackResponse.TopBaseResult apiResult = resp.getApiResult();
        if (apiResult != null && BooleanUtils.isNotTrue(apiResult.getSuccess())) {
            msg = apiResult.getErrMsg();
        }
        return msg;
    }


    private BizOrderModel queryOrderByViewId(long tenantId, long storeId, String orderId) {
        BizOrderModel bizOrderModel = orderCommonService.queryOrderByViewId(tenantId, storeId, orderId,
                DynamicOrderBizType.TAO_XIAN_DA);
        if (bizOrderModel == null) {
            log.error("订单{}不存在", orderId);
            throw new BizException("订单" + orderId + "不存在");
        }
        return bizOrderModel;
    }

    private OrderBaseModel queryOrderBaseByViewId(long tenantId, long storeId, String orderId) {
        OrderBaseModel orderBaseModel = orderCommonService.queryOrderBaseByViewId(tenantId, storeId, orderId,
                DynamicOrderBizType.TAO_XIAN_DA);
        if (orderBaseModel == null) {
            log.error("订单{}不存在", orderId);
            throw new BizException("订单" + orderId + "不存在");
        }
        return orderBaseModel;
    }

    @Override
    public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
        GetChannelOrderDetailResult orderDetailResult = new GetChannelOrderDetailResult();

        try {
            TxdOrderBaseInfo baseInfo = TxdOrderBaseInfo.fromGetChannelOrderDetailRequest(request);
            AlibabaAelophyOrderGetResponse resp = safeGetResponse(baseInfo);
            AlibabaAelophyOrderGetResponse.OrderResponse orderDto = safeGetOrderFromChannel(resp);
            if (orderDto == null) {
                return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取渠道订单失败"));
            }
            // 这个是商家号，应该是租户级别的
            String merchantCode = orderDto.getMerchantCode();
            String storeId = orderDto.getStoreId();

            ChannelPoiBaseInfoDTO channelPoiBaseInfoDTO = orderCommonService
                    .queryTenantInfoByChannelPoiInfo(DynamicChannelType.TAO_XIAN_DA, storeId);
            
            if (channelPoiBaseInfoDTO == null) {
                return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取中台门店失败"));
            }

            // 获取隐私号
            AlibabaAelophyOrderDesensitizephoneGetResponse.OrderDesensitizePhoneResult virtualMobile = fetchVirtualMobile(
                    baseInfo);

            // 通过前面准备好的前置数据，获取订单明细数据
            ChannelOrderDetailDTO channelOrderDetailDTO = convertFromChannelOrder(orderDto,
                    channelPoiBaseInfoDTO.getPoiId(), virtualMobile,resp);

            orderDetailResult.setChannelOrderDetail(channelOrderDetailDTO);
            return orderDetailResult;

        } catch (Exception e) {
            log.error("getChannelOrderDetail error, request:{}", request, e);
        }
        return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取渠道订单失败"));
    }


    private AlibabaAelophyOrderGetResponse queryOrderFromChannel(TxdOrderBaseInfo request) throws ApiException {
        AlibabaAelophyOrderGetRequest channelReq = new AlibabaAelophyOrderGetRequest();
        AlibabaAelophyOrderGetRequest.OrderGetRequest orderGetRequest = buildGetOrderDetailReq(request);
        channelReq.setOrderGetRequest(orderGetRequest);
        log.info("queryOrderFromChannel req: {}, orderId: {}", JsonUtil.toJson(channelReq), request.getViewOrderId());
        AlibabaAelophyOrderGetResponse resp = httpHelper.execute(request.getTenantId(), request.getStoreId(), channelReq);
        log.info("queryOrderFromChannel resp: {}, orderId: {}", JsonUtil.toJson(resp), request.getViewOrderId());
        return resp;
    }

    private AlibabaAelophyOrderGetRequest.OrderGetRequest buildGetOrderDetailReq(TxdOrderBaseInfo request) {

        fillExtOrderId(request);
        fillChannelPoiCode(request);

        AlibabaAelophyOrderGetRequest.OrderGetRequest orderGetRequest =
                new AlibabaAelophyOrderGetRequest.OrderGetRequest();

        orderGetRequest.setBizOrderId(Long.parseLong(request.getExtOrderId()));
        orderGetRequest.setStoreId(String.valueOf(request.getChannelPoiCode()));
        return orderGetRequest;
    }

    @Nullable
    private AlibabaAelophyOrderDesensitizephoneGetResponse.OrderDesensitizePhoneResult
            fetchVirtualMobile(TxdOrderBaseInfo request) {
        AlibabaAelophyOrderDesensitizephoneGetRequest channelReq = new AlibabaAelophyOrderDesensitizephoneGetRequest();
        AlibabaAelophyOrderDesensitizephoneGetRequest.OrderDesensitizePhoneRequest virtualMobileReq =
                buildFetchVirtualMobileReq(request);
        channelReq.setOrderDesensitizePhoneRequest(virtualMobileReq);
        try {
            log.info("fetchVirtualMobile req: {}", JsonUtil.toJson(channelReq));
            AlibabaAelophyOrderDesensitizephoneGetResponse resp = httpHelper.execute(request.getTenantId(),
                    request.getStoreId(), channelReq);
            log.info("fetchVirtualMobile resp: {}, orderId: {}", JsonUtil.toJson(resp), request.getOrderId());
            if (resp != null) {
                return resp.getModel();
            }
        } catch (Exception e) {
            log.error("fetchVirtualMobile {} error", channelReq, e);
        }
        return null;

    }

    private AlibabaAelophyOrderDesensitizephoneGetRequest.OrderDesensitizePhoneRequest
            buildFetchVirtualMobileReq(TxdOrderBaseInfo request) {
        AlibabaAelophyOrderDesensitizephoneGetRequest.OrderDesensitizePhoneRequest virtualMobileReq =
                new AlibabaAelophyOrderDesensitizephoneGetRequest.OrderDesensitizePhoneRequest();
        virtualMobileReq.setBizOrderId(request.getExtOrderId());
        virtualMobileReq.setStoreCode(request.getChannelPoiCode());
        return virtualMobileReq;
    }

    @Override
    public ResultStatus preparationMealComplete(PreparationMealCompleteRequest request) {
        log.info("preparationMealComplete req: {}", request);
        // 淘鲜达需要上报 PICKED 和 PACKAGED
        TxdOrderBaseInfo baseInfo = TxdOrderBaseInfo.fromPreparationMealCompleteRequest(request);
        //ocms消费拣货消息不会有extOrderId
        if (StringUtils.isBlank(request.getExtOrderId())) {
            fillExtOrderId(baseInfo);
            request.setExtOrderId(baseInfo.getExtOrderId());
        }
        if (StringUtils.isBlank(request.getExtOrderId())) {
            return ResultGenerator.genFailResult("淘鲜达订单extOrderId不能为空");
        }
        if (CollectionUtils.isEmpty(request.getItemInfoList())) {
            return ResultGenerator.genFailResult("淘鲜达订单商品数据不能为空");
        }

        AlibabaAelophyOrderGetResponse.OrderResponse orderResponse = safeGetOrderRespFromChannel(baseInfo);
        if (orderResponse == null) {
            return ResultGenerator.genFailResult("获取渠道订单失败");
        }
        String orderStatus = orderResponse.getOrderStatus();
        TxdOrderStatusEnum statusEnum = TxdOrderStatusEnum.fromName(orderStatus);
        if (MccConfigUtil.txdNeedNotReportMealComplete(statusEnum)) {
            log.info("preparationMealComplete 订单状态不需要上报, orderViewId: {}, status: {}", request.getExtOrderId(),
                    orderStatus);
            return ResultGenerator.genSuccessResult();
        }

        TxdTenantModeEnum tenantMode = getTenantMode(baseInfo.getTenantId(), null);
        // 双模型：平台配送&自提 走双模型方案，自配送和单模型一样
        // https://ones.sankuai.com/ones/product/51703/workItem/requirement/detail/87828875
        if (TxdTenantModeEnum.DOUBLE.equals(tenantMode) && !Objects.equals(orderResponse.getDeliveryType(), 2L)) {
            return reportMealCompleteForDoubleMode(request, baseInfo);
        }

        // 单模型或者默认情况，都走单模型方案
        return reportMealCompleteForSingleMode(request, baseInfo);
    }

    private void safeSleepBetweenPickedAndPackaged() {
        try {
            long sleepTime = MccConfigUtil.getTxdSleepTimeBetweenPickedAndPackaged();
            TimeUnit.MILLISECONDS.sleep(sleepTime);
        } catch (Exception e) {
            log.warn("safeSleepBetweenPickedAndPackaged error", e);
        }
    }

    private ResultStatus reportMealCompleteForDoubleMode(PreparationMealCompleteRequest request, TxdOrderBaseInfo baseInfo) {
        // 先上报 PICK_FINISH ，再上报 PACKAGE_FINISH，且不管 PICK_FINISH 是否成功，都尝试上报 PACKAGE_FINISH
        // 同步拣货完成(PICK_FINISH)
        try {
            AlibabaAxWarehouseOutboundCallbackResponse mealPickedResp = reportDoubleModeOrderStatus(baseInfo, PICK_FINISH);
            if (mealPickedResp == null) {
                log.warn("订单{}上报拣货完成状态失败", request.getOrderId());
            } else if (!mealPickedResp.isSuccess() || BooleanUtils.isNotTrue(mealPickedResp.getReturnSuccess())) {
                log.warn("订单{}上报拣货完成状态失败，error: {}", request.getOrderId(), mealPickedResp.getMsg());
            }
        } catch (Exception e) {
            log.error("preparationMealComplete PICK_FINISH error, request: {}", JsonUtil.toJson(request), e);
        }
        safeSleepBetweenPickedAndPackaged();
        try {
            AlibabaAxWarehouseOutboundCallbackResponse mealPackagedResp = reportDoubleModeOrderStatus(baseInfo, PACKAGE_FINISH);
            if (mealPackagedResp == null) {
                log.warn("订单{}上报打包完成状态失败", request.getOrderId());
                return ResultGenerator.genFailResult("上报打包完成失败");
            } else if (!mealPackagedResp.isSuccess() || BooleanUtils.isNotTrue(mealPackagedResp.getReturnSuccess())) {
                log.warn("订单{}上报打包完成状态失败，error: {}", request.getOrderId(), mealPackagedResp.getMsg());
                return ResultGenerator.genFailResult(mealPackagedResp.getMsg(), mealPackagedResp);
            }
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("preparationMealComplete PACKAGE_FINISH error, request: {}", JsonUtil.toJson(request), e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    private ResultStatus reportMealCompleteForSingleMode(PreparationMealCompleteRequest request, TxdOrderBaseInfo baseInfo) {
        AlibabaAelophyOrderWorkCallbackResponse mealPickedResp = null;
        AlibabaAelophyOrderWorkCallbackResponse mealPackagedResp = null;
        try {
            // 上报 PICKED 和 PACKAGED
            // 同步拣货完成(PICKED)
            mealPickedResp = reportOrderStatus(baseInfo, PICKED);
            if (mealPickedResp == null) {
                log.warn("订单{}上报拣货完成状态失败", request.getOrderId());
            } else if (!isResponseSuccess(mealPickedResp)) {
                // AlibabaAelophyOrderWorkCallbackResponse.TopBaseResult apiResult = mealPickedResp.getApiResult();
                log.warn("订单{}上报拣货完成状态失败，error: {}", request.getOrderId(), mealPickedResp.getMsg());
            }
            safeSleepBetweenPickedAndPackaged();
            // 同步打包完成(PACKAGED)，即使上报 PICKED 失败，也尝试上报 PACKAGED
            mealPackagedResp = reportOrderStatus(baseInfo, PACKAGED);
            if (mealPackagedResp == null) {
                log.warn("同步订单{}, 打包完成失败", request.getOrderId());
                return ResultGenerator.genFailResult("上报打包完成失败");
            }
            if (!isResponseSuccess(mealPackagedResp)) {
                log.warn("同步订单{}, 打包完成失败，error: {}", request.getOrderId(), mealPackagedResp.getMsg());
                return ResultGenerator.genFailResult(mealPackagedResp.getMsg(), mealPackagedResp);
            }
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("preparationMealComplete error, request: {}", JsonUtil.toJson(request), e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    @Override
    public GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request) {
        GetOrderStatusResult result = new GetOrderStatusResult();

        try {
            TxdOrderBaseInfo baseInfo = TxdOrderBaseInfo.fromGetOrderStatusRequest(request);

            AlibabaAelophyOrderGetResponse resp = safeGetResponse(baseInfo);
            AlibabaAelophyOrderGetResponse.OrderResponse orderDto = safeGetOrderFromChannel(resp);
            if (orderDto == null) {
                return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取渠道订单失败"));
            }

            OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
            orderStatusDTO.setOrderId(orderDto.getOutOrderId());
            orderStatusDTO.setStatus(TxdOrderStatusEnum.fromName(orderDto.getOrderStatus()).getStatus());
            return result.setStatus(ResultGenerator.genSuccessResult()).setOrderStatus(orderStatusDTO);

        } catch (Exception e) {
            log.error("getOrderStatus error, request:{}", request, e);
        }
        return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取渠道订单失败"));
    }

    @Nullable
    private AlibabaAelophyOrderGetResponse safeGetResponse(TxdOrderBaseInfo baseInfo){
        try {
            AlibabaAelophyOrderGetResponse resp = queryOrderFromChannel(baseInfo);
            if (resp == null || !resp.isSuccess() || resp.getApiResult() == null) {
                return null;
            }
            return resp;
        } catch(Exception e) {
            log.error("safeGetOrderFromChannel error", e);
        }
        return null;
    }

    @Nullable
    private AlibabaAelophyOrderGetResponse.OrderResponse safeGetOrderFromChannel (AlibabaAelophyOrderGetResponse resp) {
        if (resp == null || resp.getApiResult() == null) {
            return null;
        }
        return resp.getApiResult().getModel();
    }

    @Nullable
    private AlibabaAelophyOrderGetResponse.OrderResponse safeGetOrderRespFromChannel(TxdOrderBaseInfo baseInfo) {
        AlibabaAelophyOrderGetResponse alibabaAelophyOrderGetResponse = safeGetResponse(baseInfo);
        return safeGetOrderFromChannel(alibabaAelophyOrderGetResponse);
    }

    @Override
    public GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request) {
        GetOrderAfsApplyListResult result = new GetOrderAfsApplyListResult();

        AlibabaWdkOrderRefundGetRequest channelRequest = new AlibabaWdkOrderRefundGetRequest();
        TxdTenantModeEnum mode =  getTenantMode(request);
        if (TxdTenantModeEnum.SINGLE.equals(mode) && MccConfigUtil.useDefaultMode(request.getTenantId())) {
            mode = TxdTenantModeEnum.DEFAULT;
        }
        final TxdTenantModeEnum finalMode = mode;
        BizOrderModel bizOrderModel = null;
        String refundIds = mode.getRefundFromReqFunc().apply(request);
        List<String> bizOrderIds = Lists.newArrayList();
        // channelRequest里的 bizOrderIds和 refundIds，为二选一
        if (StringUtils.isNotBlank(refundIds)) {
            channelRequest.setRefundIds(refundIds);
        } else if (StringUtils.isNotBlank(request.getChannelOrderId())) {
            bizOrderModel = queryOrderByViewId(request.getTenantId(), request.getStoreId(), request.getChannelOrderId());
            setBizOrderIds(bizOrderIds, bizOrderModel);
        }

        //此处应为渠道门店id
        String channelPoiCode = queryChannelPoiIdByQnhStoreId(request.getTenantId(), request.getStoreId());
        channelRequest.setStoreId(channelPoiCode);
        if (Objects.nonNull(bizOrderModel)) {
            fillOrderFrom(channelRequest::getOrderFrom, channelRequest::setOrderFrom, request.getTenantId(), bizOrderModel);
        } else {
            fillOrderFrom(channelRequest::getOrderFrom, channelRequest::setOrderFrom, request.getTenantId(),
                    request.getStoreId(), request.getChannelOrderId());
        }
        if (CollectionUtils.isEmpty(bizOrderIds)) {
            return doGetOrderAfsApplyList(request, channelRequest, result, mode);
        } else if (bizOrderIds.size() == 1) {
            channelRequest.setBizOrderIds(bizOrderIds.get(0));
            return doGetOrderAfsApplyList(request, channelRequest, result, mode);
        } else {
            GetOrderAfsApplyListResult finalResult = new GetOrderAfsApplyListResult();
            List<OrderAfsApplyDTO> afsApplyDTOList = Lists.newArrayList();
            // 默认失败
            finalResult.setStatus(ResultGenerator.genFailResult("查询渠道退单列表失败"));
            bizOrderIds.forEach(id -> {
                channelRequest.setBizOrderIds(id);
                GetOrderAfsApplyListResult resultTmp = doGetOrderAfsApplyList(request, channelRequest, finalResult, finalMode);
                if (resultTmp.getStatus().getCode() == ResultCode.SUCCESS.getCode()) {
                    afsApplyDTOList.addAll(resultTmp.getAfsApplyList());
                    // 有一个成功就认为成功
                    finalResult.setStatus(ResultGenerator.genSuccessResult());
                }

            });
            finalResult.setAfsApplyList(afsApplyDTOList);
            return finalResult;
        }

    }

    private GetOrderAfsApplyListResult doGetOrderAfsApplyList(GetOrderAfsApplyListRequest request, AlibabaWdkOrderRefundGetRequest channelRequest, GetOrderAfsApplyListResult result, TxdTenantModeEnum mode) {
        try {
            log.info("查询渠道退单列表:request:{}", JacksonUtils.toJson(channelRequest));
            AlibabaWdkOrderRefundGetResponse response = httpHelper.execute(request.getTenantId(), request.getStoreId(),
                    channelRequest);
            log.info("查询渠道退单列表 response: {}", JacksonUtils.toJson(response));
            if (response == null || !response.isSuccess() || response.getResult() == null
                    || BooleanUtils.isNotTrue(response.getResult().getSuccess())) {
                result.setStatus(ResultGenerator.genFailResult("查询渠道退单列表失败"));
            } else if (response.isSuccess() && response.getResult() != null && response.getResult().getSuccess()) {

                List<AlibabaWdkOrderRefundGetResponse.OrderSyncRefundDto> refundOrders = response.getResult().getOrders();
                if (CollectionUtils.isEmpty(refundOrders)) {
                    result.setAfsApplyList(Lists.newArrayList());
                } else {
                    Map<String, List<AlibabaWdkOrderRefundGetResponse.OrderSyncRefundDto>> orderSyncRefundDtoMap = refundOrders.stream()
                            .collect(Collectors.groupingBy(refundDto -> mode.getRefundFromRespFunc().apply(refundDto)));
                    List<OrderAfsApplyDTO> afsApplyDTOList = orderSyncRefundDtoMap.keySet().stream().map(i -> {
                        if (orderSyncRefundDtoMap.get(i).size() > 1) {
                            throw new BizException("退单id对应多个退单");
                        }
                        AlibabaWdkOrderRefundGetResponse.OrderSyncRefundDto orderSyncRefundDto = orderSyncRefundDtoMap.get(i).get(0);
                        return convertChannelAfsApplyDTO(orderSyncRefundDto);
                    }).collect(Collectors.toList());
                    if (StringUtils.isNotBlank(request.getAfterSaleId())) {
                        afsApplyDTOList = afsApplyDTOList.stream().filter(afs -> request.getAfterSaleId().equals(afs.getAfterSaleId())).collect(Collectors.toList());
                    }
                    result.setAfsApplyList(afsApplyDTOList);
                }
                result.setStatus(ResultGenerator.genSuccessResult());
            } else {
                log.warn("查询渠道退单列表，未知结果");
                result.setStatus(ResultGenerator.genFailResult("查询渠道退单列表失败"));
            }
        } catch (Exception e) {
            log.error("txdChannelOrderServiceImpl.getOrderAfsApplyList error, request:{}", request, e);
            result.setStatus(ResultGenerator.genFailResult("调用淘鲜达接口获取退单列表失败"));
        }
        return result;
    }

    /**
     * 双模型商家不能使用父订单号只能用子订单号，单模型商家可以使用父订单号
     * @param bizOrderIds
     * @param bizOrderModel
     */
    private void setBizOrderIds(List<String> bizOrderIds, BizOrderModel bizOrderModel) {

        // 先通过 Lion 配置判断使用 bizOrderId 还是 subBizOrderId
        Long tenantId = bizOrderModel.getTenantId();
        if (MccConfigUtil.useBizOrderId(tenantId)) {
            bizOrderIds.add(bizOrderModel.getExtOrderId());
            return;
        }
        if (MccConfigUtil.useSubBizOrderId(tenantId)) {
            useSubBizOrderId(bizOrderIds, bizOrderModel);
            return;
        }

        // 如果没有 Lion 配置，走租户的配置，单模型可用 bizOrderId，双模型用subBizOrderId
        String modeConfig = queryTenantModeByTenantConfig(bizOrderModel);

        if (INTERFACE_MODEL.isMainConfigEquals(modeConfig, V_SINGLE_MODEL)) {
            bizOrderIds.add(bizOrderModel.getExtOrderId());
            return;
        }

        useSubBizOrderId(bizOrderIds, bizOrderModel);

    }

    private static void useSubBizOrderId(List<String> bizOrderIds, BizOrderModel bizOrderModel) {
        List<BizOrderItemModel> bizOrderItemModelList = bizOrderModel.getBizOrderItemModelList();
        if (CollectionUtils.isEmpty(bizOrderItemModelList)) {
            return;
        }
        List<String> subBizOrderIds = Lists.newArrayList();
        for (BizOrderItemModel bizOrderItemModel : bizOrderItemModelList) {
            if (StringUtils.isBlank(bizOrderItemModel.getExtData())) {
                log.warn("bizOrderItemModel extData is null, viewOrderId: {}, orderItemId: {}",
                        bizOrderModel.getViewOrderId(), bizOrderItemModel.getOrderItemId());
                continue;
            }
            JSONObject extData = JSON.parseObject(bizOrderItemModel.getExtData());
            if (Objects.isNull(extData)) {
                log.warn("bizOrderItemModel extData is null2, viewOrderId: {}, orderItemId: {}",
                        bizOrderModel.getViewOrderId(), bizOrderItemModel.getOrderItemId());
                continue;
            }

            String subBizOrderId = extData.getString("bizChannelItemId");
            if (StringUtils.isBlank(subBizOrderId)) {
                log.warn("bizOrderItemModel bizChannelItemId is null, viewOrderId: {}, orderItemId: {}",
                        bizOrderModel.getViewOrderId(), bizOrderItemModel.getOrderItemId());
                continue;
            }
            subBizOrderIds.add(subBizOrderId);
        }
        if (CollectionUtils.isEmpty(subBizOrderIds)) {
            return;
        }

        int groupSize = MccConfigUtil.getTxdRefundOrderBatchSize();
        List<String> grouped = StrUtils.groupAndJoin(subBizOrderIds, groupSize, ",");
        bizOrderIds.addAll(grouped);
    }

    private String queryTenantModeByTenantConfig(BizOrderModel bizOrderModel) {
        ChannelConfigQueryRequest channelConfigQueryRequest = new ChannelConfigQueryRequest();
        channelConfigQueryRequest.setChannelId(DynamicChannelType.TAO_XIAN_DA.getChannelId());
        channelConfigQueryRequest.setTenantId(bizOrderModel.getTenantId());
        channelConfigQueryRequest.setSubjectId(bizOrderModel.getTenantId());
        channelConfigQueryRequest.setConfigId(INTERFACE_MODEL.getConfigId());
        TenantChannelConfigResponse configResponse = configThriftService.queryTenantChannelConfig(channelConfigQueryRequest);
        if (Objects.isNull(configResponse) || Objects.isNull(configResponse.getChannelConfig())) {
            return null;
        }
        return configResponse.getChannelConfig().getConfigContent();
    }

    /**
     * 获取租户是单模型还是双模型
     * @param request
     * @return
     */
    private TxdTenantModeEnum getTenantMode(GetOrderAfsApplyListRequest request) {
        return getTenantMode(request.getTenantId(), request.getOrderFrom());
    }

    private TxdTenantModeEnum getTenantMode(Long tenantId, String orderFrom) {
        if (!MccConfigUtil.enableCheckOrderMode(tenantId)) {
            return TxdTenantModeEnum.DEFAULT;
        }
        // 1. 先通过 tenantId 的 Lion 配置判断
        boolean singleModeByLion = MccConfigUtil.isSingleModeByLion(tenantId);
        boolean doubleModeByLion = MccConfigUtil.isDoubleModeByLion(tenantId);

        if (singleModeByLion) {
            return TxdTenantModeEnum.SINGLE;
        }
        if (doubleModeByLion) {
            return TxdTenantModeEnum.DOUBLE;
        }
        // 2. 通过 orderFrom 判断
        if (StringUtils.isNotBlank(orderFrom)) {
            // 单模型
            if ("4".equals(orderFrom)) { // 单模型
                return TxdTenantModeEnum.SINGLE;
            } else if ("31".equals(orderFrom)) { // 双模型
                return TxdTenantModeEnum.DOUBLE;
            }
        }
        // 3.  如果租户有接口了，在这里通过 tenantId 判断，现在默认按照之前的 id


        return TxdTenantModeEnum.DEFAULT;
    }

    private OrderAfsApplyDTO convertChannelAfsApplyDTO(AlibabaWdkOrderRefundGetResponse.OrderSyncRefundDto orderSyncRefundDto) {
        OrderAfsApplyDTO dto = new OrderAfsApplyDTO();

        dto.setChannelType(DynamicChannelType.TAO_XIAN_DA.getChannelId());

        dto.setChannelOrderId(orderSyncRefundDto.getOutMianOrderId());
        if (StringUtils.isBlank(dto.getChannelOrderId())) {
            dto.setChannelOrderId(String.valueOf(orderSyncRefundDto.getTbBizParentId()));
        }
        dto.setAfterSaleId(orderSyncRefundDto.getOutMainRefundId());
        if (StringUtils.isBlank(dto.getAfterSaleId())) {
            dto.setAfterSaleId(String.valueOf(orderSyncRefundDto.getRefundOrderId()));
        }
        //todo 申请时间无
        dto.setRefundApplyTime(1000);

        // 平台优惠金额，目前当做平台整单优惠

        JSONObject attributes = JSON.parseObject(orderSyncRefundDto.getAttributes());

        // 平台红包退款金额
        int refundPlatOrderHongBaoPromotion = -attributes.getIntValue("voucher_discount_platform_fee");
        int refundMerchantOrderHongBaoPromotion = Math.abs(attributes.getIntValue("voucher_discount_merchant_fee"));
        // 红包支付数据
        if (refundMerchantOrderHongBaoPromotion > 0 || refundPlatOrderHongBaoPromotion > 0) {
            RedpackInfo redpackInfo = new RedpackInfo();
            redpackInfo.setRedpackAmountMerchant(refundMerchantOrderHongBaoPromotion);
            redpackInfo.setRedpackAmountPlatform(refundPlatOrderHongBaoPromotion);
            redpackInfo.setRedpackAmountTotal(redpackInfo.getRedpackAmountMerchant() + redpackInfo.getRedpackAmountPlatform());
            dto.setRedpackInfo(redpackInfo);
        }
        // 购物赠金
        int expandDiscountPlatformFee = Math.abs(attributes.getIntValue("expand_discount_platform_fee"));
        int expandDiscountMerchantFee = Math.abs(attributes.getIntValue("expand_discount_merchant_fee"));
        int benefitDiscountPlatformFee = Math.abs(attributes.getIntValue("benefit_discount_platform_fee"));
        int benefitDiscountMerchantFee = Math.abs(attributes.getIntValue("benefit_discount_merchant_fee"));

        TxdOrderTradeAttributes tradeAttributes = TxdOrderTradeAttributes.builder()
                .benefitDiscountMerchantFee(benefitDiscountMerchantFee)
                .benefitDiscountPlatformFee(benefitDiscountPlatformFee)
                .expandDiscountMerchantFee(expandDiscountMerchantFee)
                .expandDiscountPlatformFee(expandDiscountPlatformFee)
                .build();
        dto.setShopCardFee(getShopCardFee(tradeAttributes));

        // 红包支付金额，算作用户支付，应该被退还给用户
        int refundPrice = Math.toIntExact(ofNullable(orderSyncRefundDto.getRefundAmount()).orElse(0L) + ofNullable(orderSyncRefundDto.getRefundPostFee()).orElse(0L));

        dto.setRefundPrice(refundPrice);
        dto.setFreight(Math.toIntExact(ofNullable(orderSyncRefundDto.getRefundPostFee()).orElse(0L)));
        // 目前这个接口只能查到审核通过的退单
        dto.setAfterSaleStatus(OrderPartRefundType.REFUND_SUCCESS.getValue());
        dto.setAfterSaleRecordStatus(AfterSaleRecordStatus.AUDITED.getValue());
        //todo 退款类型判断需要测试来选取方案
        dto.setRefundType(RefundTypeEnum.PART.getValue());
        dto.setRefundAuditTime(orderSyncRefundDto.getRefundTime().getTime());
        dto.setApplyReason("暂无法获取原因");
        //todo 审批原因
        dto.setResReason("暂无法获取审批原因");
        //如果扩展字段有值就取扩展字段的退款原因
        if (attributes != null && StringUtils.isNotBlank(attributes.getString("wdkdfqrrr"))) {
            dto.setApplyReason(attributes.getString("wdkdfqrrr"));
        }
        dto.setIsAppeal(false);
        //淘鲜达售后接口只能获取到退款成功的
        dto.setAfterSaleStatus(AfterSaleApplyStatusEnum.AUDITED.getValue());
        //todo 操作员类型
        dto.setApplyOpType(0);
        //淘鲜达只有仅退款
        dto.setServiceType(ServiceTypeEnum.REFUND.getCode());
        //Todo 退款流水号
        dto.setRefundFlow("");
        //佣金类型待确认
        // dto.setCommissionType(null);

        List<RefundProductDTO> afsProductList = new ArrayList<>();
        afsProductList.add(convertRefundProductDTO(orderSyncRefundDto, 0));
        dto.setAfsProductList(afsProductList);
        return dto;
    }

    private RefundProductDTO convertRefundProductDTO(AlibabaWdkOrderRefundGetResponse.OrderSyncRefundDto orderSyncRefundDto, int refundPlatOrderPromotion) {
        RefundProductDTO product = new RefundProductDTO();
        // 商品名称
        product.setSkuName("");
        product.setSkuId("");
        // 渠道给的退款数量不准，不能直接使用
        // product.setCount(0);
        product.setSkuRefundAmount(Math.toIntExact(orderSyncRefundDto.getRefundAmount()) - refundPlatOrderPromotion);
        // product.setRefundPlatOrderPromotion(refundPlatOrderPromotion);
        product.setCustomSpu("");
        product.setChannelOrderItemId(orderSyncRefundDto.getOutOrderId());
        if (StringUtils.isBlank(product.getChannelOrderItemId())) {
            product.setChannelOrderItemId(String.valueOf(orderSyncRefundDto.getTbBizOrderId()));
        }
        return product;
    }

    @Override
    public ResultStatus agreeRefund(AgreeRefundRequest request) {
        AlibabaTclsAelophyRefundAgreeRequest channelRequest = new AlibabaTclsAelophyRefundAgreeRequest();
        channelRequest.setOutOrderId(request.getOrderId());
        channelRequest.setRefundId(request.getAfterSaleId());
        String channelPoiCode = queryChannelPoiIdByQnhStoreId(request.getTenantId(), request.getStoreId());
        channelRequest.setStoreId(channelPoiCode);
        channelRequest.setSubRefundList(request.getChannelRefundItem()
                .stream().map(i -> {
            AlibabaTclsAelophyRefundAgreeRequest.Subrefundlist subRefund = new AlibabaTclsAelophyRefundAgreeRequest.Subrefundlist();
            subRefund.setOutSubOrderId(i.getChannelItemId());
            subRefund.setRefundFee((long) i.getRefundAmount());
            return subRefund;
        }).collect(Collectors.toList()));
        channelRequest.setAuditMemo(request.getReason());
        fillOrderFrom(channelRequest::getOrderFrom, channelRequest::setOrderFrom, request.getTenantId(),
                request.getStoreId(), request.getOrderId());
        try {
            log.info("调用渠道接口同意退款,request:{}", JacksonUtils.toJson(channelRequest));
            AlibabaTclsAelophyRefundAgreeResponse response = httpHelper.execute(request.getTenantId(),
                    request.getStoreId(), channelRequest);
            if (response == null || !response.isSuccess() || response.getResult() == null
                    || BooleanUtils.isNotTrue(response.getResult().getIsSuccess())) {
                log.error("调用渠道接口同意退款失败,response:{}", JacksonUtils.toJson(response));
                return ResultGenerator.genFailResult("调用渠道接口同意退款失败");
            }
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("txdChannelOrderServiceImpl.agreeRefund error,request:{}", request, e);
            return ResultGenerator.genFailResult("调用渠道接口同意退款异常");
        }
    }

    /**
     * 填充 orderFrom 字段，由开关控制哪些模型需要填充
     * 1. 首先使用订单里的 orderFrom 字段填充
     * 2. 如果订单里没有 orderFrom 字段，则使用租户模式填充，双模型填充 31，其余填充 4
     * @param getOrderFrom
     * @param setOrderFrom
     * @param tenantId
     * @param storeId
     * @param orderId
     */
    private void fillOrderFrom(Supplier<Long> getOrderFrom, Consumer<Long> setOrderFrom, long tenantId,
                               long storeId, String orderId) {
        if (Objects.nonNull(getOrderFrom.get())) {
            return;
        }
        TxdTenantModeEnum tenantMode = getTenantMode(tenantId, null);
        if (!MccConfigUtil.enableOrderFromForTxd(tenantMode)) {
            return;
        }

        fillOrderFromByBizOrderModel(setOrderFrom, tenantId, storeId, orderId);
        if (Objects.nonNull(getOrderFrom.get())) {
            return;
        }
        fillOrderFromByTenantMode(setOrderFrom, tenantMode);
    }

    private void fillOrderFrom(Supplier<Long> getOrderFrom, Consumer<Long> setOrderFrom, long tenantId,
                               BizOrderModel bizOrderModel) {
        if (Objects.nonNull(getOrderFrom.get())) {
            return;
        }
        TxdTenantModeEnum tenantMode = getTenantMode(tenantId, null);
        if (!MccConfigUtil.enableOrderFromForTxd(tenantMode)) {
            return;
        }

        fillOrderFromByBizOrderModel(setOrderFrom, bizOrderModel);
        if (Objects.nonNull(getOrderFrom.get())) {
            return;
        }
        fillOrderFromByTenantMode(setOrderFrom, tenantMode);
    }

    private void fillOrderFromByTenantMode(Consumer<Long> setOrderFrom, TxdTenantModeEnum tenantMode) {

        if (TxdTenantModeEnum.DOUBLE.equals(tenantMode)) {
            setOrderFrom.accept(DEFAULT_DOUBLE_MODE_ORDER_FROM);
            return;
        }

        setOrderFrom.accept(DEFAULT_SINGLE_MODE_ORDER_FROM);
    }

    private void fillOrderFromByBizOrderModel(Consumer<Long> setOrderFrom, long tenantId, long storeId, String orderId) {
        BizOrderModel bizOrderModel = null;
        try {
            bizOrderModel = queryOrderByViewId(tenantId, storeId, orderId);
        } catch (Exception e) {
            log.warn("queryOrderByViewId error");
        }
        fillOrderFromByBizOrderModel(setOrderFrom, bizOrderModel);
    }

    private static void fillOrderFromByBizOrderModel(Consumer<Long> setOrderFrom, BizOrderModel bizOrderModel) {
        if (bizOrderModel == null) {
            return;
        }
        String newExtData = bizOrderModel.getNewExtData();
        if (StringUtils.isBlank(newExtData)) {
            return;
        }
        String orderFrom = JsonPathUtil.readString(newExtData, "$.orderFrom");
        Long orderFromLong = null;
        if (StringUtils.isNotBlank(orderFrom)) {
            try {
                orderFromLong = Long.parseLong(orderFrom);
            } catch (Exception e) {
                log.warn("parse orderFrom of {} error: ", bizOrderModel.getViewOrderId(), e);
            }
        }
        setOrderFrom.accept(orderFromLong);
    }

    @Override
    public ResultStatus rejectRefund(RejectRefundRequest request) {
        AlibabaTclsAelophyRefundDisagreeRequest channelRequest = new AlibabaTclsAelophyRefundDisagreeRequest();
        channelRequest.setRefundId(request.getAfterSaleId());
        channelRequest.setRejectReason(request.getReason());
        fillOrderFrom(channelRequest::getOrderFrom, channelRequest::setOrderFrom, request.getTenantId(),
                request.getStoreId(), request.getOrderId());
        try {
            log.info("调用渠道接口拒绝退款,request:{}", JacksonUtils.toJson(channelRequest));
            AlibabaTclsAelophyRefundDisagreeResponse response = httpHelper.execute(request.getTenantId(),
                    request.getStoreId(), channelRequest);
            if (response == null || !response.isSuccess() || response.getResult() == null
                    || BooleanUtils.isNotTrue(response.getResult().getIsSuccess())) {
                log.error("调用渠道接口拒绝退款失败,response:{}", JacksonUtils.toJson(response));
                return ResultGenerator.genFailResult("调用渠道接口拒绝退款失败");
            }
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("txdChannelOrderServiceImpl.rejectRefund error, request:{}", request, e);
            return ResultGenerator.genFailResult("调用渠道接口拒绝退款失败");
        }
    }

    @Override
    public ResultStatus refundGoods(RefundGoodsRequest request) {
        return null;
    }

    @Override
    public ResultStatus poiCancelOrder(PoiCancelOrderRequest request) {

        String orderId = request.getOrderId();
        long tenantId = request.getTenantId();
        long storeId = request.getStoreId();
        BizOrderModel bizOrderModel = queryOrderByViewId(tenantId, storeId, orderId);
        // 查询渠道订单数据
        AlibabaAelophyOrderGetResponse.OrderResponse orderDto = queryChannelOrder(request, bizOrderModel);

        if (orderDto == null) {
            return ResultGenerator.genResult(ResultCode.CHANNEL_ORDER_QUERY_FAILED, "查询订单详情失败");
        }
        return doCancelOrder(request, orderDto, bizOrderModel);
    }

    private ResultStatus doCancelOrder(PoiCancelOrderRequest request,
            AlibabaAelophyOrderGetResponse.OrderResponse orderDto, BizOrderModel bizOrderModel) {
        // 根据状态，调用不同接口处理
        TxdOrderStatusEnum orderStatusEnum = TxdOrderStatusEnum.fromName(orderDto.getOrderStatus());
        TxdDeliveryTypeEnum deliveryType = TxdDeliveryTypeEnum.fromCode(orderDto.getDeliveryType());
        switch (orderStatusEnum) {
            case PAID:
            case ACCEPTED:
                if (MccConfigUtil.enableRejectToCancelOrder() && deliveryType == MERCHANT_DELIVERY) {
                    // 订单发配送前，调用拒单接口
                    return reportRejected(request, bizOrderModel);
                } else {
                    // 平台配送，需要调用代客发起
                    return cancelOrderByReportPickedNone(bizOrderModel, orderStatusEnum, orderDto, request);
                }
            case PICKED:
            case PACKAGED:
                if (MccConfigUtil.enableRejectToCancelOrder() && deliveryType == MERCHANT_DELIVERY) {
                    // 订单发配送前，调用拒单接口
                    return reportRejected(request, bizOrderModel);
                } else {
                    // 平台配送，需要调用代客发起
                    return cancelOrderAfterDelivery(bizOrderModel, orderDto, request);
                }
            case SHIPPING:
            case SIGN:
            case SUCCESS:
                // 订单已完成，调用代客发起退款接口
                return cancelOrderAfterDelivery(bizOrderModel, orderDto, request);

            case CLOSE:
            case REJECTED:
            case REFUSED:
                // 订单已经退完了，无需再发起
                return ResultGenerator.genFailResult("订单状态: " + orderStatusEnum.name() + "，已经退完了，无需再发起");
            default:
                // 未知状态，不处理
                return ResultGenerator.genFailResult("订单状态为未知状态，不处理");
        }
    }

    @Nullable
    private AlibabaAelophyOrderGetResponse.OrderResponse queryChannelOrder(PoiCancelOrderRequest request,
                                                                           BizOrderModel bizOrderModel) {

        TxdOrderBaseInfo baseInfo = TxdOrderBaseInfo.fromBizOrderModel(bizOrderModel);
        try {
            AlibabaAelophyOrderGetResponse resp = queryOrderFromChannel(baseInfo);
            AlibabaAelophyOrderGetResponse.TopBaseResult result = resp.getApiResult();
            return result.getModel();
        } catch (Exception e) {
            log.error("查询订单{}详情失败: ", request.getOrderId(), e);
            return null;
        }
    }

    private ResultStatus cancelOrderAfterDelivery(BizOrderModel bizOrderModel,
            AlibabaAelophyOrderGetResponse.OrderResponse orderDto, PoiCancelOrderRequest request) {

        // 先调渠道接口查看有子单数据（例如是否可退、可退金额是多少）
        AlibabaTclsAelophyRefundCsapplyrenderResponse.RefundCsApplyRenderResponseDto renderData = queryRefundRenderData(
                bizOrderModel, orderDto);
        List<AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders> outSubOrders = null;
        List<AlibabaTclsAelophyRefundCsapplyrenderResponse.Reasonlist> reasonList = null;
        if (renderData != null) {
            outSubOrders = renderData.getOutSubOrders();
            reasonList = renderData.getReasonList();
        }
        // 正常应该不会为空
        if (CollectionUtils.isEmpty(outSubOrders) || CollectionUtils.isEmpty(reasonList)) {
            return ResultGenerator.genFailResult("查询到子单or取消原因数据为空");
        }
        Set<Long> reasonCodeSet = reasonList.stream()
                .map(AlibabaTclsAelophyRefundCsapplyrenderResponse.Reasonlist::getReasonId)
                .collect(Collectors.toSet());

        // 如果没有传 reason 过来，从 render 数据选取第一个
        if (request.getReason_code() <= 0) {
            if (reasonCodeSet.contains(REASON_480052.getCode())) {
                request.setReason_code((int) REASON_480052.getCode());
                request.setReason(REASON_480052.getDesc());
            } else {
                AlibabaTclsAelophyRefundCsapplyrenderResponse.Reasonlist reason = reasonList.get(0);
                request.setReason_code(reason.getReasonId().intValue());
                request.setReason(reason.getReasonText());
            }
        }

        // 然后调用接口一次性退完所有可以退的子单
        return cancelOrder(bizOrderModel, outSubOrders, request);

    }

    private ResultStatus cancelOrderByReportPickedNone(BizOrderModel bizOrderModel, TxdOrderStatusEnum orderStatusEnum,
            AlibabaAelophyOrderGetResponse.OrderResponse orderDto, PoiCancelOrderRequest request) {
        // 需要区分单双模型，本次先做单模型
        TxdTenantModeEnum tenantMode = getTenantMode(bizOrderModel.getTenantId(), null);
        if (TxdTenantModeEnum.DOUBLE.equals(tenantMode)) {
            return handelDoubleModeCancelOrderByReportPickedNone(bizOrderModel, orderStatusEnum, orderDto, request);
        }
        return handleSingleModeCancelOrderByReportPickedNone(bizOrderModel, orderStatusEnum, orderDto, request);
    }

    private ResultStatus handelDoubleModeCancelOrderByReportPickedNone(BizOrderModel bizOrderModel,
        TxdOrderStatusEnum orderStatusEnum, AlibabaAelophyOrderGetResponse.OrderResponse orderDto,
        PoiCancelOrderRequest request) {
        TxdOrderBaseInfo txdOrderBaseInfo = TxdOrderBaseInfo.fromPoiCancelOrderRequest(request);

        // 双模型会自动接单，这里无需兜底为接单状态，直接上报拣货完成即可
        fillItemsWithZero(txdOrderBaseInfo, orderDto);

        try {
            AlibabaAxWarehouseOutboundCallbackResponse response = reportDoubleModeOrderStatus(txdOrderBaseInfo, PICK_FINISH);
            if (response == null) {
                log.warn("同步订单{}, 取消订单失败", request.getOrderId());
                return ResultGenerator.genFailResult("取消订单失败");
            }
            if (!response.isSuccess() || BooleanUtils.isNotTrue(response.getReturnSuccess())) {
                log.warn("同步订单{}, 取消订单失败，error: {}", request.getOrderId(), response.getMsg());
                String subMsg = response.getSubMsg();
                String msg = response.getMsg();
                if (StringUtils.isNotBlank(subMsg) && subMsg.contains("未找到对应的单据")) {
                    msg = "渠道仓作业单尚未下发，无法发起全单退，请稍后再试";
                }
                return ResultGenerator.genFailResult(msg, response);
            }
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("handelDoubleModeCancelOrderByReportPickedNone error, request: {}", JsonUtil.toJson(request), e);
            return ResultGenerator.genFailResult(e.getMessage());
        }

    }

    private AlibabaAxWarehouseOutboundCallbackResponse reportDoubleModeOrderStatus(TxdOrderBaseInfo orderBaseInfo,
        TxdOrderStatusEnum statusEnum) throws ApiException {

        AlibabaAxWarehouseOutboundCallbackRequest channelReq = buildOutboundCallbackReq(orderBaseInfo, statusEnum);

        log.info("report orderStatus: {}, req: {}, orderId: {}", statusEnum.name(), JsonUtil.toJson(channelReq), orderBaseInfo.getViewOrderId());

        AlibabaAxWarehouseOutboundCallbackResponse resp = httpHelper.execute(orderBaseInfo.getTenantId(), orderBaseInfo.getStoreId(), channelReq);
        log.info("report orderStatus: {}, resp: {}, orderId: {}", statusEnum.name(), JsonUtil.toJson(resp), orderBaseInfo.getViewOrderId());

        return resp;
    }

    private AlibabaAxWarehouseOutboundCallbackRequest buildOutboundCallbackReq(TxdOrderBaseInfo orderBaseInfo, TxdOrderStatusEnum statusEnum) {
        AlibabaAxWarehouseOutboundCallbackRequest channelReq = new AlibabaAxWarehouseOutboundCallbackRequest();
        AlibabaAxWarehouseOutboundCallbackRequest.TopTradeOutBoundCallBackRequest param =
                new AlibabaAxWarehouseOutboundCallbackRequest.TopTradeOutBoundCallBackRequest();

        fillExtOrderId(orderBaseInfo);
        fillChannelPoiCode(orderBaseInfo);

        handleItemInfo(orderBaseInfo.getItemInfoList(), param);

        param.setBizOrderId(orderBaseInfo.getExtOrderId());
        param.setOutBoundStatus(statusEnum.name());
        param.setDemandStatusTime(new Date());
        param.setStoreCode(orderBaseInfo.getChannelPoiCode());

        channelReq.setTradeOutBoundCallBackRequest(param);

        return channelReq;
    }

    private ResultStatus handleSingleModeCancelOrderByReportPickedNone(BizOrderModel bizOrderModel, TxdOrderStatusEnum orderStatusEnum,
            AlibabaAelophyOrderGetResponse.OrderResponse orderDto, PoiCancelOrderRequest request) {
        TxdOrderBaseInfo txdOrderBaseInfo = TxdOrderBaseInfo.fromPoiCancelOrderRequest(request);
        // 未接单，无法直接上报拣货完成，先尝试接单
        if (orderStatusEnum == TxdOrderStatusEnum.PAID) {
            try {
                reportOrderStatus(txdOrderBaseInfo, ACCEPTED);
            } catch (Exception e) {
                log.error("handleSingleModeCancelOrderByReportPickedNone 尝试接单失败: {}", request.getOrderId(), e);
            }
        }
        fillItemsWithZero(txdOrderBaseInfo, orderDto);
        try {
            AlibabaAelophyOrderWorkCallbackResponse response = reportOrderStatus(txdOrderBaseInfo, PICKED);
            if (response == null) {
                log.warn("同步订单{}, 拣货完成失败", request.getOrderId());
                return ResultGenerator.genFailResult("上报完成失败");
            }
            if (!isResponseSuccess(response)) {
                log.warn("同步订单{}, 打包完成失败，error: {}", request.getOrderId(), response.getMsg());
                return ResultGenerator.genFailResult(getErrMsg(response), response);
            }
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("handleSingleModeCancelOrderByReportPickedNone error, request: {}", JsonUtil.toJson(request), e);
            return ResultGenerator.genFailResult(e.getMessage());
        }

    }

    private void fillItemsWithZero(TxdOrderBaseInfo txdOrderBaseInfo, AlibabaAelophyOrderGetResponse.OrderResponse orderDto) {

        List<PreparationMealCompleteItemInfo> pickItemInfoList = orderDto.getSubOrderResponseList().stream().map(item -> {
            PreparationMealCompleteItemInfo pickItemInfo = new PreparationMealCompleteItemInfo();
            pickItemInfo.setCustomSpuId(item.getSkuCode());
            pickItemInfo.setChannelItemId(item.getBizSubOrderId());
            pickItemInfo.setPickSaleQuantity("0");
            pickItemInfo.setPickStockQuantity("0");
            pickItemInfo.setPurchaseQuantity(item.getBuySaleQuantity());
            return pickItemInfo;
        }).collect(Collectors.toList());

        txdOrderBaseInfo.setItemInfoList(pickItemInfoList);
    }

    @Nullable
    private AlibabaTclsAelophyRefundCsapplyrenderResponse.RefundCsApplyRenderResponseDto
            queryRefundRenderData(BizOrderModel bizOrderModel, AlibabaAelophyOrderGetResponse.OrderResponse orderDto) {
        Long tenantId = bizOrderModel.getTenantId();
        Long storeId = bizOrderModel.getShopId();
        String viewOrderId = bizOrderModel.getViewOrderId();

        List<AlibabaAelophyOrderGetResponse.SubOrderResponse> itemList = orderDto.getSubOrderResponseList();
        List<String> outSubOrderIdList = itemList.stream()
                .map(AlibabaAelophyOrderGetResponse.SubOrderResponse::getOutSubOrderId)
                .collect(Collectors.toList());
        AlibabaTclsAelophyRefundCsapplyrenderRequest renderReq = buildCsapplyrenderRequest(tenantId, storeId,
                viewOrderId, outSubOrderIdList, orderDto.getOrderFrom());

        try {
            log.info("querySubOrdersInfo req: {}", JsonUtil.toJson(renderReq));
            AlibabaTclsAelophyRefundCsapplyrenderResponse renderResp = httpHelper.execute(tenantId, storeId, renderReq);
            log.info("querySubOrdersInfo orderId: {}, resp: {}", viewOrderId, JsonUtil.toJson(renderResp));
            AlibabaTclsAelophyRefundCsapplyrenderResponse.ApiResult apiResult = renderResp.getApiResult();
            return apiResult.getModel();

        } catch (Exception e) {
            log.error("cancelOrderAfterFinished {} error", viewOrderId, e);
            return null;
        }
    }

    private ResultStatus cancelOrder(BizOrderModel bizOrderModel,
                                     List<AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders> outSubOrders,
                                     PoiCancelOrderRequest request) {

        try {
            AlibabaTclsAelophyRefundCsapplyNewRequest refundApplyReq = buildRefundApplyRequest(bizOrderModel,
                    outSubOrders, request);
            log.info("cancelOrder refundCsapply req: {}", JsonUtil.toJson(refundApplyReq));
            AlibabaTclsAelophyRefundCsapplyNewResponse applyResp = httpHelper.execute(bizOrderModel.getTenantId(),
                    bizOrderModel.getShopId(), refundApplyReq);
            log.info("cancelOrder refundCsapply orderId: {} resp: {}", bizOrderModel.getViewOrderId(),
                    JsonUtil.toJson(applyResp));

            if (!isResponseSuccess(applyResp)) {
                return ResultGenerator.genFailResult("调用渠道接口整单退失败");
            }
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("cancelOrderAfterFinished {} error", bizOrderModel.getViewOrderId(), e);
            return ResultGenerator.genFailResult("调用渠道接口整单退异常");
        }
    }

    private AlibabaTclsAelophyRefundCsapplyNewRequest buildRefundApplyRequest(BizOrderModel bizOrderModel,
            List<AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders> outSubOrders,
            PoiCancelOrderRequest request) {

        AlibabaTclsAelophyRefundCsapplyNewRequest channelRequest = new AlibabaTclsAelophyRefundCsapplyNewRequest();

        AlibabaTclsAelophyRefundCsapplyNewRequest.RefundCsApplyNewDTO applyNewDTO = buildRefundApplyDTO(
                bizOrderModel.getViewOrderId(), bizOrderModel.getTenantId(), bizOrderModel.getShopId(),
                request.getReason_code(), request.getReason());
        fillSubRefundOrders(outSubOrders, applyNewDTO);
        fillOrderFrom(applyNewDTO::getOrderFrom, applyNewDTO::setOrderFrom, bizOrderModel.getTenantId(), bizOrderModel);

        applyNewDTO.setRequestId(MD5Util.toMD5String(applyNewDTO.toString()));

        channelRequest.setRefundCsApplyDTO(applyNewDTO);

        return channelRequest;
    }

    private void fillSubRefundOrders(List<AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders> outSubOrders,
            AlibabaTclsAelophyRefundCsapplyNewRequest.RefundCsApplyNewDTO applyNewDTO) {

        List<AlibabaTclsAelophyRefundCsapplyNewRequest.CsApplySubOrderDTO> applySubOrderDTOList = outSubOrders.stream()
                .map(outSubOrder -> {
                    // 过滤无法退的子单
                    if (BooleanUtils.isNotTrue(outSubOrder.getCanReverse())
                            || outSubOrder.getMaxRefundFee()  == null
                            || outSubOrder.getMaxRefundFee() <= 0) {
                        return null;
                    }
                    AlibabaTclsAelophyRefundCsapplyNewRequest.CsApplySubOrderDTO subOrderDTO =
                            new AlibabaTclsAelophyRefundCsapplyNewRequest.CsApplySubOrderDTO();
                    // 退完所有金额
                    subOrderDTO.setOutSubOrderId(outSubOrder.getOutSubOrderId());
                    subOrderDTO.setRefundFee(String.valueOf(outSubOrder.getMaxRefundFee()));
                    return subOrderDTO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        applyNewDTO.setSubRefundOrders(applySubOrderDTOList);
    }

    private ResultStatus reportRejected(PoiCancelOrderRequest request, BizOrderModel bizOrderModel) {
        TxdOrderBaseInfo baseInfo = TxdOrderBaseInfo.fromPoiCancelOrderRequest(request);
        try {
            AlibabaAelophyOrderWorkCallbackResponse resp = reportOrderStatus(baseInfo, REJECTED);

            if (!isResponseSuccess(resp)) {
                return ResultGenerator.genFailResult("取消订单失败");
            }
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("reportRejected {} error ", baseInfo.getViewOrderId(), e);
            return ResultGenerator.genResult(ResultCodeEnum.FAIL_ALLOW_RETRY, "调用渠道接口取消订单异常");
        }
    }

    @Override
    public ResultStatus poiPartRefundApply(PoiPartRefundRequest request) {
        AlibabaTclsAelophyRefundCsapplyNewRequest channelRequest = buildRefundApplyRequest(request);
        try {
            log.info("调用淘鲜达接口发起退款,req:{}", JacksonUtils.toJson(channelRequest));
            AlibabaTclsAelophyRefundCsapplyNewResponse response = httpHelper.execute(request.getTenantId(),
                    request.getStoreId(), channelRequest);
            log.info("调用淘鲜达接口发起退款, resp: {}", JacksonUtils.toJson(response));
            if (!isResponseSuccess(response)) {
                return ResultGenerator.genFailResult("调用渠道接口发起退款失败");
            } else {
                return ResultGenerator.genSuccessResult();
            }
        } catch (Exception e) {
            log.error("txdChannelOrderServiceImpl.poiPartRefundApply error ,request:{}", request, e);
            return ResultGenerator.genFailResult("调用渠道接口发起退款异常");
        }
    }

    private AlibabaTclsAelophyRefundCsapplyNewRequest buildRefundApplyRequest(PoiPartRefundRequest request) {
        AlibabaTclsAelophyRefundCsapplyNewRequest channelRequest = new AlibabaTclsAelophyRefundCsapplyNewRequest();
        AlibabaTclsAelophyRefundCsapplyNewRequest.RefundCsApplyNewDTO applyNewDTO = buildRefundApplyDTO(
                request.getOrderId(), request.getTenantId(), request.getStoreId(), request.getReasonCode(),
                request.getReason());
        fillSubRefundOrders(request, applyNewDTO);
        fillReasonCodeIfBlank(applyNewDTO);
        applyNewDTO.setRequestId(MD5Util.toMD5String(applyNewDTO.toString()));
        fillOrderFrom(applyNewDTO::getOrderFrom, applyNewDTO::setOrderFrom, request.getTenantId(), request.getStoreId(),
                request.getOrderId());
        channelRequest.setRefundCsApplyDTO(applyNewDTO);
        return channelRequest;
    }

    private void fillReasonCodeIfBlank(AlibabaTclsAelophyRefundCsapplyNewRequest.RefundCsApplyNewDTO applyNewDTO) {
        if (applyNewDTO.getReasonId() == null || applyNewDTO.getReasonId() <= 0) {
            Long reasonId = Lion.getConfigRepository().getLongValue("config.txd.defaultPartRefundReasonId", 480052L);
            TxdRefundApplyReasonEnum reasonEnum = TxdRefundApplyReasonEnum.fromCode(reasonId);
            applyNewDTO.setReasonId(reasonEnum.getCode());
            applyNewDTO.setRefundReason(reasonEnum.getDesc());
        }
    }

    private static void fillSubRefundOrders(PoiPartRefundRequest request,
            AlibabaTclsAelophyRefundCsapplyNewRequest.RefundCsApplyNewDTO applyNewDTO) {
        List<AlibabaTclsAelophyRefundCsapplyNewRequest.CsApplySubOrderDTO> subOrderDTOS = request.getRefundProducts()
                .stream().map(i -> {
                    AlibabaTclsAelophyRefundCsapplyNewRequest.CsApplySubOrderDTO subOrderDTO =
                            new AlibabaTclsAelophyRefundCsapplyNewRequest.CsApplySubOrderDTO();
                    // 淘鲜达退款只有金额
                    subOrderDTO.setOutSubOrderId(i.getChannelOrderItemId());
                    subOrderDTO.setRefundFee(String.valueOf(i.getRefundAmount()));
                    return subOrderDTO;
                }).collect(Collectors.toList());
        applyNewDTO.setSubRefundOrders(subOrderDTOS);
    }

    private AlibabaTclsAelophyRefundCsapplyNewRequest.RefundCsApplyNewDTO buildRefundApplyDTO(String orderId,
            long tenantId, long storeId, long reasonCode, String reason) {
        AlibabaTclsAelophyRefundCsapplyNewRequest.RefundCsApplyNewDTO applyNewDTO =
                new AlibabaTclsAelophyRefundCsapplyNewRequest.RefundCsApplyNewDTO();
        applyNewDTO.setOutOrderId(orderId);
        String channelPoiCode = queryChannelPoiIdByQnhStoreId(tenantId, storeId);
        applyNewDTO.setStoreId(channelPoiCode);
        applyNewDTO.setReasonId(reasonCode);
        applyNewDTO.setRefundReason(reason);
        return applyNewDTO;
    }

    @Override
    public CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request) {
        return null;
    }

    @Override
    public ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request) {
        return null;
    }

    @Override
    public PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request) {
        return null;
    }

    @Override
    public OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request) {
        return null;
    }

    @Override
    public GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request) {
        return null;
    }

    @Override
    public GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request) {
        ResultStatus resultStatus = ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE);
        return new GetLogisticsStatusResult().setStatus(resultStatus);
    }

    @Override
    public ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request) {
        return null;
    }

    @Override
    public ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request) {
        int status = request.getStatus();
        DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(request.getStatus());
        if (deliveryStatus == null) {
            log.warn("订单:{}，配送状态: {}，无需上报", request.getOrderId(), status);
            return ResultGenerator.genSuccessResult();
        }
        AlibabaAelophyOrderWorkCallbackResponse resp = null;
        try {
            switch (deliveryStatus) {
                case RIDER_TAKEN_MEAL:
                    // 需要上报开始配送
                    resp = reportDeliveryForRiderTakenMeal(request);
                    break;
                case DELIVERY_COMPLETED:
                    // 同步用户签收
                    resp = reportUserSigned(request);
                    break;
                default:
                    log.warn("订单:{}，解析后配送状态: {}，无需上报", request.getOrderId(), deliveryStatus);
                    return ResultGenerator.genSuccessResult();
            }
            if (isResponseSuccess(resp)) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(getErrMsg(resp), resp);
        } catch (Exception e) {
            log.error("updateDeliveryInfo error, request:{}", request, e);
            return ResultGenerator.genFailResult(e.getMessage());
        }

    }

    private AlibabaAelophyOrderWorkCallbackResponse reportDeliveryForRiderTakenMeal(UpdateDeliveryInfoRequest request)
            throws ApiException {

        TxdOrderBaseInfo baseInfo = TxdOrderBaseInfo.fromUpdateDeliveryInfoRequest(request);
        return reportOrderStatus(baseInfo, TxdOrderStatusEnum.SHIPPING);
    }

    private static void handleItemInfo(List<PreparationMealCompleteItemInfo> itemInfoList,
            AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackRequest callbackRequest) {
        List<AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackSubOrderInfo> subOrderInfoList = itemInfoList.stream()
                .map(itemInfo -> {
                    AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackSubOrderInfo subOrderInfo =
                            new AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackSubOrderInfo();
                    subOrderInfo.setBizSubOrderId(itemInfo.getChannelItemId());
                    subOrderInfo.setSkuCode(itemInfo.getCustomSpuId());
                    subOrderInfo.setPickSaleQuantity(itemInfo.getPickSaleQuantity());
                    subOrderInfo.setPickStockQuantity(itemInfo.getPickStockQuantity());
                    return subOrderInfo;
                }).collect(Collectors.toList());
        callbackRequest.setWorkCallbackSubOrderInfoList(subOrderInfoList);
    }

    private static void handleItemInfo(List<PreparationMealCompleteItemInfo> itemInfoList,
        AlibabaAxWarehouseOutboundCallbackRequest.TopTradeOutBoundCallBackRequest param) {
        List<AlibabaAxWarehouseOutboundCallbackRequest.TradeOutBoundDetailCallBackRequest> itemDetailList = itemInfoList.stream()
                .map(itemInfo -> {
                    // param.getDemandDetailCallBackRequests()
                    AlibabaAxWarehouseOutboundCallbackRequest.TradeOutBoundDetailCallBackRequest itemDetail
                            = new AlibabaAxWarehouseOutboundCallbackRequest.TradeOutBoundDetailCallBackRequest();
                    fixPickQuantity(itemInfo);

                    itemDetail.setBizSubOrderId(String.valueOf(itemInfo.getChannelItemId()));
                    itemDetail.setSkuCode(itemInfo.getCustomSpuId());
                    itemDetail.setActualSaleQuantity(itemInfo.getPickSaleQuantity());
                    itemDetail.setActualStockQuantity(itemInfo.getPickStockQuantity());
                    BigDecimal outOfStockQuantity = new BigDecimal(itemInfo.getPurchaseQuantity())
                            .subtract(new BigDecimal(itemInfo.getPickSaleQuantity()));
                    itemDetail.setOutOfStockSaleQuantity(outOfStockQuantity.toString());
                    itemDetail.setOutOfStockStockQuantity(outOfStockQuantity.toString());
                    itemDetail.setIsOutStock(!BigDecimal.ZERO.equals(outOfStockQuantity));

                    return itemDetail;
                }).collect(Collectors.toList());

        param.setDemandDetailCallBackRequests(itemDetailList);
    }

    private static void fixPickQuantity(PreparationMealCompleteItemInfo itemInfo) {
        if (!MccConfigUtil.enablePickQuantityFix()) {
            return;
        }
        BigDecimal purchaseQuantity = new BigDecimal(itemInfo.getPurchaseQuantity());
        BigDecimal pickSaleQuantity = new BigDecimal(itemInfo.getPickSaleQuantity());
        BigDecimal pickStockQuantity = new BigDecimal(itemInfo.getPickStockQuantity());
        boolean fixed = false;
        if (purchaseQuantity.compareTo(pickSaleQuantity) < 0) {
            fixed = true;
            itemInfo.setPickSaleQuantity(purchaseQuantity.toString());
        }
        if (purchaseQuantity.compareTo(pickStockQuantity) < 0) {
            fixed = true;
            itemInfo.setPickStockQuantity(purchaseQuantity.toString());
        }
        if (fixed) {
            log.warn("fixedPickQuantity for itemInfo: {}", itemInfo);
        }
    }

    private void handleDeliveryInfo(UpdateDeliveryInfoRequest request,
            AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackRequest workCallbackRequest) {
        workCallbackRequest.setDelivererName(request.getRiderName());
        String deliveryCompany = getTxdDeliveryChannel((long) request.getDeliveryChannelId());
        workCallbackRequest.setDelivererCompany(deliveryCompany);
        workCallbackRequest.setDelivererPhone(request.getRiderPhone());
        workCallbackRequest.setLogisticsNo(request.getDeliveryOrderId());
    }

    private AlibabaAelophyOrderWorkCallbackResponse reportUserSigned(UpdateDeliveryInfoRequest request)
            throws ApiException {
        TxdOrderBaseInfo baseInfo = TxdOrderBaseInfo.fromUpdateDeliveryInfoRequest(request);
        return reportOrderStatus(baseInfo, SIGN);
    }


    @Override
    public ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request) {
        if (!MccConfigUtil.getTxdUpdateRiderInfoSwitch()) {
            return ResultGenerator.genSuccessResult();
        }

        long tenantId = request.getTenantId();
        long storeId = request.getShopId();
        String channelOrderId = request.getOrderId();
        String longitude = String.valueOf(request.getLongitude());
        String latitude = String.valueOf(request.getLatitude());

        try {
            // 淘鲜达渠道更新骑手坐标接口传的是biz_order_id，对应牵牛花订单的extOrderId
            OrderBaseModel orderBaseModel = queryOrderBaseByViewId(tenantId, storeId, channelOrderId);
            String extOrderId = orderBaseModel.getExtOrderId();

            // 查询渠道门店ID
            String channelPoiId = queryChannelPoiIdByQnhStoreId(tenantId, storeId);

            AlibabaAelophyOrderLogisticsTraceCallbackRequest req = new AlibabaAelophyOrderLogisticsTraceCallbackRequest();
            AlibabaAelophyOrderLogisticsTraceCallbackRequest.LogisticsTraceCallbackRequest paramReq =
                    new AlibabaAelophyOrderLogisticsTraceCallbackRequest.LogisticsTraceCallbackRequest();
            paramReq.setStoreId(channelPoiId);
            paramReq.setBizOrderId(Long.valueOf(extOrderId));
            paramReq.setLongitude(longitude);
            paramReq.setLatitude(latitude);
            paramReq.setUpdateTime(TimeUtil.toDate(LocalDateTime.now()));
            req.setLogisticsTraceCallbackRequest(paramReq);
            log.info("TxdChannelOrderServiceImpl updateRiderInfo, req is {}", req.getLogisticsTraceCallbackRequest());
            AlibabaAelophyOrderLogisticsTraceCallbackResponse response = httpHelper.execute(tenantId, storeId, req);
            if (Objects.isNull(response)) {
                log.error("TxdChannelOrderServiceImpl updateRiderInfo failed, response is null");
                Cat.logEvent(ProjectConstant.TXD_UPDATE_RIDER_INFO_CAT_TYPE, "TxdChannelOrderServiceImpl updateRiderInfo fail");
                return ResultGenerator.genResult(TXD_REPORT_RIDER_LOCATION_ERROR);
            }
            log.info("TxdChannelOrderServiceImpl updateRiderInfo, response is {}", convertTxdResponse(response));
            if (!isResponseSuccess(response)) {
                log.error("TxdChannelOrderServiceImpl updateRiderInfo failed, response is {}", convertTxdResponse(response));
                Cat.logEvent(ProjectConstant.TXD_UPDATE_RIDER_INFO_CAT_TYPE, "TxdChannelOrderServiceImpl updateRiderInfo fail");
                return ResultGenerator.genResult(TXD_REPORT_RIDER_LOCATION_ERROR);
            } else {
                Cat.logEvent(ProjectConstant.TXD_UPDATE_RIDER_INFO_CAT_TYPE, "TxdChannelOrderServiceImpl updateRiderInfo success");
                return ResultGenerator.genSuccessResult();
            }
        } catch (Exception e) {
            log.error("TxdChannelOrderServiceImpl updateRiderInfo exception", e);
            Cat.logEvent(ProjectConstant.TXD_UPDATE_RIDER_INFO_CAT_TYPE, "TxdChannelOrderServiceImpl updateRiderInfo fail");
            return ResultGenerator.genResult(TXD_REPORT_RIDER_LOCATION_ERROR);
        }
    }

    @Override
    public ResultStatus riderInfoChange(RiderInfoChangeRequest request) {
        try {
            long tenantId = request.getTenantId();
            long shopId = request.getShopId();
            String channelOrderId = request.getOrderId();
            String deliveryOrderId = String.valueOf(request.getDeliveryOrderId());
            long deliveryChannelId = request.getDeliveryChannelId();
            String riderName = request.getRiderName();
            String riderPhone = request.getRiderPhone();

            String extOrderId = queryOrderBaseByViewId(tenantId, shopId, channelOrderId).getExtOrderId();
            String channelPoiId = queryChannelPoiIdByQnhStoreId(tenantId, shopId);
            String deliveryCompany = getTxdDeliveryChannel(deliveryChannelId);

            AlibabaAelophyOrderDelivererChangeRequest req = new AlibabaAelophyOrderDelivererChangeRequest();
            AlibabaAelophyOrderDelivererChangeRequest.DelivererChangeRequest delivererChangeRequest = new AlibabaAelophyOrderDelivererChangeRequest.DelivererChangeRequest();
            // 淘鲜达渠道同步骑手信息变更接口传的是biz_order_id，对应牵牛花订单的extOrderId
            delivererChangeRequest.setBizOrderId(Long.parseLong(extOrderId));
            delivererChangeRequest.setStoreId(channelPoiId);
            delivererChangeRequest.setDelivererName(riderName);
            delivererChangeRequest.setDelivererPhone(riderPhone);
            delivererChangeRequest.setDelivererCompany(deliveryCompany);
            delivererChangeRequest.setLogisticsNo(deliveryOrderId);
            req.setDelivererChangeRequest(delivererChangeRequest);

            log.info("TxdChannelOrderServiceImpl riderInfoChange, req is {}", req.getDelivererChangeRequest());
            AlibabaAelophyOrderDelivererChangeResponse response = httpHelper.execute(tenantId, shopId, req);
            if (Objects.isNull(response)) {
                log.error("TxdChannelOrderServiceImpl riderInfoChange failed, response is null");
                Cat.logEvent(ProjectConstant.TXD_RIDER_INFO_CHANGE_CAT_TYPE, "TxdChannelOrderServiceImpl riderInfoChange fail");
                return ResultGenerator.genResult(TXD_REPORT_RIDER_INFO_CHANGE_ERROR);
            }
            log.info("TxdChannelOrderServiceImpl riderInfoChange, response is {}", convertTxdResponse(response));
            if (!isResponseSuccess(response)) {
                log.error("TxdChannelOrderServiceImpl riderInfoChange failed, response is {}", convertTxdResponse(response));
                Cat.logEvent(ProjectConstant.TXD_RIDER_INFO_CHANGE_CAT_TYPE, "TxdChannelOrderServiceImpl riderInfoChange fail");
                return ResultGenerator.genResult(TXD_REPORT_RIDER_INFO_CHANGE_ERROR);
            } else {
                Cat.logEvent(ProjectConstant.TXD_RIDER_INFO_CHANGE_CAT_TYPE, "TxdChannelOrderServiceImpl riderInfoChange success");
                return ResultGenerator.genSuccessResult();
            }
        } catch (Exception e) {
            log.error("TxdChannelOrderServiceImpl riderInfoChange exception", e);
            Cat.logEvent(ProjectConstant.TXD_RIDER_INFO_CHANGE_CAT_TYPE, "TxdChannelOrderServiceImpl riderInfoChange fail");
            return ResultGenerator.genResult(TXD_REPORT_RIDER_INFO_CHANGE_ERROR);
        }
    }

    @Override
    public ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request) {
        return null;
    }

    @Override
    public ResultStatus selfDelivery(SelfDeliveryRequest request) {
        return null;
    }

    @Override
    public QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request) {
        return null;
    }

    /**
     * todo 目前此接口没有返回数量暂未确认是否使用此接口,如需使用,因为淘鲜达接口仅返回可退金额,需要倒推计算出可退数量
     * @param request
     * @return
     */
    @Override
    public ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request) {
        ChannelPartRefundGoodsResult result = new ChannelPartRefundGoodsResult();
        Long orderFrom = null;
        if (StringUtils.isNotBlank(request.getOrderFrom())) {
            try {
                orderFrom = Long.parseLong(request.getOrderFrom());
            } catch (Exception e) {
                log.warn("parse orderFrom error, request: {}: ", request, e);
            }
        }
        AlibabaTclsAelophyRefundCsapplyrenderRequest channelRequest = buildCsapplyrenderRequest(request.getTenantId(),
                request.getStoreId(), request.getOrderId(), request.getChannelOrderItemIds(), orderFrom);

        try {
            log.info("调用渠道接口获取订单可退信息, request:{}", JacksonUtils.toJson(channelRequest));
            AlibabaTclsAelophyRefundCsapplyrenderResponse response = httpHelper.execute(request.getTenantId(),
                    request.getStoreId(), channelRequest);
            log.info("调用渠道接口获取订单可退信息, response:{}", JacksonUtils.toJson(response));
            if (!isResponseSuccess(response)) {
                log.error("调用渠道接口获取订单可退信息失败,response:{}", JacksonUtils.toJson(response));
                result.setStatus(ResultGenerator.genFailResult("调用渠道接口获取订单可退信息失败"));
            } else if (response.getApiResult() == null || response.getApiResult().getModel() == null) {
                log.error("调用渠道接口获取订单可退信息响应为空, response:{}", response);
                result.setStatus(ResultGenerator.genFailResult("调用渠道接口获取渠道可退信息为空"));
            } else {
                AlibabaTclsAelophyRefundCsapplyrenderResponse.RefundCsApplyRenderResponseDto renderRespDto = response.getApiResult().getModel();
                List<PartRefundGoodDetailDTO> detailDTOList = new ArrayList<>();
                if (notRefundItemIsOnlyOne(renderRespDto.getOutSubOrders())) {
                    //淘鲜达只有最后一件商品时,最大可退金额包含了运费,需要扣减
                    Optional<AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders> first = filterCantRefundItem(renderRespDto.getOutSubOrders()).stream().findFirst();
                    if (first.isPresent()) {
                        PartRefundGoodDetailDTO detailDTO = new PartRefundGoodDetailDTO();
                        detailDTO.setRefundPrice(Math.toIntExact(first.get().getMaxRefundFee() - first.get().getPostFee()));
                        detailDTO.setItemId(Long.parseLong(first.get().getOutSubOrderId()));
                        detailDTOList.add(detailDTO);
                    }
                } else {
                    detailDTOList = renderRespDto.getOutSubOrders().stream().map(d -> {
                        PartRefundGoodDetailDTO detailDTO = new PartRefundGoodDetailDTO();
                        //目前来看itemId是数字类型
                        detailDTO.setItemId(Long.parseLong(d.getOutSubOrderId()));
                        if (!d.getCanReverse()) {
                            detailDTO.setRefundPrice(0);
                            return detailDTO;
                        }
                        detailDTO.setRefundPrice(Math.toIntExact(d.getMaxRefundFee()));
                        return detailDTO;
                    }).collect(Collectors.toList());
                }
                result.setPartRefundGoodsList(detailDTOList);
                result.setStatus(ResultGenerator.genSuccessResult());
            }
        } catch (Exception e) {
            log.error("txdChannelOrderServiceImpl.queryChannelPartRefundGoodsDetail error", e);
            result.setStatus(ResultGenerator.genFailResult("调用渠道接口获取订单可退信息失败"));
        }
        return result;
    }

    private AlibabaTclsAelophyRefundCsapplyrenderRequest buildCsapplyrenderRequest(Long tenantId, Long storeId,
        String outOrderId, List<String> subOrderIds, Long orderFrom) {
        AlibabaTclsAelophyRefundCsapplyrenderRequest channelRequest = new AlibabaTclsAelophyRefundCsapplyrenderRequest();
        AlibabaTclsAelophyRefundCsapplyrenderRequest.RefundCsApplyRenderDto renderDto = new AlibabaTclsAelophyRefundCsapplyrenderRequest.RefundCsApplyRenderDto();
        String channelPoiCode = queryChannelPoiIdByQnhStoreId(tenantId, storeId);
        renderDto.setStoreId(channelPoiCode);
        renderDto.setOutOrderId(outOrderId);
        renderDto.setOutSubOrderIds(subOrderIds);
        renderDto.setOrderFrom(orderFrom);
        fillOrderFrom(renderDto::getOrderFrom, renderDto::setOrderFrom, tenantId, storeId, outOrderId);
        channelRequest.setRefundCsApplyRenderDTO(renderDto);
        return channelRequest;
    }

    @Override
    public QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request) {
        QueryChannelOrderListResult result = new QueryChannelOrderListResult();
        AlibabaWdkOrderListRequest channelRequest = new AlibabaWdkOrderListRequest();
        AlibabaWdkOrderListRequest.BatchQueryRequest queryReq = new AlibabaWdkOrderListRequest.BatchQueryRequest();
        //补偿任务调用前已经获取了渠道门店id
        queryReq.setStoreId(request.getChannelPoiId());
        queryReq.setPageIndex((long) request.getPage());
        queryReq.setPageSize((long) request.getPageSize());
        queryReq.setStartTime(new Date(request.getStartTime()));
        queryReq.setEndTime(new Date(request.getEndTime()));
        fillOrderFrom(queryReq::getOrderFrom, queryReq::setOrderFrom, request.getTenantId(), null);
        channelRequest.setBatchQueryRequest(queryReq);
        try {
            log.info("调用渠道接口获取订单列表,request:{}", JacksonUtils.toJson(channelRequest));
            AlibabaWdkOrderListResponse response = httpHelper.execute(request.getTenantId(), request.getShopId(),
                    channelRequest);
            log.info("调用渠道接口获取订单列表, response:{}", JacksonUtils.toJson(response));
            if (response == null || !response.isSuccess() || response.getResult() == null
                    || !StringUtils.equals(response.getResult().getReturnCode(), ORDER_LIST_SUCCESS_CODE)) {
                result.setStatus(ResultGenerator.genFailResult("调用渠道接口获取订单列表失败"));
            } else {
                List<AlibabaWdkOrderListResponse.Order> orders = response.getResult().getOrders();
                orders = CollectionUtils.isEmpty(orders) ? Lists.newArrayList() : orders;
                List<String> channelOrderIds = orders.stream()
                        .map(AlibabaWdkOrderListResponse.Order::getBizOrderId)
                        .map(String::valueOf)
                        .collect(Collectors.toList());
                result.setChannelOrderIdList(channelOrderIds);
                //淘鲜达只有page为0时才返回totalCount,此处使用next_index分页查
                result.setNextPage(Math.toIntExact(response.getResult().getNextIndex()));
                result.setStatus(ResultGenerator.genSuccessResult());
            }
        } catch (Exception e) {
            log.error("txdChannelOrderServiceImpl.queryChannelOrderList fail", e);
            result.setStatus(ResultGenerator.genResult(ResultCode.FAIL,"调用渠道获取订单列表失败"));
        }
        return result;
    }

    @Override
    public QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request) {
        return null;
    }

    @Override
    public ResultStatus moneyRefund(MoneyRefundRequest request) {
        ResultStatus result = new ResultStatus();
        AlibabaTclsAelophyRefundCsapplyNewRequest channelRequest = new AlibabaTclsAelophyRefundCsapplyNewRequest();
        AlibabaTclsAelophyRefundCsapplyNewRequest.RefundCsApplyNewDTO applyNewDTO = new AlibabaTclsAelophyRefundCsapplyNewRequest.RefundCsApplyNewDTO();
        applyNewDTO.setOutOrderId(request.getOrderId());
        String channelPoiCode = queryChannelPoiIdByQnhStoreId(request.getTenantId(), request.getStoreId());
        applyNewDTO.setStoreId(channelPoiCode);
        applyNewDTO.setReasonId((long) request.getReasonCode());
        fillOrderFrom(applyNewDTO::getOrderFrom, applyNewDTO::setOrderFrom, request.getTenantId(), request.getStoreId(),
                request.getOrderId());

        List<AlibabaTclsAelophyRefundCsapplyNewRequest.CsApplySubOrderDTO> subOrderDTOS = request.getRefundProductInfoList().stream().map(i -> {
            AlibabaTclsAelophyRefundCsapplyNewRequest.CsApplySubOrderDTO subDto = new AlibabaTclsAelophyRefundCsapplyNewRequest.CsApplySubOrderDTO();
            subDto.setOutSubOrderId(i.getChannelOrderItemId());
            subDto.setRefundFee(String.valueOf(i.getRefundAmount()));
            return subDto;
        }).collect(Collectors.toList());
        applyNewDTO.setSubRefundOrders(subOrderDTOS);
        applyNewDTO.setRequestId(MD5Util.toMD5String(applyNewDTO.toString()));
        channelRequest.setRefundCsApplyDTO(applyNewDTO);
        try {
            log.info("调用渠道接口金额退,request:{}", JacksonUtils.toJson(channelRequest));
            AlibabaTclsAelophyRefundCsapplyNewResponse response = httpHelper.execute(request.getTenantId(),
                    request.getStoreId(), channelRequest);
            if (!isResponseSuccess(response)) {
                log.error("调用渠道接口金额退失败,response:{}", JacksonUtils.toJson(response));
                result.setCode(ResultCode.FAIL.getCode());
                result.setMsg("调用渠道接口金额退失败");
            } else {
                result.setCode(ResultCode.SUCCESS.getCode());
            }
        } catch (Exception e) {
            log.error("txdChannelOrderServiceImpl.moneyRefund error", e);
            result.setCode(ResultCode.FAIL.getCode());
            result.setMsg("调用渠道接口金额退失败");
        }
        return result;
    }

    @Override
    public ChannelOrderMoneyRefundItemResult queryChannelOrderMoneyRefundItemList(ChannelOrderMoneyRefundItemRequest request) {
        ChannelOrderMoneyRefundItemResult result = new ChannelOrderMoneyRefundItemResult();
        Long orderFrom = null;
        if (StringUtils.isNotBlank(request.getOrderFrom())) {
            try {
                orderFrom = Long.parseLong(request.getOrderFrom());
            } catch (Exception e) {
                log.warn("parse orderFrom error, request: {}: ", request, e);
            }
        }
        AlibabaTclsAelophyRefundCsapplyrenderRequest channelRequest = buildCsapplyrenderRequest(request.getTenantId(),
                request.getStoreId(), request.getOrderId(), request.getChannelOrderItemIds(), orderFrom);
        try {
            log.info("调用渠道接口获取订单可退信息,request:{}", JacksonUtils.toJson(channelRequest));
            AlibabaTclsAelophyRefundCsapplyrenderResponse response = httpHelper.execute(request.getTenantId(),
                    request.getStoreId(), channelRequest);
            log.error("调用渠道接口获取订单可退信息,response:{}", JacksonUtils.toJson(response));
            if (!isResponseSuccess(response)) {
                result.setStatus(ResultGenerator.genFailResult("调用渠道接口获取订单可退信息失败"));
            } else if (response.getApiResult() == null || response.getApiResult().getModel() == null) {
                result.setStatus(ResultGenerator.genFailResult("调用渠道接口获取渠道可退信息为空"));
            } else {
                AlibabaTclsAelophyRefundCsapplyrenderResponse.RefundCsApplyRenderResponseDto renderRespDto = response.getApiResult().getModel();
                if (notRefundItemIsOnlyOne(renderRespDto.getOutSubOrders())) {
                    List<AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders> outSubOrders = filterCantRefundItem(renderRespDto.getOutSubOrders());
                    AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders subOrder = outSubOrders.get(0);
                    ChannelOrderMoneyRefundItemDTO itemDTO = new ChannelOrderMoneyRefundItemDTO();
                    itemDTO.setChannelItemId(subOrder.getOutSubOrderId());
                    itemDTO.setCanRefundMoney(Math.toIntExact(subOrder.getMaxRefundFee() - subOrder.getPostFee()));
                    result.setMoneyRefundItemDTOList(Lists.newArrayList(itemDTO));
                    result.setStatus(ResultGenerator.genSuccessResult());
                } else {
                    List<ChannelOrderMoneyRefundItemDTO> itemDTOList = renderRespDto.getOutSubOrders().stream().map(d -> {
                        ChannelOrderMoneyRefundItemDTO itemDTO = new ChannelOrderMoneyRefundItemDTO();
                        itemDTO.setChannelItemId(d.getOutSubOrderId());
                        if (!d.getCanReverse()) {
                            itemDTO.setCanRefundMoney(0);
                            return itemDTO;
                        }
                        itemDTO.setCanRefundMoney(Math.toIntExact(d.getMaxRefundFee()));
                        return itemDTO;
                    }).collect(Collectors.toList());
                    result.setMoneyRefundItemDTOList(itemDTOList);
                    result.setStatus(ResultGenerator.genSuccessResult());
                }
            }
        } catch (Exception e) {
            log.error("txdChannelOrderServiceImpl.queryChannelOrderMoneyRefundItemList error", e);
            result.setStatus(ResultGenerator.genFailResult("调用渠道接口获取订单可退信息失败"));
        }
        return result;
    }

    private boolean notRefundItemIsOnlyOne(List<AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders> outSubOrders) {
        List<AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders> list = filterCantRefundItem(outSubOrders);
        if (list.size() == 1) {
            return true;
        }
        return false;
    }

    private List<AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders> filterCantRefundItem(List<AlibabaTclsAelophyRefundCsapplyrenderResponse.Outsuborders> outSubOrders) {
        return outSubOrders.stream()
                .filter(i -> i.getCanReverse() && i.getMaxRefundFee() > 0)
                .collect(Collectors.toList());
    }

    /**
     * 根据牵牛花中台门店ID查询渠道门店ID
    */
    private String queryChannelPoiIdByQnhStoreId(Long tenantId, Long storeId) {

        List<ChannelPoiBaseInfoDTO> channelPoiBaseInfoDTOList = tenantRemoteService.batchQueryPoiChannelPoiRelationByPoiIds(
                DynamicChannelType.TAO_XIAN_DA.getChannelId(), tenantId, Collections.singletonList(storeId));
        if (CollectionUtils.isEmpty(channelPoiBaseInfoDTOList)) {
            throw new BizException("未查询到渠道门店ID");
        }
        return channelPoiBaseInfoDTOList.get(NumberUtils.INTEGER_ZERO).getChannelPoiCode();
    }

    private String getTxdDeliveryChannel(Long deliveryChannelId) {
        Map<String, String> txdDeliveryChannelMap = MccConfigUtil.getTxdDeliveryChannelMapping();
        if (MapUtils.isEmpty(txdDeliveryChannelMap)) {
            log.error("TxdChannelOrderServiceImpl getTxdDeliveryChannel error, txdDeliveryChannelMap is empty");
            return DEFAULT_DELIVERY_COMPANY;
        }

        return txdDeliveryChannelMap.getOrDefault(String.valueOf(deliveryChannelId), DEFAULT_DELIVERY_COMPANY);
    }

    private String convertTxdResponse(TaobaoResponse txdResponse) {
        if (Objects.isNull(txdResponse)) {
            return StringUtils.EMPTY;
        }
        return JacksonUtils.toJson(txdResponse);
    }

    /**
     * 核销自提码，上报用户签收
     * @param request
     * @return
     */
    @Override
    public ResultStatus verifySelfFetchCode(VerifySelfFetchCodeRequest request) {

        try {
            AlibabaWdkOrderStcodeAcceptRequest channelReq = new AlibabaWdkOrderStcodeAcceptRequest();
            channelReq.setSelfTakeCode(request.getCode());

            String channelPoiCode = queryChannelPoiIdByQnhStoreId(request.getTenantId(), request.getStoreId());
            channelReq.setStoreId(channelPoiCode);
            log.info("verifySelfFetchCode orderId: {} req: {}", request.getOrderId(), JsonUtil.toJson(channelReq));
            AlibabaWdkOrderStcodeAcceptResponse resp = httpHelper.execute(request.getTenantId(), request.getStoreId(), channelReq);
            log.info("verifySelfFetchCode resp: {}", JsonUtil.toJson(resp));
            if (!isResponseSuccess(resp)) {
                return ResultGenerator.genFailResult("自提核销签收失败");
            }
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("verifySelfFetchCode error, request: {}", request, e);
            return ResultGenerator.genFailResult("自提核销签收异常");
        }
    }

    /**
     * 校验自提码，通过自提码，从渠道获取订单号
     * @param request
     * @return
     */
    @Override
    public GetSelfFetchCodeResult checkSelfFetchCode(SelfFetchCodeGetRequest request) {
        GetSelfFetchCodeResult getSelfFetchCodeResult = new GetSelfFetchCodeResult();
        try {
            AlibabaWdkOrderStcodeQueryRequest channelReq = new AlibabaWdkOrderStcodeQueryRequest();
            channelReq.setSelfTakeCode(request.getCode());
            String channelPoiCode = queryChannelPoiIdByQnhStoreId(request.getTenantId(), request.getStoreId());
            channelReq.setStoreId(channelPoiCode);
            log.info("checkSelfFetchCode req: {}", JsonUtil.toJson(channelReq));
            AlibabaWdkOrderStcodeQueryResponse resp = httpHelper.execute(request.getTenantId(), request.getStoreId(), channelReq);
            log.info("checkSelfFetchCode resp: {}", JsonUtil.toJson(resp));
            if (!isResponseSuccess(resp)) {
                getSelfFetchCodeResult.setStatus(ResultGenerator.genFailResult("查询自提码失败"));
                return getSelfFetchCodeResult;
            }
            AlibabaWdkOrderStcodeQueryResponse.ApiResult apiResult = resp.getApiResult();

            GetSelfFetchCodeDTO getSelfFetchCodeDTO = new GetSelfFetchCodeDTO();
            getSelfFetchCodeDTO.setChannelOrderId(apiResult.getModel());

            getSelfFetchCodeResult.setGetSelfFetchCodeDTO(getSelfFetchCodeDTO);
            getSelfFetchCodeResult.setStatus(ResultGenerator.genSuccessResult());

            return getSelfFetchCodeResult;
        } catch (Exception e) {
            log.error("checkSelfFetchCode error: ", e);
        }

        getSelfFetchCodeResult.setStatus(ResultGenerator.genFailResult("查询自提码失败"));
        return getSelfFetchCodeResult;
    }

    @Override
    public GetOrderPrivacyPhoneResult queryOrderPrivacyPhone(GetOrderPrivacyPhoneRequest request) {
        log.info("TxdChannelOrderServiceImpl.queryOrderPrivacyPhone, request:{}", request);
        TxdOrderBaseInfo baseInfo = TxdOrderBaseInfo.builder()
                .tenantId(request.getTenantId())
                .storeId(request.getStoreId())
                .viewOrderId(request.getOrderId())
                .build();
        fillExtOrderId(baseInfo);
        fillChannelPoiCode(baseInfo);

        AlibabaAelophyOrderDesensitizephoneGetResponse.OrderDesensitizePhoneResult virtualMobile = fetchVirtualMobile(
                baseInfo);
        GetOrderPrivacyPhoneResult resp = new GetOrderPrivacyPhoneResult();
        if (Objects.isNull(virtualMobile)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "淘鲜达调用渠道查询订单隐私号失败"));
        }
        String privacyPhone = virtualMobile.getVirtualNumber();
        if (StringUtils.isBlank(privacyPhone)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "淘鲜达调用渠道查询订单隐私号为空"));
        }
        resp.setPrivacyPhone(privacyPhone);
        resp.setStatus(ResultGenerator.genSuccessResult());
        log.info("TxdChannelOrderServiceImpl.queryOrderPrivacyPhone, resp:{}", resp);
        return resp;
    }



    private AlibabaAelophyOrderWorkCallbackResponse reportOrderStatus(TxdOrderBaseInfo request, TxdOrderStatusEnum statusEnum)
            throws ApiException {
        AlibabaAelophyOrderWorkCallbackRequest channelReq = buildWorkCallbackReq(request, statusEnum);

        log.info("report orderStatus: {}, req: {}, orderId: {}", statusEnum.name(), JsonUtil.toJson(channelReq), request.getViewOrderId());
        AlibabaAelophyOrderWorkCallbackResponse resp = httpHelper.execute(request.getTenantId(), request.getStoreId(),
                channelReq);
        log.info("report orderStatus: {}, resp: {}, orderId: {}", statusEnum.name(), JsonUtil.toJson(resp), request.getViewOrderId());
        return resp;
    }

    private AlibabaAelophyOrderWorkCallbackRequest buildWorkCallbackReq(TxdOrderBaseInfo request, TxdOrderStatusEnum statusEnum) {
        AlibabaAelophyOrderWorkCallbackRequest channelReq = new AlibabaAelophyOrderWorkCallbackRequest();

        AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackRequest callbackRequest =
                new AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackRequest();

        fillExtOrderId(request);
        fillChannelPoiCode(request);
        if (statusEnum == PICKED) {
            handleItemInfo(request.getItemInfoList(), callbackRequest);
        } else if (statusEnum == SHIPPING || statusEnum == SIGN) {
            handleDeliveryInfo((UpdateDeliveryInfoRequest) request.getSourceObject(), callbackRequest);
        } else if (statusEnum == REJECTED) {
            handleRejectReason(request.getSourceObject(), callbackRequest);
        }

        callbackRequest.setBizOrderId(Long.parseLong(request.getExtOrderId()));
        callbackRequest.setStoreId(request.getChannelPoiCode());
        callbackRequest.setStatus(statusEnum.name());

        channelReq.setWorkCallbackRequest(callbackRequest);
        return channelReq;
    }

    private void handleRejectReason(Object sourceObject, AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackRequest callbackRequest) {
        if (sourceObject instanceof PoiCancelOrderRequest) {
            PoiCancelOrderRequest request = (PoiCancelOrderRequest) sourceObject;
            callbackRequest.setStatusRemark(request.getReason());
        }
    }

    private void fillChannelPoiCode(TxdOrderBaseInfo request) {
        if (StringUtils.isBlank(request.getChannelPoiCode())) {
            String channelPoiCode = queryChannelPoiIdByQnhStoreId(request.getTenantId(), request.getStoreId());
            request.setChannelPoiCode(channelPoiCode);
        }
    }

    private void fillExtOrderId(TxdOrderBaseInfo request) {
        if (StringUtils.isBlank(request.getExtOrderId())) {
            OrderBaseModel orderBaseModel = queryOrderBaseByViewId(request.getTenantId(), request.getStoreId(),
                    request.getViewOrderId());
            request.setExtOrderId(orderBaseModel.getExtOrderId());

/*            BizOrderModel bizOrderModel = queryOrderByViewId(request.getTenantId(), request.getStoreId(),
                    request.getViewOrderId());
            request.setExtOrderId(bizOrderModel.getExtOrderId());*/
        }
    }

}
