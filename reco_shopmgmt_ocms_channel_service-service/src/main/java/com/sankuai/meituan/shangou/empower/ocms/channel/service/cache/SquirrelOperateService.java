package com.sankuai.meituan.shangou.empower.ocms.channel.service.cache;

import com.dianping.squirrel.client.StoreClient;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.RetryCallback;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Squirrel操作抽象类
 * <AUTHOR>
 */
@Slf4j
public abstract class SquirrelOperateService {

    @Resource(name = "redisSgNewSupplyOfc")
    protected RedisStoreClient redisClient;

    protected RedisStoreClient getRedisClient(){
        return redisClient;
    }

    /**
     * 返回对应的categoryName
     */
    public abstract String getCategoryName();

    public <T> boolean set(String key,T data) {
        if(StringUtils.isEmpty(key) || data==null || StringUtils.isEmpty(getCategoryName())){
            log.error("SquirrelOperateService.set param error . key:{},data:{},categoryName:{}",key,data,getCategoryName());
            return false;
        }
        try {

            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtil.getSquirrelOperateRetryCount(), MccConfigUtil.getSquirrelOperateRetryPeriod()).execute((RetryCallback<Boolean, Exception>) retryContext -> {
                StoreKey storeKey = new StoreKey(getCategoryName(), key);
                return getRedisClient().set(storeKey,JsonUtil.toJson(data));
            });
            if(result!=null){
                return result;
            }
        }catch (Exception e){
            log.error("SquirrelOperateService.set error . key:{},data:{},categoryName:{}",key,data,getCategoryName(),e);
        }
        return false;
    }

    public <T> Boolean multiSet(Map<String, T> dataMap) {
        if (MapUtils.isEmpty(dataMap)) {
            log.info("SquirrelOperateService.multiSet dataMap is empty ,dataMap:{}", dataMap);
            return true;
        }

        try {
            Map<StoreKey, String> keyStringMap = dataMap.entrySet().stream()
                    .collect(Collectors.toMap(entry -> new StoreKey(getCategoryName(), entry.getKey()), entry -> JsonUtil.toJson(entry.getValue()), (k1, k2) -> k2));

            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtil.getSquirrelOperateRetryCount(), MccConfigUtil.getSquirrelOperateRetryPeriod()).execute((RetryCallback<Boolean, Exception>) retryContext -> getRedisClient().multiSet(keyStringMap));

            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.multiSet error ,dataMap:{}", dataMap, e);
        }
        return false;
    }

    public <T> boolean set(String key, T data, int expireSeconds) {
        if (StringUtils.isEmpty(key) || data == null || StringUtils.isEmpty(getCategoryName())) {
            log.error("SquirrelOperateService.set param error. key:{},data:{}, categoryName:{}", key, data, getCategoryName());
            return false;
        }
        try {
            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtil.getSquirrelOperateRetryCount(),
                    MccConfigUtil.getSquirrelOperateRetryPeriod()).execute((RetryCallback<Boolean, Exception>) retryContext -> {
                        StoreKey storeKey = new StoreKey(getCategoryName(), key);
                        return getRedisClient().set(storeKey, JsonUtil.toJson(data), expireSeconds);
                    });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.set error. key:{}, data:{}, categoryName:{}", key, data, getCategoryName(), e);
        }
        return false;
    }

    public <T> Optional<T> get(String key, Class<T> clazz){
        if(StringUtils.isEmpty(key)){
            log.info("SquirrelOperateService.get key is empty ,key:{}",key);
            return Optional.empty();
        }

        try {
            StoreKey storeKey = new StoreKey(getCategoryName(), key);
            String result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtil.getSquirrelOperateRetryCount(), MccConfigUtil.getSquirrelOperateRetryPeriod()).execute((RetryCallback<String, Exception>) retryContext -> getRedisClient().get(storeKey));
            if(StringUtils.isEmpty(result)){
                return Optional.empty();
            }
            T valObj=JsonUtil.fromJson(result,clazz);
            if(valObj==null){
                return Optional.empty();
            }
            return Optional.of(valObj);
        }catch (Exception e){
            log.info("SquirrelOperateService.get error ,key:{}",key,e);
        }
        return Optional.empty();
    }

    public <T> Map<StoreKey, Optional<T>> multiGet(List<String> keys, Class<T> clazz) {
        if (CollectionUtils.isEmpty(keys)) {
            log.info("SquirrelOperateService.multiGet keys is empty ,keys:{}", keys);
            return Collections.emptyMap();
        }

        try {
            List<StoreKey> storeKeys = keys.stream().map(item -> new StoreKey(getCategoryName(), item)).collect(Collectors.toList());
            Map<StoreKey, String> keyStringMap = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtil.getSquirrelOperateRetryCount(), MccConfigUtil.getSquirrelOperateRetryPeriod()).execute((RetryCallback<Map<StoreKey, String>, Exception>) retryContext -> getRedisClient().multiGet(storeKeys));

            if (MapUtils.isEmpty(keyStringMap)) {
                return Collections.emptyMap();
            }

            return keyStringMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                    entry -> {

                        if (StringUtils.isBlank(entry.getValue())) {
                            return Optional.empty();
                        }

                        T valObj = JsonUtil.fromJson(entry.getValue(), clazz);

                        return Optional.ofNullable(valObj);
                    }, (k1, k2) -> k1));

        } catch (Exception e) {
            log.error("SquirrelOperateService.multiGet error ,keys:{}", keys, e);
            return Collections.emptyMap();
        }
    }

    public <T> Map<StoreKey, Optional<T>> multiGetWithMiss(List<String> keys, Class<T> clazz) {
        if (CollectionUtils.isEmpty(keys)) {
            log.info("SquirrelOperateService.multiGetWithMiss keys is empty ,keys:{}", keys);
            return Collections.emptyMap();
        }

        try {
            List<StoreKey> storeKeys = keys.stream().map(item -> new StoreKey(getCategoryName(), item)).collect(Collectors.toList());
            Map<StoreKey, String> keyStringMap = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtil.getSquirrelOperateRetryCount(), MccConfigUtil.getSquirrelOperateRetryPeriod()).execute((RetryCallback<Map<StoreKey, String>, Exception>) retryContext -> getRedisClient().multiGet(storeKeys, StoreClient.DeliverOption.BEST_EFFORT_DELIVER_WHIT_MISS_KEYS));

            if (MapUtils.isEmpty(keyStringMap)) {
                return Collections.emptyMap();
            }

            return keyStringMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                    entry -> {
                        if (StringUtils.isBlank(entry.getValue())) {
                            return Optional.empty();
                        }
                        T valObj = JsonUtil.fromJson(entry.getValue(), clazz);
                        return Optional.ofNullable(valObj);
                    }, (k1, k2) -> k1));

        } catch (Exception e) {
            log.error("SquirrelOperateService.multiGetWithMiss error ,keys:{}", keys, e);
            return Collections.emptyMap();
        }
    }

    public boolean delete(String key) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(getCategoryName())) {
            log.error("SquirrelOperateService.delete param error. key:{}, categoryName:{}", key, getCategoryName());
            return false;
        }
        try {

            Boolean result = RetryTemplateUtil.simpleWithFixedRetry(MccConfigUtil.getSquirrelOperateRetryCount(), MccConfigUtil.getSquirrelOperateRetryPeriod()).execute((RetryCallback<Boolean, Exception>) retryContext -> {
                StoreKey storeKey = new StoreKey(getCategoryName(), key);
                return getRedisClient().delete(storeKey);
            });
            if (result != null) {
                return result;
            }
        } catch (Exception e) {
            log.error("SquirrelOperateService.delete error. key:{}, categoryName:{}", key, getCategoryName(), e);
        }
        return false;
    }
}
