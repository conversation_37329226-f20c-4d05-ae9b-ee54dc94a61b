package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.StringUtils;
import com.doudian.open.api.retail_settle_queryTradeBillItem.RetailSettleQueryTradeBillItemRequest;
import com.doudian.open.api.retail_settle_queryTradeBillItem.RetailSettleQueryTradeBillItemResponse;
import com.doudian.open.api.retail_settle_queryTradeBillItem.data.RetailSettleQueryTradeBillItemData;
import com.doudian.open.api.retail_settle_queryTradeBillItem.param.RetailSettleQueryTradeBillItemParam;
import com.doudian.open.core.AccessToken;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.bill.DouyinBalanceBillResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSettlementService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelBalanceBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelBalanceBillPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelCheckBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelCheckBillResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementByIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementAndDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.DouyinBalanceBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.DouyinBalanceBillPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.JDBalanceBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.JDBalanceBillPageResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("dyChannelSettlementService")
public class DouyinChannelSettlementServiceImpl extends DouyinBaseService  implements ChannelSettlementService {

    @Resource
    private DouyinConverterService douyinConverterService;


    @Override
    public ChannelSettlementPageResponse getChannelSettlementList(ChannelSettlementPageRequest request) {
        return null;
    }

    @Override
    public ChannelSettlementAndDetailResponse getChannelOrderSettlementListById(ChannelOrderSettlementByIdRequest request) {
        return null;
    }

    @Override
    public ChannelOrderSettlementPageResponse getChannelOrderSettlementList(ChannelOrderSettlementPageRequest request) {
        return null;
    }

    @Override
    public ChannelBalanceBillPageResponse getBalanceBillList(ChannelBalanceBillPageRequest request) {
        return null;
    }

    @Override
    public ChannelCheckBillResponse getCheckBillList(ChannelCheckBillPageRequest request) {
        return null;
    }

    @Override
    public JDBalanceBillPageResponse getBalanceBill(JDBalanceBillPageRequest request) {
        return null;
    }

    @Override
    public DouyinBalanceBillPageResponse getDouyinBalanceBill(DouyinBalanceBillPageRequest request) {
        DouyinBalanceBillPageResponse douyinBalanceBillPageResponse = new DouyinBalanceBillPageResponse();
        //调用抖音sdk查询
        RetailSettleQueryTradeBillItemRequest retailSettleQueryTradeBillItemRequest = new RetailSettleQueryTradeBillItemRequest();
        RetailSettleQueryTradeBillItemParam param = retailSettleQueryTradeBillItemRequest.getParam();
        //店铺订单号
        param.setShopOrderId(request.getShopOrderId());
        //查询开始时间，格式为：yyyy-MM-dd HH:mm:ss
        param.setStartTime(request.getStartTime());
        //查询结束时间，和end_time的时间间隔建议不超过31天，格式为：yyyy-MM-dd HH:mm:ss
        param.setEndTime(request.getEndTime());
        //时间类型 1 订单提交时间 2 订单完成时间 3 订单交易时间 4 订单结算时间
        param.setTimeType(request.getTimeType());
        //查询游标，查询第一页时传空，后续查询使用前一次查询返回的next_cursor
        param.setCursor(request.getCursor());
        //查询数量，支持范围1～100
        param.setSize(request.getSize());
        RetailSettleQueryTradeBillItemResponse response = new RetailSettleQueryTradeBillItemResponse();
        //获取accessToken
        AccessToken accessToken = prepareRequestConfigAndToken(retailSettleQueryTradeBillItemRequest, request.getTenantId());
        log.info("抖音 DouyinChannelSettlementServiceImpl-getAccessToken accessToken:{}", JSON.toJSONString(accessToken));
        try {
            log.info("抖音 DouyinChannelSettlementServiceImpl-getDouyinBalanceBill request:{}", JSON.toJSONString(param));
            response = retailSettleQueryTradeBillItemRequest.execute(accessToken);
            log.info("抖音 DouyinChannelSettlementServiceImpl-getDouyinBalanceBill request:{}， response:{}",JSON.toJSONString(request), JSON.toJSONString(response));

        } catch (Exception e) {
            log.error("抖音 DouyinChannelSettlementServiceImpl-getDouyinBalanceBill:{}", JSON.toJSONString(param), e);
            return douyinBalanceBillPageResponse.setStatus(ResultGenerator.genFailResult("调用渠道获取账单列表失败"));
        }
        RetailSettleQueryTradeBillItemData data = response.getData();
        //如果成功，返回结果
        DouyinBalanceBillResult dyBalanceBillResult = new DouyinBalanceBillResult();
        // 成功返回有数据的结果
        if (response.isSuccess() && StringUtils.equals("10000",response.getCode()) && data != null && CollectionUtils.isNotEmpty(data.getData())) {
            douyinBalanceBillPageResponse.setStatus(ResultGenerator.genSuccessResult());
            dyBalanceBillResult.setCode(data.getCode());
            dyBalanceBillResult.setCodeMsg(data.getCodeMsg());
            dyBalanceBillResult.setHasNext(data.getHasNext());
            dyBalanceBillResult.setNextCursor(data.getNextCursor());
            dyBalanceBillResult.setData(data.getData().stream()
                    .map(douyinConverterService::toRetailSettleDataItem)
                    .collect(Collectors.toList()));
            douyinBalanceBillPageResponse.setResult(douyinConverterService.douyinBalanceBillResultMapping(dyBalanceBillResult));
            log.info("抖音 DouyinChannelSettlementServiceImpl-getDouyinBalanceBill 抖音账单的请求报文{} ,请求结果 ： {}",JSON.toJSONString(request),JSON.toJSONString(douyinBalanceBillPageResponse));
            return douyinBalanceBillPageResponse;
        }
        //成功返回空数据结果
        if (response.isSuccess() && StringUtils.equals("10000",response.getCode()) && data != null && CollectionUtils.isEmpty(data.getData())) {
            douyinBalanceBillPageResponse.setStatus(ResultGenerator.genSuccessResult());
            dyBalanceBillResult.setCode(data.getCode());
            dyBalanceBillResult.setCodeMsg(data.getCodeMsg());
            dyBalanceBillResult.setHasNext(data.getHasNext());
            dyBalanceBillResult.setNextCursor(data.getNextCursor());
            dyBalanceBillResult.setData(new ArrayList<>());
            douyinBalanceBillPageResponse.setResult(douyinConverterService.douyinBalanceBillResultMapping(dyBalanceBillResult));
            log.info("抖音 DouyinChannelSettlementServiceImpl-getDouyinBalanceBill 抖音账单的请求报文{} ,请求结果 ： {}",JSON.toJSONString(request),JSON.toJSONString(douyinBalanceBillPageResponse));
            return douyinBalanceBillPageResponse;
        }
        //否则返回失败异常数据
        douyinBalanceBillPageResponse.setStatus(ResultGenerator.genFailResult(response.getMsg()));
        dyBalanceBillResult.setCode(response.getCode());
        dyBalanceBillResult.setCodeMsg(response.getSubMsg());
        douyinBalanceBillPageResponse.setResult(douyinConverterService.douyinBalanceBillResultMapping(dyBalanceBillResult));
        log.info("抖音 DouyinChannelSettlementServiceImpl-getDouyinBalanceBill 抖音账单的请求报文{} ,请求结果 ： {}",JSON.toJSONString(request),JSON.toJSONString(douyinBalanceBillPageResponse));
        return douyinBalanceBillPageResponse;
    }
}
