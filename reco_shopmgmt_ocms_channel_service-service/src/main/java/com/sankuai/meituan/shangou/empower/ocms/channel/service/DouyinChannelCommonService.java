package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTenantStoreInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTenantSysParams;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.application.AppInfoDTO;;

/**
 * @program: reco_shopmgmt_ocms_channel_service
 * @description:
 * @author: jinyi
 * @create: 2024-01-01 21:42
 **/
public interface DouyinChannelCommonService {



    /**
     * 根据 租户id 查询租户系统信息
     * @param tenantId
     * @return
     */
    DouyinTenantSysParams getTenantSysParamsByTenantId(Long tenantId);

    /**
     * 根据 租户id 查询抖音渠道的租户应用信息
     * @param tenantId
     * @param channelId
     * @return
     */
    AppInfoDTO queryAppInfoByTenant(Long tenantId, Integer channelId);

    /**
     * 根据 抖音总店id+appKey 查询租户系统信息
     * 
     * @param headShopId
     * @return
     */
    DouyinTenantSysParams getTenantSysParamsByHeadShopId(String headShopId, String appKey);

    /**
     * 根据 租户id+渠道门店id 查询租户、中台门店数据
     * 
     * @param tenantId
     * @param channelPoiCode
     * @return
     */
    DouyinTenantStoreInfo getTenantSysParamsByTenantChannelPoiCode(Long tenantId, String channelPoiCode);

    /**
     * 根据 租户id+中台门店id 查询租户、渠道门店数据
     * 
     * @param tenantId
     * @param storeId
     * @return
     */
    DouyinTenantStoreInfo getTenantSysParamsByTenantStoreId(Long tenantId, Long storeId);



    Long getTenantIdByHeadShopId(String headShopId, String appKey);
}
