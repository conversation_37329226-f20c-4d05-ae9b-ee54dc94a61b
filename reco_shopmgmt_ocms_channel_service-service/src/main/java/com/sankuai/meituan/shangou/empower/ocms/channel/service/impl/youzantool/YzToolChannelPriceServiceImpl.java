package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.YzSkuPriceAndStockUpdateInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpecTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.ChannelStoreSkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPriceService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.YzConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemUpdateBranchSkuResult;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 作者：guohuqi
 * 时间：2022/12/6 10:38 AM
 * 功能：
 **/
@Slf4j
@Service("yzToolChannelPriceService")
public class YzToolChannelPriceServiceImpl  extends YouZanToolBaseService implements ChannelPriceService {

    @Resource
    private YzToolSpuCommonService yzSpuCommonService;

    @Override
    public ResultData updatePrice(SkuPriceRequest request) {
        return ResultGenerator.genResultData(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }

    @Override
    public ResultData updatePriceMultiChannel(SkuPriceMultiChannelRequest request) {

        ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);

        // 上游接口已经按门店分组进行调用
        request.getParamList().stream()
                .collect(Collectors.groupingBy(SkuPriceMultiChannelDTO::getChannelSpuId))
                .forEach((channelSpuId, skuList) -> {
                    List<YzSkuPriceAndStockUpdateInfo> priceAndStockUpdateInfos = ConverterUtils.convertList(skuList, YzSkuPriceAndStockUpdateInfo::convertToPrice);
                    List<ChannelStoreSkuKey> storeSkuKeys = ConverterUtils.convertList(skuList, ChannelStoreSkuKey::convertPriceSkuToSkuKey);
                    boolean isMultiSpec = !yzSpuCommonService.needYzSingeSpec(request.getTenantId());
                    try {
                        YouzanItemUpdateBranchSkuResult result;

                        if (isMultiSpec) {
                            result = yzSpuCommonService.updatePriceAndStockInfo(request.getTenantId(), skuList.get(0).getStoreId(),
                                    priceAndStockUpdateInfos);
                        }
                        else {
                            result = yzSpuCommonService.updatePriceAndStockInfoNoSku(request.getTenantId(), skuList.get(0).getStoreId(),
                                    priceAndStockUpdateInfos);
                        }

                        YzConverterUtil.converterYouzanItemUpdateBranchSkuResult(resultData, storeSkuKeys, result, StringUtils.EMPTY,
                                null, isMultiSpec);
                    } catch (BizException e) {
                        log.warn("更新商品有赞渠道价格异常，skuList {}\", skuList, e");
                        YzConverterUtil.converterYouzanItemUpdateBranchSkuResult(resultData, storeSkuKeys, null, e.getMessage(),
                                e.getErrorCode(), isMultiSpec);
                    } catch (Exception e) {
                        log.warn("更新商品有赞渠道价格失败, skuList {}", skuList, e);
                        YzConverterUtil.converterYouzanItemUpdateBranchSkuResult(resultData, storeSkuKeys, null, "有赞渠道价格更新异常，价格修改失败！",
                                ResultCode.FAIL.getCode(), isMultiSpec);
                    }
                });

        return resultData;
    }

    @Override
    public List<ChannelStorePriceInfo> batchGetPriceInfo(BaseRequest request, List<Long> skuIds) {
        return null;
    }
}
