package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.qnh;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.dianping.zebra.util.StringUtils;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.QnhCouldType;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhCommonResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostQnhEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.QnhBaseUrlConfig;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.HttpClientUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.QnhSignUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * @Author: wangyihao04
 * @Date: 2021-12-24 11:41
 * @Mail: <EMAIL>
 * 提供牵牛花的上行基础网关组装参数、解析基础结果对象的服务
 */
@Service
public class QnhChannelGateService {


    @Autowired
    private CommonLogger logger;

    public static final String APP_ID_KEY = "appid";

    public static final String DEFAULT_QNH_URL_CONFIG_STR= "{\n" +
            "  \"isSelfControl\":false,\n" +
            "  \"tencentCouldTenantId\":[],\n" +
            "  \"alibabaCloudBaseUrl\":\"http://openapi.m-glory.net\",\n" +
            "  \"tencentCloudBaseUrl\":\"http://openapi.m-glory.cn\"\n" +
            "}";

    @Autowired
    private ConfigThriftService configThriftService;

    /**
     * @param postUrlEnum 请求URL
     * @param tenantId    租户id
     * @param bizParam    请求业务参数
     * @return
     */
    public QnhCommonResponse sendPost(Long tenantId, ChannelPostQnhEnum postUrlEnum, Object bizParam) {
        //基础参数校验
        validateSendPost(postUrlEnum, bizParam);

        Map<String, Object> sysParams = getChannelSysParams(tenantId);

        Map<String, Object> postParams = generatePostParams(sysParams, bizParam);

        //appid需要放在http头中
        String appId = (String) sysParams.get(APP_ID_KEY);
        Map<String, String> httpHeaders = Maps.newHashMap();
        httpHeaders.put(APP_ID_KEY, appId);
        String url = getPostUrl(postUrlEnum, tenantId);
        String resp = postRequestByJson(url, postParams, httpHeaders, tenantId);
        return parseResponse(resp, postUrlEnum);
    }

    private Map<String, Object> getChannelSysParams(Long tenantId) {
        String systemParams = MccConfigUtil.getQnhSystemParams(tenantId);
        if (StringUtils.isBlank(systemParams)) {
            throw new RuntimeException(String.format("tenantId %d 未配置对接牵牛花的系统参数", tenantId));
        }
        return JSON.parseObject(systemParams, new TypeReference<Map<String, Object>>() {
        });
    }

    private String postRequestByJson(String postUrl, Map<String, Object> postParams, Map<String, String> httpHeaders,
                                     Long tenantId) {
        String postParamsJson = JSON.toJSONString(postParams);
        String result = HttpClientUtil.postJson(postUrl, MccConfigUtil.getHttpConnectionTimeOut(),
                MccConfigUtil.getHttpSocketTimeOut(), postParamsJson, httpHeaders);
        logger.info("BaseChannelGateService.postRequest(), http post, postUrl:{}, tenantId:{}, postParams:{}, " +
                "httpHeaders:{}, result:{}", postUrl, tenantId, postParamsJson, httpHeaders, result);
        return result;

    }

    /**
     * 计算签名所需参数
     *
     * @param sysParam
     * @param bizParam
     * @return
     */
    private Map<String, Object> generatePostParams(Map<String, Object> sysParam, Object bizParam) {
        //获取请求所需参数
        String qid = UUID.randomUUID().toString();
        Integer timeStamp = DateUtils.unixTime();
        String sign = ComputeSignature(sysParam, bizParam, qid, timeStamp);
        //构造post param
        Map<String, Object> postParam = Maps.newHashMap();
        postParam.put("data", bizParam);
        postParam.put("timestamp", timeStamp);
        postParam.put("sign", sign);
        postParam.put("qid", qid);
        postParam.put("version", sysParam.get("version"));

        return postParam;
    }

    /**
     * 计算签名 data中的数据直接取json字符串
     *
     * @param sysParam
     * @param bizParam
     * @param qid
     * @param timeStamp
     * @return
     */
    private String ComputeSignature(Map<String, Object> sysParam, Object bizParam, String qid, Integer timeStamp) {
        String secret = (String) sysParam.get("secret");
        sysParam.remove("secret");
        if (bizParam == null) {
            sysParam.put("data", "");
        } else {
            sysParam.put("data", JSON.toJSONString(bizParam));
        }
        sysParam.put("qid", qid);
        sysParam.put("timestamp", timeStamp);
        return QnhSignUtils.getSignByMD5(sysParam, secret);
    }

    private String getPostUrl(ChannelPostInter postUrlEnum, Long tenantId) {
        final String qnhBaseUrl = Lion.getConfigRepository().get("qnh.url.base.tenant", DEFAULT_QNH_URL_CONFIG_STR);
        QnhBaseUrlConfig qnhBaseUrlConfig = JSONObject.parseObject(qnhBaseUrl, QnhBaseUrlConfig.class);
        if(qnhBaseUrlConfig.isSelfControl()){
            return selfControlUrl(postUrlEnum, tenantId, qnhBaseUrlConfig);
        }
        try {
            ConfigQueryRequest configQueryRequest = buildConfigQueryRequest(ConfigItemEnum.ONLINE_TYPE.getKey(), tenantId);
            TenantConfigResponse tenantConfigResponse = configThriftService.queryTenantConfig(configQueryRequest);
            if (tenantConfigResponse == null || tenantConfigResponse.getStatus() == null || 0 != tenantConfigResponse.getStatus().getCode()) {
                logger.warn("tenant service error,use default config,request:[tenantId:" + tenantId + "]");
                return selfControlUrl(postUrlEnum, tenantId, qnhBaseUrlConfig);
            }
            String configContent = tenantConfigResponse.getConfig().getConfigContent();
            JSONObject jsonObject = JSONObject.parseObject(configContent);
            String cloudType = String.valueOf(jsonObject.get("cloudType"));
            if (QnhCouldType.TENCENT_CLOUD.getType().equals(cloudType)) {
                return qnhBaseUrlConfig.getTencentCloudBaseUrl() + postUrlEnum.getUrl();
            }
            return qnhBaseUrlConfig.getAlibabaCloudBaseUrl() + postUrlEnum.getUrl();
        } catch (Exception e) {
            logger.error("get dynimic url config faild,use default.,request:[tenantId:" + tenantId + "]", e);
            return selfControlUrl(postUrlEnum, tenantId, qnhBaseUrlConfig);
        }
    }

    private String selfControlUrl(ChannelPostInter postUrlEnum, Long tenantId, QnhBaseUrlConfig qnhBaseUrlConfig) {
        logger.warn("tenantId:" + tenantId + ",qnh callback use selfControll,config:"+JSONObject.toJSONString(qnhBaseUrlConfig));
        if (qnhBaseUrlConfig.getTencentCouldTenantId().contains(tenantId.intValue())) {
            return qnhBaseUrlConfig.getTencentCloudBaseUrl() + postUrlEnum.getUrl();
        }
        return qnhBaseUrlConfig.getAlibabaCloudBaseUrl() + postUrlEnum.getUrl();
    }

    private ConfigQueryRequest buildConfigQueryRequest(Integer configId, Long tenantId) {
        ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
        configQueryRequest.setConfigId(configId);
        configQueryRequest.setTenantId(tenantId);
        configQueryRequest.setSubjectId(tenantId);
        return configQueryRequest;
    }

    private void validateSendPost(ChannelPostInter postUrlEnum, Object bizParam) {
        Preconditions.checkArgument(org.apache.commons.lang3.StringUtils.isNotBlank(postUrlEnum.getUrl()),
                "sendPost url is blank");
        Preconditions.checkNotNull(postUrlEnum.getResultClass(), "sendPost ResultClass is null");
        Preconditions.checkNotNull(bizParam, "sendPost bizParam is blank");
    }

    private QnhCommonResponse parseResponse(String resultJson, ChannelPostQnhEnum postQnhEnum) {
        if (StringUtils.isBlank(resultJson)) {
            return null;
        }

        QnhCommonResponse qnhCommonResponse = JSON.parseObject(resultJson, QnhCommonResponse.class);

        //如果没有结果数据，只有状态信息
        if (Objects.isNull(postQnhEnum.getDataKey())) {
            return qnhCommonResponse;
        }

        JSONObject jsonObject = JSON.parseObject(resultJson);
        String dateStr = jsonObject.getString(postQnhEnum.getDataKey());

        if (StringUtils.isBlank(dateStr)) {
            return qnhCommonResponse;
        }

        if (dateStr.startsWith("{") && dateStr.endsWith("}")) {
            qnhCommonResponse.setStructuredData(JSON.parseObject(dateStr, postQnhEnum.getResultClass()));
        } else if (dateStr.startsWith("[") && dateStr.endsWith("]")) {
            qnhCommonResponse.setStructuredData(JSON.parseArray(dateStr, postQnhEnum.getResultClass()));
        } else {
            qnhCommonResponse.setData(dateStr);
        }
        return qnhCommonResponse;
    }

}
