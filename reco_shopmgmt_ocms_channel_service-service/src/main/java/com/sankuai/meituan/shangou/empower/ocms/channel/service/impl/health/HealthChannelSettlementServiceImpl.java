package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.health;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSettlementService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelSettlementServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 美团结算业务接口实现-新供给业务
 *
 * <AUTHOR>
 * @since 2021/3/1
 */
@Service("healthChannelSettlementService")
public class HealthChannelSettlementServiceImpl extends MtBrandChannelSettlementServiceImpl implements ChannelSettlementService {
    @Override
    public JDBalanceBillPageResponse getBalanceBill(JDBalanceBillPageRequest request) {
        return null;
    }

    @Override
    public DouyinBalanceBillPageResponse getDouyinBalanceBill(DouyinBalanceBillPageRequest request) {
        return null;
    }
}