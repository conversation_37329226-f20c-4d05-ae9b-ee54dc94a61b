package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import com.alibaba.fastjson.JSON;
import com.doudian.open.utils.JsonUtil;
import com.google.common.collect.Sets;
import com.jayway.jsonpath.JsonPath;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.common.enums.ChannelAfterSaleEventEnum;
import com.meituan.shangou.saas.common.enums.ChannelOrderEventEnum;
import com.meituan.shangou.saas.dto.model.PartRefundProductInfo;
import com.meituan.shangou.saas.mq.ChannelAfterSaleEvent;
import com.meituan.shangou.saas.mq.ChannelOrderEvent;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderDeliveryModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelNotifyEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.helper.TxdHttpHelper;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.NewSupplyChannelOrderNotifyMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.NewSupplyChannelOrderRefundMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.NewSupplyTxdDeliveryChangeMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.TxdDeliveryCallbackMsg;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.OrderCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.tenant.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.ServiceTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderRefundSponsor;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.channelpoi.ChannelPoiBaseInfoDTO;
import com.taobao.api.request.AlibabaAelophyOrderGetRequest;
import com.taobao.api.response.AlibabaAelophyOrderGetResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.TXD_SUCCESS_MSG;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelNotifyEnum.TXD_ORDER_STATUS_UPDATE;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelNotifyEnum.TXD_WAREHOUSE_OUTBOUND_DISPATCH;
import static com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil.canCancelWorkByDeliveryStatus;
import static com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil.canCancelWorkByOrderStatus;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
public class TxdChannelOrderCallbackService {

    private static final Set<TxdOrderStatusEnum> DELIVERY_NOTIFY_ORDER_STATUS_SET = Sets.newHashSet(TxdOrderStatusEnum.SHIPPING, TxdOrderStatusEnum.SUCCESS);

    @Resource
    private NewSupplyChannelOrderNotifyMessageProducer newSupplyChannelOrderNotifyMessageProducer;

    @Autowired
    TenantRemoteService tenantRemoteService;

    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Autowired
    private NewSupplyChannelOrderRefundMessageProducer newSupplychannelOrderRefundMessageProducer;

    @Autowired
    private NewSupplyTxdDeliveryChangeMessageProducer newSupplyTxdDeliveryChangeMessageProducer;

    private static String TXD_MOCK_SWITCH = "txd_mock_switch";

    @Resource
    private TxdHttpHelper httpHelper;

    @Resource
    private OrderCommonService orderCommonService;


    /**
     * @param notifyEnum
     * @param request
     * @return
     *  牵牛花的status == 0 表示成功，如果有其他状态也表示成功，到genResultForTxd中去改
     *  牵牛花的code->errCode字段 eg. SUCCESS
     *  牵牛花的msg->errMsg字段
     */
    public ResultStatus txdNotifyHandler(ChannelNotifyEnum notifyEnum, OrderNotifyRequest request) {
/*        if (notifyEnum == ChannelNotifyEnum.TXD_ORDER_CANCEL_CHECK
                || notifyEnum == ChannelNotifyEnum.TXD_WAREHOUSE_OUTBOUND_CANCEL) {
            *//**
             * 作业拦截接口，qimen.alibaba.aelophy.order.work.cancel
             * 仓作业取消接口，qimen.alibaba.ax.warehouse.outbound.cancel
             * 针对仓开放的场景下，不是甩单，是仅仅仓开放，配是平台配的情况下，
             * 需要定义一个错误码：0001，代表仓已经出库。
             * 这个的目的是，当仓开放的时候，售中拦截，如果返回上述错误码，就知道仓出库了，这个时候就会去拦截平台的配。
             * 其它的错误码，均认为是拦截仓失败，直接报错。
             *
             * 注意0001还会拦截平台配送
             *//*
            return ResultGenerator.genResult(ResultCode.TXD_CAN_NOT_CANCEL_CODE);
        }*/


        ChannelPoiBaseInfoDTO channelPoiBaseInfoDTO = orderCommonService
                .queryTenantInfoByChannelPoiInfo(DynamicChannelType.TAO_XIAN_DA, request.getAppPoiCode());
        if (channelPoiBaseInfoDTO == null) {
            log.warn("未查询到门店信息，request: {}", request);
            return ResultGenerator.genResult(ResultCode.CHANNEL_POI_CODE_INVALID);
        }

        long tenantId = channelPoiBaseInfoDTO.getTenantId();
        long storeId = channelPoiBaseInfoDTO.getPoiId();

        if (tenantId == BigInteger.ZERO.longValue()) {
            return ResultGenerator.genResult(ResultCode.CHANNEL_APP_ID_INVALID);
        }

        switch (notifyEnum) {
            case TXD_ORDER_STATUS_UPDATE:
                return handleOrderNotify(request, tenantId, storeId);
            case TXD_REFUND_APPLY:
            case TXD_REFUND_COMPLETE:
            case TXD_REFUND_CANCEL:
            case TXD_REFUND_DISAGREE:
                return txdAfterSaleApply(tenantId, storeId, request, notifyEnum);
            case TXD_WAREHOUSE_OUTBOUND_DISPATCH:
                return handleOrderOutboundDispatch(request, tenantId, storeId);
            case TXD_ORDER_CANCEL_CHECK:
            case TXD_WAREHOUSE_OUTBOUND_CANCEL:
                return handleOrderWorkCancel(request, tenantId, storeId);
            default:
                log.warn("淘鲜达暂不支持的消息：{}", notifyEnum);
        }

        return ResultGenerator.genResult(ResultCode.SUCCESS);
    }

    private ResultStatus handleOrderWorkCancel(OrderNotifyRequest request, long tenantId, long storeId) {
        AlibabaAelophyOrderGetResponse.OrderResponse orderDto = queryOrderFromChannel(request, tenantId, storeId);

        if (orderDto == null) {
            return ResultGenerator.genResult(ResultCode.CHANNEL_ORDER_QUERY_FAILED).setData(ProjectConstant.NG);
        }
        // 尽量不使用渠道的状态，因为状态实测会有一定的延迟，比如接单后立刻查状态，发现状态还是「已支付」
        BizOrderModel bizOrderModel = queryOrderByViewId(tenantId, storeId, orderDto.getOutOrderId());
        // 某些牵牛花某些状态，例如 22，无法做出判断，还是需要借用渠道状态来判断，
        // 当然通过这个开关也可以用来实现仅使用渠道的状态而不是用牵牛花的状态
        if (needUseChannelStatus(bizOrderModel.getOrderStatus())) {
            return checkCancelWorkByChannelStatus(orderDto.getOrderStatus());
        }
        // 使用牵牛花状态，先判断订单状态
        if (canCancelWorkByOrderStatus(bizOrderModel.getOrderStatus())) {
            return ResultGenerator.genResult(ResultCode.SUCCESS).setData(ProjectConstant.OK);
        }
        // 然后判断配送状态
        BizOrderDeliveryModel deliveryModel = bizOrderModel.getDeliveryModel();
        if (deliveryModel == null) {
            return ResultGenerator.genResult(ResultCode.CHANNEL_ORDER_QUERY_FAILED).setData(ProjectConstant.NG);
        }
        if (canCancelWorkByDeliveryStatus(deliveryModel.getDeliveryStatus())) {
            return ResultGenerator.genResult(ResultCode.SUCCESS).setData(ProjectConstant.OK);
        }

        return ResultGenerator.genResult(ResultCode.TXD_CAN_NOT_CANCEL_CODE2);
    }

    private boolean needUseChannelStatus(Integer qnhOrderStatus) {
        return MccConfigUtil.needUseChannelStatusForWorkCancel(qnhOrderStatus);
    }

    private ResultStatus checkCancelWorkByChannelStatus(String channelOrderStatus) {
        boolean canCancel = MccConfigUtil.canCancelWorkByChannelStatus(channelOrderStatus);
        log.info("checkCancelWorkByChannelStatus result: {}, status: {}", canCancel, channelOrderStatus);
        if (canCancel) {
            return ResultGenerator.genResult(ResultCode.SUCCESS).setData(ProjectConstant.OK);
        }
        return ResultGenerator.genResult(ResultCode.TXD_CAN_NOT_CANCEL_CODE2);
    }

    private BizOrderModel queryOrderByViewId(long tenantId, long storeId, String orderId) {
        BizOrderModel bizOrderModel = orderCommonService.queryOrderByViewId(tenantId, storeId, orderId,
                DynamicOrderBizType.TAO_XIAN_DA);
        if (bizOrderModel == null) {
            log.error("订单{}不存在", orderId);
            throw new BizException("订单" + orderId + "不存在");
        }
        return bizOrderModel;
    }

    private ResultStatus handleOrderOutboundDispatch(OrderNotifyRequest request, long tenantId, long storeId) {

        String body = request.getBody();
        extractOrderIdIfBlank(request);
        String viewOrderId = null;
        try {
            // 淘鲜达上报商品信息时需要的子订单 id
            viewOrderId = JsonPath.read(body, "$.channelOrderNo");
        } catch (Exception e) {
            log.warn("read channelOrderNo error ", e);
        }
        if (StringUtils.isBlank(viewOrderId)) {

            AlibabaAelophyOrderGetResponse.OrderResponse orderDto = queryOrderFromChannel(request, tenantId, storeId);

            if (orderDto == null) {
                return ResultGenerator.genResult(ResultCode.CHANNEL_ORDER_QUERY_FAILED).setData(ProjectConstant.NG);
            }
            viewOrderId = orderDto.getOutOrderId();
        }
        ChannelOrderEvent orderEvent = new ChannelOrderEvent(viewOrderId, tenantId, storeId,
                ChannelOrderEventEnum.OUT_BOUND_DISPATCHED.getEventId(), DynamicChannelType.TAO_XIAN_DA.getChannelId(),
                System.currentTimeMillis(), TXD_WAREHOUSE_OUTBOUND_DISPATCH.getAbbrev(), Collections.emptyMap(),
                JacksonUtils.toJson(request));

        if (orderEvent.getShopId() <= BigInteger.ZERO.intValue()) {
            log.error("淘鲜达 find channel store lt 0, 门店未绑定, tenantId:{}", orderEvent.getTenantId());
            ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            result.setMsg(TXD_SUCCESS_MSG);
        }

        if (orderEvent.getShopId() >= BigInteger.ZERO.intValue()) {
            newSupplyChannelOrderNotifyMessageProducer.sendMessageSync(orderEvent);
        }

        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        result.setMsg(TXD_SUCCESS_MSG);
        return result;
    }

    private void extractOrderIdIfBlank(OrderNotifyRequest request) {
        if (StringUtils.isBlank(request.getOrderId())) {
            String body = request.getBody();
            try {
                // 设置 orderId，如果订单没有创建走补偿流程，需要这个 id，理论上 request 的 orderId 会在 shepherd 里设置好，这里仅做兜底
                request.setOrderId(JsonPath.read(body, "$.bizOrderId"));
            } catch (Exception e) {
                log.warn("read bizOrderId error ", e);
            }
        }
    }

    private ResultStatus handleOrderNotify(OrderNotifyRequest request, long tenantId, long storeId) {

        AlibabaAelophyOrderGetResponse.OrderResponse orderDto = queryOrderFromChannel(request, tenantId, storeId);

        if (orderDto == null) {
            return ResultGenerator.genResult(ResultCode.CHANNEL_ORDER_QUERY_FAILED).setData(ProjectConstant.NG);
        }

        String status = request.getStatus();
        TxdOrderStatusEnum statusEnum = TxdOrderStatusEnum.fromName(status);
        ChannelOrderEvent orderEvent = null;

        switch (statusEnum) {
            case PAID:
                orderEvent = new ChannelOrderEvent(orderDto.getOutOrderId(), tenantId, storeId,
                        ChannelOrderEventEnum.NEW_ORDER.getEventId(), DynamicChannelType.TAO_XIAN_DA.getChannelId(),
                        System.currentTimeMillis(), TXD_ORDER_STATUS_UPDATE.getAbbrev(), Collections.emptyMap(),
                        JacksonUtils.toJson(request));
                break;
            case ACCEPTED:
                orderEvent = new ChannelOrderEvent(orderDto.getOutOrderId(), tenantId, storeId,
                        ChannelOrderEventEnum.BIZ_CONFIRMED.getEventId(), DynamicChannelType.TAO_XIAN_DA.getChannelId(),
                        System.currentTimeMillis(), TXD_ORDER_STATUS_UPDATE.getAbbrev(), Collections.emptyMap(),
                        JacksonUtils.toJson(request));
                break;
            case SUCCESS:
                orderEvent = new ChannelOrderEvent(orderDto.getOutOrderId(), tenantId, storeId,
                        ChannelOrderEventEnum.FINISHED.getEventId(), DynamicChannelType.TAO_XIAN_DA.getChannelId(),
                        System.currentTimeMillis(), TXD_ORDER_STATUS_UPDATE.getAbbrev(), Collections.emptyMap(),
                        JacksonUtils.toJson(request));
                break;
            case CLOSE:
                orderEvent = new ChannelOrderEvent(orderDto.getOutOrderId(), tenantId, storeId,
                        ChannelOrderEventEnum.CANCELED.getEventId(), DynamicChannelType.TAO_XIAN_DA.getChannelId(),
                        System.currentTimeMillis(), TXD_ORDER_STATUS_UPDATE.getAbbrev(), Collections.emptyMap(),
                        JacksonUtils.toJson(request));
                break;
            default:
                log.warn("暂不支持的淘鲜达订单状态变更：{}", status);
                break;
        }
        if (orderEvent != null && orderEvent.getShopId() <= BigInteger.ZERO.intValue()) {
            log.error("淘鲜达 find channel store lt 0, 门店未绑定, tenantId:{}", orderEvent.getTenantId());
            ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
            result.setMsg(TXD_SUCCESS_MSG);
        }

        if (orderEvent != null && orderEvent.getShopId() >= BigInteger.ZERO.intValue()) {
            newSupplyChannelOrderNotifyMessageProducer.sendMessageSync(orderEvent);
        }

        if (DELIVERY_NOTIFY_ORDER_STATUS_SET.contains(statusEnum)) {
            String riderName = org.apache.commons.lang3.StringUtils.EMPTY;
            String riderPhone = org.apache.commons.lang3.StringUtils.EMPTY;
            if (Objects.nonNull(orderDto.getDeliveryInfo())) {
                riderName = orderDto.getDeliveryInfo().getDeliveryName();
                riderPhone = orderDto.getDeliveryInfo().getDeliveryPhone();
            }
            notifyTmsDeliveryChange(orderDto.getOutOrderId(), request.getStatus(), riderName, riderPhone);
        }

        ResultStatus result = ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        result.setMsg(TXD_SUCCESS_MSG);
        return result;

    }

    private AlibabaAelophyOrderGetResponse.OrderResponse queryOrderFromChannel(OrderNotifyRequest request, long tenantId, long storeId) {
        AlibabaAelophyOrderGetRequest channelReq = new AlibabaAelophyOrderGetRequest();
        AlibabaAelophyOrderGetRequest.OrderGetRequest orderGetRequest = new AlibabaAelophyOrderGetRequest.OrderGetRequest();
        orderGetRequest.setStoreId(request.getAppPoiCode());
        orderGetRequest.setBizOrderId(Long.parseLong(request.getOrderId()));
        channelReq.setOrderGetRequest(orderGetRequest);
        AlibabaAelophyOrderGetResponse.OrderResponse orderDto = null;
        try {

            log.info("getChannelOrderDetail req: {}", JsonUtil.toJson(channelReq));
            AlibabaAelophyOrderGetResponse resp = httpHelper.execute(tenantId, storeId, channelReq);
            log.info("getChannelOrderDetail resp: {}", JsonUtil.toJson(resp));
            AlibabaAelophyOrderGetResponse.TopBaseResult result = resp.getApiResult();
            orderDto = result.getModel();
        } catch (Exception e) {
            log.error("handleOrderNotify getChannelOrderDetail {} error", request.getOrderId(), e);
        }
        return orderDto;
    }

    private ResultStatus txdAfterSaleApply(Long tenantId, Long shopId, OrderNotifyRequest request, ChannelNotifyEnum channelNotifyEnum) {
        if (shopId <= BigInteger.ZERO.intValue()) {
            log.warn("门店未绑定, tenantId:{}", tenantId);
            ResultStatus result = ResultGenerator.genSuccessResult();
            result.setMsg(TXD_SUCCESS_MSG);
        }

        Integer sponsor = OrderRefundSponsor.CUSTOMER.getValue();

        boolean isAppeal = false;

        ChannelAfterSaleEvent afterSaleEvent = new ChannelAfterSaleEvent();

        ChannelAfterSaleEventEnum channelAfterSaleEvent = null;

        String viewOrderId = null;

        String afterSaleId = null;

        long applyTime = 0;

        Integer refundAmount = 0;

        String reason = "";

        List<PartRefundProductInfo> productInfos = new ArrayList<>();

        List<String> refundPicList = new ArrayList<>();

        Integer freight = 0;

        String txdRefundId = "";

        String orderFrom = "";

        if (ChannelNotifyEnum.TXD_REFUND_APPLY.equals(channelNotifyEnum)) {
            channelAfterSaleEvent = ChannelAfterSaleEventEnum.APPLY;

            TxdRefundApplyMsg txdRefundApplyMsg = JSON.parseObject(request.getBody(), TxdRefundApplyMsg.class);

            viewOrderId = txdRefundApplyMsg.getOutOrderId();
            afterSaleId = txdRefundApplyMsg.getRefundId();
            applyTime = System.currentTimeMillis();
            refundAmount = txdRefundApplyMsg.getRefundFee();
            reason = Optional.ofNullable(txdRefundApplyMsg.getRefundReason()).orElse("");
            if (StringUtils.isNotEmpty(txdRefundApplyMsg.getRemarks())) {
                reason = reason + ":" + txdRefundApplyMsg.getRemarks();
            }
            freight = txdRefundApplyMsg.getRefundDeliveryFee();
            refundPicList = Arrays.stream(ofNullable(txdRefundApplyMsg.getRefundPictures()).orElse("").split(",")).filter(pic -> StringUtils.isNotEmpty(pic)).collect(Collectors.toList());

            ofNullable(txdRefundApplyMsg.getSubRefundOrders()).orElse(new ArrayList<>()).forEach(txdSubRefundOrdersDTO -> {
                        PartRefundProductInfo partRefundProductInfo = new PartRefundProductInfo();
                        partRefundProductInfo.setSkuRefundAmount(txdSubRefundOrdersDTO.getRefundFee());
                        partRefundProductInfo.setChannelOrderItemId(txdSubRefundOrdersDTO.getOutSubOrderId());
                        productInfos.add(partRefundProductInfo);
                    }
            );
        } else if (ChannelNotifyEnum.TXD_REFUND_CANCEL.equals(channelNotifyEnum)) {
            TxdRefundCancelMsg txdRefundCancelMsg = JSON.parseObject(request.getBody(), TxdRefundCancelMsg.class);
            viewOrderId = txdRefundCancelMsg.getOutOrderId();
            afterSaleId = txdRefundCancelMsg.getRefundId();

            channelAfterSaleEvent = ChannelAfterSaleEventEnum.APPLY_CANCELED;

        } else if (ChannelNotifyEnum.TXD_REFUND_COMPLETE.equals(channelNotifyEnum)) {

            TxdRefundCompleteMsg txdRefundCompleteMsg = JSON.parseObject(request.getBody(), TxdRefundCompleteMsg.class);
            // 淘鲜达退单完成没有给订单号，给的是订单明细的channelItemId
            viewOrderId = "";
            //  查退单详情时，单模型（orderFrom=4）使用 outId，在 channel 里使用 afterSaleId
            afterSaleId = txdRefundCompleteMsg.getOutMainRefundId();
            // 查退单详情时，双模型（orderFrom=31）使用 bizId，在 channel 里使用 txdRefundId
            txdRefundId = txdRefundCompleteMsg.getBizSubRefundId();

            orderFrom = txdRefundCompleteMsg.getOrderFrom();

            String status = txdRefundCompleteMsg.getOrderStatus();

            if ("SUCCESS".equals(status)) {
                channelAfterSaleEvent = ChannelAfterSaleEventEnum.AGREE;
            } else {
                channelAfterSaleEvent = ChannelAfterSaleEventEnum.REJECT;
            }

        } else if (ChannelNotifyEnum.TXD_REFUND_DISAGREE.equals(channelNotifyEnum)) {
            TxdRefundRejectMsg txdRefundRejectMsg = JSON.parseObject(request.getBody(), TxdRefundRejectMsg.class);
            afterSaleId = txdRefundRejectMsg.getRefundId();
            channelAfterSaleEvent = ChannelAfterSaleEventEnum.REJECT;
        } else {
            log.warn("售后暂不支持的消息：{}", request);
            ResultStatus result = ResultGenerator.genSuccessResult();
            result.setMsg(TXD_SUCCESS_MSG);
            return result;
        }

        // 淘鲜达默认是仅退款，不支持退货退款
        ServiceTypeEnum serviceTypeEnum = ServiceTypeEnum.REFUND;

        afterSaleEvent.setAfterSaleServiceType(serviceTypeEnum.getCode());
        afterSaleEvent.setChannelOrderId(viewOrderId);
        afterSaleEvent.setChannelType(ChannelTypeEnum.TXD.getCode());
        afterSaleEvent.setEventId(channelAfterSaleEvent.getEventId());
        afterSaleEvent.setAppeal(isAppeal);
        afterSaleEvent.setTenantId(tenantId);
        afterSaleEvent.setTimestamp(System.currentTimeMillis());
        afterSaleEvent.setAfterSaleId(afterSaleId);
        // 是否全单退还是部分退，orderbiz中进行判断，这里不设置值
        afterSaleEvent.setRefundType(RefundTypeEnum.PART.getValue());
        afterSaleEvent.setRefundApplyTime(applyTime);
        afterSaleEvent.setRefundAmount(refundAmount);
        afterSaleEvent.setSponsor(sponsor);
        afterSaleEvent.setShopId(shopId);
        afterSaleEvent.setReason(reason);
        afterSaleEvent.setRefundPicList(refundPicList);
        afterSaleEvent.setRefundProducts(productInfos);
        afterSaleEvent.setFreightFee(freight);
        afterSaleEvent.setTxdRefundId(txdRefundId);
        afterSaleEvent.setOrderFrom(orderFrom);

        afterSaleEvent.setProcessDeadline(applyTime + 24 * 3600000);
        newSupplychannelOrderRefundMessageProducer.sendMessageSync(afterSaleEvent);

        ResultStatus result = ResultGenerator.genSuccessResult();
        result.setMsg(TXD_SUCCESS_MSG);
        return result;
    }

    private void notifyTmsDeliveryChange(String channelOrderId, String channelOrderStatus, String riderName, String riderPhone) {
        try {
            TxdDeliveryCallbackMsg msg = new TxdDeliveryCallbackMsg();
            msg.setChannelOrderId(channelOrderId);
            msg.setChannelOrderStatus(channelOrderStatus);
            if (StringUtils.isNotEmpty(riderName) && StringUtils.isNotEmpty(riderPhone)) {
                msg.setRiderName(riderName);
                msg.setRiderPhone(riderPhone);
            }
            msg.setUpdateTime(TimeUtil.toMilliseconds(LocalDateTime.now()));

            log.info("TxdChannelOrderCallbackService notifyTmsDeliveryChange, msg is {}", msg);
            newSupplyTxdDeliveryChangeMessageProducer.sendMessage(msg);
        } catch (Exception e) {
            log.error("TxdChannelOrderCallbackService notifyTmsDeliveryChange error", e);
        }
    }

}
