package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.EblsImMessageContentTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.IMMessagePushProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ebls.IMMessageContent;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ebls.IMMessagePayload;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ebls.ReplyMessagePayload;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.IEBLSMessageService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.IEblsIMMessageTranfer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @AUTHOR fengjunkai03
 * @DATE 2024/3/2
 */
@Service
@Slf4j
public class EblsIMMessageTranferImpl implements IEblsIMMessageTranfer {

    @Resource
    private IMMessagePushProducer producer;

    @Resource(name = "EBLSMessageService")
    private IEBLSMessageService eblsMessageService;

    @Autowired
    private CopChannelStoreService copChannelStoreService;

    /**
     * 将IM消息推送到消息队列
     *
     * @param msg
     * @return
     */
    @Override
    public Boolean IMMessagePush2MQ(IMMessageContent msg) {
        IMMessagePayload payload = msg.getBody().getPayload();
        if(!(payload instanceof ReplyMessagePayload)){
            return false;
        }
        ReplyMessagePayload replyPayload = (ReplyMessagePayload) payload;
        if(!EblsImMessageContentTypeEnum.TEXT.getCode().equals(payload.getContentType())){
            try {
                ChannelStoreDO channelStoreDO = copChannelStoreService.selectByChannelPoiCode(200, msg.getBody().getPlatformShopId());
                if (channelStoreDO == null) {
                    log.warn("饿了么 poiCode:{}, 没有绑定中台门店。", msg.getBody().getPlatformShopId());
                    return false;
                }
                msg.setTenantID(channelStoreDO.getTenantId());
                msg.setPoiId(channelStoreDO.getStoreId());
                eblsMessageService.convertMediaId2Url(msg);
            }catch(Exception e){
                log.error("IMMessagePush2MQ convertMediaId2Url error, err:{}", JSONObject.toJSONString(msg), e);
                return false;
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msgId", msg.getBody().getPayload().getMsgId());
        jsonObject.put("contentType", payload.getContentType());
        jsonObject.put("outPoiCode", msg.getBody().getPlatformShopId());

        jsonObject.put("cuid",replyPayload.getSenderId());
        jsonObject.put("timestamp",msg.getTimestamp());
        jsonObject.put("payload", JSONObject.toJSONString(payload));
        producer.sendMessage(jsonObject.getInnerMap());
        return true;
    }


    /**
     * 将已读消息推送到消息队列
     * @param msg
     * @return
     */
    @Override
    public Boolean ReadMessagePush2MQ(IMMessageContent msg) {
        IMMessagePayload payload = msg.getBody().getPayload();
        if(!(payload instanceof ReplyMessagePayload)){
            return false;
        }
        ReplyMessagePayload replyPayload = (ReplyMessagePayload) payload;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msgId", msg.getBody().getPayload().getMsgId());
        //0指消息已读
        jsonObject.put("contentType", 0);
        jsonObject.put("outPoiCode", msg.getBody().getPlatformShopId());

        jsonObject.put("cuid",replyPayload.getSenderId());
        jsonObject.put("timestamp",msg.getTimestamp());
        producer.sendMessage(jsonObject.getInnerMap());
        return null;
    }
}
