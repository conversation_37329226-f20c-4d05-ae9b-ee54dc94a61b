package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;

import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.Cache;

@Slf4j
public class SquirrelCache implements Cache {
    // 此category 的过期时间 1H
    private static final String CATEGORY = "ocms-mybatis-cache";
    private static RedisStoreClient redisStoreClient;
    private final String id;
    private ReadWriteLock readWriteLock = new ReentrantReadWriteLock();
    private StoreKey storeKey;

    public SquirrelCache(final String id) {
        if (id == null) {
            throw new IllegalArgumentException("Cache instance require an ID");
        }
        this.id = id;
        storeKey = new StoreKey(CATEGORY, id);
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public int getSize() {
        return Integer.valueOf(getRedisStoreClient().hgetAll(storeKey).size());
    }

    @Override
    public void putObject(Object key, Object value) {
        getRedisStoreClient().hset(storeKey, key.toString(), value, MccConfigUtil.getMybatisCache());
    }

    @Override
    public Object getObject(Object key) {
        return getRedisStoreClient().hget(storeKey, key.toString());
    }

    @Override
    public Object removeObject(Object key) {
        return getRedisStoreClient().hdel(storeKey, key.toString());
    }

    @Override
    public void clear() {
        getRedisStoreClient().delete(storeKey);
    }

    @Override
    public ReadWriteLock getReadWriteLock() {
        return this.readWriteLock;
    }

    private RedisStoreClient getRedisStoreClient() {
        if (redisStoreClient == null) {
            log.info("第一次使用，初始化");
            redisStoreClient = (RedisStoreClient) ServicesLocator.getBean("redisClient");
        }
        log.info("后续使用，直接获取，不用初始化");
        return redisStoreClient;
    }
}