package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * buy订单扩展字段
 * 结构文档：https://km.sankuai.com/page/1202169107 参考order_ext结构
 *
 * <AUTHOR>
 * @date 2021/9/14 11:26 上午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SgBuyOrderExt {
    private GInfo gInfo;
    private AddressInfo addressInfo;
    private PerformExt performExt;
    private BizInfo bizInfo;
    // 还有其它字段，如需解析再加

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class GInfo {
        private boolean first_order; // 是否是首单
        private String deviceInfoByQQ; // 微信风控参数
        private String channel;//下单渠道来源
        private Boolean sfo;//是否是门店首单
        private int isPreOrder;//0：非预订单，1：预订单
        private Integer vip; // 1: 是会员，0:非会员
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class AddressInfo {
        private String f1; // 省
        private String f2; // 市
        private String f3; // 区县
        private String f4; // 街道
        private String f5; // 定位地址
        private String f6; // 用户填写的地址
        private String f7; // 收货地址经度
        private String f8; // 收货地址纬度
        private String f9; // 四级地址code
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class PerformExt {
        private int promiseTime;//承诺发货时间
        private Long epft;//预计履约完成时间,epft是estimatePerformanceFinishTime简写
        private Integer daySeq;//推单序号
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class BizInfo {
        private Integer recipientGender;//收货人性别
    }
}
