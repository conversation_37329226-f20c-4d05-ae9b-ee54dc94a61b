package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.dianping.zebra.tool.util.StringUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinCategoryPropQueryResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinCategoryQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dianping.squirrel.common.util.Preconditions;
import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.thrift.response.ThriftResponse;
import com.meituan.linz.thrift.response.ThriftResponseBuilder;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelVirtualAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelShopCategoryQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelShopCategoryQueryResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinProductUpdateRuleParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinProductUpdateRuleResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinQualificationParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinQualificationResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinRecommendChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.QueryRecommendChannelCategoryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.RecommendChannelCategoryResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ChannelProductUpdateRuleDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ChannelProductUpdateRuleRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelVirtualConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetQualificationRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetQualificationResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QualificationConfig;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.VirtualTokenGetRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * 抖音渠道虚拟配置内部服务接口
 *
 * <AUTHOR> wuyongjiang
 * @since : 2023/4/26
 */
@Slf4j
@Service("dyChannelVirtualConfigService")
public class DouyinChannelVirtualConfigServiceImpl implements ChannelVirtualConfigService {
    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private DouyinChannelGateService douyinChannelGateService;

    @Autowired
    private DouyinAccessTokenService douyinAccessTokenService;

    @Override
    public RecommendCategoryResponse recommendCategoryByVirtualConfig(RecommendCategoryRequest request) {
        RecommendCategoryResponse resp = new RecommendCategoryResponse();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());

        Map<String, Object> virtualSysParams;
        // 如果request传入了虚拟参数就直接用
        if (Objects.nonNull(request.getVirtualSysParam()) && StringUtil.isNotBlank(request.getVirtualSysParam())) {
            try {
                virtualSysParams = JSON.parseObject(request.getVirtualSysParam(), Map.class);
            } catch (Exception e) {
                log.error("Douyin recommendCategory parse virtualSysParam error, request:{}", request, e);
                throw new IllegalArgumentException("虚拟参数格式错误");
            }
        } else {
            // 否则从根据租户查询
            virtualSysParams = douyinChannelGateService.getChannelVirtualSysParam(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId());
        }
        ChannelResponseDTO<RecommendChannelCategoryResult> channelResponse = douyinChannelGateService.sendPostByVirtualSysParam(ChannelPostDouyinEnum.GET_RECOMMEND_CATEGORY, baseRequest,
                                                                        QueryRecommendChannelCategoryParam.of(request.getName()), virtualSysParams);

        if (channelResponse.isSuccess()) {
            if (channelResponse.getCoreData() != null && CollectionUtils.isNotEmpty(channelResponse.getCoreData().getCategoryDetails())) {
                List<DouyinRecommendChannelCategoryDTO> validRecommendChannelCategories = Fun.filter(channelResponse.getCoreData().getCategoryDetails(), douyinRecommendChannelCategoryDTO -> douyinRecommendChannelCategoryDTO.getCategory_detail() != null);
                DouyinRecommendChannelCategoryDTO recommendChannelCategoryDTO;
                List<DouyinRecommendChannelCategoryDTO> qualificationRecommendChannelCategories = Fun.filter(validRecommendChannelCategories, DouyinRecommendChannelCategoryDTO::isQualification);
                // 首先从有资质类目里面取
                if (CollectionUtils.isNotEmpty(qualificationRecommendChannelCategories)) {
                    recommendChannelCategoryDTO = qualificationRecommendChannelCategories.get(0);
                } else {
                    recommendChannelCategoryDTO = validRecommendChannelCategories.get(0);
                }

                if (recommendChannelCategoryDTO.getCategory_detail().getLastLevelCid() != null) {
                    resp.setChannelCategoryCode(recommendChannelCategoryDTO.getCategory_detail().getLastLevelCid());
                }
            }
            resp.setUpcCode(request.getUpcCode());
            resp.setName(request.getName());
            resp.setStatus(new ResultStatus(ResultCode.SUCCESS.getCode(), "成功", null));
        } else {
            if (StringUtils.isNotEmpty(channelResponse.getSub_code())) {
                resp.setStatus(new ResultStatus(Integer.parseInt(channelResponse.getSub_code()), channelResponse.getSub_msg(), null));
            } else {
                resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, channelResponse.getSub_msg()));
            }
        }
        return resp;
    }

    @Override
    public ThriftResponse<ChannelProductUpdateRuleDTO> queryChannelProductUpdateRuleByVirtualConfig(ChannelProductUpdateRuleRequest request) {

        Assert.throwIfNull(request,"请求为空");
        Assert.throwIfNull(request.getCategoryId(),"渠道类目id为空");

        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setChannelId(request.getChannelId());
        baseRequest.setTenantId(request.getTenantId());
        ChannelResponseDTO<DouyinProductUpdateRuleResult> channelResponseDTO =
                douyinChannelGateService.sendPostByVirtualConfig(ChannelPostDouyinEnum.GET_PRODUCT_UPDATE_RULE, baseRequest,
                        DouyinProductUpdateRuleParam.builder()
                                .category_id(Long.parseLong(request.getCategoryId()))
                                .standard_brand_id(com.dianping.zebra.util.StringUtils.isNotBlank(request.getBrandId())?Long.parseLong(request.getBrandId()):null)
                                .spu_id(com.dianping.zebra.util.StringUtils.isNotBlank(request.getSpuId())?Long.parseLong(request.getSpuId()):null)
                                .build()
                );

        Bssert.throwIfTrue(!channelResponseDTO.isSuccess(),"查询渠道接口失败");

        return ThriftResponseBuilder.buildSuccessResp(DouyinConvertUtil.convertToChannelProductUpdateRuleDTO(channelResponseDTO.getCoreData()));
    }

    @Override
    public GetQualificationResponse queryChannelQualificationByVirtualConfig(GetQualificationRequest request) {
        Assert.throwIfNull(request,"请求为空");
        Assert.throwIfNull(request.getCategoryId(),"渠道类目id为空");

        GetQualificationResponse response = new GetQualificationResponse();

        List<QualificationConfig> qualificationConfigList = new LinkedList<>();

        ChannelResponseDTO<DouyinQualificationResult> channelResponseDTO =
                douyinChannelGateService.sendPostByVirtualConfig(ChannelPostDouyinEnum.GET_PRODUCT_QUALIFICATION_CONFIG, request.getBaseInfo(), null,
                        DouyinQualificationParam.builder().category_id(request.getCategoryId()).build());
        Bssert.throwIfTrue(!channelResponseDTO.isSuccess(),"查询渠道接口失败");

        qualificationConfigList.addAll(channelResponseDTO.getCoreData().getConfig_list().stream().map(DouyinConvertUtil::buildQulifactionInfo).collect(Collectors.toList()));
        response.setQualificationConfigList(qualificationConfigList);
        response.setStatus(ResultGenerator.genSuccessResult());
        return response;
    }

    @Override
    public CategoryAttrResponse getCategoryAttrByVirtualConfig(CategoryAttrRequest request) {
        Assert.throwIfNull(request.getCategoryId(),"类目id不允许为空");
        CategoryAttrResponse response = new CategoryAttrResponse();
        List<CategoryAttrInfo> categoryAttrInfoList = new LinkedList<>();
        List<CategoryAttrInfo> saleAttrInfoList = getSaleAttrInfoList(request.getBaseInfo(), request.getCategoryId());

        ChannelResponseDTO<DouyinCategoryPropQueryResult> channelResponseDTO =
                douyinChannelGateService.sendPostByVirtualConfig(ChannelPostDouyinEnum.GET_PRODUCT_CATEGORY_PROPERTY, request.getBaseInfo(), null,
                        DouyinCategoryQueryParam.builder().category_leaf_id(request.getCategoryId()).build());
        if(channelResponseDTO.isSuccess()){
            categoryAttrInfoList.addAll(buildCategoryAttrInfoList(channelResponseDTO.getCoreData()));
        }

        response.setStatus(ResultGenerator.genSuccessResult())
                .setGeneralAttrList(categoryAttrInfoList)
                .setSaleAttrList(saleAttrInfoList)
                .setUpcRequired(-1)
                .setWeightRequired(-1);
        return response;
    }

    private List<CategoryAttrInfo> getSaleAttrInfoList(BaseRequestSimple baseRequest, long categoryId) {
        ChannelResponseDTO<DouyinProductUpdateRuleResult> channelResponseDTO = douyinChannelGateService.sendPostByVirtualConfig(
                ChannelPostDouyinEnum.GET_PRODUCT_UPDATE_RULE, baseRequest, null,
                DouyinProductUpdateRuleParam.builder()
                        .category_id(categoryId)
                        .build());
        if (channelResponseDTO == null || !channelResponseDTO.isSuccess()) {
            return Collections.emptyList();
        }
        return Optional.ofNullable(channelResponseDTO.getCoreData())
                .map(DouyinProductUpdateRuleResult::getProduct_spec_rule)
                .filter(specRule -> CollectionUtils.isNotEmpty(specRule.getRequired_spec_details()))
                .map(DouyinConvertUtil::buildSaleAttrList)
                .orElseGet(Collections::emptyList);
    }

    private List<CategoryAttrInfo> buildCategoryAttrInfoList(DouyinCategoryPropQueryResult coreData) {
        if(CollectionUtils.isEmpty(coreData.getData())){
            return Collections.emptyList();
        }
        return coreData.getData()
                .stream()
                .filter(e->Objects.equals(e.getStatus(),0L))
                .map(DouyinConvertUtil::buildCatProp)
                .collect(Collectors.toList());
    }

    @Override
    public GetCategoryResponse batchGetCategoryByVirtualConfig(CatRequest request) {
        Assert.throwIfNull(request, "请求为空");
        Assert.throwIfEmpty(request.getIds(), "渠道类目id为空");

        GetCategoryResponse getCategoryResponse = new GetCategoryResponse();
        List<CatInfo> catInfoList = new LinkedList<>();

        request.getIds().stream().forEach(categoryIdStr -> {
            Long categoryId = Long.parseLong(categoryIdStr);
            ChannelResponseDTO<List<ChannelShopCategoryQueryResult>> channelResponseDTO =
                    douyinChannelGateService.sendPostByVirtualConfig(ChannelPostDouyinEnum.GET_SHOP_CATEGORY, request.getBaseInfo(), null,
                            ChannelShopCategoryQueryParam.builder().cid(categoryId).build());
            if (channelResponseDTO.isSuccess()) {
                catInfoList.addAll(channelResponseDTO.getCoreData().stream().map(DouyinConvertUtil::buildCatInfo).collect(Collectors.toList()));
            }
        });
        getCategoryResponse.setStatus(ResultGenerator.genSuccessResult());
        getCategoryResponse.setCatInfoList(catInfoList);
        return getCategoryResponse;
    }

    @Override
    public ResultStatus fetchVirtualTokenByVirtualConfig(VirtualTokenGetRequest request) {
        Preconditions.checkNotNull(request, "请求为空");
        Preconditions.checkNotNull(request.getTenantId(), "租户id为空");
        Preconditions.checkNotNull(request.getChannelId(), "渠道id为空");
        // 查询保存的信息
        ChannelVirtualAccessConfigDO channelVirtualAccessConfigDO =
                douyinChannelGateService.selectByTenantIdAndChannelId(request.getTenantId(), request.getChannelId());
        if (Objects.isNull(channelVirtualAccessConfigDO) || StringUtils.isEmpty(channelVirtualAccessConfigDO.getVirtualSysParam())) {
            log.debug("查询账号配置信息不存在，tenantId={}, channelId={}", request.getTenantId(), request.getChannelId());
            return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg("该租户对应的应用不存在");
        }
        String virtualSysParam = channelVirtualAccessConfigDO.getVirtualSysParam();
        // 更新抖音虚拟账号信息
        ResultStatus resultStatus = douyinAccessTokenService.saveDouYinVirtualToken(request.getTenantId(), virtualSysParam);
        return resultStatus;
    }
}
