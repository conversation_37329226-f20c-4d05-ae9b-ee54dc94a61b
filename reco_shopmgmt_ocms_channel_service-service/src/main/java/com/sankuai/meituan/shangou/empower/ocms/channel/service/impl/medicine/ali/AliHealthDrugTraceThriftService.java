package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.ali;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.medicine.request.AliHealthQueryDrugByDrugTraceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.medicine.response.QueryDrugInfoByDrugTraceResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.ali.converter.AliHealthChannelConverter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.ali.dto.AliDrugTraceQueryDrugInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.ali.dto.AliDrugTraceQueryDrugInfoResponseWrapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AliHealthDrugTraceThriftService {

    private final String DRUG_TRACE_DETAIL = "alibaba.alihealth.drugtrace.top.lsyd.query.codedetail";

    @Autowired
    private AliHealthChannelGate aliHealthChannelGate;

    /**
     * 通过溯源码查询数据
     */
    public QueryDrugInfoByDrugTraceResponse queryDrugInfoByDrugTrace(AliHealthQueryDrugByDrugTraceRequest request) {
        Map<String, Object> params = new HashMap<>();

        // 公共参数
        params.put("method", DRUG_TRACE_DETAIL);
        params.put("app_key", request.getAppKey());
        params.put("timestamp", request.getTimestamp());
        params.put("ref_ent_id", request.getRelEntId());

        // 签名参数
        params.put("sign", request.getSign());

        // 业务参数
        params.put("codes", request.getDrugTrace());

        try {
            String responseStr = aliHealthChannelGate.sendPostWithoutSession(DRUG_TRACE_DETAIL, params);
            log.info("DrugTraceFacade.getTraceByCode 调用淘宝查询溯源码信息，参数: {}, 结果: {}", JSON.toJSONString(request), responseStr);

            if (StringUtils.isBlank(responseStr)) {
                log.error("DrugTraceFacade.getTraceByCode 调用淘宝查询溯源码信息，返回为空 参数: {}", JSON.toJSONString(request));
                QueryDrugInfoByDrugTraceResponse result = new QueryDrugInfoByDrugTraceResponse();
                result.setCode(ResultCodeEnum.FAIL_ALLOW_RETRY.getValue());
                result.setMessage("调用渠道失败，请重试");
                return result;
            }

            AliDrugTraceQueryDrugInfoResponseWrapper response = JSON.parseObject(responseStr, AliDrugTraceQueryDrugInfoResponseWrapper.class);

            if (response != null && response.getErrorResponse() != null) {
                log.error("DrugTraceFacade.getTraceByCode 调用淘宝查询溯源码信息，返回错误 参数: {}, 结果: {}", JSON.toJSONString(request), responseStr);
                QueryDrugInfoByDrugTraceResponse result = new QueryDrugInfoByDrugTraceResponse();
                String errorMsg = response.getErrorResponse().getMsg();
                result.setCode(ResultCodeEnum.FAIL.getValue());
                result.setMessage(errorMsg);
                return result;
            }

            if (response == null || response.getResponse() == null || !response.getResponse().isSuccess()) {
                log.error("DrugTraceFacade.getTraceByCode 调用淘宝查询溯源码信息，返回错误 参数: {}, 结果: {}", JSON.toJSONString(request), responseStr);
                QueryDrugInfoByDrugTraceResponse result = new QueryDrugInfoByDrugTraceResponse();
                String errorMsg = response != null && response.getResponse() != null && response.getResponse().getResult() != null ? response.getResponse().getResult().getMsgInfo() : "返回值错误，请参考日志";
                result.setCode(ResultCodeEnum.FAIL.getValue());
                result.setMessage(errorMsg);
                return result;
            }

            QueryDrugInfoByDrugTraceResponse result = new QueryDrugInfoByDrugTraceResponse();
            List<AliDrugTraceQueryDrugInfoResponse.CodeFullInfoDto> finalResultList =
                    response.getResponse().getResult() != null && response.getResponse().getResult().getModels() != null && CollectionUtils.isNotEmpty(response.getResponse().getResult().getModels().getDataList())
                            ? response.getResponse().getResult().getModels().getDataList()
                            : Collections.emptyList();
            result.setCode(ResultCodeEnum.SUCCESS.getValue());
            result.setMessage("SUCCESS");
            result.setDataList(finalResultList.stream().map(AliHealthChannelConverter.INSTANCE::toAliProductFullInfoDTO).collect(Collectors.toList()));
            return result;
        } catch (
                BizException e) {
            log.error("DrugTraceFacade.getTraceByCode 调用淘宝查询溯源码信息，未知错误 参数: {}, error: ", JSON.toJSONString(request), e);
            QueryDrugInfoByDrugTraceResponse result = new QueryDrugInfoByDrugTraceResponse();
            result.setCode(ResultCodeEnum.FAIL.getValue());
            result.setMessage("调用渠道失败，请重试");
            return result;
        } catch (
                Exception e) {
            log.error("DrugTraceFacade.getTraceByCode 调用淘宝查询溯源码信息，未知错误 参数: {}, error: ", JSON.toJSONString(request), e);
            QueryDrugInfoByDrugTraceResponse result = new QueryDrugInfoByDrugTraceResponse();
            result.setCode(ResultCodeEnum.FAIL_ALLOW_RETRY.getValue());
            result.setMessage("调用渠道失败，请重试");
            return result;
        }
    }
}
