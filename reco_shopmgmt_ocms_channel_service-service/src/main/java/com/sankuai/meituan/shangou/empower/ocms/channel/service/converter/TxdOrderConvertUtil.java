package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.dianping.lion.client.Lion;
import com.doudian.open.utils.JsonUtil;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.enums.GiftTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.txd.AlibabaAelophyOrderGetBody;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.txd.AlibabaAelophyPromotionInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.txd.AlibabaAelophySubOrderResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdDeliveryTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderPayTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdOrderStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.txd.TxdDiscountType;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.txd.TxdOrderDataFromWdkApi;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.txd.TxdOrderTradeAttributes;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.txd.TxdReceiverInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ActivityShareDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ChannelOrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderDeliveryDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderDiscountDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderPaymentInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderProductDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.RedpackInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ShopCardFee;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.saas.common.json.GsonUtils;
import com.taobao.api.response.AlibabaAelophyOrderDesensitizephoneGetResponse;
import com.taobao.api.response.AlibabaAelophyOrderGetResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdDeliveryTypeEnum.MERCHANT_DELIVERY;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TxdDeliveryTypeEnum.PLATFORM_DELIVERY;
import static com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.PayTypeEnum.ONLINE;
import static java.util.Optional.ofNullable;

public class TxdOrderConvertUtil {

    private static final Logger log = LoggerFactory.getLogger(TxdOrderConvertUtil.class);

    public static ChannelOrderDetailDTO convertFromChannelOrder(AlibabaAelophyOrderGetResponse.OrderResponse orderDto,
            long storeId, AlibabaAelophyOrderDesensitizephoneGetResponse.OrderDesensitizePhoneResult virtualMobile,
                                                                AlibabaAelophyOrderGetResponse resp) {
        ChannelOrderDetailDTO channelOrderDetailDTO = new ChannelOrderDetailDTO();

        /**
         * 填充订单商品数据，单品、整单优惠以及对应活动数据
         */

        fillProductDetailAndPromotionActivities(channelOrderDetailDTO, orderDto);

        /**
         * 收件人信息
         */
        AlibabaAelophyOrderGetResponse.ReceiveInfo receiveInfo = orderDto.getReceiveInfo();
        TxdDeliveryTypeEnum deliveryType = TxdDeliveryTypeEnum.fromCode(orderDto.getDeliveryType());
        fillDeliveryDetail(channelOrderDetailDTO, orderDto, receiveInfo, virtualMobile, deliveryType);

        /**
         * 订单单头数据
         */
        // 基础数据
        channelOrderDetailDTO.setChannelOrderId(orderDto.getOutOrderId());
        channelOrderDetailDTO.setChannelExtraOrderId(String.valueOf(orderDto.getBizOrderId()));
        channelOrderDetailDTO.setStoreId(storeId);
        channelOrderDetailDTO.setOrderSerialNumberStr(orderDto.getOrderNo());
        channelOrderDetailDTO.setOrderFrom(String.valueOf(orderDto.getOrderFrom()));
        if (NumberUtils.isDigits(orderDto.getOrderNo())) {
            try {
                channelOrderDetailDTO.setOrderSerialNumber(Long.parseLong(orderDto.getOrderNo()));
            } catch (Exception e) {
                log.warn("订单{}的流水号{}格式有误", orderDto.getOutOrderId(), orderDto.getOrderNo(), e);
            }
        }
        // 淘鲜达全是预约单
        channelOrderDetailDTO.setIsBooking(true);
        channelOrderDetailDTO.setComment(receiveInfo.getReceiverMemo());
        channelOrderDetailDTO.setChannelId(DynamicChannelType.TAO_XIAN_DA.getChannelId());

        List<AlibabaAelophyOrderGetResponse.OrderPayChannel> payChannels = orderDto.getPayChannels();
        if (CollectionUtils.isNotEmpty(payChannels)) {
            AlibabaAelophyOrderGetResponse.OrderPayChannel payChannel = payChannels.get(0);
            channelOrderDetailDTO.setPayType(ONLINE.getValue());
            channelOrderDetailDTO.setChannelPayMode(payChannel.getPayChannel());
            List<OrderPaymentInfo> orderPaymentInfoList = payChannels.stream()
                    .map(TxdOrderConvertUtil::buildOrderPaymentInfo)
                    .collect(Collectors.toList());
            channelOrderDetailDTO.setOrderPaymentInfoList(orderPaymentInfoList);
        }

        // 订单状态
        String orderStatus = orderDto.getOrderStatus();

        TxdOrderStatusEnum orderStatusEnum = TxdOrderStatusEnum.fromName(orderStatus);
        channelOrderDetailDTO.setChannelOrderStatus(orderStatusEnum.getStatus());
        channelOrderDetailDTO.setStatus(orderStatusEnum.getStatus());
        channelOrderDetailDTO.setBaichuanStatus(orderStatusEnum.getBaicchuanStatus());

        // 各种时间
        // 渠道没有给创建时间，用支付时间兜底吧
        channelOrderDetailDTO.setCreateTime(orderDto.getPayTime().getTime());
        channelOrderDetailDTO.setPayTime(orderDto.getPayTime().getTime());

        // 三方会员卡号
        AlibabaAelophyOrderGetResponse.OrderInfoExt orderInfoExt = orderDto.getExt();
        if (Objects.nonNull(orderInfoExt)) {
            channelOrderDetailDTO.setThreeCardNo(orderInfoExt.getMemberCardNum());
        }


        // 金额数据
        // 1. 包装费及包装费优惠，淘鲜达包装费是商家收，没有包装费优惠
        channelOrderDetailDTO.setPackageAmt(ofNullable(orderDto.getPackageFee()).orElse(0L).intValue());
        channelOrderDetailDTO.setPayPackageAmt(channelOrderDetailDTO.getPackageAmt());
        channelOrderDetailDTO.setPoiPackageAmt(channelOrderDetailDTO.getPackageAmt());
        channelOrderDetailDTO.setPlatPackageAmt(0);
        channelOrderDetailDTO.setPoiPackagePromotion(0);
        channelOrderDetailDTO.setPlatPackagePromotion(0);

        // 2. 配送费及配送费优惠
        // 10.11 更新，因淘鲜达渠道的【配送费】字段指的是顾客实际支付配送费，而不是渠道应收配送费，所以需要调整配送费相关字段的计算逻辑为：
        // 顾客支付配送费=【post_fee】， 应收配送费=顾客支付配送费+配送费平台优惠+配送费商家优惠
        int postDiscountPlatformFee = ofNullable(orderDto.getPostDiscountPlatformFee()).orElse(0L).intValue();
        int postDiscountMerchantFee = ofNullable(orderDto.getPostDiscountMerchantFee()).orElse(0L).intValue();
        int userPaidFreight = ofNullable(orderDto.getPostFee()).orElse(0L).intValue();

        channelOrderDetailDTO.setFreight(userPaidFreight + postDiscountPlatformFee + postDiscountMerchantFee);

        channelOrderDetailDTO.setPoiLogisticsPromotion(postDiscountMerchantFee);
        channelOrderDetailDTO.setPlatLogisticsPromotion(postDiscountPlatformFee);
        channelOrderDetailDTO.setPoiLogisticsTips(0);
        channelOrderDetailDTO.setCustomerLogisticsTips(0);
        channelOrderDetailDTO.setSelfPickServiceFee(0);
        if (deliveryType == MERCHANT_DELIVERY) {
            channelOrderDetailDTO.setPoiLogisticsIncome(
                    channelOrderDetailDTO.getFreight() - channelOrderDetailDTO.getPoiLogisticsPromotion());
        } else if (deliveryType == PLATFORM_DELIVERY) {
            channelOrderDetailDTO.setPoiLogisticsIncome(- channelOrderDetailDTO.getPoiLogisticsPromotion());
        }

        // 3. 佣金
        AlibabaAelophyOrderGetResponse.PlatformDeduction platformDeduction = orderDto.getPlatformDeduction();
        long platformFee = 0;
        if (platformDeduction != null) {
            platformFee = ofNullable(platformDeduction.getThirdpartnarFee()).orElse(0L)
                    + ofNullable(platformDeduction.getPayServiceFee()).orElse(0L)
                    + ofNullable(platformDeduction.getBaseLogisticsFee()).orElse(0L)
                    + ofNullable(platformDeduction.getTechnicalServiceFee()).orElse(0L);
        }
        channelOrderDetailDTO.setPlatformFee((int) platformFee);

        // 4. 赠金 & 红包支付 （这两个当前算是淘鲜达独有的流程）
        channelOrderDetailDTO.setShopCardFee(getShopCardFee(orderDto));


        channelOrderDetailDTO.setRedpackInfo(getRedpackInfo(orderDto));

        // 5. 支付、收入金额

        // 原始金额
        channelOrderDetailDTO.setOriginalAmt(ofNullable(orderDto.getOriginalFee()).orElse(0L).intValue());
        // 实付金额
        channelOrderDetailDTO.setActualPayAmt(ofNullable(orderDto.getPayFee()).orElse(0L).intValue());

        // 赠金商家承担
        int shopCardFeeMerchantShare = 0;
        ShopCardFee shopCardFee = channelOrderDetailDTO.getShopCardFee();
        if (Objects.nonNull(shopCardFee)) {
            shopCardFeeMerchantShare = shopCardFee.getShopGiveFeeShare();
        }

        // 红包商家承担
        int redpackFeeMerchant = 0;
        RedpackInfo redpackInfo = channelOrderDetailDTO.getRedpackInfo();
        if (Objects.nonNull(redpackInfo)) {
            redpackFeeMerchant = (int) redpackInfo.getRedpackAmountMerchant();
        }

        // 商家实收金额（预计收入） =
        // 【model.pay_fee】（买家支付金额）+【model.sku_discount_platform_fee】（单头中的商品平台优惠分摊）
        // +【model.sub_order_response_list.discount_platform_fee】(商品明细中的商品平台优惠分摊)
        // - 赠金商家承担 - 红包商家承担 （8.12更新，接入购物金和红包支付）
        // -【model.platform_deduction】（平台扣费对象）?+【model.post_discount_merchant_fee】（配送费平台优惠）
        // ?-【model.post_fee】（配送费） 其中配送费是否需要减，取决于是平台配送还是商家自配送
        // 10.11 更新，对于平台配送，配送费平台优惠不应该算在商家收入，需要减掉

        int bizReceiveAmt = orderDto.getPayFee().intValue() + channelOrderDetailDTO.getPlatPromotion()
                + channelOrderDetailDTO.getPlatItemPromotion() - channelOrderDetailDTO.getPlatformFee()
                - shopCardFeeMerchantShare - redpackFeeMerchant
                + postDiscountPlatformFee;
        if (deliveryType == PLATFORM_DELIVERY) {
            bizReceiveAmt -= channelOrderDetailDTO.getFreight();
            // 对于平台配送，配送费平台优惠不应该算在商家收入，需要减掉
            bizReceiveAmt -= postDiscountPlatformFee;
        }
        channelOrderDetailDTO.setBizReceiveAmt(bizReceiveAmt);
        // 组装活动分摊信息
        fillActivityShareInfo(channelOrderDetailDTO, orderDto.getSubOrderResponseList(),resp);
        //渠道用户Id
        if(StringUtils.isNotBlank(orderDto.getOpenUid())){
            channelOrderDetailDTO.setChannelUserId(orderDto.getOpenUid());
        }
        return channelOrderDetailDTO;
    }

    private static RedpackInfo getRedpackInfo(AlibabaAelophyOrderGetResponse.OrderResponse orderDto) {

        List<AlibabaAelophyOrderGetResponse.OrderFundsDiscount> fundsDiscounts = orderDto.getFundsDiscounts();
        if (CollectionUtils.isEmpty(fundsDiscounts)) {
            return null;
        }

        long redpackAmountMerchant = 0;
        long redpackAmountPlatform = 0;
        long redpackAmountTotal = 0;
        for (AlibabaAelophyOrderGetResponse.OrderFundsDiscount fundsDiscount : fundsDiscounts) {
            if (!StringUtils.equals("VOUCHER", fundsDiscount.getDiscountType())) {
                continue;
            }
            redpackAmountMerchant += ofNullable(fundsDiscount.getDiscountMerchantFee()).orElse(0L);
            redpackAmountPlatform += ofNullable(fundsDiscount.getDiscountPlatformFee()).orElse(0L);
            redpackAmountTotal += ofNullable(fundsDiscount.getDiscountFee()).orElse(0L);
        }
        if (redpackAmountTotal == 0) {
            return null;
        }

        RedpackInfo redpackInfo = new RedpackInfo();
        redpackInfo.setRedpackAmountMerchant(redpackAmountMerchant);
        redpackInfo.setRedpackAmountPlatform(redpackAmountPlatform);
        redpackInfo.setRedpackAmountTotal(redpackAmountTotal);
        return redpackInfo;
    }

    public static ShopCardFee getShopCardFee(TxdOrderTradeAttributes tradeAttributes) {
        long merchantShoppingFee = tradeAttributes.getExpandDiscountMerchantFee() + tradeAttributes.getBenefitDiscountMerchantFee();
        long platformShoppingFee = tradeAttributes.getExpandDiscountPlatformFee() + tradeAttributes.getBenefitDiscountPlatformFee();
        ShopCardFee shopCardFee = new ShopCardFee();
        shopCardFee.setShopGiveFeeShare((int)merchantShoppingFee);
        shopCardFee.setPlatformGiveFeeShare((int)platformShoppingFee);
        shopCardFee.setGiveFee(shopCardFee.getShopGiveFeeShare() + shopCardFee.getPlatformGiveFeeShare());
        shopCardFee.setBaseFee(0);
        shopCardFee.setTotalFee(shopCardFee.getGiveFee() + shopCardFee.getBaseFee());
        return shopCardFee;
    }

    private static ShopCardFee getShopCardFee(AlibabaAelophyOrderGetResponse.OrderResponse orderDto) {
        long merchantShoppingFee = ofNullable(orderDto.getMerchantExpandFee()).orElse(0L)
                + ofNullable(orderDto.getMerchantBenefitFee()).orElse(0L);
        long platformShoppingFee = ofNullable(orderDto.getPlatformExpandFee()).orElse(0L)
                + ofNullable(orderDto.getPlatformBenefitFee()).orElse(0L);
        ShopCardFee shopCardFee = new ShopCardFee();
        shopCardFee.setShopGiveFeeShare((int)merchantShoppingFee);
        shopCardFee.setPlatformGiveFeeShare((int)platformShoppingFee);
        shopCardFee.setGiveFee(shopCardFee.getShopGiveFeeShare() + shopCardFee.getPlatformGiveFeeShare());
        shopCardFee.setBaseFee(0);
        shopCardFee.setTotalFee(shopCardFee.getGiveFee() + shopCardFee.getBaseFee());
        return shopCardFee;
    }

    private static OrderPaymentInfo buildOrderPaymentInfo(AlibabaAelophyOrderGetResponse.OrderPayChannel orderPayChannel) {
        TxdOrderPayTypeEnum payTypeEnum = TxdOrderPayTypeEnum.fromType(orderPayChannel.getPayType());
        return new OrderPaymentInfo(orderPayChannel.getPayChannel(), orderPayChannel.getPayFee().intValue(),
                payTypeEnum.getTradeChannelValue());
    }


    private static void fillProductDetailAndPromotionActivities(ChannelOrderDetailDTO channelOrderDetailDTO,
                                                         AlibabaAelophyOrderGetResponse.OrderResponse orderDto) {
        List<AlibabaAelophyOrderGetResponse.SubOrderResponse> itemList = orderDto.getSubOrderResponseList();
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }

        int totalPlatItemPromotion = 0;
        int totalPoiItemPromotion = 0;
        int totalPlatPromotion = 0;
        int totalPoiPromotion = 0;

        Integer orderOriginalFee = itemList.stream().map(item -> item.getOriginalFee().intValue()).reduce(0, Integer::sum);
        if (orderOriginalFee != orderDto.getOriginalFee().intValue()) {
            log.warn("订单{}的总价不等于子订单总价之和", orderDto.getOutOrderId());
        }

        List<OrderProductDetailDTO> skuList = new ArrayList<>();
        for (AlibabaAelophyOrderGetResponse.SubOrderResponse item : itemList) {
            OrderProductDetailDTO orderProductDetailDTO = new OrderProductDetailDTO();
            // 商品基础数据
            orderProductDetailDTO.setSkuId(item.getSkuCode());
            orderProductDetailDTO.setCustomSpu(item.getSkuCode());
            orderProductDetailDTO.setUpcCode(item.getBarcode());
            // orderProductDetailDTO.setChannelSkuId()
            orderProductDetailDTO.setSkuName(item.getSkuName());
            // orderProductDetailDTO.setSpecification();
            orderProductDetailDTO.setQuantity(new BigDecimal(item.getBuySaleQuantity()).intValue());      // 有溢出风险，不过目前看应该不太可能
            orderProductDetailDTO.setChannelItemId(item.getOutSubOrderId());
            orderProductDetailDTO.setBizChannelItemId(String.valueOf(item.getBizSubOrderId()));
            orderProductDetailDTO.setWeight(ofNullable(item.getWeight()).orElse(0L) / orderProductDetailDTO.getQuantity());
            orderProductDetailDTO.setSellType(StringUtils.equals(item.getWeightFlag(), "1") ? 0 : 1);
            orderProductDetailDTO.setUnit("g");
            int itemType = new BigDecimal(item.getGiftFlag()).intValue() == 1 ? 1 : 0;
            orderProductDetailDTO.setItemType(itemType);
            if (itemType == 1) {
                Map<String, Integer> map = new HashMap<>();
                map.put("giftType", GiftTypeEnum.ONLINE.getValue());
                orderProductDetailDTO.setExtData(JsonUtil.toJson(map));
            }
            if (CollectionUtils.isNotEmpty(item.getServiceProps())) {
                String propVal = item.getServiceProps().stream()
                        .filter(Objects::nonNull)
                        .map(AlibabaAelophyOrderGetResponse.SkuServiceProperty::getPropValue)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(","));
                orderProductDetailDTO.setSkuProperty(propVal);
            }

            // 优惠数据 & 活动数据；注意整单的活动数据在后面（因为整单视作一个活动，优惠金额可以分摊开，但是活动本身还是记成一个）
            fillPromotionAndActivities(channelOrderDetailDTO, orderProductDetailDTO, item);

            // 商品金额数据
            int originalFee = item.getOriginalFee().intValue();
            int feeWithoutItemPromotion = originalFee - orderProductDetailDTO.getPoiItemPromotion() - orderProductDetailDTO.getPlatItemPromotion();
            orderProductDetailDTO.setOriginalPrice(originalFee / orderProductDetailDTO.getQuantity());                     // 原价
            orderProductDetailDTO.setActualSalePrice(feeWithoutItemPromotion / orderProductDetailDTO.getQuantity());       // 售价
            orderProductDetailDTO.setSalePrice(orderProductDetailDTO.getActualSalePrice());       // 售价

            // 平台和商家的优惠，分别把整单和单品优惠相加即可
            orderProductDetailDTO.setChannelCost(orderProductDetailDTO.getPlatPromotion() + orderProductDetailDTO.getPlatItemPromotion());
            orderProductDetailDTO.setTenantCost(orderProductDetailDTO.getPoiPromotion() + orderProductDetailDTO.getPoiItemPromotion());
            orderProductDetailDTO.setTotalDiscount(orderProductDetailDTO.getChannelCost() + orderProductDetailDTO.getTenantCost());
            // 实付金额，应该等于原价减去整单和单品优惠：售价 * 数量 - 整单优惠 or 原价 * 数量 - 单品优惠 - 整单优惠
            orderProductDetailDTO.setTotalPayAmtMinusDiscount(
                    originalFee - orderProductDetailDTO.getTotalDiscount());

            totalPlatItemPromotion += orderProductDetailDTO.getPlatItemPromotion();
            totalPoiItemPromotion += orderProductDetailDTO.getPoiItemPromotion();
            totalPlatPromotion += orderProductDetailDTO.getPlatPromotion();
            totalPoiPromotion += orderProductDetailDTO.getPoiPromotion();

            // 赠品开票金额算出来可能为负数，这里做下兜底:
            // 1. 原价为所有优惠之和 / 数量
            // 2. 售价为 (所有优惠之和 - 单品优惠之和) / 数量
            int totalPromotion = orderProductDetailDTO.getTotalDiscount();

            handleGift(orderProductDetailDTO, totalPromotion);

            skuList.add(orderProductDetailDTO);
        }

        channelOrderDetailDTO.setSkuDetails(skuList);

        // 整单活动优惠数据
        fillOrderPromotionActivities(channelOrderDetailDTO, orderDto);


        // 单头整单优惠
        channelOrderDetailDTO.setPlatPromotion(totalPlatPromotion);
        channelOrderDetailDTO.setPoiPromotion(totalPoiPromotion);
        channelOrderDetailDTO.setPlatItemPromotion(totalPlatItemPromotion);
        channelOrderDetailDTO.setPoiItemPromotion(totalPoiItemPromotion);
        channelOrderDetailDTO.setTotalDiscount(
                totalPoiPromotion + totalPoiItemPromotion + totalPlatPromotion + totalPlatItemPromotion);

    }

    private static void handleGift(OrderProductDetailDTO orderProductDetailDTO, int totalPromotion) {
        if (orderProductDetailDTO.getItemType() == 1) {     // 赠品
            if (orderProductDetailDTO.getOriginalPrice() <= 0 && orderProductDetailDTO.getActualSalePrice() <= 0
                    && orderProductDetailDTO.getSalePrice() <= 0 && totalPromotion != 0) {
                orderProductDetailDTO.setOriginalPrice(totalPromotion / orderProductDetailDTO.getQuantity());
                orderProductDetailDTO.setActualSalePrice((totalPromotion - orderProductDetailDTO.getPoiItemPromotion()
                        - orderProductDetailDTO.getPlatItemPromotion()) / orderProductDetailDTO.getQuantity());
                orderProductDetailDTO.setSalePrice(orderProductDetailDTO.getActualSalePrice());
                orderProductDetailDTO.setTotalPayAmtMinusDiscount(0);
            }
        }
    }

    private static void fillPromotionAndActivities(ChannelOrderDetailDTO channelOrderDetailDTO,
            OrderProductDetailDTO orderProductDetailDTO, AlibabaAelophyOrderGetResponse.SubOrderResponse item) {
        
        long poiItemPromotion = 0;
        long poiOrderPromotion = 0;
        long platItemPromotion = 0;
        long platOrderPromotion = 0;

        List<OrderDiscountDetailDTO> orderActivities = channelOrderDetailDTO.getActivities();
        if (orderActivities == null) {
            orderActivities = new ArrayList<>();
            channelOrderDetailDTO.setActivities(orderActivities);
        }
        
        // 活动优惠，按照活动类型区分单品和整单
        List<AlibabaAelophyOrderGetResponse.OrderActivity> activities = item.getActivitys();
        if (CollectionUtils.isNotEmpty(activities)) {
            for (AlibabaAelophyOrderGetResponse.OrderActivity activity : activities) {
                String activityType = activity.getActivityType();
                TxdDiscountType discountType = TxdDiscountType.fromName(activityType);
                // 换购优惠，跳过下
                if (discountType.discountIgnore()) {
                    continue;
                }
                if (discountType.isItemDiscount()) {
                    poiItemPromotion += ofNullable(activity.getDiscountMerchantFee()).orElse(0L);
                    platItemPromotion += ofNullable(activity.getDiscountPlatformFee()).orElse(0L);
                    // 单品优惠活动在这里加入
                    OrderDiscountDetailDTO orderDiscount = createOrderDiscount(activity, discountType);
                    orderActivities.add(orderDiscount);
                } else if (discountType.isWholeDiscount()) {
                    poiOrderPromotion += ofNullable(activity.getDiscountMerchantFee()).orElse(0L);
                    platOrderPromotion += ofNullable(activity.getDiscountPlatformFee()).orElse(0L);
                } else {  // 无法识别的优惠，按照整单优惠来处理
                    log.warn("发现淘鲜达未处理的新优惠类型: {}, 订单号: {}", activityType, channelOrderDetailDTO.getChannelOrderId());
                    poiOrderPromotion += ofNullable(activity.getDiscountMerchantFee()).orElse(0L);
                    platOrderPromotion += ofNullable(activity.getDiscountPlatformFee()).orElse(0L);
                }
            }
        }

        // 资金优惠，全当做整单优惠
        List<AlibabaAelophyOrderGetResponse.OrderFundsDiscount> fundsDiscounts = item.getFundsDiscounts();
        if (CollectionUtils.isNotEmpty(fundsDiscounts)) {
            for (AlibabaAelophyOrderGetResponse.OrderFundsDiscount fundsDiscount : fundsDiscounts) {
                // 购物金膨胀金、红包金额，从整单优惠剔除，其余暂时保留
                if (MccConfigUtil.notWholePromotion(fundsDiscount.getDiscountType())) {
                    continue;
                }
                platOrderPromotion += ofNullable(fundsDiscount.getDiscountPlatformFee()).orElse(0L);
                poiOrderPromotion += ofNullable(fundsDiscount.getDiscountMerchantFee()).orElse(0L);
            }
        }
        
        orderProductDetailDTO.setPoiPromotion((int) poiOrderPromotion);
        orderProductDetailDTO.setPoiItemPromotion((int) poiItemPromotion);
        orderProductDetailDTO.setPlatPromotion((int) platOrderPromotion);
        orderProductDetailDTO.setPlatItemPromotion((int) platItemPromotion);
        channelOrderDetailDTO.setActivities(orderActivities);

    }

    /**
     * 填充整单活动优惠数据
     * @param channelOrderDetailDTO
     * @param orderDto
     */
    private static void fillOrderPromotionActivities(ChannelOrderDetailDTO channelOrderDetailDTO,
                                              AlibabaAelophyOrderGetResponse.OrderResponse orderDto) {
        if (channelOrderDetailDTO.getActivities() == null) {
            channelOrderDetailDTO.setActivities(new ArrayList<>());
        }
        List<OrderDiscountDetailDTO> activities = channelOrderDetailDTO.getActivities();

        addOrderActivities(activities, orderDto.getActivitys());
        addFundsDiscounts(activities, orderDto.getFundsDiscounts());
    }



    /**
     * 添加活动
     * @param activities
     * @param orderActivities
     */
    private static void addOrderActivities(List<OrderDiscountDetailDTO> activities, List<AlibabaAelophyOrderGetResponse.OrderActivity> orderActivities) {
        if (CollectionUtils.isEmpty(orderActivities)) {
            return;
        }
        for (AlibabaAelophyOrderGetResponse.OrderActivity activity : orderActivities) {
            TxdDiscountType discountType = TxdDiscountType.fromName(activity.getActivityType());
            if (discountType.isItemDiscount() || discountType.discountIgnore()) {
                continue;
            }
            // 整单优惠 or unknown，unknown当做整单来记录
            OrderDiscountDetailDTO orderDiscount = createOrderDiscount(activity, discountType);
            activities.add(orderDiscount);
        }
    }

    /**
     * 添加资金优惠
     * @param activities
     * @param fundsDiscounts
     */
    private static void addFundsDiscounts(List<OrderDiscountDetailDTO> activities, List<AlibabaAelophyOrderGetResponse.OrderFundsDiscount> fundsDiscounts) {
        if (CollectionUtils.isEmpty(fundsDiscounts)) {
            return;
        }
        for (AlibabaAelophyOrderGetResponse.OrderFundsDiscount fundsDiscount : fundsDiscounts) {
            // 购物金膨胀金、红包金额，从优惠活动中剔除，其余暂时保留
            if (MccConfigUtil.notWholePromotion(fundsDiscount.getDiscountType())) {
                continue;
            }
            OrderDiscountDetailDTO orderDiscount = createOrderDiscount(fundsDiscount);
            activities.add(orderDiscount);
        }
    }

    private static OrderDiscountDetailDTO createOrderDiscount(AlibabaAelophyOrderGetResponse.OrderActivity activity,
            TxdDiscountType type) {
        OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
        String remark = activity.getActivityName();
        if (StringUtils.isBlank(remark) || StringUtils.equals("null", remark)) {
            remark = type.getDesc();
        }
        orderDiscount.setRemark(remark);
        orderDiscount.setType(activity.getActivityType());
        orderDiscount.setActDiscount(activity.getDiscountFee());
        orderDiscount.setChannelCharge(activity.getDiscountPlatformFee());
        orderDiscount.setBizCharge(activity.getDiscountMerchantFee());
        orderDiscount.setActivityId(activity.getChannelActivityId());
        orderDiscount.setLogisticsCharge(0);
        orderDiscount.setAgentCharge(0);
        return orderDiscount;
    }

    private static OrderDiscountDetailDTO createOrderDiscount(AlibabaAelophyOrderGetResponse.OrderFundsDiscount fundsDiscount) {
        OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
        orderDiscount.setRemark(fundsDiscount.getDiscountName());
        orderDiscount.setType(fundsDiscount.getDiscountType());
        orderDiscount.setActDiscount(fundsDiscount.getDiscountFee());
        orderDiscount.setChannelCharge(fundsDiscount.getDiscountPlatformFee());
        orderDiscount.setBizCharge(fundsDiscount.getDiscountMerchantFee());
        orderDiscount.setLogisticsCharge(0);
        orderDiscount.setAgentCharge(0);
        return orderDiscount;
    }


    private static void fillDeliveryDetail(ChannelOrderDetailDTO channelOrderDetailDTO,
                                    AlibabaAelophyOrderGetResponse.OrderResponse orderDto,
                                    AlibabaAelophyOrderGetResponse.ReceiveInfo receiveInfo,
                                    AlibabaAelophyOrderDesensitizephoneGetResponse.OrderDesensitizePhoneResult virtualMobile,
                                    TxdDeliveryTypeEnum deliveryType) {
        String orderId = orderDto.getOutOrderId();
        OrderDeliveryDetailDTO deliveryDetail = new OrderDeliveryDetailDTO();
        deliveryDetail.setUserName(receiveInfo.getReceiverName());
        deliveryDetail.setUserAddress(receiveInfo.getReceiverAddress());
        String userPhone = receiveInfo.getReceiverPhone();
        String userPrivacyPhone = receiveInfo.getReceiverPrivacyPhone();
        if (virtualMobile != null) {
            if (StringUtils.isNotBlank(virtualMobile.getVirtualNumber())) {
                userPhone = virtualMobile.getVirtualNumber();
            }
            if (StringUtils.isNotBlank(virtualMobile.getReceiverPrivacyPhone())) {
                userPrivacyPhone = virtualMobile.getReceiverPrivacyPhone();
            }
        }
        deliveryDetail.setUserPhone(userPhone);
        deliveryDetail.setUserPrivacyPhone(userPrivacyPhone);
        deliveryDetail.setUsePrivacyPhone(1);

        String expectArriveTime = receiveInfo.getExpectArriveTime();
        if (StringUtils.isNotBlank(expectArriveTime) && expectArriveTime.contains("~")) {
            // 定义日期时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 分割时间范围字符串
            String[] timeArray = expectArriveTime.split("~");
            try {

                LocalDateTime startTime = LocalDateTime.parse(timeArray[0], formatter);
                LocalDateTime endTime = LocalDateTime.parse(timeArray[1], formatter);

                deliveryDetail.setArrivalTime(startTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                deliveryDetail.setArrivalEndTime(endTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            } catch (Exception e) {
                log.error("fillDeliveryDetail arrivalTime error, orderId: {} expectArriveTime: {}", orderId, expectArriveTime, e);
            }

        }
        String receiverPoi = receiveInfo.getReceiverPoi();
        if (StringUtils.isNotBlank(receiverPoi) && receiverPoi.contains(",")) {
            String[] receiverPoiArr = receiverPoi.split(",");
            try {
                deliveryDetail.setLongitude(Double.parseDouble(receiverPoiArr[0]));
                deliveryDetail.setLatitude(Double.parseDouble(receiverPoiArr[1]));
            } catch (Exception e) {

                log.error("fillDeliveryDetail receiverPoi error, orderId: {} receiverPoi: {}", orderId, receiverPoi, e);
            }
        }
        deliveryDetail.setDeliveryMethod(deliveryType.getDesc());
        deliveryDetail.setIsSelfDelivery(deliveryType == MERCHANT_DELIVERY ? 1 : 0);
        deliveryDetail.setUserPhoneIsValid(true);
        TxdReceiverInfo txdReceiverInfo = null;
        if (StringUtils.isBlank(deliveryDetail.getUserName())) {
            txdReceiverInfo = MccConfigUtil.getTxdDefaultReceiverInfo();
            deliveryDetail.setUserName(txdReceiverInfo.getName());
        }
        if (StringUtils.isBlank(deliveryDetail.getUserAddress())) {
            txdReceiverInfo = ofNullable(txdReceiverInfo).orElse(MccConfigUtil.getTxdDefaultReceiverInfo());
            deliveryDetail.setUserAddress(txdReceiverInfo.getAddress());
        }
        channelOrderDetailDTO.setDeliveryDetail(deliveryDetail);

    }

    /**
     * 补齐活动分摊
     * @param channelOrderDetailDTO
     * @param subOrderResponseList
     */
    private static void fillActivityShareInfo(ChannelOrderDetailDTO channelOrderDetailDTO,
                                       List<AlibabaAelophyOrderGetResponse.SubOrderResponse> subOrderResponseList,
                                              AlibabaAelophyOrderGetResponse resp) {
        if (CollectionUtils.isEmpty(subOrderResponseList)) {
            return;
        }

        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        try {
            String body = resp.getBody();
            Map<String, AlibabaAelophyPromotionInfo> promotionMap = getActivityIdNameMap(body);
            for(AlibabaAelophyOrderGetResponse.SubOrderResponse subOrder : subOrderResponseList){
                List<AlibabaAelophyOrderGetResponse.OrderActivity> activities = subOrder.getActivitys();
                if (CollectionUtils.isEmpty(activities)) {
                    continue;
                }
                for(AlibabaAelophyOrderGetResponse.OrderActivity activity : activities){
                    ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();

                    activityShareDetailDTO.setActivityId(activity.getChannelActivityId());
                    activityShareDetailDTO.setSkuId(subOrder.getSkuCode());
                    activityShareDetailDTO.setCustomSpu(subOrder.getSkuCode());
                    activityShareDetailDTO.setChannelPromotionType(activity.getActivityType());
                    AlibabaAelophyPromotionInfo promotionInfo = promotionMap.get(activity.getChannelActivityId());

                    String activityName = ((promotionInfo == null) || StringUtils.isBlank(promotionInfo.getActive_name()))
                            ? activity.getActivityName() : promotionInfo.getActive_name();

                    activityShareDetailDTO.setPromotionRemark(activityName);
                    activityShareDetailDTO.setChannelCost(
                            Optional.ofNullable(activity.getDiscountPlatformFee()).map(Long::intValue).orElse(0));
                    activityShareDetailDTO.setTenantCost(
                            Optional.ofNullable(activity.getDiscountMerchantFee()).map(Long::intValue).orElse(0));

                    activityShareDetailDTO.setPromotionCodeErp(
                            Optional.ofNullable(promotionInfo).map(AlibabaAelophyPromotionInfo::getOut_active_id).orElse(""));
                    // sub_order_response_list 层级数据
                    activityShareDetailDTO.setDecimalSkuCount(subOrder.getBuySaleQuantity());
                    activityShareDetailDTO.setDecimalPromotionCount(subOrder.getBuySaleQuantity());

                    activityShareDetailDTO.setTotalOriginPrice(
                            Optional.ofNullable(subOrder.getOriginalFee()).map(Long::intValue).orElse(0));
                    activityShareDetailDTO.setTotalActivityPrice(
                            Optional.ofNullable(subOrder.getOriginalFee()).map(Long::intValue).orElse(0) -
                                    Optional.ofNullable(subOrder.getDiscountFee()).map(Long::intValue).orElse(0));

                    activityShareDetailDTOList.add(activityShareDetailDTO);

                }
            }
            log.info("淘鲜达活动分摊解析结果：activityShareDetailDTOList={}", GsonUtils.toJSONString(activityShareDetailDTOList));
        } catch (Exception e) {
            log.error("活动分摊异常", e);
        }

        channelOrderDetailDTO.setActivityShareDetailList(activityShareDetailDTOList);
    }

    /**
     * 获取body中活动id和名称的映射关系
     * @param body
     * @return key:活动id value:活动名称
     */
    private static Map<String,AlibabaAelophyPromotionInfo> getActivityIdNameMap(String body){
        Map<String,AlibabaAelophyPromotionInfo> activityIdNameMap = new HashMap<>();
        try {
            AlibabaAelophyOrderGetBody alibabaAelophyOrderGetBody =
                    JsonUtil.fromJson(body, AlibabaAelophyOrderGetBody.class);
            if(alibabaAelophyOrderGetBody == null
                    || alibabaAelophyOrderGetBody.getAlibaba_aelophy_order_get_response() == null
                    || alibabaAelophyOrderGetBody.getAlibaba_aelophy_order_get_response().getApi_result() == null
                    || alibabaAelophyOrderGetBody.getAlibaba_aelophy_order_get_response().getApi_result().getModel() == null
                    || alibabaAelophyOrderGetBody.getAlibaba_aelophy_order_get_response().getApi_result().getModel().getSub_order_response_list() == null
                    || alibabaAelophyOrderGetBody.getAlibaba_aelophy_order_get_response().getApi_result().getModel().getSub_order_response_list().getSub_order_response() == null){
                return activityIdNameMap;
            }

            List<AlibabaAelophySubOrderResponse> subOrderResponse = alibabaAelophyOrderGetBody
                    .getAlibaba_aelophy_order_get_response()
                    .getApi_result()
                    .getModel()
                    .getSub_order_response_list()
                    .getSub_order_response();

            activityIdNameMap = subOrderResponse.stream()
                    .map(AlibabaAelophySubOrderResponse::getPromotion_infos)
                    .filter(Objects::nonNull)
                    .filter(promotionInfos -> CollectionUtils.isNotEmpty(promotionInfos.getPromotion_info()))
                    .flatMap(promotionInfos -> promotionInfos.getPromotion_info().stream())
                    .filter(promotionInfo->StringUtils.isNotBlank(promotionInfo.getTxd_active_id()))
                    .collect(Collectors.toMap(AlibabaAelophyPromotionInfo::getTxd_active_id, Function.identity(), (v1, v2) -> v1));

            log.info("淘鲜达活动名称Map数据 : {}",JsonUtil.toJson(activityIdNameMap));
            return activityIdNameMap;
        } catch (Exception e) {
            log.error("淘鲜达解析body获取活动名称异常",e);
        }
        return activityIdNameMap;
    }

}
