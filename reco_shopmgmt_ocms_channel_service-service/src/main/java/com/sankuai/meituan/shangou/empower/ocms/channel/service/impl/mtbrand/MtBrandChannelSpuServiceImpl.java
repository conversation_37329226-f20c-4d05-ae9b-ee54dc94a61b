package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Ordering;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.AppealInfoSubmitDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelCategoryAndSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelShopCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuInSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSpuAndSkuIdUpdateByNameAndSpecDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSpuAndSkuIdUpdateByOriginDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSpuSellStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSpuStoreCategoryUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSpuUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSpuWeightInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelStandardSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelStoreCategoryResponseErrorDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelUpcCreateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MTSpuUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.RecommendChannelCategoryInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.RecommendChannelCategoryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.SensitiveWordCheckMsg;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.SpecialPictureInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ActCheckTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSpuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtBrandConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtBrandConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BasePageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.SourceType;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.AppealInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.AuditProblemDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.AuditStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchUpdateCustomSpuStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryRankDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelSpuIdDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.DynamicInfoRepairRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetHeadQuarterSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.HeadQuarterSkuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.HeadQuarterSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.NormAuditViolationDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ProblemItem;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ProductStoreCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QualityInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QualityProblemDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAppFoodCodeBySkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAuditStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAuditStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryChannelSpuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryChannelSpuIdResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryNormAuditDelSpuRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryNormAuditDelSpuResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryQualityProblemRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryQualityProblemResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RejectProblemDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SaleAttrValueDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckResultDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuId2AppFoodCodeResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuIdWithAppFoodCodeDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoSellStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuSellStatusInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStoreCategoryCodeDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStoreCategoryInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuWeightInfoDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuWeightInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SubmitAppealInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuIdByNameAndSpecRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuIdByOriginIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateSpuOptionFieldRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultBuilderUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultSpuDataUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import joptsimple.internal.Strings;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator.genResult;
import static com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtBrandConverterUtil.MT_CHANNEL_CATEGORY_SOURCE_TYPE_MEDICINE;

/**
 * @author: chenzhiyang
 * @date: 2020/4/20
 * @time: 11:49 PM
 */
@Service("mtBrandChannelSpuService")
public class MtBrandChannelSpuServiceImpl implements ChannelSpuService {

    public static final int SPU_CREATE_MAX_COUNT = 200;

    public static final int UPC_CREATE_MAX_COUNT = 50;

    public static final String EMPTY_VALUE = "EMPTY_VALUE";

    private static final int REPLACE = 2;

    private static final int ITEM_MAX_CATEGORY_SIZE = 5;

    private static final int MAX_PICTURE_COUNT = 8;
    private static final int SG_MAX_PICTURE_COUNT = 10;
    private static final int MAX_QUALIFICATION_PICTURE_COUNT = 1;

    @Value("${mt.url.base}" + "${mt.url.spuDetail}")
    private String spuDetail;

    @Value("${mt.url.base}" + "/retail/batchget")
    private String spuDetails;

    @Value("${mt.url.base}" + "${mt.url.spuList}")
    private String skuList;

    @Value("${mt.url.base}" + "${mt.url.spuAuditStatus}")
    private String spuAuditStatus;

    @Value("${mt.url.gwBase}" + "${mt.url.spuMedicineAuditStatus}")
    private String spuMedicineAuditStatus;

    @Value("${mt.url.base}" + "${mt.url.spuQualityProblems}")
    private String spuQualityProblems;

    @Value("${mt.url.base}" + "${mt.url.recommendChannelCategory}")
    private String recommendChannelCategoryUrl;

    @Value("${mt.url.base}" + "${mt.url.batchGetAppSpuCodesBySkuIds}")
    private String batchGetAppSpuCodesBySkuIds;

    @Value("${mt.url.base}" + "${mt.url.headquarterSpuList}")
    private String batchGetHeadQuarterSpuListUrl;

    @Value("${mt.url.base}" + "${mt.url.spuNormAuditDelete}")
    private String spuNormAuditDeleteUrl;

    @Value("${mt.url.base}" + "${mt.url.batchGetSpuId}")
    private String batchGetSpuIdUrl;

    @Resource
    private CommonLogger log;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private MtBrandConverterService mtConverterService;

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private TenantService tenantService;


    @Resource(name = "mtProductThreadPool")
    private ExecutorService mtProductThreadPool;

    /**
     * @param request 接口虽然支持多门店语义（多个门店创建同一批SKU); 当前使用场景storeIdList仅使用了一个门店id
     * @return
     */
    @Override
    public ResultSpuData spuCreate(SpuInfoRequest request) {
        return spuCreateCommon(request, ChannelPostMTEnum.SPU_CREATE);
    }

    @Override
    public ResultSpuData spuCreateForCleaner(SpuInfoRequest request) {
        return spuCreateCommon(request, ChannelPostMTEnum.SPU_CREATE_FOR_CLEANER);
    }

    private ResultSpuData spuCreateCommon(SpuInfoRequest request, ChannelPostInter channelPostInter) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        // 分页调用
        ListUtils.listPartition(request.getParamList(), SPU_CREATE_MAX_COUNT).forEach(data -> {
            List<SpuKey> bizKeyList = new ArrayList<>();
            try {
                // 返回结果组装用标识
                bizKeyList = getSpuKeys(data);

                // 商品图片只支持8张
                for (SpuInfoDTO spuInfoDTO : data) {
                    boolean isMedicineCategory = Fun.anyMatch(spuInfoDTO.getSkus(),
                            sku -> sku.getChannelCategorySourceType() == MT_CHANNEL_CATEGORY_SOURCE_TYPE_MEDICINE);
                    int MAX_PICTURE_COUNT_ = isMedicineCategory ? MAX_PICTURE_COUNT : SG_MAX_PICTURE_COUNT;
                    if (CollectionUtils.size(spuInfoDTO.getPictures()) > MAX_PICTURE_COUNT_) {
                        spuInfoDTO.setPictures(spuInfoDTO.getPictures().subList(0, MAX_PICTURE_COUNT_));
                    }
                }

                // 资质图只支持1张
                for (SpuInfoDTO spuInfoDTO : data) {
                    if (CollectionUtils.isNotEmpty(spuInfoDTO.getQualificationPictures()) &&
                            CollectionUtils.size(spuInfoDTO.getQualificationPictures()) > MAX_QUALIFICATION_PICTURE_COUNT) {
                        spuInfoDTO.setQualificationPictures(spuInfoDTO.getQualificationPictures().subList(0, MAX_QUALIFICATION_PICTURE_COUNT));
                    }
                }

                // 医药无人仓-映射销售属性
                if (tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId())) {
                    unmanWareHouseCustomProcess(data);
                }


                // 业务参数转换
                List<ChannelSpuInfoDTO> channelSpuInfoDTOList = mtConverterService.spuInfoDTOMappings(data);
                channelSpuInfoDTOList.forEach(x -> {
                    x.setBase_name(x.getName());
                    if (StringUtils.isBlank(x.getProduct_name_supplement())) {
                        x.setProduct_name_supplement("EMPTY_VALUE");
                    }
                });

                removeQualificationPictures(request.getBaseInfo().getTenantId(), channelSpuInfoDTOList);
                // 美团包装费下线适配
                boxPriceCustomProcess(channelSpuInfoDTOList, request.getBaseInfo());
                fillOriginUpcForSg(channelSpuInfoDTOList, data, request.getBaseInfo());
                fillOriginUpcForMedicine(channelSpuInfoDTOList, data, request.getBaseInfo());
                replaceMedicineNoUpc(channelSpuInfoDTOList, data);
                handleRemoveLimitFields(request, channelSpuInfoDTOList);

                ChannelSpuUpdateDTO dto = mtConverterService.updateSpuInfo(JSON.toJSONString(channelSpuInfoDTOList));

                //是否移除活动，默认不移除
                if (MccConfigUtil.removeActivitySwitch(request.getBaseInfo().getTenantId())) {
                    dto.setAct_check_type(ActCheckTypeEnum.ACT_REMOVE.getCode());
                }
                // 当前请求的优先级大于租户配置的优先级
                if(ActCheckTypeEnum.isVaild(request.getActCheckType())){
                    dto.setAct_check_type(request.getActCheckType());
                }
                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(channelPostInter, request.getBaseInfo(), dto);

                // 组装返回结果
                boolean isErpTenant = Objects.nonNull(request.getBaseInfo()) ? request.getBaseInfo().isErpTenant() : false;
                ResultSpuDataUtils.combinePartResultData(resultData, postResult, bizKeyList, ChannelTypeEnum.MEITUAN, isErpTenant);

            }
            catch (IllegalArgumentException e) {
                log.error("MtChannelSpuServiceImpl.spuCreate 参数校验失败, channelPostInter:{}, data:{}", channelPostInter, data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);
            }
            catch (InvokeChannelTooMuchException e) {
                log.warn("MtChannelSpuServiceImpl.spuCreate 触发限流, data:{}", data);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.TRIGGER_LIMIT.getMsg(), bizKeyList);
            }
            catch (Exception e) {
                log.error("MtChannelSpuServiceImpl.spuCreate 服务异常, channelPostInter:{}, data:{}", channelPostInter, data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), bizKeyList);
            }
        });
        return resultData;
    }

    private void removeQualificationPictures(Long tenantId, List<ChannelSpuInfoDTO> channelSpuInfoDTOList) {
        if (CollectionUtils.isEmpty(channelSpuInfoDTOList)) {
            return;
        }
        if (!MccConfigUtil.useQualificationPictures(tenantId)) {
            channelSpuInfoDTOList.forEach(spu -> {
                spu.setQualification_pictures(null);
                spu.setSpecial_pictures(null);
            });
        }
    }

    private void handleRemoveLimitFields(SpuInfoRequest request, List<ChannelSpuInfoDTO> channelSpuInfoDTOList) {
        if (request.removeLimitedField && request.isSetRemoveLimitedField()){
            channelSpuInfoDTOList.stream()
                    .filter(spu -> CollectionUtils.isNotEmpty(spu.getSkus()))
                    .flatMap(spu->spu.getSkus().stream())
                    .forEach(sku->sku.setUpc(null));
        }
    }

    private void replaceMedicineNoUpc(List<ChannelSpuInfoDTO> channelSpuInfoDTOList, List<SpuInfoDTO> spuInfoDTOList) {
        try {
            if (CollectionUtils.isEmpty(channelSpuInfoDTOList) || CollectionUtils.isEmpty(spuInfoDTOList)) {
                return;
            }

            List<SpuInfoDTO> medicineSpuInfoList = Fun.filter(spuInfoDTOList, spuInfo -> Fun.anyMatch(spuInfo.getSkus(),
                    sku -> sku.getChannelCategorySourceType() == MT_CHANNEL_CATEGORY_SOURCE_TYPE_MEDICINE));

            if (CollectionUtils.isEmpty(medicineSpuInfoList)) {
                return;
            }

            Map<String, ChannelSpuInfoDTO> appFoodCodeSpuInfoMap = Fun.toMapQuietly(channelSpuInfoDTOList, ChannelSpuInfoDTO::getApp_food_code);

            medicineSpuInfoList.forEach(spuInfo -> {
                ChannelSpuInfoDTO channelSpuInfo = appFoodCodeSpuInfoMap.get(spuInfo.getCustomSpuId());
                if (channelSpuInfo != null
                        && CollectionUtils.isNotEmpty(channelSpuInfo.getSkus())
                        && MccConfigUtil.getMedicineNoUpcCategoryCodeList().contains(spuInfo.getChannelCategoryId())) {
                    channelSpuInfo.getSkus().stream()
                            .filter(sku -> StringUtils.equals(sku.getUpc(), MtBrandConverterUtil.MEDICINE_NO_UPC))
                            .forEach(sku -> sku.setUpc(""));
                }
            });
        }
        catch (Exception e) {
            log.error("replaceMedicineNoUpc error,spuInfoDTOList:{}", JacksonUtils.toJson(spuInfoDTOList));
        }

    }

    private void fillOriginUpcForSg(List<ChannelSpuInfoDTO> channelSpuList, List<SpuInfoDTO> data, BaseRequest baseInfo) {
        try {
            if (Objects.isNull(baseInfo)) {
                return;
            }
            if (!tenantService.isErpSpuTenant(baseInfo.getTenantId())) {
                return;
            }
            if (MccConfigUtil.useNoUpc(baseInfo.getTenantId())) {
                return;
            }

            if (CollectionUtils.isEmpty(channelSpuList) || CollectionUtils.isEmpty(data)) {
                return;
            }
            List<ChannelSkuInfoDTO> noUpcSkuList = channelSpuList.stream()
                    .map(ChannelSpuInfoDTO::getSkus)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(List::stream)
                    .filter(sku -> MtBrandConverterUtil.NO_UPC.equals(sku.getUpc()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(noUpcSkuList)) {
                return;
            }

            List<SkuInSpuInfoDTO> originSkuList = data.stream()
                    .map(SpuInfoDTO::getSkus)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(originSkuList)) {
                return;
            }

            Map<String, SkuInSpuInfoDTO> originSkuMap = Fun.toMapQuietly(originSkuList, SkuInSpuInfoDTO::getCustomSkuId);
            noUpcSkuList.forEach(channelSku -> {
                SkuInSpuInfoDTO originSku = originSkuMap.get(channelSku.getSku_id());
                if (Objects.isNull(originSku)) {
                    return;
                }
                channelSku.setUpc(originSku.getUpc());
            });
        }
        catch (Exception e) {
            log.error("使用商品原upc覆盖[no_upc]失败", e);
        }
    }

    /**
     * 有ERP租户，需要将超过14位被赋值为[商品无条码]的UPC恢复回原UPC值
     */
    private void fillOriginUpcForMedicine(List<ChannelSpuInfoDTO> channelSpuList, List<SpuInfoDTO> data, BaseRequest baseInfo) {
        try {
            if (Objects.isNull(baseInfo)) {
                return;
            }
            if (!tenantService.isErpSpuTenant(baseInfo.getTenantId())) {
                return;
            }
            if (MccConfigUtil.useNoUpc(baseInfo.getTenantId())) {
                return;
            }
            if (CollectionUtils.isEmpty(channelSpuList) || CollectionUtils.isEmpty(data)) {
                return;
            }
            List<ChannelSkuInfoDTO> noUpcSkuList = channelSpuList.stream()
                    .map(ChannelSpuInfoDTO::getSkus)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(List::stream)
                    .filter(sku -> MtBrandConverterUtil.MEDICINE_NO_UPC.equals(sku.getUpc()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(noUpcSkuList)) {
                return;
            }

            List<SkuInSpuInfoDTO> originSkuList = data.stream()
                    .map(SpuInfoDTO::getSkus)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(originSkuList)) {
                return;
            }

            Map<String, SkuInSpuInfoDTO> originSkuMap = Fun.toMapQuietly(originSkuList, SkuInSpuInfoDTO::getCustomSkuId);
            noUpcSkuList.forEach(channelSku -> {
                SkuInSpuInfoDTO originSku = originSkuMap.get(channelSku.getSku_id());
                if (Objects.isNull(originSku)) {
                    return;
                }
                if (StringUtils.isNotBlank(originSku.getUpc())) {
                    channelSku.setUpc(originSku.getUpc());
                }
            });
        }
        catch (Exception e) {
            log.error("使用商品原upc覆盖[商品无条码]失败", e);
        }
    }

    /**
     * 商品包装费下线适配
     *
     * @param channelSpuInfoDTOList 渠道商品列表
     * @param baseInfo
     */
    private void boxPriceCustomProcess(List<ChannelSpuInfoDTO> channelSpuInfoDTOList, BaseRequest baseInfo) {
        channelSpuInfoDTOList.forEach(channelSpuInfoDTO -> {
            if (CollectionUtils.isEmpty(channelSpuInfoDTO.getSkus())) {
                return;
            }
            channelSpuInfoDTO.getSkus().forEach(channelSkuInfoDTO -> {
                channelSkuInfoDTO.setBox_price(null);
                channelSkuInfoDTO.setBox_num(null);
            });
        });
    }

    /**
     * 无人仓同步美团渠道定制化处理
     * @param channelSpuInfoDTOList
     */
    private void unmanWareHouseCustomProcess(List<SpuInfoDTO> channelSpuInfoDTOList) {
        channelSpuInfoDTOList.forEach(channelSpuInfoDTO -> {
            // 添加固定销售属性
            Long attrId = MccConfigUtil.getSaleAttrByCategory(channelSpuInfoDTO.getChannelCategoryId());
            if (attrId != null) {
                log.debug("无人仓灌入属性:attr:{},spuId:{}",attrId,channelSpuInfoDTO.getSpuId());
                Long valueId = -1L;
                for (SkuInSpuInfoDTO skuInfoDTO : channelSpuInfoDTO.getSkus()) {
                    SaleAttrValueDTO saleAttrValueDTO = new SaleAttrValueDTO();
                    saleAttrValueDTO.setAttrId(attrId);
                    saleAttrValueDTO.setValueId(valueId--);
                    saleAttrValueDTO.setValue(skuInfoDTO.getSpec());
                    skuInfoDTO.setSaleAttrValueList(Arrays.asList(saleAttrValueDTO));
                }
            }
        });
    }

    @Override
    public ResultSpuData updateWeightBySpu(SpuWeightInfoRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getSpuWeightInfoDtoList())) {
            return resultData;
        }
        // 分页调用
        ListUtils.listPartition(request.getSpuWeightInfoDtoList(), SPU_CREATE_MAX_COUNT).forEach(data -> {
            List<SpuKey> bizKeyList = new ArrayList<>();
            try {
                // 返回结果组装用标识
                bizKeyList = getSpuKeysFromSpuWeightInfoDto(data);

                // 业务参数转换
                List<ChannelSpuWeightInfoDTO> channelSpuInfoDTOList = mtConverterService.spuWeightInfoDTOMappings(data);

                ChannelSpuUpdateDTO dto = mtConverterService.updateSpuInfo(JSON.toJSONString(channelSpuInfoDTOList));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.SPU_CREATE, request.getBaseInfo(), dto);

                // 组装返回结果
                boolean isErpTenant = Objects.nonNull(request.getBaseInfo()) ? request.getBaseInfo().isErpTenant() : false;
                ResultSpuDataUtils.combinePartResultData(resultData, postResult, bizKeyList, ChannelTypeEnum.MEITUAN, isErpTenant);

            }
            catch (IllegalArgumentException e) {
                log.error("MtChannelSpuServiceImpl.updateWeightBySpu 参数校验失败, data:{}", data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);
            }
            catch (Exception e) {
                log.error("MtChannelSpuServiceImpl.updateWeightBySpu 服务异常, data:{}", data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), bizKeyList);
            }
        });

        return resultData;
    }

    /**
     * @param request 接口虽然支持多门店语义（多个门店创建同一批SPU); 当前使用场景storeIdList仅使用了一个门店id
     * @return
     */
    @Override
    public ResultSpuData upcCreate(SpuInfoRequest request) {
        return upcCreateCommon(request, ChannelPostMTEnum.SPU_UPC_CREATE);
    }

    @Override
    public ResultSpuData upcCreateForCleaner(SpuInfoRequest request) {
        return upcCreateCommon(request, ChannelPostMTEnum.SPU_UPC_CREATE_FOR_CLEANER);
    }

    private ResultSpuData upcCreateCommon(SpuInfoRequest request, ChannelPostInter channelPostInter) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }

        List<ChannelUpcCreateDTO> channelUpcCreateDTOList = new ArrayList<>();

        request.getParamList().forEach(spuInfoDTO -> {
            spuInfoDTO.getSkus().forEach(skuInSpuInfoDTO -> {
                ChannelUpcCreateDTO channelUpcCreateDTO = mtConverterService.upcInfoDTOMapping(spuInfoDTO);
                ChannelStandardSku standardSku = mtConverterService.standardSkuInfoDTOMapping(skuInSpuInfoDTO);
                channelUpcCreateDTO.setUpc_code(MtConverterUtil.convertUpc(skuInSpuInfoDTO));
                channelUpcCreateDTO.setStandard_sku(standardSku);
                channelUpcCreateDTOList.add(channelUpcCreateDTO);
            });
        });

        ListUtils.listPartition(channelUpcCreateDTOList, UPC_CREATE_MAX_COUNT).forEach(data -> {
            List<SpuKey> bizKeyList = new ArrayList<>();
            Map<String, SpuKey> spuKeyMap = Maps.newHashMap();
            try {
                // 返回结果组装用标识
                channelUpcCreateDTOList.forEach(item -> {
                    SkuKey skuKey = new SkuKey().setCustomSkuId(item.getStandard_sku().getSku_id());
                    SpuKey spuKey = spuKeyMap.get(item.getApp_food_code());
                    if (Objects.isNull(spuKey) || CollectionUtils.isEmpty(spuKey.getSkus())) {
                        spuKey = new SpuKey().setCustomSpuId(item.getApp_food_code());
                        spuKey.setSkus(Lists.newArrayList(skuKey));
                    }
                    else {
                        List<SkuKey> skuKeys = spuKey.getSkus();
                        skuKeys.add(skuKey);
                        spuKey.setSkus(skuKeys);

                    }
                    spuKeyMap.put(item.getApp_food_code(), spuKey);
                });
                bizKeyList = spuKeyMap.values().stream().collect(Collectors.toList());
                // 业务参数转换
                ChannelSpuUpdateDTO dto = mtConverterService.updateSpuInfo(JSON.toJSONString(data));
                if(ActCheckTypeEnum.isVaild(request.getActCheckType())){
                    dto.setAct_check_type(request.getActCheckType());
                }

                // 调用渠道接口 内部会循环storeIdList(多个门店创建同样的SPU)，并填充app_poi_code
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(channelPostInter, request.getBaseInfo(), dto);

                // 结果组装
                boolean isErpTenant = Objects.nonNull(request.getBaseInfo()) ? request.getBaseInfo().isErpTenant() : false;
                ResultSpuDataUtils.combinePartResultData(resultData, postResult, bizKeyList, ChannelTypeEnum.MEITUAN, isErpTenant);

            }
            catch (IllegalArgumentException e) {
                log.error("MtChannelSpuServiceImpl.upcCreate 参数校验失败, channelPostInter:{}, data:{}", channelPostInter, data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);
            }
            catch (InvokeChannelTooMuchException e) {
                log.warn("MtBrandChannelSpuServiceImpl.upcCreate 触发限流，data:{}", data);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.TRIGGER_LIMIT.getMsg(), bizKeyList);
            }
            catch (Exception e) {
                log.error("MtChannelSpuServiceImpl.upcCreate 服务异常, channelPostInter:{}, data:{}", channelPostInter, data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), bizKeyList);
            }
        });

        return resultData;
    }

    /**
     * @param data
     * @return
     */
    private List<SpuKey> getSpuKeys(SpuInfoSellStatusDTO data) {
        List<SpuKey> bizKeyList = new ArrayList<>();
        data.getCustomSpuIds().forEach(customSpuId -> {
            SpuKey spuKey = new SpuKey().setCustomSpuId(customSpuId);
            List<SkuKey> skuKeys = Lists.newArrayList();
            spuKey.setSkus(skuKeys);
            bizKeyList.add(spuKey);
        });
        return bizKeyList;
    }

    private List<SpuKey> getSpuKeys(List<SpuInfoDTO> data) {
        List<SpuKey> bizKeyList = new ArrayList<>();
        data.forEach(spuInfoDTO -> {
            SpuKey spuKey = new SpuKey().setCustomSpuId(spuInfoDTO.getCustomSpuId());
            List<SkuKey> skuKeys = new ArrayList<>();
            spuInfoDTO.getSkus().forEach(skuInSpuInfoDTO -> {
                SkuKey skuKey = new SkuKey().setCustomSkuId(skuInSpuInfoDTO.getCustomSkuId());
                skuKeys.add(skuKey);
            });
            spuKey.setSkus(skuKeys);
            bizKeyList.add(spuKey);
        });
        return bizKeyList;
    }

    private List<SpuKey> getSpuKeysFromSpuWeightInfoDto(List<SpuWeightInfoDto> data) {
        List<SpuKey> bizKeyList = new ArrayList<>();
        data.forEach(spuInfoDTO -> {
            SpuKey spuKey = new SpuKey().setCustomSpuId(spuInfoDTO.getCustomSpuId());
            List<SkuKey> skuKeys = new ArrayList<>();
            spuInfoDTO.getSkuWeightInfoDtoList().forEach(skuInSpuInfoDTO -> {
                SkuKey skuKey = new SkuKey().setCustomSkuId(skuInSpuInfoDTO.getCustomSkuId());
                skuKeys.add(skuKey);
            });
            spuKey.setSkus(skuKeys);
            bizKeyList.add(spuKey);
        });
        return bizKeyList;
    }


    /**
     * @param request 接口虽然支持多门店语义（多个门店创建同一批SPU); 当前使用场景storeIdList仅使用了一个门店id
     * @return
     */
    @Override
    public ResultSpuData updateBySpuOrUpc(SpuInfoRequest request) {
        return updateBySpuOrUpcCommon(request, false);
    }


    @Override
    public ResultSpuData updateBySpuOrUpcForCleaner(SpuInfoRequest request) {
        return updateBySpuOrUpcCommon(request, true);
    }

    private ResultSpuData updateBySpuOrUpcCommon(SpuInfoRequest request, boolean isCleaner) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }

        // 更新操作不设置价格
        // request.getParamList().stream().filter(Objects::nonNull).forEach(item -> item.getSkus().stream().forEach(itemDetail->itemDetail.setPriceIsSet(false)));

        //卖点置空,穿特殊约定的字符串给开放平台，除了美团医药，传该值会报错卖点不能超过10个字符
        request.getParamList().forEach(param -> {
            if (StringUtils.isBlank(param.getSellPoint()) && !tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId())) {
                param.setSellPoint(EMPTY_VALUE);
            }
        });

        List<SpuInfoDTO> spuCreateList = request.getParamList().stream().filter(item -> item.getSourceType() != SourceType.UpcCreate.getValue()).collect(Collectors.toList());

        List<SpuInfoDTO> upcCreateList = request.getParamList().stream().filter(item -> item.getSourceType() == SourceType.UpcCreate.getValue()).collect(Collectors.toList());
        // 根据sku创建的商品进行更新
        if (CollectionUtils.isNotEmpty(spuCreateList)) {

            SpuInfoRequest skuUpdateRequest = new SpuInfoRequest().setBaseInfo(request.getBaseInfo()).setParamList(spuCreateList);
            skuUpdateRequest.setActCheckType(request.getActCheckType());
            skuUpdateRequest.setRemoveLimitedField(request.isRemoveLimitedField());
            resultData = isCleaner ? this.spuCreateForCleaner(skuUpdateRequest) : this.spuCreate(skuUpdateRequest);
        }
        // 根据upc创建的商品进行更新
        if (CollectionUtils.isNotEmpty(upcCreateList)) {

            SpuInfoRequest upcUpdateRequest = new SpuInfoRequest().setBaseInfo(request.getBaseInfo()).setParamList(upcCreateList);
            upcUpdateRequest.setActCheckType(request.getActCheckType());

            ResultSpuData upcUpdateResultData = (isCleaner ? this.upcCreateForCleaner(request) : this.upcCreate(upcUpdateRequest));

            resultData.sucData.addAll(upcUpdateResultData.sucData);

            resultData.errorData.addAll(upcUpdateResultData.errorData);
        }
        // 对sku更新失败的的商品重试进行upc更新
        retryUpcUpdate(resultData, request.getBaseInfo(), request.paramList, isCleaner, request.getActCheckType());

        return resultData;
    }

    /**
     * 修改按upc新增只能按upc修改问题临时方案
     * 现在没有标识区分商品是通过sku还是upc接口新增，通过sku修改接口修改返回错误信息是
     * "此商品为标品，不允许更新"时，再调用upc更新接口更新
     *
     * @param resultData
     * @param baseInfo
     * @param data
     * @param isCleaner
     * @param actCheckType
     */
    private void retryUpcUpdate(ResultSpuData resultData, BaseRequest baseInfo, List<SpuInfoDTO> data, boolean isCleaner, Integer actCheckType) {
        if (CollectionUtils.isEmpty(resultData.getErrorData())) {
            return;
        }

        Map<String, SpuInfoDTO> appleMap = data.stream().collect(Collectors.toMap(SpuInfoDTO::getCustomSpuId, a -> a, (k1, k2) -> k1));
        SpuInfoRequest request = new SpuInfoRequest().setBaseInfo(baseInfo).setParamList(Lists.newArrayList()).setActCheckType(actCheckType);
        Iterator<ResultErrorSpu> itr = resultData.getErrorData().iterator();
        while (itr.hasNext()) {
            ResultErrorSpu item = itr.next();
            if (MccConfigUtil.getMTUpcUpdateErrMgs().equals(item.getErrorMsg())) {
                request.getParamList().add(appleMap.get(item.getSpuInfo().getCustomSpuId()));
                itr.remove();
            }
            ;
        }

        if (CollectionUtils.isNotEmpty(request.getParamList())) {
            ResultSpuData res = isCleaner ? this.upcCreateForCleaner(request) : this.upcCreate(request);
            resultData.getSucData().addAll(res.getSucData());
            resultData.getErrorData().addAll(res.getErrorData());
        }
    }

    /**
     * @param request 多门店接口；内部逐门店串行
     * @return
     */
    @Override
    public ResultSpuData deleteSpu(SpuInfoDeleteRequest request) {
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();

            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

            Map<Long, List<SpuInfoDeleteDTO>> storeDeleteMap = request.getParamList().stream()
                    .collect(Collectors.groupingBy(SpuInfoDeleteDTO::getStoreId));

            storeDeleteMap.forEach((storeId, deleteSpus) -> {
                BaseRequest storeRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                if (CollectionUtils.size(deleteSpus) == 1) {
                    Pair<SpuKey, ChannelResponseDTO> resultPair = deleteSingleSpu(storeId, storeRequest, deleteSpus.get(0));
                    fillDeleteResultData(storeId, channelId, resultPair, resultData);
                    return;
                }

                List<Future<Pair<SpuKey, ChannelResponseDTO>>> futureList = deleteSpus.stream()
                        .map(item -> mtProductThreadPool.submit(() -> deleteSingleSpu(storeId, storeRequest, item)))
                        .collect(Collectors.toList());

                futureList.forEach(future -> {
                    try {
                        Pair<SpuKey, ChannelResponseDTO> resultPair = future.get(5L,
                                TimeUnit.SECONDS);
                        fillDeleteResultData(storeId, channelId, resultPair, resultData);
                    }
                    catch (Exception e) {
                        log.warn("美团删除商品未知异常", e);
                        throw new BizException("美团删除商品异常:" + e.getMessage());
                    }
                });
            });
            return resultData;
        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.deleteSpu, 批量删除商品SPU服务异常, request:{}", request, e);
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量删除商品SPU失败");
        }
    }

    private void fillDeleteResultData(long storeId, int channelId, Pair<SpuKey, ChannelResponseDTO> resultPair, ResultSpuData resultData) {
        Map<Long, ChannelResponseDTO> postResult = new HashMap<>();
        postResult.put(storeId, resultPair.getValue());
        ResultBuilderUtil.resultAnalysis(channelId, storeId, resultPair.getKey(), postResult, resultData);
    }

    private Pair<SpuKey, ChannelResponseDTO> deleteSingleSpu(Long storeId, BaseRequest storeRequest, SpuInfoDeleteDTO item) {
        SpuKey bizKey = new SpuKey().setCustomSpuId(item.getCustomSpuId());
        try {
            ChannelSpuDeleteDTO channelSpuDeleteDTO = mtConverterService.spuDeleteMapping(item);

            // 请求线上渠道
            Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPostAppMapDto(ChannelPostMTEnum.SPU_DELETE, storeRequest, channelSpuDeleteDTO);
            if (Objects.isNull(postResult) || postResult.get(storeId) == null) {
                return Pair.of(bizKey, ChannelResponseDTO.defaultErrorResponse(ResultCode.UNKNOWN_ERROR.getCode(), "渠道未返回数据"));
            }
            return Pair.of(bizKey, postResult.get(storeId));
        }
        catch (InvokeChannelTooMuchException e) {
            log.warn("美团删除商品触发限流, item:{}", item);
            return Pair.of(bizKey, ChannelResponseDTO.defaultLimitErrorResponse());
        }
        catch (Exception e) {
            log.warn("美团删除商品异常 item:{}", item, e);
            return Pair.of(bizKey, ChannelResponseDTO.defaultErrorResponse(ResultCode.UNKNOWN_ERROR.getCode(), e.getMessage()));
        }
    }

    @Override
    public ResultSpuData deleteCategoryAndSpu(BaseRequest request) {
        try {
            Long storeId = request.getStoreIdList().get(0);
            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
            ChannelCategoryAndSpuDeleteDTO channelCategoryAndSpuDeleteDTO = mtConverterService.categoryAndSpuMapping(request);
            Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_AND_SPU_DELETE, request, storeId, channelCategoryAndSpuDeleteDTO);
            if (Objects.isNull(postResult)) {
                ResultBuilderUtil.resultAnalysis(request.getChannelId(), request.getTenantId(), "调用渠道接口失败", resultData);
            }
            ResultBuilderUtil.resultAnalysis(request.getChannelId(), request.getTenantId(), postResult, resultData);
            return resultData;
        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.deleteCategoryAndSpu, 批量删除分类和商品服务异常, request:{}", request, e);
        }
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量删除分类和商品服务失败");
    }

    /**
     * @param request 多门店接口；内部逐门店串行
     * @return
     */
    @Override
    public ResultSpuData deleteSku(SkuInSpuInfoDeleteRequest request) {
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();

            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
            // 逐门店串行
            request.getParamList().forEach(item -> {
                Long storeId = item.getStoreId();

                BaseRequest storeRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                SpuKey bizKey = new SpuKey().setCustomSpuId(item.getCustomSpuId());
                List<SkuKey> skuKeys = Arrays.asList(new SkuKey().setCustomSkuId(item.getCustomSkuId()));
                bizKey.setSkus(skuKeys);
                ChannelSkuInSpuDeleteDTO channelSkuInSpuDeleteDTO = mtConverterService.skuInSpuDeleteMapping(item);
                //是否移除活动，默认不移除
                if (MccConfigUtil.removeActivitySwitch(request.getBaseInfo().getTenantId())) {
                    channelSkuInSpuDeleteDTO.setAct_check_type(ActCheckTypeEnum.ACT_REMOVE.getCode());
                }
                // 当前请求的优先级大于租户配置的优先级
                if(ActCheckTypeEnum.isVaild(request.getActCheckType())){
                    channelSkuInSpuDeleteDTO.setAct_check_type(request.getActCheckType());
                }

                // 请求线上渠道
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.SKU_IN_SPU_DELETE, storeRequest, channelSkuInSpuDeleteDTO);
                if (Objects.isNull(postResult)) {
                    ResultBuilderUtil.resultAnalysis(channelId, storeId, bizKey, "调用渠道接口失败", resultData);
                }
                ResultBuilderUtil.resultAnalysis(channelId, storeId, bizKey, postResult, resultData);
            });
            return resultData;
        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.deleteSku, 批量删除商品SKU服务异常, request:{}", request, e);
        }
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量删除商品SKU失败");
    }

    /**
     * @param request 多门店接口；内部逐门店串行
     * @return
     */
    @Override
    public ResultSpuData updateSpuSellStatus(SpuSellStatusInfoRequest request) {
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();

            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
            // 逐门店串行
            request.getParamList().forEach(item -> {
                Long storeId = item.getStoreId();

                BaseRequest storeRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                List<SpuKey> bizKeyList = getSpuKeys(item);
                ChannelSpuSellStatusDTO channelSpuSellStatusDTO = mtConverterService.updateSpuSellStatusMapping(item);
                // 请求线上渠道
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.SPU_SELL_STATIS, storeRequest, channelSpuSellStatusDTO);

                boolean isErpTenant = Objects.nonNull(request.getBaseInfo()) ? request.getBaseInfo().isErpTenant() : false;
                ResultSpuDataUtils.combinePartResultData(resultData, postResult, bizKeyList, ChannelTypeEnum.MEITUAN, isErpTenant);
            });
            return resultData;
        }
        catch (InvokeChannelTooMuchException e) {
            log.warn("MtChannelSpuServiceImpl.updateSpuSellStatus, 触发限流, request:{}", request);
            return ResultGenerator.genResultSpuData(ResultCode.TRIGGER_LIMIT, "触发限流批量修改商品上下架状态触发限流");
        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.updateSpuSellStatus, 批量修改商品上下架状态失败, request:{}", request, e);
        }
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量修改商品上下架状态失败");
    }

    /**
     * @param request 多门店接口；内部逐门店串行
     * @return
     */
    @Override
    public ResultSpuData updateCustomSpuIdByOriginId(UpdateCustomSpuIdByOriginIdRequest request) {
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();

            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
            // 逐门店串行
            request.getParamList().forEach(item -> {
                Long storeId = item.getStoreId();

                BaseRequest storeRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                String customSpuId = Objects.isNull(item.getCustomSpuIdCurrent()) ? item.getCustomSpuIdOrigin() : item.getCustomSpuIdCurrent();
                SpuKey bizKey = new SpuKey().setCustomSpuId(customSpuId);
                ChannelSpuAndSkuIdUpdateByOriginDTO channelSpuAndSkuIdUpdateByOriginDTO = mtConverterService.updateCustomSpuIdByOriginIdMapping(item);

                // 请求线上渠道
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.UPDATE_CUSTOM_SPU_SKU_ID, storeRequest, channelSpuAndSkuIdUpdateByOriginDTO);
                if (Objects.isNull(postResult)) {
                    ResultBuilderUtil.resultAnalysis(channelId, item.getStoreId(), bizKey, "调用渠道接口失败", resultData);
                }
                ResultBuilderUtil.resultAnalysis(channelId, item.getStoreId(), bizKey, postResult, resultData, false);
            });
            return resultData;
        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.updateCustomSpuIdByOriginId, 根据原商品编码更换新商品编码服务异常, request:{}", request, e);
        }
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "根据原商品编码更换新商品编码失败");
    }


    /**
     * @param request 多门店接口；内部逐门店串行
     * @return
     */
    @Override
    public ResultSpuData updateCustomSpuIdByNameAndSpec(UpdateCustomSpuIdByNameAndSpecRequest request) {
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();

            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
            // 逐门店串行
            request.getParamList().forEach(item -> {
                Long storeId = item.getStoreId();

                BaseRequest storeRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                SpuKey bizKey = new SpuKey().setCustomSpuId(item.getCustomSpuId());
                ChannelSpuAndSkuIdUpdateByNameAndSpecDTO channelSpuAndSkuIdUpdateByNameAndSpecDTO = mtConverterService.valueOfUpdateId(item);

                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.UPDATE_CUSTOM_SPU_SKU_ID_BY_NAME, storeRequest, channelSpuAndSkuIdUpdateByNameAndSpecDTO);
                if (Objects.isNull(postResult)) {
                    ResultBuilderUtil.resultAnalysis(channelId, item.getStoreId(), bizKey, "调用渠道接口失败", resultData);
                }
                ResultBuilderUtil.resultAnalysis(channelId, item.getStoreId(), bizKey, postResult, resultData, false);
            });
            return resultData;
        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.updateCustomSpuIdByNameAndSpec, 根据商品名称和规格名称更换新的商品编码服务异常, request:{}", request, e);
        }
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "根据商品名称和规格名称更换新的商品编码失败");
    }

    /**
     * @param request 单门店接口
     * @return
     */
    @Override
    public GetSpuInfoResponse getSpuInfo(GetSpuInfoRequest request) {
        GetSpuInfoResponse response = new GetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();
            long storeId = request.getStoreId();

            // 获取渠道门店编码
            String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
            Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
            if (MapUtils.isEmpty(channelStoreDOMap)) {
                return response.setStatus(genResult(ResultCode.CHANNEL_STORE_CONFIG_ERROR));
            }

            // 构造参数
            Map<String, String> bizParam = Maps.newHashMap();
            String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
            String appFoodCode = request.getCustomSpuId();
            bizParam.put("app_poi_code", appPoiCode);
            bizParam.put("app_spu_code", appFoodCode);

            //限频处理
            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            //限频处理
            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
            boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.GET_SPU_DETAIL, appId);
            if (!tryAcquire) {
                // 获取当前执行权失败，返回可重试
                return response.setStatus(genResult(ResultCode.FAIL_ALLOW_RETRY, ProjectConstant.TRIGGER_LIMIT_MSG)).setSpuInfo(null);
            }

            // 请求线上渠道
            Map<String, Object> spuInfoMap = mtBrandChannelGateService.sendEncodedGet(spuDetail, null, baseRequest, bizParam);

            if (MapUtils.isNotEmpty(spuInfoMap) && ProjectConstant.NG.equals(spuInfoMap.get(ProjectConstant.DATA))) {
                JSONObject item = (JSONObject) spuInfoMap.get(ProjectConstant.ERROR);
                if (Objects.nonNull(item) && Objects.nonNull(item.getInteger("code"))
                        && item.getInteger("code") == ResultCode.MT_CHANNEL_SKU_NOT_EXIST.getCode()) {
                    return response.setStatus(ResultGenerator.genSuccessResult()).setSpuInfo(null);
                }
                // 返回渠道错误信息
                if (item != null && item.getString("msg") != null) {
                    return response.setStatus(genResult(ResultCode.FAIL, "获取美团商品失败:" + item.getString("msg")));
                }
            }

            if (MapUtils.isEmpty(spuInfoMap) || Objects.isNull(spuInfoMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(spuInfoMap.get(ProjectConstant.DATA))) {
                return response.setStatus(genResult(ResultCode.FAIL, "获取美团商品失败"));
            }
            JSONObject item = (JSONObject) spuInfoMap.get(ProjectConstant.DATA);

            // 组装数据
            ChannelSpuInfoDTO channelSpuInfoDTO = new ChannelSpuInfoDTO();
            // 异常情况处理，可能存在没有sku和店内分类信息
            if (StringUtils.isBlank(item.getString("skus")) || StringUtils.isBlank(item.getString("category_list"))) {
                log.error("MtChannelSku.getSpuInfo request:{} skus or category_list is empty json:{}", request, item);
                return response.setStatus(ResultGenerator.genSuccessResult()).setSpuInfo(null);
            }
            List<ChannelSkuInfoDTO> channelSkuInfoDTOS = convertSkuInfo((item.get("skus")).toString());
            List<ChannelShopCategoryDTO> channelShopCategoryDTOS = JSONArray.parseArray((item.get("category_list")).toString(), ChannelShopCategoryDTO.class);
            if (CollectionUtils.isEmpty(channelSkuInfoDTOS) || CollectionUtils.isEmpty(channelShopCategoryDTOS)) {
                log.error("MtChannelSku.getSpuInfo request:{} skus or category_list is empty json:{}", request, item);
                return response.setStatus(ResultGenerator.genSuccessResult()).setSpuInfo(null);
            }

            channelSpuInfoDTO.setCategory_list(channelShopCategoryDTOS);
            channelSpuInfoDTO.setSkus(channelSkuInfoDTOS);
            channelSpuInfoDTO.setApp_poi_code(appPoiCode);
            channelSpuInfoDTO.setApp_food_code(item.getString("app_spu_code"));
            channelSpuInfoDTO.setName(item.getString("name"));
            channelSpuInfoDTO.setDescription(item.getString("description"));
            channelSpuInfoDTO.setCategory_code(item.getString("category_code"));
            channelSpuInfoDTO.setCategory_name(item.getString("category_name"));
            channelSpuInfoDTO.setTag_id(item.getString("tag_id"));
            channelSpuInfoDTO.setZh_name(item.getString("zh_name"));
            channelSpuInfoDTO.setProduct_name(item.getString("product_name"));
            channelSpuInfoDTO.setOrigin_name(item.getString("origin_name"));
            channelSpuInfoDTO.setFlavour(item.getString("flavour"));
            channelSpuInfoDTO.setPicture(item.getString("picture"));
            channelSpuInfoDTO.setPicture_contents(item.getString("picture_contents"));
            channelSpuInfoDTO.setIs_sold_out(item.getInteger("is_sold_out"));
            channelSpuInfoDTO.setPrice(item.getFloat("price"));
            channelSpuInfoDTO.setMin_order_count(item.getInteger("min_order_count"));
            channelSpuInfoDTO.setBox_price(item.getFloat("box_price"));
            channelSpuInfoDTO.setBox_num(item.getFloat("box_num"));
            channelSpuInfoDTO.setUnit(item.getString("unit"));
            channelSpuInfoDTO.setSequence(item.getInteger("sequence"));
            channelSpuInfoDTO.setIsSp(item.getInteger("isSp"));
            channelSpuInfoDTO.setIs_specialty(item.getInteger("is_specialty"));
            channelSpuInfoDTO.setProperties(item.getString("properties"));
            channelSpuInfoDTO.setVideo_id(item.getLong("video_id"));
            channelSpuInfoDTO.setVideo_url_mp4(item.getString("video_url_mp4"));
            channelSpuInfoDTO.setCommon_attr_value(item.getString("common_attr_value"));
            channelSpuInfoDTO.setIs_show_upc_pic_contents(item.getInteger("is_show_upc_pic_contents"));
            channelSpuInfoDTO.setLimit_sale_info(item.getString("limit_sale_info"));
            channelSpuInfoDTO.setStock_config(item.getString("stock_config"));

            channelSpuInfoDTO.setAudit_status(item.getInteger("audit_status"));
            channelSpuInfoDTO.setIs_complete(item.getInteger("is_complete"));

            channelSpuInfoDTO.setSell_point(item.getString("sell_point"));

            channelSpuInfoDTO.setNorm_audit_type((item).getInteger("norm_audit_type"));
            channelSpuInfoDTO.setNorm_audit_status((item).getInteger("norm_audit_status"));

            channelSpuInfoDTO.setQua_approval_date(item.getLong("qua_approval_date"));
            channelSpuInfoDTO.setQua_effective_date(item.getLong("qua_effective_date"));
            channelSpuInfoDTO.setQua_pictures(item.getString("qua_pictures"));
            channelSpuInfoDTO.setProduct_rules(item.getString("product_rules"));
            channelSpuInfoDTO.setQualification_pictures(item.getString("qualification_pictures"));

            channelSpuInfoDTO.setBase_name(item.getString("base_name"));
            channelSpuInfoDTO.setProduct_name_supplement(item.getString("product_name_supplement"));
            channelSpuInfoDTO.setProduct_name_supplement_seq(item.getInteger("product_name_supplement_seq"));
            if (StringUtils.isNotBlank(item.getString("special_pictures"))) {
                channelSpuInfoDTO.setSpecial_pictures(JSONArray.parseArray(item.getString("special_pictures"), SpecialPictureInfoDTO.class));
            }
            SpuInfoDTO spuInfoDTO = mtConverterService.channelSpuInfoDTOMapping(channelSpuInfoDTO);
            return response.setStatus(ResultGenerator.genSuccessResult()).setSpuInfo(spuInfoDTO);
        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.getSpuInfo, 获取单个商品信息异常, request:{}", request, e);
        }
        return response.setStatus(ResultGenerator.genSuccessResult()).setSpuInfo(null);
    }


    private List<ChannelSkuInfoDTO> convertSkuInfo(String skusJson) {
        List<ChannelSkuInfoDTO> channelSkuInfoDTOS = JSONArray.parseArray(skusJson, ChannelSkuInfoDTO.class);
        // 排除未售卖的规格
        if (MccConfigUtil.isOpenSkuNoneSaleFilter()) {
            channelSkuInfoDTOS = channelSkuInfoDTOS.stream().filter(sku -> !ProjectConstant.SKU_NONE_SELL_FLAG.equals(sku.getIsSellFlag())).collect(Collectors.toList());
        }
        return channelSkuInfoDTOS;
    }

    /**
     * @param request 单门店接口
     * @return
     */
    @Override
    public GetSpuInfosResponse getSpuInfoList(GetSpuInfosRequest request) {
        GetSpuInfosResponse response = new GetSpuInfosResponse().setStatus(ResultGenerator.genSuccessResult());
        if (CollectionUtils.isEmpty(request.getCustomSpuIds())) {
            return response;
        }

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();
        long storeId = request.getStoreId();

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(genResult(ResultCode.CHANNEL_STORE_CONFIG_ERROR));
        }

        // 构造参数
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        bizParam.put("app_poi_code", appPoiCode);
        bizParam.put("app_spu_codes", Joiner.on(",").join(request.getCustomSpuIds()));

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        // 请求线上渠道
        Map<String, Object> spuInfoMap;
        if (MccConfigUtil.isSpuQueryUseUrlEncode(tenantId)) {
            spuInfoMap = mtBrandChannelGateService.sendEncodedGet(spuDetails, null, baseRequest, bizParam);
        }
        else {
            spuInfoMap = mtBrandChannelGateService.sendGet(spuDetails, null, baseRequest, bizParam);
        }

        if (MapUtils.isNotEmpty(spuInfoMap) && ProjectConstant.NG.equals(spuInfoMap.get(ProjectConstant.DATA))) {
            JSONObject item = (JSONObject) spuInfoMap.get(ProjectConstant.ERROR);
            if (Objects.nonNull(item) && Objects.nonNull(item.getInteger("code"))
                    && item.getInteger("code") == ResultCode.MT_CHANNEL_SKU_NOT_EXIST.getCode()) {
                return response.setStatus(ResultGenerator.genSuccessResult()).setSpuInfos(Lists.newArrayList());
            }
            // 返回渠道错误信息
            if (item != null && item.getString("msg") != null) {
                return response.setStatus(genResult(ResultCode.FAIL, "获取美团商品失败:" + item.getString("msg")));
            }
        }

        if (MapUtils.isEmpty(spuInfoMap) || Objects.isNull(spuInfoMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(spuInfoMap.get(ProjectConstant.DATA))) {
            return response.setStatus(genResult(ResultCode.FAIL, "获取多个美团商品失败"));
        }
        List<SpuInfoDTO> spuInfoDTOList = Lists.newArrayList();
        JSONArray spuInfoJSONArray = (JSONArray) spuInfoMap.get(ProjectConstant.DATA);
        // 组装数据
        spuInfoJSONArray.forEach(item -> spuInfoDTOList.add(convertSingleItem((JSONObject) item, appPoiCode)));
        response.setSpuInfos(spuInfoDTOList);
        return response;
    }

    /**
     * @param request 单门店接口
     * @return
     */
    @Override
    public BatchGetSpuInfoResponse batchGetSpuInfo(BatchGetSpuInfoRequest request) {
        BatchGetSpuInfoResponse response = new BatchGetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();
            long storeId = request.getStoreId();

            // 获取渠道门店编码
            String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
            Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
            if (MapUtils.isEmpty(channelStoreDOMap)) {
                return response.setStatus(genResult(ResultCode.FAIL)).setSpuInfos(Collections.emptyList());
            }

            // 构造参数
            int pageSize = request.getPageSize();
            Map<String, String> bizParam = Maps.newHashMap();
            String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
            bizParam.put("offset", String.valueOf((request.getPageNum() - 1) * pageSize));
            bizParam.put("limit", String.valueOf(pageSize));
            bizParam.put("app_poi_code", appPoiCode);
            if(request.isUseMtProductId()){
                bizParam.put("use_mt_product_id", "2");
            }

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            //限频处理
            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
            boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.BATCH_GET_SPUINFO, appId);
            if (!tryAcquire) {
                log.warn("MtBrandChannelSpuServiceImpl.batchGetSpuInfo 触发限流，tenantId: {}, storeId: {}", tenantId, storeId);
                return response.setStatus(genResult(ResultCode.TRIGGER_LIMIT)).setSpuInfos(Collections.emptyList());
            }

            // 请求线上渠道7x
            Map spuInfoMap = mtBrandChannelGateService.sendGet(skuList, null, baseRequest, bizParam);
            if (MapUtils.isEmpty(spuInfoMap) || Objects.isNull(spuInfoMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(spuInfoMap.get(ProjectConstant.DATA))) {
                String errorMsg = "获取美团商品列表失败";
                if (MapUtils.isNotEmpty(spuInfoMap)) {
                    JSONObject error = (JSONObject) spuInfoMap.get(ProjectConstant.ERROR);
                    errorMsg = error != null ? error.getString(ProjectConstant.MSG) : errorMsg;
                }
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, errorMsg));
            }

            List<SpuInfoDTO> spuInfoDTOList = Lists.newArrayList();
            JSONArray spuInfoJSONArray = (JSONArray) spuInfoMap.get(ProjectConstant.DATA);
            JSONObject extraInfo = (JSONObject) spuInfoMap.get(ProjectConstant.EXTRA_INFO);
            int totalCount = extraInfo != null && null != extraInfo.get(ProjectConstant.TOTAL_COUNT) ? (int) extraInfo.get(ProjectConstant.TOTAL_COUNT) : 0;
            int totalPage = totalCount % request.getPageSize() == 0 ? totalCount / request.getPageSize() : (totalCount / request.getPageSize()) + 1;
            PageInfo pageInfo = new PageInfo(request.getPageNum(), request.getPageSize(), totalPage, totalCount);
            // 组装数据
            spuInfoJSONArray.forEach(item -> spuInfoDTOList.add(convertSingleItem((JSONObject) item, appPoiCode)));

            return response.setStatus(ResultGenerator.genSuccessResult()).setSpuInfos(spuInfoDTOList).setPageInfo(pageInfo);
        }
        catch (Exception e) {
            log.error("MtBrandChannelSpuServiceImpl.batchGetSpuInfo, 批量获取商品信息异常, request:{}", request, e);
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "业务异常"));
        }
    }

    private SpuInfoDTO convertSingleItem(JSONObject item, String appPoiCode) {
        ChannelSpuInfoDTO channelSpuInfoDTO = new ChannelSpuInfoDTO();
        List<ChannelSkuInfoDTO> channelSkuInfoDTOS = null;
        if (StringUtils.isNotBlank(item.getString("skus"))) {
            channelSkuInfoDTOS = convertSkuInfo(item.get("skus").toString());
        }
        List<ChannelShopCategoryDTO> channelShopCategoryDTOS = null;
        if (StringUtils.isNotBlank((item).getString("category_list"))) {
            channelShopCategoryDTOS = JSONArray.parseArray(((item).get("category_list")).toString(), ChannelShopCategoryDTO.class);
        }
        if (CollectionUtils.isEmpty(channelSkuInfoDTOS) || CollectionUtils.isEmpty(channelShopCategoryDTOS)) {
            log.warn("MtChannelSku.batchGetSpuInfo skus or category_list is empty json:{}", item);
        }
        channelSpuInfoDTO.setMt_spu_id((item).getString("mt_spu_id"));
        channelSpuInfoDTO.setCategory_list(channelShopCategoryDTOS);
        channelSpuInfoDTO.setSkus(channelSkuInfoDTOS);
        channelSpuInfoDTO.setApp_poi_code(appPoiCode);
        channelSpuInfoDTO.setApp_food_code((item).getString("app_spu_code"));
        channelSpuInfoDTO.setName((item).getString("name"));
        channelSpuInfoDTO.setDescription((item).getString("description"));
        channelSpuInfoDTO.setCategory_code((item).getString("category_code"));
        channelSpuInfoDTO.setCategory_name((item).getString("category_name"));
        channelSpuInfoDTO.setTag_id((item).getString("tag_id"));
        channelSpuInfoDTO.setZh_name((item).getString("zh_name"));
        channelSpuInfoDTO.setProduct_name((item).getString("product_name"));
        channelSpuInfoDTO.setOrigin_name((item).getString("origin_name"));
        channelSpuInfoDTO.setFlavour((item).getString("flavour"));
        channelSpuInfoDTO.setPicture((item).getString("picture"));
        channelSpuInfoDTO.setPicture_contents((item).getString("picture_contents"));
        channelSpuInfoDTO.setIs_sold_out((item).getInteger("is_sold_out"));
        channelSpuInfoDTO.setPrice((item).getFloat("price"));
        channelSpuInfoDTO.setMin_order_count((item).getInteger("min_order_count"));
        channelSpuInfoDTO.setBox_price((item).getFloat("box_price"));
        channelSpuInfoDTO.setBox_num((item).getFloat("box_num"));
        channelSpuInfoDTO.setUnit((item).getString("unit"));
        channelSpuInfoDTO.setSequence((item).getInteger("sequence"));
        channelSpuInfoDTO.setIsSp((item).getInteger("isSp"));
        channelSpuInfoDTO.setIs_specialty((item).getInteger("is_specialty"));
        channelSpuInfoDTO.setProperties((item).getString("properties"));
        channelSpuInfoDTO.setVideo_id((item).getLong("video_id"));
        channelSpuInfoDTO.setVideo_url_mp4((item).getString("video_url_mp4"));
        channelSpuInfoDTO.setVideo_pic_large_url(item.getString("video_pic_large_url"));
        channelSpuInfoDTO.setCommon_attr_value((item).getString("common_attr_value"));
        channelSpuInfoDTO.setIs_show_upc_pic_contents((item).getInteger("is_show_upc_pic_contents"));
        channelSpuInfoDTO.setLimit_sale_info((item).getString("limit_sale_info"));
        channelSpuInfoDTO.setStock_config((item).getString("stock_config"));

        channelSpuInfoDTO.setAudit_status((item).getInteger("audit_status"));
        channelSpuInfoDTO.setIs_complete((item).getInteger("is_complete"));

        channelSpuInfoDTO.setSell_point((item).getString("sell_point"));

        channelSpuInfoDTO.setNorm_audit_type((item).getInteger("norm_audit_type"));
        channelSpuInfoDTO.setNorm_audit_status((item).getInteger("norm_audit_status"));

        //是否组合商品
        channelSpuInfoDTO.setIs_combination((item).getInteger("is_combination"));

        channelSpuInfoDTO.setQua_approval_date(item.getLong("qua_approval_date"));
        channelSpuInfoDTO.setQua_effective_date(item.getLong("qua_effective_date"));
        channelSpuInfoDTO.setQua_pictures(item.getString("qua_pictures"));
        channelSpuInfoDTO.setProduct_rules(item.getString("product_rules"));
        channelSpuInfoDTO.setQualification_pictures(item.getString("qualification_pictures"));
        if (StringUtils.isNotBlank(item.getString("special_pictures"))) {
            channelSpuInfoDTO.setSpecial_pictures(JSONArray.parseArray(item.getString("special_pictures"), SpecialPictureInfoDTO.class));
        }
        Object indicatorItems = ((JSONObject) item).get("indicator_items");
        if (Objects.nonNull(indicatorItems)) {
            channelSpuInfoDTO.setIndicator_items(JSON.parseArray(indicatorItems.toString(), Integer.class));
        }


        channelSpuInfoDTO.setProduct_name_supplement_seq((item).getInteger("product_name_supplement_seq"));
        channelSpuInfoDTO.setProduct_name_supplement((item).getString("product_name_supplement"));
        channelSpuInfoDTO.setBase_name((item).getString("base_name"));

        return mtConverterService.channelSpuInfoDTOMapping(channelSpuInfoDTO);
    }

    /**
     * 歪马业务, 新供给不可调用
     *
     * @param request
     * @return
     */
    @Deprecated
    @Override
    public GetHeadQuarterSpuInfoResponse batchGetHeadQuarterSpuInfo(BasePageRequest request) {
        GetHeadQuarterSpuInfoResponse response = new GetHeadQuarterSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        try {
            BaseRequestSimple baseInfo = new BaseRequestSimple(request.getTenantId(), request.getChannelId());
            Map<String, Object> bizParam = Maps.newHashMap();
            Long merchantId = MccConfigUtil.getMerchantId(request.getTenantId(), request.getChannelId());
            bizParam.put("merchant_id", merchantId);
            bizParam.put("page_num", request.getPageNo());
            bizParam.put("page_size", request.getPageSize());

            //
            Map headQuarterSpuMap = mtBrandChannelGateService.sendPost(batchGetHeadQuarterSpuListUrl, null,
                    baseConverterService.baseRequest(baseInfo), bizParam);
            if (MapUtils.isEmpty(headQuarterSpuMap) || Objects.isNull(headQuarterSpuMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(headQuarterSpuMap.get(ProjectConstant.DATA))) {
                return response.setStatus(genResult(ResultCode.FAIL, "获取美团商品列表失败"));
            }

            List<HeadQuarterSpuInfoDTO> headQuarterSpuInfoDTOList = Lists.newArrayList();
            JSONArray headQuarterSpuJSONArray = (JSONArray) headQuarterSpuMap.get(ProjectConstant.DATA);
            headQuarterSpuJSONArray.forEach(item -> {
                JSONObject spuObject = (JSONObject) item;
                HeadQuarterSpuInfoDTO dto = new HeadQuarterSpuInfoDTO();
                dto.setAppSpuCode(spuObject.getString("app_spu_code"));
                dto.setName(spuObject.getString("name"));
                dto.setDescription(spuObject.getString("description"));
                dto.setCategoryName(spuObject.getString("category_name"));
                dto.setSecondaryCategoryName(spuObject.getString("secondary_category_name"));
                dto.setCategoryList(spuObject.getString("category_name_list"));
                dto.setIsSoldOut(Optional.ofNullable(spuObject.getInteger("is_sold_out")).orElse(1));
                dto.setSequence(Optional.ofNullable(spuObject.getInteger("sequence")).orElse(-1));
                dto.setTagId(spuObject.getLong("tag_id"));
                dto.setPicture(spuObject.getString("picture"));
                dto.setAuditStatus(Optional.ofNullable(spuObject.getInteger("audit_status")).orElse(0));
                dto.setNormAuditType(Optional.ofNullable(spuObject.getInteger("norm_audit_type")).orElse(0));
                dto.setProperties(spuObject.getString("properties"));
                dto.setSellPoint(spuObject.getString("sell_point"));
                dto.setCommonAttrValue(spuObject.getString("common_attr_value"));
                dto.setPictureContents(spuObject.getString("picture_contents"));
                dto.setIsSpecialty(Optional.ofNullable(spuObject.getInteger("is_specialty")).orElse(0));
                dto.setPoiCount(Optional.ofNullable(spuObject.getInteger("poi_count")).orElse(0));

                JSONArray headQuarterSkuJSONArray = spuObject.getJSONArray("skus");
                List<HeadQuarterSkuInfoDTO> skuInfoDTOList = Lists.newArrayList();
                dto.setSkuList(skuInfoDTOList);
                headQuarterSkuJSONArray.forEach(sku -> {
                    JSONObject skuObject = (JSONObject) sku;
                    HeadQuarterSkuInfoDTO skuInfoDTO = new HeadQuarterSkuInfoDTO();
                    skuInfoDTO.setCustomSkuId(skuObject.getString("sku_id"));
                    skuInfoDTO.setSpec(skuObject.getString("spec"));
                    skuInfoDTO.setUpc(skuObject.getString("upc"));
                    skuInfoDTO.setStock(skuObject.getString("stock"));
                    skuInfoDTO.setPrice(skuObject.getString("price"));
                    skuInfoDTO.setUnit(skuObject.getString("unit"));
                    skuInfoDTO.setMinOrderCount(skuObject.getInteger("min_order_count"));
                    skuInfoDTO.setAvailableTimes(skuObject.getString("available_times"));
                    skuInfoDTO.setLadderBoxNum(skuObject.getString("ladder_box_num"));
                    skuInfoDTO.setLadderBoxPrice(skuObject.getString("ladder_box_price"));
                    skuInfoDTO.setWeightForUnit(skuObject.getString("weight_for_unit"));
                    skuInfoDTO.setWeightUnit(skuObject.getString("weight_unit"));
                    skuInfoDTO.setIsSellFlag(Optional.ofNullable(skuObject.getInteger("isSellFlag")).orElse(0));
                    skuInfoDTO.setAllowNoUpc(Optional.ofNullable(skuObject.getBoolean("allow_no_upc")).orElse(false));

                    skuInfoDTOList.add(skuInfoDTO);
                });

                headQuarterSpuInfoDTOList.add(dto);
            });

            response.setSpuInfoList(headQuarterSpuInfoDTOList);
        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.batchGetHeadQuarterSpuInfo, 查询总部商品异常, request:{}", request, e);
            return response.setStatus(ResultGenerator.genFailResult("查询总部商品异常"));
        }
        return response.setStatus(ResultGenerator.genSuccessResult());
    }

    /**
     * @param request 单门店接口
     * @return
     */
    @Override
    public QueryAuditStatusResponse getAuditStatus(QueryAuditStatusRequest request) {
        QueryAuditStatusResponse response = new QueryAuditStatusResponse().setStatus(ResultGenerator.genSuccessResult());
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();
            long storeId = request.getStoreId();

            // 获取渠道门店编码
            String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
            Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
            if (MapUtils.isEmpty(channelStoreDOMap)) {
                return response.setStatus(genResult(ResultCode.CHANNEL_STORE_CONFIG_ERROR)).setAuditStatusList(Collections.emptyList());
            }

            // 构造参数
            Map<String, String> bizParam = Maps.newHashMap();
            String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
            // 判断是否医药，构造不同的请求参数
            boolean isMedicine = request.isIsMedicine();
            if(isMedicine) {
                bizParam.put("app_poi_code", appPoiCode);
                if (StringUtils.isNotBlank(request.getCustomSpuId())) {
                    bizParam.put("app_medicine_codes", request.getCustomSpuId());
                }
            } else {
                bizParam.put("app_poi_code", appPoiCode);
                if (StringUtils.isNotBlank(request.getCustomSpuId())) {
                    bizParam.put("app_food_code", request.getCustomSpuId());
                }
                if (request.getAuditStatus() != -1) {
                    bizParam.put("audit_status", String.valueOf(request.getAuditStatus()));
                }
                if (request.getNormAuditStatus() != -1) {
                    bizParam.put("norm_audit_status", String.valueOf(request.getNormAuditStatus()));
                }
                bizParam.put("page_num", String.valueOf(request.getPageNum()));
                bizParam.put("page_size", String.valueOf(request.getPageSize()));
            }

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            //限频处理
            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
            boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.GET_AUDIT_STATUS, appId);
            if (!tryAcquire) {
                // 获取当前执行权失败，返回可重试
                return response.setStatus(genResult(ResultCode.FAIL_ALLOW_RETRY)).setAuditStatusList(Collections.emptyList());
            }

            // 请求线上渠道
            Map<String, Object> spuAuditStatusMap;
            JSONArray auditStatusJSONArray;
            List<AuditStatusDTO> auditStatusDTOList = Lists.newArrayList();
            if (MccConfigUtil.isSpuQueryUseUrlEncode(tenantId)) {
                spuAuditStatusMap = mtBrandChannelGateService.sendEncodedGet(isMedicine ? spuMedicineAuditStatus : spuAuditStatus, null, baseRequest, bizParam);
            }
            else {
                spuAuditStatusMap = mtBrandChannelGateService.sendGet(isMedicine ? spuMedicineAuditStatus : spuAuditStatus, null, baseRequest, bizParam);
            }
            log.info("查询门店商品审核数据, request:{}, response:{}", baseRequest, spuAuditStatusMap);
            //取出具体的报错信息添加到msg里
            String msg = null;
            if (MapUtils.isEmpty(spuAuditStatusMap) || Objects.isNull(spuAuditStatusMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(spuAuditStatusMap.get(ProjectConstant.DATA))) {
                if (MapUtils.isNotEmpty(spuAuditStatusMap) && Objects.nonNull(spuAuditStatusMap.get(ProjectConstant.ERROR))) {
                    msg = ((JSONObject) spuAuditStatusMap.get(ProjectConstant.ERROR)).getString(ProjectConstant.MSG);
                }
                msg = StringUtils.isBlank(msg) ? "" : "：" + msg;
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团商品请求失败" + msg));
            }
            //医药的接口返回列表是data.hits，零售返回的是data
            if (isMedicine) {
                Map data = (JSONObject) spuAuditStatusMap.get(ProjectConstant.DATA);
                if (MapUtils.isEmpty(data) || Objects.isNull(data.get(ProjectConstant.MT_YY_DATA)) || ProjectConstant.NG.equals(data.get(ProjectConstant.MT_YY_DATA))) {
                    return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团医药商品列表失败"));
                }
                auditStatusJSONArray = (JSONArray) data.get(ProjectConstant.MT_YY_DATA);
            } else {
                auditStatusJSONArray = (JSONArray) spuAuditStatusMap.get(ProjectConstant.DATA);
            }

            // 组装数据
            auditStatusJSONArray.forEach(item -> {
                AuditStatusDTO auditStatusDTO = new AuditStatusDTO();
                //分医药和零售的数据结构
                if (isMedicine) {
                    auditStatusDTO.setCustomSpuId(((JSONObject) item).getString("app_medicine_code"));
                    auditStatusDTO.setName(((JSONObject) item).getString("name"));
                    //医药的并不返回上下架状态，默认写个0
                    auditStatusDTO.setIsSoldOut(0);
                    auditStatusDTO.setCtime(((JSONObject) item).getLong("audit_commit_time"));
                    //审核中的商品不会有审核时间，其值为null
                    Long finishTime = ((JSONObject) item).getLong("audit_finish_time");
                    auditStatusDTO.setUtime(finishTime != null ? finishTime : 0L);
                    //医药的审核状态为4是脏数据，但是零售的状态4是纠错驳回，这儿为了不冲突，改为-1返回
                    auditStatusDTO.setAuditStatus(((JSONObject) item).getInteger("audit_status") == 4 ? -1 : ((JSONObject) item).getInteger("audit_status"));
                    auditStatusDTO.setComment(((JSONObject) item).getString("reject_reasons"));
                } else {
                    auditStatusDTO.setCustomSpuId(((JSONObject) item).getString("app_food_code"));
                    auditStatusDTO.setName(((JSONObject) item).getString("name"));
                    auditStatusDTO.setIsSoldOut(((JSONObject) item).getInteger("is_sold_out"));
                    auditStatusDTO.setCtime(((JSONObject) item).getLong("ctime"));
                    auditStatusDTO.setUtime(((JSONObject) item).getLong("utime"));
                    auditStatusDTO.setAuditStatus(((JSONObject) item).getInteger("audit_status"));
                    auditStatusDTO.setComment(((JSONObject) item).getString("comment"));
                    auditStatusDTO.setTagId(((JSONObject) item).getLong("tag_id").toString());
                }
                if (Objects.nonNull(((JSONObject) item).get("norm_audit_type"))) {
                    Integer normAuditType = ((JSONObject) item).getInteger("norm_audit_type");
                    if (Objects.nonNull(normAuditType)) {
                        auditStatusDTO.setNormAuditType(normAuditType);
                    }
                }
                if (Objects.nonNull(((JSONObject) item).get("norm_audit_status"))) {
                    Integer normAuditStatus = ((JSONObject) item).getInteger("norm_audit_status");
                    if (Objects.nonNull(normAuditStatus)) {
                        auditStatusDTO.setNormAuditStatus(normAuditStatus);
                    }
                }
                if (((JSONObject) item).containsKey("grace_period_end_time")) {
                    Long gracePeriodEndTime = ((JSONObject) item).getLong("grace_period_end_time");
                    if (Objects.nonNull(gracePeriodEndTime)) {
                        auditStatusDTO.setGracePeriodEndTime(gracePeriodEndTime);
                    }
                }
                if (Objects.nonNull(((JSONObject) item).get("norm_violation_list"))) {
                    JSONArray violationJSONArray = (JSONArray) ((JSONObject) item).get("norm_violation_list");
                    if (Objects.nonNull(violationJSONArray)) {
                        List<NormAuditViolationDTO> violationDTOS = Lists.newArrayList();
                        violationJSONArray.forEach(obj -> {
                            NormAuditViolationDTO violationDTO = new NormAuditViolationDTO();
                            violationDTO.setNormAuditComment(((JSONObject) obj).getString("norm_comment"));
                            if (Objects.nonNull(((JSONObject) obj).get("picture_url_list"))) {
                                violationDTO.setImgUrlList(
                                        ((JSONObject) obj).getJSONArray("picture_url_list")
                                                .stream()
                                                .filter(Objects::nonNull)
                                                .map(idx -> (String) idx)
                                                .collect(Collectors.toList()));
                            }
                            // 新增合规审核驳回原因列表
                            if (Objects.nonNull(((JSONObject) obj).get("norm_violation_problem_list"))) {
                                JSONArray normViolationProblemJSONArray =  ((JSONObject) obj).getJSONArray(
                                        "norm_violation_problem_list");
                                List<RejectProblemDTO> rejectProblemDTOList = new ArrayList<>();
                                if (CollectionUtils.isNotEmpty(normViolationProblemJSONArray)) {
                                    normViolationProblemJSONArray.forEach(normViolationProblemItem -> {
                                        if (normViolationProblemItem == null) {
                                            return;
                                        }
                                        if (Objects.nonNull(((JSONObject) normViolationProblemItem).get("problem_second_list"))) {
                                            JSONArray secondNormViolationProblemJSONArray =  ((JSONObject) normViolationProblemItem).getJSONArray(
                                                    "problem_second_list");
                                            if (CollectionUtils.isNotEmpty(secondNormViolationProblemJSONArray)) {
                                                boolean flag = false;
                                                for (Object secondNormViolationProblemItem : secondNormViolationProblemJSONArray) {
                                                    if (secondNormViolationProblemItem == null) {
                                                        continue;
                                                    }
                                                    RejectProblemDTO secondRejectProblemDTO = new RejectProblemDTO();
                                                    secondRejectProblemDTO.setProblemUniqCode(((JSONObject) secondNormViolationProblemItem)
                                                            .getString("problem_uniq_code"));
                                                    secondRejectProblemDTO.setProblemName(((JSONObject) secondNormViolationProblemItem)
                                                            .getString("problem_name"));
                                                    rejectProblemDTOList.add(secondRejectProblemDTO);
                                                    flag = true;
                                                }
                                                // 当前节点有子节点时不再添加到结果列表中
                                                if(flag){
                                                    return;
                                                }
                                            }
                                        }
                                        RejectProblemDTO rejectProblemDTO = new RejectProblemDTO();
                                        rejectProblemDTO.setProblemUniqCode(((JSONObject) normViolationProblemItem)
                                                .getString("problem_uniq_code"));
                                        rejectProblemDTO.setProblemName(((JSONObject) normViolationProblemItem)
                                                .getString("problem_name"));
                                        rejectProblemDTOList.add(rejectProblemDTO);
                                    });
                                    violationDTO.setNormViolationProblemDTOList(rejectProblemDTOList);
                                }
                            }
                            violationDTOS.add(violationDTO);
                        });
                        auditStatusDTO.setNormAuditViolationDTOList(violationDTOS);
                    }
                }

                // 质量审核驳回原因列表
                if (Objects.nonNull(((JSONObject) item).get("audit_problem_list"))) {
                    List<AuditProblemDTO> auditProblemDTOList = new ArrayList<>();
                    JSONArray auditProblemJSONArray = (JSONArray) ((JSONObject) item).get("audit_problem_list");
                    if (CollectionUtils.isNotEmpty(auditProblemJSONArray)) {
                        auditProblemJSONArray.forEach(auditProblemItem -> {
                            if (auditProblemItem == null) {
                                return;
                            }
                            AuditProblemDTO auditProblemDTO = new AuditProblemDTO();
                            auditProblemDTO.setFirstProblemUniqCode(((JSONObject) auditProblemItem).getString(
                                    "problem_uniq_code"));
                            auditProblemDTO.setFirstProblemName(((JSONObject) auditProblemItem).getString(
                                    "problem_name"));

                            // 第二级问题列表
                            List<RejectProblemDTO> secendRejectProblemDTOList = new ArrayList<>();
                            if (Objects.nonNull(((JSONObject) auditProblemItem).get("problem_second_list"))) {
                                JSONArray secondAuditProblemJSONArray =  ((JSONObject) auditProblemItem).getJSONArray(
                                        "problem_second_list");
                                if (CollectionUtils.isNotEmpty(secondAuditProblemJSONArray)) {
                                    secondAuditProblemJSONArray.forEach(secondAuditProblemItem -> {
                                        if (secondAuditProblemItem == null) {
                                            return;
                                        }
                                        RejectProblemDTO rejectProblemDTO = new RejectProblemDTO();
                                        rejectProblemDTO.setProblemUniqCode(((JSONObject) secondAuditProblemItem)
                                                .getString("problem_uniq_code"));
                                        rejectProblemDTO.setProblemName(((JSONObject) secondAuditProblemItem)
                                                .getString("problem_name"));
                                        secendRejectProblemDTOList.add(rejectProblemDTO);
                                    });
                                    auditProblemDTO.setSecendRejectProblemDTOList(secendRejectProblemDTOList);
                                }
                            }
                            auditProblemDTOList.add(auditProblemDTO);
                        });
                    }
                    auditStatusDTO.setAuditProblemDTOList(auditProblemDTOList);
                }

                // 商品申诉详情列表
                if (Objects.nonNull(((JSONObject) item).get("appeal_info"))) {
                    List<AppealInfoDTO> appealInfoDTOList = new ArrayList<>();
                    JSONArray appealInfoList = ((JSONObject) item).getJSONArray("appeal_info");
                    if (CollectionUtils.isNotEmpty(appealInfoList)) {
                        appealInfoList.forEach(appealInfoItem -> {
                            if (appealInfoItem == null) {
                                return;
                            }
                            AppealInfoDTO appealInfoDTO = new AppealInfoDTO();
                            if (Objects.nonNull(((JSONObject) appealInfoItem).getInteger("appeal_status"))) {
                                appealInfoDTO.setAppealStatus(((JSONObject) appealInfoItem).getInteger("appeal_status"));
                            }
                            appealInfoDTO.setAppealRejectComment(((JSONObject) appealInfoItem)
                                    .getString("appeal_reject_comment"));
                            if (Objects.nonNull(((JSONObject) appealInfoItem).getInteger("appeal_type"))) {
                                appealInfoDTO.setAppealType(((JSONObject) appealInfoItem).getInteger("appeal_type"));
                            }
                            appealInfoDTO.setAppealProblemName(((JSONObject) appealInfoItem)
                                    .getString("appeal_problem_name"));
                            appealInfoDTOList.add(appealInfoDTO);
                        });
                        auditStatusDTO.setAppealInfoDTOList(appealInfoDTOList);
                    }
                }

                auditStatusDTOList.add(auditStatusDTO);
            });

            response.setAuditStatusList(auditStatusDTOList);
        }
        catch (Exception e) {
            log.error("MtBrandChannelSpuServiceImpl.getAuditStatus, 查询门店商品审核状态信息, request:{}", request, e);
        }
        return response.setStatus(ResultGenerator.genSuccessResult());
    }

    /**
     * @param request 单门店接口
     * @return
     */
    @Override
    public RecommendChannelCategoryQueryResponse queryRecommendChannelCategory(RecommendChannelCategoryQueryRequest request) {
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();
        long storeId = request.getStoreId();

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        // 请求线上渠道  根据商品UPC或商品名称查询平台推荐类目信息
        RecommendChannelCategoryParam param = new RecommendChannelCategoryParam(request.getUpc(), request.getName());
        // 补充渠道门店id
        if (storeId != 0) {
            ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(tenantId, channelId, storeId);
            if (channelStore != null) {
                String appPoiCode = channelStore.getChannelOnlinePoiCode();
                param.setApp_poi_code(appPoiCode);
            }
        }
        Map<String, Object> resultMap = mtBrandChannelGateService.sendEncodedGet(recommendChannelCategoryUrl, null, baseRequest, param);

        // 解析结果
        RecommendChannelCategoryQueryResponse response = new RecommendChannelCategoryQueryResponse();

        // 未返回结果, 直接返回
        if (MapUtils.isEmpty(resultMap)) {
            return response.setStatus(ResultGenerator.genFailResult("根据商品UPC或商品名称查询平台推荐类目信息失败"));
        }

        ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(resultMap), ChannelResponseDTO.class);

        // 平台返回错误, 直接返回
        if (ProjectConstant.NG.equals(channelResponseDTO.getData())) {
            return response.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg()));
        }

        // 平台返回成功
        RecommendChannelCategoryDTO recommendChannelCategoryDTO = null;
        RecommendChannelCategoryInfo recommendChannelCategoryInfo = JSON.parseObject(
                channelResponseDTO.getData(), RecommendChannelCategoryInfo.class);
        if (recommendChannelCategoryInfo != null) {
            recommendChannelCategoryDTO = recommendChannelCategoryInfo.convert2DTO();
        }
        return response.setStatus(ResultGenerator.genSuccessResult())
                .setRecommendChannelCategoryDTO(recommendChannelCategoryDTO);
    }

    /**
     * 传参为空时
     * {
     * "data": "ng",
     * "error": {
     * "code": 1,
     * "msg": "sourceFoodCode不合法"
     * },
     * "result_code": 3
     * }
     * 所有的参数都不存在商品时
     * {
     * "data": "ng",
     * "error": {
     * "code": 841,
     * "msg": "不存在此商品"
     * },
     * "result_code": 3
     * }
     * //成功的情况，如果部分商品不存在，会只返回存在的商品
     * {
     * "result_code": 1,
     * "data": [{
     * "sku_id": "initAuidtProduct9005",
     * "app_spu_codes": [
     * "skuSfc001",
     * "openAudit005",
     * "openAudit008",
     * "ddddd1"
     * ]
     * }]
     * }
     * 歪马业务
     *
     * @param request 单门店接口
     * @return
     */

    @Deprecated
    @Override
    public SkuId2AppFoodCodeResponse queryAppFoodCodeBySkuId(QueryAppFoodCodeBySkuIdRequest request) {
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();
        long storeId = request.getStoreId();

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        // 构造参数  这里只放sku_ids 因为app_poi_code 会在基础的系统参数中加入到map中。
        Map<String, String> bizParam = Maps.newHashMap();
        bizParam.put("sku_ids", Joiner.on(",").join(request.getSkuIds()));

        // 请求线上渠道
        Map<Long, ChannelResponseDTO> resultMap = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.GET_APP_FOOD_CODE, baseRequest, bizParam);
        ChannelResponseDTO channelResponseDTO = resultMap.get(request.getStoreId());
        if (Objects.isNull(channelResponseDTO)) {
            //未获取到返回值 return error resp
            return new SkuId2AppFoodCodeResponse().setStatus(genResult(ResultCode.FAIL));
        }

        //成功时data中为查询的数据，失败时data中为ng。这里应该至少有一条成功数据
        if (StringUtils.isNotBlank(channelResponseDTO.getData()) && !channelResponseDTO.getData().equals("ng")) {
            JSONArray objects = JSON.parseArray(channelResponseDTO.getData());
            List<SkuIdWithAppFoodCodeDTO> skuIdWithAppFoodCodeDTOS = Optional.ofNullable(objects)
                    .map(Collection::stream)
                    .orElse(Stream.empty())
                    .map(object -> (JSONObject) object)
                    .map(jsonObject -> {
                        SkuIdWithAppFoodCodeDTO dto = new SkuIdWithAppFoodCodeDTO();
                        dto.setSkuId(jsonObject.getString("sku_id"));
                        String appSpuCodesString = jsonObject.getString("app_spu_codes");
                        List<String> app_spu_codes = JSONObject.parseArray(appSpuCodesString, String.class);
                        dto.setAppfoodCodes(app_spu_codes);
                        return dto;
                    }).collect(Collectors.toList());

            return new SkuId2AppFoodCodeResponse()
                    .setStatus(new ResultStatus(ResultGenerator.genSuccessResult()))
                    .setData(skuIdWithAppFoodCodeDTOS);
        }
        else {
            //return error resp 一般为签名异常或者限流，所有商品都不存在，请求参数为空
            String errorMsg = ResultCode.FAIL.getMsg();
            if (Objects.nonNull(channelResponseDTO.getError())) {
                errorMsg = channelResponseDTO.getError().getMsg();
            }
            return new SkuId2AppFoodCodeResponse().setStatus(genResult(ResultCode.FAIL, errorMsg));
        }

    }


    @Override
    public ResultStatus updateCustomSpuStoreCategory(UpdateCustomSpuStoreCategoryRequest request) {
        List<CategoryRankDTO> categoryRanks = request.getCategoryRankList();
        if (CollectionUtils.isEmpty(categoryRanks) || categoryRanks.size() > ITEM_MAX_CATEGORY_SIZE) {
            return genResult(ResultCode.INVALID_PARAM, "分类不能为空，且最多不能超过5个");
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Arrays.asList(request.getStoreId()));

        ChannelSpuStoreCategoryUpdateDTO updateDTO = new ChannelSpuStoreCategoryUpdateDTO();
        updateDTO.setOperate_type(REPLACE);
        updateDTO.setApp_spu_codes(request.getCustomSpuId());
        updateDTO.setCategory_code_list(JacksonUtils.toJson(Fun.map(categoryRanks, CategoryRankDTO::getCategoryId)));
        // 调用渠道接口
        Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPostAppMapDto(ChannelPostMTEnum.SPU_STORE_CATEGORY_UPDATE, baseRequest, updateDTO);
        ChannelResponseDTO channelResponseDTO = postResult.get(request.getStoreId());
        if (channelResponseDTO == null) {
            return genResult(ResultCode.FAIL, "修改商品分类无返回值");
        }

        // result_code为1表示成功
        if (channelResponseDTO.getResult_code() != null && channelResponseDTO.getResult_code() == 1) {
            return ResultGenerator.genSuccessResult();
        }

        List<ChannelResponseDTO.ChannelResponseError> errorList = channelResponseDTO.getError_list();
        if (CollectionUtils.isEmpty(errorList)) {
            return genResult(ResultCode.FAIL, StringUtils.isNotBlank(channelResponseDTO.getMsg()) ? channelResponseDTO.getMsg() : "修改商品分类失败");
        }

        return genResult(ResultCode.FAIL, errorList.get(0).getMsg());
    }

    @Override
    public ResultSpuData batchUpdateSpuStoreCategoryCode(BatchUpdateCustomSpuStoreCategoryRequest request) {

        List<ProductStoreCategoryDTO> productStoreCategoryList = request.getProductStoreCategoryList();
        if (CollectionUtils.isEmpty(productStoreCategoryList)) {
            return ResultGenerator.genResultSpuData(ResultCode.INVALID_PARAM, "商品列表不能为空");
        }
        //按店内分类分组
        ArrayListMultimap<String, String> storeCategoryToCustomSpuIdMap = ArrayListMultimap.create();
        productStoreCategoryList.forEach(productStoreCategoryDTO -> {

            if (CollectionUtils.isEmpty(productStoreCategoryDTO.getCategoryRankList())) {
                return;
            }
            List<CategoryRankDTO> categoryRankList = productStoreCategoryDTO.getCategoryRankList();
            categoryRankList.sort(Comparator.comparingInt(CategoryRankDTO::getRank));
            String storeCategoryCodeString = JacksonUtils.toJson(Fun.map(categoryRankList, CategoryRankDTO::getCategoryId));
            storeCategoryToCustomSpuIdMap.put(storeCategoryCodeString, productStoreCategoryDTO.getCustomSpuId());
        });


        List<ResultSuccessSpu> resultSuccessSpus = new ArrayList<>();
        List<ResultErrorSpu> resultErrorSpus = new ArrayList<>();

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Collections.singletonList(request.getStoreId()));
        //按店内分类分组调用接口
        storeCategoryToCustomSpuIdMap.keySet().forEach(storeCategoryCodeString -> {
            List<String> customSpuIds = storeCategoryToCustomSpuIdMap.get(storeCategoryCodeString);
            ResultSpuData subData = null;
            try {
                ChannelSpuStoreCategoryUpdateDTO updateDTO = new ChannelSpuStoreCategoryUpdateDTO();
                updateDTO.setOperate_type(REPLACE);
                updateDTO.setApp_spu_codes(Joiner.on(",").join(customSpuIds));
                updateDTO.setCategory_code_list(storeCategoryCodeString);
                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPostAppMapDto(ChannelPostMTEnum.SPU_STORE_CATEGORY_UPDATE, baseRequest, updateDTO);
                subData = parseResult(postResult, request.getStoreId(), customSpuIds);
            }
            catch (Exception e) {
                log.error("更新商品分类失败:{}", customSpuIds, e);
                subData = parseResult(new HashMap<>(), request.getStoreId(), customSpuIds);
            }
            resultSuccessSpus.addAll(Objects.nonNull(subData.getSucData()) ? subData.getSucData() : new ArrayList<>());
            resultErrorSpus.addAll(Objects.nonNull(subData.getErrorData()) ? subData.getErrorData() : new ArrayList<>());
        });


        ResultSpuData resultSpuData = new ResultSpuData();
        resultSpuData.setSucData(resultSuccessSpus);
        resultSpuData.setErrorData(resultErrorSpus);
        if (CollectionUtils.isEmpty(resultErrorSpus)) {
            resultSpuData.setStatus(genResult(ResultCode.SUCCESS));
        }
        else {
            resultSpuData.setStatus(genResult(ResultCode.FAIL));
        }
        return resultSpuData;
    }


    private ResultSpuData parseResult(Map<Long, ChannelResponseDTO> postResult, Long storeId, List<String> customSpuIds) {
        ResultSpuData resultSpuData = new ResultSpuData();
        ChannelResponseDTO channelResponseDTO = postResult.get(storeId);
        List<String> allCustomSpuIds = new ArrayList<>(customSpuIds);
        //接口无响应
        if (channelResponseDTO == null) {
            List<ResultErrorSpu> resultErrorSpus = Fun.map(allCustomSpuIds, customSpuId -> {
                ResultErrorSpu resultErrorSpu = new ResultErrorSpu();
                resultErrorSpu.setErrorMsg("修改商品分类无返回值");
                resultErrorSpu.setErrorCode(ResultCode.FAIL.getCode());
                resultErrorSpu.setChannelId(ChannelTypeEnum.MEITUAN.getCode());
                SpuKey spuKey = new SpuKey();
                spuKey.setCustomSpuId(customSpuId);
                resultErrorSpu.setSpuInfo(spuKey);
                resultErrorSpu.setStoreId(storeId);
                return resultErrorSpu;
            });
            resultSpuData.setErrorData(resultErrorSpus);
            return resultSpuData;
        }
        // result_code为1表示成功
        if (channelResponseDTO.getResult_code() != null && channelResponseDTO.getResult_code() == 1) {
            List<ResultSuccessSpu> resultSuccessSpus = Fun.map(allCustomSpuIds, customSpuId -> {
                ResultSuccessSpu resultSuccessSpu = new ResultSuccessSpu();
                SpuKey spuKey = new SpuKey();
                spuKey.setCustomSpuId(customSpuId);
                resultSuccessSpu.setSpuInfo(spuKey);
                resultSuccessSpu.setStoreId(storeId);
                resultSuccessSpu.setChannelId(ChannelTypeEnum.MEITUAN.getCode());
                return resultSuccessSpu;
            });
            resultSpuData.setSucData(resultSuccessSpus);
            return resultSpuData;
        }

        //接口返回错误
        List<ChannelResponseDTO.ChannelResponseError> errorList = channelResponseDTO.getError_list();
        if (CollectionUtils.isNotEmpty(errorList)) {
            List<String> errorSpuIds = new ArrayList<>();
            List<ResultErrorSpu> resultErrorSpus = new ArrayList<>();
            //失败商品
            errorList.forEach(error -> {
                ResultErrorSpu resultErrorSpu = new ResultErrorSpu();
                List<ChannelStoreCategoryResponseErrorDTO> channelStoreCategoryResponseErrs = JSON.parseArray(error.getMsg(), ChannelStoreCategoryResponseErrorDTO.class);
                if (CollectionUtils.isEmpty(channelStoreCategoryResponseErrs)) {
                    return;
                }
                resultErrorSpu.setErrorCode(error.getCode());
                resultErrorSpu.setErrorMsg(channelStoreCategoryResponseErrs.get(0).getErrorMsg());
                SpuKey spuKey = new SpuKey();
                spuKey.setCustomSpuId(channelStoreCategoryResponseErrs.get(0).getAppSpuCode());
                resultErrorSpu.setSpuInfo(spuKey);
                resultErrorSpu.setChannelId(ChannelTypeEnum.MEITUAN.getCode());
                resultErrorSpu.setStoreId(storeId);
                resultErrorSpus.add(resultErrorSpu);
                errorSpuIds.add(channelStoreCategoryResponseErrs.get(0).getAppSpuCode());
            });

            //成功商品
            allCustomSpuIds.removeAll(errorSpuIds);
            List<ResultSuccessSpu> resultSuccessSpus = Fun.map(allCustomSpuIds, customSpuId -> {
                ResultSuccessSpu resultSuccessSpu = new ResultSuccessSpu();
                SpuKey spuKey = new SpuKey();
                spuKey.setCustomSpuId(customSpuId);
                resultSuccessSpu.setSpuInfo(spuKey);
                resultSuccessSpu.setStoreId(storeId);
                resultSuccessSpu.setChannelId(ChannelTypeEnum.MEITUAN.getCode());
                return resultSuccessSpu;
            });
            resultSpuData.setSucData(resultSuccessSpus);
            resultSpuData.setErrorData(resultErrorSpus);
            return resultSpuData;
        }

        return resultSpuData;
    }


    @Override
    public SensitiveWordCheckResponse checkSensitiveWords(SensitiveWordCheckRequest request) {

        SensitiveWordCheckResponse response = new SensitiveWordCheckResponse()
                .setStatus(ResultGenerator.genSuccessResult());
        List<SensitiveWordDTO> sensitiveWordList = request.getSensitiveWordList();
        if (CollectionUtils.isEmpty(sensitiveWordList)) {
            return response;
        }

        List<Map<String, Object>> contentList = Lists.newArrayList();
        for (SensitiveWordDTO word : sensitiveWordList) {
            Map<String, Object> content = Maps.newHashMap();
            content.put("type", word.getType());
            content.put("content", word.getContent());
            contentList.add(content);
        }

        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put("contents", JacksonUtils.toJson(contentList));

        if (request.getLastCategoryId() >= 0) {
            bizParam.put("tag_id", request.getLastCategoryId());
        }

        // 请求线上渠道
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setChannelId(request.getBaseInfo().getChannelId());
        baseRequest.setTenantId(request.getBaseInfo().getTenantId());
        baseRequest.setStoreIdList(Arrays.asList(request.getStoreId()));

        Map<Long, ChannelResponseDTO> resultMap = mtBrandChannelGateService.sendPostAppMapDto(ChannelPostMTEnum.CHECK_SENSITIVE_WORDS,
                baseRequest, bizParam);

        ChannelResponseDTO channelResponseDTO = resultMap.get(request.getStoreId());
        if (channelResponseDTO == null) {
            return response.setStatus(genResult(ResultCode.FAIL, "批量校验敏感词失败"));
        }

        List<ChannelResponseDTO.ChannelResponseError> errorList = channelResponseDTO.getError_list();
        if (CollectionUtils.isEmpty(errorList)) {
            return response;
        }

        List<SensitiveWordCheckResultDTO> checkResultDtoList = Lists.newArrayList();
        for (ChannelResponseDTO.ChannelResponseError error : errorList) {
            if (!Objects.equals(1, error.getCode())) {
                continue;
            }
            SensitiveWordCheckMsg msg = JacksonUtils.parse(error.getMsg(), SensitiveWordCheckMsg.class);
            if (msg == null) {
                continue;
            }

            SensitiveWordCheckResultDTO dto = new SensitiveWordCheckResultDTO();
            dto.setContent(msg.getContent());
            dto.setSensitiveWordSet(msg.getSensitiveWordSet());
            checkResultDtoList.add(dto);
        }

        response.setCheckResultList(checkResultDtoList);
        return response;
    }

    @Override
    public QueryNormAuditDelSpuResponse queryNormAuditDelSpuInfo(QueryNormAuditDelSpuRequest request) {
        QueryNormAuditDelSpuResponse response = new QueryNormAuditDelSpuResponse().setStatus(ResultGenerator.genSuccessResult());
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();
            long storeId = request.getStoreId();

            // 获取渠道门店编码
            String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
            Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
            if (MapUtils.isEmpty(channelStoreDOMap)) {
                return response.setStatus(genResult(ResultCode.FAIL)).setAuditStatusList(Collections.emptyList());
            }

            // 构造参数
            Map<String, String> bizParam = Maps.newHashMap();
            String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
            bizParam.put("app_poi_code", appPoiCode);
            if (StringUtils.isNotBlank(request.getCustomSpuId())) {
                bizParam.put("app_spu_code", request.getCustomSpuId());
            }
            bizParam.put("page_num", String.valueOf(request.getPageNum()));
            bizParam.put("page_size", String.valueOf(request.getPageSize()));

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            //限频处理
            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
            boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.GET_NORM_AUDIT_DEL_DETAIL, appId);
            if (!tryAcquire) {
                // 获取当前执行权失败，返回可重试
                return response.setStatus(genResult(ResultCode.FAIL_ALLOW_RETRY)).setAuditStatusList(Collections.emptyList());
            }

            // 请求线上渠道
            Map<String, Object> spuNormAuditDelMap;
            if (MccConfigUtil.isSpuQueryUseUrlEncode(tenantId)) {
                spuNormAuditDelMap = mtBrandChannelGateService.sendEncodedGet(spuNormAuditDeleteUrl, null, baseRequest, bizParam);
            }
            else {
                spuNormAuditDelMap = mtBrandChannelGateService.sendGet(spuNormAuditDeleteUrl, null, baseRequest, bizParam);
            }
            if (MapUtils.isEmpty(spuNormAuditDelMap) || Objects.isNull(spuNormAuditDelMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(spuNormAuditDelMap.get(ProjectConstant.DATA))) {
                return response.setStatus(genResult(ResultCode.FAIL, "获取美团合规审核删除商品信息失败"));
            }

            List<AuditStatusDTO> auditStatusDTOList = Lists.newArrayList();
            JSONArray auditStatusJSONArray = (JSONArray) spuNormAuditDelMap.get(ProjectConstant.DATA);
            // 组装数据
            auditStatusJSONArray.forEach(item -> {
                AuditStatusDTO auditStatusDTO = new AuditStatusDTO();
                auditStatusDTO.setCustomSpuId(((JSONObject) item).getString("app_spu_code"));
                auditStatusDTO.setName(((JSONObject) item).getString("name"));
                auditStatusDTO.setIsSoldOut(((JSONObject) item).getInteger("is_sold_out"));
                auditStatusDTO.setCtime(((JSONObject) item).getLong("ctime"));
                auditStatusDTO.setUtime(((JSONObject) item).getLong("utime"));
                auditStatusDTO.setNormAuditStatus(((JSONObject) item).getInteger("norm_audit_status"));
                auditStatusDTO.setNormAuditType(((JSONObject) item).getInteger("norm_audit_type"));
                if (Objects.nonNull(((JSONObject) item).get("norm_comment"))) {
                    NormAuditViolationDTO violationDTO = new NormAuditViolationDTO();
                    violationDTO.setNormAuditComment(((JSONObject) item).getString("norm_comment"));

                    // 新增合规审核驳回原因列表
                    if (Objects.nonNull(((JSONObject) item).get("norm_violation_problem_list"))) {
                        JSONArray normViolationProblemJSONArray =  ((JSONObject) item).getJSONArray(
                                "norm_violation_problem_list");
                        List<RejectProblemDTO> rejectProblemDTOList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(normViolationProblemJSONArray)) {
                            normViolationProblemJSONArray.forEach(normViolationProblemItem -> {
                                if (normViolationProblemItem == null) {
                                    return;
                                }
                                RejectProblemDTO rejectProblemDTO = new RejectProblemDTO();
                                rejectProblemDTO.setProblemUniqCode(((JSONObject) normViolationProblemItem)
                                        .getString("problem_uniq_code"));
                                rejectProblemDTO.setProblemName(((JSONObject) normViolationProblemItem)
                                        .getString("problem_name"));
                                rejectProblemDTOList.add(rejectProblemDTO);
                            });
                            violationDTO.setNormViolationProblemDTOList(rejectProblemDTOList);
                        }
                    }
                    auditStatusDTO.setNormAuditViolationDTOList(Lists.newArrayList(violationDTO));
                }

                // 新增申诉详情
                if (Objects.nonNull(((JSONObject) item).get("appeal_info"))) {
                    List<AppealInfoDTO> appealInfoDTOList = new ArrayList<>();
                    JSONArray appealInfoList = ((JSONObject) item).getJSONArray("appeal_info");
                    if (CollectionUtils.isNotEmpty(appealInfoList)) {
                        appealInfoList.forEach(appealInfoItem -> {
                            if (appealInfoItem == null) {
                                return;
                            }
                            AppealInfoDTO appealInfoDTO = new AppealInfoDTO();
                            if (Objects.nonNull(((JSONObject) appealInfoItem).getInteger("appeal_status"))) {
                                appealInfoDTO.setAppealStatus(((JSONObject) appealInfoItem).getInteger("appeal_status"));
                            }
                            if (Objects.nonNull(((JSONObject) appealInfoItem).getInteger("appeal_type"))) {
                                appealInfoDTO.setAppealType(((JSONObject) appealInfoItem).getInteger("appeal_type"));
                            }
                            appealInfoDTO.setAppealRejectComment(((JSONObject) appealInfoItem).getString("appeal_reject_comment"));
                            appealInfoDTO.setAppealProblemName(((JSONObject) appealInfoItem).getString("appeal_problem_name"));
                            appealInfoDTOList.add(appealInfoDTO);
                        });
                        auditStatusDTO.setAppealInfoDTOList(appealInfoDTOList);
                    }
                }

                auditStatusDTOList.add(auditStatusDTO);
            });

            response.setAuditStatusList(auditStatusDTOList);
        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.queryNormAuditDelSpuInfo, 查询商品合规审核删除详情异常, request:{}", request, e);
        }
        return response.setStatus(ResultGenerator.genSuccessResult());
    }

    @Override
    public QueryChannelSpuIdResponse queryChannelSpuId(QueryChannelSpuIdRequest request) {
        QueryChannelSpuIdResponse response = new QueryChannelSpuIdResponse();
        response.setStatus(ResultGenerator.genSuccessResult());

        Long tenantId = request.getBaseInfo().getTenantId();
        Long storeId = request.getStoreId();
        Integer channelId = request.getBaseInfo().getChannelId();

        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId,
                Collections.singletonList(storeId));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return response.setStatus(genResult(ResultCode.FAIL)).setChannelSpuIdList(Collections.emptyList());
        }

        // 构造参数
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        bizParam.put("app_poi_code", appPoiCode);
        bizParam.put("app_spu_codes", Joiner.on(",").join(request.getCustomSpuIdList()));

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        //限频处理
        Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
        boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.GET_CHANNEL_SPU_ID, appId);
        if (!tryAcquire) {
            // 获取当前执行权失败，返回可重试
            return response.setStatus(genResult(ResultCode.FAIL_ALLOW_RETRY)).setChannelSpuIdList(Collections
                    .emptyList());
        }

        // 请求线上渠道
        Map channelSpuIdResultMap = mtBrandChannelGateService.sendPost(batchGetSpuIdUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(channelSpuIdResultMap)) {
            return response.setStatus(genResult(ResultCode.FAIL, "获取美团渠道商品ID失败"));
        }

        List<ChannelSpuIdDTO> channelSpuIdList = Lists.newArrayList();
        JSONArray channelSpuIdArray = (JSONArray) channelSpuIdResultMap.get(ProjectConstant.SUCCESS_LIST);
        if (channelSpuIdArray == null || channelSpuIdArray.size() == 0) {
            return response.setStatus(genResult(ResultCode.FAIL, "获取美团渠道商品ID失败"));
        }
        // 组装数据
        channelSpuIdArray.forEach(item -> {
            ChannelSpuIdDTO channelSpuIdDTO = new ChannelSpuIdDTO();
            channelSpuIdDTO.setCustomSpuId(((JSONObject) item).getString("app_spu_code"));
            channelSpuIdDTO.setChannelSpuId(((JSONObject) item).getString("real_spu_id"));
            channelSpuIdList.add(channelSpuIdDTO);
        });

        response.setChannelSpuIdList(channelSpuIdList);
        return response;
    }

    @Override
    public QueryQualityProblemResponse queryQualityProblem(QueryQualityProblemRequest request) {
        QueryQualityProblemResponse response = new QueryQualityProblemResponse().setStatus(ResultGenerator.genSuccessResult());
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();
            long storeId = request.getStoreId();
            String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
            Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
            if (MapUtils.isEmpty(channelStoreDOMap)) {
                return response.setStatus(genResult(ResultCode.CHANNEL_STORE_CONFIG_ERROR)).setQualityProblemList(Collections.emptyList());
            }
            Map<String, String> bizParam = convertToBizParam(request, channelStoreKey, channelStoreDOMap);

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));
            //限频处理
            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
            boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.GET_QUALITY_INFO, appId);
            if (!tryAcquire) {
                // 获取当前执行权失败，返回可重试
                return response.setStatus(genResult(ResultCode.FAIL_ALLOW_RETRY)).setQualityProblemList(Collections.emptyList());
            }
            // 请求线上渠道
            Map<String, Object> spuQualityProblemMap;
            if (MccConfigUtil.isSpuQueryUseUrlEncode(tenantId)) {
                spuQualityProblemMap = mtBrandChannelGateService.sendEncodedGet(spuQualityProblems, null, baseRequest, bizParam);
            }
            else {
                spuQualityProblemMap = mtBrandChannelGateService.sendGet(spuQualityProblems, null, baseRequest, bizParam);
            }
            if (MapUtils.isEmpty(spuQualityProblemMap) || Objects.isNull(spuQualityProblemMap.get(ProjectConstant.SUCCESS_LIST)) || ProjectConstant.NG.equals(spuQualityProblemMap.get(ProjectConstant.SUCCESS_LIST))) {
                //取出具体的报错信息添加到msg里
                String msg = null;
                if (MapUtils.isNotEmpty(spuQualityProblemMap) && Objects.nonNull(spuQualityProblemMap.get(ProjectConstant.ERROR_LIST))) {
                    JSONArray msgs = ((JSONArray) spuQualityProblemMap.get(ProjectConstant.ERROR_LIST));
                    if (CollectionUtils.isNotEmpty(msgs)){
                        List<String> msgStrList = msgs.stream().map( errMsg -> ((JSONObject) errMsg).getString(ProjectConstant.MSG))
                                .filter(Objects::nonNull).collect(Collectors.toList());
                        msg = Strings.join(msgStrList,";");

                    }
                }
                msg = StringUtils.isBlank(msg) ? "" :  msg;
                return response.setStatus(genResult(ResultCode.FAIL, "获取美团商品管家质量信息失败:" + msg));
            }
            List<QualityProblemDTO> problemList = convertResult2QualityProblemList(spuQualityProblemMap);
            return response.setQualityProblemList(problemList);

        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.queryQualityProblem, 查询美团商品管家质量信息失败, request:{}", request, e);
        }
        response.setStatus(genResult(ResultCode.FAIL, "获取美团商品管家质量信息失败"));
        response.setQualityProblemList(Collections.emptyList());
        return response;
    }

    private List<QualityProblemDTO> convertResult2QualityProblemList(Map<String, Object> spuQualityProblemMap) {
        List<QualityProblemDTO> qualityProblemList = Lists.newArrayList();
        JSONArray valueJa = (JSONArray) spuQualityProblemMap.get(ProjectConstant.SUCCESS_LIST);
        // 组装数据
        for (Object item : valueJa) {
            QualityProblemDTO qualityProblemDTO = new QualityProblemDTO();
            qualityProblemDTO.setCustomSpuId(((JSONObject) item).getString("app_spu_code"));
            List<QualityInfo> qualityInfoList = new ArrayList<>();
            JSONArray qualityInfos = ((JSONObject) item).getJSONArray("quality_info");
            if (CollectionUtils.isNotEmpty(qualityInfos)) {
                qualityInfos.forEach(qualityInfoItem -> {
                    QualityInfo qualityInfo = new QualityInfo();
                    qualityInfo.setEffectCode(((JSONObject) qualityInfoItem).getLong("effect_code"));
                    qualityInfo.setEffectName(((JSONObject) qualityInfoItem).getString("effect_name"));
                    JSONArray problemItems = ((JSONObject) qualityInfoItem).getJSONArray("problem_item");
                    List<ProblemItem> problemItemList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(problemItems)) {
                        problemItems.forEach(problemItem -> {
                            ProblemItem problem = new ProblemItem();
                            problem.setProblemCode(((JSONObject) problemItem).getLong("problem_code"));
                            problem.setProblemName(((JSONObject) problemItem).getString("problem_name"));
                            problem.setProblemMsg(((JSONObject) problemItem).getString("problem_msg"));
                            problemItemList.add(problem);
                        });
                    }
                    qualityInfo.setProblemItem(problemItemList);
                    qualityInfoList.add(qualityInfo);
                });
                qualityProblemDTO.setQualityInfo(qualityInfoList);
            }

            // 新增可申诉的二级问题项列表
            if (Objects.nonNull(((JSONObject) item).get("quality_problem_list"))) {
                List<RejectProblemDTO> rejectProblemDTOList = new ArrayList<>();
                JSONArray qualityAppealProblemList = ((JSONObject) item).getJSONArray("quality_problem_list");
                if (CollectionUtils.isNotEmpty(qualityAppealProblemList)) {
                    qualityAppealProblemList.forEach(qualityProblemItem -> {
                        if (qualityProblemItem == null) {
                            return;
                        }
                        RejectProblemDTO rejectProblemDTO = new RejectProblemDTO();
                        rejectProblemDTO.setProblemUniqCode(((JSONObject) qualityProblemItem).getString("problem_uniq_code"));
                        rejectProblemDTO.setProblemName(((JSONObject) qualityProblemItem).getString("problem_name"));
                        rejectProblemDTOList.add(rejectProblemDTO);
                    });
                    qualityProblemDTO.setQualityProblemDTOList(rejectProblemDTOList);
                }
            }

            // 新增商品申诉详情
            if (Objects.nonNull(((JSONObject) item).get("appeal_info"))) {
                List<AppealInfoDTO> appealInfoDTOList = new ArrayList<>();
                JSONArray appealInfoList = ((JSONObject) item).getJSONArray("appeal_info");
                if (CollectionUtils.isNotEmpty(appealInfoList)) {
                    appealInfoList.forEach(appealInfoItem -> {
                        if (appealInfoItem == null) {
                            return;
                        }
                        AppealInfoDTO appealInfoDTO = new AppealInfoDTO();
                        if (Objects.nonNull(((JSONObject) appealInfoItem).getInteger("appeal_status"))) {
                            appealInfoDTO.setAppealStatus(((JSONObject) appealInfoItem).getInteger("appeal_status"));
                        }
                        if (Objects.nonNull(((JSONObject) appealInfoItem).getInteger("appeal_type"))) {
                            appealInfoDTO.setAppealType(((JSONObject) appealInfoItem).getInteger("appeal_type"));
                        }
                        appealInfoDTO.setAppealRejectComment(((JSONObject) appealInfoItem).getString("appeal_reject_comment"));
                        appealInfoDTO.setAppealProblemName(((JSONObject) appealInfoItem).getString("appeal_problem_name"));
                        appealInfoDTOList.add(appealInfoDTO);
                    });
                    qualityProblemDTO.setAppealInfoDTOList(appealInfoDTOList);
                }
            }

            qualityProblemList.add(qualityProblemDTO);
        }
        return qualityProblemList;
    }

    private static Map<String, String> convertToBizParam(QueryQualityProblemRequest request, String channelStoreKey, Map<String, ChannelStoreDO> channelStoreDOMap) {
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
        bizParam.put("app_poi_code", appPoiCode);
        if (CollectionUtils.isNotEmpty(request.getCustomSpuIds())) {
            bizParam.put("app_spu_codes", Strings.join(request.getCustomSpuIds(), ","));
        }

        bizParam.put("diagnosis_type", String.valueOf(request.getDiagnosisType()));
        return bizParam;
    }


    @Override
    public ProductSequenceBatchSetResponse batchSetSequence(ProductSequenceBatchSetRequest request) {
        return null;
    }

    @Override
    public ResultSpuData updateStoreCategory(SpuStoreCategoryInfoRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }

        //按照相同的店内分类分组
        Map<String, Set<String>> sortStoreCategoryCode2CustomSpuIdSet = request.getParamList()
                .stream()
                .collect(Collectors.groupingBy(
                        //key
                        spuStoreCategoryCodeDTO -> Optional.ofNullable(spuStoreCategoryCodeDTO.getStoreCategoryCodeList())
                                .map(storeCategoryCodeList -> {
                                    List<String> sortedCopy = Ordering.natural().sortedCopy(storeCategoryCodeList);
                                    return JacksonUtils.toJson(sortedCopy);
                                })
                                .orElse(JacksonUtils.toJson(ImmutableList.of())),
                        //value
                        Collectors.mapping(SpuStoreCategoryCodeDTO::getCustomSpuId, Collectors.toSet())));

        sortStoreCategoryCode2CustomSpuIdSet.forEach((sortStoreCategoryCode, customSpuIdSet) -> {
            for (List<String> customSpuIdSubList : Iterables.partition(customSpuIdSet, UPC_CREATE_MAX_COUNT)) {
                List<SpuKey> bizKeyList = Fun.map(customSpuIdSubList, customSpuId -> {
                    SpuKey spuInfo = new SpuKey();
                    spuInfo.setCustomSpuId(customSpuId);
                    return spuInfo;
                });
                try {
                    // 业务参数转换
                    String customSpuIdContent = String.join(",", customSpuIdSubList);
                    ChannelSpuStoreCategoryUpdateDTO dto = new ChannelSpuStoreCategoryUpdateDTO();
                    dto.setOperate_type(REPLACE);
                    dto.setApp_spu_codes(customSpuIdContent);
                    dto.setCategory_code_list(sortStoreCategoryCode);
                    // 调用渠道接口
                    Map<Long, ChannelResponseDTO<String>> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.SPU_STORE_CATEGORY_UPDATE, request.getBaseInfo(), dto);
                    List<ResultSuccessSpu> successSpuList = Lists.newArrayList();
                    List<ResultErrorSpu> errorSpuList = Lists.newArrayList();

                    postResult.forEach((storeId, responseResult) -> convertResult(storeId, responseResult, bizKeyList, successSpuList, errorSpuList));

                    // 组装返回结果
                    resultData.getSucData().addAll(successSpuList);
                    resultData.getErrorData().addAll(errorSpuList);

                } catch (IllegalArgumentException e) {
                    log.error("MtChannelSpuServiceImpl.updateStoreCategory 参数校验失败, category_code_list:{}, spuIdSet:{}", sortStoreCategoryCode, customSpuIdSubList, e);
                    ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);
                } catch (Exception e) {
                    log.error("MtChannelSpuServiceImpl.updateStoreCategory 服务异常, category_code_list:{}, spuIdSet:{}", sortStoreCategoryCode, customSpuIdSubList, e);
                    ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), bizKeyList);
                }
            }
        });
        return resultData;
    }

    private void convertResult(Long storeId, ChannelResponseDTO<String> responseDTO, List<SpuKey> spuKeyList, List<ResultSuccessSpu> successSpuList, List<ResultErrorSpu> errorSpuList) {
        if (Objects.equals(responseDTO.getResult_code(), 1)) {
            //成功
            spuKeyList.forEach(spuKey -> {
                ResultSuccessSpu resultSuccessSpu = new ResultSuccessSpu();
                resultSuccessSpu.setStoreId(storeId);
                SpuKey spuInfo = new SpuKey();
                spuInfo.setCustomSpuId(spuKey.getCustomSpuId());
                resultSuccessSpu.setSpuInfo(spuInfo);
                successSpuList.add(resultSuccessSpu);
            });
        } else {
            //失败
            for (ChannelResponseDTO.ChannelResponseError error : responseDTO.getError_list()) {
                if (StringUtils.isBlank(error.getMsg())) {
                    continue;
                }
                List<Msg> msgList = JSONObject.parseObject(error.getMsg(), new TypeReference<List<Msg>>() {
                });

                for (Msg msg : Optional.ofNullable(msgList).orElse(ImmutableList.of())) {
                    ResultErrorSpu resultErrorSpu = new ResultErrorSpu();
                    resultErrorSpu.setStoreId(storeId);
                    SpuKey spuInfo = new SpuKey();
                    spuInfo.setCustomSpuId(msg.getAppSpuCode());
                    resultErrorSpu.setSpuInfo(spuInfo);
                    resultErrorSpu.setErrorCode(ResultCodeEnum.FAIL.getValue());
                    resultErrorSpu.setErrorMsg(msg.getErrorMsg());
                    errorSpuList.add(resultErrorSpu);
                }
            }
        }
    }

    @Data
    private static class Msg {
        private String appSpuCode;
        private String errorMsg;
    }

    /**
     * 本方法针对平台动态信息缺陷，进行问题商品全量快速修复，为缩小影响范围，只推送动态信息，其他功能慎用，后果自负
     * @param request
     * @return
     */
    public ResultSpuData batchPushSpuDynamicInfo(DynamicInfoRepairRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }

        List<SpuKey> bizKeyList = request.getParamList().stream()
                .map(param -> new SpuKey().setCustomSpuId(param.getCustomSpuId()))
                .collect(Collectors.toList());
        try {

            // 业务参数转换
            List<ChannelSpuInfoDTO> channelSpuInfoDTOList = mtConverterService.dynamicRepairSpuInfoDTOMappings(request.getParamList());

            ChannelSpuUpdateDTO channelSpuUpdateDTO = new ChannelSpuUpdateDTO();
            channelSpuUpdateDTO.setFood_data(JSON.toJSONString(channelSpuInfoDTOList));

            // 调用渠道接口
            Map<Long, ChannelResponseDTO> postResult =
                    mtBrandChannelGateService.sendPost(ChannelPostMTEnum.SPU_CREATE, request.getBaseInfo(), channelSpuUpdateDTO);

            // 组装返回结果
            boolean isErpTenant = Objects.nonNull(request.getBaseInfo()) ? request.getBaseInfo().isErpTenant() : false;
            ResultSpuDataUtils.combinePartResultData(resultData, postResult, bizKeyList, ChannelTypeEnum.MEITUAN, isErpTenant);
        }
        catch (IllegalArgumentException e) {
            log.error("MtChannelSpuServiceImpl.batchPushSpuDynamicInfo 参数校验失败, data:{}", request, e);
            ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);
        }
        catch (InvokeChannelTooMuchException e) {
            log.warn("MtChannelSpuServiceImpl.batchPushSpuDynamicInfo 触发限流, data:{}", request);
            ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.TRIGGER_LIMIT.getMsg(), bizKeyList);
        }
        catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.batchPushSpuDynamicInfo 服务异常, data:{}", request, e);
            ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), bizKeyList);
        }
        return resultData;
    }

    @Override
    public ResultStatus updateOptionFieldBySpu(UpdateSpuOptionFieldRequest request) {
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setTenantId(request.getBaseInfo().getTenantId());
        baseRequest.setChannelId(request.getBaseInfo().getChannelId());
        baseRequest.setStoreIdList(Lists.newArrayList(request.getStoreId()));

        Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.SPU_UPDATE,
                baseRequest, MTSpuUpdateDTO.of(request));
        if (postResult == null || postResult.get(request.getStoreId()) == null) {
            return ResultGenerator.genResult(ResultCode.RESULT_PARSE, "返回值为空");
        }
        ChannelResponseDTO channelResponseDTO = postResult.get(request.getStoreId());
        if(CollectionUtils.isNotEmpty(channelResponseDTO.getError_list())){
            ChannelResponseDTO.ChannelResponseError error = (ChannelResponseDTO.ChannelResponseError) channelResponseDTO.getError_list().get(0);
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(error.getCode());
            resultStatus.setMsg(error.getMsg());
            return resultStatus;
        }
        if(channelResponseDTO.getError() != null){
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(channelResponseDTO.getError().getCode());
            resultStatus.setMsg(channelResponseDTO.getError().getMsg());
            return resultStatus;
        }

        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus submitAppealInfo(SubmitAppealInfoRequest request) {
        try {
            long tenantId = request.getBaseInfo().getTenantId();
            int channelId = request.getBaseInfo().getChannelId();
            long storeId = request.getStoreId();

            // 构造参数
            AppealInfoSubmitDTO appealInfoSubmitDTO = new AppealInfoSubmitDTO();
            if (StringUtils.isNotBlank(request.getCustomSpuId())) {
                appealInfoSubmitDTO.setApp_spu_code(request.getCustomSpuId());
            }
            if (CollectionUtils.isNotEmpty(request.getAppealProblemCodeList())) {
                String appealProblemCodes = String.join(",", request.getAppealProblemCodeList());
                appealInfoSubmitDTO.setAppeal_problem_uniq_codes(appealProblemCodes);
            }
            if (StringUtils.isNotBlank(request.getAppealExplainDesc())) {
                appealInfoSubmitDTO.setSupplement_explain(request.getAppealExplainDesc());
            }
            if (CollectionUtils.isNotEmpty(request.getAppealPictureUrls())) {
                String appealPics = String.join(",", request.getAppealPictureUrls());
                appealInfoSubmitDTO.setSupplement_picture(appealPics);
            }

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            //限频处理
            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
            boolean tryAcquire = clusterRateLimiter.tryAcquire(ChannelPostMTEnum.SUBMIT_APPEAL_STATUS, appId);
            if (!tryAcquire) {
                // 获取当前执行权失败，返回可重试
                return ResultGenerator.genResult(ResultCode.FAIL_ALLOW_RETRY);
            }

            // 请求线上渠道
            Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPostAppMapDto(
                    ChannelPostMTEnum.SUBMIT_APPEAL_STATUS, baseRequest, appealInfoSubmitDTO);

            ChannelResponseDTO channelResponseDTO = postResult.get(request.getStoreId());
            if (channelResponseDTO == null) {
                return genResult(ResultCode.FAIL, "提交门店商品申诉状态无返回值");
            }

            // result_code为1表示成功
            if (channelResponseDTO.getResult_code() != null && channelResponseDTO.getResult_code() == 1) {
                return ResultGenerator.genSuccessResult();
            }

            List<ChannelResponseDTO.ChannelResponseError> errorList = channelResponseDTO.getError_list();

            if (CollectionUtils.isEmpty(errorList)) {
                return genResult(ResultCode.FAIL, StringUtils.isNotBlank(channelResponseDTO.getMsg()) ?
                        channelResponseDTO.getMsg() : "提交门店商品申诉状态信息失败");
            }

            // 取出具体的报错信息添加到msg里
            List<String> msgStrList = errorList.stream().filter(Objects::nonNull)
                                               .map(ChannelResponseDTO.ChannelResponseError::getMsg)
                                               .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            String msg = Strings.join(msgStrList,";");
            return ResultGenerator.genResult(ResultCode.FAIL, "提交门店商品申诉状态信息失败:" + msg);
        } catch (Exception e) {
            log.error("MtChannelSpuServiceImpl.submitAppealInfo, 提交门店商品申诉状态信息异常, request:{} errMsg:{}",
                    request, e.getMessage());
            return ResultGenerator.genResult(ResultCode.FAIL, e.getMessage());
        }
    }
}
