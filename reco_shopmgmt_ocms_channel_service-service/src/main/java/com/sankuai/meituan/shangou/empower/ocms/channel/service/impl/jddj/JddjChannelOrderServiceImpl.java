package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemAfterSaleCreateModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemCreateForMiddleModel;
import com.meituan.shangou.saas.o2o.dto.request.BizAfterSaleCreateOrUpdateRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizAfterSaleCreateOrUpdateResponse;
import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.AfsAuditStageEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.AfsReviewTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JDApplyDealTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JDDJGoodsPromotionShareTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JDDJGoodsPromotionTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MockChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ChannelStatusConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjOrderConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.orderTrack.OrderTrackService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelOrderThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.ServiceTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderPartRefundRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.AfsApplyType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelOrderStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderPartRefundType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderRefundSponsor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils.getIntegerValue;


/**
 * @description:
 * @author: zhaolei12
 * @create: 2019/1/28 下午5:46
 */
@Service("jddjChannelOrderService")
public class JddjChannelOrderServiceImpl implements ChannelOrderService, JddjAfterSaleService, JddjMockTestService {

    @Resource
    private JddjConverterService jddjConverterService;

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private ChannelOrderThriftServiceProxy channelOrderThriftServiceProxy;

    @Resource
    private CommonLogger log;
    @Resource
    private TenantService tenantService;

    private static final String IS_OPEN_OR_CLOSE_SALES_PROMOTION = "is_open_or_close_sales_promotion";

    private static final String SYSTEM_OPERATOR = "系统";

    private static final String RIDER_INFO_CHANGED_DELIVERY_STATUS = "10022";

    @Resource
    private OrderTrackService orderTrackService;

    private final String JD_QUESTION_PIC  = "http://img10.360buyimg.com/o2o/";

    @Override
    public ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);
            PoiConfirmOrderDTO poiConfirmOrderDTO = jddjConverterService.poiConfirmOrderMapping(request);
            if (StringUtils.isBlank(poiConfirmOrderDTO.getOperator())) {
                poiConfirmOrderDTO.setOperator("未知用户");
            }
            ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.POI_CONFIRM, baseRequest, poiConfirmOrderDTO);
            if (channelResponseDTO.getDataResponse().isSuccess()) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(channelResponseDTO.getDataResponse().getMsg());

        } catch (Exception e) {
            log.error("jddjChannelOrderService poiConfirmOrder ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }


    @Override
    public JddjAfterSaleDetailResult afterSaleDetail(JddjGetAfterSaleDetailRequest request) {
        JddjAfterSaleDetailResult result = new JddjAfterSaleDetailResult();
        if (request.getAfterSaleId() != null && request.getAfterSaleId().startsWith("JD")) {
            result.setCode(ResultCode.FAIL.getCode());
            result.setMsg("JD开头的售后单不查询");
            return result;
        }
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setAppId(request.getAppId());
        addStoreId2BaseRequest(request.getStoreId(), baseRequest);

        Map<String, Object> bizParam = ImmutableMap.of("afsServiceOrder", request.getAfterSaleId());
        ChannelResponseDTO<JddjAfterSaleDetail> resultMap = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.AFTERSALE_DETAIL, baseRequest, bizParam);
        log.info("JddjChannelOrderServiceImpl.afterSaleDetail, request:{}, resultMap:{}", request, resultMap);
        if (Objects.isNull(resultMap) || resultMap.getCoreData() == null) {
            result.setCode(ResultCode.FAIL.getCode());
            result.setMsg(resultMap != null ? resultMap.getMsg() : "失败");
        } else {
            result.setCode(ResultCode.SUCCESS.getCode());
            result.setMsg("成功");
            addJdPointsDeductionMoneyIfApplicable(result, resultMap, request.getTenantId());
            //处理京东多张图片拼接前缀问题
            dealQuestionPic(result);
        }
        return result;
    }


    private void addJdPointsDeductionMoneyIfApplicable(JddjAfterSaleDetailResult result, ChannelResponseDTO<JddjAfterSaleDetail> resultMap, Long tenantId){
        JddjAfterSaleDetail jddjAfterSaleDetail = resultMap.getDataResponse().getCoreData();
        // 检查租户京东订单的积分抵扣金额是否需要计入平台整单优惠   true -> 是
        if(MccConfigUtil.checkJdPointsDeductionMoneyDiscountsTenant(tenantId)){
            // 退单优惠金额 = 退单优惠金额 + 积分抵扣金额
            Long afsVirtualMoney = Optional.ofNullable(jddjAfterSaleDetail.getVirtualMoney()).orElse(0L) + Optional.ofNullable(jddjAfterSaleDetail.getPlatformIntegralDeductMoney()).orElse(0L);
            jddjAfterSaleDetail.setVirtualMoney(afsVirtualMoney);
            // 修改明细
            jddjAfterSaleDetail.getAfsDetailList().forEach(afsDetail -> {
                int detailVirtualMoney = Optional.ofNullable(afsDetail.getVirtualMoney()).orElse(0) + Optional.ofNullable(afsDetail.getPlatformIntegralDeductMoney()).orElse(0);
                afsDetail.setVirtualMoney(detailVirtualMoney);
            });
        }
        // 检查该租户是否支持京东先退款后退货退单处理逻辑
        if(!MccConfigUtil.checkJdAfsRefundReturnTypeTenants(tenantId)) {
            // 没有命中需要把extMap置为空
            jddjAfterSaleDetail.setExtMap(null);
        }

        result.setAfterSaleDetail(jddjAfterSaleDetail);
    }

    private void dealQuestionPic(JddjAfterSaleDetailResult result) {
        try {
            if(isCheckParams(result)){
                return;
            }
            JddjAfterSaleDetail afterSaleDetail = result.getAfterSaleDetail();
            String questionPic = afterSaleDetail.getQuestionPic().trim();
            List<String> pictureList = new ArrayList<>();
            //多张的情况
            if(questionPic.contains(",")){
                String[] images = questionPic.split(",");
                String questionImages = null;
                for (String image: images){
                    //防止有错误脏数据的情况  如“, ”
                    if(StringUtils.isBlank(image)){
                        continue;
                    }
                    pictureList.add(JD_QUESTION_PIC + image.trim());
                    //questionImages = StringUtils.isBlank(questionImages) ? JD_QUESTION_PIC + image.trim() : questionImages + ","+ JD_QUESTION_PIC + image.trim();
                }
                //afterSaleDetail.setQuestionPic(questionImages);
                afterSaleDetail.setQuestionPic(pictureList.size() > 0 ? JSON.toJSONString(pictureList) : null);
                log.info("转换后的images JddjChannelOrderServiceImpl.afterSaleDetail  questionPic: {}", afterSaleDetail.getQuestionPic());
                return;
            }
            pictureList.add(JD_QUESTION_PIC + questionPic);
            //afterSaleDetail.setQuestionPic(JD_QUESTION_PIC + questionPic);
            afterSaleDetail.setQuestionPic(pictureList.size() > 0 ? JSON.toJSONString(pictureList) : null);
            log.info("转换后的image JddjChannelOrderServiceImpl.afterSaleDetail  questionPic: {}", afterSaleDetail.getQuestionPic());
        }catch (Exception e){
            log.info("JddjChannelOrderServiceImpl.dealQuestionPic error : ", e);
        }

    }

    private boolean isCheckParams(JddjAfterSaleDetailResult result) {
        return Objects.isNull(result.getAfterSaleDetail()) || StringUtils.isBlank(result.getAfterSaleDetail().getQuestionPic());
    }


    @Override
    public GetChannelOrderDetailResult getMockOrderDetail(GetChannelOrderDetailRequest request) {
        GetChannelOrderDetailResult orderDetailResult = new GetChannelOrderDetailResult();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId());
        addStoreId2BaseRequest(request.getSotreId(), baseRequest);
        ChannelOrderDetailParam bizParam = jddjConverterService.channelOrderDetailParamMapping(request);
        ChannelResponseDTO<ChannelOrderListDetail> resultMap = jddjChannelGateService.sendPostAppDto(MockChannelPostJDDJEnum.ORDER_DETAIL, baseRequest, bizParam);
        log.info("jddjChannelOrderServiceImpl.getMockOrderDetail, request:{}, resultMap:{}", request, resultMap);
        if (Objects.isNull(resultMap)) {
            return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单详情失败"));
        }
        return orderDetailAnalysis(request.getTenantId(), request.getChannelId(), resultMap, orderDetailResult);
    }

    @Override
    public boolean isMockOrder(long tenantId, String channelOrderId) {
        GetChannelOrderDetailRequest orderDetailRequest = new GetChannelOrderDetailRequest();
        orderDetailRequest.setTenantId(tenantId);
        orderDetailRequest.setOrderId(channelOrderId);
        orderDetailRequest.setChannelId(ChannelType.JD2HOME.getValue());
        return getMockOrderDetail(orderDetailRequest).getChannelOrderDetail() != null;
    }

    @Override
    public boolean isMockAfterSaleOrder(long tenantId, String afterSaleId) {
        JddjAfterSaleDetailResult result = new JddjAfterSaleDetailResult();
        BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(ChannelType.JD2HOME.getValue());
        Map<String, Object> bizParam = ImmutableMap.of("afsServiceOrder", afterSaleId);
        ChannelResponseDTO<JddjAfterSaleDetail> resultMap = jddjChannelGateService.sendPost(MockChannelPostJDDJEnum.AFTERSALE_DETAIL, baseRequest, bizParam);
        log.info("JddjChannelOrderServiceImpl.afterSaleDetail, request:{}, resultMap:{}", baseRequest, resultMap);
        if (Objects.isNull(resultMap) || resultMap.getCoreData() == null) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
        GetChannelOrderDetailResult orderDetailResult = new GetChannelOrderDetailResult();
        ChannelResponseDTO<ChannelOrderListDetail> resultMap = doGetChannelOrder(request);
        if (Objects.isNull(resultMap)) {
            return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单详情失败"));
        }
        GetChannelOrderDetailResult getChannelOrderDetailResult = orderDetailAnalysis(request.getTenantId(), request.getChannelId(), resultMap, orderDetailResult);

        return getChannelOrderDetailResult;
    }

    private ChannelResponseDTO<ChannelOrderListDetail> doGetChannelOrder(GetChannelOrderDetailRequest request) {
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setAppId(request.getAppId());
        addStoreId2BaseRequest(request.getSotreId(), baseRequest);
        ChannelOrderDetailParam bizParam = jddjConverterService.channelOrderDetailParamMapping(request);
        ChannelResponseDTO<ChannelOrderListDetail> resultMap = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.ORDER_DETAIL, baseRequest, bizParam);
        Cat.logEvent("channelHttpCall", ChannelPostJDDJEnum.ORDER_DETAIL.getUrl());
        log.info("jddjChannelOrderServiceImpl.getChannelOrderDetail, request:{}, resultMap:{}", request, resultMap);
        return resultMap;
    }

    private GetChannelOrderDetailResult orderDetailAnalysis(long tenantId, int channelId, ChannelResponseDTO<ChannelOrderListDetail> result,
                                                            GetChannelOrderDetailResult orderDetailResult) {

        ChannelResponseResult<ChannelOrderListDetail> orderDetailResponse = result.getDataResponse();
        if (result.getCode().equals(String.valueOf(ResultCodeEnum.SUCCESS.getValue()))) {
            ChannelOrderListDetail channelOrderListDetail = orderDetailResponse.getResultData();
            if (Objects.isNull(channelOrderListDetail)) {
                return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, "订单不存在"));
            }
            List<ChannelOrderDetail> channelOrderDetailList = channelOrderListDetail.getResultList();
            if (channelOrderListDetail.getTotalCount() <= 0 || CollectionUtils.isEmpty(channelOrderDetailList)) {
                return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, "订单不存在"));
            }
            ChannelOrderDetail channelOrderDetail = channelOrderDetailList.get(0);
            //订单主数据
            ChannelOrderDetailDTO channelOrderDetailDTO = jddjConverterService.channelOrderDetailMapping(channelOrderDetail);
            //渠道用户Id
            if(StringUtils.isNotBlank(channelOrderDetailDTO.getBuyerAccount())){
                channelOrderDetailDTO.setChannelUserId(channelOrderDetailDTO.getBuyerAccount());
            }
            // 包装费处理 查询【包装袋费用是否商家收取】的配置
            boolean isMerchantsChargePackageFee = tenantService.isMerchantsChargePackageFee(tenantId,
                    DynamicChannelType.JD2HOME);
            if (isMerchantsChargePackageFee) {
                channelOrderDetailDTO.setPlatPackageAmt(0);
                channelOrderDetailDTO.setPoiPackageAmt(channelOrderDetailDTO.getPayPackageAmt());
            } else {
                channelOrderDetailDTO.setPlatPackageAmt(channelOrderDetailDTO.getPayPackageAmt());
                channelOrderDetailDTO.setPoiPackageAmt(0);
            }
            //积分抵扣金额计入优惠
            addJdPointsDeductionMoneyIfApplicable(channelOrderDetailDTO, tenantId);

            //todo npe
            channelOrderDetailDTO.setBaichuanStatus(BaichuanOrderStatusConverter.jddjOrderStatusMapping(channelOrderDetail.getOrderStatus()));
            if (Objects.equals(channelOrderDetail.getSrcOrderType(), "23")) {
                channelOrderDetailDTO.setSubBizCode(OrderSubBizTypeEnum.JDDJ_SELECTION.getValue());
            } else if (Objects.equals(channelOrderDetail.getSrcOrderType(), "20")) {
                channelOrderDetailDTO.setSubBizCode(OrderSubBizTypeEnum.JDDJ_FIT_SURVIVAL.getValue());
            }
            //门店信息
            channelOrderDetailDTO.setStoreId(copChannelStoreService.selectChannelStoreId(tenantId, channelId, channelOrderDetail.getDeliveryStationNoIsv()));
            //计算原总价
            channelOrderDetailDTO.setOriginalAmt(calcOriginalAmt(channelOrderDetail));
            //自提服务费
            if (StringUtils.isNotBlank(channelOrderDetail.getOrderJdSendpay())) {
                try {
                    OrderJdSendpay orderJdSendpay = JSON.parseObject(channelOrderDetail.getOrderJdSendpay(), OrderJdSendpay.class);
                    channelOrderDetailDTO.setSelfPickServiceFee(Optional.ofNullable(orderJdSendpay.getSelfPickPayableMoney()).orElse(0));
                } catch (Exception e) {
                    log.error("解析自提服务费错误");
                    log.error("自提服务费字段" + channelOrderDetail.getOrderJdSendpay());
                }
            }
            //配送信息
            channelOrderDetailDTO.setDeliveryDetail(jddjConverterService.deliveryDetailMapping(channelOrderDetail));
            //开票信息
            channelOrderDetailDTO.setInvoiceDetail(jddjConverterService.invoiceDetailMapping(channelOrderDetail.getOrderInvoice()));
            //商品信息
            channelOrderDetailDTO.setSkuDetails(jddjConverterService.orderSkuDetailListMapping(channelOrderDetail.getProduct()));

            // 处理sku属性字段是否透传
            dealSkuDetailsSkuPropertySwitch(tenantId, channelOrderDetailDTO);

            // 歪马租户或者赠品灰度开关关闭时，去除itemType赠品设置保留原逻辑；
            if (BusinessIdTracer.isDrunkHorseTenant(tenantId) || !MccConfigUtil.getGiftProductToOrderDetailSwitch(tenantId)) {
                channelOrderDetailDTO.getSkuDetails().forEach(detail -> {
                    detail.setItemType(0);
                });
            // 针对线上赠品需要设置扩展参数
            } else {
                channelOrderDetailDTO.getSkuDetails().forEach(detail -> {
                    if (detail.getItemType() == 1) {
                        JSONObject extJSON = new JSONObject();
                        if (detail.isSetExtData()) {
                            try{
                                String extData = detail.getExtData();
                                extJSON = JSON.parseObject(extData);
                            } catch (Exception e) {
                                log.error("解析ext异常，detail：{}", JSON.toJSONString(detail), e);
                            }
                        }
                        // 添加线上赠品标（giftType : 0 (平台赠品)），标志打在明细的扩展字段
                        extJSON.put("giftType", 0);
                        detail.setExtData(extJSON.toJSONString());
                    }
                });
            }
            // 查询渠道商品金额拆分数据
            List<JDGoodsSettlementInfo> jdSettlementInfos = getJdSettlementInfos(channelOrderDetail.getOrderId(), channelId, tenantId, channelOrderDetailDTO.getStoreId());
            log.info("查询渠道商品金额拆分数据 jdSettlementInfos: {}", jdSettlementInfos);
            // 判断租户是否支持计算运费金额
            int poiFarDistanceFreight = 0;
            if(MccConfigUtil.checkJdFreightDetailTenants(tenantId)) {
                // 商家支付远距离运费 --> 从【订单列表查询接口】获取
                poiFarDistanceFreight = Optional.ofNullable(channelOrderDetail.getMerchantPaymentDistanceFreightMoney()).orElse(0);
                // 运费金额详细汇总
                freightMoneyDetail(channelOrderDetailDTO, channelOrderDetail, jdSettlementInfos);
            }
            channelOrderDetailDTO.setPoiFarDistanceFreight(poiFarDistanceFreight);
            //活动信息
            channelOrderDetailDTO.setActivities(orderActivitieInfoList(tenantId, jdSettlementInfos, channelOrderDetail));
            //运费优惠分摊
            divideLogisticsPromotion(channelOrderDetailDTO, channelOrderDetail.getDiscount(), tenantId);
            orderIsReservation(channelOrderDetailDTO, channelOrderDetail);
            channelOrderDetailDTO.setChannelPayMode(String.valueOf(JDDJPayChannel.enumOf(channelOrderDetail.getPayChannel()).getValue()));
            // mock处理上线删除
            //mockOrderAndRefund(channelOrderDetailDTO, tenantId);
            return orderDetailResult.setStatus(ResultGenerator.genSuccessResult()).setChannelOrderDetail(channelOrderDetailDTO);
        }
        return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, result.getMsg()));
    }

    private void mockOrderAndRefund(ChannelOrderDetailDTO channelOrderDetailDTO, long tenantId){
        List<MockJdOrderAndRefundDTO> mockJdOrderAndRefundDTOList = MccConfigUtil.mockJdOrderAndRefund(tenantId);
        if(CollectionUtils.isNotEmpty(mockJdOrderAndRefundDTOList)){
            Map<String,MockJdOrderAndRefundDTO> mockJdOrderAndRefundDTOMap = mockJdOrderAndRefundDTOList.stream()
                    .collect(Collectors.toMap(MockJdOrderAndRefundDTO::getSkuId, Function.identity()));
            channelOrderDetailDTO.getSkuDetails().forEach(skuDetail -> {
                MockJdOrderAndRefundDTO mockJdOrderAndRefundDTO = mockJdOrderAndRefundDTOMap.get(skuDetail.getSkuId());
                if(Objects.nonNull(mockJdOrderAndRefundDTO)){
                    skuDetail.setSkuId(mockJdOrderAndRefundDTO.getChangeData().getSkuId());
                    if(StringUtils.isNotEmpty(mockJdOrderAndRefundDTO.getChangeData().getSkuName())) {
                        skuDetail.setSkuName(mockJdOrderAndRefundDTO.getChangeData().getSkuName());
                    }
                    if(StringUtils.isNotEmpty(mockJdOrderAndRefundDTO.getChangeData().getSkuProperty())) {
                        skuDetail.setSkuProperty(mockJdOrderAndRefundDTO.getChangeData().getSkuProperty());
                    }
                    if(StringUtils.isNotEmpty(mockJdOrderAndRefundDTO.getChangeData().getChannelItemId())){
                        skuDetail.setChannelItemId(mockJdOrderAndRefundDTO.getChangeData().getChannelItemId());
                    }
                }
            });
        }
    }

    private void mockOrderAndRefundPromotion(OrderPromotionResult result, long tenantId){
        List<MockJdOrderAndRefundDTO> mockJdOrderAndRefundDTOList = MccConfigUtil.mockJdOrderAndRefund(tenantId);
        if(CollectionUtils.isNotEmpty(mockJdOrderAndRefundDTOList)){
            Map<String,MockJdOrderAndRefundDTO> mockJdOrderAndRefundDTOMap = mockJdOrderAndRefundDTOList.stream()
                    .collect(Collectors.toMap(MockJdOrderAndRefundDTO::getSkuId, Function.identity()));
            result.getSkuPromotions().forEach(skuDetail -> {
                MockJdOrderAndRefundDTO mockJdOrderAndRefundDTO = mockJdOrderAndRefundDTOMap.get(skuDetail.getCustomSkuId());
                if(Objects.nonNull(mockJdOrderAndRefundDTO)){
                    skuDetail.setCustomSkuId(mockJdOrderAndRefundDTO.getChangeData().getSkuId());
                    skuDetail.setChannelSkuId(mockJdOrderAndRefundDTO.getChangeData().getSkuId());
                    if(StringUtils.isNotEmpty(mockJdOrderAndRefundDTO.getChangeData().getSkuName())) {
                        skuDetail.setSkuName(mockJdOrderAndRefundDTO.getChangeData().getSkuName());
                    }
                    if(StringUtils.isNotEmpty(mockJdOrderAndRefundDTO.getChangeData().getChannelItemId())){
                        skuDetail.setChannelItemId(mockJdOrderAndRefundDTO.getChangeData().getChannelItemId());
                    }
                }
            });
        }
    }

    private void mockOrderAndRefundPromotion(GoodsSettlementResult result, long tenantId){
        List<MockJdOrderAndRefundDTO> mockJdOrderAndRefundDTOList = MccConfigUtil.mockJdOrderAndRefund(tenantId);
        if(CollectionUtils.isNotEmpty(mockJdOrderAndRefundDTOList)){
            Map<String,MockJdOrderAndRefundDTO> mockJdOrderAndRefundDTOMap = mockJdOrderAndRefundDTOList.stream()
                    .collect(Collectors.toMap(MockJdOrderAndRefundDTO::getSkuId, Function.identity()));
            result.getGoodSettlementInfos().forEach(skuDetail -> {
                MockJdOrderAndRefundDTO mockJdOrderAndRefundDTO = mockJdOrderAndRefundDTOMap.get(skuDetail.getCustomSkuId());
                if(Objects.nonNull(mockJdOrderAndRefundDTO)){
                    skuDetail.setCustomSkuId(mockJdOrderAndRefundDTO.getChangeData().getSkuId());
                    if(StringUtils.isNotEmpty(mockJdOrderAndRefundDTO.getChangeData().getChannelItemId())){
                        skuDetail.setChannelItemId(mockJdOrderAndRefundDTO.getChangeData().getChannelItemId());
                    }
                }
            });
        }
    }

    private void mockOrderAndRefundPromotion(GetOrderAfsApplyListResult result, long tenantId){
        List<MockJdOrderAndRefundDTO> mockJdOrderAndRefundDTOList = MccConfigUtil.mockJdOrderAndRefund(tenantId);
        if(CollectionUtils.isNotEmpty(mockJdOrderAndRefundDTOList)){
            Map<String,MockJdOrderAndRefundDTO> mockJdOrderAndRefundDTOMap = mockJdOrderAndRefundDTOList.stream()
                    .collect(Collectors.toMap(MockJdOrderAndRefundDTO::getSkuId, Function.identity()));
            result.getAfsApplyList().forEach(afsApply -> {afsApply.getAfsProductList().forEach(skuDetail -> {
                MockJdOrderAndRefundDTO mockJdOrderAndRefundDTO = mockJdOrderAndRefundDTOMap.get(skuDetail.getSkuId());
                if(Objects.nonNull(mockJdOrderAndRefundDTO)){
                    skuDetail.setSkuId(mockJdOrderAndRefundDTO.getChangeData().getSkuId());
                }
            });});
        }
    }

    private void dealSkuDetailsSkuPropertySwitch(long tenantId, ChannelOrderDetailDTO channelOrderDetailDTO) {
        try {
            if (MccConfigUtil.getJddjSkuPropertySaveSwitch(tenantId)) {
                // 去掉sku规格，后续使用商品规格填充
                channelOrderDetailDTO.getSkuDetails().forEach(skuDetail -> {
                    skuDetail.setSpecification(null);
                });
            } else {
                // 去掉sku属性，沿用老逻辑
                channelOrderDetailDTO.getSkuDetails().forEach(skuDetail -> {
                    skuDetail.setSkuProperty(null);
                });
            }
        } catch (Exception e) {
            log.error("dealSkuDetailsSkuPropertySwitch tenantId:{}, channelOrderDetailDTO:{}, error", tenantId, channelOrderDetailDTO, e);
        }
    }

    /**
     * 运费金额详细汇总
     *
     * @param channelOrderDetailDTO
     * @param jdSettlementInfos
     */
    private void freightMoneyDetail(ChannelOrderDetailDTO channelOrderDetailDTO, ChannelOrderDetail channelOrderDetail, List<JDGoodsSettlementInfo> jdSettlementInfos) {
        try {
            // 初始化部分类型的运费金额 -- 基础运费、距离运费（先从【订单列表查询】接口获取，如果【订单金额拆分】接口有数据，则更新）
            channelOrderDetailDTO.setBaseFreight(Optional.ofNullable(channelOrderDetail.getOrderBaseFreightMoney()).orElse(0));
            channelOrderDetailDTO.setWeightFreight(0);
            channelOrderDetailDTO.setDistanceFreight(Optional.ofNullable(channelOrderDetail.getOrderDistanceStepFreight()).orElse(0));
            channelOrderDetailDTO.setTimeFrameFreight(0);
            // 金额拆分数据为空 或者 所有商品数据都没有有运费金额数据
            if(CollectionUtils.isEmpty(jdSettlementInfos) || jdSettlementInfos.stream().allMatch(any ->  CollectionUtils.isEmpty(any.getOrderFreightMoneyList()))){
                log.info("金额拆分数据为空或商品都没有运费金额数据! jdSettlementInfos:{}", JSON.toJSONString(jdSettlementInfos));
                return ;
            }
            // 分组聚集各类型金额
            Map<Integer, JDOrderFreightMoney> freightMoneyMap = new HashMap<>(4);
            for (JDGoodsSettlementInfo jdSettlementInfo : jdSettlementInfos) {
                if(CollectionUtils.isEmpty(jdSettlementInfo.getOrderFreightMoneyList())){
                    continue;
                }
                for (JDOrderFreightMoney jdOrderFreightMoney : jdSettlementInfo.getOrderFreightMoneyList()) {
                    if(freightMoneyMap.containsKey(jdOrderFreightMoney.getFreightType())){
                        JDOrderFreightMoney jdOrderFreightMoneyAll = freightMoneyMap.get(jdOrderFreightMoney.getFreightType());
                        jdOrderFreightMoneyAll.setFreightMoney(Optional.ofNullable(jdOrderFreightMoneyAll.getFreightMoney()).orElse(0L) + Optional.ofNullable(jdOrderFreightMoney.getFreightMoney()).orElse(0L));
                        jdOrderFreightMoneyAll.setSingleFreightMoney(Optional.ofNullable(jdOrderFreightMoneyAll.getSingleFreightMoney()).orElse(0L) + Optional.ofNullable(jdOrderFreightMoney.getSingleFreightMoney()).orElse(0L));
                        freightMoneyMap.put(jdOrderFreightMoney.getFreightType(), jdOrderFreightMoneyAll);
                    }else{
                        freightMoneyMap.put(jdOrderFreightMoney.getFreightType(), jdOrderFreightMoney);
                    }
                }
            }
            // 对应赋值运费金额
            for (JdFreightMoneyTypeEnum value : JdFreightMoneyTypeEnum.values()) {
                Long freightMoney = freightMoneyMap.containsKey(value.getCode()) ? Optional.ofNullable(freightMoneyMap.get(value.getCode()).getFreightMoney()).orElse(0L) : 0L;
                switch (value){
                    case BASE_FREIGHT:
                        channelOrderDetailDTO.setBaseFreight(Optional.ofNullable(freightMoney).orElse(0L).intValue());
                        break;
                    case WEIGHT_FREIGHT:
                        channelOrderDetailDTO.setWeightFreight(Optional.ofNullable(freightMoney).orElse(0L).intValue());
                        break;
                    case DISTANCE_FREIGHT:
                        channelOrderDetailDTO.setDistanceFreight(Optional.ofNullable(freightMoney).orElse(0L).intValue());
                        break;
                    case TIME_FRAME_FREIGHT:
                        channelOrderDetailDTO.setTimeFrameFreight(Optional.ofNullable(freightMoney).orElse(0L).intValue());
                        break;
                }
            }
            // 渠道【订单金额拆分接口】有值后，与接口中所有运费类型的运费金额相加之和对比，应收配送费 = 到家门店基础运费+到家门店重量运费+到家门店距离运费+到家门店时段运费。
            Integer allOrderFreightMoney = Optional.ofNullable(channelOrderDetailDTO.getBaseFreight()).orElse(0) + Optional.ofNullable(channelOrderDetailDTO.getWeightFreight()).orElse(0)
                    + Optional.ofNullable(channelOrderDetailDTO.getDistanceFreight()).orElse(0) + Optional.ofNullable(channelOrderDetailDTO.getTimeFrameFreight()).orElse(0);
            channelOrderDetailDTO.setFreight(allOrderFreightMoney);
        }catch (Exception e){
            log.error("商品金额拆分查询接口返回数据异常");
        }
    }

    /**
     * 商品金额拆分查询接口
     * @param orderId
     * @param channelId
     * @param tenantId
     * @param storeId
     * @return
     */
    private List<JDGoodsSettlementInfo> getJdSettlementInfos(String orderId, int channelId, long tenantId, long storeId){
        try {
            if (StringUtils.isEmpty(orderId)) {
                log.info("商品金额拆分查询接口orderId为空");
                return new ArrayList<>();
            }
            Map<String, Object> param = new HashMap<>();
            param.put("orderId", orderId);
            BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId);
            addStoreId2BaseRequest(storeId, baseRequest);
            ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.GOODS_SETTLEMENT_INFO, baseRequest, param);
            return JSON.parseArray(channelResponseDTO.getDataResponse().getData(), JDGoodsSettlementInfo.class);
        }catch (Exception e){
            log.error("商品金额拆分查询接口调用失败! orderId={}，channelId={}，tenantId={}，storeId={}", orderId, channelId, tenantId, storeId);
            return new ArrayList<>();
        }
    }


    private void addJdPointsDeductionMoneyIfApplicable(ChannelOrderDetailDTO channelOrderDetailDTO, Long tenantId) {
        // 检查租户京东订单的积分抵扣金额是否计入平台整单优惠
        if(MccConfigUtil.checkJdPointsDeductionMoneyDiscountsTenant(tenantId)){
            // 京东订单优惠金额 = 订单优惠金额 + 积分抵扣金额
            channelOrderDetailDTO.setTotalOrderPromotion(channelOrderDetailDTO.getTotalOrderPromotion() + channelOrderDetailDTO.getScoreDeduction());
        }
    }

    private void orderIsReservation(ChannelOrderDetailDTO channelOrderDetailDTO, ChannelOrderDetail channelOrderDetail) {
        try {
            if(Objects.isNull(channelOrderDetail) || StringUtils.isBlank(channelOrderDetail.getBusinessTag())){
                return;
            }
//            log.info("京东预约订单处理前 channelOrderDetail: {}", JSON.toJSONString(channelOrderDetail));
            if(channelOrderDetail.getBusinessTag().contains("dj_aging_nextday")){
                // 预订单，营业即送
                channelOrderDetailDTO.setIsOpeningDelivery(true);
                channelOrderDetailDTO.setIsBooking(true);
                log.info("JD 营业即送/预订单");
            }
        }catch (Exception e){
            log.info("JD orderIsReservation 预约单/营业即送 deal error :",e);
        }
    }

    private List<OrderDiscountDetailDTO> orderActivitieInfoList(long tenantId, List<JDGoodsSettlementInfo> jdSettlementInfos, ChannelOrderDetail channelOrderDetail) {

        try {
            // 开关
            if( !ConfigUtilAdapter.getBoolean(IS_OPEN_OR_CLOSE_SALES_PROMOTION, false)){
                log.info("开关:京东活动优惠信息统计关闭 ");
            }

            List<OrderDiscountDetailDTO> activities = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(jdSettlementInfos)) {
                //装整品促销数据 促销号进行统计，如果有相同的则进行叠加数据
                Map<String, OrderDiscountDetailDTO> bigOrdermap = new HashMap<>();
                for (JDGoodsSettlementInfo jdGoodsSettlementInfo : jdSettlementInfos) {
                    if (Objects.isNull(jdGoodsSettlementInfo)) {
                        continue;
                    }
                    //拆分整单优惠信息
                    splitBigOrderPreferential(bigOrdermap, jdGoodsSettlementInfo.getDiscountlist());
                    //拆分单品优惠信息
                    splitOneOrderPreferential(activities, jdGoodsSettlementInfo);
                }
                if (bigOrdermap.size() > 0) {
                    activities.addAll(bigOrdermap.entrySet().stream().map(ent -> ent.getValue())
                            .collect(Collectors.toList()));
                }
            }
            // 从详情和拆分信息中处理运费活动信息
            activities.addAll(orderFreightActivitieInfoList(tenantId, channelOrderDetail, jdSettlementInfos));

            log.info("查询促销活动赋值 activities: {}", activities);
            return activities;

        }catch (Exception e){
            log.info("京东处理订单活动促销 JddjChannelOrderServiceImpl.orderActivitieInfoList error: ", e);
            return new ArrayList<>();
        }
    }

    private void splitLogisticsPreferential(Map<String, OrderDiscountDetailDTO> bigOrdermap, List<JDGoodsActivityDetail> freightDiscountList){
        try {
            if(CollectionUtils.isEmpty(freightDiscountList)){
                return;
            }

            for (JDGoodsActivityDetail jdGoodsActivityDetail : freightDiscountList){
                if (JddjOrderConvertUtil.isPlatformLogisticsActivity(jdGoodsActivityDetail.getPromotionType())){
                    // 不会出现平台的，如果有就跳过平台的活动分摊
                    continue;
                }
                //促销号 活动编号
                String promotionCode = jdGoodsActivityDetail.getPromotionCode();
                // 如果不存在促销号，则默认一个规则去合并运费
                if(StringUtils.isEmpty(promotionCode)){
                    promotionCode = Objects.isNull(jdGoodsActivityDetail.getPromotionType()) ? "freightDiscount" : "freightDiscount" + jdGoodsActivityDetail.getPromotionType();
                }
                //已经存在的情况下 只需叠加金额即可
                if(Objects.nonNull(bigOrdermap.get(promotionCode))){
                    OrderDiscountDetailDTO discountDetailDTO = bigOrdermap.get(promotionCode);
                    discountDetailDTO.setActDiscount(discountDetailDTO.getActDiscount() + jdGoodsActivityDetail.getSkuDiscountMoney());
                    discountDetailDTO.setBizCharge(discountDetailDTO.getBizCharge() + jdGoodsActivityDetail.getSaleMoney());
                    discountDetailDTO.setChannelCharge(discountDetailDTO.getChannelCharge() + jdGoodsActivityDetail.getCostMoney());
                    bigOrdermap.put(promotionCode, discountDetailDTO);
                    continue;
                }
                //不存在的情况下
                OrderDiscountDetailDTO dto = new OrderDiscountDetailDTO();
                dto.setActivityId(promotionCode);
                dto.setActDiscount(jdGoodsActivityDetail.getSkuDiscountMoney());
                dto.setBizCharge(jdGoodsActivityDetail.getSaleMoney());
                dto.setChannelCharge(jdGoodsActivityDetail.getCostMoney());
                String activityType = JDDJOrderPromotionTypeEnum.enumOf(getCodeKey(jdGoodsActivityDetail.getPromotionType(), jdGoodsActivityDetail.getPromotionDetailType())).getOrderPromotionName();
                checkIsUnknownActivity(activityType);
                dto.setType(dealType(jdGoodsActivityDetail.getPromotionType(), jdGoodsActivityDetail.getPromotionDetailType()));
                dto.setRemark(activityType);
                bigOrdermap.put(promotionCode, dto);
            }
        }catch (Exception e){
            log.error("促销整单运费优惠数据拆分 JddjChannelOrderServiceImpl.splitLogisticsPreferential error", e);
        }
    }

    private void splitOrderDetailLogisticsPreferential(Map<String, OrderDiscountDetailDTO> bigOrdermap, List<OrderActivitiesInfo> discount){
        try {
            if(CollectionUtils.isEmpty(discount)){
                return;
            }
            for (OrderActivitiesInfo activity : discount) {
                // 只记录平台
                if (JddjOrderConvertUtil.isPlatformLogisticsActivity(activity.getDiscountType())) {
                    //促销号 活动编号
                    String promotionCode = activity.getDiscountCode();
                    // 如果不存在促销号，则默认一个规则去合并运费
                    if(StringUtils.isEmpty(promotionCode)){
                        promotionCode = Objects.isNull(activity.getDiscountType()) ? "freightDiscount" : "freightDiscount" + activity.getDiscountType();
                    }
                    //已经存在的情况下 只需叠加金额即可
                    if(Objects.nonNull(bigOrdermap.get(promotionCode))){
                        OrderDiscountDetailDTO discountDetailDTO = bigOrdermap.get(promotionCode);
                        discountDetailDTO.setActDiscount(discountDetailDTO.getActDiscount() + activity.getDiscountPrice());
                        // 此处都为平台承担
                        //discountDetailDTO.setBizCharge(discountDetailDTO.getBizCharge());
                        discountDetailDTO.setChannelCharge(discountDetailDTO.getChannelCharge() + activity.getDiscountPrice());
                        bigOrdermap.put(promotionCode, discountDetailDTO);
                        continue;
                    }
                    //不存在的情况下
                    OrderDiscountDetailDTO dto = new OrderDiscountDetailDTO();
                    dto.setActivityId(promotionCode);
                    dto.setActDiscount(activity.getDiscountPrice());
                    dto.setBizCharge(0);
                    dto.setChannelCharge(activity.getDiscountPrice());
                    String activityType = JDDJOrderPromotionTypeEnum.enumOf(getCodeKey(activity.getDiscountType(), activity.getDiscountDetailType())).getOrderPromotionName();
                    checkIsUnknownActivity(activityType);
                    dto.setType(dealType(activity.getDiscountType(), activity.getDiscountDetailType()));
                    dto.setRemark(activityType);
                    bigOrdermap.put(promotionCode, dto);
                }
            }
        }catch (Exception e){
            log.error("促销订单详情中获取整单运费优惠活动信息 JddjChannelOrderServiceImpl.splitOrderDetailLogisticsPreferential error", e);
        }
    }

    private void splitOneOrderPreferential(List<OrderDiscountDetailDTO> activities, JDGoodsSettlementInfo jdGoodsSettlementInfo) {

        try {
            if (jdGoodsSettlementInfo.getPromotionType() == null || jdGoodsSettlementInfo.getPromotionType() == JDDJGoodsPromotionTypeEnum.NONE.getGoodsPromotionType()) {
                return;
            }
            Integer skuCount = jdGoodsSettlementInfo.getSkuCount();
            if(Objects.isNull(skuCount)){
                log.error("单品的商品 skuCount 为null");
                return;
            }
            OrderDiscountDetailDTO dto = new OrderDiscountDetailDTO();
            // 活动ID  单品的
            dto.setActivityId(jdGoodsSettlementInfo.getPromotionId() == null ? "" : String.valueOf(jdGoodsSettlementInfo.getPromotionId()));
            //商家承担
            dto.setBizCharge(jdGoodsSettlementInfo.getSaleMoney() * skuCount);
            //平台承担
            dto.setChannelCharge(jdGoodsSettlementInfo.getCostMoney() * skuCount);
            //活动总优惠金额 = 商家承担 + 平台承担
            dto.setActDiscount(dto.getBizCharge() + dto.getChannelCharge());
            // 活动类型
            Integer promotionType = jdGoodsSettlementInfo.getPromotionType();
            String activityType = JDDJGoodsPromotionTypeEnum.enumOf(promotionType).getGoodsPromotionName();
            checkIsUnknownActivity(activityType);
            dto.setType(Objects.isNull(promotionType) ? "-1" : String.valueOf(promotionType));
            dto.setRemark(activityType);
            activities.add(dto);
        }catch (Exception e){
            log.error("组合促销单品 JddjChannelOrderServiceImpl.splitOneOrderPreferential error", e);
        }

    }

    /**
     * 整单优惠信息
     */
    private void splitBigOrderPreferential(Map<String, OrderDiscountDetailDTO> bigOrdermap, List<JDGoodsActivityDetail> discountLists) {
        try {
            if(Objects.isNull(discountLists)){
                return;
            }
            for (JDGoodsActivityDetail jdGoodsActivityDetail : discountLists){
                //促销号 活动编号
                String promotionCode = jdGoodsActivityDetail.getPromotionCode();
                if(StringUtils.isBlank(promotionCode)){
                    continue;
                }
                //已经存在的情况下 只需叠加金额即可
                if(bigOrdermap.containsKey(promotionCode) && Objects.nonNull(bigOrdermap.get(promotionCode))){
                    OrderDiscountDetailDTO discountDetailDTO = bigOrdermap.get(promotionCode);
                    discountDetailDTO.setActDiscount(discountDetailDTO.getActDiscount() + jdGoodsActivityDetail.getSkuDiscountMoney());
                    discountDetailDTO.setBizCharge(discountDetailDTO.getBizCharge() + jdGoodsActivityDetail.getSaleMoney());
                    discountDetailDTO.setChannelCharge(discountDetailDTO.getChannelCharge() + jdGoodsActivityDetail.getCostMoney());
                    bigOrdermap.put(promotionCode, discountDetailDTO);
                    continue;
                }
                //不存在的情况下
                OrderDiscountDetailDTO dto = new OrderDiscountDetailDTO();
                dto.setActivityId(promotionCode);
                dto.setActDiscount(jdGoodsActivityDetail.getSkuDiscountMoney());
                dto.setBizCharge(jdGoodsActivityDetail.getSaleMoney());
                dto.setChannelCharge(jdGoodsActivityDetail.getCostMoney());
                String activityType = JDDJOrderPromotionTypeEnum.enumOf(getCodeKey(jdGoodsActivityDetail.getPromotionType(), jdGoodsActivityDetail.getPromotionDetailType())).getOrderPromotionName();
                checkIsUnknownActivity(activityType);
                dto.setType(dealType(jdGoodsActivityDetail.getPromotionType(), jdGoodsActivityDetail.getPromotionDetailType()));
                dto.setRemark(activityType);
                bigOrdermap.put(promotionCode, dto);
            }
        }catch (Exception e){
            log.error("组合促销整品 JddjChannelOrderServiceImpl.splitBigOrderPreferential error", e);
        }

    }

    private String dealType(Integer promotionType, Integer promotionDetailType) {
        if(Objects.isNull(promotionDetailType)){
            return Objects.isNull(promotionType) ? "-1" : promotionType + "000";
        }
        if(Objects.isNull(promotionType)){
            return "000" + promotionDetailType;
        }
        return promotionType + "000" + promotionDetailType;
    }

    private String getCodeKey(Integer promotionType, Integer promotionDetailType) {
        // 优惠类型为7, 8, 12, 15, 16, 18时，不管promotionDetailType的值，统一处理为【promotionType-f】，映射对应promotionType的枚举
        if(Objects.nonNull(promotionType) && Arrays.asList(7, 8, 12, 15, 16, 18).contains(promotionType)){
            return promotionType +"-f";
        }
        if(Objects.isNull(promotionDetailType)){
            return Objects.isNull(promotionType) ? "-1" : promotionType + "-";
        }
        if(Objects.isNull(promotionType)){
            return "-" + promotionDetailType;
        }
        return promotionType + "-" + promotionDetailType;
    }


    private void checkIsUnknownActivity(String activityType) {
        if("未知优惠类型".equals(activityType)){
            log.info("京东优惠活动统计-活动类型发生未知类型");
            Cat.logEvent("JD_ACTIVITY_TYPE_UNKNOWN_CAT", "JD_ACTIVITY_TYPE_ERROR");
        }
    }
    /**
     * 运费优惠分摊
     *
     * @param channelOrderDetailDTO
     * @param discount
     */
    private void divideLogisticsPromotion(ChannelOrderDetailDTO channelOrderDetailDTO, List<OrderActivitiesInfo> discount, Long tenantId) {
        int platLogisticsPromotion = 0;
        int poiLogisticsPromotion = 0;
        if (CollectionUtils.isNotEmpty(discount)) {
            for (OrderActivitiesInfo activity : discount) {
                if (JddjOrderConvertUtil.isPlatformLogisticsActivity(activity.getDiscountType())) {
                    platLogisticsPromotion += Optional.ofNullable(activity.getDiscountPrice()).orElse(0L);
                }
                if (JddjOrderConvertUtil.isPoiLogisticsActivity(activity.getDiscountType())){
                    // 检查该租户是否支持京东运费调整承担方
                    if(MccConfigUtil.checkJdFreightAdjustBearerTenants(tenantId)){
                        platLogisticsPromotion += Optional.ofNullable(activity.getPlatPayMoney()).orElse(0L);
                        poiLogisticsPromotion += Optional.ofNullable(activity.getVenderPayMoney()).orElse(0L);
                    }else{
                        poiLogisticsPromotion += Optional.ofNullable(activity.getDiscountPrice()).orElse(0L);
                    }
                }
                if (JddjOrderConvertUtil.isShareLogisticsActivity(activity.getDiscountType())) {
                    platLogisticsPromotion += Optional.ofNullable(activity.getPlatPayMoney()).orElse(0L);
                    poiLogisticsPromotion += Optional.ofNullable(activity.getVenderPayMoney()).orElse(0L);
                }
            }
        }
        channelOrderDetailDTO.setPoiLogisticsPromotion(poiLogisticsPromotion);
        channelOrderDetailDTO.setPlatLogisticsPromotion(platLogisticsPromotion);
        //自送
        if (Objects.equals(channelOrderDetailDTO.getDeliveryDetail().isSelfDelivery, 1)) {
            channelOrderDetailDTO.setPoiLogisticsIncome(channelOrderDetailDTO.getFreight() - poiLogisticsPromotion - channelOrderDetailDTO.getPoiFarDistanceFreight());
        } else {
            channelOrderDetailDTO.setPoiLogisticsIncome(-poiLogisticsPromotion - channelOrderDetailDTO.getPoiFarDistanceFreight());
        }
    }

    private int calcOriginalAmt(ChannelOrderDetail channelOrderDetail) {
        return channelOrderDetail.getOrderTotalMoney() + channelOrderDetail.getOrderFreightMoney() + channelOrderDetail.getPackagingMoney();
    }


    @Override
    public ResultStatus preparationMealComplete(PreparationMealCompleteRequest request) {
        ChannelOrderListDetail channelOrderListDetail = getChannelOrderDetail(request.getChannelId(), request.getOrderId(), request.getTenantId(), request.getStoreId());


        if (CollectionUtils.isEmpty(channelOrderListDetail.getResultList())) {
            return ResultGenerator.genFailResult("京东渠道查询订单为空");
        }


        ChannelOrderDetail channelOrderDetail = channelOrderListDetail.getResultList().get(0);
        String deliveryCarrierNo = channelOrderDetail.getDeliveryCarrierNo();

        if (StringUtils.isBlank(deliveryCarrierNo)) {
            return ResultGenerator.genFailResult("京东渠道查询订单配送类型为空");
        }

        ChannelPostJDDJEnum channelPostJDDJEnum = null;
        // 承运商编号(9966:达达专送;2938:商家自送;3587:同城快递;9999:到店自提)
        switch (deliveryCarrierNo) {
            case "9966":
                //拣货完成且众包配送接口
                channelPostJDDJEnum = ChannelPostJDDJEnum.MEAL_COMPLETE;
                break;
            case "3587":
                //拣货完成且众包配送接口
                channelPostJDDJEnum = ChannelPostJDDJEnum.MEAL_COMPLETE;
                log.info("同城快递类型调用拣货完成成功！");
                break;
            case "9999":
                //拣货完成且顾客自提接口
                channelPostJDDJEnum = ChannelPostJDDJEnum.SELF_ORDER_MEAL_COMPLETE;
                break;
            case "2938":
                //拣货完成且商家自送
                channelPostJDDJEnum = ChannelPostJDDJEnum.ORDER_SERLLER_DELIVERY;
                break;
            default:
                // 后续测试其他情况
                log.error("暂不支持的配送方式，直接拣货成功 PreparationMealCompleteRequest:{}",request);
                return ResultGenerator.genFailResult("暂不支持的配送方式!");
        }

        request.setOperator(StringUtils.defaultString(request.getOperator(), "sys"));
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
        addStoreId2BaseRequest(request.getStoreId(), baseRequest);
        PreparationMealCompleteDTO mealCompleteDTO = jddjConverterService.mealCompleteMapping(request);
        ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(channelPostJDDJEnum, baseRequest, mealCompleteDTO);
        if (channelResponseDTO.getDataResponse().isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult("拣货完成且众包配送推送失败:" + channelResponseDTO.getDataResponse().getMsg());
    }

    @Override
    public GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request) {
        GetOrderStatusResult resp = new GetOrderStatusResult();
        ChannelOrderListDetail orderDetailDto = getChannelOrderDetail(request.getChannelId(), request.getOrderId(), request.getTenantId(), request.getStoreId(), request.getAppId());
        if (orderDetailDto != null && CollectionUtils.isNotEmpty(orderDetailDto.getResultList())) {
            Integer jdOrderStatus = orderDetailDto.getResultList().get(0).getOrderStatus();
            if (jdOrderStatus == null) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单状态为空"));
            }
            resp.setStatus(ResultGenerator.genSuccessResult());
            OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
            orderStatusDTO.setStatus(OrderStatusConverter.jddjOrderStatusMapping(jdOrderStatus));
            orderStatusDTO.setOrderId(request.getOrderId());
            resp.setOrderStatus(orderStatusDTO);
        } else {
            resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取订单失败"));
        }
        return resp;
    }

    @Override
    public ResultStatus agreeRefund(AgreeRefundRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);
            if (request.getAfsApplyType() == AfsApplyType.AFTER_SALE.getValue()) {

                JDApplyDealTypeEnum jdApplyDealType = getJDApplyDealType(request.getOrderId(), request.getChannelId(), request.getTenantId(), request.getStoreId());
                if (Objects.isNull(jdApplyDealType)) {
                    return ResultGenerator.genFailResult(ResultCode.AFTERSALE_NOT_EXIST.getMsg());
                }
                // 退货退款的售后单抛出异常
                if (jdApplyDealType.equals(JDApplyDealTypeEnum.REFUND_GOODS)) {
                    return ResultGenerator.genFailResult(ResultCode.AFTERSALE_HAS_UPDATE.getMsg());
                }

                //售后退款审批
                ChannelResponseDTO resultData = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.AFTERSALE_ORDER_REFUND, baseRequest, jddjConverterService.agreeAfterSaleRefund(request));
                String msg = resultData.getDataResponse().isSuccess() ? ResultCode.SUCCESS.getMsg() : resultData.getErrorMsg();
                return resultData.getDataResponse().isSuccess() ? ResultGenerator.genSuccessResult().setMsg(msg) : ResultGenerator.genFailResult(msg);
            } else {
                //售中退款审批
                ChannelResponseDTO resultData = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.ORDER_REFUND, baseRequest, jddjConverterService.agreeRefund(request));
                String msg = resultData.getDataResponse().isSuccess() ? ResultCode.SUCCESS.getMsg() : resultData.getErrorMsg();
                return resultData.getDataResponse().isSuccess() ? ResultGenerator.genSuccessResult().setMsg(msg) : ResultGenerator.genFailResult(msg);
            }
        } catch (Exception e) {
            log.error("jddjChannelOrderService rejectRefund ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public ResultStatus rejectRefund(RejectRefundRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);
            if (request.getAfsApplyType() == AfsApplyType.AFTER_SALE.getValue()) {

                JDApplyDealTypeEnum jdApplyDealType = getJDApplyDealType(request.getAfterSaleId(), request.getChannelId(), request.getTenantId(), request.getStoreId());
                if (Objects.isNull(jdApplyDealType)) {
                    return ResultGenerator.genFailResult(ResultCode.AFTERSALE_NOT_EXIST.getMsg());
                }
                // 退货退款的售后单抛出异常
                if (jdApplyDealType.equals(JDApplyDealTypeEnum.REFUND_GOODS)) {
                    return ResultGenerator.genFailResult(ResultCode.AFTERSALE_HAS_UPDATE.getMsg());
                }


                ChannelResponseDTO resultData = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.AFTERSALE_ORDER_REFUND, baseRequest, jddjConverterService.rejectAfterSaleRefund(request));
                String msg = resultData.getDataResponse().isSuccess() ? ResultCode.SUCCESS.getMsg() : resultData.getErrorMsg();
                return resultData.getDataResponse().isSuccess() ? ResultGenerator.genSuccessResult().setMsg(msg) : ResultGenerator.genFailResult(msg);
            } else {
                ChannelResponseDTO resultData = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.ORDER_REFUND, baseRequest, jddjConverterService.rejectRefund(request));

                String msg = resultData.getDataResponse().isSuccess() ? ResultCode.SUCCESS.getMsg() : resultData.getErrorMsg();
                return resultData.getDataResponse().isSuccess() ? ResultGenerator.genSuccessResult().setMsg(msg) : ResultGenerator.genFailResult(msg);
            }
        } catch (Exception e) {
            log.error("jddjChannelOrderService rejectRefund ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public ResultStatus refundGoods(RefundGoodsRequest request) {
        try {
            JDApplyDealTypeEnum jdApplyDealType = getJDApplyDealType(request.getAfterSaleId(), request.getChannelId(), request.getTenantId(), request.getStoreId());
            if (Objects.isNull(jdApplyDealType)) {
                return ResultGenerator.genFailResult(ResultCode.AFTERSALE_NOT_EXIST.getMsg());
            }
            // 非退货退款售后单抛出异常
            if (!jdApplyDealType.equals(JDApplyDealTypeEnum.REFUND_GOODS)) {
                return ResultGenerator.genFailResult(ResultCode.AFTERSALE_HAS_UPDATE.getMsg());
            }
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);

            AfsReviewTypeEnum reviewType = AfsReviewTypeEnum.enumOf(request.getReviewType());
            ChannelResponseDTO resultData = null;
            switch (reviewType) {
                case AGREE_REFUND_GOODS:
                    //售后退款审批
                    resultData = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.AFTERSALE_ORDER_REFUND, baseRequest, jddjConverterService.agreeAfterSaleRefund(request));
                    break;
                case REJECT_REFUND:
                    if (request.getAuditStage() == AfsAuditStageEnum.FINAL_AUDIT.getValue()) {
                        ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(request.getTenantId(), request.getChannelId(), request.getStoreId());
                        if (channelStoreDO == null || StringUtils.isEmpty(channelStoreDO.getChannelOnlinePoiCode())) {
                            return ResultGenerator.genFailResult(ResultCode.GET_CHANNEL_POICODE_MSG.getMsg());
                        }
                        JddjConfirmReceiptFailOpenDTO jddjConfirmReceiptFailOpenDTO = new JddjConfirmReceiptFailOpenDTO();
                        jddjConfirmReceiptFailOpenDTO.setAfsServiceOrder(request.getAfterSaleId());
                        jddjConfirmReceiptFailOpenDTO.setPin("system");
                        jddjConfirmReceiptFailOpenDTO.setStationNo(channelStoreDO.getChannelOnlinePoiCode());
                        //售后终审审审批驳回
                        resultData = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.POI_REJECT_AFTERSALE_REFUND_GOODS, baseRequest, jddjConfirmReceiptFailOpenDTO);
                    } else {
                        //售后初审审批驳回
                        resultData = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.AFTERSALE_ORDER_REFUND, baseRequest, jddjConverterService.rejectAfterSaleRefund(request));
                    }
                    break;
                case AGREE_REFUND:
                    resultData = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.POI_RECEIVER_AFTERSALE_REFUND_GOODS, baseRequest, jddjConverterService.receiveAfterSaleRefundGoods(request));
                    break;
                default:
                    log.error("jddjChannelOrderService refundGoods reviewType ERROR!");
                    break;
            }
            String msg = resultData.getDataResponse().isSuccess() ? ResultCode.SUCCESS.getMsg() : resultData.getErrorMsg();
            return resultData.getDataResponse().isSuccess() ? ResultGenerator.genSuccessResult().setMsg(msg) : ResultGenerator.genFailResult(msg);
        } catch (Exception e) {
            log.error("jddj.refundGoods ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    private JDApplyDealTypeEnum getJDApplyDealType(String afterSaleId, int channelId, long tenantId, long storeId) {
        JddjGetAfterSaleDetailRequest getAfterSaleDetailRequest = new JddjGetAfterSaleDetailRequest();
        getAfterSaleDetailRequest.setAfterSaleId(afterSaleId);
        getAfterSaleDetailRequest.setChannelId(channelId);
        getAfterSaleDetailRequest.setTenantId(tenantId);
        getAfterSaleDetailRequest.setStoreId(storeId);
        JddjAfterSaleDetailResult afterSaleDetailResult = afterSaleDetail(getAfterSaleDetailRequest);
        if (Objects.nonNull(afterSaleDetailResult.getAfterSaleDetail())) {
            JddjAfterSaleDetail afterSaleDetail = afterSaleDetailResult.getAfterSaleDetail();
            String applyDeal = afterSaleDetail.getApplyDeal();
            return JDApplyDealTypeEnum.enumOf(applyDeal);
        }
        return null;
    }

    @Override
    public ResultStatus poiCancelOrder(PoiCancelOrderRequest request) {
        try {
            //京东全单退款与部分退款类似
//            if (CollectionUtils.isEmpty(request.getRefundProducts())) {
//                return ResultGenerator.genFailResult("【京东】退款商品不能为空");
//            }
//            PoiPartRefundRequest partRefundRequest = jddjConverterService.convertCancel2PartRefund(request);
//            return poiPartRefundApply(partRefundRequest);

            // 京东部分退款不允许所有商品全退，上述逻辑与文档描述相反

            GetOrderStatusRequest getOrderStatusRequest = new GetOrderStatusRequest();

            getOrderStatusRequest.setTenantId(request.getTenantId());
            getOrderStatusRequest.setChannelId(request.getChannelId());
            getOrderStatusRequest.setOrderId(request.getOrderId());
            getOrderStatusRequest.setStoreId(request.getStoreId());


            try {
                GetOrderStatusResult getOrderStatusResult = getOrderStatus(getOrderStatusRequest);

                if (getOrderStatusResult == null || getOrderStatusResult.getStatus() == null || getOrderStatusResult.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                    return ResultGenerator.genFailResult("获取渠道订单状态错误");
                }

                if (getOrderStatusResult.getOrderStatus().getStatus() == ChannelOrderStatus.NEW_ORDER.getValue()) {
                    // 渠道订单为待接单状态，调用接单接口不接单
                    PoiConfirmOrderRequest poiConfirmOrderRequest = new PoiConfirmOrderRequest();
                    poiConfirmOrderRequest.setTenantId(request.getTenantId());
                    poiConfirmOrderRequest.setChannelId(request.getChannelId());
                    poiConfirmOrderRequest.setOrderId(request.getOrderId());
                    poiConfirmOrderRequest.setIsAgreed(false);
                    poiConfirmOrderRequest.setOperator(request.getOperatorId());
                    poiConfirmOrderRequest.setStoreId(request.getStoreId());

                    return poiConfirmOrder(poiConfirmOrderRequest);
                } else {
                    // 京东取消订单只允许在自配送场景下发生
                    // 其他状态调用订单取消接口
                    //组装请求参数
                    BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
                    addStoreId2BaseRequest(request.getStoreId(), baseRequest);

                    JddjCancelAndRefundParam jddjCancelAndRefundParam = new JddjCancelAndRefundParam();
                    jddjCancelAndRefundParam.setOperPin(request.getOperatorId());
                    jddjCancelAndRefundParam.setOperRemark(request.getReason());
                    jddjCancelAndRefundParam.setOrderId(Long.valueOf(request.getOrderId()));
                    jddjCancelAndRefundParam.setOperTime(DateUtils.currentTimeDefault());

                    ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.CANCEL_AND_REFUND, baseRequest, jddjCancelAndRefundParam);

                    if (channelResponseDTO.getDataResponse().isSuccess()) {
                        return ResultGenerator.genSuccessResult().setData(channelResponseDTO.getDataResponse().getResultData());
                    }

                    String msg = channelResponseDTO.getErrorMsg();

                    if (channelResponseDTO.getDataResponse().getDetail() != null) {
                        msg = msg + ":" + channelResponseDTO.getDataResponse().getDetail();
                    }

                    return ResultGenerator.genFailResult(msg);
                }
            } catch (Exception e) {
                log.error("jddj.poiCancelOrder ERROR!", e);
                return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
            }


        } catch (Exception e) {
            log.error("jddj.poiCancelOrder ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request) {


        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        addStoreId2BaseRequest(request.getStoreId(), baseRequest);

        JddjReverseOrderParam jddjReverseOrderParam = new JddjReverseOrderParam();
        jddjReverseOrderParam.setOrderId(request.getOrderId());
        List<JddjReverseOrderParam.RefDiffAdjustSku> refDiffAdjustSkus = new ArrayList<>();
        jddjReverseOrderParam.setRefDiffAdjustSkuList(refDiffAdjustSkus);
        JddjReverseOrderParam.RefDiffAdjustSku refDiffAdjustSku = new JddjReverseOrderParam.RefDiffAdjustSku();

        refDiffAdjustSku.setOutSkuId(request.getSkuId());
        refDiffAdjustSku.setSkuActualWeight(refDiffAdjustSku.getSkuActualWeight());
        refDiffAdjustSkus.add(refDiffAdjustSku);


        ChannelResponseDTO<JddjReverseOrderResultData> refundResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.REVERSE_ORDER, baseRequest, jddjReverseOrderParam);

        log.info("poiPartRefundCalculate, request:{}, getResult:{}", request, refundResult);

        if (Objects.isNull(refundResult) || refundResult.getCoreData() == null) {
            return buildCalculateResult(ResultGenerator.genResult(ResultCode.UNKNOWN_ERROR), null);
        } else if (!refundResult.getDataResponse().isSuccess()) {
            return buildCalculateResult(ResultGenerator.genFailResult(refundResult.getErrorMsg()), null);
        }

        String orderId = refundResult.getCoreData().getOrderId();
        String refDiffReverseOrderId = refundResult.getCoreData().getRefDiffReverseOrderId();

        Map<String, String> params = new HashMap<>();

        params.put("orderId", orderId);
        params.put("refDiffReverseOrderId", refDiffReverseOrderId);

        ChannelResponseDTO<JddjQueryReverseResultData> queryResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.QUERY_REVERSE_ORDER, baseRequest, params);

        if (Objects.isNull(queryResult) || queryResult.getCoreData() == null) {
            return buildCalculateResult(ResultGenerator.genResult(ResultCode.UNKNOWN_ERROR), null);
        } else if (!queryResult.getDataResponse().isSuccess()) {
            return buildCalculateResult(ResultGenerator.genFailResult(queryResult.getErrorMsg()), null);
        }


        return buildCalculateResult(ResultGenerator.genSuccessResult(), queryResult.getCoreData());
    }

    private CalculateRefundResult buildCalculateResult(ResultStatus status, JddjQueryReverseResultData resultData) {
        CalculateRefundResult result = new CalculateRefundResult();
        result.setStatus(status);
        if (Objects.nonNull(resultData)) {
            CalculateRefundDTO calculateRefundDTO = new CalculateRefundDTO();
            calculateRefundDTO.setAppFoodCode(resultData.getOrderId());
            BigDecimal refundMoneyFen = new BigDecimal(resultData.getRefDiffMoney());
            calculateRefundDTO.setRefundMoney(refundMoneyFen.divide(new BigDecimal(100)).doubleValue());
            // TODO 检查这里用的哪个SKUID
            calculateRefundDTO.setSkuId(resultData.getRefDiffAdjustSkuList().get(0).getSkuId());
            result.setCalculateRefundDTO(calculateRefundDTO);
        }
        return result;
    }

    @Override
    public ResultStatus poiPartRefundApply(PoiPartRefundRequest request) {
        try {
            //获取渠道订单
            ChannelOrderListDetail channelOrderListDetail = getChannelOrderDetail(request.getChannelId(), request.getOrderId(), request.getTenantId(), request.getStoreId());
            if (channelOrderListDetail == null || CollectionUtils.isEmpty(channelOrderListDetail.getResultList())) {
                return ResultGenerator.genFailResult("渠道订单不存在");
            }
            if (ProjectConstant.MT_WEIGHT_REFUND_TYPE == request.getPartRefundType()) {
                return weightRefundApply(request, channelOrderListDetail.getResultList().get(0));
            }


            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                    .setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);

            PoiCommitAfterSaleDTO poiCommitAfterSaleDTO = jddjConverterService.poiRefundMapping(request, channelOrderListDetail.getResultList().get(0));
            log.info("poiPartRefundApply.poiRefundMapping 后:{}",poiCommitAfterSaleDTO);
            ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.POI_COMMIT_AFTERSALE, baseRequest, poiCommitAfterSaleDTO);
            if (channelResponseDTO.getDataResponse().isSuccess()) {
                return ResultGenerator.genSuccessResult().setData(channelResponseDTO.getDataResponse().getResultData());
            }
            return ResultGenerator.genFailResult(channelResponseDTO.getDataResponse().getMsg());
        } catch (Exception e) {
            log.error("jddj.poiPartRefundApply ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    private ChannelResponseDTO<JddjReverseOrderResultData> executeWeightRefundApply(PoiPartRefundRequest request, ChannelOrderDetail channelOrderDetail){
        log.info("poiPartRefundCalculate, request:{}", request);
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        addStoreId2BaseRequest(request.getStoreId(), baseRequest);

        JddjReverseOrderParam jddjReverseOrderParam = new JddjReverseOrderParam();
        jddjReverseOrderParam.setSpecialLogicFlag(1);
        jddjReverseOrderParam.setOrderId(request.getOrderId());
        List<JddjReverseOrderParam.RefDiffAdjustSku> refDiffAdjustSkus = new ArrayList<>();
        jddjReverseOrderParam.setRefDiffAdjustSkuList(refDiffAdjustSkus);
        Map<String, List<RefundProductInfoDTO>> customerSkuID2RefundProductInfo = request.getRefundProducts().stream()
                .collect(Collectors.groupingBy(RefundProductInfoDTO::getCustomSkuId));
        for (Map.Entry<String, List<RefundProductInfoDTO>> entry: customerSkuID2RefundProductInfo.entrySet()){
            if (CollectionUtils.isEmpty(entry.getValue())){
                continue;
            }
            Optional<Double> channelWeight = channelOrderDetail.getProduct().stream().filter(v->Objects.equals(v.getSkuIdIsv(), entry.getKey())).map(OrderSkuDetail::getSkuWeight).findFirst();
            if (!channelWeight.isPresent()){
                continue;
            }
            int skuChannelWeight = BigDecimal.valueOf(channelWeight.get()).multiply(BigDecimal.valueOf(1000)).intValue();
            JddjReverseOrderParam.RefDiffAdjustSku refDiffAdjustSku = new JddjReverseOrderParam.RefDiffAdjustSku();
            refDiffAdjustSku.setOutSkuId(entry.getKey());
            refDiffAdjustSku.setSkuCutWeight(entry.getValue().stream().map(v-> skuChannelWeight - v.actualWeight).reduce(0D, Double::sum));
            refDiffAdjustSkus.add(refDiffAdjustSku);
        }
        return jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.REVERSE_ORDER, baseRequest, jddjReverseOrderParam);
    }

    private ResultStatus weightRefundApply(PoiPartRefundRequest request, ChannelOrderDetail channelOrderDetail) {
        ChannelResponseDTO<JddjReverseOrderResultData> refundResult = executeWeightRefundApply(request, channelOrderDetail);
        log.info("请求{},退差渠道返回数据,{}", request, refundResult);
        if (Objects.isNull(refundResult) || refundResult.getCoreData() == null ||Objects.isNull(refundResult.getDataResponse())) {
            if (Objects.nonNull(refundResult) && Objects.nonNull(refundResult.getDataResponse()) && Objects.nonNull(refundResult.getDataResponse().getMsg())){
                return ResultGenerator.genFailResult(refundResult.getDataResponse().getMsg());
            }
            return ResultGenerator.genFailResult(ResultCode.UNKNOWN_ERROR.getMsg());
        } else if (!refundResult.getDataResponse().isSuccess()) {
            return ResultGenerator.genFailResult(refundResult.getErrorMsg());
        }
        String refDiffReverseOrderId = refundResult.getCoreData().getRefDiffReverseOrderId();
        return generateWeightAfterSaleApply(request, refDiffReverseOrderId, channelOrderDetail);
    }

    private ResultStatus generateWeightAfterSaleApply(PoiPartRefundRequest request, String refDiffReverseOrderId, ChannelOrderDetail channelOrderDetail) {
        Map<String, String> params = new HashMap<>();
        params.put("orderId", request.getOrderId());
        params.put("refDiffReverseOrderId", refDiffReverseOrderId);
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        addStoreId2BaseRequest(request.getStoreId(), baseRequest);
        ChannelResponseDTO<List<JddjQueryReverseResultData>> queryResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.QUERY_REVERSE_ORDER, baseRequest, params);
        if (Objects.isNull(queryResult) || queryResult.getCoreData() == null) {
            log.error("查询京东退差单错误单号：{}，退差单：{}, 响应：{}", request.getOrderId(), refDiffReverseOrderId, queryResult );
            return ResultGenerator.genFailResult(ResultCode.UNKNOWN_ERROR.getMsg());
        } else if (!queryResult.getDataResponse().isSuccess()) {
            log.error("查询京东退差单错误单号：{}，退差单：{}, 响应：{}", request.getOrderId(), refDiffReverseOrderId, queryResult );
            return ResultGenerator.genFailResult(queryResult.getErrorMsg());
        }
        return doWeightRefund(request, refDiffReverseOrderId, queryResult.getCoreData(), channelOrderDetail);
    }

    private ResultStatus doWeightRefund(PoiPartRefundRequest request, String refDiffReverseOrderId, List<JddjQueryReverseResultData> coreData, ChannelOrderDetail channelOrderDetail) {
        JddjQueryReverseResultData refundData =  coreData.get(0);
        OrderPartRefundRequest param = new OrderPartRefundRequest();
        param.setTenantId(request.getTenantId());
        param.setChannelType(ChannelTypeEnum.JD2HOME.getCode());
        param.setOrderPartRefundType(OrderPartRefundType.APPLY.getValue());
        param.setSponsor(OrderRefundSponsor.TENANT.getValue());
        param.setRefundId(refDiffReverseOrderId);
        param.setReason(request.getReason());
        param.setChannelOrderId(request.getOrderId());
        param.setAfsApplyType(AfsApplyType.ON_SALE.getValue());
        param.setRefundApplyTime(ConverterUtils.stringToMillis(refundData.getCreatTime()));
        param.setIsAppeal(false);

        BizAfterSaleCreateOrUpdateRequest bizAfterSaleCreateOrUpdateRequest = buildBizAfterSaleCreateOrUpdateRequest(param, refundData, channelOrderDetail);
        BizAfterSaleCreateOrUpdateResponse response = channelOrderThriftServiceProxy.orderAfterSaleNotify(bizAfterSaleCreateOrUpdateRequest);
        if (Objects.isNull(response)) {
            return ResultGenerator.genResult(ResultCode.FAIL, "推送克重退款消息失败").setData(ProjectConstant.NG);
        }
        //完结退单
        bizAfterSaleCreateOrUpdateRequest.setOperateType(OrderPartRefundType.TENANT_AGREE.getValue());
        response = channelOrderThriftServiceProxy.orderAfterSaleNotify(bizAfterSaleCreateOrUpdateRequest);

        if (response.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue()) {
            //记录渠道克重退轨迹
            ChannelTypeEnum channelTypeEnum = EnumUtil.getEnumByCode(request.getChannelId(), ChannelTypeEnum.class);
            OrderBizTypeEnum orderBizTypeEnum = OrderConvertUtils.convertBizType(channelTypeEnum);

            OrderTrackEvent orderTrackEvent = new OrderTrackEvent();
            orderTrackEvent.setTrackSource(TrackSource.CHANNEL.getType());
            orderTrackEvent.setTrackOpType(TrackOpType.ORDER_TENANT_WEIGHT_REFUND_SUCCESS.getOpType());
            orderTrackEvent.setOperateTime(Instant.now().toEpochMilli());
            orderTrackEvent.setTenantId(request.getTenantId());
            orderTrackEvent.setOrderBizType(orderBizTypeEnum == null ? null : orderBizTypeEnum.getValue());
            orderTrackEvent.setUnifyOrderId(channelOrderDetail.getOrderId());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("afterSaleId", refDiffReverseOrderId);
            orderTrackEvent.setExt(jsonObject.toJSONString());
            orderTrackService.sendAsyncMessage(orderTrackEvent);
            return ResultGenerator.genSuccessResult().setData(ProjectConstant.OK);
        }
        return ResultGenerator.genResult(ResultCode.FAIL, "推送克重退款消息失败").setData(ProjectConstant.NG);
    }

    private BizAfterSaleCreateOrUpdateRequest buildBizAfterSaleCreateOrUpdateRequest(OrderPartRefundRequest partParam, JddjQueryReverseResultData coreData, ChannelOrderDetail channelOrderDetail) {
        BizAfterSaleCreateOrUpdateRequest request = new BizAfterSaleCreateOrUpdateRequest();
        request.setTenantId(partParam.getTenantId());
        request.setChannelId(partParam.getChannelType());
        request.setShopId(copChannelStoreService.selectChannelStoreId(partParam.getTenantId(), ChannelTypeEnum.JD2HOME.getCode(), channelOrderDetail.getDeliveryStationNoIsv()));
        request.setOrderViewId(partParam.getChannelOrderId());
        request.setAfterSaleId(partParam.getRefundId());
        request.setReason(partParam.getReason());
        request.setSponsor(partParam.getSponsor());
        request.setAppeal(partParam.isIsAppeal());
        request.setAfsApplyType(partParam.getAfsApplyType());
        request.setAfsApplyPattern(AfterSalePatternEnum.WEIGHT.getValue());
        request.setOperateTime(partParam.getRefundApplyTime());
        request.setOperateType(partParam.getOrderPartRefundType());
        request.setOrderSource(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue());
        request.setRefundAmount(JddjConvertUtil.parseLong2Int(coreData.getRefDiffMoney()));
        List<BizOrderItemAfterSaleCreateModel> orderItems = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(coreData.getRefDiffAdjustSkuList())){
            for (JddjQueryReverseResultData.RefDiffAdjustSkuVO refundSku : coreData.getRefDiffAdjustSkuList()){
                BizOrderItemAfterSaleCreateModel bizOrderItemAfterSaleCreateModel = new BizOrderItemAfterSaleCreateModel();
                bizOrderItemAfterSaleCreateModel.setCustomerSkuId(refundSku.getOutSkuId());
                bizOrderItemAfterSaleCreateModel.setSkuName(refundSku.getWareName());
                bizOrderItemAfterSaleCreateModel.setQuantity(0);
                bizOrderItemAfterSaleCreateModel.setBoxAmt(0);
                bizOrderItemAfterSaleCreateModel.setPartialRefundCount(BigDecimal.valueOf(refundSku.getSkuCutWeight())
                        .divide(BigDecimal.valueOf(refundSku.getSkuWeight()), 3, RoundingMode.HALF_UP)
                        .doubleValue());
                //退货重量为整数
                bizOrderItemAfterSaleCreateModel.setRefundWeight(BigDecimal.valueOf(Optional.ofNullable(refundSku.getSkuCutWeight()).orElse(0D)).setScale(0, RoundingMode.HALF_UP).doubleValue());
                bizOrderItemAfterSaleCreateModel.setCustomSkuIdVersion(BizOrderItemCreateForMiddleModel.CUSTOM_SKU_VERSION_SPU);
                bizOrderItemAfterSaleCreateModel.setRefundAmt(JddjConvertUtil.parseLong2Int(refundSku.getCashMoney()));
                bizOrderItemAfterSaleCreateModel.setFoodPrice(JddjConvertUtil.parseLong2Int(refundSku.getPayPrice()));
                bizOrderItemAfterSaleCreateModel.setRefundPlatItemPromotion(JddjConvertUtil.parseLong2Int(refundSku.getPlatPayMoney()));
                bizOrderItemAfterSaleCreateModel.setRefundPoiItemPromotion(JddjConvertUtil.parseLong2Int(refundSku.getVenderPayMoney()));
                orderItems.add(bizOrderItemAfterSaleCreateModel);
                if (CollectionUtils.isEmpty(refundSku.getRefDiffAdjustSkuDiscountList())){
                    continue;
                }
                Long platPromotion = refundSku.getRefDiffAdjustSkuDiscountList().stream()
                        .map(JddjQueryReverseResultData.RefDiffAdjustSkuDiscountVO::getPlatPayMoney)
                        .filter(Objects::nonNull)
                        .reduce(0L, Long::sum);
                Long poiPromotion = refundSku.getRefDiffAdjustSkuDiscountList().stream()
                        .map(JddjQueryReverseResultData.RefDiffAdjustSkuDiscountVO::getVenderPayMoney)
                        .filter(Objects::nonNull)
                        .reduce(0L, Long::sum);
                bizOrderItemAfterSaleCreateModel.setRefundPlatOrderPromotion(JddjConvertUtil.parseLong2Int(platPromotion));
                bizOrderItemAfterSaleCreateModel.setRefundPoiOrderPromotion(JddjConvertUtil.parseLong2Int(poiPromotion));
            }
        }
        request.setProducts(orderItems);
        return request;
    }
    ChannelOrderListDetail getChannelOrderDetail(int channelId, String channelOrderId, long tenantId, long storeId, long appId) {
        GetChannelOrderDetailRequest channelOrderDetailRequest = new GetChannelOrderDetailRequest();
        channelOrderDetailRequest.setChannelId(channelId);
        channelOrderDetailRequest.setOrderId(channelOrderId);
        channelOrderDetailRequest.setTenantId(tenantId);
        channelOrderDetailRequest.setSotreId(storeId);
        channelOrderDetailRequest.setAppId(appId);
        ChannelResponseDTO<ChannelOrderListDetail> channelOrderDetailResult = doGetChannelOrder(channelOrderDetailRequest);
        return channelOrderDetailResult.getDataResponse().getCoreData();
    }


    ChannelOrderListDetail getChannelOrderDetail(int channelId, String channelOrderId, long tenantId, long storeId) {
        GetChannelOrderDetailRequest channelOrderDetailRequest = new GetChannelOrderDetailRequest();
        channelOrderDetailRequest.setChannelId(channelId);
        channelOrderDetailRequest.setOrderId(channelOrderId);
        channelOrderDetailRequest.setTenantId(tenantId);
        channelOrderDetailRequest.setSotreId(storeId);
        ChannelResponseDTO<ChannelOrderListDetail> channelOrderDetailResult = doGetChannelOrder(channelOrderDetailRequest);
        return channelOrderDetailResult.getDataResponse().getCoreData();
    }

    @Override
    public ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);
            PoiReceiveRefundGoodsDTO poiConfirmOrderDTO = jddjConverterService.poiConfirmReceiveGoods(request);
            ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.POI_RECEIVER_REFUND_GOODS, baseRequest, poiConfirmOrderDTO);
            if (channelResponseDTO.getDataResponse().isSuccess()) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(channelResponseDTO.getDataResponse().getMsg());
        } catch (Exception e) {
            log.error("jddj.poiConfirmReceiveGoods ERROR!", e);
            return ResultGenerator.genResult(ResultCode.FAIL, ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }


    @Override
    public ResultStatus verifySelfFetchCode(VerifySelfFetchCodeRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getStoreId()));
            JDverifySelfFetchCodeParam bizParam = jddjConverterService.verifySelfFetchCodeMapping(request);
            if (StringUtils.isBlank(bizParam.getOperPin())) {
                bizParam.setOperPin("未知用户");
            }
            ChannelResponseDTO<String> resultData = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.ORDER_SELF_PICK_CHECKOUT, baseRequest, bizParam);
            if (resultData.getDataResponse().isSuccess()) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(resultData.getErrorMsg());
        } catch (Exception e) {
            log.error("JddjChannelOrderServiceImpl verifySelfFetchCode ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }


    @Override
    public PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);

            PoiAdjustOrderDTO poiAdjustOrder = jddjConverterService.poiAdjustOrder(request);
            ChannelResponseDTO<JdAfterAdjustOrderInfoDTO> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.POI_ADJUST_GOODS, baseRequest, poiAdjustOrder);
            if (channelResponseDTO.getDataResponse().isSuccess()) {
                AfterAdjustOrderInfo afterAdjustOrderInfo = jddjConverterService.afterAdjustOrderInfo(channelResponseDTO.getDataResponse().getCoreData());
                PoiAdjustOrderResult result = new PoiAdjustOrderResult();
                result.setStatus(ResultGenerator.genSuccessResult());
                result.setAfterAdjustOrderInfo(afterAdjustOrderInfo);
                return result;
            }
            //本次调整，如果渠道调用异常不能阻塞后续流程
            log.info("JddjChannelOrderServiceImpl.getSuccessPoiAdjustOrderResult pretend to succeed orderId: {}, channelResponseDTO: {}", request.getOrderId(), JSON.toJSONString(channelResponseDTO));
            return getSuccessPoiAdjustOrderResult();
            //判断是不是已经调整过了
            /*
            PoiAdjustOrderResult result = new PoiAdjustOrderResult();
            if (String.valueOf(10010).equals(channelResponseDTO.getDataResponse().getCode())) {
                ResultStatus status = new ResultStatus(ResultCodeEnum.ORDER_HAS_ADJUSTED.getValue(), channelResponseDTO.getCoreData().getMsg(), null);
                result.setStatus(status);
                return result;
            }
            return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, channelResponseDTO.getDataResponse().getMsg()));
            */
        } catch (Exception e) {
            PoiAdjustOrderResult result = new PoiAdjustOrderResult();
            log.error("jddj.poiConfirmReceiveGoods ERROR!", e);
            return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }
    }

    private PoiAdjustOrderResult getSuccessPoiAdjustOrderResult() {
        PoiAdjustOrderResult result = new PoiAdjustOrderResult();
        result.setStatus(ResultGenerator.genSuccessResult());
        result.setAfterAdjustOrderInfo(new AfterAdjustOrderInfo());
        return result;
    }


    @Override
    public OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request) {
        OrderShouldSettlementResult result = new OrderShouldSettlementResult();
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId()).setAppId(request.getAppId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);
            JDOrderShouldSettlementDetailParam param = jddjConverterService.shouldSettlementMapping(request);
            ChannelResponseDTO<JDOrderShouldSettlementInfo> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.ORDER_SHOULD_SETTLEMENT_INFO, baseRequest, param);

            JDOrderShouldSettlementInfo data = null;
            if (channelResponseDTO.isSuccess() && (data = channelResponseDTO.getDataResponse().getResultData()) != null) {
                OrderShouldSettlementInfo settlementInfo = new OrderShouldSettlementInfo();
                settlementInfo.setOrderId(data.getOrderId() + "");
                settlementInfo.setBillTime(data.getBillTime() != null ? data.getBillTime() : 0L);
                settlementInfo.setSettlementAmount(data.getSettlementAmount());// 元转分
                settlementInfo.setUserActualPaidGoodsMoney(data.getUserActualPaidGoodsMoney());

                Map<String, Long> detailItem = Maps.newHashMap();
                detailItem.put("platOrderGoodsDiscountMoney", Long.valueOf(data.getPlatOrderGoodsDiscountMoney()));
                detailItem.put("platSkuGoodsDiscountMoney", Long.valueOf(data.getPlatSkuGoodsDiscountMoney()));
                detailItem.put("venderOrderGoodsDiscountMoney", Long.valueOf(data.getVenderOrderGoodsDiscountMoney()));
                detailItem.put("venderSkuGoodsDiscountMoney", Long.valueOf(data.getVenderSkuGoodsDiscountMoney()));
                detailItem.put("venderFreightDiscountMoney", Long.valueOf(data.getVenderFreightDiscountMoney()));
                detailItem.put("goodsCommission", Long.valueOf(data.getGoodsCommission()));
                detailItem.put("freightCommission", Long.valueOf(data.getFreightCommission()));
                detailItem.put("packageCommission", Long.valueOf(data.getPackageCommission()));
                detailItem.put("guaranteedCommission", Long.valueOf(data.getGuaranteedCommission()));
                detailItem.put("venderDeliveryFreight", Long.valueOf(data.getVenderDeliveryFreight()));
                detailItem.put("platFreightDiscountMoney", Long.valueOf(data.getPlatFreightDiscountMoney()));
                detailItem.put("packageMoney", Long.valueOf(data.getPackageMoney()));
                detailItem.put("distanceFreightMoney", Long.valueOf(data.getDistanceFreightMoney()));
                detailItem.put("venderPaidTips", Long.valueOf(data.getVenderPaidTips()));
                detailItem.put("orderCashOnDeliveryMoney", Long.valueOf(data.getOrderCashOnDeliveryMoney()));
                detailItem.put("orderGiftCardMoney", Long.valueOf(data.getOrderGiftCardMoney()));
                settlementInfo.setBillDetailItem(detailItem);

                settlementInfo.setTotalGoodsCommission(data.getGoodsCommission());
                settlementInfo.setFreightCommission(data.getFreightCommission());
                settlementInfo.setGuaranteedCommission(data.getGuaranteedCommission());
                settlementInfo.setPlatFreightDiscountMoney(data.getPlatFreightDiscountMoney());
                settlementInfo.setVenderDeliveryFreight(data.getVenderDeliveryFreight());
                settlementInfo.setVenderPaidTips(data.getVenderPaidTips());
                settlementInfo.setVenderFreightDiscountMoney(data.getVenderFreightDiscountMoney());
                settlementInfo.setDistanceFreightMoney(data.getDistanceFreightMoney());
                settlementInfo.setBaseServiceMoney(data.getBaseServiceMoney());

                result.setData(settlementInfo);
                result.setStatus(ResultGenerator.genSuccessResult());
                return result;
            }
            return result.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg()));
        } catch (Exception e) {
            log.error("jddjChannelOrderService queryOrderShouldSettlementInfo ERROR!", e);
            return result.setStatus(ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }
    }

    Map<Long, String> channelSkuId2CustomerSkuId(List<Long> channelSkuIdList, long tenantId, String channelOrderId,long storeId) {
        ChannelOrderListDetail orderListDetail = getChannelOrderDetail(ChannelType.JD2HOME.getValue(), channelOrderId, tenantId,storeId);
        ChannelOrderDetail orderDetail = orderListDetail.getResultList().get(0);
        return channelSkuIdList.stream().distinct().collect(
                Collectors.toMap(
                        channelSkuId -> channelSkuId, //key -> channelSkuId
                        channelSkuId -> Optional.ofNullable(orderDetail.getProduct().stream().filter(e -> e.getSkuId().equals(channelSkuId)).findAny().orElse(new OrderSkuDetail()).getSkuIdIsv()).orElse(StringUtils.EMPTY))); //value skuIdIsv

    }


    Map<Long, OrderSkuDetail> channelSkuId2SkuDetailInfo(long tenantId, String channelOrderId,long storeId) {
        ChannelOrderListDetail orderListDetail = getChannelOrderDetail(ChannelType.JD2HOME.getValue(), channelOrderId, tenantId,storeId);
        ChannelOrderDetail orderDetail = orderListDetail.getResultList().get(0);
        return orderDetail.getProduct().stream().distinct().collect(
                Collectors.toMap(
                        OrderSkuDetail::getSkuId, //key -> channelSkuId
                        Function.identity(), (v1, v2) -> v2)); //value skuIdIsv

    }

    @Override
    public GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request) {
        GoodsSettlementResult result = new GoodsSettlementResult();
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId()).setAppId(request.getAppId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);
            JDGoodsSettlementDetailParam param = jddjConverterService.goodsSettlementMapping(request);
            ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.GOODS_SETTLEMENT_INFO, baseRequest, param);
            List<JDGoodsSettlementInfo> jdSettlementInfos = JSON.parseArray(channelResponseDTO.getDataResponse().getData(), JDGoodsSettlementInfo.class);
            if (jdSettlementInfos != null) {
                //需要将渠道skuId转成customSkuId
                List<Long> channelSkuIdList = jdSettlementInfos.stream().map(e -> e.getSkuId()).collect(Collectors.toList());

                ChannelOrderListDetail orderListDetail = getChannelOrderDetail(ChannelType.JD2HOME.getValue(), request.getOrderId(), request.getTenantId(),request.getStoreId());
                ChannelOrderDetail orderDetail = orderListDetail.getResultList().get(0);
                Map<Long, String> customSkuMap = channelSkuIdList.stream().distinct().collect(
                        Collectors.toMap(
                                channelSkuId -> channelSkuId, //key -> channelSkuId
                                channelSkuId -> Optional.ofNullable(orderDetail.getProduct().stream().filter(e -> e.getSkuId().equals(channelSkuId)).findAny().orElse(new OrderSkuDetail()).getSkuIdIsv()).orElse(StringUtils.EMPTY))); //value skuIdIsv

                List<ActivityShareDetailDTO> activityShareDetailDTOList = splitSkuPromotionCatchException(jdSettlementInfos, orderDetail, customSkuMap, request.getTenantId());
                result.setActivityShareDetailDTOList(activityShareDetailDTOList);

                //订单纬度活动拆分
                List<GoodsActivityDetailDTO> goodsActivityDetailDTOS = jdSettlementInfos.stream().map(e -> jddjConverterService.convertGoodsActivityDetail(e, customSkuMap)).collect(Collectors.toList());
                result.setStatus(ResultGenerator.genSuccessResult());
                result.setGoodSettlementInfos(goodsActivityDetailDTOS);
                // 运费优惠
                if(MccConfigUtil.checkJdOrderActivityShareUpdateFreightDiscountTenants(request.getTenantId())) {
                    // 运费活动列表
                    result.setFreightActivityDTOList(orderFreightActivitieInfoList(request.getTenantId(), orderDetail, jdSettlementInfos));
                }
                // mock处理上线删除
                //mockOrderAndRefundPromotion(result, request.getTenantId());
            } else {
                result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, channelResponseDTO.getDataResponse().getErrorCode() + ":" + channelResponseDTO.getDataResponse().getErrorInfos()));
            }

        } catch (Exception e) {
            log.error("elmChannelOrderService queryGoodsSettlementInfo ERROR!", e);
            return result.setStatus(ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }

        return result;
    }

    private List<OrderDiscountDetailDTO> orderFreightActivitieInfoList(Long tenantId, ChannelOrderDetail orderDetail, List<JDGoodsSettlementInfo> jdSettlementInfos) {
        try {
            List<OrderDiscountDetailDTO> activities = new ArrayList<>();
            // 检查该租户是否支持京东运费优惠记录写入调整
            if(MccConfigUtil.checkJdFreightPromotionRecordTenants(tenantId)) {
                Map<String, OrderDiscountDetailDTO> bigOrdermap = new HashMap<>();
                if(CollectionUtils.isNotEmpty(jdSettlementInfos)) {
                    //装整品促销数据 促销号进行统计，如果有相同的则进行叠加数据
                    for (JDGoodsSettlementInfo jdGoodsSettlementInfo : jdSettlementInfos) {
                        if (Objects.isNull(jdGoodsSettlementInfo)) {
                            continue;
                        }
                        //拆分运费优惠信息，主要是8，15，16活动
                        splitLogisticsPreferential(bigOrdermap, jdGoodsSettlementInfo.getFreightDiscountList());
                    }
                }
                // 使用订单详情拆分运费优惠，涉及活动7，12，18都为平台优惠
                splitOrderDetailLogisticsPreferential(bigOrdermap, orderDetail.getDiscount());
                if (!bigOrdermap.isEmpty()) {
                    activities.addAll(new ArrayList<>(bigOrdermap.values()));
                }
            }
            log.info("查询运费促销活动赋值 activities: {}", activities);
            return activities;

        }catch (Exception e){
            log.info("京东处理订单运费活动促销 JddjChannelOrderServiceImpl.orderFreightActivitieInfoList error: ", e);
            return new ArrayList<>();
        }
    }

    /**
     * 捕捉异常，避免影响订单入库。
     */
    private List<ActivityShareDetailDTO> splitSkuPromotionCatchException(List<JDGoodsSettlementInfo> jdSettlementInfoList, ChannelOrderDetail orderDetail, Map<Long, String> customSkuMap, long tenantId) {
        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        try {
            if (!MccConfigUtil.getActivityShareSwitch()) {
                log.info("活动优惠拆分功能控制为false");
                return activityShareDetailDTOList;
            }
            // 单品优惠
            activityShareDetailDTOList.addAll(splitSkuPromotion(jdSettlementInfoList, orderDetail, customSkuMap));
            // 整单优惠
            activityShareDetailDTOList.addAll(splitOrderPromotion(jdSettlementInfoList, orderDetail, customSkuMap));
            // 运费优惠
            activityShareDetailDTOList.addAll(splitLogisticPromotion(orderDetail, customSkuMap, tenantId));
            if(MccConfigUtil.checkJdOrderActivityShareUpdateFreightDiscountTenants(tenantId)) {
                // 运费优惠（订单金额拆分接口-freightDiscountList：类型为 8、15、16的优惠）
                activityShareDetailDTOList.addAll(splitPlatAndPoiLogisticsPromotion(jdSettlementInfoList, orderDetail, customSkuMap));
            }
        } catch (Exception ex) {
            log.error("活动分摊异常", ex);
        }
        return activityShareDetailDTOList;
    }


    /**
     * 运费优惠
     */
    private List<ActivityShareDetailDTO> splitLogisticPromotion(ChannelOrderDetail orderDetail, Map<Long, String> customSkuMap, Long tenantId) {
        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        List<OrderActivitiesInfo> discountList = orderDetail.getDiscount();

        boolean tenantSwitch = MccConfigUtil.checkJdOrderActivityShareUpdateFreightDiscountTenants(tenantId);
        if (CollectionUtils.isNotEmpty(discountList)) {
            for (OrderActivitiesInfo discount : discountList) {
                log.info("开始运费优惠拆分，discount={}。", discount);
                // 渠道给的单位是元，这里之前已经转为分
                int poi_charge = 0;
                int mt_charge = 0;
                String activityId = "";

                if (tenantSwitch) {
                    // 只处理平台运费活动
                    if (!JddjOrderConvertUtil.isPlatformLogisticsActivity(discount.getDiscountType())) {
                        continue;
                    }

                    mt_charge = Optional.ofNullable(discount.getDiscountPrice()).map(Long::intValue).orElse(0);
                    activityId = getPlatLogisticsPromotionActivityId(discount.getOrderShareRatioData());
                } else {
                    // 只处理运费活动
                    if (!JddjOrderConvertUtil.isLogisticsActivity(discount.getDiscountType())) {
                        continue;
                    }

                    // 看京东订单详情接口文档，运费discountType=15 or 16 类型时才直接返回字段venderPayMoney、platPayMoney,否则需要解析字段orderShareRatioData，获取金额。
                    if (discount.getVenderPayMoney() != null) {
                        poi_charge = getIntegerValue(discount.getVenderPayMoney());
                    }
                    if (discount.getPlatPayMoney() != null) {
                        mt_charge = getIntegerValue(discount.getPlatPayMoney());
                    }

                    String orderShareRatioData = discount.getOrderShareRatioData();
                    if (StringUtils.isNotBlank(orderShareRatioData)) {
                        String[] dataSplit = orderShareRatioData.split(ProjectConstant.AND_STR);
                        for (int j = 0; j < dataSplit.length; j++) {
                            String[] ratioData = dataSplit[j].split(ProjectConstant.EQUAL_STR);
                            String entryName = ratioData[0];
                            String entryValue = ratioData[1];
                            // 解析促销活动ID
                            if (StringUtils.endsWithIgnoreCase(entryName, ProjectConstant.PROMOTION_ID)) {
                                activityId = entryValue;
                                continue;
                            }

                            //商家分摊活动金额
                            if (StringUtils.equalsIgnoreCase(entryName, ProjectConstant.VENDER_PAY_MONEY)) {
                                // venderPayMoney 为空才覆盖
                                if (poi_charge == 0) {
                                    poi_charge = Integer.parseInt(entryValue);
                                }
                                continue;
                            }

                            // 平台分摊活动金额
                            if (StringUtils.equalsIgnoreCase(entryName, ProjectConstant.PLAT_PAY_MONEY)) {
                                if (mt_charge == 0) {
                                    mt_charge = Integer.parseInt(entryValue);
                                }
                            }
                        }
                    }
                }

                // 计算商品总的实际价格，即优惠后。订单商品销售价总金额，等于sum（京东到家销售价skuJdPrice*商品下单数量skuCount）
                int totalActualSalePrice = orderDetail.getOrderTotalMoney();

                // 京东渠道，同一skuid因为有优惠导致售价不同，所以同一个shkuid会有多条明细，每条明细售价不同，根据sku进行分组。
                Map<Long, OrderSkuDetail> skuToDetailMap = new HashMap<>();
                String skuIds = orderDetail.getProduct().stream().map(OrderSkuDetail::getSkuId).filter(Objects::nonNull).map(String::valueOf)
                        .collect(Collectors.joining(","));
                for (OrderSkuDetail orderSkuDetail : orderDetail.getProduct()) {
                    if (StringUtils.isEmpty(discount.getSkuIds())) {
                        List<Long> tenantConfig = MccConfigUtil.getActivityShareAddSkuTenantConfig();
                        boolean allTenant =
                                CollectionUtils.isNotEmpty(tenantConfig) && tenantConfig.size() == 1 && Objects.equals(tenantConfig.get(0), -1L);
                        boolean currentTenant = CollectionUtils.isNotEmpty(tenantConfig) && tenantConfig.contains(tenantId);
                        if (Objects.equals(orderDetail.getSrcOrderType(), "20") && (allTenant || currentTenant)) {
                            /* 判断是物竞天择的订单 */
                            log.info("当前订单优惠的sku为空，订单号：{}，discount：{}，进行明细所有订单号填充：[{}]", orderDetail.getOrderId(),
                                    discount, skuIds);
                            discount.setSkuIds(skuIds);
                        } else {
                            log.info("当前订单优惠的sku为空，订单号：{}，discount：{}", orderDetail.getOrderId(), discount);
                            continue;
                        }
                    }
                    // skuid 是否参加此优惠
                    if (!discount.getSkuIds().contains(String.valueOf(orderSkuDetail.getSkuId()))) {
                        continue;
                    }

                    if (skuToDetailMap.get(orderSkuDetail.getSkuId()) != null) {
                        OrderSkuDetail newDetail = skuToDetailMap.get(orderSkuDetail.getSkuId());
                        newDetail.setSkuCount(newDetail.getSkuCount() + orderSkuDetail.getSkuCount());
                        // newDetail 不能再 数量乘以原价，初始放入的时候已经乘过。
                        newDetail.setSkuJdPrice(newDetail.getSkuJdPrice() + orderSkuDetail.getSkuCount() * orderSkuDetail.getSkuJdPrice());
                    } else {
                        // 复制新的对象，然后再进行修改属性，不能修改原对象
                        OrderSkuDetail newDetail = JacksonUtils.parse(JacksonUtils.toJson(orderSkuDetail), OrderSkuDetail.class);
                        // 总原价
                        newDetail.setSkuJdPrice(newDetail.getSkuCount() * newDetail.getSkuJdPrice());
                        skuToDetailMap.put(orderSkuDetail.getSkuId(), newDetail);
                    }

                }

                List<OrderSkuDetail> newSkuDetailList = new ArrayList<>(skuToDetailMap.values());

                // log.info("排序前skuDetailList={}", newSkuDetailList);
                ActivityPromotionSplitUtil.sortProductList(tenantId, newSkuDetailList, OrderSkuDetail::getSkuJdPrice,
                        OrderSkuDetail::getSkuCount, OrderSkuDetail::getSkuName);

                // 商品总数量
                int totalQuantity = newSkuDetailList.stream().mapToInt(OrderSkuDetail::getSkuCount).sum();

                // 复制一份。累积减的时候使用，不能影响原值。
                int lastPoiPromotion = poi_charge;
                int lastPlatPromotion = mt_charge;

                // 遍历商品明细
                for (int index = 0; index < newSkuDetailList.size(); index++) {
                    OrderSkuDetail skuDetail = newSkuDetailList.get(index);
                    ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();

                    // 运费有多种，老牵牛花统一用f表示子类型
                    String promotionType = discount.getDiscountType() + ProjectConstant.HYPHEN + "f";
                    String activityName = StringUtils.isNotBlank(discount.getDiscountName()) ? discount.getDiscountName()
                            : "运费优惠" + discount.getDiscountType() + ProjectConstant.HYPHEN + discount.getDiscountDetailType();
                    // 运费没有活动ID
                    activityShareDetailDTO.setActivityId(StringUtils.isNotBlank(activityId) ? activityId : activityName);
                    activityShareDetailDTO.setChannelPromotionType(promotionType);
                    activityShareDetailDTO.setPromotionRemark(activityName);
                    //
                    activityShareDetailDTO.setSkuId(customSkuMap.get(skuDetail.getSkuId()));
                    // sku数量在分组时，已经计算过
                    activityShareDetailDTO.setSkuCount(skuDetail.getSkuCount());
                    // 运费优惠，所有商品都参加优惠
                    activityShareDetailDTO.setPromotionCount(skuDetail.getSkuCount());
                    // skuStorePrice 原价
                    activityShareDetailDTO.setTotalOriginPrice(skuDetail.getSkuStorePrice() * skuDetail.getSkuCount());
                    // skuJdPrice 成交价,售价
                    // 这里的skuJdPrice是所有相同的sku加总的结果，在sku分组时已经计算过了
                    int actualSalePrice = skuDetail.getSkuJdPrice();

                    if (index == newSkuDetailList.size() - 1) {
                        // 尾差法：最后一个商品，取剩余的优惠。
                        activityShareDetailDTO.setChannelCost(lastPlatPromotion);
                        activityShareDetailDTO.setTenantCost(lastPoiPromotion);
                    } else {
                        /*
                          参考下游 settlement 服务单头金额到明细的拆分方式
                          com.sankuai.meituan.shangou.empower.settlement.domain.finance.factory.FinanceCombinationFactory#splitHead2Detail
                         */
                        double percent = 0d;
                        if (totalActualSalePrice == 0) {
                            // 按优惠后的价格比例分摊，如果优惠后价格为0，说明所有商品优惠后价格为0，按数量拆分
                            percent = skuDetail.getSkuCount() * 1.0 / totalQuantity;
                        } else {
                            percent = actualSalePrice * 1.0 / totalActualSalePrice;
                        }
                        int poiPromotion = Double.valueOf(poi_charge * percent).intValue();
                        int platItemPromotion = Double.valueOf(mt_charge * percent).intValue();
                        activityShareDetailDTO.setChannelCost(platItemPromotion);
                        activityShareDetailDTO.setTenantCost(poiPromotion);
                        lastPoiPromotion -= poiPromotion;
                        lastPlatPromotion -= platItemPromotion;
                    }

                    // 运费优惠不影响活动价格。单品优惠的活动价格 = 原价 - 优惠。运费优惠不这么计算
                    // activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice());
                    activityShareDetailDTO.setTotalActivityPrice(
                            activityShareDetailDTO.getTotalOriginPrice() - activityShareDetailDTO.getChannelCost()
                                    - activityShareDetailDTO.getTenantCost());
                    activityShareDetailDTOList.add(activityShareDetailDTO);
                }

            }
        }
        log.info("活动分摊结果activityShareDetailDTOList，订单详情接口优惠信息中的拆分结果:{}", JacksonUtils.toJson(activityShareDetailDTOList));
        return activityShareDetailDTOList;
    }

    private String getPlatLogisticsPromotionActivityId(String orderShareRatioData) {
        if (StringUtils.isNotBlank(orderShareRatioData)) {
            String[] dataSplit = orderShareRatioData.split(ProjectConstant.AND_STR);
            for (String s : dataSplit) {
                String[] ratioData = s.split(ProjectConstant.EQUAL_STR);
                String entryName = ratioData[0];
                String entryValue = ratioData[1];
                // 解析促销活动ID
                if (StringUtils.endsWithIgnoreCase(entryName, ProjectConstant.PROMOTION_ID)) {
                    return entryValue;
                }
            }
        }

        return "";
    }

    /**
     * 平台与商家共同承担运费优惠
     */
    private List<ActivityShareDetailDTO> splitPlatAndPoiLogisticsPromotion(List<JDGoodsSettlementInfo> jdSettlementInfoList, ChannelOrderDetail orderDetail, Map<Long, String> customSkuMap) {
        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(jdSettlementInfoList)) {
            return activityShareDetailDTOList;
        }

        for (JDGoodsSettlementInfo settlementInfo : jdSettlementInfoList) {
            if (CollectionUtils.isEmpty(settlementInfo.getFreightDiscountList())) {
                continue;
            }

            log.info("开始运费优惠拆分-平台商家共同承担, freightDiscountList:{}", JacksonUtils.toJson(settlementInfo.getFreightDiscountList()));
            for (JDGoodsActivityDetail activityDetail : settlementInfo.getFreightDiscountList()) {
                if (activityDetail.getPromotionType() == null) {
                    log.error("运费优惠类型异常，JDGoodsActivityDetail:{}", JacksonUtils.toJson(activityDetail));
                    continue;
                }
                if (JddjOrderConvertUtil.isPlatformLogisticsActivity(activityDetail.getPromotionType())) {
                    log.warn("新版订单金额拆分接口出现仅平台承担的运费优惠，JDGoodsActivityDetail:{}", JacksonUtils.toJson(activityDetail));
                    continue;
                }

                ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
                String promotionType = activityDetail.getPromotionType() + ProjectConstant.HYPHEN + "f";
                String activityName = getActivityName(activityDetail);
                String activityId = StringUtils.isNotBlank(activityDetail.getPromotionCode()) ? activityDetail.getPromotionCode() : activityName;
                int totalCount = getSameSkuCount(orderDetail, settlementInfo.getSkuId());

                activityShareDetailDTO.setActivityId(activityId);
                activityShareDetailDTO.setChannelPromotionType(promotionType);
                activityShareDetailDTO.setPromotionRemark(activityName);
                activityShareDetailDTO.setSkuId(customSkuMap.get(settlementInfo.getSkuId()));
                // 销售数量为相同 sku 总数量
                activityShareDetailDTO.setSkuCount(totalCount);
                // 参加活动数量取渠道的数量（有可能和总数量不一致）
                activityShareDetailDTO.setPromotionCount(Optional.ofNullable(settlementInfo.getSkuCount()).orElse(0));
                activityShareDetailDTO.setChannelCost(getIntegerValue(activityDetail.getCostMoney()));
                activityShareDetailDTO.setTenantCost(getIntegerValue(activityDetail.getSaleMoney()));
                activityShareDetailDTO.setTotalOriginPrice(totalCount * settlementInfo.getPdjPrice());
                // 用以上的数据计算优惠后的总价
                activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice()
                        - activityShareDetailDTO.getChannelCost()
                        - activityShareDetailDTO.getTenantCost());

                activityShareDetailDTOList.add(activityShareDetailDTO);
            }
        }

        log.info("活动分摊结果activityShareDetailDTOList，平台商家共同承担:{}", JacksonUtils.toJson(activityShareDetailDTOList));
        return activityShareDetailDTOList;
    }

    private String getActivityName(JDGoodsActivityDetail activityDetail) {
        if (StringUtils.isNotBlank(activityDetail.getDiscountName())) {
            return activityDetail.getDiscountName();
        }
        String promotionDetailType = Optional.ofNullable(activityDetail.getPromotionDetailType()).map(String::valueOf).orElse("");
        return "运费优惠" + activityDetail.getPromotionType() + ProjectConstant.HYPHEN + promotionDetailType;
    }

    /**
     * 整单优惠
     */
    private List<ActivityShareDetailDTO> splitOrderPromotion(List<JDGoodsSettlementInfo> jdSettlementInfoList, ChannelOrderDetail orderDetail, Map<Long, String> customSkuMap) {
        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        // jdSettlementInfoList.discountlist 是整单优惠
        if (CollectionUtils.isEmpty(jdSettlementInfoList)) {
            return activityShareDetailDTOList;
        }

        // 不能改变原对象。深拷贝复制一份
        List<JDGoodsSettlementInfo> newInfoList = new ArrayList<>();
        for (JDGoodsSettlementInfo jdGoodsSettlementInfo : jdSettlementInfoList) {
            JDGoodsSettlementInfo newInfo = JacksonUtils.parse(JacksonUtils.toJson(jdGoodsSettlementInfo), JDGoodsSettlementInfo.class);
            newInfoList.add(newInfo);
        }

        Map<Long, Optional<JDGoodsSettlementInfo>> skuToOrderDiscountMap = newInfoList.stream().collect(Collectors.groupingBy(
                JDGoodsSettlementInfo::getSkuId,
                Collectors.reducing((a, b) -> {
                    // 获取此 skuid 的所有整单优惠
                    List<JDGoodsActivityDetail> discountlist = a.getDiscountlist();
                    discountlist.addAll(b.getDiscountlist());

                    // 整单优惠按照 "大优惠类型" 拼接 "小优惠类型" 得到的key进行分组，同一类优惠的优惠金额加总
                    Map<String, Optional<JDGoodsActivityDetail>> promotionToOrderDiscountMap = discountlist.stream().collect(Collectors.groupingBy(
                            orderActivityDetail -> {
                                String promotionDetailType = orderActivityDetail.getPromotionDetailType() == null ? "" : String.valueOf(orderActivityDetail.getPromotionDetailType());
                                return orderActivityDetail.getPromotionType() + ProjectConstant.HYPHEN + promotionDetailType;
                            },
                            Collectors.reducing((c, d) -> {
                                c.setCostMoney(c.getCostMoney() + d.getCostMoney());
                                c.setSaleMoney(c.getSaleMoney() + d.getSaleMoney());
                                return c;
                            })
                    ));

                    Collection<Optional<JDGoodsActivityDetail>> discountCollection = promotionToOrderDiscountMap.values();
                    List<JDGoodsActivityDetail> discountList = discountCollection.stream().map(Optional::get).collect(Collectors.toList());
                    a.setDiscountlist(discountList);
                    return a;
                })));

        log.info("活动分摊splitOrderPromotion, skuToOrderDiscountMap={}", skuToOrderDiscountMap);


        // 开始分摊整单优惠
        for (Optional<JDGoodsSettlementInfo> infoOptional : skuToOrderDiscountMap.values()) {
            JDGoodsSettlementInfo jdGoodsSettlementInfo = infoOptional.get();
            List<JDGoodsActivityDetail> discountlist = jdGoodsSettlementInfo.getDiscountlist();
            if (CollectionUtils.isEmpty(discountlist)) {
                continue;
            }

            for (JDGoodsActivityDetail discount : discountlist) {
                ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();

                // 大优惠一定有，小优惠promotionDetailType可能为null需要转换下，否则是字符串null
                String promotionDetailType = discount.getPromotionDetailType() == null ? "" : String.valueOf(discount.getPromotionDetailType());
                String channelPromotionType = discount.getPromotionType() + ProjectConstant.HYPHEN + promotionDetailType;

                activityShareDetailDTO.setChannelPromotionType(channelPromotionType);
                activityShareDetailDTO.setActivityId(StringUtils.isNotBlank(discount.getPromotionCode()) ? discount.getPromotionCode() : "");

                // 设置完活动ID activityId后，匹配活动名称，
                setPromotionRemark(activityShareDetailDTO, orderDetail, jdGoodsSettlementInfo);

                // 转换skuID
                Long skuId = jdGoodsSettlementInfo.getSkuId();
                activityShareDetailDTO.setSkuId(customSkuMap.get(skuId));
                // 统计相同skuID商品的数量
                int totalCount = getSameSkuCount(orderDetail, skuId);

                activityShareDetailDTO.setSkuCount(totalCount);
                // pdjPrice 原价
                activityShareDetailDTO.setTotalOriginPrice(totalCount * jdGoodsSettlementInfo.getPdjPrice());
                // 整单优惠，所有商品都参加优惠
                activityShareDetailDTO.setPromotionCount(totalCount);

                // 正单优惠已经拆分好了
                activityShareDetailDTO.setChannelCost(getIntegerValue(discount.getCostMoney()));
                activityShareDetailDTO.setTenantCost(getIntegerValue(discount.getSaleMoney()));

                // 用以上的数据计算优惠后的总价
                activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() - activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());

                // 订单多方承担信息，和单品的不一样。
                handleOassShareRatioData(discount.getOrderDiscountShareList(), activityShareDetailDTO);

                activityShareDetailDTOList.add(activityShareDetailDTO);
            }

        }
        log.info("活动分摊结果activityShareDetailDTOList，整单优惠:{}", JacksonUtils.toJson(activityShareDetailDTOList));
        log.info("活动分摊结果jdSettlementInfoList={}", JacksonUtils.toJson(jdSettlementInfoList));
        log.info("活动分摊结果jdSettlementInfoList new ={}", JacksonUtils.toJson(newInfoList));

        return activityShareDetailDTOList;
    }

    private void setPromotionRemark(ActivityShareDetailDTO activityShareDetailDTO, ChannelOrderDetail orderDetail, JDGoodsSettlementInfo jdGoodsSettlementInfo) {
        // 到订单详情里匹配活动名称。
        for (OrderActivitiesInfo orderActivitiesInfo : orderDetail.getDiscount()) {
            String detailType = orderActivitiesInfo.getDiscountDetailType() == null ? "" : String.valueOf(orderActivitiesInfo.getDiscountDetailType());
            String type = orderActivitiesInfo.getDiscountType() + ProjectConstant.HYPHEN + detailType;

            if (orderActivitiesInfo.getSkuIds() != null
                    && orderActivitiesInfo.getSkuIds().contains(String.valueOf(jdGoodsSettlementInfo.getSkuId()))
                    && type.equals(activityShareDetailDTO.getChannelPromotionType())
                    && StringUtils.isNotBlank(orderActivitiesInfo.getOrderShareRatioData())
                    && orderActivitiesInfo.getOrderShareRatioData().contains(activityShareDetailDTO.getActivityId())) {

                activityShareDetailDTO.setPromotionRemark(orderActivitiesInfo.getDiscountName());
                break;
            }
        }
    }

    private int getSameSkuCount(ChannelOrderDetail orderDetail, Long skuId) {
        // 统计相同skuID商品的数量
        int count = orderDetail.getProduct().stream().mapToInt(product -> {
            if (Objects.equals(product.getSkuId(), skuId)) {
                return product.getSkuCount();
            }
            return 0;
        }).sum();
        return count;
    }


    /**
     * 整合营销数据解析
     */
    private void handleOassShareRatioData(List<OassShareRatioData> skuPromotionShareList, ActivityShareDetailDTO activityShareDetailDTO) {
        if (CollectionUtils.isNotEmpty(skuPromotionShareList)) {
            int poiMarketPromotion = 0;
            int supplierMarketPromotion = 0;
            for (OassShareRatioData skuPromotionShare : skuPromotionShareList) {
                // 类型（2、3：整合营销）
                //商家可通过识别type=2/3获取整合营销补贴金额。原costMoney字段仍然返回平台承担金额，其中包含整合营销补贴金额。若要获取除整合营销补贴金额外的其它平台承担金额，商家可自行计算。
                //例：costMoney=10元，整合营销补贴金额为2元（type=2/3时，shareMoney的值），则其它平台承担金额为10-2=8元。
                int type = skuPromotionShare.getType() == null ? 0 : skuPromotionShare.getType();
                int shareMoney = skuPromotionShare.getShareMoney() == null ? 0 : skuPromotionShare.getShareMoney();
                if (type == JDDJGoodsPromotionShareTypeEnum.SHARE3.getValue()) {
                    supplierMarketPromotion = supplierMarketPromotion + shareMoney;
                }
                if (type == JDDJGoodsPromotionShareTypeEnum.SHARE2.getValue()) {
                    poiMarketPromotion = poiMarketPromotion + shareMoney;
                }
            }
            // 商家
            activityShareDetailDTO.setPoiMarketPromotion(poiMarketPromotion);
            // 供应商
            activityShareDetailDTO.setSupplierMarketPromotion(supplierMarketPromotion);
        }

    }

    /**
     * 单品优惠
     */
    private List<ActivityShareDetailDTO> splitSkuPromotion(List<JDGoodsSettlementInfo> jdSettlementInfoList, ChannelOrderDetail orderDetail, Map<Long, String> customSkuMap) {
        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        log.info("活动分摊splitSkuPromotion, jdSettlementInfoList={}, orderListDetail={}, customSkuMap={}",
                JacksonUtils.toJson(jdSettlementInfoList), JacksonUtils.toJson(orderDetail), JacksonUtils.toJson(customSkuMap));

        // jdSettlementInfoList 是单品优惠
        if (CollectionUtils.isNotEmpty(jdSettlementInfoList)) {
            for (JDGoodsSettlementInfo jdGoodsSettlementInfo : jdSettlementInfoList) {
                if (jdGoodsSettlementInfo.getPromotionType() == null || jdGoodsSettlementInfo.getPromotionType() == JDDJGoodsPromotionTypeEnum.NONE.getGoodsPromotionType()) {
                    // 无商品级别促销优惠
                    continue;
                }

                ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();

                activityShareDetailDTO.setActivityId(jdGoodsSettlementInfo.getPromotionId() == null ? "" : String.valueOf(jdGoodsSettlementInfo.getPromotionId()));
                // outActivityId 是外部活动编号，即牵牛花生成的活动ID
                // activityShareDetailDTO.setActivityId(jdGoodsSettlementInfo.getOutActivityId() == null ? "" : jdGoodsSettlementInfo.getOutActivityId());

                activityShareDetailDTO.setChannelPromotionType(String.valueOf(jdGoodsSettlementInfo.getPromotionType()));
                activityShareDetailDTO.setPromotionRemark(JDDJGoodsPromotionTypeEnum.enumOf(jdGoodsSettlementInfo.getPromotionType()).getGoodsPromotionName());
                //
                activityShareDetailDTO.setSkuId(customSkuMap.get(jdGoodsSettlementInfo.getSkuId()));
                int totalCount = getSameSkuCount(orderDetail, jdGoodsSettlementInfo.getSkuId());
                activityShareDetailDTO.setSkuCount(totalCount);
                //购买数量（SKU总的数量）
                activityShareDetailDTO.setTotalOriginPrice(jdGoodsSettlementInfo.getPdjPrice() * totalCount);
                //参加活动的SKU数量
                activityShareDetailDTO.setPromotionCount(jdGoodsSettlementInfo.getSkuCount() == null ? 0 : jdGoodsSettlementInfo.getSkuCount());

                // 单品优惠的需要乘以活动数量
                activityShareDetailDTO.setChannelCost(getIntegerValue(jdGoodsSettlementInfo.getCostMoney()) * activityShareDetailDTO.getPromotionCount());
                activityShareDetailDTO.setTenantCost(getIntegerValue(jdGoodsSettlementInfo.getSaleMoney()) * activityShareDetailDTO.getPromotionCount());

                // 用以上的数据计算优惠后的总价
                activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() - activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());

                // 单品多方承担信息
                handleOassShareRatioData(jdGoodsSettlementInfo.getSkuPromotionShareList(), activityShareDetailDTO);

                activityShareDetailDTOList.add(activityShareDetailDTO);
            }
        }
        log.info("活动分摊结果activityShareDetailDTOList，单品优惠:{}", JacksonUtils.toJson(activityShareDetailDTOList));

        return activityShareDetailDTOList;
    }

    //
    @Override
    public OrderPromotionResult queryOrderPromotionInfo(GoodsSettlementInfoRequest request) {
        OrderPromotionResult result = new OrderPromotionResult();
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);
            JDGoodsSettlementDetailParam param = jddjConverterService.goodsSettlementMapping(request);
            ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.GOODS_SETTLEMENT_INFO, baseRequest, param);
            List<JDGoodsSettlementInfo> jdSettlementInfos = JSON.parseArray(channelResponseDTO.getDataResponse().getData(), JDGoodsSettlementInfo.class);
            if (jdSettlementInfos != null) {
                Map<Long, OrderSkuDetail> channelSkuId2Detail = channelSkuId2SkuDetailInfo(request.getTenantId(), request.getOrderId(),request.getStoreId());
                result.setStatus(ResultGenerator.genSuccessResult());
                List<OrderItemPromotionResult> orderItemPromotions = extractPromotionInfo(jdSettlementInfos, channelSkuId2Detail, request.getTenantId());
                result.setSkuPromotions(orderItemPromotions);
                // mock处理上线删除
                //mockOrderAndRefundPromotion(result, request.getTenantId());
            } else {
                result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, channelResponseDTO.getDataResponse().getErrorCode() + ":" + channelResponseDTO.getDataResponse().getErrorInfos()));
            }

        } catch (Exception e) {
            log.error("elmChannelOrderService queryGoodsSettlementInfo ERROR!", e);
            return result.setStatus(ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }

        return result;
    }

    private List<OrderItemPromotionResult> extractPromotionInfo(List<JDGoodsSettlementInfo> jdSettlementInfos, Map<Long, OrderSkuDetail> channelSkuId2Detail, Long tenantId) {
        List<OrderItemPromotionResult> results = new ArrayList<>();
        if (CollectionUtils.isEmpty(jdSettlementInfos)) {
            return results;
        }
        for (JDGoodsSettlementInfo goodsSettlementInfo : jdSettlementInfos) {
            results.add(converterToPromotion(goodsSettlementInfo, channelSkuId2Detail, tenantId));
        }
        return results;
    }

    private OrderItemPromotionResult converterToPromotion(JDGoodsSettlementInfo goodsPromotion, Map<Long, OrderSkuDetail> channelSkuId2Detail, Long tenantId) {
        OrderItemPromotionResult result = new OrderItemPromotionResult();
        OrderSkuDetail orderSkuDetail = channelSkuId2Detail.get(goodsPromotion.getSkuId());
        if (Objects.isNull(orderSkuDetail)){
            log.error("JDDJ优惠信息非法{},map{}", goodsPromotion, channelSkuId2Detail);
        }
        result.setCustomSkuId(orderSkuDetail.getSkuIdIsv());
        result.setChannelSkuId(String.valueOf(orderSkuDetail.getSkuId()));
        result.setSkuName(orderSkuDetail.getSkuName());
        result.setUpc(orderSkuDetail.getUpcCode());

        result.setChannelItemId(goodsPromotion.getSkuUuid());
        result.setCount(goodsPromotion.getSkuCount());
        result.setPromotionPrice(goodsPromotion.getPromotionPrice());
        int platItemPromotion = (int) (goodsPromotion.getCostMoney() * goodsPromotion.getSkuCount());
        int poiItemPromotion = (goodsPromotion.getPdjPrice() - goodsPromotion.getPromotionPrice()) * goodsPromotion.getSkuCount() - platItemPromotion;
        int platPromotion = 0;
        int poiPromotion = 0;
        result.setPlatItemPromotion(platItemPromotion);
        result.setPoiItemPromotion(poiItemPromotion);
        calculateMarkerPromotion(goodsPromotion.getSkuPromotionShareList(), result);

        if (CollectionUtils.isNotEmpty(goodsPromotion.getDiscountlist())) {
            for (JDGoodsActivityDetail activityDetail : goodsPromotion.getDiscountlist()) {
                platPromotion += Optional.ofNullable(activityDetail.getCostMoney()).orElse(0L);
                poiPromotion += Optional.ofNullable(activityDetail.getSaleMoney()).orElse(0L);
                calculateMarkerPromotion(activityDetail.getOrderDiscountShareList(), result);
            }
        }
        // 检查租户京东订单的积分抵扣金额是否计入平台整单优惠
        if(MccConfigUtil.checkJdPointsDeductionMoneyDiscountsTenant(tenantId)){
            platPromotion += Optional.ofNullable(goodsPromotion.getPlatformIntegralDeductMoney()).orElse(0L);
        }

        result.setPlatPromotion(platPromotion);
        result.setPoiPromotion(poiPromotion);


        return result;
    }

    private void calculateMarkerPromotion(List<OassShareRatioData> skuPromotionShareList, OrderItemPromotionResult itemPromotion) {
        int supplierMarketPromotion = 0;
        int poiMarketPromotion = 0;
        if (CollectionUtils.isNotEmpty(skuPromotionShareList)) {
            for (OassShareRatioData oassShareRatioData : skuPromotionShareList) {
                Integer type = oassShareRatioData.getType();
                int shareMoney = Optional.ofNullable(oassShareRatioData.getShareMoney()).orElse(0);
                if (Objects.equals(type, 3)) {
                    supplierMarketPromotion += shareMoney;
                }
                if (Objects.equals(type, 2)) {
                    poiMarketPromotion += shareMoney;
                }
            }
        }
        itemPromotion.setSupplierMarketPromotion(itemPromotion.getSupplierMarketPromotion() + supplierMarketPromotion);
        itemPromotion.setPoiMarketPromotion(itemPromotion.getPoiMarketPromotion() + poiMarketPromotion);
    }


    private boolean isValidPromotion(JDGoodsSettlementInfo e) {
        return e != null && !Integer.valueOf(1).equals(e.getPromotionType()); //1、无优惠
    }

    @Override
    public GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request) {
        ResultStatus resultStatus = ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE);
        return new GetLogisticsStatusResult().setStatus(resultStatus);
    }

    @Override
    public GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request) {

        GetOrderAfsApplyListResult result = new GetOrderAfsApplyListResult();
        List<OrderAfsApplyDTO> afsApplyList = new ArrayList<>();

        if (StringUtils.isNotBlank(request.getAfterSaleId())) {
            JddjGetAfterSaleDetailRequest getAfterSaleDetailRequest = new JddjGetAfterSaleDetailRequest();
            getAfterSaleDetailRequest.setChannelId(ChannelTypeEnum.JD2HOME.getCode());
            getAfterSaleDetailRequest.setTenantId(request.getTenantId());
            getAfterSaleDetailRequest.setAfterSaleId(request.getAfterSaleId());
            getAfterSaleDetailRequest.setStoreId(request.getStoreId());
            getAfterSaleDetailRequest.setAppId(request.getAppId());
            JddjAfterSaleDetailResult afterSaleDetailResult = afterSaleDetail(getAfterSaleDetailRequest);
            if (afterSaleDetailResult.getAfterSaleDetail() == null) {
                result.setAfsApplyList(afsApplyList).setStatus(ResultGenerator.genResult(ResultCode.FAIL, afterSaleDetailResult.getMsg()));
                return result;
            }
            OrderAfsApplyDTO orderAfsApply = jddjConverterService.convertOrderAfsApplyDTO(afterSaleDetailResult.getAfterSaleDetail());
            //没有状态默认初始化
            orderAfsApply.setChannelAfsStatus(Optional.ofNullable(afterSaleDetailResult.getAfterSaleDetail().getAfsServiceState()).orElse(10).toString());
            String applyReason =  getApplyReason(afterSaleDetailResult.getAfterSaleDetail());
            String applyPicture =  StringUtils.isNotEmpty(afterSaleDetailResult.getAfterSaleDetail().getQuestionPic()) ? afterSaleDetailResult.getAfterSaleDetail().getQuestionPic() : "";
            //原因
            orderAfsApply.setApplyReason(applyReason);
            //将图片放在这里
            orderAfsApply.setRefundImgUrl(applyPicture);

            if(StringUtils.isNotBlank(afterSaleDetailResult.getAfterSaleDetail().getApplyDeal())) {
                orderAfsApply.setServiceType(JDApplyDealTypeEnum.enumOf(afterSaleDetailResult.getAfterSaleDetail().getApplyDeal()) == JDApplyDealTypeEnum.REFUND_GOODS ? ServiceTypeEnum.REFUND_GOODS.getCode() : ServiceTypeEnum.REFUND.getCode());
            }
            orderAfsApply.setReturnGoodsStatusType(ChannelStatusConvertUtil.jddjReturnGoodsStatusMapping(afterSaleDetailResult.getAfterSaleDetail()));
            orderAfsApply.setPickUpRefundGoodsAddress(afterSaleDetailResult.getAfterSaleDetail().getPickwareAddress());
            orderAfsApply.setRefundGoodsPrivacyContactPhone(afterSaleDetailResult.getAfterSaleDetail().getCustomerMobilePhone());

            // 填充京东退单数据
            fillJdRefundData(orderAfsApply, afterSaleDetailResult.getAfterSaleDetail(), request.getTenantId());

            afsApplyList.add(orderAfsApply);
            result.setAfsApplyList(afsApplyList).setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, ProjectConstant.OK));
        } else {
            BaseRequest baseRequest = new BaseRequest()
                    .setChannelId(request.getChannelType())
                    .setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);
            Map param = Maps.newHashMap();
            param.put(ProjectConstant.ORDER_ID_HUMP_, request.getChannelOrderId());
            ChannelResponseDTO<JdOrderAfsApplyDTO> resultMap = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.AFTERSALE_DETAIL_LIST, baseRequest, param);
            log.info("JddjChannelOrderServiceImpl.getOrderAfsApplyList, request:{}, resultMap:{}", request, resultMap);

            if (Objects.isNull(resultMap) || !StringUtils.equals(resultMap.getCode(), String.valueOf(ResultCode.SUCCESS.getCode())) || !resultMap.getSuccess()) {
                log.error("根据viewOrderId查询京东售后记录失败：viewOrderID={}", request.getChannelOrderId());
                return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "根据viewOrderId查询京东售后记录失败"));
            }

            JdOrderAfsApplyDTO jdOrderAfsApplyDTO = JSON.parseObject(resultMap.getData(), JdOrderAfsApplyDTO.class);
            if (Objects.isNull(jdOrderAfsApplyDTO) || CollectionUtils.isEmpty(jdOrderAfsApplyDTO.getAfsSeriveOrderList())) {
                log.info("该京东订单无渠道售后记录：viewOrderID={}", request.getChannelOrderId());
                return result.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, "该京东订单无渠道售后记录"));
            }

            List<JdAfsServiceOrderDTO> afsServiceOrderList = jdOrderAfsApplyDTO.getAfsSeriveOrderList();

            //通过售后id，查询售后详情
            JddjGetAfterSaleDetailRequest jdAfterSaleRequest = new JddjGetAfterSaleDetailRequest();
            jdAfterSaleRequest.setChannelId(ChannelTypeEnum.JD2HOME.getCode());
            jdAfterSaleRequest.setTenantId(request.getTenantId());
            jdAfterSaleRequest.setStoreId(request.getStoreId());

            for (JdAfsServiceOrderDTO afsServiceOrder : afsServiceOrderList) {
                jdAfterSaleRequest.setAfterSaleId(afsServiceOrder.getAfsServiceOrder());

                JddjAfterSaleDetailResult result2 = afterSaleDetail(jdAfterSaleRequest);
                if (result2 == null || result2.getCode() != ResultCode.SUCCESS.getCode()) {
                    log.error("根据售后id查询京东售后记录失败:viewOrderId={}", request.getChannelOrderId());
                    return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "京东渠道根据售后id查询售后记录失败"));
                }

                JddjAfterSaleDetail afterSaleDetail = result2.getAfterSaleDetail();
                if (afterSaleDetail == null) {
                    log.error("根据售后id未查询到京东售后记录：viewOrderId={}", request.getChannelOrderId());
                    return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "根据售后id未查询到京东售后记录"));
                }

                OrderAfsApplyDTO orderAfsApply = jddjConverterService.convertOrderAfsApplyDTO(afterSaleDetail);
                //没有状态默认初始化
                orderAfsApply.setChannelAfsStatus(Optional.ofNullable(afterSaleDetail.getAfsServiceState()).orElse(10).toString());
                String applyReason = getApplyReason(afterSaleDetail);
                orderAfsApply.setApplyReason(applyReason);
                orderAfsApply.setReturnGoodsStatusType(ChannelStatusConvertUtil.jddjReturnGoodsStatusMapping(afterSaleDetail));
                orderAfsApply.setPickUpRefundGoodsAddress(afterSaleDetail.getPickwareAddress());
                orderAfsApply.setRefundGoodsPrivacyContactPhone(afterSaleDetail.getCustomerMobilePhone());

                // 填充京东退单数据
                fillJdRefundData(orderAfsApply, afterSaleDetail, request.getTenantId());

                afsApplyList.add(orderAfsApply);
            }
            result.setAfsApplyList(afsApplyList).setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, ProjectConstant.OK));
        }
        // mock处理上线删除
        //mockOrderAndRefundPromotion(result, request.getTenantId());
        return result;
    }

    private void fillJdRefundData(OrderAfsApplyDTO orderAfsApply, JddjAfterSaleDetail afterSaleDetail, Long tenantId){
        try {
            if(Objects.isNull(afterSaleDetail)){
                return;
            }
            // 京东退单数据
            if (MccConfigUtil.checkSupportJdPackageFeeAndFreightTenants(tenantId)) {


                // mock售后单头数据
                // mockAfsOrderData(afterSaleDetail);

                // 设置退单包装费及相关优惠
                resetRefundPackageFeeAndPromotion(afterSaleDetail, orderAfsApply, tenantId);

                // 设置退单运费及相关优惠
                resetRefundFreightFeeAndPromotion(afterSaleDetail, orderAfsApply);
                // 扣除商家运费收入 在这里没法判断出订单实际的配送状态，放到orderbiz去解决

            }
        }catch (Exception e){
            // 异常不影响主流程
            log.error("填充京东退单数据处理异常：afterSaleId={}，afterSaleDetail: {}",tenantId, JSON.toJSONString(afterSaleDetail), e);
        }
    }

    /**
     * mock京东退单单头数据
     *
     * 这里需要跟渠道的字段保持一致
     *
     * @param afterSaleDetail
     */
    private void mockAfsOrderData(JddjAfterSaleDetail afterSaleDetail){

        Map<String, Long> mockData = MccConfigUtil.getMockJdAfsOrderData();
        if(CollectionUtils.isEmpty(mockData)){
            return;
        }
        // 包装费
        afterSaleDetail.setPackagingMoney(mockData.get("packagingMoney"));
        // 应收配送费
        afterSaleDetail.setOrderReceivableFreight(mockData.get("orderReceivableFreight"));

    }

    /**
     * 设置退单包装费及相关优惠
     *
     * @param afterSaleDetail
     * @param orderAfsApply
     * @param tenantId
     */
    private void resetRefundPackageFeeAndPromotion(JddjAfterSaleDetail afterSaleDetail, OrderAfsApplyDTO orderAfsApply, Long tenantId){
        try {

            // mock京东退单包装费优惠数据 Map<skuIdIsv, List<JddjAfsSkuPackageDiscount>>
//            Map<String, List<JddjAfsSkuPackageDiscount>> mockPackagePromotionMap = MccConfigUtil.getMockJdRefundPackagePromotionData();
//            if(CollectionUtils.isNotEmpty(mockPackagePromotionMap)){
//                for (JddjAfsServiceDetail detail : afterSaleDetail.getAfsDetailList()) {
//                    List<JddjAfsSkuPackageDiscount> jddjAfsSkuPackageDiscountList = mockPackagePromotionMap.get(detail.getSkuIdIsv());
//                    if(CollectionUtils.isEmpty(jddjAfsSkuPackageDiscountList)){
//                        continue;
//                    }
//                    List<JddjAfsSkuPackageDiscount> afsSkuPackageDiscountList = detail.getAfsSkuPackageDiscountList();
//                    if(Objects.isNull(afsSkuPackageDiscountList)){
//                        afsSkuPackageDiscountList = new ArrayList<>();
//                    }
//                    afsSkuPackageDiscountList.addAll(jddjAfsSkuPackageDiscountList);
//                    detail.setAfsSkuPackageDiscountList(afsSkuPackageDiscountList);
//                }
//
//            }

            // 包装费
            orderAfsApply.setOriginalPackageFee(ChannelStatusConvertUtil.parseLong2Int(afterSaleDetail.getPackagingMoney()));
            // 包装费平台优惠
            Integer platPackagePromotion = 0;
            // 包装费商家优惠
            Integer poiPackagePromotion = 0;
            // 累计明细里面的包装费优惠之和
            for (JddjAfsServiceDetail detail : afterSaleDetail.getAfsDetailList()) {
                // 存在包装费优惠
                if (CollectionUtils.isNotEmpty(detail.getAfsSkuPackageDiscountList())) {
                    for (JddjAfsSkuPackageDiscount skuDiscount : detail.getAfsSkuPackageDiscountList()) {
                        // 平台承担
                        platPackagePromotion += Optional.ofNullable(skuDiscount.getPlatPayMoney())
                                .map(ChannelStatusConvertUtil::parseLong2Int)
                                .orElse(0);
                        // 商家承担
                        poiPackagePromotion += Optional.ofNullable(skuDiscount.getVenderPayMoney())
                                .map(ChannelStatusConvertUtil::parseLong2Int)
                                .orElse(0);

                    }
                }
            }
            // 退回顾客支付包装费 = 包装费 - 商家包装费优惠 - 平台包装费优惠
            orderAfsApply.setPayPackageFee(orderAfsApply.getOriginalPackageFee() - poiPackagePromotion - platPackagePromotion);
            // 包装费-优惠金额-商家承担
            orderAfsApply.setPoiPackagePromotion(poiPackagePromotion);
            // 包装费-优惠金额-平台承担
            orderAfsApply.setPlatPackagePromotion(platPackagePromotion);
            // 包装费处理 查询【包装袋费用是否商家收取】的配置
            boolean isMerchantsChargePackageFee = tenantService.isMerchantsChargePackageFee(tenantId,
                    DynamicChannelType.JD2HOME);
            if (isMerchantsChargePackageFee) {
                orderAfsApply.setPlatPackageIncome(0);
                // 扣除商家包装费收入 --> 跟正单保持一致，不扣除平台的包装费优惠金额
                orderAfsApply.setPoiPackageIncome(orderAfsApply.getOriginalPackageFee());
            } else {
                // 扣除平台包装费收入 --> 跟正单保持一致，不扣除商家的包装费优惠金额
                orderAfsApply.setPlatPackageIncome(orderAfsApply.getOriginalPackageFee());
                orderAfsApply.setPoiPackageIncome(0);
            }
        }catch (Exception e){
            log.error("resetRefundPackageFeeAndPromotion error! afterSaleDetail:{}", JSON.toJSONString(afterSaleDetail), e);
        }
    }


    /**
     * 设置退单运费及相关优惠
     * 注：这里不设置扣除商家运费收入数据
     *
     * @param afterSaleDetail
     * @param orderAfsApply
     */
    private void resetRefundFreightFeeAndPromotion(JddjAfterSaleDetail afterSaleDetail, OrderAfsApplyDTO orderAfsApply){
        try {

            // mock京东退单运费订单级优惠数据 Map<skuIdIsv, List<JddjAfsSkuDiscount>>
//            Map<String, List<JddjAfsSkuDiscount>> mockFreightOrderPromotionMap = MccConfigUtil.getMockJdRefundFreightOrderPromotionData();
//            if(CollectionUtils.isNotEmpty(mockFreightOrderPromotionMap)){
//                for (JddjAfsServiceDetail detail : afterSaleDetail.getAfsDetailList()) {
//                    List<JddjAfsSkuDiscount> jddjAfsSkuPackageDiscountList = mockFreightOrderPromotionMap.get(detail.getSkuIdIsv());
//                    if(CollectionUtils.isEmpty(jddjAfsSkuPackageDiscountList)){
//                        continue;
//                    }
//                    List<JddjAfsSkuDiscount> afsSkuDiscountList = detail.getAfsSkuDiscountList();
//                    if(Objects.isNull(afsSkuDiscountList)){
//                        afsSkuDiscountList = new ArrayList<>();
//                    }
//                    afsSkuDiscountList.addAll(jddjAfsSkuPackageDiscountList);
//                    detail.setAfsSkuDiscountList(afsSkuDiscountList);
//                }
//
//            }
//
//            // mock京东退单运费商品级优惠数据 Map<skuIdIsv, List<JddjAfsSkuFreightDiscount>>
//            Map<String, List<JddjAfsSkuFreightDiscount>> mockFreightItemPromotionMap = MccConfigUtil.getMockJdRefundFreightItemPromotionData();
//            if(CollectionUtils.isNotEmpty(mockFreightItemPromotionMap)){
//                for (JddjAfsServiceDetail detail : afterSaleDetail.getAfsDetailList()) {
//                    List<JddjAfsSkuFreightDiscount> jddjAfsSkuPackageDiscountList = mockFreightItemPromotionMap.get(detail.getSkuIdIsv());
//                    if(CollectionUtils.isEmpty(jddjAfsSkuPackageDiscountList)){
//                        continue;
//                    }
//                    List<JddjAfsSkuFreightDiscount> afsSkuPackageDiscountList = detail.getAfsSkuFreightDiscountList();
//                    if(Objects.isNull(afsSkuPackageDiscountList)){
//                        afsSkuPackageDiscountList = new ArrayList<>();
//                    }
//                    afsSkuPackageDiscountList.addAll(jddjAfsSkuPackageDiscountList);
//                    detail.setAfsSkuFreightDiscountList(afsSkuPackageDiscountList);
//                }
//
//            }

            // 京东运费优惠类型配置 Map<"运费优惠字段", Map<"一级优惠类型", [二级优惠类型1,二级优惠类型2]>>
            Map<String, Map<String, List<Integer>>> jdAfsOrderLogisticsDiscountType = MccConfigUtil.getJdAfsOrderLogisticsDiscountType();

            // 应收配送费
            orderAfsApply.setFreight(ChannelStatusConvertUtil.parseLong2Int(afterSaleDetail.getOrderReceivableFreight()));
            // 运费商家优惠 = 订单级运费优惠（商家） + 明细级运费优惠（商家）
            Integer poiLogisticsPromotion = 0;
            // 运费平台优惠 = 订单级运费优惠（平台） + 明细级运费优惠（平台）
            Integer platLogisticsPromotion = 0;
            // 累计运费优惠之和
            for (JddjAfsServiceDetail detail : afterSaleDetail.getAfsDetailList()) {
                // 存在订单级运费优惠
                if (CollectionUtils.isNotEmpty(detail.getAfsSkuDiscountList())) {
                    for (JddjAfsSkuDiscount skuDiscount : detail.getAfsSkuDiscountList()) {
                        // 非运费优惠 -> 不统计
                        if (!isAfsOrderLogisticsDiscountType(jdAfsOrderLogisticsDiscountType, skuDiscount.getDiscountType(), skuDiscount.getDetailDiscountType())) {
                            continue;
                        }
                        // 商家承担
                        poiLogisticsPromotion += Optional.ofNullable(skuDiscount.getVenderPayMoney())
                                .map(ChannelStatusConvertUtil::parseLong2Int)
                                .orElse(0);
                        // 平台承担
                        platLogisticsPromotion += Optional.ofNullable(skuDiscount.getPlatPayMoney())
                                .map(ChannelStatusConvertUtil::parseLong2Int)
                                .orElse(0);

                    }
                }

                // 存在明细级运费优惠
                if (CollectionUtils.isNotEmpty(detail.getAfsSkuFreightDiscountList())) {
                    for (JddjAfsSkuFreightDiscount skuDiscount : detail.getAfsSkuFreightDiscountList()) {
                        // 非运费优惠 -> 不统计
                        if (!isAfsItemLogisticsDiscountType(jdAfsOrderLogisticsDiscountType, skuDiscount.getDiscountType(), skuDiscount.getDetailDiscountType())) {
                            continue;
                        }
                        // 商家承担
                        poiLogisticsPromotion += Optional.ofNullable(skuDiscount.getVenderPayMoney())
                                .map(ChannelStatusConvertUtil::parseLong2Int)
                                .orElse(0);
                        // 平台承担
                        platLogisticsPromotion += Optional.ofNullable(skuDiscount.getPlatPayMoney())
                                .map(ChannelStatusConvertUtil::parseLong2Int)
                                .orElse(0);

                    }
                }
            }
            // 历史的运费优惠有问题，这里重新设值
            // 运费商家优惠
            orderAfsApply.setPoiLogisticsPromotion(poiLogisticsPromotion);
            // 运费平台优惠
            orderAfsApply.setPlatLogisticsPromotion(platLogisticsPromotion);
        }catch (Exception e){
            log.error("resetRefundFreightFeeAndPromotion error! afterSaleDetail:{}", JSON.toJSONString(afterSaleDetail), e);
        }
    }
    

    /**
     * 根据lion配置，判断是否是订单级运费优惠
     *
     * lion配置 - jdAfsOrderLogisticsDiscountType
     * 一级优惠类型 - discountType
     * 二级优惠类型 - detailDiscountType
     * @param
     * @return
     */
    public boolean isAfsOrderLogisticsDiscountType(Map<String, Map<String, List<Integer>>> jdAfsOrderLogisticsDiscountType, Integer discountType, Integer detailDiscountType){

        return isAfsLogisticsDiscountType(jdAfsOrderLogisticsDiscountType, "afsSkuDiscountList", discountType, detailDiscountType);
    }

    /**
     * 根据lion配置，判断是否是商品级运费优惠
     *
     * lion配置 - jdAfsOrderLogisticsDiscountType
     * 一级优惠类型 - discountType
     * 二级优惠类型 - detailDiscountType
     * @param
     * @return
     */
    public boolean isAfsItemLogisticsDiscountType(Map<String, Map<String, List<Integer>>> jdAfsOrderLogisticsDiscountType, Integer discountType, Integer detailDiscountType){

        return isAfsLogisticsDiscountType(jdAfsOrderLogisticsDiscountType, "afsSkuFreightDiscountList", discountType, detailDiscountType);
    }

    /**
     * 根据lion配置，判断是否是运费优惠
     *
     * lion配置 - jdAfsOrderLogisticsDiscountType
     * 运费优惠字段 - logisticsField
     * 一级优惠类型 - discountType
     * 二级优惠类型 - detailDiscountType 允许为null
     * @param
     * @return
     */
    public boolean isAfsLogisticsDiscountType(Map<String, Map<String, List<Integer>>> jdAfsOrderLogisticsDiscountType, String logisticsField, Integer discountType, Integer detailDiscountType){
        try{
            // 基础参数校验，为空的话，则表示不支持。这里允许二级优惠类型为null，取决于一级优惠类型是否配置的是全量。
            if(CollectionUtils.isEmpty(jdAfsOrderLogisticsDiscountType) || Objects.isNull(discountType)){
                return false;
            }
            Map<String, List<Integer>> logisticsDiscount = jdAfsOrderLogisticsDiscountType.get(logisticsField);
            // 运费优惠字段的优惠类型判断
            if(CollectionUtils.isEmpty(logisticsDiscount)){
                return false;
            }
            List<Integer> detailDiscountTypeList = logisticsDiscount.get(String.valueOf(discountType));
            // 未配置二级优惠类型，表示不支持
            if(CollectionUtils.isEmpty(detailDiscountTypeList)){
                return false;
            }
            // 兜底逻辑，如果二级优惠类型为null，属于运费优惠，但配置上又配置有具体值，这里可以对应在lion上配置[0]的二级优惠类型。
            if(Objects.isNull(detailDiscountType)){
                detailDiscountType = 0;
            }
            // -1全量 || 包含二级运费优惠
            return detailDiscountTypeList.contains(-1) || detailDiscountTypeList.contains(detailDiscountType);
        }catch (Exception e){
            log.error("isAfsLogisticsDiscountType error! jdAfsOrderLogisticsDiscountType:{}, logisticsField:{}, discountType:{}, detailDiscountType:{}", JSON.toJSONString(jdAfsOrderLogisticsDiscountType), logisticsField, discountType, detailDiscountType, e);
        }
        return false;
    }

    @Override
    public QueryReverseJddjResult queryReverseJddj(QueryReverseJddjRequest request) {
        QueryReverseJddjResult result = new QueryReverseJddjResult();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(ChannelTypeEnum.JD2HOME.getCode());
        Map<String, Object> bizParam = new HashMap<>();
        bizParam.put("orderId", request.getChannelOrderId());
        bizParam.put("refDiffReverseOrderId", request.getRefDiffReverseOrderId());
        ChannelResponseDTO<List<JddjQueryReverseResultData>> resultMap;
        // 判断是否使用原方法（新方法出现问题时才会修改配置，回到原方法）
        if (MccConfigUtil.isJddjReversUseSendPostSwitch()) {
            // 使用原方法sendPost
            resultMap = jddjChannelGateService.sendPost(ChannelPostJDDJEnum.QUERY_REVERSE_ORDER, baseRequest, bizParam);
        } else {
            // 使用新方法sendPostAppDto
            addStoreId2BaseRequest(request.getShopId(), baseRequest);
            resultMap = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.QUERY_REVERSE_ORDER, baseRequest, bizParam);
        }
        log.info("JddjChannelOrderServiceImpl.queryReverseJddj, request:{}, resultMap:{}", request, resultMap);
        if (Objects.isNull(resultMap) || resultMap.getCoreData() == null) {
            log.error("查询京东退差单错误单号：{}，退差单：{}, 响应：{}", request.getChannelOrderId(), request.getRefDiffReverseOrderId(), resultMap );
            return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "根据渠道单号以及售后单号，未查询到京东退差价逆向单记录"));
        } else if (!resultMap.getDataResponse().isSuccess()) {
            log.error("查询京东退差单错误单号：{}，退差单：{}, 响应：{}", request.getChannelOrderId(), request.getRefDiffReverseOrderId(), resultMap );
            return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, resultMap.getErrorMsg()));
        } else {
            return result
                    .setStatus(ResultGenerator.genResult(ResultCode.SUCCESS))
                    .setAfsApplyList(resultMap.getCoreData()
                            .stream()
                            .map(JddjQueryReverseResultData::toJddjQueryReverseResultDto).collect(Collectors.toList()));
        }
    }

    @Override
    public ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request) {
        //渠道无对应接口，直接返回成功
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ResultCode.SUCCESS.getCode());
        resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
        return resultStatus;
    }

    @Override
    public ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request) {
        if (!MccConfigUtil.getJddjUpdateDeliveryInfoSwitch()) {
            // 降级开关打开，仅走历史逻辑
            return syncDeliveryInfo(request);
        }

        long tenantId = request.getTenantId();
        long storeId = request.getShopId();
        String orderId = request.getOrderId();
        try {
            // 走新接口同步配送状态
            List<JddjDeliveryInfo> deliveryInfoList = buildDeliveryInfoList(request);
            if (CollectionUtils.isNotEmpty(deliveryInfoList)) {
                BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
                addStoreId2BaseRequest(request.getShopId(), baseRequest);
                JddjUpdateDeliveryInfoParam param = new JddjUpdateDeliveryInfoParam();
                param.setOrderId(request.getOrderId());
                param.setDeliveryInfoList(deliveryInfoList);

                log.info("JddjChannelOrderServiceImpl updateDeliveryInfo, baseRequest is {}, param is {}", baseRequest, param);
                ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.JDDJ_RECEIVE_LOGISTIC_INFOS_FOR_OPEN_API_PLATFORM, baseRequest, param);
                log.info("JddjChannelOrderServiceImpl updateDeliveryInfo channelResponseDTO is {}", channelResponseDTO);

                if (Objects.isNull(channelResponseDTO) || Objects.isNull(channelResponseDTO.getDataResponse())) {
                    log.error("JddjChannelOrderServiceImpl.updateDeliveryInfo, data response is null, orderId is {}, tenantId is {}, storeId is {}", orderId, tenantId, storeId);
                    Cat.logEvent(ProjectConstant.JDDJ_UPDATE_DELIVERY_INFO_CAT_TYPE, "updateDeliveryInfo failed_" + tenantId + "_" + storeId);
                } else if (channelResponseDTO.getDataResponse().isSuccess()) {
                    Cat.logEvent(ProjectConstant.JDDJ_UPDATE_DELIVERY_INFO_CAT_TYPE, "updateDeliveryInfo success");
                } else {
                    log.error("JddjChannelOrderServiceImpl.updateDeliveryInfo failed, orderId is {}, tenantId is {}, storeId is {}", orderId, tenantId, storeId);
                    Cat.logEvent(ProjectConstant.JDDJ_UPDATE_DELIVERY_INFO_CAT_TYPE, "updateDeliveryInfo failed_" + tenantId + "_" + storeId);
                }
            }
        } catch (Exception e) {
            log.error("JddjChannelOrderServiceImpl.updateDeliveryInfo failed, orderId is {}, tenantId is {}, storeId is {}", orderId, tenantId, storeId, e);
            Cat.logEvent(ProjectConstant.JDDJ_UPDATE_DELIVERY_INFO_CAT_TYPE, "updateDeliveryInfo failed_" + tenantId + "_" + storeId);
        }

        // 保留历史逻辑同步「拣货完成」和「配送完成」
        return syncDeliveryInfo(request);
    }

    private List<JddjDeliveryInfo> buildDeliveryInfoList(UpdateDeliveryInfoRequest request) {
        DeliveryStatus qnhDeliveryStatus = DeliveryStatus.findByValue(request.getStatus());
        Optional<JddjDeliveryStatusEnum> deliveryStatusEnumOptional = JddjDeliveryStatusEnum.transformQnhDeliveryStatus(qnhDeliveryStatus);
        if (!deliveryStatusEnumOptional.isPresent()) {
            // 对应牵牛花配送状态与京东渠道配送状态没有映射上的场景
            log.info("JddjChannelOrderServiceImpl buildDeliveryInfoList, deliveryStatusEnumOptional is empty, request is {}", request);
            return Collections.emptyList();
        }
        String deliveryStatus = String.valueOf(deliveryStatusEnumOptional.get().getCode());

        JddjDeliveryInfo deliveryInfo = new JddjDeliveryInfo();
        deliveryInfo.setDeliveryStatus(deliveryStatus);
        if (Objects.nonNull(request.getRiderName()) && Objects.nonNull(request.getRiderPhone())) {
            deliveryInfo.setDeliveryManName(request.getRiderName());
            deliveryInfo.setDeliveryManPhone(request.getRiderPhone());
        }
        deliveryInfo.setOperateTime(TimeUtil.toTimeStr(LocalDateTime.now()));
        deliveryInfo.setOperator(Optional.ofNullable(request.getRiderName()).orElse(SYSTEM_OPERATOR));

        return Collections.singletonList(deliveryInfo);
    }

    private List<JddjDeliveryInfo> buildDeliveryInfoList(RiderInfoChangeRequest request) {
        JddjDeliveryInfo deliveryInfo = new JddjDeliveryInfo();
        deliveryInfo.setDeliveryStatus(RIDER_INFO_CHANGED_DELIVERY_STATUS);
        if (Objects.nonNull(request.getRiderName()) && Objects.nonNull(request.getRiderPhone())) {
            deliveryInfo.setDeliveryManName(request.getRiderName());
            deliveryInfo.setDeliveryManPhone(request.getRiderPhone());
        }
        deliveryInfo.setOperateTime(TimeUtil.toTimeStr(LocalDateTime.now()));
        deliveryInfo.setOperator(Optional.ofNullable(request.getRiderName()).orElse(SYSTEM_OPERATOR));

        return Collections.singletonList(deliveryInfo);
    }

    @Override
    public ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request) {
        if (!MccConfigUtil.getJddjUpdateRiderInfoSwitch()) {
            // 降级开关打开，走历史逻辑
            return syncDeliveryInfo(request);
        }

        long tenantId = request.getTenantId();
        long storeId = request.getShopId();
        String orderId = request.getOrderId();
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
        addStoreId2BaseRequest(request.getShopId(), baseRequest);
        JddjUpdateRiderInfoParam param = new JddjUpdateRiderInfoParam();
        param.setOrderId(request.getOrderId());
        param.setLatitude(String.valueOf(request.getLatitude()));
        param.setLongitude(String.valueOf(request.getLongitude()));

        log.info("JddjChannelOrderServiceImpl updateRiderInfo, baseRequest is {}, param is {}", baseRequest, param);
        ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.JDDJ_SYNC_LOGISTICS_MESSAGE, baseRequest, param);
        log.info("JddjChannelOrderServiceImpl updateRiderInfo channelResponseDTO is {}", channelResponseDTO);

        if (Objects.isNull(channelResponseDTO) || Objects.isNull(channelResponseDTO.getDataResponse())) {
            log.error("JddjChannelOrderServiceImpl updateRiderInfo, data response is null, orderId is {}, tenantId is {}, storeId is {}", orderId, tenantId, storeId);
            Cat.logEvent(ProjectConstant.JDDJ_UPDATE_RIDER_INFO_CAT_TYPE, "updateRiderInfo failed_" + tenantId + "_" + storeId);
            return new ResultStatus(ResultCode.FAIL.getCode(), "调用京东到家自配送回传骑手坐标接口无响应结果", null);
        }

        if (channelResponseDTO.getDataResponse().isSuccess()) {
            Cat.logEvent(ProjectConstant.JDDJ_UPDATE_RIDER_INFO_CAT_TYPE, "updateRiderInfo success");
            return new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null);
        } else {
            log.error("JddjChannelOrderServiceImpl updateRiderInfo failed, orderId is {}, tenantId is {}, storeId is {}", orderId, tenantId, storeId);
            Cat.logEvent(ProjectConstant.JDDJ_UPDATE_RIDER_INFO_CAT_TYPE, "updateRiderInfo failed_" + tenantId + "_" + storeId);
            return new ResultStatus(ResultCode.FAIL.getCode(), channelResponseDTO.getErrorMsg(), null);
        }
    }

    /**
     * 同步京东渠道配送状态变更的历史逻辑，历史逻辑里updateDeliveryInfo()和updateRiderInfo()都是这个逻辑
    */
    private ResultStatus syncDeliveryInfo(UpdateDeliveryInfoRequest request) {
        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        addStoreId2BaseRequest(request.getShopId(), baseRequest);
        Map<String, String> param = Maps.newHashMap();
        param.put("orderId", request.getOrderId());
        param.put("operator", StringUtils.isBlank(request.getRiderName()) ? "未知配送用户" : request.getRiderName());
        ResultStatus resultStatus = new ResultStatus();
        ChannelResponseDTO<String> channelResponseDTO=null;
        log.info("JddjChannelOrderServiceImpl.updateOrderDeliveryStatus baseRequest:{}  param:{}", baseRequest, param);
        DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(request.getStatus());

        switch (deliveryStatus) {
            //骑手已取餐 2024.04.16调整为拣货完成的时候调用
            case RIDER_TAKEN_MEAL: {
                if(!MccConfigUtil.jddjDeliverySwitch()){//mcc控制是否发送 针对原始订单的处理方式 后续可通过关闭mcc进行关闭
                    log.info("JddjChannelOrderServiceImpl.updateOrderDeliveryStatus  将拣货完成状态调整支完成拣货时触发。 baseRequest:{}  param:{}",baseRequest,param);
                    //对于非法状态不做处理
                    resultStatus.setCode(ResultCode.SUCCESS.getCode());
                    resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
                    return resultStatus;
                }
                channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.ORDER_SERLLER_DELIVERY, baseRequest, param);
                log.info("JddjChannelOrderServiceImpl.updateOrderDeliveryStatus  调用京东到家拣货完成且商家自送接口响应:{}", channelResponseDTO);
                break;
            }
            case DELIVERY_COMPLETED: {
                param.put("operTime", DateUtils.currentTimeDefault());
                param.put("operPin", StringUtils.isBlank(request.getRiderName()) ? "未知配送用户" : request.getRiderName());
                channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.JDDJ_DELIVERY_END_ORDER, baseRequest, param);
                log.info("JddjChannelOrderServiceImpl.updateOrderDeliveryStatus  调用京东到家订单妥投接口响应:{}", channelResponseDTO);
                break;
            }
            default: {
                log.warn("JddjChannelOrderServiceImpl.updateOrderDeliveryStatus  无法处理配送状态:{}", request.getStatus());
                //对于非法状态不做处理
                resultStatus.setCode(ResultCode.SUCCESS.getCode());
                resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
                return resultStatus;
            }
        }

        if (Objects.isNull(channelResponseDTO) || Objects.isNull(channelResponseDTO.getDataResponse())) {
            log.error("JddjChannelOrderServiceImpl.updateOrderDeliveryStatus  调用京东到家配送相关接口无响应结果");
            throw new ChannelBizException("调用京东到家配送相关接口无响应结果");
        }

        String msg = channelResponseDTO.getDataResponse().isSuccess() ? ResultCode.SUCCESS.getMsg() : channelResponseDTO.getErrorMsg();
        int code = channelResponseDTO.getDataResponse().isSuccess() ? ResultCode.SUCCESS.getCode() : ResultCode.FAIL.getCode();
        resultStatus.setMsg(msg);
        resultStatus.setCode(code);
        return resultStatus;
    }

    @Override
    public ResultStatus riderInfoChange(RiderInfoChangeRequest request) {
        long tenantId = request.getTenantId();
        long storeId = request.getShopId();
        String orderId = request.getOrderId();

        List<JddjDeliveryInfo> deliveryInfoList = buildDeliveryInfoList(request);
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
        addStoreId2BaseRequest(request.getShopId(), baseRequest);
        JddjUpdateDeliveryInfoParam param = new JddjUpdateDeliveryInfoParam();
        param.setOrderId(request.getOrderId());
        param.setDeliveryInfoList(deliveryInfoList);

        log.info("JddjChannelOrderServiceImpl riderInfoChange, baseRequest is {}, param is {}", baseRequest, param);
        ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.JDDJ_RECEIVE_LOGISTIC_INFOS_FOR_OPEN_API_PLATFORM, baseRequest, param);
        log.info("JddjChannelOrderServiceImpl riderInfoChange channelResponseDTO is {}", channelResponseDTO);

        if (Objects.isNull(channelResponseDTO) || Objects.isNull(channelResponseDTO.getDataResponse())) {
            log.error("JddjChannelOrderServiceImpl riderInfoChange, data response is null, orderId is {}, tenantId is {}, storeId is {}", orderId, tenantId, storeId);
            Cat.logEvent(ProjectConstant.JDDJ_RIDER_INFO_CHANGE_CAT_TYPE, "riderInfoChange failed_" + tenantId + "_" + storeId);
            return new ResultStatus(ResultCode.FAIL.getCode(), "调用京东到家同步骑手信息变更接口无响应结果", null);
        }
        if (channelResponseDTO.getDataResponse().isSuccess()) {
            Cat.logEvent(ProjectConstant.JDDJ_RIDER_INFO_CHANGE_CAT_TYPE, "riderInfoChange success");
            return new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null);
        } else {
            log.error("JddjChannelOrderServiceImpl riderInfoChange failed, orderId is {}, tenantId is {}, storeId is {}", orderId, tenantId, storeId);
            Cat.logEvent(ProjectConstant.JDDJ_RIDER_INFO_CHANGE_CAT_TYPE, "riderInfoChange failed_" + tenantId + "_" + storeId);
            return new ResultStatus(ResultCode.FAIL.getCode(), channelResponseDTO.getErrorMsg(), null);
        }
    }

    @Override
    public ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request) {
        return new ResultStatus(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(), ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg(), null);
    }


    @Override
    public ResultStatus selfDelivery(SelfDeliveryRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);
            SelfDeliveryDTO selfDeliveryDTO = jddjConverterService.selfDeliveryMapping(request);
            if (StringUtils.isBlank(selfDeliveryDTO.getUpdatePin())) {
                selfDeliveryDTO.setUpdatePin("未知用户");
            }
            ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.MODIFY_SELF_DELIVERY, baseRequest, selfDeliveryDTO);

            if (channelResponseDTO.getDataResponse().isSuccess()) {
                return ResultGenerator.genSuccessResult();
            }

            return ResultGenerator.genFailResult(channelResponseDTO.getDataResponse().getMsg());

        } catch (Exception e) {
            log.error("jddjChannelOrderService selfDelivery ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request) {
        return null;
    }

    @Override
    public ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request) {
        return null;
    }

    @Override
    public QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request) {
        QueryChannelOrderListResult result = new QueryChannelOrderListResult();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setAppId(request.getAppId());
        addStoreId2BaseRequest(request.getShopId(), baseRequest);
        JdChannelOrderListParam bizParam = new JdChannelOrderListParam();
        bizParam.setOrderStartTime_begin(DateUtils.dateToString(new Date(request.getStartTime())));
        bizParam.setOrderStartTime_end(DateUtils.dateToString(new Date(request.getEndTime())));
        bizParam.setPageNo((long) request.getPage());
        bizParam.setPageSize(request.getPageSize());
        bizParam.setDeliveryStationNoIsv(request.getChannelPoiId());
        bizParam.setReturnedFields(Lists.newArrayList(ProjectConstant.ORDER_ID_HUMP_));
        Cat.logEvent("channelHttpCall", ChannelPostJDDJEnum.ORDER_DETAIL.getUrl());
        ChannelResponseDTO<ChannelOrderListDetail> resultMap = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.ORDER_DETAIL, baseRequest, bizParam);
        log.info("jddjChannelOrderServiceImpl.queryChannelOrderList, request:{}, resultMap:{}", request, resultMap);
        if (Objects.isNull(resultMap)) {
            return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单详情失败"));
        }
        return orderListAnalysis(request.getTenantId(), request.getChannelId(), resultMap, result);
    }

    public GetOrderPrivacyPhoneResult queryOrderPrivacyPhone(GetOrderPrivacyPhoneRequest request) {
        log.info("JddjChannelOrderServiceImpl.queryOrderPrivacyPhone, request:{}", request);
        GetOrderAfsApplyListRequest getOrderAfsApplyListRequest = new GetOrderAfsApplyListRequest();
        getOrderAfsApplyListRequest.setChannelOrderId(request.getOrderId());
        getOrderAfsApplyListRequest.setStoreId(request.getStoreId());
        getOrderAfsApplyListRequest.setTenantId(request.getTenantId());
        getOrderAfsApplyListRequest.setChannelType(request.getChannelId());
        getOrderAfsApplyListRequest.setAfterSaleId(request.getAfterSaleId());
        GetOrderAfsApplyListResult result = getOrderAfsApplyList(getOrderAfsApplyListRequest);

        GetOrderPrivacyPhoneResult resp = new GetOrderPrivacyPhoneResult();
        if (Objects.isNull(result.getStatus()) || result.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "京东调用渠道查询订单隐私号失败"));
        }
        List<OrderAfsApplyDTO> orderAfsApplyDTOList = result.getAfsApplyList();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orderAfsApplyDTOList)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "京东调用渠道查询订单隐私号失败"));
        }

        String privacyPhone = orderAfsApplyDTOList.get(0).getRefundGoodsPrivacyContactPhone();
        if (StringUtils.isBlank(privacyPhone)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "京东调用渠道查询订单隐私号为空"));
        }

        resp.setPrivacyPhone(privacyPhone);
        resp.setStatus(ResultGenerator.genSuccessResult());
        log.info("JddjChannelOrderServiceImpl.queryOrderPrivacyPhone, resp:{}", resp);
        return resp;

    }

    private QueryChannelOrderListResult orderListAnalysis(long tenantId, int channelId, ChannelResponseDTO<ChannelOrderListDetail> resultMap, QueryChannelOrderListResult result) {

        ChannelResponseResult<ChannelOrderListDetail> orderDetailResponse = resultMap.getDataResponse();
        if (resultMap.getCode().equals(String.valueOf(ResultCodeEnum.SUCCESS.getValue()))) {
            ChannelOrderListDetail channelOrderListDetail = orderDetailResponse.getResultData();
            if (Objects.isNull(channelOrderListDetail)) {
                return result.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, "订单不存在"));
            }
            List<ChannelOrderDetail> channelOrderDetailList = channelOrderListDetail.getResultList();
            if (channelOrderListDetail.getTotalCount() <= 0 || CollectionUtils.isEmpty(channelOrderDetailList)) {
                return result.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, "订单不存在"));
            }
            List<String> channelOrderIdList = Lists.newArrayList();
            channelOrderDetailList.forEach(channelOrderDetail -> {
                //订单主数据
                channelOrderIdList.add(channelOrderDetail.getOrderId());
            });
            return result.setStatus(ResultGenerator.genSuccessResult()).setChannelOrderIdList(channelOrderIdList).setTotal(channelOrderListDetail.getTotalCount());
        }
        return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, resultMap.getMsg()));
    }

    @Override
    public QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request) {
        return null;
    }

    @Override
    public ChannelOrderMoneyRefundItemResult queryChannelOrderMoneyRefundItemList(ChannelOrderMoneyRefundItemRequest request)  {
        ChannelOrderMoneyRefundItemResult resp = new ChannelOrderMoneyRefundItemResult();
        ChannelOrderListDetail channelOrderListDetail = getChannelOrderDetail(request.getChannelId(), request.getOrderId(), request.getTenantId(),request.getStoreId());
        if (channelOrderListDetail == null || CollectionUtils.isEmpty(channelOrderListDetail.getResultList())) {
            log.error("queryChannelOrderMoneyRefundItemList, 渠道订单不存在{}",request);
            resp.setStatus(ResultGenerator.genFailResult("调用京东到家配送相关接口无响应结果"));
            return resp;
        }
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
        addStoreId2BaseRequest(request.getStoreId(), baseRequest);
        JddjCalcMoneyRequest jddjCalcMoneyRequest = new JddjCalcMoneyRequest();
        jddjCalcMoneyRequest.setOrderId(request.getOrderId());
        jddjCalcMoneyRequest.setSkuList(Lists.newArrayList());
        Map<String, Integer> uniqueKey2SkuCount = new HashMap<>();
        for (OrderSkuDetail orderSkuDetail : channelOrderListDetail.getResultList().get(0).getProduct()){
            JddjCalcMoneyRequest.VenderAfsSkuDTO venderAfsSkuDTO = new JddjCalcMoneyRequest.VenderAfsSkuDTO();
            venderAfsSkuDTO.setSkuId(orderSkuDetail.getSkuId());
            venderAfsSkuDTO.setSkuCount(orderSkuDetail.getSkuCount());
            venderAfsSkuDTO.setPromotionType(orderSkuDetail.getPromotionType());
            venderAfsSkuDTO.setIsPartialRefund(1);
            jddjCalcMoneyRequest.getSkuList().add(venderAfsSkuDTO);
            uniqueKey2SkuCount.put(orderSkuDetail.getSkuId() + String.valueOf(orderSkuDetail.getPromotionType()), orderSkuDetail.getSkuCount());
        }
        ChannelResponseDTO<JddjVenderAfsCalcMoneyResult> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.QUERY_SKU_AFS_CAN_REFUND_AMOUNT, baseRequest, jddjCalcMoneyRequest);
        if (Objects.isNull(channelResponseDTO) || Objects.isNull(channelResponseDTO.getDataResponse())) {
            log.error("JddjChannelOrderServiceImpl.queryChannelOrderMoneyRefundItemList  调用京东到家配送相关接口无响应结果{}",request);
            resp.setStatus(ResultGenerator.genFailResult("调用京东到家配送相关接口无响应结果"));
            return resp;
        }
        if (!channelResponseDTO.getDataResponse().isSuccess()) {
            log.error("JddjChannelOrderServiceImpl.queryChannelOrderMoneyRefundItemList  request:{}, response:{}",request,channelResponseDTO.getDataResponse());
            resp.setStatus(ResultGenerator.genFailResult(channelResponseDTO.getDataResponse().getMsg()));
        }
        if (Objects.nonNull(channelResponseDTO.getDataResponse().getResultData()) && CollectionUtils.isNotEmpty(channelResponseDTO.getDataResponse().getResultData().getAfsSkuList())){
            List<Long> channelSkuIdList = channelResponseDTO.getDataResponse().getCoreData().getAfsSkuList()
                    .stream().map(JddjVenderAfsCalcMoneyResult.VenderAfsSkuDTO::getSkuId)
                    .collect(Collectors.toList());
            Map<Long, String> customSkuMap = channelSkuIdList.stream().distinct().collect(
                    Collectors.toMap(
                            channelSkuId -> channelSkuId,
                            channelSkuId -> channelOrderListDetail.getResultList().get(0).getProduct()
                                    .stream().filter(e -> e.getSkuId().equals(channelSkuId))
                                    .findAny().orElse(new OrderSkuDetail()).getSkuIdIsv()));
            resp.setMoneyRefundItemDTOList(channelResponseDTO.getDataResponse().getResultData().getAfsSkuList().stream().map(sku->{
                ChannelOrderMoneyRefundItemDTO channelOrderMoneyRefundItemDTO = new ChannelOrderMoneyRefundItemDTO();
                channelOrderMoneyRefundItemDTO.setSkuId(customSkuMap.getOrDefault(sku.getSkuId(), StringUtils.EMPTY));
                channelOrderMoneyRefundItemDTO.setCanRefundSkuCount(uniqueKey2SkuCount.getOrDefault(sku.getSkuId() + String.valueOf(sku.getPromotionType()), 0));
                channelOrderMoneyRefundItemDTO.setCanRefundMoney(JddjConvertUtil.parseLong2Int(sku.getMaxRefundableMoney()));
                channelOrderMoneyRefundItemDTO.setCurrentPrice(JddjConvertUtil.parseLong2Int(sku.getSkuPrice()));
                return channelOrderMoneyRefundItemDTO;
            }).collect(Collectors.toList()));
        }
        resp.setStatus(ResultGenerator.genSuccessResult());
        return resp;
    }


    @Override
    public ResultStatus moneyRefund(MoneyRefundRequest request) {
        try {
            //获取渠道订单
            ChannelOrderListDetail channelOrderListDetail = getChannelOrderDetail(request.getChannelId(), request.getOrderId(), request.getTenantId(), request.getStoreId());
            if (channelOrderListDetail == null || CollectionUtils.isEmpty(channelOrderListDetail.getResultList())) {
                return ResultGenerator.genFailResult("渠道订单不存在");
            }
            //组装请求参数
            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            addStoreId2BaseRequest(request.getStoreId(), baseRequest);
            PoiCommitAfterSaleDTO poiCommitAfterSaleDTO = jddjConverterService.moneyRefundMapping(request, channelOrderListDetail.getResultList().get(0));
            log.info("moneyRefund.moneyRefundMapping 后:{}",poiCommitAfterSaleDTO);
            ChannelResponseDTO<String> channelResponseDTO = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.POI_COMMIT_AFTERSALE, baseRequest, poiCommitAfterSaleDTO);
            if (channelResponseDTO.getDataResponse().isSuccess()) {
                return ResultGenerator.genSuccessResult().setData(channelResponseDTO.getDataResponse().getResultData());
            }
            return ResultGenerator.genFailResult(channelResponseDTO.getDataResponse().getMsg());
        } catch (Exception e) {
            log.error("jddj.poiPartRefundApply ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    private void addStoreId2BaseRequest(long storeId, BaseRequest baseRequest){
        if(storeId <= NumberUtils.LONG_ZERO){
            return;
        }
        baseRequest.setStoreIdList(ImmutableList.of(storeId));
    }

    private String getApplyReason(JddjAfterSaleDetail afterSaleDetail) {
         String questionTypeCid = JDAfterSaleReasonEnum.enumOf(afterSaleDetail.getQuestionTypeCid()).getValue();
         String questionDesc = afterSaleDetail.getQuestionDesc();
         return StringUtils.join(questionTypeCid, StringUtils.isNotBlank(questionDesc) ? ":" + questionDesc : "");
    }
}
