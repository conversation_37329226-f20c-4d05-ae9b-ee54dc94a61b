package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.meituan.dorado.rpc.handler.filter.FilterHandler;
import com.meituan.dorado.rpc.meta.RpcInvocation;
import com.meituan.dorado.rpc.meta.RpcResult;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2019-08-29 14:53
 */
public interface SyncDataInterceptor {

    /**
     * 判断是否需要拦截处理
     */
    Boolean isIntercept(RpcInvocation invocation);

    /**
     * 拦截后核心的调用
     */
    RpcResult invoke(FilterHandler nextHandler, RpcInvocation invocation) throws Throwable;
}
