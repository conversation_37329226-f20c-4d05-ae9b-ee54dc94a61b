package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import javax.annotation.Resource;

import com.dianping.cat.Cat;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.PoiDistributedLockCacheConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelVirtualAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.VirtualAccessConfigListPageQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.OrderIdAware;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.DrunkHorseWrongPoiMapping;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.ChannelAuthChangeMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.message.ChannelAuthChangeMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.metric.ChannelErrorMetric;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.PoiChannelAppThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.PoiDistributedLock;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.common.PoiChannelConstant;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.common.Status;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.common.enums.StatusCodeEnum;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.AppInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByAppIdRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByPoiRequest;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryRequest;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelAppDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtTokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.ChannelAppMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelOrderThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.HttpClientUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.UrlUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * 渠道请求公共服务基类
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:18
 **/
@Slf4j
public abstract class BaseChannelGateService {

    @Resource
    private com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService copAccessConfigService;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private ChannelAppMapper channelAppMapper;

    @Resource
    private com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService copChannelStoreService;

    @Resource(name = "mtTokenRedisClient")
    private RedisStoreClient mtTokenRedisStoreClient;

    @Resource
    private ChannelOrderThriftServiceProxy channelOrderThriftServiceProxy;

    @Resource
    private TenantService tenantService;

    @Resource
    private PoiChannelAppThriftServiceProxy poiChannelAppThriftServiceProxy;

    @Autowired
    private DrunkHorseCacheService drunkHorseCacheService;

    @Autowired
    private ChannelErrorMetric channelErrorMetric;

    @Autowired
    private ChannelAuthChangeMessageProducer channelAuthChangeMessageProducer;

    @Autowired
    private PoiDistributedLock poiDistributedLock;



    /**
     * 多品牌各渠道发送请求公共方法门店维度 post
     *
     * @param postUrlEnum 请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam    请求业务参数
     * @return
     */
    public <T> T sendPostAppMapDto(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {

        // 参数校验
        validateSendPost(postUrlEnum, bizParam);

        // 业务参数实体转换为Map
        Map<String, Object> bizParamMap = ConverterUtils.convertToMap(bizParam, baseRequest.getChannelId());

        // 有门店信息接口调用时，多门店请求，返回以门店id为key的map
        Map<Long, T> res = Maps.newHashMap();
        Map<String, Object> finalBizParamMap = bizParamMap;
        Map<String, ChannelStoreDO> pois = getTenantPoiInfo(baseRequest);
        long tenantId = baseRequest.getTenantId();
        int channelId = baseRequest.getChannelId();
        baseRequest.getStoreIdList().forEach(storeId -> {
            String channelStoreKey = KeyUtils.genChannelStoreKey(baseRequest.getTenantId(), baseRequest.getChannelId(), storeId);
            String channelOnlinePoiCode = pois.get(channelStoreKey).getChannelOnlinePoiCode();
            Map<String, Object> appChannelSysParams = getAppChannelSysParams(tenantId, channelId,
                    pois.get(channelStoreKey).getAppId(), storeId, baseRequest.getOrderId());
            BaseRequest baseRequest1 = new BaseRequest();
            baseRequest1.setChannelId(channelId);
            baseRequest1.setTenantId(tenantId);
            baseRequest1.setStoreIdList(Arrays.asList(storeId));
            setAccessTokenIfNeed(channelOnlinePoiCode, baseRequest1, appChannelSysParams, finalBizParamMap);
            T object = postToChannel(pois.get(channelStoreKey).getChannelPoiCode(), channelOnlinePoiCode, postUrlEnum, appChannelSysParams, finalBizParamMap, baseRequest);
            res.put(storeId, object);
        });


        return (T) res;
    }

    /**
     * 多品牌各渠道发送请求公共方法 app维度 post
     *
     * @param postUrlEnum 请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam    请求业务参数
     * @return
     */
    public <T> T sendPostAppDto(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
        // 参数校验
        validateSendPost(postUrlEnum, bizParam);
        // 业务参数实体转换为Map
        Map<String, Object> bizParamMap = ConverterUtils.convertToMap(bizParam, baseRequest.getChannelId());
        Map<String, Object> sysParam = getSysParam(baseRequest, bizParamMap);
        return postToChannel(postUrlEnum, sysParam, bizParamMap, baseRequest);
    }

    private void setAccessTokenIfNeed(String appPoiCode, BaseRequest baseRequest,Map<String, Object> sysParam,Map<String, Object> bizParamMap){
        int channelId = baseRequest.getChannelId();
        if(ChannelTypeEnum.MEITUAN.getCode() != channelId && ChannelTypeEnum.MT_MEDICINE.getCode() != channelId) {
            return;
        }
        if(sysParam.get("app_id") != null) {
            String tenantAppId = (String)sysParam.get("app_id");
            if(MccConfigUtil.isQnhTenant(tenantAppId)) {
                return;
            }
        }
        long tenantId = baseRequest.getTenantId();
        long appId = baseRequest.getAppId();
        if(StringUtils.isEmpty(appPoiCode) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(baseRequest.getStoreIdList())) {
            ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(tenantId, channelId, baseRequest.getStoreIdList().get(0));
            if(channelStore == null) {
                return;
            }
            appPoiCode = channelStore.getChannelOnlinePoiCode();
        }

        if(appId <= 0 && org.apache.commons.collections4.CollectionUtils.isNotEmpty(baseRequest.getStoreIdList())) {
            ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(tenantId, channelId,
                    baseRequest.getStoreIdList().get(0));
            if(channelStore == null) {
                return;
            }
            appId = channelStore.getAppId();
        }
        ChannelAppDO channelAppDO = channelAppMapper.selectByTenantIdAndChannelIdAndAppId(tenantId, channelId, appId);
        if(channelAppDO == null || channelAppDO.getType() == 1) {
            return;
        }

        if(StringUtils.isEmpty(appPoiCode) && (bizParamMap.get(ProjectConstant.ORDER_ID) != null || bizParamMap.get("wm_order_id_view") != null)) {
            String orderId = (String) (bizParamMap.get(ProjectConstant.ORDER_ID) != null ? bizParamMap.get(ProjectConstant.ORDER_ID) : bizParamMap.get("wm_order_id_view"));
            appPoiCode = getAppPoiCodeByViewOrderId(tenantId, channelId, orderId);
        }
        if(StringUtils.isEmpty(appPoiCode)) {
            return;
        }
        MtTokenMessage mtTokenMessage = getMtIsvAccessToken(tenantId, appPoiCode, baseRequest, sysParam);

        if(mtTokenMessage != null && StringUtils.isNotEmpty(mtTokenMessage.getAccessToken())) {
            sysParam.put(ProjectConstant.MT_ACCESS_TOKEN, mtTokenMessage.getAccessToken());
        }
    }

    public String getAppPoiCodeByViewOrderId(Long tenantId, Integer channelId, String viewOrderId) {
        if (Objects.equals(ChannelTypeEnum.MT_MEDICINE.getCode(), channelId)) {
            return getMedicineAppPoiCodeByViewOrderId(tenantId, channelId, viewOrderId);
        } else {
            BizOrderQueryRequest request = new BizOrderQueryRequest();
            request.setTenantId(tenantId);
            request.setShopId(0L);
            request.setViewOrderId(viewOrderId);
            request.setOrderBizType(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue());
            request.setFromMaster(true);
            Optional<BizOrderModel> bizOrderModelOpt = channelOrderThriftServiceProxy.queryOrderInfo(request);
            if (bizOrderModelOpt.isPresent()) {
                ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(tenantId, channelId,
                        bizOrderModelOpt.get().shopId);
                if (channelStore == null) {
                    return null;
                }
                return channelStore.getChannelOnlinePoiCode();
            }
        }

        return null;
    }

    /**
     * 医药因为订单来源比较多，所以需要查询多次，后续可以修改为批量查询
     */
    private String getMedicineAppPoiCodeByViewOrderId(Long tenantId, Integer channelId, String viewOrderId) {
        if (StringUtils.isBlank(viewOrderId)) {
            return null;
        }
        // 医药所有可能的bizCode
        List<Integer> orderBizTypeList = Lists.newArrayList(
                OrderBizTypeEnum.MEITUAN_MEDICINE.getValue(),
                OrderBizTypeEnum.MEITUAN_WAIMAI.getValue(),
                OrderBizTypeEnum.SELF_PLATFORM.getValue(),
                OrderBizTypeEnum.SELF_MEDICINE_ORDER.getValue()
        );
        // 按照常用的顺序依次查询
        for (Integer bizType : orderBizTypeList) {
            Optional<BizOrderModel> orderOpt = getOrderByViewOrderId(tenantId, viewOrderId, bizType);
            if (!orderOpt.isPresent()) {
                continue;
            }
            ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(tenantId, channelId,
                    orderOpt.get().shopId);
            if (channelStore == null) {
                return null;
            }
            return channelStore.getChannelOnlinePoiCode();
        }
        return null;
    }

    private Optional<BizOrderModel> getOrderByViewOrderId(Long tenantId, String viewOrderId, Integer bizType) {
        BizOrderQueryRequest request = new BizOrderQueryRequest();
        request.setTenantId(tenantId);
        request.setShopId(0L);
        request.setViewOrderId(viewOrderId);
        request.setOrderBizType(bizType);
        request.setFromMaster(true);
        return channelOrderThriftServiceProxy.queryOrderInfo(request);
    }

    /**
     * 获取美团访问令牌
     * @param tenantId 租户ID
     * @param appPoiCode 店铺编码
     * @param baseRequest 基本请求参数
     * @param sysParam 系统参数
     * @return 访问令牌
     */
    public MtTokenMessage getMtIsvAccessToken(Long tenantId, String appPoiCode, BaseRequest baseRequest, Map<String, Object> sysParam) {
        String appId = Objects.nonNull(sysParam.get("app_id")) ? String.valueOf(sysParam.get("app_id")) : "";
        if (StringUtils.isNotEmpty(appId) && MccConfigUtil.isMtIsvNewCategory(appId)){
            return getMtIsvAccessTokenByTenantAppId2(tenantId, appPoiCode, appId, baseRequest, sysParam);
        }
        return getMtIsvAccessTokenByTenantAppId1(tenantId, appPoiCode, appId, baseRequest, sysParam);
    }

    private MtTokenMessage getMtIsvAccessTokenByTenantAppId1(Long tenantId, String appPoiCode, String appId, BaseRequest baseRequest, Map<String, Object> sysParam) {
        StoreKey storeKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + appPoiCode);
        MtTokenMessage mtTokenMessage = mtTokenRedisStoreClient.get(storeKey);
        if (mtTokenMessage == null) {
            Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "need_auth");
            return authAndSetTokenWithLock(tenantId, appPoiCode, baseRequest, sysParam, storeKey);
        } else if (mtTokenMessage.valid()) {
            return mtTokenMessage;
        } else {
            if (!mtTokenMessage.refreshValid()) {
                Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "refresh_token_invalid");
                mtTokenRedisStoreClient.delete(storeKey);
                log.error("美团渠道refresh_token过期，缓存已删除，storeKey={}", storeKey);
                return null;
            }
            MtTokenMessage mtTokenMessageNew = mtRefreshTokenAndSetCacheWithLock(baseRequest, sysParam, mtTokenMessage.getRefreshToken(), storeKey, appPoiCode, tenantId);
            if (mtTokenMessageNew != null) {
                mtTokenMessage = mtTokenMessageNew;
            }else{
                Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "refresh_token_failed");
            }
        }
        return mtTokenMessage;
    }

    private MtTokenMessage getMtIsvAccessTokenByTenantAppId2(long tenantId, String appPoiCode, String appId, BaseRequest baseRequest, Map<String, Object> sysParam) {
        StoreKey newStoreKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + appPoiCode + appId);
        StoreKey storeKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + appPoiCode);
        MtTokenMessage mtTokenMessage = mtTokenRedisStoreClient.get(newStoreKey);
        if (mtTokenMessage == null) {
            // 新缓存不存在，读旧
            MtTokenMessage oldMtTokenMessage = mtTokenRedisStoreClient.get(storeKey);
            if (oldMtTokenMessage == null) {
                Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "need_auth");
                //旧也不存在，重新获取，双写
                return authAndSetTokenWithLock(tenantId, appPoiCode, baseRequest, sysParam, storeKey);
            } else if (oldMtTokenMessage.valid()) {
                //旧有效，设置新
                mtTokenRedisStoreClient.set(newStoreKey, oldMtTokenMessage);
                return oldMtTokenMessage;
            } else {
                //旧失效，删除旧，重新获取，双写
                if (!oldMtTokenMessage.refreshValid()) {
                    //旧失效，删除旧，返回空
                    Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "refresh_token_invalid");
                    mtTokenRedisStoreClient.delete(storeKey);
                    log.error("美团渠道refresh_token过期，缓存已删除，storeKey={}", storeKey);
                    return null;
                }
                //旧失效，重新获取，双写
                MtTokenMessage mtTokenMessageNew = mtRefreshTokenAndSetCacheWithLock(baseRequest, sysParam, oldMtTokenMessage.getRefreshToken(), storeKey, appPoiCode, tenantId);
                if (mtTokenMessageNew != null) {
                    mtTokenMessage = mtTokenMessageNew;
                }else{
                    Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "refresh_token_failed");
                }
            }
            return mtTokenMessage;

        }
        // 新缓存存在
        else if (mtTokenMessage.valid()) {
            //新缓存有效返回
            return mtTokenMessage;
        } else {
            //新缓存无效
            if (!mtTokenMessage.refreshValid()) {
                //新缓存refresh_token无效，删除新缓存，返回空
                Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "refresh_token_invalid");
                mtTokenRedisStoreClient.delete(newStoreKey);
                //新旧缓存refreshToken失效是一致的，新Key失效，旧Key必失效，双删，重试时走重新授权
                mtTokenRedisStoreClient.delete(storeKey);
                log.error("美团渠道refresh_token过期，缓存已删除，newStoreKey={},storeKey={}", newStoreKey,storeKey);
                return null;
            }
            //新缓存refresh_token有效，重新获取，双写
            MtTokenMessage mtTokenMessageNew = mtRefreshTokenAndSetCacheWithLock(baseRequest, sysParam, mtTokenMessage.getRefreshToken(), storeKey, appPoiCode, tenantId);
            if (mtTokenMessageNew != null) {
                mtTokenMessage = mtTokenMessageNew;
            }else{
                Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "refresh_token_failed");
            }
        }
        return mtTokenMessage;
    }

    private MtTokenMessage getMtIsvAccessTokenByTenantAppId3(long tenantId, String appPoiCode, String appId, BaseRequest baseRequest, Map<String, Object> sysParam) {
        StoreKey storeKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + appPoiCode + appId);
        MtTokenMessage mtTokenMessage = mtTokenRedisStoreClient.get(storeKey);
        if (mtTokenMessage == null) {
            Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "need_auth");
            return authAndSetTokenWithLock(tenantId, appPoiCode, baseRequest, sysParam, storeKey);
        } else if (mtTokenMessage.valid()) {
            return mtTokenMessage;
        } else {
            if (!mtTokenMessage.refreshValid()) {
                Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "refresh_token_invalid");
                mtTokenRedisStoreClient.delete(storeKey);
                log.error("美团渠道refresh_token过期，缓存已删除，storeKey={}", storeKey);
                return null;
            }
            MtTokenMessage mtTokenMessageNew = mtRefreshTokenAndSetCacheWithLock(baseRequest, sysParam, mtTokenMessage.getRefreshToken(), storeKey, appPoiCode, tenantId);
            if (mtTokenMessageNew != null) {
                mtTokenMessage = mtTokenMessageNew;
            }else{
                Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "refresh_token_failed");
            }
        }
        return mtTokenMessage;
    }

    private MtTokenMessage authAndSetTokenWithLock(long tenantId, String appPoiCode, BaseRequest baseRequest, Map<String, Object> sysParam, StoreKey storeKey) {
        String appKey = (String) sysParam.getOrDefault("app_id", "");
        boolean isIsv = MccConfigUtil.getMtIsvAppKeySet().contains(appKey);
        if (!isIsv) {
            return authAndSetToken(tenantId, appPoiCode, baseRequest, sysParam, storeKey, appKey);
        }else {
            String lockKey = String.format(PoiDistributedLockCacheConstant.MT_AUTH_LOCK_PREFIX, tenantId, appPoiCode);
            try{
                boolean locked = poiDistributedLock.lock(lockKey, 10);
                if (!locked) {
                    Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "auth_lock_failed");
                    log.warn("BaseChannelGateService.authAndSetTokenWithLock 获取锁失败, lockKey:{}", lockKey);
                    return null;
                }
                return authAndSetToken(tenantId, appPoiCode, baseRequest, sysParam, storeKey, appKey);
            }catch (Exception e){
                log.error("BaseChannelGateService.authAndSetTokenWithLock 异常, lockKey:{}", lockKey, e);
                return null;
            }finally{
                poiDistributedLock.unlock(lockKey);
            }
        }
    }

    public void setToken(Long tenantId, int channelId, String appPoiCode, Map<String,Object> sysParam,String appKey) {
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setChannelId(channelId);
        baseRequest.setTenantId(tenantId);
        baseRequest.setStoreIdList(Lists.newArrayList(Long.valueOf(appPoiCode)));
        StoreKey newStoreKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + appPoiCode + appKey);
        authAndSetTokenWithLock(tenantId, appPoiCode, baseRequest, sysParam, newStoreKey);
    }

    private MtTokenMessage authAndSetToken(long tenantId, String appPoiCode, BaseRequest baseRequest, Map<String, Object> sysParam, StoreKey storeKey, String appKey) {
        Map<String, Object> tokenMap = mtOauthAuthorize(appPoiCode, baseRequest, sysParam);
        if (MapUtils.isNotEmpty(tokenMap) && 0 == (Integer) tokenMap.get("status")) {
            MtTokenMessage mtTokenMessageNew = setAccessToken(storeKey, tokenMap, appKey, appPoiCode, tenantId);
            return mtTokenMessageNew;
        } else {
            log.error("获取美团token失败 baseRequest:{} tokenMap:{}", baseRequest, tokenMap);
            return null;
        }
    }

    private MtTokenMessage mtRefreshTokenAndSetCacheWithLock(BaseRequest baseRequest, Map<String, Object> sysParam,
                                                     String refreshToken, StoreKey storeKey, String appPoiCode, Long tenantId){
        String appKey = (String) sysParam.getOrDefault("app_id", "");
        boolean isIsv = MccConfigUtil.getMtIsvAppKeySet().contains(appKey);
        if (!isIsv) {
            return mtRefreshTokenAndSetCache(baseRequest, sysParam, refreshToken, storeKey, appPoiCode, tenantId);
        }else {
            String lockKey = String.format(PoiDistributedLockCacheConstant.MT_AUTH_LOCK_PREFIX, tenantId, appPoiCode);
            try{
                boolean locked = poiDistributedLock.lock(lockKey, 10);
                if (!locked) {
                    Cat.logEvent(ProjectConstant.MT_TOKEN_PROCESS_TYPE, "refresh_token_lock_failed");
                    log.warn("BaseChannelGateService.mtRefreshTokenAndSetCacheWithLock 获取锁失败, lockKey:{}", lockKey);
                    return null;
                }
                return mtRefreshTokenAndSetCache(baseRequest, sysParam, refreshToken, storeKey, appPoiCode, tenantId);
            }catch (Exception e){
                log.error("BaseChannelGateService.mtRefreshTokenAndSetCacheWithLock 获取锁失败, lockKey:{}", lockKey, e);
                return null;
            }finally{
                poiDistributedLock.unlock(lockKey);
            }
        }
    }

    public MtTokenMessage mtRefreshTokenAndSetCache(BaseRequest baseRequest, Map<String, Object> sysParam,
            String refreshToken, StoreKey storeKey, String appPoiCode, Long tenantId) {
        Map<String, Object> tokenRefreshMap = mtRefreshToken(baseRequest, sysParam, refreshToken);
        if (MapUtils.isNotEmpty(tokenRefreshMap) && 0 == (Integer) tokenRefreshMap.get("status")) {
            MtTokenMessage mtTokenMessageStore = new MtTokenMessage();
            String accessToken = (String) tokenRefreshMap.get(ProjectConstant.MT_ACCESS_TOKEN);
            long mtTokenExpire = MccConfigUtil.getMtTokenExpire();
            long expiresIn = (Integer) tokenRefreshMap.get("expires_in");
            mtTokenMessageStore.setAccessToken(accessToken);
            long todayZero = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            long oneDay = TimeUnit.SECONDS.convert(1, TimeUnit.DAYS);
            if (mtTokenExpire < oneDay) {
                todayZero = System.currentTimeMillis();
            }
            mtTokenMessageStore.setExpires(todayZero + Math.min(mtTokenExpire, expiresIn) * 1000L);
            mtTokenMessageStore.setRefreshToken((String) tokenRefreshMap.get("refresh_token"));
            mtTokenMessageStore.setRefreshExpires(System.currentTimeMillis() + (Integer) tokenRefreshMap.get("re_expires_in") * 1000L);
            // 美团isv应用（一应用多租户）发送渠道授权变更消息
            String appKey = (String) sysParam.getOrDefault("app_id", "");
            StoreKey newStoreKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + appPoiCode + appKey );
            // 租户id + 渠道门店id & 租户id + 渠道门店id + appKey 存储
            mtTokenRedisStoreClient.set(storeKey, mtTokenMessageStore);
            mtTokenRedisStoreClient.set(newStoreKey, mtTokenMessageStore);
            if(MccConfigUtil.getMtIsvAppKeySet().contains(appKey)) {
                sendChannelAuthChangeMsg(appKey, tenantId, appPoiCode, tokenRefreshMap);
            }
            return mtTokenMessageStore;
        }
        return null;
    }



    public MtTokenMessage setAccessToken(StoreKey storeKey, Map<String, Object> tokenMap, String appKey, String appPoiCode, Long tenantId) {
        String accessToken;
        accessToken = (String) tokenMap.get(ProjectConstant.MT_ACCESS_TOKEN);
        Integer expire = (Integer) tokenMap.get("expires_in");
        String refreshToken = (String) tokenMap.get("refresh_token");
        Integer refreshExpire = (Integer) tokenMap.get("re_expires_in");
        MtTokenMessage mtTokenMessageStore = new MtTokenMessage();
        long mtTokenExpire = MccConfigUtil.getMtTokenExpire();
        mtTokenMessageStore.setAccessToken(accessToken);

        long todayZero = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        long oneDay =  TimeUnit.SECONDS.convert(1, TimeUnit.DAYS);
        if(mtTokenExpire < oneDay) {
            todayZero = System.currentTimeMillis();
        }
        mtTokenMessageStore.setExpires(todayZero + Math.min(mtTokenExpire, expire.longValue()) * 1000L);
        mtTokenMessageStore.setRefreshToken(refreshToken);
        mtTokenMessageStore.setRefreshExpires(System.currentTimeMillis() + refreshExpire.longValue() * 1000L);
        StoreKey oldStoreKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + appPoiCode);
        StoreKey newStoreKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + appPoiCode + appKey );
        // 租户id + 渠道门店id & 租户id + 渠道门店id + appKey 存储
        mtTokenRedisStoreClient.set(oldStoreKey, mtTokenMessageStore);
        mtTokenRedisStoreClient.set(newStoreKey, mtTokenMessageStore);
        // 美团ISV应用（一应用多租户）发送渠道授权变更消息
        if(MccConfigUtil.getMtIsvAppKeySet().contains(appKey)){
            sendChannelAuthChangeMsg(appKey, tenantId, appPoiCode, tokenMap);
        }
        return mtTokenMessageStore;
    }

    private void sendChannelAuthChangeMsg(String appKey, Long tenantId, String appPoiCode,  Map<String, Object> tokenMap) {
        try {
            String accessToken = (String) tokenMap.get(ProjectConstant.MT_ACCESS_TOKEN);
            Integer expire = (Integer) tokenMap.get("expires_in");
            String refreshToken = (String) tokenMap.get("refresh_token");
            Integer refreshExpire = (Integer) tokenMap.get("re_expires_in");
            Long currentTime = System.currentTimeMillis();
            ChannelAuthChangeMessage.Token token = ChannelAuthChangeMessage.Token.builder()
                    .accessToken(accessToken)
                    .accessTokenGrantDate(currentTime)
                    .accessTokenExpireDate(currentTime + expire.longValue() * 1000L)
                    .refreshToken(refreshToken)
                    .refreshTokenGrantDate(currentTime)
                    .refreshTokenExpireDate(currentTime + refreshExpire.longValue() * 1000L)
                    .build();
            ChannelAuthChangeMessage channelAuthChangeMessage = ChannelAuthChangeMessage.builder()
                    .changeType("UPDATE_TOKEN")
                    .channelId(ChannelTypeEnum.MEITUAN.getCode())
                    .appKey(appKey)
                    .tenantId(tenantId)
                    .thirdPartyPoiCode(appPoiCode)
                    .token(token)
                    .build();
            boolean send = channelAuthChangeMessageProducer.sendMessageSync(channelAuthChangeMessage);
            if (!send) {
                log.error("BaseChannelGateService.sendMessageSync fail，msg:{}", channelAuthChangeMessage);
            }
        }catch (Exception e){
            log.error("BaseChannelGateService.sendMessageSync 异常，appKey:{}, appPoiCode:{}, tokenMap:{}", appKey, appPoiCode, tokenMap, e);
        }
    }

    private Map<String, Object> mtRefreshToken(BaseRequest baseRequest, Map<String, Object> sysParam, String refresh_token) {
        Map<String, Object> bizParamMapRefresh = new HashMap();
        bizParamMapRefresh.put("grant_type", "refresh_token");
        bizParamMapRefresh.put("refresh_token", refresh_token);
        HashMap<String, Object> sysParamRefreshToken = new HashMap<>(sysParam);
        String urlRefToken = getPostUrl(ChannelPostMTEnum.OAUTH_TOKEN);
        Map<String, Object> postParamToken = generatePostParams(urlRefToken, null,
                sysParamRefreshToken,
                bizParamMapRefresh);
        Map<String, Object> tokenRefreshMap = postUrl(urlRefToken, baseRequest, postParamToken);
        return tokenRefreshMap;
    }

    public MtTokenMessage getIsvTokenFromCache(long tenantId, String appPoiCode) {
        StoreKey storeKey = new StoreKey(ProjectConstant.MT_ACCESS_TOKEN_CACHE, tenantId + appPoiCode);
        return mtTokenRedisStoreClient.get(storeKey);
    }

    private Map<String, Object> mtOauthAuthorize(String appPoiCode, BaseRequest baseRequest,
                                                 Map<String, Object> sysParam) {
        Map<String, Object> bizParamMap = new HashMap();
        bizParamMap.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParamMap.put("response_type", "token");
        HashMap<String, Object> sysParamToken = new HashMap<>(sysParam);
        String urlToken = getPostUrl(ChannelPostMTEnum.OAUTH_AUTHORIZE);
        Map<String, String> postParamToken = generatePostParams(urlToken, null,
                sysParamToken,
                bizParamMap);
        Map<String, Object> tokenMap = getUrl(urlToken,
                baseRequest, postParamToken);
        return tokenMap;
    }


    protected Map<String, Object> getSysParam(BaseRequest baseRequest, Map<String, Object> bizParamMap) {
        Map<String, Object> sysParam = getSysParam(baseRequest);
        String appPoiCode = null;
        if(Objects.nonNull(bizParamMap) &&  Objects.nonNull(bizParamMap.get(ProjectConstant.APP_POI_CODE))) {
            appPoiCode = (String) bizParamMap.get(ProjectConstant.APP_POI_CODE);
        }
        setAccessTokenIfNeed(appPoiCode, baseRequest, sysParam, bizParamMap);
        return sysParam;
    }

    public Map<String, Object> getSysParam(BaseRequest baseRequest) {
        Map<String, Object> sysParam;
        // 获取系统参数
        if (baseRequest.getAppId() > 0) {
            if (baseRequest.getChannelId() == ChannelTypeEnum.ELEM.getCode() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(baseRequest.getStoreIdList())){
                Long storeId = baseRequest.getStoreIdList().get(0) == ProjectConstant.UNKNOW_STORE_ID ? null : baseRequest.getStoreIdList().get(0);
                sysParam = getAppChannelSysParams(baseRequest.getTenantId(), baseRequest.getChannelId(),
                        baseRequest.getAppId(), storeId);
            } else {
                sysParam = getAppChannelSysParams(baseRequest.getTenantId(), baseRequest.getChannelId(),
                        baseRequest.getAppId(), null, baseRequest.getOrderId());
            }
        }
        else {
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(baseRequest.getStoreIdList())) {
                Long storeId = baseRequest.getStoreIdList().get(0);
                String channelStoreKey = KeyUtils.genChannelStoreKey(baseRequest.getTenantId(), baseRequest.getChannelId(), storeId);
                Map<String, ChannelStoreDO> tenantPoiInfo = getTenantPoiInfo(baseRequest);
                sysParam = getAppChannelSysParams(baseRequest.getTenantId(), baseRequest.getChannelId(),
                        tenantPoiInfo.get(channelStoreKey).getAppId(), storeId, baseRequest.getOrderId());
            }
            else {
                sysParam = getChannelSysParams(baseRequest);
                log.warn("基础服务兜底没有门店id及appId的情况baseRequest{}", baseRequest);
            }
        }
        return sysParam;
    }

    /**
     * 各渠道发送请求公共方法
     *
     * @param url      请求URL
     * @param bizParam 请求业务参数
     * @return
     */
    public Map<String, Object> sendPostApp(String url, String method, BaseRequest baseRequest, Object bizParam) {
        // 参数实体转换为Map
        Map<String, Object> bizParamMap = null;
        try {
            if (bizParam != null) {
                if (bizParam instanceof Map) {
                    bizParamMap = (Map<String, Object>) bizParam;
                }
                else {
                    bizParamMap = ConverterUtils.convertToMap(bizParam, baseRequest.getChannelId());
                }
            }
        }
        catch (Exception e) {
            log.error("BaseChannelGateService.sendPost error", e);
        }
        Map<String, Object> sysParam = getSysParam(baseRequest, bizParamMap);

        // 请求
        return sendPost(url, method, baseRequest, bizParam, sysParam);
    }

    /**
     * 多品牌各渠道发送请求公共方法 get
     *
     * @param url         请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam    请求业务参数
     * @return
     */
    public Map<String, Object> sendGetApp(String url, String method, BaseRequest baseRequest, Object bizParam) {

        // 参数校验
        validateSendPost(url, method, baseRequest, bizParam);

        // 参数实体转换为Map
        Map<String, Object> bizParamMap = null;
        if (!(bizParam instanceof Map)) {
            try {
                bizParamMap = ConverterUtils.getProperty(bizParam);
            }
            catch (Exception e) {
                log.error("sendGet 获取bizParamMap异常", e);
            }
        }
        else {
            bizParamMap = (Map<String, Object>) bizParam;
        }

        Map<String, Object> sysParam = getSysParam(baseRequest, bizParamMap);
        // 拼接参数
        Map<String, String> postParam = generatePostParams(url, method, sysParam, bizParamMap);

        // 请求
        return getUrl(url, baseRequest, postParam);
    }


    /***
     * 多品牌各渠道发送请求公共方法
     * 对get的参数进行encode
     * 因为商家退款接口输入特殊字符导致接口调用失败
     * 注意：底层getUrl没有对param进行encode，在这里先对param进行encode，如果后续httpClient的assemble方法也进行了encode，这里需要放开
     * ****/
    public Map<String, Object> sendEncodedGetApp(String url, String method, BaseRequest baseRequest, Object bizParam) {

        // 参数校验
        validateSendPost(url, method, baseRequest, bizParam);

        // 参数实体转换为Map
        Map<String, Object> bizParamMap = null;
        if (!(bizParam instanceof Map)) {
            try {
                bizParamMap = ConverterUtils.getProperty(bizParam);
            }
            catch (Exception e) {
                log.error("sendGet 获取bizParamMap异常", e);
            }
        }
        else {
            bizParamMap = (Map<String, Object>) bizParam;
        }

        Map<String, Object> sysParam = getSysParam(baseRequest, bizParamMap);

        // 拼接参数
        Map<String, String> postParam = generatePostParams(url, method, sysParam, bizParamMap);

        //encode value
        if (bizParamMap != null) {
            bizParamMap.forEach((key, value) -> {
                if (value instanceof String) {
                    postParam.put(key, UrlUtil.urlEnCodeSafe((String) value));
                }
            });
        }
        // 请求
        return getUrl(url, baseRequest, postParam);
    }

    public Map<String, Object> getAppChannelSysParams(Long tenantId, Integer channelId,
                                                      Long appId, Long storeId){
        return getAppChannelSysParams(tenantId, channelId, appId, storeId, null);
    }
    public Map<String, Object> getAppChannelSysParams(Long tenantId, Integer channelId,
                                                      Long appId, Long storeId, String viewOrderId) {

        String sysParamJson = copAccessConfigService.selectAppSysParams(tenantId,
                channelId, appId);
        //双写cop表后，判断appId是否>10000,并且是饿了么应用
        if (channelId == ChannelTypeEnum.ELEM.getCode()  && appId > PoiChannelConstant.ELM_ISV_QNH_APP_ID_BEGIN) {
            //是从cop表里查出来的饿了么isv应用，系统参数只为空，走下面的新接口逻辑
            sysParamJson = null;
        }
        // 饿了么渠道，如果sysParamJson为空，查poiChannel接口
        log.info("sysParamJson before check:{}, storeId:{}", sysParamJson, storeId);
        if (channelId == ChannelTypeEnum.ELEM.getCode() && StringUtils.isBlank(sysParamJson)){

            AppInfoDTO appInfoDTO = null;
            // 饿了么ISV请求渠道，通过 牵牛花应用appId 或者 牵牛花门店 storeId 查询应用信息
            if (Objects.nonNull(storeId)) {
                appInfoDTO = poiChannelAppThriftServiceProxy.queryAppInfoByPoi(new AppInfoQueryByPoiRequest(tenantId, storeId, channelId));
            } else if (Objects.nonNull(appId)) {
                appInfoDTO = poiChannelAppThriftServiceProxy.queryAppInfoByAppId(new AppInfoQueryByAppIdRequest(tenantId, appId, channelId));
            }

            if (appInfoDTO != null) {
                Map<String, Object> poiChannelSysParamMap = new HashMap<>();
                poiChannelSysParamMap.put("source", appInfoDTO.getAppKey());
                poiChannelSysParamMap.put("secret", appInfoDTO.getSecret());
                poiChannelSysParamMap.put("version", 3);
                poiChannelSysParamMap.put("access_token", appInfoDTO.getAccessToken());

                sysParamJson = JacksonUtils.toJson(poiChannelSysParamMap);
            }
        }

        Preconditions.checkArgument(StringUtils.isNotBlank(sysParamJson), "copAccessConfig error tenantId=%s," +
                "channelId=%s,appId=%s, storeId=%s, sysParamJson=%s", tenantId, channelId, appId, storeId, sysParamJson);
        if (storeId != null) {
            String qnhParamJson = MccConfigUtil.getQnhSysCodeStr(tenantId, storeId);
            if (StringUtils.isNotEmpty(qnhParamJson)) {
                log.info("sysParamJson:{},qnhParamJson:{}", sysParamJson, qnhParamJson);
                sysParamJson = qnhParamJson;
            }
        }
        // 美团渠道，歪马租户，做一下篡门店兼容
        if (BusinessIdTracer.isDrunkHorseTenant(tenantId) && MccConfigUtil.isMtChannelWxMallPoiSwitchOpen()){
            DrunkHorseWrongPoiMapping wrongPoiMapping = drunkHorseCacheService.getCrossOrderMapping(viewOrderId);
            if (wrongPoiMapping != null){
                // 微商城门店在闪购渠道下单，上行接口需要用微商城门店的签名信息
                String drunkHorseParamJson = copAccessConfigService.selectAppSysParams(tenantId, wrongPoiMapping.getTargetChannel(), wrongPoiMapping.getAppId());
                if (StringUtils.isNotBlank(drunkHorseParamJson)){
                    log.info("歪马门店跨渠道，获取config. sysParamJson:{}, drunkHorseParamJson:{}", sysParamJson, drunkHorseParamJson);
                    sysParamJson = drunkHorseParamJson;
                }
            }
        }
        Map<String, Object> sysParam = JSON.parseObject(sysParamJson);
        return sysParam;
    }


    /**
     * 各渠道发送请求公共方法
     *
     * @param postUrlEnum 请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam    请求业务参数
     * @return
     */
    public <T> T sendPost(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {

        // 参数校验
        validateSendPost(postUrlEnum, bizParam);

        if (bizParam instanceof OrderIdAware){
            baseRequest.setOrderId(((OrderIdAware) bizParam).getOrderId());
        }

        // 获取系统参数
        Map<String, Object> sysParam = getChannelSysParams(baseRequest);

        // 业务参数实体转换为Map
        Map<String, Object> bizParamMap = ConverterUtils.convertToMap(bizParam, baseRequest.getChannelId());

        // 基础数据拉取时，没有门店数据直接返回结果对象
        if (CollectionUtils.isEmpty(baseRequest.getStoreIdList())) {
            return postToChannel(postUrlEnum, sysParam, bizParamMap, baseRequest);
        }

        // 有门店信息接口调用时，多门店请求，返回以门店id为key的map
        Map<Long, T> res = Maps.newHashMap();
        Map<String, Object> finalBizParamMap = bizParamMap;
        Map<String, ChannelStoreDO> pois = getTenantPoiInfo(baseRequest);
        baseRequest.getStoreIdList().forEach(storeId -> {
            String channelStoreKey = KeyUtils.genChannelStoreKey(baseRequest.getTenantId(), baseRequest.getChannelId(), storeId);
            T object = postToChannel(pois.get(channelStoreKey).getChannelPoiCode(), pois.get(channelStoreKey).getChannelOnlinePoiCode(), postUrlEnum, sysParam, finalBizParamMap, baseRequest);
            res.put(storeId, object);
        });
        return (T) res;
    }

    /**
     * 各渠道发送请求公共方法
     * 备注：通过虚拟账号信息发送请求到指定的渠道
     *
     * @param postUrlEnum 请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam    请求业务参数
     * @return
     */
    public <T> T sendPostByVirtualConfig(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {
        return null;
    }

    /**
     * BaseRequestSimple参数请求方法入口
     *
     * @param postUrlEnum
     * @param baseRequestSimple
     * @param storeId
     * @param bizParam
     * @param <T>
     * @return
     */
    public <T> T sendPost(ChannelPostInter postUrlEnum, BaseRequestSimple baseRequestSimple, Long storeId, Object bizParam) {
        BaseRequest baseRequest = baseConverterService.baseRequest(baseRequestSimple);
        if (storeId != null) {
            baseRequest.setStoreIdList(Lists.newArrayList(storeId));
        }

        if (bizParam instanceof OrderIdAware){
            baseRequest.setOrderId(((OrderIdAware) bizParam).getOrderId());
        }

        return sendPost(postUrlEnum, baseRequest, bizParam);
    }

    /**
     * BaseRequestSimple参数请求方法入口
     * 备注：通过虚拟账号信息发送请求到指定的渠道
     *
     * @param postUrlEnum
     * @param baseRequestSimple
     * @param storeId
     * @param bizParam
     * @param <T>
     * @return
     */
    public <T> T sendPostByVirtualConfig(ChannelPostInter postUrlEnum, BaseRequestSimple baseRequestSimple, Long storeId, Object bizParam) {
        BaseRequest baseRequest = baseConverterService.baseRequest(baseRequestSimple);
        if (storeId != null) {
            baseRequest.setStoreIdList(Lists.newArrayList(storeId));
        }

        return sendPostByVirtualConfig(postUrlEnum, baseRequest, bizParam);
    }

    /**
     * BaseRequestSimple参数请求方法入口
     *
     * @param postUrlEnum
     * @param tenantId
     * @param channelId
     * @param storeId
     * @param bizParam
     * @param <T>
     * @return
     */
    public <T> T sendPost(ChannelPostInter postUrlEnum, Long tenantId, Integer channelId, Object bizParam, Long... storeId) {
        BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(channelId);
        if (storeId.length > 0) {
            baseRequest.setStoreIdList(Lists.newArrayList(storeId[0]));
        }
        if (bizParam instanceof OrderIdAware){
            baseRequest.setOrderId(((OrderIdAware) bizParam).getOrderId());
        }

        return sendPost(postUrlEnum, baseRequest, bizParam);
    }

    /**
     * @param channelPoiCode       渠道门店编码
     * @param channelOnlinePoiCode 渠道门店编码（三方）
     * @param postUrlEnum          接口枚举
     * @param sysParam             系统参数
     * @param bizParamMap          业务参数
     * @param baseRequest          请求基本信息
     * @param <T>                  返回类型
     * @return
     */
    protected abstract <T> T postToChannel(String channelPoiCode, String channelOnlinePoiCode, ChannelPostInter postUrlEnum, Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest);

    protected <T> T postToChannel(ChannelPostInter postUrlEnum, Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest) {
        return postToChannel(null, null, postUrlEnum, sysParam, bizParamMap, baseRequest);
    }

    private void validateSendPost(ChannelPostInter postUrlEnum, Object bizParam) {
        Preconditions.checkArgument(StringUtils.isNotBlank(postUrlEnum.getUrl()), "sendPost url is blank");
        Preconditions.checkNotNull(postUrlEnum.getResultClass(), "sendPost ResultClass is null");
        Preconditions.checkNotNull(bizParam, "sendPost bizParam is blank");
    }

    protected String postRequest(String url, Map<String, Object> postParams, BaseRequest baseRequest) {
        return postRequest(url, MccConfigUtil.getHttpSocketTimeOut(), postParams, baseRequest);
    }

    protected String postRequest(String url, Map<String, Object> postParams, BaseRequest baseRequest, boolean isCleaner) {
        return postRequest(url, MccConfigUtil.getHttpSocketTimeOut(), postParams, baseRequest, isCleaner, true);
    }


    protected String postRequest(ChannelPostInter postUrlEnum, Map<String, Object> postParams, BaseRequest baseRequest) {
        // 图片上传不打印具体参数
        boolean printPostParams = true;
        if (postUrlEnum == ChannelPostMTEnum.PICTURE_UPLOAD || postUrlEnum == ChannelPostELMEnum.PICTURE_UPLOAD) {
            printPostParams = false;
        }

        return postRequest(getPostUrl(postUrlEnum), MccConfigUtil.getHttpSocketTimeOut(postUrlEnum.getSocketTimeoutMccKey()), postParams,
                baseRequest, postUrlEnum.isCleaner(), printPostParams);
    }

    protected String postRequest(String postUrl, int httpSocketTimeOut, Map<String, Object> postParams, BaseRequest baseRequest) {
        return postRequest( postUrl,  httpSocketTimeOut, postParams,  baseRequest, false, true);
    }

    private String postRequest(String postUrl, int httpSocketTimeOut, Map<String, Object> postParams, BaseRequest baseRequest,
                               boolean isCleaner, boolean printPostParams) {
        String result = HttpClientUtil.post(postUrl, MccConfigUtil.getHttpConnectionTimeOut(), httpSocketTimeOut, postParams, null, isCleaner);

        if (printPostParams) {
            log.info("BaseChannelGateService.postRequest(), http post, postUrl:{}, baseRequest:{}, isCleaner:{}, postParams:{},  result:{}", postUrl, baseRequest, isCleaner, postParams, result);
        }
        else {
            log.info("BaseChannelGateService.postRequest(), http post, postUrl:{}, baseRequest:{}, isCleaner:{},  result:{}", postUrl, baseRequest, isCleaner, result);

        }
        return result;
    }

    protected String postRequest(ChannelPostInter postUrlEnum, Map<String, Object> postParams,
                                 BaseRequest baseRequest, Map<String, String> httpHeaders){
        boolean isCleaner = postUrlEnum.isCleaner();
        String result = HttpClientUtil.post(getPostUrl(postUrlEnum), MccConfigUtil.getHttpConnectionTimeOut(),
                MccConfigUtil.getHttpSocketTimeOut(postUrlEnum.getSocketTimeoutMccKey()), postParams, httpHeaders, null, isCleaner);
        log.info("BaseChannelGateService.postRequest(), http post, postUrl:{}, baseRequest:{}, isCleaner:{}, postParams:{},  result:{}", postUrlEnum, baseRequest, isCleaner, postParams , result);
        return result;
    }



    public String postRequestByJson(String postUrl, Map<String, Object> postParams, Map<String, String> httpHeaders,
                                    BaseRequest baseRequest) {
        String result = HttpClientUtil.postJson(postUrl, MccConfigUtil.getHttpConnectionTimeOut(),
                MccConfigUtil.getHttpSocketTimeOut(), JSON.toJSONString(postParams), httpHeaders);
        log.info("BaseChannelGateService.postRequest(), http post, postUrl:{}, baseRequest:{}, postParams:{}, " +
                "httpHeaders:{}, result:{}", postUrl, baseRequest, postParams, httpHeaders, result);
        return result;

    }

    protected String postRequest(ChannelPostInter postUrlEnum, Map<String, Object> postParams, List<String> fileNames, BaseRequest baseRequest) {
        boolean isCleaner = postUrlEnum.isCleaner();
        String result = HttpClientUtil.post(getPostUrl(postUrlEnum),
                MccConfigUtil.getHttpConnectionTimeOut(), MccConfigUtil.getHttpSocketTimeOut(postUrlEnum.getSocketTimeoutMccKey()), postParams, fileNames, isCleaner);

        // 图片上传不打印具体参数
        if (postUrlEnum == ChannelPostMTEnum.PICTURE_UPLOAD || postUrlEnum == ChannelPostELMEnum.PICTURE_UPLOAD) {
            log.info("BaseChannelGateService.postRequest(), http post, postUrlEnum:{}, baseRequest:{}, fileNames:{}, isCleaner:{}, result:{}", postUrlEnum, baseRequest, fileNames, isCleaner, result);
        }
        else {
            log.info("BaseChannelGateService.postRequest(), http post, postUrlEnum:{}, baseRequest:{}, postParams:{}, fileNames:{}, isCleaner:{}, result:{}", postUrlEnum, baseRequest, postParams, fileNames, isCleaner, result);
        }
        return result;
    }

    /* --------------------以后要替换掉-------------------------*/

    /**
     * 各渠道发送请求公共方法
     *
     * @param url      请求URL
     * @param bizParam 请求业务参数
     * @return
     */
    @Deprecated
    public Map<String, Object> sendPost(String url, String method, BaseRequest baseRequest, Object bizParam) {
        // 获取系统参数
        Map<String, Object> sysParam = getChannelSysParams(baseRequest);

        // 请求
        return sendPost(url, method, baseRequest, bizParam, sysParam);
    }

    /**
     * 通过虚拟的系统配置信息向各渠道发送请求
     *
     * @param url      请求URL
     * @param bizParam 请求业务参数
     * @return
     */
    public Map<String, Object> sendPostByVirtualConfig(String url, String method, BaseRequest baseRequest, Object bizParam) {
        // 获取系统参数
        Map<String, Object> sysParam = getChannelVirtualSysParam(baseRequest.getTenantId(), baseRequest.getChannelId());

        // 请求
        return sendPost(url, method, baseRequest, bizParam, sysParam);
    }

    /**
     * 通过虚拟的系统配置信息向各渠道发送请求
     * 和sendPostByVirtualConfig方法区别是前者根据租户查虚拟系统参数，后者直接传递
     * @param url      请求URL
     * @param bizParam 请求业务参数
     * @return
     */
    public Map<String, Object> sendPostByVirtualSysParam(String url, String method, BaseRequest baseRequest,
                                                         Object bizParam, Map<String, Object> sysParam) {

        // 请求
        return sendPost(url, method, baseRequest, bizParam, sysParam);
    }

    /**
     * 通过虚拟的系统配置信息向各渠道发送请求公共方法门店维度 post
     *
     * @param postUrlEnum 请求URL
     * @param baseRequest 请求U基本参数
     * @param bizParam    请求业务参数
     * @return
     */
    public <T> T sendPostAppMapDtoByVirtualConfig(ChannelPostInter postUrlEnum, BaseRequest baseRequest, Object bizParam) {

        // 参数校验
        validateSendPost(postUrlEnum, bizParam);

        // 业务参数实体转换为Map
        Map<String, Object> bizParamMap = ConverterUtils.convertToMap(bizParam, baseRequest.getChannelId());

        // 通过虚拟配置返回渠道门店和系统参数
        ChannelStoreDO poi = getChannelVirtualPoiParamByLion(baseRequest);
        Map<String, Object> appChannelSysParams = getChannelVirtualSysParamByLion();

        // 构造参数
        Map<Long, T> res = Maps.newHashMap();
        Map<String, Object> finalBizParamMap = bizParamMap;
        long tenantId = baseRequest.getTenantId();
        int channelId = baseRequest.getChannelId();
        baseRequest.getStoreIdList().forEach(storeId -> {
            String channelOnlinePoiCode = poi.getChannelOnlinePoiCode();
            BaseRequest baseRequest1 = new BaseRequest();
            baseRequest1.setChannelId(channelId);
            baseRequest1.setTenantId(tenantId);
            baseRequest1.setStoreIdList(Arrays.asList(storeId));
            setAccessTokenIfNeed(channelOnlinePoiCode, baseRequest1, appChannelSysParams, finalBizParamMap);
            T object = postToChannel(poi.getChannelPoiCode(), channelOnlinePoiCode, postUrlEnum, appChannelSysParams, finalBizParamMap, baseRequest);
            res.put(storeId, object);
        });

        return (T) res;
    }

    /**
     * 通过线上Lion配置获取虚拟的系统参数
     */
    protected Map<String, Object> getChannelVirtualSysParamByLion() {
        return null;
    }

    /**
     * 通过线上Lion配置获取虚拟的门店参数
     */
    protected ChannelStoreDO getChannelVirtualPoiParamByLion(BaseRequest baseRequest) {
        return null;
    }

    /**
     * 获取渠道虚拟系统参数
     */
    protected Map<String, Object> getChannelVirtualSysParam(Long tenantId, Integer channelId) {
        return null;
    }

    /**
     * 各渠道发送请求公共方法
     *
     * @param url      请求URL
     * @param bizParam 请求业务参数
     * @return
     */
    public Map<String, Object> sendPost(String url, String method, BaseRequest baseRequest, Object bizParam, Map<String, Object> systemParam) {

        // 参数校验
        validateSendPost(url, method, baseRequest, bizParam);

        // 参数实体转换为Map
        Map<String, Object> bizParamMap = null;
        try {
            if (bizParam != null) {
                if (bizParam instanceof Map) {
                    bizParamMap = (Map<String, Object>) bizParam;
                }
                else {
                    bizParamMap = ConverterUtils.convertToMap(bizParam, baseRequest.getChannelId());
                }
            }
        }
        catch (Exception e) {
            log.error("BaseChannelGateService.sendPost error", e);
        }

        // 拼接参数
        Map<String, Object> postParam = generatePostParams(url, method, systemParam, bizParamMap);

        // 请求
        return postUrl(url, baseRequest, postParam);
    }
    /* --------------------以后要替换掉 end-------------------------*/


    public Map<String, Object> sendGet(String url, String method, BaseRequest baseRequest, Object bizParam) {

        // 参数校验
        validateSendPost(url, method, baseRequest, bizParam);

        if (bizParam instanceof OrderIdAware){
            baseRequest.setOrderId(((OrderIdAware) bizParam).getOrderId());
        }

        // 参数实体转换为Map
        Map<String, Object> bizParamMap = null;
        if (!(bizParam instanceof Map)) {
            try {
                bizParamMap = ConverterUtils.getProperty(bizParam);
            }
            catch (Exception e) {
                log.error("sendGet 获取bizParamMap异常", e);
            }
        }
        else {
            bizParamMap = (Map<String, Object>) bizParam;
        }

        // 获取系统参数
        Map<String, Object> sysParam = getChannelSysParams(baseRequest);

        // 拼接参数
        Map<String, String> postParam = generatePostParams(url, method, sysParam, bizParamMap);

        // 请求
        return getUrl(url, baseRequest, postParam);
    }

    /***
     * 对get的参数进行encode
     * 因为商家退款接口输入特殊字符导致接口调用失败
     * 注意：底层getUrl没有对param进行encode，在这里先对param进行encode，如果后续httpClient的assemble方法也进行了encode，这里需要放开
     * ****/
    public Map<String, Object> sendEncodedGet(String url, String method, BaseRequest baseRequest, Object bizParam) {

        // 参数校验
        validateSendPost(url, method, baseRequest, bizParam);

        // 参数实体转换为Map
        Map<String, Object> bizParamMap = null;
        if (!(bizParam instanceof Map)) {
            try {
                bizParamMap = ConverterUtils.getProperty(bizParam);
            }
            catch (Exception e) {
                log.error("sendGet 获取bizParamMap异常", e);
            }
        }
        else {
            bizParamMap = (Map<String, Object>) bizParam;
        }

        // 获取系统参数
        Map<String, Object> sysParam = getChannelSysParams(baseRequest);

        // 拼接参数
        Map<String, String> postParam = generatePostParams(url, method, sysParam, bizParamMap);

        //encode value
        if (bizParamMap != null) {
            bizParamMap.forEach((key, value) -> {
                if (value instanceof String) {
                    postParam.put(key, UrlUtil.urlEnCodeSafe((String) value));
                }
            });
        }
        // 请求
        return getUrl(url, baseRequest, postParam);
    }


    protected Map<String, Object> postUrl(String url, BaseRequest baseRequest, Map<String, Object> postParam) {
        String result = HttpClientUtil.post(url,
                MccConfigUtil.getHttpConnectionTimeOut(), MccConfigUtil.getHttpSocketTimeOut(), postParam);
        log.info("BaseChannelGateService.postUrl(), http post, url:{}, baseRequest:{}, postParam:{}, result:{}", url, baseRequest, postParam, result);
        return JSON.parseObject(result);
    }

    protected Map<String, Object> getUrl(String url, BaseRequest baseRequest, Map<String, String> postParam) {
        String result = HttpClientUtil.get(url,
                MccConfigUtil.getHttpConnectionTimeOut(), MccConfigUtil.getHttpSocketTimeOut(), postParam);
        log.info("BaseChannelGateService.getUrl(), http get, url:{}, baseRequest:{}, postParam:{}, result:{}", url, baseRequest, postParam, result);
        return JSON.parseObject(result);
    }

    protected abstract Map generatePostParams(String url, String method, Map<String, Object> sysParam, Map<String, Object> bizParam);

    protected abstract String getPostUrl(ChannelPostInter postUrlEnum);

    protected void validateSendPost(String url, String method, BaseRequest baseRequest, Object bizParam) {
        Preconditions.checkArgument(StringUtils.isNotBlank(url), "sendPost url is blank");
        Preconditions.checkNotNull(baseRequest, "sendPost baseRequest is blank");

        Integer channelId = baseRequest.getChannelId();
        Preconditions.checkArgument(ChannelTypeEnum.abbrevMap.containsKey(channelId), "sendPost channelId=%s is not correct", channelId);
        Preconditions.checkArgument(ChannelTypeEnum.ELEM.getCode() != channelId || StringUtils.isNotBlank(method), "sendPost elm method=%s is null");
    }

    @Deprecated
    //在没传storeId时，部分租户会有多个sysParamJson，导致报错，故该方法废弃
    public Map<String, Object> getChannelSysParams(BaseRequest baseRequest) {
        String sysParamJson = copAccessConfigService.selectSysParams(baseRequest.getTenantId(), baseRequest.getChannelId());
        Preconditions.checkArgument(StringUtils.isNotBlank(sysParamJson), "copAccessConfig error tenantId=%s,channelId=%s", baseRequest.getTenantId(), baseRequest.getChannelId());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(baseRequest.getStoreIdList())) {
            String qnhParamJson = MccConfigUtil.getQnhSysCodeStr(baseRequest.getTenantId(), baseRequest.getStoreIdList().get(0));
            if (StringUtils.isNotEmpty(qnhParamJson)) {
                log.info("sysParamJson:{},qnhParamJson:{}", sysParamJson, qnhParamJson);
                sysParamJson = qnhParamJson;
            }
        }


        // 美团渠道，歪马租户，做一下篡门店兼容
        if (BusinessIdTracer.isDrunkHorseTenant(baseRequest.getTenantId())
                && MccConfigUtil.isMtChannelWxMallPoiSwitchOpen()){
            DrunkHorseWrongPoiMapping wrongPoiMapping = drunkHorseCacheService.getCrossOrderMapping(baseRequest.getOrderId());
            if (wrongPoiMapping != null){
                // 微商城门店在闪购渠道下单，上行接口需要用微商城门店的签名信息
                String drunkHorseParamJson = copAccessConfigService.selectSysParams(baseRequest.getTenantId(), wrongPoiMapping.getTargetChannel());
                if (StringUtils.isNotBlank(drunkHorseParamJson)){
                    log.info("sysParamJson:{}, drunkHorseParamJson:{}", sysParamJson, drunkHorseParamJson);
                    sysParamJson = drunkHorseParamJson;
                }
            }
        }
        Map<String, Object> sysParam = JSON.parseObject(sysParamJson);

        return sysParam;
    }

    //在没传storeId时,使用该方法获取渠道系统参数
    public Map<String, Object> getChannelSysParams(Long tenantId, Integer channelId) {
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if(CollectionUtils.isEmpty(copAccessConfigDOList)){
            log.error("CopAccessConfigService.findTenantChannelConfigApp 渠道系统参数获取失败，tenantId:{},channelId:{}", tenantId, channelId);
            throw new BizException("渠道系统参数获取失败");
        }
        //取任意一个系统参数
        String sysParamJson = copAccessConfigDOList.get(0).getSysParams();
        Preconditions.checkArgument(StringUtils.isNotBlank(sysParamJson), "copAccessConfig error tenantId=%s,channelId=%s", tenantId, channelId);
        return JSON.parseObject(sysParamJson);
    }

    protected Map<String, ChannelStoreDO> getTenantPoiInfo(BaseRequest baseRequest) {
        Map<String, ChannelStoreDO> pois = copChannelStoreService.getChannelPoiCode(baseRequest.getTenantId(), baseRequest.getChannelId(), baseRequest.getStoreIdList());
        Preconditions.checkNotNull(pois, ChannelResultStatusEnum.VALIDATE_TELNET_ERROR.getDesc(), baseRequest.storeIdList.toString());
        Preconditions.checkArgument(pois.size() == baseRequest.getStoreIdList().size(), ChannelResultStatusEnum.VALIDATE_TELNET_ERROR.getDesc(), baseRequest.storeIdList.toString());
        pois.values().forEach(t -> {
            Preconditions.checkArgument(StringUtils.isNotBlank(t.getChannelOnlinePoiCode()), ChannelResultStatusEnum.VALIDATE_TELNET_ERROR.getDesc(), t.getStoreId());
        });

        return pois;
    }

    /**
     * 通过租户和渠道ID查询渠道虚拟接入配置信息
     * @param tenantId
     * @param channelId
     * @return
     */
    public ChannelVirtualAccessConfigDO selectByTenantIdAndChannelId(Long tenantId, Integer channelId) {
        return new ChannelVirtualAccessConfigDO();
    }

    public List<ChannelVirtualAccessConfigDO> pageQueryTenantChannelConfig(VirtualAccessConfigListPageQueryDTO pageQuery) {
        return new ArrayList<>();
    }

    /**
     * 基于租户+渠道+应用ID三元组（三个参数必填）获取渠道参数
     *
     * @param tenantId
     * @param channelId
     * @param appId
     * @return
     */
    public Map<String, Object> getSysParamByChannelIdAndAppId(long tenantId, int channelId, long appId) {
        BaseRequest request = new BaseRequest();
        request.setTenantId(tenantId);
        request.setChannelId(channelId);
        request.setAppId(appId);
        return getSysParam(request);
    }

    @SuppressWarnings("deprecation")
    protected <R> void channelResponseMetric(ChannelTypeEnum channel, ChannelPostInter api, BaseRequest request, R response, Function<R, String> getErrorMessage) {
        try {
            if (!MccConfigUtil.getChannelApiErrorMetricEnabled()) {
                return;
            }
            channelErrorMetric.report(channel, api, request, response, getErrorMessage);
        }
        catch (Exception e) {
            log.warn("channelResponseMetric exception", e);
        }
    }
}

