package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.meituan.linz.boot.util.Strings;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.EblsImMessageContentTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ebls.IMMessageContent;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ebls.IMMessagePayload;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ebls.SendMessageRespond;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.IEBLSMessageService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.HttpClientUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.StrUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.security.sdk.SecSdk;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.protocol.HTTP;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.imageio.stream.ImageInputStream;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @AUTHOR fengjunkai03
 * @DATE 2024/2/22
 */
@Service("EBLSMessageService")
@Slf4j
public class EBLSMessageServiceImpl implements IEBLSMessageService {

    private static final Integer extendTime = 86400;

    private static final Integer MAX_MEDIA_SIZE = 3*1024*1024;


    @Value("${elm.url.base}")
    private String baseUrl;

    @Value("${elm.url.mediaUpload}")
    private String mediaUploadUrl;

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private ElmChannelSpuServiceImpl elmChannelSpuService;

    @Override
    public SendMessageRespond sendMessage(IMMessageContent message) {
        message.setCmd("im.message.send");
        message.setVersion("3");
        message.setTicket(UUID.randomUUID().toString().toUpperCase());
        try {
            queryAppidAndSecret(message);
            String contentType = message.getBody().getPayload().getContentType();
            if (EblsImMessageContentTypeEnum.TEXT.getCode().equals(contentType)) {
                return send2Elm(message);
            } else if (EblsImMessageContentTypeEnum.CUSTOM.getCode().equals(contentType)) {
                convertSpu2ItemId(message);
                return send2Elm(message);
            } else {
                // 含有多媒体的消息，需要先上传媒体文件，转换为媒体文件ID之后，再发送。
                convertUrl2MediaId(message);
                return send2Elm(message);
            }
        }catch (Exception e){
            SendMessageRespond sendMessageRespond = new SendMessageRespond();
            sendMessageRespond.setErrCode(-1);
            sendMessageRespond.setErrMsg(e.getMessage());
            return sendMessageRespond;
        }
    }

    /**
     * 查询appid和secret
     * @param message
     */
    private void queryAppidAndSecret(IMMessageContent message){

        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(message.getTenantID(),
                ChannelType.ELEM.getValue(), message.getPoiId());
        Map<String, Object> appChannelSysParams = elmChannelGateService.getAppChannelSysParams(message.getTenantID(),
                ChannelType.ELEM.getValue(), channelStore.getAppId(), message.getPoiId());
        message.setSource(String.valueOf(appChannelSysParams.get("source")));
        if (appChannelSysParams.containsKey("access_token")) {
            message.setAccess_token(String.valueOf(appChannelSysParams.get("access_token")));
        }
        String secret = String.valueOf(appChannelSysParams.get("secret"));
        message.setSecret(secret);
    }

    /**
     * 将spuId转换为itemId
     * @param message
     * @return
     */
    private void convertSpu2ItemId(IMMessageContent message) {
        String content = message.getBody().getPayload().getContent();
        JSONObject originContentJson = JSONObject.parseObject(content);
        JSONObject contentJson = new JSONObject();

        // 商品卡片需要把spu转换成itemId
        GetSpuInfoRequest request = new GetSpuInfoRequest(new BaseRequestSimple(message.getTenantID(), 200),
                message.getPoiId(), originContentJson.getString("spuId"), "", null);
        Long itemId = elmChannelSpuService.getSpuItemId(request);
        if (Objects.isNull(itemId)) {
            Cat.logEvent("ELM_IM_EXCEPTION", "CUSTOMER_SPU_NOT_FOUND_ITEM");
            throw new RuntimeException("未查询到商品信息");
        }
        JSONObject itemIdJson = new JSONObject();
        itemIdJson.put("itemId", itemId);
        contentJson.put("data", itemIdJson.toJSONString());
        contentJson.put("type", originContentJson.get("type"));
        message.getBody().getPayload().setContent(contentJson.toJSONString());
    }

    /**
     * 将url转换为mediaId
     * @param message
     * @return
     */
    private void convertUrl2MediaId(IMMessageContent message) throws IOException {

        String originCmd = message.getCmd();
        String originVersion = message.getVersion();
        String originTicket = message.getTicket();
        String originSubBizType = message.getBody().getSubBizType();
        message.setCmd("im.upload.media");
        message.setVersion("3");
        if(StringUtils.isEmpty(originTicket)) {
            message.setTicket(UUID.randomUUID().toString().toUpperCase());
        }
        message.getBody().setSubBizType("UPLOAD_MEDIA");

        uploadMediaAndResetContent(message);
        message.setCmd(originCmd);
        message.setVersion(originVersion);
        message.getBody().setSubBizType(originSubBizType);
    }

    /**
     * 将message中的mediaUrl，替换为mediaId
     * @param message
     * @throws IOException
     */
    private void uploadMediaAndResetContent(IMMessageContent message) throws IOException {
        String content = message.getBody().getPayload().getContent();
        JSONObject contentJson = JSONObject.parseObject(content);

        JSONObject bodyJson = new JSONObject();
        bodyJson.put("platformShopId",message.getBody().getPlatformShopId());
        bodyJson.put("bizType",message.getBody().getBizType());
        bodyJson.put("subBizType","UPLOAD_MEDIA");
        byte[] fileBytes  = null;
        byte[] fileBytesCover  = null;
        //图片格式，需要指定格式类型
        if(EblsImMessageContentTypeEnum.IMAGE.getCode().equals(message.getBody().getPayload().getContentType())) {
            String mediaUrl = contentJson.getString("mediaUrl");
            fileBytes  = downLoadFile(mediaUrl);
            ImageInputStream iis = ImageIO.createImageInputStream(new ByteArrayInputStream(fileBytes));
            String FormatName = ImageIO.getImageReaders(iis).next().getFormatName();
            if ("JPEG".equals(FormatName)) {
                contentJson.put("fileType", 0);
            } else if ("png".equals(FormatName)) {
                contentJson.put("fileType", 2);
            } else {
                throw new RuntimeException("上传文件格式异常，只支持JPG和PNG格式");
            }
            bodyJson.put("type", contentJson.getInteger("fileType"));
            contentJson.put("size",fileBytes.length);
            contentJson.put("mediaUrl",null);
        }else if (EblsImMessageContentTypeEnum.VOICE.getCode().equals(message.getBody().getPayload().getContentType())){
            String mediaUrl = contentJson.getString("mediaUrl");
            fileBytes  = downLoadFile(mediaUrl);
            contentJson.put("mediaUrl",null);
            //音频就支持者一种格式
            bodyJson.put("type", 4);

        }else if(EblsImMessageContentTypeEnum.VIDEO.getCode().equals(message.getBody().getPayload().getContentType())){
            String mediaUrl = contentJson.getString("mediaUrl");
            String coverUrl = contentJson.getString("coverUrl");
            fileBytes  = downLoadFile(mediaUrl);
            fileBytesCover  = downLoadFile(coverUrl);
            //视频只支持mp4
            bodyJson.put("type", 6);
        }
        String bodyString = bodyJson.toJSONString();
        bodyString = bodyString.replaceAll("/", "\\/");
        message.setEncrypt("");
        String sign = computeSign(message,bodyString,true);
        message.setSign(sign);

        if(EblsImMessageContentTypeEnum.IMAGE.getCode().equals(message.getBody().getPayload().getContentType())){
            String mediaBase64 = Base64.getEncoder().encodeToString(fileBytes);
            String mediaId = uploadFile(message,mediaBase64,bodyString);
            contentJson.put("mediaId", mediaId);
        }else if(EblsImMessageContentTypeEnum.VOICE.getCode().equals(message.getBody().getPayload().getContentType())){
            String mediaBase64 = Base64.getEncoder().encodeToString(fileBytes);
            String mediaId = uploadFile(message,mediaBase64,bodyString);
            contentJson.put("mediaId", mediaId);
        } else if(EblsImMessageContentTypeEnum.VIDEO.getCode().equals(message.getBody().getPayload().getContentType())){
            String mediaBase64 = Base64.getEncoder().encodeToString(fileBytes);
            String coverBase64 = Base64.getEncoder().encodeToString(fileBytesCover);
            String mediaId = uploadFile(message,mediaBase64,bodyString);
            String coverMediaId = uploadFile(message, coverBase64,bodyString);
            contentJson.put("videoMediaId", mediaId);
            contentJson.put("coverMediaId", coverMediaId);
        }
        message.getBody().getPayload().setContent(contentJson.toJSONString());
    }

    private String uploadFile(IMMessageContent message,String mediaBase64,String bodyString) throws UnsupportedEncodingException {
        if(StringUtils.isBlank(mediaBase64)){
            return null;
        }
        bodyString = urldataEncode(bodyString);
        mediaBase64= urldataEncode(mediaBase64);
        StringBuffer requestParam = new StringBuffer();
        if (StringUtils.isNotBlank(message.getAccess_token())) {
            requestParam.append("access_token=").append(message.getAccess_token()).append("&");
        }
        requestParam.append("cmd=").append(message.getCmd()).append("&");
        requestParam.append("version=").append(message.getVersion()).append("&");
        requestParam.append("timestamp=").append(message.getTimestamp()).append("&");
        requestParam.append("ticket=").append(message.getTicket()).append("&");
        requestParam.append("source=").append(message.getSource()).append("&");
        requestParam.append("sign=").append(message.getSign()).append("&");
        requestParam.append("body=").append(bodyString).append("&");
        requestParam.append("encrypt=").append(message.getEncrypt()).append("&");
        requestParam.append("file_type=").append(2).append("&");

        requestParam.append("file_data=").append(mediaBase64);
        JSONObject resultObject = uploadFile2EblsIM(requestParam.toString());
        String mediaId = resultObject.getJSONObject("body").getJSONObject("data").getString("mediaId");
        return mediaId;
    }

    private byte[] downLoadFile(String mediaUrl) throws IOException {
        URL url = new URL(mediaUrl);
        URLConnection connection = url.openConnection();
        InputStream inputStream = connection.getInputStream();

        BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
        ByteArrayOutputStream fileOutputStream = new ByteArrayOutputStream(10*1024);
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
            fileOutputStream.write(buffer, 0, bytesRead);
            if(fileOutputStream.size() > MAX_MEDIA_SIZE){
                Cat.logEvent("ELM_IM_EXCEPTION", "UPLOAD_FILE_OVERSIZE");
                throw new RuntimeException("上传文件过大，上传文件最大不能超过"+MAX_MEDIA_SIZE+"Byte");
            }
        }
        byte[] fileBytes = fileOutputStream.toByteArray();
        return fileBytes;
    }

    /**
     * 向饿了么发送消息已读
     * @param message
     * @return
     */
    @Override
    public void setMessageRead(IMMessageContent message) {
        message.setCmd("im.message.read");
        message.setVersion("3");
        message.setTicket(UUID.randomUUID().toString().toUpperCase());
        IMMessagePayload payload = message.getBody().getPayload();
        queryAppidAndSecret(message);
        if(CollectionUtils.isNotEmpty(payload.getMsgIdList())){
            List<String> msgIdList = payload.getMsgIdList();
            //防止干扰
            payload.setMsgIdList(null);
            for(String msgId : msgIdList){
                payload.setMsgId(msgId);
                send2Elm(message);
            }
        }else if(StringUtils.isNotBlank(payload.getMsgId())) {
            send2Elm(message);
        }
    }

    /**
     * 将mediaId转换为url
     *
     * @param msg
     * @return
     */
    @Override
    public void convertMediaId2Url(IMMessageContent msg) throws Exception{
        if(StringUtils.isEmpty(msg.getSecret()) || StringUtils.isEmpty(msg.getSource())){
            queryAppidAndSecret(msg);
        }
        String originCmd = msg.getCmd();
        String originVersion = msg.getVersion();
        String originTicket = msg.getTicket();
        String originSubBizType = msg.getBody().getSubBizType();
        msg.setCmd("im.get.media.url");
        msg.setVersion("3");
        msg.setTicket(UUID.randomUUID().toString().toUpperCase());
        msg.getBody().setSubBizType("GET_MEDIA_URL");
        IMMessagePayload payload = msg.getBody().getPayload();
        String content = payload.getContent();
        if(StringUtils.isBlank(content)){
            return;
        }
        JSONObject contentJson = JSONObject.parseObject(content);
        String mediaId = contentJson.getString("mediaId");
        String coverMediaId = contentJson.getString("coverMediaId");
        String mediaUrl = null;
        String coverUrl = null;
        if(StringUtils.isBlank(mediaId) && StringUtils.isBlank(coverMediaId)){
            return ;
        }
        JSONObject bodyJson = new JSONObject();
        bodyJson.put("platformShopId",msg.getBody().getPlatformShopId());
        bodyJson.put("bizType",msg.getBody().getBizType());
        bodyJson.put("subBizType",msg.getBody().getSubBizType());
        bodyJson.put("payload",new JSONObject());
        bodyJson.getJSONObject("payload").put("urlExpireTime",extendTime);
        if(StringUtils.isNotBlank(mediaId) ){
            bodyJson.getJSONObject("payload").put("mediaId",mediaId);
            mediaUrl = getMediaUrlByMediaId(msg,bodyJson);
        }
        if(StringUtils.isNotBlank(coverMediaId)){
            bodyJson.getJSONObject("payload").put("mediaId",coverMediaId);
            coverUrl = getMediaUrlByMediaId(msg,bodyJson);
        }
        if(StringUtils.isNotBlank(mediaUrl)){
            contentJson.put("mediaUrl",mediaUrl);
        }
        if(StringUtils.isNotBlank(coverUrl)){
            contentJson.put("coverUrl",coverUrl);
        }
        payload.setContent(contentJson.toJSONString());
        msg.setCmd(originCmd);
        msg.setVersion(originVersion);
        msg.setTicket(originTicket);
        msg.getBody().setSubBizType(originSubBizType);
    }

    /**
     * 将饿百的mediaID转换为mediaURL
     * @param msg
     * @param bodyJson
     * @return
     * @throws UnsupportedEncodingException
     */
    private String getMediaUrlByMediaId(IMMessageContent msg,JSONObject bodyJson) throws UnsupportedEncodingException {
        String bodyString = bodyJson.toJSONString();
        JSONObject resultObject = post2Elm(msg, bodyString);
        String mediaUrl = resultObject.getJSONObject("body").getJSONObject("data").getString("url");
        return mediaUrl;

    }


    private SendMessageRespond send2Elm(IMMessageContent message){

        JSONObject jsonObject = post2Elm(message);
        SendMessageRespond respond  = new SendMessageRespond();
        respond.setCmd(jsonObject.getString("cmd"));
        respond.setSign(jsonObject.getString("sign"));
        respond.setSource(jsonObject.getString("source"));
        respond.setTimestamp(jsonObject.getLong("timestamp"));
        respond.setVersion(jsonObject.getInteger("version"));
        respond.setTicket(jsonObject.getString("ticket"));
        respond.setErrCode(jsonObject.getJSONObject("body").getInteger("errno"));
        respond.setErrMsg(jsonObject.getJSONObject("body").getString("error"));
        respond.setTraceId(jsonObject.getString("traceid"));
        log.info("[饿百消息发送]发送消息内容："+JSONObject.toJSONString(message)+"\n发送消息结果:" +JSONObject.toJSONString(respond));
        return respond;
    }

    private JSONObject post2Elm(IMMessageContent message) {
        String postStr = verifySignEbls(message, null);
        JSONObject resultObject = sendEblsPost(postStr, baseUrl);
        return resultObject;
    }
    private JSONObject post2Elm(IMMessageContent message, String bodyStr) {
        String postStr = verifySignEbls(message, bodyStr);
        JSONObject resultObject = sendEblsPost(postStr, baseUrl);
        return resultObject;
    }

    /**
     * 计算签名
     * @param message
     * @param bodyJson
     * @param isUpload
     * @return
     */
    private String computeSign(IMMessageContent message, String bodyJson,Boolean isUpload){
        Map<String, String> signParamMap = new TreeMap<>(Comparator.naturalOrder());
        if (StringUtils.isNotBlank(message.getAccess_token())) {
            signParamMap.put("access_token", message.getAccess_token());
        }
        signParamMap.put("body", bodyJson);
        signParamMap.put("cmd", message.getCmd());
        signParamMap.put("encrypt", message.getEncrypt());
        if(isUpload){
            signParamMap.put("file_type", String.valueOf(2));
//            文件上传方式取固定值2
        }
        signParamMap.put("secret", message.getSecret());
        signParamMap.put("source", message.getSource());
        signParamMap.put("ticket", message.getTicket());
        signParamMap.put("timestamp", String.valueOf(message.getTimestamp()));
        signParamMap.put("version", message.getVersion());
        String toSign = signParamMap.entrySet().stream()
                .map(entry -> Strings.of("{}={}", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining("&"));
        return StrUtils.getMD5(toSign);
    }



    /**
     * 计算签名，并构建参数
     * @param message
     * @return
     */
    private String verifySignEbls(IMMessageContent message, String body) {
        StringBuffer requestParam = new StringBuffer();
        try {
            String bodyJson = body;
            if (StringUtils.isEmpty(body)) {
                bodyJson = JSONObject.toJSONString(message.getBody());
            }
            bodyJson = bodyJson.replace("/", "\\/");
            message.setEncrypt("");
            String sign = computeSign(message,bodyJson,false);
            message.setSign(sign);
            bodyJson = urldataEncode(bodyJson);
            if (StringUtils.isNotBlank(message.getAccess_token())) {
                requestParam.append("access_token=").append(message.getAccess_token()).append("&");
            }
            requestParam.append("cmd=").append(message.getCmd()).append("&");
            requestParam.append("version=").append(message.getVersion()).append("&");
            requestParam.append("timestamp=").append(message.getTimestamp()).append("&");
            requestParam.append("ticket=").append(message.getTicket()).append("&");
            requestParam.append("source=").append(message.getSource()).append("&");
            requestParam.append("sign=").append(message.getSign()).append("&");
            requestParam.append("body=").append(bodyJson).append("&");
            requestParam.append("encrypt=").append(message.getEncrypt());

        } catch (Exception e) {
            e.printStackTrace();
        }
        return requestParam.toString();
    }

    private String urldataEncode(String bodyJson) throws UnsupportedEncodingException {
        bodyJson = URLEncoder.encode(bodyJson, "utf-8");
        return bodyJson;

    }

    public JSONObject sendEblsPost(String params,String url) {
        if(!SecSdk.checkSSRFWithoutRedirect(url)){
            throw new RuntimeException("url不合法");
        }
        StringEntity reqEntity = new StringEntity(params, "UTF8");
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader(HTTP.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
        httpPost.setEntity(reqEntity);

        RequestConfig globalConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.IGNORE_COOKIES).build();
        httpPost.setConfig(globalConfig);
        String result = HttpClientUtil.post(httpPost,false);

        return JSONObject.parseObject(result);
    }


    public JSONObject uploadFile2EblsIM(String params){
        return sendEblsPost(params,mediaUploadUrl);
    }
}
