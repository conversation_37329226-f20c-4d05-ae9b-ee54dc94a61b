package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostYZEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpecTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.CustomSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuUpdateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelMerchantSpuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.YzConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductChannelErrorMappingUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemAdd;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemIncrementalUpdate;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemAddResult;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemDetailGetResult;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemIncrementalUpdateParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemIncrementalUpdateResult;
import com.youzan.cloud.open.sdk.gen.v3_0_1.api.YouzanItemDelete;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanItemDeleteResult;
import com.youzan.cloud.open.sdk.gen.v4_0_0.api.YouzanItemUpdate;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanItemUpdateResult;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: wangyihao04
 * @Date: 2022/11/28 5:16 PM
 * @Mail: <EMAIL>
 * 有赞总部商品对接实现类
 */
@Service("yzToolChannelMerchantSpuService")
@Slf4j
public class YzToolChannelMerchantSpuServiceImpl extends YouZanToolBaseService implements ChannelMerchantSpuService {

    @Resource
    private YzToolSpuCommonService yzSpuCommonService;

    @Resource(name = "yzProductThreadPool")
    private ExecutorService yzProductThreadPool;

    @Override
    public MerchantSpuCreateResponse createSpu(MerchantSpuCreateRequest request) {
        List<MerchantSpuDTO> paramList = request.getParamList();
        //校验有赞特殊逻辑
        valid(paramList);

        long tenantId = request.getBaseInfo().getTenantId();
        AppMessage appMessage = getMainAppMessage(tenantId);

        List<Future<MerchantSpuResult>> futureList = paramList.stream()
                .map(spuDTO -> yzProductThreadPool.submit(() -> createSingleSpu(tenantId, spuDTO, appMessage)))
                .collect(Collectors.toList());

        List<MerchantSpuResult> resultList = new ArrayList<>();
        futureList.forEach(future -> {
            try {
                MerchantSpuResult result = future.get(10L, TimeUnit.SECONDS);
                resultList.add(result);
            } catch (Exception e) {
                log.warn("创建总部商品未知异常", e);
            }
        });

        MerchantSpuCreateResponse response = new MerchantSpuCreateResponse();
        response.setStatus(ChannelStatus.buildSuccess());
        response.setSpuResultList(resultList);
        return response;
    }

    private MerchantSpuResult createSingleSpu(long tenantId, MerchantSpuDTO spuDTO, AppMessage appMessage) {
        String errorMsg;
        Integer errorCode = ResultCode.FAIL.getCode();
        try {
            clearStoreCategoryIfNotSync(spuDTO, tenantId);

            YouzanItemAdd youzanItemAdd = null;
            if (yzSpuCommonService.needYzSingeSpec(tenantId)) {
                youzanItemAdd = YzConverterUtil.createRequestToYzAPISingleSpec(spuDTO);
            } else {
                youzanItemAdd = YzConverterUtil.createRequestToYzAPI(spuDTO);
            }
            YouzanItemAddResult result = getResult4YouZanByRhino(ChannelPostYZEnum.CREATE_MERCHANT_SPU, appMessage,
                    youzanItemAdd, YouzanItemAddResult.class);
            if (result != null && result.getSuccess()) {
                Long itemId = result.getData().getItemId();
                YouzanItemDetailGetResult.YouzanItemDetailGetResultData spuDetail = yzSpuCommonService.getSpuDetail(tenantId, itemId);
                return YzConverterUtil.buildMerchantSpuResult(spuDTO, itemId, spuDetail);
            }

            log.warn("调用有赞创建总部商品接口失败, spuDTO {}，result:{}", spuDTO, JacksonUtils.toJson(result));
            errorMsg = result == null ? "渠道无返回值" : result.getMessage();
        }
        catch (BizException e) {
            log.warn("调用有赞创建总部商品接口异常, spuDTO {}.", spuDTO, e);
            errorCode = e.getErrorCode();
            errorMsg = e.getMessage();
        }
        catch (Exception e) {
            log.warn("调用有赞创建总部商品接口异常, spuDTO {}.", spuDTO, e);
            errorMsg = e.getMessage();
        }

        MerchantSpuResult failSpuResult = new MerchantSpuResult();
        failSpuResult.setCustomSpuId(spuDTO.getCustomSpuId());
        failSpuResult.setResultCode(errorCode);
        failSpuResult.setResultMsg(errorMsg);
        //填充统一渠道错误码
        Integer unifyError = ProductChannelErrorMappingUtils
                .getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN.getCode(), String.valueOf(errorCode), errorMsg);
        failSpuResult.setChannelUnifyError(unifyError);
        return failSpuResult;
    }

    @Override
    public MerchantSpuUpdateResponse updateSpu(MerchantSpuUpdateRequest request) {
        List<MerchantSpuDTO> paramList = request.getParamList();
        //校验有赞特殊逻辑
        valid(paramList);

        long tenantId = request.getBaseInfo().getTenantId();
        AppMessage appMessage = getMainAppMessage(tenantId);

        // 更新区分单规格与多规格
        List<Future<MerchantSpuResult>> futureList = paramList.stream()
                .map(spuDTO -> yzProductThreadPool.submit(() -> {

                    clearStoreCategoryIfNotSync(spuDTO, tenantId);

                    if (yzSpuCommonService.needYzSingeSpec(tenantId)) {
                        return updateSingleSpuNoSku(tenantId, spuDTO, appMessage);
                    }
                    else {
                        return updateSingleSpu(tenantId, spuDTO, appMessage);
                    }
                }))
                .collect(Collectors.toList());

        List<MerchantSpuResult> resultList = new ArrayList<>();
        futureList.forEach(future -> {
            try {
                MerchantSpuResult result = future.get(10L, TimeUnit.SECONDS);
                resultList.add(result);
            } catch (Exception e) {
                log.warn("更新总部商品未知异常", e);
            }
        });

        MerchantSpuUpdateResponse response = new MerchantSpuUpdateResponse();
        response.setStatus(ChannelStatus.buildSuccess());
        response.setSpuResultList(resultList);
        return response;
    }

    /**
     * 如果上架关闭了店内分类同步，则清空店内分类(此处兜底)
     *
     * @param spuDTO
     * @param tenantId
     */
    private static void clearStoreCategoryIfNotSync(MerchantSpuDTO spuDTO, long tenantId) {
        if (MccConfigUtil.stopSyncStoreCategoryToChannel(EnhanceChannelType.YZ.getChannelId(), tenantId)) {
            if (CollectionUtils.isNotEmpty(spuDTO.getChannelStoreCategoryCode())) {
                spuDTO.setChannelStoreCategoryCode(new ArrayList<>());
                log.warn("商品店内分类同步已关闭，但商品店内分类不为空, tenantId:{}", tenantId);
            }
        }
    }

    private MerchantSpuResult updateSingleSpu(long tenantId, MerchantSpuDTO spuDTO, AppMessage appMessage) {
        String errorMsg;
        Integer errorCode = ResultCode.FAIL.getCode();
        try {
            YouzanItemUpdate youzanItemUpdate = YzConverterUtil.updateRequestToYzAPI(spuDTO);
            YouzanItemUpdateResult result = getResult4YouZanByRhino(ChannelPostYZEnum.UPDATE_MERCHANT_SPU, appMessage,
                    youzanItemUpdate, YouzanItemUpdateResult.class);
            if (result != null && result.getSuccess()) {
                Long itemId = result.getData().getItemId();
                YouzanItemDetailGetResult.YouzanItemDetailGetResultData spuDetail = yzSpuCommonService.getSpuDetail(tenantId, itemId);
                return YzConverterUtil.buildMerchantSpuResult(spuDTO, itemId, spuDetail);
            }
            log.warn("调用有赞更新总部商品接口失败, spuDTO {}，result:{}", spuDTO, JacksonUtils.toJson(result));
            errorMsg = result == null ? "渠道无返回值" : result.getMessage();
        }
        catch (BizException e) {
            log.warn("调用有赞创建总部商品接口异常, spuDTO {}.", spuDTO, e);
            errorCode = e.getErrorCode();
            errorMsg = e.getMessage();
        }
        catch (Exception e) {
            log.warn("调用有赞更新总部商品接口异常, spuDTO:{}", spuDTO, e);
            errorMsg = e.getMessage();
        }
        MerchantSpuResult failSpuResult = new MerchantSpuResult();
        failSpuResult.setCustomSpuId(spuDTO.getCustomSpuId());
        failSpuResult.setResultCode(errorCode);
        failSpuResult.setResultMsg(errorMsg);

        //填充统一渠道错误码
        Integer unifyError = ProductChannelErrorMappingUtils
                .getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN.getCode(), String.valueOf(errorCode), errorMsg);
        failSpuResult.setChannelUnifyError(unifyError);

        return failSpuResult;
    }

    private MerchantSpuResult updateSingleSpuNoSku(long tenantId, MerchantSpuDTO spuDTO, AppMessage appMessage) {
        String errorMsg;
        Integer errorCode = ResultCode.FAIL.getCode();
        try {
            YouzanItemIncrementalUpdateParams
                    .YouzanItemIncrementalUpdateParamsParam youzanItemIncrementalUpdateParamsParam = YzConverterUtil.updateRequestToYzAPINoSku(spuDTO);

            YouzanItemIncrementalUpdateParams itemIncrementalUpdateParams = new YouzanItemIncrementalUpdateParams();
            itemIncrementalUpdateParams.setParam(youzanItemIncrementalUpdateParamsParam);

            YouzanItemIncrementalUpdate incrementalUpdate = new YouzanItemIncrementalUpdate();
            incrementalUpdate.setAPIParams(itemIncrementalUpdateParams);

            YouzanItemIncrementalUpdateResult result = getResult4YouZanByRhino(ChannelPostYZEnum.UPDATE_MERCHANT_SPU_NO_SKU, appMessage,
                    incrementalUpdate, YouzanItemIncrementalUpdateResult.class);
            if (result != null && result.getSuccess()) {
                Long itemId = result.getData().getItemId();
                YouzanItemDetailGetResult.YouzanItemDetailGetResultData spuDetail = yzSpuCommonService.getSpuDetail(tenantId, itemId);
                return YzConverterUtil.buildMerchantSpuResult(spuDTO, itemId, spuDetail);
            }
            log.warn("调用有赞更新单规格总部商品接口失败, spuDTO {}，result:{}", spuDTO, JacksonUtils.toJson(result));
            errorMsg = result == null ? "渠道无返回值" : result.getMessage();
        }
        catch (BizException e) {
            log.warn("调用有赞创建单规格总部商品接口异常, spuDTO {}.", spuDTO, e);
            errorCode = e.getErrorCode();
            errorMsg = e.getMessage();
        }
        catch (Exception e) {
            log.warn("调用有赞更新单规格总部商品接口异常, spuDTO:{}", spuDTO, e);
            errorMsg = e.getMessage();
        }
        MerchantSpuResult failSpuResult = new MerchantSpuResult();
        failSpuResult.setCustomSpuId(spuDTO.getCustomSpuId());
        failSpuResult.setResultCode(errorCode);
        failSpuResult.setResultMsg(errorMsg);

        //填充统一渠道错误码
        Integer unifyError = ProductChannelErrorMappingUtils
                .getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN.getCode(), String.valueOf(errorCode), errorMsg);
        failSpuResult.setChannelUnifyError(unifyError);

        return failSpuResult;
    }

    @Override
    public MerchantSpuDeleteResponse deleteSpu(MerchantSpuDeleteRequest request) {

        long tenantId = request.getBaseInfo().getTenantId();
        AppMessage appMessage = getMainAppMessage(tenantId);

        List<Future<MerchantSpuResult>> futureList = request.getParamList().stream()
                .map(spuDTO -> yzProductThreadPool.submit(() -> deleteSingleSpu(spuDTO, appMessage, request.isOptByChannelSpuId())))
                .collect(Collectors.toList());

        List<MerchantSpuResult> resultList = new ArrayList<>();
        futureList.forEach(future -> {
            try {
                MerchantSpuResult result = future.get(10L, TimeUnit.SECONDS);
                resultList.add(result);
            } catch (Exception e) {
                log.warn("删除总部商品未知异常", e);
            }
        });

        MerchantSpuDeleteResponse response = new MerchantSpuDeleteResponse();
        response.setStatus(ChannelStatus.buildSuccess());
        response.setSpuResultList(resultList);
        return response;
    }

    private MerchantSpuResult deleteSingleSpu( CustomSpuDeleteDTO spuDTO, AppMessage appMessage, boolean optByChannelSpuId) {
        YouzanItemDelete youzanItemDelete = YzConverterUtil.deleteRequestToYzAPI(spuDTO);

        MerchantSpuResult spuResult = new MerchantSpuResult();
        // 通过渠道spuId删除商品
        if (optByChannelSpuId) {
            spuResult.setChannelSpuId(spuDTO.getChannelSpuId());
        } else {
            spuResult.setCustomSpuId(spuDTO.getCustomSpuId());
        }
        try {
            YouzanItemDeleteResult result = getResult4YouZanByRhino(ChannelPostYZEnum.DELETE_MERCHANT_SPU, appMessage,
                    youzanItemDelete, YouzanItemDeleteResult.class);
            log.info("删除总部商品：spuDTO {}, result {}.", spuDTO, JSON.toJSONString(result));
            if (MccConfigUtil.getYzNotExistSpuCodes().contains(result.getCode())) {
                log.info("删除的商品在有赞平台不存在");
                spuResult.setResultCode(ResultCode.SUCCESS.getCode());
                return spuResult;
            }

            if (!result.getSuccess()) {
                log.warn("删除总部商品失败 spuDTO:{},result:{}", spuDTO, JacksonUtils.toJson(result));
                spuResult.setResultCode(ResultCode.FAIL.getCode());
                spuResult.setResultMsg(result.getMessage());
                return spuResult;
            }

            if (!result.getData().getIsSuccess()) {
                spuResult.setResultCode(ResultCode.FAIL.getCode());
                spuResult.setResultMsg(ResultCode.YZ_DELETE_SPU_ERROR.getMsg());
            } else {
                spuResult.setResultCode(ResultCode.SUCCESS.getCode());
            }
        }
        catch (BizException e) {
            log.warn("调用有赞创建总部商品接口异常, spuDTO {}.", spuDTO, e);
            spuResult.setResultCode(e.getErrorCode());
            spuResult.setResultMsg(e.getMessage());
        }
        catch (Exception e) {
            log.warn("调用有赞删除总部商品接口异常, spuDTO {}.", spuDTO, e);
            spuResult.setResultCode(ResultCode.FAIL.getCode());
            spuResult.setResultMsg(e.getMessage());
        }
        return spuResult;
    }

    /**
     * 获取总部渠道商品详情
     *
     * @param request
     * @return
     */
    @Override
    public MerchantSpuDetailResponse getSpuDetail(MerchantSpuDetailRequest request){
        MerchantSpuDetailResponse response = new MerchantSpuDetailResponse();
        ChannelStatus channelStatus = ChannelStatus.build(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(),
                ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg());
        response.setStatus(channelStatus);

        return response;
    }
    @Override
    public MerchantSpuDetailResponse getSpuDetailSingle(MerchantSpuDetailRequest request) {
        MerchantSpuDetailResponse result = new MerchantSpuDetailResponse();
        try {
            Long tenantId = request.getBaseInfo().getTenantId();
            Long itemId = Long.valueOf(request.getChannelSpuId());
            YouzanItemDetailGetResult.YouzanItemDetailGetResultData spuDetail = yzSpuCommonService.getSpuDetail(tenantId, itemId);
            MerchantSpuDTO merchantSpuDTO = YzConverterUtil.detailResultToMerchantSpuDTO(spuDetail);
            result.setStatus(ChannelStatus.buildSuccess());
            result.setMerchantSpuDTO(merchantSpuDTO);
        } catch (BizException e) {
            //未查询到，返回成功，空数据
            String message = String.format("YzToolChannelMerchantSpuService.getSpuDetail 查询商品不存在, request:%s,message:%s",request,e.getMessage());
            log.warn(message, e);
            result.setStatus(ChannelStatus.build(e.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            String message = String.format("YzToolChannelMerchantSpuService.getSpuDetail 服务异常, request:%s,message:%s",request,e.getMessage());
            log.error(message, e);
            result.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), e.getMessage()));
        }
        return result;
    }


    private void valid(List<MerchantSpuDTO> paramList) {
        paramList.forEach(spuDTO -> {
            spuDTO.getImages().forEach(imageId -> {
                Preconditions.checkArgument(StringUtils.isNumeric(imageId), "有赞图片必须为Long类型");
            });
            spuDTO.getChannelStoreCategoryCode().forEach(storeCategoryCode -> {
                Preconditions.checkArgument(StringUtils.isNumeric(storeCategoryCode), "有赞店内分类必须为Long类型");
            });
        });
    }

    @Override
    public MerchantSpuCreateResponse getSpuCreateStatus(MerchantSpuDetailRequest request) {
        MerchantSpuCreateResponse response = new MerchantSpuCreateResponse();
        ChannelStatus channelStatus = ChannelStatus.build(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(),
                ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg());
        response.setStatus(channelStatus);
        return response;
    }
}
