package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.DrunkHorseCopAccessAndStore;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/***
 * author : <EMAIL>
 * date : 2024/6/13
 * time : 16:49
 * 描述 :
 **/
@Service
@Slf4j
public class DrunkHorseOrderService {

    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    /***
     * 闪购渠道出现微信门店，将微信门店转成闪购门店
     * **/
    public long wxMallPoi2MtPoi(Long tenantId, String appPoiCode) {
        Long wxMallStoreId =  copChannelStoreService.selectChannelStoreId(tenantId, DynamicChannelType.MT_DRUNK_HORSE.getChannelId(), appPoiCode);
        long mtStoreId = ProjectConstant.UNKNOW_STORE_ID;
        if (wxMallStoreId != null && wxMallStoreId > 0){
            ChannelStoreDO mtChannelStoreDO =  copChannelStoreService.getChannelStore(tenantId, DynamicChannelType.MEITUAN.getChannelId(), wxMallStoreId);
            if (mtChannelStoreDO != null){
                mtStoreId = mtChannelStoreDO.getStoreId();
            }
        }
        log.info("[歪马闪购渠道出现微商城门店]门店转化，appPoiCode:{}, 美团渠道门店:{}", appPoiCode, mtStoreId);
        return mtStoreId;
    }

    public DrunkHorseCopAccessAndStore queryCrossPoiAccessAndStoreConfig(String tenantAppId, String appPoiCode, Integer sourceChannelId, Integer targetChannelId){
        CopAccessConfigDO copAccessConfig = copAccessConfigService.selectByTenantAppIdAndChannelId(tenantAppId, targetChannelId);
        if (copAccessConfig != null && StringUtils.isNotBlank(appPoiCode)){
            //门店原渠道对应的配置
            CopAccessConfigDO targetAccessConfig =  copAccessConfigService.selectByTenantIdAndChannelId(copAccessConfig.getTenantId(), sourceChannelId);
            Long wxMallStoreId =  copChannelStoreService.selectChannelStoreId(copAccessConfig.getTenantId(), targetChannelId, appPoiCode);
            if (targetAccessConfig != null && wxMallStoreId != null && wxMallStoreId > 0){
                ChannelStoreDO mtChannelStoreDO =  copChannelStoreService.getChannelStore(copAccessConfig.getTenantId(), sourceChannelId, wxMallStoreId);
                if (mtChannelStoreDO != null){
                    DrunkHorseCopAccessAndStore drunkHorseCopAccessAndStore = new DrunkHorseCopAccessAndStore();
                    // 转换了门店的 access
                    drunkHorseCopAccessAndStore.setCopAccessConfigDO(targetAccessConfig);
                    drunkHorseCopAccessAndStore.setChannelStoreDO(mtChannelStoreDO);
                    // 错误渠道的 access
                    drunkHorseCopAccessAndStore.setCrossPoiAccessConfigDO(copAccessConfig);
                    return drunkHorseCopAccessAndStore;
                }
            }
        }
        return null;
    }
}
