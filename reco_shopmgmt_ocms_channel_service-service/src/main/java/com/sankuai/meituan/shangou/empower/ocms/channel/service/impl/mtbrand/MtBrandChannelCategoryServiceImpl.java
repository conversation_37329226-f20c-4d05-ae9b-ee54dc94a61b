package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ProductUnstructRuleInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.MtChannelResultEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.CommonThreadPool;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductChannelErrorMappingUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultCodeUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 美团渠道商品分类内部服务接口
 *
 * <AUTHOR>
 * @create 2019-01-09 下午4:39
 **/
@Service("mtBrandChannelCategoryService")
public class MtBrandChannelCategoryServiceImpl implements ChannelCategoryService {
    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private MtConverterService mtConverterService;

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Value("${mt.url.base}" + "${mt.url.getSpTagIds}")
    private String getSpTagIds;

    @Value("${mt.url.base}" + "${mt.url.categoryAttrList}")
    private String getCategoryAttrList;

    @Value("${mt.url.base}" + "${mt.url.categoryRequired}")
    private String getCategoryRequired;

    @Value("${mt.url.base}" + "${mt.url.categoryAttrValueList}")
    private String getCategoryAttrValueList;

    @Value("${mt.url.base}" + "${mt.url.productRules}")
    private String getCategoryProductRules;

    @Resource
    private CommonThreadPool commonThreadPool;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private CommonLogger log;

    @Autowired
    @Qualifier("mtBrandChannelSkuService")
    private MtBrandChannelSkuServiceImpl mtBrandChannelSkuService;

    @Autowired
    @Qualifier("mtBrandChannelSkuService")
    private ChannelSkuService mtChannelSkuService;



    /**
     * 美团失败关键字
     * 用于判断返回的消息题中的msg字段的匹配，如果匹配到则认为是部分成功；中台认为失败。
     */
    public static final String MT_FAIL_KEYWORD = "失败";

    public static final int QUERY_CHANNEL_DYNAMIC_INFO = 2;

    /**
     * @param request 支持同租户多门店，内部逐门店串行
     * @return
     */
    @Override
    public CreateCategoryResponse createCategory(CategoryRequest request) {
        log.info("开始创建美团前台分类 CategoryRequest:{}", request);
        CreateCategoryResponse resp = new CreateCategoryResponse();
        List<CategoryPoiResult> categoryPoiResults = Lists.newArrayList();
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo(), request.getParamList());
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setData(Collections.emptyList());
        }

        //先创建一级分类 逐门店串行
        for (CategoryInfoDTO categoryInfoDTO : request.getParamList()) {
            long storeId = categoryInfoDTO.getStoreId();

            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setStoreId(storeId)
                    .setCode(categoryInfoDTO.getCode());
            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }
            if (StringUtils.isNotEmpty(categoryInfoDTO.getParentName())) {
                continue;
            }
            Map createParam = Maps.newHashMap();
            createParam.put(ProjectConstant.APP_POI_CODE, channelStoreDOMap.get(KeyUtils.genChannelStoreKey(tenantId,
                    channelId, storeId)).getChannelOnlinePoiCode());
            createParam.put(ProjectConstant.CATEGORY_CODE, categoryInfoDTO.getCode());
            createParam.put(ProjectConstant.CATEGORY_NAME, categoryInfoDTO.getName());
            createParam.put(ProjectConstant.SEQUENCE, categoryInfoDTO.getSort());

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            // 渠道接口
            Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_UPDATE, baseRequest, createParam);

            // 提前装入结果集
            categoryPoiResults.add(categoryPoiResult);

            if (MapUtils.isEmpty(postResult)) {
                log.error("创建美团前台分类失败, 渠道返回空, CategoryRequest:{}", request);
                categoryPoiResult.setResultCode(1).setMsg("创建美团前台分类失败, 渠道返回空");
                continue;
            }

            ChannelResponseDTO<String> channelResponseDTO = postResult.get(storeId);
            if (Objects.isNull(channelResponseDTO)) {
                log.error("创建美团前台分类失败, 渠道返回空, CategoryRequest:{}", request);
                categoryPoiResult.setResultCode(1).setMsg("创建美团前台分类失败, 渠道返回空");
                continue;
            }

            if ("ok".equals(channelResponseDTO.getData())) {
                log.info("创建美团前台分类成功, channelResponseDTO: {}", channelResponseDTO);
                categoryPoiResult.setResultCode(0).setChannelCode(categoryInfoDTO.getCode());
            } else {
                categoryPoiResult.setResultCode(ResultCodeUtils.parseErrorCode(channelResponseDTO.getErrorCode())).setMsg(channelResponseDTO.getErrorMsg());
                //填充统一渠道错误码
                ProductChannelUnifyErrorEnum resErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.MEITUAN, channelResponseDTO.getErrno(), channelResponseDTO.getErrorMsg());
                categoryPoiResult.setChannelUnifyError(resErrorEnum);
            }
        }
        //再创建二级分类，避免创建二级分类时一级分类不存在 逐门店串行
        for (CategoryInfoDTO categoryInfoDTO : request.getParamList()) {
            long storeId = categoryInfoDTO.getStoreId();

            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setStoreId(storeId)
                    .setCode(categoryInfoDTO.getCode());
            if (Strings.isNullOrEmpty(categoryInfoDTO.getParentName())) {
                continue;
            }
            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }

            Map createParam = Maps.newHashMap();
            createParam.put(ProjectConstant.APP_POI_CODE, channelStoreDOMap.get(KeyUtils.genChannelStoreKey(tenantId,
                    channelId, storeId)).getChannelOnlinePoiCode());
            createParam.put(ProjectConstant.CATEGORY_CODE_ORIGIN, categoryInfoDTO.getChannelParentCode());
            createParam.put(ProjectConstant.CATEGORY_CODE, categoryInfoDTO.getChannelParentCode());
            createParam.put(ProjectConstant.CATEGORY_NAME, categoryInfoDTO.getParentName());
            createParam.put(ProjectConstant.SECONDARY_CATEGORY_CODE, categoryInfoDTO.getCode());
            createParam.put(ProjectConstant.SECONDARY_CATEGORY_NAME, categoryInfoDTO.getName());
            createParam.put(ProjectConstant.SEQUENCE, categoryInfoDTO.getSort());

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            // 渠道接口
            Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_UPDATE, baseRequest, createParam);

            // 提前装入结果集
            categoryPoiResults.add(categoryPoiResult);

            if (MapUtils.isEmpty(postResult)) {
                log.error("创建美团前台二级分类失败, 渠道返回空, CategoryRequest:{}", request);
                categoryPoiResult.setResultCode(1).setMsg("创建美团前台二级分类失败, 渠道返回空");
                continue;
            }

            ChannelResponseDTO<String> channelResponseDTO = postResult.get(storeId);
            if (Objects.isNull(channelResponseDTO)) {
                log.error("创建美团前台二级分类失败, 渠道返回空, CategoryRequest:{}", request);
                categoryPoiResult.setResultCode(1).setMsg("创建美团前台二级分类失败, 渠道返回空");
                continue;
            }

            if ("ok".equals(channelResponseDTO.getData())) {
                log.info("创建美团前台分二级类成功, channelResponseDTO: {}", channelResponseDTO);
                categoryPoiResult.setResultCode(0).setChannelCode(categoryInfoDTO.getCode());
            } else {
                categoryPoiResult.setResultCode(ResultCodeUtils.parseErrorCode(channelResponseDTO.getErrorCode())).setMsg(channelResponseDTO.getErrorMsg());
                //填充统一渠道错误码
                ProductChannelUnifyErrorEnum resErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.MEITUAN, channelResponseDTO.getErrno(), channelResponseDTO.getErrorMsg());
                categoryPoiResult.setChannelUnifyError(resErrorEnum);
            }
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    /**
     * @param request 支持同租户多门店，内部逐门店串行
     * @return
     */
    @Override
    public UpdateCategoryResponse updateCategory(CategoryUpdateRequest request) {
        log.info("开始更新美团前台分类 CategoryRequest:{}", request);
        UpdateCategoryResponse resp = new UpdateCategoryResponse();
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo(), request.getParamList());
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setData(Collections.emptyList());
        }

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        //逐门店串行
        List<CategoryPoiResult> categoryPoiResults = Lists.newArrayList();
        for (CategoryInfoUpdateDTO categoryInfoDTO : request.getParamList()) {
            long storeId = categoryInfoDTO.getStoreId();
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setStoreId(storeId)
                    .setCode(categoryInfoDTO.getCode())
                    .setChannelCode(categoryInfoDTO.getChannelCategoryCode());

            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }
            Map updateParam = Maps.newHashMap();
            updateParam.put(ProjectConstant.APP_POI_CODE, channelStoreDOMap.get(KeyUtils.genChannelStoreKey(tenantId,
                    channelId, storeId)).getChannelOnlinePoiCode());
            updateParam.put(ProjectConstant.CATEGORY_CODE_ORIGIN, categoryInfoDTO.getChannelCategoryCode());
            updateParam.put(ProjectConstant.CATEGORY_CODE, categoryInfoDTO.getChannelCategoryCode());
            updateParam.put(ProjectConstant.CATEGORY_NAME, categoryInfoDTO.getName());
            updateParam.put(ProjectConstant.SEQUENCE, categoryInfoDTO.getSort());

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            // 渠道接口
            Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_UPDATE, baseRequest, updateParam);

            // 提前装入结果集
            categoryPoiResults.add(categoryPoiResult);

            if (MapUtils.isEmpty(postResult)) {
                log.error("更新美团前台二级分类失败, 渠道返回空, CategoryRequest:{}", request);
                categoryPoiResult.setResultCode(1).setMsg("更新美团前台二级分类失败, 渠道返回空");
                continue;
            }

            ChannelResponseDTO<String> channelResponseDTO = postResult.get(storeId);
            if (Objects.isNull(channelResponseDTO)) {
                log.error("更新美团前台二级分类失败, 渠道返回空, CategoryRequest:{}", request);
                categoryPoiResult.setResultCode(1).setMsg("更新美团前台二级分类失败, 渠道返回空");
                continue;
            }

            if ("ok".equals(channelResponseDTO.getData())) {
                log.info("更新美团前台二级分类成功, channelResponseDTO: {}", channelResponseDTO);
                categoryPoiResult.setResultCode(0).setChannelCode(categoryInfoDTO.getCode());
            } else {
                log.error("更新美团前台二级分类失败, channelResponseDTO: {}", channelResponseDTO);
                categoryPoiResult.setResultCode(ResultCodeUtils.parseErrorCode(channelResponseDTO.getErrorCode())).setMsg(channelResponseDTO.getErrorMsg());
                //填充渠道统一错误
                ProductChannelUnifyErrorEnum resErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.MEITUAN, channelResponseDTO.getErrno(), channelResponseDTO.getErrorMsg());
                categoryPoiResult.setChannelUnifyError(resErrorEnum);
            }
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    /**
     * 检查结果是否成功
     *
     * @param updateCategoryMap
     * @return true 成功
     * false 失败
     */
    private boolean checkSuccess(Map updateCategoryMap) {
        if (updateCategoryMap.get(ProjectConstant.MSG) != null) {
            String msg = String.valueOf(updateCategoryMap.get(ProjectConstant.MSG));
            if (StringUtils.isNotBlank(msg) && msg.indexOf(MT_FAIL_KEYWORD) > -1) {
                return false;
            }
        }

        return ProjectConstant.OK.equals(updateCategoryMap.get(ProjectConstant.DATA));
    }

    /**
     * @param request 支持同租户多门店，内部逐门店串行
     * @return
     */
    @Override
    public UpdateCategoryResponse deleteCategory(CategoryDeleteRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();
        //逐门店串行
        request.getParamList().forEach(data -> {
            try {
                // 调用渠道接口
                long storeId = data.getStoreId();
                BaseRequest baseRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                Preconditions.checkArgument(StringUtils.isNotBlank(data.getChannelCategoryCode()) || StringUtils.isNotBlank(data.getChannelCategoryName()), "删除店内分类失败，未指定分类");
                // 渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_DELETE, baseRequest, mtConverterService.deleteCategory(data));

                // 结果组装
                ProductResultDataUtils.combineResultData(resultData, postResult, data.getCode(), ChannelTypeEnum.MEITUAN);

            } catch (IllegalArgumentException e) {
                log.error("MtChannelCategoryServiceImpl.deleteCategory 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getCode());

            } catch (InvokeChannelTooMuchException e) {
                log.warn("MtChannelCategoryServiceImpl.deleteCategory 触发限流, data:{}", data);
                ResultDataUtils.newResultData(ChannelResultStatusEnum.TRIGGER_LIMIT, "");
            } catch (Exception e) {
                log.error("MtChannelCategoryServiceImpl.deleteCategory 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getCode());
            }
        });

        return convertResultData(resultData);
    }

    /**
     * @param request 支持同租户多门店，内部逐门店串行
     * @return
     */
    @Override
    public UpdateCategoryResponse sortCategory(CategorySortRequest request) {
        ResultData resultData;
        int nThreads = MccConfigUtil.getCategorySortNThreads();
        if (nThreads > 0) {
            resultData = asynSortCategory(request);
        } else {
            resultData = syncSortCategory(request);
        }

        return convertResultData(resultData);
    }


    /**
     * 分类排序 - 异步并行
     *
     * @param request
     * @return
     */
    private ResultData asynSortCategory(CategorySortRequest request) {
        List<ChannelCategoryDTO> postList = Lists.newArrayList();
        request.getParamList().forEach(data -> {
            // 业务参数转换
            mtConverterService.sortCategory(data.getSortItemList()).forEach(item -> {
                item.setStoreId(data.getStoreId());
                postList.add(item);
            });
        });

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        ResultData resultData = ResultDataUtils.newSynchronizedResultData(ChannelResultStatusEnum.SUCCESS);
        CountDownLatch count = new CountDownLatch(postList.size());
        // 门店独立执行
        postList.forEach(data -> {
            commonThreadPool.submit(() -> {
                try {
                    long storeId = data.getStoreId();
                    BaseRequest baseRequest = new BaseRequest()
                            .setTenantId(tenantId)
                            .setChannelId(channelId)
                            .setStoreIdList(Lists.newArrayList(storeId));

                    // 调用渠道接口
                    Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_UPDATE, baseRequest, data);

                    // 结果组装
                    ProductResultDataUtils.combineResultData(resultData, postResult, data.getCategory_code(), ChannelTypeEnum.MEITUAN);

                } catch (IllegalArgumentException e) {
                    log.error("MtChannelCategoryServiceImpl.asynSortCategory 参数校验失败, data:{}", data, e);
                    ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getCategory_code());

                } catch (Exception e) {
                    log.error("MtChannelCategoryServiceImpl.asynSortCategory 服务异常, data:{}", data, e);
                    ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getCategory_code());
                } finally {
                    count.countDown();
                }
            });
        });

        try {
            count.await(120, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("MtChannelCategoryServiceImpl.asynSortCategory 服务InterruptedException异常, request:{}", request, e);
        }
        return resultData;
    }

    /**
     * 分类排序 - 同步串行
     *
     * @param request
     * @return
     */
    private ResultData syncSortCategory(CategorySortRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        // 逐门店执行
        request.getParamList().forEach(data -> {
            // 业务参数转换
            mtConverterService.sortCategory(data.getSortItemList()).forEach(sku -> {
                try {
                    long storeId = data.getStoreId();
                    BaseRequest baseRequest = new BaseRequest()
                            .setTenantId(tenantId)
                            .setChannelId(channelId)
                            .setStoreIdList(Lists.newArrayList(storeId));

                    // 调用渠道接口
                    Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_UPDATE, baseRequest, sku);

                    // 结果组装
                    ProductResultDataUtils.combineResultData(resultData, postResult, sku.getCategory_code(), ChannelTypeEnum.MEITUAN);

                } catch (IllegalArgumentException e) {
                    log.error("MtChannelCategoryServiceImpl.syncSortCategory 参数校验失败, data:{}", data, e);
                    ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getCode());

                } catch (Exception e) {
                    log.error("MtChannelCategoryServiceImpl.syncSortCategory 服务异常, data:{}", data, e);
                    ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, sku.getCategory_code());
                }
            });
        });

        return resultData;
    }


    /**
     * 获取美团后台商品类目（末级类目id); 不传门店id, 内部通过获取品牌id与渠道交互
     *
     * @param request
     * @return
     */
    @Override
    public GetCategoryResponse batchGetCategory(CatRequest request) {
        // 新供给支持一租户多品牌后，与渠道交互的secret存在品牌维度；通过storeId找到品牌appId，通过品牌appId找到secret
        // 此处使用租户下任意有效品牌id，去找secret，再与渠道交互
        GetCategoryResponse response = new GetCategoryResponse().setStatus(ResultGenerator.genSuccessResult());
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        List<CopAccessConfigDO> copAccessConfigDOS = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOS)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取租户品牌数据失败"));
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                //任意一个品牌appId（就以首个）
                .setAppId(copAccessConfigDOS.get(0).getAppId());

        Map<String, String> bizParam = Maps.newHashMap();

        if (request.isSetCategoryType()) {
            bizParam.put("category_type", String.valueOf(request.getCategoryType()));
        }

        Map spTagMap = mtBrandChannelGateService.sendGet(getSpTagIds, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(spTagMap) || Objects.isNull(spTagMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(spTagMap.get(ProjectConstant.DATA))) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团类目失败"));
        }
        List<CatInfo> catInfoList = Lists.newArrayList();
        JSONArray catInfoJSONArray = (JSONArray) spTagMap.get(ProjectConstant.DATA);
        List<ChannelCatInfo> catInfos = catInfoJSONArray.toJavaList(ChannelCatInfo.class);
        catInfoList.addAll(mtConverterService.channelCatInfosMapping(catInfos));
        return response.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(catInfoList);
    }

    @Override
    public GetCategoryResponse getSecondCategoryByParentId(CatRequest req) {
        return new GetCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE)).setCatInfoList(Collections.emptyList());
    }

    /**
     * 牵牛花业务，获取渠道类目商品发布规则
     *
     * @param request
     * @return
     */
    @Override
    public CategoryProductRulesResponse getCategoryProductRules(CategoryAttrRequest request) {
       CategoryProductRulesResponse response = new CategoryProductRulesResponse();
        Map<String, Long> bizParam = Maps.newHashMap();
        // 末级类目ID
        bizParam.put(ProjectConstant.TAG_ID, request.getCategoryId());
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        baseRequest.setStoreIdList(Lists.newArrayList(request.getStoreId()));
        // 查询商品发布规则
        Map categoryProductRulesInfo = mtBrandChannelGateService.sendGetApp(getCategoryProductRules, null, baseRequest,
                bizParam);

        if (MapUtils.isEmpty(categoryProductRulesInfo)) {
            return response.setStatus(ResultGenerator.genFailResult("获取美团商品发布规则失败"));
        }
        if (MtChannelResultEnum.ALL_SUCCESS.getValue() != (int)categoryProductRulesInfo .get(ProjectConstant.RESULT_CODE)) {
            JSONArray errorJSONArray = (JSONArray) categoryProductRulesInfo.get(ProjectConstant.ERROR_LIST);
            if (Objects.nonNull(errorJSONArray) && !errorJSONArray.isEmpty()) {
                String errMsg = errorJSONArray.stream().map(error -> ((JSONObject) error).getString(ProjectConstant.MSG)).collect(Collectors.joining(","));
                return response.setStatus(ResultGenerator.genFailResult(errMsg));
            }
        }

        JSONObject successMap = (JSONObject) categoryProductRulesInfo.get(ProjectConstant.MT_SUCCESS_MAP_KEY);
        JSONArray structRules = successMap.getJSONArray("struct_rules");
        ProductUnstructRuleInfo unstructRule = successMap.getObject("unstruct_rules", ProductUnstructRuleInfo.class);
        List<ProductRule> productRules = new ArrayList<>();
        if(structRules != null && !structRules.isEmpty()){
            List<ProductRuleInfo> ruleList = structRules.toJavaList(ProductRuleInfo.class);
            productRules = mtConverterService.productRuleMapping(ruleList);
        }
        ProductUnstructRule productUnstructRule = mtConverterService.productUnStructRuleMapping(unstructRule);
        response.setStatus(ResultGenerator.genSuccessResult())
               .setRuleList(productRules)
               .setUnstructRule(productUnstructRule);
        return response;
    }

    /**
     * 牵牛花业务，获取渠道类目属性
     *
     * @param request
     * @return
     */
    @Override
    public CategoryAttrResponse getCategoryAttr(CategoryAttrRequest request) {
        CategoryAttrResponse response = new CategoryAttrResponse();
        Map<String, Long> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.TAG_ID, request.getCategoryId());
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        baseRequest.setStoreIdList(Lists.newArrayList(request.getStoreId()));
        Map categoryAttrMap = mtBrandChannelGateService.sendGetApp(getCategoryAttrList, null, baseRequest, bizParam);
        if (!MapUtils.isEmpty(categoryAttrMap) && !Objects.isNull(categoryAttrMap.get(ProjectConstant.ERROR))) {
            JSONObject error = (JSONObject) categoryAttrMap.get(ProjectConstant.ERROR);
            return response.setStatus(ResultGenerator.genFailResult(error.getString(ProjectConstant.MSG)));
        }
        if (MapUtils.isEmpty(categoryAttrMap) || Objects.isNull(categoryAttrMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(categoryAttrMap.get(ProjectConstant.DATA))) {
            return response.setStatus(ResultGenerator.genFailResult("获取美团类目属性失败"));
        }

        //查询类目必填性
        Map categoryRequiredInfo = mtBrandChannelGateService.sendGetApp(getCategoryRequired, null, baseRequest,
                bizParam);

        if (MapUtils.isEmpty(categoryRequiredInfo)) {
            return response.setStatus(ResultGenerator.genFailResult("获取美团类目属性必填性失败"));
        }
        if (MtChannelResultEnum.ALL_SUCCESS.getValue() != (int)categoryRequiredInfo.get(ProjectConstant.RESULT_CODE)) {
            JSONArray errorJSONArray = (JSONArray) categoryRequiredInfo.get(ProjectConstant.ERROR_LIST);
            if (Objects.nonNull(errorJSONArray) && !errorJSONArray.isEmpty()) {
                String errMsg = errorJSONArray.stream().map(error -> {
                    return ((JSONObject) error).getString(ProjectConstant.MSG);
                }).collect(Collectors.joining(","));
                return response.setStatus(ResultGenerator.genFailResult(errMsg));
            }
        }

        JSONObject successMap = (JSONObject) categoryRequiredInfo.get(ProjectConstant.MT_SUCCESS_MAP_KEY);
        Integer upcRequired = successMap.getInteger("upc");
        Integer weightRequired = successMap.getInteger("weight");


        List<CategoryAttrInfo> generalAttrList = Lists.newArrayList();
        List<CategoryAttrInfo> saleAttrList = Lists.newArrayList();
        List<CategoryAttrInfo> skuAttrList = Lists.newArrayList();
        JSONObject attrInfoJSONObject = (JSONObject) categoryAttrMap.get(ProjectConstant.DATA);
        ChannelCategoryAttrResult result = attrInfoJSONObject.toJavaObject(ChannelCategoryAttrResult.class);
        if (CollectionUtils.isNotEmpty(result.getGeneral_attrs())) {
            generalAttrList.addAll(mtConverterService.channelCategoryAttrsMapping(result.getGeneral_attrs()));
        }
        if (CollectionUtils.isNotEmpty(result.getSale_attrs())) {
            saleAttrList.addAll(mtConverterService.channelCategoryAttrsMapping(result.getSale_attrs()));
        }
        if (CollectionUtils.isNotEmpty(result.getSku_attrs())) {
            skuAttrList.addAll(mtConverterService.channelCategoryAttrsMapping(result.getSku_attrs()));
        }
        response.setStatus(ResultGenerator.genSuccessResult())
                .setGeneralAttrList(generalAttrList)
                .setSaleAttrList(saleAttrList)
                .setSkuAttrList(skuAttrList)
                .setUpcRequired(upcRequired)
                .setWeightRequired(weightRequired);
        return response;
    }

    /**
     * 牵牛花业务，获取渠道类目特殊属性
     *
     * @param request
     * @return
     */
    @Override
    public CategoryAttrValueResponse getCategoryAttrValue(CategoryAttrValueRequest request) {
        CategoryAttrValueResponse response = new CategoryAttrValueResponse();
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.MT_ATTR_ID, request.getAttrId());
        bizParam.put(ProjectConstant.MT_KEYWORD, request.getKeyword());
        bizParam.put(ProjectConstant.MT_POI_PAGE_NUM, request.getPageNo());
        bizParam.put(ProjectConstant.MT_POI_PAGE_SIZE, request.getPageSize());
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        List<CopAccessConfigDO> copAccessConfigDOS = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOS)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取租户品牌数据失败"));
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setAppId(copAccessConfigDOS.get(0).getAppId());
        Map<String, Object> attrValueMap;
        if (MccConfigUtil.isSpuQueryUseUrlEncode(baseRequest.getTenantId())) {
            attrValueMap = mtBrandChannelGateService.sendEncodedGet(getCategoryAttrValueList, null, baseRequest, bizParam);
        } else {
            attrValueMap = mtBrandChannelGateService.sendGet(getCategoryAttrValueList, null, baseRequest, bizParam);
        }
        if (!MapUtils.isEmpty(attrValueMap) && !Objects.isNull(attrValueMap.get(ProjectConstant.ERROR))) {
            JSONObject error = (JSONObject) attrValueMap.get(ProjectConstant.ERROR);
            if (error.getInteger("code").equals(ProjectConstant.PLATFORM_CHANNEL_SPECIAL_ATTR_EMPTY_VALUE_CODE)) {
                return response.setStatus(ResultGenerator.genSuccessResult()).setValueList(Lists.emptyList());
            }
            return response.setStatus(ResultGenerator.genFailResult(error.getString(ProjectConstant.MSG)));
        }
        if (MapUtils.isEmpty(attrValueMap) || Objects.isNull(attrValueMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(attrValueMap.get(ProjectConstant.DATA))) {
            return response.setStatus(ResultGenerator.genFailResult("获取美团特殊属性值失败"));
        }
        List<AttrValueInfo> valueInfoList = Lists.newArrayList();
        JSONArray valueJSONArray = (JSONArray) attrValueMap.get(ProjectConstant.DATA);
        List<CategoryAttrValueInfo> resultValueList = valueJSONArray.toJavaList(CategoryAttrValueInfo.class);
        valueInfoList.addAll(mtConverterService.attrValuesMapping(resultValueList));

        return response.setStatus(ResultGenerator.genSuccessResult()).setValueList(valueInfoList);
    }

    /**
     * @param request 支持多门店，内部逐门店串行
     * @return
     */
    @Override
    public UpdateCategoryResponse degradeCategory(CategoryDegradeRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        //逐门店串行
        request.getParamList().forEach(data -> {
            try {
                long storeId = data.getStoreId();
                BaseRequest baseRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_UPDATE, baseRequest, mtConverterService.degradeCategory(data));

                // 结果组装
                ProductResultDataUtils.combineResultData(resultData, postResult, data.getCode(), ChannelTypeEnum.MEITUAN);

            } catch (IllegalArgumentException e) {
                log.error("MtChannelCategoryServiceImpl.degradeCategory 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getCode());

            } catch (Exception e) {
                log.error("MtChannelCategoryServiceImpl.degradeCategory 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getCode());
            }
        });

        return convertResultData(resultData);
    }

    /**
     * @param request 支持多门店，内部逐门店串行
     * @return
     */
    @Override
    public UpdateCategoryResponse adjustCategoryLevel(CategoryLevelAdjustRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        // 逐门店串行
        request.getParamList().forEach(data -> {
            try {
                // 业务参数转换
                ChannelCategoryDTO postData = null;
                switch (data.getAdjustType()) {
                    case UPGRADE:
                        postData = mtConverterService.adjustCategoryLevel1(data);
                        break;
                    case DEGRADE:
                        postData = mtConverterService.adjustCategoryLevel2(data);
                        break;
                    default:
                        throw new IllegalArgumentException("参数错误：调整类型" + data.getAdjustType());
                }

                if (data.isSetSort()) {
                    postData.setSequence(data.getSort());
                }

                long storeId = data.getStoreId();
                BaseRequest baseRequest = new BaseRequest()
                        .setTenantId(tenantId)
                        .setChannelId(channelId)
                        .setStoreIdList(Lists.newArrayList(storeId));

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_UPDATE, baseRequest, postData);

                // 结果组装
                ProductResultDataUtils.combineResultData(resultData, postResult, data.getCode(), ChannelTypeEnum.MEITUAN);

            } catch (IllegalArgumentException e) {
                log.error("MtChannelCategoryServiceImpl.degradeCategory 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getCode());

            } catch (Exception e) {
                log.error("MtChannelCategoryServiceImpl.degradeCategory 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getCode());
            }
        });

        return convertResultData(resultData);
    }

    /**
     * 将ResultData转换为UpdateCategoryResponse
     *
     * @param resultData
     * @return
     */
    private UpdateCategoryResponse convertResultData(ResultData resultData) {
        UpdateCategoryResponse resp = baseConverterService.toUpdateCategoryResponse(resultData);
        resp.getData().addAll(baseConverterService.toUpdateCategoryResponseSuc(resultData.getSucData()));

        return resp;
    }

    /**
     * @param request 支持多门店，内部逐门店串行
     * @return
     */
    @Override
    public UpdateCategoryResponse updateCategoryChannelCode(CategoryUpdateRequest request) {
        log.info("update front category channelCode, param:{}", request);
        UpdateCategoryResponse resp = new UpdateCategoryResponse();
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo(), request.getParamList());
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL)).setData(Collections.emptyList());
        }

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        List<CategoryPoiResult> categoryPoiResults = Lists.newArrayList();
        // 逐门店串行
        for (CategoryInfoUpdateDTO categoryInfoDTO : request.getParamList()) {
            if (null == channelStoreDOMap.get(KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(),
                    request.getBaseInfo().getChannelId(), categoryInfoDTO.getStoreId()))) {
                continue;
            }
            Map updateParam = buildUpdateChannelCodeParam(request, channelStoreDOMap, categoryInfoDTO);

            long storeId = categoryInfoDTO.getStoreId();
            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            // 渠道接口
            Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_UPDATE, baseRequest, updateParam);

            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setStoreId(categoryInfoDTO.getStoreId())
                    .setCode(categoryInfoDTO.getCode());

            // 提前装入结果集
            categoryPoiResults.add(categoryPoiResult);

            if (MapUtils.isEmpty(postResult)) {
                log.error("更新美团渠道类目失败, 渠道返回空, CategoryRequest:{}", request);
                categoryPoiResult.setResultCode(1).setMsg("更新美团渠道类目失败, 渠道返回空");
                continue;
            }

            ChannelResponseDTO<String> channelResponseDTO = postResult.get(storeId);
            if (Objects.isNull(channelResponseDTO)) {
                log.error("更新美团渠道类目失败, 渠道返回空, CategoryRequest:{}", request);
                categoryPoiResult.setResultCode(1).setMsg("更新美团渠道类目失败, 渠道返回空");
                continue;
            }

            if ("ok".equals(channelResponseDTO.getData())) {
                log.info("更新美团渠道类目成功, channelResponseDTO: {}", channelResponseDTO);
                categoryPoiResult.setResultCode(0).setChannelCode(categoryInfoDTO.getCode());
            } else {
                categoryPoiResult.setResultCode(ResultCodeUtils.parseErrorCode(channelResponseDTO.getErrorCode())).setMsg(channelResponseDTO.getErrorMsg());
            }
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    /**
     * @param request 单门店接口
     * @return
     */
    @Override
    public UpdateCategoryResponse updateSmartSortSwitch(CategorySmartSortSwitchRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        long tenantId = request.getTenantId();
        int channelId = request.getChannelId();
        long storeId = request.getStoreId();

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        // 渠道接口
        Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_SEQUENCE_SET,
                baseRequest, buildUpdateChannelCategorySequenceParam(request));

        ProductResultDataUtils.combineResultData(resultData, postResult, ChannelTypeEnum.MEITUAN);
        return convertResultData(resultData);
    }

    /**
     * @param request 单门店接口
     * @return
     */
    @Override
    public CategorySmartSortQueryResponse queryStoreCategorySmartSort(CategorySmartSortQueryRequest request) {

        CatRequest catRequest = new CatRequest();
        BaseRequestSimple simple = new BaseRequestSimple();
        simple.setChannelId(request.getChannelId());
        simple.setTenantId(request.getTenantId());
        catRequest.setBaseInfo(simple);
        catRequest.setStoreId(request.getStoreId());

        GetCategoryResponse getCategoryResponse = mtBrandChannelSkuService.batchGetChannelStoreCategoryInfo(catRequest);

        if (getCategoryResponse.getCatInfoList() == null) {
            return new CategorySmartSortQueryResponse().setStatus(ResultGenerator.genResult(ResultCode.SUCCESS))
                    .setData(Collections.emptyList());
        }

        List<CategorySmartSortDTO> data = new ArrayList<>();
        getCategoryResponse.getCatInfoList().forEach(cat -> {
            CategorySmartSortDTO dto = new CategorySmartSortDTO();
            dto.setChannelCategoryCode(cat.getCatId());
            dto.setSmartSort(cat.getSmartSort() == Constant.VALUE_SMART_SORT_SWITCH_ON);
            dto.setName(cat.getName());
            data.add(dto);
        });
        return new CategorySmartSortQueryResponse().setStatus(ResultGenerator.genResult(ResultCode.SUCCESS))
                .setData(data);
    }

    /**
     * @param request 接口无门店输入；内部通过获取品牌id与渠道交互 todo 待验证
     * @return
     */
    @Override
    public RecommendCategoryResponse recommendCategory(RecommendCategoryRequest request) {
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();
        long storeId = request.getStoreId();
        List<Long> storeIdList = com.google.common.collect.Lists.newArrayList(storeId);

        List<CopAccessConfigDO> copAccessConfigDOS = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOS)) {
            return new RecommendCategoryResponse().setStatus(new ResultStatus(ResultCode.FAIL.getCode(), "获取租户品牌数据失败", null));
        }

        //取appId
        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(tenantId, channelId, storeId);
        //默认取任意一个品牌appId（就以首个）
        Long appId = copAccessConfigDOS.get(0).getAppId();
        if (channelStore != null) {
            appId = channelStore.getAppId();
        }

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setAppId(appId)
                .setStoreIdList(storeIdList);

        // 渠道接口
        String url = mtBrandChannelGateService.getPostUrl(ChannelPostMTEnum.RECOMMEND_CATEGORY);
        Map<String, Object> httpResult = mtBrandChannelGateService.sendEncodedGet(url,
                null,
                baseRequest,
                buildRecommendCategoryParam(request, channelStore));

        if (MapUtils.isEmpty(httpResult)) {
            return new RecommendCategoryResponse().setStatus(new ResultStatus(ResultCode.FAIL.getCode(), "渠道返回空", null));
        }

        //kfpt的接口让我无语凝噎
        String data = httpResult.get("data").toString();
        if (StringUtils.equals("ng", data)) {
            Object error = httpResult.get("error");
            String msg = "渠道返回错误不能解析";

            if (Objects.nonNull(error)) {
                msg = error.toString();
            }

            return new RecommendCategoryResponse().setStatus(new ResultStatus(ResultCode.FAIL.getCode(), msg, httpResult.toString()));
        }

        Map<String, String> object = JSON.parseObject(data, new TypeReference<Map<String, String>>() {
        });
        RecommendCategoryResponse response = new RecommendCategoryResponse();
        response.setStatus(new ResultStatus(ResultCode.SUCCESS.getCode(), "成功", null));
        String tagIdStr = object.get(ProjectConstant.TAG_ID);
        if (StringUtils.isNotBlank(tagIdStr)) {
            response.setChannelCategoryCode(Long.parseLong(tagIdStr));
        }
        response.setUpcCode(object.get(ProjectConstant.UPC_CODE));
        response.setName(object.get(ProjectConstant.NAME));
        response.setCategoryProperties(object.get(ProjectConstant.MT_GENERAL_ATTRS));
        if (object.containsKey(ProjectConstant.MT_WEIGHT_FOR_UNIT)) {
            response.setWeightForUnit(Double.valueOf(object.get(ProjectConstant.MT_WEIGHT_FOR_UNIT)));
        }
        response.setWeightUnit(object.get(ProjectConstant.MT_WEIGHT_UNIT));
        return response;
    }

    @Override
    public UpdateCategoryResponse topCategory(CategoryTopRequest request) {
        log.info("开始置顶美团前台分类 CategoryRequest:{}", request);
        UpdateCategoryResponse resp = new UpdateCategoryResponse();
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo(), request.getParamList());
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setData(Collections.emptyList());
        }

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        //逐门店串行
        List<CategoryPoiResult> categoryPoiResults = Lists.newArrayList();
        for (CategoryInfoTopDTO categoryInfoDTO : request.getParamList()) {
            long storeId = categoryInfoDTO.getStoreId();
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setStoreId(storeId)
                    .setCode(categoryInfoDTO.getCode())
                    .setChannelCode(categoryInfoDTO.getChannelCategoryCode());

            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }
            Map updateParam = Maps.newHashMap();
            updateParam.put(ProjectConstant.APP_POI_CODE, channelStoreDOMap.get(KeyUtils.genChannelStoreKey(tenantId,
                    channelId, storeId)).getChannelOnlinePoiCode());
            updateParam.put(ProjectConstant.CATEGORY_CODE_ORIGIN, categoryInfoDTO.getChannelCategoryCode());
            updateParam.put(ProjectConstant.CATEGORY_CODE, categoryInfoDTO.getChannelCategoryCode());
            updateParam.put(ProjectConstant.CATEGORY_NAME, categoryInfoDTO.getName());
            updateParam.put(ProjectConstant.SEQUENCE, categoryInfoDTO.getSort());
            updateParam.put(ProjectConstant.TOP_FLAG, categoryInfoDTO.getTopFlag());
            //如果开启限时置顶
            if (categoryInfoDTO.getTopFlag() == ProjectConstant.YES) {
                updateParam.put(ProjectConstant.WEEKS_TIME, categoryInfoDTO.getWeeksTime());
                updateParam.put(ProjectConstant.PERIOD, categoryInfoDTO.getPeriod());
            }

            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            // 渠道接口
            Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.CATEGORY_UPDATE, baseRequest, updateParam);

            // 提前装入结果集
            categoryPoiResults.add(categoryPoiResult);

            if (MapUtils.isEmpty(postResult)) {
                log.error("置顶美团前台分类失败, 渠道返回空, CategoryRequest:{}", request);
                categoryPoiResult.setResultCode(1).setMsg("置顶美团前台分类失败, 渠道返回空");
                continue;
            }

            ChannelResponseDTO<String> channelResponseDTO = postResult.get(storeId);
            if (Objects.isNull(channelResponseDTO)) {
                log.error("置顶美团前台分类失败, 渠道返回空, CategoryRequest:{}", request);
                categoryPoiResult.setResultCode(1).setMsg("置顶美团前台分类失败, 渠道返回空");
                continue;
            }

            if ("ok".equals(channelResponseDTO.getData())) {
                log.info("置顶美团前台分类成功, channelResponseDTO: {}", channelResponseDTO);
                categoryPoiResult.setResultCode(0).setChannelCode(categoryInfoDTO.getCode());
            } else {
                categoryPoiResult.setResultCode(ResultCodeUtils.parseErrorCode(channelResponseDTO.getErrorCode())).setMsg(channelResponseDTO.getErrorMsg());
            }
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    @Override
    public GetCategoryResponse queryStoreCategoryList(CatRequest request) {

        CatRequest catRequest = new CatRequest();
        BaseRequestSimple simple = new BaseRequestSimple();
        simple.setChannelId(request.getBaseInfo().getChannelId());
        simple.setTenantId(request.getBaseInfo().getTenantId());
        catRequest.setBaseInfo(simple);
        catRequest.setStoreId(request.getStoreId());
        return mtChannelSkuService.batchGetChannelStoreCategoryInfo(catRequest);
    }

    /**
     * 构建结果
     *
     * @param categoryInfoDTO
     * @param updateCategoryMap
     * @return
     */
    private CategoryPoiResult buildCategoryPoiResultResult(CategoryInfoUpdateDTO categoryInfoDTO, Map updateCategoryMap) {
        CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                .setStoreId(categoryInfoDTO.getStoreId())
                .setCode(categoryInfoDTO.getCode());

        if (ProjectConstant.OK.equals(updateCategoryMap.get(ProjectConstant.DATA))) {
            return categoryPoiResult.setResultCode(0);
        }

        log.error("channel error, channelResult:{}", updateCategoryMap);
        return categoryPoiResult.setResultCode(1)
                .setMsg(JSON.parseObject(String.valueOf(updateCategoryMap.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG));
    }

    /**
     * 构建修改渠道编码参数
     *
     * @param request
     * @param channelStoreDOMap
     * @param categoryInfoDTO
     * @return
     */
    private Map buildUpdateChannelCodeParam(CategoryUpdateRequest request, Map<String, ChannelStoreDO> channelStoreDOMap, CategoryInfoUpdateDTO categoryInfoDTO) {
        Map updateParam = Maps.newHashMap();
        updateParam.put(ProjectConstant.APP_POI_CODE, channelStoreDOMap.get(KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(),
                request.getBaseInfo().getChannelId(), categoryInfoDTO.getStoreId())).getChannelOnlinePoiCode());
        updateParam.put(ProjectConstant.CATEGORY_NAME_ORIGIN, categoryInfoDTO.getName());
        // 设置新的渠道编码(channelCode)
        updateParam.put(ProjectConstant.CATEGORY_CODE, categoryInfoDTO.getCode());
        updateParam.put(ProjectConstant.CATEGORY_NAME, categoryInfoDTO.getName());
        updateParam.put(ProjectConstant.SEQUENCE, categoryInfoDTO.getSort());
        return updateParam;
    }

    /**
     * 构建修改渠道编码参数
     *
     * @param request
     * @return
     */
    private Map<String, Object> buildUpdateChannelCategorySequenceParam(CategorySmartSortSwitchRequest request) {
        Map<String, Object> updateParam = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(request.getTenantId(), request.getChannelId(), request.getStoreId());
        updateParam.put(ProjectConstant.APP_POI_CODE, channelStoreDO.getChannelOnlinePoiCode());

        Map<String, Object> categorySwitch = new HashMap<>(2);
        List<Map<String, Object>> data = new ArrayList<>(1);
        categorySwitch.put(ProjectConstant.CATEGORY_CODE, request.getChannelCategoryCode());
        categorySwitch.put(ProjectConstant.SMART_SWITCH, request.isSmartSort() ? Constant.VALUE_SMART_SORT_SWITCH_ON : Constant.VALUE_SMART_SORT_SWITCH_OFF);
        data.add(categorySwitch);
        updateParam.put(ProjectConstant.CATEGORY_SEQUENCE_DATA, JSON.toJSONString(data));
        return updateParam;
    }

    private Map<String, Object> buildRecommendCategoryParam(RecommendCategoryRequest request, ChannelStoreDO channelStore) {
        Map<String, Object> updateParam = new HashMap<>(2);
        if (StringUtils.isNotBlank(request.getUpcCode())) {
            updateParam.put(ProjectConstant.UPC_CODE, request.getUpcCode());
        }

        if (StringUtils.isNotBlank(request.getName())) {
            updateParam.put(ProjectConstant.NAME, request.getName());
        }
        if (request.isSetQueryType() && request.getQueryType() == QUERY_CHANNEL_DYNAMIC_INFO) {
            updateParam.put(ProjectConstant.MT_TYPE, request.getQueryType());
            updateParam.put(ProjectConstant.TAG_ID, request.getCategoryId());
        }
        if (channelStore != null) {
            String appPoiCode = channelStore.getChannelOnlinePoiCode();
            updateParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        }
        return updateParam;
    }
}
