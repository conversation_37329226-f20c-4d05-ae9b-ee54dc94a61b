package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.PushChannelSourceEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.TenantChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleUpdateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuUpdateResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.PushChannelOperatorType;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.PushChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.PushChannelProductResultProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.ProductPushResultDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.dto.PushChannelResultDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSingleSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuSellStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoSellStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuSellStatusInfoRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Service
@Slf4j
public class PushChannelResultManager {

    @Autowired
    private TenantService tenantService;
    @Autowired
    private PushChannelProductResultProducer pushChannelProductResultProducer;

    /**
     * 批量同步渠道结果 推送mq
     */
    public void sendSpuPushResultMsg(SpuInfoRequest request, ResultSpuData resultSpuData, PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        try {
            if (resultSpuData.getStatus() == null || tenantService.isErpSpuTenant(request.getBaseInfo().getTenantId())) {
                return;
            }

            sendMessage(result2MessageDTO(request, resultSpuData, operatorType, pushChannelType));
        }
        catch (Exception e) {
            log.error("send spu push result error ", e);
        }
    }

    /**
     * 更新上下架
     */
    public void sendSpuPushSaleStatusResultMsg(SpuSellStatusInfoRequest request, ResultSpuData resultSpuData, PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        try {
            if (resultSpuData.getStatus() == null || tenantService.isErpSpuTenant(request.getBaseInfo().getTenantId())) {
                return;
            }

            sendMessage(updateSaleStatus2Message(request, resultSpuData, operatorType, pushChannelType));
        }
        catch (Exception e) {
            log.error("send spu sale status push result error ", e);
        }
    }

    /**
     * 批量同步渠道结果 转化成 mq的消息体
     */
    private Map<Long, PushChannelResultDTO> result2MessageDTO(SpuInfoRequest request, ResultSpuData resultSpuData, PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        Map<Long, PushChannelResultDTO> storeMsgMap = new HashMap<>();
        Map<String, String> customId2SpuIdMap = Fun.toMapQuietly(request.getParamList(), SpuInfoDTO::getCustomSpuId, SpuInfoDTO::getSpuId);
        Map<String, Integer> spuId2SpuStatusMap = buildSpuStatusMap(request);

        if (CollectionUtils.isNotEmpty(resultSpuData.getErrorData())) {
            BaseRequest baseRequest = request.getBaseInfo();
            boolean isSingleStoreId = CollectionUtils.isNotEmpty(baseRequest.getStoreIdList()) && baseRequest.getStoreIdList().size() == 1;
            for (ResultErrorSpu errorSpu : resultSpuData.getErrorData()) {
                if (errorSpu.getSpuInfo() == null || StringUtils.isBlank(errorSpu.getSpuInfo().getCustomSpuId())) {
                    continue;
                }
                long storeId = errorSpu.getStoreId();
                // errorSpu中的门店id可能为空（即默认的0），这里做兜底处理
                if (isSingleStoreId && !Objects.equals(storeId, baseRequest.getStoreIdList().get(0))){
                    storeId = baseRequest.getStoreIdList().get(0);
                }
                PushChannelResultDTO resultMsg = getAndInitPutMsg(storeMsgMap, baseRequest.getTenantId(), baseRequest.getChannelId(), storeId, operatorType, pushChannelType);

                String spuId = customId2SpuIdMap.get(errorSpu.getSpuInfo().getCustomSpuId());
                ProductPushResultDTO productResult = new ProductPushResultDTO();
                productResult.setCustomSpuId(errorSpu.getSpuInfo().getCustomSpuId());
                productResult.setSpuId(spuId);
                productResult.setCode(errorSpu.getErrorCode());
                productResult.setMessage(errorSpu.getErrorMsg());
                productResult.setSoldOut(spuId2SpuStatusMap.get(spuId));

                resultMsg.getResults().add(productResult);
            }
        }

        // 整个请求失败，如限流等，这时候errorData里会没有数据，因此需要补全
        if (resultSpuData.getStatus().getCode() != 0 && CollectionUtils.isEmpty(resultSpuData.getErrorData())) {
            for (Long storeId : request.getBaseInfo().getStoreIdList()) {
                BaseRequest baseRequest = request.getBaseInfo();
                PushChannelResultDTO resultMsg = getAndInitPutMsg(storeMsgMap, baseRequest.getTenantId(), baseRequest.getChannelId(), storeId, operatorType, pushChannelType);

                // 整个接口失败，这就是这批推送所有的商品均失败了
                resultMsg.getResults().addAll(request.getParamList().stream().filter(spu -> StringUtils.isNotBlank(spu.getCustomSpuId())).map(spu -> {
                    ProductPushResultDTO dto = new ProductPushResultDTO();
                    dto.setCustomSpuId(spu.getCustomSpuId());
                    dto.setSpuId(customId2SpuIdMap.get(spu.getCustomSpuId()));
                    dto.setCode(resultSpuData.getStatus().getCode());
                    dto.setMessage(resultSpuData.getStatus().getMsg());
                    return dto;
                }).collect(Collectors.toList()));
            }
        }

        if (CollectionUtils.isNotEmpty(resultSpuData.getSucData())) {
            BaseRequest baseRequest = request.getBaseInfo();
            boolean isSingleStoreId = CollectionUtils.isNotEmpty(baseRequest.getStoreIdList()) && baseRequest.getStoreIdList().size() == 1;
            for (ResultSuccessSpu sucDatum : resultSpuData.getSucData()) {
                long storeId = sucDatum.getStoreId();
                // sucDatum中的门店id可能为空（即默认的0），这里做兜底处理
                if (isSingleStoreId && !Objects.equals(storeId, baseRequest.getStoreIdList().get(0))){
                    storeId = baseRequest.getStoreIdList().get(0);
                }
                PushChannelResultDTO resultMsg = getAndInitPutMsg(storeMsgMap, baseRequest.getTenantId(), baseRequest.getChannelId(), storeId, operatorType, pushChannelType);
                if (StringUtils.isNotBlank(sucDatum.getSpuInfo().getCustomSpuId())) {
                    String spuId = customId2SpuIdMap.get(sucDatum.getSpuInfo().getCustomSpuId());
                    ProductPushResultDTO result = ProductPushResultDTO.success(sucDatum.getSpuInfo().getCustomSpuId(), spuId);
                    result.setSoldOut(spuId2SpuStatusMap.get(spuId));
                    resultMsg.getResults().add(result);
                }

            }
        }

        return storeMsgMap;
    }

    private Map<String, Integer> buildSpuStatusMap(SpuInfoRequest request) {
        Map<String, Integer> spuId2SpuStatusMap = new HashMap<>();
        for (SpuInfoDTO spuInfoDTO : request.getParamList()) {
            spuId2SpuStatusMap.put(spuInfoDTO.getSpuId(), spuInfoDTO.getStatus());
        }
        return spuId2SpuStatusMap;
    }

    /**
     * 批量同步上下架结果 转化成 mq的消息体
     */
    private Map<Long, PushChannelResultDTO> updateSaleStatus2Message(SpuSellStatusInfoRequest request, ResultSpuData resultSpuData, PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        Map<Long, PushChannelResultDTO> storeMsgMap = new HashMap<>();
        Map<String, Integer> customId2SpuStatusMap = buildSpuStatusMap(request);
        if (CollectionUtils.isNotEmpty(resultSpuData.getErrorData())) {
            for (ResultErrorSpu errorSpu : resultSpuData.getErrorData()) {
                if (errorSpu.getSpuInfo() == null || StringUtils.isBlank(errorSpu.getSpuInfo().getCustomSpuId())) {
                    continue;
                }
                PushChannelResultDTO resultMsg = getAndInitPutMsg(storeMsgMap, request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(), errorSpu.getStoreId(), operatorType, pushChannelType);

                ProductPushResultDTO productResult = new ProductPushResultDTO();
                productResult.setCustomSpuId(errorSpu.getSpuInfo().getCustomSpuId());
                productResult.setSoldOut(customId2SpuStatusMap.get(errorSpu.getSpuInfo().getCustomSpuId()));
                productResult.setCode(errorSpu.getErrorCode());
                productResult.setMessage(errorSpu.getErrorMsg());
                resultMsg.getResults().add(productResult);
            }
        }
        // 整个请求失败，如限流等，这时候errorData里会没有数据，因此需要补全
        if (resultSpuData.getStatus().getCode() != 0 && CollectionUtils.isEmpty(resultSpuData.getErrorData())) {
            for (SpuInfoSellStatusDTO spuInfoSellStatusDTO : request.getParamList()) {
                Long storeId = spuInfoSellStatusDTO.getStoreId();

                PushChannelResultDTO resultMsg = getAndInitPutMsg(storeMsgMap, request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(), storeId, operatorType, pushChannelType);
                resultMsg.getResults().addAll(Fun.map(spuInfoSellStatusDTO.getCustomSpuIds(), customSpuId ->
                        ProductPushResultDTO.builder()
                                .customSpuId(customSpuId)
                                .code(resultSpuData.getStatus().getCode())
                                .message(resultSpuData.getStatus().getMsg())
                                .soldOut(spuInfoSellStatusDTO.getSpuStatus())
                                .build()));
            }
        }

        if (CollectionUtils.isNotEmpty(resultSpuData.getSucData())) {
            for (ResultSuccessSpu sucDatum : resultSpuData.getSucData()) {
                if (sucDatum.getSpuInfo() == null || StringUtils.isBlank(sucDatum.getSpuInfo().getCustomSpuId())) {
                    continue;
                }

                PushChannelResultDTO resultMsg = getAndInitPutMsg(storeMsgMap, request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(), sucDatum.getStoreId(), operatorType, pushChannelType);
                ProductPushResultDTO result = ProductPushResultDTO.success(sucDatum.getSpuInfo().getCustomSpuId());
                result.setSoldOut(customId2SpuStatusMap.get(result.getCustomSpuId()));
                resultMsg.getResults().add(result);
            }
        }

        return storeMsgMap;
    }

    private Map<String, Integer> buildSpuStatusMap(SpuSellStatusInfoRequest request) {
        Map<String, Integer> customId2SpuStatusMap = new HashMap<>();
        for (SpuInfoSellStatusDTO spuInfoSellStatusDTO : request.getParamList()) {
            for (String customSpuId : spuInfoSellStatusDTO.getCustomSpuIds()) {
                customId2SpuStatusMap.put(customSpuId, spuInfoSellStatusDTO.getSpuStatus());
            }
        }
        return customId2SpuStatusMap;
    }


    /**
     * 单个spu推送结果
     */
    public void sendSingleSpuPushResultMsg(SingleSpuInfoRequest request, ResultSingleSpuData resultSpuData, PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        try {
            if (resultSpuData.getStatus() == null || tenantService.isErpSpuTenant(request.getBaseInfo().getTenantId())) {
                return;
            }
            BaseRequest baseRequest = request.getBaseInfo();

            sendMessage(singleSpuResult2Msg(baseRequest.getTenantId(), baseRequest.getChannelId(),
                    baseRequest.getStoreIdList(), request.getParam().getCustomSpuId(),
                    resultSpuData,  request.getParam().getStatus(),operatorType, pushChannelType, request.getParam().getSpuId()));
        }
        catch (Exception e) {
            log.error("send single spu push result error ", e);
        }
    }

    /**
     * 单个SPU更新上下架
     */
    public void sendSingleSpuSaleStatusPushResultMsg(SingleSpuSellStatusRequest request, ResultSingleSpuData resultSpuData, PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        try {
            if (resultSpuData == null || tenantService.isErpSpuTenant(request.getBaseInfo().getTenantId())) {
                return;
            }

            BaseRequestSimple baseRequest = request.getBaseInfo();

            sendMessage(singleSpuResult2Msg(baseRequest.getTenantId(), baseRequest.getChannelId(),
                    Lists.newArrayList(request.getParam().getStoreId()), request.getParam().getCustomSpuId(),
                    resultSpuData, request.getParam().getSpuStatus(), operatorType, pushChannelType, null));
        }
        catch (Exception e) {
            log.error("send single spu sale status push result error ", e);
        }
    }



    public void sendMerchantBatchSpuPushResultMsg(MerchantSpuCreateRequest request, MerchantSpuCreateResponse response,
                                                   PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        try {
            TenantChannelRequest baseInfo = request.getBaseInfo();
            if (PushChannelSourceEnum.CleanerTask.getCode() == baseInfo.getSource() ||
                    response.getStatus() == null || tenantService.isErpSpuTenant(baseInfo.getTenantId())) {
                return;
            }
            List<MerchantSpuResult> merchantSpuResultList = response.getSpuResultList();
            Map<String, String> customSpuId2SpuIdMap = Fun.toMapQuietly(request.getParamList(), MerchantSpuDTO::getCustomSpuId, MerchantSpuDTO::getSpuId);
            sendMerchantMessage(batchMerchantSpuResult2Msg(baseInfo.getTenantId(), baseInfo.getAppId(),  baseInfo.getChannelId(),
                    merchantSpuResultList, operatorType, pushChannelType, customSpuId2SpuIdMap));
        }
        catch (Exception e) {
            log.error("send single spu push result error ", e);
        }
    }


    public void sendMerchantBatchSpuPushResultMsg(MerchantSpuUpdateRequest request, MerchantSpuUpdateResponse response,
                                                   PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        try {
            TenantChannelRequest baseInfo = request.getBaseInfo();
            if (PushChannelSourceEnum.CleanerTask.getCode() == baseInfo.getSource() ||
                    response.getStatus() == null || tenantService.isErpSpuTenant(baseInfo.getTenantId())) {
                return;
            }
            List<MerchantSpuResult> merchantSpuResultList = response.getSpuResultList();

            Map<String, String> customSpuId2SpuIdMap = Fun.toMapQuietly(request.getParamList(), MerchantSpuDTO::getCustomSpuId, MerchantSpuDTO::getSpuId);

            sendMerchantMessage(batchMerchantSpuResult2Msg(baseInfo.getTenantId(), baseInfo.getAppId(), baseInfo.getChannelId(),
                    merchantSpuResultList, operatorType, pushChannelType, customSpuId2SpuIdMap));
        }
        catch (Exception e) {
            log.error("send single spu push result error ", e);
        }
    }



    public void sendMerchantSingleSpuPushResultMsg(MerchantSpuSingleCreateRequest request, MerchantSpuSingleCreateResponse response,
                                                   PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        try {
            TenantChannelRequest baseInfo = request.getBaseInfo();
            if (PushChannelSourceEnum.CleanerTask.getCode() == baseInfo.getSource() ||
                    response.getStatus() == null || tenantService.isErpSpuTenant(baseInfo.getTenantId())) {
                return;
            }
            MerchantSpuResult merchantSpuResult = response.getSpuResult();

            sendMerchantMessage(singleMerchantSpuResult2Msg(baseInfo.getTenantId(), baseInfo.getAppId(), baseInfo.getChannelId(),
                    request.getMerchantSpuDTO().getCustomSpuId(), merchantSpuResult, operatorType, pushChannelType, request.getMerchantSpuDTO().getSpuId()));
        }
        catch (Exception e) {
            log.error("send single spu push result error ", e);
        }
    }


    public void sendMerchantSingleSpuPushResultMsg(MerchantSpuSingleUpdateRequest request, MerchantSpuSingleUpdateResponse response,
                                                   PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        try {
            TenantChannelRequest baseInfo = request.getBaseInfo();
            if (PushChannelSourceEnum.CleanerTask.getCode() == baseInfo.getSource() ||
                    response.getStatus() == null || tenantService.isErpSpuTenant(baseInfo.getTenantId())) {
                return;
            }
            MerchantSpuResult merchantSpuResult = response.getSpuResult();

            sendMerchantMessage(singleMerchantSpuResult2Msg(baseInfo.getTenantId(), baseInfo.getAppId(), baseInfo.getChannelId(),
                    request.getMerchantSpuDTO().getCustomSpuId(), merchantSpuResult, operatorType, pushChannelType, null));
        }
        catch (Exception e) {
            log.error("send single spu push result error ", e);
        }
    }


    private Map<Long, PushChannelResultDTO> singleSpuResult2Msg(Long tenantId, Integer channelId, List<Long> requestPoiIds,
                                                                String requestCustomSpuId, ResultSingleSpuData resultSpuData,
                                                                Integer saleStatus,
                                                                PushChannelOperatorType operatorType, PushChannelType pushChannelType, String spuId) {

        Map<Long, PushChannelResultDTO> storeResultMap = new HashMap<>();

        if (resultSpuData.getErrorData() != null) {
            String customSpuId = resultSpuData.getErrorData().getSpuInfo().getCustomSpuId();
            Long storeId = resultSpuData.getErrorData().getStoreId();
            if (StringUtils.isBlank(customSpuId)) {
                return storeResultMap;
            }
            if (storeId == -1 && CollectionUtils.isNotEmpty(requestPoiIds) && requestPoiIds.size() == 1){
                storeId = requestPoiIds.get(0);
            }

            PushChannelResultDTO resultMsg = getAndInitPutMsg(storeResultMap, tenantId, channelId, storeId, operatorType, pushChannelType);

            resultMsg.getResults().add(ProductPushResultDTO.builder()
                    .customSpuId(customSpuId)
                    .spuId(spuId)
                    .code(resultSpuData.getErrorData().getErrorCode())
                    .message(resultSpuData.getErrorData().getErrorMsg())
                    .soldOut(saleStatus)
                    .build());
        }

        // 整个请求失败，如限流等，这时候errorData里会没有数据，因此需要补全
        if (resultSpuData.getStatus().getCode() != 0 && resultSpuData.getErrorData() == null) {
            for (Long storeId : requestPoiIds) {
                PushChannelResultDTO resultMsg = getAndInitPutMsg(storeResultMap, tenantId, channelId, storeId, operatorType, pushChannelType);

                resultMsg.getResults().add(ProductPushResultDTO.builder()
                        .code(resultSpuData.getStatus().getCode())
                        .customSpuId(requestCustomSpuId)
                        .spuId(spuId)
                        .soldOut(saleStatus)
                        .message(resultSpuData.getStatus().getMsg())
                        .build());
            }
        }

        if (resultSpuData.getSucData() != null && resultSpuData.getSucData().getSpuInfo() != null) {
            PushChannelResultDTO resultMsg = getAndInitPutMsg(storeResultMap, tenantId, channelId, resultSpuData.getSucData().getStoreId(), operatorType, pushChannelType);
            ProductPushResultDTO result = ProductPushResultDTO.success(resultSpuData.getSucData().getSpuInfo().getCustomSpuId(), spuId);
            result.setSoldOut(saleStatus);
            resultMsg.getResults().add(result);
        }

        return storeResultMap;
    }


    private PushChannelResultDTO batchMerchantSpuResult2Msg(Long tenantId,
                                                            int appId,
                                                            Integer channelId,
                                                            List<MerchantSpuResult> merchantSpuResultList,
                                                            PushChannelOperatorType operatorType,
                                                            PushChannelType pushChannelType, Map<String, String> customSpuId2SpuIdMap) {

        PushChannelResultDTO pushChannelResultDTO = new PushChannelResultDTO();
        pushChannelResultDTO.setTenantId(tenantId);
        pushChannelResultDTO.setChannelId(channelId);
        pushChannelResultDTO.setContentCode(pushChannelType.getCode());
        pushChannelResultDTO.setOperatorCode(operatorType.getCode());
        if (appId > 0){
            pushChannelResultDTO.setAppId(appId);
        }

        customSpuId2SpuIdMap = Optional.ofNullable(customSpuId2SpuIdMap).orElse(Collections.emptyMap());
        List<ProductPushResultDTO> results = new ArrayList<>();
        for (MerchantSpuResult merchantSpuResult : merchantSpuResultList) {
            // 解决 京东、抖音、有赞渠道新增商品失败，无法记录渠道异常信息的问题
            String spuId = customSpuId2SpuIdMap.get(merchantSpuResult.getCustomSpuId());
            if (merchantSpuResult.getResultCode() != ResultCode.SUCCESS.getCode()) {
                ProductPushResultDTO productPushResultDTO = ProductPushResultDTO.builder()
                        .code(merchantSpuResult.getResultCode())
                        .customSpuId(merchantSpuResult.getCustomSpuId())
                        .spuId(spuId)
                        .message(merchantSpuResult.getResultMsg())
                        .build();
                results.add(productPushResultDTO);
            } else {
                results.add(ProductPushResultDTO.success(merchantSpuResult.getCustomSpuId(), spuId));
            }
        }
        pushChannelResultDTO.setResults(results);

        return pushChannelResultDTO;
    }


    private PushChannelResultDTO singleMerchantSpuResult2Msg(Long tenantId,
                                                             int appId,
                                                             Integer channelId,
                                                             String requestCustomSpuId,
                                                             MerchantSpuResult merchantSpuResult,
                                                             PushChannelOperatorType operatorType,
                                                             PushChannelType pushChannelType,
                                                             String spuId) {

        PushChannelResultDTO pushChannelResultDTO = new PushChannelResultDTO();
        pushChannelResultDTO.setTenantId(tenantId);
        pushChannelResultDTO.setChannelId(channelId);
        pushChannelResultDTO.setContentCode(pushChannelType.getCode());
        pushChannelResultDTO.setOperatorCode(operatorType.getCode());
        if (appId > 0){
            pushChannelResultDTO.setAppId(appId);
        }


        if (merchantSpuResult.getResultCode() != ResultCode.SUCCESS.getCode()) {
            pushChannelResultDTO.setResults(Lists.newArrayList(ProductPushResultDTO.builder()
                    .code(merchantSpuResult.getResultCode())
                    .customSpuId(requestCustomSpuId)
                    .spuId(spuId)
                    .message(merchantSpuResult.getResultMsg())
                    .build()));
        } else {
            pushChannelResultDTO.setResults(Lists.newArrayList(ProductPushResultDTO.success(requestCustomSpuId, spuId)));
        }

        return pushChannelResultDTO;
    }




    /**
     * 获取门店下的消息体
     * 如果没有，则进行初始化，并且放到storeMsgMap中
     */
    private PushChannelResultDTO getAndInitPutMsg(Map<Long, PushChannelResultDTO> storeMsgMap, Long tenantId, Integer channelId, Long storeId, PushChannelOperatorType operatorType, PushChannelType pushChannelType) {
        PushChannelResultDTO resultMsg = storeMsgMap.get(storeId);
        if (resultMsg == null) {
            resultMsg = new PushChannelResultDTO();
            resultMsg.setTenantId(tenantId);
            resultMsg.setPoiId(storeId);
            resultMsg.setChannelId(channelId);
            resultMsg.setOperatorCode(operatorType.getCode());
            resultMsg.setContentCode(pushChannelType.getCode());
            resultMsg.setResults(new ArrayList<>());
            storeMsgMap.put(storeId, resultMsg);
        }
        return resultMsg;
    }

    private void sendMessage(Map<Long, PushChannelResultDTO> storeResultMap) {
        log.info("异常商品信息结果发送:{}", JacksonUtils.toJson(storeResultMap));
        for (Map.Entry<Long, PushChannelResultDTO> entry : storeResultMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue().getResults())) {
                continue;
            }
            String partKey = String.format("%d_%d", entry.getKey(), entry.getValue().getChannelId());
            pushChannelProductResultProducer.sendMessageSync(entry.getValue(), partKey);
        }
    }

    private void sendMerchantMessage(PushChannelResultDTO pushChannelResultDTO) {
        String partKey = String.format("%d_%d", pushChannelResultDTO.getTenantId(), pushChannelResultDTO.getChannelId());
        pushChannelProductResultProducer.sendMessageSync(pushChannelResultDTO, partKey);
    }

}
