package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * 渠道门店配送服务
 * @author: luokun04
 * @Date: 2019/8/6
 */
public interface ChannelPoiShippingService {

    /**
     * 更新门店配送范围
     * @param updatePoiShippingRequest
     * @return
     */
    ResultStatus updatePoiShipping(UpdatePoiShippingRequest updatePoiShippingRequest);

    ResultStatus batchUpdatePoiShipping(BatchUpdatePoiShippingRequest request);

    ResultStatus resetPoiShipping(ResetPoiShippingRequest resetPoiShippingRequest);

    ResultStatus deletePoiShipping(DeletePoiShippingRequest deletePoiShippingRequest);

    /**
     * 查询门店配送范围
     */
    QueryPoiShippingResponse queryPoiShipping(QueryPoiShippingRequest queryPoiShippingRequest);

    /**
     * 批量查询多门店配送范围
     * @param request
     * @return
     */
    QueryPoiShippingAreaResponse batchQueryPoiShippingAreaInfo(BatchQueryPoiShippingAreaRequest request);


    /**
     * 查询单门店配送范围信息(结果包含mtShippingAreaId)
     * @param request
     * @return
     */
    QueryPoiShippingAreaResponse queryPoiShippingAreaInfo(QueryPoiShippingRequest request);

    /**
     * 通过shippingAreaId来更新正常时段的配送范围
     * @param request
     * @return
     */
    ResultStatus updatePoiRegularPeriodShippingByShippingAreaId(UpdatePoiRegularPeriodShippingByShippingAreaIdRequest request);

    /**
     * 通过shippingAreaId来更新特殊时段的配送范围
     * @param request
     * @return
     */
    ResultStatus updatePoiSpecialPeriodShippingByShippingAreaId(UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest request);

    /**
     * 通过shippingAreaId来删除指定配送
     * @param request
     * @return
     */
    ResultStatus deletePoiShippingByShippingAreaId(DeletePoiShippingByShippingIdRequest request);

}
