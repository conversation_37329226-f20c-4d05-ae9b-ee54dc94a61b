package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.google.common.collect.Maps;
import com.meituan.shangou.saas.tenant.thrift.PoiRelationThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiRelationTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiRelationQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiRelationSimpleMapResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: goulei02
 * @date: 2024/9/26
 */
@Slf4j
@Service
public class PoiRelationThriftServiceProxy {

    @Resource
    private PoiRelationThriftService poiRelationThriftService;

    public Map<Long, Long> queryStoreWarehouseMapping(Long tenantId, List<Long> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Maps.newHashMap();
        }
        PoiRelationQueryRequest request = new PoiRelationQueryRequest();
        request.setTenantId(tenantId);
        request.setPoiIdList(storeIds);
        request.setReverseRelation(false);
        request.setRelationType(PoiRelationTypeEnum.STORE_SHAREABLE_WAREHOUSE_RELATION.code());
        log.info("poiRelationThriftService batchQueryRelationMapByPoiIds request:{}", request);
        PoiRelationSimpleMapResponse response = poiRelationThriftService.batchQueryRelationMapByPoiIds(request);
        log.info("poiRelationThriftService batchQueryRelationMapByPoiIds response:{}", response);
        if (response == null || response.getStatus() == null || response.getStatus().getCode() != 0) {
            throw new ChannelBizException("查询门店与共享仓映射关系异常");
        }
        return Optional.ofNullable(response.getPoiRelationMap()).orElse(new HashMap<>())
                .entrySet()
                .stream()
                .filter(it -> CollectionUtils.isNotEmpty(it.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, it -> it.getValue().get(0), (f, s) -> s));
    }
}
