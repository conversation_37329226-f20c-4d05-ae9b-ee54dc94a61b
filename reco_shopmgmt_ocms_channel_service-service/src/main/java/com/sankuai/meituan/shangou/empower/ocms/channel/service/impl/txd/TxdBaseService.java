package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostTxdEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.AppThriftService;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.AppInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByPoiRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByTenantRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.response.AppInfoListQueryResponse;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.response.AppInfoQueryResponse;
import com.taobao.api.ApiException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.TaobaoRequest;
import com.taobao.api.TaobaoResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/4/18
 */
@Slf4j
@Service
public class TxdBaseService extends BaseChannelGateService {
    @Resource
    private ClusterRateLimiter clusterRateLimiter;
    @Autowired
    private AppThriftService appThriftService;

    public String getChannelPoiCode(Long storeId, BaseRequest baseRequest) {
        Map<String, ChannelStoreDO> pois = getTenantPoiInfo(baseRequest);
        String channelStoreKey = KeyUtils.genChannelStoreKey(baseRequest.getTenantId(), baseRequest.getChannelId(), storeId);

        if (pois.get(channelStoreKey) != null) {
            return pois.get(channelStoreKey).getChannelPoiCode();
        }
        else {
            log.warn("未查询到渠道门店id:{},{}", baseRequest, storeId);
            return null;
        }
    }


    public TaobaoResponse sendPost(ChannelPostInter postUrlEnum, BaseRequest baseRequest, TaobaoRequest request) throws ApiException {
        // 获取系统参数
        Map<String, Object> sysParam = getChannelSysParams(baseRequest);
        // 限流检查
        if (!rhinoCheck(postUrlEnum, (String) sysParam.get(ProjectConstant.TXD_APP_KEY))) {
            TaobaoResponse response = new TaobaoResponse() {
            };
            response.setErrorCode(String.valueOf(ResultCode.TRIGGER_LIMIT.getCode()));
            response.setCode(String.valueOf(ResultCode.TRIGGER_LIMIT.getCode()));
            response.setMessage(ResultCode.TRIGGER_LIMIT.getMsg());
            response.setMsg(ResultCode.TRIGGER_LIMIT.getMsg());
            return response;
        }
        // 调用
        TaobaoClient client = new DefaultTaobaoClient(TxdConstant.defaultHttpsAddressUrl, (String) sysParam.get(ProjectConstant.TXD_APP_KEY),
                (String) sysParam.get(ProjectConstant.SECRET));
        // 图片上传不打印完整入参
        if (postUrlEnum == ChannelPostTxdEnum.PICTURE_UPLOAD) {
            log.info("调用渠道入参: url:{},base request:{}", postUrlEnum.getUrl(), JacksonUtils.toJson(baseRequest));
        }
        else {
            log.info("调用渠道入参:{}", JacksonUtils.toJson(request));
        }
        TaobaoResponse response = client.execute(request, (String) sysParam.get(ProjectConstant.TXD_SESSION_KEY));
        channelResponseMetric(ChannelTypeEnum.TXD, postUrlEnum, baseRequest, response, resp -> resp.isSuccess() ? null : resp.getMessage());
        log.info("渠道原始日志:{}", JacksonUtils.toJson(response));
        return response;
    }

    /**
     * 获取淘鲜达系统参数
     * @param baseRequest
     * @return
     */
    @Override
    public Map<String, Object> getChannelSysParams(BaseRequest baseRequest) {
        Map<String, Object> sysParam = new HashMap<>();
        try {
            AppInfoDTO data;
            // 有门店id可获取到唯一的sessionKey,没有则取列表第一个
            if (CollectionUtils.isNotEmpty(baseRequest.getStoreIdList())) {
                AppInfoQueryByPoiRequest queryByPoiRequest = new AppInfoQueryByPoiRequest();
                queryByPoiRequest.setTenantId(baseRequest.getTenantId());
                queryByPoiRequest.setChannelId(baseRequest.getChannelId());
                queryByPoiRequest.setPoiId(baseRequest.getStoreIdList().get(0));
                AppInfoQueryResponse appInfoQueryResponse = appThriftService.queryAppInfoByPoi(queryByPoiRequest);
                if (appInfoQueryResponse == null || appInfoQueryResponse.getData() == null) {
                    throw new BizException("获取淘鲜达渠道session key 失败");
                }
                data = appInfoQueryResponse.getData();
            }
            else {
                AppInfoQueryByTenantRequest queryByTenantRequest = new AppInfoQueryByTenantRequest();
                queryByTenantRequest.setTenantId(baseRequest.getTenantId());
                queryByTenantRequest.setChannelIdList(Collections.singletonList(baseRequest.getChannelId()));
                AppInfoListQueryResponse appInfoListQueryResponse = appThriftService.queryAppInfoByTenant(queryByTenantRequest);
                if (appInfoListQueryResponse == null || CollectionUtils.isEmpty(appInfoListQueryResponse.getData())) {
                    throw new BizException("获取淘鲜达渠道session key 失败");
                }
                data = appInfoListQueryResponse.getData().get(0);
            }
            sysParam.put(ProjectConstant.TXD_APP_KEY, data.getAppKey());
            sysParam.put(ProjectConstant.SECRET, data.getSecret());
            sysParam.put(ProjectConstant.TXD_SESSION_KEY, data.getAccessToken());
        }
        catch (Exception e) {
            log.error("get txd session error,request:{}", baseRequest, e);
            throw new BizException("获取淘鲜达渠道session key 失败");
        }
        return sysParam;
    }

    public boolean rhinoCheck(ChannelPostInter channelPostInter, String uuid) {
        // 限流检查
        if (MccConfigUtil.getChannelLimiterErrorPoster().contains(channelPostInter)) {
            if (channelPostInter.requestLimited()) {
                boolean pass = clusterRateLimiter.tryAcquire(channelPostInter, uuid);
                if (!pass) {
                    log.info("TxdChannelSpuServiceImpl.postToChannel appId:{} url:{} 未获取到令牌 直接返回null", uuid,
                            channelPostInter.getUrl());
                }
                return pass;
            }
        }
        return true;
    }

    @Override
    protected <T> T postToChannel(String channelPoiCode, String channelOnlinePoiCode, ChannelPostInter postUrlEnum, Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest) {
        return null;
    }

    @Override
    protected Map generatePostParams(String url, String method, Map<String, Object> sysParam, Map<String, Object> bizParam) {
        return Collections.emptyMap();
    }

    @Override
    protected String getPostUrl(ChannelPostInter postUrlEnum) {
        return "";
    }
}
