package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostYZEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSkuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.YzConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChangeCustomSkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSkuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.PictureUploadStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuSellStatusInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ImageUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.gen.v3_0_0.api.YouzanItemcategoriesTaglistSearch;
import com.youzan.cloud.open.sdk.gen.v3_0_0.api.YouzanMaterialsStoragePlatformImgUpload;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: wangyihao04
 * @Date: 2022/11/30 11:07 AM
 * @Mail: <EMAIL>
 */
@Slf4j
@Service("yzToolChannelSkuService")
public class YzToolChannelSkuServiceImpl extends YouZanToolBaseService implements ChannelSkuService {

    @Resource(name = "yzPictureUploadThreadPool")
    private ExecutorService pictureUploadThreadPool;

    @Resource
    private YzConverterService yzConverterService;

    @Override
    public ResultData skuCreate(SkuInfoRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }

    @Override
    public ResultData upcCreate(SkuInfoRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }

    @Override
    public ResultData updateSku(SkuInfoRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }

    @Override
    public ResultData deleteSku(SkuInfoDeleteRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }

    @Override
    public ResultData pictureUpload(PictureUploadRequest request) {
        int channelId = request.getBaseInfo().getChannelId();
        AppMessage appMessage = getMainAppMessage(request.getBaseInfo().getTenantId());
        List<ResultSuccessSku> successList = new ArrayList<>();
        List<ResultErrorSku> errorList = new ArrayList<>();
        List<Future<ImageUploadResult>> futureList = request.getParamList()
                .stream()
                .map(pictureUploadDTO -> pictureUploadThreadPool.submit(
                        () -> doUpload(appMessage, pictureUploadDTO)))
                .collect(Collectors.toList());
        futureList.forEach(future -> {
            try {
                ImageUploadResult uploadResult = future.get(5000, TimeUnit.SECONDS);
                if (uploadResult != null && uploadResult.success) {
                    StringBuilder result = new StringBuilder().append(uploadResult.imageId);
                    if (request.supportYzUrl) {
                        result.append("|").append(uploadResult.imageUrl);
                    }
                    successList.add(buildResultSuccessSku(channelId, uploadResult.skuId, result.toString()));
                }
                else {
                    errorList.add(buildResultErrorSku(channelId, uploadResult.skuId, uploadResult.msg));
                }
            } catch (Exception e) {
                log.warn("上传图片失败未知失败", e);
            }
        });

        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        resultData.setSucData(successList);
        resultData.setErrorData(errorList);
        return resultData;
    }

    private ImageUploadResult doUpload(AppMessage appMessage, PictureUploadDTO pictureUploadDTO) {
        File file = null;
        try {
            file = ImageUtils.urlToFile(pictureUploadDTO.getUrl());
            YouzanMaterialsStoragePlatformImgUpload youzanMaterialsStoragePlatformImgUpload = new YouzanMaterialsStoragePlatformImgUpload();

            //创建参数对象,并设置参数
            YouzanMaterialsStoragePlatformImgUploadParams youzanMaterialsStoragePlatformImgUploadParams = new YouzanMaterialsStoragePlatformImgUploadParams();
            youzanMaterialsStoragePlatformImgUploadParams.setImage(file);
            youzanMaterialsStoragePlatformImgUpload.setAPIParams(youzanMaterialsStoragePlatformImgUploadParams);
            YouzanMaterialsStoragePlatformImgUploadResult result4YouZan = getResult4YouZanByRhino(ChannelPostYZEnum.UPLOAD_SPU_IMG,
                    appMessage, youzanMaterialsStoragePlatformImgUpload, YouzanMaterialsStoragePlatformImgUploadResult.class);
            if (result4YouZan == null){
                throw new BizException("上传图片未响应");
            }
            if (!result4YouZan.getSuccess()){
                throw new BizException(String.format("上传图片失败, %s", result4YouZan.getMessage()));
            }
            if (result4YouZan.getData() == null){
                throw new BizException("上传图片返回结果没有数据");
            }
            return ImageUploadResult.buildSuccess(pictureUploadDTO.getUid(),
                    result4YouZan.getData().getImageId(), result4YouZan.getData().getImageUrl());
        } catch (Exception e){
            log.warn("有赞上传图片异常", e);
            return ImageUploadResult.buildError(pictureUploadDTO.getUid(), e.getMessage());
        } finally {
            log.info("删除上传后的图片文件, 文件长度 {}", file.length());
            cleanFile(file);
        }
    }

    static class ImageUploadResult{
        boolean success;
        String msg;
        String skuId;
        Long imageId;

        String imageUrl;

        static ImageUploadResult buildSuccess(String skuId, Long imageId, String imageUrl){
            ImageUploadResult result = new ImageUploadResult();
            result.success = true;
            result.skuId = skuId;
            result.imageId = imageId;
            result.imageUrl = imageUrl;
            return result;
        }

        static ImageUploadResult buildError(String skuId, String msg){
            ImageUploadResult result = new ImageUploadResult();
            result.success = false;
            result.msg = msg;
            result.skuId = skuId;
            return result;
        }
    }

    /**
     * if (!result4YouZan.getSuccess()){
     * throw new BizException(String.format("上传图片失败, %s", result4YouZan.getMessage()));
     * }
     * ResultSuccessSku successSku = buildResultSuccessSku(channelId,
     * pictureUploadDTO.getUid(), result4YouZan.getData().getImageId());
     * successList.add(successSku);
     * <p>
     * } catch (Exception e) {
     * ResultErrorSku errorSku = buildResultErrorSku(channelId, pictureUploadDTO.getUid(), e.getMessage());
     * errorList.add(errorSku);
     * //异常打日志，不抛出，避免影响后续图片上传
     * log.error("上传图片失败, 参数 {} ", pictureUploadDTO, e);
     * }
     *
     * @param channelId
     * @param skuId
     * @param msg
     * @return
     */

    private ResultErrorSku buildResultErrorSku(Integer channelId, String skuId, String msg) {
        ResultErrorSku errorSku = new ResultErrorSku()
                .setSkuId(skuId)
                .setChannelId(channelId)
                .setErrorMsg(msg)
                .setErrorCode(ResultCode.FAIL.getCode());
        return errorSku;
    }

    private ResultSuccessSku buildResultSuccessSku(Integer channelId, String skuId, String resultInfo) {
        ResultSuccessSku successSku = new ResultSuccessSku()
                .setSkuId(skuId)
                .setChannelId(channelId)
                .setChannelResultInfo(resultInfo);
        return successSku;
    }

    private void cleanFile(File file) {
        if (file == null) {
            return;
        }
        try {
            Files.delete(file.toPath());
        } catch (IOException e) {
            log.error("删除上传图片的暂存文件失败", e);
        }
    }

    @Override
    public PictureUploadResult getPictureUploadStatus(PictureUploadStatusRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }

    @Override
    public ResultData updateSkuSellStatus(SkuSellStatusInfoRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }

    @Override
    public ResultData updateSkuSellStatusForCleaner(SkuSellStatusInfoRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }

    @Override
    public ResultData updateCustomSkuId(UpdateCustomSkuIdRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }

    @Override
    public BatchGetSkuInfoResponse batchGetSkuInfo(BatchGetSkuInfoRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }

    @Override
    public GetSkuInfoResponse getSkuInfo(GetSkuInfoRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }

    @Override
    public GetCategoryResponse batchGetChannelStoreCategoryInfo(CatRequest request) {
        GetCategoryResponse response = new GetCategoryResponse().setStatus(ResultGenerator.genSuccessResult());
        if (request == null || request.getBaseInfo() == null)
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "拉取有赞分类失败：参数错误"));
        AppMessage appMessage = getMainAppMessage(request.getBaseInfo().getTenantId());
        YouzanItemcategoriesTaglistSearch youzanItemcategoriesTaglistSearch = new YouzanItemcategoriesTaglistSearch();
        // 创建参数对象,并设置参数
        YouzanItemcategoriesTaglistSearchParams youzanItemcategoriesTaglistSearchParams = new YouzanItemcategoriesTaglistSearchParams();
        int pageNo = 1;
        youzanItemcategoriesTaglistSearchParams.setPageNo(pageNo);
        youzanItemcategoriesTaglistSearchParams.setPageSize(20);
        youzanItemcategoriesTaglistSearchParams.setOrderBy("id:asc");
        youzanItemcategoriesTaglistSearch.setAPIParams(youzanItemcategoriesTaglistSearchParams);
        YouzanItemcategoriesTaglistSearchResult result = null;
        List<YouzanItemcategoriesTaglistSearchResult.YouzanItemcategoriesTaglistSearchResultTags> list = new ArrayList<>();
        while (pageNo <= 100){
            try {
                result = getResult4YouZanByRhino(ChannelPostYZEnum.CATEGORY_SEARCH, appMessage, youzanItemcategoriesTaglistSearch, YouzanItemcategoriesTaglistSearchResult.class);
            }
            catch (BizException e) {
                log.error("batchGetChannelStoreCategoryInfo bizException", e);
                return response.setStatus(ResultGenerator.genResult(ResultCode.parseOrDefault(e.getErrorCode(), ResultCode.FAIL), e.getMessage()));
            }
            catch (Exception e) {
                log.error("batchGetChannelStoreCategoryInfo error", e);
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, e.getMessage()));
            }
            log.info("查询有赞分类详情:tenantId {}, result {}.", request.getBaseInfo().getTenantId(), JSON.toJSONString(result));
            if (!result.getSuccess()){
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, result.getMessage()));
            }
            if(CollectionUtils.isEmpty(result.getData().getTags())){
                break;
            }
            list.addAll(result.getData().getTags());
            youzanItemcategoriesTaglistSearchParams.setPageNo(++pageNo);
            youzanItemcategoriesTaglistSearchParams.setPageSize(20);
            youzanItemcategoriesTaglistSearch.setAPIParams(youzanItemcategoriesTaglistSearchParams);
        }
        List<CatInfo> catInfos = new ArrayList<>(yzConverterService.convertCatInfos(list));
        return response.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(catInfos);
    }

    @Override
    public ResultData changeCustomSkuId(ChangeCustomSkuIdRequest request) {
        throw new UnsupportedOperationException("暂不支持的操作");
    }
}
