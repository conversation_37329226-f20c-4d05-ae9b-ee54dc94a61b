package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator.genResult;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SubmitAppealInfoRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.shangou.saas.tenant.thrift.common.constants.TenantConfigConstants;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ChannelConfigDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostTxdEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSpuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.TxdConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BasePageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSingleSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchUpdateCustomSpuStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetHeadQuarterSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAppFoodCodeBySkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAuditStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAuditStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryChannelSpuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryChannelSpuIdResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryNormAuditDelSpuRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryNormAuditDelSpuResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryQualityProblemRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryQualityProblemResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuSellStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuSellStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuId2AppFoodCodeResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoSellStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuSellStatusInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStoreCategoryCodeDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStoreCategoryInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuUpcUpdateDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuUpcUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuWeightInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuIdByNameAndSpecRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuIdByOriginIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultSpuDataUtils;
import com.taobao.api.ApiException;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.response.AlibabaWdkSkuQueryResponse;
import com.taobao.api.response.AlibabaWdkSkuStoreskuScrollQueryResponse;
import com.youzan.cloud.open.sdk.common.exception.SDKException;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024/4/16
 */
@Slf4j
@Service("txdChannelSpuService")
public class TxdChannelSpuServiceImpl implements ChannelSpuService {

    @Autowired
    private TxdBaseService txdBaseService;

    @Autowired
    private TenantService tenantService;

    public static final String AX_CHANNEL_CODE = "31";
    public static final String TXD_CHANNEL_CODE = "4";


    @Override
    public ResultSpuData spuCreate(SpuInfoRequest request) {
        return spuCreateCommon(request, ChannelPostTxdEnum.SKU_CREATE);
    }

    @Override
    public ResultSpuData spuCreateForCleaner(SpuInfoRequest request) {
        return spuCreateCommon(request, ChannelPostTxdEnum.SKU_CREATE);
    }


    private ResultSpuData spuCreateCommon(SpuInfoRequest request, ChannelPostInter channelPostInter) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        List<SpuInfoDTO> spuList = request.getParamList();
        Long storeId = getStoreId(request);
        if (CollectionUtils.isEmpty(spuList)) {
            return resultData;
        }
        List<SpuKey> spuKeys = Fun.map(spuList, spu -> getSpuKey(storeId, spu));
        try {
            // 1 创建商品
            TaobaoResponse commonResponse = txdBaseService.sendPost(channelPostInter, request.getBaseInfo(), TxdConvertUtil.buildAddRequest(txdBaseService.getChannelPoiCode(storeId, request.getBaseInfo()), spuList));
            return TxdConvertUtil.genAddSpuResult(storeId, spuKeys, commonResponse);
        } catch (ApiException e) {
            log.warn("调用淘鲜达创建商品接口异常, request {}.", request, e);
            ProductResultDataUtils.combineExceptionDataList(resultData, TxdConvertUtil.parseTxdErrorCode(e.getErrCode()), e.getMessage(),
                    spuKeys, ChannelTypeEnum.TXD);
        } catch (Exception e) {
            log.warn("创建淘鲜达商品系统异常, request {}.", request, e);
            ProductResultDataUtils.combineExceptionDataList(resultData, ResultCode.FAIL.getCode(), e.getMessage(), spuKeys, ChannelTypeEnum.TXD);
        }
        return resultData;
    }


    private Long getStoreId(SpuInfoRequest request) {
        Long storeId;
        if (CollectionUtils.isNotEmpty(request.getBaseInfo().getStoreIdList())) {
            storeId = request.getBaseInfo().getStoreIdList().get(0);
        } else {
            storeId = 0L;
        }
        return storeId;
    }

    private SpuKey getSpuKey(Long storeId, SpuInfoDTO data) {
        SpuKey spuKey = new SpuKey().setCustomSpuId(data.getCustomSpuId());
        List<SkuKey> skuKeys = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(data.getSkus())) {
            data.getSkus().forEach(skuInSpuInfoDTO -> {
                SkuKey skuKey = new SkuKey().setCustomSkuId(skuInSpuInfoDTO.getCustomSkuId());
                skuKeys.add(skuKey);
            });
        }
        spuKey.setSkus(skuKeys);
        if (storeId != null) {
            spuKey.setStoreId(storeId);
        }
        return spuKey;
    }

    private SpuKey getSpuKey(Long storeId, SpuStoreCategoryCodeDTO data) {
        SpuKey spuKey = new SpuKey().setCustomSpuId(data.getCustomSpuId());
        if (storeId != null) {
            spuKey.setStoreId(storeId);
        }
        return spuKey;
    }

    @Override
    public ResultSpuData upcCreate(SpuInfoRequest request) {
        return spuCreateCommon(request, ChannelPostTxdEnum.SKU_CREATE);
    }


    @Override
    public ResultSpuData updateBySpuOrUpc(SpuInfoRequest request) {
        return updateBySpuOrUpcCommon(request, ChannelPostTxdEnum.SKU_UPDATE);
    }

    @Override
    public ResultSpuData updateBySpuOrUpcForCleaner(SpuInfoRequest request) {
        return updateBySpuOrUpcCommon(request, ChannelPostTxdEnum.SKU_UPDATE);
    }


    private ResultSpuData updateBySpuOrUpcCommon(SpuInfoRequest request, ChannelPostInter channelPostInter) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        List<SpuInfoDTO> spuList = request.getParamList();
        Long storeId = getStoreId(request);
        if (CollectionUtils.isEmpty(spuList)) {
            return resultData;
        }
        List<SpuKey> spuKeys = Fun.map(spuList, spu -> getSpuKey(storeId, spu));
        try {
            Long tenantId = request.getBaseInfo().getTenantId();
            TaobaoResponse commonResponse = txdBaseService.sendPost(channelPostInter, request.getBaseInfo(), TxdConvertUtil.buildUpdateRequest(txdBaseService.getChannelPoiCode(storeId, request.getBaseInfo()), tenantId, spuList));
            return TxdConvertUtil.genUpdateSpuResult(storeId, spuKeys, commonResponse);
        } catch (ApiException e) {
            log.warn("调用淘鲜达更新商品接口异常, request {}.", request, e);
            ProductResultDataUtils.combineExceptionDataList(resultData, TxdConvertUtil.parseTxdErrorCode(e.getErrCode()), e.getMessage(),
                    spuKeys, ChannelTypeEnum.TXD);
        } catch (Exception e) {
            log.warn("更新淘鲜达商品系统异常, request {}.", request, e);
            ProductResultDataUtils.combineExceptionDataList(resultData, ResultCode.FAIL.getCode(), e.getMessage(), spuKeys, ChannelTypeEnum.TXD);
        }
        return resultData;
    }

    @Override
    public ResultSpuData updateWeightBySpu(SpuWeightInfoRequest request) {
        return null;
    }

    @Override
    public ResultSpuData deleteSpu(SpuInfoDeleteRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        List<SpuInfoDeleteDTO> paramList = request.getParamList();
        if (CollectionUtils.isEmpty(paramList)) {
            return resultData;
        }

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        Map<Long, List<SpuInfoDeleteDTO>> storeSpuMap = Fun.groupingBy(paramList, SpuInfoDeleteDTO::getStoreId);
        storeSpuMap.forEach((storeId, oneStoreParams) -> {
            List<SpuKey> spuKeys = Fun.map(oneStoreParams, SpuInfoDeleteDTO::getSpuKey);
            BaseRequest storeRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));
            String channelPoiId = txdBaseService.getChannelPoiCode(storeId, storeRequest);
            try {
                TaobaoResponse updateResult = txdBaseService.sendPost(ChannelPostTxdEnum.SKU_UPDATE, storeRequest, TxdConvertUtil.buildUpdateRequestForDelete(channelPoiId, tenantId, oneStoreParams));
                ResultSpuData oneStoreResultSpuData = TxdConvertUtil.genUpdateSpuResult(storeId, spuKeys, updateResult);
                resultData.getSucData().addAll(oneStoreResultSpuData.getSucData());
                resultData.getErrorData().addAll(oneStoreResultSpuData.getErrorData());
            } catch (ApiException e) {
                log.warn("调用淘鲜达更新商品接口异常, request {}.", request, e);
                ProductResultDataUtils.combineExceptionDataList(resultData, TxdConvertUtil.parseTxdErrorCode(e.getErrCode()),
                        e.getMessage(), spuKeys, ChannelTypeEnum.TXD);
            } catch (Exception e) {
                log.warn("更新淘鲜达商品系统异常, request {}.", request, e);
                ProductResultDataUtils.combineExceptionDataList(resultData, ResultCode.FAIL.getCode(), e.getMessage(), spuKeys, ChannelTypeEnum.TXD);
            }
        });
        return resultData;
    }

    @Override
    public ResultSingleSpuData deleteSingleSpu(SingleSpuInfoDeleteRequest request) {
        ResultSingleSpuData resultData = ResultGenerator.genResultSingleSpuData(ResultCode.SUCCESS);

        int channelId = request.getBaseInfo().getChannelId();
        long tenantId = request.getBaseInfo().getTenantId();
        long storeId = request.getParam().getStoreId();

        BaseRequest storeRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Collections.singletonList(storeId));

        SpuKey spuKey = Optional.ofNullable(request.getParam())
                .map(SpuInfoDeleteDTO::getSpuKey)
                .orElse(new SpuKey(request.getParam().getCustomSpuId(), request.getParam().getChannelSpuId(), Collections.emptyList()));

        try {
            String txdChannelCode = getTxdChannelCode(tenantId);
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SINGLE_SKU_STATUS_UPDATE, storeRequest,
                    TxdConvertUtil.buildSingleSpuOffSaleRequest(
                            txdBaseService.getChannelPoiCode(storeId, storeRequest),
                            request.getParam(),
                            txdChannelCode));
            return TxdConvertUtil.genUpdateSingleSpuStatusResult(storeId, spuKey, commonResponse);
        }
        catch (ApiException e) {
            log.warn("调用淘鲜达商品接口异常, request {}.", request, e);
            return ResultSpuDataUtils.combineExceptionData(resultData, e.getMessage(), spuKey);
        }
        catch (Exception e) {
            log.warn("更新淘鲜达商品上下架系统异常, request {}.", request, e);
            return ResultSpuDataUtils.combineExceptionData(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), spuKey);
        }
    }

    @Override
    public ResultSpuData deleteCategoryAndSpu(BaseRequest request) {
        return null;
    }

    @Override
    public ResultSpuData deleteSku(SkuInSpuInfoDeleteRequest request) {
        return null;
    }

    @Override
    public ResultSpuData updateSpuSellStatus(SpuSellStatusInfoRequest request) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        List<SpuInfoSellStatusDTO> paramList = request.getParamList();

        if (CollectionUtils.isEmpty(paramList)) {
            return resultData;
        }

        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();

        paramList.forEach(oneStoreParam -> {
            Long storeId = oneStoreParam.getStoreId();
            List<SpuKey> spuKeys = Fun.map(oneStoreParam.getSpuKeys(), spu -> spu.setStoreId(storeId));
            BaseRequest storeRequest = new BaseRequest()
                    .setTenantId(tenantId)
                    .setChannelId(channelId)
                    .setStoreIdList(Lists.newArrayList(storeId));

            try {
                TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SKU_STATUS_UPDATE, storeRequest, TxdConvertUtil.buildStatusRequest(txdBaseService.getChannelPoiCode(storeId, storeRequest), oneStoreParam));
                ResultSpuData oneStoreSpuData = TxdConvertUtil.genUpdateSpuStatusResult(storeId, spuKeys, commonResponse);
                resultData.getSucData().addAll(oneStoreSpuData.getSucData());
                resultData.getErrorData().addAll(oneStoreSpuData.getErrorData());
            }
            catch (ApiException e) {
                log.warn("调用淘鲜达更新上下架商品接口异常, request {}.", request, e);
                ProductResultDataUtils.combineExceptionDataList(resultData, TxdConvertUtil.parseTxdErrorCode(e.getErrCode()),
                        e.getMessage(), spuKeys, ChannelTypeEnum.TXD);
            } catch (Exception e) {
                log.warn("更新淘鲜达商品上下架系统异常, request {}.", request, e);
                ProductResultDataUtils.combineExceptionDataList(resultData, ResultCode.FAIL.getCode(), e.getMessage(), spuKeys, ChannelTypeEnum.TXD);
            }
        });

        return resultData;
    }

    @Override
    public ResultSingleSpuData updateSingleSpuSellStatus(SingleSpuSellStatusRequest request) {
        ResultSingleSpuData resultData = ResultGenerator.genResultSingleSpuData(ResultCode.SUCCESS);

        int channelId = request.getBaseInfo().getChannelId();
        long tenantId = request.getBaseInfo().getTenantId();
        long storeId = request.getParam().getStoreId();

        BaseRequest storeRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Collections.singletonList(storeId));

        SpuKey spuKey = Optional.ofNullable(request.getParam())
                .map(SingleSpuSellStatusDTO::getSpuKey)
                .orElse(new SpuKey(request.getParam().getCustomSpuId(), request.getParam().getChannelSpuId(), Collections.emptyList()));

        try {
            String txdChannelCode = getTxdChannelCode(tenantId);
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SINGLE_SKU_STATUS_UPDATE, storeRequest,
                    TxdConvertUtil.buildSingleSpuStatusRequest(
                            txdBaseService.getChannelPoiCode(storeId, storeRequest),
                            request.getParam(),
                            txdChannelCode));
            return TxdConvertUtil.genUpdateSingleSpuStatusResult(storeId, spuKey, commonResponse);
        }
        catch (ApiException e) {
            log.warn("调用淘鲜达更新上下架商品接口异常, request {}.", request, e);
            return ResultSpuDataUtils.combineExceptionData(resultData, e.getMessage(), spuKey);
        }
        catch (Exception e) {
            log.warn("更新淘鲜达商品上下架系统异常, request {}.", request, e);
            return ResultSpuDataUtils.combineExceptionData(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), spuKey);
        }
    }

    @Override
    public ResultSpuData updateCustomSpuIdByOriginId(UpdateCustomSpuIdByOriginIdRequest request) {
        return null;
    }

    @Override
    public ResultSpuData updateCustomSpuIdByNameAndSpec(UpdateCustomSpuIdByNameAndSpecRequest request) {
        return null;
    }

    @Override
    public GetSpuInfoResponse getSpuInfo(GetSpuInfoRequest request) throws SDKException {
        GetSpuInfoResponse response = new GetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        BaseRequest storeRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SKU_QUERY, storeRequest,
                    TxdConvertUtil.buildQuerySpuRequest(txdBaseService.getChannelPoiCode(request.getStoreId(), storeRequest),
                            Collections.singletonList(request.getCustomSpuId())));
            return TxdConvertUtil.genQuerySpuResult(commonResponse);
        } catch (ApiException e) {
            log.warn("调用淘鲜达查询商品接口异常, request {}.", request, e);
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(TxdConvertUtil.parseTxdErrorCode(e.getErrCode()));
            resultStatus.setMsg(e.getErrMsg());
            return response.setStatus(resultStatus);
        } catch (Exception e) {
            log.warn("查询淘鲜达商品系统异常, request {}.", request, e);
            return response.setStatus(genResult(ResultCode.FAIL));
        }
    }

    @Override
    public GetSpuInfosResponse getSpuInfoList(GetSpuInfosRequest request) {
        GetSpuInfosResponse response = new GetSpuInfosResponse().setStatus(ResultGenerator.genSuccessResult());
        BaseRequest storeRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SKU_QUERY, storeRequest,
                    TxdConvertUtil.buildQuerySpuRequest(txdBaseService.getChannelPoiCode(request.getStoreId(), storeRequest),request.getCustomSpuIds()));
            return TxdConvertUtil.genGetSpuInfosResult((AlibabaWdkSkuQueryResponse) commonResponse);
        } catch (ApiException e) {
            log.warn("调用淘鲜达查询商品接口异常, request {}.", request, e);
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(TxdConvertUtil.parseTxdErrorCode(e.getErrCode()));
            resultStatus.setMsg(e.getErrMsg());
            return response.setStatus(resultStatus);
        } catch (Exception e) {
            log.warn("查询淘鲜达商品系统异常, request {}.", request, e);
            return response.setStatus(genResult(ResultCode.FAIL));
        }
    }

    @Override
    public BatchGetSpuInfoResponse batchGetSpuInfo(BatchGetSpuInfoRequest request) {
        BatchGetSpuInfoResponse response = new BatchGetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        BaseRequest storeRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()));
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.STORE_SKU_QUERY, storeRequest,
                    TxdConvertUtil.buildQueryStoreSpuRequest(txdBaseService.getChannelPoiCode(request.getStoreId(), storeRequest),
                            request.getOffset()));
            AlibabaWdkSkuStoreskuScrollQueryResponse queryResponse = (AlibabaWdkSkuStoreskuScrollQueryResponse) commonResponse;
            if (StringUtils.isBlank(request.getOffset())
                    && Optional.ofNullable(queryResponse).map(r -> Objects.isNull(r.getResult()) ? 0L : r.getResult().getTotal()).orElse(0L) > 0L) {
                // 第一次查询，scrollId为空，只能查询到数量，无法获取model数据，需要再查询一次，为了分页查询逻辑统一，特此用返回的scrollId再查询一次
                commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.STORE_SKU_QUERY, storeRequest,
                        TxdConvertUtil.buildQueryStoreSpuRequest(txdBaseService.getChannelPoiCode(request.getStoreId(), storeRequest),
                                Optional.ofNullable(queryResponse.getResult()).map(AlibabaWdkSkuStoreskuScrollQueryResponse.ApiScrollPageResult::getScrollId).orElse(null)));
                queryResponse = (AlibabaWdkSkuStoreskuScrollQueryResponse) commonResponse;
            }

            if (Objects.nonNull(queryResponse) && queryResponse.isSuccess()) {
                BatchGetSpuInfoResponse batchGetSpuInfoResponse = TxdConvertUtil.genQueryStoreSpuResult(queryResponse);
                List<SpuInfoDTO> channelSpus = batchGetSpuInfoResponse.getSpuInfos();
                if (CollectionUtils.isNotEmpty(channelSpus)) {
                    // 门店查询的spu信息，没有分类等信息，需要使用sku query再查询一次全量商品信息
                    List<String> skuCodes = Fun.map(channelSpus, SpuInfoDTO::getCustomSpuId);
                    TaobaoResponse skuQueryResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SKU_QUERY, storeRequest,
                            TxdConvertUtil.buildQuerySpuRequest(txdBaseService.getChannelPoiCode(request.getStoreId(), storeRequest),
                                    skuCodes));
                    TxdConvertUtil.genBatchQuerySpuResult((AlibabaWdkSkuQueryResponse) skuQueryResponse, batchGetSpuInfoResponse);
                }
                return batchGetSpuInfoResponse;
            }
            else {
                return TxdConvertUtil.genQueryStoreSpuResult(queryResponse);
            }
        } catch (ApiException e) {
            log.warn("调用淘鲜达查询门店商品接口异常, request {}.", request, e);
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(TxdConvertUtil.parseTxdErrorCode(e.getErrCode()));
            resultStatus.setMsg(e.getErrMsg());
            return response.setStatus(resultStatus);
        } catch (Exception e) {
            log.warn("查询淘鲜达门店商品系统异常, request {}.", request, e);
            return response.setStatus(genResult(ResultCode.FAIL));
        }
    }

    @Override
    public GetHeadQuarterSpuInfoResponse batchGetHeadQuarterSpuInfo(BasePageRequest request) {
        return null;
    }

    @Override
    public QueryAuditStatusResponse getAuditStatus(QueryAuditStatusRequest request) {
        return null;
    }

    @Override
    public RecommendChannelCategoryQueryResponse queryRecommendChannelCategory(RecommendChannelCategoryQueryRequest request) {
        return null;
    }

    @Override
    public SkuId2AppFoodCodeResponse queryAppFoodCodeBySkuId(QueryAppFoodCodeBySkuIdRequest request) {
        return null;
    }

    @Override
    public ResultStatus updateCustomSpuStoreCategory(UpdateCustomSpuStoreCategoryRequest request) {
        return null;
    }

    @Override
    public ResultSpuData batchUpdateSpuStoreCategoryCode(BatchUpdateCustomSpuStoreCategoryRequest request) {
        return null;
    }

    @Override
    public SensitiveWordCheckResponse checkSensitiveWords(SensitiveWordCheckRequest request) {
        return null;
    }

    @Override
    public QueryNormAuditDelSpuResponse queryNormAuditDelSpuInfo(QueryNormAuditDelSpuRequest request) {
        return null;
    }

    @Override
    public QueryChannelSpuIdResponse queryChannelSpuId(QueryChannelSpuIdRequest request) {
        return null;
    }

    @Override
    public ProductSequenceBatchSetResponse batchSetSequence(ProductSequenceBatchSetRequest request) {
        return null;
    }

    @Override
    public ResultSpuData updateStoreCategory(SpuStoreCategoryInfoRequest request) {
        // 淘鲜达为更新线下分类
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        List<SpuStoreCategoryCodeDTO> paramList = request.getParamList();
        if (CollectionUtils.isEmpty(paramList)) {
            return resultData;
        }
        long tenantId = request.getBaseInfo().getTenantId();
        if (!MccConfigUtil.getTxdNeedUpdateProductCategoryTenants().contains(tenantId)) {
            return resultData;
        }
        int channelId = request.getBaseInfo().getChannelId();
        long storeId = request.getBaseInfo().getStoreIdList().get(0);
        BaseRequest storeRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Collections.singletonList(storeId));
        String channelPoiId = txdBaseService.getChannelPoiCode(storeId, storeRequest);
        List<SpuKey> spuKeys = Fun.map(paramList, spu -> getSpuKey(storeId, spu));
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SKU_UPDATE, storeRequest, TxdConvertUtil.buildUpdateOfflineCategoryRequest(channelPoiId, paramList));
            ResultSpuData resultSpuData = TxdConvertUtil.genUpdateSpuResult(storeId, spuKeys, commonResponse);
            return resultSpuData;
        } catch (ApiException e) {
            log.warn("调用淘鲜达更新商品线下分类接口异常, request {}.", request, e);
            ProductResultDataUtils.combineExceptionDataList(resultData, TxdConvertUtil.parseTxdErrorCode(e.getErrCode()), e.getMessage(),
                    spuKeys, ChannelTypeEnum.TXD);
        } catch (Exception e) {
            log.warn("调用淘鲜达更新商品线下分类接口异常, request {}.", request, e);
            ProductResultDataUtils.combineExceptionDataList(resultData, ResultCode.FAIL.getCode(), e.getMessage(), spuKeys, ChannelTypeEnum.TXD);
        }
        return resultData;
    }

    @Override
    public ResultSpuData updateSpuUpc(SpuUpcUpdateRequest request) {
        // 淘鲜达修改商品条码（即 barcodes）
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        List<SpuUpcUpdateDto> paramList = request.getParamList();
        if (CollectionUtils.isEmpty(paramList)) {
            return resultData;
        }
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();
        long storeId = request.getBaseInfo().getStoreIdList().get(0);
        BaseRequest storeRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Collections.singletonList(storeId));
        String channelPoiId = txdBaseService.getChannelPoiCode(storeId, storeRequest);
        List<SpuKey> spuKeys = Fun.map(paramList, spu -> new SpuKey().setCustomSpuId(spu.getCustomSpuId()).setStoreId(storeId));
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SKU_UPDATE, storeRequest,
                    TxdConvertUtil.buildUpdateUpcRequest(channelPoiId, paramList));
            ResultSpuData resultSpuData = TxdConvertUtil.genUpdateSpuResult(storeId, spuKeys, commonResponse);
            return resultSpuData;
        } catch (ApiException e) {
            log.warn("调用淘鲜达更新商品的barcodes接口异常, request {}.", request, e);
            ProductResultDataUtils.combineExceptionDataList(resultData, TxdConvertUtil.parseTxdErrorCode(e.getErrCode()), e.getMessage(),
                    spuKeys, ChannelTypeEnum.TXD);
        } catch (Exception e) {
            log.warn("调用淘鲜达更新商品的barcodes未知异常, request {}.", request, e);
            ProductResultDataUtils.combineExceptionDataList(resultData, ResultCode.FAIL.getCode(), e.getMessage(), spuKeys,
                    ChannelTypeEnum.TXD);
        }
        return resultData;
    }

    @Override
    public QueryQualityProblemResponse queryQualityProblem(QueryQualityProblemRequest request) {
        return null;
    }

    @Override
    public ResultStatus submitAppealInfo(SubmitAppealInfoRequest request) {
        return null;
    }

    private String getTxdChannelCode(long tenantId) {
        ChannelConfigDto channelConfig = tenantService.queryTenantChannelConfig(
                tenantId, tenantId, EnhanceChannelType.TXD.getChannelId(), ConfigItemEnum.INTERFACE_MODEL);
        String configContent = Optional.ofNullable(channelConfig)
                .map(ChannelConfigDto::getConfigContent)
                .orElse(null);
        // 单模型传 TXD_CHANNEL_CODE(4)，双模型传 AX_CHANNEL_CODE(31)
        return ConfigItemEnum.INTERFACE_MODEL.isMainConfigEquals(configContent, TenantConfigConstants.V_DUAL_MODEL)
                ? AX_CHANNEL_CODE
                : TXD_CHANNEL_CODE;
    }
}
