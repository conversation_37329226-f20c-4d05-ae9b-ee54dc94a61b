package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ChannelConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.YzSkuPriceAndStockUpdateInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.CustomChannelSkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.CustomSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.ChannelStoreSkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ProductChannelUnifyErrorEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductChannelErrorMappingUtils;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.SaleStatusEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemAdd;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemCommonSearch;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemSecondgroupQueryByupperid;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemUpdateBranchSku;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.*;
import com.youzan.cloud.open.sdk.gen.v3_0_0.api.YouzanItemcategoriesTagDelete;
import com.youzan.cloud.open.sdk.gen.v3_0_0.api.YouzanItemcategoriesTagUpdate;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanItemcategoriesTagDeleteParams;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanItemcategoriesTagDeleteResult;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanItemcategoriesTagUpdateParams;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanItemcategoriesTagUpdateResult;
import com.youzan.cloud.open.sdk.gen.v3_0_1.api.YouzanItemDelete;
import com.youzan.cloud.open.sdk.gen.v3_0_1.api.YouzanItemUpdateDelisting;
import com.youzan.cloud.open.sdk.gen.v3_0_1.api.YouzanItemUpdateListing;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanItemDeleteParams;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanItemUpdateDelistingParams;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanItemUpdateDelistingResult;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanItemUpdateListingParams;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanItemUpdateListingResult;
import com.youzan.cloud.open.sdk.gen.v4_0_0.api.YouzanItemUpdate;
import com.youzan.cloud.open.sdk.gen.v4_0_0.api.YouzanItemcategoriesTagAdd;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanItemUpdateParams;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanItemcategoriesTagAddParams;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanItemcategoriesTagAddResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 作者：guohuqi
 * 时间：2022/12/2 5:06 PM
 * 功能：
 **/
public class YzConverterUtil {
    public static final String YZ_DEFAULT_SPEC_NAME = "规格";

    public static MerchantSpuResult buildMerchantSpuResult(MerchantSpuDTO spuDTO, Long itemId,
                                                     YouzanItemDetailGetResult.YouzanItemDetailGetResultData spuDetail) {
        MerchantSpuResult spuResult = new MerchantSpuResult();
        spuResult.setResultCode(ResultCode.SUCCESS.getCode());
        spuResult.setResultMsg(ResultCode.SUCCESS.getMsg());
        spuResult.setCustomSpuId(spuDTO.getCustomSpuId());
        spuResult.setChannelSpuId(String.valueOf(itemId));
        List<CustomChannelSkuKey> customChannelSkuKeys = ConverterUtils
                .convertList(spuDetail.getSkuList(),
                        sku -> new CustomChannelSkuKey(sku.getSkuNo(), String.valueOf(sku.getSkuId())));
        spuResult.setSkuResultList(customChannelSkuKeys);
        return spuResult;
    }

    public static YouzanItemAdd createRequestToYzAPI(MerchantSpuDTO spuDTO) {
        YouzanItemAddParams.YouzanItemAddParamsParam itemAddParam = new YouzanItemAddParams.YouzanItemAddParamsParam();
        //百川对接属性
        //渠道编码 customSpuId
        itemAddParam.setItemNo(spuDTO.getCustomSpuId());
        //商品名称
        itemAddParam.setTitle(spuDTO.getName());
        //商品图片
        itemAddParam.setImageIds(spuDTO.getImages()
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));
        //商品分组
        itemAddParam.setTagIds(spuDTO.getChannelStoreCategoryCode()
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));

        //商品卖点
        itemAddParam.setSellPoint(spuDTO.getSellPoint());
        //商品描述
        itemAddParam.setDesc(spuDTO.getDescription());

        //规格信息
        List<YouzanItemAddParams.YouzanItemAddParamsSkulist> itemAddParamsSkulists = spuDTO.getSkuList()
                .stream()
                .map(skuDTO -> {
                    YouzanItemAddParams.YouzanItemAddParamsSkulist skuParam = new YouzanItemAddParams.YouzanItemAddParamsSkulist();
                    skuParam.setSkuNo(skuDTO.getCustomSkuId());
                    YouzanItemAddParams.YouzanItemAddParamsSkuprops skuprops = new YouzanItemAddParams.YouzanItemAddParamsSkuprops();
                    skuprops.setPropName(YZ_DEFAULT_SPEC_NAME);
                    skuprops.setPropValueName(skuDTO.getSpecValueName());
                    skuParam.setSkuProps(Lists.newArrayList(skuprops));
                    skuParam.setWeight(Long.valueOf(skuDTO.getWeight()));
                    skuParam.setBarcode(skuDTO.getUpc());
                    skuParam.setPrice(skuDTO.getSuggestSalePrice());
                    return skuParam;
                }).collect(Collectors.toList());

        itemAddParam.setSkuList(itemAddParamsSkulists);

        //有赞渠道默认属性
        //商品类型-实物商品
        itemAddParam.setItemType(0);
        //是否上架商品  0-不上架，管理端在仓库中。
        itemAddParam.setIsDisplay(0);
        //售后设置 统一设置支持退款
        YouzanItemAddParams.YouzanItemAddParamsPostsaleparam postsaleparam =
                new YouzanItemAddParams.YouzanItemAddParamsPostsaleparam();
        YouzanItemAddParams.YouzanItemAddParamsRefundparam refundparam = new YouzanItemAddParams.YouzanItemAddParamsRefundparam();
        refundparam.setSupportRefund(true);
        postsaleparam.setRefundParam(refundparam);
        //itemAddParam.setPostSaleParam(postsaleparam);
        //配送设置 统一设置支持同城
        YouzanItemAddParams.YouzanItemAddParamsItemdeliveryparam itemdeliveryparam = new YouzanItemAddParams.YouzanItemAddParamsItemdeliveryparam();
        YouzanItemAddParams.YouzanItemAddParamsDeliverysetting deliverysetting = new YouzanItemAddParams.YouzanItemAddParamsDeliverysetting();
        deliverysetting.setCityDelivery(true);
        itemdeliveryparam.setDeliverySetting(deliverysetting);
        itemAddParam.setItemDeliveryParam(itemdeliveryparam);

        YouzanItemAddParams itemAddParams = new YouzanItemAddParams();
        itemAddParams.setParam(itemAddParam);

        YouzanItemAdd youzanItemAdd = new YouzanItemAdd();
        youzanItemAdd.setAPIParams(itemAddParams);
        return youzanItemAdd;
    }

    /**
     * 有赞支持单规格创建商品
     *
     * @param spuDTO
     * @return
     */
    public static YouzanItemAdd createRequestToYzAPISingleSpec(MerchantSpuDTO spuDTO) {
        YouzanItemAddParams.YouzanItemAddParamsParam itemAddParam = new YouzanItemAddParams.YouzanItemAddParamsParam();

        if (CollectionUtils.isEmpty(spuDTO.getSkuList())) {
            throw new BizException("创建商品规格为空");
        }

        // 编码条码
        itemAddParam.setBarcode(spuDTO.getSkuList().get(0).getUpc());

        // 渠道编码 customSpuId
        itemAddParam.setItemNo(spuDTO.getCustomSpuId());
        // 配送设置 统一设置支持同城&自提
        YouzanItemAddParams.YouzanItemAddParamsItemdeliveryparam itemDeliveryParam = new YouzanItemAddParams.YouzanItemAddParamsItemdeliveryparam();
        YouzanItemAddParams.YouzanItemAddParamsDeliverysetting deliverySetting = new YouzanItemAddParams.YouzanItemAddParamsDeliverysetting();
        deliverySetting.setCityDelivery(true);
        deliverySetting.setSelfPick(true);
        itemDeliveryParam.setDeliverySetting(deliverySetting);

        // 设置同城配送费用续重是否收费，true-收取，false-不收取
        Integer weight = spuDTO.getSkuList().get(0).getWeight();
        if (weight != null && weight > 0) {
            deliverySetting.setHeavyContinued(true);
        }

        itemAddParam.setItemDeliveryParam(itemDeliveryParam);


        // 商品类型-实物商品
        itemAddParam.setItemType(ChannelConstant.YZ_REAL_GOODS);

        // 商品名称
        itemAddParam.setTitle(spuDTO.getName());

        // 售价填充
        YouzanItemAddParams.YouzanItemAddParamsItempriceparam itemPriceParam = new YouzanItemAddParams.YouzanItemAddParamsItempriceparam();
        itemPriceParam.setPrice(spuDTO.getSkuList().get(0).getSuggestSalePrice());
        itemAddParam.setItemPriceParam(itemPriceParam);

        // 库存填充
        itemAddParam.setQuantity(ChannelConstant.YZ_DEFAULT_STOCK);

        // 商品描述
        itemAddParam.setDesc(spuDTO.getDescription());

        // 商品分组
        itemAddParam.setTagIds(spuDTO.getChannelStoreCategoryCode()
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));

        // 商品图片
        itemAddParam.setImageIds(spuDTO.getImages()
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));

        // 商品重量
        if (spuDTO.getSkuList().get(0).getWeight() != null) {
            itemAddParam.setItemWeight(spuDTO.getSkuList().get(0).getWeight().longValue());
        }

        // 商品卖点
        itemAddParam.setSellPoint(spuDTO.getSellPoint());

        // 是否上架商品  0-不上架，管理端在仓库中。
        itemAddParam.setIsDisplay(ChannelConstant.YZ_NOT_ON_THE_SHELF);

        // 售后设置 统一设置支持退款
        YouzanItemAddParams.YouzanItemAddParamsPostsaleparam postsaleparam =
                new YouzanItemAddParams.YouzanItemAddParamsPostsaleparam();
        YouzanItemAddParams.YouzanItemAddParamsRefundparam refundparam = new YouzanItemAddParams.YouzanItemAddParamsRefundparam();
        refundparam.setSupportRefund(true);
        postsaleparam.setRefundParam(refundparam);


        YouzanItemAddParams itemAddParams = new YouzanItemAddParams();
        itemAddParams.setParam(itemAddParam);

        YouzanItemAdd youzanItemAdd = new YouzanItemAdd();
        youzanItemAdd.setAPIParams(itemAddParams);
        return youzanItemAdd;
    }

    public static YouzanItemUpdate updateRequestToYzAPI(MerchantSpuDTO spuDTO) {
        Preconditions.checkArgument(StringUtils.isNotBlank(spuDTO.getChannelSpuId()), "商品的渠道标识不能为空");
        Preconditions.checkArgument(StringUtils.isNumeric(spuDTO.getChannelSpuId()), "商品的渠道标识必须为Long型");

        YouzanItemUpdateParams.YouzanItemUpdateParamsParam updateParam = new YouzanItemUpdateParams.YouzanItemUpdateParamsParam();
        //百川对接属性
        //渠道编码 customSpuId
        updateParam.setItemNo(spuDTO.getCustomSpuId());
        updateParam.setItemId(Long.parseLong(spuDTO.getChannelSpuId()));

        //商品名称
        updateParam.setTitle(spuDTO.getName());
        //商品图片
        updateParam.setImageIds(spuDTO.getImages()
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));
        //商品分组
        updateParam.setTagIds(spuDTO.getChannelStoreCategoryCode()
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));

        //商品卖点
        updateParam.setSellPoint(spuDTO.getSellPoint());
        //商品描述
        updateParam.setDesc(spuDTO.getDescription());

        //规格信息
        List<YouzanItemUpdateParams.YouzanItemUpdateParamsSkulist> itemAddParamsSkulists = spuDTO.getSkuList()
                .stream()
                .map(skuDTO -> {
                    YouzanItemUpdateParams.YouzanItemUpdateParamsSkulist skuParam = new YouzanItemUpdateParams.YouzanItemUpdateParamsSkulist();
                    skuParam.setSkuNo(skuDTO.getCustomSkuId());
                    skuParam.setSkuId(StringUtils.isNotBlank(skuDTO.getChannelSkuId()) ? Long.valueOf(skuDTO.getChannelSkuId()) : null);
                    YouzanItemUpdateParams.YouzanItemUpdateParamsSkuprops skuprops = new YouzanItemUpdateParams.YouzanItemUpdateParamsSkuprops();
                    skuprops.setPropName(YZ_DEFAULT_SPEC_NAME);
                    skuprops.setPropValueName(skuDTO.getSpecValueName());
                    skuParam.setSkuProps(Lists.newArrayList(skuprops));
                    skuParam.setWeight(Long.valueOf(skuDTO.getWeight()));
                    skuParam.setBarcode(skuDTO.getUpc());
                    skuParam.setPrice(skuDTO.getSuggestSalePrice());
                    return skuParam;
                }).collect(Collectors.toList());

        updateParam.setSkuList(itemAddParamsSkulists);

        //有赞渠道默认属性
        //商品类型-实物商品
        updateParam.setItemType(0);
        //是否上架商品  0-不上架，管理端在仓库中。TODO 此处如何考虑上下架信息的设置
        updateParam.setIsDisplay(0);
        //售后设置 统一设置支持退款
//        YouzanItemUpdateParams.YouzanItemUpdateParamsPostsaleparam postsaleparam =
//                new YouzanItemUpdateParams.YouzanItemUpdateParamsPostsaleparam();
//        YouzanItemUpdateParams.YouzanItemUpdateParamsRefundparam refundparam = new YouzanItemUpdateParams.YouzanItemUpdateParamsRefundparam();
//        refundparam.setSupportRefund(true);
//        postsaleparam.setRefundParam(refundparam);
//        updateParam.setPostSaleParam(postsaleparam);

        //配送设置 统一设置支持同城
        YouzanItemUpdateParams.YouzanItemUpdateParamsItemdeliveryparam itemdeliveryparam = new YouzanItemUpdateParams.YouzanItemUpdateParamsItemdeliveryparam();
        YouzanItemUpdateParams.YouzanItemUpdateParamsDeliverysetting deliverysetting = new YouzanItemUpdateParams.YouzanItemUpdateParamsDeliverysetting();
        deliverysetting.setCityDelivery(true);
        itemdeliveryparam.setDeliverySetting(deliverysetting);
        updateParam.setItemDeliveryParam(itemdeliveryparam);

        YouzanItemUpdateParams updateParams = new YouzanItemUpdateParams();
        updateParams.setParam(updateParam);

        YouzanItemUpdate youzanItemUpdate = new YouzanItemUpdate();
        youzanItemUpdate.setAPIParams(updateParams);
        return youzanItemUpdate;
    }


    public static YouzanItemIncrementalUpdateParams
            .YouzanItemIncrementalUpdateParamsParam updateRequestToYzAPINoSku(MerchantSpuDTO spuDTO) {
        YouzanItemIncrementalUpdateParams.YouzanItemIncrementalUpdateParamsParam params = new YouzanItemIncrementalUpdateParams.YouzanItemIncrementalUpdateParamsParam();
        if (CollectionUtils.isEmpty(spuDTO.getSkuList())) {
            throw new BizException("创建商品规格为空");
        }

        // 渠道id
        params.setItemId(Long.parseLong(spuDTO.getChannelSpuId()));
        // 商品名称
        params.setTitle(spuDTO.getName());
        // 商品图片
        params.setImageIds(spuDTO.getImages()
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));
        // 商品描述
        params.setDesc(spuDTO.getDescription());
        // 商品分组
        params.setTagIds(spuDTO.getChannelStoreCategoryCode()
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));
        // 商品卖点
        params.setSellPoint(spuDTO.getSellPoint());

        // 默认库存
        params.setQuantity(ChannelConstant.YZ_DEFAULT_STOCK);
        // 价格
        if (spuDTO.getSkuList().get(0).getSuggestSalePrice() != null) {
            YouzanItemIncrementalUpdateParams.YouzanItemIncrementalUpdateParamsItempriceparam itemPriceParam =
                    new YouzanItemIncrementalUpdateParams.YouzanItemIncrementalUpdateParamsItempriceparam();
            itemPriceParam.setPrice(spuDTO.getSkuList().get(0).getSuggestSalePrice());
            params.setItemPriceParam(itemPriceParam);
        }

        // 自定义编码
        params.setItemNo(spuDTO.getCustomSpuId());

        // 重量修改
        if (spuDTO.getSkuList().get(0).getWeight() != null) {
            params.setItemWeight(Long.valueOf(spuDTO.getSkuList().get(0).getWeight()));
        }


        return params;
    }

    public static YouzanItemDelete deleteRequestToYzAPI(CustomSpuDeleteDTO deleteDTO) {
        Preconditions.checkArgument(StringUtils.isNotBlank(deleteDTO.getChannelSpuId()), "商品的渠道标识不能为空");
        Preconditions.checkArgument(StringUtils.isNumeric(deleteDTO.getChannelSpuId()), "商品的渠道标识必须为Long型");

        YouzanItemDeleteParams deleteParams = new YouzanItemDeleteParams();
        deleteParams.setItemId(Long.parseLong(deleteDTO.getChannelSpuId()));

        YouzanItemDelete youzanItemDelete = new YouzanItemDelete();
        youzanItemDelete.setAPIParams(deleteParams);

        return youzanItemDelete;
    }

    public static YouzanItemUpdateListing onlineSpuInfoToYzAPI(Long channelPoiId, SpuKey spuKey) {
        Preconditions.checkArgument(StringUtils.isNotBlank(spuKey.getChannelSpuId()), "门店商品的渠道标识不能为空");

        List<YouzanItemUpdateListingParams.YouzanItemUpdateListingParamsNodekdtitems> spuNodeList = Lists.newArrayList();
        YouzanItemUpdateListingParams.YouzanItemUpdateListingParamsNodekdtitems nodekdtitems
                = new YouzanItemUpdateListingParams.YouzanItemUpdateListingParamsNodekdtitems();
        nodekdtitems.setNodeKdtId(channelPoiId);
        nodekdtitems.setNodeItemId(Long.parseLong(spuKey.getChannelSpuId()));
        spuNodeList.add(nodekdtitems);

        YouzanItemUpdateListingParams youzanItemUpdateListingParams = new YouzanItemUpdateListingParams();
        youzanItemUpdateListingParams.setNodeKdtItems(spuNodeList);

        YouzanItemUpdateListing youzanItemUpdateListing = new YouzanItemUpdateListing();
        youzanItemUpdateListing.setAPIParams(youzanItemUpdateListingParams);

        return youzanItemUpdateListing;
    }

    public static ResultSpuData converterYouzanItemUpdateListingResult(Long storeId, SpuKey spukey, YouzanItemUpdateListingResult result) {

        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);


        if (!result.getSuccess() || !result.getData().getIsSuccess()) {
            resultSpuData.getStatus().setCode(ResultCode.FAIL.getCode());
            resultSpuData.getStatus().setMsg(result.getMessage());

            ResultErrorSpu spuResult = new ResultErrorSpu();
            spuResult.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
            spuResult.setSpuInfo(spukey);
            spuResult.setStoreId(storeId);
            spuResult.setErrorCode(result.getCode());
            spuResult.setErrorMsg(String.format("门店商品上架失败, %s", result.getMessage()));

            resultSpuData.getErrorData().add(spuResult);
        } else {
            ResultSuccessSpu successSpu = new ResultSuccessSpu();
            successSpu.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
            successSpu.setSpuInfo(spukey);
            successSpu.setStoreId(storeId);
            successSpu.setChannelResultInfo(String.valueOf(spukey.getChannelSpuId()));
            resultSpuData.getSucData().add(successSpu);
        }

        // 填充统一渠道错误码
        if (CollectionUtils.isNotEmpty(resultSpuData.getErrorData())) {
            resultSpuData.getErrorData().stream().forEach(errorData -> {
                ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN, String.valueOf(errorData.getErrorCode()), errorData.getErrorMsg());
                errorData.setChannelUnifyError(unifyErrorEnum);
            });
        }

        return resultSpuData;
    }

    public static ResultSpuData converterYouzanItemUpdateBranchDisplayResult(Long storeId, SpuKey spukey, YouzanItemUpdateBranchDisplayResult result) {

        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);


        if (!result.getSuccess() || (result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getFailedList()))) {
            resultSpuData.getStatus().setCode(ResultCode.FAIL.getCode());
            resultSpuData.getStatus().setMsg(result.getMessage());

            ResultErrorSpu spuResult = new ResultErrorSpu();
            spuResult.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
            spuResult.setSpuInfo(spukey);
            spuResult.setStoreId(storeId);
            spuResult.setErrorCode(result.getCode());
            spuResult.setErrorMsg(String.format("门店商品上架失败, %s", result.getMessage()));

            resultSpuData.getErrorData().add(spuResult);
        } else {
            ResultSuccessSpu successSpu = new ResultSuccessSpu();
            successSpu.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
            successSpu.setSpuInfo(spukey);
            successSpu.setStoreId(storeId);
            successSpu.setChannelResultInfo(String.valueOf(spukey.getChannelSpuId()));
            resultSpuData.getSucData().add(successSpu);
        }

        // 填充统一渠道错误码
        if (CollectionUtils.isNotEmpty(resultSpuData.getErrorData())) {
            resultSpuData.getErrorData().stream().forEach(errorData -> {
                ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN, String.valueOf(errorData.getErrorCode()), errorData.getErrorMsg());
                errorData.setChannelUnifyError(unifyErrorEnum);
            });
        }

        return resultSpuData;
    }
    public static YouzanItemUpdateDelisting offlineSpuInfoToYzAPI(Long channelPoiId, SpuKey spuKey) {
        Preconditions.checkArgument(StringUtils.isNotBlank(spuKey.getChannelSpuId()), "门店商品的渠道标识不能为空");

        List<YouzanItemUpdateDelistingParams.YouzanItemUpdateDelistingParamsNodekdtitems> spuNodeList = Lists.newArrayList();
        YouzanItemUpdateDelistingParams.YouzanItemUpdateDelistingParamsNodekdtitems nodekdtitems
                = new YouzanItemUpdateDelistingParams.YouzanItemUpdateDelistingParamsNodekdtitems();
        nodekdtitems.setNodeKdtId(channelPoiId);
        nodekdtitems.setNodeItemId(Long.parseLong(spuKey.getChannelSpuId()));
        spuNodeList.add(nodekdtitems);

        YouzanItemUpdateDelistingParams youzanItemUpdateDelistingParams = new YouzanItemUpdateDelistingParams();
        youzanItemUpdateDelistingParams.setNodeKdtItems(spuNodeList);

        YouzanItemUpdateDelisting youzanItemUpdateDelisting = new YouzanItemUpdateDelisting();
        youzanItemUpdateDelisting.setAPIParams(youzanItemUpdateDelistingParams);

        return youzanItemUpdateDelisting;
    }

    public static ResultSpuData converterYouzanItemUpdateDelistingResult(Long storeId, SpuKey spukey, boolean deleteOpt,
                                                                         YouzanItemUpdateDelistingResult result) {

        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        boolean isIgnoreCode = deleteOpt && MccConfigUtil.getYzNotExistSpuCodes().contains(result.getCode());
        if (!isIgnoreCode && (!result.getSuccess() || !result.getData().getIsSuccess())) {
            resultSpuData.getStatus().setCode(ResultCode.FAIL.getCode());
            resultSpuData.getStatus().setMsg(ResultCode.FAIL.getMsg());

            ResultErrorSpu spuResult = new ResultErrorSpu();
            spuResult.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
            spuResult.setSpuInfo(spukey);
            spuResult.setStoreId(storeId);
            spuResult.setErrorCode(result.getCode());
            spuResult.setErrorMsg(String.format("门店商品下架失败, %s", result.getMessage()));

            resultSpuData.getErrorData().add(spuResult);
        } else {
            ResultSuccessSpu successSpu = new ResultSuccessSpu();
            successSpu.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
            successSpu.setSpuInfo(spukey);
            successSpu.setStoreId(storeId);
            resultSpuData.getSucData().add(successSpu);
        }


        // 填充统一渠道错误码
        if (CollectionUtils.isNotEmpty(resultSpuData.getErrorData())) {
            resultSpuData.getErrorData().stream().forEach(errorData -> {
                ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN, String.valueOf(errorData.getErrorCode()), errorData.getErrorMsg());
                errorData.setChannelUnifyError(unifyErrorEnum);
            });
        }

        return resultSpuData;
    }

    public static void converterYouzanItemUpdateBranchSkuResult(ResultData rawResultData,
                                                                List<ChannelStoreSkuKey> storeSkuKeyList,
                                                                YouzanItemUpdateBranchSkuResult skuResult,
                                                                String errMsg,
                                                                Integer errorCode,
                                                                Boolean isMultiSpec) {
        if (Objects.nonNull(skuResult) && skuResult.getSuccess()) {
            if (CollectionUtils.isEmpty(skuResult.getData())) {
                storeSkuKeyList.forEach(skuDTO ->  rawResultData.getSucData()
                        .add(new ResultSuccessSku().setStoreId(skuDTO.getStoreId()).setSkuId(skuDTO.getSkuId())));
            } else {
                convertYouzanItemUpdateBranchSkuResultData(rawResultData, storeSkuKeyList, skuResult.getData(), isMultiSpec);
            }
        } else if (Objects.nonNull(skuResult) && !skuResult.getSuccess()) {
            if (CollectionUtils.isEmpty(skuResult.getData())) {
                storeSkuKeyList.forEach(skuDTO ->{
                    rawResultData.getErrorData().add(new ResultErrorSku().setStoreId(skuDTO.getStoreId()).setSkuId(skuDTO.getSkuId())
                            .setErrorCode(skuResult.getCode()).setErrorMsg(skuResult.getMessage()));
                });
            } else {
                convertYouzanItemUpdateBranchSkuResultData(rawResultData, storeSkuKeyList, skuResult.getData(), isMultiSpec);
            }
        } else {
            storeSkuKeyList.forEach(skuDTO ->{
                int errCode = Objects.isNull(errorCode) ? ResultCode.FAIL.getCode() : errorCode;
                rawResultData.getErrorData().add(new ResultErrorSku().setStoreId(skuDTO.getStoreId()).setSkuId(skuDTO.getSkuId())
                        .setErrorCode(errCode).setErrorMsg(errMsg));
            });
        }

        // 填充统一渠道错误码
        if (CollectionUtils.isNotEmpty(rawResultData.getErrorData())) {
            rawResultData.getErrorData().stream().forEach(errorData -> {
                ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN, String.valueOf(errorData.getErrorCode()), errorData.getErrorMsg());
                errorData.setChannelUnifyError(unifyErrorEnum);
            });
        }
    }

    private static void convertYouzanItemUpdateBranchSkuResultData(ResultData rawResultData,
                                                                   List<ChannelStoreSkuKey> storeSkuKeyList,
                                                                   List<YouzanItemUpdateBranchSkuResult.YouzanItemUpdateBranchSkuResultData> data,
                                                                   boolean isMultiType) {
        // 单规格单独处理
        if (!isMultiType) {
            convertYouzanItemUpdateBranchSkuResultDataForNoSku(rawResultData, storeSkuKeyList, data);
            return;
        }

        Map<Long, ChannelStoreSkuKey> skuMap = storeSkuKeyList.stream()
                .collect(Collectors.toMap(sku -> Long.parseLong(sku.getChannelSkuId()), v -> v, (s1, s2) -> s1));
        data.forEach(currSkuResult -> {
            ChannelStoreSkuKey skuKey = skuMap.get(currSkuResult.getSkuId());
            if (Objects.nonNull(skuKey) && currSkuResult.getIsSuccess()) {
                rawResultData.getSucData().add(new ResultSuccessSku().setStoreId(skuKey.getStoreId()).setSkuId(skuKey.getSkuId()));
            } else if (Objects.nonNull(skuKey) && !currSkuResult.getIsSuccess()) {
                rawResultData.getErrorData().add(new ResultErrorSku().setStoreId(skuKey.getStoreId()).setSkuId(skuKey.getSkuId())
                        .setErrorCode(currSkuResult.getCode()).setErrorMsg(currSkuResult.getMessage()));
            } else {
                rawResultData.getErrorData().add(new ResultErrorSku().setStoreId(currSkuResult.getNodeKdtId()).setSkuId(String.valueOf(currSkuResult.getSkuId()))
                        .setErrorCode(currSkuResult.getCode()).setErrorMsg(currSkuResult.getMessage()));
            }
        });
    }

    private static void convertYouzanItemUpdateBranchSkuResultDataForNoSku(ResultData rawResultData,
                                                                   List<ChannelStoreSkuKey> storeSkuKeyList,
                                                                   List<YouzanItemUpdateBranchSkuResult.YouzanItemUpdateBranchSkuResultData> data) {
        // 单规格一个门店只有一个sku
        if (CollectionUtils.isEmpty(storeSkuKeyList)) {
            return;
        }
        ChannelStoreSkuKey skuKey = storeSkuKeyList.get(0);
        data.forEach(currSkuResult -> {
             if (Objects.nonNull(skuKey) && currSkuResult.getIsSuccess()) {
                rawResultData.getSucData().add(
                        new ResultSuccessSku()
                        .setStoreId(skuKey.getStoreId())
                        .setSkuId(skuKey.getSkuId()));
            }
            else if (Objects.nonNull(skuKey) && !currSkuResult.getIsSuccess()) {
                rawResultData.getErrorData().add(
                        new ResultErrorSku()
                        .setStoreId(skuKey.getStoreId())
                        .setSkuId(skuKey.getSkuId())
                        .setErrorCode(currSkuResult.getCode())
                        .setErrorMsg(currSkuResult.getMessage()));
             }
             else {
                 rawResultData.getErrorData().add(
                         new ResultErrorSku()
                         .setStoreId(currSkuResult.getNodeKdtId())
                         .setSkuId(String.valueOf(currSkuResult.getSkuId()))
                         .setErrorCode(currSkuResult.getCode())
                         .setErrorMsg(currSkuResult.getMessage()));

             }
        });
    }

    public static SpuKey getSpuKey(SpuInfoDTO data) {
        SpuKey spuKey = new SpuKey().setCustomSpuId(data.getCustomSpuId()).setChannelSpuId(data.getChannelSpuId());
        List<SkuKey> skuKeys = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(data.getSkus())) {
            data.getSkus().forEach(skuInSpuInfoDTO -> {
                SkuKey skuKey = new SkuKey().setCustomSkuId(skuInSpuInfoDTO.getCustomSkuId());
                skuKeys.add(skuKey);
            });
        }
        spuKey.setSkus(skuKeys);
        return spuKey;
    }

    public static void setChannelSpuInfo(SpuKey spuKey, YouzanItemDetailGetResult.YouzanItemDetailGetResultData detail) {
        Map<String, Long> skuIdMap = Optional.ofNullable(detail.getSkuList()).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(YouzanItemDetailGetResult.YouzanItemDetailGetResultSkulist::getSkuNo,
                        YouzanItemDetailGetResult.YouzanItemDetailGetResultSkulist::getSkuId, (s1, s2) -> s1));
        spuKey.setChannelSpuId(detail.getItemId().toString());
        if (CollectionUtils.isNotEmpty(spuKey.getSkus())) {
            spuKey.getSkus().forEach(skuKey -> {
                skuKey.setChannelSkuId(skuIdMap.getOrDefault(skuKey.getCustomSkuId(), 0L).toString());
            });
        }
    }

    public static ResultSpuData convertAndMerge2SpuResult(ResultSpuData spuData, YouzanItemUpdateBranchSkuResult result,
                                                          SpuKey spuKey, Long storeId) {
        if (Objects.isNull(result)) {
            spuData.addToErrorData(
                    new ResultErrorSpu().setChannelId(ChannelTypeEnum.YOU_ZAN.getCode())
                            .setStoreId(storeId)
                            .setSpuInfo(spuKey)
                            .setErrorCode(ResultCode.FAIL.getCode())
                            .setErrorMsg("调用异常"));
        } else if (!result.getSuccess() && CollectionUtils.isEmpty(result.getData())) {
            spuData.addToErrorData(
                    new ResultErrorSpu().setChannelId(ChannelTypeEnum.YOU_ZAN.getCode())
                            .setStoreId(storeId)
                            .setSpuInfo(spuKey)
                            .setErrorCode(ResultCode.FAIL.getCode())
                            .setErrorMsg(result.getMessage()));


        } else if (result.getSuccess() && CollectionUtils.isEmpty(result.getData())) {
            spuData.addToSucData(new ResultSuccessSpu().setSpuInfo(spuKey).setChannelId(ChannelTypeEnum.YOU_ZAN.getCode()).setStoreId(storeId));
        } else if (CollectionUtils.isNotEmpty(result.getData())) {
            StringBuilder sb = new StringBuilder();
            result.getData().forEach(resultData -> {
                if (!resultData.getIsSuccess()) {
                    if (resultData.getSkuId() != null) {
                        sb.append("skuId:").append(resultData.getSkuId()).append(" | ");
                    }
                    sb.append(resultData.getMessage()).append(",");
                }
            });

            if (StringUtils.isNotBlank(sb.toString())) {
                spuData.addToErrorData(new ResultErrorSpu().setChannelId(ChannelTypeEnum.YOU_ZAN.getCode())
                        .setSpuInfo(spuKey)
                        .setStoreId(storeId)
                        .setErrorCode(ResultCode.FAIL.getCode())
                        .setErrorMsg(sb.toString()));
            }
        } else {
            // 不做逻辑处理
        }

        // 填充统一渠道错误码
        if (CollectionUtils.isNotEmpty(spuData.getErrorData())) {
            spuData.getErrorData().stream().forEach(errorData -> {
                ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils
                        .getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN, String.valueOf(errorData.getErrorCode()), errorData.getErrorMsg());
                errorData.setChannelUnifyError(unifyErrorEnum);
            });
        }


        return spuData;
    }

    public static ResultErrorSpu buildDefaultErrorSpuInfo(Long storeId, SpuKey spuKey, String errMsg) {
        return buildErrorSpuInfo(storeId, spuKey, ResultCode.FAIL.getCode(), errMsg);
    }

    public static ResultErrorSpu buildErrorSpuInfo(Long storeId, SpuKey spuKey, int errorCode, String errMsg) {
        ResultErrorSpu errorSpu = new ResultErrorSpu();
        errorSpu.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
        errorSpu.setSpuInfo(spuKey);
        errorSpu.setStoreId(storeId);
        errorSpu.setErrorCode(errorCode);
        errorSpu.setErrorMsg(errMsg);
        ProductChannelUnifyErrorEnum unifyErrorEnum = ProductChannelErrorMappingUtils
                .getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN, String.valueOf(errorCode), errMsg);
        errorSpu.setChannelUnifyError(unifyErrorEnum);
        return errorSpu;
    }

    public static ResultSuccessSpu buildResultSuccessSpu(Long storeId, SpuKey spuKey) {
        ResultSuccessSpu successSpu = new ResultSuccessSpu();
        successSpu.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
        successSpu.setSpuInfo(spuKey);
        successSpu.setStoreId(storeId);
        successSpu.setChannelResultInfo(String.valueOf(spuKey.getChannelSpuId()));
        return successSpu;
    }

    public static YouzanItemcategoriesTagAdd converterToYouzanItemcategoriesTagAdd(CategoryInfoDTO categoryInfoDTO) {

        YouzanItemcategoriesTagAddParams.YouzanItemcategoriesTagAddParamsAddgroupparam categoryParam =
                new YouzanItemcategoriesTagAddParams.YouzanItemcategoriesTagAddParamsAddgroupparam();
        categoryParam.setName(categoryInfoDTO.getName());
        if (StringUtils.isNotBlank(categoryInfoDTO.getChannelParentCode())) {
            Preconditions.checkArgument(StringUtils.isNumeric(categoryInfoDTO.getChannelParentCode()), "分类ID必须为数字");
            categoryParam.setUpperid(Long.parseLong(categoryInfoDTO.getChannelParentCode()));
        }

        YouzanItemcategoriesTagAddParams addParams = new YouzanItemcategoriesTagAddParams();
        addParams.setAddGroupParam(categoryParam);

        YouzanItemcategoriesTagAdd categoriesTagAdd = new YouzanItemcategoriesTagAdd();
        categoriesTagAdd.setAPIParams(addParams);

        return categoriesTagAdd;
    }

    public static CategoryPoiResult converterYouzanItemcategoriesTagAddResult(YouzanItemcategoriesTagAddResult tagAddResult,
                                                                             CategoryInfoDTO categoryInfoDTO, String msg) {
        CategoryPoiResult categoryPoiResult = new CategoryPoiResult();
        categoryPoiResult.setStoreId(categoryInfoDTO.getStoreId());
        categoryPoiResult.setCode(categoryInfoDTO.getCode());
        if (Objects.isNull(tagAddResult)) {
            categoryPoiResult.setResultCode(ResultCode.FAIL.getCode());
            categoryPoiResult.setMsg(StringUtils.isNotBlank(msg) ? msg : "创建分类信息未知异常");
        } else if (tagAddResult.getSuccess()) {
            if (Objects.nonNull(tagAddResult.getData())) {
                categoryPoiResult.setResultCode(ResultCode.SUCCESS.getCode());
                categoryPoiResult.setChannelCode(tagAddResult.getData().getId().toString());
            } else {
                categoryPoiResult.setResultCode(ResultCode.FAIL.getCode());
                categoryPoiResult.setMsg("未返回分类创建结果信息");
            }
        } else {
            categoryPoiResult.setResultCode(ResultCode.FAIL.getCode());
            categoryPoiResult.setMsg(tagAddResult.getMessage());
        }
        
        if (!Objects.equals(categoryPoiResult.getResultCode(), ResultCode.SUCCESS.getCode()) && Objects.nonNull(tagAddResult)) {
            //填充渠道统一错误
            ProductChannelUnifyErrorEnum resErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN, String.valueOf(tagAddResult.getCode()), tagAddResult.getMessage());
            categoryPoiResult.setChannelUnifyError(resErrorEnum);
        }

        return categoryPoiResult;
    }

    public static YouzanItemcategoriesTagUpdate converterToYouzanItemcategoriesTagUpdate(CategoryInfoUpdateDTO categoryInfoUpdateDTO) {

        Preconditions.checkArgument(StringUtils.isNumeric(categoryInfoUpdateDTO.getChannelCategoryCode()), "有赞分类ID错误");
        YouzanItemcategoriesTagUpdateParams updateParams = new YouzanItemcategoriesTagUpdateParams();
        updateParams.setName(categoryInfoUpdateDTO.getName());
        updateParams.setTagId(Long.parseLong(categoryInfoUpdateDTO.getChannelCategoryCode()));

        YouzanItemcategoriesTagUpdate categoriesTagUpdate = new YouzanItemcategoriesTagUpdate();
        categoriesTagUpdate.setAPIParams(updateParams);

        return categoriesTagUpdate;
    }

    public static CategoryPoiResult converterYouzanItemcategoriesTagUpdateResult(YouzanItemcategoriesTagUpdateResult tagUpdateResult,
                                                                              CategoryInfoUpdateDTO categoryInfoUpdateDTO, String msg) {
        CategoryPoiResult categoryPoiResult = new CategoryPoiResult();
        categoryPoiResult.setStoreId(categoryInfoUpdateDTO.getStoreId());
        categoryPoiResult.setCode(categoryInfoUpdateDTO.getCode());
        if (Objects.isNull(tagUpdateResult)) {
            categoryPoiResult.setResultCode(ResultCode.FAIL.getCode());
            categoryPoiResult.setMsg(StringUtils.isNotBlank(msg) ? msg : "更新分类信息未知异常");
        } else if (tagUpdateResult.getSuccess()) {
            categoryPoiResult.setResultCode(ResultCode.SUCCESS.getCode());
            categoryPoiResult.setChannelCode(categoryInfoUpdateDTO.getChannelCategoryCode());
        } else {
            categoryPoiResult.setResultCode(ResultCode.FAIL.getCode());
            categoryPoiResult.setMsg(tagUpdateResult.getMessage());
        }

        if (!Objects.equals(categoryPoiResult.getResultCode(), ResultCode.SUCCESS.getCode()) && Objects.nonNull(tagUpdateResult)) {
            //填充渠道统一错误
            ProductChannelUnifyErrorEnum resErrorEnum = ProductChannelErrorMappingUtils.getChannelUnifiedError(ChannelTypeEnum.YOU_ZAN, String.valueOf(tagUpdateResult.getCode()), tagUpdateResult.getMessage());
            categoryPoiResult.setChannelUnifyError(resErrorEnum);
        }

        return categoryPoiResult;
    }

    public static YouzanItemcategoriesTagDelete converterToYouzanItemcategoriesTagDelete(CategoryInfoDeleteDTO categoryInfoDeleteDTO) {

        Preconditions.checkArgument(StringUtils.isNumeric(categoryInfoDeleteDTO.getChannelCategoryCode()), "有赞分类ID错误");
        YouzanItemcategoriesTagDeleteParams deleteParams = new YouzanItemcategoriesTagDeleteParams();
        deleteParams.setTagId(Long.parseLong(categoryInfoDeleteDTO.getChannelCategoryCode()));

        YouzanItemcategoriesTagDelete categoriesTagDelete = new YouzanItemcategoriesTagDelete();
        categoriesTagDelete.setAPIParams(deleteParams);

        return categoriesTagDelete;
    }

    public static YouzanItemSecondgroupQueryByupperid convert2YouzanItemSecondgroupQueryByupperid(String parentId) {
        Preconditions.checkArgument(StringUtils.isNumeric(parentId), "有赞分类ID错误");

        YouzanItemSecondgroupQueryByupperidParams params = new YouzanItemSecondgroupQueryByupperidParams();
        params.setUpperId(Long.parseLong(parentId));
        params.setChannel(0);

        return new YouzanItemSecondgroupQueryByupperid(params);
    }

    public static CategoryPoiResult converterYouzanItemcategoriesTagDeleteResult(YouzanItemcategoriesTagDeleteResult tagDeleteResult,
                                                                                 CategoryInfoDeleteDTO categoryInfoDeleteDTO, String msg) {
        CategoryPoiResult categoryPoiResult = new CategoryPoiResult();
        categoryPoiResult.setStoreId(categoryInfoDeleteDTO.getStoreId());
        categoryPoiResult.setCode(categoryInfoDeleteDTO.getCode());
        if (Objects.isNull(tagDeleteResult)) {
            categoryPoiResult.setResultCode(ResultCode.FAIL.getCode());
            categoryPoiResult.setMsg(StringUtils.isNotBlank(msg) ? msg : "删除分类信息未知异常");
        } else if (tagDeleteResult.getSuccess() || Objects.equals(tagDeleteResult.getMessage(), "商品分组不存在")) {
            categoryPoiResult.setResultCode(ResultCode.SUCCESS.getCode());
        } else {
            categoryPoiResult.setResultCode(ResultCode.FAIL.getCode());
            categoryPoiResult.setMsg(tagDeleteResult.getMessage());
        }

        return categoryPoiResult;
    }


    public static CategoryInfoDeleteDTO converterAdjustCategoryLevelDel(CategoryLevelAdjustDTO adjustDTO) {
        CategoryInfoDeleteDTO deleteDTO = new CategoryInfoDeleteDTO();
        deleteDTO.setStoreId(adjustDTO.getStoreId());
        deleteDTO.setCode(adjustDTO.getCode());
        deleteDTO.setChannelCategoryCode(adjustDTO.getChannelCode());

        return deleteDTO;
    }

    public static CategoryInfoDTO converterAdjustCategoryLevel(CategoryLevelAdjustDTO adjustDTO) {
        CategoryInfoDTO categoryInfoDTO = new CategoryInfoDTO();
        categoryInfoDTO.setCode(adjustDTO.getCode());
        categoryInfoDTO.setName(adjustDTO.getName());
        categoryInfoDTO.setChannelParentCode(adjustDTO.getChannelParentCode());
        categoryInfoDTO.setStoreId(adjustDTO.getStoreId());

        return categoryInfoDTO;
    }

    public static YouzanItemUpdateBranchSku skuPriceAndStockUpdateToYzAPI(List<YzSkuPriceAndStockUpdateInfo> priceAndStockUpdateInfos,
                                                                          String channelPoiId) {

        List<YouzanItemUpdateBranchSkuParams.YouzanItemUpdateBranchSkuParamsMultiskuupdateopenparamlist> updateOpenParamList
                = Lists.newArrayList();
        priceAndStockUpdateInfos.stream()
                .collect(Collectors.groupingBy(YzSkuPriceAndStockUpdateInfo::getChannelSkuId))
                .forEach((channelSkuId, updateInfos) -> {
                    List<YouzanItemUpdateBranchSkuParams.YouzanItemUpdateBranchSkuParamsUpdatepricestockopenparamlist> openParamList
                            = updateInfos.stream()
                            .map(info -> {
                                YouzanItemUpdateBranchSkuParams.YouzanItemUpdateBranchSkuParamsUpdatepricestockopenparamlist itemUpdateBranchSku
                                        = new YouzanItemUpdateBranchSkuParams.YouzanItemUpdateBranchSkuParamsUpdatepricestockopenparamlist();
                                itemUpdateBranchSku.setNodeKdtId(Long.valueOf(channelPoiId));
                                if (Objects.nonNull(info.getPrice())) {
                                    itemUpdateBranchSku.setPrice(ConverterUtils.priceConvert(info.getPrice()));
                                }
                                if (Objects.nonNull(info.getStockQty())) {
                                    itemUpdateBranchSku.setStockNum(BigDecimal.valueOf(info.getStockQty()).longValue());
                                }

                                return itemUpdateBranchSku;})
                            .collect(Collectors.toList());

                    YouzanItemUpdateBranchSkuParams.YouzanItemUpdateBranchSkuParamsMultiskuupdateopenparamlist skuupdateopenparamlist
                            = new YouzanItemUpdateBranchSkuParams.YouzanItemUpdateBranchSkuParamsMultiskuupdateopenparamlist();
                    skuupdateopenparamlist.setRootSkuId(Long.parseLong(channelSkuId));
                    skuupdateopenparamlist.setUpdatePriceStockOpenParamList(openParamList);

                    updateOpenParamList.add(skuupdateopenparamlist);
                });

        YouzanItemUpdateBranchSkuParams branchSkuParams = new YouzanItemUpdateBranchSkuParams();
        branchSkuParams.setMultiSkuUpdateOpenParamList(updateOpenParamList);
        branchSkuParams.setRootItemId(Long.valueOf(priceAndStockUpdateInfos.get(0).getMerchantChannelSpuId()));

        YouzanItemUpdateBranchSku youzanItemUpdateBranchSku = new YouzanItemUpdateBranchSku();
        youzanItemUpdateBranchSku.setAPIParams(branchSkuParams);

        return youzanItemUpdateBranchSku;
    }

    public static YouzanItemUpdateBranchSku skuPriceAndStockUpdateToYzAPINoSku(List<YzSkuPriceAndStockUpdateInfo> priceAndStockUpdateInfos,
                                                                          String channelPoiId) {

        List<YouzanItemUpdateBranchSkuParams.YouzanItemUpdateBranchSkuParamsNoskuupdateopenparamlist> noSkuUpdateOpenParamListList = new ArrayList<>();

        priceAndStockUpdateInfos
                .forEach(updateInfos -> {
                    YouzanItemUpdateBranchSkuParams.YouzanItemUpdateBranchSkuParamsNoskuupdateopenparamlist noskuupdateopenparam =
                            new YouzanItemUpdateBranchSkuParams.YouzanItemUpdateBranchSkuParamsNoskuupdateopenparamlist();

                    noskuupdateopenparam.setNodeKdtId(Long.valueOf(channelPoiId));
                    if (Objects.nonNull(updateInfos.getPrice())) {
                        noskuupdateopenparam.setPrice(ConverterUtils.priceConvert(updateInfos.getPrice()));
                    }
                    if (Objects.nonNull(updateInfos.getStockQty())) {
                        noskuupdateopenparam.setStockNum(BigDecimal.valueOf(updateInfos.getStockQty()).longValue());
                    }
                    noSkuUpdateOpenParamListList.add(noskuupdateopenparam);
                });


        YouzanItemUpdateBranchSkuParams branchSkuParams = new YouzanItemUpdateBranchSkuParams();
        branchSkuParams.setNoSkuUpdateOpenParamList(noSkuUpdateOpenParamListList);
        branchSkuParams.setRootItemId(Long.valueOf(priceAndStockUpdateInfos.get(0).getMerchantChannelSpuId()));

        YouzanItemUpdateBranchSku youzanItemUpdateBranchSku = new YouzanItemUpdateBranchSku();
        youzanItemUpdateBranchSku.setAPIParams(branchSkuParams);

        return youzanItemUpdateBranchSku;
    }


    /**
     * 构建有赞商品搜索请求
     *
     * @param channelPoiIds
     * @param searchAfter
     * @param sorts
     * @param pageNo
     * @param pageSize
     * @return
     */
    public static YouzanItemCommonSearch createItemCommonSearchRequest(List<Long> channelPoiIds, List<Object> searchAfter,
                                                                       List<String> sorts, Integer pageNo, Integer pageSize) {
        YouzanItemCommonSearch youzanItemCommonSearch = new YouzanItemCommonSearch();

        YouzanItemCommonSearchParams youzanItemCommonSearchParams = new YouzanItemCommonSearchParams();
        youzanItemCommonSearchParams.setKdtIds(channelPoiIds);
        youzanItemCommonSearchParams.setSearchAfter(searchAfter);
        youzanItemCommonSearchParams.setSorts(sorts);
        youzanItemCommonSearchParams.setPageNo(pageNo);
        youzanItemCommonSearchParams.setPageSize(pageSize);
        youzanItemCommonSearch.setAPIParams(youzanItemCommonSearchParams);

        return youzanItemCommonSearch;
    }

    /**
     * 基于有赞商品搜索的明细结果组装spu信息，暂时只组装少量必须字段!! 并且注意不会组装商品的分类名称（只有分类id)
     *
     * @param item
     * @return
     */
    public static SpuInfoDTO buildSpuInfoDTO(YouzanItemCommonSearchResult.YouzanItemCommonSearchResultItems item) {
        if (item == null) {
            return null;
        }

        SpuInfoDTO spuInfoDTO = new SpuInfoDTO();
        // 渠道商品SPU编码
        spuInfoDTO.setChannelSpuId(String.valueOf(item.getItemId()));
        // 渠道商品SPU自定义编码，等同于商品创建时的item_no字段，但接口没有返回此字段且没有使用场景，所以留空
        spuInfoDTO.setCustomSpuId(null);
        // 商品名称
        spuInfoDTO.setName(item.getTitle());

        // 前台分类id集合
        // 注意：有赞渠道是通过两个字段返回（一二级），所以这儿简单将其合并，上游系统如果有使用，可能需要自己再做去重
        // 并且为了避免查店内分类信息，这儿只填充分类编码信息，名称字段用编码进行填充！！保证上线工具无分类商品统计正常
        Set<String> frontCategoryCodes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(item.getTagIds())) {
            frontCategoryCodes.addAll(item.getTagIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(item.getSecondTagIds())) {
            frontCategoryCodes.addAll(item.getSecondTagIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList()));
        }
        spuInfoDTO.setFrontCategoryCodes(new ArrayList<>(frontCategoryCodes));
        spuInfoDTO.setCategoryList(frontCategoryCodes.stream()
                .map(categoryCode -> new ChannelStoreCategory(String.valueOf(categoryCode), String.valueOf(categoryCode), null, null))
                .collect(Collectors.toList()));

        // 上下架状态
        spuInfoDTO.setStatus(Objects.equals(item.getIsDisplay(), 1)? SaleStatusEnum.ON_SALE.getCode() : SaleStatusEnum.OFF_SALE.getCode());
        // 组装规格信息：TODO 注意有赞的商品搜索接口没有返回规格相关信息，目前规格信息没有用到的场景，所以暂时留空
        SkuInSpuInfoDTO skuInSpuInfoDTO = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO.setChannelSkuId(StringUtils.EMPTY);
        skuInSpuInfoDTO.setCustomSkuId(StringUtils.EMPTY);
        skuInSpuInfoDTO.setUpc(StringUtils.EMPTY);
        spuInfoDTO.setSkus(Lists.newArrayList(skuInSpuInfoDTO));

        return spuInfoDTO;
    }

    public static CatInfo convert2CatInfo(YouzanItemSecondgroupQueryByupperidResult.YouzanItemSecondgroupQueryByupperidResultData secondGroup) {
        if (secondGroup == null) {
            return null;
        }
        CatInfo catInfo = new CatInfo();
        catInfo.setCatId(String.valueOf(secondGroup.getGroupId()));
        catInfo.setName(secondGroup.getTitle());
        catInfo.setParentId(String.valueOf(secondGroup.getUpperId()));
        catInfo.setParentName(secondGroup.getUpperTitle());
        if (secondGroup.getScore() != null) {
            catInfo.setSequence(secondGroup.getScore().intValue());
        }
        if (secondGroup.getHasChildren() != null) {
            catInfo.setDepth(secondGroup.getHasChildren() == 0 ? 2 : 1);
        }
        return catInfo;
    }


    public static MerchantSpuDTO detailResultToMerchantSpuDTO(YouzanItemDetailGetResult.YouzanItemDetailGetResultData spuDetail) {
        MerchantSpuDTO merchantSpuDTO = new MerchantSpuDTO();
        merchantSpuDTO.setCustomSpuId(spuDetail.getItemNo());
        merchantSpuDTO.setChannelSpuId(String.valueOf(spuDetail.getItemId()));
        merchantSpuDTO.setName(spuDetail.getTitle());
        merchantSpuDTO.setImages(Fun.map(spuDetail.getImages(), YouzanItemDetailGetResult.YouzanItemDetailGetResultImages::getImageUrl));
        List<MerchantSkuDTO> merchantSkuDTOList = Fun.map(spuDetail.getSkuList(), YzConverterUtil::converterToMerchantSkuDTO);
        merchantSpuDTO.setSkuList(merchantSkuDTOList);
        return merchantSpuDTO;
    }

    private static MerchantSkuDTO converterToMerchantSkuDTO(YouzanItemDetailGetResult.YouzanItemDetailGetResultSkulist skuDetail) {
        MerchantSkuDTO merchantSkuDTO  = new MerchantSkuDTO();
        merchantSkuDTO.setChannelSkuId(String.valueOf(skuDetail.getSkuId()));
        merchantSkuDTO.setCustomSkuId(skuDetail.getSkuNo());
        merchantSkuDTO.setUpc(skuDetail.getSkuBarcode());
        if (Objects.nonNull(skuDetail.getWeight())) {
            merchantSkuDTO.setWeight(skuDetail.getWeight().intValue());
        }
        return merchantSkuDTO;
    }
    
    public static CategoryPoiResult processErrorCode(CategoryPoiResult categoryPoiResult, Exception e) {
        if (e instanceof BizException) {
            categoryPoiResult.setResultCode(((BizException) e).getErrorCode());
        }
        return categoryPoiResult;
    }
}
