package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.ali.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class AliResponse {

    @JSONField(name = "code")
    private String errorCode;

    @JSONField(name = "msg")
    private String msg;

    @JSONField(name = "sub_code")
    private String subCode;

    @JSONField(name = "sub_msg")
    private String subMsg;

    @J<PERSON>NField(name = "request_id")
    private String requestId;

    public boolean isSuccess() {
        return (this.errorCode == null || this.errorCode.length() == 0 || this.errorCode.equals("0"))
                && (this.subCode == null || this.subCode.length() == 0);
    }
}
