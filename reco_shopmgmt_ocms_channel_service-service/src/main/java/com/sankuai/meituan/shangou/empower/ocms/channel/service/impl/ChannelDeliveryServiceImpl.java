package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtLogisticsStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelDeliveryService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm.ElmChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.LogisticsStatusDTO;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @description:
 * @author: zhaolei12
 * @create: 2019-02-15 16:07
 */
@Service
public class ChannelDeliveryServiceImpl implements ChannelDeliveryService {

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CommonLogger log;

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Value("${mt.url.base}" + "${mt.url.logisticsStatus}")
    private String logisticStatusUrl;

    @Override
    public OrderDeliveryInfo getOrderDeliveryInfo(long tenantId, String orderId, Long appId, long storeId) {
        try {
            BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(ChannelTypeEnum.ELEM.getCode()).setAppId(appId).setStoreIdList(Arrays.asList(storeId));
            Map<String, Object> bizParam = Maps.newHashMapWithExpectedSize(1);
            bizParam.put(ProjectConstant.ORDER_ID, orderId);
            ChannelResponseDTO<String> channelResponseDTO = elmChannelGateService
                .sendPostReturnDto(ChannelPostELMEnum.ORDER_DELIVERY_GET, baseRequest, bizParam);
            if (Objects.isNull(channelResponseDTO)) {
                log.warn("ElmChannelOrderServiceImpl.getOrderDeliveryInfo, 未获取到订单配送信息, tenantId:{}, orderId:{}", tenantId, orderId);
                return null;
            }
            ChannelResponseResult<String> responseResult = channelResponseDTO.getBody();
            if (Objects.isNull(responseResult)) {
                return null;
            }
            return JSON.parseObject(responseResult.getData(), OrderDeliveryInfo.class);
        } catch (Exception e) {
            log.error("ElmChannelOrderServiceImpl.getOrderDeliveryInfo, 获取订单配送信息异常, tenantId:{}, orderId:{}", tenantId, orderId, e);
        }
        return null;
    }
    
    @Override
    public PartRefundDetailInfo getRefundRecordForElm(long tenantId, String orderId, Long appId) {
        try {

            BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(ChannelTypeEnum.ELEM.getCode()).setAppId(appId);
            Map<String, Object> bizParam = Maps.newHashMapWithExpectedSize(1);
            bizParam.put(ProjectConstant.ORDER_ID, orderId);
            ChannelResponseDTO<String> channelResponseDTO = elmChannelGateService
                    .sendPostReturnDto(ChannelPostELMEnum.ORDER_PARTREFUND_GET, baseRequest, bizParam);
            if (Objects.isNull(channelResponseDTO)) {
                return null;
            }
            ChannelResponseResult<String> responseResult = channelResponseDTO.getBody();
            if (Objects.isNull(responseResult)) {
                return null;
            }
            return JSON.parseObject(responseResult.getData(), PartRefundDetailInfo.class);
        } catch (Exception e) {
            log.error("ChannelDeliveryServiceImpl.getRefundRecordForElm, tenantId:{}, orderId:{}", tenantId, orderId, e);
        }
        return null;
    }

    @Override
    public GetLogisticsStatusResult getMtOrderLogisticsStatus(long tenantId, String orderId, long storeId) {
        GetLogisticsStatusResult resp = new GetLogisticsStatusResult();


        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(ChannelTypeEnum.MEITUAN.getCode())
                .setTenantId(tenantId);
        if(storeId > NumberUtils.LONG_ZERO){
            baseRequest.setStoreIdList(ImmutableList.of(storeId));
        }
        Map<String, Object> param = new HashMap<>();
        param.put("order_id", orderId);
        Map viewStatusMap = mtBrandChannelGateService.sendGet(logisticStatusUrl, null, baseRequest, param);
        if (MapUtils.isEmpty(viewStatusMap)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询订单配送状态失败"));
        }
        if ("ng".equals(viewStatusMap.get(ProjectConstant.DATA))) {
            resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, JSON.parseObject(String.valueOf(viewStatusMap.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG)));
        } else {
            MtLogisticsStatus data = JSON.parseObject(String.valueOf(viewStatusMap.get(ProjectConstant.DATA)), MtLogisticsStatus.class);
            if (data == null) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单配送状态为空"));
            }
            LogisticsStatusDTO orderStatusDTO = new LogisticsStatusDTO();
            orderStatusDTO.setRiderName(data.getDispatcher_name());
            orderStatusDTO.setRiderPhone(data.getDispatcher_mobile());
            orderStatusDTO.setOrderId(orderId);
            orderStatusDTO.setStatus(data.getLogistics_status());
            resp.setStatus(ResultGenerator.genSuccessResult())
                    .setLogisticsStatus(orderStatusDTO);
        }
        return resp;
    }
}
