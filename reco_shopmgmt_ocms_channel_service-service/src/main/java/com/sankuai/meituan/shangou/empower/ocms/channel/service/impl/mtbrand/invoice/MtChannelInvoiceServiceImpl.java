package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.invoice;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.HealthBatchDecryptResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.invoice.ApplyBlueInvoiceReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.invoice.ApplyRedInvoiceReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.qnh.req.invoice.UploadInvoiceReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.invoice.CreateInvoiceSgHandReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.invoice.RedFlushForSgHandReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.MtChannelInvoiceService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.request.InvoiceTaskCreateForSgHandRequest;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.request.InvoiceTaskCreateRequest;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.request.RedFlushForSgHangRequest;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.response.InvoiceTaskCreateResponse;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.response.ResultResponse;
import com.sankuai.sgfnqnh.finance.tax.thrift.service.InvoiceManageThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 美团渠道发票接口
 */
@Service
@Slf4j
public class MtChannelInvoiceServiceImpl implements MtChannelInvoiceService {
    @Resource
    public MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private InvoiceManageThriftService invoiceManageThriftService;

    @org.springframework.beans.factory.annotation.Value("${blueInvoiceUploadUrl}")
    private String blueInvoiceUploadUrl;

    @org.springframework.beans.factory.annotation.Value("${redInvoiceUploadUrl}")
    private String redInvoiceUploadUrl;

    @Override
    public ResultStatus uploadBlueInvoice(UploadInvoiceReq req) {
        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(req.getChannelId())
                .setTenantId(req.getTenantId());

        ResultStatus resultStatus = new ResultStatus();
        Map<String, Object> param = Maps.newHashMap();
        param.put("order_id", req.getOrderId());
        param.put("invoice_url", req.getInvoiceUrl());
        param.put("invoice_id", req.getInvoiceId());
        param.put("invoice_task_id", req.getInvoiceTaskId());
        String url = blueInvoiceUploadUrl;

    	log.info("MtChannelInvoiceServiceImpl.uploadBlueInvoice 上传发票信息 baseRequest:{}   param:{}", baseRequest, param);
        Map<String, Object> resultMap = mtBrandChannelGateService.sendPost(url, null, baseRequest, param);
        log.info("MtChannelOrderServiceImpl.uploadBlueInvoice  上传发票信息接口返回:{}", resultMap);

        if (MapUtils.isEmpty(resultMap) || (!Objects.equals(resultMap.get(ProjectConstant.RESULT_CODE), 1))) {
            resultStatus.setCode(ResultCode.FAIL.getCode());
            log.info("发送上传发票信息请求 结果为空");
        } else {
            resultStatus.setCode(ResultCode.SUCCESS.getCode());
            resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
        }
        return resultStatus;
    }

    @Override
    public ResultStatus uploadRedInvoice(UploadInvoiceReq req) {
        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(req.getChannelId())
                .setTenantId(req.getTenantId());

        ResultStatus resultStatus = new ResultStatus();
        Map<String, Object> param = Maps.newHashMap();
        param.put("order_id", req.getOrderId());
        param.put("invoice_url", req.getInvoiceUrl());
        param.put("invoice_id", req.getInvoiceId());
        param.put("invoice_task_id", req.getInvoiceTaskId());
        String url = redInvoiceUploadUrl;

        log.info("MtChannelInvoiceServiceImpl.uploadRedInvoice 上传发票红冲信息 baseRequest:{}   param:{}", baseRequest, param);
        Map<String, Object> resultMap = mtBrandChannelGateService.sendPost(url, null, baseRequest, param);
        log.info("MtChannelOrderServiceImpl.uploadRedInvoice  上传发票红冲信息接口返回:{}", resultMap);

        if (MapUtils.isEmpty(resultMap) || (!Objects.equals(resultMap.get(ProjectConstant.RESULT_CODE), 1))) {
            resultStatus.setCode(ResultCode.FAIL.getCode());
            log.info("发送红冲发票信息请求 结果为空");
        } else {
            resultStatus.setCode(ResultCode.SUCCESS.getCode());
            resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
        }
        return resultStatus;
    }

    @Override
    public ResultStatus applyBlueInvoice(CreateInvoiceSgHandReq req) {
        ResultStatus resultStatus = new ResultStatus();
        InvoiceTaskCreateForSgHandRequest createInvoiceTaskRequest = new InvoiceTaskCreateForSgHandRequest();
        createInvoiceTaskRequest.setViewOrderId(req.getViewOrderId());
        createInvoiceTaskRequest.setInvoiceTaskId(req.getInvoiceTaskId());
        createInvoiceTaskRequest.setPushType(req.getPushType());
        createInvoiceTaskRequest.setInvoiceTitle(req.getInvoiceTitle());
        createInvoiceTaskRequest.setTaxpayerId(req.getTaxpayerId());
        createInvoiceTaskRequest.setNeedInvoiceByCategory(req.getNeedInvoiceByCategory());
        createInvoiceTaskRequest.setCompanyAddress(req.getCompanyAddress());
        createInvoiceTaskRequest.setCompanyPhone(req.getCompanyPhone());
        createInvoiceTaskRequest.setAccountBank(req.getAccountBank());
        createInvoiceTaskRequest.setAccountNumber(req.getAccountNumber());
        createInvoiceTaskRequest.setEmail(req.getEmail());
        createInvoiceTaskRequest.setEmailExpireTime(req.getEmailExpireTime());
        createInvoiceTaskRequest.setItemTotalInvoiceAmount(req.getItemTotalInvoiceAmount());
        ResultResponse<String> resultResponse = invoiceManageThriftService.createInvoiceTaskForSgHand(createInvoiceTaskRequest);
        log.info("申请开票，req：{},resultResponse:{}",new Gson().toJson(req),new Gson().toJson(resultResponse));
        if(resultResponse.getCode() != 0) {
            log.info("申请开票失败，req：{},resultResponse:{}",new Gson().toJson(req),new Gson().toJson(resultResponse));
        }
        resultStatus.setCode(resultResponse.getCode());
        resultStatus.setMsg(resultResponse.getMsg());
        return resultStatus;
    }

    @Override
    public ResultStatus applyRedInvoice(RedFlushForSgHandReq req) {
        ResultStatus resultStatus = new ResultStatus();
        RedFlushForSgHangRequest redFlushRequest = new RedFlushForSgHangRequest();
        redFlushRequest.setViewOrderId(req.getViewOrderId());
        redFlushRequest.setInvoiceTaskId(req.getInvoiceTaskId());
        redFlushRequest.setInvoiceId(req.getInvoiceId());
        ResultResponse<String> resultResponse = invoiceManageThriftService.redFlushForSgHand(redFlushRequest);
        log.info("申请红冲，req：{},resultResponse:{}",new Gson().toJson(req),new Gson().toJson(resultResponse));
        if(resultResponse.getCode() != 0) {
            log.info("申请红冲失败，req：{},resultResponse:{}",new Gson().toJson(req),new Gson().toJson(resultResponse));
        }
        resultStatus.setCode(resultResponse.getCode());
        resultStatus.setMsg(resultResponse.getMsg());
        return resultStatus;
    }
}
