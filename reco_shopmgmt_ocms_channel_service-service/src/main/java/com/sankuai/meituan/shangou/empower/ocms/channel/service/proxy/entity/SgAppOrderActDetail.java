/*
 * Copyright (c) 2010-2011 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

/**
 * Пусть вечная мира во всем мире！
 *
 * @Author: wangruiguo
 * @Date: 2021/7/27 11:47 上午
 * @Description: 
 */
public class SgAppOrderActDetail {
    private long act_id;
    /**
     * 商品维度活动id
     */
    private long sku_act_id;
    private int type;
    private String remark;
    private double mtCharge;//美团承担
    private double poiCharge;//门店承担
    private int count;

    public long getAct_id() {
        return act_id;
    }

    public void setAct_id(long act_id) {
        this.act_id = act_id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public double getMtCharge() {
        return mtCharge;
    }

    public void setMtCharge(double mtCharge) {
        this.mtCharge = mtCharge;
    }

    public double getPoiCharge() {
        return poiCharge;
    }

    public void setPoiCharge(double poiCharge) {
        this.poiCharge = poiCharge;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public long getSku_act_id() {
        return sku_act_id;
    }

    public void setSku_act_id(long sku_act_id) {
        this.sku_act_id = sku_act_id;
    }

    @Override
    public String toString() {
        return "SgAppOrderActDetail{" +
                "act_id=" + act_id +
                ", sku_act_id=" + sku_act_id +
                ", type=" + type +
                ", remark='" + remark + '\'' +
                ", mtCharge=" + mtCharge +
                ", poiCharge=" + poiCharge +
                ", count=" + count +
                '}';
    }
}
