package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.shangou.saas.common.enums.AuditTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class NewSupplyDeliveryCacheService {

    @Resource(name = "redisSgNewSupplyOfc")
    private RedisStoreClient redisStoreClient;

    private static final String CATEGORY_NAME = "cancel_delivery_order";

    public void set(String key, Integer value) {
        try {
            StoreKey storeKey = new StoreKey(CATEGORY_NAME, key);
            redisStoreClient.set(storeKey, JsonUtil.toJson(value));
        } catch (Exception e) {
            log.error("newSupply redis set error", e);

        }
    }

    public Boolean isExistCancelDelivery(String key) {
        try {
            StoreKey storeKey = new StoreKey(CATEGORY_NAME, key);
            return redisStoreClient.exists(storeKey);
        } catch (Exception e) {
            log.error("newSupply redis isExistCancelDelivery error", e);
            return false;
        }
    }

}
