package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JdBusinessMaskEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.JddjFixStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MultiSpecOptTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TransportTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.CustomSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/18
 **/
public class JddjSpuConverterService {


    public static ChannelNewSkuCreateDTO convert2Single(MerchantSpuDTO spu) {
        ChannelNewSkuCreateDTO single = new ChannelNewSkuCreateDTO();
        MerchantSkuDTO sku = spu.getSkuList().get(0);
        single.setOutSkuId(sku.getCustomSkuId());
        single.setShopCategories(spu.getChannelStoreCategoryCode());
        single.setCategoryId(spu.getChannelCategoryId());
        single.setBrandId(ProjectConstant.JDDJ_DEFAULT_BRAND_ID);
        if(StringUtils.isNotEmpty(spu.getBrandCode())){
            single.setBrandId(spu.getBrandCode());
        }
        single.setSkuName(spu.getName());
        single.setSkuPrice(sku.getSuggestSalePrice());
        single.setWeight(convert2Kg(sku.getWeight()));
        single.setUpc(convertUpc(sku.getUpc()));
        single.setImages(convertImages(spu.getImages()));
        single.setProductDesc(spu.getDescription());
        single.setFixedStatus(JddjFixStatusEnum.ON_SALE.getCode());
        if (Objects.nonNull(spu.getDeliveryRequirement())) {
            TransportTypeEnum transportTypeEnum = TransportTypeEnum.enumOf(spu.getDeliveryRequirement());
            if (Objects.nonNull(transportTypeEnum)) {
                single.setTransportAttribute(transportTypeEnum.getJdCode());
            }
        }
        if (Objects.nonNull(spu.getAllowWeightRefund())) {
            JdBusinessMaskEnum jdBusinessMaskEnum = JdBusinessMaskEnum.enumOf(spu.getAllowWeightRefund());
            if (Objects.nonNull(jdBusinessMaskEnum)) {
                single.setBusinessMark(Lists.newArrayList(jdBusinessMaskEnum.getJdCode()));
            }
        }

        // 医疗器械资质信息
        if (Objects.nonNull(spu.getMedicalDeviceQuaInfo())) {
            if (CollectionUtils.isNotEmpty(spu.getMedicalDeviceQuaInfo().getQuaPictures())) {
                single.setRegistImage(spu.getMedicalDeviceQuaInfo().getQuaPictures().get(0));
            }

            single.setApprovalTime(DateUtils.convertDateFormat(spu.getMedicalDeviceQuaInfo().getQuaApprovalDate(), DateUtils.YYYY_MM_DD, DateUtils.DefaultLongFormat));

            String effectiveDate = spu.getMedicalDeviceQuaInfo().getQuaEffectiveDate();
            if ("-1".equals(spu.getMedicalDeviceQuaInfo().getQuaEffectiveDate())) {
                effectiveDate = "2099-12-31";
            }
            single.setEndTime(DateUtils.convertDateFormat(effectiveDate, DateUtils.YYYY_MM_DD, DateUtils.DefaultLongFormat));
        }
        return single;
    }


    /**
     * UPC转换，超过长度置空处理
     *
     * @param upc
     * @return
     */
    public static String convertUpc(String upc) {
        if (StringUtils.isBlank(upc)) {
            return null;
        }
        if (upc.length() > MccConfigUtil.getJdSkuUpcMaxLength()) {
            return null;
        }
        return upc;
    }

    /**
     * 图片取前6张，并且添加裁剪后缀
     *
     * @param images
     * @return
     */
    public static List<String> convertImages(List<String> images) {
        if (CollectionUtils.isEmpty(images)) {
            return images;
        }
        return images.subList(0, images.size() > 6 ? 6 : images.size())
                .stream().map(img -> img.contains("meituan") ? img + ProjectConstant.JDDJ_IMAGE_CUT_SUFFIX : img).collect(Collectors.toList());
    }

    public static ChannelNewSkuUpdateDTO convert2SingleUpdate(MerchantSpuDTO spu) {
        ChannelNewSkuUpdateDTO single = new ChannelNewSkuUpdateDTO();
        single.setShopCategories(spu.getChannelStoreCategoryCode());
        single.setCategoryId(spu.getChannelCategoryId());
        single.setBrandId(ProjectConstant.JDDJ_DEFAULT_BRAND_ID);
        if(StringUtils.isNotEmpty(spu.getBrandCode())){
            single.setBrandId(spu.getBrandCode());
        }
        single.setSkuName(spu.getName());
        single.setImages(convertImages(spu.getImages()));
        single.setProductDesc(spu.getDescription());
        single.setFixedStatus(JddjFixStatusEnum.ON_SALE.getCode());
        MerchantSkuDTO sku = spu.getSkuList().get(0);
        single.setOutSkuId(sku.getCustomSkuId());
        single.setSkuPrice(sku.getSuggestSalePrice());
        single.setWeight(convert2Kg(sku.getWeight()));
        single.setUpc(convertUpc(sku.getUpc()));
        if (Objects.nonNull(spu.getDeliveryRequirement())) {
            TransportTypeEnum transportTypeEnum = TransportTypeEnum.enumOf(spu.getDeliveryRequirement());
            if (Objects.nonNull(transportTypeEnum)) {
                single.setTransportAttribute(transportTypeEnum.getJdCode());
            }
        }
        if (Objects.nonNull(spu.getAllowWeightRefund())) {
            JdBusinessMaskEnum jdBusinessMaskEnum = JdBusinessMaskEnum.enumOf(spu.getAllowWeightRefund());
            if (Objects.nonNull(jdBusinessMaskEnum)) {
                single.setBusinessMark(Lists.newArrayList(jdBusinessMaskEnum.getJdCode()));
            }
        }

        // 医疗器械资质信息
        if (Objects.nonNull(spu.getMedicalDeviceQuaInfo())) {
            if (CollectionUtils.isNotEmpty(spu.getMedicalDeviceQuaInfo().getQuaPictures())) {
                single.setRegistImage(spu.getMedicalDeviceQuaInfo().getQuaPictures().get(0));
            }

            single.setApprovalTime(DateUtils.convertDateFormat(spu.getMedicalDeviceQuaInfo().getQuaApprovalDate(), DateUtils.YYYY_MM_DD, DateUtils.DefaultLongFormat));

            String effectiveDate = spu.getMedicalDeviceQuaInfo().getQuaEffectiveDate();
            if ("-1".equals(spu.getMedicalDeviceQuaInfo().getQuaEffectiveDate())) {
                effectiveDate = "2099-12-31";
            }
            single.setEndTime(DateUtils.convertDateFormat(effectiveDate, DateUtils.YYYY_MM_DD, DateUtils.DefaultLongFormat));
        }
        return single;
    }

    public static ChannelSpuCreateDTO convert2Spu(MerchantSpuDTO spu) {
        ChannelSpuCreateDTO spuCreate = new ChannelSpuCreateDTO();
        spuCreate.setOutSuperId(spu.getCustomSpuId());
        spuCreate.setSuperName(spu.getName());
        spuCreate.setShopCategories(spu.getChannelStoreCategoryCode());
        spuCreate.setCategoryId(spu.getChannelCategoryId());
        spuCreate.setBrandId(ProjectConstant.JDDJ_DEFAULT_BRAND_ID);
        if(StringUtils.isNotEmpty(spu.getBrandCode())){
            spuCreate.setBrandId(spu.getBrandCode());
        }
        spuCreate.setImages(convertImages(spu.getImages()));
        spuCreate.setProductDesc(spu.getDescription());
        spuCreate.setFixedStatus(JddjFixStatusEnum.ON_SALE.getCode());
        spuCreate.setSaleAttrRelationInfoList(spu.getSaleAttrRelationsList().stream().map(SaleAttrRelationInfo::build).collect(Collectors.toList()));
        spuCreate.setSkuMainInfo(spu.getSkuList().stream().map(ChannelSpuCreateDTO.ChannelSpuSkuCreateDTO::build).collect(Collectors.toList()));

        // 医疗器械资质信息
        if (Objects.nonNull(spu.getMedicalDeviceQuaInfo())) {
            if (CollectionUtils.isNotEmpty(spu.getMedicalDeviceQuaInfo().getQuaPictures())) {
                spuCreate.setRegistImage(spu.getMedicalDeviceQuaInfo().getQuaPictures().get(0));
            }


            spuCreate.setApprovalTime(DateUtils.convertDateFormat(spu.getMedicalDeviceQuaInfo().getQuaApprovalDate(), DateUtils.YYYY_MM_DD, DateUtils.DefaultLongFormat));

            String effectiveDate = spu.getMedicalDeviceQuaInfo().getQuaEffectiveDate();
            if ("-1".equals(spu.getMedicalDeviceQuaInfo().getQuaEffectiveDate())) {
                effectiveDate = "2099-12-31";
            }
            spuCreate.setEndTime(DateUtils.convertDateFormat(effectiveDate, DateUtils.YYYY_MM_DD, DateUtils.DefaultLongFormat));
        }
        return spuCreate;
    }


    public static Double convert2Kg(Integer weight) {
        return BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_DOWN).doubleValue();
    }


    public static ChannelSpuBaseUpdateDTO convert2SpuBase(MerchantSpuDTO spu) {
        ChannelSpuBaseUpdateDTO base = new ChannelSpuBaseUpdateDTO();
        base.setOutSuperId(spu.getCustomSpuId());
        base.setSuperName(spu.getName());
        base.setShopCategories(spu.getChannelStoreCategoryCode());
        base.setCategoryId(spu.getChannelCategoryId());
        base.setBrandId(ProjectConstant.JDDJ_DEFAULT_BRAND_ID);
        if(StringUtils.isNotEmpty(spu.getBrandCode())){
            base.setBrandId(spu.getBrandCode());
        }
        base.setImages(convertImages(spu.getImages()));
        base.setProductDesc(spu.getDescription());

        // 医疗器械资质信息
        if (Objects.nonNull(spu.getMedicalDeviceQuaInfo())) {
            if (CollectionUtils.isNotEmpty(spu.getMedicalDeviceQuaInfo().getQuaPictures())) {
                base.setRegistImage(spu.getMedicalDeviceQuaInfo().getQuaPictures().get(0));
            }


            base.setApprovalTime(DateUtils.convertDateFormat(spu.getMedicalDeviceQuaInfo().getQuaApprovalDate(), DateUtils.YYYY_MM_DD, DateUtils.DefaultLongFormat));

            String effectiveDate = spu.getMedicalDeviceQuaInfo().getQuaEffectiveDate();
            if ("-1".equals(spu.getMedicalDeviceQuaInfo().getQuaEffectiveDate())) {
                effectiveDate = "2099-12-31";
            }
            base.setEndTime(DateUtils.convertDateFormat(effectiveDate, DateUtils.YYYY_MM_DD, DateUtils.DefaultLongFormat));
        }
        return base;
    }

    public static List<ChannelSpuSaleAttrAddDTO> convert2SaleAttrAdd(MerchantSpuDTO spu, List<SaleAttrValueRelationDTO> existAttrList) {
        if (CollectionUtils.isEmpty(spu.getSaleAttrRelationsList())) {
            return Collections.emptyList();
        }
        Map<String, String> attrNameIdMap = Maps.newHashMap();
        ArrayListMultimap<String, String> existSaleAttrNameValuesMap = ArrayListMultimap.create();
        existAttrList.forEach(dto -> {
            //颜色 1
            attrNameIdMap.put(dto.getSaleAttrName(), dto.getSaleAttrId());
            //颜色 绿色
            existSaleAttrNameValuesMap.put(dto.getSaleAttrName(), dto.getSaleAttrValueName());
        });
        // 新增销售属性，排除已存在销售属性
        List<ChannelSpuSaleAttrAddDTO> list = Lists.newArrayList();
        spu.getSaleAttrRelationsList().forEach(saleAttr ->
                saleAttr.getSaleAttrValueNameList().forEach(value -> {
                    List<String> values = existSaleAttrNameValuesMap.get(saleAttr.getSaleAttrName());
                    // 排除已存在销售属性
                    if (values != null && values.contains(value)) {
                        return;
                    }
                    ChannelSpuSaleAttrAddDTO dto = new ChannelSpuSaleAttrAddDTO();
                    dto.setOutSuperId(spu.getCustomSpuId());
                    dto.setSaleAttrId(attrNameIdMap.get(saleAttr.getSaleAttrName()));
                    dto.setSaleAttrValueName(value);
                    list.add(dto);
                })
        );
        return list;
    }

    public static List<ChannelSpuSkuRollBackDTO> convert2SkuRollBack(MerchantSpuDTO spu) {
        List<String> skuIds = spu.getSkuList().stream().filter(sku -> MultiSpecOptTypeEnum.RECOVERY.isOptType(sku.getMultiSpecOptType())).map(MerchantSkuDTO::getChannelSkuId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return skuIds.stream().map(skuId -> {
            ChannelSpuSkuRollBackDTO dto = new ChannelSpuSkuRollBackDTO();
            dto.setSkuId(skuId);
            return dto;
        }).collect(Collectors.toList());
    }

    public static List<ChannelSpuSkuCreateDTO> convert2SpuSkuAppend(MerchantSpuDTO spu) {
        List<MerchantSkuDTO> appendList = spu.getSkuList().stream().filter(sku -> MultiSpecOptTypeEnum.CREATE.isOptType(sku.getMultiSpecOptType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appendList)) {
            return Collections.emptyList();
        }
        return appendList.stream().map(sku -> buildCreate(spu, sku)).collect(Collectors.toList());
    }

    public static List<ChannelSpuSkuUpdateDTO> convert2SpuSkuUpdate(MerchantSpuDTO spu) {
        List<MerchantSkuDTO> updateList = spu.getSkuList().stream().filter(sku -> !MultiSpecOptTypeEnum.CREATE.isOptType(sku.getMultiSpecOptType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateList)) {
            return Collections.emptyList();
        }
        return spu.getSkuList().stream().map(sku -> buildUpdate(spu, sku)).collect(Collectors.toList());

    }

    private static ChannelSpuSkuCreateDTO buildCreate(MerchantSpuDTO spu, MerchantSkuDTO sku) {
        ChannelSpuSkuCreateDTO dto = new ChannelSpuSkuCreateDTO();
        dto.setOutSuperId(spu.getCustomSpuId());
        dto.setSkuName(spu.getName());
        dto.setOutSkuId(sku.getCustomSkuId());
        dto.setSkuPrice(sku.getSuggestSalePrice());
        dto.setWeight(convert2Kg(sku.getWeight()));
        dto.setUpcCode(convertUpc(sku.getUpc()));
        dto.setSaleAttrValues(sku.getAttrValueList().stream().map(SaleAttrValueRelationInfo::build).collect(Collectors.toList()));
        return dto;
    }

    private static ChannelSpuSkuUpdateDTO buildUpdate(MerchantSpuDTO spu, MerchantSkuDTO sku) {
        ChannelSpuSkuUpdateDTO dto = new ChannelSpuSkuUpdateDTO();
        dto.setOutSuperId(spu.getCustomSpuId());
        dto.setImages(convertImages(spu.getImages()));
        dto.setOutSkuId(sku.getCustomSkuId());
        dto.setSkuPrice(sku.getSuggestSalePrice());
        dto.setWeight(convert2Kg(sku.getWeight()));
        dto.setUpcCode(convertUpc(sku.getUpc()));
        dto.setSaleAttrValues(sku.getAttrValueList().stream().map(SaleAttrValueRelationInfo::build).collect(Collectors.toList()));
        if (MultiSpecOptTypeEnum.DELETE.isOptType(sku.getMultiSpecOptType())) {
            dto.setFixedStatus(JddjFixStatusEnum.DELETE.getCode());
        } else {
            //默认上架状态
            dto.setFixedStatus(JddjFixStatusEnum.ON_SALE.getCode());
        }
        return dto;
    }


    public static ChannelNewSkuUpdateDTO convert2SingleOffSale(CustomSpuDeleteDTO spu) {
        ChannelNewSkuUpdateDTO single = new ChannelNewSkuUpdateDTO();
        single.setOutSkuId(spu.getCustomSpuId());
        single.setFixedStatus(JddjFixStatusEnum.OFF_SALE.getCode());
        return single;
    }

    public static ChannelNewSkuUpdateDTO convert2SingleDelete(CustomSpuDeleteDTO spu) {
        ChannelNewSkuUpdateDTO single = new ChannelNewSkuUpdateDTO();
        single.setOutSkuId(spu.getCustomSpuId());
        single.setFixedStatus(JddjFixStatusEnum.DELETE.getCode());
        return single;
    }


    public static ChannelSpuBaseUpdateDTO convert2MultiOffSale(CustomSpuDeleteDTO spu) {
        ChannelSpuBaseUpdateDTO multi = new ChannelSpuBaseUpdateDTO();
        multi.setOutSuperId(spu.getCustomSpuId());
        multi.setFixedStatus(JddjFixStatusEnum.OFF_SALE.getCode());
        return multi;
    }

    public static ChannelSpuBaseUpdateDTO convert2MultiDelete(CustomSpuDeleteDTO spu) {
        ChannelSpuBaseUpdateDTO multi = new ChannelSpuBaseUpdateDTO();
        multi.setOutSuperId(spu.getCustomSpuId());
        multi.setFixedStatus(JddjFixStatusEnum.DELETE.getCode());
        return multi;
    }

    public static ChannelSpuSingleDetailRequest convert2SingleDetail(MerchantSpuDetailRequest merchantSpuDetailRequest) {
        ChannelSpuSingleDetailRequest single = new ChannelSpuSingleDetailRequest();
        single.setPageSize(ProjectConstant.JD_SINGLE_DEFAULT_PAGE_INFO);
        single.setPageNo(ProjectConstant.JD_SINGLE_DEFAULT_PAGE_INFO);
        single.setIsFilterDel(ProjectConstant.JD_DEFAULT_FILTER_DELETE);
        single.setSkuId(merchantSpuDetailRequest.getChannelSpuId());
        return single;
    }

}
