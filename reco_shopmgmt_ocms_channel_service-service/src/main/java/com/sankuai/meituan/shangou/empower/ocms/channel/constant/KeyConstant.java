package com.sankuai.meituan.shangou.empower.ocms.channel.constant;

/**
 * @description: 常量类
 * @author: fanxiaolin
 * @date: 2023/6/16 17:44
 */
public class KeyConstant {
    /**
     * 常量类,不允许外部实例化
     */
    private KeyConstant() {

    }

    /**
     * 包装费
     */
    public static final String PACKING = "包装费";

    /**
     * 打包费
     */
    public static final String PACKING_SUB = "打包费";

    /**
     * 续重费
     */
    public static final String LOGISTICS = "续重费";

    /**
     * 自提包装费
     */
    public static final String ZT_PACKING = "自提包装费";

    /**
     * 默认支付方式
     */
    public static final String DEFAULT_PAY_MODE = "100";

    /**
     * 微信支付方式
     */
    public static final String WECHAT_PAY_MODE = "300";



    // openApi状态变更通知,自生成售后单识别前缀，不可以随意更改
    public static final String OPEN_API_AFTER_SALE_ID_PREFIX = "AFS-";

}
