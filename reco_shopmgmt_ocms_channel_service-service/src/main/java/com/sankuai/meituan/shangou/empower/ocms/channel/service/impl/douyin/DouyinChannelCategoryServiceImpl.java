package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;


import com.dianping.rhino.util.Preconditions;
import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelCategoryOperateResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelShopCategoryQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelShopCategoryQueryResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinCategoryPropQueryResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinCategoryQueryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinProductUpdateRuleParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinProductUpdateRuleResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinRecommendChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.QueryRecommendChannelCategoryParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.RecommendChannelCategoryResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DouYinOperateTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCategoryService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryProductRulesResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultCodeUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrValueRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrValueResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryDegradeRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryLevelAdjustRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryPoiResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySmartSortQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySmartSortQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySmartSortSwitchRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySortRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryTopRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CreateCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCategoryResponse;

import javax.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;

/**
 * 抖音渠道商品分类内部服务接口
 *
 * <AUTHOR>
 * @create 2023-12-18
 **/
@Service("dyChannelCategoryService")
public class DouyinChannelCategoryServiceImpl implements ChannelCategoryService {

    @Resource
    private BaseConverterService baseConverterService;

    @Autowired
    private DouyinChannelGateService douyinChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private CommonLogger log;

    @Value("${douyin.url.base}")
    private String baseUrl;


    @Override
    public CreateCategoryResponse createCategory(CategoryRequest request) {
        log.info("开始创建抖音店内分类 CategoryRequest:{}", request);
        CreateCategoryResponse resp = new CreateCategoryResponse();
        List<CategoryPoiResult> categoryPoiResults = new ArrayList<>();
        BaseRequestSimple baseInfo = request.getBaseInfo();

        for (CategoryInfoDTO categoryInfoDTO : request.getParamList()) {
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setCode(categoryInfoDTO.getCode());
            categoryPoiResults.add(categoryPoiResult);
            try {
                checkCreateCategoryParam(categoryInfoDTO);
                String channelParentCode = categoryInfoDTO.getChannelParentCode();
                Map<String, Object> createParam = new HashMap<>(4);
                createParam.put(ProjectConstant.DOUYIN_OPERATE_TYPE, DouYinOperateTypeEnum.CREATE.getCode());
                createParam.put(ProjectConstant.DOUYIN_CATEGORY_NAME, categoryInfoDTO.getName());
                if (StringUtils.isNotBlank(channelParentCode)) {
                    createParam.put(ProjectConstant.DOUYIN_PARENT_CATEGORY_ID, Long.valueOf(channelParentCode));
                }
                // 抖音渠道推送倒序， 但又不能超过100000（解决sort为负抖音报错的问题）
                createParam.put(ProjectConstant.DOUYIN_RANK, getDouyinRank(categoryInfoDTO.getSort()));
                applyTemplateIdForCategoryOperate(baseInfo.getTenantId(), createParam);

                ChannelResponseDTO<ChannelCategoryOperateResult> channelResponseDTO = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SHOP_MARKET_CATEGORY_OPERATE, baseInfo, null, createParam);
                if (channelResponseDTO.isSuccess()) {
                    Long id = channelResponseDTO.getCoreData().getCategory_id();
                    categoryPoiResult.setResultCode(0)
                            .setChannelCode(String.valueOf(id));
                }
                else {
                    log.warn("douyinChannelCategoryServiceImpl.createCategory 请求抖音返回失败，categoryInfoDTO: {}, channelResponseDTO: {}", categoryInfoDTO, channelResponseDTO);
                    categoryPoiResult
                            .setResultCode(ResultCodeUtils.parseErrorCode(channelResponseDTO.getCode()))
                            .setMsg(channelResponseDTO.getAggregateErrorMsg()).setChannelUnifyError(channelResponseDTO.getUnifyUnifiedErrorEnum());
                }
            }
            catch (IllegalArgumentException e) {
                log.error("douyinChannelCategoryServiceImpl.createCategory 参数校验失败, categoryInfoDTO:{}", categoryInfoDTO, e);
                categoryPoiResult.setResultCode(1).setMsg(e.getMessage());
            }
            catch (Exception e) {
                log.error("douyinChannelCategoryServiceImpl.createCategory 服务异常, categoryInfoDTO:{}", categoryInfoDTO, e);
                categoryPoiResult.setResultCode(1).setMsg(e.getMessage());
            }
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }
    
    /**
     * 背景:
     *  1 抖音排序值是越大越靠前，与牵牛花排序值相反，牵牛花排序存在负数
     *  2 抖音排序值在[0-100000]范围内,目前暂时使用50000减去牵牛花排序值
     * @param sort
     * @return
     */
    private int getDouyinRank(int sort) {
        return ProjectConstant.DOUYIN_CATEGORY_MAX_RANK - sort;
    }

    private void checkCreateCategoryParam(CategoryInfoDTO categoryInfoDTO) {
        Preconditions.checkNotNull(categoryInfoDTO.getName(), "name is null");
        Preconditions.checkArgument(categoryInfoDTO.getName().length() <= ProjectConstant.DOUYIN_CATEGORY_MAX_LENGTH, "分类名称不能超过16字符，请修改后重试");
    }

    /**
     * 更新店内分类
     *
     * @param request 请求参数
     * @return 调用结果
     */
    @Override
    public UpdateCategoryResponse updateCategory(CategoryUpdateRequest request) {
        log.info("开始更新抖音店内分类 CategoryRequest:{}", request);
        BaseRequestSimple baseInfo = request.getBaseInfo();

        UpdateCategoryResponse resp = new UpdateCategoryResponse();
        List<CategoryPoiResult> categoryPoiResults = new ArrayList<>();
        for (CategoryInfoUpdateDTO categoryInfoUpdateDTO : request.getParamList()) {
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setCode(categoryInfoUpdateDTO.getCode());
            categoryPoiResults.add(categoryPoiResult);
            try {
                // 父渠道分类编码为0时传null值（一级分类排序的情况下，父渠道分类编码传0会导致排序失效）
                if (Objects.equals(ProjectConstant.PARENT_CATEGORY_CODE_ZERO, categoryInfoUpdateDTO.getChannelParentCategoryCode())) {
                    categoryInfoUpdateDTO.setChannelParentCategoryCode(null);
                }
                // 验证请求参数
                checkUpdateCategoryParam(categoryInfoUpdateDTO);
                // 调用抖音接口更新渠道分类
                ChannelResponseDTO<ChannelCategoryOperateResult> channelResponseDTO = updateCategory(baseInfo, categoryInfoUpdateDTO);
                if (channelResponseDTO.isSuccess()) {
                    categoryPoiResult.setResultCode(0);
                }
                else {
                    log.warn("douyinChannelCategoryServiceImpl.updateCategory 请求抖音返回失败，categoryInfoUpdateDTO: {}, channelResponseDTO：{}", categoryInfoUpdateDTO, channelResponseDTO);
                    categoryPoiResult
                            .setResultCode(ResultCodeUtils.parseErrorCode(channelResponseDTO.getCode()))
                            .setMsg(channelResponseDTO.getAggregateErrorMsg()).setChannelUnifyError(channelResponseDTO.getUnifyUnifiedErrorEnum());
                }
            }
            catch (IllegalArgumentException e) {
                log.error("douyinChannelCategoryServiceImpl.updateCategory 参数校验失败, categoryInfoUpdateDTO:{}", categoryInfoUpdateDTO, e);
                categoryPoiResult.setResultCode(1).setMsg(e.getMessage());
            }
            catch (Exception e) {
                log.error("douyinChannelCategoryServiceImpl.updateCategory 服务异常, categoryInfoUpdateDTO:{}", categoryInfoUpdateDTO, e);
                categoryPoiResult.setResultCode(1).setMsg(e.getMessage());
            }
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    private void checkUpdateCategoryParam(CategoryInfoUpdateDTO categoryInfoDTO) {
        Preconditions.checkNotNull(categoryInfoDTO.getChannelCategoryCode(), "channelCategoryCode is null");
        Preconditions.checkNotNull(categoryInfoDTO.getName(), "name is null");
        Preconditions.checkArgument(categoryInfoDTO.getName().length() <= ProjectConstant.DOUYIN_CATEGORY_MAX_LENGTH, "分类名称不能超过16字符");
    }

    /**
     * 调用抖音接口更新渠道分类
     *
     * @param baseInfo 基础信息
     * @param categoryInfoDTO 类目信息
     * @return 渠道响应结果
     */
    private ChannelResponseDTO<ChannelCategoryOperateResult> updateCategory(BaseRequestSimple baseInfo, CategoryInfoUpdateDTO categoryInfoDTO) {
        String channelParentCategoryCode = categoryInfoDTO.getChannelParentCategoryCode();
        Map<String, Object> updateParam = new HashMap<>(8);
        updateParam.put(ProjectConstant.DOUYIN_OPERATE_TYPE, DouYinOperateTypeEnum.UPDATE.getCode());
        updateParam.put(ProjectConstant.DOUYIN_CATEGORY_ID, Long.valueOf(categoryInfoDTO.getChannelCategoryCode()));
        if (StringUtils.isNotBlank(channelParentCategoryCode)) {
            updateParam.put(ProjectConstant.DOUYIN_PARENT_CATEGORY_ID, Long.valueOf(channelParentCategoryCode));
        }
        updateParam.put(ProjectConstant.DOUYIN_CATEGORY_NAME, categoryInfoDTO.getName());
        // 抖音渠道推送倒序， 但又不能超过100000（解决sort为负抖音报错的问题）
        updateParam.put(ProjectConstant.DOUYIN_RANK, getDouyinRank(categoryInfoDTO.getSort()));
        applyTemplateIdForCategoryOperate(baseInfo.getTenantId(), updateParam);

        return douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SHOP_MARKET_CATEGORY_OPERATE, baseInfo, null, updateParam);
    }

    /**
     * 删除店内分类
     *
     * @param request 请求参数
     * @return 调用结果
     */
    @Override
    public UpdateCategoryResponse deleteCategory(CategoryDeleteRequest request) {
        log.info("开始删除抖音店内分类 CategoryRequest:{}", request);
        UpdateCategoryResponse resp = new UpdateCategoryResponse();
        List<CategoryPoiResult> categoryPoiResults = new ArrayList<>();
        BaseRequestSimple baseInfo = request.getBaseInfo();

        for (CategoryInfoDeleteDTO categoryInfoDeleteDTO : request.getParamList()) {
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setCode(categoryInfoDeleteDTO.getCode());
            categoryPoiResults.add(categoryPoiResult);
            try {
                Preconditions.checkNotNull(categoryInfoDeleteDTO.getChannelCategoryCode(), "channelCategoryCode is null");

                String channelParentCategoryCode = categoryInfoDeleteDTO.getChannelParentCategoryCode();
                Map<String, Object> deleteParam = new HashMap<>(4);
                deleteParam.put(ProjectConstant.DOUYIN_OPERATE_TYPE, DouYinOperateTypeEnum.DELETE.getCode());
                deleteParam.put(ProjectConstant.DOUYIN_CATEGORY_ID, Long.valueOf(categoryInfoDeleteDTO.getChannelCategoryCode()));
                if (StringUtils.isNotBlank(channelParentCategoryCode)) {
                    deleteParam.put(ProjectConstant.DOUYIN_PARENT_CATEGORY_ID, Long.valueOf(channelParentCategoryCode));
                }
                applyTemplateIdForCategoryOperate(baseInfo.getTenantId(), deleteParam);
                ChannelResponseDTO<ChannelCategoryOperateResult> channelResponseDTO = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.SHOP_MARKET_CATEGORY_OPERATE, baseInfo, null, deleteParam);
                // 抖音侧返回店内分类不存在同样认为是删除成功
                if (channelResponseDTO.isSuccess() || ProjectConstant.DOUYIN_CATEGORY_NOT_EXISTS_ERROR_CODE.equals(channelResponseDTO.getSub_code())) {
                    categoryPoiResult.setResultCode(0);
                }
                else {
                    log.warn("douyinChannelCategoryServiceImpl.deleteCategory 请求抖音返回失败， categoryInfoDeleteDTO: {}, channelResponseDTO：{}", categoryInfoDeleteDTO, channelResponseDTO);
                    categoryPoiResult
                            .setResultCode(ResultCodeUtils.parseErrorCode(channelResponseDTO.getCode()))
                            .setMsg(channelResponseDTO.getAggregateErrorMsg()).setChannelUnifyError(channelResponseDTO.getUnifyUnifiedErrorEnum());
                }

                // 虽然店内分类不存在也被认定为删除成功，但仍需记录日志
                if (ProjectConstant.DOUYIN_CATEGORY_NOT_EXISTS_ERROR_CODE.equals(channelResponseDTO.getSub_code())) {
                    log.warn("douyinChannelCategoryServiceImpl.deleteCategory 请求抖音返回店内分类不存在， channelResponseDTO：{}", channelResponseDTO);
                }

            }
            catch (IllegalArgumentException e) {
                log.error("douyinChannelCategoryServiceImpl.deleteCategory 参数校验失败, categoryInfoDeleteDTO:{}", categoryInfoDeleteDTO, e);
                categoryPoiResult.setResultCode(1).setMsg(e.getMessage());
            }
            catch (Exception e) {
                log.error("douyinChannelCategoryServiceImpl.deleteCategory 服务异常, categoryInfoDeleteDTO:{}", categoryInfoDeleteDTO, e);
                categoryPoiResult.setResultCode(1).setMsg(e.getMessage());
            }
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    /**
     * 店内分类排序
     *
     * @param request
     * @return
     */
    @Override
    public UpdateCategoryResponse sortCategory(CategorySortRequest request) {
        log.info("开始对抖音店内分类进行排序 CategorySortRequest:{}", request);
        UpdateCategoryResponse resp = new UpdateCategoryResponse();
        List<CategoryPoiResult> categoryPoiResults = new ArrayList<>();
        BaseRequestSimple baseInfo = request.getBaseInfo();

        request.getParamList().forEach(parentCategoryInfoSortDTO -> {
            if (CollectionUtils.isEmpty(parentCategoryInfoSortDTO.getSortItemList())) {
                return;
            }
            // 遍历待排序分类，调用更新接口进行排序
            parentCategoryInfoSortDTO.getSortItemList().forEach(childCategoryInfoSortItemDTO -> {
                CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                        .setCode(childCategoryInfoSortItemDTO.getChannelCategoryCode());
                categoryPoiResults.add(categoryPoiResult);
                try {
                    // 验证请求参数
                    Preconditions.checkNotNull(childCategoryInfoSortItemDTO.getChannelCategoryCode(), "channelCategoryCode is null");

                    CategoryInfoUpdateDTO categoryInfoUpdateDTO = DouyinConvertUtil.buildCategoryUpdateDTO(childCategoryInfoSortItemDTO
                            , parentCategoryInfoSortDTO.getCode(), parentCategoryInfoSortDTO.getChannelCategoryCode());
                    // 调用抖音接口更新渠道分类
                    ChannelResponseDTO<ChannelCategoryOperateResult> channelResponseDTO = updateCategory(baseInfo, categoryInfoUpdateDTO);
                    if (channelResponseDTO.isSuccess()) {
                        categoryPoiResult.setResultCode(0);
                    }
                    else {
                        log.warn("douyinChannelCategoryServiceImpl.sortCategory 请求抖音返回失败， categoryInfoUpdateDTO: {}, channelResponseDTO：{}", categoryInfoUpdateDTO, channelResponseDTO);
                        categoryPoiResult
                                .setResultCode(ResultCodeUtils.parseErrorCode(channelResponseDTO.getCode()))
                                .setMsg(channelResponseDTO.getAggregateErrorMsg()).setChannelUnifyError(channelResponseDTO.getUnifyUnifiedErrorEnum());
                    }
                }
                catch (IllegalArgumentException e) {
                    log.error("douyinChannelCategoryServiceImpl.sortCategory 参数校验失败,parentCategoryInfoSortDTO: {}, childCategoryInfoSortItemDTO:{}", parentCategoryInfoSortDTO, childCategoryInfoSortItemDTO, e);
                    categoryPoiResult.setResultCode(1).setMsg(e.getMessage());
                }
                catch (Exception e) {
                    log.error("douyinChannelCategoryServiceImpl.sortCategory 服务异常, parentCategoryInfoSortDTO: {}, childCategoryInfoSortItemDTO:{}", parentCategoryInfoSortDTO, childCategoryInfoSortItemDTO, e);
                    categoryPoiResult.setResultCode(1).setMsg(e.getMessage());
                }
            });
        });

        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    @Override
    public GetCategoryResponse batchGetCategory(CatRequest request) {
        Assert.throwIfNull(request,"请求为空");
        Assert.throwIfEmpty(request.getIds(),"渠道类目id为空");

        GetCategoryResponse getCategoryResponse = new GetCategoryResponse();
        List<CatInfo> catInfoList = new LinkedList<>();

        request.getIds().stream().forEach(categoryIdStr ->{
                Long categoryId = Long.parseLong(categoryIdStr);
                ChannelResponseDTO<List<ChannelShopCategoryQueryResult>> channelResponseDTO =
                        douyinChannelGateService.sendPost(ChannelPostDouyinEnum.GET_SHOP_CATEGORY, request.getBaseInfo(), null,
                                ChannelShopCategoryQueryParam.builder().cid(categoryId).build());
                if(channelResponseDTO.isSuccess()){
                    catInfoList.addAll(channelResponseDTO.getCoreData().stream().map(DouyinConvertUtil::buildCatInfo).collect(Collectors.toList()));
                }
        });
        getCategoryResponse.setStatus(ResultGenerator.genSuccessResult());
        getCategoryResponse.setCatInfoList(catInfoList);
        return getCategoryResponse;
    }

    @Override
    public GetCategoryResponse getSecondCategoryByParentId(CatRequest req) {
        return new GetCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public CategoryProductRulesResponse getCategoryProductRules(CategoryAttrRequest request) {
        return new CategoryProductRulesResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE)).setRuleList(Collections.emptyList());
    }

    @Override
    public CategoryAttrResponse getCategoryAttr(CategoryAttrRequest request) {
        Assert.throwIfNull(request.getCategoryId(),"类目id不允许为空");
        CategoryAttrResponse response = new CategoryAttrResponse();
        List<CategoryAttrInfo> categoryAttrInfoList = getCategoryAttrInfoList(request.getBaseInfo(), request.getCategoryId());
        List<CategoryAttrInfo> saleAttrInfoList = getSaleAttrInfoList(request.getBaseInfo(), request.getCategoryId());
        if (CollectionUtils.isNotEmpty(saleAttrInfoList)) {
            log.info("获取到抖音类目销售属性: categoryId={}", request.getCategoryId());
        }
        response.setStatus(ResultGenerator.genSuccessResult())
                .setGeneralAttrList(categoryAttrInfoList)
                .setSaleAttrList(saleAttrInfoList)
                .setUpcRequired(-1)
                .setWeightRequired(-1);
        return response;
    }

    private List<CategoryAttrInfo> getCategoryAttrInfoList(BaseRequestSimple baseRequest, long categoryId) {
        ChannelResponseDTO<DouyinCategoryPropQueryResult> channelResponseDTO = douyinChannelGateService.sendPost(
                ChannelPostDouyinEnum.GET_PRODUCT_CATEGORY_PROPERTY, baseRequest, null,
                DouyinCategoryQueryParam.builder()
                        .category_leaf_id(categoryId)
                        .build());
        if (channelResponseDTO == null || !channelResponseDTO.isSuccess() || channelResponseDTO.getCoreData() == null) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(channelResponseDTO.getCoreData().getData())) {
            return Collections.emptyList();
        }
        return channelResponseDTO.getCoreData()
                .getData()
                .stream()
                .filter(e -> Objects.equals(e.getStatus(), 0L))
                .map(DouyinConvertUtil::buildCatProp)
                .collect(Collectors.toList());
    }

    private List<CategoryAttrInfo> getSaleAttrInfoList(BaseRequestSimple baseRequest, long categoryId) {
        ChannelResponseDTO<DouyinProductUpdateRuleResult> channelResponseDTO = douyinChannelGateService.sendPost(
                ChannelPostDouyinEnum.GET_PRODUCT_UPDATE_RULE, baseRequest, null,
                DouyinProductUpdateRuleParam.builder()
                        .category_id(categoryId)
                        .build());
        if (channelResponseDTO == null || !channelResponseDTO.isSuccess()) {
            return Collections.emptyList();
        }
        return Optional.ofNullable(channelResponseDTO.getCoreData())
                .map(DouyinProductUpdateRuleResult::getProduct_spec_rule)
                .filter(specRule -> CollectionUtils.isNotEmpty(specRule.getRequired_spec_details()))
                .map(DouyinConvertUtil::buildSaleAttrList)
                .orElseGet(Collections::emptyList);
    }

    @Override
    public CategoryAttrValueResponse getCategoryAttrValue(CategoryAttrValueRequest request) {
        return new CategoryAttrValueResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    @Deprecated
    public UpdateCategoryResponse degradeCategory(CategoryDegradeRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public UpdateCategoryResponse adjustCategoryLevel(CategoryLevelAdjustRequest request) {
        UpdateCategoryResponse resp = new UpdateCategoryResponse().setStatus(ResultGenerator.genSuccessResult()).setData(new ArrayList<>());
        request.getParamList().forEach(data -> {
            /* 删除原等级分类 */
            CategoryDeleteRequest deleteRequst = new CategoryDeleteRequest()
                    .setBaseInfo(request.getBaseInfo())
                    .setParamList(Collections.singletonList(baseConverterService.adjustCategoryLevelDel(data)));

            UpdateCategoryResponse delResp = deleteCategory(deleteRequst);
            if (ResultCode.SUCCESS.getCode() == delResp.status.code && delResp.getData().get(0).getResultCode() == 0) {
                /* 创建目标等级分类 */
                CategoryInfoDTO categoryInfoDTO = null;
                switch (data.getAdjustType()) {
                    case UPGRADE:
                        categoryInfoDTO = baseConverterService.upgradeCategoryLevel(data);
                        break;
                    case DEGRADE:
                        categoryInfoDTO = baseConverterService.degradeCategoryLevel(data);
                        break;
                    default:
                        break;
                }
                CategoryRequest createRequest = new CategoryRequest()
                        .setParamList(Collections.singletonList(categoryInfoDTO))
                        .setBaseInfo(request.getBaseInfo());
                CreateCategoryResponse createResp = createCategory(createRequest);

                resp.getData().addAll(createResp.getData());
            }
            else {
                resp.getData().addAll(delResp.getData());
            }
        });

        return resp;
    }

    @Override
    public UpdateCategoryResponse updateCategoryChannelCode(CategoryUpdateRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public UpdateCategoryResponse updateSmartSortSwitch(CategorySmartSortSwitchRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public CategorySmartSortQueryResponse queryStoreCategorySmartSort(CategorySmartSortQueryRequest request) {
        return new CategorySmartSortQueryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public RecommendCategoryResponse recommendCategory(RecommendCategoryRequest request) {
        RecommendCategoryResponse resp = new RecommendCategoryResponse();
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        ChannelResponseDTO<RecommendChannelCategoryResult> channelResponse = douyinChannelGateService.sendPost(ChannelPostDouyinEnum.GET_RECOMMEND_CATEGORY, baseRequest, QueryRecommendChannelCategoryParam.of(request.getName()));
        if (channelResponse.isSuccess()) {
            if (channelResponse.getCoreData() != null && CollectionUtils.isNotEmpty(channelResponse.getCoreData().getCategoryDetails())) {
                List<DouyinRecommendChannelCategoryDTO> validRecommendChannelCategories = Fun.filter(channelResponse.getCoreData().getCategoryDetails(), douyinRecommendChannelCategoryDTO -> douyinRecommendChannelCategoryDTO.getCategory_detail() != null);
                if (CollectionUtils.isNotEmpty(validRecommendChannelCategories)) {
                    DouyinRecommendChannelCategoryDTO recommendChannelCategoryDTO;
                    List<DouyinRecommendChannelCategoryDTO> qualificationRecommendChannelCategories = Fun.filter(validRecommendChannelCategories, DouyinRecommendChannelCategoryDTO::isQualification);
                    // 首先从有资质类目里面取
                    if (CollectionUtils.isNotEmpty(qualificationRecommendChannelCategories)) {
                        recommendChannelCategoryDTO = qualificationRecommendChannelCategories.get(0);
                    } else {
                        recommendChannelCategoryDTO = validRecommendChannelCategories.get(0);
                    }

                    if (recommendChannelCategoryDTO.getCategory_detail().getLastLevelCid() != null) {
                        resp.setChannelCategoryCode(recommendChannelCategoryDTO.getCategory_detail().getLastLevelCid());
                    }
                }
            }
            resp.setUpcCode(request.getUpcCode());
            resp.setName(request.getName());
            resp.setStatus(new ResultStatus(ResultCode.SUCCESS.getCode(), "成功", null));
        } else {
            if (StringUtils.isNotEmpty(channelResponse.getSub_code())) {
                resp.setStatus(new ResultStatus(
                        ResultCodeUtils.parseErrorCode(channelResponse.getCode()),
                        channelResponse.getSub_msg(), null));
            } else {
                resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, channelResponse.getSub_msg()));
            }
        }
        return resp;
    }

    @Override
    public UpdateCategoryResponse topCategory(CategoryTopRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public GetCategoryResponse queryStoreCategoryList(CatRequest request) {
        return null;
    }

    private void applyTemplateIdForCategoryOperate(long tenantId, Map<String, Object> param) {
        Map<String, Long> tenantCateTemplateConfig = MccConfigUtil.getDouyinTenantCategoryTemplateConfig();
        Optional.ofNullable(tenantCateTemplateConfig)
                .filter(MapUtils::isNotEmpty)
                .map(it -> it.get(String.valueOf(tenantId)))
                .ifPresent(templateId -> param.put(ProjectConstant.DOUYIN_TEMP_ID, templateId));
    }
}
