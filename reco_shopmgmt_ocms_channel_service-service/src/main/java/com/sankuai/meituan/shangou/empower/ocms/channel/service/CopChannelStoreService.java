package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import java.util.List;
import java.util.Map;

import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreBaseInfoDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.PoiCodeAppIdDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;

/**
 * @description: 渠道门店服务
 * @author: zhaolei12
 * @create: 2019-02-20 13:46
 */
public interface CopChannelStoreService {

    Map<String, ChannelStoreDO> getChannelPoiCode(long tenantId, int channelId, List<Long> storeIdList);

    /**
     * 查询
     * @param tenantId 租户id
     * @param channelId 渠道id
     * @param storeId 门店id
     * @return
     */
    ChannelStoreDO getChannelStore(Long tenantId, Integer channelId, Long storeId);

    ChannelStoreDO selectChannelStore(long tenantId, int channelId, String channelPoiCode);

    Long selectChannelStoreId(long tenantId, int channelId, String channelPoiCode);

    Long selectChannelStoreIdIgnoreValid(long tenantId, int channelId, String channelPoiCode);

    <T> Map<String, ChannelStoreDO> getChannelPoiCode(BaseRequestSimple baseInfo, List<T> list);

    List<ChannelStoreBaseInfoDO> selectAllDisableSyncDataStoreBaseInfos(long tenantId, int channelId);

    /**
     * 根据商家门店编码查询渠道门店基础信息
     *
     * @param tenantId 租户id
     * @param channelId 渠道id
     * @param poiCodeList 商家门店编码列表
     * @return
     */
    List<PoiCodeAppIdDO> selectAllByPoiCode(long tenantId, int channelId, List<String> poiCodeList);

    /**
     * 根据商家门店编码查询渠道门店基础信息
     *
     * @param tenantId 租户id
     * @param channelId 渠道id
     * @param poiCodeList 商家门店编码列表
     * @return
     */
    List<PoiCodeAppIdDO> selectAllByChannelPoiCode(long tenantId, int channelId, List<String> poiCodeList);

    /**
     * 获取租户、渠道、poiCode 维度下，poiCode 到 appId 的映射信息
     *
     * @param tenantId 租户id
     * @param channelId 渠道id
     * @param poiCodeList 商家门店编码列表
     * @return
     */
    Map<String, Long> getPoiCodeAppIdMapping(long tenantId, int channelId, List<String> poiCodeList);

    /**
     *
     * 通过渠道门店获取中台门店
     * @param channelId
     * @param channelPoiCode
     * @return
     */
    ChannelStoreDO selectByChannelPoiCode(Integer channelId, String channelPoiCode);

}
