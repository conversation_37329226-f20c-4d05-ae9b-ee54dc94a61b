package com.sankuai.meituan.shangou.empower.ocms.channel.constant;

import org.apache.commons.lang3.math.NumberUtils;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/13 11:33
 * @Description:
 */
public interface ChannelErrorCode {

    interface CommonErrorCode {
        /**
         * 默认的超时返回Code
         */
        int RHINO_LIMIT = 100032;
        /**
         * 兼容历史的超时返回Code
         */
        int RHINO_LIMIT_BAK = 10032;

        static boolean matchRhinoLimit(int code) {
            return code == RHINO_LIMIT || code == RHINO_LIMIT_BAK;
        }

        static boolean matchRhinoLimit(String code) {
            return matchRhinoLimit(NumberUtils.toInt(code, -1));
        }
    }


    interface ElmErrorCode {
        int STORE_ALREADY_OPEN = 201100;
        int STORE_ALREADY_CLOSED = 201107;
    }

}
