package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.StoreIdNotExistException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelDeliverySyncService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery.ChangeDeliveryPlatformRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.DeletePoiShippingRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-10-13
 * @email <EMAIL>
 */
@Slf4j
@Service
public class DrunkHorseChannelDeliverySyncServiceImpl implements ChannelDeliverySyncService {

    @Resource
    private DrunkHorseChannelGateService drunkHorseChannelGateService;
    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Value("${drunkhorse.url.base}" + "${drunkhorse.url.deliveryMethodChange}")
    private String deliveryMethodChangeUrl;

    @Override
    public ResultStatus syncDeliveryPlatformChange(ChangeDeliveryPlatformRequest request) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        Map<String, Object> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtChannelPoiShippingServiceImpl.deliveryMethodChange 获取app_poi_code失败");
            return ResultGenerator.genFailResult("歪马微商城渠道改变配送方式失败");

        }
        DeliveryPlatformEnum deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(request.getDeliveryPlatform());
        if (Objects.isNull(deliveryPlatformEnum)) {
            log.error("MtChannelPoiShippingServiceImpl.deliveryMethodChange 未知deliveryPlatform");
            return ResultGenerator.genFailResult("歪马微商城渠道改变配送方式失败");
        }

        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.ORDER_ID, request.getChannelOrderId());
        bizParam.put(ProjectConstant.DELIVERY_METHOD,  mappingDeliveryPlatform(deliveryPlatformEnum));

        Map<String, Object> resultMap = drunkHorseChannelGateService.sendPost(deliveryMethodChangeUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(resultMap)) {
            return ResultGenerator.genFailResult("调用渠道改变配送方式失败");
        }
        ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(resultMap),
                ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道改变配送方式失败返回数据解析失败");
        }
        if (Objects.nonNull(channelResponseDTO.getResult_code()) && Objects.equals(channelResponseDTO.getResult_code(), 1)) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult("调用渠道改变配送方式失败");
    }

    /**
     * 履约方式
     * 1：自配
     * 2：三方配送
     */
    private int mappingDeliveryPlatform(DeliveryPlatformEnum deliveryPlatformEnum) {
        switch (deliveryPlatformEnum) {
            case MERCHANT_SELF_DELIVERY:
                return 1;
            case DAP_DELIVERY_PLATFORM:
                return 2;
            default:
                throw new BizException("未知配送平台");
        }
    }

    private String getAppPoiCode(long tenantId, int channelId, long storeId) {
        // 获取渠道门店编码
        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channelId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(tenantId, channelId, Collections.singletonList(storeId));
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            throw new StoreIdNotExistException("未查询到配置门店");
        }
        return channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode();
    }

}
