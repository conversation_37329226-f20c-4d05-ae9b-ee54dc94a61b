package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.YouzanUpdateStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.gen.v3_0_0.api.YouzanRetailOpenStockAdjust;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanRetailOpenStockAdjustParams;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanRetailOpenStockAdjustResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * @Author: wangyihao04
 * @Date: 2021-09-13 14:12
 * @Mail: <EMAIL>
 */
@Slf4j
@Service
public class YzChannelStockService extends YouZanBaseService {

    @Autowired
    private CopChannelStoreService copChannelStoreService;
    @Autowired
    private CopAccessConfigService copAccessConfigService;

    /**
     * 成功
     * {
     * "code": 200,
     * "data": true,
     * "success": true,
     * "message": "successful"
     * }
     * 失败
     * 1 商品不存在
     * {
     * "code": 234000003,
     * "data": null,
     * "success": false,
     * "message": "明细中的商品不存在: [BM5184320247]"
     * }
     * 2 商品明细为空  正常不会出现，会判断集合不为空
     * {
     * "code": 234000003,
     * "message": "参数错误 : 商品明细不能为空",
     * "success": false,
     * "trace_id": "bifrost-gateway-0a5bdc47-3071631034-91a8d35f"
     * }
     * <p>
     * 3 同步组合商品  这种需要调用方前置过滤掉组合商品，否则会影响整批
     * {
     * "code": 234000003,
     * "message": "明细中商品的库存:[52469736]（skuId）记录不存在，联系技术支持处理",
     * "success": false,
     * "trace_id": "bifrost-gateway-0ae8494f-3145793323-3edfb681"
     * }
     * 4 系统异常
     * {
     * "gw_err_resp": {
     * "trace_id": "bifrost-gateway-0a5aa569-3071625180-139cfd6a",
     * "err_msg": "系统异常",
     * "err_code": 5001
     * }
     * }
     *
     * @param request
     * @return
     */
    public ResultStatus updateChannelStock(YouzanUpdateStockRequest request) {

        YouzanRetailOpenStockAdjust stockAdjust = requestToYzAPI(request);
        AppMessage appMessage = getAppMessage(request.getTenantId(), request.getStoreId());
        try {

            YouzanRetailOpenStockAdjustResult stockAdjustResult =
                    getResult4YouZanWithRetry(appMessage, stockAdjust, YouzanRetailOpenStockAdjustResult.class);
            log.info("更新有赞库存返回 {}", JSON.toJSONString(stockAdjustResult));
            //解析返回值
            return transformToResultStatus(stockAdjustResult);
        } catch (SDKException e) {
            log.error("调用渠道更新库存有赞SDK异常", e);
            //异常解析返回值
            return new ResultStatus(ResultCode.YZ_STOCK_SYS_ERROR.getCode(), e.getMessage(), null);
        }
    }

    private AppMessage getAppMessage(Long tenantId, Long storeId) {
        CopAccessConfigDO tenantChannelConfig = copAccessConfigService.findTenantChannelConfig(tenantId, ChannelTypeEnum.YOU_ZAN.getCode());
        if (tenantChannelConfig == null) {
            throw new BizException(ResultCode.CHANNEL_APP_ID_INVALID.getCode(), "租户未绑定");
        }
        ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(tenantId, ChannelTypeEnum.YOU_ZAN.getCode(), storeId);
        if (channelStore == null) {
            throw new BizException(ResultCode.CHANNEL_POI_CODE_INVALID.getCode(), "门店未绑定");
        }

        JSONObject config = JSON.parseObject(tenantChannelConfig.getSysParams());

        return AppMessage.builder()
                .clientId(config.getString(ProjectConstant.YZ_CLIENT_ID))
                .grantId(config.getString(ProjectConstant.YZ_GRANT_ID))
                .clientSecret(config.getString(ProjectConstant.SECRET))
                .build();
    }

    private YouzanRetailOpenStockAdjust requestToYzAPI(YouzanUpdateStockRequest request) {
        YouzanRetailOpenStockAdjust stockAdjust = new YouzanRetailOpenStockAdjust();

        YouzanRetailOpenStockAdjustParams stockAdjustParams = new YouzanRetailOpenStockAdjustParams();
        stockAdjustParams.setCreateTime(DateUtils.getCurrentFormatDateStr(DateUtils.DefaultLongFormat));
        stockAdjustParams.setRetailSource(StringUtils.EMPTY);
        stockAdjustParams.setWarehouseCode(request.getWarehouseCode());
        stockAdjustParams.setSourceOrderNo(request.getSourceOrderNo());
        stockAdjustParams.setOrderItems(
                request.getStockParamList().stream()
                        .map(stockParam -> {
                            YouzanRetailOpenStockAdjustParams.YouzanRetailOpenStockAdjustParamsOrderitems orderitem =
                                    new YouzanRetailOpenStockAdjustParams.YouzanRetailOpenStockAdjustParamsOrderitems();
                            orderitem.setSkuCode(stockParam.getSkuId());
                            orderitem.setQuantity(String.valueOf(stockParam.getStockQty()));
                            return orderitem;
                        }).collect(Collectors.toList())
        );
        stockAdjust.setAPIParams(stockAdjustParams);
        return stockAdjust;
    }

    public static ResultStatus transformToResultStatus(YouzanRetailOpenStockAdjustResult stockAdjustResult) {
        if (stockAdjustResult.getSuccess()) {
            return new ResultStatus(ResultCode.SUCCESS.getCode(), null, null);
        } else {
            return new ResultStatus(ResultCode.YZ_STOCK_BIZ_ERROR.getCode(), stockAdjustResult.getMessage(), null);
        }
    }
}
