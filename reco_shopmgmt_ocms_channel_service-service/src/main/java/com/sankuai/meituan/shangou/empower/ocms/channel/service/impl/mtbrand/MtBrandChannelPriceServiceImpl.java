package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ActCheckTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPriceService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ProductResultDataUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 美团渠道商品价格内部服务接口
 *
 * <AUTHOR>
 * @create 2019-01-09 下午4:40
 **/
@Service("mtBrandChannelPriceService")
public class MtBrandChannelPriceServiceImpl implements ChannelPriceService {
    public static final int PRICE_UPDATE_MAX_COUNT = 50;

    @Resource
    private MtConverterService mtConverterService;

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private CommonLogger log;

    /**
     * @param request 单门店
     * @return
     */
    @Deprecated
    @Override
    public ResultData updatePrice(SkuPriceRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        // 分页调用
        ListUtils.listPartition(request.getParamList(), PRICE_UPDATE_MAX_COUNT).forEach(data -> {
            List<String> bizKeyList = Lists.newArrayList();
            try {
                // 返回结果组装用标识
                bizKeyList = data.stream().map(SkuPriceDTO::getSkuId).collect(Collectors.toList());

                // 业务参数转换
                ChannelSkuUpdateDTO dto = mtConverterService.updatePrice(JSON.toJSONString(mtConverterService.updatePrice(data)));

                //是否移除活动，默认不移除
                if (MccConfigUtil.removeActivitySwitch(request.getTenantId())) {
                    dto.setAct_check_type(ActCheckTypeEnum.ACT_REMOVE.getCode());
                }
                // 当前请求的优先级大于租户配置的优先级
                if(ActCheckTypeEnum.isVaild(request.getActCheckType())){
                    dto.setAct_check_type(request.getActCheckType());
                }

                // 调用渠道接口
                Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.PRICE_UPDATE, getBaseRequest(request), dto);

                // 组装返回结果
                ProductResultDataUtils.combineResultDataList(resultData, postResult, bizKeyList, ChannelTypeEnum.MEITUAN);

            } catch (IllegalArgumentException e) {
                log.error("MtChannelPriceServiceImpl.updatePrice 参数校验失败, data:{}", data, e);
                ProductResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);

            } catch (Exception e) {
                log.error("MtChannelPriceServiceImpl.updatePrice 服务异常, data:{}", data, e);
                ProductResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList);
            }
        });
        return resultData;
    }

    /**
     * @param request 单门店
     * @return
     */
    @Override
    public ResultData updatePriceMultiChannel(SkuPriceMultiChannelRequest request) {
        int channelId = request.getParamList().get(0).getChannelId();
        long storeId = request.getParamList().get(0).getStoreId();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(channelId)
                .setStoreIdList(Lists.newArrayList(storeId)).setAsyncInvoke(request.isAsyncInvoke());
        //按spu分组
        Map<String, List<SkuPriceMultiChannelDTO>> spuAndSkuInfo = request.getParamList()
                .stream()
                .collect(Collectors.groupingBy(SkuPriceMultiChannelDTO::getSpuId));

        try {
            //业务参数转换
            ChannelSkuUpdateDTO dto = mtConverterService.updatePrice(JSON.toJSONString(
                    mtConverterService.convertSpuPriceInfos(Lists.newArrayList(spuAndSkuInfo.entrySet()))));

            //是否移除活动，默认不移除
            if (MccConfigUtil.removeActivitySwitch(request.getTenantId())) {
                dto.setAct_check_type(ActCheckTypeEnum.ACT_REMOVE.getCode());
            }
            // 当前请求的优先级大于租户配置的优先级
            if(ActCheckTypeEnum.isVaild(request.getActCheckType())){
                dto.setAct_check_type(request.getActCheckType());
            }

            //调用渠道
            Map<Long, ChannelResponseDTO> postResult = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.PRICE_UPDATE, baseRequest, dto);
            //构建结果
            return ProductResultDataUtils.buildMTPriceResultData(postResult, spuAndSkuInfo);
        } catch (InvokeChannelTooMuchException e) {
            List<String> skuIds  = request.getParamList().stream().map(SkuPriceMultiChannelDTO::getChannelSkuId).collect(Collectors.toList());
            return ProductResultDataUtils.combineExceptionDataList(ResultDataUtils.newResultData(ResultCodeEnum.TRIGGER_LIMIT.getValue(), ProjectConstant.TRIGGER_LIMIT_MSG, String.valueOf(e.getWaitTimeMills())), e.getMessage(), skuIds, storeId);
        } catch (Exception e) {
            log.error("批量更新商品价格失败", e);
            return ProductResultDataUtils.newResultData(ResultCodeEnum.FAIL.getValue(), "批量更新商品价格失败");
        }
    }

    @Override
    public List<ChannelStorePriceInfo> batchGetPriceInfo(BaseRequest request, List<Long> skuIds) {
        return null;
    }

}
