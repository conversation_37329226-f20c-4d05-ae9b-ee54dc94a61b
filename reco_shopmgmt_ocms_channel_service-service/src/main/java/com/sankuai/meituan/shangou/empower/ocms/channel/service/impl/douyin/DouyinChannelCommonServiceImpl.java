package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelVirtualAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTenantStoreInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTenantSysParams;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.DouyinChannelCommonService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.application.AppInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: reco_shopmgmt_ocms_channel_service
 * @description:
 * @author: jinyi
 * @create: 2024-01-01 21:44
 **/
@Service
@Slf4j
public class DouyinChannelCommonServiceImpl implements DouyinChannelCommonService {

    @Autowired
    private DouyinAccessTokenService douyinAccessTokenService;

    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Autowired
    private CopChannelStoreService copChannelStoreService;

    @Autowired
    private DouyinChannelGateService douyinChannelGateService;

    @Override
    public DouyinTenantSysParams getTenantSysParamsByTenantId(Long tenantId) {

        CopAccessConfigDO copAccessConfig = getCopAccessConfigByTenantId(tenantId);

        String sysParams = copAccessConfig.getSysParams();
        Map<String, String> sysParamsMap = JacksonUtils.parseMap(sysParams, String.class, String.class);
        String shopId = sysParamsMap.get("shop_id");
        String appKey = sysParamsMap.get("app_key");
        String secret = sysParamsMap.get("secret");

        DouyinTokenMessage token = douyinAccessTokenService.getTokenFromRedis(appKey, shopId);
        if (Objects.isNull(token)) {
            log.info("getTenantSysParamsByTenantId 查询token为空，tenantId={}", tenantId);
            throw new BizException("查询token不存在");
        }

        return DouyinTenantSysParams.builder().tenantId(tenantId).appKey(appKey).secret(secret).shopId(shopId)
                .accessToken(token.getAccessToken()).build();
    }

    @Override
    public AppInfoDTO queryAppInfoByTenant(Long tenantId, Integer channelId) {
        if(channelId != ChannelType.DOU_YIN.getValue()) {
            log.info("当前查询非抖音渠道, tenantId={}, channelId={}", tenantId, channelId);
            throw new IllegalArgumentException("查询非抖音渠道");
        }

        CopAccessConfigDO copAccessConfig = getCopAccessConfigByTenantId(tenantId);

        String sysParams = copAccessConfig.getSysParams();
        Map<String, String> sysParamsMap = JacksonUtils.parseMap(sysParams, String.class, String.class);
        String shopId = sysParamsMap.get("shop_id");
        String appKey = sysParamsMap.get("app_key");
        String secret = sysParamsMap.get("secret");

        DouyinTokenMessage token = douyinAccessTokenService.getTokenFromRedis(appKey, shopId);
        if (Objects.isNull(token)) {
            log.info("queryDouYinAppInfoByTenant 查询token为空，tenantId={}", tenantId);
            throw new BizException("查询token不存在");
        }

        AppInfoDTO appInfoDTO = new AppInfoDTO();
        appInfoDTO.setChannelId(channelId);
        appInfoDTO.setType(2);
        appInfoDTO.setAppKey(appKey);
        appInfoDTO.setSecret(secret);
        appInfoDTO.setSubAppKey(shopId);
        appInfoDTO.setAccessToken(token.getAccessToken());
        appInfoDTO.setQnhAppId(copAccessConfig.getAppId());
        appInfoDTO.setAppName(Objects.isNull(copAccessConfig.getAppName())? appKey : copAccessConfig.getAppName());
        return appInfoDTO;
    }

    @Override
    public DouyinTenantSysParams getTenantSysParamsByHeadShopId(String headShopId, String appKey) {

        // 1.从redis中获取 租户id
        DouyinTokenMessage token = douyinAccessTokenService.getTokenFromRedis(appKey, headShopId);
        if (Objects.isNull(token)) {
            log.info("getTenantSysParamsByHeadShopId 查询token为空，headShopId={}, appKey={}", headShopId, appKey);
            throw new BizException("查询token不存在");
        }

        Long tenantId = token.getTenantId();

        // 2.根据 租户id 获取抖音渠道的 配置信息
        CopAccessConfigDO copAccessConfig = getCopAccessConfigByTenantId(tenantId);
        String sysParams = copAccessConfig.getSysParams();
        Map<String, String> sysParamsMap = JacksonUtils.parseMap(sysParams, String.class, String.class);

        String secret = sysParamsMap.get("secret");

        return DouyinTenantSysParams.builder().tenantId(tenantId).appKey(appKey).secret(secret)
                .accessToken(token.getAccessToken()).build();
    }

    @Override
    public DouyinTenantStoreInfo getTenantSysParamsByTenantChannelPoiCode(Long tenantId, String channelPoiCode) {

        // 1 获取中台门店id
        Long storeId = copChannelStoreService.selectChannelStoreId(tenantId, ChannelTypeEnum.DOU_YIN.getCode(),
                channelPoiCode);
        if (storeId == ProjectConstant.UNKNOW_STORE_ID) {
            throw new BizException(ResultCode.CHANNEL_POI_CODE_INVALID.getCode(), "门店未绑定");
        }

        // 2 获取租户系统参数
        DouyinTenantSysParams tenantSysParams = getTenantSysParamsByTenantId(tenantId);

        return DouyinTenantStoreInfo.builder()
                .tenantId(tenantId)
                .accessToken(tenantSysParams.getAccessToken())
                .appKey(tenantSysParams.getAppKey())
                .secret(tenantSysParams.getSecret())
                .storeId(storeId)
                .channelPoiCode(channelPoiCode)
                .build();
    }

    @Override
    public DouyinTenantStoreInfo getTenantSysParamsByTenantStoreId(Long tenantId, Long storeId) {

        // 获取渠道门店id
        ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(tenantId,
                ChannelTypeEnum.DOU_YIN.getCode(), storeId);
        if (channelStoreDO == null) {
            throw new BizException(ResultCode.CHANNEL_POI_CODE_INVALID.getCode(), "门店未绑定");
        }

        // 2 获取租户系统参数
        DouyinTenantSysParams tenantSysParams = getTenantSysParamsByTenantId(tenantId);

        return DouyinTenantStoreInfo.builder()
                .tenantId(tenantId)
                .accessToken(tenantSysParams.getAccessToken())
                .appKey(tenantSysParams.getAppKey())
                .secret(tenantSysParams.getSecret())
                .storeId(storeId)
                .channelPoiCode(channelStoreDO.getChannelOnlinePoiCode())
                .build();
    }

    private CopAccessConfigDO getCopAccessConfigByTenantId(Long tenantId) {
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId,
                ChannelTypeEnum.DOU_YIN.getCode());
        if (CollectionUtils.isEmpty(copAccessConfigDOList) || copAccessConfigDOList.size() != 1) {
            // 抖音渠道单租户只能有一个应用，这里查询为空，或者超过一条记录，都认为是非法数据
            throw new IllegalArgumentException("租户关联的应用数为空或超过一个，不能获取对应密钥信息");
        } else {
            return copAccessConfigDOList.get(0);
        }
    }



    @Override
    public Long getTenantIdByHeadShopId(String headShopId, String appKey) {

        // 1.从redis中获取 租户id
        DouyinTokenMessage token = douyinAccessTokenService.getTokenFromRedis(appKey, headShopId);
        if (Objects.isNull(token)) {
            log.info("getTenantSysParamsByHeadShopId 查询token为空，headShopId={}, appKey={}", headShopId, appKey);
            return null;
        }

        return token.getTenantId();
    }
}
