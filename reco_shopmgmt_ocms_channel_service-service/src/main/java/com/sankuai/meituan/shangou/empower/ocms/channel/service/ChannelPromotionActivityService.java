package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.promotion.ChannelActQueryByItemReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.promotion.MtDeduceFeeActivityQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.promotion.MtDeduceFeeActivityQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.promotion.PromotionQueryByItemResponse;

/***
 * @description:
 * @author: zhangheng
 * @create: 2024/4/19
 **/
public interface ChannelPromotionActivityService {

	/**
	 * 查询商品活动
	 *
	 * @param request
	 * @return
	 */
	PromotionQueryByItemResponse queryItemActivity(ChannelActQueryByItemReq request);

	/**
	 * 查询美团减运费活动
	 *
	 * @param request
	 * @return
	 */
	MtDeduceFeeActivityQueryResponse queryMtDeduceFeeActivities(MtDeduceFeeActivityQueryRequest request);
}
