package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.linz.boot.util.Fun;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.meituan.shangou.saas.tenant.thrift.exceptions.TenantBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPoiCategoryOneInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPoiCategoryTree;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPoiMappingInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtAppDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.DxPushMessageProducer;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.PoiChannelAppThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.common.PoiChannelConstant;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.AppInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByTenantRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.LIST_PARTITION_NUM;
import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.RHINO_UPTIMATE_SET;


/**
 * 新供给侧渠道门店服务
 * （一租户多品牌需求中，将新供给和歪马的服务代码拆分开来）
 *
 * <AUTHOR>
 */
@Service("mtBrandChannelPoiService")
public class MtBrandChannelPoiServiceImpl implements ChannelPoiService {

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;
    @Resource
    private MtConverterService mtConverterService;
    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Autowired
    private DxPushMessageProducer dxPushMessageProducer;

    @Resource
    private BaseConverterService baseConverterService;

    @Value("${mt.url.base}" + "${mt.url.shoplist}")
    private String shoplist;

    @Value("${mt.url.base}" + "${mt.url.shopget}")
    private String shopget;

    @Value("${mt.url.base}" + "${mt.url.poiSettleCategoryList}")
    private String categoryList;

    @Value("${mt.url.base}")
    private String mtUrlBase;

    @Value("${mt.url.createAppBase}")
    private String mtCreateAppBase;

    @Resource
    private CommonLogger log;

    @Autowired
    private PoiChannelAppThriftServiceProxy poiChannelAppThriftServiceProxy;

    private static final ExecutorService threadPoolExecutor  =
            new ExecutorServiceTraceWrapper(new ThreadPoolExecutor(4, 8, 2, TimeUnit.MINUTES,
                    new ArrayBlockingQueue<>(100),
                    new ThreadFactoryBuilder().setNameFormat("MtBrandChannelPoiPool-%d").build(),new ThreadPoolExecutor.CallerRunsPolicy()));


    @Override
    public GetPoiInfoResponse batchGetPoiInfo(BaseRequestSimple req) {
        GetPoiInfoResponse resp = new GetPoiInfoResponse();

        long tenantId = req.getTenantId();
        int channelId = req.getChannelId();
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOList)) {
            log.warn("获取租户品牌数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取租户品牌数据失败"));
        }

        List<PoiInfo> poiInfoListInAllApps = new ArrayList<>();
        // 按渠道/品牌应用维度进行查询
        for (CopAccessConfigDO copAccessConfigDO : copAccessConfigDOList) {
            long appId = copAccessConfigDO.getAppId() == null ? 1L : copAccessConfigDO.getAppId();
            String appKey = copAccessConfigDO.getTenantAppId();
            BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);
            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            String mtAppIdKey = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

            // 限频控制
            if (StringUtils.isNotBlank(mtAppIdKey) && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.BATCH_GET_POILIST, mtAppIdKey)) {
                log.warn("batchGetPoiInfo 获取令牌失败 不阻塞流程 直接调用接口");
            }

            // 查询门店列表
            Map<String, Object> shoplistMap = mtBrandChannelGateService.sendGet(shoplist, null, baseRequest, new Object());
            if (shoplistMap == null || shoplistMap.get(ProjectConstant.ERROR) != null) {
                // 一个应用查询失败，直接全部失败
                log.warn("查询门店列表失败 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表失败"));
            }
            JSONArray shopIdJsonArray = (JSONArray) shoplistMap.get(ProjectConstant.DATA);
            if (shopIdJsonArray == null || shopIdJsonArray.isEmpty()) {
                // 一个应用查询为空，则跳过当前应用
                log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
                continue;
            }
            List<String> totalShopIds = shopIdJsonArray.toJavaList(String.class);

            // 查询门店详情
            List<List<String>> shopIdsPartition = Lists.partition(totalShopIds, LIST_PARTITION_NUM);
            List<PoiInfo> poiInfoList = shopIdsPartition.parallelStream().map(shopIds -> {
                String appPoiCodes = String.join(",", shopIds);
                Map<String, Object> param = Maps.newHashMap();
                param.put(ProjectConstant.APP_POI_CODES, appPoiCodes);
                // 限频控制
                if (StringUtils.isNotBlank(mtAppIdKey) && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.BATCH_GET_POIDETAIL, mtAppIdKey)) {
                    log.warn("batchGetPoiInfo 获取令牌失败 不阻塞流程 直接调用接口");
                }
                Map<String, Object> poiListMap = mtBrandChannelGateService.sendGet(shopget, null, baseRequest, param);
                if (MapUtils.isEmpty(poiListMap) || Objects.isNull(poiListMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(poiListMap.get(ProjectConstant.DATA))) {
                    return null;
                }
                JSONArray poiJsonArray = (JSONArray) poiListMap.get(ProjectConstant.DATA);
                List<ChannelPoiInfo> channelPoiInfos = poiJsonArray.toJavaList(ChannelPoiInfo.class);
                List<PoiInfo> poiInfos = mtConverterService.poiInfoListMapping(channelPoiInfos);
                fillAppId(poiInfos, appId, appKey);
                return poiInfos;
            }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());

            // 添加到汇总列表
            if (CollectionUtils.isNotEmpty(poiInfoList)) {
                poiInfoListInAllApps.addAll(poiInfoList);
            }
        }

        if (poiInfoListInAllApps.isEmpty()) {
            log.warn("查询门店列表为空 tenantId: {} - channelId: {}", tenantId, channelId);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表为空"));
        }
        resp.setStatus(ResultGenerator.genSuccessResult());
        resp.setPoiInfoList(poiInfoListInAllApps);
        return resp;
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIds(BaseRequestSimple req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());

        long tenantId = req.getTenantId();
        int channelId = req.getChannelId();
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOList)) {
            log.warn("获取租户品牌数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取租户品牌数据失败"));
        }

        // 美团一应用多租户的ISV应用，无法通过该接口拉取渠道门店
        List<CopAccessConfigDO> nonISVCopAccessConfigDOList = copAccessConfigDOList.stream()
                .filter(k -> k.getAppId() < PoiChannelConstant.MT_QNH_APP_ID_BEGIN).collect(Collectors.toList());

        //  一应用多租户的ISV应用qnh_app_id -> app_poi_code
        Map<Long, AppInfoDTO> qnhAppIdToAppInfoMap = getIsvQnhAppIdToAppInfoMap(copAccessConfigDOList, tenantId, channelId);

        List<String> totalShopIdsInAllApps = new ArrayList<>();
        List<AppPoiCodeDTO> appPoiCodeDTOList = new ArrayList<>();
        // 按渠道/品牌应用维度进行查询
        Map<Long,Future<List<String >>> futureMap = new HashMap<>();

        // 异步并发执行
        for (CopAccessConfigDO copAccessConfigDO : nonISVCopAccessConfigDOList) {
            long appId = copAccessConfigDO.getAppId() == null ? 1L : copAccessConfigDO.getAppId();
            Future<List<String >> future =  threadPoolExecutor.submit(new Callable<List<String>>() {
                @Override
                public List<String> call() throws Exception {
                    BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);
                    Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
                    String mtAppIdKey = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
                    // 限频控制
                    if (StringUtils.isNotBlank(mtAppIdKey) && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.BATCH_GET_POILIST, mtAppIdKey)) {
                        log.warn("batchGetPoiIds 获取令牌失败 不阻塞流程 直接调用接口");
                    }
                    // 查询门店列表
                    Map<String, Object> shopListMap = mtBrandChannelGateService.sendGet(shoplist, null, baseRequest, new Object());
                    if (shopListMap == null || shopListMap.get(ProjectConstant.ERROR) != null) {
                        // 一个应用查询失败，直接全部失败
                        log.warn("查询门店列表失败 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
//                        return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表失败"));
                        throw new TenantBizException(ResultCode.FAIL.getCode(),"查询门店列表失败");
                    }
                    JSONArray shopIdJsonArray = (JSONArray) shopListMap.get(ProjectConstant.DATA);
                    if (shopIdJsonArray == null || shopIdJsonArray.isEmpty()) {
                        // 一个应用查询为空，则跳过当前应用
                        log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appId: {}", tenantId, channelId, appId);
                        return null;
                    }
                    return shopIdJsonArray.toJavaList(String.class);
                }
            });
            futureMap.put(appId,future);
        }

        // 取结果
        for (Map.Entry<Long, Future<List<String>>> entry : futureMap.entrySet()) {
            Long appId = entry.getKey();
            Future<List<String>> future = entry.getValue();
            try {
                List<String> totalShopIds = future.get(10L, TimeUnit.SECONDS);
                if (CollectionUtils.isNotEmpty(totalShopIds)) {
                    totalShopIdsInAllApps.addAll(totalShopIds);
                    // appPoiCode 关联 appId 进行透出
                    for (String appPoiCode : totalShopIds) {
                        AppPoiCodeDTO appPoiCodeDTO = new AppPoiCodeDTO(appPoiCode, appId);
                        appPoiCodeDTOList.add(appPoiCodeDTO);
                    }
                }
            } catch (Exception e) {
                log.error("MtBrandChannelPoiServiceImpl async get app poi id,tenant:{},channel:{},app:{} err.",
                        tenantId,channelId,appId,e);
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL,"查询应用门店列表失败"));
            }
        }

        // 取isv（一应用多租户）的结果
        for (Map.Entry<Long, AppInfoDTO> entry : qnhAppIdToAppInfoMap.entrySet()) {
            Long appId = entry.getKey();
            String appPoiCode = entry.getValue().getExtParam().getAppPoiCode();
            totalShopIdsInAllApps.add(appPoiCode);
            appPoiCodeDTOList.add(new AppPoiCodeDTO(appPoiCode, appId));
        }


        if (totalShopIdsInAllApps.isEmpty()) {
            log.warn("查询门店列表为空 tenantId: {} - channelId: {}", tenantId, channelId);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表为空"));
        }
        resp.setStoreIds(totalShopIdsInAllApps);
        resp.setAppPoiCodeDTOList(appPoiCodeDTOList);
        return resp;
    }

    private Map<Long, AppInfoDTO> getIsvQnhAppIdToAppInfoMap(List<CopAccessConfigDO> copAccessConfigDOList, long tenantId, int channelId) {
        Set<Long> isvQnhAppIdSet = copAccessConfigDOList.stream().map(CopAccessConfigDO::getAppId)
                .filter(k -> k >= PoiChannelConstant.MT_QNH_APP_ID_BEGIN).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(isvQnhAppIdSet)){
            return new HashMap<>();
        }
    
        AppInfoQueryByTenantRequest appInfoQueryByTenantRequest = new AppInfoQueryByTenantRequest();
        appInfoQueryByTenantRequest.setTenantId(tenantId);
        appInfoQueryByTenantRequest.setChannelIdList(Lists.newArrayList(channelId));
        List<AppInfoDTO> appInfoDTOList = poiChannelAppThriftServiceProxy.queryAppInfoByTenant(appInfoQueryByTenantRequest);
        Map<Long, AppInfoDTO> qnhAppIdToAppInfoMap = Fun.toMapQuietly(
                Fun.filter(appInfoDTOList,
                        k -> k.getChannelId() == channelId && isvQnhAppIdSet.contains(k.getQnhAppId())
                                && k.getExtParam() != null && k.getExtParam().getAppPoiCode() != null),
                k -> k.getQnhAppId(), Function.identity());


        List<Long> errorIsvQnhAppIdSet = Fun.filter(isvQnhAppIdSet, k -> !qnhAppIdToAppInfoMap.containsKey(k));
        if (CollectionUtils.isNotEmpty(errorIsvQnhAppIdSet)){
            log.error("getIsvQnhAppIdToAppInfoMap异常，tenantId={}, channelId={}, isvQnhAppIdSet={}未查询到appPoiCode", tenantId, channelId, errorIsvQnhAppIdSet);
        }

        return qnhAppIdToAppInfoMap;
    }

    @Override
    public GetPoiIdsResponse batchGetPoiIdsBySysParam(BatchGetPoiIdsRequest req) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());
        long tenantId = req.getTenantId();
        int channelId = req.getChannelId();
        BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(channelId);
        Map<String, Object> sysParam = JSON.parseObject(req.getSysParams());
        // 查询门店列表
        Map<String, Object> shopListMap = mtBrandChannelGateService.sendGet(shoplist, null, baseRequest,sysParam,
                new Object());
        if (shopListMap == null || shopListMap.get(ProjectConstant.ERROR) != null) {
            // 一个应用查询失败，直接全部失败
            log.warn("查询门店列表失败 tenantId: {} - channelId: {} - sysParam: {}", tenantId, channelId,sysParam);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表失败"));
        }
        JSONArray shopIdJsonArray = (JSONArray) shopListMap.get(ProjectConstant.DATA);
        if (shopIdJsonArray == null || shopIdJsonArray.isEmpty()) {
            // 门店列表为空为正常逻辑
            log.info("查询门店列表为空 tenantId: {} - channelId: {}", tenantId, channelId);
            resp.setStoreIds(Collections.emptyList());
            return resp;
        }
        List<String> totalShopIds = shopIdJsonArray.toJavaList(String.class);
        resp.setStoreIds(totalShopIds);
        return resp;
    }

    @Override
    public GetPoiInfoResponse batchGetPoiDetails(BatchGetPoiDetailsRequest request) {
        GetPoiInfoResponse resp = new GetPoiInfoResponse();

        long tenantId = request.getTenantId();
        int channelId = request.getChannelId();
        List<String> appPoiCodeList = request.getStoreIds();
        if (CollectionUtils.isEmpty(appPoiCodeList)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "需传入门店的开放平台编码"));
        }

        List<PoiInfo> poiInfoListInAllApps = new ArrayList<>();

        // 根据是否传入 appId 进行处理。
        // 注：当初始化拉取信息时，一定需要传入 appId，因为此时渠道门店信息表中还没有数据，也就不能根据 appPoiCode 去关联了
        if (request.isSetAppId()) {
            try {
                long appId = request.getAppId();
                List<PoiInfo> poiInfoList = getPoiInfoInApp(tenantId, channelId, appId, appPoiCodeList, request.isAsyncInvoke());
                if (CollectionUtils.isNotEmpty(poiInfoList)) {
                    poiInfoListInAllApps.addAll(poiInfoList);
                }
            } catch (InvokeChannelTooMuchException e) {
                return resp.setStatus(new ResultStatus(ResultCodeEnum.TRIGGER_LIMIT.getValue(), "", String.valueOf(e.getWaitTimeMills())));
            }
        } else {
            // 根据 appPoiCode 获取 appId
            Map<String, Long> poiCodeAppIdMapping = poiCode2AppId(tenantId, channelId, appPoiCodeList);

            // 按照 appId 将 appPoiCode 分组
            Map<Long, List<String>> appPoiCodeGroupByAppIdMap = new HashMap<>();
            for (Map.Entry<String, Long> entry : poiCodeAppIdMapping.entrySet()) {
                String appPoiCode = entry.getKey();
                Long appId = entry.getValue();
                List<String> list = appPoiCodeGroupByAppIdMap.getOrDefault(appId, new ArrayList<>());
                list.add(appPoiCode);
                appPoiCodeGroupByAppIdMap.put(appId, list);
            }

            // 按照 appId 维度进行查询
            for (Map.Entry<Long, List<String>> entry : appPoiCodeGroupByAppIdMap.entrySet()) {
                long appId = entry.getKey();
                List<String> appPoiCodeListInAppId = entry.getValue();
                try {
                    List<PoiInfo> poiInfoList = getPoiInfoInApp(tenantId, channelId, appId, appPoiCodeListInAppId, request.isAsyncInvoke());
                    if (CollectionUtils.isNotEmpty(poiInfoList)) {
                        poiInfoListInAllApps.addAll(poiInfoList);
                    }
                } catch (InvokeChannelTooMuchException e) {
                    return resp.setStatus(new ResultStatus(ResultCodeEnum.TRIGGER_LIMIT.getValue(), "", String.valueOf(e.getWaitTimeMills())));
                }
            }
        }

        if (poiInfoListInAllApps.isEmpty()) {
            log.warn("查询门店列表为空 tenantId: {} - channelId: {} - appPoiCodeList: {}", tenantId, channelId, appPoiCodeList);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询门店列表为空"));
        }
        resp.setStatus(ResultGenerator.genSuccessResult());
        resp.setPoiInfoList(poiInfoListInAllApps);
        return resp;
    }

    /**
     * 获取某个品牌下的渠道应用信息
     *
     * @param tenantId 租户id
     * @param channelId 渠道id
     * @param appId 品牌应用id
     * @param appPoiCodeListInAppId 渠道商家门店编码列表
     * @param isAsyncInvoke 是否异步，限流用参数
     * @return 渠道应用信息列表
     */
    private List<PoiInfo> getPoiInfoInApp(long tenantId, int channelId, long appId, List<String> appPoiCodeListInAppId, boolean isAsyncInvoke) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);
        Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
        String mtAppIdKey = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

        List<List<String>> shopIdsPartition = Lists.partition(appPoiCodeListInAppId, LIST_PARTITION_NUM);
        List<PoiInfo> poiInfoList = shopIdsPartition.parallelStream().map(shopIds -> {
            String appPoiCodes = String.join(",", shopIds);
            Map<String, Object> param = Maps.newHashMap();
            param.put(ProjectConstant.APP_POI_CODES, appPoiCodes);
            // 限频控制
            rateLimitManage(mtAppIdKey, ChannelPostMTEnum.BATCH_GET_POIDETAIL, isAsyncInvoke);

            Map<String, Object> poiListMap = mtBrandChannelGateService.sendGet(shopget, null, baseRequest, param);
            if (MapUtils.isEmpty(poiListMap) || Objects.isNull(poiListMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(poiListMap.get(ProjectConstant.DATA))) {
                return null;
            }
            JSONArray poiJsonArray = (JSONArray) poiListMap.get(ProjectConstant.DATA);
            List<ChannelPoiInfo> channelPoiInfos = poiJsonArray.toJavaList(ChannelPoiInfo.class);
            List<PoiInfo> poiInfos = mtConverterService.poiInfoListMapping(channelPoiInfos);
            fillAppId(poiInfos, appId, mtAppIdKey);
            return poiInfos;
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        return poiInfoList;
    }

    /**
     * 获取根据渠道的开放平台门店Id，查询渠道的外卖门店Id
     *
     * @param request
     * @return
     */
    @Override
    public GetPoiIdsResponse getPoiInnerId(BatchGetPoiDetailsRequest request) {
        GetPoiIdsResponse resp = new GetPoiIdsResponse().setStatus(ResultGenerator.genSuccessResult());

        long tenantId = request.getTenantId();
        int channelId = request.getChannelId();
        List<String> appPoiCodeList = request.getStoreIds();
        if (CollectionUtils.isEmpty(appPoiCodeList)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "需传入门店的开放平台编码"));
        }
        // 这个接口暂只处理一个门店的情况
        String appPoiCode = appPoiCodeList.get(0);

        long appId;
        // 根据是否传入 appId 进行处理。
        // 注：当初始化拉取信息时，一定需要传入 appId，因为此时渠道门店信息表中还没有数据，也就不能根据 appPoiCode 去关联了
        if (request.isSetAppId()) {
            appId = request.getAppId();
        } else {
            // 根据 appPoiCode 获取 appId
            Map<String, Long> poiCodeAppIdMapping = poiCode2AppId(tenantId, channelId, appPoiCodeList);
            appId = poiCodeAppIdMapping.get(appPoiCode);
        }

        BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);
        Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
        String mtAppIdKey = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

        // 查询渠道门店的外卖平台门店ID
        List<String> wmPoiIds;
        try {
            Map<String, Object> param = Maps.newHashMap();
            param.put(ProjectConstant.APP_POI_CODE, appPoiCode);
            param.put(ProjectConstant.MT_POI_PAGE_NUM, 1);
            param.put(ProjectConstant.MT_POI_PAGE_SIZE, 1);
            // 限频控制
            rateLimitManage(mtAppIdKey, ChannelPostMTEnum.GET_POI_INNER_ID, request.isAsyncInvoke());

            Map<String, Object> idListMap = mtBrandChannelGateService.sendGet(mtUrlBase + "/ecommerce/poi/bound/list", null, baseRequest, param);
            String wmPoiId;
            if (idListMap == null || idListMap.get(ProjectConstant.ERROR) != null) {
                JSONObject jsonObject = (JSONObject) idListMap.get(ProjectConstant.ERROR);
                //isv 没有权限调用 该接口 当绑定门店时不填写code，这两个code相同
                if(708 == (Integer)jsonObject.get("code")) {
                    wmPoiId = appPoiCode;
                } else {
                    // 发送大象消息
//                    dxPushMessageProducer.pushPoiBindMessage(tenantId,String.valueOf(appId),channelId,appPoiCode,"获取外卖门店ID失败");
                    return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询渠道的外卖门店Id失败"));
                }
            } else {
                JSONArray shopIdJsonArray = (JSONArray) idListMap.get(ProjectConstant.DATA);
                if (shopIdJsonArray == null || shopIdJsonArray.isEmpty()) {
//                    dxPushMessageProducer.pushPoiBindMessage(tenantId,String.valueOf(appId),channelId,appPoiCode,"获取外卖门店ID失败");
                    return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询渠道的外卖门店Id失败"));
                }
                List<ChannelPoiMappingInfo> poiMappingInfo = shopIdJsonArray.toJavaList(ChannelPoiMappingInfo.class);
                wmPoiId = poiMappingInfo.get(0).getWm_poi_id().toString();
                if (StringUtils.isEmpty(wmPoiId)) {
//                    dxPushMessageProducer.pushPoiBindMessage(tenantId,String.valueOf(appId),channelId,appPoiCode,"获取外卖门店ID为空");
                }
            }
            wmPoiIds = Lists.newArrayList(wmPoiId);
        } catch (InvokeChannelTooMuchException e) {
            return resp.setStatus(new ResultStatus(ResultCodeEnum.TRIGGER_LIMIT.getValue(), "", String.valueOf(e.getWaitTimeMills())));
        }
        return resp.setStoreIds(wmPoiIds);
    }

    /**
     * 获取租户、渠道、poiCode 维度下，poiCode 到 appId 的映射信息
     * 当从渠道门店基础信息表中获取不到关联数据，且租户只关联一个品牌时，进行兜底
     *
     * @param tenantId 租户id
     * @param channelId 渠道id
     * @param poiCodeList 商家门店编码列表
     * @return
     */
    private Map<String, Long> poiCode2AppId(long tenantId, int channelId, List<String> poiCodeList) {
        Map<String, Long> poiCodeAppIdMapping = copChannelStoreService.getPoiCodeAppIdMapping(tenantId, channelId, poiCodeList);
        if (poiCodeAppIdMapping == null || poiCodeAppIdMapping.isEmpty() || poiCodeAppIdMapping.size() != poiCodeList.size()) {
            // 如果在渠道门店基础表中不存在数据，这里进行兜底，目前已知的一种情况是：菜大全增量拉取渠道门店
            // 或者存在部分数据时也进行兜底
            List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
            if (copAccessConfigDOList == null || copAccessConfigDOList.size() != 1) {
                // 租户维度只有单个品牌才能进行兜底，这里查询为空，或者超过一条记录，都认为是非法数据
                throw new IllegalArgumentException("租户关联的品牌数为空或超过一个，不能获取对应密钥信息");
            } else {
                Long appId = copAccessConfigDOList.get(0).getAppId();
                return poiCodeList.stream().collect(Collectors.toMap(poiCode -> poiCode, poiCode -> appId));
            }
        } else {
            return poiCodeAppIdMapping;
        }
    }

    @Override
    public ResultStatus poiOpen(ChannelPoiIdRequest req) {
        ArrayList<Long> storeIdList = new ArrayList<>();
        if(req.getStoreId() > 0) {
            storeIdList.add(req.getStoreId());
        }
        Map<String, Object> sysParam =
                mtBrandChannelGateService.getSysParam(new BaseRequest().setChannelId(req.getChannelId())
                        .setTenantId(req.getTenantId()).setStoreIdList(storeIdList));
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

        // 限频控制
        if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.SHOP_OPEN, appId)) {
            log.warn("Call blocking failed frequently, Continue");
        }

        Map<String,String> bizParam = Maps.newHashMapWithExpectedSize(1);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());

        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(),storeIdList,false);
        Map<String, Object> respMap = mtBrandChannelGateService.sendGet(mtUrlBase + "/poi/open", null, baseRequest, bizParam);

        if (MapUtils.isEmpty(respMap)) {
            return ResultGenerator.genResult(ResultCode.FAIL, "门店营业状态修改失败");
        }

        if (respMap.get(ProjectConstant.ERROR) != null) {
            JSONObject error = (JSONObject)respMap.get(ProjectConstant.ERROR);
            return ResultGenerator.genResult(ResultCode.FAIL, error.getString("msg"));
        }
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus poiClose(ChannelPoiIdRequest req) {
        ArrayList<Long> storeIdList = new ArrayList<>();
        if(req.getStoreId() > 0) {
            storeIdList.add(req.getStoreId());
        }
        Map<String, Object> sysParam =
                mtBrandChannelGateService.getSysParam(new BaseRequest().setChannelId(req.getChannelId())
                        .setTenantId(req.getTenantId()).setStoreIdList(storeIdList));
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

        // 限频控制
        if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.SHOP_CLOSE, appId)) {
            log.warn("Call blocking failed frequently, Continue");
        }

        Map<String,String> bizParam = Maps.newHashMapWithExpectedSize(1);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());

        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(),storeIdList,false);
        Map<String, Object> respMap = mtBrandChannelGateService.sendGet(mtUrlBase + "/poi/close", null, baseRequest, bizParam);

        if (MapUtils.isEmpty(respMap)) {
            return ResultGenerator.genResult(ResultCode.FAIL, "门店营业状态修改失败");
        }

        if (respMap.get(ProjectConstant.ERROR) != null) {
            JSONObject error = (JSONObject)respMap.get(ProjectConstant.ERROR);
            return ResultGenerator.genResult(ResultCode.FAIL, error.getString("msg"));
        }
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus promotionInfoUpdate(PoiPromotionInfoUpdateRequest req) {
        ArrayList<Long> storeIdList = new ArrayList<>();
        if(req.getStoreId() > 0) {
            storeIdList.add(req.getStoreId());
        }
        Map<String, Object> sysParam =
                mtBrandChannelGateService.getSysParam(new BaseRequest().setChannelId(req.getChannelId())
                        .setTenantId(req.getTenantId()).setStoreIdList(storeIdList));
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

        // 限频控制
        if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.SHOP_PROMOTION_SET, appId)) {
            log.warn("Call blocking failed frequently, Continue");
        }

        Map<String,String> bizParam = Maps.newHashMapWithExpectedSize(1);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());
        if (StringUtils.isNotEmpty(req.getPromotionInfo())) {
            bizParam.put(ProjectConstant.MT_PROMOTION_INFO, req.getPromotionInfo());
        }


        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(),storeIdList,false);
        Map<String, Object> respMap = mtBrandChannelGateService.sendPost(mtUrlBase + "/poi/updatepromoteinfo", null, baseRequest, bizParam);

        if (respMap.get(ProjectConstant.ERROR) != null) {
            JSONObject error = (JSONObject)respMap.get(ProjectConstant.ERROR);
            return ResultGenerator.genResult(ResultCode.FAIL, error.getString("msg"));
        }
        return ResultGenerator.genSuccessResult();
    }

    /**
     * 营业时间更新
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus shippingTimeUpdate(PoiShippingTimeUpdateRequest req) {
        ArrayList<Long> storeIdList = new ArrayList<>();
        if(req.getStoreId() > 0) {
            storeIdList.add(req.getStoreId());
        }
        Map<String, Object> sysParam =
                mtBrandChannelGateService.getSysParam(new BaseRequest().setChannelId(req.getChannelId())
                        .setTenantId(req.getTenantId()).setStoreIdList(storeIdList));
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

        // 限频控制
        if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.SHOP_SHIPPING_TIME_SET, appId)) {
            log.warn("Call blocking failed frequently, Continue");
        }

        if (CollectionUtils.isEmpty(req.getShippingTimes())) {
            return ResultGenerator.genResult(ResultCode.FAIL, "营业时间为空");
        }

        Map<String,String> bizParam = Maps.newHashMapWithExpectedSize(1);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());

        // 获取 shippingTime 参数
        String shippingTime = convertShippingTime(req.getShippingTimes(), req.getShippingDays());

        bizParam.put(ProjectConstant.MT_SHIPPING_TIME, shippingTime);

        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(),storeIdList,false);
        return updateShopInfo(baseRequest, bizParam);
//        Map<String, Object> respMap = mtChannelGateService.sendGet(mtUrlBase + "/poi/shippingtime/update", null, baseRequest, bizParam);
//
//        if (respMap.get(ProjectConstant.ERROR) != null) {
//            JSONObject error = (JSONObject)respMap.get(ProjectConstant.ERROR);
//            return ResultGenerator.genResult(ResultCode.FAIL, error.getString("msg"));
//        }
//        return ResultGenerator.genSuccessResult();
    }


    /**
     * 使门店接受预订单
     * @param req
     * @return
     */
    @Override
    public ResultStatus prebookStatusOpen(ChannelPoiIdRequest req) {
        ArrayList<Long> storeIdList = new ArrayList<>();
        if(req.getStoreId() > 0) {
            storeIdList.add(req.getStoreId());
        }
        // 构造业务参数
        HashMap<String, String> bizParam = Maps.newHashMapWithExpectedSize(2);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());
        bizParam.put(ProjectConstant.MT_PREBOOK, String.valueOf(ProjectConstant.YES));

        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(),
                storeIdList, false);

        return updateShopInfo(baseRequest, bizParam);
    }

    @Override
    public ResultStatus prebookStatusClose(ChannelPoiIdRequest req) {
        ArrayList<Long> storeIdList = new ArrayList<>();
        if(req.getStoreId() > 0) {
            storeIdList.add(req.getStoreId());
        }
        // 构造业务参数
        HashMap<String, String> bizParam = Maps.newHashMapWithExpectedSize(2);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());
        bizParam.put(ProjectConstant.MT_PREBOOK, String.valueOf(ProjectConstant.NO));

        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(),
                storeIdList, false);

        return updateShopInfo(baseRequest, bizParam);

    }

    /**
     * 更新门店的接受预定的日期范围
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus updatePrebookDays(PoiPrebookDaysUpdateRequest req) {
        ArrayList<Long> storeIdList = new ArrayList<>();
        if(req.getStoreId() > 0) {
            storeIdList.add(req.getStoreId());
        }
        // 构造业务参数
        HashMap<String, String> bizParam = Maps.newHashMapWithExpectedSize(2);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());
        bizParam.put(ProjectConstant.MT_PREBOOK_MIN_DAYS, String.valueOf(req.getPrebookMinDays()));
        bizParam.put(ProjectConstant.MT_PREBOOK_MAX_DAYS, String.valueOf(req.getPrebookMaxDays()));

        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(), storeIdList, false);

        return updateShopInfo(baseRequest, bizParam);
    }

    private ResultStatus updateShopInfo(BaseRequest baseRequest, Map<String, String> bizParam) {
        Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

        // 限频控制
        if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.SHOP_UPDATE_INFO, appId)) {
            log.warn("Call blocking failed frequently, Continue");
        }

        Map<String, Object> respMap = mtBrandChannelGateService.sendPost(mtUrlBase + "/poi/save", null, baseRequest, bizParam);

        if (respMap.get(ProjectConstant.ERROR) != null) {
            JSONObject error = (JSONObject)respMap.get(ProjectConstant.ERROR);
            return ResultGenerator.genResult(ResultCode.FAIL, error.getString("msg"));
        }

        return ResultGenerator.genSuccessResult();
    }

    /**
     * 获取门店公告信息
     *
     * @param req
     * @return
     */
    @Override
    public GetPoiPromotionInfoResponse getPoiPromotionInfo(ChannelPoiIdRequest req) {
        GetPoiPromotionInfoResponse response = new GetPoiPromotionInfoResponse();

        BatchGetPoiDetailsRequest request = new BatchGetPoiDetailsRequest();

        request.setTenantId(req.getTenantId());
        request.setChannelId(req.getChannelId());
        request.setStoreIds(Arrays.asList(req.getChannelPoiCode()));

        GetPoiInfoResponse getPoiInfoResponse = batchGetPoiDetails(request);
        response.setStatus(getPoiInfoResponse.getStatus());
        if (CollectionUtils.isNotEmpty(getPoiInfoResponse.getPoiInfoList())) {
            response.setPromotionInfo(getPoiInfoResponse.getPoiInfoList().get(0).getPromotionInfo());
        }

        return response;
    }

    /**
     * 获取门店营业状态
     *
     * @param req
     * @return
     */
    @Override
    public ChannelPoiStatusResponse getPoiStatus(ChannelPoiIdRequest req) {
        ChannelPoiStatusResponse response = new ChannelPoiStatusResponse();

        BatchGetPoiDetailsRequest request = new BatchGetPoiDetailsRequest();

        request.setTenantId(req.getTenantId());
        request.setChannelId(req.getChannelId());
        request.setStoreIds(Arrays.asList(req.getChannelPoiCode()));

        GetPoiInfoResponse getPoiInfoResponse = batchGetPoiDetails(request);
        response.setStatus(getPoiInfoResponse.getStatus());
        if (CollectionUtils.isNotEmpty(getPoiInfoResponse.getPoiInfoList())) {
            response.setPoiStatus(getPoiInfoResponse.getPoiInfoList().get(0).getOpenLevel());
        }

        return response;
    }

    /**
     * 门店授权
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus poiAuth(ChannelPoiAuthRequest req) {
        return poiBindOrUnBind(req,1);
    }

    /**
     * 门店解析授权
     *
     * @param req
     * @return
     */
    @Override
    public ResultStatus poiDeAuth(ChannelPoiAuthRequest req) {
        return poiBindOrUnBind(req,2);
    }

    @Override
    public ResultStatus poiHotlineUpdate(ChannelPoiHotlineRequest req) {
        ArrayList<Long> storeIdList = new ArrayList<>();
        if(req.getChannelId() > 0) {
            storeIdList.add(req.getStoreId());
        }
        Map<String,String> bizParam = Maps.newHashMapWithExpectedSize(1);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getChannelPoiCode());
        bizParam.put(ProjectConstant.MT_HOTLINE, req.getHotline());

        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(),storeIdList,false);
        return updateShopInfo(baseRequest, bizParam);
    }

    @Override
    public QueryPoiAuthDetailResponse queryPoiAuthDetailInfo(BaseChannelPoiRequestSimple request) {
        return new QueryPoiAuthDetailResponse().setStatus(ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能"));
    }

    @Override
    public ResultStatus updatePoiAuthInfo(UpdatePoiAuthInfoRequest request) {
        return ResultGenerator.genResult(ResultCode.FAIL, "渠道暂不支持此功能");
    }

    @Override
    public ResultStatus updatePoiAddress(PoiAddressUpdateRequest request) {
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public UpdateSaleafterAddressMessageResponse updateSaleafterAddressMessage(UpdateSaleafterAddressMessageRequest request) {
        return ResultGenerator.UpdateSafeAddressResultData(ResultCode.FAIL, "美团渠道暂不支持此功能");
    }


    private ResultStatus poiBindOrUnBind(ChannelPoiAuthRequest req, int type) {
        BaseRequest baseRequest = new BaseRequest(req.getTenantId(), req.getChannelId(), null, false);
        Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

        // 限频控制
        if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.SHOP_AUTH, appId)) {
            log.warn("Call blocking failed frequently, Continue");
        }

        Map<String,Object> bizParam = Maps.newHashMapWithExpectedSize(3);
        bizParam.put(ProjectConstant.TYPE, type);
        bizParam.put(ProjectConstant.APP_POI_CODE, req.getStoreCode());
        bizParam.put(ProjectConstant.APP_POI_ID, req.getStoreId());

        Map<String, Object> respMap = mtBrandChannelGateService.sendPost(mtUrlBase + "/ecommerce/poi/bind/app/by/account", null, baseRequest, bizParam);

        if (respMap.get(ProjectConstant.ERROR) != null) {
            JSONObject error = (JSONObject)respMap.get(ProjectConstant.ERROR);
            return ResultGenerator.genResult(ResultCode.FAIL, error.getString("msg"));
        }

        return ResultGenerator.genSuccessResult();
    }


    private String convertShippingTime(List<ShippingTime> shippingTimes, Set<Integer> shippingDays) {
        String shippingTime = StringUtils.join(shippingTimes.stream()
                .map(s -> s.getStartTime() + "-" + s.getEndTime()).sorted().collect(Collectors.toList()), ",");
        StringBuilder dayShippingTime = new StringBuilder();
        if (CollectionUtils.isNotEmpty(shippingDays)) {
            for (int i = 0; i < 7; ++i) {
                if (shippingDays.contains(i)) {
                    dayShippingTime.append(shippingTime);
                }
                dayShippingTime.append(";");
            }
            dayShippingTime.deleteCharAt(dayShippingTime.length() - 1);
        } else {
            dayShippingTime.append(shippingTime);
        }
        return dayShippingTime.toString();
    }

    /**
     * 限频控制
     * @param appId
     * @param postEnum
     * @param isAsync
     */
    private void rateLimitManage(String appId, ChannelPostMTEnum postEnum, Boolean isAsync) {
        if (StringUtils.isBlank(appId)) {
            return;
        }
        long waitTime = clusterRateLimiter.tryAcquire(postEnum, appId, isAsync);
        // 同步调用出现限频时，不进行限频优化
        if (waitTime != 0 && !isAsync) {
            log.warn("Call blocking failed frequently, Continue");
            return;
        }
        if (waitTime !=0) {
            if (RHINO_UPTIMATE_SET.contains(postEnum)) {
                log.info("MtBrandChannelPoiService appId:{} url:{} 未获取到令牌 抛出异常", appId, postEnum.getUrl());
                throw new InvokeChannelTooMuchException(waitTime);
            } else {
                log.warn("Call blocking failed frequently, Continue");
            }
        }
    }

    /**
     * 在 PoiInfo 中填充 appId 信息
     *
     * @param poiInfos
     * @param appId
     */
    private void fillAppId(List<PoiInfo> poiInfos, long appId, String appKey) {
        if (CollectionUtils.isEmpty(poiInfos)) {
            return;
        }
        for (PoiInfo poiInfo : poiInfos) {
            poiInfo.setAppId(appId);
            poiInfo.setAppKey(appKey);
        }
    }

    public ResultStatus appOnline(Integer newAppId) {
        Map<String,Object> bizParam = Maps.newHashMapWithExpectedSize(1);
        bizParam.put(ProjectConstant.MT_NEW_APP_ID_KEY, newAppId);
        BaseRequest baseRequest = new BaseRequest(0, ChannelType.MEITUAN.getValue(), new ArrayList<>(),false);
        Map<String, Object> templateAppSysParam = MccConfigUtil.getTemplateAppSysParam();
        Map<String, Object> respMap = mtBrandChannelGateService.sendPost( mtUrlBase + "/ecommerce/market/appOnline",
                null, baseRequest, bizParam, templateAppSysParam);

        if (respMap.get(ProjectConstant.MT_ERROR_RESPONSE_ERROR_LIST_KEY) != null) {
            JSONArray error = (JSONArray)respMap.get(ProjectConstant.MT_ERROR_RESPONSE_ERROR_LIST_KEY);
            return ResultGenerator.genResult(ResultCode.FAIL, error.toString());
        }
        return ResultGenerator.genSuccessResult();
    }

    public ResultStatus getAppSecret(Integer newAppId, String authorizeCode) {
        Map<String,Object> bizParam = Maps.newHashMapWithExpectedSize(2);
        bizParam.put(ProjectConstant.MT_NEW_APP_ID_KEY, newAppId);
        bizParam.put(ProjectConstant.MT_AUTHORIZE_CODE, authorizeCode);
        BaseRequest baseRequest = new BaseRequest(0, ChannelType.MEITUAN.getValue(), new ArrayList<>(),false);
        Map<String, Object> templateAppSysParam = MccConfigUtil.getTemplateAppSysParam();
        Map<String, Object> respMap = mtBrandChannelGateService.sendPost(mtUrlBase + "/ecommerce/market/getAppSecret",
                null, baseRequest, bizParam, templateAppSysParam);

        if (respMap.get(ProjectConstant.MT_ERROR_RESPONSE_ERROR_LIST_KEY) != null) {
            JSONArray error = (JSONArray)respMap.get(ProjectConstant.MT_ERROR_RESPONSE_ERROR_LIST_KEY);
            return ResultGenerator.genResult(ResultCode.FAIL, error.toString());
        }
        ResultStatus resultStatus = ResultGenerator.genSuccessResult();
        JSONObject successMap = (JSONObject) respMap.get(ProjectConstant.MT_SUCCESS_MAP_KEY);
        resultStatus.setData(successMap.getString("secret"));
        return resultStatus;
    }

    public ResultStatus createApp(MtAppDTO mtAppDTO) {
        Map<String, Object> bizParam = JSONObject.parseObject(JSONObject.toJSONString(mtAppDTO));
        BaseRequest baseRequest = new BaseRequest(0, ChannelType.MEITUAN.getValue(), new ArrayList<>(), false);
        Map<String, Object> templateAppSysParam = MccConfigUtil.getCreateAppTemplateParam(mtAppDTO.getAppId());
        if (MapUtils.isEmpty(templateAppSysParam)) {
            return ResultGenerator.genResult(ResultCode.FAIL, "appId有误");
        }

        Map<String, Object> respMap = mtBrandChannelGateService.sendPost(mtCreateAppBase + "/api/app/createApp", null, baseRequest, bizParam, templateAppSysParam);
        if(MapUtils.isEmpty(respMap)){
            return ResultGenerator.genResult(ResultCode.FAIL,"调用开放平台创建应用失败,请检查接口参数是否填写正确");
        }

        if (!respMap.containsKey(ProjectConstant.CODE)) {
            return ResultGenerator.genResult(ResultCode.FAIL,"调用开放平台创建应用返回code为空,请联系开放平台排查问题");
        }

        int code = Integer.parseInt(respMap.get(ProjectConstant.CODE).toString());
        if (code != ResultCode.SUCCESS.getCode()) {
            return ResultGenerator.genResult(ResultCode.FAIL, respMap.get(ProjectConstant.MSG) == null ? "创建应用失败" : respMap.get(ProjectConstant.MSG));
        }

        ResultStatus resultStatus = ResultGenerator.genSuccessResult();
        Object data = respMap.get(ProjectConstant.DATA);
        if (Objects.isNull(data)) {
            return ResultGenerator.genResult(ResultCode.FAIL, "调用开放平台创建应用返回appId为空,请联系开放平台排查问题");
        }
        resultStatus.setData(data.toString());
        return resultStatus;
    }

    @Override
    public ChannelPoiCategoryResponse queryChannelPoiCategory(ChannelPoiCategoryRequest request) {
        log.info("MtChannelPoiServiceImpl.queryChannelPoiCategory request={}", request);
        ChannelPoiCategoryResponse response = new ChannelPoiCategoryResponse();
        long tenantId = request.getTenantId();
        int channelId = request.getChannelId();
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId,
                channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOList)) {
            log.warn("获取租户品牌数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            response.setCode(ResultCode.FAIL.getCode());
            response.setMsg("获取租户品牌数据失败");
            return response;
        }

        // 按渠道/品牌应用维度进行查询
        List<ChannelPoiCategoryDTO> data = new ArrayList<>();
        for (CopAccessConfigDO copAccessConfigDO : copAccessConfigDOList) {
            long appId = Optional.ofNullable(copAccessConfigDO.getAppId()).orElse(1L);
            BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);
            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            String mtAppIdKey = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

            // 限频控制
            if (StringUtils.isNotBlank(mtAppIdKey)
                    && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.GET_POI_CATEGORY_LIST, mtAppIdKey)) {
                log.warn("queryChannelPoiCategory 获取令牌失败 不阻塞流程 直接调用接口");
            }

            List<ChannelPoiCategoryDTO> appData = getChannelPoiCategoryDTOList(baseRequest, request, mtAppIdKey);
            data.addAll(appData);
        }

        response.setCode(ResultCode.SUCCESS.getCode());
        response.setMsg(ResultCode.SUCCESS.getMsg());
        response.setData(data);
        log.info("MtChannelPoiServiceImpl.queryChannelPoiCategory response={}", response);
        return response;
    }

    private List<ChannelPoiCategoryDTO> getChannelPoiCategoryDTOList(BaseRequest baseRequest,
            ChannelPoiCategoryRequest request, String appId) {
        // 获取门店详细信息
        List<ChannelPoiInfo> poiInfoList = getPoiInfoList(baseRequest, request.getChannelPoiCodeList(), appId);
        if (CollectionUtils.isEmpty(poiInfoList)) {
            return new ArrayList<>();
        }

        Map<String, List<String>> channelPoiCodeToThirdTagNameMap = Fun.toMapQuietly(poiInfoList,
                ChannelPoiInfo::getApp_poi_code, k -> Optional.ofNullable(k.getTag_name())
                        .map(p -> Arrays.asList(p.split(","))).orElse(new ArrayList<>()));

        // 查询全量品类树
        ChannelPoiCategoryTree categoryTree = getChannelPoiCategoryTree(baseRequest, appId);

        Map<String, ChannelPoiCategoryOneInfo> cat2ToCat1Map = getCat2ToCat1Map(categoryTree);

        // 末级品类换取一级品类信息
        List<ChannelPoiCategoryDTO> data = request.getChannelPoiCodeList().stream().flatMap(k -> {
            // 获取末级品类
            List<String> tagNameList = channelPoiCodeToThirdTagNameMap.get(k);
            if (CollectionUtils.isNotEmpty(tagNameList)) {
                return tagNameList.stream().map(tagName -> {
                    ChannelPoiCategoryDTO channelPoiCategoryDTO = new ChannelPoiCategoryDTO();
                    channelPoiCategoryDTO.setChannelId(request.getChannelId());
                    channelPoiCategoryDTO.setChannelPoiCode(k);

                    Optional<ChannelPoiCategoryOneInfo> oneInfo = Optional.ofNullable(cat2ToCat1Map.get(tagName));
                    channelPoiCategoryDTO
                            .setCat1Id(oneInfo.map(ChannelPoiCategoryOneInfo::getLevel_one_code).orElse(null));
                    channelPoiCategoryDTO
                            .setCat1Name(oneInfo.map(ChannelPoiCategoryOneInfo::getLevel_one_name).orElse(null));
                    return channelPoiCategoryDTO;
                });
            }
            return Stream.empty();
        }).collect(Collectors.toList());
        return data;
    }

    private ChannelPoiCategoryTree getChannelPoiCategoryTree(BaseRequest baseRequest, String appId) {
        // 限频控制
        if (StringUtils.isNotBlank(appId)
                && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.GET_POI_CATEGORY_LIST, appId)) {
            log.warn("Call blocking failed frequently, Continue");
        }
        Map categoryListMap = mtBrandChannelGateService.sendGet(categoryList, null,
                baseRequest, Maps.newHashMap());
        if (MapUtils.isEmpty(categoryListMap) || Objects.isNull(categoryListMap.get(ProjectConstant.DATA))
                || ProjectConstant.NG.equals(categoryListMap.get(ProjectConstant.DATA))) {
            log.info("{} 返回数据为空", categoryList);
            return null;
        }
        JSONObject categoryJsonarray = (JSONObject)categoryListMap.get(ProjectConstant.DATA);
        ChannelPoiCategoryTree categoryTree = categoryJsonarray.toJavaObject(ChannelPoiCategoryTree.class);
        return categoryTree;
    }

    /**
     * 末级品类To一级品类的Map
     *
     * @param categoryTree
     * @return
     */
    Map<String, ChannelPoiCategoryOneInfo> getCat2ToCat1Map(ChannelPoiCategoryTree categoryTree) {
        Map<String, ChannelPoiCategoryOneInfo> cat2ToCat1Map = new HashMap<>();
        if (categoryTree == null) {
            return cat2ToCat1Map;
        }
        if (CollectionUtils.isNotEmpty(categoryTree.getSingle_category_tree())) {
            Map<String, ChannelPoiCategoryOneInfo> singleCat2ToCat1Map = categoryTree.getSingle_category_tree().stream()
                    .flatMap(p -> p.getLevel_two_info().stream()
                            .map(q -> new AbstractMap.SimpleEntry<>(q.getLevel_two_name(), p)))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue,
                            (o1, o2) -> o1));
            cat2ToCat1Map.putAll(singleCat2ToCat1Map);
        }
        if (CollectionUtils.isNotEmpty(categoryTree.getSingle_category_tree())) {
            Map<String, ChannelPoiCategoryOneInfo> multiCat2ToCat1Map = categoryTree.getMulti_category_tree().stream()
                    .flatMap(p -> p.getLevel_two_info().stream()
                            .map(q -> new AbstractMap.SimpleEntry<>(q.getLevel_two_name(), p)))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue,
                            (o1, o2) -> o1));
            cat2ToCat1Map.putAll(multiCat2ToCat1Map);
        }
        return cat2ToCat1Map;
    }

    @Override
    public List<ChannelPoiInfo> queryChannelPoiInfoList(long tenantId, List<String> channelPoiCode, int channelId) {
        List<CopAccessConfigDO> copAccessConfigDOList = copAccessConfigService.findTenantChannelConfigApp(tenantId, channelId);
        if (CollectionUtils.isEmpty(copAccessConfigDOList)) {
            log.warn("获取租户品牌数据失败 tenantId: {} - channelId: {}", tenantId, channelId);
            return Lists.newArrayList();
        }

        List<ChannelPoiInfo> channelPoiInfoList = Lists.newArrayList();
        for (CopAccessConfigDO copAccessConfigDO : copAccessConfigDOList) {
            long appId = copAccessConfigDO.getAppId() == null ? 1L : copAccessConfigDO.getAppId();
            BaseRequest baseRequest = new BaseRequest().setChannelId(channelId).setTenantId(tenantId).setAppId(appId);
            Map<String, Object> sysParam = mtBrandChannelGateService.getSysParam(baseRequest);
            String mtAppIdKey = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
            channelPoiInfoList.addAll(Optional.ofNullable(getPoiInfoList(baseRequest, channelPoiCode, mtAppIdKey)).orElse(Lists.newArrayList()));
        }

        return channelPoiInfoList;
    }

    private List<ChannelPoiInfo> getPoiInfoList(BaseRequest baseRequest, List<String> channelPoiCodeList,
            String appId) {
        List<List<String>> shopIds_partition = Lists.partition(channelPoiCodeList, LIST_PARTITION_NUM);
        List<ChannelPoiInfo> poiInfoList = shopIds_partition.parallelStream().map(shopIds -> {
            String app_poi_codes = String.join(",", shopIds);
            Map param = Maps.newHashMap();
            param.put(ProjectConstant.APP_POI_CODES, app_poi_codes);
            // 限频控制
            if (StringUtils.isNotBlank(appId)
                    && !clusterRateLimiter.tryAcquire(ChannelPostMTEnum.BATCH_GET_POIDETAIL, appId)) {
                log.warn("Call blocking failed frequently, Continue");
            }
            Map poiListMap = mtBrandChannelGateService.sendGet(shopget, null, baseRequest,
                    param);
            if (MapUtils.isEmpty(poiListMap) || Objects.isNull(poiListMap.get(ProjectConstant.DATA))
                    || ProjectConstant.NG.equals(poiListMap.get(ProjectConstant.DATA))) {
                log.info("{} 返回数据为空，param={}", shopget, param);
                return null;
            }
            JSONArray poiJsonarray = (JSONArray)poiListMap.get(ProjectConstant.DATA);
            List<ChannelPoiInfo> channelPoiInfos = poiJsonarray.toJavaList(ChannelPoiInfo.class);
            return channelPoiInfos;
        }).filter(result -> CollectionUtils.isNotEmpty(result)).flatMap(Collection::stream)
                .collect(Collectors.toList());
        return poiInfoList;
    }

}

