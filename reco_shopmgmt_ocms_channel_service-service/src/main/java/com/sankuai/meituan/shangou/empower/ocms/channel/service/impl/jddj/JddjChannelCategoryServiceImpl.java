package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Strings;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.CopAccessConfigMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCategoryService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrValueRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrValueResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryDegradeRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoSortItemDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryLevelAdjustRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryPoiResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySmartSortQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySmartSortQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySmartSortSwitchRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySortRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryTopRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CreateCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KmsUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.CategoryAttrValueTypeEnum;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 饿了么渠道商品分类内部服务接口
 *
 * <AUTHOR>
 * @create 2019-01-09 下午4:39
 **/
@Service("jddjChannelCategoryService")
public class JddjChannelCategoryServiceImpl implements ChannelCategoryService {

    @Value("${jddj.url.base}" + "${jddj.url.catlist}")
    private String catlist;
    @Value("${jddj.url.base}" + "${jddj.url.categoryCreate}")
    private String categoryCreate;
    @Value("${jddj.url.base}" + "${jddj.url.categoryUpdate}")
    private String categoryUpdate;
    @Value("${jddj.url.base}" + "${jddj.url.categorySaleAttr}")
    private String categorySaleAttr;
    @Value("${jddj.url.base}" + "${jddj.url.categoryAttrList}")
    private String categoryAttrList;
    @Value("${jddj.url.base}" + "${jddj.url.recommendBrandAndCate}")
    private String recommendBrandUrl;

    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private JddjConverterService jddjConverterService;

    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private CommonLogger log;

    @Autowired
    private CopAccessConfigMapper copAccessConfigMapper;

    @Autowired
    private JddjChannelAppIdUtils jddjChannelAppIdUtils;

    @Autowired
    private TenantService tenantService;

    @Override
    public CreateCategoryResponse createCategory(CategoryRequest request) {
        log.info("开始创建京东到家前台分类 CategoryRequest:{}", request);
        CreateCategoryResponse resp = new CreateCategoryResponse();
        List<CategoryPoiResult> categoryPoiResults = Lists.newArrayList();

        Map<String, Object> sysParams = safeGetSysParam(request.getBaseInfo());
        String appId = String.valueOf(sysParams.get(ProjectConstant.JDDJ_APP_KEY));
        for (CategoryInfoDTO categoryInfoDTO : request.getParamList()) {
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setCode(categoryInfoDTO.getCode());
            Map createParam = Maps.newHashMap();
            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }
            createParam.put("shopCategoryName", categoryInfoDTO.getName());
            createParam.put("shopCategoryLevel", categoryInfoDTO.getLevel());
            if (StringUtils.isNotEmpty(categoryInfoDTO.getChannelParentCode())) {
                createParam.put("pid", categoryInfoDTO.getChannelParentCode());
            } else {
                createParam.put("pid", 0);
            }

            // 限频检查
            if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.CATEGORY_CREATE, Strings.of("{}_{}",
                    request.getBaseInfo().getTenantId(), appId))) {
                log.warn("创建京东到家前台分类 获取令牌失败 categoryInfoDTO：{}", categoryInfoDTO);
                categoryPoiResults.add(categoryPoiResult.setResultCode(ResultCode.TRIGGER_LIMIT.getCode()).setMsg(ResultCode.TRIGGER_LIMIT.getMsg()));
                continue;
            }
            Map createCategoryMap = jddjChannelGateService.sendPost(categoryCreate, null, baseConverterService.baseRequest(request.getBaseInfo()), createParam, sysParams);
            JSONObject createCategoryJsonobj = JSON.parseObject(String.valueOf(createCategoryMap.get(ProjectConstant.DATA)));
            if ((createCategoryJsonobj.getInteger(ProjectConstant.CODE) != null)
                    && (createCategoryJsonobj.getInteger(ProjectConstant.CODE) == 0)) {
                String id = null;
                if (createCategoryJsonobj.containsKey("success")) {
                    //线上环境
                    id = createCategoryJsonobj.getJSONObject("result").getString("id");
                } else {
                    //mock地址
                    id = categoryPoiResult.getCode();
                }

                categoryPoiResult.setResultCode(0)
                        .setChannelCode(id);
            } else {
                categoryPoiResult.setResultCode(1)
                        .setMsg(createCategoryJsonobj.getString("msg"));
            }
            categoryPoiResults.add(categoryPoiResult);
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    @Override
    public UpdateCategoryResponse updateCategory(CategoryUpdateRequest request) {
        log.info("开始更新京东到家前台分类 CategoryRequest:{}", request);

        // Map<String, Object> sysParams = jddjChannelGateService.getChannelSysParams(new BaseRequest().setChannelId(request.getBaseInfo().getChannelId()).setTenantId(request.getBaseInfo().getTenantId()));
        Map<String, Object> sysParams = safeGetSysParam(request.getBaseInfo());
        String appId = String.valueOf(sysParams.get(ProjectConstant.JDDJ_APP_KEY));
        UpdateCategoryResponse resp = new UpdateCategoryResponse();
        List<CategoryPoiResult> categoryPoiResults = Lists.newArrayList();
        for (CategoryInfoUpdateDTO categoryInfoDTO : request.getParamList()) {
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setCode(categoryInfoDTO.getCode());

            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }

            Map updateParam = Maps.newHashMap();
            updateParam.put("id", categoryInfoDTO.getChannelCategoryCode());
            updateParam.put("shopCategoryName", categoryInfoDTO.getName());

            // 限频检查
            if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.CATEGORY_UPDATE, Strings.of("{}_{}",
                    request.getBaseInfo().getTenantId(), appId))) {
                log.warn("修改京东到家前台分类 获取令牌失败 不阻塞流程 直接调用接口， categoryInfoDTO：{}", categoryInfoDTO);
            }
            Map updateCategoryMap = jddjChannelGateService.sendPost(categoryUpdate, null, baseConverterService.baseRequest(request.getBaseInfo()), updateParam, sysParams);
            JSONObject updateCategoryJsonobj = JSON.parseObject(String.valueOf(updateCategoryMap.get(ProjectConstant.DATA)));
            if ((updateCategoryJsonobj.getInteger(ProjectConstant.CODE) == null)
                    || (updateCategoryJsonobj.getInteger(ProjectConstant.CODE) != 0)) {
                String msg = updateCategoryJsonobj.getString("msg");
                if (msg.contains("店内分类名称或类型未修改")) {
                    categoryPoiResult.setResultCode(0);
                } else {
                    categoryPoiResult.setResultCode(1)
                            .setMsg(updateCategoryJsonobj.getString("msg")).setChannelCode(String.valueOf(request.getBaseInfo().getChannelId()));
                }
            } else {
                categoryPoiResult.setResultCode(0);
            }
            categoryPoiResults.add(categoryPoiResult);
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    @Override
    public UpdateCategoryResponse deleteCategory(CategoryDeleteRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        request.getParamList().forEach(data -> {
            try {
                // 调用渠道接口
                // ChannelResponseDTO postResult = jddjChannelGateService.sendPost(ChannelPostJDDJEnum.CATEGORY_DELETE,request.getBaseInfo(), null, jddjConverterService.deleteCategory(data));
                // 注意：因为原有sendPost方法在底层实现如果传入了门店id，那会返回map对象（必异常），因此可以确认此方法之前都是没有传递门店id的
                BaseRequest baseRequest = new BaseRequest();
                baseRequest.setTenantId(request.getBaseInfo().getTenantId());
                baseRequest.setChannelId(request.getBaseInfo().getChannelId());
                int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
                baseRequest.setAppId(appId);
                baseRequest.setErpTenant(request.getBaseInfo().isErpTenant());
                ChannelResponseDTO postResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.
                        CATEGORY_DELETE, baseRequest, jddjConverterService.deleteCategory(data));

                // 组装返回结果
                ResultDataUtils.combineJddjResultData(resultData, postResult, data.getChannelCategoryCode());

            } catch (IllegalArgumentException e) {
                log.error("JddjChannelCategoryServiceImpl.deleteCategory 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getChannelCategoryCode());

            } catch (Exception e) {
                log.error("JddjChannelCategoryServiceImpl.deleteCategory 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getChannelCategoryCode());
            }
        });

        return convertResultData(resultData);
    }

    @Override
    public UpdateCategoryResponse sortCategory(CategorySortRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

        request.getParamList().forEach(data -> {
            List<String> bizKeyList = null;
            try {
                // 返回结果组装用标识
                bizKeyList = data.getSortItemList().stream().map(CategoryInfoSortItemDTO::getChannelCategoryCode).collect(Collectors.toList());

                // 调用渠道接口
                // ChannelResponseDTO postResult = jddjChannelGateService.sendPost(ChannelPostJDDJEnum.CATEGORY_SORT, request.getBaseInfo(), null, jddjConverterService.sortCategory(data));
                // 注意：因为原有sendPost方法在底层实现如果传入了门店id，那会返回map对象（必异常），因此可以确认此方法之前都是没有传递门店id的
                BaseRequest baseRequest = new BaseRequest();
                baseRequest.setTenantId(request.getBaseInfo().getTenantId());
                baseRequest.setChannelId(request.getBaseInfo().getChannelId());
                baseRequest.setErpTenant(request.getBaseInfo().isErpTenant());
                int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
                baseRequest.setAppId(appId);
                ChannelResponseDTO postResult = jddjChannelGateService.sendPostAppDto(ChannelPostJDDJEnum.CATEGORY_SORT, baseRequest, jddjConverterService.sortCategory(data));

                if (postResult.getDataResponse() != null) {
                    postResult.setSuccess(postResult.getDataResponse().getSuccess());
                    postResult.setCode(postResult.getDataResponse().getCode());
                    postResult.setMsg(postResult.getDataResponse().getMsg());
                }

                // 组装返回结果
                ResultDataUtils.combineResultDataList(resultData, postResult, bizKeyList);

                if (CollectionUtils.isNotEmpty(resultData.getErrorData())) {
                    for (ResultErrorSku resultErrorSku : resultData.getErrorData()) {
                        resultErrorSku.setStoreId(data.getStoreId());
                    }
                }

            } catch (IllegalArgumentException e) {
                log.error("JddjChannelCategoryServiceImpl.sortCategory 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);

            } catch (Exception e) {
                log.error("JddjChannelCategoryServiceImpl.sortCategory 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, bizKeyList);
            }
        });

        return convertResultData(resultData);
    }

    @Override
    public GetCategoryResponse batchGetCategory(CatRequest req) {
        GetCategoryResponse resp = new GetCategoryResponse();
        List<String> fields = Lists.newArrayList();
        fields.add(ProjectConstant.JDDJ_FIELD_ID);
        fields.add(ProjectConstant.JDDJ_FIELD_CATEGORY_NAME);
        fields.add(ProjectConstant.JDDJ_FIELD_PID);
        fields.add(ProjectConstant.JDDJ_FIELD_CATEGORY_LEVEL);
        fields.add(ProjectConstant.JDDJ_FIELD_CATEGORY_STATUS);
        fields.add(ProjectConstant.JDDJ_FIELD_CATEGORY_LEAF);
        if (BooleanUtils.isTrue(req.isCheckUpc())) {
            fields.add(ProjectConstant.JDDJ_FIELD_CHECK_UPC_STATUS);
        }
        List catInfoList = Lists.newArrayList();
        for (String id : req.getIds()) {
            Map param = new HashMap();
            param.put(ProjectConstant.ID, id);
            param.put(ProjectConstant.FIELDS, fields);
            // Map catMap = jddjChannelGateService.sendPost(catlist, null, baseConverterService.baseRequest(req.getBaseInfo()), param);
            BaseRequest baseRequest = new BaseRequest();
            baseRequest.setTenantId(req.getBaseInfo().getTenantId());
            baseRequest.setChannelId(req.getBaseInfo().getChannelId());
            int appId = jddjChannelAppIdUtils.safeGetChannelAppId(req.getBaseInfo().getTenantId(), req.getBaseInfo().getAppId());
            baseRequest.setAppId(appId);
            baseRequest.setErpTenant(req.getBaseInfo().isErpTenant());
            Map catMap = jddjChannelGateService.sendPostApp(catlist, null, baseRequest, param);
            if (Integer.parseInt((String) catMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询类目信息失败"));
            }
            String catJsonstr = (String) catMap.get(ProjectConstant.DATA);
            if(StringUtils.isBlank(JSON.parseObject(catJsonstr).getString(ProjectConstant.RESULT))){
                continue;
            }
            List<ChannelCatInfo> channelCatInfos = JSON.parseObject(catJsonstr).getJSONArray(ProjectConstant.RESULT).toJavaList(ChannelCatInfo.class);
            for (ChannelCatInfo channelCatInfo : channelCatInfos) {
                if (channelCatInfo.getCategoryStatus() != ProjectConstant.JDDJ_CATEGORY_VALID) {
                    continue;
                }
                CatInfo catInfo = new CatInfo();
                catInfo.setCatId(channelCatInfo.getId());
                catInfo.setName(channelCatInfo.getCategoryName());
                catInfo.setParentId(channelCatInfo.getPid());
                catInfo.setDepth(channelCatInfo.getCategoryLevel() + 1);
                catInfo.setIsLeaf(Objects.equals(1, channelCatInfo.getLeaf()));
                if (Objects.nonNull(channelCatInfo.getCheckUPCStatus())) {
                    catInfo.setCheckUpcStatus(channelCatInfo.getCheckUPCStatus());
                }

                catInfoList.add(catInfo);
            }
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(catInfoList);
    }

    @Override
    public GetCategoryResponse getSecondCategoryByParentId(CatRequest req) {
        return new GetCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE)).setCatInfoList(Collections.emptyList());
    }

    @Override
    public CategoryProductRulesResponse getCategoryProductRules(CategoryAttrRequest request) {
        return new CategoryProductRulesResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE)).setRuleList(Collections.emptyList());
    }

    @Override
    public CategoryAttrResponse getCategoryAttr(CategoryAttrRequest request) {
        CategoryAttrResponse resp = new CategoryAttrResponse();
        //加密获取accessToken
        String accessToken = KmsUtils.getStringValueByKeyName("kms_ocmschannel_access_token");
        if (StringUtils.isBlank(accessToken)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取accessToken的Kms加密信息失败"));
        }
        Map<String, Object> param = new HashMap<>();
        param.put("categoryId", request.getCategoryId());
        param.put("source", "000005");
        param.put("accessToken", accessToken);

        // 支持京东多应用
        int appId = jddjChannelAppIdUtils.safeGetChannelAppId(request.getBaseInfo().getTenantId(), request.getBaseInfo().getAppId());
        request.getBaseInfo().setAppId(appId);

        if (Objects.equals(request.getAttrType(), ChannelCategoryAttrTypeEnum.NORMAL)) {
            Map catMap = jddjChannelGateService.sendPostApp(categoryAttrList, null, baseConverterService.baseRequest(request.getBaseInfo()), param);
            if (catMap == null || Integer.parseInt((String) catMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "根据平台类目id查询类目属性信息失败"));
            }
            String catAttrListStr = (String) catMap.get(ProjectConstant.DATA);
            if (StringUtils.isBlank(catAttrListStr)) {
                return resp;
            }

            JSONObject jsonObject = JSON.parseObject(catAttrListStr);
            if (jsonObject == null || jsonObject.getJSONObject(ProjectConstant.RESULT) == null) {
                return resp;
            }
            JddjSkuCategoryAttrResult channelCatAttrInfo = JSON.parseObject(catAttrListStr).getJSONObject(ProjectConstant.RESULT).toJavaObject(JddjSkuCategoryAttrResult.class);
            resp.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));
            if (channelCatAttrInfo == null) {
                return resp;
            }
            resp.setGeneralAttrList(channelCatAttrInfo.convert2Info());
            return resp;
        } else {
            Map catMap = jddjChannelGateService.sendPostApp(categorySaleAttr, null, baseConverterService.baseRequest(request.getBaseInfo()), param);
            if (catMap == null || Integer.parseInt((String) catMap.get(ProjectConstant.CODE)) != ResultCode.SUCCESS.getCode()) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "根据平台类目id查询类目销售属性失败"));
            }
            String catAttrListStr = (String) catMap.get(ProjectConstant.DATA);
            List<CategorySaleAttrResult> channelCatAttrInfoList = JSON.parseObject(catAttrListStr).getJSONArray(ProjectConstant.RESULT).toJavaList(CategorySaleAttrResult.class);
            resp.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));
            if (CollectionUtils.isEmpty(channelCatAttrInfoList)) {
                return resp;
            }
            resp.setSaleAttrList(channelCatAttrInfoList.stream().map(CategorySaleAttrResult::convert2Info).collect(Collectors.toList()));
            return resp;
        }
    }

    @Override
    public CategoryAttrValueResponse getCategoryAttrValue(CategoryAttrValueRequest request) {
        return new CategoryAttrValueResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    @Deprecated
    public UpdateCategoryResponse degradeCategory(CategoryDegradeRequest request) {
        UpdateCategoryResponse resp = new UpdateCategoryResponse().setStatus(ResultGenerator.genSuccessResult()).setData(Lists.newArrayList());
        request.getParamList().forEach(data -> {
            /* 删除一级分类 */
            CategoryDeleteRequest deleteRequst = new CategoryDeleteRequest()
                    .setBaseInfo(request.getBaseInfo())
                    .setParamList(Lists.newArrayList(baseConverterService.degradeCategoryDel(data)));

            UpdateCategoryResponse delResp = deleteCategory(deleteRequst);
            if (ResultCode.SUCCESS.getCode() == delResp.status.code && delResp.getData().get(0).getResultCode() == 0) {
                /* 删除成功后创建二级分类 */
                CategoryRequest createRequst = new CategoryRequest()
                        .setParamList(Lists.newArrayList(baseConverterService.degradeCategory(data)))
                        .setBaseInfo(request.getBaseInfo());
                CreateCategoryResponse createResp = createCategory(createRequst);
                resp.getData().addAll(createResp.getData());
            } else {
                resp.getData().addAll(delResp.getData());
            }
        });

        return resp;
    }

    @Override
    public UpdateCategoryResponse adjustCategoryLevel(CategoryLevelAdjustRequest request) {
        UpdateCategoryResponse resp = new UpdateCategoryResponse().setStatus(ResultGenerator.genSuccessResult()).setData(Lists.newArrayList());
        request.getParamList().forEach(data -> {
            /* 删除原等级分类 */
            CategoryDeleteRequest deleteRequst = new CategoryDeleteRequest()
                    .setBaseInfo(request.getBaseInfo())
                    .setParamList(Lists.newArrayList(baseConverterService.adjustCategoryLevelDel(data)));

            UpdateCategoryResponse delResp = deleteCategory(deleteRequst);
            if (ResultCode.SUCCESS.getCode() == delResp.status.code && delResp.getData().get(0).getResultCode() == 0) {
                /* 创建目标等级分类 */
                CategoryInfoDTO categoryInfoDTO = null;
                switch (data.getAdjustType()) {
                    case UPGRADE:
                        categoryInfoDTO = baseConverterService.upgradeCategoryLevel(data);
                        break;
                    case DEGRADE:
                        categoryInfoDTO = baseConverterService.degradeCategoryLevel(data);
                        break;
                    default:
                        break;
                }
                CategoryRequest createRequst = new CategoryRequest()
                        .setParamList(Lists.newArrayList(categoryInfoDTO))
                        .setBaseInfo(request.getBaseInfo());
                CreateCategoryResponse createResp = createCategory(createRequst);

                resp.getData().addAll(createResp.getData());
            } else {
                resp.getData().addAll(delResp.getData());
            }
        });

        return resp;
    }

    /**
     * 将ResultData转换为UpdateCategoryResponse
     *
     * @param resultData
     * @return
     */
    private UpdateCategoryResponse convertResultData(ResultData resultData) {
        UpdateCategoryResponse resp = baseConverterService.toUpdateCategoryResponse(resultData);
        resp.getData().addAll(baseConverterService.toUpdateCategoryResponseSuc(resultData.getSucData()));

        return resp;
    }

    @Override
    public UpdateCategoryResponse updateCategoryChannelCode(CategoryUpdateRequest request) {
        // ignore impl
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.SUCCESS))
                .setData(Collections.emptyList());
    }

    @Override
    public UpdateCategoryResponse updateSmartSortSwitch(CategorySmartSortSwitchRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.emptyList());
    }

    @Override
    public CategorySmartSortQueryResponse queryStoreCategorySmartSort(CategorySmartSortQueryRequest request) {
        return new CategorySmartSortQueryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.emptyList());
    }

    @Override
    public RecommendCategoryResponse recommendCategory(RecommendCategoryRequest request) {
        RecommendCategoryResponse resp;
        resp = getRecommendCategoryCode(request);
        if (resp.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
            return resp;
        } else if (resp.getChannelCategoryCode() == 0) {
            log.warn("JDDJ recommendCategory return 0 request:{}:", request);
            //京东渠道类目接口不稳定，有时会返回null,如果是空就进行一次重试
            resp = getRecommendCategoryCode(request);
        }
        return resp;
    }

    private RecommendCategoryResponse getRecommendCategoryCode(RecommendCategoryRequest request) {
        RecommendCategoryResponse resp = new RecommendCategoryResponse();

        List fields = Lists.newArrayList();
        fields.add(ProjectConstant.JDDJ_FIELD_CATEGORY);
        Map param = new HashMap();
        param.put(ProjectConstant.PRODUCT_NAME, request.getName());
        param.put(ProjectConstant.FIELDS, fields);
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        if (baseRequest.getAppId() <= 0) {
            List<Integer> appIdList = copAccessConfigMapper.queryChannelAppIds(baseRequest.getTenantId(), baseRequest.getChannelId());
            if (CollectionUtils.isEmpty(appIdList)) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "租户未配置该渠道应用"));
            }
            baseRequest.setAppId(appIdList.get(0).longValue());
        }
        Map<String, Object> sysParams = jddjChannelGateService.getSysParamByChannelIdAndAppId(baseRequest.getTenantId(), baseRequest.getChannelId(), baseRequest.getAppId());
        String appId = String.valueOf(sysParams.get(ProjectConstant.JDDJ_APP_KEY));

        // 限频检查
        if (org.apache.commons.lang3.StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.RECOMMEND_CATE_BRAND, String.valueOf(request.getBaseInfo().getTenantId()))) {
            log.warn("按名称查询推荐类目，获取令牌失败，request {}", param);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.TRIGGER_LIMIT, ResultCode.TRIGGER_LIMIT.getMsg()));
        }

        Map brandMap = jddjChannelGateService.sendPostApp(recommendBrandUrl, null, baseRequest, param);
        if (Integer.parseInt((String) brandMap.get(ProjectConstant.CODE)) != 0) {
            String msg = (String) brandMap.get(ProjectConstant.MSG);
            String message = String.format("查询推荐类目失败,message:%s",msg);
            log.warn(message);
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, message));
        }
        String brandJsonstr = (String) brandMap.get(ProjectConstant.DATA);
        JSONObject brandJson = JSON.parseObject(brandJsonstr).getJSONObject(ProjectConstant.RESULT);

        Long categoryId = brandJson.getLong(ProjectConstant.JDDJ_FIELD_CATEGORYID);
        resp.setStatus(new ResultStatus(ResultCode.SUCCESS.getCode(), "成功", null));
        resp.setUpcCode(request.getUpcCode());
        resp.setName(request.getName());
        if(categoryId != null){
            resp.setChannelCategoryCode(categoryId);
        }
        return resp;
    }

    @Override
    public UpdateCategoryResponse topCategory(CategoryTopRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public GetCategoryResponse queryStoreCategoryList(CatRequest request) {
        return null;
    }

    private Map<String, Object> safeGetSysParam(BaseRequestSimple baseRequest) {
        if (baseRequest.isSetAppId() && baseRequest.getAppId() > 0) {
            return jddjChannelGateService.getSysParamByChannelIdAndAppId(baseRequest.getTenantId(), baseRequest.getChannelId(), baseRequest.getAppId());
        }
        return jddjChannelGateService.getChannelSysParams(new BaseRequest().setChannelId(baseRequest.getChannelId()).setTenantId(baseRequest.getTenantId()));
    }


}
