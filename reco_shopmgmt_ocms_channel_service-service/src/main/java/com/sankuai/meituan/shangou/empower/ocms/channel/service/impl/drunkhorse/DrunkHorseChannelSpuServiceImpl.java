package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSpuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelSpuServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2022/01/03
 */
@Service("mtDrunkHorseChannelSpuService")
public class DrunkHorseChannelSpuServiceImpl extends MtChannelSpuServiceImpl implements ChannelSpuService {
    @Value("${mt_drunkhorse.url.base}" + "${mt.url.spuDetail}")
    private String spuDetail;

    @Value("${mt_drunkhorse.url.base}" + "/retail/batchget")
    private String spuDetails;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.spuList}")
    private String skuList;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.spuAuditStatus}")
    private String spuAuditStatus;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.recommendChannelCategory}")
    private String recommendChannelCategoryUrl;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.batchGetAppSpuCodesBySkuIds}")
    private String batchGetAppSpuCodesBySkuIds;

    @Value("${mt_drunkhorse.url.base}" + "${mt.url.headquarterSpuList}")
    private String batchGetHeadQuarterSpuListUrl;

    @Autowired
    @Qualifier("mtDrunkHorseChannelGateService")
    private MtChannelGateService mtChannelGateService;

    @Override
    public String getSpuDetail() {
        return spuDetail;
    }

    @Override
    public String getSpuDetails() {
        return spuDetails;
    }

    @Override
    public String getSkuList() {
        return skuList;
    }

    @Override
    public String getSpuAuditStatus() {
        return spuAuditStatus;
    }

    @Override
    public String getRecommendChannelCategoryUrl() {
        return recommendChannelCategoryUrl;
    }

    @Override
    public String getBatchGetAppSpuCodesBySkuIds() {
        return batchGetAppSpuCodesBySkuIds;
    }

    @Override
    public String getBatchGetHeadQuarterSpuListUrl() {
        return batchGetHeadQuarterSpuListUrl;
    }

    @Override
    public MtChannelGateService getMtChannelGateService() {
        return mtChannelGateService;
    }
}
