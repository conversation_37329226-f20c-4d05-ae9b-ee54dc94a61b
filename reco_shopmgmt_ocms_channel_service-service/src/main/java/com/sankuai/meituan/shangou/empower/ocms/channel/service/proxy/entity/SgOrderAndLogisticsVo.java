package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;


import java.util.List;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021年07月21日18:53:18
 */
public class SgOrderAndLogisticsVo {

    private String result;
    private Long order_id;
    private Float shipping_fee;
    private String app_order_code;
    private String app_poi_code;
    private Long wm_poi_id;
    private String wm_poi_name;
    private String wm_poi_address;
    private String wm_poi_phone;
    private String recipient_name;
    private String recipient_phone;
    private String recipient_address;
    private SgOpenRecipientAddressDetail recipient_address_detail;
    private Double total;
    private String caution;
    private String remark;
    private String shipper_phone;
    private Integer has_invoiced;
    private String invoice_title;
    private Integer status;
    private Integer is_third_shipping;
    private Integer pay_type;
    private Integer is_pre;
    private Integer expect_deliver_time;
    private Long ctime;
    private Long utime;
    private Long source_id;
    private Integer shipping_type;
    private Integer day_seq;
    private Integer dinners_number;
    private Long user_id;
    private Boolean is_favorites;
    private Boolean is_poi_first_order;
    private String poi_receive_detail;
    private String logistics_code;
    private String detail;
    private String extras;
    private Double latitude;
    private Double longitude;
    private Double avg_send_time;
    private Long wm_order_id_view;
    private Double original_price;
    private Long city_id;
    private Long delivery_time;
    private Integer estimate_arrival_time; //新增订单预计送达时间
    private Long order_send_time;
    private Long order_receive_time;
    private Long order_confirm_time;
    private Long order_cancel_time;
    private Long order_completed_time;
    private Integer logistics_status;
    private Integer logistics_id;
    private String logistics_name;
    private Integer logistics_send_time;
    private Integer logistics_confirm_time;
    private Integer logistics_cancel_time;
    private Integer logistics_fetch_time;
    private Integer logistics_completed_time;
    private String logistics_dispatcher_name;
    private String logistics_dispatcher_mobile;
    private Integer pay_status;
    private Integer unpaid_time;
    private Integer paying_time;
    private Integer pay_done_time;
    private Integer refund_apply_time;
    private Integer refund_confirm_time;
    private Integer refund_reject_time;
    private Integer refund_complete_time;
    private String taxpayer_id;
    private Integer pick_type;
    private Integer shipping_service; //配送服务
    private String user_member_info; //用户会员信息
    private Integer package_bag_money; //打包袋
    private String sku_benefit_detail; //商品优惠详情
    private String backup_recipient_phone;//备用隐私号
    private String register_phone;//注册手机号，针对瑞幸添加，由于法律风险，暂不提供真正的注册手机号，此字段值与recipient_phone内容一致
    private String channel; //订单来源属性标识
    private String user_number; //用户代码
    private Long openUid;//开放平台用户ID
    private String package_bag_money_yuan; //打包袋金额，单位元
    private String poi_receive_detail_yuan; //商家应收款详情，单位元
    private Long total_weight;//订单总重量
    private Integer invMakeType;//开发票方式，1美团发票合作商家，在线自动开具电子发票

    private Integer incmp_code;
    private Set<Integer> incmp_modules;
    // 预定人手机号 如果用户设置了隐私保护 手机号是隐私号，否则真实手机号
    private String order_phone_number;

    // 订单业务打标枚举 16-特价版订单
    private Set<Integer> order_tag_list;

    //CPS营销费用
//    private List<ChargeAmount> extendsAmount;

    //是否扫码配送订单
    private Boolean scan_deliver_flag;

    //扫码配送二维码
    private String scan_deliver_qr_content;

    //是否是预售订单
    private Boolean is_pre_sale_order;

    /**
     * 疫情登记信息
     */
    private String patient_collection_info;

    /**
     * buy order new field
     */
    // 是否品牌新客 1-是，0-不是
    private int is_brand_first_order;
    // 支付流水号
    private String trace_no;
    // 订单支付时间
    private Long order_pay_time;
    // 收件人性别
    private Integer recipient_gender;
    /** 订单物流信息列表 */
    private List<SgOpenThirdLogisticsInfo> sg_open_third_logistics_infos;
    // 全城送订单标识 1-是 0-否
    private Integer is_whole_city_ship;

    // 配送费
    private double discount_shipping_fee;

    // 是否使用隐私号
    private int pri_phone;

    // 隐私号
    private String privacy_phone;

    // 备用隐私号
    private List<String> backup_privacy_phones;

    private Integer is_vip;


    public Integer getIs_whole_city_ship() {
        return is_whole_city_ship;
    }

    public void setIs_whole_city_ship(Integer is_whole_city_ship) {
        this.is_whole_city_ship = is_whole_city_ship;
    }

    public List<SgOpenThirdLogisticsInfo> getSg_open_third_logistics_infos() {
        return sg_open_third_logistics_infos;
    }

    public void setSg_open_third_logistics_infos(List<SgOpenThirdLogisticsInfo> sg_open_third_logistics_infos) {
        this.sg_open_third_logistics_infos = sg_open_third_logistics_infos;
    }

    public Integer getRecipient_gender() {
        return recipient_gender;
    }

    public void setRecipient_gender(Integer recipient_gender) {
        this.recipient_gender = recipient_gender;
    }

    public int getIs_brand_first_order() {
        return is_brand_first_order;
    }

    public void setIs_brand_first_order(int is_brand_first_order) {
        this.is_brand_first_order = is_brand_first_order;
    }

    public String getTrace_no() {
        return trace_no;
    }

    public void setTrace_no(String trace_no) {
        this.trace_no = trace_no;
    }

// for push start

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Long getOrder_id() {
        return order_id;
    }

    public void setOrder_id(Long order_id) {
        this.order_id = order_id;
    }

    public Float getShipping_fee() {
        return shipping_fee;
    }

    public void setShipping_fee(Float shipping_fee) {
        this.shipping_fee = shipping_fee;
    }

    public String getApp_order_code() {
        return app_order_code;
    }

    public void setApp_order_code(String app_order_code) {
        this.app_order_code = app_order_code;
    }

    public String getApp_poi_code() {
        return app_poi_code;
    }

    public void setApp_poi_code(String app_poi_code) {
        this.app_poi_code = app_poi_code;
    }

    public Long getWm_poi_id() {
        return wm_poi_id;
    }

    public void setWm_poi_id(Long wm_poi_id) {
        this.wm_poi_id = wm_poi_id;
    }

    public String getWm_poi_name() {
        return wm_poi_name;
    }

    public void setWm_poi_name(String wm_poi_name) {
        this.wm_poi_name = wm_poi_name;
    }

    public String getWm_poi_address() {
        return wm_poi_address;
    }

    public void setWm_poi_address(String wm_poi_address) {
        this.wm_poi_address = wm_poi_address;
    }

    public String getWm_poi_phone() {
        return wm_poi_phone;
    }

    public void setWm_poi_phone(String wm_poi_phone) {
        this.wm_poi_phone = wm_poi_phone;
    }

    public String getRecipient_name() {
        return recipient_name;
    }

    public void setRecipient_name(String recipient_name) {
        this.recipient_name = recipient_name;
    }

    public String getRecipient_phone() {
        return recipient_phone;
    }

    public void setRecipient_phone(String recipient_phone) {
        this.recipient_phone = recipient_phone;
    }

    public String getRecipient_address() {
        return recipient_address;
    }

    public void setRecipient_address(String recipient_address) {
        this.recipient_address = recipient_address;
    }

    public SgOpenRecipientAddressDetail getRecipient_address_detail() {
        return recipient_address_detail;
    }

    public void setRecipient_address_detail(SgOpenRecipientAddressDetail recipient_address_detail) {
        this.recipient_address_detail = recipient_address_detail;
    }

    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public String getCaution() {
        return caution;
    }

    public void setCaution(String caution) {
        this.caution = caution;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getShipper_phone() {
        return shipper_phone;
    }

    public void setShipper_phone(String shipper_phone) {
        this.shipper_phone = shipper_phone;
    }

    public Integer getHas_invoiced() {
        return has_invoiced;
    }

    public void setHas_invoiced(Integer has_invoiced) {
        this.has_invoiced = has_invoiced;
    }

    public String getInvoice_title() {
        return invoice_title;
    }

    public void setInvoice_title(String invoice_title) {
        this.invoice_title = invoice_title;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIs_third_shipping() {
        return is_third_shipping;
    }

    public void setIs_third_shipping(Integer is_third_shipping) {
        this.is_third_shipping = is_third_shipping;
    }

    public Integer getPay_type() {
        return pay_type;
    }

    public void setPay_type(Integer pay_type) {
        this.pay_type = pay_type;
    }

    public Integer getIs_pre() {
        return is_pre;
    }

    public void setIs_pre(Integer is_pre) {
        this.is_pre = is_pre;
    }

    public Integer getExpect_deliver_time() {
        return expect_deliver_time;
    }

    public void setExpect_deliver_time(Integer expect_deliver_time) {
        this.expect_deliver_time = expect_deliver_time;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Long getUtime() {
        return utime;
    }

    public void setUtime(Long utime) {
        this.utime = utime;
    }

    public Long getSource_id() {
        return source_id;
    }

    public void setSource_id(Long source_id) {
        this.source_id = source_id;
    }

    public Integer getShipping_type() {
        return shipping_type;
    }

    public void setShipping_type(Integer shipping_type) {
        this.shipping_type = shipping_type;
    }

    public Integer getDay_seq() {
        return day_seq;
    }

    public void setDay_seq(Integer day_seq) {
        this.day_seq = day_seq;
    }

    public Integer getDinners_number() {
        return dinners_number;
    }

    public void setDinners_number(Integer dinners_number) {
        this.dinners_number = dinners_number;
    }

    public Long getUser_id() {
        return user_id;
    }

    public void setUser_id(Long user_id) {
        this.user_id = user_id;
    }

    public Boolean getIs_favorites() {
        return is_favorites;
    }

    public void setIs_favorites(Boolean is_favorites) {
        this.is_favorites = is_favorites;
    }

    public Boolean getIs_poi_first_order() {
        return is_poi_first_order;
    }

    public void setIs_poi_first_order(Boolean is_poi_first_order) {
        this.is_poi_first_order = is_poi_first_order;
    }

    public String getPoi_receive_detail() {
        return poi_receive_detail;
    }

    public void setPoi_receive_detail(String poi_receive_detail) {
        this.poi_receive_detail = poi_receive_detail;
    }

    public String getLogistics_code() {
        return logistics_code;
    }

    public void setLogistics_code(String logistics_code) {
        this.logistics_code = logistics_code;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getExtras() {
        return extras;
    }

    public void setExtras(String extras) {
        this.extras = extras;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getAvg_send_time() {
        return avg_send_time;
    }

    public void setAvg_send_time(Double avg_send_time) {
        this.avg_send_time = avg_send_time;
    }

    public Long getWm_order_id_view() {
        return wm_order_id_view;
    }

    public void setWm_order_id_view(Long wm_order_id_view) {
        this.wm_order_id_view = wm_order_id_view;
    }

    public Double getOriginal_price() {
        return original_price;
    }

    public void setOriginal_price(Double original_price) {
        this.original_price = original_price;
    }

    public Long getCity_id() {
        return city_id;
    }

    public void setCity_id(Long city_id) {
        this.city_id = city_id;
    }

    public Long getDelivery_time() {
        return delivery_time;
    }

    public void setDelivery_time(Long delivery_time) {
        this.delivery_time = delivery_time;
    }

    public Integer getEstimate_arrival_time() {
        return estimate_arrival_time;
    }

    public void setEstimate_arrival_time(Integer estimate_arrival_time) {
        this.estimate_arrival_time = estimate_arrival_time;
    }

    public Long getOrder_send_time() {
        return order_send_time;
    }

    public void setOrder_send_time(Long order_send_time) {
        this.order_send_time = order_send_time;
    }

    public Long getOrder_receive_time() {
        return order_receive_time;
    }

    public void setOrder_receive_time(Long order_receive_time) {
        this.order_receive_time = order_receive_time;
    }

    public Long getOrder_confirm_time() {
        return order_confirm_time;
    }

    public void setOrder_confirm_time(Long order_confirm_time) {
        this.order_confirm_time = order_confirm_time;
    }

    public Long getOrder_cancel_time() {
        return order_cancel_time;
    }

    public void setOrder_cancel_time(Long order_cancel_time) {
        this.order_cancel_time = order_cancel_time;
    }

    public Long getOrder_completed_time() {
        return order_completed_time;
    }

    public void setOrder_completed_time(Long order_completed_time) {
        this.order_completed_time = order_completed_time;
    }

    public Integer getLogistics_status() {
        return logistics_status;
    }

    public void setLogistics_status(Integer logistics_status) {
        this.logistics_status = logistics_status;
    }

    public Integer getLogistics_id() {
        return logistics_id;
    }

    public void setLogistics_id(Integer logistics_id) {
        this.logistics_id = logistics_id;
    }

    public String getLogistics_name() {
        return logistics_name;
    }

    public void setLogistics_name(String logistics_name) {
        this.logistics_name = logistics_name;
    }

    public Integer getLogistics_send_time() {
        return logistics_send_time;
    }

    public void setLogistics_send_time(Integer logistics_send_time) {
        this.logistics_send_time = logistics_send_time;
    }

    public Integer getLogistics_confirm_time() {
        return logistics_confirm_time;
    }

    public void setLogistics_confirm_time(Integer logistics_confirm_time) {
        this.logistics_confirm_time = logistics_confirm_time;
    }

    public Integer getLogistics_cancel_time() {
        return logistics_cancel_time;
    }

    public void setLogistics_cancel_time(Integer logistics_cancel_time) {
        this.logistics_cancel_time = logistics_cancel_time;
    }

    public Integer getLogistics_fetch_time() {
        return logistics_fetch_time;
    }

    public void setLogistics_fetch_time(Integer logistics_fetch_time) {
        this.logistics_fetch_time = logistics_fetch_time;
    }

    public Integer getLogistics_completed_time() {
        return logistics_completed_time;
    }

    public void setLogistics_completed_time(Integer logistics_completed_time) {
        this.logistics_completed_time = logistics_completed_time;
    }

    public String getLogistics_dispatcher_name() {
        return logistics_dispatcher_name;
    }

    public void setLogistics_dispatcher_name(String logistics_dispatcher_name) {
        this.logistics_dispatcher_name = logistics_dispatcher_name;
    }

    public String getLogistics_dispatcher_mobile() {
        return logistics_dispatcher_mobile;
    }

    public void setLogistics_dispatcher_mobile(String logistics_dispatcher_mobile) {
        this.logistics_dispatcher_mobile = logistics_dispatcher_mobile;
    }

    public Integer getPay_status() {
        return pay_status;
    }

    public void setPay_status(Integer pay_status) {
        this.pay_status = pay_status;
    }

    public Integer getUnpaid_time() {
        return unpaid_time;
    }

    public void setUnpaid_time(Integer unpaid_time) {
        this.unpaid_time = unpaid_time;
    }

    public Integer getPaying_time() {
        return paying_time;
    }

    public void setPaying_time(Integer paying_time) {
        this.paying_time = paying_time;
    }

    public Integer getPay_done_time() {
        return pay_done_time;
    }

    public void setPay_done_time(Integer pay_done_time) {
        this.pay_done_time = pay_done_time;
    }

    public Integer getRefund_apply_time() {
        return refund_apply_time;
    }

    public void setRefund_apply_time(Integer refund_apply_time) {
        this.refund_apply_time = refund_apply_time;
    }

    public Integer getRefund_confirm_time() {
        return refund_confirm_time;
    }

    public void setRefund_confirm_time(Integer refund_confirm_time) {
        this.refund_confirm_time = refund_confirm_time;
    }

    public Integer getRefund_reject_time() {
        return refund_reject_time;
    }

    public void setRefund_reject_time(Integer refund_reject_time) {
        this.refund_reject_time = refund_reject_time;
    }

    public Integer getRefund_complete_time() {
        return refund_complete_time;
    }

    public void setRefund_complete_time(Integer refund_complete_time) {
        this.refund_complete_time = refund_complete_time;
    }

    public String getTaxpayer_id() {
        return taxpayer_id;
    }

    public void setTaxpayer_id(String taxpayer_id) {
        this.taxpayer_id = taxpayer_id;
    }

    public Integer getPick_type() {
        return pick_type;
    }

    public void setPick_type(Integer pick_type) {
        this.pick_type = pick_type;
    }

    public Integer getShipping_service() {
        return shipping_service;
    }

    public void setShipping_service(Integer shipping_service) {
        this.shipping_service = shipping_service;
    }

    public String getUser_member_info() {
        return user_member_info;
    }

    public void setUser_member_info(String user_member_info) {
        this.user_member_info = user_member_info;
    }

    public Integer getPackage_bag_money() {
        return package_bag_money;
    }

    public void setPackage_bag_money(Integer package_bag_money) {
        this.package_bag_money = package_bag_money;
    }

    public String getSku_benefit_detail() {
        return sku_benefit_detail;
    }

    public void setSku_benefit_detail(String sku_benefit_detail) {
        this.sku_benefit_detail = sku_benefit_detail;
    }

    public String getBackup_recipient_phone() {
        return backup_recipient_phone;
    }

    public void setBackup_recipient_phone(String backup_recipient_phone) {
        this.backup_recipient_phone = backup_recipient_phone;
    }

    public String getRegister_phone() {
        return register_phone;
    }

    public void setRegister_phone(String register_phone) {
        this.register_phone = register_phone;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getUser_number() {
        return user_number;
    }

    public void setUser_number(String user_number) {
        this.user_number = user_number;
    }

    public Long getOpenuid() {
        return openUid;
    }

    public void setOpenuid(Long openUid) {
        this.openUid = openUid;
    }

    public String getPackage_bag_money_yuan() {
        return package_bag_money_yuan;
    }

    public void setPackage_bag_money_yuan(String package_bag_money_yuan) {
        this.package_bag_money_yuan = package_bag_money_yuan;
    }

    public String getPoi_receive_detail_yuan() {
        return poi_receive_detail_yuan;
    }

    public void setPoi_receive_detail_yuan(String poi_receive_detail_yuan) {
        this.poi_receive_detail_yuan = poi_receive_detail_yuan;
    }

    public Long getTotal_weight() {
        return total_weight;
    }

    public void setTotal_weight(Long total_weight) {
        this.total_weight = total_weight;
    }

    public Integer getInvmaketype() {
        return invMakeType;
    }

    public void setInvmaketype(Integer invMakeType) {
        this.invMakeType = invMakeType;
    }

    public Integer getIncmp_code() {
        return incmp_code;
    }

    public void setIncmp_code(Integer incmp_code) {
        this.incmp_code = incmp_code;
    }

    public Set<Integer> getIncmp_modules() {
        return incmp_modules;
    }

    public void setIncmp_modules(Set<Integer> incmp_modules) {
        this.incmp_modules = incmp_modules;
    }

    public String getOrder_phone_number() {
        return order_phone_number;
    }

    public void setOrder_phone_number(String order_phone_number) {
        this.order_phone_number = order_phone_number;
    }

    public Set<Integer> getOrder_tag_list() {
        return order_tag_list;
    }

    public void setOrder_tag_list(Set<Integer> order_tag_list) {
        this.order_tag_list = order_tag_list;
    }

    public Boolean getScan_deliver_flag() {
        return scan_deliver_flag;
    }

    public void setScan_deliver_flag(Boolean scan_deliver_flag) {
        this.scan_deliver_flag = scan_deliver_flag;
    }

    public String getScan_deliver_qr_content() {
        return scan_deliver_qr_content;
    }

    public void setScan_deliver_qr_content(String scan_deliver_qr_content) {
        this.scan_deliver_qr_content = scan_deliver_qr_content;
    }

    public Boolean getIs_pre_sale_order() {
        return is_pre_sale_order;
    }

    public void setIs_pre_sale_order(Boolean is_pre_sale_order) {
        this.is_pre_sale_order = is_pre_sale_order;
    }

    public String getPatient_collection_info() {
        return patient_collection_info;
    }

    public void setPatient_collection_info(String patient_collection_info) {
        this.patient_collection_info = patient_collection_info;
    }

    public Long getOrder_pay_time() {
        return order_pay_time;
    }

    public void setOrder_pay_time(Long order_pay_time) {
        this.order_pay_time = order_pay_time;
    }

    public double getDiscount_shipping_fee() {
        return discount_shipping_fee;
    }

    public void setDiscount_shipping_fee(double discount_shipping_fee) {
        this.discount_shipping_fee = discount_shipping_fee;
    }

    public int getPri_phone() {
        return pri_phone;
    }

    public void setPri_phone(int pri_phone) {
        this.pri_phone = pri_phone;
    }

    public String getPrivacy_phone() {
        return privacy_phone;
    }

    public void setPrivacy_phone(String privacy_phone) {
        this.privacy_phone = privacy_phone;
    }

    public List<String> getBackup_privacy_phones() {
        return backup_privacy_phones;
    }

    public void setBackup_privacy_phones(List<String> backup_privacy_phones) {
        this.backup_privacy_phones = backup_privacy_phones;
    }

    public Integer getIs_vip() {
        return is_vip;
    }

    public void setIs_vip(Integer is_vip) {
        this.is_vip = is_vip;
    }

    @Override
    public String toString() {
        return "SgOrderAndLogisticsVo{" +
                "result='" + result + '\'' +
                ", order_id=" + order_id +
                ", shipping_fee=" + shipping_fee +
                ", app_order_code='" + app_order_code + '\'' +
                ", app_poi_code='" + app_poi_code + '\'' +
                ", wm_poi_id=" + wm_poi_id +
                ", wm_poi_name='" + wm_poi_name + '\'' +
                ", wm_poi_address='" + wm_poi_address + '\'' +
                ", wm_poi_phone='" + wm_poi_phone + '\'' +
                ", recipient_name='" + recipient_name + '\'' +
                ", recipient_phone='" + recipient_phone + '\'' +
                ", recipient_address='" + recipient_address + '\'' +
                ", recipient_address_detail=" + recipient_address_detail +
                ", total=" + total +
                ", caution='" + caution + '\'' +
                ", remark='" + remark + '\'' +
                ", shipper_phone='" + shipper_phone + '\'' +
                ", has_invoiced=" + has_invoiced +
                ", invoice_title='" + invoice_title + '\'' +
                ", status=" + status +
                ", is_third_shipping=" + is_third_shipping +
                ", pay_type=" + pay_type +
                ", is_pre=" + is_pre +
                ", expect_deliver_time=" + expect_deliver_time +
                ", ctime=" + ctime +
                ", utime=" + utime +
                ", source_id=" + source_id +
                ", shipping_type=" + shipping_type +
                ", day_seq=" + day_seq +
                ", dinners_number=" + dinners_number +
                ", user_id=" + user_id +
                ", is_favorites=" + is_favorites +
                ", is_poi_first_order=" + is_poi_first_order +
                ", poi_receive_detail='" + poi_receive_detail + '\'' +
                ", logistics_code='" + logistics_code + '\'' +
                ", detail='" + detail + '\'' +
                ", extras='" + extras + '\'' +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", avg_send_time=" + avg_send_time +
                ", wm_order_id_view=" + wm_order_id_view +
                ", original_price=" + original_price +
                ", city_id=" + city_id +
                ", delivery_time=" + delivery_time +
                ", estimate_arrival_time=" + estimate_arrival_time +
                ", order_send_time=" + order_send_time +
                ", order_receive_time=" + order_receive_time +
                ", order_confirm_time=" + order_confirm_time +
                ", order_cancel_time=" + order_cancel_time +
                ", order_completed_time=" + order_completed_time +
                ", logistics_status=" + logistics_status +
                ", logistics_id=" + logistics_id +
                ", logistics_name='" + logistics_name + '\'' +
                ", logistics_send_time=" + logistics_send_time +
                ", logistics_confirm_time=" + logistics_confirm_time +
                ", logistics_cancel_time=" + logistics_cancel_time +
                ", logistics_fetch_time=" + logistics_fetch_time +
                ", logistics_completed_time=" + logistics_completed_time +
                ", logistics_dispatcher_name='" + logistics_dispatcher_name + '\'' +
                ", logistics_dispatcher_mobile='" + logistics_dispatcher_mobile + '\'' +
                ", pay_status=" + pay_status +
                ", unpaid_time=" + unpaid_time +
                ", paying_time=" + paying_time +
                ", pay_done_time=" + pay_done_time +
                ", refund_apply_time=" + refund_apply_time +
                ", refund_confirm_time=" + refund_confirm_time +
                ", refund_reject_time=" + refund_reject_time +
                ", refund_complete_time=" + refund_complete_time +
                ", taxpayer_id='" + taxpayer_id + '\'' +
                ", pick_type=" + pick_type +
                ", shipping_service=" + shipping_service +
                ", user_member_info='" + user_member_info + '\'' +
                ", package_bag_money=" + package_bag_money +
                ", sku_benefit_detail='" + sku_benefit_detail + '\'' +
                ", backup_recipient_phone='" + backup_recipient_phone + '\'' +
                ", register_phone='" + register_phone + '\'' +
                ", channel='" + channel + '\'' +
                ", user_number='" + user_number + '\'' +
                ", openUid=" + openUid +
                ", package_bag_money_yuan='" + package_bag_money_yuan + '\'' +
                ", poi_receive_detail_yuan='" + poi_receive_detail_yuan + '\'' +
                ", total_weight=" + total_weight +
                ", invMakeType=" + invMakeType +
                ", incmp_code=" + incmp_code +
                ", incmp_modules=" + incmp_modules +
                ", order_phone_number='" + order_phone_number + '\'' +
                ", order_tag_list=" + order_tag_list +
                ", scan_deliver_flag=" + scan_deliver_flag +
                ", scan_deliver_qr_content='" + scan_deliver_qr_content + '\'' +
                ", is_pre_sale_order=" + is_pre_sale_order +
                ", patient_collection_info='" + patient_collection_info + '\'' +
                ", is_brand_first_order=" + is_brand_first_order +
                ", trace_no='" + trace_no + '\'' +
                ", order_pay_time=" + order_pay_time +
                ", recipient_gender=" + recipient_gender +
                ", sg_open_third_logistics_infos=" + sg_open_third_logistics_infos +
                ", is_whole_city_ship=" + is_whole_city_ship +
                ", discount_shipping_fee=" + discount_shipping_fee +
                ", pri_phone=" + pri_phone +
                ", privacy_phone='" + privacy_phone + '\'' +
                ", backup_privacy_phones=" + backup_privacy_phones +
                ", is_vip=" + is_vip +
                '}';
    }
}
