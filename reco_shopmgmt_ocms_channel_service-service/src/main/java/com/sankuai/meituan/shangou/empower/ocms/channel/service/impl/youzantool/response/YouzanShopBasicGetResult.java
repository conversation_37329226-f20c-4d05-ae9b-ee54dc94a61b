package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool.response;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/11/29
 */
public class YouzanShopBasicGetResult implements Serializable {
    public static final Long serialVersionUID = 1L;
    @JSONField(
            name = "code"
    )
    private int code;
    @J<PERSON>NField(
            name = "data"
    )
    private YouzanShopBasicGetResult.YouzanShopBasicGetResultData data;
    @JSONField(
            name = "message"
    )
    private String message;
    @J<PERSON>NField(
            name = "success"
    )
    private boolean success;
    @JSONField(
            name = "request_id"
    )
    private String requestId;
    @J<PERSON>NField(
            name = "error_data"
    )
    private HashMap<String, Object> errorData;
    @J<PERSON>NField(
            name = "trace_id"
    )
    private String traceId;

    public YouzanShopBasicGetResult() {
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return this.code;
    }

    public void setData(YouzanShopBasicGetResult.YouzanShopBasicGetResultData data) {
        this.data = data;
    }

    public YouzanShopBasicGetResult.YouzanShopBasicGetResultData getData() {
        return this.data;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMessage() {
        return this.message;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public boolean getSuccess() {
        return this.success;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getRequestId() {
        return this.requestId;
    }

    public void setErrorData(HashMap<String, Object> errorData) {
        this.errorData = errorData;
    }

    public Map<String, Object> getErrorData() {
        return this.errorData;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getTraceId() {
        return this.traceId;
    }

    @Data
    public static class ShopAddress implements Serializable{
        @JSONField(
                name = "lng"
        )
        private String lng;
        @JSONField(
                name = "lat"
        )
        private String lat;
        @JSONField(
                name = "address"
        )
        private String address;

        @JSONField(
                name = "province"
        )
        private String province;

        @JSONField(
                name = "city"
        )
        private String city;


        @JSONField(
                name = "county"
        )
        private String county;

    }

    @Data
    public static class CustomerServicePhoneNumber implements Serializable {
        @JSONField(
                name = "area_code"
        )
        private String areaCode;
        @JSONField(
                name = "phone_number"
        )
        private String phoneNumber;
    }
    public static class YouzanShopBasicGetResultUpgradetypelist implements Serializable{
        @JSONField(
                name = "upgrade_type_name"
        )
        private String upgradeTypeName;
        @JSONField(
                name = "finished_at"
        )
        private String finishedAt;
        @JSONField(
                name = "upgrade_type"
        )
        private Integer upgradeType;

        public YouzanShopBasicGetResultUpgradetypelist() {
        }

        public void setUpgradeTypeName(String upgradeTypeName) {
            this.upgradeTypeName = upgradeTypeName;
        }

        public String getUpgradeTypeName() {
            return this.upgradeTypeName;
        }

        public void setFinishedAt(String finishedAt) {
            this.finishedAt = finishedAt;
        }

        public String getFinishedAt() {
            return this.finishedAt;
        }

        public void setUpgradeType(Integer upgradeType) {
            this.upgradeType = upgradeType;
        }

        public Integer getUpgradeType() {
            return this.upgradeType;
        }
    }

    public static class YouzanShopBasicGetResultData implements Serializable{
        @JSONField(
                name = "physical_url"
        )
        private String physicalUrl;
        @JSONField(
                name = "parent_kdt_id"
        )
        private Long parentKdtId;
        @JSONField(
                name = "role"
        )
        private Integer role;
        @JSONField(
                name = "root_kdt_id"
        )
        private Long rootKdtId;
        @JSONField(
                name = "cert_type"
        )
        private Integer certType;
        @JSONField(
                name = "upgrade_type_list"
        )
        private List<YouzanShopBasicGetResultUpgradetypelist> upgradeTypeList;
        @JSONField(
                name = "name"
        )
        private String name;
        @JSONField(
                name = "logo"
        )
        private String logo;
        @JSONField(
                name = "intro"
        )
        private String intro;
        @JSONField(
                name = "url"
        )
        private String url;
        @JSONField(
                name = "sid"
        )
        private Long sid;
        @JSONField(
                name = "line_of_business"
        )
        private Integer lineOfBusiness;

        @JSONField(
                name = "shop_address"
        )
        private ShopAddress shopAddress;

        @JSONField(
                name = "customer_service_phone_number"
        )
        private CustomerServicePhoneNumber customerServicePhoneNumber;

        public YouzanShopBasicGetResultData() {
        }

        public CustomerServicePhoneNumber getCustomerServicePhoneNumber() {
            return this.customerServicePhoneNumber;
        }

        public void setCustomerServicePhoneNumber(CustomerServicePhoneNumber customerServicePhoneNumber) {
            this.customerServicePhoneNumber = customerServicePhoneNumber;
        }

        public ShopAddress getShopAddress() {
            return shopAddress;
        }

        public void setShopAddress(ShopAddress shopAddress) {
            this.shopAddress = shopAddress;
        }

        public void setPhysicalUrl(String physicalUrl) {
            this.physicalUrl = physicalUrl;
        }

        public String getPhysicalUrl() {
            return this.physicalUrl;
        }

        public void setParentKdtId(Long parentKdtId) {
            this.parentKdtId = parentKdtId;
        }

        public Long getParentKdtId() {
            return this.parentKdtId;
        }

        public void setRole(Integer role) {
            this.role = role;
        }

        public Integer getRole() {
            return this.role;
        }

        public void setRootKdtId(Long rootKdtId) {
            this.rootKdtId = rootKdtId;
        }

        public Long getRootKdtId() {
            return this.rootKdtId;
        }

        public void setCertType(Integer certType) {
            this.certType = certType;
        }

        public Integer getCertType() {
            return this.certType;
        }

        public void setUpgradeTypeList(List<YouzanShopBasicGetResult.YouzanShopBasicGetResultUpgradetypelist> upgradeTypeList) {
            this.upgradeTypeList = upgradeTypeList;
        }

        public List<YouzanShopBasicGetResult.YouzanShopBasicGetResultUpgradetypelist> getUpgradeTypeList() {
            return this.upgradeTypeList;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getName() {
            return this.name;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public String getLogo() {
            return this.logo;
        }

        public void setIntro(String intro) {
            this.intro = intro;
        }

        public String getIntro() {
            return this.intro;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getUrl() {
            return this.url;
        }

        public void setSid(Long sid) {
            this.sid = sid;
        }

        public Long getSid() {
            return this.sid;
        }

        public void setLineOfBusiness(Integer lineOfBusiness) {
            this.lineOfBusiness = lineOfBusiness;
        }

        public Integer getLineOfBusiness() {
            return this.lineOfBusiness;
        }
    }
}
