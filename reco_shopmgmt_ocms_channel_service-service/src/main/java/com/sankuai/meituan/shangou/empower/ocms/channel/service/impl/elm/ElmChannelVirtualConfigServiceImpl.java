package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.BatchSpuCreateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelResponseResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelSkuCategoryProperty;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelSkuCreateBatchResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelSkuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.SkuSpecResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostELMEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelVirtualConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelCategoryThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SkuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuCategoryPropertyResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuCategoryPropertyInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultBuilderUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultSpuDataUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 饿了么渠道虚拟配置内部服务接口
 *
 * <AUTHOR>
 * @since 2023/4/26
 */
@Slf4j
@Service("elmChannelVirtualConfigService")
public class ElmChannelVirtualConfigServiceImpl implements ChannelVirtualConfigService {
    private static final int SPU_BATCH_MAX_COUNT = 50;
    private static final String RECOMMEND_CATEGORY_PROPERTY = "recommendCategoryProperty";

    @Autowired
    private ElmConverterService elmConverterService;
    @Autowired
    private ElmChannelGateService elmChannelGateService;
    @Autowired
    private ClusterRateLimiter clusterRateLimiter;
    @Resource
    private ChannelCategoryThriftServiceProxy channelCategoryThriftServiceProxy;

    @Autowired
    private TenantService tenantService;

    @Value("${elm.url.base}")
    private String baseUrl;

    @Override
    public RecommendCategoryResponse recommendCategoryByVirtualConfig(RecommendCategoryRequest request) {
        return new RecommendCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public ResultSpuData createStoreProductsByVirtualConfig(SpuInfoRequest request) {
        return createStoreProductsByVirtualConfig(request,null);
    }

    @Override
    public ResultSpuData deleteStoreProductsByVirtualConfig(SpuInfoDeleteRequest request) {
        return deleteStoreProductsByVirtualConfig(request, null);
    }

    @Override
    public ResultSpuData createStoreProductsForRecommendCategoryProperty(SpuInfoRequest request) {
        return createStoreProductsByVirtualConfig(request, RECOMMEND_CATEGORY_PROPERTY);
    }

    @Override
    public ResultSpuData deleteStoreProductsForRecommendCategoryProperty(SpuInfoDeleteRequest request) {
        return deleteStoreProductsByVirtualConfig(request, RECOMMEND_CATEGORY_PROPERTY);
    }

    /**
     * 复用ElmChannelSpuService创建渠道商品的逻辑，在获取渠道系统参数的地方有修改
     *
     * @param request
     * @param divideScene 隔离场景，不同调用场景隔离配置参数，防止干扰
     * @return
     */
    public ResultSpuData createStoreProductsByVirtualConfig(SpuInfoRequest request, String divideScene) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultData;
        }
        ListUtils.listPartition(request.getParamList(), SPU_BATCH_MAX_COUNT).forEach(data -> {
            Map<String, SpuKey> upcSpuKeyMap = data.stream().collect(Collectors.toMap(this::convert2Upc, this::convert2Key, (v1, v2) -> v2));
            Map<String, SpuKey> customSpuIdMap = Fun.toMapQuietly(data, SpuInfoDTO::getCustomSpuId, this::convert2Key);
            // 返回结果组装用标识
            List<SpuKey> bizKeyList = Lists.newArrayList(customSpuIdMap.values());
            try {
                BatchSpuCreateDTO createParam = new BatchSpuCreateDTO();
                boolean isConvenienceOrFlagshipStoreMode = tenantService.isConvenienceOrFlagshipStoreMode(request.getBaseInfo().getTenantId());
                boolean isErpSpuTenant = tenantService.isErpSpuTenant(request.getBaseInfo().getTenantId());
                boolean isMedicineUmWareHouse = tenantService.isMedicineAdultUnmanWarehouse(request.getBaseInfo().getTenantId());

                createParam.setCreate_list(data.stream().map(spu -> elmConverterService.convert(spu, false, false,
                                request.getBaseInfo().getTenantId(),
                                channelCategoryThriftServiceProxy.getMultiSpecType(EnhanceChannelType.ELEM.getChannelId(),
                                        request.getBaseInfo().getTenantId(), spu.getChannelCategoryId()), true,
                                isConvenienceOrFlagshipStoreMode, isErpSpuTenant, isMedicineUmWareHouse))
                        .collect(Collectors.toList()));
                Map<Long, ChannelResponseDTO> postResult;
                if(StringUtils.isNotBlank(divideScene)){
                    postResult = elmChannelGateService.sendPostAppMapDtoByDivideScene(ChannelPostELMEnum.BATCH_SKU_CREATE, request.getBaseInfo(), createParam, divideScene);
                }else {
                    // 调用渠道接口
                    postResult = elmChannelGateService.sendPostAppMapDtoByVirtualConfig(ChannelPostELMEnum.BATCH_SKU_CREATE, request.getBaseInfo(), createParam);
                }
                // 解析结果
                postResult.values().forEach(response -> {
                    ChannelResponseResult<ChannelSkuCreateBatchResult> createResult = response.getBody();
                    // 使用upc匹配成功的商品
                    if (createResult.getData() != null && CollectionUtils.isNotEmpty(createResult.getData().getResult_list())) {
                        createResult.getData().getResult_list().forEach(r -> {
                            SpuKey spuKey = Optional.ofNullable(customSpuIdMap.get(r.getCustom_sku_id())).orElse(upcSpuKeyMap.get(r.getUpc()));
                            if (spuKey == null) {
                                return;
                            }
                            //设置饿了么平台返回的末级渠道类目
                            spuKey.setCategoryId(r.getCat3_id());
                            //设置饿了么平台返回的规格id
                            if (CollectionUtils.isNotEmpty(r.getSku_spec_result())) {
                                Map<String, String> customSkuId2ChannelSkuId = r.getSku_spec_result().stream().collect(Collectors.toMap(SkuSpecResult::getSku_spec_custom_id, SkuSpecResult::getSku_spec_id));
                                spuKey.getSkus().forEach(skukey -> {
                                    skukey.setChannelSkuId(customSkuId2ChannelSkuId.get(skukey.getCustomSkuId()));
                                });
                            }
                            bizKeyList.remove(spuKey);
                            resultData.addToSucData(new ResultSuccessSpu().setSpuInfo(spuKey).setChannelResultInfo(r.getSku_id()));
                        });
                    }
                    // 剩余的添加到失败列表
                    bizKeyList.forEach(spuKey -> {
                        ResultErrorSpu errorSpu = new ResultErrorSpu().setSpuInfo(spuKey).setErrorMsg(createResult.getError()).setErrorCode(createResult.getErrno());
                        resultData.addToErrorData(errorSpu);
                    });
                });
            } catch (IllegalArgumentException e) {
                log.error("ElmChannelVirtualConfigServiceImpl.createProductsByVirtualConfig 参数校验失败, data:{}", data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);
            } catch (BizException e) {
                log.warn("ElmChannelVirtualConfigServiceImpl.createProductsByVirtualConfig 业务异常, data:{}", data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, e.getMessage(), bizKeyList);
                if (Objects.nonNull(e.getErrorCode())) {
                    resultData.getStatus().setCode(e.getErrorCode()).setMsg(e.getMessage());
                }
            } catch (Exception e) {
                log.error("ElmChannelVirtualConfigServiceImpl.createProductsByVirtualConfig 服务异常, data:{}", data, e);
                ResultSpuDataUtils.combineExceptionDataList(resultData, ResultCode.UNKNOWN_ERROR.getMsg(), bizKeyList);
            }
        });
        return resultData;
    }

    /**
     * 复用ElmChannelSpuService删除渠道商品的逻辑，仅在获取渠道系统参数的地方有修改
     *
     * @param request
     * @param divideScene 隔离场景，不同调用场景隔离配置参数，防止干扰
     * @return
     */
    public ResultSpuData deleteStoreProductsByVirtualConfig(SpuInfoDeleteRequest request, String divideScene) {
        try {
            BaseRequestSimple baseInfo = request.getBaseInfo();
            ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
            // 按门店分组
            Map<Long, List<SpuInfoDeleteDTO>> storeDeleteMap = request.getParamList().stream().collect(Collectors.groupingBy(SpuInfoDeleteDTO::getStoreId));
            storeDeleteMap.forEach((storeId, list) -> {
                BaseRequest baseRequest = new BaseRequest().setTenantId(baseInfo.getTenantId()).setChannelId(baseInfo.getChannelId()).setStoreIdList(Lists.newArrayList(storeId));
                SkuInfoDeleteRequest deleteRequest = new SkuInfoDeleteRequest();
                deleteRequest.setBaseInfo(baseRequest);
                deleteRequest.setParamList(list.stream().map(e -> new SkuInfoDeleteDTO().setSkuId(e.getCustomSpuId())).collect(Collectors.toList()));
                ResultData skuResult = deleteSkuByVirtualConfig(deleteRequest, divideScene);
                ResultSpuData storeResultSpuData = convert2SpuResult(skuResult);
                ResultStatus status = storeResultSpuData.getStatus();
                if (status.getCode() != ResultCode.SUCCESS.getCode()) {
                    list.forEach(p -> {
                        ResultErrorSpu errorSpu = new ResultErrorSpu().setStoreId(storeId).setErrorMsg(status.getMsg()).setErrorCode(status.getCode()).setSpuInfo(new SpuKey().setCustomSpuId(p.getCustomSpuId()));
                        resultData.addToErrorData(errorSpu);
                    });
                    return;
                }
                if (CollectionUtils.isNotEmpty(storeResultSpuData.getSucData())) {
                    storeResultSpuData.getSucData().forEach(resultData::addToSucData);
                }
                if (CollectionUtils.isNotEmpty(storeResultSpuData.getErrorData())) {
                    storeResultSpuData.getErrorData().forEach(resultData::addToErrorData);
                }
            });
            return resultData;
        } catch (Exception e) {
            log.error("ElmChannelVirtualConfigServiceImpl.deleteStoreProductsByVirtualConfig, 根据虚拟配置批量删除商品SPU异常, request:{}", request, e);
        }
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "根据虚拟配置批量删除商品SPU失败");
    }

    /**
     * 仅支持单门店
     *
     * @param request
     */
    public ResultData deleteSkuByVirtualConfig(SkuInfoDeleteRequest request, String divideScene) {
        BaseRequest baseInfo = request.getBaseInfo();
        ResultData resultData = ResultGenerator.genResultData(ResultCode.SUCCESS);
        Long storeId = baseInfo.getStoreIdList().iterator().next();

        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId())
                .setStoreIdList(Lists.newArrayList(storeId));
        List<String> skuList = request.getParamList().stream().map(SkuInfoDeleteDTO::getSkuId)
                .collect(Collectors.toList());
        Lists.partition(skuList, MccConfigUtil.getElmSkuDeleteBatch()).forEach(data -> {
            ChannelSkuDeleteDTO channelSkuDeleteDTO = new ChannelSkuDeleteDTO();
            channelSkuDeleteDTO.setCustom_sku_id(StringUtils.join(data, ","));
            Map<Long, ChannelResponseDTO> postResult;
            if(StringUtils.isNotBlank(divideScene)){
                postResult = elmChannelGateService.sendPostAppMapDtoByDivideScene(ChannelPostELMEnum.SKU_DELETE, baseRequest, channelSkuDeleteDTO, divideScene);
            }else {
                postResult = elmChannelGateService.sendPostAppMapDtoByVirtualConfig(ChannelPostELMEnum.SKU_DELETE, baseRequest, channelSkuDeleteDTO);
            }
            log.info("ElmChannelVirtualConfigServiceImpl.deleteSkuByVirtualConfig, ELM删除商品接口返回数据 channelId:{}, storeId:{}, s:{}, result:{}",
                    baseInfo.getChannelId(), storeId, data, postResult);
            ResultBuilderUtil.buildSkuDelResult(baseInfo.getChannelId(), storeId, data, postResult, null, resultData);
            try {
                Thread.sleep(200);
            } catch (Exception e) {
                log.error("ElmChannelVirtualConfigServiceImpl.deleteSkuByVirtualConfig sleep异常, request:{}", request, e);
            }
        });
        return resultData;
    }

    /**
     * 通过虚拟配置获取商品类目属性
     * @param request
     * @return
     */
    @Override
    public GetSpuCategoryPropertyResponse getSpuCategoryRecommendPropertyInfo(GetSpuInfoRequest request) {
        GetSpuCategoryPropertyResponse response = new GetSpuCategoryPropertyResponse().setStatus(ResultGenerator.genSuccessResult());
        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SKU_CATEGORY_PROPERTY_QUERY, String.valueOf(request.getBaseInfo().getTenantId()))) {
            log.warn("查询商品类目属性 获取令牌失败 不阻塞流程 直接调用接口， request{}", request);
        }
        Map<String, String> bizParam = Maps.newHashMap();
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(request.getBaseInfo().getTenantId())
                .setChannelId(request.getBaseInfo().getChannelId());
        ChannelStoreDO channelVirtualPoi = elmChannelGateService.getChannelVirtualPoiByDivideScene(baseRequest, RECOMMEND_CATEGORY_PROPERTY);
        bizParam.put("shop_id", channelVirtualPoi.getChannelOnlinePoiCode());
        if (StringUtils.isNotBlank(request.getCustomSpuId())) {
            bizParam.put("custom_sku_id", request.getCustomSpuId());
        }
        if (StringUtils.isNotBlank(request.getUpc())) {
            bizParam.put("upc", request.getUpc());
        }
        if (StringUtils.isNotBlank(request.getChannelSpuId())) {
            bizParam.put("sku_id", request.getChannelSpuId());
        }
        Map<String, Object> categoryPropertyInfoMap = elmChannelGateService.sendPostByDivideScene(baseUrl, "sku.category.property.query", baseRequest, bizParam, RECOMMEND_CATEGORY_PROPERTY);
        // 超时时，会返回 null
        if (Objects.isNull(categoryPropertyInfoMap)) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, ResultCode.FAIL.getMsg()));
        }
        JSONObject categoryPropertyInfoMapBody = (JSONObject) categoryPropertyInfoMap.get(ProjectConstant.BODY);
        if ((int) categoryPropertyInfoMapBody.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, categoryPropertyInfoMapBody.getString(ProjectConstant.ERROR)));
        }
        JSONObject categoryPropertyInfoMapData = categoryPropertyInfoMapBody.getJSONObject(ProjectConstant.DATA);
        if (Objects.isNull(categoryPropertyInfoMapData)) {
            return response;
        }
        ChannelSkuCategoryProperty categoryPropertyInfo = categoryPropertyInfoMapData.toJavaObject(ChannelSkuCategoryProperty.class);
        List<ChannelSkuCategoryProperty.PropValueDTO> itemPropValues = categoryPropertyInfo.getItemPropValues();
        List<SpuCategoryPropertyInfo> propertyList = Fun.map(itemPropValues, each -> {
            SpuCategoryPropertyInfo spuCategoryPropertyInfo = new SpuCategoryPropertyInfo();
            spuCategoryPropertyInfo.setPropId(each.getPropId());
            spuCategoryPropertyInfo.setPropText(each.getPropText());
            spuCategoryPropertyInfo.setValueId(each.getValueId());
            spuCategoryPropertyInfo.setValueText(each.getValueText());
            spuCategoryPropertyInfo.setSaleProp(BooleanUtils.isTrue(each.getSaleProp()));
            return spuCategoryPropertyInfo;
        });
        return response.setStatus(ResultGenerator.genSuccessResult()).setPropertyList(propertyList);
    }

    /**
     * 通过虚拟配置获取门店营业状态
     *
     * @param req
     * @return
     */
    public ChannelPoiStatusResponse getPoiStatusByVirtualConfig(ChannelPoiIdRequest req, String divideScene) {
        ChannelPoiStatusResponse response = new ChannelPoiStatusResponse();
        if (!clusterRateLimiter.tryAcquire(ChannelPostELMEnum.SHOP_BUSINESS_GET, String.valueOf(req.getTenantId()))) {
            log.warn("查询门店列表 获取令牌失败 不阻塞流程 直接调用接口， request{}", req);
        }

        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.SHOP_ID, req.getChannelPoiCode());
        bizParam.put("platformFlag", "1");

        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setChannelId(req.getChannelId());
        baseRequest.setTenantId(req.getTenantId());
        baseRequest.setStoreIdList(Lists.newArrayList(req.getStoreId()));
        Map<String, Object> poiOpenResp;
        String methodUrl = ChannelPostELMEnum.SHOP_BUSINESS_GET.getUrl();
        if(StringUtils.isNotBlank(divideScene)){
            poiOpenResp = elmChannelGateService.sendPostByDivideScene(baseUrl, methodUrl, baseRequest, bizParam, divideScene);
        }else {
            poiOpenResp = elmChannelGateService.sendPostByVirtualConfig(baseUrl, methodUrl, baseRequest, bizParam);
        }

        JSONObject body = (JSONObject) poiOpenResp.get(ProjectConstant.BODY);

        if ((int) body.get(ProjectConstant.ERRNO) != ResultCode.SUCCESS.getCode()) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, (String) body.get(ProjectConstant.ERROR)));
        }

        JSONObject data = (JSONObject) body.get(ProjectConstant.DATA);
        response.setPoiStatus(data.getIntValue(ProjectConstant.ELM_BUSINESS_STATUS));
        response.setStatus(ResultGenerator.genSuccessResult());

        return response;
    }

    private ResultSpuData convert2SpuResult(ResultData skuResultData) {
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        resultData.setStatus(skuResultData.getStatus());
        if (CollectionUtils.isNotEmpty(skuResultData.getErrorData())) {
            skuResultData.getErrorData().forEach(errorData -> {
                SpuKey spuKey = new SpuKey().setCustomSpuId(errorData.getSkuId());
                resultData.addToErrorData(new ResultErrorSpu()
                        .setStoreId(errorData.getStoreId())
                        .setSpuInfo(spuKey)
                        .setErrorCode(errorData.getErrorCode())
                        .setErrorMsg(errorData.getErrorMsg()));
            });
        }
        if (CollectionUtils.isNotEmpty(skuResultData.getSucData())) {
            skuResultData.getSucData().forEach(sucData -> {
                SpuKey spuKey = new SpuKey().setCustomSpuId(sucData.getSkuId());
                resultData.addToSucData(new ResultSuccessSpu()
                        .setStoreId(sucData.getStoreId())
                        .setSpuInfo(spuKey));
            });
        }
        return resultData;
    }

    private String convert2Upc(SpuInfoDTO spuInfoDTO) {
        return spuInfoDTO.getSkus().stream().map(SkuInSpuInfoDTO::getUpc).collect(Collectors.joining(","));
    }

    private SpuKey convert2Key(SpuInfoDTO spuInfoDTO) {
        SpuKey spuKey = new SpuKey().setCustomSpuId(spuInfoDTO.getCustomSpuId()).setChannelSpuId(spuInfoDTO.getChannelSpuId());
        List<SkuKey> skuKeys = new ArrayList<>();
        spuInfoDTO.getSkus().forEach(skuInSpuInfoDTO -> {
            SkuKey skuKey = new SkuKey().setCustomSkuId(skuInSpuInfoDTO.getCustomSkuId());
            skuKeys.add(skuKey);
        });
        spuKey.setSkus(skuKeys);
        return spuKey;
    }
}
