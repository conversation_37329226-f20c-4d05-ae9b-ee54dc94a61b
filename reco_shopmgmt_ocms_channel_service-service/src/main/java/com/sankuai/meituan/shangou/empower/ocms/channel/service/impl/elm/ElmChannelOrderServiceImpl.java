package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.elm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.base.CharMatcher;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.KeyConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.LionConfigKeyConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.DeliveryTrackDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelDeliveryService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.cache.ElmSelfDeliveryStatusSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ChannelStatusConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.ElmConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.orderTrack.OrderTrackService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.*;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.ServiceTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelOrderStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.saas.common.json.GsonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.SELF_DELIVERY_LOCATION_SYNC_SET;

/**
 * @description:
 * @author: zhaolei12
 * @create: 2019/1/28 下午5:46
 */
@Service("elmChannelOrderService")
public class ElmChannelOrderServiceImpl implements ChannelOrderService {

    @Resource
    private ElmConverterService elmConverterService;

    @Resource
    private ElmChannelGateService elmChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private ChannelDeliveryService channelDeliveryService;

    @Resource
    private CommonLogger log;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private ElmSelfDeliveryStatusCompensateService elmSelfDeliveryStatusCompensateService;

    @Resource
    private ElmSelfDeliveryStatusSquirrelOperationService elmSquirrelService;

    @Resource
    private OrderTrackService orderTrackService;

    private static final String SUCCESS_CODE = "0";

    private static final String UN_FINISHED_TIME = "0";

    @Value("${elm.url.base}")
    private String baseUrl;

    @Value("${elm.url.orderDetail}")
    private String orderDetailMethod;
    @Value("${elm.url.orderStatus}")
    private String orderStatus;

    @Value("${elm.url.deliveryStatus}")
    private String deliveryStatus;

    @Value("${elm.url.orderSendout}")
    private String orderSendout;

    //订单送达，自配送订单使用
    @Value("${elm.url.orderComplete}")
    private String orderComplete;

    @Value("${elm.url.orderSwitchSelfDelivery}")
    private String orderSwitchSelfDelivery;

    @Value("${elm.url.getDeliveryStateRecord}")
    private String getDeliveryStateRecord;

    @Value("${elm.url.selfDeliveryLocationSync}")
    private String selfDeliveryLocationSync;

    private String orderGiftParseBusinessKey = "订单赠品解析";

    private String orderGiftParseTagName = "饿了么赠品解析";

    private static final long SECOND = TimeUnit.SECONDS.toMillis(1);

    @Override
    public ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getStoreId()));
            PoiConfirmOrderParam bizParam = elmConverterService.poiConfirmOrderParamMapping(request);
            ChannelResponseDTO<String> resultData = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.ORDER_CONFIRM, baseRequest, bizParam);
            log.info("ElmChannelOrderServiceImpl.poiConfirmOrder, request:{}, resultMap:{}", request, resultData);
            return getResultStatus(resultData);
        } catch (Exception e) {
            log.error("elmChannelOrderService poiConfirmOrder ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
        GetChannelOrderDetailResult orderDetailResult = new GetChannelOrderDetailResult();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setAppId(request.getAppId()).setStoreIdList(Arrays.asList(request.getSotreId()));
        ChannelOrderDetailParam bizParam = elmConverterService.channelOrderDetailParamMapping(request);
        ChannelResponseDTO<String> resultMap = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.ORDER_DETAIL, baseRequest, bizParam);
        log.info("ElmChannelOrderServiceImpl.getChannelOrderDetail, request:{}, resultMap:{}", request, resultMap);
        if (Objects.isNull(resultMap)) {
            return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单详情失败"));
        }
        return orderDetailAnalysis(request.getTenantId(), request.getChannelId(), resultMap, orderDetailResult);
    }


    private ChannelOrderDetail getElmOrderDetail(String orderId, long tenantId, long storeId, Long appId) {
        BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId).setChannelId(ChannelTypeEnum.ELEM.getCode()).setStoreIdList(Arrays.asList(storeId)).setAppId(appId);
        ChannelOrderDetailParam bizParam = new ChannelOrderDetailParam();
        bizParam.setOrder_id(orderId);
        ChannelResponseDTO<String> resultMap = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.ORDER_DETAIL, baseRequest, bizParam);
        ChannelResponseResult<String> channelResponseResult = resultMap.getBody();
        log.info("ElmChannelOrderServiceImpl.getElmOrderDetail, request:{}, resultMap:{}", baseRequest, resultMap);
        if (!Objects.isNull(resultMap) && channelResponseResult.getErrno().equals(ResultCodeEnum.SUCCESS.getValue())) {
            ChannelOrderListDetail channelOrderListDetail = JSON.parseObject(channelResponseResult.getData(), ChannelOrderListDetail.class);
            if (!Objects.isNull(channelOrderListDetail)) {
                return channelOrderListDetail.getOrder();
            }
        }
        return null;
    }

    private GetChannelOrderDetailResult orderDetailAnalysis(long tenantId, int channelId, ChannelResponseDTO<String> result,
                                                            GetChannelOrderDetailResult orderDetailResult) {
        ChannelResponseResult<String> channelResponseResult = result.getBody();
        if (channelResponseResult.getErrno().equals(ResultCodeEnum.SUCCESS.getValue())) {

            ChannelOrderListDetail channelOrderListDetail = JSON.parseObject(channelResponseResult.getData(), ChannelOrderListDetail.class);
            if (Objects.isNull(channelOrderListDetail)) {
                return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, "订单不存在"));
            }
            ChannelOrderDetail channelOrderDetail = channelOrderListDetail.getOrder();
            if (Objects.isNull(channelOrderDetail)) {
                return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, "订单不存在"));
            }
            //订单主数据
            ChannelOrderDetailDTO channelOrderDetailDTO = elmConverterService.channelOrderDetailMapping(channelOrderDetail);
            channelOrderDetailDTO.setBaichuanStatus(BaichuanOrderStatusConverter.elmoOrderStatusMapping(channelOrderDetail.getStatus()));

            if (Objects.nonNull(channelOrderListDetail.getShop())) {
                //门店信息
                channelOrderDetailDTO.setStoreId(copChannelStoreService.selectChannelStoreId(tenantId, channelId, channelOrderListDetail.getShop().getId()));
                channelOrderDetailDTO.setChannelStoreName(channelOrderListDetail.getShop().getName());
            }
            //配送信息
            OrderDeliveryDetailDTO orderDeliveryDetailDTO = elmConverterService.deliveryDetailMapping(channelOrderDetail);
            // 校验租户是否支持
            if(isSupportExpensiveProductPickupCode(orderDeliveryDetailDTO, channelOrderDetailDTO.getChannelOrderId(), tenantId)){
                orderDeliveryDetailDTO.setExpensiveProductPickupCode(null);
            }
            if (Objects.nonNull(channelOrderListDetail.getUser())) {
                channelOrderDetailDTO.setUserId(channelOrderListDetail.getUser().getUser_id());
                //渠道用户id
                if(StringUtils.isNotBlank(channelOrderListDetail.getUser().getUser_id())){
                    channelOrderDetailDTO.setChannelUserId(channelOrderListDetail.getUser().getUser_id());
                }
                channelOrderDetailDTO.setBuyerAccount(channelOrderListDetail.getUser().getUser_id()); // 因userId后面被转成了long，与其他渠道不兼容，使用buyerAccount替代
                orderDeliveryDetailDTO.setUserName(Optional.ofNullable(channelOrderListDetail.getUser().getName())
                        .filter(StringUtils::isNotEmpty).orElse("******"));
                orderDeliveryDetailDTO.setUserAddress(channelOrderListDetail.getUser().getAddress());
                orderDeliveryDetailDTO.setUserPhone(channelOrderListDetail.getUser().getPhone());
                orderDeliveryDetailDTO.setUserPhoneIsValid(!StringUtils.contains(channelOrderListDetail.getUser().getPhone(), "*"));
                orderDeliveryDetailDTO.setUserPrivacyPhone(channelOrderListDetail.getUser().getPrivacy_phone());
                //解析收货地址坐标信息
                if (Objects.nonNull(channelOrderListDetail.getUser().getCoord_amap())) {
                    try {
                        double longitude = Double.valueOf(channelOrderListDetail.getUser().getCoord_amap().getLongitude());
                        double latitude = Double.valueOf(channelOrderListDetail.getUser().getCoord_amap().getLatitude());
                        orderDeliveryDetailDTO.setLongitude(longitude);
                        orderDeliveryDetailDTO.setLatitude(latitude);
                    } catch (NumberFormatException e) {
                        if (!isFinishedOrder(channelOrderDetail.getFinished_time())) {
                            log.warn("租户{}饿了么渠道订单无法解析收货人经纬度信息, channelOrderId:{}, coord:{}, exception:",
                                    tenantId, channelOrderDetail.getOrder_id(),
                                    channelOrderListDetail.getUser().getCoord_amap(), e);
                        }
                    }
                }
                //elem 自配送匹配渠道状态码错误，由4，6 转换为2，6
                //4:饿了么众包   6:饿了么自配送
                if (MccConfigUtil.getElemSelfDeliveryCodeConfig().contains(channelOrderDetail.getDelivery_party())) {
                    orderDeliveryDetailDTO.setIsSelfDelivery(1);
                } else {
                    orderDeliveryDetailDTO.setIsSelfDelivery(0);
                }
            }
            channelOrderDetailDTO.setDeliveryDetail(orderDeliveryDetailDTO);
            //运费
            if (Objects.nonNull(channelOrderDetail.getDelivery_fee())) {
                channelOrderDetailDTO.setPlatLogisticsPromotion(Optional.ofNullable(channelOrderDetail.getDelivery_fee().getPlatform_delivery_fee()).orElse(0));
                channelOrderDetailDTO.setPoiLogisticsPromotion(Optional.ofNullable(channelOrderDetail.getDelivery_fee().getShop_delivery_fee()).orElse(0));
            }
            // 渠道配送：配送到家，非自送
            if (Objects.equals(channelOrderDetail.getBusiness_type(), "0") && Objects.equals(orderDeliveryDetailDTO.getIsSelfDelivery(), 0)) {
                channelOrderDetailDTO.setPoiLogisticsIncome(-channelOrderDetailDTO.getPoiLogisticsPromotion());
            } else {
                channelOrderDetailDTO.setPoiLogisticsIncome(channelOrderDetailDTO.getFreight() - channelOrderDetailDTO.getPoiLogisticsPromotion());
            }
            //是否有地址变更费（饿了么渠道叫用户加价费）
            setSelfDeliverAddressChangeFee(channelOrderDetailDTO, channelOrderDetail.getModify_order_fee());
            //超客订单开关
            if (MccConfigUtil.validTenantOpenConfig(AppKeyEnum.ORDER_BIZ.getAppKey(), tenantId, LionConfigKeyConstant.MT_AND_ELE_AMOUNT_EXCHANGE_SWITCH)) {

                //标记订单是否是超客订单
                channelOrderDetailDTO.setSuperCustomerOrder(Optional.ofNullable(channelOrderDetail.getIs_super_customer_order()).map(superCustomerOrder -> Objects.equals(superCustomerOrder, 1) ? 1 : 0).orElse(0));

                //超客订单 商家运费收入 金额处理
                if (Objects.equals(channelOrderDetailDTO.getSuperCustomerOrder(), 1)) {
                    channelOrderDetailDTO.setPoiLogisticsIncome(channelOrderDetailDTO.getFreight() - channelOrderDetailDTO.getPoiLogisticsPromotion());
                    //自配送才验证是否有地址变更费
                    setSelfDeliverAddressChangeFee(channelOrderDetailDTO, channelOrderDetail.getModify_order_fee());
                }
            }
            //包装费
            channelOrderDetailDTO.setPlatPackageAmt(channelOrderDetailDTO.getPackageAmt() - channelOrderDetailDTO.getPoiPackageAmt());
            channelOrderDetailDTO.setPayPackageAmt(channelOrderDetailDTO.getPackageAmt());

            //开票信息
            channelOrderDetailDTO.setInvoiceDetail(elmConverterService.invoiceDetailMapping(channelOrderDetail));
            //商品信息
            List<OrderSkuDetail> skuDetails = channelOrderListDetail.getProducts().get(0);
            List<OrderActivitiesInfo> discount = channelOrderListDetail.getDiscount();
            channelOrderDetailDTO.setSkuDetails(elmConverterService.orderSkuDetailListMapping(skuDetails));
			// 歪马租户或者赠品灰度开关关闭时，去除itemType赠品设置保留原逻辑；
			if (BusinessIdTracer.isDrunkHorseTenant(tenantId) || !MccConfigUtil.getGiftProductToOrderDetailSwitch(tenantId)) {
				channelOrderDetailDTO.getSkuDetails().forEach(detail -> {
					detail.setItemType(0);
				});
				// 针对线上赠品需要设置扩展参数
			} else {
				channelOrderDetailDTO.getSkuDetails().forEach(detail -> {
					if (detail.getItemType() == 1) {
						JSONObject extJSON = new JSONObject();
						if (detail.isSetExtData()) {
							try{
								String extData = detail.getExtData();
								extJSON = JSON.parseObject(extData);
							} catch (Exception e) {
								log.error("解析ext异常，detail：{}", JSON.toJSONString(detail), e);
							}
						}
						// 添加线上赠品标（giftType : 0 (平台赠品)），标志打在明细的扩展字段
						extJSON.put("giftType", 0);
						detail.setExtData(extJSON.toJSONString());
					}
				});
			}

			//购物金信息
            if (channelOrderListDetail.getOrder().getShop_card_fee() != null) {
                channelOrderDetailDTO.setShopCardFee(parserElmShopCardFee(channelOrderListDetail.getOrder().getShop_card_fee()));
            }
            //活动信息
            channelOrderDetailDTO.setActivities(parserElmGiftInfo(elmConverterService.orderActivitieInfoListMapping(discount)));
            //商品活动分摊金额【注意：在最后解析】
            channelOrderDetailDTO.setSkuSharedActivities(ElmConverterUtil.parseGoodsActivities(channelOrderDetailDTO, skuDetails, discount));
            //elm提取优惠信息
            ElmConverterUtil.extractPromotionFromActivities(channelOrderDetailDTO, skuDetails, discount, tenantId);
            // 活动分摊
            splitSkuPromotionFromSkuBenefitCatchException(channelOrderDetailDTO, skuDetails, discount, tenantId);

            channelOrderDetailDTO.setChannelPayMode(KeyConstant.DEFAULT_PAY_MODE);
            recordMetrics(channelOrderDetail);
            return orderDetailResult.setStatus(ResultGenerator.genSuccessResult()).setChannelOrderDetail(channelOrderDetailDTO);
        }
        return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, channelResponseResult.getError()));
    }

    private void setSelfDeliverAddressChangeFee(ChannelOrderDetailDTO channelOrderDetailDTO, Integer addressChangeFee) {
        if(Objects.nonNull(addressChangeFee) && addressChangeFee != 0) {
            channelOrderDetailDTO.setAddressChangeFee(ConverterUtils.fenToYuanString(addressChangeFee));
        }
    }

    private boolean isSupportExpensiveProductPickupCode(OrderDeliveryDetailDTO orderDeliveryDetailDTO, String channelOrderId, Long tenantId){
        try {
            // 歪马租户不设置
            if (BusinessIdTracer.isDrunkHorseTenant(tenantId)) {
                return true;
            }
            // 租户未配置在lion里面，不设置
            if (StringUtils.isNotEmpty(orderDeliveryDetailDTO.getExpensiveProductPickupCode())
                    && !MccConfigUtil.checkElmSupportExpensiveProductPickupCodeTenants(tenantId)) {
                return true;
            }
            return false;
        }catch (Exception e){
            log.warn("isSupportExpensiveProductPickupCode is error! channelOrderId={}; tenantId={}; orderDelivery:{}", channelOrderId, tenantId, JSON.toJSONString(orderDeliveryDetailDTO), e);
            return true;
        }
    }

    private ShopCardFee parserElmShopCardFee(ShopCardInfo shopCardInfo) {
        ShopCardFee shopCardFee = new ShopCardFee();
        shopCardFee.setTotalFee(shopCardInfo.getTotal_fee());
        shopCardFee.setBaseFee(shopCardInfo.getBase_fee());
        shopCardFee.setGiveFee(shopCardInfo.getGive_fee());
        shopCardFee.setPlatformGiveFeeShare(shopCardInfo.getPlatform_give_fee_share());
        shopCardFee.setShopGiveFeeShare(shopCardInfo.getShop_give_fee_share());
        return shopCardFee;
    }

    private ShopCardFee parserElmAfsShopCardFee(ElmReverseResult.RefundShopCardDetail shopCardInfo) {
        ShopCardFee shopCardFee = new ShopCardFee();
        shopCardFee.setTotalFee(shopCardInfo.getRefundShopCardPrice());
        shopCardFee.setBaseFee(shopCardInfo.getBaseShopCardPrice());
        shopCardFee.setGiveFee(shopCardInfo.getGiveShopCardPrice());
        shopCardFee.setPlatformGiveFeeShare(shopCardInfo.getPlatformGiveShopCardRate());
        shopCardFee.setShopGiveFeeShare(shopCardInfo.getShopGiveShopCardRate());
        return shopCardFee;
    }


    private void recordMetrics(ChannelOrderDetail channelOrderDetail) {
        try {
            if (channelOrderDetail.hasDownGrade()) {
                MetricHelper.build().name("order.downgrade").tag("channel", "elm").count();
            }
        } catch (Exception e) {
            log.error("埋点失败", e);
        }
    }

    /**
     * 捕捉异常，避免影响订单入库。
     */
    private void splitSkuPromotionFromSkuBenefitCatchException(ChannelOrderDetailDTO channelOrderDetailDTO, List<OrderSkuDetail> skuDetails, List<OrderActivitiesInfo> discount, long tenantId) {
        try {
            if (!MccConfigUtil.getActivityShareSwitch()) {
                log.info("活动优惠拆分功能控制为false");
                return;
            }
            splitSkuPromotionFromSkuBenefit(channelOrderDetailDTO, skuDetails, discount, tenantId);
        } catch (Exception ex) {
            log.error("活动分摊异常", ex);
        }
    }

    /**
     * 活动分摊到商品级别
     */
    private void splitSkuPromotionFromSkuBenefit(ChannelOrderDetailDTO channelOrderDetailDTO, List<OrderSkuDetail> skuDetails, List<OrderActivitiesInfo> discount, long tenantId) {
//        log.info("解析活动分摊数据");
        Map<String, ActivityDiscount> activityDiscountMap = ElmConverterUtil.buildProductDiscountMap(discount);
        List<ActivityShareDetailDTO> shareDetailDTOList = ElmConverterUtil.splitSkuPromotionFromActivityDiscount(skuDetails,
                activityDiscountMap, channelOrderDetailDTO.getSkuDetails(), tenantId);
        log.info("解析活动分摊数据,shareDetailDTOList={}", GsonUtils.toJSONString(shareDetailDTOList));
        channelOrderDetailDTO.setActivityShareDetailList(shareDetailDTOList);
    }

    /**
     * 解析赠品信息
     *
     * @param orderDiscountDetailDTOS
     * @return
     */
    private List<OrderDiscountDetailDTO> parserElmGiftInfo(List<OrderDiscountDetailDTO> orderDiscountDetailDTOS) {
        if (CollectionUtils.isEmpty(orderDiscountDetailDTOS)) {
            return null;
        }
        try {
            orderDiscountDetailDTOS.forEach(orderDiscountDetailDTO -> {
                if (orderDiscountDetailDTO.getType().equals("zeng")) {
                    ChannelGiftInfo elmGiftInfo = new ChannelGiftInfo();
                    //elm赠品信息：下单满赠(满赠ASD2测试1),只有赠品名称和数量
                    String eleMEDiscount = orderDiscountDetailDTO.getRemark();
                    int begin = CharMatcher.is('(').indexIn(eleMEDiscount);
                    int end = CharMatcher.is(')').lastIndexIn(eleMEDiscount);
                    //匹配最外层括号，过滤掉活动名称
                    String skuNameQuantity = eleMEDiscount.substring(begin + 1, end);
                    //数字匹配
                    String regex = "^\\d+$";
                    //从后往前第一位数字下标
                    int lastIndex = 0;
                    Pattern pattern = Pattern.compile(regex);
                    for (int i = skuNameQuantity.length() - 1; i > 0; i--) {
                        //当前位为数字且前一位非数字
                        if (pattern.matcher(String.valueOf(skuNameQuantity.charAt(i))).find()) {
                            if (!pattern.matcher(String.valueOf(skuNameQuantity.charAt(i - 1))).find()) {
                                lastIndex = i;
                                break;
                            }
                        }
                    }
                    //赠品名称
                    elmGiftInfo.setName(skuNameQuantity.substring(0, lastIndex));
                    //剩余字符串可能是不带单位"份"
                    String quantityWithUnit = skuNameQuantity.substring(lastIndex);
                    StringBuilder sb = new StringBuilder();
                    for (int j = 0; j < quantityWithUnit.length(); j++) {
                        if (pattern.matcher(String.valueOf(quantityWithUnit.charAt(j))).find()) {
                            sb.append(quantityWithUnit.charAt(j));
                        } else {
                            break;
                        }
                    }
                    //赠品数量
                    elmGiftInfo.setQuantity(Integer.valueOf(sb.toString()));
                    orderDiscountDetailDTO.setGiftInfo(elmGiftInfo);
                }
            });
        } catch (Exception e) {
            MetricUtils.count(orderGiftParseBusinessKey, orderGiftParseTagName, "failed", 1);
            log.warn("Elm 消息体赠品解析异常,促销信息:{}", orderDiscountDetailDTOS, e);
        }
        return orderDiscountDetailDTOS;
    }

    @Override
    public ResultStatus preparationMealComplete(PreparationMealCompleteRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest()
                    .setChannelId(request.getChannelId()).setTenantId(request.getTenantId()).setStoreIdList(Arrays.asList(request.getStoreId()));
            OrderPickcompleteInputParam bizParam = OrderPickcompleteInputParam.builder()
                    .order_id(request.getOrderId()).build();
            ChannelResponseDTO<String> resultMap = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.PICK_COMPLETE,
                    baseRequest, bizParam);
            log.info("ElmChannelOrderServiceImpl.preparationMealComplete, request:{}, resultMap:{}",
                    baseRequest, resultMap);
            triggerMonitor(request);
            return getResultStatus(resultMap);
        } catch (Exception e) {
            log.error("ElmChannelOrderServiceImpl.preparationMealComplete ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    private void triggerMonitor(PreparationMealCompleteRequest request) {
        try {
            // 屏蔽歪马
            if (MccConfigUtil.getDHTenantIdList().contains(String.valueOf(request.getTenantId()))) {
                return;
            }

            long completeTime = request.getCompleteTime();
            long merchantTakeOrderTime = request.getMerchantTakeOrderTime();
            if (completeTime <= NumberUtils.LONG_ZERO || merchantTakeOrderTime <= NumberUtils.LONG_ZERO) {
                return;
            }

            // 对拣货完成时间距离接单时间不足1min的订单打点
            Integer intervalSeconds = MccConfigUtil.getElmPreparationMealCompleteTime();
            if (Duration.between(Instant.ofEpochMilli(merchantTakeOrderTime), Instant.ofEpochMilli(completeTime)).getSeconds() < intervalSeconds) {
                String catName = request.getTenantId() + "_" + request.getStoreId() + "_" + request.getOrderId();
                Cat.logEvent(ProjectConstant.ELM_PREPARATION_MEAL_COMPLETE_ILLEGAL_CAT_STR, catName);
            }
        } catch (Exception e) {
            log.error("ElmChannelOrderServiceImpl triggerMonitor exception", e);
        }
    }

    @Override
    public GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request) {
        GetOrderStatusResult resp = new GetOrderStatusResult();
        OrderStatusDTO orderStatusDTO = new OrderStatusDTO().setOrderId(request.getOrderId());
        resp.setOrderStatus(orderStatusDTO);

        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId())
                .setAppId(request.getAppId());
        addStoreId2BaseRequest(request.getStoreId(), baseRequest);

        Map param = Maps.newHashMap();
        param.put("order_id", request.getOrderId());
        Map viewStatusMap = elmChannelGateService.sendPostApp(baseUrl, orderStatus, baseRequest, param);
        JSONObject viewStatusJsonobj = JSON.parseObject(String.valueOf(viewStatusMap.get(ProjectConstant.BODY)));
        if (Integer.valueOf(0).equals(viewStatusJsonobj.get(ProjectConstant.ERRNO))) {
            Integer elmOrderStatus = viewStatusJsonobj.getJSONObject(ProjectConstant.DATA).getInteger(ProjectConstant.STATUS);
            if (elmOrderStatus == null) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询订单状态为空"));
            }
            int status = OrderStatusConverter.elmoOrderStatusMapping(elmOrderStatus);
            if (status == ChannelOrderStatus.FINISHED.getValue()) {
                //饿了么订单完成后，用户在申请取消订单，订单状态不会扭转；而美团的订单状态在该条件下会发生扭转，这里统一按照美团的逻辑
                ChannelOrderDetail orderDetail = getElmOrderDetail(request.getOrderId(), request.getTenantId(), request.getStoreId(), request.getAppId());
                if (orderDetail != null && orderDetail.getExt() != null && orderDetail.getExt().orderHasCanceled()) {
                    log.info("饿了么存在订单完成后，用户取消订单的申请，且已经审批，将订单状态置位已取消, order:{}", request.getOrderId());
                    status = ChannelOrderStatus.CANCELED.getValue();
                }
            }
            resp.setStatus(ResultGenerator.genSuccessResult())
                    .getOrderStatus().setStatus(status);

        } else {
            resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, viewStatusJsonobj.getString(ProjectConstant.ERROR)));
        }

        return resp;
    }

    @Override
    public ResultStatus agreeRefund(AgreeRefundRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest()
                    .setChannelId(request.getChannelId()).setTenantId(request.getTenantId()).setStoreIdList(Arrays.asList(request.getStoreId()));

            ElmOrderProcessRefundParam bizParam = ElmOrderProcessRefundParam.builder()
                    .reverse_order_id(request.getAfterSaleId())
                    .order_id(request.getOrderId())
                    .idempotent_id(ElmConverterUtil.generateIdempotentId())
                    .action_type("1")
                    .build();
            ChannelResponseDTO<String> resultMap = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.POI_REVERSE_PROCESS,
                    baseRequest, bizParam);
            log.info("ElmChannelOrderServiceImpl.agreeRefund, request:{}, resultMap:{}",
                    baseRequest, resultMap);
            return getResultStatus(resultMap);
        } catch (Exception e) {
            log.error("elmChannelOrderService agreeRefund ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    /***
     * 1.4 接口返回状态码说明
     * 所有操作如果成功则返回状态码0，如果失败状态码为非0
     * **/
    private ResultStatus getResultStatus(ChannelResponseDTO resultData) {
        if (Objects.isNull(resultData)) {
            return ResultGenerator.genFailResult("调用渠道切换商家自配送失败解析失败");
        }
        if (resultData.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(resultData.getErrorMsg());
    }

    @Override
    public ResultStatus rejectRefund(RejectRefundRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest()
                    .setChannelId(request.getChannelId()).setTenantId(request.getTenantId()).setStoreIdList(Arrays.asList(request.getStoreId()));

            ElmOrderProcessRefundParam bizParam = ElmOrderProcessRefundParam.builder()
                    .reverse_order_id(request.getAfterSaleId())
                    .order_id(request.getOrderId())
                    .idempotent_id(ElmConverterUtil.generateIdempotentId())
                    .action_type("2")
                    .reason_code((StringUtils.isBlank(request.getReasonCode()) || "-1".equals(request.getReasonCode())) ? "7001" : request.getReasonCode())
                    .reason_remarks(request.getReason())
                    .build();

            if ("7001".equals(bizParam.getReason_code()) && StringUtils.isBlank(bizParam.getReason_remarks())) {
                bizParam.setReason_remarks("未填写原因");
            }

            ChannelResponseDTO<String> resultMap = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.POI_REVERSE_PROCESS,
                    baseRequest, bizParam);
            log.info("ElmChannelOrderServiceImpl.rejectRefund, request:{}, resultMap:{}",
                    baseRequest, resultMap);
            return getResultStatus(resultMap);
        } catch (Exception e) {
            log.error("elmChannelOrderService rejectRefund ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public ResultStatus refundGoods(RefundGoodsRequest request) {
        try {
            AfsReviewTypeEnum reviewType = AfsReviewTypeEnum.enumOf(request.getReviewType());

            BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getStoreId()));

            ElmOrderProcessRefundParam bizParam = new ElmOrderProcessRefundParam();

            bizParam.setReverse_order_id(request.getAfterSaleId());
            bizParam.setOrder_id(request.getOrderId());
            bizParam.setIdempotent_id(ElmConverterUtil.generateIdempotentId());
            // 兼容当前可能出现的-1(其他)
            bizParam.setReason_code(request.getReasonCode() == -1 ? "7001" : String.valueOf(request.getReasonCode()));
            bizParam.setReason_remarks(request.getReason());

            if ("7001".equals(bizParam.getReason_code()) && StringUtils.isBlank(bizParam.getReason_remarks())) {
                bizParam.setReason_remarks("未填写原因");
            }

            switch (reviewType) {
                case AGREE_REFUND_GOODS:
                    // 一审通过
                    bizParam.setAction_type("3");
                    break;
                case REJECT_REFUND:
                    if (request.getAuditStage() == AfsAuditStageEnum.FINAL_AUDIT.getValue()) {
                        // 二审驳回
                        // bizParam.setReason_code("7302"); // 原因固定7302 未收到退货
                        bizParam.setAction_type("2");
                    } else {
                        // 一审驳回
                        bizParam.setAction_type("4");
                    }
                    break;
                case AGREE_REFUND:
                    // 二审通过
                    bizParam.setAction_type("1");
                    break;
                default:
                    log.error("elmChannelOrderService refundGoods reviewType ERROR! 不支持的退货退款状态");
                    return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + "不支持的退货退款状态");
            }

            ChannelResponseDTO<String> resultMap = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.POI_REVERSE_PROCESS,
                    baseRequest, bizParam);

            log.info("ElmChannelOrderServiceImpl.refundGoods, request:{}, resultMap:{}",
                    baseRequest, resultMap);

            return getResultStatus(resultMap);
        } catch (Exception e) {
            log.error("elmChannelOrderService refundGoods ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public ResultStatus poiCancelOrder(PoiCancelOrderRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getStoreId()));

            ElmPoiReverseApplyParam bizParam = elmConverterService.poiCancelOrderApplyMapping(request);

            if (StringUtils.isBlank(bizParam.getReason_code()) || "-1".equals(bizParam.getReason_code())) {
                bizParam.setReason_code("7001");
            }

            if ("7001".equals(bizParam.getReason_code()) && StringUtils.isBlank(bizParam.getReason_remarks())) {
                bizParam.setReason_remarks("未填写原因");
            }

            ChannelResponseDTO<String> resultData = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.POI_REVERSE_APPLY, baseRequest, bizParam);
            log.info("ElmChannelOrderServiceImpl.poiCancelOrder, request:{}, resultMap:{}", request, resultData);
            return getResultStatus(resultData);
        } catch (Exception e) {
            log.error("elmChannelOrderService poiCancelOrder ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public ResultStatus poiPartRefundApply(PoiPartRefundRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getStoreId()));


            ElmPoiReverseApplyParam bizParam = elmConverterService.poiPartRefundApplyMapping(request);

            if (StringUtils.isBlank(bizParam.getReason_code()) || "-1".equals(bizParam.getReason_code())) {
                bizParam.setReason_code("7001");
            }

            if ("7001".equals(bizParam.getReason_code()) && StringUtils.isBlank(bizParam.getReason_remarks())) {
                bizParam.setReason_remarks("未填写原因");
            }

            ChannelResponseDTO<String> resultData = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.POI_REVERSE_APPLY, baseRequest, bizParam);
            log.info("ElmChannelOrderServiceImpl.poiPartRefundApply, request:{}, resultMap:{}", request, resultData);
            //如果失败返回code为100006，需要采用平台商品Id重试一次
            if(Objects.nonNull(resultData) && String.valueOf(ElmResultCodeEnum.ERROR_100006.getCode()).equals(resultData.getErrno()) && CollectionUtils.isNotEmpty(bizParam.getRefund_product_list())){
                adjustBizParam(bizParam,request);
                resultData = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.POI_REVERSE_APPLY, baseRequest, bizParam);
                log.info("retry ElmChannelOrderServiceImpl.poiPartRefundApply, request:{}, resultMap:{}", request, resultData);
            }
            return getResultStatus(resultData);
        } catch (Exception e) {
            log.error("elmChannelOrderService poiPartRefundApply ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    private void adjustBizParam(ElmPoiReverseApplyParam bizParam, PoiPartRefundRequest request) {
        log.info("retry poiPartRefundApply adjustBizParam bizParam: {}, request:{}", bizParam, request);
        Map<String, RefundProductInfoDTO> productInfoDTOMap = request.getRefundProducts().stream()
                .filter(item -> StringUtils.isNotBlank(item.getCustomSkuId()))
                .collect(Collectors.toMap(RefundProductInfoDTO::getCustomSkuId, Function.identity()));
        bizParam.getRefund_product_list().forEach(item -> {
            RefundProductInfoDTO dto = productInfoDTOMap.get(item.getPlatform_sku_id());
            if(Objects.nonNull(dto) && StringUtils.isNotBlank(dto.getBaiduProductId())){
                item.setPlatform_sku_id(dto.getBaiduProductId());
            }
        });
    }

    @Override
    public ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request) {
        return ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE);
    }

    @Override
    public ResultStatus verifySelfFetchCode(VerifySelfFetchCodeRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setStoreIdList(Arrays.asList(request.getStoreId()));
            ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(request.getTenantId(), request.getChannelId(), request.getStoreId());
            if (channelStore == null) {
                return ResultGenerator.genFailResult("参数异常，未查询到渠道门店");
            }
            Map<String, Object> bizParam = new HashMap<>();
            if (Objects.nonNull(request.getCode())) {
                bizParam.put("pick_up_code", request.getCode());
            }
            if (Objects.nonNull(request.getQrCode())) {
                bizParam.put("qr_code", request.getQrCode());
            }
            bizParam.put("shop_id", channelStore.getChannelOnlinePoiCode());
            ChannelResponseDTO<String> resultData = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.ORDER_SELF_PICK_CHECKOUT, baseRequest, bizParam);
            log.info("ElmChannelOrderServiceImpl.verifySelfFetchCode, request:{}, resultMap:{}", request, resultData);
            if (resultData.isSuccess() && Objects.nonNull(resultData.getBody())) {
                return ResultGenerator.genSuccessResult().setData(resultData.getBody().getData());
            } else {
                return ResultGenerator.genFailResult(resultData.getErrorMsg());
            }
        } catch (Exception e) {
            log.error("elmChannelOrderService verifySelfFetchCode ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Override
    public PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request) {
        PoiAdjustOrderResult result = new PoiAdjustOrderResult();
        return result.setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
    }

    @Override
    public OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request) {
        OrderShouldSettlementResult result = new OrderShouldSettlementResult();
        return result.setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
    }

    @Override
    public GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request) {
        GetChannelOrderDetailRequest orderDetailRequest = new GetChannelOrderDetailRequest();
        orderDetailRequest.setTenantId(request.getTenantId());
        orderDetailRequest.setOrderId(request.getOrderId());
        orderDetailRequest.setChannelId(request.getChannelId());
        orderDetailRequest.setSotreId(request.getStoreId());
        GetChannelOrderDetailResult orderDetailResult = getChannelOrderDetail(orderDetailRequest);
        GoodsSettlementResult result = new GoodsSettlementResult();
        if (Objects.nonNull(orderDetailResult) && orderDetailResult.getStatus().getCode() == ResultCode.SUCCESS.getCode()) {
            result.setStatus(ResultGenerator.genSuccessResult());
            List<GoodsActivityDetailDTO> goodsActivityDetailDTOList = orderDetailResult.getChannelOrderDetail().getSkuSharedActivities();
            result.setGoodSettlementInfos(goodsActivityDetailDTOList);
        } else {
            result.setStatus(orderDetailResult.getStatus());
        }
        return result;
    }

    @Override
    public GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request) {
        GetLogisticsStatusResult resp = new GetLogisticsStatusResult();

        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId())
                .setStoreIdList(Arrays.asList(request.getStoreId()));
        Map param = Maps.newHashMap();
        param.put("order_id", request.getOrderId());
        Map viewStatusMap = elmChannelGateService.sendPostApp(baseUrl, deliveryStatus, baseRequest, param);
        JSONObject viewStatusJsonobj = JSON.parseObject(String.valueOf(viewStatusMap.get(ProjectConstant.BODY)));
        if (Integer.valueOf(0).equals(viewStatusJsonobj.get(ProjectConstant.ERRNO))) {
            ElmLogisticsStatusDTO data = viewStatusJsonobj.getObject(ProjectConstant.DATA, ElmLogisticsStatusDTO.class);

            if (data == null) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "查询配送状态为空"));
            }
            LogisticsStatusDTO logisticsStatusDTO = elmConverterService.convertLogistcsStatus(data);
            logisticsStatusDTO.setOrderId(request.getOrderId());
            resp.setStatus(ResultGenerator.genSuccessResult())
                    .setLogisticsStatus(logisticsStatusDTO);

        } else {
            resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, viewStatusJsonobj.getString(ProjectConstant.ERROR)));
        }

        return resp;
    }

    @Override
    public GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request) {
        GetOrderAfsApplyListResult result = new GetOrderAfsApplyListResult();
        result.setAfsApplyList(new LinkedList<>());

        long appId = request.getAppId();
        if (request.getStoreId() <= NumberUtils.LONG_ZERO && appId <= NumberUtils.LONG_ZERO) {
            result.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, "获取饿了么部分退款记录失败，参数错误"));
            return result;
        }

        if (request.getStoreId() > NumberUtils.LONG_ZERO && appId <= NumberUtils.LONG_ZERO) {
            ChannelStoreDO channelStore = copChannelStoreService.getChannelStore(request.getTenantId(), request.getChannelType(), request.getStoreId());
            if (Objects.isNull(channelStore)) {
                result.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS, "获取饿了么部分退款记录失败，获取appid失败"));
                return result;
            }
            appId = channelStore.getAppId();
        }

        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(ChannelTypeEnum.ELEM.getCode()).setAppId(appId).setStoreIdList(Arrays.asList(request.getStoreId()));
        Map<String, Object> bizParam = Maps.newHashMapWithExpectedSize(1);
        bizParam.put(ProjectConstant.ORDER_ID, request.getChannelOrderId());
        ChannelResponseDTO<String> channelResponseDTO = elmChannelGateService
                .sendPostReturnDto(ChannelPostELMEnum.ORDER_REVERSE_QUERY, baseRequest, bizParam);
        ;
        log.info("ElmChannelOrderServiceImpl.getOrderAfsApplyList, request:{}, channelResponseDTO:{}", request, channelResponseDTO);

        if (Objects.isNull(channelResponseDTO)) {
            result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取饿了么退款记录失败"));
            return result;
        }

        ChannelResponseResult<String> responseResult = channelResponseDTO.getBody();
        if (Objects.isNull(responseResult)) {
            result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取饿了么退款记录失败"));
            return result;
        }

        ElmReverseResult elmReverseResult = JSON.parseObject(responseResult.getData(), ElmReverseResult.class);

        if(elmReverseResult == null || elmReverseResult.getReverse_order_list() == null){
            result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "解析饿了么退款记录失败"));
            return result;
        }
        for (ElmReverseResult.ElmReverseOrderInfo elmReverseOrderInfo : elmReverseResult.getReverse_order_list()) {
            OrderAfsApplyDTO orderAfsApplyDTO = null;

            if (StringUtils.isNotEmpty(request.getAfterSaleId())) {
                // 如果传入的afterSaleId那么只返回对应的售后单
                if (request.getAfterSaleId().equals(elmReverseOrderInfo.getRefund_order_id())) {
                    orderAfsApplyDTO = elmReverseOrderMapping(elmReverseOrderInfo, elmReverseResult.getCommission_amount(), appId);
                }

            } else {
                orderAfsApplyDTO = elmReverseOrderMapping(elmReverseOrderInfo, elmReverseResult.getCommission_amount(), appId);
            }

            if (orderAfsApplyDTO != null) {
                orderAfsApplyDTO.setMerchantIncome(Optional.ofNullable(elmReverseResult.getMerchant_income()).orElse(0L).intValue());
                result.getAfsApplyList().add(orderAfsApplyDTO);
            }
        }

        result.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));

        return result;

    }

    private OrderAfsApplyDTO elmReverseOrderMapping(ElmReverseResult.ElmReverseOrderInfo elmReverseOrderInfo, Long elmCommission, long appId) {
        String reasonCodeDesc = Optional.ofNullable(elmReverseOrderInfo.getRefund_reason_code_desc()).orElse("");
        String reasonContent = Optional.ofNullable(elmReverseOrderInfo.getRefund_reason_content()).orElse("");
        String applyReason = reasonCodeDesc + (StringUtils.isBlank(reasonContent) ? "" : ":" + reasonContent);
        Map<String, String> extend = Maps.newHashMap();
        extend.put("appId", String.valueOf(appId));
        if (Objects.nonNull(elmReverseOrderInfo.getFast_refund_type())) {
            String fastRefundType = getElmFastRefundRemark(elmReverseOrderInfo.getFast_refund_type());
            extend.put("fastRefundType", fastRefundType);
        }
        OrderAfsApplyDTO orderAfsApply = new OrderAfsApplyDTO()
                .setChannelType(ChannelTypeEnum.ELEM.getCode())
                .setChannelOrderId(elmReverseOrderInfo.getOrder_id())
                .setAfterSaleId(elmReverseOrderInfo.getRefund_order_id())
                .setRefundApplyTime(elmReverseOrderInfo.getApply_time())
                .setApplyReason(applyReason)
                .setAfterSaleStatus(ChannelStatusConvertUtil.elmAfterSaleStatusMapping(elmReverseOrderInfo.getRefund_status(), elmReverseOrderInfo.getReturn_goods_status(), elmReverseOrderInfo.getWhether_return_goods()))
                .setRefundType(ChannelStatusConvertUtil.elmRefundTypeMapping(elmReverseOrderInfo.getIs_refund_all()))
                .setAfsProductList(elmReverseProductMapping(elmReverseOrderInfo.getSub_reverse_order_list()))
                .setCommission(Optional.ofNullable(elmCommission).orElse(0L).intValue())
                .setCommissionType(AfsCommissionTypeEnum.ORDER.getType())
                .setApplyOpType(ChannelStatusConvertUtil.elmOpUserType(elmReverseOrderInfo.getOperator_role()))
                .setRefundImgUrl(JSON.toJSONString(elmReverseOrderInfo.getImage_list()))
                .setExtend(extend)
                .setServiceType(elmReverseOrderInfo.getWhether_return_goods() == 1 ? ServiceTypeEnum.REFUND_GOODS.getCode() : ServiceTypeEnum.REFUND.getCode())
                .setProcessDeadline(Optional.ofNullable(elmReverseOrderInfo.getLast_node_timeout_time()).orElse(0L));


        // Integer refundPrice = orderAfsApply.getAfsProductList().stream().mapToInt(RefundProductDTO::getSkuRefundAmount).sum();

        orderAfsApply.setRefundPrice(elmReverseOrderInfo.getApply_refund_user_amount().intValue());
        if (elmReverseOrderInfo.getReturn_goods_info() != null) {
            orderAfsApply.setReturnGoodsStatusType(ChannelStatusConvertUtil.elmReturnGoodsStatusMapping(elmReverseOrderInfo));
            orderAfsApply.setPickUpRefundGoodsAddress(elmReverseOrderInfo.getReturn_goods_info().getPick_up_address());
            orderAfsApply.setRefundGoodsPrivacyContactPhone(elmReverseOrderInfo.getReturn_goods_info().getContact_phone());
        }
        boolean existsPartialRefund = orderAfsApply.getAfsProductList().stream()
                .map(RefundProductDTO::getPartialRefundFlag)
                .anyMatch(v -> Objects.equals(v, 1));
        if (existsPartialRefund && Objects.equals(orderAfsApply.getRefundType(), RefundTypeEnum.PART.getValue())) {
            orderAfsApply.setRefundType(RefundTypeEnum.AMOUNT.getValue());
        }
        //逆向单场景：100 售中, 200 售后
        if (Objects.equals(elmReverseOrderInfo.getScene(), "100")) {
            orderAfsApply.setAfsApplyType(1);
        } else {
            orderAfsApply.setAfsApplyType(2);
        }
        //购物金信息
        if (elmReverseOrderInfo.getRefund_shop_card_detail() != null) {
            orderAfsApply.setShopCardFee(parserElmAfsShopCardFee(elmReverseOrderInfo.getRefund_shop_card_detail()));
        }
        calculateVirtualGoods(elmReverseOrderInfo, orderAfsApply);

        return orderAfsApply;
    }

    private List<RefundProductDTO> elmReverseProductMapping(List<ElmReverseResult.ApiReverseOrderLineDTO> elmReverseProducts) {

        List<RefundProductDTO> ret = new LinkedList<>();

        if (CollectionUtils.isEmpty(elmReverseProducts)) {
            return ret;
        }

        for (ElmReverseResult.ApiReverseOrderLineDTO elmReverseProduct : elmReverseProducts) {
            if (!"0".equals(elmReverseProduct.getVirtual_type())) {
                // 非0表示的是其他退费，不属于商品维度，如包装费
                continue;
            }

            RefundProductDTO refundProductDTO = new RefundProductDTO();
            refundProductDTO.setSkuId(elmReverseProduct.getCustom_sku_id());
            refundProductDTO.setRefundPoiPromotion(elmReverseProduct.getDiscount_detail().getMerchant_discount_amount().intValue()); // todo lyt npe
            //82800617 记录【代理商优惠】至【整单优惠】平台承担部分
            int platformDiscountAmount = Optional.ofNullable(elmReverseProduct.getDiscount_detail().getPlatform_discount_amount()).orElse(0L).intValue();
            int agentDiscountAmount = Optional.ofNullable(elmReverseProduct.getDiscount_detail().getAgent_discount_amount()).orElse(0L).intValue();
            refundProductDTO.setRefundPlatPromotion(platformDiscountAmount + agentDiscountAmount);
            refundProductDTO.setSkuName(elmReverseProduct.getSku_name());
            refundProductDTO.setCount(elmReverseProduct.getRefund_quantity().intValue());
            refundProductDTO.setSkuRefundAmount(elmReverseProduct.getApply_refund_user_amount().intValue());
            refundProductDTO.setChannelOrderItemId(elmReverseProduct.getSub_biz_order_id());
            refundProductDTO.setFreeGift(elmReverseProduct.getFree_gift());
            if (Objects.equals(elmReverseProduct.getFund_calculate_type(), 1)) {
                refundProductDTO.setPartialRefundFlag(1);
            }
            ret.add(refundProductDTO);
        }
        return ret;
    }


    private String getElmFastRefundRemark(String fastRefundType) {
        Map<String, String> remarkMap = MccConfigUtil.getElmFastRefundTrackRemark();
        String remark = remarkMap.getOrDefault(fastRefundType, remarkMap.get("default"));
        return String.format("符合[%s]条件, 平台自动同意退款", remark);
    }

    /**
     * 计算虚拟商品费用
     *
     * @param elmReverseOrderInfo 退单明细
     * @param orderAfsApply       退单表头
     */
    private void calculateVirtualGoods(ElmReverseResult.ElmReverseOrderInfo elmReverseOrderInfo, OrderAfsApplyDTO orderAfsApply) {
        int freight = 0, packageFee = 0, platPackagePromotion = 0,
                poiPackagePromotion = 0, platPackageIncome = 0, poiPackageIncome = 0,
                payPackageFee = 0, poiLogisticsPromotion = 0,
                platLogisticsPromotion = 0;
        List<ElmReverseResult.ApiReverseOrderLineDTO> elmReverseProducts = elmReverseOrderInfo.getSub_reverse_order_list();
        if (CollectionUtils.isNotEmpty(elmReverseProducts)) {
            for (ElmReverseResult.ApiReverseOrderLineDTO elmReverseProduct : elmReverseProducts) {
                if (elmReverseProduct.getVirtual_type() == null) {
                    continue;
                }

                //包装费用
                switch (elmReverseProduct.getVirtual_type()) {
                    //包装
                    case "1":
                        packageFee += elmReverseProduct.getDiscount_detail().getTotal_price();
                        platPackagePromotion += elmReverseProduct.getDiscount_detail().getPlatform_discount_amount();
                        poiPackagePromotion += elmReverseProduct.getDiscount_detail().getMerchant_discount_amount();
                        platPackageIncome += elmReverseProduct.getReverse_package_settle().getPlatform_order_fee();
                        poiPackageIncome += elmReverseProduct.getReverse_package_settle().getMerchant_total_fee();
                        payPackageFee += (elmReverseProduct.getDiscount_detail().getTotal_price() - elmReverseProduct.getDiscount_detail().getDiscount_total_amount());
                        break;
                    //运费
                    case "4":
                        freight += elmReverseProduct.getDiscount_detail().getTotal_price();
                        poiLogisticsPromotion += elmReverseProduct.getDiscount_detail().getMerchant_discount_amount();
                        platLogisticsPromotion += elmReverseProduct.getDiscount_detail().getPlatform_discount_amount();
                        break;
                    default:
                        break;
                }
            }
        }
        orderAfsApply.setPayPackageFee(payPackageFee);
        orderAfsApply.setPlatPackagePromotion(platPackagePromotion);
        orderAfsApply.setPoiPackagePromotion(poiPackagePromotion);
        orderAfsApply.setOriginalPackageFee(packageFee);
        orderAfsApply.setPlatPackageIncome(platPackageIncome);
        orderAfsApply.setPoiPackageIncome(poiPackageIncome);

        orderAfsApply.setFreight(freight);
        orderAfsApply.setPoiLogisticsPromotion(poiLogisticsPromotion);
        orderAfsApply.setPlatLogisticsPromotion(platLogisticsPromotion);
    }

    @Override
    public ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request) {
        //渠道无对应接口，直接返回成功
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ResultCode.SUCCESS.getCode());
        resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
        return resultStatus;
    }

    @Override
    public ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request) {
        if (MccConfigUtil.getElmSelfDeliveryStatusCompensateSwitch()) {
            // 校验是否需要补偿漏传的自配送状态
            Optional<Integer> currentDeliveryStatusOptional = elmSquirrelService.getDeliveryStatusFromCache(request.getOrderId());
            if (elmSelfDeliveryStatusCompensateService.isNeedCompensate(request.getOrderId(), DeliveryStatusEnum.valueOf(request.getStatus()), currentDeliveryStatusOptional)) {
                // 补偿配送状态会先同步遗漏的配送状态，再同步当前配送状态，异步处理并保序
                elmSelfDeliveryStatusCompensateService.compensateDelivery(request, DeliveryStatusEnum.valueOf(request.getStatus()), currentDeliveryStatusOptional);
                return new ResultStatus(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null);
            }
        }

        ResultStatus resultStatus = new ResultStatus();
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId()).setStoreIdList(Arrays.asList(request.getShopId()));
        Map<String, Object> result;
        Map<String, Object> param = buildBusinessParam(request);
        if (Objects.isNull(param)) {
            log.warn("更新配送信息条件不足，不更新，直接返回成功:{}", request);
            resultStatus.setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
            return resultStatus;
        }
        //https://open-retail.ele.me/#/apidoc/me.ele.retail:order.selfDeliveryStateSync-3?aopApiCategory=order_all&type=null
        log.info("ElmChannelOrderServiceImpl.updateOrderDeliveryStatus  调用饿了么配送状态接口  baseRequest:{}   param:{}", baseRequest, param);
        result = elmChannelGateService.sendPostApp(baseUrl, orderSendout, baseRequest, param);
        log.info("ElmChannelOrderServiceImpl.updateOrderDeliveryStatus  调用饿了么配送接口响应:{}", result);

        try {
            DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(request.getStatus());
            if (SELF_DELIVERY_LOCATION_SYNC_SET.contains(deliveryStatus)) {
                //https://open-retail.ele.me/#/apidoc/me.ele.retail:order.selfDeliveryLocationSync-3?aopApiCategory=order_all&type=null
                Map<String, Object> riderParam = ImmutableMap.of(
                        "order_id", request.getOrderId(),
                        "location", new Location(
                                String.valueOf(Clock.systemUTC().millis() / SECOND),
                                "0",
                                String.valueOf(request.getLatitude()),
                                String.valueOf(request.getLongitude())
                        )
                );
                log.info("ElmChannelOrderServiceImpl.updateOrderDeliveryStatus  调用饿了么订单自配送接入骑手轨迹接口 baseRequest:{}  riderParam:{}", baseRequest, riderParam);
                Map<String, Object> syncLocationRes = elmChannelGateService.sendPostApp(baseUrl, selfDeliveryLocationSync, baseRequest, riderParam);
                log.info("ElmChannelOrderServiceImpl.updateOrderDeliveryStatus  调用饿了么订单自配送接入骑手轨迹接口响应:{}", syncLocationRes);
            }
        } catch (Exception e) {
            log.error("elmChannelOrderService.updateDeliveryInfo selfDeliveryLocationSync ERROR!", e);
        }

        if (Objects.nonNull(result.get(ProjectConstant.BODY)) &&
                Integer.valueOf(0).equals(((JSONObject) result.get(ProjectConstant.BODY)).get(ProjectConstant.ERRNO))) {
            return resultStatus.setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
        } else {
            return resultStatus.setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg());
        }
    }

    public ResultStatus compensateDeliveryInfo(UpdateDeliveryInfoRequest request) {
        ResultStatus resultStatus = new ResultStatus();
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId()).setStoreIdList(Collections.singletonList(request.getShopId()));
        Map<String, Object> result;
        Map<String, Object> param = buildBusinessParam(request);
        if (Objects.isNull(param)) {
            log.warn("ElmChannelOrderServiceImpl param is null");
            resultStatus.setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
            return resultStatus;
        }

        //https://open-retail.ele.me/#/apidoc/me.ele.retail:order.selfDeliveryStateSync-3?aopApiCategory=order_all&type=null
        log.info("ElmChannelOrderServiceImpl compensateDeliveryInfo baseRequest is {}, param is {}", baseRequest, param);
        result = elmChannelGateService.sendPostApp(baseUrl, orderSendout, baseRequest, param);
        log.info("ElmChannelOrderServiceImpl compensateDeliveryInfo result is {}", result);
        if (Objects.nonNull(result.get(ProjectConstant.BODY)) &&
                Integer.valueOf(0).equals(((JSONObject) result.get(ProjectConstant.BODY)).get(ProjectConstant.ERRNO))) {
            // 补偿牵牛花配送轨迹,配送状态回传成功后再补偿
            sendOrderTrack(request);
            return resultStatus.setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
        } else {
            return resultStatus.setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg());
        }
    }

    /**
     * 补偿配送轨迹
     */
    private void sendOrderTrack(UpdateDeliveryInfoRequest request) {
        try {
            if (!MccConfigUtil.getElmSelfDeliveryStatusCompensateTrackSwitch()) {
                return;
            }
            Integer orderBizTypeValue = DynamicOrderBizType.channelId2OrderBizTypeValue(request.getChannelId());
            Integer trackOpType = convertTrackOpType(request.getStatus());
            DeliveryTrackDetail deliveryTrackDetail = new DeliveryTrackDetail();
            deliveryTrackDetail.setRiderName("系统补偿-system");
            deliveryTrackDetail.setRiderPhone(request.getRiderPhone());
            OrderTrackEvent orderTrackEvent = new OrderTrackEvent();
            orderTrackEvent.setTrackSource(TrackSource.DELIVERY.getType());
            orderTrackEvent.setTrackOpType(trackOpType);
            orderTrackEvent.setOperateTime(Instant.now().toEpochMilli());
            orderTrackEvent.setTenantId(request.getTenantId());
            orderTrackEvent.setOrderBizType(orderBizTypeValue);
            orderTrackEvent.setUnifyOrderId(request.getOrderId());
            orderTrackEvent.setExt(JacksonUtils.toJson(deliveryTrackDetail));
            orderTrackService.sendAsyncMessage(orderTrackEvent);
        } catch (Exception e) {
            log.error("elmChannelOrderService.sendOrderTrack error: ", e);
        }
    }

    private Integer convertTrackOpType(Integer deliveryStatus) {
        DeliveryStatus status = DeliveryStatus.findByValue(deliveryStatus);
        if (Objects.isNull(status)) {
            log.warn("无法处理的配送状态:{}", deliveryStatus);
            return null;
        }
        switch (status) {
            //骑手已接单
            case RIDER_ACCEPTED_ORDER:
                return TrackOpType.RIDER_ASSIGNED.getOpType();
            //骑手已到店
            case RIDER_ARRIVE_SHOP:
                return TrackOpType.RIDER_REACH_SHOP.getOpType();
            //骑手已取餐
            case RIDER_TAKEN_MEAL:
                return TrackOpType.RIDER_TAKE_GOODS.getOpType();
            //骑手已送达
            case DELIVERY_COMPLETED:
                return TrackOpType.RIDER_DELIVERED.getOpType();
            default:
                return null;
        }
    }

    @Override
    public ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request) {
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId()).setStoreIdList(Arrays.asList(request.getShopId()));
        Map<String, Object> syncLocationRes;

        DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(request.getStatus());
        if (SELF_DELIVERY_LOCATION_SYNC_SET.contains(deliveryStatus)) {
            //https://open-retail.ele.me/#/apidoc/me.ele.retail:order.selfDeliveryLocationSync-3?aopApiCategory=order_all&type=null
            Map<String, Object> param = ImmutableMap.of(
                    "order_id", request.getOrderId(),
                    "location", new Location(
                            String.valueOf(Clock.systemUTC().millis() / SECOND),
                            "0",
                            String.valueOf(request.getLatitude()),
                            String.valueOf(request.getLongitude())
                    )
            );
            log.info("ElmChannelOrderServiceImpl.updateRiderInfo  调用饿了么订单自配送同步骑手轨迹接口 baseRequest:{}  param:{}", baseRequest, param);
            syncLocationRes = elmChannelGateService.sendPostApp(baseUrl, selfDeliveryLocationSync, baseRequest, param);
            log.info("ElmChannelOrderServiceImpl.updateRiderInfo  调用饿了么订单自配送同步骑手轨迹接口响应:{}", syncLocationRes);
            if (Objects.nonNull(syncLocationRes.get(ProjectConstant.BODY)) &&
                    Integer.valueOf(0).equals(((JSONObject) syncLocationRes.get(ProjectConstant.BODY)).get(ProjectConstant.ERRNO))) {
                return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
            } else {
                return new ResultStatus().setCode(ResultCode.FAIL.getCode()).setMsg(ResultCode.FAIL.getMsg());
            }
        }
        log.warn("ElmChannelOrderServiceImpl.updateRiderInfo  该状态下的骑手信息无需同步:{}", request.getStatus());
        return new ResultStatus().setCode(ResultCode.SUCCESS.getCode()).setMsg(ResultCode.SUCCESS.getMsg());
    }

    @Override
    public ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request) {
        return new ResultStatus(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(), ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg(), null);
    }

    @Override
    public ResultStatus selfDelivery(SelfDeliveryRequest request) {
        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId())
                .setStoreIdList(Arrays.asList(request.getStoreId()));
        Map bizParam = Maps.newHashMap();
        bizParam.put("order_id", request.getOrderId());
        log.info("ElmChannelOrderServiceImpl.selfDelivery  调用饿了么切换商家自配送  baseRequest:{}   param:{}", baseRequest, bizParam);
        ChannelResponseDTO<String> resultData = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.ORDER_SWITCH_SELF_DELIVERY, baseRequest, bizParam);
        log.info("ElmChannelOrderServiceImpl.selfDelivery  调用饿了么切换商家自配送响应:{}", resultData);
        return getResultStatus(resultData);
    }

    /**
     * 接口不再使用，饿了么开放平台已无order.getDeliveryStateRecord接口
     */
    @Override
    @Deprecated
    public QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request) {
        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());
        Map bizParam = Maps.newHashMap();
        bizParam.put("order_id", request.getOrderId());
        log.info("ElmChannelOrderServiceImpl.queryDeliveryExceptionDescription  调用饿了么获取订单配送记录  baseRequest:{}   param:{}", baseRequest, bizParam);
        ChannelResponseDTO<GetDeliveryStateRecordResult> resultData = elmChannelGateService.sendPost(ChannelPostELMEnum.GET_DELIVERY_STATE_RECORD, baseRequest, bizParam);
        log.info("ElmChannelOrderServiceImpl.queryDeliveryExceptionDescription  调用饿了么获取订单配送记录响应:{}", resultData);
        QueryDeliveryExceptionDescriptionResponse response = new QueryDeliveryExceptionDescriptionResponse();
        try {
            response.setExceptionDescription(resultData.getCoreData().getExceptionDescription());
        } catch (Exception e) {
            log.warn("ElmChannelOrderServiceImpl.queryDeliveryExceptionDescription 解析响应错误:{}", resultData, e);
        }

        return response;
    }

    @Override
    public ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request) {
        return null;
    }

    @Override
    public QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request) {
        QueryChannelOrderListResult result = new QueryChannelOrderListResult();
        List<ChannelResponseDTO<String>> resultMapList = new ArrayList<>();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setAppId(request.getAppId());
        addStoreId2BaseRequest(request.getShopId(), baseRequest);
        ChannelOrderListParam bizParam = new ChannelOrderListParam();
        bizParam.setPage(request.getPage());
        bizParam.setStart_time(request.getStartTime());
        bizParam.setEnd_time(request.getEndTime());
        if (request.getShopId() != 0) {
            bizParam.setShop_id(String.valueOf(request.getShopId()));
        }
        if (StringUtils.isNotBlank(request.getChannelPoiId())) {
            bizParam.setBaidu_shop_id(request.getChannelPoiId());
        }
        if (request.getPageSize() != 0) {
            bizParam.setPage_size(request.getPageSize());
        }
        ChannelResponseDTO<String> resultMap = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.GET_ORDER_LIST, baseRequest, bizParam);
        resultMapList.add(resultMap);

        log.info("ElmChannelOrderServiceImpl.queryChannelOrderList, request:{}, resultMap:{}", request, resultMapList);
        return buildQueryChannelOrderListResult(result, resultMapList);
    }

    @Override
    public QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request) {
        return null;
    }

    @Override
    public CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request) {
        return null;
    }

    @Override
    public GetOrderPrivacyPhoneResult queryOrderPrivacyPhone(GetOrderPrivacyPhoneRequest request) {
        log.info("ElmChannelOrderServiceImpl.queryOrderPrivacyPhone, request:{}", request);
        GetOrderAfsApplyListRequest getOrderAfsApplyListRequest = new GetOrderAfsApplyListRequest();
        getOrderAfsApplyListRequest.setChannelOrderId(request.getOrderId());
        getOrderAfsApplyListRequest.setStoreId(request.getStoreId());
        getOrderAfsApplyListRequest.setTenantId(request.getTenantId());
        getOrderAfsApplyListRequest.setChannelType(request.getChannelId());
        GetOrderAfsApplyListResult result = getOrderAfsApplyList(getOrderAfsApplyListRequest);

        GetOrderPrivacyPhoneResult resp = new GetOrderPrivacyPhoneResult();
        if (Objects.isNull(result.getStatus()) || result.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "饿了么调用渠道查询订单隐私号失败, request:{}", request));
        }
        List<OrderAfsApplyDTO> orderAfsApplyDTOList = result.getAfsApplyList();
        if (CollectionUtils.isEmpty(orderAfsApplyDTOList)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "饿了么调用渠道查询订单隐私号失败, request:{}", request));
        }

        Optional<OrderAfsApplyDTO> orderAfsApplyDTO = orderAfsApplyDTOList.stream().filter(item -> item.getAfterSaleId().equals(request.getAfterSaleId())).findFirst();
        String privacyPhone = orderAfsApplyDTO.map(OrderAfsApplyDTO::getRefundGoodsPrivacyContactPhone).orElse(null);
        // 饿了么存在退单详情查不到的情况，此时查正单兜底
        if (StringUtils.isBlank(privacyPhone)) {
            log.info("饿了么调用渠道查询退单详情订单隐私号为空, request:{}", request);
            GetChannelOrderDetailRequest getChannelOrderDetailRequest = new GetChannelOrderDetailRequest();
            getChannelOrderDetailRequest.setChannelId(ChannelTypeEnum.ELEM.getCode());
            getChannelOrderDetailRequest.setOrderId(request.getOrderId());
            getChannelOrderDetailRequest.setTenantId(request.getTenantId());
            getChannelOrderDetailRequest.setSotreId(request.getStoreId());
            GetChannelOrderDetailResult channelOrderDetail = getChannelOrderDetail(getChannelOrderDetailRequest);
            log.info("ElmChannelOrderServiceImpl.queryOrderPrivacyPhone, 查询渠道订单详情：request={},result={}", request, result);
            if (channelOrderDetail == null || channelOrderDetail.getChannelOrderDetail() == null || channelOrderDetail.getChannelOrderDetail().getDeliveryDetail() == null) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "饿了么调用渠道查询订单隐私号为空, request:{}", request));
            }
            privacyPhone = channelOrderDetail.getChannelOrderDetail().getDeliveryDetail().getUserPhone();
            if (StringUtils.isBlank(privacyPhone)) {
                return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "饿了么调用渠道查询订单隐私号为空, request:{}", request));
            }
        }

        resp.setPrivacyPhone(privacyPhone);
        resp.setStatus(ResultGenerator.genSuccessResult());
        log.info("ElmChannelOrderServiceImpl.queryOrderPrivacyPhone, resp:{}", resp);
        return resp;
    }

    private Map<String, Object> buildBusinessParam(UpdateDeliveryInfoRequest request) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("order_id", request.getOrderId());
        param.put("distributor_id", 201);
        param.put("state", 21);
        param.put("knight", new Knight(1L, request.getRiderName(), request.getRiderPhone()));
        try {
            param.put("selfStatus", convertDeliveryStatus(request.getStatus()));
            param.put("distributorInfoDTO", getDistributorInfoDTO(request.getDeliveryChannelId()));
        } catch (ChannelBizException e) {
            log.warn("饿了么订单更新配送状态转换失败 status:{}", request.getStatus(), e);
            return null;
        }
        return param;
    }

    private DistributorInfoDTO getDistributorInfoDTO(Integer deliveryChannelId) {
        if (Objects.isNull(deliveryChannelId)) {
            return DistributorInfoDTO.DEFAULT;
        }

        Map<String, ElmChannelOrderServiceImpl.DistributorInfoDTO> distributorMapping = MccConfigUtil.getElmSelfDeliveryDistributorMapping();
        if (MapUtils.isEmpty(distributorMapping)) {
            return DistributorInfoDTO.DEFAULT;
        }
        return distributorMapping.getOrDefault(String.valueOf(deliveryChannelId), DistributorInfoDTO.DEFAULT);
    }

    @Override
    public QueryAfsOrderListResult queryAfsOrderList(QueryChannelAfsOrderListRequest request) {
        QueryAfsOrderListResult result = new QueryAfsOrderListResult();
        List<ChannelResponseDTO<String>> resultMapList = new ArrayList<>();
        BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setAppId(request.getAppId());
        addStoreId2BaseRequest(request.getShopId(), baseRequest);
        ChannelAfsOrderListParam bizParam = new ChannelAfsOrderListParam();
        bizParam.setPage_index(request.getPage());
        bizParam.setStart_time(request.getStartTime());
        bizParam.setEnd_time(request.getEndTime());
        if (request.getShopId() != 0) {
            bizParam.setShop_id(String.valueOf(request.getShopId()));
        }
        if (StringUtils.isNotBlank(request.getChannelPoiId())) {
            bizParam.setPlatform_shop_id(request.getChannelPoiId());
        }
        ChannelResponseDTO<String> resultMap = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.GET_AFS_ORDER_LIST, baseRequest, bizParam);
        resultMapList.add(resultMap);

        log.info("ElmChannelOrderServiceImpl.queryAfsOrderList, request:{}, resultMap:{}", request, resultMapList);
        return buildListResult(result, resultMapList);
    }

    private QueryAfsOrderListResult buildListResult(QueryAfsOrderListResult result, List<ChannelResponseDTO<String>> resultMapList) {
        if (CollectionUtils.isEmpty(resultMapList)) {
            return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取租户订单分页列表错误"));
        }

        for (ChannelResponseDTO<String> resultMap : resultMapList) {
            //支付状态为已支付
            String alreadyPayed = "2";
            ChannelResponseResult<String> channelResponseResult = resultMap.getBody();
            if (channelResponseResult.getErrno().equals(ResultCodeEnum.SUCCESS.getValue())) {
                ChannelAfsOrderListResult orderListResult = JSON.parseObject(channelResponseResult.getData(), ChannelAfsOrderListResult.class);
                result.setPageCount(orderListResult.getPage_size());
                result.setTotal(orderListResult.getTotal());
                List<AfsOrderInfo> afsOrderInfoList = orderListResult.getQuery_list().stream().map(dto -> {
                    AfsOrderInfo info = new AfsOrderInfo();
                    info.setChannelOrderId(dto.getOrderId().toString());
                    info.setChannelAfsOrderId(dto.getRefundOrderId().toString());
                    return info;
                }).collect(Collectors.toList());
                result.setChannelAfsOrderIdList(afsOrderInfoList);
            }
        }

        return result.setStatus(ResultGenerator.genSuccessResult());

    }

    private int convertDeliveryStatus(int status) {
        DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(status);
        if (Objects.isNull(deliveryStatus)) {
            log.warn("饿了么渠道无法处理配送状态:{}", status);
            throw new ChannelBizException("饿了么渠道无法处理配送状态:" + status);
        }
        switch (deliveryStatus) {
            // 配送待分配
            case WAIT_DISPATCH_RIDER:
                return 2;
            //骑手已接单
            case RIDER_ACCEPTED_ORDER:
                return 3;
            //骑手已到店
            case RIDER_ARRIVE_SHOP:
                return 8;
            //骑手已取餐
            case RIDER_TAKEN_MEAL:
                return 20;
            //骑手已送达
            case DELIVERY_COMPLETED:
                return 30;
            //配送取消
            case DELIVERY_CANCEL:
                return 6;
            //配送拒单
            case DELIVERY_FAILED:
                return 101;
            //配送异常
            case DELIVERY_EXCEPTION:
                return 7;
            //无法处理，抛出异常
            default:
                throw new ChannelBizException("饿了么渠道不需要回传的配送状态:" + status);
        }
    }

    private boolean isFinishedOrder(String finished_time) {
        //完成的订单finished_time会有完成秒级时间戳
        return StringUtils.isNotEmpty(finished_time) && !UN_FINISHED_TIME.equals(finished_time);
    }

    private QueryChannelOrderListResult buildQueryChannelOrderListResult(QueryChannelOrderListResult result, ChannelResponseDTO<String> resultMap) {
        //支付状态为已支付
        String alreadyPayed = "2";
        ChannelResponseResult<String> channelResponseResult = resultMap.getBody();
        if (channelResponseResult.getErrno().equals(ResultCodeEnum.SUCCESS.getValue())) {
            ChannelOrderListResult orderListResult = JSON.parseObject(channelResponseResult.getData(), ChannelOrderListResult.class);
            result.setPage(orderListResult.getPage());
            result.setPageCount(orderListResult.getPages());
            result.setTotal(orderListResult.getTotal());
            List<String> orderIdList = orderListResult.getList().stream().filter(dto -> alreadyPayed.equals(dto.getPay_status())).map(ChannelOrderPageQueryDTO::getOrder_id).collect(Collectors.toList());
            return result.setStatus(ResultGenerator.genSuccessResult()).setChannelOrderIdList(orderIdList);
        }
        return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, channelResponseResult.getError()));

    }

    private void addStoreId2BaseRequest(long storeId, BaseRequest baseRequest) {
        if (storeId <= NumberUtils.LONG_ZERO) {
            return;
        }
        baseRequest.setStoreIdList(ImmutableList.of(storeId));
    }

    /**
     * 饿了么接口限流错误码
     */
    private static final Integer ELM_LIMIT_CODE = 20502;

    private QueryChannelOrderListResult buildQueryChannelOrderListResult(QueryChannelOrderListResult result, List<ChannelResponseDTO<String>> resultMapList) {
        if (CollectionUtils.isEmpty(resultMapList)) {
            return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取租户订单分页列表错误"));
        }
        List<String> orderIdList = new ArrayList<>();
        boolean hasTriggerLimit = false;
        for (ChannelResponseDTO<String> resultMap : resultMapList) {
            //支付状态为已支付
            String alreadyPayed = "2";
            ChannelResponseResult<String> channelResponseResult = resultMap.getBody();
            if (channelResponseResult.getErrno().equals(ResultCodeEnum.SUCCESS.getValue())) {
                ChannelOrderListResult orderListResult = JSON.parseObject(channelResponseResult.getData(), ChannelOrderListResult.class);
                result.setPage(orderListResult.getPage());
                result.setPageCount(orderListResult.getPages());
                result.setTotal(orderListResult.getTotal());
                List<String> orderIds = orderListResult.getList().stream().filter(dto -> alreadyPayed.equals(dto.getPay_status())).map(ChannelOrderPageQueryDTO::getOrder_id).collect(Collectors.toList());
                orderIdList.addAll(orderIds);
            } else if (channelResponseResult.getErrno().equals(ELM_LIMIT_CODE)){
                // 饿了么接口限流
                hasTriggerLimit = true;
            }
        }
        if (CollectionUtils.isEmpty(orderIdList)) {
            ResultCode errorCode = hasTriggerLimit ? ResultCode.TRIGGER_LIMIT : ResultCode.FAIL;
            return result.setStatus(ResultGenerator.genResult(errorCode, "调用渠道获取租户订单分页列表错误"));
        }
        return result.setStatus(ResultGenerator.genSuccessResult()).setChannelOrderIdList(orderIdList);
    }


    @Override
    public ChannelOrderMoneyRefundItemResult queryChannelOrderMoneyRefundItemList(ChannelOrderMoneyRefundItemRequest request) {
        ChannelOrderMoneyRefundItemResult result = new ChannelOrderMoneyRefundItemResult();
        result.setStatus(ResultGenerator.genSuccessResult());
        try {
            BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId())
                    .setStoreIdList(Lists.newArrayList(request.getStoreId()));
            Map bizParam = Maps.newHashMap();
            bizParam.put("order_id", request.getOrderId());
            bizParam.put("refund_all_sub_order", true);
            ChannelResponseDTO<String> resultData = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.ORDER_REVERSE_CONSULT, baseRequest, bizParam);
            log.info("ElmChannelOrderServiceImpl.queryChannelOrderMoneyRefundItemList, request:{}, resultMap:{}", request, resultData);
            return buildQueryChannelOrderListResult(result, resultData.getBody());
        } catch (Exception e) {
            log.error("elmChannelOrderService queryChannelOrderMoneyRefundItemList ERROR!", e);
            return result.setStatus(ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }
    }

    private ChannelOrderMoneyRefundItemResult buildQueryChannelOrderListResult(ChannelOrderMoneyRefundItemResult result,
                                                                               ChannelResponseResult<String> responseResult) {
        if (Objects.isNull(responseResult)) {
            result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取饿了么可退商品列表失败"));
            return result;
        }
        if (!responseResult.getErrno().equals(ResultCodeEnum.SUCCESS.getValue())) {
            result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, responseResult.getError()));
            return result;
        }

        ElmReverseConsultResult elmReverseConsultResult = JSON.parseObject(responseResult.getData(), ElmReverseConsultResult.class);
        if (elmReverseConsultResult != null && CollectionUtils.isNotEmpty(elmReverseConsultResult.getSub_order_consult_result_list())) {
            List<ChannelOrderMoneyRefundItemDTO> moneyRefundItemDTOList = new ArrayList<>();
            for (ElmReverseConsultResult.OrderLineConsultResultDTO dto : elmReverseConsultResult.getSub_order_consult_result_list()) {
                //虚拟商品不做展示
                if (dto.getCommodity_type() == 4) {
                    continue;
                }
                ChannelOrderMoneyRefundItemDTO moneyRefundItemDTO = new ChannelOrderMoneyRefundItemDTO();
                moneyRefundItemDTO.setSkuId(StringUtils.isNotEmpty(dto.getCustom_sku_spec_id()) ? dto.getCustom_sku_spec_id() : dto.getCustom_sku_id());
                moneyRefundItemDTO.setSkuName(dto.getSku_name());
                moneyRefundItemDTO.setCanRefundSkuCount(dto.getCur_refund_quantity().intValue());
                moneyRefundItemDTO.setCanRefundMoney(dto.getCur_refund_user_amount().intValue());
                moneyRefundItemDTO.setChannelItemId(dto.getSub_biz_order_id());
                moneyRefundItemDTO.setCurrentPrice(dto.getTotalRefundUserAmount().intValue());
                moneyRefundItemDTOList.add(moneyRefundItemDTO);
            }
            result.setMoneyRefundItemDTOList(moneyRefundItemDTOList);
        }
        return result;
    }

    @Override
    public ResultStatus moneyRefund(MoneyRefundRequest request) {
        try {
            BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId())
                    .setStoreIdList(Lists.newArrayList(request.getStoreId()));
            ElmPoiReverseApplyParam bizParam = elmConverterService.moneyRefundMapping(request);
            ChannelResponseDTO<String> resultData = elmChannelGateService.sendPostReturnDto(ChannelPostELMEnum.POI_REVERSE_APPLY, baseRequest, bizParam);
            log.info("ElmChannelOrderServiceImpl.moneyRefund, request:{}, resultMap:{}", request, resultData);
            return getResultStatus(resultData);
        } catch (Exception e) {
            log.error("elmChannelOrderService poiPartRefundApply ERROR!", e);
            return ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage());
        }
    }

    @Data
    @AllArgsConstructor
    public static class Knight {
        private Long id;
        private String name;
        private String phone;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DistributorInfoDTO {
        private static final DistributorInfoDTO DEFAULT = new DistributorInfoDTO("99999", "其他配送", "");

        private String distributorTypeId;
        private String distributorName;
        private String distributorPhone;
    }

    @Data
    @AllArgsConstructor
    public static class Location {
        /**
         * 当前时间，格式：10位时间戳
         */
        private String UTC;

        /**
         * 海拔高度
         */
        private String altitude;

        /**
         * 纬度（高德坐标系）
         */
        private String latitude;

        /**
         * 经度（高德坐标系）
         */
        private String longitude;
    }
}
