package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.CategoryAttrValueInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.OriginPlaceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.AreaInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.GetAreaListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.GetAreaListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.AttrValueInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrValueResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelActDataInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPullPhoneParamInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuActAllDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuActResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuActivityParamInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelSkuActivityResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.PullPhoneInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.MtChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelActDataInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QuerySkuActivityInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.BatchPullPhoneRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.CommonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MtSignUtils;

/**
 * 美团渠道部分公共业务功能
 *
 * @description:
 * @author: zhaolei12
 * @create: 2019-03-15 11:38
 */
@Service("mtBrandChannelCommonService")
public class MtBrandChannelCommonServiceImpl implements MtChannelCommonService, ChannelCommonService {
    /**
     * 新供给渠道网关服务
     */
    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private MtConverterService mtConverterService;

    @Value("${mt.url.base}" + "${mt.url.actRetailDiscountList}")
    private String actRetailDiscountListUrl;

    @Value("${mt.url.base}" + "${mt.url.actAllGetByAppFoodCodes}")
    private String actAllGetByAppFoodCodesUrl;

    @Value("${mt.url.base}" + "${mt.url.getOriginList}")
    private String getOriginListUrl;

    @Autowired
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private CommonLogger log;

    @Resource
    private BaseConverterService baseConverterService;

    @Deprecated
    @Override
    public List<PullPhoneInfo> batchPullPhoneNumber(BatchPullPhoneRequest request) {
        ChannelPullPhoneParamInfo pullPhoneParamInfo = new ChannelPullPhoneParamInfo();
        pullPhoneParamInfo.setOffset(request.getOffset());
        pullPhoneParamInfo.setLimit(request.getLimit());
        Long storeId = request.getStoreId() == 0L ? null : request.getStoreId();
        ChannelResponseDTO channelResponseDTO;

        if (storeId != null) {
            BaseRequest baseRequest = new BaseRequest()
                    .setTenantId(request.getTenantId()).setChannelId(request.getChannelId())
                    .setStoreIdList(Lists.newArrayList(storeId));

            // 渠道接口
            Map<Long, ChannelResponseDTO> postResult = getMtBrandChannelGateService()
                    .sendPost(ChannelPostMTEnum.ORDER_BATCH_PULL_PHONE, baseRequest, pullPhoneParamInfo);
            log.info("MtBrandChannelBaseServiceImpl.batchPullPhoneNumber, request:{}, postResult:{}", request, postResult);
            if (MapUtils.isEmpty(postResult)) {
                return Lists.emptyList();
            }
            channelResponseDTO = postResult.get(request.getStoreId());
        } else {
            BaseRequestSimple baseRequestSimple = new BaseRequestSimple()
                    .setTenantId(request.getTenantId())
                    .setChannelId(request.getChannelId());

            // 渠道接口
            channelResponseDTO = getMtBrandChannelGateService()
                    .sendPost(ChannelPostMTEnum.ORDER_BATCH_PULL_PHONE, baseRequestSimple, storeId, pullPhoneParamInfo);
            log.info("MtChannelCommonServiceImpl.batchPullPhoneNumber, request:{}, postResult:{}", request, channelResponseDTO);
        }
        if (Objects.isNull(channelResponseDTO)) {
            return Lists.emptyList();
        }
        if (channelResponseDTO.isSuccess()) {
            return JSON.parseArray(channelResponseDTO.getData(), PullPhoneInfo.class);
        }
        return Lists.emptyList();
    }

    /**
     * 单门店：查询门店活动商品
     *
     * @param baseRequest 新供给在请求体中，必须传入storeIdList（secret需要通过门店id获取）
     * @return
     */
    @Override
    public List<ChannelActDataInfoDTO> queryPoiActivity(BaseRequest baseRequest) {
        List<ChannelActDataInfo> channelActDataInfoList = Lists.newArrayList();
        ChannelPullPhoneParamInfo paramInfo = new ChannelPullPhoneParamInfo();
        String channelStoreKey = KeyUtils.genChannelStoreKey(baseRequest.getTenantId(), baseRequest.getChannelId(),
                baseRequest.getStoreIdList().get(0));
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(baseRequest.getTenantId(), baseRequest.getChannelId(), baseRequest.getStoreIdList());
        paramInfo.setApp_poi_code(channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode());
        //分页大小
        int actRetailDiscountPageSize = MccConfigUtil.getActRetailDiscountPageSize();
        paramInfo.setLimit(actRetailDiscountPageSize);
        int offset = 0;

        List<ChannelActDataInfo> partActDataInfoList;
        do {
            paramInfo.setOffset(offset++);

            // 渠道接口
            Map<String, Object> getResult = getMtBrandChannelGateService().sendGet(getActRetailDiscountListUrl(), null, baseRequest, paramInfo);
            log.info("MtBrandChannelBaseServiceImpl.queryPoiActivity, baseRequest:{}, getResult:{}", baseRequest, getResult);
            if (MapUtils.isEmpty(getResult)) {
                return Lists.emptyList();
            }
            ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
            if (Objects.isNull(channelResponseDTO)) {
                return Lists.emptyList();
            }
            partActDataInfoList = Lists.newArrayList();
            //返回data不为空，并且不是失败（ng）
            if (StringUtils.isNotBlank(channelResponseDTO.getData()) && !ProjectConstant.NG.equals(channelResponseDTO.getData())) {
                partActDataInfoList = JSON.parseArray(channelResponseDTO.getData(), ChannelActDataInfo.class);
                channelActDataInfoList.addAll(partActDataInfoList);
            }
        } while (partActDataInfoList.size() >= actRetailDiscountPageSize);
        return mtConverterService.channelActDataInfoListMapping(channelActDataInfoList);
    }

    /**
     * 单门店：查询商品参加的活动
     *
     * @param request 新供给在请求体中，必须传入storeIdList（secret需要通过门店id获取）
     * @return
     */
    @Override
    public ChannelSkuActAllDetail querySkuActivityInfoList(QuerySkuActivityInfoRequest request) {

        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getTenantId(), request.getChannelId(), request.getStoreId());
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService
                .getChannelPoiCode(request.getTenantId(), request.getChannelId(), Lists.newArrayList(request.getStoreId()));

        BaseRequest baseRequest =
                new BaseRequest().setTenantId(request.getTenantId()).setChannelId(request.getChannelId()).setStoreIdList(Lists.newArrayList(request.getStoreId()));
        ChannelSkuActivityParamInfo paramInfo = new ChannelSkuActivityParamInfo();
        paramInfo.setApp_poi_code(channelStoreDOMap.get(channelStoreKey).getChannelOnlinePoiCode());
        paramInfo.setApp_food_codes(Joiner.on(",").join(request.getCustomSpuIds()));
        paramInfo.setPage_size(50);

        ChannelSkuActAllDetail actAllDetail = new ChannelSkuActAllDetail();
        actAllDetail.setSkuErrMap(Maps.newHashMap());
        actAllDetail.setActivityDetails(Lists.newArrayList());

        List<Integer> activityTypeList = CollectionUtils.isNotEmpty(request.getActivityTypeList()) ? request.getActivityTypeList() : Arrays.asList(0);
        activityTypeList.stream().forEach(activityType -> {
            int pageNum = 1;
            boolean isContinue = true;
            do {
                paramInfo.setPage_num(pageNum);
                paramInfo.setQuery_type(activityType);

                // 渠道接口
                Map<String, Object> getResult = getMtBrandChannelGateService().sendGet(getActAllGetByAppFoodCodesUrl(), null, baseRequest, paramInfo);
                log.info("MtBrandChannelBaseServiceImpl.querySkuActivityInfoList, baseRequest:{}, getResult:{}", baseRequest, getResult);
                if (MapUtils.isEmpty(getResult)) {
                    break;
                }
                ChannelSkuActResponseDTO responseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelSkuActResponseDTO.class);
                if (Objects.isNull(responseDTO)) {
                    break;
                }
                if (responseDTO.getError() != null) {
                    throw new IllegalStateException("查询活动异常:" + responseDTO.getError().getMsg());
                }
                // 若存在错误商品信息，则添加
                if (CollectionUtils.isNotEmpty(responseDTO.getError_list())) {
                    responseDTO.getError_list().forEach(errInfo -> {
                        actAllDetail.getSkuErrMap().put(errInfo.getApp_food_code(), errInfo.getMsg());
                    });
                    // 如果失败了就不再循环，防止出现无限循环
                    isContinue = false;
                }

                //返回data不为空，并且不是失败（ng）
                if (StringUtils.isNotBlank(responseDTO.getData()) && !ProjectConstant.NG.equals(responseDTO.getData())) {
                    ChannelSkuActivityResult activityResult = JSON.parseObject(responseDTO.getData(), ChannelSkuActivityResult.class);
                    if (CollectionUtils.isNotEmpty(activityResult.getAct_list())) {
                        actAllDetail.getActivityDetails().addAll(activityResult.getAct_list());
                    }
                    if (isContinue) {
                        isContinue = activityResult.getCount() >= paramInfo.getPage_size();
                    }
                }
                pageNum++;
                // 为了防止无限次调用，这里兜底不能超过10页
            } while (isContinue && pageNum <= 10);
        });

        return actAllDetail;
    }

    /**
     * 当前渠道回调鉴权开关关闭，未进入本逻辑
     *
     * @param url
     * @param params
     * @param ip
     * @param originalUrl
     * @return
     */
    @Override
    public ResultStatus auth(String url, Map<String, String> params, String ip, String originalUrl) {
        // 获取租户ID
        String tenantAppId = params.get("app_id");
        if (StringUtils.isBlank(tenantAppId)) {
            return ResultGenerator.genFailResult("无效请求, 无Appid").setData("appId为空");
        }
        Long tenantId = copAccessConfigService.selectTenantId(currentChannelType().getCode(), tenantAppId);
        if (tenantId == null) {
            return ResultGenerator.genFailResult("未获取到租户配置").setData("租户为空");
        }

        // 获取白名单
        String mtWhiteList = MccConfigUtil.getMtWhiteList();
        Set<Long> tenantIds = new HashSet<>();
        if (StringUtils.isNotBlank(mtWhiteList)) {
            String[] tenantIdStrings = mtWhiteList.split(",");
            for (String tenantIdString : tenantIdStrings) {
                tenantIds.add(Long.valueOf(tenantIdString.trim()));
            }
        }
        if (tenantIds.contains(tenantId)) {
            return ResultGenerator.genSuccessResult().setData(String.valueOf(tenantId));
        }

        String secret = getSecret(currentChannelType().getCode(), tenantAppId);
        if (StringUtils.isBlank(secret)) {
            log.warn("美团租户 tenantId:{}, secret为空", tenantId);
            return ResultGenerator.genFailResult("未获取到secret").setData(String.valueOf(tenantId));
        }
        Map<String, String> originParams = new HashMap<>(params);
        String requestSign = params.remove("sig");
        String sign = "";
        String paramsForSig = "";
        try {
            // 获取美团url
            if (StringUtils.isNotBlank(originalUrl)) {
                url = originalUrl;
            } else {
                url = getMtUrl(tenantId, url, ip);
            }

            for (String key : params.keySet()) {
                String decodeValue = CommonUtils.decode(CommonUtils.decode(params.get(key)));
                params.put(key, decodeValue);
            }
            paramsForSig = MtSignUtils.concatParamsForCallBack(new HashMap<>(params));
            sign = MtSignUtils.getSig(url, paramsForSig, secret);
        } catch (Exception e) {
            // 美团开放平台encode次数不一致，大部分是两次，如果是一次可能会有decode异常的情况，捕获异常不作处理
            log.warn("Mtauth.CommonUtils.decode异常", e);
        }

        log.info("美团验签，租户ID:{}, 开放平台sign:{}, 渠道网关sign:{}", tenantId, requestSign, sign);
        if (!sign.equalsIgnoreCase(requestSign)) {
            log.warn("美团验签失败baseString:{}", url + "?" + paramsForSig + secret);
            if (decodeOneTime(url, tenantId, secret, originParams, requestSign)) {
                return ResultGenerator.genFailResult("验签失败").setData(String.valueOf(tenantId));
            }
        }
        return ResultGenerator.genSuccessResult().setData(String.valueOf(tenantId));
    }

    private boolean decodeOneTime(String url, Long tenantId, String secret, Map<String, String> originParams, String requestSign) {
        String paramsForSig = "";
        String sign = "";
        try {
            for (String key : originParams.keySet()) {
                String decodeValue = CommonUtils.decode(originParams.get(key));
                originParams.put(key, decodeValue);
            }
            originParams.remove("sig");
            paramsForSig = MtSignUtils.concatParamsForCallBack(new HashMap<>(originParams));
            sign = MtSignUtils.getSig(url, paramsForSig, secret);
            log.info("美团验签，租户ID:{}, 开放平台sign:{}, 渠道网关sign:{}", tenantId, requestSign, sign);
        } catch (Exception e) {
            log.warn("Mtauth.CommonUtils.decode异常", e);
        }
        if (!sign.equalsIgnoreCase(requestSign)) {
            log.warn("美团验签失败baseString:{}", url + "?" + paramsForSig + secret);
            return true;
        }
        return false;
    }

    private String getMtUrl(Long tenantId, String url, String ip) {
        String jsonString = MccConfigUtil.getTenantIp();
        Map<String, String> tenantIp = JSON.parseObject(jsonString, Map.class);
        if (tenantIp.get(String.valueOf(tenantId)) != null) {
            ip = tenantIp.get(String.valueOf(tenantId));
        }
        //如果x real ip不在vipList中说明是渠道回调直接填的shepherd域名 直接返回url
        if (!isContainVipList(ip)) {
            return url;
        }
        //后续情况为x real ip不为空且在vip集群中则拼装url
        String[] urls = url.split("//");
        String[] urlPaths = urls[1].split("/");
        if (ip.matches("[\\d.:]+")) {
            if (url.contains("test")) {
                ip = ip + "/test";
            } else if (url.contains("dev")) {
                ip = ip + "/dev";
            } else if (url.contains("st")) {
                ip = ip + "/stage";
            }
        }
        urlPaths[0] = ip;
        url = urls[0] + "//" + StringUtils.join(urlPaths, "/");
        return url;
    }

    private String getSecret(int channelId, String tenantAppId) {
        String sysParams = copAccessConfigService.selectByTenantAppIdAndChannelId(tenantAppId, channelId).getSysParams();
        return JSON.parseObject(sysParams).getString("secret");
    }

    /**
     * @param ip
     * @return boolean
     * @throws
     * @Description: 判断当前 x real ip是否在vip列表中
     */
    private boolean isContainVipList(String ip) {
        String vipList = MccConfigUtil.getVipList();
        String[] vips = vipList.split(",");
        for (String vip : vips) {
            if (vip.equals(ip)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 匹配的渠道类型/默认美团
     *
     * @return 渠道类型
     */
    public ChannelTypeEnum currentChannelType() {
        return ChannelTypeEnum.MEITUAN;
    }

    @Override
    public GetAreaListResponse getAreaList(GetAreaListRequest request) {
        GetAreaListResponse response = new GetAreaListResponse();
        Map<String, Object> bizParam = Maps.newHashMap();
        if (request.isSetParentAreaId()) {
            bizParam.put(ProjectConstant.PARENT_ID, request.getParentAreaId());
        }
        if (request.isSetKeyword()) {
            bizParam.put(ProjectConstant.MT_KEYWORD, request.getKeyword());
        }
        BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
        //获取系统级参数
        Map<String, Object> sysParam = getMtBrandChannelGateService().getChannelSysParams(baseRequest.getTenantId(), baseRequest.getChannelId());
        Map areaMap = getMtBrandChannelGateService().sendGet(getGetOriginListUrl(), null, baseRequest, sysParam, bizParam);
        if (MapUtils.isEmpty(areaMap) || Objects.isNull(areaMap.get(ProjectConstant.SUCCESS_LIST)) || ProjectConstant.NG.equals(areaMap.get(ProjectConstant.DATA))) {
            return response.setStatus(ResultGenerator.genFailResult("获取地区列表失败"));
        }
        List<AreaInfo> areaInfoList = Lists.newArrayList();
        JSONArray valueJa = (JSONArray) areaMap.get(ProjectConstant.SUCCESS_LIST);
        List<OriginPlaceInfo> resultValueList = valueJa.toJavaList(OriginPlaceInfo.class);
        areaInfoList.addAll(mtConverterService.areaInfoListMapping(resultValueList));
        return response.setStatus(ResultGenerator.genSuccessResult()).setAreaList(areaInfoList);
    }

    public MtBrandChannelGateService getMtBrandChannelGateService() {
        return mtBrandChannelGateService;
    }

    public String getActRetailDiscountListUrl() {
        return actRetailDiscountListUrl;
    }

    public String getActAllGetByAppFoodCodesUrl() {
        return actAllGetByAppFoodCodesUrl;
    }

    public String getGetOriginListUrl() {
        return getOriginListUrl;
    }
}
