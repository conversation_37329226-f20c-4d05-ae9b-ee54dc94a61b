/**
 * meituan.com Inc. Copyright (c) 2010-2021 All Rights Reserved.
 */
package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.entity;

/**
 * <p>
 *  TSP第三方配送物流信息
 * </p>
 * <AUTHOR>
 * @date 2021/12/1 4:20 下午
 */
public class SgOpenThirdLogisticsInfo {
    /** 快递公司名称 */
    private String sp_name;
    /** 快递公司编码 */
    private String sp_code;
    /** 快递公司单号 */
    private String sp_pkg_num;
    /** 物流状态 1=已发货带揽件,10=已发货，20=配送中，40=已签收，100=已取消,枚举 ThirdSpStatusEnum中的status字段 */
    private Integer sp_status;

    @Override public String toString() {
        return "SgOpenThirdLogisticsInfo{" + "sp_name='" + sp_name + '\'' + ", sp_code='" + sp_code + '\''
            + ", sp_pkg_num='" + sp_pkg_num + '\'' + ", sp_status='" + sp_status + '\'' + '}';
    }

    public String getSp_name() {
        return sp_name;
    }

    public void setSp_name(String sp_name) {
        this.sp_name = sp_name;
    }

    public String getSp_code() {
        return sp_code;
    }

    public void setSp_code(String sp_code) {
        this.sp_code = sp_code;
    }

    public String getSp_pkg_num() {
        return sp_pkg_num;
    }

    public void setSp_pkg_num(String sp_pkg_num) {
        this.sp_pkg_num = sp_pkg_num;
    }

    public Integer getSp_status() {
        return sp_status;
    }

    public void setSp_status(Integer sp_status) {
        this.sp_status = sp_status;
    }
}
