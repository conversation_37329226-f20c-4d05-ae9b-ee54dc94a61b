package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzantool;

import static com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.YzConverterUtil.buildResultSuccessSpu;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SubmitAppealInfoRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.YzSkuPriceAndStockUpdateInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostYZEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpecTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpuStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSpuService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.YzConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BasePageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.PageInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSpu;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchUpdateCustomSpuStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetHeadQuarterSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAppFoodCodeBySkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAuditStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAuditStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryChannelSpuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryChannelSpuIdResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryNormAuditDelSpuRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryNormAuditDelSpuResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryQualityProblemRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryQualityProblemResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuId2AppFoodCodeResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuSellStatusInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStoreCategoryInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuWeightInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuIdByNameAndSpecRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuIdByOriginIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateSpuOptionFieldRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.SaleStatusEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.youzan.cloud.open.sdk.common.exception.SDKException;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemCommonSearch;
import com.youzan.cloud.open.sdk.gen.v1_0_0.api.YouzanItemUpdateBranchDisplay;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemCommonSearchResult;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemDetailGetResult;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemUpdateBranchDisplayParams;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemUpdateBranchDisplayResult;
import com.youzan.cloud.open.sdk.gen.v1_0_0.model.YouzanItemUpdateBranchSkuResult;
import com.youzan.cloud.open.sdk.gen.v3_0_1.api.YouzanItemUpdateDelisting;
import com.youzan.cloud.open.sdk.gen.v3_0_1.api.YouzanItemUpdateListing;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanItemUpdateDelistingResult;
import com.youzan.cloud.open.sdk.gen.v3_0_1.model.YouzanItemUpdateListingResult;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: wangyihao04
 * @Date: 2022/11/28 7:24 PM
 * @Mail: <EMAIL>
 */
@Slf4j
@Service("yzToolChannelSpuService")
public class YzToolChannelSpuServiceImpl extends YouZanToolBaseService implements ChannelSpuService {
    @Resource
    private YzToolChannelSkuServiceImpl yzChannelSkuService;
    @Resource
    private YzToolSpuCommonService yzSpuCommonService;
    @Resource
    private YzToolChannelPriceServiceImpl yzChannelPriceServiceImpl;
    @Resource
    private YzToolChannelStockServiceImpl yzChannelStockServiceImpl;

    @Resource
    CopChannelStoreService copChannelStoreService;
    @Override
    public ResultSpuData updateWeightBySpu(SpuWeightInfoRequest request) {
        return null;
    }

    private static final Integer YZ_ONLINE = 1;


    @Override
    public ResultSpuData spuCreate(SpuInfoRequest request) {
        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList()) || CollectionUtils.isEmpty(request.getBaseInfo().getStoreIdList())) {
            return resultSpuData;
        }
        request.getParamList().forEach(item -> {
            request.getBaseInfo().getStoreIdList().forEach( storeId -> {
                SpuKey spuKey = YzConverterUtil.getSpuKey(item);
                try {
                    // 更新门店商品价格和库存
                    updatePriceAndStock(request, storeId, item, spuKey, resultSpuData);

                    // 执行上下架操作
                    ResultSpuData currSpuData ;
                    if (item.getStatus() == SpuStatusEnum.ON_LINE.getCode()) {
                        if ((yzSpuCommonService.needYzSingeSpec(request.getBaseInfo().getTenantId()))) {
                            // 单规格使用总部商品渠道id
                            spuKey.setChannelSpuId(item.getMerchantChannelSpuId());
                            currSpuData = optSpuOnlineOfflineStatus(request.getBaseInfo().getTenantId(), storeId, spuKey, SpuStatusEnum.ON_LINE.getCode());
                        }
                        else {
                            currSpuData = optSpuOnlineStatus(request.getBaseInfo().getTenantId(), storeId, spuKey);
                        }
                    } else {
                        // 若是下架操作创建商品，对有赞渠道无意义，直接返回成功
                        currSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
                        currSpuData.getSucData().add(buildResultSuccessSpu(storeId, spuKey));
                    }

                    if (CollectionUtils.isNotEmpty(currSpuData.getErrorData())) {
                        resultSpuData.getErrorData().addAll(currSpuData.getErrorData());
                    }
                    if (CollectionUtils.isNotEmpty(currSpuData.getSucData())) {
                        resultSpuData.getSucData().addAll(currSpuData.getSucData());
                    }
                } catch (BizException e) {
                    resultSpuData.addToErrorData(YzConverterUtil.buildErrorSpuInfo(storeId, spuKey, e.getErrorCode(),
                            String.format("创建门店商品接口调用异常：%s", e.getMessage())));
                } catch (IllegalArgumentException e) {
                    resultSpuData.addToErrorData(YzConverterUtil.buildDefaultErrorSpuInfo(storeId, spuKey,
                            String.format("商品参数校验异常：%s", e.getMessage())));
                } catch (Exception e) {
                    log.error("YzToolChannelSpuServiceImpl.spuCreate 服务异常, data:{}", item, e);
                    resultSpuData.addToErrorData(YzConverterUtil.buildDefaultErrorSpuInfo(storeId, spuKey,
                            String.format("创建门店商品异常：%s", e.getMessage())));
                }
            });

        });
        return resultSpuData;
    }

    @Override
    public ResultSpuData spuCreateForCleaner(SpuInfoRequest request) {
        return spuCreate(request);
    }

    private void updatePriceAndStock(SpuInfoRequest request, Long storeId, SpuInfoDTO spuInfoDTO, SpuKey spuKey,
                                     ResultSpuData resultSpuData) throws Exception {
        Preconditions.checkArgument(StringUtils.isNumeric(spuInfoDTO.getMerchantChannelSpuId()), "商品渠道ID必须为数字");

        boolean singleSpecType = yzSpuCommonService.needYzSingeSpec(request.getBaseInfo().getTenantId());
        YouzanItemUpdateBranchSkuResult result;
        if (singleSpecType) {
            // 1.更新价格和库存
            result = yzSpuCommonService.updatePriceAndStockInfoNoSku(request.getBaseInfo().getTenantId(), storeId,
                    YzSkuPriceAndStockUpdateInfo.convertToPriceAndStockNoSku(spuInfoDTO, storeId));
        }
        else {
            // 1.获取商品详情
            YouzanItemDetailGetResult.YouzanItemDetailGetResultData detail =
                    yzSpuCommonService.getStoreSpuDetail(request.getBaseInfo().getTenantId(), storeId,
                            Long.parseLong(spuInfoDTO.getMerchantChannelSpuId()));
            YzConverterUtil.setChannelSpuInfo(spuKey, detail);

            // 2.更新价格和库存
            result = yzSpuCommonService.updatePriceAndStockInfo(request.getBaseInfo().getTenantId(), storeId,
                    YzSkuPriceAndStockUpdateInfo.convertToPriceAndStock(spuInfoDTO, storeId, detail));

        }

        YzConverterUtil.convertAndMerge2SpuResult(resultSpuData, result, spuKey, storeId);
    }

    @Override
    public ResultSpuData upcCreate(SpuInfoRequest request) {
        return new ResultSpuData().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public ResultSpuData upcCreateForCleaner(SpuInfoRequest request) {
        return upcCreate(request);
    }

    @Override
    public ResultSpuData updateBySpuOrUpc(SpuInfoRequest request) {
        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList()) || CollectionUtils.isEmpty(request.getBaseInfo().getStoreIdList())) {
            return resultSpuData;
        }
        request.getParamList().forEach(item -> {
            request.getBaseInfo().getStoreIdList().forEach( storeId -> {
                SpuKey spuKey = YzConverterUtil.getSpuKey(item);
                try {
                    boolean singleSpecType = yzSpuCommonService.needYzSingeSpec(request.getBaseInfo().getTenantId());
                    // 执行上下架操作
                    ResultSpuData currSpuData = null;
                    if (singleSpecType) {
                        currSpuData = optSpuOnlineOfflineStatus(request.getBaseInfo().getTenantId(), storeId, spuKey, SpuStatusEnum.ON_LINE.getCode());
                    }
                    else {
                        if (item.getStatus() == SpuStatusEnum.ON_LINE.getCode()) {
                            currSpuData = optSpuOnlineStatus(request.getBaseInfo().getTenantId(), storeId, spuKey);
                        }
                        else {
                            currSpuData = optSpuOfflineStatus(request.getBaseInfo().getTenantId(), storeId, spuKey, false);
                        }
                    }

                    if (CollectionUtils.isNotEmpty(currSpuData.getErrorData())) {
                        resultSpuData.getErrorData().addAll(currSpuData.getErrorData());
                    }
                    if (CollectionUtils.isNotEmpty(currSpuData.getSucData())) {
                        resultSpuData.getSucData().addAll(currSpuData.getSucData());
                    }
                } catch (IllegalArgumentException e) {
                    resultSpuData.addToErrorData(YzConverterUtil.buildDefaultErrorSpuInfo(storeId, spuKey,
                            String.format("当前商品参数校验异常：%s", e.getMessage())));
                } catch (Exception e) {
                    log.error("yzChannelStockServiceImpl.updateBySpuOrUpc 服务异常, data:{}", item, e);
                    resultSpuData.addToErrorData(YzConverterUtil.buildDefaultErrorSpuInfo(storeId, spuKey,
                            String.format("更新门店商品异常：%s", e.getMessage())));
                }
            });
        });
        return resultSpuData;
    }

    @Override
    public ResultSpuData updateBySpuOrUpcForCleaner(SpuInfoRequest request) {
        // 目前数据清洗不涉及到有赞渠道，暂不实现
        return new ResultSpuData().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public ResultSpuData deleteSpu(SpuInfoDeleteRequest request) {
        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultSpuData;
        }

        request.getParamList().forEach(deleteSpu -> {
            try {
                ResultSpuData currSpuData;
                if (yzSpuCommonService.needYzSingeSpec(request.getBaseInfo().getTenantId())) {
                    currSpuData = optSpuOnlineOfflineStatus(request.getBaseInfo().getTenantId(), deleteSpu.getStoreId(),
                            deleteSpu.getSpuKey(), SpuStatusEnum.OFF_LINE.getCode());
                }
                else {
                    currSpuData = optSpuOfflineStatus(request.getBaseInfo().getTenantId(),
                            deleteSpu.getStoreId(), deleteSpu.getSpuKey(), true);
                }

                if (CollectionUtils.isNotEmpty(currSpuData.getErrorData())) {
                    resultSpuData.getErrorData().addAll(currSpuData.getErrorData());
                }
                if (CollectionUtils.isNotEmpty(currSpuData.getSucData())) {
                    resultSpuData.getSucData().addAll(currSpuData.getSucData());
                }
            } catch (Exception e) {
                log.error("yzChannelStockServiceImpl.deleteSpu 服务异常, data:{}", deleteSpu, e);
                resultSpuData.addToErrorData(YzConverterUtil.buildDefaultErrorSpuInfo(deleteSpu.getStoreId(), deleteSpu.getSpuKey(),
                        String.format("删除门店商品异常：%s", e.getMessage())));
            }
        });

        return resultSpuData;
    }

    @Override
    public ResultSpuData deleteCategoryAndSpu(BaseRequest request) {
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "批量删除分类和商品服务失败");
    }

    @Override
    public ResultSpuData deleteSku(SkuInSpuInfoDeleteRequest request) {
        return ResultGenerator.genResultSpuData(ResultCode.FAIL, "有赞渠道不支持删除规格操作");
    }

    @Override
    public ResultSpuData updateSpuSellStatus(SpuSellStatusInfoRequest request) {
        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(request.getParamList())) {
            return resultSpuData;
        }

        resultSpuData.setErrorData(Lists.newArrayList());
        resultSpuData.setSucData(Lists.newArrayList());
        request.getParamList().forEach(spuInfoDTO -> {
            spuInfoDTO.getSpuKeys().forEach(spuKey -> {
                ResultSpuData optRes ;
                // 判断是否走单规格
                if (yzSpuCommonService.needYzSingeSpec(request.getBaseInfo().getTenantId())) {
                    optRes = optSpuOnlineOfflineStatus(request.getBaseInfo().getTenantId(), spuInfoDTO.getStoreId(), spuKey, spuInfoDTO.getSpuStatus());
                }
                else {
                    if (spuInfoDTO.getSpuStatus() == SpuStatusEnum.ON_LINE.getCode()) {
                        optRes = optSpuOnlineStatus(request.getBaseInfo().getTenantId(), spuInfoDTO.getStoreId(), spuKey);
                    }
                    else if (spuInfoDTO.getSpuStatus() == SpuStatusEnum.OFF_LINE.getCode()) {
                        optRes = optSpuOfflineStatus(request.getBaseInfo().getTenantId(), spuInfoDTO.getStoreId(), spuKey, false);
                    }
                    else {
                        ResultErrorSpu errorSpu = YzConverterUtil.buildDefaultErrorSpuInfo(spuInfoDTO.getStoreId(),
                                spuKey, String.format("门店商品上下架状态错误: %s", spuInfoDTO.getSpuStatus()));
                        resultSpuData.getErrorData().add(errorSpu);
                        return;
                    }
                }

                if (CollectionUtils.isNotEmpty(optRes.getErrorData())) {
                    resultSpuData.getErrorData().addAll(optRes.getErrorData());
                }
                if (CollectionUtils.isNotEmpty(optRes.getSucData())) {
                    resultSpuData.getSucData().addAll(optRes.getSucData());
                }
            });
        });

        return resultSpuData;
    }

    private ResultSpuData optSpuOnlineStatus(Long tenantId, Long storeId, SpuKey spuKey) {

        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        try {
            AppMessage appMessage = getMainAppMessage(tenantId);
            Long yzChannelPoiId = yzSpuCommonService.getYzChannelPoiId(tenantId, storeId);
            YouzanItemUpdateListing youzanItemUpdateListing = YzConverterUtil.onlineSpuInfoToYzAPI(yzChannelPoiId, spuKey);
            YouzanItemUpdateListingResult result = getResult4YouZanByRhino(ChannelPostYZEnum.LISTING_STORE_SPU, appMessage,
                    youzanItemUpdateListing, YouzanItemUpdateListingResult.class);
            log.info("门店商品上架操作记录：spuKey {}, result {}.", spuKey, JSON.toJSONString(result));

            resultSpuData = YzConverterUtil.converterYouzanItemUpdateListingResult(storeId, spuKey, result);
        } catch (Exception e) {
            log.warn("调用有赞商品上架接口失败, storeId {}, spuKey {}.", storeId, spuKey, e);

            resultSpuData.getStatus().setCode(ResultCode.FAIL.getCode());
            resultSpuData.getStatus().setMsg(ResultCode.FAIL.getMsg());

            int errorCode = ResultCode.FAIL.getCode();
            String errorMsg = String.format("门店商品上架失败, %s", e.getMessage());
            if (e instanceof BizException) {
                errorCode = ((BizException) e).getErrorCode();
                errorMsg = e.getMessage();
            }

            ResultErrorSpu errorSpu = new ResultErrorSpu();
            errorSpu.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
            errorSpu.setSpuInfo(spuKey);
            errorSpu.setStoreId(storeId);
            errorSpu.setErrorCode(errorCode);
            errorSpu.setErrorMsg(errorMsg);

            resultSpuData.getErrorData().add(errorSpu);
        }

        return resultSpuData;
    }

    private ResultSpuData optSpuOnlineOfflineStatus(Long tenantId, Long storeId, SpuKey spuKey, int saleStatus) {

        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        try {
            AppMessage appMessage = getMainAppMessage(tenantId);
            Long yzChannelPoiId = yzSpuCommonService.getYzChannelPoiId(tenantId, storeId);


            YouzanItemUpdateBranchDisplay youzanItemUpdateBranchDisplay = new YouzanItemUpdateBranchDisplay();
            YouzanItemUpdateBranchDisplayParams youzanItemUpdateBranchDisplayParams = new YouzanItemUpdateBranchDisplayParams();
            youzanItemUpdateBranchDisplayParams.setItemId(Long.parseLong(spuKey.getChannelSpuId()));

            // 设置上下架以及不可售的门店
            List<Long> upList = new ArrayList<>();
            List<Long> downList = new ArrayList<>();
            if (saleStatus == SpuStatusEnum.ON_LINE.getCode()) {
                upList.add(yzChannelPoiId);
            }else if (saleStatus == SpuStatusEnum.OFF_LINE.getCode()) {
                downList.add(yzChannelPoiId);
            }
            youzanItemUpdateBranchDisplayParams.setTakeNoSaleNodeKdtIdList(new ArrayList<>());
            youzanItemUpdateBranchDisplayParams.setTakeUpNodeKdtIdList(upList);
            youzanItemUpdateBranchDisplayParams.setTakeDownNodeKdtIdList(downList);

            youzanItemUpdateBranchDisplay.setAPIParams(youzanItemUpdateBranchDisplayParams);

            YouzanItemUpdateBranchDisplayResult result = getResult4YouZanByRhino(ChannelPostYZEnum.UPDATE_SPU_STOCK_AND_PRICE_NO_SKU, appMessage,
                    youzanItemUpdateBranchDisplay, YouzanItemUpdateBranchDisplayResult.class);

            log.info("门店商品上架操作记录：spuKey {}, result {}.", spuKey, JSON.toJSONString(result));
            resultSpuData = YzConverterUtil.converterYouzanItemUpdateBranchDisplayResult(storeId, spuKey, result);
        } catch (Exception e) {
            log.warn("调用有赞商品上架接口失败, storeId {}, spuKey {}.", storeId, spuKey, e);

            resultSpuData.getStatus().setCode(ResultCode.FAIL.getCode());
            resultSpuData.getStatus().setMsg(ResultCode.FAIL.getMsg());

            int errorCode = ResultCode.FAIL.getCode();
            String errorMsg = String.format("门店商品上架失败, %s", e.getMessage());
            if (e instanceof BizException) {
                errorCode = ((BizException) e).getErrorCode();
                errorMsg = e.getMessage();
            }

            ResultErrorSpu errorSpu = new ResultErrorSpu();
            errorSpu.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
            errorSpu.setSpuInfo(spuKey);
            errorSpu.setStoreId(storeId);
            errorSpu.setErrorCode(errorCode);
            errorSpu.setErrorMsg(errorMsg);

            resultSpuData.getErrorData().add(errorSpu);
        }

        return resultSpuData;
    }

    private ResultSpuData optSpuOfflineStatus(Long tenantId, Long storeId, SpuKey spuKey, boolean deleteOpt) {

        ResultSpuData resultSpuData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        try {
            AppMessage appMessage = getMainAppMessage(tenantId);

            YouzanItemUpdateDelisting youzanItemUpdateDelisting =
                    YzConverterUtil.offlineSpuInfoToYzAPI(yzSpuCommonService.getYzChannelPoiId(tenantId, storeId), spuKey);

            YouzanItemUpdateDelistingResult result = getResult4YouZanByRhino(ChannelPostYZEnum.DELISTING_STORE_SPU,
                    appMessage, youzanItemUpdateDelisting, YouzanItemUpdateDelistingResult.class);
            log.info("门店商品下架操作记录：spuKey {}, result {}.", spuKey, JSON.toJSONString(result));

            resultSpuData = YzConverterUtil.converterYouzanItemUpdateDelistingResult(storeId, spuKey, deleteOpt, result);
        } catch (Exception e) {
            log.warn("调用有赞商品下架接口失败, storeId {}, spuKey {}.", storeId, spuKey, e);

            resultSpuData.getStatus().setCode(ResultCode.FAIL.getCode());
            resultSpuData.getStatus().setMsg(ResultCode.FAIL.getMsg());

            int errorCode = ResultCode.FAIL.getCode();
            String errorMsg = String.format("门店商品上架失败, %s", e.getMessage());
            if (e instanceof BizException) {
                errorCode = ((BizException) e).getErrorCode();
                errorMsg = e.getMessage();
            }

            ResultErrorSpu errorSpu = new ResultErrorSpu();
            errorSpu.setChannelId(ChannelTypeEnum.YOU_ZAN.getCode());
            errorSpu.setSpuInfo(spuKey);
            errorSpu.setStoreId(storeId);
            errorSpu.setErrorCode(errorCode);
            errorSpu.setErrorMsg(errorMsg);

            resultSpuData.getErrorData().add(errorSpu);
        }

        return resultSpuData;
    }

    @Override
    public ResultSpuData updateCustomSpuIdByOriginId(UpdateCustomSpuIdByOriginIdRequest request) {
        return new ResultSpuData().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public ResultSpuData updateCustomSpuIdByNameAndSpec(UpdateCustomSpuIdByNameAndSpecRequest request) {
        return new ResultSpuData().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    // 仅支持稽核，返回渠道价格、库存、上下架信息
    public GetSpuInfoResponse getSpuInfo(GetSpuInfoRequest request) throws SDKException {
        if (request == null || request.getBaseInfo() == null || request.getSpuId() == null){
            return new GetSpuInfoResponse().setStatus(ResultGenerator.genResult(ResultCode.FAIL, "参数错误，拉取有赞商品信息失败"));
        }

        GetSpuInfoResponse response = new GetSpuInfoResponse();
        response.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));

        SpuInfoDTO spuInfo = new SpuInfoDTO();
        spuInfo.setCustomSpuId(request.getCustomSpuId());

        BaseRequestSimple baseInfo = request.getBaseInfo();
        YouzanItemDetailGetResult.YouzanItemDetailGetResultData detail = null;
        try {
            detail = yzSpuCommonService.getStoreSpuDetail(baseInfo.getTenantId(), request.storeId, Long.parseLong(request.getSpuId()));
        }
        catch (BizException e) {
            log.error("yzSpuCommonService.getStoreSpuDetail failed, tenantId: {}, storeId: {}, spuId: {}",
                    baseInfo.getTenantId(), request.storeId, request.getSpuId(), e);
            response.setStatus(ResultGenerator.genResult(ResultCode.parseOrDefault(e.getErrorCode(), ResultCode.FAIL), e.getMessage()));
            return response;
        }

        if (detail == null){
            throw new BizException(String.format("查询有赞总部商品详情失败,  request:%s", request));
        }

        SkuInSpuInfoDTO skuInfo = new SkuInSpuInfoDTO();
        spuInfo.setSkus(Lists.newArrayList(skuInfo));
        if (detail.getItemPriceParam() != null && detail.getItemPriceParam().getPrice() != null) {
            skuInfo.setPrice(MoneyUtils.fenToYuan(detail.getItemPriceParam().getPrice()).doubleValue());
        }
        else if (CollectionUtils.isNotEmpty(detail.getSkuList()) && detail.getSkuList().get(0).getPrice() != null) {
            skuInfo.setPrice(MoneyUtils.fenToYuan(detail.getSkuList().get(0).getPrice()).doubleValue());
        }

        if (detail.getQuantity() != null) {
            skuInfo.setStock(Integer.parseInt(detail.getQuantity().toString()));
        }
        else if (CollectionUtils.isNotEmpty(detail.getSkuList()) && detail.getSkuList().get(0).getStockNum() != null) {
            skuInfo.setStock(Integer.parseInt(detail.getSkuList().get(0).getStockNum().toString()));
        }

        int saleStatus = SaleStatusEnum.OFF_SALE.getCode();
        if (YZ_ONLINE.equals(detail.getIsDisplay() )) {
            saleStatus = SaleStatusEnum.ON_SALE.getCode();
        }

        spuInfo.setStatus(saleStatus);

        if (detail.getTitle() != null) {
            spuInfo.setName(detail.getTitle());
        }

        response.setSpuInfo(spuInfo);
        return response;
    }

    @Override
    public GetSpuInfosResponse getSpuInfoList(GetSpuInfosRequest request) {
        return new GetSpuInfosResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    /**
     * 实现有赞商品查询接口，注意查询的商品信息只填充了基础的字段（编码、分类、上下架等），暂时先只满足上线工具需求！！
     *
     * @param request
     * @return
     */
    @Override
    public BatchGetSpuInfoResponse batchGetSpuInfo(BatchGetSpuInfoRequest request) {
        BatchGetSpuInfoResponse response = new BatchGetSpuInfoResponse().setStatus(ResultGenerator.genSuccessResult());
        // step1: 参数校验
        if (request == null || request.getBaseInfo() == null) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.INVALID_PARAM));
        }
        if (request.getBaseInfo().getTenantId() <= 0 ) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.INVALID_PARAM));
        }
        // 查询门店商品需要校验门店id
        if(request.getStoreId() <= 0 && !request.tenantProduct){
            return response.setStatus(ResultGenerator.genResult(ResultCode.INVALID_PARAM));
        }
        if (request.getPageSize() <= 0 || request.getPageSize() > 50) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.INVALID_PARAM));
        }

        try {
            // step2: build request
            AppMessage appMessage = getMainAppMessage(request.getBaseInfo().getTenantId());
            Long yzChannelPoiId = null;
            if (request.yzMainStore || request.tenantProduct) {
                // 有赞主店查询
                String grantId = appMessage.getGrantId();
                if (StringUtils.isNumeric(grantId)) {
                    yzChannelPoiId = Long.valueOf(grantId);
                }
            }
            else {
                // 正常店铺查询
                yzChannelPoiId = yzSpuCommonService.getYzChannelPoiId(request.getBaseInfo().getTenantId(), request.getStoreId());
            }
            if (yzChannelPoiId == null) {
                throw new BizException("yzChannelPoiId为空");
            }
            List<Object> searchAfter = new ArrayList<>();
            if (StringUtils.isNotBlank(request.getOffset()) && StringUtils.isNumeric(request.getOffset())) {
                searchAfter.add(Long.valueOf(request.getOffset()));
            }
            // 注意：有赞商品的搜索如果是基于pageNo + pageSize查询，最多只能查询前5000条数据，因此需要采用offset + pageSize的查询方式
            // ID升序查询，offset（searchAfter）需要传空（第一次查询）或者上一次查询的最大itemId（已存储在offset字段）
            // 参考：https://doc.youzanyun.com/detail/API/0/3561
            YouzanItemCommonSearch itemCommonSearch = YzConverterUtil.createItemCommonSearchRequest(Lists.newArrayList(yzChannelPoiId),
                    searchAfter, Lists.newArrayList("ID_ASC"), null, request.getPageSize());

            // step3: call api
            YouzanItemCommonSearchResult itemCommonSearchResult = getResult4YouZanByRhino(ChannelPostYZEnum.ITEM_COMMON_SEARCH,
                    appMessage, itemCommonSearch, YouzanItemCommonSearchResult.class);

            // step4: parse result
            if (itemCommonSearchResult == null) {
                log.error("item_common_search结果为空, request:{}", JacksonUtils.toJson(itemCommonSearch));
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, ProjectConstant.NO_RESPONSE));
            }
            if (!itemCommonSearchResult.getSuccess()) {
                log.error("item_common_search失败, request:{}, response:{}", JacksonUtils.toJson(itemCommonSearch), JacksonUtils.toJson(itemCommonSearchResult));
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, itemCommonSearchResult.getMessage()));
            }
            YouzanItemCommonSearchResult.YouzanItemCommonSearchResultData searchResultData = itemCommonSearchResult.getData();
            if (searchResultData == null) {
                log.error("item_common_search搜索结果为空, request:{}, response:{}", JacksonUtils.toJson(itemCommonSearch), JacksonUtils.toJson(itemCommonSearchResult));
                return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "searchResultData为空"));
            }

            // step5: build resp
            List<YouzanItemCommonSearchResult.YouzanItemCommonSearchResultItems> items = searchResultData.getItems();
            // spu列表
            List<SpuInfoDTO> spuInfos = new ArrayList<>();
            // 查询游标
            String offset = null;
            if (CollectionUtils.isNotEmpty(items)) {
                spuInfos.addAll(items.stream()
                        .map(YzConverterUtil::buildSpuInfoDTO)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
                // 计算offset时需要进行一次兜底排序，因为不保证渠道返回的商品列表一定是升序
                Optional<Long> maxItemIdOptional = items.stream()
                        .map(YouzanItemCommonSearchResult.YouzanItemCommonSearchResultItems::getItemId)
                        .max(Comparator.naturalOrder());
                if (maxItemIdOptional.isPresent()) {
                    offset = String.valueOf(maxItemIdOptional.get());
                }
            }
            PageInfo pageInfo = new PageInfo();
            YouzanItemCommonSearchResult.YouzanItemCommonSearchResultPaginator paginator = searchResultData.getPaginator();
            // 注意因为实际是offset + pageSize查询，所以分页参数不一定准确，调用方不要有依赖！
            if (paginator != null) {
                pageInfo.setPageNum(paginator.getPageNo());
                pageInfo.setPageSize(request.getPageSize());
                pageInfo.setTotalPage((paginator.getTotalCount() + request.getPageSize() - 1) / request.getPageSize());
                pageInfo.setTotalNum(paginator.getTotalCount());
            }

            return response.setSpuInfos(spuInfos)
                    .setPageInfo(pageInfo)
                    .setOffset(offset);
        }
        catch (BizException e) {
            log.error("item_common_search bizException, request:{}", JacksonUtils.toJson(request), e);
            return response.setStatus(ResultGenerator.genResult(ResultCode.parseOrDefault(e.getErrorCode(), ResultCode.FAIL), e.getMessage()));
        }
        catch (Exception e) {
            log.error("item_common_search fail, request:{}", JacksonUtils.toJson(request), e);
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, e.getMessage()));
        }
    }

    @Override
    public GetHeadQuarterSpuInfoResponse batchGetHeadQuarterSpuInfo(BasePageRequest request) {
        return new GetHeadQuarterSpuInfoResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public QueryAuditStatusResponse getAuditStatus(QueryAuditStatusRequest request) {
        return new QueryAuditStatusResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public RecommendChannelCategoryQueryResponse queryRecommendChannelCategory(RecommendChannelCategoryQueryRequest request) {
        return new RecommendChannelCategoryQueryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public SkuId2AppFoodCodeResponse queryAppFoodCodeBySkuId(QueryAppFoodCodeBySkuIdRequest request) {
        return new SkuId2AppFoodCodeResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public ResultStatus updateCustomSpuStoreCategory(UpdateCustomSpuStoreCategoryRequest request) {
        return ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }

    @Override
    public ResultSpuData batchUpdateSpuStoreCategoryCode(BatchUpdateCustomSpuStoreCategoryRequest request) {
        return new ResultSpuData().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public SensitiveWordCheckResponse checkSensitiveWords(SensitiveWordCheckRequest request) {
        return new SensitiveWordCheckResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public QueryNormAuditDelSpuResponse queryNormAuditDelSpuInfo(QueryNormAuditDelSpuRequest request) {
        return new QueryNormAuditDelSpuResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public QueryChannelSpuIdResponse queryChannelSpuId(QueryChannelSpuIdRequest request) {
        return new QueryChannelSpuIdResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public ProductSequenceBatchSetResponse batchSetSequence(ProductSequenceBatchSetRequest request) {
        ProductSequenceBatchSetResponse response = new ProductSequenceBatchSetResponse();
        response.setStatus(ChannelStatus.build(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(), ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg()));

        return response;
    }

    @Override
    public ResultSpuData updateStoreCategory(SpuStoreCategoryInfoRequest request) {
        return new ResultSpuData().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public QueryQualityProblemResponse queryQualityProblem(QueryQualityProblemRequest request) {
        return null;
    }

    @Override
    public ResultStatus updateOptionFieldBySpu(UpdateSpuOptionFieldRequest request) {
        return ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }

    @Override
    public ResultStatus submitAppealInfo(SubmitAppealInfoRequest request) {
        return null;
    }
}
