package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.ChannelBizException;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.QueryPoiSkuListBySkuIds;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.constant.CommonResultCode;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.PoiSkuInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.PoiSkuInfoListResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerMasterCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PoiSpuThriftServiceProxy {

    @Resource
    private EmpowerMasterCheckService.Iface empowerMasterCheckService;

    @MethodLog(logResponse = true, logException = false)
    public List<String> queryInfinitySkuIds(Long tenantId, Long storeId, List<String> skuIds) {
        try {
            QueryPoiSkuListBySkuIds request = new QueryPoiSkuListBySkuIds(tenantId, storeId, skuIds);
            log.info("empowerMasterCheckService queryPoiSkuListByIds request:{}", request);
            PoiSkuInfoListResult result = empowerMasterCheckService.queryPoiSkuListByIds(request);
            log.info("empowerMasterCheckService queryPoiSkuListByIds result:{}", result);

            if (result == null || result.getStatus() == null || CommonResultCode.SUCCESS.getValue() != result.getStatus().getCode()) {
                log.error("empowerMasterCheckService.queryPoiSkuListByIds error,result={} ", result);
                throw new ChannelBizException("查询无限库存门店商品异常");
            }
            return Optional.ofNullable(result.getPoiSkuInfoList())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(it -> 1 == it.getStockInfiniteFlag())
                    .map(PoiSkuInfo::getSkuId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("empowerMasterCheckService.queryPoiSkuListByIds error", e);
            throw new ChannelBizException("查询无限库存门店商品异常");
        }
    }

}
