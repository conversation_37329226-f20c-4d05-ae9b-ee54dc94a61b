package com.sankuai.meituan.shangou.empower.ocms.channel.service.interceptor;

import com.meituan.dorado.rpc.handler.filter.FilterHandler;
import com.meituan.dorado.rpc.meta.RpcInvocation;
import com.meituan.dorado.rpc.meta.RpcResult;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.TenantSwitchGetRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantSwitchGetResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreBaseInfoDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.SyncDataInterceptor;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 数据同步拦截器基类，会提供统一的处理流程，具体差异由各个业务决定
 * <AUTHOR>
 * @Date 2019-08-29 14:52
 */
@Slf4j
public abstract class SyncDataInterceptBase implements SyncDataInterceptor {
    @Resource
    protected CopChannelStoreService copChannelStoreService;
    @Resource
    protected CopAccessConfigService copAccessConfigService;
    @Resource
    private ConfigThriftService configThriftService;

    private static final String StandeSpecSwitch = "1";//灰度开关
    private static final String StandardPara = "SPEC_STANDARD"; //查询灰度开关的配置
    /**
     * 当前参数是否需要从列表中排除
     * @param requestData
     * @return
     */
    protected long getStoreIdBy(Object requestData, InterceptInfo interceptInfo){
        return 0;
    }

    protected abstract List<String> getAffectMethods();

    /**
     * 渠道维度：初始化已开通渠道
     * 渠道门店维度：根据传入门店的列表，初始化已开通线上数据同步的门店列表
     */
    private void initializeChannelStoreInfo(InterceptInfo interceptInfo){
        interceptInfo.getChannelStoreIds().forEach((channelId, storeIds) -> {
            List<ChannelStoreBaseInfoDO> disableStoreInfos = copChannelStoreService.selectAllDisableSyncDataStoreBaseInfos(interceptInfo.getTenantId(), channelId);
            interceptInfo.getAllDisableChannelStoreBaseInfos().put(channelId, disableStoreInfos);
            if(checkStandard(interceptInfo) && (CollectionUtils.isNotEmpty(storeIds))) {
                return;
            } else {
                if (CollectionUtils.isNotEmpty(storeIds)) {
                    List<Long> enableStoreIds = storeIds.stream().filter(storeId ->
                            storeId > 0 && disableStoreInfos.stream().noneMatch(info -> info.getStoreId().longValue() == storeId)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(enableStoreIds)) {
                        interceptInfo.getEnableChannelStoreIds().put(channelId, enableStoreIds);
                    }
                }
            }
        });
    }

    protected abstract void parseArgument(Object argument, InterceptInfo interceptInfo);

    /**
     * 是否需要对请求参数做过滤处理，默认是RequestDataList不为空是需要处理
     * @return
     */
    protected boolean isProcessRequest(InterceptInfo interceptInfo){
        return CollectionUtils.isNotEmpty(interceptInfo.getRequestDataList());
    }

    /**
     * 阻止原调用后，需要创建一个结果
     * @return
     */
    protected RpcResult createCustomResult(Class returnType, InterceptInfo interceptInfo) {
        return new RpcResult();
    }

    /**
     * 是否阻止原调用
     * @return
     */
    protected boolean isPreventOriginalInvoke(InterceptInfo interceptInfo) {
        // RequestDataList为空时说明没有门店信息，不需要阻止原调用
        return CollectionUtils.isNotEmpty(interceptInfo.getRequestDataList()) &&
                interceptInfo.getEnableChannelStoreIds().keySet().size() == 0 && interceptInfo.preventOriginalInvoke;
    }

    protected void processRequest(InterceptInfo interceptInfo){
        // 没有阻止原调用的情况才需要处理Request，把禁用的门店相关数据过滤掉
        if(isProcessRequest(interceptInfo) && !isPreventOriginalInvoke(interceptInfo)){
            interceptInfo.getChannelStoreIds().keySet().forEach(channelId ->{
                log.info("processRequest channelId:{}, 处理请求参数后门店列表为：{}", channelId, interceptInfo.getEnableChannelStoreIds());
                // 移除不在enable的门店的列表里
                interceptInfo.getRequestDataList().removeIf(item ->
                        !interceptInfo.getEnableChannelStoreIds().containsKey(channelId) ||
                        !interceptInfo.getEnableChannelStoreIds().get(channelId).contains(getStoreIdBy(item, interceptInfo))
                );
            });
        }
    }

    protected RpcResult invokeInternal(FilterHandler nextHandler, RpcInvocation invocation, InterceptInfo interceptInfo) throws Throwable {
        if(isPreventOriginalInvoke(interceptInfo)){
            return createCustomResult(invocation.getMethod().getReturnType(), interceptInfo);
        }
        else {
            return nextHandler.handle(invocation);
        }
    }

    protected RpcResult processResult(RpcResult result, InterceptInfo interceptInfo) {
        return result;
    }

    @Override
    public Boolean isIntercept(RpcInvocation invocation){
        return getAffectMethods().contains(invocation.getMethod().getName());
    }

    @Override
    public RpcResult invoke(FilterHandler nextHandler, RpcInvocation invocation) throws Throwable {
        Object[] arguments = invocation.getArguments();
        if(arguments == null || arguments.length == 0 || arguments[0] == null){
            String serviceName = invocation.getServiceInterface().getName() + "." + invocation.getMethod().getName();
            log.error("SyncDataInterceptor没有拿到request信息. serviceName:{} request({})", serviceName, invocation.getArguments());
            return nextHandler.handle(invocation);
        }

        InterceptInfo interceptInfo = new InterceptInfo();
        interceptInfo.setMethodName(invocation.getMethod().getName());
        interceptInfo.setArgument(arguments[0]);
        log.info("进入拦截器调用，service:{}, method:{}, request:{}", invocation.getServiceInterface().getName(), interceptInfo.getMethodName(), arguments[0]);

        // 解析参数，解析成功后，会赋值tenantId、requestDataList、channelStoreIds
        parseArgument(arguments[0], interceptInfo);
        log.info("解析请求后，tenantId:{}，requestDataList:{}, channelStoreIds:{}",
                interceptInfo.getTenantId(), interceptInfo.getRequestDataList(), interceptInfo.getChannelStoreIds());

        //查询启用门店列表
        initializeChannelStoreInfo(interceptInfo);
        log.info("查询启用门店列表，allDisableChannelStoreBaseInfos:{}, enableChannelStoreIds:{}",
                interceptInfo.getAllDisableChannelStoreBaseInfos(), interceptInfo.getEnableChannelStoreIds());

        // 处理请求参数
        processRequest(interceptInfo);
        log.info("处理请求后，request:{}, interceptInfo:{}", arguments[0], interceptInfo);

        // 内部调用
        RpcResult result = invokeInternal(nextHandler, invocation, interceptInfo);
        log.info("内部调用后，result:{}", result.getReturnVal());

        // 处理结果
        processResult(result, interceptInfo);
        log.info("处理结果后，result:{}", result.getReturnVal());

        return result;
    }

    protected final ResultStatus createCustomResultStatus(int channelId, long storeId, InterceptInfo interceptInfo){
        String storeName = String.valueOf(storeId);
        Optional<ChannelStoreBaseInfoDO> channelStoreOp = interceptInfo.getAllDisableChannelStoreBaseInfos().get(channelId).stream()
                .filter(info -> info.getStoreId().longValue() == storeId).findFirst();
        if(channelStoreOp.isPresent()){
            storeName = channelStoreOp.get().getStoreName();
        } else{
            log.warn("createCustomResultStatus, storeId：{},不在禁用门店列表中, channelId:{}", storeId);
        }
        if(checkStandard(interceptInfo))
        {
            return ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE, MccConfigUtil.getStandardErrorMessage());
        } else {
            return ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE, String.format("'%s'停用商品价格库存同步，请检查配置", storeName));
        }
    }

    @Data
    protected class InterceptInfo {
        private Object argument;
        public String methodName;
        public long tenantId;

        /**
         * 单一渠道和单一门店时使用
         */
        public int channelId;
        public long storeId;
        public boolean preventOriginalInvoke = true;

        /**
         * 必须是原参数的引用，过滤是在参数里做的处理
         */
        public List requestDataList;
        /**
         * 入参里的门店列表，如果门店列表为空，则代表入参为渠道维度
         */
        public Map<Integer, List<Long>> channelStoreIds = new HashMap<>();
        /**
         * 用于渠道-门店维度，在传入每个渠道的门店列表里查询已启用数据同步的门店列表
         */
        public Map<Integer, List<Long>> enableChannelStoreIds = new HashMap<>();
        /**
         * 用于渠道维度，每个渠道所有禁用数据同步的门店列表
         */
        public Map<Integer, List<ChannelStoreBaseInfoDO>> allDisableChannelStoreBaseInfos = new HashMap<>();

        /**
         * String: channelId + storeId
         */
        protected List<String> commonErrorSkuIds = new ArrayList<>();
        protected Map<String, List<String>> errorSkuIds = new HashMap<>();
    }

    /*
   数据清洗期间灰度判断
    */
    public boolean checkStandard(InterceptInfo invocation)
    {
        TenantSwitchGetRequest tenantSwitchGetRequest =new TenantSwitchGetRequest();
        long tenantId = invocation.getTenantId();
        List<String> switchKey = new ArrayList<>();
        switchKey.add(StandardPara);
        tenantSwitchGetRequest.setTenantId(tenantId);
        tenantSwitchGetRequest.setSwitchKey(switchKey);
        TenantSwitchGetResponse tenantSwitchGetResponse = configThriftService.getTenantSwitch(tenantSwitchGetRequest);
        Map<String, String> switchValue = tenantSwitchGetResponse.getSwitchValue();
        /*
        上行流量拦截开关。可以适用于多规格。拦截器开关打开，直接拦截，不再判断灰度。兜底。
        开关key：switch_spu_interceptor_enable，默认false不拦截
         */
        if(tenantInterceptEnable(tenantId)){
            log.info("该租户上行数据被拦截，tenantId:{}",tenantId);
            return true;
        }
        /*
        规格规范化，如果Mcc打开，不拦截。否则判断是否灰度，灰度期间，拦截
         */
        if(MccConfigUtil.getMccDatasynchronizationParseSwitch()){
            return false;
        }
        if (switchValue.get(StandardPara) !=null && switchValue.get(StandardPara).equals(StandeSpecSwitch)){
            return true;
        }
        return false;
    }

    private boolean tenantInterceptEnable(Long tenantId){
        String tenantIdList = MccConfigUtil.getTenantIdOfIntercept();
        Set<Long> tenantIds = new HashSet<>();
        if (StringUtils.isNotBlank(tenantIdList)) {
            String[] tenantIdStrings = tenantIdList.split(",");
            for (String tenantIdString : tenantIdStrings) {
                tenantIds.add(Long.valueOf(tenantIdString.trim()));
            }
        }
        return tenantIds.contains(tenantId);
    }
}
