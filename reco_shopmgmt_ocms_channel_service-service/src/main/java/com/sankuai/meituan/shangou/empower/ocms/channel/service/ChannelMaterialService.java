package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialSearchRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialSearchResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialUploadResponse;

/**
 * 渠道素材服务
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface ChannelMaterialService {
    /**
     * 批量上传素材, 仅支持图片、视频
     *
     * @param request
     * @return
     */
    MaterialUploadResponse batchUploadMaterial(MaterialUploadRequest request);

    /**
     * 切换工具虚拟账号批量上传素材, 仅支持图片、视频
     *
     * @param request
     * @return
     */
    MaterialUploadResponse batchUploadMaterialByVirtualConfig(MaterialUploadRequest request);

    /**
     * 搜索素材详情
     *
     * @param request
     * @return
     */
    MaterialSearchResponse searchMaterial(MaterialSearchRequest request);

    /**
     * 切换工具虚拟账号搜索素材详情
     *
     * @param request
     * @return
     */
    MaterialSearchResponse searchMaterialByVirtualConfig(MaterialSearchRequest request);

}
