package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ProductSequenceBatchSetResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.SpecPictureBatchSaveRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.SpecPictureBatchSaveResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BasePageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSingleSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchUpdateCustomSpuStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelSpuDistributeInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetHeadQuarterSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuCategoryPropertyResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosByCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAppFoodCodeBySkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAuditStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryAuditStatusResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryChannelSpuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryChannelSpuIdResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryNormAuditDelSpuRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryNormAuditDelSpuResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryQualityProblemRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryQualityProblemResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendChannelCategoryQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSkuInSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuSellStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuId2AppFoodCodeResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuSellStatusInfoByChannelSkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuSellStatusInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStoreCategoryInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuUpcUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuWeightInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SubmitAppealInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuIdByNameAndSpecRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuIdByOriginIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateCustomSpuStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.UpdateSpuOptionFieldRequest;
import com.youzan.cloud.open.sdk.common.exception.SDKException;

import javax.annotation.Nullable;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * @create 2020-04-20 1:48 AM
 **/
public interface ChannelSpuService {
    /**
     * 按spu创建商品
     *
     * @param request
     * @return
     */
    ResultSpuData spuCreate(SpuInfoRequest request);

    /**
     * 按spu创建商品(单个）
     *
     * @param request
     * @return
     */
    default ResultSingleSpuData createSingleSpu(SingleSpuInfoRequest request){
        throw new RuntimeException("Current channel does not support create single spu");
    }

    /**
     * 按spu创建商品（用于清洗工具大批量处理）
     *
     * @param request
     * @return
     */
    default ResultSpuData spuCreateForCleaner(SpuInfoRequest request) {
        return null;
    }

    /**
     * 按upc创建商品
     *
     * @param request
     * @return
     */
    ResultSpuData upcCreate(SpuInfoRequest request);

    /**
     * 按upc创建商品（用于清洗工具大批量处理）
     *
     * @param request
     * @return
     */
    @Nullable
    default ResultSpuData upcCreateForCleaner(SpuInfoRequest request) {
        return null;
    }

    /**
     * 更新商品
     *
     * @param request
     * @return
     */
    ResultSpuData updateBySpuOrUpc(SpuInfoRequest request);

    /**
     *
     * 更新商品(单个)
     * @param request
     * @return
     */
    default ResultSingleSpuData updateSingleSpu(SingleSpuInfoRequest request) {
        throw new RuntimeException("Current channel does not support update single spu");
    }

    /**
     * 更新商品（用于清洗工具大批量处理）
     *
     * @param request
     * @return
     */
    @Nullable
    default ResultSpuData updateBySpuOrUpcForCleaner(SpuInfoRequest request) {
        return null;
    }

    /**
     * 更新商品
     *
     * @param request
     * @return
     */
    ResultSpuData updateWeightBySpu(SpuWeightInfoRequest request);
    /**
     * 删除商品
     *
     * @param request
     * @return
     */
    ResultSpuData deleteSpu(SpuInfoDeleteRequest request);

    /**
     * 删除商品(单个)
     *
     * @param request
     * @return
     */
    default ResultSingleSpuData deleteSingleSpu(SingleSpuInfoDeleteRequest request){
        throw new RuntimeException("Current channel does not support delete single spu");
    }

    // 提醒一下：单门店执行
    ResultSpuData deleteCategoryAndSpu(BaseRequest request);

    /**
     * 删除商品
     *
     * @param request
     * @return
     */
    ResultSpuData deleteSku(SkuInSpuInfoDeleteRequest request);


    /**
     * 删除单个商品
     * @param request
     * @return
     */
    default ResultSingleSpuData deleteSingleSku(SingleSkuInSpuInfoDeleteRequest request){
        throw new RuntimeException("Current channel does not support delete single spu");
    }

    /**
     * 商品上架/下架
     *
     * @param request
     * @return
     */
    ResultSpuData updateSpuSellStatus(SpuSellStatusInfoRequest request);

    /**
     * 商品上架/下架(单个)
     *
     * @param request
     * @return
     */
    default ResultSingleSpuData updateSingleSpuSellStatus(SingleSpuSellStatusRequest request) {
        throw new RuntimeException("Current channel does not support update single spu sell status");
    }

    default ResultData updateSpuSellStatusByChannelSkuIds(SpuSellStatusInfoByChannelSkuIdRequest request){
        throw new RuntimeException("Current channel does not support  updateSpuSellStatusByChannelSkuIds");
    }

    /**
     * 更新商品customSkuId
     *
     * @param request
     * @return
     */
    ResultSpuData updateCustomSpuIdByOriginId(UpdateCustomSpuIdByOriginIdRequest request);
    /**
     * 更新商品customSkuId
     *
     * @param request
     * @return
     */
    ResultSpuData updateCustomSpuIdByNameAndSpec(UpdateCustomSpuIdByNameAndSpecRequest request);
    /**
     * 获取单个商品信息
     * @param request
     * @return
     */
    GetSpuInfoResponse getSpuInfo(GetSpuInfoRequest request) throws SDKException;

    /**
     * 获取多个商品信息
     * @param request
     * @return
     */
    GetSpuInfosResponse getSpuInfoList(GetSpuInfosRequest request);

    /**
     * 批量拉取商品
     * @param request
     * @return
     */
    BatchGetSpuInfoResponse batchGetSpuInfo(BatchGetSpuInfoRequest request);


    default ResultSpuData distributeToStoreAsync(ChannelSpuDistributeInfoRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    default ResultSpuData getStoreDistributeStatus(ChannelSpuDistributeInfoRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    /**
     * 获取店铺内分类下商品
     * @param request
     * @return
     */
    default BatchGetSpuInfoResponse getSpuInfosByCategory(GetSpuInfosByCategoryRequest request){
        throw new UnsupportedOperationException("暂不支持方法");
    }
    GetHeadQuarterSpuInfoResponse batchGetHeadQuarterSpuInfo(BasePageRequest request);
    /**
     * 获取商品审核状态
     * @param request
     * @return
     */
    QueryAuditStatusResponse getAuditStatus(QueryAuditStatusRequest request);



    /**
     * 根据商品UPC或商品名称查询平台推荐类目信息
     * @param request 请求参数
     * @retur 平台推荐的类目
     */
    RecommendChannelCategoryQueryResponse queryRecommendChannelCategory(RecommendChannelCategoryQueryRequest request);


    SkuId2AppFoodCodeResponse queryAppFoodCodeBySkuId(QueryAppFoodCodeBySkuIdRequest request);

    /**
     * 更新渠道商品店内分类排序
     * @param request
     * @return
     */
    ResultStatus updateCustomSpuStoreCategory(UpdateCustomSpuStoreCategoryRequest request);


    /**
     * 批量更新商品的店内分类code
     * @param request
     * @return
     */
    ResultSpuData batchUpdateSpuStoreCategoryCode(BatchUpdateCustomSpuStoreCategoryRequest request);




    SensitiveWordCheckResponse checkSensitiveWords(SensitiveWordCheckRequest request);

    /**
     * 查询合规审核删除的商品详情
     * @param request
     * @return
     */
    QueryNormAuditDelSpuResponse queryNormAuditDelSpuInfo(QueryNormAuditDelSpuRequest request);

    /**
     * 查询渠道内部实体商品id
     * @param request
     * @return
     */
    QueryChannelSpuIdResponse queryChannelSpuId(QueryChannelSpuIdRequest request);

    /**
     * 批量设置商品顺序
     * 
     * @param request
     * @return
     */
    ProductSequenceBatchSetResponse batchSetSequence(ProductSequenceBatchSetRequest request);
    /**
     * 更新渠道商品店内分类
     *
     * @param request 请求
     * @return 结果
     */
    ResultSpuData updateStoreCategory(SpuStoreCategoryInfoRequest request);

    /**
     * 更新商品的 upc
     * @param request
     * @return
     */
    default ResultSpuData updateSpuUpc(SpuUpcUpdateRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    /**
     * 批量设置渠道规格
     * @param request
     * @return
     */
    default SpecPictureBatchSaveResponse batchSaveSpecPicture(SpecPictureBatchSaveRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }


    /**
     * 批量查询商品质量信息
     * @param request
     * @return
     */
    QueryQualityProblemResponse queryQualityProblem(QueryQualityProblemRequest request);


    /**
     * 单个SPU更新接口，且不传入的接口，默认是不会参与更新的
     */
    default ResultStatus updateOptionFieldBySpu(UpdateSpuOptionFieldRequest request) {
        return ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }

    default GetSpuCategoryPropertyResponse getSpuCategoryPropertyInfo(GetSpuInfoRequest request){
        throw new UnsupportedOperationException("Current channel does not support get spu category property");
    }

    /**
     * 提交商品申诉信息
     *
     * @param request
     * @return
     */
    ResultStatus submitAppealInfo(SubmitAppealInfoRequest request);
}
