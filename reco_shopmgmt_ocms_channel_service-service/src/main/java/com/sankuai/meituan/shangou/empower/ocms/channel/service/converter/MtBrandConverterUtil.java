package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.alibaba.fastjson.JSONObject;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderProductLabelEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhChannelOrderSettlementDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MTFeeDetailEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.MTSettleStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpuStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ProductLabelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ProductPickingDateRangeDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ShippingTime;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementSkuBenefitDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementSkuShippingDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.SettlementFeeDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.PhoneNumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2019-06-25
 * @desc 美团
 */
@Slf4j
public class MtBrandConverterUtil {

    private static final String MT_BILL_TIME_CHARS = "~";
    private static final int MT_BILL_TIME_CHARS_LENGTH = 2;
    public static final String NO_UPC = "no_upc";

    /**
     * 医药门店商品跳过upc校验的标志
     */
    public static final String MEDICINE_NO_UPC = "商品无条码";

    /**
     * 美团渠道类目类型-医药，主要是为了区分零售和医药类目针对upc跳过校验的标记字段
     */
    public static final int MT_CHANNEL_CATEGORY_SOURCE_TYPE_MEDICINE = 2;

    /**
     * 美团返还配送费的配送类型code, 快速达 4011、及时达4012  光速达 4001配送方式
     */
    public static final Set<String> MT_REBACK_FREIGHT_LOGISTICS_CODE = new HashSet<>(Arrays.asList("4011", "4012", "4001"));

    /**
     * 字段值置空时的特殊值
     */
    public static final String EMPTY_VALUE = "EMPTY_VALUE";

    public static List<SettlementFeeDetail> convertSettlementFeeList(MTOrderSettlementListResult mtOrderSettlementListResult) {
        //处理
        List<SettlementFeeDetail> settlementFeeDetailList = Lists.newArrayList();
        for (MTFeeDetailEnum value : MTFeeDetailEnum.values()) {
            if (StringUtils.isBlank(value.getCode())) {
                continue;
            }
            String fieldValue = ConverterUtils.getFieldValue(mtOrderSettlementListResult, value.getCode());
            if (fieldValue != null) {
                settlementFeeDetailList.add(buildSettlementFeeDetail(fieldValue, value, mtOrderSettlementListResult.isRefund()));
            }
        }
        return settlementFeeDetailList;
    }

    private static SettlementFeeDetail buildSettlementFeeDetail(String fieldValue, MTFeeDetailEnum detailEnum, boolean isRefund) {
        double feeValue = Double.parseDouble(fieldValue);
        SettlementFeeDetail settlementFeeDetail = new SettlementFeeDetail();
        settlementFeeDetail.setFeeKey(detailEnum.getCode());
        settlementFeeDetail.setFeeDesc(detailEnum.getDescription());
        settlementFeeDetail.setFeeValue(BigDecimal.valueOf(feeValue)
                .multiply(BigDecimal.valueOf(detailEnum.getConvertPoint()))
                .setScale(0, RoundingMode.HALF_UP).longValue());
        if (isRefund && detailEnum.getSymbol() == 0) {
            settlementFeeDetail.setFeeValue(-settlementFeeDetail.getFeeValue());
        }
        return settlementFeeDetail;
    }

    public static List<SettlementFeeDetail> convertQnhSettlementFeeDetail(QnhChannelOrderSettlementDetail detail) {
        List<SettlementFeeDetail> details = new ArrayList<>();
        for (MTFeeDetailEnum value : MTFeeDetailEnum.values()) {
            if (StringUtils.isBlank(value.getQnhField())) {
                continue;
            }
            String qnhFieldValue = ConverterUtils.getFieldValue(detail, value.getQnhField());
            if (qnhFieldValue != null) {
                details.add(buildSettlementFeeDetail(qnhFieldValue, value, detail.isRefund()));
            }
        }
        return details;
    }


    public static MTSettleStatusEnum convertMtSettleStatus(int mtSettleStatus) {
        return MTSettleStatusEnum.enumOf(mtSettleStatus);

    }

    public static List<SaleAttrValue> convertOpenSaleAttrValueList(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        List<SaleAttrValue> res = new ArrayList<>();
        if (Objects.isNull(skuInSpuInfoDTO)) {
            return res;
        }
        List<SaleAttrValueDTO> saleAttrValueList = skuInSpuInfoDTO.getSaleAttrValueList();
        if (CollectionUtils.isNotEmpty(saleAttrValueList)) {
            for (SaleAttrValueDTO saleAttrValueDTO : saleAttrValueList) {
                SaleAttrValue saleAttrValue = new SaleAttrValue();
                saleAttrValue.setAttrId(saleAttrValueDTO.getAttrId());
                saleAttrValue.setValueId(saleAttrValueDTO.getValueId());
                saleAttrValue.setValue(saleAttrValueDTO.getValue());
                res.add(saleAttrValue);
            }
        }
        return res;
    }


    public static long convertSettlementFinishDate(String mtBillTime, long settlementDate) {
        if (StringUtils.isNotBlank(mtBillTime)) {
            String[] split = mtBillTime.split(MT_BILL_TIME_CHARS);
            if (split.length == MT_BILL_TIME_CHARS_LENGTH) {
                return DateUtils.parse(split[1], "yyyy-MM-dd").getTime();
            }
        }

        return settlementDate;
    }

    public static long orderCreateTime(ChannelOrderDetail channelOrderDetail) {
        if (channelOrderDetail != null) {
            if (channelOrderDetail.getCtime() != null) {
                return TimeUnit.SECONDS.toMillis(channelOrderDetail.getCtime());
            } else if (channelOrderDetail.getUtime() != null) {
                return TimeUnit.SECONDS.toMillis(channelOrderDetail.getUtime());
            }
        }
        return System.currentTimeMillis();
    }

    /**
     * 退款类型：1-全额退款；2-部分退款；3-退差价。
     **/
    public static int convertRefundType(int refund_type) {
        switch (refund_type) {
            case 1:
                return RefundTypeEnum.ALL.getValue();
            case 2:
                return RefundTypeEnum.PART.getValue();
            case 3:
                return RefundTypeEnum.WEIGHT.getValue();
            default:
                return RefundTypeEnum.PART.getValue();

        }
    }

    public static long convertTime(int time) {
        return time * 1000L;
    }

    /**
     * 解析是否为商家自配送
     *
     * @param logisticsCode 订单配送方式
     * @return
     */
    public static int isSelfDelivery(String logisticsCode) {
        //自配送 1003实际为自配+众包，需要商家自己通过渠道配置保证只发自配
        if ("0000".equals(logisticsCode) || "1003".equals(logisticsCode)) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 解析原始配送类型
     *
     * @param logisticsCode 订单配送方式
     * @return
     */
    public static int convertDeliveryType(String logisticsCode) {
        if (StringUtils.isBlank(logisticsCode)) {
            return DistributeTypeEnum.UN_KNOWN.getValue();
        }

        switch (logisticsCode) {
            case "0000":
                return DistributeTypeEnum.SELF_DELIVERY.getValue();
            case "1001":
            case "1002":
            case "1004":
                return DistributeTypeEnum.ZHUAN_SONG.getValue();
            case "2002":
                return DistributeTypeEnum.KUAI_SONG.getValue();
            case "3001":
                return DistributeTypeEnum.COMBO.getValue();
            case "1003":
                return DistributeTypeEnum.ZONG_BAO.getValue();
            case "4001":
            case "4011":
            case "4012":
            case "4015":
                return DistributeTypeEnum.QIKE.getValue();
            default:
                return DistributeTypeEnum.UN_KNOWN.getValue();
        }
    }


    /**
     * 解析获取用户隐私号
     *
     * @param channelOrderDetail
     * @return
     */
    public static String getUserPrivacyPhone(ChannelOrderDetail channelOrderDetail) {
        /**
         *  判断receipt_phone是否是真实号码
         *  1. 号码是11位数字
         *  2. 备注信息里面没有形如"手机号 158****9248"字段
         */
        String recipientPhone = channelOrderDetail.getRecipient_phone();
        boolean recipientPhoneIsReal = PhoneNumberUtils.isValidElevenNumberMobileNumber(recipientPhone);
        String cautionRemarkPhone = getCautionRemarkPhone(channelOrderDetail);
        if (recipientPhoneIsReal && StringUtils.isBlank(cautionRemarkPhone)) {
            // receipt_phone是真实号码，使用该号码作为隐私号返回
            return PhoneNumberUtils.transferToPrivacyPhone(recipientPhone);
        }
        // 返回订单备注中的隐私号码
        return cautionRemarkPhone;
    }

    /**
     * 获取订单备注中的手机虚拟号
     *
     * @param channelOrderDetail 【如遇缺货】： 缺货时电话与我沟通 收餐人隐私号 13049479512_4121，手机号 158****9248 到店自取
     * @return 13049479512_4121
     */
    private static String getCautionRemarkPhone(ChannelOrderDetail channelOrderDetail) {
        String privacyPhoneRemarkPattern = ".+手机号 \\d{3}\\*{4}\\d{4}.*";
        String caution = channelOrderDetail.getCaution();
        if (Pattern.matches(privacyPhoneRemarkPattern, caution)) {
            int index = caution.indexOf("手机号 ");
            return caution.substring(index + 4, index + 15);
        }
        // 如果用户没有开启隐私号模式，订单备注信息中没有隐私号，返回空字符串
        return "";
    }

    public static long getLatestCloseTime(String shippingTime) {
        if (StringUtils.isBlank(shippingTime)) {
            return 0L;
        }
        String[] shippingTimeList = shippingTime.split(";", Calendar.DAY_OF_WEEK);
        if (shippingTimeList.length == Calendar.DAY_OF_WEEK) {
            int week = todayForWeek();
            shippingTime = shippingTimeList[week];
        }
        if (StringUtils.isBlank(shippingTime)) {
            return 0L;
        }
        String[] todayShippingTimes = shippingTime.split(",");
        String[] todayShippingTimeList = todayShippingTimes[todayShippingTimes.length - 1].split("-");
        if (todayShippingTimeList.length != 2) {
            return 0L;
        }
        return ConverterUtils.getMillsByHour(todayShippingTimeList[1]);
    }


    public static List<ShippingTime> convertShippingTime(String shippingTime) {
        if (StringUtils.isEmpty(shippingTime)) {
            return Lists.newArrayList();
        }

        return Arrays.asList(StringUtils.split(shippingTime, ";"))
                .stream().map(s -> {
                    String[] times = StringUtils.split(s, "-");
                    if (times.length < 2) {
                        return new ShippingTime();
                    }
                    return new ShippingTime(times[0], times[1]);
                }).collect(Collectors.toList());
    }


    /**
     * 判断当前日期是星期几<br>
     * <br>
     *
     * @return dayForWeek 判断结果<br>
     * @Exception 发生异常<br>
     */
    private static int todayForWeek() {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        int dayForWeek = 0;
        if (c.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            dayForWeek = 6;
        } else {
            dayForWeek = c.get(Calendar.DAY_OF_WEEK) - 2;
        }
        return dayForWeek;
    }

    /**
     * 可售时间转换成美团开放平台需要的map格式
     *
     * @param json
     * @return
     */
    public static Map<String, String> convertStringToMapForAvailableTimes(String json) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(json);
        Map<String, String> currentDataMap = (Map) jsonObject;
        return currentDataMap;
    }


    /**
     * 开放平台返回的格式有问题，沟通结果为先自己看能不能处理，去到最外层的双引号
     * {"monday":"\"04:00-05:00\"","tuesday":"\"04:00-05:00\"","wednesday":"\"04:00-05:00\"","thursday":"\"04:00-05:00\"","friday":"\"04:00-05:00\"","saturday":"\"04:00-05:00\"","sunday":"\"04:00-05:00\""}
     *
     * @param map
     * @return
     */
    public static String convertMapToStringForAvailableTimes(Map<String, String> map) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String str = entry.getValue().replaceAll("\\\"", "");
            entry.setValue(str);
        }
        return JSONObject.toJSONString(map);
    }


    /**
     * 对单分类、多分类做兼容逻辑处理
     * 优先用code，没有code用name
     * 用于美团渠道多分类编码列表的生成，如果多分类列表中只有一个分类，作为单分类处理
     * 将末级店内分类列表转换成需要的字符串
     * 美团开放平台的限制为：category_code_list与category_name、category_code、category_name_list字段必须且只能填写一个
     *
     * @param categoryList
     * @return
     */
    public static String convert2MTCategory_code_list(List<ChannelLeafStoreCategory> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return null;
        }
        if (categoryList.size() == 1) {
            return null;
        }
        List<String> codes = categoryList.stream().map(ChannelLeafStoreCategory::getCategoryCode).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(codes)) {
            return null;
        }
        String result = StringUtils.join(codes, ",");
        result = String.format("\"%s\"", StringUtils.join(result.split(","), "\",\""));
        return "[" + result + "]";
    }

    /**
     * 对单分类、多分类做兼容逻辑处理
     * 优先用code，没有code用name
     * 用于美团渠道多分类名称列表的生成,如果多分类列表中只有一个分类，作为单分类处理
     * 将末级店内分类列表转换成需要的字符串
     * 美团开放平台的限制为：category_code_list与category_name、category_code、category_name_list字段必须且只能填写一个
     *
     * @param categoryList
     * @return
     */
    public static String convert2MTCategory_name_list(List<ChannelLeafStoreCategory> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return null;
        }
        if (categoryList.size() == 1) {
            return null;
        }
        List<String> codes = categoryList.stream().map(ChannelLeafStoreCategory::getCategoryCode).filter(Objects::nonNull).collect(Collectors.toList());
        //说明有category_code_list，category_name_list已经不能再赋值了
        if (CollectionUtils.isNotEmpty(codes)) {
            return null;
        }
        List<String> names = categoryList.stream().map(ChannelLeafStoreCategory::getCategoryName).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(names)) {
            return null;
        }
        String result = StringUtils.join(names, ",");
        result = String.format("\"%s\"", StringUtils.join(result.split(","), "\",\""));
        return "[" + result + "]";
    }

    /**
     * 对单分类、多分类做兼容逻辑处理，优先用code，没有code用name
     *
     * @param skuInfoDTO
     * @return
     */
    public static String convert2MTCategoryName(SkuInfoDTO skuInfoDTO) {
        if (CollectionUtils.isEmpty(skuInfoDTO.getLeafStoreCategoryList())) {
            return skuInfoDTO.isSetChannelFrontCategory() ? null : skuInfoDTO.getFrontCategoryName();
        }
        if (skuInfoDTO.getLeafStoreCategoryList().size() == 1) {
            if (StringUtils.isEmpty(skuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryCode())) {
                return skuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryName();
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 对单分类、多分类做兼容逻辑处理，优先用code，没有code用name
     *
     * @param skuInfoDTO
     * @return
     */
    public static String convert2MTCategoryCode(SkuInfoDTO skuInfoDTO) {
        if (CollectionUtils.isEmpty(skuInfoDTO.getLeafStoreCategoryList())) {
            return skuInfoDTO.getChannelFrontCategory();
        }
        if (skuInfoDTO.getLeafStoreCategoryList().size() == 1) {
            return skuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryCode();
        } else {
            return null;
        }
    }

    // 创建SPU映射转换
    public static String convertSpec(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        // 传空字符串置空
        if (Objects.nonNull(skuInSpuInfoDTO.getSpec()) && Strings.isBlank(skuInSpuInfoDTO.getSpec())) {
            return "EMPTY_VALUE";
        }
        return skuInSpuInfoDTO.getSpec();
    }

    public static String convertPrice(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        // 更新商品不更新价格
        if (!skuInSpuInfoDTO.isSetPrice()) {
            return null;
        }
        if (skuInSpuInfoDTO.getPrice() < 0) {
            return String.valueOf(0);
        } else {
            return String.valueOf(skuInSpuInfoDTO.getPrice());
        }
    }

    public static String convertStock(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (!skuInSpuInfoDTO.isSetStock()) {
            return null;
        }
        return String.valueOf(skuInSpuInfoDTO.getStock());
    }

    /**
     * UPC转美团平台UPC
     *
     * @param skuInSpuInfoDTO
     * @return
     */
    public static String convertUpc(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        String upc = skuInSpuInfoDTO.getUpc();
        int channelCategorySourceType = skuInSpuInfoDTO.getChannelCategorySourceType();
        if (channelCategorySourceType == MT_CHANNEL_CATEGORY_SOURCE_TYPE_MEDICINE) {
            return convertUpcForMedicine(upc);
        }

        return convertUpc(upc);
    }

    public static String convertUpc(String upc) {
        if (StringUtils.isBlank(upc)) {
            return NO_UPC;
        }

        int upcMaxLength = MccConfigUtil.getMtSkuUpcMaxLength();
        if (upc.length() > upcMaxLength) {
            return NO_UPC;
        }

        return upc;
    }

    public static String convertUpcForMedicine(String upc) {
        if (StringUtils.isBlank(upc)) {
            return MEDICINE_NO_UPC;
        }

        int upcMaxLength = MccConfigUtil.getMtSkuUpcMaxLength();
        if (upc.length() > upcMaxLength) {
            return MEDICINE_NO_UPC;
        }

        return upc;
    }

    public static String convertWeightForUnit(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (StringUtils.isBlank(skuInSpuInfoDTO.getWeightUnit())) {
            return null;
        }
        return new BigDecimal(String.valueOf(skuInSpuInfoDTO.getWeightForUnit())).stripTrailingZeros().toPlainString();
    }


    public static String convertWeightUnit(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (StringUtils.isBlank(skuInSpuInfoDTO.getWeightUnit())) {
            return null;
        }
        return skuInSpuInfoDTO.getWeightUnit();
    }

    public static Integer convertWeight(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (StringUtils.isNotBlank(skuInSpuInfoDTO.getWeightUnit())) {
            return null;
        }
        if (!skuInSpuInfoDTO.isSetWeight()) {
            return null;
        }
        return skuInSpuInfoDTO.getWeight();
    }

    /*
     * ladder_box_num和ladder_box_price的组合与box_num和box_price互斥
     */

    public static String convertBoxNum(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (skuInSpuInfoDTO.getIsLadderBox() > 0) {
            return null;
        }
        if (skuInSpuInfoDTO.getBoxQuantity() <= 0) {
            return String.valueOf(1);
        } else {
            return String.valueOf(skuInSpuInfoDTO.getBoxQuantity());
        }
    }

    public static String convertBoxPrice(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (skuInSpuInfoDTO.getIsLadderBox() > 0) {
            return null;
        }
        if (skuInSpuInfoDTO.getBoxPrice() <= 0) {
            return String.valueOf(0);
        } else {
            return String.valueOf(skuInSpuInfoDTO.getBoxPrice());
        }
    }

    public static String convertLadderBoxNum(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (skuInSpuInfoDTO.getIsLadderBox() == 0) {
            return null;
        }
        return String.valueOf(skuInSpuInfoDTO.getLadderBoxQuantity());
    }

    public static String convertLadderBoxPrice(SkuInSpuInfoDTO skuInSpuInfoDTO) {
        if (skuInSpuInfoDTO.getIsLadderBox() == 0) {
            return null;
        }
        return String.valueOf(skuInSpuInfoDTO.getLadderBoxPrice());
    }

    public static Integer convertStatus(SpuInfoDTO spuInfoDTO) {
        // 商品上下架状态（1-上架；2-下架）-> 美团平台 0-上架，1-下架
        if (spuInfoDTO.getStatus() == SpuStatusEnum.ON_LINE.getCode()) {
            return 0;
        }
        return 1;
    }

    public static Integer convertIsSpecialty(SpuInfoDTO spuInfoDTO) {
        if (spuInfoDTO.getIsSpecialty() < 0) {
            return null;
        } else {
            return Integer.valueOf(spuInfoDTO.getIsSpecialty());
        }
    }

    public static Integer convertSequence(SpuInfoDTO spuInfoDTO) {
        if (spuInfoDTO.getSequence() < 0) {
            return null;
        } else {
            return Integer.valueOf(spuInfoDTO.getSequence());
        }
    }

    public static String convert2MTCategoryCode(SpuInfoDTO spuInfoDTO) {
        if (CollectionUtils.isEmpty(spuInfoDTO.getLeafStoreCategoryList())) {
            return spuInfoDTO.getLeafStoreCategoryCode();
        }
        if (spuInfoDTO.getLeafStoreCategoryList().size() == 1) {
            return spuInfoDTO.getLeafStoreCategoryList().get(0).getCategoryCode();
        } else {
            return null;
        }
    }

    // 拉取SPU映射转换

    public static Integer convertBoxNum(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getBox_num())) {
            return Double.valueOf(channelSkuInfoDTO.getBox_num()).intValue();
        } else if (StringUtils.isNotBlank(channelSkuInfoDTO.getLadder_box_num())) {
            return Double.valueOf(channelSkuInfoDTO.getLadder_box_num()).intValue();
        } else {
            return 1;
        }
    }

    public static Double convertBoxPrice(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getBox_price())) {
            return Double.valueOf(channelSkuInfoDTO.getBox_price());
        } else if (StringUtils.isNotBlank(channelSkuInfoDTO.getLadder_box_price())) {
            return Double.valueOf(channelSkuInfoDTO.getLadder_box_price());
        } else {
            return 0.0D;
        }
    }

    public static Integer convertLadderBoxQuantity(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getLadder_box_num())) {
            return Double.valueOf(channelSkuInfoDTO.getLadder_box_num()).intValue();
        } else {
            return 0;
        }
    }

    public static Double convertLadderBoxPrice(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getLadder_box_price())) {
            return Double.valueOf(channelSkuInfoDTO.getLadder_box_price());
        }
        return 0.0D;
    }

    public static String convertUnit(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getUnit())) {
            return channelSkuInfoDTO.getUnit();
        } else {
            return "份";
        }
    }

    public static String convertPictureContents(SpuInfoDTO spuInfoDTO) {
        List<String> pictureContents = spuInfoDTO.getPictureContents();
        if (CollectionUtils.isEmpty(pictureContents)) {
            return Strings.EMPTY;
        }
        return StringUtils.join(pictureContents, ',');
    }

    public static String convertQualificationPictures(SpuInfoDTO spuInfoDTO) {
        if(CollectionUtils.isNotEmpty(spuInfoDTO.getSpecialPictureInfos())){
            return null;
        }
        List<String> qualificationPictures = spuInfoDTO.getQualificationPictures();
        if (CollectionUtils.isEmpty(qualificationPictures)) {
            return EMPTY_VALUE;
        }
        return StringUtils.join(qualificationPictures, ',');
    }

    public static List<SpecialPictureInfoDTO> convertSpecialPictureInfos(SpuInfoDTO spuInfoDTO) {
        List<ProductSpecialPicture> specialPictureInfos = spuInfoDTO.getSpecialPictureInfos();
        if(CollectionUtils.isEmpty(specialPictureInfos)){
            return null;
        }
        return specialPictureInfos.stream().filter(Objects::nonNull).map(productSpecialPicture -> {
            SpecialPictureInfoDTO specialPicture = new SpecialPictureInfoDTO();
            specialPicture.setSpecial_picture_type(productSpecialPicture.getSpecialPictureType());
            specialPicture.setSpecial_picture_title(productSpecialPicture.getSpecialPictureTitle());
            if(CollectionUtils.isEmpty(productSpecialPicture.getSpecialPictures())){
                specialPicture.setEmptyOrNot(EMPTY_VALUE);
            }else {
                specialPicture.setSpecial_picture_urls(StringUtils.join(productSpecialPicture.getSpecialPictures(), ','));
            }
            return specialPicture;
        }).collect(Collectors.toList());
    }

    public static Integer convertWeight(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (channelSkuInfoDTO.getWeight() > 0) {
            return channelSkuInfoDTO.getWeight();
        } else {
            return 0;
        }
    }

    public static Double convertWeightForUnit(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getWeight_for_unit())) {
            return Double.valueOf(channelSkuInfoDTO.getWeight_for_unit());
        } else {
            return 0.0D;
        }
    }

    public static Double convertPrice(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isNotBlank(channelSkuInfoDTO.getPrice())) {
            return Double.valueOf(channelSkuInfoDTO.getPrice());
        } else {
            return 0.0D;
        }
    }

    public static Integer convertStock(ChannelSkuInfoDTO channelSkuInfoDTO) {
        if (StringUtils.isBlank(channelSkuInfoDTO.getStock())) {
            return 99999;
        } else if (channelSkuInfoDTO.getStock().equalsIgnoreCase("*")) {
            return 99999;
        } else {
            return Integer.valueOf(channelSkuInfoDTO.getStock());
        }
    }

    public static String convertStock(SkuInSpuStockDTO skuStockDTO) {
        if (skuStockDTO.getStockQty() < 0) {
            return String.valueOf(0);
        } else {
            return String.valueOf((int) skuStockDTO.getStockQty());
        }
    }


    public static List<ChannelSettlementSkuBenefitDetail> convertSkuBenefitDetailList(List<MTOrderSettlementListResult
            .WmAppOrderSkuBenefitDetail> wmAppOrderSkuBenefitDetailList) {
        return Optional.ofNullable(wmAppOrderSkuBenefitDetailList).map(List::stream).orElse(Stream.empty())
                .map(MTOrderSettlementListResult.WmAppOrderSkuBenefitDetail::toChannelSettlementSkuBenefityDetail)
                .collect(Collectors.toList());
    }

    public static List<ChannelSettlementSkuShippingDetail> convertSkuShippingDetailList(List<MTOrderSettlementListResult
            .WmAppOrderSkuShippingDetail> wmAppOrderShippingActDetails) {
        return Optional.ofNullable(wmAppOrderShippingActDetails).map(List::stream).orElse(Stream.empty())
                .map(MTOrderSettlementListResult.WmAppOrderSkuShippingDetail::toChannelSettlementSkuShippingDetail)
                .collect(Collectors.toList());
    }

    public static List<ProductLabelDTO> convertProductLabelList(List<SkuDetailLabelDTO> labelList) {
        try {
            if(CollectionUtils.isEmpty(labelList)){
                return Lists.newArrayList();
            }
            return labelList.stream().filter(Objects::nonNull)
                    .map(item -> {
                        ProductLabelDTO productLabelDTO = new ProductLabelDTO();
                        if (item.getLabel_code() == null) {
                            return productLabelDTO;
                        }
                        Long label = item.getLabel_code();
                        Map<Long, Integer> mtProductLabelMap = MccConfigUtil.getMtProductLabelMap();
                        if(mtProductLabelMap == null || Objects.isNull(mtProductLabelMap.get(label))){
                            return null;
                        }
                        productLabelDTO.setType(mtProductLabelMap.get(label));
                        productLabelDTO.setName(item.getLabel_name());
                        productLabelDTO.setDesc(item.getDescription());
                        return productLabelDTO;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }catch (Exception e){
            log.error("转换商品标签异常", e);
        }
        return Lists.newArrayList();
    }

    public static ProductPickingDateRangeDTO convertProductPickingDateRange(SkuDetailPikingDateRangeDTO skuDetailPikingDateRangeDTO) {
        try {
            if(skuDetailPikingDateRangeDTO == null){
                return null;
            }
            ProductPickingDateRangeDTO productPickingDateRangeDTO = new ProductPickingDateRangeDTO();
            productPickingDateRangeDTO.setProductionStartDate(skuDetailPikingDateRangeDTO.getProduction_start_date());
            productPickingDateRangeDTO.setProductionEndDate(skuDetailPikingDateRangeDTO.getProduction_end_date());
            return productPickingDateRangeDTO;
        }catch (Exception e){
            log.error("转换拣货时间段（新鲜承诺）异常", e);
        }
        return null;
    }

    /**
     * 检查美团配送code是否需要返还配送费,运费返还:快速达 4011、及时达4012  光速达 4001配送方式
     *
     * @param logisticsCode
     * @return
     */
    public static boolean checkLogisticsCodeRebackFreight(String logisticsCode) {
        return MT_REBACK_FREIGHT_LOGISTICS_CODE.contains(logisticsCode);
    }


    public static List<SkuAttr> parseSkuAttr(String skuAttr) {
        if(StringUtils.isBlank(skuAttr)){
            return null;
        }
        return JacksonUtils.parseList(skuAttr, SkuAttr.class);
    }


}
