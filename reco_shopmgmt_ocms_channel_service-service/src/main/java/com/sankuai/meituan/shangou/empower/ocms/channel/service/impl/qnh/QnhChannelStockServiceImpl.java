package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.qnh;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.QnhProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhCommonResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.QnhSkuSpuMappingInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostQnhEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelStockService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultErrorSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSuccessSku;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("qnhChannelStockService")
public class QnhChannelStockServiceImpl implements ChannelStockService {
    public static final int STOCK_UPDATE_MAX_COUNT = 50;

    @Autowired
    private QnhChannelGateService qnhChannelGateService;

    @Resource
    private QnhSkuSpuConvertService qnhSkuSpuConvertService;

    @Resource
    private QnhStoreService qnhStoreService;

    @Override
    public ResultSpuData updateStockBySpu(SpuStockRequest request) {
        return null;
    }

    @Override
    public ResultData updateStockMultiChannel(SkuStockMultiChannelRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        if (CollectionUtils.isEmpty(request.getSkuStockList())) {
            return resultData;
        }
        String storeId = qnhStoreService.mappingQnhStoreIdByStoreId(request.getTenantId(), request.skuStockList.get(0).getStoreId());
        ArrayListMultimap<ChannelResultStatusEnum, SkuStockMultiChannelDTO> multimap = ArrayListMultimap.create();

        ListUtils.listPartition(request.skuStockList, STOCK_UPDATE_MAX_COUNT).forEach(skuStockMultiChannelDTOS -> {
            Map<String, SkuStockMultiChannelDTO> skuStockMap = new HashMap<>();
            skuStockMultiChannelDTOS.forEach(stock -> {
                skuStockMap.put(stock.getSkuId(), stock);
            });
            try {
                List<QnhSkuSpuMappingInfo> qnhSkuSpuMappingInfos = qnhSkuSpuConvertService.queryQnhInfoByFnSkuList(request.getTenantId(),new ArrayList<>(skuStockMap.keySet()));
                if (CollectionUtils.isEmpty(qnhSkuSpuMappingInfos)) {
                    log.info("skuStockMultiChannelDTOS:{},qnhSkuSpuMappingInfos:{}", skuStockMultiChannelDTOS, qnhSkuSpuMappingInfos);
                    multimap.putAll(ChannelResultStatusEnum.VALIDATE_CONVERT_ERROR, skuStockMultiChannelDTOS);
                    return;
                }
                Map<String, Object> bizParam = Maps.newHashMap();
                List<Map<String, Object>> items = new ArrayList<>();
                qnhSkuSpuMappingInfos.forEach(mapping -> {
                    Map<String, Object> stockBizParam = Maps.newHashMap();
                    stockBizParam.put(QnhProjectConstant.REGION_CODE, storeId);
                    stockBizParam.put(QnhProjectConstant.QNH_SKU_ID, mapping.getQnhSkuId());
                    stockBizParam.put(QnhProjectConstant.UPC, mapping.getQnhUpc());
                    stockBizParam.put(QnhProjectConstant.QNH_ID, mapping.getQnhId());
                    stockBizParam.put(QnhProjectConstant.STOCK, skuStockMap.get(mapping.getSkuId()).getStockQty());
                    items.add(stockBizParam);
                });
                bizParam.put(QnhProjectConstant.ITEM, items);
                QnhCommonResponse<String> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.BATH_UPDATE_STORE_STOCK, bizParam);
                log.info("updateStockMultiChannel,response:{}", qnhCommonResponse);
                if (qnhCommonResponse != null && "0".equals(qnhCommonResponse.getCode())) {
                    multimap.putAll(ChannelResultStatusEnum.SUCCESS, skuStockMultiChannelDTOS);
                    return;
                }
                multimap.putAll(ChannelResultStatusEnum.OTHER_ERROR, skuStockMultiChannelDTOS);
            } catch (Exception e) {
                log.warn("skuStockMultiChannelDTOS:{} error", skuStockMultiChannelDTOS, e);
                multimap.putAll(ChannelResultStatusEnum.OTHER_ERROR, skuStockMultiChannelDTOS);
            }
        });
        for (ChannelResultStatusEnum statusEnum : multimap.keySet()) {
            List<SkuStockMultiChannelDTO> stockMultiChannelDTOList = multimap.get(statusEnum);
            if (statusEnum == ChannelResultStatusEnum.SUCCESS) {
                List<ResultSuccessSku> successSkuList = new ArrayList<>();
                stockMultiChannelDTOList.forEach(w -> {
                    ResultSuccessSku successSku = new ResultSuccessSku();
                    successSku.setSkuId(w.getSkuId());
                    successSku.setChannelId(w.getChannelId());
                    successSku.setChannelSkuId(w.getChannelSkuId());
                    successSku.setSkuId(w.getSkuId());
                    successSku.setStoreId(w.getStoreId());
                    successSkuList.add(successSku);
                });
                resultData.setSucData(successSkuList);
            } else {
                if (resultData.getErrorData() == null) {
                    resultData.setErrorData(new ArrayList<>());
                }
                stockMultiChannelDTOList.forEach(w -> {
                    ResultErrorSku errorSku = new ResultErrorSku();
                    errorSku.setSkuId(w.getSkuId());
                    errorSku.setChannelId(w.getChannelId());
                    errorSku.setChannelSkuId(w.getChannelSkuId());
                    errorSku.setSkuId(w.getSkuId());
                    errorSku.setStoreId(w.getStoreId());
                    errorSku.setErrorCode(statusEnum.getCode());
                    errorSku.setErrorMsg(statusEnum.getDesc());
                    resultData.getErrorData().add(errorSku);
                });
            }
        }
        return resultData;
    }

    @Override
    public BatchGetStockInfoResponse batchGetStockInfo(BatchGetStockInfoRequest request) {
        return null;
    }
}
