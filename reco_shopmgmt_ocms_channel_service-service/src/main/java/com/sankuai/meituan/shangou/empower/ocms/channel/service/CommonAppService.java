package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtTokenMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.youzan.AppMessage;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.common.AppSecretDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.common.TokenDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.common.request.AppSecretRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.common.request.QueryTokenRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand.MtBrandChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.youzan.YzAccessTokenService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.application.AppInfoDTO;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: reco_shopmgmt_ocms_channel_service
 * @description: 通用应用服务类
 * @author: jinyi
 * @create: 2024-12-26 11:43
 **/
@Service
@Slf4j
public class CommonAppService {


    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private YzAccessTokenService yzAccessTokenService;
    
    @Autowired
    private DouyinChannelCommonService douyinChannelCommonService;



    public TokenDTO queryToken(QueryTokenRequest request){
        TokenDTO tokenDTO = new TokenDTO();
        Integer channelId = request.getChannelId();
        String accessToken = "";

        try {
            if (ChannelTypeEnum.MEITUAN.getCode() == channelId) {
                accessToken = getMtToken(request);
            } else if (ChannelTypeEnum.ELEM.getCode() == channelId) {
                // 饿了么软件应用无Token，ISV应用Token可在qnhPoi服务查
                log.info("CommonAppService.queryToken不支持饿了么渠道的token查询，request={}", request);
            } else if (ChannelTypeEnum.JD2HOME.getCode() == channelId) {
                accessToken = getJdToken(request);
            } else if (ChannelTypeEnum.YOU_ZAN.getCode() == channelId) {
                accessToken = getYzToken(request);
            } else if (ChannelTypeEnum.DOU_YIN.getCode() == channelId) {
                accessToken = getDyToken(request);
            } else {
                log.info("CommonAppService.queryToken 该渠道不支持此操作，request={}", request);
            }
        }catch (Exception e){
            log.info("CommonAppService.queryToken 查询token失败，request={}, exception={}", request, e);
        }

        tokenDTO.setAccessToken(accessToken);
        return tokenDTO;
    }
    
    private String getDyToken(QueryTokenRequest request){
        AppInfoDTO appInfoDTO = douyinChannelCommonService.queryAppInfoByTenant(request.getTenantId(), request.getChannelId());
        
        return appInfoDTO.getAccessToken();
    }


    private String getYzToken(QueryTokenRequest request){
        // 有赞Token，查询失效时需要刷新

        CopAccessConfigDO tenantChannelConfig = copAccessConfigService.findTenantChannelConfig(request.getTenantId(), request.getChannelId());
        if (tenantChannelConfig == null) {
            throw new BizException(ResultCode.CHANNEL_APP_ID_INVALID.getCode(), "租户未绑定");
        }
        Map<String, Object> sysParam = JacksonUtils.parseMap(tenantChannelConfig.getSysParams(), String.class, Object.class);



        AppMessage appMessage = AppMessage.builder()
                .clientId(tenantChannelConfig.getTenantAppId())
                .clientSecret((String) sysParam.get(ProjectConstant.SECRET))
                .grantId(request.getChannelPoiCode())
                .build();

        String accessToken = yzAccessTokenService.getAccessToken(appMessage);

        return accessToken;
    }

    private String getJdToken(QueryTokenRequest request){

        String sysParamJson = copAccessConfigService.selectAppSysParams(request.getTenantId(),
                request.getChannelId(), request.getQnhAppId());
        if (StringUtils.isBlank(sysParamJson)){
            log.info("CommonAppService.getJdToken 查询渠道系统参数为空，request={}", request);
            return null;
        }

        Map<String, Object> sysParam = JacksonUtils.parseMap(sysParamJson, String.class, Object.class);

        String accessToken = String.valueOf(sysParam.get("token"));
        return accessToken;
    }


    /**
     * 获取美团Token
     * @param request
     * @return
     */
    private String getMtToken(QueryTokenRequest request){
        // 美团渠道获取Token，Token不存在要授权，Token失效需要刷新
        // 快捷授权需要的系统参数：app_id、app_poi_code、response_type、sig，详见https://tscc.meituan.com/home/<USER>/market/10210
        // Token刷新需要的系统参数：app_id、grant_type、refresh_token、sig

        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setTenantId(request.getTenantId());
        baseRequest.setChannelId(request.getChannelId());
        baseRequest.setAppId(request.getQnhAppId());

        String sysParamJson = copAccessConfigService.selectAppSysParams(request.getTenantId(), request.getChannelId(), request.getQnhAppId());
        Map<String, Object> sysParam = JacksonUtils.parseMap(sysParamJson, String.class, Object.class);


        MtTokenMessage mtTokenMessage = mtBrandChannelGateService.getMtIsvAccessToken(request.getTenantId(), request.getChannelPoiCode(), baseRequest, sysParam);


        return mtTokenMessage.getAccessToken();
    }

    /**
     * 查询appSecret
     * @param request
     * @return
     */
    public AppSecretDTO queryAppSecret(AppSecretRequest request) {
        AppSecretDTO data = new AppSecretDTO();

        String sysParamsJson = copAccessConfigService.selectSysParams(request.getAppKey(), request.getChannelId());
        if (StringUtils.isEmpty(sysParamsJson)) {
            log.warn("查询系统参数不存在，appKey={}, channelId={}", request.getAppKey(), request.getChannelId());
            return data;
        }
        Map<String, Object> sysParam = JacksonUtils.parseMap(sysParamsJson, String.class, Object.class);
        String secret = (String) sysParam.get(ProjectConstant.SECRET);
        data.setSecret(secret);

        return data;
    }
}
