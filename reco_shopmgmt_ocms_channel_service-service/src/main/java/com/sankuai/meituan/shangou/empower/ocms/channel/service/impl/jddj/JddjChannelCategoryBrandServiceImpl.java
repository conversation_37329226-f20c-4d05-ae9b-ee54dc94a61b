package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.CopAccessConfigMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.RecommendCategoryBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.RecommendCategoryBrandResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCategoryBrandService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;

/**
 * <AUTHOR>
 * @description 京东类目品牌实现服务
 * @since 2023/12/19 16:03
 */
@Service("jddjChannelCategoryBrandService")
public class JddjChannelCategoryBrandServiceImpl implements ChannelCategoryBrandService {
    @Resource
    private JddjChannelGateService jddjChannelGateService;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;
    @Resource
    private CommonLogger log;

    @Value("${jddj.url.base}" + "${jddj.url.recommendBrandAndCate}")
    private String recommendBrandUrl;

    @Autowired
    private CopAccessConfigMapper copAccessConfigMapper;

    @Override
    public RecommendCategoryBrandResponse getRecommendCategoryBrand(RecommendCategoryBrandRequest req) {
        RecommendCategoryBrandResponse recommendCategoryBrandResponse = new RecommendCategoryBrandResponse();

        List<String> fields = new ArrayList<>();
        fields.add(ProjectConstant.JDDJ_FIELD_BRAND);
        fields.add(ProjectConstant.JDDJ_FIELD_CATEGORY);

        Map<String, Object> param = new HashMap<>();
        param.put(ProjectConstant.PRODUCT_NAME, req.getSpuName());
        param.put(ProjectConstant.FIELDS, fields);

        BaseRequest baseRequest = new BaseRequest().setChannelId(req.getChannelId()).setTenantId(req.getTenantId());
        if (baseRequest.getAppId() <= 0) {
            List<Integer> appIdList = copAccessConfigMapper.queryChannelAppIds(req.getTenantId(), req.getChannelId());
            if (CollectionUtils.isEmpty(appIdList)) {
                recommendCategoryBrandResponse.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), "租户未配置该渠道应用"));
                return recommendCategoryBrandResponse;
            }
            baseRequest.setAppId(appIdList.get(0).longValue());
        }
        Map<String, Object> sysParams = jddjChannelGateService.getChannelSysParams(req.getTenantId(), req.getChannelId());
        String appId = String.valueOf(sysParams.get(ProjectConstant.JDDJ_APP_KEY));

        // rhino限频检查
        if (StringUtils.isNotBlank(appId) &&
                !clusterRateLimiter.tryAcquire(ChannelPostJDDJEnum.RECOMMEND_CATE_BRAND, String.valueOf(req.getTenantId()))) {
            log.error("按名称查询推荐品牌与类目，获取令牌失败，request {}", param);
            recommendCategoryBrandResponse.setStatus(ChannelStatus.build(ResultCode.TRIGGER_LIMIT.getCode(), ResultCode.TRIGGER_LIMIT.getMsg()));
            return recommendCategoryBrandResponse;
        }

        Map<String, Object> resultMap = jddjChannelGateService.sendPostApp(recommendBrandUrl, null, baseRequest, param);
        if (resultMap == null || resultMap.get(ProjectConstant.CODE) == null) {
            log.error("查询推荐品牌类目出现通讯异常, req:{}", JacksonUtils.toJson(req));

            recommendCategoryBrandResponse.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), "查询推荐品牌类目通讯异常"));
            return recommendCategoryBrandResponse;
        }

        if (Integer.parseInt((String) resultMap.get(ProjectConstant.CODE)) != 0) {
            String channelMsg = (String) resultMap.get(ProjectConstant.MSG);
            log.error("查询推荐品牌类目失败, req:{}, channelMsg:{}", JacksonUtils.toJson(req), channelMsg);

            recommendCategoryBrandResponse.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), channelMsg));
            return recommendCategoryBrandResponse;
        }

        if (resultMap.get(ProjectConstant.DATA) == null) {
            recommendCategoryBrandResponse.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), "查询推荐品牌类目结果为空"));
            return recommendCategoryBrandResponse;
        }

        String dataStr = (String) resultMap.get(ProjectConstant.DATA);

        if (StringUtils.isEmpty(dataStr)) {
            recommendCategoryBrandResponse.setStatus(ChannelStatus.build(ResultCode.FAIL.getCode(), "查询推荐品牌类目结果为空"));
            return recommendCategoryBrandResponse;
        }

        JSONObject dataJson = JSON.parseObject(dataStr).getJSONObject(ProjectConstant.RESULT);

        String brandId = dataJson.getString(ProjectConstant.JDDJ_FIELD_BRANDID);
        Long categoryId = dataJson.getLong(ProjectConstant.JDDJ_FIELD_CATEGORYID);

        recommendCategoryBrandResponse.setCategoryCode(categoryId);
        recommendCategoryBrandResponse.setBrandId(brandId);
        recommendCategoryBrandResponse.setStatus(ChannelStatus.buildSuccess());
        return recommendCategoryBrandResponse;
    }
}
