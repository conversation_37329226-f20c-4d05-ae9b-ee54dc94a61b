package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.Lion;
import com.doudian.open.api.address_list.data.AddressListItem;
import com.doudian.open.api.afterSale_ApplyMarketAfterSale.AfterSaleApplyMarketAfterSaleRequest;
import com.doudian.open.api.afterSale_ApplyMarketAfterSale.AfterSaleApplyMarketAfterSaleResponse;
import com.doudian.open.api.afterSale_ApplyMarketAfterSale.param.AfterSaleApplyMarketAfterSaleParam;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailRequest;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailResponse;
import com.doudian.open.api.afterSale_Detail.data.AfterSaleInfo;
import com.doudian.open.api.afterSale_Detail.data.OrderInfo;
import com.doudian.open.api.afterSale_Detail.data.ProcessInfo;
import com.doudian.open.api.afterSale_Detail.param.AfterSaleDetailParam;
import com.doudian.open.api.afterSale_List.AfterSaleListRequest;
import com.doudian.open.api.afterSale_List.AfterSaleListResponse;
import com.doudian.open.api.afterSale_List.data.AfterSaleListData;
import com.doudian.open.api.afterSale_List.param.AfterSaleListParam;
import com.doudian.open.api.afterSale_fillLogistics.AfterSaleFillLogisticsRequest;
import com.doudian.open.api.afterSale_fillLogistics.param.AfterSaleFillLogisticsParam;
import com.doudian.open.api.afterSale_operate.AfterSaleOperateRequest;
import com.doudian.open.api.afterSale_operate.AfterSaleOperateResponse;
import com.doudian.open.api.afterSale_operate.param.AfterSaleOperateParam;
import com.doudian.open.api.afterSale_operate.param.EvidenceItem;
import com.doudian.open.api.afterSale_operate.param.ItemsItem;
import com.doudian.open.api.afterSale_operate.param.Logistics;
import com.doudian.open.api.afterSale_rejectReasonCodeList.AfterSaleRejectReasonCodeListRequest;
import com.doudian.open.api.afterSale_rejectReasonCodeList.AfterSaleRejectReasonCodeListResponse;
import com.doudian.open.api.afterSale_rejectReasonCodeList.data.AfterSaleRejectReasonCodeListData;
import com.doudian.open.api.afterSale_rejectReasonCodeList.param.AfterSaleRejectReasonCodeListParam;
import com.doudian.open.api.instantShopping_cancelDelivery.InstantShoppingCancelDeliveryRequest;
import com.doudian.open.api.instantShopping_cancelDelivery.InstantShoppingCancelDeliveryResponse;
import com.doudian.open.api.instantShopping_cancelDelivery.param.InstantShoppingCancelDeliveryParam;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.InstantShoppingNotifyDeliveryStatusRequest;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.InstantShoppingNotifyDeliveryStatusResponse;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.param.InstantShoppingNotifyDeliveryStatusParam;
import com.doudian.open.api.instantShopping_notifyPickingStatus.InstantShoppingNotifyPickingStatusRequest;
import com.doudian.open.api.instantShopping_notifyPickingStatus.InstantShoppingNotifyPickingStatusResponse;
import com.doudian.open.api.instantShopping_notifyPickingStatus.param.InstantShoppingNotifyPickingStatusParam;
import com.doudian.open.api.instantShopping_reportRiderLocation.InstantShoppingReportRiderLocationRequest;
import com.doudian.open.api.instantShopping_reportRiderLocation.InstantShoppingReportRiderLocationResponse;
import com.doudian.open.api.instantShopping_reportRiderLocation.param.InstantShoppingReportRiderLocationParam;
import com.doudian.open.api.order_logisticsAdd.OrderLogisticsAddRequest;
import com.doudian.open.api.order_logisticsAdd.OrderLogisticsAddResponse;
import com.doudian.open.api.order_logisticsAdd.param.OrderLogisticsAddParam;
import com.doudian.open.api.order_logisticsEdit.OrderLogisticsEditRequest;
import com.doudian.open.api.order_logisticsEdit.OrderLogisticsEditResponse;
import com.doudian.open.api.order_logisticsEdit.param.OrderLogisticsEditParam;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailRequest;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailResponse;
import com.doudian.open.api.order_orderDetail.data.*;
import com.doudian.open.api.order_orderDetail.param.OrderOrderDetailParam;
import com.doudian.open.api.order_searchList.OrderSearchListRequest;
import com.doudian.open.api.order_searchList.OrderSearchListResponse;
import com.doudian.open.api.order_searchList.data.OrderSearchListData;
import com.doudian.open.api.order_searchList.data.ShopOrderListItem;
import com.doudian.open.api.order_searchList.param.OrderSearchListParam;
import com.doudian.open.api.product_detail.ProductDetailRequest;
import com.doudian.open.api.product_detail.ProductDetailResponse;
import com.doudian.open.api.product_detail.data.DeliveryInfosItem;
import com.doudian.open.api.product_detail.data.ProductDetailData;
import com.doudian.open.api.product_detail.data.SpecPricesItem;
import com.doudian.open.api.product_detail.param.ProductDetailParam;
import com.doudian.open.api.superm_createVirtualMobile.SupermCreateVirtualMobileRequest;
import com.doudian.open.api.superm_createVirtualMobile.SupermCreateVirtualMobileResponse;
import com.doudian.open.api.superm_createVirtualMobile.data.MobileInfo;
import com.doudian.open.api.superm_createVirtualMobile.param.SupermCreateVirtualMobileParam;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.DoudianOpConfig;
import com.doudian.open.utils.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.enums.GiftTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.sankuai.meituan.shangou.empower.delivery.enums.DeliveryCancelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.PayTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DecryptReqDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DecryptRespDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.DouyinTenantStoreInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.dy.InstantCustomInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.DouyinConfigDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.douyin.MockDouYinPromotionInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.DouyinChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinOrderConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.OrderDegradeModuleEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ActivityPromotionSplitUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelOrderStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryStatus;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryOrderSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.StoreSubDeliveryConfigDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.StoreDeliveryConfigSimpleQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.DeliveryConfigSimpleQueryResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.json.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.joda.time.LocalDateTime;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType.DOU_YIN;
import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode.DY_ADD_LOGISTICS_ERROR;
import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode.DY_EDIT_LOGISTICS_ERROR;
import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode.DY_NOTIFY_DELIVERY_STATUS_ERROR;
import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode.DY_REPORT_RIDER_LOCATION_ERROR;
import static com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode.DY_SELF_DELIVERY_ERROR;
import static com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.PayTypeEnum.CASH_DELIVERY;
import static com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.PayTypeEnum.ONLINE;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM;

@Slf4j
@Service("dyChannelOrderService")
public class DouyinChannelOrderServiceImpl extends DouyinBaseService implements ChannelOrderService {

    /**
     * 商家自配送CODE
    */
    private static final String DISTRIBUTION_CODE_MERCHANT = "MERCHANT";

    /**
     * 骑手真实号手机号类型
     */
    private static final Integer REAL_RIDER_PHONE_TYPE_CODE = 0;

    /**
     * 牵牛花管理配送取消平台配送其他原因code
     */
    private static final Long CANCEL_DELIVERY_OTHER_REASON_CODE = 200L;
    /**
     * 非牵牛花管理配送取消平台配送其他原因code
     */
    private static final Long CANCEL_DELIVERY_REASON_CODE = 100L;
    /**
     * 商家营销活动信息
     */
    private static final String INSTANT_CUSTOMER_INFO = "instant_custom_info";

    @Resource
    private DouyinRetryChannelService douyinRetryChannelService;

    @Resource
    private DouyinChannelCommonService douyinChannelCommonService;

    @Resource
    private DeliveryConfigurationThriftService deliveryConfigurationThriftService;
    @Resource
    private TenantService tenantService;

    private static final String DOUYIN_REDPACK_ACTIVITY_TYPE = "hongbao";
    private static final String DOUYIN_REDPACK_ACTIVITY_NAME = "抖音红包优惠";

    @Override
    public ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request) {
        // douyin自动接单
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
        GetChannelOrderDetailResult orderDetailResult = new GetChannelOrderDetailResult();
        OrderOrderDetailRequest orderOrderDetailRequest = new OrderOrderDetailRequest();
        OrderOrderDetailParam param = orderOrderDetailRequest.getParam();
        String orderId = request.getOrderId();
        param.setShopOrderId(orderId);
        try {
            log.info("请求订单详情{}, req: {}", orderOrderDetailRequest.getUrlPath(), orderOrderDetailRequest);
            AccessToken accessToken = prepareRequestConfigAndToken(orderOrderDetailRequest, request.getTenantId());
            // 获取订单详情
            OrderOrderDetailResponse response = orderOrderDetailRequest.execute(accessToken);

            log.info("请求订单详情, resp: {}", response);
            if (response.isSuccess() && response.getData() != null) {
                OrderOrderDetailData detailData = response.getData();
                ShopOrderDetail shopOrderDetail = detailData.getShopOrderDetail();

                // mock支付优惠，以及运费优惠分摊平台，上线需要注释或者删除
                //mockDouYinPromotion(shopOrderDetail);

                List<SkuOrderListItem> skuOrderList = shopOrderDetail.getSkuOrderList();
                if (CollectionUtils.isEmpty(skuOrderList)) {
                    log.error("渠道订单{}商品数据为空", orderId);
                    return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单详情失败"));
                }
                // 获取渠道门店号，用来获取中台门店号
                StoreInfo storeInfo = skuOrderList.get(0).getStoreInfo();
                if (storeInfo == null) {
                    log.error("渠道订单{}商品所属门店为空", orderId);
                    return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单详情失败"));
                }
                DouyinTenantStoreInfo tenantStoreInfo = null;
                try {
                    // 获取中台门店信息
                    tenantStoreInfo = douyinChannelCommonService
                            .getTenantSysParamsByTenantChannelPoiCode(request.getTenantId(), storeInfo.getStoreId());
                } catch (Exception e) {
                    log.error("获取抖音门店: {} 对应的中台门店失败，tenantId: {}", storeInfo.getStoreId(), request.getTenantId(), e);
                }
                if (tenantStoreInfo == null) {
                    log.error("获取抖音门店: {} 对应的中台门店失败，tenantId: {}", storeInfo.getStoreId(), request.getTenantId());
                    return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取中台门店数据失败"));
                }
                // 解密信息，先用脱敏数据兜底
                DecryptRespDto decryptRespDto = new DecryptRespDto();

                MobileInfo mobileInfo = new MobileInfo();
                // 处理加密数据
                handleEncryptData(request, shopOrderDetail, orderId, decryptRespDto, mobileInfo);

                // 查询渠道商品数据，主要是为了查询商品重量
                List<ProductDetailData> productDetailList = Collections.emptyList();
                if (MccConfigUtil.isDYQueryProductDetailSwitch()) {
                    productDetailList = fetchProductDetailList(request.getTenantId(), skuOrderList);
                }
                // 查询中台门店配送配置，用来判断是平台配送还是自配送
                StoreSubDeliveryConfigDto storeSubDeliveryConfigDto = queryStoreDeliveryConfig(request.getTenantId(), tenantStoreInfo.getStoreId());
                // 查询【包装袋费用是否商家收取】的配置
                boolean isMerchantsChargePackageFee = tenantService.isMerchantsChargePackageFee(request.getTenantId(),
                        DynamicChannelType.DOU_YIN);
                // 通过前面准备好的前置数据，获取订单明细数据
                ChannelOrderDetailDTO channelOrderDetailDTO = convertFromShopOrderDetail(shopOrderDetail,
                        decryptRespDto, mobileInfo, productDetailList, tenantStoreInfo, storeSubDeliveryConfigDto,
                        isMerchantsChargePackageFee, request.getTenantId());
                orderDetailResult.setChannelOrderDetail(channelOrderDetailDTO);
                return orderDetailResult;
            }
        } catch (Exception e) {
            log.error("调用渠道获取订单{},租户id: {}, 门店号: {}详情失败: ", request.getOrderId(), request.getTenantId(),
                    request.getSotreId(), e);
        }

        return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单详情失败"));

    }

    private void mockDouYinPromotion(ShopOrderDetail shopOrderDetail){
        try {
            String mockDouYinPromotionInfoStr = MccConfigUtil.getMockDouYinPromotionInfo();
            log.info("抖音促销信息mock字符串:{}", mockDouYinPromotionInfoStr);
            MockDouYinPromotionInfo mockDouYinPromotionInfo = JSON.parseObject(mockDouYinPromotionInfoStr, MockDouYinPromotionInfo.class);
            if (mockDouYinPromotionInfo == null) {
                return;
            }

            // 平台优惠
            Integer officialDeductionAmount = Optional.ofNullable(mockDouYinPromotionInfo.getOfficialDeductionAmount()).orElse(-1);
            Integer allowanceAmount = Optional.ofNullable(mockDouYinPromotionInfo.getAllowanceAmount()).orElse(-1);
            Integer userBalanceAmount = Optional.ofNullable(mockDouYinPromotionInfo.getUserBalanceAmount()).orElse(-1);
            Integer goldCoinAmount = Optional.ofNullable(mockDouYinPromotionInfo.getGoldCoinAmount()).orElse(-1);
            if(officialDeductionAmount > -1){
                shopOrderDetail.getPromotionDetail().getPlatformDiscountDetail().setOfficialDeductionAmount(officialDeductionAmount.longValue());
                shopOrderDetail.setPlatformCostAmount(shopOrderDetail.getPlatformCostAmount() + officialDeductionAmount);
            }
            if(allowanceAmount > -1){
                shopOrderDetail.getPromotionDetail().getPlatformDiscountDetail().setAllowanceAmount(allowanceAmount.longValue());
                shopOrderDetail.setPlatformCostAmount(shopOrderDetail.getPlatformCostAmount() + allowanceAmount);
            }
            if(userBalanceAmount > -1){
                shopOrderDetail.getPromotionDetail().getPlatformDiscountDetail().setUserBalanceAmount(userBalanceAmount.longValue());
                shopOrderDetail.setPlatformCostAmount(shopOrderDetail.getPlatformCostAmount() + userBalanceAmount);
            }
            if(goldCoinAmount > -1){
                shopOrderDetail.getPromotionDetail().getPlatformDiscountDetail().setGoldCoinAmount(goldCoinAmount.longValue());
                shopOrderDetail.setPlatformCostAmount(shopOrderDetail.getPlatformCostAmount() + goldCoinAmount);
            }

            int hadShareOfficialDeductionAmount = 0;
            int hadShareAllowanceAmount = 0;
            int hadShareUserBalanceAmount = 0;
            int hadShareGoldCoinAmount = 0;
            List<SkuOrderListItem> filterGiftList = shopOrderDetail.getSkuOrderList().stream()
                    .filter(item -> !DouyinOrderConverterUtil.convertIsGift(item.getGivenProductType())).collect(Collectors.toList());
            int orderItemCount = filterGiftList.size();
            for (int i = 0; i < orderItemCount; i++) {
                SkuOrderListItem skuOrderListItem = filterGiftList.get(i);
                if (i == orderItemCount - 1) {
                    if(officialDeductionAmount > -1){
                        long remainOfficialDeductionAmount = officialDeductionAmount - hadShareOfficialDeductionAmount;
                        skuOrderListItem.getPromotionDetail().getPlatformDiscountDetail().setOfficialDeductionAmount(remainOfficialDeductionAmount);
                    }
                    if(allowanceAmount > -1){
                        long remainAllowanceAmount = allowanceAmount - hadShareAllowanceAmount;
                        skuOrderListItem.getPromotionDetail().getPlatformDiscountDetail().setAllowanceAmount(remainAllowanceAmount);
                    }
                    if(userBalanceAmount > -1){
                        long remainUserBalanceAmount = userBalanceAmount - hadShareUserBalanceAmount;
                        skuOrderListItem.getPromotionDetail().getPlatformDiscountDetail().setUserBalanceAmount(remainUserBalanceAmount);
                    }
                    if(goldCoinAmount > -1){
                        long remainGoldCoinAmount = goldCoinAmount - hadShareGoldCoinAmount;
                        skuOrderListItem.getPromotionDetail().getPlatformDiscountDetail().setGoldCoinAmount(remainGoldCoinAmount);
                    }
                } else {
                    if(officialDeductionAmount > -1){
                        long shareValue = officialDeductionAmount / orderItemCount;
                        skuOrderListItem.getPromotionDetail().getPlatformDiscountDetail().setOfficialDeductionAmount(shareValue);
                        hadShareOfficialDeductionAmount += shareValue;
                    }
                    if(allowanceAmount > -1){
                        long shareValue = allowanceAmount / orderItemCount;
                        skuOrderListItem.getPromotionDetail().getPlatformDiscountDetail().setAllowanceAmount(shareValue);
                        hadShareAllowanceAmount += shareValue;
                    }
                    if(userBalanceAmount > -1){
                        long shareValue = userBalanceAmount / orderItemCount;
                        skuOrderListItem.getPromotionDetail().getPlatformDiscountDetail().setUserBalanceAmount(shareValue);
                        hadShareUserBalanceAmount += shareValue;
                    }
                    if(goldCoinAmount > -1){
                        long shareValue = goldCoinAmount / orderItemCount;
                        skuOrderListItem.getPromotionDetail().getPlatformDiscountDetail().setGoldCoinAmount(shareValue);
                        hadShareGoldCoinAmount += shareValue;
                    }
                }
            }


            // 运费优惠
            if(Optional.ofNullable(mockDouYinPromotionInfo.getFreightDiscountPlat()).orElse(-1) > -1
                    || Optional.ofNullable(mockDouYinPromotionInfo.getFreightDiscountPoi()).orElse(-1) > -1){
                boolean hasFreightDiscount = false;
                if(CollectionUtils.isEmpty(shopOrderDetail.getExtraPromotionAmountDetail())){
                    shopOrderDetail.setExtraPromotionAmountDetail(new ArrayList<>());
                }
                for (ExtraPromotionAmountDetailItem extraPromotionAmountDetailItem : shopOrderDetail.getExtraPromotionAmountDetail()) {
                    if(DouyinCustomPromotionTypeEnum.FREIGHT_DISCOUNT.getCode().equals(extraPromotionAmountDetailItem.getPromotionType())){
                        hasFreightDiscount = true;
                        if(Optional.ofNullable(mockDouYinPromotionInfo.getFreightDiscountPlat()).orElse(-1) > -1){
                            extraPromotionAmountDetailItem.getShareCost().setPlatformCost(mockDouYinPromotionInfo.getFreightDiscountPlat().longValue());
                        }
                        if(Optional.ofNullable(mockDouYinPromotionInfo.getFreightDiscountPoi()).orElse(-1) > -1){
                            extraPromotionAmountDetailItem.getShareCost().setShopCost(mockDouYinPromotionInfo.getFreightDiscountPoi().longValue());
                        }
                        break;
                    }
                }
                if(!hasFreightDiscount) {
                    ExtraPromotionAmountDetailItem mockExtraPromotionAmountDetailItem = new ExtraPromotionAmountDetailItem(){{
                        setPromotionType(DouyinCustomPromotionTypeEnum.FREIGHT_DISCOUNT.getCode());
                        setShareCost(new ShareCost(){{
                            setAuthorCost(0L);
                            setPlatformCost(0L);
                            setShopCost(0L);
                        }});
                        setPromotionDesc(DouyinCustomPromotionTypeEnum.FREIGHT_DISCOUNT.getDesc());
                    }};
                    if(Optional.ofNullable(mockDouYinPromotionInfo.getFreightDiscountPlat()).orElse(-1) > -1){
                        mockExtraPromotionAmountDetailItem.getShareCost().setPlatformCost(mockDouYinPromotionInfo.getFreightDiscountPlat().longValue());
                    }
                    if(Optional.ofNullable(mockDouYinPromotionInfo.getFreightDiscountPoi()).orElse(-1) > -1){
                        mockExtraPromotionAmountDetailItem.getShareCost().setShopCost(mockDouYinPromotionInfo.getFreightDiscountPoi().longValue());
                    }
                    shopOrderDetail.getExtraPromotionAmountDetail().add(mockExtraPromotionAmountDetailItem);
                }
            }

            // 外卖运费优惠
            if(Optional.ofNullable(mockDouYinPromotionInfo.getFoodtakeoutShopFreightDiscountPlat()).orElse(-1) > -1
                    || Optional.ofNullable(mockDouYinPromotionInfo.getFoodtakeoutShopFreightDiscountPoi()).orElse(-1) > -1){
                boolean hasFoodTakeFreightDiscount = false;
                for (ExtraPromotionAmountDetailItem extraPromotionAmountDetailItem : shopOrderDetail.getExtraPromotionAmountDetail()) {
                    if(DouyinCustomPromotionTypeEnum.FOODTAKEOUT_SHOP_FREIGHT_DISOUNT.getCode().equals(extraPromotionAmountDetailItem.getPromotionType())){
                        hasFoodTakeFreightDiscount = true;
                        if(Optional.ofNullable(mockDouYinPromotionInfo.getFoodtakeoutShopFreightDiscountPlat()).orElse(-1) > -1){
                            extraPromotionAmountDetailItem.getShareCost().setPlatformCost(mockDouYinPromotionInfo.getFoodtakeoutShopFreightDiscountPlat().longValue());
                        }
                        if(Optional.ofNullable(mockDouYinPromotionInfo.getFoodtakeoutShopFreightDiscountPoi()).orElse(-1) > -1){
                            extraPromotionAmountDetailItem.getShareCost().setShopCost(mockDouYinPromotionInfo.getFoodtakeoutShopFreightDiscountPoi().longValue());
                        }
                        break;
                    }
                }
                if(!hasFoodTakeFreightDiscount) {
                    ExtraPromotionAmountDetailItem mockExtraPromotionAmountDetailItem = new ExtraPromotionAmountDetailItem(){{
                        setPromotionType(DouyinCustomPromotionTypeEnum.FOODTAKEOUT_SHOP_FREIGHT_DISOUNT.getCode());
                        setShareCost(new ShareCost(){{
                            setAuthorCost(0L);
                            setPlatformCost(0L);
                            setShopCost(0L);
                        }});
                        setPromotionDesc(DouyinCustomPromotionTypeEnum.FOODTAKEOUT_SHOP_FREIGHT_DISOUNT.getDesc());
                    }};
                    if(Optional.ofNullable(mockDouYinPromotionInfo.getFoodtakeoutShopFreightDiscountPlat()).orElse(-1) > -1){
                        mockExtraPromotionAmountDetailItem.getShareCost().setPlatformCost(mockDouYinPromotionInfo.getFoodtakeoutShopFreightDiscountPlat().longValue());
                    }
                    if(Optional.ofNullable(mockDouYinPromotionInfo.getFoodtakeoutShopFreightDiscountPoi()).orElse(-1) > -1){
                        mockExtraPromotionAmountDetailItem.getShareCost().setShopCost(mockDouYinPromotionInfo.getFoodtakeoutShopFreightDiscountPoi().longValue());
                    }
                    shopOrderDetail.getExtraPromotionAmountDetail().add(mockExtraPromotionAmountDetailItem);
                }
            }

            int shareMixChargePlat = -1;
            if(Optional.ofNullable(mockDouYinPromotionInfo.getMixedChargePlat()).orElse(-1) > -1){
                shareMixChargePlat = mockDouYinPromotionInfo.getMixedChargePlat() / orderItemCount;
            }
            int shareMixChargePoi = -1;
            if(Optional.ofNullable(mockDouYinPromotionInfo.getMixedChargePoi()).orElse(-1) > -1){
                shareMixChargePoi = mockDouYinPromotionInfo.getMixedChargePoi() / orderItemCount;
            }
            int shareGovernmentReducePlat = -1;
            if(Optional.ofNullable(mockDouYinPromotionInfo.getGovernmentReducePlat()).orElse(-1) > -1){
                shareGovernmentReducePlat = mockDouYinPromotionInfo.getGovernmentReducePlat() / orderItemCount;
            }
            int shareGovernmentReducePoi = -1;
            if(Optional.ofNullable(mockDouYinPromotionInfo.getGovernmentReducePoi()).orElse(-1) > -1){
                shareGovernmentReducePoi = mockDouYinPromotionInfo.getGovernmentReducePoi() / orderItemCount;
            }
            int shareOtherPromotionPayDiscountPlat = -1;
            if(Optional.ofNullable(mockDouYinPromotionInfo.getOtherPromotionPayDiscountPlat()).orElse(-1) > -1){
                shareOtherPromotionPayDiscountPlat = mockDouYinPromotionInfo.getOtherPromotionPayDiscountPlat() / orderItemCount;
            }
            int shareOtherPromotionPayDiscountPoi = -1;
            if(Optional.ofNullable(mockDouYinPromotionInfo.getOtherPromotionPayDiscountPoi()).orElse(-1) > -1){
                shareOtherPromotionPayDiscountPoi = mockDouYinPromotionInfo.getOtherPromotionPayDiscountPoi() / orderItemCount;
            }
            int count = 1;
            for (SkuOrderListItem skuOrderListItem : filterGiftList) {
                if(shareMixChargePlat > -1 || shareMixChargePoi > -1){
                    PromotionPayAmountDetailsItem mockPromotionPayAmountDetailsItem = new PromotionPayAmountDetailsItem(){{
                        setPayPromotionType(DouyinCustomPromotionTypeEnum.MIXED_CHARGE.getCode());
                        setShareCost(new ShareCost_5_5(){{
                            setPlatformCost(0L);
                            setShopCost(0L);
                        }});
                    }};

                    if(shareMixChargePlat > -1) {
                        if(count == orderItemCount){
                            mockPromotionPayAmountDetailsItem.getShareCost().setPlatformCost(mockDouYinPromotionInfo.getMixedChargePlat().longValue());
                        } else {
                            mockPromotionPayAmountDetailsItem.getShareCost().setPlatformCost((long) shareMixChargePlat);
                            mockDouYinPromotionInfo.setMixedChargePlat(mockDouYinPromotionInfo.getMixedChargePlat() - shareMixChargePlat);
                        }
                        shopOrderDetail.setPlatformCostAmount(shopOrderDetail.getPlatformCostAmount() + mockPromotionPayAmountDetailsItem.getShareCost().getPlatformCost());
                    }
                    if(shareMixChargePoi > -1) {
                        if(count == orderItemCount){
                            mockPromotionPayAmountDetailsItem.getShareCost().setShopCost(mockDouYinPromotionInfo.getMixedChargePoi().longValue());
                        } else {
                            mockPromotionPayAmountDetailsItem.getShareCost().setShopCost((long) shareMixChargePoi);
                            mockDouYinPromotionInfo.setMixedChargePoi(mockDouYinPromotionInfo.getMixedChargePoi() - shareMixChargePoi);
                        }
                    }
                    skuOrderListItem.getPromotionPayAmountDetails().add(mockPromotionPayAmountDetailsItem);
                }
                if(shareGovernmentReducePlat > -1 || shareGovernmentReducePoi > -1){
                    PromotionPayAmountDetailsItem mockPromotionPayAmountDetailsItem = new PromotionPayAmountDetailsItem(){{
                        setPayPromotionType(DouyinCustomPromotionTypeEnum.GOVERNMENT_REDUCE.getCode());
                        setShareCost(new ShareCost_5_5(){{
                            setPlatformCost(0L);
                            setShopCost(0L);
                        }});
                    }};

                    if(shareGovernmentReducePlat > -1) {
                        if(count == orderItemCount){
                            mockPromotionPayAmountDetailsItem.getShareCost().setPlatformCost(mockDouYinPromotionInfo.getGovernmentReducePlat().longValue());
                        } else {
                            mockPromotionPayAmountDetailsItem.getShareCost().setPlatformCost((long) shareGovernmentReducePlat);
                            mockDouYinPromotionInfo.setGovernmentReducePlat(mockDouYinPromotionInfo.getGovernmentReducePlat() - shareGovernmentReducePlat);
                        }
                        shopOrderDetail.setPlatformCostAmount(shopOrderDetail.getPlatformCostAmount() + mockPromotionPayAmountDetailsItem.getShareCost().getPlatformCost());
                    }
                    if(shareGovernmentReducePoi > -1) {
                        if(count == orderItemCount){
                            mockPromotionPayAmountDetailsItem.getShareCost().setShopCost(mockDouYinPromotionInfo.getGovernmentReducePoi().longValue());
                        } else {
                            mockPromotionPayAmountDetailsItem.getShareCost().setShopCost((long) shareGovernmentReducePoi);
                            mockDouYinPromotionInfo.setGovernmentReducePoi(mockDouYinPromotionInfo.getGovernmentReducePoi() - shareGovernmentReducePoi);
                        }
                    }
                    skuOrderListItem.getPromotionPayAmountDetails().add(mockPromotionPayAmountDetailsItem);
                }
                if(shareOtherPromotionPayDiscountPlat > -1 || shareOtherPromotionPayDiscountPoi > -1){
                    PromotionPayAmountDetailsItem mockPromotionPayAmountDetailsItem = new PromotionPayAmountDetailsItem(){{
                        setPayPromotionType("other_pay_promotion_type");
                        setShareCost(new ShareCost_5_5(){{
                            setPlatformCost(0L);
                            setShopCost(0L);
                        }});
                    }};

                    if(shareOtherPromotionPayDiscountPlat > -1) {
                        if(count == orderItemCount){
                            mockPromotionPayAmountDetailsItem.getShareCost().setPlatformCost(mockDouYinPromotionInfo.getOtherPromotionPayDiscountPlat().longValue());
                        } else {
                            mockPromotionPayAmountDetailsItem.getShareCost().setPlatformCost((long) shareOtherPromotionPayDiscountPlat);
                            mockDouYinPromotionInfo.setOtherPromotionPayDiscountPlat(mockDouYinPromotionInfo.getOtherPromotionPayDiscountPlat() - shareOtherPromotionPayDiscountPlat);
                        }
                        shopOrderDetail.setPlatformCostAmount(shopOrderDetail.getPlatformCostAmount() + mockPromotionPayAmountDetailsItem.getShareCost().getPlatformCost());
                    }
                    if(shareOtherPromotionPayDiscountPoi > -1) {
                        if(count == orderItemCount){
                            mockPromotionPayAmountDetailsItem.getShareCost().setShopCost(mockDouYinPromotionInfo.getOtherPromotionPayDiscountPoi().longValue());
                        } else {
                            mockPromotionPayAmountDetailsItem.getShareCost().setShopCost((long) shareOtherPromotionPayDiscountPoi);
                            mockDouYinPromotionInfo.setOtherPromotionPayDiscountPoi(mockDouYinPromotionInfo.getOtherPromotionPayDiscountPoi() - shareOtherPromotionPayDiscountPoi);
                        }
                    }
                    skuOrderListItem.getPromotionPayAmountDetails().add(mockPromotionPayAmountDetailsItem);
                }
                count++;
            }
            log.info("抖音促销信息mock成功后返回值:{}", JSON.toJSONString(shopOrderDetail));
        } catch (Exception e) {
            log.error("抖音促销信息mock失败 ", e);
        }

    }

    private void handleEncryptData(GetChannelOrderDetailRequest request, ShopOrderDetail shopOrderDetail,
            String orderId, DecryptRespDto decryptRespDto, MobileInfo mobileInfo) {

        // 需要跳过解密的 或者订单已经完成或者订单已经取消，不进行解密操作
        if (request.isSkipDecrypt() || isOrderFinishedOrCanceled(shopOrderDetail)) {
            return ;
        }

        DecryptReqDto decryptReqDto = buildDecryptReqDto(shopOrderDetail);
        int timeout = Lion.getConfigRepository().getIntValue("config.douyin.decryptForOrderDetailHttpReadTimeoutMillSec",
                1500);

        DecryptRespDto decryptResp = douyinRetryChannelService.decryptWithoutRetry(request.getTenantId(), orderId,
                decryptReqDto, timeout);

        decryptRespDto.copyFrom(decryptResp);

        // 解密后如果已经是虚拟号（隐私小号）了，则不去获取隐私小号了；仅返回的是明文，才去获取隐私小号
        if (BooleanUtils.isNotTrue(decryptRespDto.getIsVirtualTel()) && !Lion.getConfigRepository()
                .getBooleanValue("switch.douyin.disableFetchVirtualMobile", false)) {
            // 隐私小号非必需，没有的话用明文兜底 TODO 获取隐私小号需要满足平台配送
            MobileInfo mobileInfo1 = fetchVirtualMobile(request.getTenantId(), orderId);
            if (mobileInfo1 != null) {
                mobileInfo.setMobileVirtual(mobileInfo1.getMobileVirtual());
                mobileInfo.setExpireTime(mobileInfo1.getExpireTime());
                mobileInfo.setIsNewCreate(mobileInfo1.getIsNewCreate());
            }
        }

    }

    private static DecryptReqDto buildDecryptReqDto(ShopOrderDetail shopOrderDetail) {
        DecryptReqDto decryptReqDto = new DecryptReqDto();
        decryptReqDto.setName(shopOrderDetail.getEncryptPostReceiver());
        decryptReqDto.setAddress(shopOrderDetail.getPostAddr().getEncryptDetail());
        decryptReqDto.setMobile(shopOrderDetail.getEncryptPostTel());
        return decryptReqDto;
    }

    @Override
    public QueryAfsOrderListResult queryAfsOrderList(QueryChannelAfsOrderListRequest queryChannelAfsOrderListRequest) {
        QueryAfsOrderListResult result = new QueryAfsOrderListResult();
        AfterSaleListRequest request = new AfterSaleListRequest();
        AfterSaleListParam param = request.getParam();
        AccessToken accessToken = prepareRequestConfigAndToken(request, queryChannelAfsOrderListRequest.getTenantId());
        param.setLogisticsStatus(1L);
        param.setPayType(1L);
        param.setStartTime(queryChannelAfsOrderListRequest.getStartTime());
        param.setEndTime(queryChannelAfsOrderListRequest.getEndTime());
        param.setPage((long) (queryChannelAfsOrderListRequest.getPage() - 1));
        param.setSize((long) queryChannelAfsOrderListRequest.getPageSize());
        AfterSaleListResponse response = request.execute(accessToken);
        AfterSaleListData data = response.getData();
        List<AfsOrderInfo> afsOrderInfoList = data.getItems().stream().map(item -> {
            AfsOrderInfo info = new AfsOrderInfo();
            info.setChannelAfsOrderId(item.getAftersaleInfo().getAftersaleId());
            info.setChannelOrderId(item.getOrderInfo().getShopOrderId());
            return info;
        }).collect(Collectors.toList());
        return result.setStatus(ResultGenerator.genSuccessResult()).setChannelAfsOrderIdList(afsOrderInfoList);
    }

    @Nullable
    private StoreSubDeliveryConfigDto queryStoreDeliveryConfig(long tenantId, Long storeId) {
        StoreDeliveryConfigSimpleQueryRequest request = new StoreDeliveryConfigSimpleQueryRequest();
        request.setTenantId(tenantId);
        request.setStoreIdList(Collections.singletonList(storeId));
        try {
            DeliveryConfigSimpleQueryResponse response = deliveryConfigurationThriftService.simpleQueryDeliveryConfig(request);
            log.info("查询到门店配送方式: {}", JsonUtil.toJson(response));
            List<StoreSubDeliveryConfigDto> storeChannelDeliveryConfigs = response.getBatchStoreDeliveryConfigDtoList().get(0).getStoreChannelDeliveryConfigList();
            Optional<StoreSubDeliveryConfigDto> storeSubDeliveryConfigDto = storeChannelDeliveryConfigs.stream()
                    .filter(config -> (int) config.getChannelType() == DOU_YIN.getChannelId())
                    .findAny();
            return storeSubDeliveryConfigDto.orElse(null);
        } catch (Exception e) {
            log.error("查询门店配送方式失败，租户id: {}, 中台门店id: {}", tenantId, storeId, e);
        }
        return null;
    }

    /**
     * 从渠道获取商品详情列表，订单这里主要是为了查重量
     * @param tenantId
     * @param skuOrderList
     * @return
     */
    private List<ProductDetailData> fetchProductDetailList(long tenantId, List<SkuOrderListItem> skuOrderList) {
        if (CollectionUtils.isEmpty(skuOrderList)) {
            return Collections.emptyList();
        }
        return skuOrderList.stream()
                .map(sku -> fetchProductDetail(tenantId, sku))
                .collect(Collectors.toList());
    }

    private ProductDetailData fetchProductDetail(long tenantId, SkuOrderListItem skuItem) {
        ProductDetailRequest request = new ProductDetailRequest();
        ProductDetailParam param = request.getParam();
        param.setProductId(skuItem.getProductIdStr());
        param.setOutProductId(skuItem.getOutProductId());
        log.info("调用渠道接口{}获取商品信息， request：{}", request.getUrlPath(), param);
        try {
            // param.setStoreId(Long.parseLong(skuItem.getStoreInfo().getStoreId()));
            AccessToken token = prepareRequestConfigAndToken(request, tenantId);
            if (MccConfigUtil.isDYQueryProductDetailSwitch()) {
                ProductDetailResponse response = request.execute(token);

                log.info("调用渠道接口{}获取商品信息， response：{}", request.getUrlPath(), response);
                if (response.isSuccess()) {
                    return response.getData();
                }
            }
        } catch (Exception e) {
            log.error("获取商品明细失败, tenantId: {}, param: {}", tenantId, param, e);
        }
        return null;
    }

    private ChannelOrderDetailDTO convertFromShopOrderDetail(ShopOrderDetail shopOrderDetail,
            DecryptRespDto decryptRespDto, MobileInfo mobileInfo, List<ProductDetailData> productDetailList,
            DouyinTenantStoreInfo tenantStoreInfo, StoreSubDeliveryConfigDto storeSubDeliveryConfigDto,
            boolean isMerchantsChargePackageFee, Long tenantId) {
        ChannelOrderDetailDTO channelOrderDetailDTO = new ChannelOrderDetailDTO();
        // 默认为true，当没没查到配送方式的时候，认为是平台配送
        boolean isPlatformDelivery = true;
        DeliveryPlatformEnum deliveryPlatformEnum = null;
        if (storeSubDeliveryConfigDto != null) {
            // 牵牛花未管理配送 || (牵牛花管理配送 && 配送方式为渠道配送)
            if (storeSubDeliveryConfigDto.getOpenFlag() == 0) {
                deliveryPlatformEnum = DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM;
                isPlatformDelivery = true;
            } else {
                Integer deliveryCode = storeSubDeliveryConfigDto.getDeliveryPlatformCode();
                deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryCode);
                isPlatformDelivery = ORDER_CHANNEL_DELIVERY_PLATFORM == deliveryPlatformEnum;
            }
        }

        /**
         * 填充订单商品数据,
         * 顺带计算单头的(商家,平台) * (单品,整单,总优惠)，
         * 单品优惠数据，整单优惠数据，单品活动优惠数据
         */
        fillProductDetailAndPromotion(channelOrderDetailDTO, shopOrderDetail.getSkuOrderList(), productDetailList);

        /**
         * 收件人信息
         */
        fillDeliveryDetail(channelOrderDetailDTO, shopOrderDetail, decryptRespDto, mobileInfo, deliveryPlatformEnum);

        /**
         * 活动优惠数据，主要包含整单的，单品的在fillProductDetailAndPromotion完成
         */
        fillActivities(channelOrderDetailDTO, shopOrderDetail);

        /**
         * 订单单头数据
         */
        channelOrderDetailDTO.setChannelOrderId(shopOrderDetail.getOrderId());
        if (tenantStoreInfo != null) {
            channelOrderDetailDTO.setStoreId(tenantStoreInfo.getStoreId());
        }
        String orderSerialNo = shopOrderDetail.getSupermarketOrderSerialNo();
        channelOrderDetailDTO.setOrderSerialNumberStr(orderSerialNo);
        try {
            if (NumberUtils.isDigits(orderSerialNo)) {
                channelOrderDetailDTO.setOrderSerialNumber(Long.parseLong(orderSerialNo));
            } else {
                String orderSerialNoReplaced = orderSerialNo.replace("-", "");
                if (NumberUtils.isDigits(orderSerialNoReplaced)) {
                    channelOrderDetailDTO.setOrderSerialNumber(Long.parseLong(orderSerialNoReplaced));
                } else {
                    log.warn("订单{}的流水号格式有误: {}", shopOrderDetail.getOrderId(), orderSerialNo);
                }
            }
        } catch (Exception e) {
            log.warn("订单{}的流水号格式有误: {}", shopOrderDetail.getOrderId(), orderSerialNo, e);
        }
        channelOrderDetailDTO.setChannelOrderStatus(shopOrderDetail.getOrderStatus().intValue());

        channelOrderDetailDTO.setStatus(parseChannelStatusToChannelOrderStatus(shopOrderDetail.getOrderStatus()));
        channelOrderDetailDTO.setBaichuanStatus(parseChannelStatusToOrderStatusEnum(shopOrderDetail.getOrderStatus()));
        // 立即送达为立即单，非立即送达为预约单
        channelOrderDetailDTO.setIsBooking(!shopOrderDetail.getEarlyArrival());
        List<ShopOrderTagUiItem> shopOrderTagUi = shopOrderDetail.getShopOrderTagUi();
        String comment = "";
        if (CollectionUtils.isNotEmpty(shopOrderTagUi)) {
            List<String> tags = shopOrderTagUi.stream()
                    .map(ShopOrderTagUiItem::getText)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            comment = String.join(",", tags);
        }
        if (StringUtils.isNotBlank(comment)) {
            if (StringUtils.isNotBlank(shopOrderDetail.getBuyerWords())) {
                channelOrderDetailDTO.setComment(String.join(",", comment, shopOrderDetail.getBuyerWords()));
            } else {
                channelOrderDetailDTO.setComment(comment);
            }
        } else {
            channelOrderDetailDTO.setComment(shopOrderDetail.getBuyerWords());
        }
        channelOrderDetailDTO.setChannelId(DOU_YIN.getChannelId());
        PayTypeEnum payTypeEnum = PayTypeEnum.getType(shopOrderDetail.getPayType());
        channelOrderDetailDTO.setChannelPayMode(payTypeEnum.name());
        channelOrderDetailDTO
                .setPayType(shopOrderDetail.getPayType() == 0 ? CASH_DELIVERY.getValue() : ONLINE.getValue());
        // 各种时间
        channelOrderDetailDTO.setCreateTime(shopOrderDetail.getCreateTime() * 1000);
        channelOrderDetailDTO.setPayTime(shopOrderDetail.getPayTime() * 1000);
        channelOrderDetailDTO.setCompletedTime(shopOrderDetail.getFinishTime() * 1000);
        channelOrderDetailDTO.setConfirmTime(System.currentTimeMillis());
        //渠道没有取消时间，用最后更新时间代替
        if(shopOrderDetail.getOrderStatus() == 4){
            channelOrderDetailDTO.setCancelTime(shopOrderDetail.getUpdateTime() * 1000);
        }

        // 各种金额
        // 打包费金额
        channelOrderDetailDTO.setPackageAmt(shopOrderDetail.getPackingAmount().intValue());
        channelOrderDetailDTO.setPayPackageAmt(channelOrderDetailDTO.getPackageAmt());
        // 抖音打包费根据M端【渠道设置】中【包装袋费用是否商家收取】配置来收取包装费
        if (isMerchantsChargePackageFee) {
            channelOrderDetailDTO.setPlatPackageAmt(0);
            // 商家包装费收入=顾客实付包装费+包装费平台优惠
            channelOrderDetailDTO.setPoiPackageAmt(
                    channelOrderDetailDTO.getPayPackageAmt() + channelOrderDetailDTO.getPlatPackagePromotion());
        } else {
            // 平台包装费收入=顾客实付包装费+包装费商家优惠
            channelOrderDetailDTO.setPlatPackageAmt(
                    channelOrderDetailDTO.getPayPackageAmt() + channelOrderDetailDTO.getPoiPackagePromotion());
            channelOrderDetailDTO.setPoiPackageAmt(0);
        }
        // 配送费
        fillLogistics(channelOrderDetailDTO, shopOrderDetail, isPlatformDelivery);

        // 原始金额
        channelOrderDetailDTO.setOriginalAmt(shopOrderDetail.getOrderAmount().intValue());
        // 实付金额
        channelOrderDetailDTO.setActualPayAmt(shopOrderDetail.getPayAmount().intValue());
        // 商家实收金额 = 实付金额 + 平台优惠 + 平台运费优惠 + 平台包装费优惠(暂未接入) - 平台收配送费（仅平台配送才减） - 平台收包装费 - 佣金(暂未接入)
        int poiIncome = channelOrderDetailDTO.getActualPayAmt()
                + shopOrderDetail.getPlatformCostAmount().intValue()
                + channelOrderDetailDTO.getPlatLogisticsPromotion()
                - channelOrderDetailDTO.getPlatPackageAmt();
        if (isPlatformDelivery) {
            poiIncome -= (channelOrderDetailDTO.getFreight()
                    - channelOrderDetailDTO.getPlatLogisticsPromotion()
                    - channelOrderDetailDTO.getPoiLogisticsPromotion());
        }
        channelOrderDetailDTO.setBizReceiveAmt(poiIncome);

        // 活动分摊信息
        fillActivityShareInfo(channelOrderDetailDTO, shopOrderDetail, tenantId);
        //渠道用户Id
        if(StringUtils.isNotBlank(shopOrderDetail.getDoudianOpenId())){
            channelOrderDetailDTO.setChannelUserId(shopOrderDetail.getDoudianOpenId());
        }
        return channelOrderDetailDTO;
    }

    private void fillLogistics(ChannelOrderDetailDTO channelOrderDetailDTO, ShopOrderDetail shopOrderDetail, boolean isPlatformDelivery){
        channelOrderDetailDTO.setFreight(Optional.ofNullable(shopOrderDetail.getPostOriginAmount()).orElse(0L).intValue());
        int poiLogisticsPromotion = 0;
        int platLogisticsPromotion = 0;
        if(CollectionUtils.isNotEmpty(shopOrderDetail.getExtraPromotionAmountDetail())) {
            for (ExtraPromotionAmountDetailItem extraPromotionAmountDetailItem : shopOrderDetail.getExtraPromotionAmountDetail()) {
                if (DouyinCustomPromotionTypeEnum.isLogisticsPromotion(extraPromotionAmountDetailItem.getPromotionType())) {
                    ShareCost shareCost = extraPromotionAmountDetailItem.getShareCost();
                    poiLogisticsPromotion += Optional.ofNullable(shareCost).map(ShareCost::getShopCost).orElse(0L).intValue();
                    platLogisticsPromotion += (Optional.ofNullable(shareCost).map(ShareCost::getPlatformCost).orElse(0L).intValue()
                            + Optional.ofNullable(shareCost).map(ShareCost::getAuthorCost).orElse(0L).intValue());
                }
            }
        }

        // 商家运费优惠、平台运费优惠
        channelOrderDetailDTO.setPoiLogisticsPromotion(poiLogisticsPromotion);
        channelOrderDetailDTO.setPlatLogisticsPromotion(platLogisticsPromotion);

        // 平台配送
        if (isPlatformDelivery) {
            channelOrderDetailDTO.setPoiLogisticsIncome(-channelOrderDetailDTO.getPoiLogisticsPromotion());
        } else {
            channelOrderDetailDTO.setPoiLogisticsIncome(channelOrderDetailDTO.getFreight() - channelOrderDetailDTO.getPoiLogisticsPromotion());
        }


        // 商家运费小费、用户运费小费都为0
        channelOrderDetailDTO.setPoiLogisticsTips(0);
        channelOrderDetailDTO.setCustomerLogisticsTips(0);
        channelOrderDetailDTO.setSelfPickServiceFee(0);
    }

    private void fillItemPromotionActivities(ChannelOrderDetailDTO channelOrderDetailDTO, OrderProductDetailDTO orderProductDetailDTO, CampaignInfoItem campaignInfo) {
        List<OrderDiscountDetailDTO> orderDiscountDetailDTOS = channelOrderDetailDTO.getActivities();
        if (orderDiscountDetailDTOS == null) {
            orderDiscountDetailDTOS = new ArrayList<>();
            channelOrderDetailDTO.setActivities(orderDiscountDetailDTOS);
        }

        OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
        orderDiscount.setRemark(campaignInfo.getCampaignName());
        ShareDiscountCost shareDiscountCost = campaignInfo.getShareDiscountCost();
        orderDiscount.setChannelCharge(shareDiscountCost.getPlatformCost());
        orderDiscount.setBizCharge(shareDiscountCost.getShopCost());
        orderDiscount.setActDiscount(campaignInfo.getCampaignAmount());
        orderDiscount.setType(String.valueOf(campaignInfo.getCampaignType()));
        orderDiscount.setActivityId(parseActivityId(campaignInfo.getExtraMap()));
        orderDiscount.setLogisticsCharge(0);
        orderDiscount.setAgentCharge(0);
        // todo 是否还有别的要添加呢

        orderDiscountDetailDTOS.add(orderDiscount);

    }

    private void fillActivities(ChannelOrderDetailDTO channelOrderDetailDTO, ShopOrderDetail shopOrderDetail) {
        // 整单优惠，单头级别数据
        PromotionDetail promotionDetail = shopOrderDetail.getPromotionDetail();
        ShopDiscountDetail shopDiscountDetail = promotionDetail.getShopDiscountDetail();
        PlatformDiscountDetail platformDiscountDetail = promotionDetail.getPlatformDiscountDetail();
        List<OrderDiscountDetailDTO> orderDiscountDetailDTOS = channelOrderDetailDTO.getActivities();
        if (orderDiscountDetailDTOS == null) {
            orderDiscountDetailDTOS = new ArrayList<>();
            channelOrderDetailDTO.setActivities(orderDiscountDetailDTOS);
        }
        if (shopDiscountDetail != null) {
            List<CouponInfoItem> couponInfos = shopDiscountDetail.getCouponInfo();
            if (CollectionUtils.isNotEmpty(couponInfos)) {
                for (CouponInfoItem couponInfo : couponInfos) {
                    OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
                    ShareDiscountCost shareDiscountCost = couponInfo.getShareDiscountCost();
                    orderDiscount.setActivityId(parseActivityId(couponInfo.getExtraMap()));
                    orderDiscount.setBizCharge(shareDiscountCost.getShopCost());
                    orderDiscount.setChannelCharge(shareDiscountCost.getPlatformCost());
                    orderDiscount.setActDiscount(couponInfo.getCouponAmount());
                    orderDiscount.setType(String.valueOf(couponInfo.getCouponType()));
                    // orderDiscount.setItemId()
                    orderDiscount.setRemark(couponInfo.getCouponName());
                    orderDiscountDetailDTOS.add(orderDiscount);
                }
            }

            List<FullDiscountInfoItem> fullDiscountInfos = shopDiscountDetail.getFullDiscountInfo();
            if (CollectionUtils.isNotEmpty(fullDiscountInfos)) {
                for (FullDiscountInfoItem fullDiscountInfo : fullDiscountInfos) {
                    OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
                    ShareDiscountCost shareDiscountCost = fullDiscountInfo.getShareDiscountCost();
                    orderDiscount.setActivityId(parseActivityId(fullDiscountInfo.getExtraMap()));
                    orderDiscount.setBizCharge(shareDiscountCost.getShopCost());
                    orderDiscount.setChannelCharge(shareDiscountCost.getPlatformCost());
                    orderDiscount.setActDiscount(fullDiscountInfo.getCampaignAmount());
                    orderDiscount.setType(String.valueOf(fullDiscountInfo.getCampaignType()));
                    // orderDiscount.setItemId()
                    orderDiscount.setRemark(fullDiscountInfo.getCampaignName());
                    orderDiscountDetailDTOS.add(orderDiscount);
                }
            }

            List<RedpackInfoItem> redpackInfos = shopDiscountDetail.getRedpackInfo();
            if (CollectionUtils.isNotEmpty(redpackInfos)) {
                for (RedpackInfoItem redpackInfo : redpackInfos) {
                    OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
                    ShareDiscountCost shareDiscountCost = redpackInfo.getShareDiscountCost();
                    orderDiscount.setActivityId(parseActivityId(redpackInfo.getExtraMap()));
                    orderDiscount.setBizCharge(shareDiscountCost.getShopCost());
                    orderDiscount.setChannelCharge(shareDiscountCost.getPlatformCost());
                    orderDiscount.setActDiscount(redpackInfo.getRedpackAmount());
                    // orderDiscount.setType(String.valueOf(fullDiscountInfo.getCampaignType()));
                    // orderDiscount.setItemId()
                    orderDiscount.setRemark("商家红包");
                    orderDiscountDetailDTOS.add(orderDiscount);
                }
            }
        }
        if (platformDiscountDetail != null) {
            List<CouponInfoItem_5_5> couponInfos = platformDiscountDetail.getCouponInfo();
            if (CollectionUtils.isNotEmpty(couponInfos)) {
                for (CouponInfoItem_5_5 couponInfo : couponInfos) {
                    OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
                    ShareDiscountCost shareDiscountCost = couponInfo.getShareDiscountCost();
                    orderDiscount.setActivityId(parseActivityId(couponInfo.getExtraMap()));
                    orderDiscount.setBizCharge(shareDiscountCost.getShopCost());
                    orderDiscount.setChannelCharge(shareDiscountCost.getPlatformCost());
                    orderDiscount.setActDiscount(couponInfo.getCouponAmount());
                    orderDiscount.setType(String.valueOf(couponInfo.getCouponType()));
                    // orderDiscount.setItemId()
                    orderDiscount.setRemark(couponInfo.getCouponName());
                    orderDiscountDetailDTOS.add(orderDiscount);
                }
            }

            List<FullDiscountInfoItem_5_5> fullDiscountInfos = platformDiscountDetail.getFullDiscountInfo();
            if (CollectionUtils.isNotEmpty(fullDiscountInfos)) {
                for (FullDiscountInfoItem_5_5 fullDiscountInfo : fullDiscountInfos) {
                    OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
                    ShareDiscountCost shareDiscountCost = fullDiscountInfo.getShareDiscountCost();
                    orderDiscount.setActivityId(parseActivityId(fullDiscountInfo.getExtraMap()));
                    orderDiscount.setBizCharge(shareDiscountCost.getShopCost());
                    orderDiscount.setChannelCharge(shareDiscountCost.getPlatformCost());
                    orderDiscount.setActDiscount(fullDiscountInfo.getCampaignAmount());
                    orderDiscount.setType(String.valueOf(fullDiscountInfo.getCampaignType()));
                    // orderDiscount.setItemId()
                    orderDiscount.setRemark(fullDiscountInfo.getCampaignName());
                    orderDiscountDetailDTOS.add(orderDiscount);
                    orderDiscountDetailDTOS.add(orderDiscount);
                }
            }

            List<RedpackInfoItem> redpackInfos = platformDiscountDetail.getRedpackInfo();
            if (CollectionUtils.isNotEmpty(redpackInfos)) {
                for (RedpackInfoItem redpackInfo : redpackInfos) {
                    OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
                    ShareDiscountCost shareDiscountCost = redpackInfo.getShareDiscountCost();
                    orderDiscount.setActivityId(parseActivityId(redpackInfo.getExtraMap()));
                    orderDiscount.setBizCharge(shareDiscountCost.getShopCost());
                    orderDiscount.setChannelCharge(shareDiscountCost.getPlatformCost());
                    orderDiscount.setActDiscount(redpackInfo.getRedpackAmount());
                    // orderDiscount.setItemId()
                    orderDiscount.setRemark("平台红包");
                    orderDiscountDetailDTOS.add(orderDiscount);
                }
            }
            // 当金额不为0时，添加对应金额的优惠信息
            addOrderPlatDiscountDetailDTO(orderDiscountDetailDTOS, platformDiscountDetail.getOfficialDeductionAmount(),
                    DouyinCustomPromotionTypeEnum.GFLJ);
            addOrderPlatDiscountDetailDTO(orderDiscountDetailDTOS, platformDiscountDetail.getAllowanceAmount(),
                    DouyinCustomPromotionTypeEnum.GWBTDK);
            addOrderPlatDiscountDetailDTO(orderDiscountDetailDTOS, platformDiscountDetail.getUserBalanceAmount(),
                    DouyinCustomPromotionTypeEnum.YEDK);
            addOrderPlatDiscountDetailDTO(orderDiscountDetailDTOS, platformDiscountDetail.getGoldCoinAmount(), DouyinCustomPromotionTypeEnum.JBDK);
        }
        // 运费优惠
        if(CollectionUtils.isNotEmpty(shopOrderDetail.getExtraPromotionAmountDetail())){
            for (ExtraPromotionAmountDetailItem extraPromotionAmountDetailItem : shopOrderDetail.getExtraPromotionAmountDetail()) {
                if(DouyinCustomPromotionTypeEnum.isLogisticsPromotion(extraPromotionAmountDetailItem.getPromotionType())) {
                    OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
                    ShareCost shareDiscountCost = extraPromotionAmountDetailItem.getShareCost();
                    if (shareDiscountCost != null) {
                        orderDiscount.setActivityId(extraPromotionAmountDetailItem.getPromotionType());
                        orderDiscount.setBizCharge(Optional.ofNullable(shareDiscountCost.getShopCost()).orElse(0L));
                        orderDiscount.setChannelCharge(Optional.ofNullable(shareDiscountCost.getPlatformCost()).orElse(0L)
                                + Optional.ofNullable(shareDiscountCost.getAuthorCost()).orElse(0L));
                        orderDiscount.setActDiscount(orderDiscount.getBizCharge() + orderDiscount.getChannelCharge());
                        orderDiscount.setRemark(extraPromotionAmountDetailItem.getPromotionDesc());
                        orderDiscountDetailDTOS.add(orderDiscount);
                    }
                }
            }
        }

        // 支付优惠活动
        Map<String, OrderDiscountDetailDTO> promotionPayAmountTypeMap = Maps.newHashMap();
        for (SkuOrderListItem skuOrderListItem : shopOrderDetail.getSkuOrderList()) {
            if(CollectionUtils.isNotEmpty(skuOrderListItem.getPromotionPayAmountDetails())) {
                for (PromotionPayAmountDetailsItem promotionPayAmountDetail : skuOrderListItem.getPromotionPayAmountDetails()) {
                    DouyinCustomPromotionTypeEnum promotionTypeEnum = DouyinCustomPromotionTypeEnum
                            .enumOf(promotionPayAmountDetail.getPayPromotionType());
                    ShareCost_5_5 shareDiscountCost = promotionPayAmountDetail.getShareCost();
                    if(shareDiscountCost != null) {
                        //OrderDiscountDetailDTO orderDiscount = promotionPayAmountTypeMap.get(promotionPayAmountDetail.getPayPromotionType());
                        OrderDiscountDetailDTO orderDiscount = promotionPayAmountTypeMap.get(Optional.ofNullable(promotionTypeEnum)
                                .map(DouyinCustomPromotionTypeEnum::getCode)
                                .orElse("other"));
                        if(orderDiscount != null){
                            orderDiscount.setBizCharge(orderDiscount.getBizCharge()
                                    + Optional.ofNullable(shareDiscountCost.getShopCost()).orElse(0L));
                            orderDiscount.setChannelCharge(orderDiscount.getChannelCharge()
                                    + Optional.ofNullable(shareDiscountCost.getPlatformCost()).orElse(0L));
                            orderDiscount.setActDiscount(orderDiscount.getBizCharge() + orderDiscount.getChannelCharge());
                        } else {
                            orderDiscount = new OrderDiscountDetailDTO();
                            orderDiscount.setActivityId(promotionPayAmountDetail.getPayPromotionType());
                            orderDiscount.setBizCharge(Optional.ofNullable(shareDiscountCost.getShopCost()).orElse(0L));
                            orderDiscount.setChannelCharge(Optional.ofNullable(shareDiscountCost.getPlatformCost()).orElse(0L));
                            orderDiscount.setActDiscount(orderDiscount.getBizCharge() + orderDiscount.getChannelCharge());
                            orderDiscount.setRemark(Optional.ofNullable(promotionTypeEnum)
                                .map(DouyinCustomPromotionTypeEnum::getDesc)
                                .orElse("支付优惠-未知"));
                            promotionPayAmountTypeMap.put(promotionPayAmountDetail.getPayPromotionType(), orderDiscount);
                        }
                    }
                }
            }
        }
        orderDiscountDetailDTOS.addAll(promotionPayAmountTypeMap.values());
        channelOrderDetailDTO.setActivities(orderDiscountDetailDTOS);
    }

    /**
     * 添加平台优惠明细
     * 
     * @param orderDiscountDetailDTOS
     * @param amount
     * @param douyinCustomPromotionTypeEnum
     */
    private void addOrderPlatDiscountDetailDTO(List<OrderDiscountDetailDTO> orderDiscountDetailDTOS, Long amount,
                                               DouyinCustomPromotionTypeEnum douyinCustomPromotionTypeEnum) {
        if (amount != null && amount > 0) {
            OrderDiscountDetailDTO orderDiscount = new OrderDiscountDetailDTO();
            orderDiscount.setActivityId(douyinCustomPromotionTypeEnum.getCode());
            orderDiscount.setBizCharge(0L);
            orderDiscount.setChannelCharge(amount);
            orderDiscount.setActDiscount(amount);
            orderDiscount.setRemark(douyinCustomPromotionTypeEnum.getDesc());
            orderDiscountDetailDTOS.add(orderDiscount);
        }
    }


    /**
     * 解析抖音订单状态
     * @param orderStatus 订单状态1 待确认/待支付（订单创建完毕）105 已支付 2 备货中
     *                    101 部分发货 3 已发货（全部发货）4 已取消5 已完成（已收货）
     * @return com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum
     */
    private int parseChannelStatusToOrderStatusEnum(Long orderStatus) {
        if (orderStatus == null) {
            return OrderStatusEnum.UN_KNOWN.getValue();
        }
        switch (orderStatus.intValue()) {
            case 1:
            case 105:
                return OrderStatusEnum.SUBMIT.getValue();
            case 2:
            case 101:
            case 3:
                return OrderStatusEnum.PICKING.getValue();
            case 4:
                return  OrderStatusEnum.CANCELED.getValue();
            case 5:
                return OrderStatusEnum.COMPLETED.getValue();
            default:
                return OrderStatusEnum.UN_KNOWN.getValue();
        }
    }

    /**
     *
     * @param orderStatus 订单状态1 待确认/待支付（订单创建完毕）105 已支付 2 备货中
     *                    101 部分发货 3 已发货（全部发货）4 已取消5 已完成（已收货）
     * @return com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelOrderStatus

     */
    private int parseChannelStatusToChannelOrderStatus(Long orderStatus) {
        if (orderStatus == null) {
            return 0;
        }
        switch (orderStatus.intValue()) {
            case 1:
            case 105:
                return ChannelOrderStatus.NEW_ORDER.getValue();
            case 2:
            case 101:
            case 3:
                return ChannelOrderStatus.FULFILLMENT.getValue();
            case 4:
                return ChannelOrderStatus.CANCELED.getValue();
            case 5:
                return ChannelOrderStatus.FINISHED.getValue();
            default:
                return 0;
        }

    }

    private void fillDeliveryDetail(ChannelOrderDetailDTO channelOrderDetailDTO, ShopOrderDetail shopOrderDetail,
                                    DecryptRespDto decryptRespDto, MobileInfo mobileInfo, DeliveryPlatformEnum deliveryPlatformEnum) {
        OrderDeliveryDetailDTO deliveryDetail = new OrderDeliveryDetailDTO();
        
        deliveryDetail.setUserName(StringUtils.isNotBlank(decryptRespDto.getName()) ? decryptRespDto.getName()
                : shopOrderDetail.getMaskPostReceiver());
        deliveryDetail.setUserAddress(StringUtils.isNotBlank(decryptRespDto.getAddress()) ? decryptRespDto.getAddress()
                : shopOrderDetail.getMaskPostAddr().getDetail());
        deliveryDetail.setUserPhone(shopOrderDetail.getMaskPostTel());
        // UserPrivacyPhone隐私号，可以用*号
        deliveryDetail.setUserPrivacyPhone(shopOrderDetail.getMaskPostTel());
        deliveryDetail.setUsePrivacyPhone(1);
        // UserPhone, 拨打电话需要，不能含*号，先使用明解密后的数据，使用"_"拼接，后续如果有隐私小号，再使用隐私小号
        if (BooleanUtils.isTrue(decryptRespDto.getIsVirtualTel())) {
            deliveryDetail.setUserPhone(decryptRespDto.getPhoneNoA() + "_" +decryptRespDto.getPhoneNoB());
        } else if (StringUtils.isNotBlank(decryptRespDto.getMobile())){
            deliveryDetail.setUserPhone(decryptRespDto.getMobile());
        }
        if (BooleanUtils.isTrue(shopOrderDetail.getEarlyArrival())) {
            // 尽快送达，即时单
            deliveryDetail.setArrivalTime(shopOrderDetail.getTargetArrivalTime() * 1000);
            deliveryDetail.setArrivalEndTime(shopOrderDetail.getTargetArrivalTime() * 1000);
        } else {
            // 非尽快送达，预定单
            deliveryDetail.setArrivalTime(shopOrderDetail.getEarliestReceiptTime() * 1000);
            deliveryDetail.setArrivalEndTime(shopOrderDetail.getLatestReceiptTime() * 1000);
        }
        deliveryDetail.setLatitude(Double.parseDouble(shopOrderDetail.getUserCoordinate().getUserCoordinateLatitude()));
        deliveryDetail.setLongitude(Double.parseDouble(shopOrderDetail.getUserCoordinate().getUserCoordinateLongitude()));

        // 隐私小号，如果获取成功再覆盖明文
        if (mobileInfo != null && StringUtils.isNotBlank(mobileInfo.getMobileVirtual())) {
            deliveryDetail.setUserPhone(mobileInfo.getMobileVirtual().replace("-", "_"));
        }
        // 只要设置为notBlank或者不含“自提”这个词语，就是到家订单，否则是自提订单
        deliveryDetail.setDeliveryMethod(deliveryPlatformEnum == null ? "未知" : deliveryPlatformEnum.getDesc());
        //是否为商家自配送 0:否 1:是
        deliveryDetail.setIsSelfDelivery(deliveryPlatformEnum == ORDER_CHANNEL_DELIVERY_PLATFORM ? 0 : 1);
        deliveryDetail.setUserPhoneIsValid(true);
        // 解密地址为空，或者解密号码为空且获取隐私号失败，需要降级补偿
        if (StringUtils.isBlank(decryptRespDto.getAddress())
                || (StringUtils.isBlank(decryptRespDto.getMobile())
                && (mobileInfo == null || StringUtils.isBlank(mobileInfo.getMobileVirtual())))) {
            // 订单状态为已取消或者已完成，则无需降级
            if(needAddDownFlag(shopOrderDetail.getOrderStatus())){
                channelOrderDetailDTO.setDownFlag(1);
                channelOrderDetailDTO.setDegradeModules(Lists.newArrayList(OrderDegradeModuleEnum.DY_USER_SENSITIVE_DATA.getValue()));
            }
        }
        // 设置最晚拣货时间
        fillPickUpEndTime(deliveryDetail, shopOrderDetail);
        log.info("orderId: {}, deliveryDetail: {}", shopOrderDetail.getOrderId(), deliveryDetail);
        channelOrderDetailDTO.setDeliveryDetail(deliveryDetail);
    }

    /**
     *
     * @param orderStatus 订单状态1 待确认/待支付（订单创建完毕）105 已支付 2 备货中
     *                    101 部分发货 3 已发货（全部发货）4 已取消5 已完成（已收货）
     * @return boolean

     */
    private boolean needAddDownFlag(Long orderStatus) {
        if (orderStatus == null) {
            return true;
        }
        switch (orderStatus.intValue()) {
            case 4:
            case 5:
                return false;
            default:
                return true;
        }

    }

    private void fillPickUpEndTime(OrderDeliveryDetailDTO deliveryDetail, ShopOrderDetail shopOrderDetail){
        try {
            if (shopOrderDetail.getReceiptTimeMap() != null
                    && shopOrderDetail.getReceiptTimeMap().get("LatestStockingCompletionTime") != null
            ) {
                deliveryDetail.setPickUpEndTime(Long.parseLong(shopOrderDetail.getReceiptTimeMap().get("LatestStockingCompletionTime") + "000"));
            }
        } catch (Exception e) {
            log.error("fillPickUpEndTime error orderId: {}, receiptTimeMap: {}", shopOrderDetail.getOrderId(), shopOrderDetail.getReceiptTimeMap());
        }
    }

    private void fillProductDetailAndPromotion(ChannelOrderDetailDTO channelOrderDetailDTO, List<SkuOrderListItem> skuOrderList,
                                               List<ProductDetailData> productDetailList) {
        if (CollectionUtils.isEmpty(skuOrderList)) {
            return;
        }
        int totalPoiItemPromotion = 0;
        int totalPoiOrderPromotion = 0;
        int totalPlatItemPromotion = 0;
        int totalPlatOrderPromotion = 0;
        List<OrderProductDetailDTO> skuList = new ArrayList<>();
        for(int i = 0; i < skuOrderList.size(); i++) {
            SkuOrderListItem sku = skuOrderList.get(i);
            OrderProductDetailDTO orderProductDetailDTO = new OrderProductDetailDTO();
            orderProductDetailDTO.setSkuId(String.valueOf(sku.getOutSkuId()));
            orderProductDetailDTO.setChannelSkuId(String.valueOf(sku.getSkuId()));
            orderProductDetailDTO.setCustomSpu(sku.getOutProductId());
            orderProductDetailDTO.setSkuName(sku.getProductName());
            // orderProductDetailDTO.setSkuPicUrls(sku.getProductPic());
            orderProductDetailDTO.setSpecification(DouyinOrderConverterUtil.obtainSpecInfo(sku.getSpec()));
            orderProductDetailDTO.setQuantity(sku.getItemNum().intValue());      // 有溢出风险，不过目前看应该不太可能
            orderProductDetailDTO.setChannelItemId(sku.getOrderId());
            // 设置重量，是从渠道查的，double转long可能精度问题，并且这里catch异常，不让重量影响落库
            if (MccConfigUtil.isDYQueryProductDetailSwitch() && CollectionUtils.isNotEmpty(productDetailList)) {
                ProductDetailData productDetail = productDetailList.get(i);
                try {
                    List<SpecPricesItem> specPrices = productDetail.getSpecPrices();
                    List<DeliveryInfosItem> deliveryInfos = specPrices.get(0).getDeliveryInfos();
                    for (DeliveryInfosItem deliveryInfo : deliveryInfos) {
                        if (StringUtils.equals("weight", deliveryInfo.getInfoType())) {
                            orderProductDetailDTO.setWeight(Long.parseLong(deliveryInfo.getInfoValue()));
                            orderProductDetailDTO.setUnit(deliveryInfo.getInfoUnit());
                            break;
                        }
                    }
                } catch (Exception e) {
                    log.error("设置商品重量失败, sku: {}, productDetail: {}", sku, productDetail, e);
                }
            }

            if(DouyinOrderConverterUtil.convertIsGift(sku.getGivenProductType())) {
                // 赠品
                orderProductDetailDTO.setItemType(1);
                orderProductDetailDTO.setOriginalPrice(0);           // 原价
                orderProductDetailDTO.setActualSalePrice(0);       // 售价
                orderProductDetailDTO.setSalePrice(0);       // 售价
                orderProductDetailDTO.setTotalPayAmtMinusDiscount(0);     // 实付金额
                // 单品优惠数据
                orderProductDetailDTO.setPlatItemPromotion(0);
                orderProductDetailDTO.setPoiItemPromotion(0);
                orderProductDetailDTO.setTenantCost(0);          // 商家承担金额
                orderProductDetailDTO.setChannelCost(0);     // 平台承担金额
                orderProductDetailDTO.setTotalDiscount(0);      // 总优惠
                JSONObject extJSON = new JSONObject();
                if (orderProductDetailDTO.isSetExtData()) {
                    try{
                        String extData = orderProductDetailDTO.getExtData();
                        extJSON = JSON.parseObject(extData);
                    } catch (Exception e) {
                        log.error("解析ext异常，detail：{}", JSON.toJSONString(orderProductDetailDTO), e);
                    }
                }
                // 添加线上赠品标（giftType  :  0 (平台赠品)），标志打在明细的扩展字段
                extJSON.put("giftType", GiftTypeEnum.ONLINE.getValue());
                orderProductDetailDTO.setExtData(extJSON.toJSONString());
            } else {
                // 非赠品
                orderProductDetailDTO.setItemType(0);
                // 各种金额
                orderProductDetailDTO.setOriginalPrice(sku.getGoodsPrice().intValue());           // 原价
                orderProductDetailDTO.setActualSalePrice(sku.getOriginAmount().intValue());       // 售价
                orderProductDetailDTO.setSalePrice(orderProductDetailDTO.getActualSalePrice());       // 售价
                orderProductDetailDTO.setTotalPayAmtMinusDiscount(sku.getPayAmount().intValue());     // 实付金额
                // 平台&商家 整单&单品优惠，暂时按照promotion_detail里的为整单，campaign_info里的为单品来计算
                // 这个暂时不用， 单品优惠，仅campaign_info.campaign_type == 7 && campaign_info.campaign_sub_type == 0， 这个暂时不用
                // 这个暂时不用，整单优惠，campaign_info里面订的其他优惠以及skuOrderList里的，这个暂时不用
                // 整单优惠，可直接从PromotionShopAmount和PromotionPlatformAmount取，无需从promotion_detail取
                Long poiOrderPromotion = sku.getPromotionShopAmount();
                Long platOrderPromotion = sku.getPromotionPlatformAmount();

                Long platItemPromotion = 0L;
                Long poiItemPromotion = 0L;
                List<CampaignInfoItem> campaignInfos = sku.getCampaignInfo();
                if (CollectionUtils.isNotEmpty(campaignInfos)) {
                    for (CampaignInfoItem campaignInfo : campaignInfos) {
                        ShareDiscountCost shareDiscountCost = campaignInfo.getShareDiscountCost();
                        platItemPromotion += shareDiscountCost.getPlatformCost();
                        poiItemPromotion += shareDiscountCost.getShopCost();
                        fillItemPromotionActivities(channelOrderDetailDTO, orderProductDetailDTO, campaignInfo);
                    }
                }
                // 单品优惠数据
                orderProductDetailDTO.setPlatItemPromotion(platItemPromotion.intValue());
                orderProductDetailDTO.setPoiItemPromotion(poiItemPromotion.intValue());

                // 整单优惠数据
                fillOrderPromotion(orderProductDetailDTO, sku);

                totalPlatOrderPromotion += orderProductDetailDTO.getPlatPromotion();
                totalPlatItemPromotion += orderProductDetailDTO.getPlatItemPromotion();
                totalPoiOrderPromotion += orderProductDetailDTO.getPoiPromotion();
                totalPoiItemPromotion += orderProductDetailDTO.getPoiItemPromotion();

                orderProductDetailDTO.setTenantCost(
                        orderProductDetailDTO.getPoiItemPromotion() + orderProductDetailDTO.getPoiPromotion());          // 商家承担金额
                orderProductDetailDTO.setChannelCost(
                        orderProductDetailDTO.getPlatItemPromotion() + orderProductDetailDTO.getPlatPromotion());     // 平台承担金额

                orderProductDetailDTO
                        .setTotalDiscount(orderProductDetailDTO.getTenantCost() + orderProductDetailDTO.getChannelCost());      // 总优惠
            }
            skuList.add(orderProductDetailDTO);
        }
        channelOrderDetailDTO.setSkuDetails(skuList);

        channelOrderDetailDTO.setPlatItemPromotion(totalPlatItemPromotion);
        channelOrderDetailDTO.setPlatPromotion(totalPlatOrderPromotion);
        channelOrderDetailDTO.setPoiItemPromotion(totalPoiItemPromotion);
        channelOrderDetailDTO.setPoiPromotion(totalPoiOrderPromotion);
        channelOrderDetailDTO.setTotalDiscount(
                totalPoiOrderPromotion + totalPoiItemPromotion + totalPlatOrderPromotion + totalPlatItemPromotion);
    }

    private void fillOrderPromotion(OrderProductDetailDTO orderProductDetailDTO, SkuOrderListItem sku) {

        ShopDiscountDetail shopDiscountDetail = sku.getPromotionDetail().getShopDiscountDetail();
        PlatformDiscountDetail_5_5 platformDiscountDetail = sku.getPromotionDetail().getPlatformDiscountDetail();
        List<PromotionPayAmountDetailsItem> promotionPayAmountDetails = sku.getPromotionPayAmountDetails();
        long poiOrderPromotion = 0;
        long platOrderPromotion = 0;
        if (shopDiscountDetail != null) {
            List<FullDiscountInfoItem> fullDiscountInfos = shopDiscountDetail.getFullDiscountInfo();

            if (CollectionUtils.isNotEmpty(fullDiscountInfos)) {
                for (FullDiscountInfoItem fullDiscountInfo : fullDiscountInfos) {
                    ShareDiscountCost shareDiscountCost = fullDiscountInfo.getShareDiscountCost();
                    poiOrderPromotion += shareDiscountCost.getShopCost();
                    platOrderPromotion += shareDiscountCost.getPlatformCost();
                }
            }

            List<CouponInfoItem> couponInfos = shopDiscountDetail.getCouponInfo();
            if (CollectionUtils.isNotEmpty(couponInfos)) {
                for (CouponInfoItem couponInfo : couponInfos) {
                    ShareDiscountCost shareDiscountCost = couponInfo.getShareDiscountCost();
                    poiOrderPromotion += shareDiscountCost.getShopCost();
                    platOrderPromotion += shareDiscountCost.getPlatformCost();
                }
            }

            List<RedpackInfoItem> redpackInfos = shopDiscountDetail.getRedpackInfo();
            if (CollectionUtils.isNotEmpty(redpackInfos)) {
                for (RedpackInfoItem redpackInfo : redpackInfos) {
                    ShareDiscountCost shareDiscountCost = redpackInfo.getShareDiscountCost();
                    poiOrderPromotion += shareDiscountCost.getShopCost();
                    platOrderPromotion += shareDiscountCost.getPlatformCost();
                }
            }
        }

        if (platformDiscountDetail != null) {
            List<FullDiscountInfoItem_6_6> fullDiscountInfos = platformDiscountDetail.getFullDiscountInfo();

            if (CollectionUtils.isNotEmpty(fullDiscountInfos)) {
                for (FullDiscountInfoItem_6_6 fullDiscountInfo : fullDiscountInfos) {
                    ShareDiscountCost shareDiscountCost = fullDiscountInfo.getShareDiscountCost();
                    poiOrderPromotion += shareDiscountCost.getShopCost();
                    platOrderPromotion += shareDiscountCost.getPlatformCost();
                }
            }

            List<CouponInfoItem_6_6> couponInfos = platformDiscountDetail.getCouponInfo();
            if (CollectionUtils.isNotEmpty(couponInfos)) {
                for (CouponInfoItem_6_6 couponInfo : couponInfos) {
                    ShareDiscountCost shareDiscountCost = couponInfo.getShareDiscountCost();
                    poiOrderPromotion += shareDiscountCost.getShopCost();
                    platOrderPromotion += shareDiscountCost.getPlatformCost();
                }
            }
            List<RedpackInfoItem> redpackInfos = platformDiscountDetail.getRedpackInfo();
            if (CollectionUtils.isNotEmpty(redpackInfos)) {
                for (RedpackInfoItem redpackInfo : redpackInfos) {
                    ShareDiscountCost shareDiscountCost = redpackInfo.getShareDiscountCost();
                    poiOrderPromotion += shareDiscountCost.getShopCost();
                    platOrderPromotion += shareDiscountCost.getPlatformCost();
                }
            }
            // 平台优惠-官方立减金额
            platOrderPromotion += Optional.ofNullable(platformDiscountDetail.getOfficialDeductionAmount()).orElse(0L);
            // 平台优惠-购物补贴抵扣金额
            platOrderPromotion += Optional.ofNullable(platformDiscountDetail.getAllowanceAmount()).orElse(0L);
            // 平台优惠-余额抵扣金额
            platOrderPromotion += Optional.ofNullable(platformDiscountDetail.getUserBalanceAmount()).orElse(0L);
            // 平台优惠-金币抵扣金额
            platOrderPromotion += Optional.ofNullable(platformDiscountDetail.getGoldCoinAmount()).orElse(0L);
        }

        // 支付优惠
        if(CollectionUtils.isNotEmpty(promotionPayAmountDetails)){
            for (PromotionPayAmountDetailsItem promotionPayAmountDetail : promotionPayAmountDetails) {
                ShareCost_5_5 shareDiscountCost = promotionPayAmountDetail.getShareCost();
                poiOrderPromotion += shareDiscountCost.getShopCost();
                platOrderPromotion += shareDiscountCost.getPlatformCost();
            }
        }

        orderProductDetailDTO.setPoiPromotion((int) poiOrderPromotion);
        orderProductDetailDTO.setPlatPromotion((int) platOrderPromotion);
    }

    private void fillActivityShareInfo(ChannelOrderDetailDTO channelOrderDetailDTO, ShopOrderDetail shopOrderDetail, long tenantId) {
        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        try {
            // 商品粒度的优惠，渠道已拆分完成
            activityShareDetailDTOList.addAll(splitProductPromotion(shopOrderDetail.getSkuOrderList()));
            // 订单粒度的优惠，渠道暂未拆分
            activityShareDetailDTOList.addAll(splitOrderPromotion(channelOrderDetailDTO, shopOrderDetail.getExtraPromotionAmountDetail(), tenantId));
            log.info("抖音活动分摊解析结果：activityShareDetailDTOList={}", GsonUtils.toJSONString(activityShareDetailDTOList));
        } catch (Exception e) {
            log.error("活动分摊异常", e);
        }

        channelOrderDetailDTO.setActivityShareDetailList(activityShareDetailDTOList);
    }

    private List<ActivityShareDetailDTO> splitProductPromotion(List<SkuOrderListItem> skuOrderList) {
        if (CollectionUtils.isEmpty(skuOrderList)) {
            return Collections.emptyList();
        }

        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        skuOrderList.forEach(item -> {
            // 优惠信息:promotion_detail
            PromotionDetail_4_4 skuPromotionDetail = item.getPromotionDetail();
            activityShareDetailDTOList.addAll(buildActDetailsFromPromotionDetail(skuPromotionDetail, item));

            // 活动信息:campaign_info
            List<CampaignInfoItem> campaignInfoItems = item.getCampaignInfo();
            activityShareDetailDTOList.addAll(buildActDetailsFromCampaignInfo(campaignInfoItems, item));

            // 支付优惠信息:promotion_pay_amount_details
            List<PromotionPayAmountDetailsItem> promotionPayAmountDetails = item.getPromotionPayAmountDetails();
            activityShareDetailDTOList.addAll(buildActDetailsFromPromotionPayAmount(promotionPayAmountDetails, item));
        });
        return activityShareDetailDTOList;
    }

    private List<ActivityShareDetailDTO> buildActDetailsFromPromotionDetail(PromotionDetail_4_4 skuPromotionDetail,
                                                                            SkuOrderListItem item) {
        if (skuPromotionDetail == null) {
            return Collections.emptyList();
        }

        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        List<CouponInfoItem> couponInfoItems = new ArrayList<>();
        List<FullDiscountInfoItem> fullDiscountInfoItems = new ArrayList<>();
        List<RedpackInfoItem> redpackInfoItems = new ArrayList<>();
        // 店铺优惠信息
        ShopDiscountDetail shopDiscountDetail = skuPromotionDetail.getShopDiscountDetail();
        if (shopDiscountDetail != null) {
            couponInfoItems.addAll(CollectionUtils.emptyIfNull(shopDiscountDetail.getCouponInfo()));
            fullDiscountInfoItems.addAll(CollectionUtils.emptyIfNull(shopDiscountDetail.getFullDiscountInfo()));
            redpackInfoItems.addAll(CollectionUtils.emptyIfNull(shopDiscountDetail.getRedpackInfo()));
        }

        // 平台优惠信息
        PlatformDiscountDetail_5_5 platformDiscountDetail = skuPromotionDetail.getPlatformDiscountDetail();
        if (platformDiscountDetail != null) {
            List<CouponInfoItem_6_6> couponInfo = platformDiscountDetail.getCouponInfo();
            List<FullDiscountInfoItem_6_6> fullDiscountInfo = platformDiscountDetail.getFullDiscountInfo();
            couponInfoItems.addAll(DouyinOrderConverterUtil.convertCouponInfo(couponInfo));
            fullDiscountInfoItems.addAll(DouyinOrderConverterUtil.convertFullDiscountInfo(fullDiscountInfo));
            redpackInfoItems.addAll(CollectionUtils.emptyIfNull(platformDiscountDetail.getRedpackInfo()));

            // 特殊平台优惠
            addSpecialPlatPromotion(activityShareDetailDTOList, item, platformDiscountDetail.getOfficialDeductionAmount(),
                    DouyinCustomPromotionTypeEnum.GFLJ);
            addSpecialPlatPromotion(activityShareDetailDTOList, item, platformDiscountDetail.getAllowanceAmount(),
                    DouyinCustomPromotionTypeEnum.GWBTDK);
            addSpecialPlatPromotion(activityShareDetailDTOList, item, platformDiscountDetail.getUserBalanceAmount(),
                    DouyinCustomPromotionTypeEnum.YEDK);
            addSpecialPlatPromotion(activityShareDetailDTOList, item, platformDiscountDetail.getGoldCoinAmount(),
                    DouyinCustomPromotionTypeEnum.JBDK);
        }

        // 达人优惠信息
        KolDiscountDetail_5_5 kolDiscountDetail = skuPromotionDetail.getKolDiscountDetail();
        if (kolDiscountDetail != null) {
            List<CouponInfoItem_6_6> couponInfo = kolDiscountDetail.getCouponInfo();
            List<FullDiscountInfoItem_6_6> fullDiscountInfo = kolDiscountDetail.getFullDiscountInfo();
            couponInfoItems.addAll(DouyinOrderConverterUtil.convertCouponInfo(couponInfo));
            fullDiscountInfoItems.addAll(DouyinOrderConverterUtil.convertFullDiscountInfo(fullDiscountInfo));
            redpackInfoItems.addAll(CollectionUtils.emptyIfNull(kolDiscountDetail.getRedpackInfo()));
        }

        activityShareDetailDTOList.addAll(buildActivityShareDetailDTO(couponInfoItems, fullDiscountInfoItems, redpackInfoItems, item));
        return activityShareDetailDTOList;
    }

    private void addSpecialPlatPromotion(List<ActivityShareDetailDTO> activityShareList, SkuOrderListItem skuOrderListItem,
                                         Long amount, DouyinCustomPromotionTypeEnum promotionTypeEnum) {
        if (amount == null || amount <= 0) {
            return;
        }

        ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
        activityShareDetailDTO.setActivityId(promotionTypeEnum.getCode())
                .setChannelPromotionType(promotionTypeEnum.getCode())
                .setPromotionRemark(promotionTypeEnum.getDesc())
                .setSkuId(skuOrderListItem.getOutSkuId())
                .setCustomSpu(skuOrderListItem.getOutProductId())
                .setSkuCount(skuOrderListItem.getItemNum().intValue())
                .setPromotionCount(skuOrderListItem.getItemNum().intValue())
                .setChannelCost(amount.intValue())
                .setTenantCost(0)
                .setTotalOriginPrice(skuOrderListItem.getGoodsPrice().intValue() * skuOrderListItem.getItemNum().intValue());
        activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() -
                activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());
        activityShareList.add(activityShareDetailDTO);
    }

    private List<ActivityShareDetailDTO> buildActivityShareDetailDTO(List<CouponInfoItem> couponInfoItems,
                                                                     List<FullDiscountInfoItem> fullDiscountInfoItems,
                                                                     List<RedpackInfoItem> redpackInfoItems,
                                                                     SkuOrderListItem skuOrderListItem) {
        List<ActivityShareDetailDTO> activityShareDetailDTOList = new ArrayList<>();
        // 抖音商品参加的整单优惠-优惠券活动
        couponInfoItems.forEach(item -> {
            ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
            activityShareDetailDTO.setActivityId(parseActivityId(item.getExtraMap()))
                    .setChannelPromotionType(String.valueOf(item.getCouponType()))
                    .setPromotionRemark(item.getCouponName())
                    .setSkuId(skuOrderListItem.getOutSkuId())
                    .setCustomSpu(skuOrderListItem.getOutProductId())
                    .setSkuCount(skuOrderListItem.getItemNum().intValue())
                    .setPromotionCount(skuOrderListItem.getItemNum().intValue())
                    .setChannelCost(Optional.ofNullable(item.getShareDiscountCost()).map(ShareDiscountCost::getPlatformCost).map(Long::intValue).orElse(0))
                    .setTenantCost(Optional.ofNullable(item.getShareDiscountCost()).map(ShareDiscountCost::getShopCost).map(Long::intValue).orElse(0))
                    .setTotalOriginPrice(skuOrderListItem.getGoodsPrice().intValue() * skuOrderListItem.getItemNum().intValue());
            activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() - activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());
            activityShareDetailDTOList.add(activityShareDetailDTO);
        });

        // 抖音商品参加的整单优惠-促销活动
        fullDiscountInfoItems.forEach(item -> {
            ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
            activityShareDetailDTO.setActivityId(parseActivityId(item.getExtraMap()))
                    .setChannelPromotionType(String.valueOf(item.getCampaignType()))
                    .setPromotionRemark(item.getCampaignName())
                    .setSkuId(skuOrderListItem.getOutSkuId())
                    .setCustomSpu(skuOrderListItem.getOutProductId())
                    .setSkuCount(skuOrderListItem.getItemNum().intValue())
                    .setPromotionCount(skuOrderListItem.getItemNum().intValue())
                    .setChannelCost(Optional.ofNullable(item.getShareDiscountCost()).map(ShareDiscountCost::getPlatformCost).map(Long::intValue).orElse(0))
                    .setTenantCost(Optional.ofNullable(item.getShareDiscountCost()).map(ShareDiscountCost::getShopCost).map(Long::intValue).orElse(0))
                    .setTotalOriginPrice(skuOrderListItem.getGoodsPrice().intValue() * skuOrderListItem.getItemNum().intValue());
            activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() - activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());
            activityShareDetailDTOList.add(activityShareDetailDTO);
        });

        // 抖音商品参加的整单优惠-红包
        redpackInfoItems.forEach(item -> {
            ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
            activityShareDetailDTO.setActivityId(parseActivityId(item.getExtraMap()))
                    .setChannelPromotionType(DOUYIN_REDPACK_ACTIVITY_TYPE)
                    .setPromotionRemark(DOUYIN_REDPACK_ACTIVITY_NAME)
                    .setSkuId(skuOrderListItem.getOutSkuId())
                    .setCustomSpu(skuOrderListItem.getOutProductId())
                    .setSkuCount(skuOrderListItem.getItemNum().intValue())
                    .setPromotionCount(skuOrderListItem.getItemNum().intValue())
                    .setChannelCost(Optional.ofNullable(item.getShareDiscountCost()).map(ShareDiscountCost::getPlatformCost).map(Long::intValue).orElse(0))
                    .setTenantCost(Optional.ofNullable(item.getShareDiscountCost()).map(ShareDiscountCost::getShopCost).map(Long::intValue).orElse(0))
                    .setTotalOriginPrice(skuOrderListItem.getGoodsPrice().intValue() * skuOrderListItem.getItemNum().intValue());
            activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() - activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());
            activityShareDetailDTOList.add(activityShareDetailDTO);
        });

        return activityShareDetailDTOList;
    }

    private List<ActivityShareDetailDTO> buildActDetailsFromCampaignInfo(List<CampaignInfoItem> campaignInfoItems,
                                                                         SkuOrderListItem skuOrderListItem) {
        // 抖音商品参加的单品补贴活动
        return Fun.map(campaignInfoItems, item -> {
            ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
            activityShareDetailDTO.setActivityId(parseActivityId(item.getExtraMap()))
                    .setChannelPromotionType(String.valueOf(item.getCampaignType()))
                    .setPromotionRemark(item.getCampaignName())
                    .setSkuId(skuOrderListItem.getOutSkuId())
                    .setCustomSpu(skuOrderListItem.getOutProductId())
                    .setSkuCount(skuOrderListItem.getItemNum().intValue())
                    .setPromotionCount(skuOrderListItem.getItemNum().intValue())
                    .setChannelCost(Optional.ofNullable(item.getShareDiscountCost()).map(ShareDiscountCost::getPlatformCost)
                            .map(Long::intValue).orElse(0))
                    .setTenantCost(Optional.ofNullable(item.getShareDiscountCost()).map(ShareDiscountCost::getShopCost)
                            .map(Long::intValue).orElse(0))
                    .setTotalOriginPrice(skuOrderListItem.getGoodsPrice().intValue() * skuOrderListItem.getItemNum().intValue());
            activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() -
                    activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());
            return activityShareDetailDTO;
        });
    }

    private List<ActivityShareDetailDTO> buildActDetailsFromPromotionPayAmount(List<PromotionPayAmountDetailsItem> amountDetails,
                                                                               SkuOrderListItem skuOrderListItem) {
        return Fun.map(amountDetails, item -> {
            DouyinCustomPromotionTypeEnum promotionTypeEnum = DouyinCustomPromotionTypeEnum.enumOf(item.getPayPromotionType());
            ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
            activityShareDetailDTO.setActivityId(item.getPayPromotionType())
                    .setChannelPromotionType(item.getPayPromotionType())
                    .setPromotionRemark(Optional.ofNullable(promotionTypeEnum)
                            .map(DouyinCustomPromotionTypeEnum::getDesc).orElse("支付优惠-未知"))
                    .setSkuId(skuOrderListItem.getOutSkuId())
                    .setCustomSpu(skuOrderListItem.getOutProductId())
                    .setSkuCount(skuOrderListItem.getItemNum().intValue())
                    .setPromotionCount(skuOrderListItem.getItemNum().intValue())
                    .setChannelCost(Optional.ofNullable(item.getShareCost()).map(ShareCost_5_5::getPlatformCost)
                            .map(Long::intValue).orElse(0))
                    .setTenantCost(Optional.ofNullable(item.getShareCost()).map(ShareCost_5_5::getShopCost)
                            .map(Long::intValue).orElse(0))
                    .setTotalOriginPrice(skuOrderListItem.getGoodsPrice().intValue() * skuOrderListItem.getItemNum().intValue());
            activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() -
                    activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());
            return activityShareDetailDTO;
        });
    }

    private String parseActivityId(Map<String, String> extraMap) {
        if (MapUtils.isEmpty(extraMap)) {
            return StringUtils.EMPTY;
        }
        String customerInfoStr = extraMap.get(INSTANT_CUSTOMER_INFO);
        if (StringUtils.isBlank(customerInfoStr)) {
            return StringUtils.EMPTY;
        }
        InstantCustomInfo instantCustomInfo = GsonUtil.toJavaBean(customerInfoStr, InstantCustomInfo.class);
        return Optional.ofNullable(instantCustomInfo).map(InstantCustomInfo::getInstant_activity_id).orElse(StringUtils.EMPTY);
    }

    private List<ActivityShareDetailDTO> splitOrderPromotion(ChannelOrderDetailDTO channelOrderDetail,
                                                             List<ExtraPromotionAmountDetailItem> promotionList, long tenantId) {
        if (CollectionUtils.isEmpty(promotionList)) {
            return Collections.emptyList();
        }

        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        promotionList.forEach(promotion -> {
            if (DouyinCustomPromotionTypeEnum.isLogisticsPromotion(promotion.getPromotionType())) {
                activityShareDetailDTOList.addAll(splitLogisticsPromotion(channelOrderDetail, promotion, tenantId));
            } else {
                log.info("[抖音渠道活动分摊解析]未适配的优惠类型，promotion:{}", GsonUtil.toJSONString(promotion));
            }
        });
        return activityShareDetailDTOList;
    }

    private List<ActivityShareDetailDTO> splitLogisticsPromotion(ChannelOrderDetailDTO channelOrderDetail,
                                                                 ExtraPromotionAmountDetailItem promotion, long tenantId) {

        List<OrderProductDetailDTO> skuDetails = Fun.filter(channelOrderDetail.getSkuDetails(),
                detail -> !Objects.equals(detail.getItemType(), 1));
        if (CollectionUtils.isEmpty(skuDetails)) {
            log.info("[抖音渠道活动分摊解析]无可分摊的商品明细，skuDetails:{}", GsonUtil.toJSONString(channelOrderDetail.getSkuDetails()));
            return Collections.emptyList();
        }

        int shopCost = getCostValue(promotion.getShareCost(), ShareCost::getShopCost);
        int platformCost = getCostValue(promotion.getShareCost(), ShareCost::getPlatformCost)
                + getCostValue(promotion.getShareCost(), ShareCost::getAuthorCost);
        if (shopCost <= 0 && platformCost <= 0) {
            log.info("[抖音渠道活动分摊解析]分摊金额不合法，promotion:{}", GsonUtil.toJSONString(promotion));
            return Collections.emptyList();
        }

        int totalActualSalePrice = skuDetails.stream().mapToInt(detail -> detail.getSalePrice() * detail.getQuantity()).sum();
        int totalQuantity = skuDetails.stream().mapToInt(OrderProductDetailDTO::getQuantity).sum();
        // log.info("排序前skuDetailList={}", GsonUtil.toJSONString(skuDetails));
        ActivityPromotionSplitUtil.sortProductList(tenantId, skuDetails, skuDetail -> skuDetail.getSalePrice() * skuDetail.getQuantity(),
                OrderProductDetailDTO::getQuantity, OrderProductDetailDTO::getSkuName);

        List<ActivityShareDetailDTO> activityShareDetailDTOList = Lists.newArrayList();
        int lastPlatPromotion = platformCost, lastPoiPromotion = shopCost;
        for (int index = 0; index < skuDetails.size(); index++) {
            OrderProductDetailDTO skuDetail = skuDetails.get(index);
            ActivityShareDetailDTO activityShareDetailDTO;
            if (index == skuDetails.size() - 1) {
                activityShareDetailDTO = buildActivityShareDetailDTO(promotion, skuDetail, lastPlatPromotion, lastPoiPromotion);
            } else {
                /*
                  参考下游 settlement 服务单头金额到明细的拆分方式
                  com.sankuai.meituan.shangou.empower.settlement.domain.finance.factory.FinanceCombinationFactory#splitHead2Detail
                 */
                double percent = 0d;
                if (totalActualSalePrice == 0) {
                    percent = skuDetail.getQuantity() * 1.0 / totalQuantity;
                } else {
                    percent = skuDetail.getSalePrice() * skuDetail.getQuantity() * 1.0 / totalActualSalePrice;
                }

                int poiPromotion = Double.valueOf(shopCost * percent).intValue();
                int platPromotion = Double.valueOf(platformCost * percent).intValue();
                lastPoiPromotion -= poiPromotion;
                lastPlatPromotion -= platPromotion;
                activityShareDetailDTO = buildActivityShareDetailDTO(promotion, skuDetail, platPromotion, poiPromotion);
            }
            activityShareDetailDTOList.add(activityShareDetailDTO);
        }

        return activityShareDetailDTOList;
    }

    private ActivityShareDetailDTO buildActivityShareDetailDTO(ExtraPromotionAmountDetailItem promotion,
                                                               OrderProductDetailDTO skuDetail,
                                                               int channelCost, int tenantCost) {
        ActivityShareDetailDTO activityShareDetailDTO = new ActivityShareDetailDTO();
        activityShareDetailDTO.setActivityId(promotion.getPromotionType())
                .setChannelPromotionType(promotion.getPromotionType())
                .setPromotionRemark(promotion.getPromotionDesc())
                .setSkuId(skuDetail.getSkuId())
                .setCustomSpu(skuDetail.getCustomSpu())
                .setSkuCount(skuDetail.getQuantity())
                .setPromotionCount(skuDetail.getQuantity())
                .setChannelCost(channelCost)
                .setTenantCost(tenantCost)
                .setTotalOriginPrice(skuDetail.getOriginalPrice() * skuDetail.getQuantity());
        activityShareDetailDTO.setTotalActivityPrice(activityShareDetailDTO.getTotalOriginPrice() -
                activityShareDetailDTO.getChannelCost() - activityShareDetailDTO.getTenantCost());
        return activityShareDetailDTO;
    }

    private int getCostValue(ShareCost shareCost, Function<ShareCost, Long> function) {
        return Optional.ofNullable(shareCost).map(function).map(Long::intValue).orElse(0);
    }

    public MobileInfo fetchVirtualMobile(long tenantId, String orderId) {
        SupermCreateVirtualMobileRequest request = new SupermCreateVirtualMobileRequest();
        SupermCreateVirtualMobileParam param = request.getParam();
        param.setShopOrderId(Long.valueOf(orderId));
        log.info("获取隐私小号{}, req:{}", request.getUrlPath(), request);
        try {
            AccessToken token = prepareRequestConfigAndToken(request, tenantId);
            SupermCreateVirtualMobileResponse response = request.execute(token);
            if (response.isSuccess()) {
                log.info("获取订单{}的隐私小号成功,resp:{}", orderId, response);
                return response.getData().getMobileInfo();
            } else {
                log.error("获取订单{}的隐私小号失败,resp:{}", orderId, response);
            }
        } catch (Exception e) {
            log.error("获取订单{}的隐私小号异常: ", orderId, e);
        }
        return null;
    }

    @Override
    public ResultStatus preparationMealComplete(PreparationMealCompleteRequest request) {
        // 检查租户是否进行通知
        if (!MccConfigUtil.checkDouyinPreparationMealCompleteNotify(request.getTenantId())) {
            return ResultGenerator.genSuccessResult();
        }
        // 查询渠道门店id
        DouyinTenantStoreInfo tenantStoreInfo = getTenantSysParamsByTenantStoreId(request.getTenantId(),
                request.getStoreId());
        InstantShoppingNotifyPickingStatusRequest notifyPickingStatusRequest = new InstantShoppingNotifyPickingStatusRequest();
        InstantShoppingNotifyPickingStatusParam param = notifyPickingStatusRequest.getParam();
        AccessToken accessToken = prepareRequestConfigAndToken(notifyPickingStatusRequest, request.getTenantId());
        param.setStoreId(Long.parseLong(tenantStoreInfo.getChannelPoiCode()));
        param.setShopOrderId(request.getOrderId());
        // 拣货状态：目前仅支持2拣货完成
        param.setNotifyType(2L);
        // 状态更新时间戳，十位时间戳
        param.setUpdateTime(System.currentTimeMillis() / 1000);
        InstantShoppingNotifyPickingStatusResponse response = notifyPickingStatusRequest.execute(accessToken);
        log.info("订单{}请求[回传商家拣货状态]接口, resp: {}", request.getOrderId(), response);
        if (response.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }
        String tag = handleErrMsg(response);
        if (MccConfigUtil.checkPreparationMealCompleteIgnoreErrorMsg(tag)) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(response.getMsg());
    }

    private static String handleErrMsg(InstantShoppingNotifyPickingStatusResponse response) {
        String subMsg = response.getSubMsg();
        String tag = "unknown";
        if (StringUtils.isNotBlank(subMsg) && subMsg.contains(":")) {
            tag = subMsg.split(":")[0];
        } else {
            tag += subMsg;
        }
        MetricHelper.build().name("douyin.preparationMealComplete.fail").tag("subMsg", tag).count();
        return tag;
    }

    @Override
    public GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request) {
        GetOrderStatusResult result = new GetOrderStatusResult();
        OrderOrderDetailRequest orderOrderDetailRequest = new OrderOrderDetailRequest();
        OrderOrderDetailParam param = orderOrderDetailRequest.getParam();
        String orderId = request.getOrderId();
        param.setShopOrderId(orderId);
        try {
            log.info("请求订单详情{}, req: {}", orderOrderDetailRequest.getUrlPath(), orderOrderDetailRequest);
            AccessToken accessToken = prepareRequestConfigAndToken(orderOrderDetailRequest, request.getTenantId());
            OrderOrderDetailResponse response = orderOrderDetailRequest.execute(accessToken);

            log.info("请求订单详情, resp: {}", response);
            if (response.isSuccess() && response.getData() != null) {
                OrderOrderDetailData detailData = response.getData();
                ShopOrderDetail shopOrderDetail = detailData.getShopOrderDetail();
                OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
                orderStatusDTO.setOrderId(shopOrderDetail.getOrderId());
                orderStatusDTO.setStatus(parseChannelStatusToChannelOrderStatus(shopOrderDetail.getOrderStatus()));
                return result.setStatus(ResultGenerator.genSuccessResult()).setOrderStatus(orderStatusDTO);
            }
        } catch (Exception e) {
            log.error("调用渠道获取订单{},租户id: {}, 详情失败: ", request.getOrderId(), request.getTenantId(), e);
        }
        return result.setStatus(ResultGenerator.genResult(ResultCode.DY_QUERY_ORDER_STATUS_ERROR));
    }

    @Override
    public GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request) {
        GetOrderAfsApplyListResult ret = new GetOrderAfsApplyListResult();
        AfterSaleDetailRequest dyRequest = new AfterSaleDetailRequest();
        AfterSaleDetailParam param = dyRequest.getParam();
        param.setAfterSaleId(request.getAfterSaleId());
        param.setNeedOperationRecord(true);
        dyRequest.setParam(param);
        //获取请求 token
        AccessToken accessToken = prepareRequestConfigAndToken(dyRequest, request.getTenantId());
        AfterSaleDetailResponse response = dyRequest.execute(accessToken);
        if (response == null || !response.isSuccess() || response.getData() == null) {
            log.error("查询抖音渠道退款信息失败，request：{},response:{}", JSON.toJSONString(request),JSON.toJSONString(response));
            return ret.setStatus(ResultGenerator.genResult(ResultCode.AFTERSALE_NOT_EXIST));
        }
        log.info("查询抖音渠道退款信息成功，request：{},response:{}", JSON.toJSONString(request),JSON.toJSONString(response));
        OrderInfo orderInfo = response.getData().getOrderInfo();
        ProcessInfo processInfo = response.getData().getProcessInfo();
        AfterSaleInfo afterSaleInfo = response.getData().getProcessInfo().getAfterSaleInfo();
        if (orderInfo == null || processInfo == null || afterSaleInfo == null) {
            log.error("查询抖音渠道退款信息失败,内容为 null，request：{},response:{}", JSON.toJSONString(request),JSON.toJSONString(response));
            return ret.setStatus(ResultGenerator.genResult(ResultCode.AFTERSALE_NOT_EXIST));
        }
        //过滤退单类型
        if (afterSaleInfo.getAfterSaleType() == DouyinAfterSaleTypeEnum.EXCHANGE_GOODS.getCode() || afterSaleInfo.getAfterSaleType() > DouyinAfterSaleTypeEnum.SYSTEM_CANCEL.getCode()) {
            log.info("不支持的售后类型:{}, 售后单号: {}, 租户: {}", afterSaleInfo.getAfterSaleType(), afterSaleInfo.getAfterSaleId(), request.getTenantId());
            return ret.setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE));
        }
        //参数转化
        OrderAfsApplyDTO dto = DouyinOrderConverterUtil.convertAfsApplyDTO(response.getData());

        ret.setAfsApplyList(Lists.newArrayList(dto)).setStatus(ResultGenerator.genSuccessResult());
        return ret;
    }

    @Override
    public ResultStatus agreeRefund(AgreeRefundRequest request) {
        AfterSaleOperateRequest saleOperateRequest = new AfterSaleOperateRequest();
        AfterSaleOperateParam param = saleOperateRequest.getParam();
        // 201:同意仅退款
        param.setType(DouyinAfterSaleOperateType.AGREE_ONLY_REFUND.getType());
        List<ItemsItem> items = new ArrayList<>();
        ItemsItem item = new ItemsItem();
        item.setAftersaleId(request.getAfterSaleId());
        items.add(item);
        param.setItems(items);
        AccessToken token = prepareRequestConfigAndToken(saleOperateRequest, request.getTenantId());
        AfterSaleOperateResponse response = saleOperateRequest.execute(token);
        log.info("退单号{}同意仅退款请求返回结果:{}", request.getAfterSaleId(), response);
        if (response.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        } else {
            return ResultGenerator.genFailResult(response.getMsg());
        }
    }

    @Override
    public ResultStatus rejectRefund(RejectRefundRequest request) {
        AfterSaleOperateRequest saleOperateRequest = new AfterSaleOperateRequest();
        AfterSaleOperateParam param = saleOperateRequest.getParam();
        // 202:拒绝仅退款
        param.setType(DouyinAfterSaleOperateType.REJECT_ONLY_REFUND.getType());
        ItemsItem item = new ItemsItem();
        item.setAftersaleId(request.getAfterSaleId());
        item.setReason(request.getReason());
        item.setRejectReasonCode(Long.valueOf(request.getReasonCode()));
        if (CollectionUtils.isNotEmpty(request.getEvidenceDTOList())) {
            List<EvidenceItem> evidenceItems = request.getEvidenceDTOList().stream().map(itm -> {
                EvidenceItem evidenceItem = new EvidenceItem();
                evidenceItem.setType(itm.getType());
                evidenceItem.setUrl(itm.getUrl());
                evidenceItem.setDesc(itm.getDesc());
                return evidenceItem;
            }).collect(Collectors.toList());
            item.setEvidence(evidenceItems);
        }
        List<ItemsItem> items = new ArrayList<>();
        items.add(item);
        param.setItems(items);
        AccessToken token = prepareRequestConfigAndToken(saleOperateRequest, request.getTenantId());
        AfterSaleOperateResponse response = saleOperateRequest.execute(token);
        log.info("退单号{}拒绝仅退款请求返回结果:{}", request.getAfterSaleId(), response);
        if (response.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        } else {
            return ResultGenerator.genFailResult(response.getMsg());
        }
    }

    @Override
    public ResultStatus refundGoods(RefundGoodsRequest request) {
        DouyinConfigDto requestConfig = getRequestConfigAndToken(request.getTenantId());
        AfterSaleOperateRequest saleOperateRequest = new AfterSaleOperateRequest();
        saleOperateRequest.setConfig(requestConfig.getDoudianOpConfig());
        AfterSaleOperateParam param = saleOperateRequest.getParam();
        ItemsItem item = new ItemsItem();
        item.setAftersaleId(request.getAfterSaleId());
        AfsReviewTypeEnum reviewType = AfsReviewTypeEnum.enumOf(request.getReviewType());
        if (reviewType == null) {
            throw new BizException(ResultCode.INVALID_PARAM.getCode(), "未知的退货退款类型");
        }
        switch (reviewType) {
            case AGREE_REFUND_GOODS:
                // 一审通过 101:同意退货申请（一次审核）
                param.setType(DouyinAfterSaleOperateType.AGREE_RETURN_GOODS_FIRST_REVIEW.getType());
                // 获取门店地址id
                DouyinTenantStoreInfo tenantStoreInfo = getTenantSysParamsByTenantStoreId(request.getTenantId(),
                        request.getStoreId());
                try {
                    List<AddressListItem> addressList = douyinRetryChannelService.getAddressList(requestConfig,
                            Long.valueOf(tenantStoreInfo.getChannelPoiCode()));
                    if (CollectionUtils.isNotEmpty(addressList)) {
                        AddressListItem addressListItem = addressList.get(0);
                        Logistics logistics = new Logistics();
                        logistics.setReceiverAddressId(addressListItem.getAddressId());
                        item.setLogistics(logistics);
                    }
                } catch (Exception e) {
                    log.error("获取门店地址异常", e);
                }
                break;
            case AGREE_REFUND:
                // 二审通过 111:同意退货（二次审核）
                param.setType(DouyinAfterSaleOperateType.AGREE_RETURN_GOODS_SECOND_REVIEW.getType());
                break;
            case REJECT_REFUND:
                if (request.getAuditStage() == AfsAuditStageEnum.FIRST_AUDIT.getValue()) {
                    // 一审驳回 102:拒绝退货申请（一次审核）
                    param.setType(DouyinAfterSaleOperateType.REJECT_RETURN_GOODS_FIRST_REVIEW.getType());
                }
                if (request.getAuditStage() == AfsAuditStageEnum.FINAL_AUDIT.getValue()) {
                    // 二审驳回 112:拒绝退货 (二次审核)
                    param.setType(DouyinAfterSaleOperateType.REJECT_RETURN_GOODS_SECOND_REVIEW.getType());
                }
                item.setReason(request.getReason());
                item.setRejectReasonCode(Long.valueOf(request.getReasonCode()));
                // 设置凭据
                if (CollectionUtils.isNotEmpty(request.getEvidenceDTOList())) {
                    List<EvidenceItem> evidenceItems = request.getEvidenceDTOList().stream().map(itm -> {
                        EvidenceItem evidenceItem = new EvidenceItem();
                        evidenceItem.setType(itm.getType());
                        evidenceItem.setUrl(itm.getUrl());
                        evidenceItem.setDesc(itm.getDesc());
                        return evidenceItem;
                    }).collect(Collectors.toList());
                    item.setEvidence(evidenceItems);
                }
                break;
            default:
                throw new BizException(ResultCode.INVALID_PARAM.getCode(), "未知的退货退款类型");
        }
        List<ItemsItem> items = new ArrayList<>();
        items.add(item);
        param.setItems(items);
        AfterSaleOperateResponse response = saleOperateRequest.execute(requestConfig.getAccessToken());
        log.info("退单号{}退货退款审核[{}]请求返回结果:{}", request.getAfterSaleId(),
                DouyinAfterSaleOperateType.findDesc(param.getType()), response);
        if (response.isSuccess()) {
            // 抖音退货流程修改，return_method为5,7商家自有运力需请求该接口
            if (reviewType == AfsReviewTypeEnum.AGREE_REFUND_GOODS) {
                // 一审通过，需要调用抖店开放平台预约上门取货 prd https://km.sankuai.com/collabpage/1377730053
                if (!fillAfterSaleLogistics(request.getAfterSaleId(), requestConfig, null)) {
                    return ResultGenerator.genFailResult("售后商家填写物流信息失败");
                }
            }
            return ResultGenerator.genSuccessResult();
        } else {
            return ResultGenerator.genFailResult(response.getMsg());
        }
    }

    /**
     * 填写商家售后物流信息，并失败后重试
     * @param afterSaleId
     * @param requestConfig
     * @param storeId
     * @return
     */
    private boolean fillAfterSaleLogistics(String afterSaleId, DouyinConfigDto requestConfig, Long storeId) {
        AfterSaleFillLogisticsRequest request = new AfterSaleFillLogisticsRequest();
        request.setConfig(requestConfig.getDoudianOpConfig());
        AfterSaleFillLogisticsParam param = request.getParam();
        param.setAftersaleId(Long.valueOf(afterSaleId));
        // 3：超市预约上门取货
        param.setSendType(3);
        param.setCompanyCode("-");
        param.setTrackingNo("-");
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime begin = now.plusDays(1).withHourOfDay(17).withMinuteOfHour(0).withSecondOfMinute(0);
        LocalDateTime end = now.plusDays(1).withHourOfDay(18).withMinuteOfHour(0).withSecondOfMinute(0);
        param.setBookTimeBegin(begin.toDate().getTime() / 1000L);
        param.setBookTimeEnd(end.toDate().getTime() / 1000L);
        param.setStoreId(storeId);
        // 2:线下取货
        param.setPickUpType(2);
        return douyinRetryChannelService.fillAfterSaleLogistics(request, requestConfig.getAccessToken());
    }

    @Override
    public ResultStatus poiCancelOrder(PoiCancelOrderRequest request) {
        DouyinConfigDto requestConfig = getRequestConfigAndToken(request.getTenantId());
        List<String> refundFailSkuNameList = new ArrayList<>();
        List<String> refundFailMsgList = new ArrayList<>();
        for (RefundProductInfoDTO refundProductInfoDTO : request.getRefundProducts()) {
            AfterSaleApplyMarketAfterSaleRequest saleRequest = new AfterSaleApplyMarketAfterSaleRequest();
            saleRequest.setConfig(requestConfig.getDoudianOpConfig());
            AfterSaleApplyMarketAfterSaleParam saleParam = saleRequest.getParam();
            // 售后原因枚举 MissItem = 2缺货，只支持整单退； RefuseSign = 3 拒收，支持按件数退，金额由抖音计算
            saleParam.setAfterSaleReason(request.getReason_code());
            saleParam.setSkuOrderId(Long.valueOf(refundProductInfoDTO.getChannelOrderItemId()));
            if (request.getReason_code() == DouyinAfterSaleReason.RefuseSign.getReason()) {
                saleParam.setItemCount((long)refundProductInfoDTO.getCount());
            }
            try {
                AfterSaleApplyMarketAfterSaleResponse response = saleRequest.execute(requestConfig.getAccessToken());
                log.info("订单号[{}]订单商品名称[{}]整单取消请求渠道接口response:{}", request.getOrderId(),
                        refundProductInfoDTO.getSkuName(), response);
                if (!response.isSuccess()) {
                    refundFailMsgList.add(Optional.ofNullable(response.getMsg()).orElse("")
                            + Optional.ofNullable(response.getSubMsg()).orElse(""));
                    refundFailSkuNameList.add(refundProductInfoDTO.getSkuName());
                }
            } catch (Exception e) {
                log.error("订单号[{}]订单商品名称[{}]整单取消请求渠道接口异常", request.getOrderId(), refundProductInfoDTO.getSkuName(), e);
                refundFailSkuNameList.add(refundProductInfoDTO.getSkuName());
            }
        }
        if (CollectionUtils.isNotEmpty(refundFailSkuNameList)) {
            // 渠道错误的话，直接返回渠道报错信息
            if(CollectionUtils.isNotEmpty(refundFailMsgList)){
                return ResultGenerator.genFailResult(refundFailMsgList.get(0));
            } else {
                boolean isPartError = refundFailSkuNameList.size() != request.getRefundProducts().size();
                return ResultGenerator.genFailResult(String.format(isPartError ? "部分商品退款失败：%s" : "%s发起退款失败",
                        refundFailSkuNameList.stream().map(s -> "[" + s + "]").collect(Collectors.joining(""))));
            }
        }
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public ResultStatus poiPartRefundApply(PoiPartRefundRequest request) {
        DouyinConfigDto requestConfig = getRequestConfigAndToken(request.getTenantId());
        List<String> refundFailSkuNameList = new ArrayList<>();
        List<String> refundFailMsgList = new ArrayList<>();
        for (RefundProductInfoDTO refundProductInfoDTO : request.getRefundProducts()) {
            AfterSaleApplyMarketAfterSaleRequest saleRequest = new AfterSaleApplyMarketAfterSaleRequest();
            saleRequest.setConfig(requestConfig.getDoudianOpConfig());
            AfterSaleApplyMarketAfterSaleParam saleParam = saleRequest.getParam();
            // 售后原因枚举 MissItem = 2缺货，发货前部分退； RefuseSign = 3 拒收，支持按件数退，金额由抖音计算
            saleParam.setAfterSaleReason(request.getReasonCode());
            saleParam.setSkuOrderId(Long.valueOf(refundProductInfoDTO.getChannelOrderItemId()));
            if (refundProductInfoDTO.getCount() > 0) {
                saleParam.setItemCount((long)refundProductInfoDTO.getCount());
            }
            try {
                AfterSaleApplyMarketAfterSaleResponse response = saleRequest.execute(requestConfig.getAccessToken());
                log.info("订单号[{}]订单商品名称[{}]部分退请求渠道接口response:{}", request.getOrderId(),
                        refundProductInfoDTO.getSkuName(), response);
                if (!response.isSuccess()) {
                    refundFailMsgList.add(Optional.ofNullable(response.getMsg()).orElse("")
                            + Optional.ofNullable(response.getSubMsg()).orElse(""));
                    refundFailSkuNameList.add(refundProductInfoDTO.getSkuName());
                }
            } catch (Exception e) {
                log.error("订单号[{}]订单商品名称[{}]部分退请求渠道接口异常", request.getOrderId(), refundProductInfoDTO.getSkuName(), e);
                refundFailSkuNameList.add(refundProductInfoDTO.getSkuName());
            }
        }
        if (CollectionUtils.isNotEmpty(refundFailSkuNameList)) {
            // 渠道错误的话，直接返回渠道报错信息
            if(CollectionUtils.isNotEmpty(refundFailMsgList)){
                return ResultGenerator.genFailResult(refundFailMsgList.get(0));
            } else {
                boolean isPartError = refundFailSkuNameList.size() != request.getRefundProducts().size();
                return ResultGenerator.genFailResult(String.format(isPartError ? "部分商品退款失败：%s" : "%s发起退款失败",
                        refundFailSkuNameList.stream().map(s -> "[" + s + "]").collect(Collectors.joining(""))));
            }
        }
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request) {
        return null;
    }

    @Override
    public ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request) {
        return null;
    }

    @Override
    public PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request) {
        return null;
    }

    @Override
    public OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request) {
        return null;
    }

    @Override
    public GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request) {
        return null;
    }

    @Override
    public GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request) {
        ResultStatus resultStatus = ResultGenerator.genResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE);
        return new GetLogisticsStatusResult().setStatus(resultStatus);
    }

    @Override
    public ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request) {
        //渠道无对应接口，直接返回成功
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ResultCode.SUCCESS.getCode());
        resultStatus.setMsg(ResultCode.SUCCESS.getMsg());
        return resultStatus;
    }

    @Override
    public ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request) {
        // 调用/instantShopping/notifyDeliveryStatus上报配送信息
        InstantShoppingNotifyDeliveryStatusRequest channelRequest = new InstantShoppingNotifyDeliveryStatusRequest();
        InstantShoppingNotifyDeliveryStatusParam param = channelRequest.getParam();
        param.setShopOrderId(request.getOrderId());
        param.setDistributionCode(DISTRIBUTION_CODE_MERCHANT);
        param.setDistributionDeliveryId(request.getDeliveryOrderId());
        param.setRiderName(request.getRiderName());
        // 骑手手机号类型，0是真实号，1是隐私号
        param.setRiderPhoneType(0);
        param.setRiderPhone(request.getRiderPhone());
        param.setRiderLongitude(String.valueOf(request.getLongitude()));
        param.setRiderLatitude(String.valueOf(request.getLatitude()));

        long now = System.currentTimeMillis() / 1000;
        param.setUpdateTime(now);
        param.setReportTime(now);

        int status = request.getStatus();
        DeliveryStatus deliveryStatus = DeliveryStatus.findByValue(request.getStatus());
        if (deliveryStatus == null) {
            // TODO 返回值是否要修改下
            log.warn("订单:{}，配送状态: {}，无需上报", request.getOrderId(), status);
            return ResultGenerator.genSuccessResult();
        }
        DouyinChannelDeliveryStatusEnum channelDeliveryStatus = null;
        switch (deliveryStatus) {
            case WAIT_DELIVERY:
            case NEW_DELIVERY_ORDER:
            case APPLY_DELIVERY:
            case WAIT_DISPATCH_RIDER:
                channelDeliveryStatus = DouyinChannelDeliveryStatusEnum.WAIT_RIDER_ACCEPT;
                break;
            case RIDER_ACCEPTED_ORDER:
                channelDeliveryStatus = DouyinChannelDeliveryStatusEnum.RIDER_ACCEPTED_ORDER;
                break;
            case RIDER_ARRIVE_SHOP:
                channelDeliveryStatus = DouyinChannelDeliveryStatusEnum.RIDER_ARRIVE_SHOP;
                break;
            case RIDER_TAKEN_MEAL:
                channelDeliveryStatus = DouyinChannelDeliveryStatusEnum.RIDER_TAKEN_MEAL;
                break;
            case DELIVERY_COMPLETED:
                channelDeliveryStatus = DouyinChannelDeliveryStatusEnum.DELIVERY_COMPLETED;
                break;
            case DELIVERY_CANCEL:
                channelDeliveryStatus = DouyinChannelDeliveryStatusEnum.DELIVERY_CANCEL;
                break;
            default:
                log.warn("抖音暂不支持的配送状态: {}", deliveryStatus.name());
        }
        if (channelDeliveryStatus == null || channelDeliveryStatus == DouyinChannelDeliveryStatusEnum.WAIT_RIDER_ACCEPT) {
            // 没有配送状态，或者待骑手接单状态，不上报
            log.warn("订单:{}，配送状态: {}，无需上报", request.getOrderId(), deliveryStatus.name());
            return ResultGenerator.genSuccessResult();
        }
        param.setStatus(channelDeliveryStatus.getCode());
        if (channelDeliveryStatus == DouyinChannelDeliveryStatusEnum.DELIVERY_CANCEL) {
            setCancelReason(request, param);
        }
        try {
            AccessToken token = prepareRequestConfigAndToken(channelRequest, request.getTenantId());
            log.info("调用上报配送状态接口{}, req: {}", channelRequest.getUrlPath(), param);
            InstantShoppingNotifyDeliveryStatusResponse response = channelRequest.execute(token);
            log.info("调用上报配送状态接口{}, resp: {}", channelRequest.getUrlPath(), response);
            if (response.isSuccess()) {
                return ResultGenerator.genSuccessResult();
            } else {
                // 查不到运力订单，调用一下发货接口
                if (StringUtils.equals(response.getSubCode(), "isv.business-failed:20111")) {
                    ResultStatus resultStatus = addLogistics(request, token, channelRequest.getConfig());
                    // 发货了，再次调用同步接口
                    if (resultStatus.getCode() == ResultCode.SUCCESS.getCode()) {
                        log.info("再次调用上报配送状态接口{}, req: {}", channelRequest.getUrlPath(), param);
                        response = channelRequest.execute(token);
                        log.info("再次调用上报配送状态接口{}, resp: {}", channelRequest.getUrlPath(), response);
                        if (response.isSuccess()) {
                            return ResultGenerator.genSuccessResult();
                        }
                    }
                } else {
                    return ResultGenerator.genResult(DY_NOTIFY_DELIVERY_STATUS_ERROR, response.getSubMsg());
                }
            }
        } catch (Exception e) {
            log.error("调用{}, param: {}, 异常: ", channelRequest.getUrlPath(), param, e);
        }
        return  ResultGenerator.genResult(DY_NOTIFY_DELIVERY_STATUS_ERROR);
    }

    private void setCancelReason(UpdateDeliveryInfoRequest request, InstantShoppingNotifyDeliveryStatusParam param) {
        int cancelReasonCode = request.getCancelReasonCode();
        DeliveryCancelTypeEnum deliveryCancelTypeEnum = DeliveryCancelTypeEnum.enumOf(cancelReasonCode);
        DouyinChannelDeliveryCancelTypeEnum channelCancelType = null;
        if (deliveryCancelTypeEnum == null) {
            channelCancelType = DouyinChannelDeliveryCancelTypeEnum.OTHER_REASON;
            param.setCancelCode(channelCancelType.getCode());
            param.setCancelReason(channelCancelType.getDesc());
            return;
        }
        switch (deliveryCancelTypeEnum) {
            case CLIENT_CANCEL:
            case CLIENT_CANCEL_FOR_OTHERS:
                channelCancelType = DouyinChannelDeliveryCancelTypeEnum.OTHER_REASON_CANCEL;
                break;
            case SYSTEM_CANCEL_FOR_CLIENT:
                channelCancelType = DouyinChannelDeliveryCancelTypeEnum.USER_CANCEL_FOR_SELF_REASON;
                break;
            default:
                channelCancelType = DouyinChannelDeliveryCancelTypeEnum.OTHER_REASON;
        }
        param.setCancelCode(channelCancelType.getCode());
        param.setCancelReason(channelCancelType.getDesc());
    }

    private ResultStatus addLogistics(UpdateDeliveryInfoRequest updateDeliveryInfoRequest, AccessToken token, DoudianOpConfig config) {

        OrderLogisticsAddRequest request = new OrderLogisticsAddRequest();
        request.setConfig(config);
        OrderLogisticsAddParam param = request.getParam();
        param.setOrderId(updateDeliveryInfoRequest.getOrderId());
        param.setLogisticsCode(updateDeliveryInfoRequest.getDeliveryOrderId());
        DouyinTenantStoreInfo tenantStoreInfo = getTenantSysParamsByTenantStoreId(updateDeliveryInfoRequest.getTenantId(),
                updateDeliveryInfoRequest.getShopId());
        param.setStoreId(Long.parseLong(tenantStoreInfo.getChannelPoiCode()));

        param.setCompanyCode(DISTRIBUTION_CODE_MERCHANT);
        param.setIsRefundReject(true);

        try {
            log.info("调用发配送接口{}, req: {}", request.getUrlPath(), param);
            OrderLogisticsAddResponse response = request.execute(token);
            log.info("调用发配送接口{}, resp: {}", request.getUrlPath(), response);
            if (response.isSuccess()) {
                return ResultGenerator.genSuccessResult();
            } else {
                // 没有能发货的订单，请修改后重试；这个是由于重新发配送，配送单不一样导致的，需要调用logistics/edit接口先修改下配送单号
                if (StringUtils.equals(response.getSubCode(),"isv.business-failed:70023")) {
                    return editLogistics(updateDeliveryInfoRequest, token, config);
                } else {
                    return ResultGenerator.genResult(DY_ADD_LOGISTICS_ERROR, response.getSubMsg());
                }
            }
        } catch (Exception e) {
            log.error("调用{}, param: {}, 异常: ", request.getUrlPath(), param, e);
        }
        return ResultGenerator.genResult(DY_ADD_LOGISTICS_ERROR);
    }

    /**
     * 查询渠道门店信息
     *
     * @param tenantId
     * @param shopId
     * @return
     */
    private DouyinTenantStoreInfo getTenantSysParamsByTenantStoreId(long tenantId, long shopId) {
        DouyinTenantStoreInfo tenantStoreInfo = null;
        try {
            tenantStoreInfo = douyinChannelCommonService.getTenantSysParamsByTenantStoreId(tenantId, shopId);
            log.info("查询渠道门店成功，orderId: {}, tenantStoreInfo: {}", shopId, JsonUtil.toJson(tenantStoreInfo));
        } catch (Exception e) {
            log.error("查询渠道门店失败，tenantId: {}, storeId: {}", tenantId, shopId, e);
        }
        if (tenantStoreInfo == null || StringUtils.isBlank(tenantStoreInfo.getChannelPoiCode())) {
            log.info("查询渠道门店为空，tenantId: {}, storeId: {}", tenantId, shopId);
            throw new BizException(ResultCode.INVALID_PARAM.getCode(), "获取抖音租户系统参数为空");
        }
        return tenantStoreInfo;
    }

    private ResultStatus editLogistics(UpdateDeliveryInfoRequest updateDeliveryInfoRequest, AccessToken token, DoudianOpConfig config) {
        OrderLogisticsEditRequest request = new OrderLogisticsEditRequest();
        request.setConfig(config);
        OrderLogisticsEditParam param = request.getParam();

        param.setOrderId(updateDeliveryInfoRequest.getOrderId());
        param.setLogisticsCode(updateDeliveryInfoRequest.getDeliveryOrderId());
        DouyinTenantStoreInfo tenantStoreInfo = getTenantSysParamsByTenantStoreId(updateDeliveryInfoRequest.getTenantId(),
                updateDeliveryInfoRequest.getShopId());
        param.setStoreId(Long.parseLong(tenantStoreInfo.getChannelPoiCode()));

        param.setCompanyCode(DISTRIBUTION_CODE_MERCHANT);

        try {
            log.info("调用修改配送接口{}, req: {}", request.getUrlPath(), param);
            OrderLogisticsEditResponse response = request.execute(token);
            log.info("调用修改配送接口{}, req: {}", response, param);
            if (response.isSuccess()) {
                return ResultGenerator.genSuccessResult();
            } else {
                return ResultGenerator.genResult(DY_EDIT_LOGISTICS_ERROR, response.getSubMsg());
            }
        } catch (Exception e) {
            log.error("调用{}, param: {}, 异常: ",request.getUrlPath(), param, e);
        }
        return ResultGenerator.genResult(DY_EDIT_LOGISTICS_ERROR);
    }

    @Override
    public ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request) {
        try {
            InstantShoppingReportRiderLocationRequest req = new InstantShoppingReportRiderLocationRequest();
            InstantShoppingReportRiderLocationParam param = req.getParam();
            param.setShopOrderId(request.getOrderId());
            param.setDistributionCode(DISTRIBUTION_CODE_MERCHANT);
            param.setDistributionDeliveryId(request.deliveryOrderId);
            param.setRiderName(request.getRiderName());
            param.setRiderPhoneType(REAL_RIDER_PHONE_TYPE_CODE);
            param.setRiderPhone(request.getRiderPhone());
            param.setRiderLongitude(String.valueOf(request.getLongitude()));
            param.setRiderLatitude(String.valueOf(request.getLatitude()));
            param.setReportTime(TimeUtil.toSeconds(java.time.LocalDateTime.now()));
            AccessToken accessToken = prepareRequestConfigAndToken(req, request.getTenantId());

            log.info("调用抖音上报骑手信息接口{}, request: {}", req.getUrlPath(), param);
            InstantShoppingReportRiderLocationResponse response = req.execute(accessToken);
            log.info("调用抖音上报骑手信息接口{}, response: {}", req.getUrlPath(), response);
            if (Objects.isNull(response) || !response.isSuccess()) {
                log.error("DouyinChannelOrderServiceImpl updateRiderInfo failed, response is {}", response);
                return ResultGenerator.genResult(DY_REPORT_RIDER_LOCATION_ERROR);
            } else {
                return ResultGenerator.genSuccessResult();
            }
        } catch (Exception e) {
            log.error("DouyinChannelOrderServiceImpl updateRiderInfo exception", e);
            return ResultGenerator.genResult(DY_REPORT_RIDER_LOCATION_ERROR);
        }
    }

    @Override
    public ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request) {
        return new ResultStatus(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(), ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg(), null);
    }

    @Override
    public ResultStatus selfDelivery(SelfDeliveryRequest request) {
        try {
            InstantShoppingCancelDeliveryRequest req = new InstantShoppingCancelDeliveryRequest();
            InstantShoppingCancelDeliveryParam param = req.getParam();
            param.setShopOrderId(request.getOrderId());
            param.setStoreId(Long.valueOf(request.getChannelStoreId()));
            param.setCancelCode(CANCEL_DELIVERY_OTHER_REASON_CODE);
            if (request.getIsQnhManagement() == DeliveryOrderSourceEnum.NOT_QNH_MANAGEMENT.getCode()) {
                param.setCancelCode(CANCEL_DELIVERY_REASON_CODE);
            }

            AccessToken accessToken = prepareRequestConfigAndToken(req, request.getTenantId());
            log.info("DouyinChannelOrderServiceImpl selfDelivery request is {}", req);
            InstantShoppingCancelDeliveryResponse response = req.execute(accessToken);
            log.info("DouyinChannelOrderServiceImpl selfDelivery response is {}", response);
            if (Objects.isNull(response)) {
                log.error("DouyinChannelOrderServiceImpl selfDelivery failed, response为空");
                return ResultGenerator.genResult(DY_SELF_DELIVERY_ERROR);
            } else if (! response.isSuccess()) {
                log.error("DouyinChannelOrderServiceImpl selfDelivery failed, response:{}", response);
                return ResultGenerator.genFailResult(response.getSubMsg());
            } else {
                return ResultGenerator.genSuccessResult();
            }
        } catch (Exception e) {
            log.error("DouyinChannelOrderServiceImpl selfDelivery exception", e);
            return ResultGenerator.genResult(DY_SELF_DELIVERY_ERROR);
        }
    }

    @Override
    public QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request) {
        return null;
    }

    @Override
    public ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request) {
        return null;
    }

    @Override
    public QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request) {
        QueryChannelOrderListResult result = new QueryChannelOrderListResult();
//        DouyinConfigDto requestConfig = getRequestConfigAndToken(request.getTenantId());
        OrderSearchListRequest orderSearchListRequest = new OrderSearchListRequest();
        OrderSearchListParam param = orderSearchListRequest.getParam();
        //【下单端】 0、站外 1、火山 2、抖音 3、头条 4、西瓜 5、微信 6、值点app 7、头条lite 8、懂车帝 9、皮皮虾 11、抖音极速版 12、TikTok 13、musically 14、穿山甲 15、火山极速版 16、服务市场 26、番茄小说 27、UG教育营销电商平台 28、Jumanji 29、电商SDK
//        param.setBType(2L);
        //预售类型：0-普通订单；1-全款预售；2-定金预售；3-定金找货；
//        param.setPresellType(0L);
        //订单类型 0、普通订单 2、虚拟商品订单 4、电子券（poi核销） 5、三方核销
//        param.setOrderType(0L);
        param.setCreateTimeStart(request.getStartTime());
        param.setCreateTimeEnd(request.getEndTime());
        //交易类型：0-普通；1-拼团；2-定金预售；3-订金找货；4-拍卖；5-0元单；6-回收；7-寄卖；10-寄样；11-0元抽奖(超级福袋)；12-达人买样；13-普通定制；16-大众竞拍；18-小时达；102-定金预售的赠品单；103-收款；
//        param.setTradeType(0L);
        param.setSize((long) request.getPageSize());
        param.setPage((long) request.getPage() - 1);
        param.setOrderBy("create_time");
        OrderSearchListResponse response = new OrderSearchListResponse();
        AccessToken accessToken = prepareRequestConfigAndToken(orderSearchListRequest, request.getTenantId());
        try {
            log.info("抖音 DouyinChannelOrderServiceImpl-queryChannelOrderList request:{}", JSON.toJSONString(param));
            response = orderSearchListRequest.execute(accessToken);
            log.info("抖音 DouyinChannelOrderServiceImpl-queryChannelOrderList response:{}", JSON.toJSONString(response));

        } catch (Exception e) {
            log.error("抖音 DouyinChannelOrderServiceImpl-queryChannelOrderList request:{}", JSON.toJSONString(param), e);
            return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单列表失败"));
        }
        Map<String, Long> channelOrderMap = Maps.newHashMap();
        OrderSearchListData data = response.getData();
        if (response.isSuccess() && data != null && CollectionUtils.isNotEmpty(data.getShopOrderList())) {
            List<ShopOrderListItem> orderList = data.getShopOrderList();
            List<ShopOrderListItem> shopOrderList = orderList.stream().filter(shopOrderListItem -> shopOrderListItem.getPayTime() != null && shopOrderListItem.getPayTime() != 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shopOrderList)) {
                result.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));
                result.setTotal(response.getData().getTotal().intValue());
                long currentCount = (response.getData().getPage() + 1) * request.getPageSize();
                if (currentCount > response.getData().getTotal()) {
                    result.setHasMore(0);
                } else {
                    result.setHasMore(1);
                }
                return result;
            }
            for (ShopOrderListItem shopOrderListItem : shopOrderList) {
                List<com.doudian.open.api.order_searchList.data.SkuOrderListItem> skuOrderList = shopOrderListItem.getSkuOrderList();

                if (CollectionUtils.isEmpty(skuOrderList)) {
                    log.error("渠道订单{}商品数据为空", shopOrderListItem.getOrderId());
                    continue;

                }
                // 获取渠道门店号，用来获取中台门店号
                com.doudian.open.api.order_searchList.data.StoreInfo storeInfo = skuOrderList.get(0).getStoreInfo();
                if (storeInfo == null) {
                    log.error("渠道订单{}商品所属门店为空", shopOrderListItem.getOrderId());
                    continue;
                }
                DouyinTenantStoreInfo tenantStoreInfo = null;
                try {
                    // 获取中台门店信息
                    tenantStoreInfo = douyinChannelCommonService
                            .getTenantSysParamsByTenantChannelPoiCode(request.getTenantId(), storeInfo.getStoreId());
                } catch (Exception e) {
                    log.error("获取抖音门店: {} 对应的中台门店失败，tenantId: {}", storeInfo.getStoreId(), request.getTenantId());
                }
                if (tenantStoreInfo == null) {
                    log.error("获取抖音门店: {} 对应的中台门店失败，tenantId: {}", storeInfo.getStoreId(), request.getTenantId());
                    continue;
                }

                channelOrderMap.put(shopOrderListItem.getOrderId(),tenantStoreInfo.getStoreId());
            }
            result.setChannelOrderIdMap(channelOrderMap);
            result.setStatus(ResultGenerator.genResult(ResultCode.SUCCESS));
            result.setTotal(response.getData().getTotal().intValue());
            long currentCount = (response.getData().getPage() + 1) * request.getPageSize();
            if (currentCount > response.getData().getTotal()) {
                result.setHasMore(0);
            } else {
                result.setHasMore(1);
            }
            return result;

        }
        return result.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道获取订单列表失败"));
    }

    @Override
    public QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request) {
        return null;
    }

    @Override
    public ResultStatus moneyRefund(MoneyRefundRequest request) {
        DouyinConfigDto requestConfig = getRequestConfigAndToken(request.getTenantId());
        List<String> refundFailSkuNameList = new ArrayList<>();
        List<String> refundFailMsgList = new ArrayList<>();
        // 抖音只能一次退一件商品
        for (MoneyRefundProductInfoDTO moneyRefundProductInfoDTO : request.getRefundProductInfoList()) {
            AfterSaleApplyMarketAfterSaleRequest saleRequest = new AfterSaleApplyMarketAfterSaleRequest();
            saleRequest.setConfig(requestConfig.getDoudianOpConfig());
            AfterSaleApplyMarketAfterSaleParam saleParam = saleRequest.getParam();
            // 售后原因枚举 MissGram = 1缺重
            saleParam.setAfterSaleReason(DouyinAfterSaleReason.MissGram.getReason());
            saleParam.setSkuOrderId(Long.valueOf(moneyRefundProductInfoDTO.getChannelOrderItemId()));
            saleParam.setRefundAmount((long)moneyRefundProductInfoDTO.getRefundAmount());
            saleParam.setMissGram(moneyRefundProductInfoDTO.getApplyWeight());
            try {
                AfterSaleApplyMarketAfterSaleResponse response = saleRequest.execute(requestConfig.getAccessToken());
                log.info("订单号[{}]订单商品名称[{}]金额退请求渠道接口response:{}", request.getOrderId(),
                        moneyRefundProductInfoDTO.getSkuName(), response);
                if (!response.isSuccess()) {
                    refundFailMsgList.add(Optional.ofNullable(response.getMsg()).orElse("")
                            + Optional.ofNullable(response.getSubMsg()).orElse(""));
                    refundFailSkuNameList.add(moneyRefundProductInfoDTO.getSkuName());
                }
            } catch (Exception e) {
                log.error("订单号[{}]订单商品名称[{}]金额退请求渠道接口异常", request.getOrderId(), moneyRefundProductInfoDTO.getSkuName(),
                        e);
                refundFailSkuNameList.add(moneyRefundProductInfoDTO.getSkuName());
            }
        }
        if (CollectionUtils.isNotEmpty(refundFailSkuNameList)) {
            // 渠道错误的话，直接返回渠道报错信息
            if(CollectionUtils.isNotEmpty(refundFailMsgList)){
                return ResultGenerator.genFailResult(refundFailMsgList.get(0));
            } else {
                boolean isPartError = refundFailSkuNameList.size() != request.getRefundProductInfoList().size();
                return ResultGenerator.genFailResult(String.format(isPartError ? "部分商品退款失败：%s" : "%s发起退款失败",
                        refundFailSkuNameList.stream().map(s -> "[" + s + "]").collect(Collectors.joining(""))));
            }
        }
        return ResultGenerator.genSuccessResult();
    }

    @Override
    public RejectReasonCodeResponse getRejectReasonAndCodeList(RejectReasonCodeRequest request) {
        DouyinConfigDto requestConfig = getRequestConfigAndToken(request.getTenantId());
        AfterSaleRejectReasonCodeListRequest codeListRequest = new AfterSaleRejectReasonCodeListRequest();
        codeListRequest.setConfig(requestConfig.getDoudianOpConfig());
        AfterSaleRejectReasonCodeListParam param = codeListRequest.getParam();
        param.setAftersaleId(Long.valueOf(request.getAfterSaleId()));
        AfterSaleRejectReasonCodeListResponse response = codeListRequest.execute(requestConfig.getAccessToken());
        log.info("aftersaleId:{}获取拒绝原因列表 response:{}", request.getAfterSaleId(), response);
        RejectReasonCodeResponse resp = new RejectReasonCodeResponse();
        if (!response.isSuccess()) {
            resp.setStatus(ResultGenerator.genFailResult(response.getMsg()));
            return resp;
        }
        AfterSaleRejectReasonCodeListData data = response.getData();
        List<com.doudian.open.api.afterSale_rejectReasonCodeList.data.ItemsItem> items = data == null ? null
                : data.getItems();
        if (CollectionUtils.isEmpty(items)) {
            resp.setStatus(ResultGenerator.genFailResult("查询抖音商家拒绝原因列表返回数据为空"));
            return resp;
        }
        List<RejectReasonCodeDTO> rejectReasonCodeList = items.stream().map(item -> {
            RejectReasonCodeDTO dto = new RejectReasonCodeDTO();
            dto.setReason(item.getReason());
            dto.setRejectReasonCode(item.getRejectReasonCode());
            dto.setImage(item.getImage());
            dto.setOrderType(item.getOrderType());
            dto.setEvidenceDescription(item.getEvidenceDescription());
            dto.setEvidenceNeed(item.getEvidenceNeed());
            dto.setPkg(item.getPkg());
            return dto;
        }).collect(Collectors.toList());
        resp.setStatus(ResultGenerator.genSuccessResult());
        resp.setRejectReasonCodeDtoList(rejectReasonCodeList);
        return resp;
    }

    @Override
    public DecryptUserSensitiveDataResponse decryptUserSensitiveData(DecryptUserSensitiveDataRequest request) {
        // 参数检查
        DecryptUserSensitiveDataResponse response = new DecryptUserSensitiveDataResponse();

        // 专门调用渠道进行解密，超时时间设置长一点
        int timeout = Lion.getConfigRepository().getIntValue("config.douyin.decryptForRetryHttpReadTimeoutMillSec",
                10 * 1000);

        OrderOrderDetailData channelDetail = queryChannelOrderDetail(request);
        if (channelDetail == null || channelDetail.getShopOrderDetail() == null) {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(ResultCode.CHANNEL_ORDER_QUERY_FAILED.getCode());
            resultStatus.setMsg(ResultCode.CHANNEL_ORDER_QUERY_FAILED.getMsg());
            response.setStatus(resultStatus);
            return response;
        }


        ShopOrderDetail shopOrderDetail = channelDetail.getShopOrderDetail();
        if (isOrderFinishedOrCanceled(shopOrderDetail)) {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.setCode(-1);
            resultStatus.setMsg("order finished or canceled");
            response.setStatus(resultStatus);
            return response;
        }
        try {

            DecryptReqDto decryptReqDto = buildDecryptReqDto(shopOrderDetail);

            DecryptRespDto decryptRespDto = douyinRetryChannelService.decryptWithoutRetry(request.getTenantId(),
                    request.getOrderId(), decryptReqDto, timeout);

            if (StringUtils.isNotBlank(decryptRespDto.getAddress())
                    && StringUtils.isNotBlank(decryptRespDto.getMobile())) {
                response.setStatus(ResultGenerator.genSuccessResult());
                DecryptUserSensitiveDataDTO dataDTO = new DecryptUserSensitiveDataDTO();
                dataDTO.setName(decryptRespDto.getName());
                dataDTO.setAddress(decryptRespDto.getAddress());
                if (BooleanUtils.isTrue(decryptRespDto.getIsVirtualTel())) {
                    dataDTO.setPhone(decryptRespDto.getPhoneNoA() + "_" +decryptRespDto.getPhoneNoB());
                } else {
                    dataDTO.setPhone(decryptRespDto.getMobile());
                }
                response.setUserSensitiveDataDTO(dataDTO);
            } else {
                response.setStatus(ResultGenerator.genFailResult("解密失败"));
            }
            
        } catch (Exception e) {
            log.error("decryptUserSensitiveData解密订单敏感信息异常, request: {} ", request, e);
            response.setStatus(ResultGenerator.genFailResult("解密异常"));
        }
        return response;
    }

    @Nullable
    private OrderOrderDetailData queryChannelOrderDetail(DecryptUserSensitiveDataRequest request) {
        OrderOrderDetailRequest orderOrderDetailRequest = new OrderOrderDetailRequest();
        OrderOrderDetailParam param = orderOrderDetailRequest.getParam();
        String orderId = request.getOrderId();
        param.setShopOrderId(orderId);
        try {
            log.info("请求订单详情{}, req: {}", orderOrderDetailRequest.getUrlPath(), orderOrderDetailRequest);
            AccessToken accessToken = prepareRequestConfigAndToken(orderOrderDetailRequest, request.getTenantId());
            // 获取订单详情
            OrderOrderDetailResponse response = orderOrderDetailRequest.execute(accessToken);
            log.info("请求订单详情, resp: {}", response);

            if (response != null && response.isSuccess()) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("请求订单详情异常{} ", orderId , e);
        }
        return null;
    }

    private boolean isOrderFinishedOrCanceled(ShopOrderDetail shopOrderDetail) {
        return shopOrderDetail.getOrderStatus() == 4 || shopOrderDetail.getOrderStatus() == 5;
    }
}
