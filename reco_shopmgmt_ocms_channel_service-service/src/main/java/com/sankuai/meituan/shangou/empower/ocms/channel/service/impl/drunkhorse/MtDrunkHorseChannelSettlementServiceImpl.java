package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.drunkhorse;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ChannelErrorCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MTOrderSettlementListParam;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MTOrderSettlementListResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MtChannelSettlementDetailExtraInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelSettlementService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelBalanceBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelBalanceBillPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelCheckBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelCheckBillResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementByIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementPageDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelOrderSettlementSummary;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementAndDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.ChannelSettlementPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.DouyinBalanceBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.DouyinBalanceBillPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.JDBalanceBillPageRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.JDBalanceBillPageResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;

/**
 * 美团歪马微商城接口实现
 *
 * <AUTHOR>
 * @since 2025/02/20
 */
@Service("mtDrunkHorseChannelSettlementService")
public class MtDrunkHorseChannelSettlementServiceImpl implements ChannelSettlementService {

    @Resource
    private MtConverterService mtConverterService;

    @Resource
    private MtChannelGateService mtChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private CommonLogger log;

    @Value("${mt.url.settlementBaseV1}" + "${mt.url.billList}")
    private String settlementBillListUrl;

    @Value("${mt.url.base}" + "${mt.url.billList}")
    private String settlementBaseBillListUrl;

    @Override
    public ChannelSettlementPageResponse getChannelSettlementList(ChannelSettlementPageRequest request) {
        return null;
    }

    @Override
    public ChannelSettlementAndDetailResponse getChannelOrderSettlementListById(ChannelOrderSettlementByIdRequest request) {
        return null;
    }

    @Override
    public ChannelOrderSettlementPageResponse getChannelOrderSettlementList(ChannelOrderSettlementPageRequest request) {

        ChannelOrderSettlementPageResponse result = new ChannelOrderSettlementPageResponse();
        try {
            log.info("MtDrunkHorseChannelSettlementServiceImpl-getChannelOrderSettlementList 渠道账单请求 request={}", JSON.toJSONString(request));
            Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getTenantId(), request.getChannelId(), Arrays.asList(request.getShopId()));

            BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId()).setTenantId(request.getTenantId());
            MTOrderSettlementListParam param = mtConverterService.convertChannelSettlementDetailRequset(request);
            param.setOffset((request.getPageNo() - 1) * request.getPageSize());
            ChannelStoreDO store = channelStoreDOMap.get("" + request.getTenantId() + "-" + request.getChannelId() + "-" + request.getShopId());
            // 判断门店渠道是否存在
            if (store == null) {
                result.setResultStatus(ResultGenerator.genResult(ResultCode.CHANNEL_STORE_CONFIG_ERROR, "门店渠道配置信息不存在!"));
                return result;
            }
            param.setApp_poi_code(store.getChannelOnlinePoiCode());
            String billUrl = settlementBaseBillListUrl;
            boolean switchMTBillInnerNetUrl = MccConfigUtil.switchMTBillInnerNetUrl();
            if (!switchMTBillInnerNetUrl) {
                billUrl = settlementBillListUrl;
            }
            Map<String, Object> settlementMap = getChannelGateService().sendGet(billUrl, null, baseRequest, param);
            if (settlementMap == null) {
                log.error("未获取到渠道账单信息 request={}", request);
                result.setResultStatus(ResultGenerator.genResult(ResultCode.CHANNEL_SYSTEM_ERROR, "未获取到渠道账单信息"));
                return result;
            }
            Object error = settlementMap.get("error");
            if (Objects.nonNull(error)) {
                log.warn("获取外卖渠道结算信息失败request:{} response:{}", request, settlementMap);
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(error));
                ResultCode resultCode = convert2ResultCode(jsonObject.getString("code"));
                result.setResultStatus(ResultGenerator.genResult(resultCode, jsonObject.getString("msg")));
                return result;
            }
            Object data = settlementMap.get("data");
            Object extraInfo = settlementMap.get("extra_info");
            if (data == null || extraInfo == null) {
                log.error("获取结算信息失败:request:{} ,requestWmParam:{}, response:{} ", request, param, settlementMap);
                result.setResultStatus(ResultGenerator.genFailResult("获取结算信息失败"));
                return result;
            }
            log.info("获取外卖渠道结算信息成功:request:{},response:{}", request, JSON.toJSONString(settlementMap));

            // 获取当前查询条件下总账单数和总金额
            MtChannelSettlementDetailExtraInfo channelSettlementDetailExtraInfo = JSON.parseObject(JSON.toJSONString(extraInfo), MtChannelSettlementDetailExtraInfo.class);
            ChannelOrderSettlementSummary summary = new ChannelOrderSettlementSummary();

            if (channelSettlementDetailExtraInfo == null) {
                summary.setTotalCount(0);
                summary.setTotalSettleMilliSum(0);
            } else {
                summary.setTotalCount(channelSettlementDetailExtraInfo.getTotal_count());
                long settleSum = BigDecimal.valueOf(Double.valueOf(channelSettlementDetailExtraInfo.getSettle_sum()))
                        .multiply(BigDecimal.valueOf(100))
                        .longValue();
                summary.setTotalSettleMilliSum(settleSum);
            }

            ChannelOrderSettlementPageDTO channelOrderSettleDetailListDataDTO = new ChannelOrderSettlementPageDTO();
            channelOrderSettleDetailListDataDTO.setChannelOrderSettlementSummary(summary);

            List<MTOrderSettlementListResult> resultList = new ArrayList<>();
            JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(data));
            if(Objects.nonNull(jsonArray)){
                for (int i = 0; i< jsonArray.size(); i++) {
                    String originalOrderSettlementData = jsonArray.getJSONObject(i).toJSONString();
                    MTOrderSettlementListResult mtOrderSettlementListResult = jsonArray.getObject(i, (Type) MTOrderSettlementListResult.class);
                    mtOrderSettlementListResult.setOriginalOrderSettlementData(originalOrderSettlementData);
                    resultList.add(mtOrderSettlementListResult);
                }
            }
            if (CollectionUtils.isEmpty(resultList)) {
                result.setResultStatus(ResultGenerator.genSuccessResult());
                result.setChannelOrderSettlementPageDto(channelOrderSettleDetailListDataDTO);
                return result;
            }
            result.setResultStatus(ResultGenerator.genSuccessResult());
            List<ChannelOrderSettlementDTO> channelOrderSettleDetailDTOList = resultList.stream().map(mtConverterService::convertChannelOrderSettlement).collect(Collectors.toList());

            channelOrderSettleDetailListDataDTO.setChannelOrderSettlementDtoList(channelOrderSettleDetailDTOList);
            result.setChannelOrderSettlementPageDto(channelOrderSettleDetailListDataDTO);

        } catch (Exception e) {
            log.error("获取美团渠道账单明细发生异常 request:{}", request, e);
            return result.setResultStatus(ResultGenerator.genFailResult(ResultCode.UNDEAL_ERROR.getMsg() + e.getMessage()));
        }

        return result;

    }

    private ResultCode convert2ResultCode(String code) {
        if (ChannelErrorCode.MT.POI_NOT_EXIST.equals(code)) {
            return ResultCode.CHANNEL_STORE_CONFIG_ERROR;
        } else if (ChannelErrorCode.MT.NO_API_PERMISSION.equals(code)) {
            return ResultCode.CHANNEL_STORE_AUTH_ERROR;
        } else{
            return ResultCode.CHANNEL_SYSTEM_ERROR;
        }

    }

    @Override
    public ChannelBalanceBillPageResponse getBalanceBillList(ChannelBalanceBillPageRequest request) {
        return null;
    }

    @Override
    public ChannelCheckBillResponse getCheckBillList(ChannelCheckBillPageRequest request) {
        return null;
    }
    public BaseChannelGateService getChannelGateService() {
        return mtChannelGateService;
    }

    @Override
    public JDBalanceBillPageResponse getBalanceBill(JDBalanceBillPageRequest request) {
        return null;
    }

    @Override
    public DouyinBalanceBillPageResponse getDouyinBalanceBill(DouyinBalanceBillPageRequest request) {
        return null;
    }
}