package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.common.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.common.MedicineTenantService;
import deps.redis.clients.util.CollectionUtils;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class MedicineTenantServiceImpl implements MedicineTenantService {

    private static final String MEDICINE_SQUIRREL_CATEGORY = "emproduct_medicine_kv";
    private static final String MEDICINE_MERCHANT_ID_CACHE_KEY = "uwms_merchant_id";

    @Resource(name = "redisClient")
    private RedisStoreClient redisClient;

    /**
     * 查询所有的无人仓租户id
     * 刷新缓存中数据的位置在emproduct的 UpdateMedicineMerchantIdTask.java
     */
    @Override
    public List<Long> getAllUwmsTenantIds() {
        StoreKey storeKey = new StoreKey(MEDICINE_SQUIRREL_CATEGORY, MEDICINE_MERCHANT_ID_CACHE_KEY);
        Object cachedValueObj = redisClient.get(storeKey);
        String cachedValue = cachedValueObj instanceof String ? (String) cachedValueObj : null;

        if (StringUtils.isBlank(cachedValue)) {
            return Collections.emptyList();
        }

        return JSON.parseArray(cachedValue, Long.class);
    }
}
