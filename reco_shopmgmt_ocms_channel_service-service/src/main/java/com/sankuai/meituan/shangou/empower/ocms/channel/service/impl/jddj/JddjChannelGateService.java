package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import static com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant.RHINO_UPTIMATE_SET;

import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelVirtualAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.ChannelVirtualConfigMapper;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.BaseChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.JddjSignUtils;

/**
 * 京东到家请求公共服务接口
 *
 * <AUTHOR>
 * @create 2019-01-08 下午8:09
 **/
@Service
public class JddjChannelGateService extends BaseChannelGateService {

    @Value("${jddj.url.base}")
    private String baseUrl;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private CommonLogger log;

    @Resource
    private ChannelVirtualConfigMapper channelVirtualConfigMapper;

    @Override
    protected Map<String, Object> generatePostParams(String url, String method, Map<String, Object> sysParam, Map<String, Object> bizParam) {
        return generatePostParams(sysParam, bizParam);
    }

    private Map<String, Object> generatePostParams(Map<String, Object> sysParam, Map<String, Object> bizParam) {
        String secret = (String) sysParam.get("secret");
        sysParam.remove("secret");
        if (bizParam == null) {
            sysParam.put("jd_param_json", "");
        } else {
            sysParam.put("jd_param_json", JSON.toJSONString(bizParam));
        }

        sysParam.put("timestamp", DateUtils.currentTimeDefault());
        String sign = JddjSignUtils.getSignByMD5(sysParam, secret);
        sysParam.put("sign", sign);
        return sysParam;
    }

    @Override
    public <T> T postToChannel(String channelPoiCode, String channelOnlinePoiCode, ChannelPostInter postUrlEnum, Map<String, Object> sysParam, Map<String, Object> bizParamMap, BaseRequest baseRequest) {
        replaceStoreId(bizParamMap, channelOnlinePoiCode, channelPoiCode);

        String secret = (String) sysParam.get(ProjectConstant.SECRET);

        Map<String, Object> postParams = generatePostParams(sysParam, bizParamMap);

        // 限频
        String appId = String.valueOf(sysParam.get(ProjectConstant.JDDJ_APP_KEY));
        if (StringUtils.isNotBlank(appId) && postUrlEnum.requestLimited()) {
            long waitTime = clusterRateLimiter.tryAcquire(postUrlEnum.generateLimitedResourceKey(), appId, baseRequest.isAsyncInvoke());
            if (waitTime != 0) {
                if (RHINO_UPTIMATE_SET.contains(postUrlEnum)) {
                    log.info("JddjChannelGateService.postToChannel appId:{} url:{} 未获取到令牌 抛出异常", appId, postUrlEnum.getUrl());
                    throw new InvokeChannelTooMuchException(waitTime);
                } else {
                    log.info("JddjChannelGateService.postToChannel appId:{} url:{} 未获取到令牌 直接返回默认值", appId, postUrlEnum.getUrl());
                    return (T) JSON.parseObject("{\"code\":\"10032\",\"msg\":\"调用频繁，请稍后再试。\",\"data\":null,\"success\":false}", ChannelResponseDTO.class);
                }
            }
        }

        ChannelResponseDTO dto = dealPostResult(postRequest(postUrlEnum, postParams, baseRequest), postUrlEnum.getResultClass(), secret);
        channelResponseMetric(ChannelTypeEnum.JD2HOME, postUrlEnum, baseRequest, dto, ChannelResponseDTO::getResponseError);

        if (dto != null) {
            log.info("【渠道原始日志】, operate:{}, url:{}, success:{}, isErp:{}, request:{}, postParams:{}, result:{}",
                    postUrlEnum.getUrlShortName(),
                    postUrlEnum.getUrl(),
                    ProjectConstant.SUCCESS_CODE.equals(dto.getCode()),
                    baseRequest.isErpTenant(),
                    baseRequest,
                    postParams,
                    JacksonUtils.toJson(dto));
        }

        return (T) dto;
    }

    private void replaceStoreId(Map<String, Object> bizParamMap, String outStoreId, String storeId) {
        if (StringUtils.isNotBlank(outStoreId) && bizParamMap.containsKey(Constant.FIELD_NAME_STOREID_JDDJ)) {
            bizParamMap.put(Constant.FIELD_NAME_STOREID_JDDJ, outStoreId);
        }
        if (StringUtils.isNotBlank(storeId) &&  bizParamMap.containsKey(Constant.FIELD_NAME_STOREID_JDDJ2)) {
            bizParamMap.put(Constant.FIELD_NAME_STOREID_JDDJ2, storeId);
        }
    }

    @Override
    public String getPostUrl(ChannelPostInter postUrlEnum) {
        return baseUrl + postUrlEnum.getUrl();
    }

    private <T> T dealPostResult(String resultJson, Class resultClass, String secret) {
        ChannelResponseDTO dto = ChannelResponseDTO.parseResult(resultJson, resultClass, secret);

        return (T) dto;
    }

    @Override
    protected Map<String, Object> getChannelVirtualSysParam(Long tenantId, Integer channelId) {
        ChannelVirtualAccessConfigDO virtualAccessConfig = channelVirtualConfigMapper.selectByTenantChannel(tenantId, channelId);
        if (Objects.isNull(virtualAccessConfig) || StringUtils.isBlank(virtualAccessConfig.getVirtualSysParam())) {
            log.error("channelVirtualConfigMapper.selectByTenantChannel 虚拟渠道系统参数获取失败，tenantId:{}, channelId:{}", tenantId, channelId);
            throw new BizException("虚拟渠道系统参数获取失败");
        }
        return JSON.parseObject(virtualAccessConfig.getVirtualSysParam());
    }

}
