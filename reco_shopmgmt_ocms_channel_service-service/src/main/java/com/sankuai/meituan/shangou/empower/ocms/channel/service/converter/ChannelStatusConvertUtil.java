package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.meituan.shangou.saas.common.enums.OcmsRefundSponsorEnum;
import com.meituan.shangou.saas.common.enums.ReturnGoodsStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ElmReverseResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JddjAfsExtMap;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.JddjAfterSaleDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.EnumUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.client.common.enums.ServiceTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.*;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: 状态映射工具类
 * @author: zhaolei12
 * @create: 2019-02-15 17:04
 */
@Slf4j
public class ChannelStatusConvertUtil {

    /**
     * 美团配送状态映射
     */
    public static int mtDeliveryStatusMapping(int deliveryStatus) {
        switch (deliveryStatus) {
            case 0://生成运单
                return DeliveryStatus.NEW_DELIVERY_ORDER.getValue();
            case 5:
                //配送侧压单
                return DeliveryStatus.APPLY_DELIVERY.getValue();
            case 10://配送单发往配送
                return DeliveryStatus.RIDER_ACCEPTED_ORDER.getValue();
            case 15://配送单已确认
                return DeliveryStatus.RIDER_ARRIVE_SHOP.getValue();
            case 20://骑手已取餐
                return DeliveryStatus.RIDER_TAKEN_MEAL.getValue();
            case 40://骑手已送达
                return DeliveryStatus.DELIVERY_COMPLETED.getValue();
            case 100://配送单已取消
                return DeliveryStatus.DELIVERY_CANCEL.getValue();
            default:
                String selfMapping = ConfigUtilAdapter.getString("deliverystatus.mt.self.mapping");
                return selfMapping(selfMapping, deliveryStatus);
        }
    }

    public static int deliveryStatusToDap(int deliveryStatus) {
        switch (deliveryStatus) {
            case 10://生成运单
                return 10;
            case 20://DeliveryStatus.APPLY_DELIVERY.getValue()
                return 10;
            case 40:
                return 20;
            case 45://配送单已确认
                return 25;
            case 50://骑手已取餐
                return 30;
            case 60://骑手已送达
                return 50;
            case 130://配送单已取消
                return 99;
            default:
                return -1;
        }
    }


    /**
     * 自配送状态映射
     */
    public static int mtSelfDeliveryStatusMapping(int selfDeliveryStatus) {
        switch (selfDeliveryStatus) {
            case 20://骑手已取餐
                return DeliveryStatus.RIDER_TAKEN_MEAL.getValue();
            default:
                return 0;
        }
    }

    /**
     * 饿百配送状态映射
     */
    public static int elmDeliveryStatusMapping(int deliveryStatus) {
        switch (deliveryStatus) {
            case 0:
            case 1://等待请求配送
                return DeliveryStatus.WAIT_DELIVERY.getValue();
            case 2://生成运单
                return DeliveryStatus.NEW_DELIVERY_ORDER.getValue();
            case 3://请求配送
                return DeliveryStatus.APPLY_DELIVERY.getValue();
            case 4://等待分配骑士
                return DeliveryStatus.WAIT_DISPATCH_RIDER.getValue();
            case 7://骑士接单
                return DeliveryStatus.RIDER_ACCEPTED_ORDER.getValue();
            case 8://骑士取餐
                return DeliveryStatus.RIDER_TAKEN_MEAL.getValue();
            case 15://配送取消
            case 19://不在配送
                return DeliveryStatus.DELIVERY_CANCEL.getValue();
            case 16://配送完成
                return DeliveryStatus.DELIVERY_COMPLETED.getValue();
            case 17://配送异常
                return DeliveryStatus.DELIVERY_EXCEPTION.getValue();
            case 20://配送拒单
                return DeliveryStatus.DELIVERY_FAILED.getValue();
            case 21://骑手到店
                return DeliveryStatus.RIDER_ARRIVE_SHOP.getValue();
            case 18://自行配送
            default:
                String selfMapping = ConfigUtilAdapter.getString("deliverystatus.elm.self.mapping");
                return selfMapping(selfMapping, deliveryStatus);

        }
    }

    private static int selfMapping(String selfMapping, int deliveryStatus) {
        if (StringUtils.isNotBlank(selfMapping)){
            String[] mappingList = StringUtils.split(selfMapping, ";");
            for (String mapping: mappingList) {
                String item[] = StringUtils.split(mapping, ",");
                if (item != null && item.length == 2 && NumberUtils.toInt(item[0]) == deliveryStatus){
                    return  NumberUtils.toInt(item[1], 0);
                }
            }
        }
        return 0;
    }

    /**
     * 饿百配送异常描述映射
     */
    public static String elmDeliveryExceptionDescriptionMapping(String subStatus) {
        switch (subStatus) {
            //不存在对应子状态
            case "0":
                return "";
            case "1":
                return "商家取消";
            case "2":
                return "配送商取消";
            case "3":
                return "用户取消";
            case "4":
                return "物流系统取消";
            case "5":
                return "呼叫配送晚";
            case "6":
                return "餐厅出餐问题";
            case "7":
                return "商户中断配送";
            case "8":
                return "用户不接电话";
            case "9":
                return "用户退单";
            case "10":
                return "用户地址错误";
            case "11":
                return "超出服务范围";
            case "12":
                return "骑手标记异常";
            case "13":
                return "系统自动标记异常-订单超过3小时未送达";
            case "14":
                return "其他异常";
            case "15":
                return "超市取消/异常";
            case "101":
                return "只支持在线订单";
            case "102":
                return "超出服务范围";
            case "103":
                return "请求配送过晚,无法呼叫";
            case "104":
                return "系统异常";
            case "105":
                return "超出营业时间";
            default:
                return "";
        }
    }

    /**
     * 京东到家配送状态映射
     */
    public static int jddjDeliveryStatusMapping(int deliveryStatus) {
        switch (deliveryStatus) {
            case 10://等待抢单
            case 21://配送员取消抢单，等待重新抢单
                return DeliveryStatus.WAIT_DISPATCH_RIDER.getValue();
            case 20://已抢单
            case 22://更换配送员
                return DeliveryStatus.RIDER_ACCEPTED_ORDER.getValue();
            case 23://配送员已到店
                return DeliveryStatus.RIDER_ARRIVE_SHOP.getValue();
            case 25://取货失败
            case 26://取货失败审核驳回
            case 27://取货失败待审核
                if(!MccConfigUtil.getPlatformExceptionSwitch()){
                    return DeliveryStatus.TAKE_MEAL_FAIL.getValue();
                }
                return DeliveryStatus.DELIVERY_EXCEPTION.getValue();
            case 28://骑手异常上报
                if(!MccConfigUtil.getPlatformExceptionSwitch()){
                    String selfMapping = ConfigUtilAdapter.getString("deliverystatus.jd.self.mapping");
                    return selfMapping(selfMapping, deliveryStatus);
                }
                return DeliveryStatus.DELIVERY_EXCEPTION.getValue();
            case 29:
                if(!MccConfigUtil.getPlatformExceptionSwitch()){
                    String selfMapping = ConfigUtilAdapter.getString("deliverystatus.jd.self.mapping");
                    return selfMapping(selfMapping, deliveryStatus);
                }
                return DeliveryStatus.DELIVERY_NORMAL.getValue();
            case 30://取货完成
                return DeliveryStatus.RIDER_TAKEN_MEAL.getValue();
            case 35://投递失败
                return DeliveryStatus.DELIVERY_FAILED.getValue();
            case 40://已完成
                return DeliveryStatus.DELIVERY_COMPLETED.getValue();
            default:
                String selfMapping = ConfigUtilAdapter.getString("deliverystatus.jd.self.mapping");
                return selfMapping(selfMapping, deliveryStatus);
        }
    }

    public static int mtRefundPartTypeMapping(String notifyType, String resType, int isAppeal) {
        //发起部分退申请
        if (MtNotifyTypeEnum.PART.getAbbrev().equals(notifyType)) {
            //是否申诉申请
            if (IsAppealEnum.IS_APPEAL.getCode() == isAppeal) {
                return OrderAllRefundType.APPEAL_APPLY.getValue();
            }
            return OrderAllRefundType.APPLY.getValue();
        }
        //其它状态流转映射
        return mtResTypeMapping(resType);
    }

    public static int mtRefundGoodsTypeMapping(String status, int isAppeal, String opType) {

        MtRefundStatusEnum refundStatusEnum = EnumUtil.getEnumByAbbrev(status, MtRefundStatusEnum.class);
        //是否申诉申请
        if (Objects.isNull(refundStatusEnum) && IsAppealEnum.IS_APPEAL.getCode() == isAppeal) {
            return OrderAllRefundType.APPEAL_APPLY.getValue();
        }
        if (Objects.isNull(refundStatusEnum)) {
            return 0;
        }

        MtOpUserTypeEnum mtOpUserType = EnumUtil.getEnumByAbbrev(opType, MtOpUserTypeEnum.class);
        switch (refundStatusEnum) {
            case APPLY:
                return OrderRefundGoodsType.APPLY.getValue();
            case FIRST_APPLY_AGREE:
                return OrderRefundGoodsType.FIRST_APPLY_AGREE.getValue();
            case FIRST_APPLY_REJECT:
                return OrderRefundGoodsType.FIRST_APPLY_REJECT.getValue();
            case FIRST_APPLY_APPEAL:
                return OrderRefundGoodsType.FIRST_APPLY_APPEAL.getValue();
            case FIRST_APPLY_APPEAL_AGREE:
                return OrderRefundGoodsType.FIRST_APPLY_APPEAL_AGREE.getValue();
            case FINAL_APPLY:
                return OrderRefundGoodsType.CUSTOMER_SENT.getValue();
            case FINAL_APPLY_AGREE:
                return OrderRefundGoodsType.TENANT_AGREE.getValue();
            case FINAL_APPLY_REJECT:
                return OrderRefundGoodsType.TENANT_REJECT.getValue();
            case FINAL_APPLY_APPEAL:
                return OrderRefundGoodsType.APPLY_APPEAL.getValue();
            case FINAL_APPLY_APPEAL_AGREE:
                return OrderRefundGoodsType.CALL_CENTER_AGREE.getValue();
            case FINAL_APPLY_APPEAL_REJECT:
            case FIRST_APPLY_APPEAL_REJECT:
                return OrderRefundGoodsType.CALL_CENTER_REJECT.getValue();
            case APPLY_CANCEL:
                return mtOpUserType.equals(MtOpUserTypeEnum.CUSTOMER) ? OrderRefundGoodsType.APPLY_CANCEL_BY_CUSTOMER.getValue() : OrderRefundGoodsType.APPLY_CANCEL_BY_CHANNEL.getValue();
            default:
                return 0;
        }
    }

    @Deprecated
    public static int elmRefundPartTypeMapping(String type, String reasonType) {
        int status = Integer.parseInt(type);
        switch (status) {
            case 10:
                return OrderPartRefundType.APPLY.getValue();
            case 20:
                return OrderPartRefundType.REFUND_SUCCESS.getValue();
            case 30:
                return OrderPartRefundType.APPEAL_APPLY.getValue();
            case 40:
                return OrderPartRefundType.APPLY_CANCEL_BY_CUSTOMER.getValue();
            case 50:
                return OrderPartRefundType.TENANT_REJECT.getValue();
            default:
                return elmRefundPartTypeMapByReasonType(reasonType);
        }
    }

    /**
     * 该退单当前退款状态：0初始化，10申请，20拒绝，30仲裁，40关闭，50成功，60失败
     * <p>
     * 0无效状态 1001买家已申请退货等待审批处理，1002商家拒绝退货申请，1003退货仲裁已发起客服介入中，1004已同意退货等待发货，1005已发货等待卖家确认收货，1006已收到货并同意退款，1007未收到货不同意退款，1008退货关闭
     * <p>
     * 是否退货标志 0：否 1：是
     * <p>
     * 虽然枚举值叫xxxRefundType，其实在售后单上又叫afterSaleStatus注意甄别！！！
     *
     * @return
     */

    public static int elmAfterSaleStatusMapping(Integer elmRefundStatus, Integer elmReturnGoodsStatus, Integer whetherReturnGoods) {
        if (whetherReturnGoods == 0) {
            // 仅退款
            switch (elmRefundStatus) {
                case 0:
                case 10:
                    return OrderPartRefundType.APPLY.getValue();
                case 20:
                    return OrderPartRefundType.TENANT_REJECT.getValue();
                case 30:
                    return OrderPartRefundType.APPEAL_APPLY.getValue();
                case 40:
                    return OrderPartRefundType.APPLY_CANCELED.getValue();
                case 50:
                    return OrderPartRefundType.REFUND_SUCCESS.getValue();
                case 60:
                    return OrderPartRefundType.TENANT_REJECT.getValue();
                default:
                    break;

            }

        } else {
            switch (elmReturnGoodsStatus) {
                case 1001:
                    // 1001买家已申请退货等待审批处理
                    return OrderRefundGoodsType.APPLY.getValue();
                case 1002:
                    // 1002商家拒绝退货申请
                    return OrderRefundGoodsType.FIRST_APPLY_REJECT.getValue();
                case 1003:
                    // 1003退货仲裁已发起客服介入中
                    return OrderRefundGoodsType.FIRST_APPLY_APPEAL.getValue();
                case 1004:
                    // 1004已同意退货等待发货
                    return OrderRefundGoodsType.FIRST_APPLY_AGREE.getValue();
                case 1005:
                    // 1005已发货等待卖家确认收货
                    return OrderRefundGoodsType.FINAL_APPLY.getValue();
                case 1006:
                    // 1006已收到货并同意退款
                    return OrderRefundGoodsType.TENANT_AGREE.getValue();
                case 1007:
                    if(elmRefundStatus==30){
                        //二审拒绝且是申述状态的场景是二审申述
                       return OrderRefundGoodsType.APPLY_APPEAL.getValue();
                    }else {
                        // 1007未收到货不同意退款
                        return OrderRefundGoodsType.TENANT_REJECT.getValue();
                    }


                case 1008:
                    // 1008退货关闭
                    return OrderRefundGoodsType.APPLY_CANCELED.getValue();
                case 0:
                    switch (elmRefundStatus) {
                        case 30:
                            // 二审申诉
                            return OrderRefundGoodsType.APPLY_APPEAL.getValue();
                        case 60:
                            //添加取消场景的映射
                        case 40:
                            // 二审申诉失败或未申诉
                            return OrderRefundGoodsType.APPLY_CANCELED.getValue();
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }

        }

        return 0;
    }


    /**
     * 1103 表示用户撤销
     * 1104 表示商家拒绝用户退单,用户未申请仲裁,系统自动退单失败
     * 1302 表示客服仲裁退款失败
     * 1301 表示客服仲裁退款成功
     * 1202 表示商家同意
     * 1304 表示用户申请仲裁,客服未处理,系统自动退单成功
     * 1203 表示商家超时未处理,系统自动退单成功
     * **/
    private static int elmRefundPartTypeMapByReasonType(String reasonType) {
        if(StringUtils.isBlank(reasonType)){
            return 0;
        }
        switch (reasonType){
            case "1103":
                return OrderPartRefundType.APPLY_CANCEL_BY_CUSTOMER.getValue();
            case "1104":
                return OrderPartRefundType.TENANT_REJECT.getValue();
            case "1302":
                return OrderPartRefundType.CALL_CENTER_REJECT.getValue();
            case "1301":
                return OrderPartRefundType.CALL_CENTER_AGREE.getValue();
            case "1202":
                return OrderPartRefundType.TENANT_AGREE.getValue();
            case "1304":
            case "1203":
                return OrderPartRefundType.CHANNEL_CUSTOMER_AGREE.getValue();
            default:
                return 0;
        }
    }

    public static int mtRefundTypeMapping(String notifyType, String status, int isAppeal) {
        //发起整单退款申请
        if (MtNotifyTypeEnum.APPLY.getAbbrev().equals(notifyType)) {
            //是否申诉申请
            if (IsAppealEnum.IS_APPEAL.getCode() == isAppeal) {
                return OrderAllRefundType.APPEAL_APPLY.getValue();
            }
            return OrderAllRefundType.APPLY.getValue();
        }
        //其它状态流转
        return mtResTypeMapping(status);
    }

    public static int mtResTypeMapping(String status) {
        MtResTypeEnum resTypeEnum = EnumUtil.getEnumByAbbrev(status, MtResTypeEnum.class);
        if (Objects.isNull(resTypeEnum)) {
            return 0;
        }
        switch (resTypeEnum) {
            case UNTREATED:
                return OrderAllRefundType.APPLY.getValue();
            case TENANT_REJECT:
                return OrderAllRefundType.TENANT_REJECT.getValue();
            case TENANT_AGREE:
                return OrderAllRefundType.TENANT_AGREE.getValue();
            case CALL_REJECT:
                return OrderAllRefundType.CALL_CENTER_REJECT.getValue();
            case CALL_AGREE:
                return OrderAllRefundType.CALL_CENTER_AGREE.getValue();
            case TIMEOUT_AUTO_AGREE:
            case SYSTEM_AUTO_CONFIRM:
                return OrderAllRefundType.CHANNEL_SYSTEM_AGREE.getValue();
            case CANCEL_REFUND_APPLY:
                return OrderAllRefundType.APPLY_CANCEL_BY_CUSTOMER.getValue();
            case CANCEL_REFUND_APPEAL:
                return OrderAllRefundType.APPEAL_CANCEL_BY_CUSTOMER.getValue();
            default:
                return 0;
        }
    }


    public static int mtAfsRecordMapping(String status) {
        MtResTypeEnum resTypeEnum = EnumUtil.getEnumByAbbrev(status, MtResTypeEnum.class);
        if (Objects.isNull(resTypeEnum)) {
            return 0;
        }
        switch (resTypeEnum) {
            case UNTREATED:
                return AfterSaleRecordStatus.COMMIT.getValue();//提交
            case TENANT_AGREE:
            case CALL_AGREE:
            case TIMEOUT_AUTO_AGREE:
            case SYSTEM_AUTO_CONFIRM:
                return AfterSaleRecordStatus.AUDITED.getValue();//已审核
            case TENANT_REJECT:
            case CALL_REJECT:
                return AfterSaleRecordStatus.AUDITED_REJECT.getValue();//已审核驳回
            case CANCEL_REFUND_APPLY:
            case CANCEL_REFUND_APPEAL:
                return AfterSaleRecordStatus.CANCEL.getValue();//已取消
            default:
                return 0;
        }
    }

    public static int dhAfterSaleStatusMapping(int refundStatus) {
        DrunkHorseRefundStatusEnum refundStatusEnum = DrunkHorseRefundStatusEnum.enumOf(refundStatus);
        if (refundStatusEnum == null){
            return 0;
        }
        switch (refundStatusEnum) {
            case APPLY:
                return OrderAllRefundType.APPLY.getValue();//提交
            case CONFIRM:
                return OrderAllRefundType.TENANT_AGREE.getValue();
            case REJECT:
                return OrderAllRefundType.TENANT_REJECT.getValue();
            case CANCEL_APPLY:
                return OrderAllRefundType.APPLY_CANCELED.getValue();
            case SUCCESS:
                return OrderAllRefundType.REFUND_SUCCESS.getValue();
            default:
                return 0;

        }
    }

    public static int dhAfterSaleRecordStatusMapping(int refundStatus) {
        DrunkHorseRefundStatusEnum refundStatusEnum = DrunkHorseRefundStatusEnum.enumOf(refundStatus);
        if (refundStatusEnum == null){
            return 0;
        }
        switch (refundStatusEnum) {
            case APPLY:
                return AfterSaleRecordStatus.COMMIT.getValue();//提交
            case CONFIRM:
                return AfterSaleRecordStatus.AUDITED.getValue();
            case REJECT:
                return AfterSaleRecordStatus.AUDITED_REJECT.getValue();
            case CANCEL_APPLY:
                return AfterSaleRecordStatus.CANCEL.getValue();
            case SUCCESS:
                return AfterSaleRecordStatus.FINISH.getValue();
            default:
                return 0;

        }
    }

    @Deprecated
    public static int elmRefundTypeMapping(String type) {
        int status = Integer.parseInt(type);
        switch (status) {
            case 10:
                return OrderAllRefundType.APPLY.getValue();
            case 20:
                return OrderAllRefundType.APPEAL_APPLY.getValue();
            case 30:
                return OrderAllRefundType.CALL_CENTER_REJECT.getValue();
            case 40:
                return OrderAllRefundType.CALL_CENTER_AGREE.getValue();
            case 50:
                return OrderAllRefundType.TENANT_REJECT.getValue();
            case 60:
                return OrderAllRefundType.TENANT_AGREE.getValue();
            case 70:
                return OrderAllRefundType.APPLY_CANCEL_BY_CUSTOMER.getValue();
            default:
                return 0;
        }
    }



    public static int elmPartAfsRecordMapping(String type) {
        int status = Integer.parseInt(type);
        switch (status) {
            case 10://发起申请
            case 30://用户申请仲裁,客服介入
                return AfterSaleRecordStatus.COMMIT.getValue();//提交
            case 20://部分退款成功
                return AfterSaleRecordStatus.AUDITED.getValue();//已审核
            case 40://部分退款失败
            case 50://商家拒绝用户发起的部分退款申请
                return AfterSaleRecordStatus.AUDITED_REJECT.getValue();//已审核驳回
            default:
                return 0;
        }
    }

    public static int elmAllAfsRecordMapping(String type) {
        int status = Integer.parseInt(type);
        switch (status) {
            case 10://发起申请
            case 20://客服介入
                return AfterSaleRecordStatus.COMMIT.getValue();//提交
            case 40://客服同意
            case 60://商户同意
                return AfterSaleRecordStatus.AUDITED.getValue();//已审核
            case 30://客服拒绝
            case 50://商户拒绝
                return AfterSaleRecordStatus.AUDITED_REJECT.getValue();//已审核驳回
            case 70://申请失效
                return AfterSaleRecordStatus.CANCEL.getValue();//已取消
            default:
                return 0;
        }
    }

    /**
     * ELM订单状态映射
     */
    public static ChannelOrderStatus elmOrderStatusMapping(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        }
        try {
            int orderStatus = Integer.parseInt(status);
            switch (orderStatus) {
                case 1:
                    return ChannelOrderStatus.NEW_ORDER;
                case 5:
                    return ChannelOrderStatus.BIZ_CONFIRMED;
                case 7:
                case 8:
                    return ChannelOrderStatus.FULFILLMENT;
                case 9:
                    return ChannelOrderStatus.FINISHED;
                case 15:
                    return ChannelOrderStatus.CANCEL_APPLIED;
                case 10:
                    return ChannelOrderStatus.CANCELED;
                default:
                    return null;

            }
        } catch (NumberFormatException e) {
            log.warn("ChannelStatusConvertUtil.orderStatusMapping, 渠道订单状态类型转换异常, status:{}", status);
        }
        return null;
    }

    public static int mtSponsorMapping(String notifyType, String resType, int isAppeal, String tenantAppId) {
        MtResTypeEnum resTypeEnum = EnumUtil.getEnumByAbbrev(resType, MtResTypeEnum.class);
        if (Objects.isNull(resTypeEnum)) {
            return OrderRefundSponsor.UNKNOWN.getValue();
        }
        //1用户发起全额退款申请的消息，申请待处理
        //7用户申诉的消息，申请待美团客服48小时内介入处理，商家无法操作
        if (notifyType.equals(MtNotifyTypeEnum.APPLY.getAbbrev()) && MtResTypeEnum.UNTREATED == resTypeEnum) {
            return OrderRefundSponsor.CUSTOMER.getValue();
        }
        //6用户取消全额退款申请的消息，无需处理
        if (notifyType.equals(MtNotifyTypeEnum.CANCEL_REFUND.getAbbrev()) && MtResTypeEnum.CANCEL_REFUND_APPLY == resTypeEnum) {
            return OrderRefundSponsor.CUSTOMER.getValue();
        }
        //8用户取消申诉的消息，无需处理
        if (notifyType.equals(MtNotifyTypeEnum.CANCEL_REFUND_COMPLAINT.getAbbrev()) && MtResTypeEnum.CANCEL_REFUND_APPEAL == resTypeEnum) {
            return OrderRefundSponsor.CUSTOMER.getValue();
        }
        // 歪马租户区分系统和客服
        if (MccConfigUtil.isMtDrunkHorseTenant(tenantAppId)){
            if (MtResTypeEnum.CALL_REJECT.equals(resTypeEnum)  || MtResTypeEnum.CALL_AGREE.equals(resTypeEnum)){
                return OcmsRefundSponsorEnum.CALL_CENTER.getValue();
            }else if (MtResTypeEnum.TIMEOUT_AUTO_AGREE.equals(resTypeEnum)  || MtResTypeEnum.SYSTEM_AUTO_CONFIRM.equals(resTypeEnum)){
                return OcmsRefundSponsorEnum.SYSTEM.getValue();
            }
        }
        return subMtSponsorMapping(notifyType, resTypeEnum);
    }

    public static int mtPartSponsorMapping(String notifyType, String resType, int isAppeal, String tenantAppId) {
        MtResTypeEnum resTypeEnum = EnumUtil.getEnumByAbbrev(resType, MtResTypeEnum.class);
        if (Objects.isNull(resTypeEnum)) {
            return OrderRefundSponsor.UNKNOWN.getValue();
        }
        //1用户发起部分退款申请的消息，申请待处理
        if (notifyType.equals(MtNotifyTypeEnum.PART.getAbbrev()) && MtResTypeEnum.UNTREATED == resTypeEnum) {
            return OrderRefundSponsor.CUSTOMER.getValue();
        }
        //6用户取消部分退款申请的消息，无需处理
        if (notifyType.equals(MtNotifyTypeEnum.CANCEL_REFUND.getAbbrev()) && MtResTypeEnum.CANCEL_REFUND_APPLY == resTypeEnum) {
            return OrderRefundSponsor.CUSTOMER.getValue();
        }
        //7商家主动发起部分退款的消息，同时会再推送另一条消息notify_type=agree & res_type=2，表示商家主动给用户进行了商品部分退款，此时已完成退款操作
        if (notifyType.equals(MtNotifyTypeEnum.PART.getAbbrev()) && MtResTypeEnum.TENANT_AGREE == resTypeEnum) {
            return OrderRefundSponsor.TENANT.getValue();
        }
        // 歪马租户区分系统和客服
        if (MccConfigUtil.isMtDrunkHorseTenant(tenantAppId)){
            if (MtResTypeEnum.CALL_REJECT == resTypeEnum || MtResTypeEnum.CALL_AGREE == resTypeEnum){
                return OcmsRefundSponsorEnum.CALL_CENTER.getValue();
            }else if (MtResTypeEnum.TIMEOUT_AUTO_AGREE == resTypeEnum || MtResTypeEnum.SYSTEM_AUTO_CONFIRM == resTypeEnum){
                return OcmsRefundSponsorEnum.SYSTEM.getValue();
            }
        }

        return subMtSponsorMapping(notifyType, resTypeEnum);
    }

    public static int mtRefundGoodsSponsorMapping(String opType) {
        try {
            MtOpUserTypeEnum opTypeEnum = EnumUtil.getEnumByAbbrev(opType, MtOpUserTypeEnum.class);
            switch (opTypeEnum) {
                case CUSTOMER:
                    return OrderRefundSponsor.CUSTOMER.getValue();
                case TENANT:
                    return OrderRefundSponsor.TENANT.getValue();
                case CALL_CENTER:
                case BD:
                case SYSTEM:
                case CHANNEL:
                    return OrderRefundSponsor.CHANNEL.getValue();
                default:
                    return OrderRefundSponsor.UNKNOWN.getValue();
            }
        } catch (NumberFormatException e) {
            log.warn("ChannelStatusConvertUtil.mtRefundGoodsSponsorMapping, 退款操作发起方类型枚举转换异常, opType:{}", opType);
        }
        return OrderRefundSponsor.UNKNOWN.getValue();
    }

    private static int subMtSponsorMapping(String notifyType, MtResTypeEnum resTypeEnum) {
        //2商家或客服帮商家同意用户退款申请的消息，申请已处理
        if (notifyType.equals(MtNotifyTypeEnum.AGREE.getAbbrev()) && isChannelAutoOperator(resTypeEnum)) {
            return OrderRefundSponsor.TENANT.getValue();
        }
        //3商家或客服驳回用户退款申请的消息，申请已处理
        if (notifyType.equals(MtNotifyTypeEnum.REJECT.getAbbrev()) && isCallOperator(resTypeEnum)) {
            return OrderRefundSponsor.TENANT.getValue();
        }
        //4商家超时未处理而自动同意用户退款申请的消息，申请已处理
        if (notifyType.equals(MtNotifyTypeEnum.AGREE.getAbbrev()) && MtResTypeEnum.TIMEOUT_AUTO_AGREE == resTypeEnum) {
            return OrderRefundSponsor.CHANNEL.getValue();
        }
        //5系统自动同意退款的消息，例如极速退款，申请已处理
        if (notifyType.equals(MtNotifyTypeEnum.AGREE.getAbbrev()) && MtResTypeEnum.SYSTEM_AUTO_CONFIRM == resTypeEnum) {
            return OrderRefundSponsor.CHANNEL.getValue();
        }
        return OrderRefundSponsor.UNKNOWN.getValue();
    }

    private static boolean isCallOperator(MtResTypeEnum resTypeEnum) {
        return MtResTypeEnum.UNTREATED == resTypeEnum || MtResTypeEnum.CALL_REJECT == resTypeEnum;
    }

    private static boolean isChannelAutoOperator(MtResTypeEnum resTypeEnum) {
        return MtResTypeEnum.TENANT_AGREE == resTypeEnum || MtResTypeEnum.CALL_AGREE == resTypeEnum;
    }

    public static int elmSponsorMapping(String notifyType, String status) {
        try {
            int type = Integer.parseInt(status);
            //10:发起申请,20:客服介入,30:客服拒绝,40:客服同意,50:商户拒绝,60:商户同意,70:申请失效
            switch (type) {
                case 10:
                    return OrderRefundSponsor.CUSTOMER.getValue();
                case 20:
                case 30:
                case 40:
                    return OrderRefundSponsor.CHANNEL.getValue();
                case 50:
                case 60:
                    return OrderRefundSponsor.TENANT.getValue();
                case 70:
                    return OrderRefundSponsor.UNKNOWN.getValue();
                default:
                    return OrderRefundSponsor.UNKNOWN.getValue();
            }
        } catch (NumberFormatException e) {
            log.warn("ChannelStatusConvertUtil.elmPartSponsorMapping, 渠道订单状态类型转换异常, notifyType:{}, status:{}", notifyType, status, e);
        }
        return OrderRefundSponsor.TENANT.getValue();
    }

    public static int elmPartSponsorMapping(String notifyType, String status) {
        try {
            //10表示商家/用户发起部分退款申请 20表示部分退款成功 30用户申请仲裁,客服介入 40表示部分退款失败 50表示商家拒绝用户发起的部分退款申请
            int partRefundStatus = Integer.parseInt(status);
            switch (partRefundStatus) {
                case 10:
                    return genSponsorByNotifyType(notifyType);
                case 30:
                    return OrderRefundSponsor.CHANNEL.getValue();
                case 20:
                case 40:
                    return OrderRefundSponsor.CUSTOMER.getValue();
                case 50:
                    return OrderRefundSponsor.TENANT.getValue();
                default:
                    return OrderRefundSponsor.UNKNOWN.getValue();
            }
        } catch (NumberFormatException e) {
            log.warn("ChannelStatusConvertUtil.elmPartSponsorMapping, 渠道订单状态类型转换异常, notifyType:{}, status:{}", notifyType, status, e);
        }
        return OrderRefundSponsor.TENANT.getValue();
    }

    private static int genSponsorByNotifyType(String notifyType) {
        CancelTypeEnum cancelTypeEnum = EnumUtil.getEnumByAbbrev(notifyType, CancelTypeEnum.class);
        if (Objects.isNull(cancelTypeEnum)) {
            return OrderRefundSponsor.UNKNOWN.getValue();
        }
        switch (cancelTypeEnum) {
            case ELM_USER_CANCEL:
                return OrderRefundSponsor.TENANT.getValue();
            case ELM_USER_REFUND:
                return OrderRefundSponsor.CUSTOMER.getValue();
            default:
                return OrderRefundSponsor.UNKNOWN.getValue();
        }
    }

    /**
     * 歪马配送方式映射
     */
    public static String drunkHorseDeliveryMethodMapping() {
        return "自配送";
    }

    /**
     * MT配送方式映射
     */
    public static String mtDeliveryMethodMapping(String code, Integer pickType) {

        if (Integer.valueOf(1).equals(pickType)) {
            return "用户自提";
        }else if (StringUtils.isBlank(code)){
            return "";
        }

        switch (code) {
            case "0000":
                return "商家自配";
            case "0002":
                return "趣活";
            case "0016":
                return "达达";
            case "0033":
                return "E代送";
            case "1001":
                return "美团专送-加盟";
            case "1002":
                return "美团专送-自建";
            case "1003":
                return "美团跑腿（原众包）";
            case "1004":
                return "美团专送-城市代理";
            case "2001":
                return "角马";
            case "2002":
                return "快送";
            case "3001":
                return "混合送（即美团专送+快送）";
            case "30011001":
                return "混合加盟";
            case "30011002":
                return "混合自建";
            case "30012002":
                return "混合快送";
            case "2010":
                return "全城送";
            case "4001":
                return "美团企客-光速达";
            case "4011":
                return "美团企客-快速达";
            case "4012":
                return "美团企客-及时达";
            case "4015":
                return "美团企客-全城送";
            default:
                log.warn("ChannelStatusConvertUtil.mtDeliveryMethodMapping, 美团未知的配送方式, code:{}", code);
                //return Strings.EMPTY;
                //非自提订单、配送方式未知返回兜底文案
                return MccConfigUtil.mtDeliveryMethodNameDefaultSwitch() ? "未知配送" : Strings.EMPTY;
        }
    }


    /**
     * MT自提码,day_seq参数为自提单的取货码
     */
    public static String mtSelfDeliveryCode(Integer pickType, String daySeq) {
        try {
            if (Integer.valueOf(1).equals(pickType) && Objects.nonNull(daySeq)) {
                return daySeq;
            }
            return "";
        } catch (Exception e) {
            log.warn("mt 取货类型处理错误", e);
            return "";
        }
    }



    /**
     * EB配送方式映射，新逻辑，新接取货类型字段
     */
    public static String elmDeliveryMethodMapping(String code, String businessType) {
        if (MccConfigUtil.getElmDeliveryMethodMappingSwitcher()) {
            try {
                int type=Integer.parseInt(businessType);
                //新逻辑
                ElmBusinessTypeEnum elmBusinessTypeEnum = ElmBusinessTypeEnum.enumOf(type);
                switch (elmBusinessTypeEnum) {
                    case HOME_DELIVERY:
                        return elmDeliveryMethodMapping(code);
                    case STORE_DELIVERY:
                        return "用户自提";
                    case UN_KNOWN:
                    default:
                        return elmDeliveryMethodMapping(code);
                }
            }catch (Exception e){
                log.warn("elm 取货类型处理错误,配送方式采用老映射方式",e);
                return elmDeliveryMethodMapping(code);
            }
        } else {
            //老逻辑
            return elmDeliveryMethodMapping(code);
        }
    }

    /**
     * ELE自提码,pick_up_code参数为自提单的取货码
     */
    public static String eleSelfDeliveryCode(String businessType, String pickUpCode) {
        try {
            int type = Integer.parseInt(businessType);
            //新逻辑
            ElmBusinessTypeEnum elmBusinessTypeEnum = ElmBusinessTypeEnum.enumOf(type);
            switch (elmBusinessTypeEnum) {
                case STORE_DELIVERY:
                    return pickUpCode;
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("elm 取货类型处理错误", e);
            return "";
        }
    }


    /**
     * EB配送方式映射
     */
    public static String elmDeliveryMethodMapping(String code) {
        switch (code) {
            case "1":
                return "蜂鸟";
            case "2":
                return "蜂鸟自配送";
            case "3":
                return "蜂鸟众包";
            case "4":
                return "饿了么众包";
            case "5":
                return "蜂鸟配送";
            case "6":
                return "饿了么自配送";
            case "7":
                return "全城送";
            case "8":
                return "快递配送";
            case "9":
                return "半日达";
            case "10":
                return "鸟潮小时达";
            default:
                try {
                    // 从Lion配置中获取映射关系
                    Map<String, String> elmDeliveryMethodConfig = MccConfigUtil.getElmDeliveryMethodConfig();
                    String deliveryMethod = elmDeliveryMethodConfig.get(code);
                    if (StringUtils.isNotBlank(deliveryMethod)) {
                        return deliveryMethod;
                    } else {
                        // 配置获取不到时，获取配置的默认值
                        log.warn("ChannelStatusConvertUtil.elmDeliveryMethodMapping, 饿百未知的配送方式, code:{}", code);
                        return elmDeliveryMethodConfig.get("default");
                    }
                } catch (Exception e) {
                    log.error("elmDeliveryMethodMapping error code:{},", code, e);
                }
                return "未知";
        }
    }

    /**
     * @param elmOpUserType 逆向单操作者角色：退款发起角色:10用户,20商户,25API商家,30客服,40系统,50物流,60风控
     * @return
     */
    public static int elmOpUserType(String elmOpUserType) {
        switch (elmOpUserType) {
            case "10":
                return OrderRefundSponsor.CUSTOMER.getValue();
            case "20":
            case "25":
                return OrderRefundSponsor.TENANT.getValue();
            case "30":
                return OrderRefundSponsor.CHANNEL.getValue();
            default:
                log.warn("不支持的逆向单作者角色：{}", elmOpUserType);
                return OrderRefundSponsor.UNKNOWN.getValue();

        }
    }

    public static int elmRefundTypeMapping(Integer isRefundAll) {
        // 订单所有商品是否已全部退完，0|未退完 1|已退完
        if (isRefundAll == 1) {
            return RefundTypeEnum.ALL.getValue();
        } else {
            return RefundTypeEnum.PART.getValue();
        }
    }


    /***
     * 京东售中退款类型
     * **/
    public static int jddjOnSaleRefundTypeMapping(String status) {
        switch (status) {
            case "20030":
                return OrderAllRefundType.APPLY.getValue();

            case "20031":
                return OrderAllRefundType.TENANT_AGREE.getValue();

            case "20032":
                return OrderAllRefundType.TENANT_REJECT.getValue();

            default:
                log.error("未知京东退款类型:{}", status);
        }
        return -1;
    }

    /**
     * 商家开通平台代审售后单: 待用户反馈（11）， 待客服反馈（12），售后单状态会附加这两个状态。
     * 退款售后单状态变化：待审核（10）——退款处理中（30）——退款成功（32）
     * 退货售后单状态变化：待审核（10）——待退货（110）—— 取货失败即退货失败（113）/取货成功（111）——1111	退货成功-商品已送至门店——1112商家已确认收货—待退款（112）——退货退款成功（114）
     * 退货售后单取消状态变化： 待审核（10）——待退货（110）—— 已取消（50）
     * 直陪售后单状态变化：待审核（10）-待直赔（90）-直赔成功（92）/直赔失败（93）
     * 异常售后单状态变化： 待审核（10）-审核失败（40）
     **/
    //售后单状态（10:待审核,20:待取件,30:退款处理中,31:待商家收货审核,32:退款成功,33:退款失败,40:审核不通过-驳回,
    // 50:客户取消,60:商家收货审核不通过, 91:直赔,92:直赔成功,93:直赔失败,90:待赔付, 110:待退货,111:取货成功,
    // 1101 取货中,1111 退货成功-商品已送至门店,1112 退货成功-商家已确认收货112:退货成功-待退款,113:退货失败,114:退货成功）
    public static int jddjAfterSaleRefundAllRefundTypeMapping(String status, ChannelNotifyEnum notifyEnum) {
        if (ChannelNotifyEnum.JDDJ_NEW_AFTERSALE.equals(notifyEnum)) {
            //新建售后单
            return OrderAllRefundType.APPLY.getValue();
        } else if (ChannelNotifyEnum.JDDJ_UPDATE_AFTERSALE.equals(notifyEnum)) {
            return OrderAllRefundType.UPDATE_APPLY.getValue();
        } else {
            switch (status) {
                case "10":
                    return OrderAllRefundType.APPLY.getValue();//待审批 = 新建
                case "32": //退款成功
                case "92": //直赔成功
                case "114"://退货退款成功
                    return OrderAllRefundType.REFUND_SUCCESS.getValue();
                case "50": //已取消
                    return OrderAllRefundType.APPLY_CANCELED.getValue();
                case "93": //直赔失败
                    log.info("京东遇到异常售后审批订单,取消售后单审批");
                    return OrderAllRefundType.APPLY_CANCELED.getValue();
                case "40": //审核不通过-驳回
                    return OrderAllRefundType.TENANT_REJECT.getValue();
                case "110": //待退货,待退货
                    return OrderRefundGoodsType.FIRST_APPLY_AGREE.getValue();
                case "12"://待客服反馈
                    return OrderAllRefundType.APPEAL_APPLY.getValue();

                default:
                    log.info("其他售后订单消息，不做处理:{}", status);
                    return -1;
            }
        }
    }

    /**
     *
     * 10 待审核;11 待用户反馈;12 待客服反馈;30 退款处理中;32 退款成功;33 退款失败;40 审核失败;50 已取消;90 待直陪;92 直陪成功;
     * 93 直陪失败;110 待退货;111 取货成功;1101 取货中;1111 退货成功-商品已送至门店;1112 退货成功-商家已确认收货;
     *  112 退货成功-待退款;113 退货失败;114 退货退款成功;1201 已确认收货;120 待换货;122 换货失败;123 换货成功;
     *  124 线下解决;130 待维修;133 维修成功;商家开通平台代审售后单: 待用户反馈（11）， 待客服反馈（12），
     *  售后单状态会附加这两个状态。;退款售后单状态变化：待审核（10）——退款处理中（30）——退款成功（32）;退货售后单状态变化：待审核（10）——待退货（110）—— 取货失败即退货失败（113）/取货成功（111）——1111 退货成功-商品已送至门店——1112商家已确认收货—待退款（112）——退货退款成功
     *  （114）;退货售后单取消状态变化： 待审核（10）——待退货（110）—— 已取消（50）;直陪售后单状态变化：
     *  待审核（10）-待直赔（90）-直赔成功（92）/直赔失败（93）;异常售后单状态变化： 待审核（10）-审核失败（40）
     *
     */
    public static int jddjAfterSaleRefundAllRefundGoodsTypeMapping(String status, ChannelNotifyEnum notifyEnum, JddjAfsExtMap extMap) {
        if (ChannelNotifyEnum.JDDJ_NEW_AFTERSALE.equals(notifyEnum)) {
            //新建售后单
            return OrderRefundGoodsType.APPLY.getValue();
        } else if (ChannelNotifyEnum.JDDJ_UPDATE_AFTERSALE.equals(notifyEnum)) {
            return OrderAllRefundType.UPDATE_APPLY.getValue();
        } else {
            switch (status) {
                case "10":
                    return OrderRefundGoodsType.APPLY.getValue();//待审批 = 新建
                case "32": //退款成功
                case "92": //直赔成功
                case "114"://退货退款成功
                case "1112":
                        return OrderRefundGoodsType.TENANT_AGREE.getValue();
                case "112": //退货成功-待退款
                    if(extMap != null){
                        if(Objects.equals(extMap.getRefundReturn(), 1)){
                            // 先退款再退货流程，112表示初审通过
                            return OrderRefundGoodsType.FIRST_APPLY_AGREE.getValue();
                        } else if(Objects.equals(extMap.getPickUpRefund(), 1)){
                            // 取件即退款流程，112表示取货成功
                            return OrderRefundGoodsType.CUSTOMER_SENT.getValue();
                        }
                    }
                    return OrderRefundGoodsType.TENANT_AGREE.getValue();
                case "111": // 取货成功
                case "1111":
                    return OrderRefundGoodsType.CUSTOMER_SENT.getValue();
                case "116": // 取件即退款,待退货
                    return OrderRefundGoodsType.REFUND_CUSTOMER_SENT.getValue();
                case "110": //待退货,待退货
                    return OrderRefundGoodsType.FIRST_APPLY_AGREE.getValue();
                case "115": //退款成功待退货
                    return OrderRefundGoodsType.REFUND_WAITE_RETURN.getValue();
                case "50": //已取消
                    return OrderRefundGoodsType.APPLY_CANCELED.getValue();
                case "113":
                    return OrderRefundGoodsType.TENANT_REJECT.getValue();
                case "93": //直赔失败
                    log.info("京东遇到异常售后审批订单,取消售后单审批");
                    return OrderRefundGoodsType.APPLY_CANCELED.getValue();
                case "40": //审核不通过-驳回
                    return OrderRefundGoodsType.FIRST_APPLY_REJECT.getValue();
                case "12"://待客服反馈
                    return OrderRefundGoodsType.APPLY_APPEAL.getValue();

                default:
                    log.info("其他售后订单消息，不做处理:{}", status);
                    return -1;
            }
        }
    }

    /***
     * 售后发起方（10:客服，20:用户APP，40:商家，50:用户H5，60:用户RN）
     * ***/
    public static int jddjSponsorMapping(int orderSource) {
        switch (orderSource) {
            case 10:
                return OrderRefundSponsor.CHANNEL.getValue();
            case 20:
            case 50:
            case 60:
                return OrderRefundSponsor.CUSTOMER.getValue();
            case 40:
                return OrderRefundSponsor.TENANT.getValue();
            default:
                return OrderRefundSponsor.UNKNOWN.getValue();
        }
    }

    public static int jddjAfterSaleRefundPartRefundTypeMapping(String status, ChannelNotifyEnum notifyEnum) {
        if (ChannelNotifyEnum.JDDJ_NEW_AFTERSALE.equals(notifyEnum)) {
            //新建售后单
            return OrderPartRefundType.APPLY.getValue();
        } else if (ChannelNotifyEnum.JDDJ_UPDATE_AFTERSALE.equals(notifyEnum)) {
            return OrderPartRefundType.UPDATE_APPLY.getValue();
        } else {
            switch (status) {
                case "10":
                    return OrderPartRefundType.APPLY.getValue();
                case "32": //退款成功
                case "92": //直赔成功
                case "114"://退货退款成功
                    return OrderPartRefundType.REFUND_SUCCESS.getValue();
                case "50": //已取消
                    return OrderPartRefundType.APPLY_CANCELED.getValue();
                case "93": //直赔失败
                    log.info("京东遇到异常售后审批订单,取消售后单审批");
                    return OrderPartRefundType.APPLY_CANCELED.getValue();
                case "40": //审核不通过-驳回
                    return OrderPartRefundType.TENANT_REJECT.getValue();
                case "110": //待退货,待退货
                    return OrderRefundGoodsType.FIRST_APPLY_AGREE.getValue();
                case "12"://待客服反馈
                    return OrderPartRefundType.APPEAL_APPLY.getValue();
                default:
                    log.info("其他售后订单消息，不做处理:{}", status);
                    return -1;
            }
        }
    }

    public static int jddjAfsRecordMapping(String status) {
        switch (status) {
            case "10"://待审核
                return AfterSaleRecordStatus.COMMIT.getValue();//提交
            case "32": //退款成功

            case "92": //直赔成功
            case "114"://退货退款成功
                return AfterSaleRecordStatus.AUDITED.getValue();//已审核
            case "50": //已取消
            case "93": //直赔失败
                return AfterSaleRecordStatus.CANCEL.getValue();//已取消
            case "40": //审核不通过-驳回
                return AfterSaleRecordStatus.AUDITED_REJECT.getValue();//已审核驳回
            case "110": //待退货,待退货
                return AfterSaleRecordStatus.AUDIT_ING.getValue();// 因为这个枚举没有退货相关状态，用审核中替代，否则问题更加严重
            default:
                return 0;
        }
    }


    public static int parseLong2Int(Long value){
        return Optional.ofNullable(value).map(Long::intValue).orElse(0);
    }

    public static int jddjAfsRefundType(Integer isLastApply){
        if(isLastApply == null){
            return RefundTypeEnum.PART.getValue();
        }
        if(isLastApply == 1){
            return RefundTypeEnum.ALL.getValue();
        }
        return RefundTypeEnum.PART.getValue();
    }

    public static int jddjAfsFreightMapping(JddjAfterSaleDetail afterSaleDetail){
        return parseLong2Int(afterSaleDetail.getAfsFreight()) + Optional.ofNullable(afterSaleDetail.getTongchengFreightMoney()).orElse(0);
    }

    public static int jddjAfsRefundPriceMapping(JddjAfterSaleDetail afterSaleDetail){
        //京东物竞天择
       if (Objects.equals(afterSaleDetail.getAfsServiceOrder(), 20)){
           return parseLong2Int(afterSaleDetail.getRefundUserMmoney());
       }else {
           return parseLong2Int(afterSaleDetail.getCashMoney()) + parseLong2Int(afterSaleDetail.getPlatformIntegralDeductMoney())
                   + parseLong2Int(afterSaleDetail.getOrderFreightMoney()) + parseLong2Int(afterSaleDetail.getPackagingMoney())
                   + parseLong2Int(afterSaleDetail.getSelfPickPayMoney());
       }
    }

    /*
    有赞售后状态:
    WAIT_SELLER_AGREE-买家已经申请退款，等待卖家同意;
    WAIT_BUYER_RETURN_GOODS-卖家已经同意退款，等待买家退货;
    WAIT_SELLER_CONFIRM_GOODS-买家已经退货，等待卖家确认收货;
    SELLER_REFUSE_BUYER-卖家拒绝退款;
    SELLER_REFUSE_BUYER_RETURN_GOODS-卖家未收到货,拒绝退款 ;
    SELLER_RETURN_GOODS-商家确认收货并发送换货;
    CLOSED-退款关闭;
    SUCCESS-退款成功;
    */

    public static int yzAfterSaleRefundPartRefundTypeMapping(String status) {
        switch (status) {
            case "WAIT_SELLER_AGREE":
                return OrderPartRefundType.APPLY.getValue();
            case "WAIT_BUYER_RETURN_GOODS":
            case "WAIT_SELLER_CONFIRM_GOODS":
            case "SUCCESS":
                return OrderPartRefundType.REFUND_SUCCESS.getValue();
            case "SELLER_REFUSE_BUYER":
            case "SELLER_REFUSE_BUYER_RETURN_GOODS":
                return OrderPartRefundType.TENANT_REJECT.getValue();
            case "CLOSED":
                return OrderPartRefundType.APPLY_CANCELED.getValue();
            default:
                log.info("其他售后订单消息，不做处理:{}", status);
                return -1;
        }
    }

    public static int yzAfterSaleRefundAllRefundTypeMapping(String status) {
        switch (status) {
            case "WAIT_SELLER_AGREE":
                return OrderAllRefundType.APPLY.getValue();
            case "WAIT_BUYER_RETURN_GOODS":
            case "WAIT_SELLER_CONFIRM_GOODS":
            case "SUCCESS":
                return OrderAllRefundType.REFUND_SUCCESS.getValue();
            case "SELLER_REFUSE_BUYER":
            case "SELLER_REFUSE_BUYER_RETURN_GOODS":
                return OrderAllRefundType.TENANT_REJECT.getValue();
            case "CLOSED":
                return OrderAllRefundType.APPLY_CANCELED.getValue();
            default:
                log.info("其他售后订单消息，不做处理:{}", status);
                return -1;
        }
    }

    /*
   有赞售后状态:
   WAIT_SELLER_AGREE-买家已经申请退款，等待卖家同意;
   WAIT_BUYER_RETURN_GOODS-卖家已经同意退款，等待买家退货;
   WAIT_SELLER_CONFIRM_GOODS-买家已经退货，等待卖家确认收货;
   SELLER_REFUSE_BUYER-卖家拒绝退款;
   SELLER_REFUSE_BUYER_RETURN_GOODS-卖家未收到货,拒绝退款 ;
   SELLER_RETURN_GOODS-商家确认收货并发送换货;
   CLOSED-退款关闭;
   SUCCESS-退款成功;
   */
    public static int yzAfterSaleRefundGoodsTypeMapping(String status) {
        switch (status) {
            case "WAIT_SELLER_AGREE":
                return OrderRefundGoodsType.APPLY.getValue();
            case "WAIT_BUYER_RETURN_GOODS":
                return OrderRefundGoodsType.FIRST_APPLY_AGREE.getValue();
            case "SELLER_REFUSE_BUYER":
                return OrderRefundGoodsType.FIRST_APPLY_REJECT.getValue();
            case "CLOSED":
                return OrderRefundGoodsType.APPLY_CANCELED.getValue();
            case "SUCCESS":
                return OrderRefundGoodsType.TENANT_AGREE.getValue();
            case "SELLER_REFUSE_BUYER_RETURN_GOODS":
                return OrderRefundGoodsType.TENANT_REJECT.getValue();

            default:
                log.info("其他售后订单消息，不做处理:{}", status);
                return -1;
        }
    }

    /*
   有赞售后状态:
   WAIT_SELLER_AGREE-买家已经申请退款，等待卖家同意;
   WAIT_BUYER_RETURN_GOODS-卖家已经同意退款，等待买家退货;
   WAIT_SELLER_CONFIRM_GOODS-买家已经退货，等待卖家确认收货;
   SELLER_REFUSE_BUYER-卖家拒绝退款;
   SELLER_REFUSE_BUYER_RETURN_GOODS-卖家未收到货,拒绝退款 ;
   SELLER_RETURN_GOODS-商家确认收货并发送换货;
   CLOSED-退款关闭;
   SUCCESS-退款成功;
   */
    public static int yzAfsRecordMapping(String status) {
        switch (status) {
            case "WAIT_SELLER_AGREE":
                return AfterSaleRecordStatus.COMMIT.getValue();
            case "WAIT_BUYER_RETURN_GOODS":
            case "WAIT_SELLER_CONFIRM_GOODS":
            case "SUCCESS":
                return AfterSaleRecordStatus.AUDITED.getValue();//已审核
            case "CLOSED":
                return AfterSaleRecordStatus.CANCEL.getValue();//已取消
            case "SELLER_REFUSE_BUYER":
            case "SELLER_REFUSE_BUYER_RETURN_GOODS":
                return AfterSaleRecordStatus.AUDITED_REJECT.getValue();//已审核驳回
            default:
                return 0;
        }
    }

    public static int jddjAfterSaleStatusMapping(String status, String applyDeal, JddjAfsExtMap extMap) {
        ServiceTypeEnum serviceTypeEnum = JDApplyDealTypeEnum.enumOf(applyDeal) == JDApplyDealTypeEnum.REFUND_GOODS ? ServiceTypeEnum.REFUND_GOODS : ServiceTypeEnum.REFUND;

        if (serviceTypeEnum == ServiceTypeEnum.REFUND_GOODS) {
            return jddjAfterSaleRefundAllRefundGoodsTypeMapping(status, null, extMap);
        }

        return jddjAfterSaleRefundAllRefundTypeMapping(status, null);
    }

    public static boolean jddjIsAppeal(String status, Integer dutyAssume, Integer approveType) {
        JddjDutyAssumeEnum jddjDutyAssumeEnum = JddjDutyAssumeEnum.enumOf(dutyAssume);

        if (jddjDutyAssumeEnum == JddjDutyAssumeEnum.JDDJ || jddjDutyAssumeEnum == JddjDutyAssumeEnum.LOGISTICS) {
            return true;
        }

        if ("12".equals(status)) {
            return true;
        }

        if (JddjApproveTypeEnum.CUSTOMER_SERVICE.getCode().equals(approveType)) {
            return true;
        }

        return false;
    }


    public static int dyOpUserType(Long applyRole) {
        DouyinApplyRoleEnum douyinApplyRoleEnum = DouyinApplyRoleEnum.valueOfEnum(applyRole);
        if(douyinApplyRoleEnum==null){
            log.warn("不支持的逆向单作者角色：{},applyRole:{}", douyinApplyRoleEnum,applyRole);
            return OrderRefundSponsor.UNKNOWN.getValue();
        }
        switch (douyinApplyRoleEnum){
            case BUYER:
                return OrderRefundSponsor.CUSTOMER.getValue();
            case SELLER:
                return OrderRefundSponsor.TENANT.getValue();
            case CUSTOMER_SERVICE:
            case SYSTEM:
                return OrderRefundSponsor.CHANNEL.getValue();
            default:
                log.warn("不支持的逆向单作者角色：{},applyRole:{}", douyinApplyRoleEnum,applyRole);
                return OrderRefundSponsor.UNKNOWN.getValue();
        }
    }

    public static int elmReturnGoodsStatusMapping(ElmReverseResult.ElmReverseOrderInfo elmReverseOrderInfo){
        ElmReverseResult.ElmReturnGoodsInfo elmReturnGoodsInfo = elmReverseOrderInfo.getReturn_goods_info();
        if (Objects.isNull(elmReturnGoodsInfo) || Objects.isNull(elmReturnGoodsInfo.getReturn_goods_type())) {
            return ReturnGoodsStatusEnum.UNKNOWN.getCode();
        }
        switch (elmReturnGoodsInfo.getReturn_goods_type()){
            case 0:
                return ReturnGoodsStatusEnum.WAIT_STORE_PICKUP.getCode();
            case 1:
                return ReturnGoodsStatusEnum.WAIT_CUSTOM_SENDBACK.getCode();
            default:
                return ReturnGoodsStatusEnum.UNKNOWN.getCode();
        }
    }

    public static int jddjReturnGoodsStatusMapping(JddjAfterSaleDetail afterSaleDetail) {
        if (afterSaleDetail.getAfsServiceState() != null) {
            // 如果同意退款或拒绝退款则设置为商家是否收到货
            switch (afterSaleDetail.getAfsServiceState()) {
                case 32: //退款成功
                case 92: //直赔成功
                case 114://退货退款成功
                case 112: //退货成功-待退款
                case 1112:
                    // 先退款再退货流程，112不能算作商家已收货
                    if(afterSaleDetail.getAfsServiceState() != 112
                            || afterSaleDetail.getExtMap() == null
                            || !Objects.equals(afterSaleDetail.getExtMap().getRefundReturn(), 1)){
                        return ReturnGoodsStatusEnum.MERCHANT_CONFIRM.getCode();
                    }
                case 113:
                    return ReturnGoodsStatusEnum.MERCHANT_REFUSE.getCode();
            }
        }

        if (afterSaleDetail.getPickwareMethod() == null) {
            return ReturnGoodsStatusEnum.UNKNOWN.getCode();
        }
        switch (afterSaleDetail.getPickwareMethod()) {
            case 1:
            case 3:
                if (Objects.nonNull(afterSaleDetail.getAfsServiceState()) &&
                        (afterSaleDetail.getAfsServiceState() == 111 || afterSaleDetail.getAfsServiceState() == 1111)) {
                    return ReturnGoodsStatusEnum.CUSTOM_SENT.getCode();
                } else {
                    return ReturnGoodsStatusEnum.WAIT_CUSTOM_SENDBACK.getCode();
                }
            case 2:
                return ReturnGoodsStatusEnum.WAIT_STORE_PICKUP.getCode();
            default:
                return ReturnGoodsStatusEnum.UNKNOWN.getCode();
        }
    }

}
