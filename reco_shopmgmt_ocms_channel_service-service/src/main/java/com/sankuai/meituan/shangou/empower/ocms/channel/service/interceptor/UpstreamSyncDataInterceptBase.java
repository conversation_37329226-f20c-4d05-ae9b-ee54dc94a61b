package com.sankuai.meituan.shangou.empower.ocms.channel.service.interceptor;

import com.meituan.dorado.rpc.meta.RpcResult;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 上游数据同步拦截器基类
 * <AUTHOR>
 * @Date 2019-08-30 17:45
 */
@Slf4j
public abstract class UpstreamSyncDataInterceptBase<TResult> extends SyncDataInterceptBase{

    protected abstract List<String> getErrorSkuIds(int channelId, long storeId, InterceptInfo interceptInfo);
    protected abstract List<TResult> getErrorDataList(RpcResult result);

    @Override
    protected long getStoreIdBy(Object requestData, InterceptInfo interceptInfo) {
        return Long.valueOf(requestData.toString());
    }

    @Override
    protected RpcResult processResult(RpcResult result, InterceptInfo interceptInfo) {
        interceptInfo.getChannelStoreIds().forEach((channelId, storeIds) -> {
            List<Long> filteredStoreIds = storeIds.stream().filter(store ->
                    !interceptInfo.getEnableChannelStoreIds().containsKey(channelId) ||
                    !interceptInfo.getEnableChannelStoreIds().get(channelId).contains(store)).collect(Collectors.toList());
            for (Long storeId : filteredStoreIds){
                getErrorDataList(result).addAll(
                        getErrorSkuIds(channelId, storeId, interceptInfo).stream().map(skuId -> createResultErrorSku(channelId, storeId, skuId, interceptInfo)).collect(Collectors.toList())
                );
            }
        });
        return result;
    }

    protected abstract TResult createResultErrorSku(int channelId, long storeId, String skuId, InterceptInfo interceptInfo);

    protected String createKey(int channelId,long storeId) {
        return String.format("%s_%s", channelId, storeId);
    }
}
