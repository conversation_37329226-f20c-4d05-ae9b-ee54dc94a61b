package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSingleData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuPriceResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSkuPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceRequest;
import org.assertj.core.util.Lists;

import javax.annotation.Nullable;
import java.util.List;

/**
 * @description: 渠道商品品类内部服务接口，各渠道父接口
 * @author: chenhaie
 * @create: 2019/1/7 下午5:27
 */
public interface ChannelPriceService {
    /**
     * 更新商品价格
     *
     * @param request
     * @return
     */
    @Deprecated
    ResultData updatePrice(SkuPriceRequest request);

    /**
     * 多渠道更新商品价格接口
     *
     * @param request
     * @return
     */
    ResultData updatePriceMultiChannel(SkuPriceMultiChannelRequest request);

    /**
     * 多渠道更新商品价格接口(用于清洗工具大批量数据)
     *
     * @param request
     * @return
     */
    @Nullable
    default ResultData updatePriceMultiChannelForCleaner(SkuPriceMultiChannelRequest request) {
        return null;
    }

    /**
     * 跟新渠道的单个sku价格
     * @param request
     * @return
     */
    default ResultSingleData updateSingleSkuPrice(SingleSkuPriceRequest request) {
        throw new RuntimeException("Current channel does not support update single sku price");
    }

    /**
     * 多渠道批量查询商品门店价格信息接口
     *
     * @param request
     * @param skuIds
     * @return
     */
    List<ChannelStorePriceInfo> batchGetPriceInfo(BaseRequest request, List<Long> skuIds);

    /**
     * 构造baseRequest
     * @param request
     * @return
     */
    default BaseRequest getBaseRequest(SkuPriceRequest request) {
        return new BaseRequest()
                .setTenantId(request.getTenantId())
                .setChannelId(request.getChannelId())
                .setStoreIdList(Lists.newArrayList(request.getStoreId()))
                .setAsyncInvoke(request.isAsyncInvoke());
    }

    default BatchGetSkuPriceResponse batchGetSkuPrice(BatchGetSkuPriceRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }
}
