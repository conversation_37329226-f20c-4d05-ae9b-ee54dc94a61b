package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelStoreCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanItemcategoriesTaglistSearchResult;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanRetailOpenCategoryQueryResult;
import com.youzan.cloud.open.sdk.gen.v3_0_0.model.YouzanTradeRefundGetResult;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanTradeGetResult;

/**
 * 美团实体转换统一接口类
 *
 * <AUTHOR>
 * @since 6/4/21 6:17 PM
 **/

@Mapper(componentModel = "spring", uses = {MappingConverterUtils.class}, imports = {
        MoneyUtils.class,
        ChannelStatusConvertUtil.class, StringUtils.class, DateUtils.class, Optional.class, Collectors.class, Collections.class,
        YouzanTradeGetResult.class, BigDecimal.class, Objects.class, MtConverterUtil.class, YouzanTradeGetResult.YouzanTradeGetResultIteminfo.class,
        Map.Entry.class})
public interface YzConverterService {


    @Mappings({
            @Mapping(source = "channelOrderDetail.fullOrderInfo.orderInfo.tid", target = "channelOrderId"),
            @Mapping(source = "channelOrderDetail.fullOrderInfo.remarkInfo.buyerMessage", target = "comment"),
    })
    ChannelOrderDetailDTO orderDetailMapping(YouzanTradeGetResult.YouzanTradeGetResultData channelOrderDetail);

    @Mappings({
            @Mapping(expression = "java(StringUtils.isBlank(product.getSkuNo()) ? product.getItemNo() : product.getSkuNo())", target = "skuId"),
            @Mapping(source = "title", target = "skuName"),
            @Mapping(source = "oid", target = "channelItemId"),
            @Mapping(expression = "java(String.valueOf(product.getItemId()))", target = "channelSkuId"),
            @Mapping(source = "skuPropertiesName", target = "specification"),
            @Mapping(source = "itemProps", target = "skuProperty"),
            @Mapping(source = "itemNo", target = "customSpu"),
            @Mapping(source = "num", target = "quantity"),
            @Mapping(source = "picPath", target = "skuPicUrls"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(product.getDiscountPrice()))", target = "salePrice"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(product.getPrice()))", target = "originalPrice"),
            @Mapping(target = "weight", ignore = true),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(product.getPayment()))", target = "totalPayAmtMinusDiscount"),
            @Mapping(source = "skuBarcode", target = "upcCode")
    })
    OrderProductDetailDTO productMapping(YouzanTradeGetResult.YouzanTradeGetResultOrders product);

    @Mappings({
            @Mapping(source = "promotionId", target = "activityId"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderDiscount.getDiscountFee()))", target = "actDiscount"),
            @Mapping(expression = "java(MoneyUtils.yuanToFen(orderDiscount.getDiscountFee()))", target = "bizCharge"),
            @Mapping(expression = "java(Optional.ofNullable(orderDiscount.getItemInfo()).orElse(Collections.emptyList()).stream().map" +
                    "(YouzanTradeGetResult.YouzanTradeGetResultIteminfo::getOid).collect(Collectors.toList()))",
                    target = "skuInfo"),
            @Mapping(expression = "java(Optional.ofNullable(orderDiscount.getPromotionTitle()).filter(v -> StringUtils.isNotEmpty(v))" +
                    ".orElse(orderDiscount.getPromotionContent()))",
                    target = "remark"),
            @Mapping(source = "promotionType", target = "type"),
    })
    OrderDiscountDetailDTO orderActivitiyInfoMapping(YouzanTradeGetResult.YouzanTradeGetResultOrder orderDiscount);

    List<OrderDiscountDetailDTO> orderActivitiyInfosMapping(List<YouzanTradeGetResult.YouzanTradeGetResultOrder> orderDiscountList);


    @Mappings({
            @Mapping(source = "itemId", target = "customSkuId"),
            @Mapping(source = "oid", target = "channelItemId"),
            @Mapping(target = "agentCost", constant = "0"),
            @Mapping(target = "logisticsCost", constant = "0"),
            @Mapping(target = "channelJiFenCost", constant = "0"),
            @Mapping(target = "channelPromotionType", constant = "0"),
            @Mapping(target = "goodActivityDetail", expression = "java(sharedActivityItemsMapping(itemActivity.getPromotions()))")

    })
    GoodsActivityDetailDTO activityDetailMapping(YouzanTradeGetResult.YouzanTradeGetResultItem itemActivity);

    List<GoodsActivityDetailDTO> activityDetailsMapping(List<YouzanTradeGetResult.YouzanTradeGetResultItem> itemActivities);

    @Mappings({
            @Mapping(target = "channelPromotionType", source = "promotionType"),
            @Mapping(target = "promotionRemark", expression = "java(Optional.ofNullable(promotion.getPromotionTitle())" +
                    ".filter(v -> StringUtils.isNotEmpty(v)).orElse(promotion.getPromotionContent()))"),
            @Mapping(target = "tenantCost", expression = "java(Objects.nonNull(promotion.getDiscountFee()) ? MoneyUtils.yuanToFen(promotion.getDiscountFee()) : 0L )"),
            @Mapping(target = "activityId", source = "promotionId"),
            @Mapping(target = "promotionCount", constant = "1")
    })
    GoodsSharedActivityItem sharedActivityItemMapping(YouzanTradeGetResult.YouzanTradeGetResultPromotions promotion);

    List<GoodsSharedActivityItem> sharedActivityItemsMapping(List<YouzanTradeGetResult.YouzanTradeGetResultPromotions> promotions);


    @Mappings({
            @Mapping(source = "itemNum", target = "count"),
            @Mapping(source = "refundFee", target = "skuRefundAmount"),
            @Mapping(source = "oid", target = "channelOrderItemId")
    })
    RefundProductDTO convertRefundProductDTO(YouzanTradeRefundGetResult.YouzanTradeRefundGetResultRefundorderitem afsDetail);

    List<RefundProductDTO> convertRefundProductDTOs(List<YouzanTradeRefundGetResult.YouzanTradeRefundGetResultRefundorderitem> afsDetails);


    @Mappings({
            @Mapping(target = "catId", source = "id"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "depth", constant = "1"),
    })
    CatInfo convertCatInfo(YouzanItemcategoriesTaglistSearchResult.YouzanItemcategoriesTaglistSearchResultTags tag);

    List<CatInfo> convertCatInfos(List<YouzanItemcategoriesTaglistSearchResult.YouzanItemcategoriesTaglistSearchResultTags> tags);
}
