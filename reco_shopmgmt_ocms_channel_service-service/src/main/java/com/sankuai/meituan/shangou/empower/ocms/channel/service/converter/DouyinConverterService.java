package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ChannelConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelMaterialSearchDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelMaterialUploadDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.bill.DouyinBalanceBillResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialSearchRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.DataItem;

/**
 * 抖音实体转换统一接口类
 *
 * <AUTHOR>
 * @create 2023-12-19 下午8:09
 **/
@Mapper(componentModel = "spring", imports = {ChannelConstant.class})
public interface DouyinConverterService {

    @Mappings({
            @Mapping(target = "materials", source = "materials"),
            @Mapping(target = "need_distinct", source = "needDistinct")
    })
    ChannelMaterialUploadDto materialUploadDtoMapping(MaterialUploadRequest materialUploadRequest);

    @Mappings({
            @Mapping(target = "request_id", source = "requestId"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "url", source = "url"),
            @Mapping(target = "folder_id", expression = "java(ChannelConstant.DOUYIN_MATERIAL_DEFAULT_FOLDER_ID)")
    })
    ChannelMaterialUploadDto.MaterialDto materialDtoMapping(MaterialUploadRequest.Material material);

    @Mappings({
            @Mapping(target = "material_id_list", source = "materialIdList"),
            @Mapping(target = "material_type", source = "materialTypeList"),
            @Mapping(target = "operate_status", source = "operateStatusList"),
            @Mapping(target = "audit_status", source = "auditStatusList"),
    })
    ChannelMaterialSearchDto materialSearchDtoMapping(MaterialSearchRequest materialSearchRequest);


    @Mappings({
            @Mapping(target = "governmentPromotionShopReduceAmount", source = "governmentPromotionShopReduceAmount"),
            @Mapping(target = "accountType", source = "accountType"),
            @Mapping(target = "authorCouponAmount", source = "authorCouponAmount"),
            @Mapping(target = "bankPayPromotionAmount", source = "bankPayPromotionAmount"),
            @Mapping(target = "unitShopId", source = "unitShopId"),
            @Mapping(target = "storeShopName", source = "storeShopName"),
            @Mapping(target = "partnerCommission", source = "partnerCommission"),
            @Mapping(target = "unitDivide", source = "unitDivide"),
            @Mapping(target = "deliveryType", source = "deliveryType"),
            @Mapping(target = "source", source = "source"),
            @Mapping(target = "mixedCouponShopCostApportAmount", source = "mixedCouponShopCostApportAmount"),
            @Mapping(target = "platformServiceFee", source = "platformServiceFee"),
            @Mapping(target = "partnerShopId", source = "partnerShopId"),
            @Mapping(target = "freeCommissionFlag", source = "freeCommissionFlag"),
            @Mapping(target = "shopCoupon", source = "shopCoupon"),
            @Mapping(target = "mainDivide", source = "mainDivide"),
            @Mapping(target = "packingAmount", source = "packingAmount"),
            @Mapping(target = "mainShopId", source = "mainShopId"),
            @Mapping(target = "authorId", source = "authorId"),
            @Mapping(target = "orderFinishTime", source = "orderFinishTime"),
            @Mapping(target = "outcomeTotalAmount", source = "outcomeTotalAmount"),
            @Mapping(target = "postPromotionAmount", source = "postPromotionAmount"),
            @Mapping(target = "ztPlatformPayPromotionAmount", source = "ztPlatformPayPromotionAmount"),
            @Mapping(target = "unitShopName", source = "unitShopName"),
            @Mapping(target = "orderId", source = "orderId"),
            @Mapping(target = "partnerName", source = "partnerName"),
            @Mapping(target = "payAmount", source = "payAmount"),
            @Mapping(target = "postAmount", source = "postAmount"),
            @Mapping(target = "incomeTotalAmount", source = "incomeTotalAmount"),
            @Mapping(target = "recyclerAmount", source = "recyclerAmount"),
            @Mapping(target = "shopOrderId", source = "shopOrderId"),
            @Mapping(target = "shopDeductionAmount", source = "shopDeductionAmount"),
            @Mapping(target = "postDeductionPlatform", source = "postDeductionPlatform"),
            @Mapping(target = "storeShopId", source = "storeShopId"),
            @Mapping(target = "authorName", source = "authorName"),
            @Mapping(target = "colonelCommission", source = "colonelCommission"),
            @Mapping(target = "settleAmount", source = "settleAmount"),
            @Mapping(target = "orderTradeTime", source = "orderTradeTime"),
            @Mapping(target = "promotionAmount", source = "promotionAmount"),
            @Mapping(target = "douCustomerCommission", source = "douCustomerCommission"),
            @Mapping(target = "orderCreateTime", source = "orderCreateTime"),
            @Mapping(target = "settleStatus", source = "settleStatus"),
            @Mapping(target = "platformServiceFeeRate", source = "platformServiceFeeRate"),
            @Mapping(target = "orderType", source = "orderType"),
            @Mapping(target = "settleTime", source = "settleTime"),
            @Mapping(target = "remark", source = "remark"),
            @Mapping(target = "productPriceTotalAmount", source = "productPriceTotalAmount"),
            @Mapping(target = "zrPlatformPayPromotionAmount", source = "zrPlatformPayPromotionAmount"),
            @Mapping(target = "mainShopName", source = "mainShopName"),
            @Mapping(target = "authorCommission", source = "authorCommission"),
            @Mapping(target = "storeDivide", source = "storeDivide"),
            @Mapping(target = "postDeductionShop", source = "postDeductionShop"),
            @Mapping(target = "refundSalesNo", source = "refundSalesNo")
    })
    DouyinBalanceBillResult.DataItem toRetailSettleDataItem(
            com.doudian.open.api.retail_settle_queryTradeBillItem.data.DataItem douyinDataItem);


    @Mappings({
            @Mapping(source = "code", target = "code"),
            @Mapping(source = "codeMsg", target = "codeMsg"),
            @Mapping(source = "hasNext", target = "hasNext"),
            @Mapping(source = "nextCursor", target = "nextCursor"),
            @Mapping(expression = "java(convertDouyinDataItems(dyBalanceBillResult.getData()))", target = "data")
    })
    com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement.DouyinBalanceBillResult douyinBalanceBillResultMapping(com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.bill.DouyinBalanceBillResult dyBalanceBillResult);


    List<DataItem> convertDouyinDataItems(List<com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.bill.DouyinBalanceBillResult.DataItem> dataItems);



    @Mappings({
            @Mapping(target = "governmentPromotionShopReduceAmount", source = "governmentPromotionShopReduceAmount"),
            @Mapping(target = "accountType", source = "accountType"),
            @Mapping(target = "authorCouponAmount", source = "authorCouponAmount"),
            @Mapping(target = "bankPayPromotionAmount", source = "bankPayPromotionAmount"),
            @Mapping(target = "unitShopId", source = "unitShopId"),
            @Mapping(target = "storeShopName", source = "storeShopName"),
            @Mapping(target = "partnerCommission", source = "partnerCommission"),
            @Mapping(target = "unitDivide", source = "unitDivide"),
            @Mapping(target = "deliveryType", source = "deliveryType"),
            @Mapping(target = "source", source = "source"),
            @Mapping(target = "mixedCouponShopCostApportAmount", source = "mixedCouponShopCostApportAmount"),
            @Mapping(target = "platformServiceFee", source = "platformServiceFee"),
            @Mapping(target = "partnerShopId", source = "partnerShopId"),
            @Mapping(target = "freeCommissionFlag", source = "freeCommissionFlag"),
            @Mapping(target = "shopCoupon", source = "shopCoupon"),
            @Mapping(target = "mainDivide", source = "mainDivide"),
            @Mapping(target = "packingAmount", source = "packingAmount"),
            @Mapping(target = "mainShopId", source = "mainShopId"),
            @Mapping(target = "authorId", source = "authorId"),
            @Mapping(target = "orderFinishTime", source = "orderFinishTime"),
            @Mapping(target = "outcomeTotalAmount", source = "outcomeTotalAmount"),
            @Mapping(target = "postPromotionAmount", source = "postPromotionAmount"),
            @Mapping(target = "ztPlatformPayPromotionAmount", source = "ztPlatformPayPromotionAmount"),
            @Mapping(target = "unitShopName", source = "unitShopName"),
            @Mapping(target = "orderId", source = "orderId"),
            @Mapping(target = "partnerName", source = "partnerName"),
            @Mapping(target = "payAmount", source = "payAmount"),
            @Mapping(target = "postAmount", source = "postAmount"),
            @Mapping(target = "incomeTotalAmount", source = "incomeTotalAmount"),
            @Mapping(target = "recyclerAmount", source = "recyclerAmount"),
            @Mapping(target = "shopOrderId", source = "shopOrderId"),
            @Mapping(target = "shopDeductionAmount", source = "shopDeductionAmount"),
            @Mapping(target = "postDeductionPlatform", source = "postDeductionPlatform"),
            @Mapping(target = "storeShopId", source = "storeShopId"),
            @Mapping(target = "authorName", source = "authorName"),
            @Mapping(target = "colonelCommission", source = "colonelCommission"),
            @Mapping(target = "settleAmount", source = "settleAmount"),
            @Mapping(target = "orderTradeTime", source = "orderTradeTime"),
            @Mapping(target = "promotionAmount", source = "promotionAmount"),
            @Mapping(target = "douCustomerCommission", source = "douCustomerCommission"),
            @Mapping(target = "orderCreateTime", source = "orderCreateTime"),
            @Mapping(target = "settleStatus", source = "settleStatus"),
            @Mapping(target = "platformServiceFeeRate", source = "platformServiceFeeRate"),
            @Mapping(target = "orderType", source = "orderType"),
            @Mapping(target = "settleTime", source = "settleTime"),
            @Mapping(target = "remark", source = "remark"),
            @Mapping(target = "productPriceTotalAmount", source = "productPriceTotalAmount"),
            @Mapping(target = "zrPlatformPayPromotionAmount", source = "zrPlatformPayPromotionAmount"),
            @Mapping(target = "mainShopName", source = "mainShopName"),
            @Mapping(target = "authorCommission", source = "authorCommission"),
            @Mapping(target = "storeDivide", source = "storeDivide"),
            @Mapping(target = "postDeductionShop", source = "postDeductionShop"),
            @Mapping(target = "refundSalesNo", source = "refundSalesNo")
    })
    DataItem convertDouyinDataItem(com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.bill.DouyinBalanceBillResult.DataItem dataItem);
}