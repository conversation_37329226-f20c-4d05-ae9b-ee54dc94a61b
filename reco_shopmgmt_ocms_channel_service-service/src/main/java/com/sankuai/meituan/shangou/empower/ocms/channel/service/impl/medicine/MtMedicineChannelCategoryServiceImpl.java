package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTMedicineEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelCategoryService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.medicine.ChannelMedicineCatDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.medicine.ChannelMedicineCatDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtMedicineConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.CommonThreadPool;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 美团医药渠道商品分类内部服务接口
 *
 * <AUTHOR>
 * @create 2020-07-31 下午4:39
 **/
@Service("mtMedicineChannelCategoryService")
public class MtMedicineChannelCategoryServiceImpl implements ChannelCategoryService {
    @Resource
    private BaseConverterService baseConverterService;

    @Resource
    private MtConverterService mtConverterService;

    @Resource
    private MtMedicineChannelGateService mtMedicineChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Value("${mt.url.base}" + "${mt.url.getSpTagIds}")
    private String getSpTagIds;

    @Value("${mt.url.base}" + "${mt.url.categoryCreate}")
    private String categoryCreate;

    @Resource
    private CommonThreadPool commonThreadPool;

    @Resource
    private ClusterRateLimiter clusterRateLimiter;

    @Resource
    private CopAccessConfigService copAccessConfigService;

    @Resource
    private CommonLogger log;

    /**
     * 美团失败关键字
     * 用于判断返回的消息题中的msg字段的匹配，如果匹配到则认为是部分成功；中台认为失败。
     */
    public static final String MT_FAIL_KEYWORD = "失败";

    @Override
    public CreateCategoryResponse createCategory(CategoryRequest request) {
        log.info("开始创建美团药品分类 CategoryRequest:{}", request);
        CreateCategoryResponse resp = new CreateCategoryResponse();
        List<CategoryPoiResult> categoryPoiResults = Lists.newArrayList();
        long tenantId = request.getBaseInfo().getTenantId();
        int channelId = request.getBaseInfo().getChannelId();
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo(), request.getParamList());
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setData(Collections.emptyList());
        }
        // 美团应用维度限流，目前仅为进程内限流。这里取appid只为获取请求令牌 根据rhino管理端配置执行重试策略。新的sendpost方法还会再获取一次appid
        String sysParamJson = copAccessConfigService.selectSysParams(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId());
        Preconditions.checkArgument(StringUtils.isNotBlank(sysParamJson), "copAccessConfig error tenantId=%s,channelId=%s", tenantId, channelId);
        Map<String, Object> sysParam = JSON.parseObject(sysParamJson);
        Preconditions.checkArgument(MapUtils.isNotEmpty(sysParam) && sysParam.containsKey(ProjectConstant.MT_APP_ID_KEY), "未获取到美团appId");
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));
        //先创建一级分类
        for (CategoryInfoDTO categoryInfoDTO : request.getParamList()) {
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setStoreId(categoryInfoDTO.getStoreId())
                    .setCode(categoryInfoDTO.getCode());
            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }
            if (StringUtils.isNotEmpty(categoryInfoDTO.getParentName())) {
                continue;
            }
            Map createParam = Maps.newHashMap();

            createParam.put(ProjectConstant.APP_POI_CODE, channelStoreDOMap.get(KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(),
                    request.getBaseInfo().getChannelId(), categoryInfoDTO.getStoreId())).getChannelOnlinePoiCode());


            createParam.put(ProjectConstant.CATEGORY_CODE, categoryInfoDTO.getCode());
            createParam.put(ProjectConstant.CATEGORY_NAME, categoryInfoDTO.getName());
            createParam.put(ProjectConstant.SEQUENCE, categoryInfoDTO.getSort());

            // 限频控制
            if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostMTMedicineEnum.MEDICINECAT_SAVE, appId)) {
                log.warn("Call blocking failed frequently, Continue");
            }

            Map createCategoryMap = mtMedicineChannelGateService.sendPostApp(mtMedicineChannelGateService.getPostUrl(ChannelPostMTMedicineEnum.MEDICINECAT_SAVE), null, baseConverterService.baseRequest(request.getBaseInfo()), createParam);
            log.info("创建美团药品分类结果 createCategoryMap:{}", createCategoryMap);
            if ("ok".equals(createCategoryMap.get(ProjectConstant.DATA))) {
                categoryPoiResult.setResultCode(0)
                        .setChannelCode(categoryInfoDTO.getCode());
            } else {
                log.error("创建美团药品分类失败 createCategoryMap:{}", createCategoryMap);
                categoryPoiResult.setResultCode(1)
                        .setMsg(JSON.parseObject(String.valueOf(createCategoryMap.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG));
            }
            categoryPoiResults.add(categoryPoiResult);
        }
        //再创建二级分类，避免创建二级分类时一级分类不存在
        for (CategoryInfoDTO categoryInfoDTO : request.getParamList()) {
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setStoreId(categoryInfoDTO.getStoreId())
                    .setCode(categoryInfoDTO.getCode());
            if (Strings.isNullOrEmpty(categoryInfoDTO.getParentName())) {
                continue;
            }
            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }


            Map createParam = Maps.newHashMap();

            createParam.put(ProjectConstant.APP_POI_CODE, channelStoreDOMap.get(KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(),
                    request.getBaseInfo().getChannelId(), categoryInfoDTO.getStoreId())).getChannelOnlinePoiCode());
            createParam.put(ProjectConstant.CATEGORY_CODE, categoryInfoDTO.getChannelParentCode());
            //createParam.put(ProjectConstant.CATEGORY_NAME, categoryInfoDTO.getParentName());
            createParam.put(ProjectConstant.MEDICINE_SECONDARY_CATEGORY_CODE, categoryInfoDTO.getCode());
            createParam.put(ProjectConstant.MEDICINE_SECONDARY_CATEGORY_NAME, categoryInfoDTO.getName());
            createParam.put(ProjectConstant.MEDICINE_SECONDARY_SEQUENCE, categoryInfoDTO.getSort());

            // 限频控制
            if (!clusterRateLimiter.tryAcquire(ChannelPostMTMedicineEnum.MEDICINECAT_SAVE, appId)) {
                log.warn("Call blocking failed frequently, Continue");
            }

            Map createCategoryMap = mtMedicineChannelGateService.sendPostApp(mtMedicineChannelGateService.getPostUrl(ChannelPostMTMedicineEnum.MEDICINECAT_SAVE), null, baseConverterService.baseRequest(request.getBaseInfo()), createParam);
            log.info("创建美团药品二级分类结果 createCategoryMap:{}", createCategoryMap);
            if ("ok".equals(createCategoryMap.get(ProjectConstant.DATA))) {
                categoryPoiResult.setResultCode(0)
                        .setChannelCode(categoryInfoDTO.getCode());
            } else {
                log.error("创建美团药品分类失败 createCategoryMap:{}", createCategoryMap);
                categoryPoiResult.setResultCode(1)
                        .setMsg(JSON.parseObject(String.valueOf(createCategoryMap.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG));
            }
            categoryPoiResults.add(categoryPoiResult);
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    //更新药品分类
    @Override
    public UpdateCategoryResponse updateCategory(CategoryUpdateRequest request) {
        log.info("开始更新美团药品分类 CategoryRequest:{}", request);
        UpdateCategoryResponse resp = new UpdateCategoryResponse();
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(request.getBaseInfo(), request.getParamList());
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL))
                    .setData(Collections.emptyList());
        }

        Map<String, Object> sysParam = mtMedicineChannelGateService.getChannelSysParams(new BaseRequest().setChannelId(request.getBaseInfo().getChannelId()).setTenantId(request.getBaseInfo().getTenantId()));
        String appId = String.valueOf(sysParam.get(ProjectConstant.MT_APP_ID_KEY));

        List<CategoryPoiResult> categoryPoiResults = Lists.newArrayList();
        for (CategoryInfoUpdateDTO categoryInfoDTO : request.getParamList()) {
            CategoryPoiResult categoryPoiResult = new CategoryPoiResult()
                    .setStoreId(categoryInfoDTO.getStoreId())
                    .setCode(categoryInfoDTO.getCode())
                    .setChannelCode(categoryInfoDTO.getChannelCategoryCode());

            if (categoryInfoDTO.getName().length() > ProjectConstant.CATEGORY_MAX_LENGTH) {
                categoryPoiResults.add(categoryPoiResult.setResultCode(1).setMsg(ProjectConstant.CATEGORY_MAX_LENGTH_MSG));
                continue;
            }

            Map updateParam = Maps.newHashMap();
            updateParam.put(ProjectConstant.APP_POI_CODE, channelStoreDOMap.get(KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(),
                    request.getBaseInfo().getChannelId(), categoryInfoDTO.getStoreId())).getChannelOnlinePoiCode());
            if (categoryInfoDTO.getLevel() == 1) {
                updateParam.put(ProjectConstant.CATEGORY_CODE, categoryInfoDTO.getCode());
                updateParam.put(ProjectConstant.CATEGORY_NAME, categoryInfoDTO.getName());
                updateParam.put(ProjectConstant.SEQUENCE, categoryInfoDTO.getSort());
            } else {
                updateParam.put(ProjectConstant.MEDICINE_SECONDARY_CATEGORY_CODE, categoryInfoDTO.getCode());
                updateParam.put(ProjectConstant.MEDICINE_SECONDARY_CATEGORY_NAME, categoryInfoDTO.getName());
                updateParam.put(ProjectConstant.MEDICINE_SECONDARY_SEQUENCE, categoryInfoDTO.getSort());
            }

            // 限频控制
            if (StringUtils.isNotBlank(appId) && !clusterRateLimiter.tryAcquire(ChannelPostMTMedicineEnum.MEDICINECAT_UPDATE, appId)) {
                log.warn("Call blocking failed frequently, Continue");
            }

            Map updateCategoryMap = mtMedicineChannelGateService.sendPostApp(mtMedicineChannelGateService.getPostUrl(ChannelPostMTMedicineEnum.MEDICINECAT_UPDATE), null, baseConverterService.baseRequest(request.getBaseInfo()), updateParam);
            log.info("更新美团药品分类结果 createCategoryMap:{}", updateCategoryMap);
            if (checkSuccess(updateCategoryMap)) {
                categoryPoiResult.setResultCode(0);
            } else {
                log.error("创建美团药品分类失败 createCategoryMap:{}", updateCategoryMap);
                categoryPoiResult.setResultCode(1)
                        .setMsg(JSON.parseObject(String.valueOf(updateCategoryMap.get(ProjectConstant.ERROR))).getString(ProjectConstant.MSG));
            }
            categoryPoiResults.add(categoryPoiResult);
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setData(categoryPoiResults);
    }

    /**
     * 检查结果是否成功
     *
     * @param updateCategoryMap
     * @return true 成功
     * false 失败
     */
    private boolean checkSuccess(Map updateCategoryMap) {
        if (updateCategoryMap.get(ProjectConstant.MSG) != null) {
            String msg = String.valueOf(updateCategoryMap.get(ProjectConstant.MSG));
            if (StringUtils.isNotBlank(msg) && msg.indexOf(MT_FAIL_KEYWORD) > -1) {
                return false;
            }
        }

        return ProjectConstant.OK.equals(updateCategoryMap.get(ProjectConstant.DATA));
    }

    @Override
    public UpdateCategoryResponse deleteCategory(CategoryDeleteRequest request) {
        ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);
        request.getParamList().forEach(data -> {
            try {
                Preconditions.checkArgument(StringUtils.isNotBlank(data.getChannelCategoryCode()) || StringUtils.isNotBlank(data.getChannelCategoryName()), "删除店内分类失败，未指定分类");
                ChannelMedicineCatDeleteDTO channelMedicineCatDeleteDTO = new ChannelMedicineCatDeleteDTO();
                channelMedicineCatDeleteDTO.setCategory_code(data.getCode());

                BaseRequest baseRequest = baseConverterService.baseRequest(request.getBaseInfo());
                if (data.getStoreId() > 0) {
                    baseRequest.setStoreIdList(Lists.newArrayList(data.getStoreId()));
                }
                Map<Long, ChannelResponseDTO> postResult = mtMedicineChannelGateService.sendPostAppMapDto(ChannelPostMTMedicineEnum.MEDICINECAT_DELETE, baseRequest, channelMedicineCatDeleteDTO);

                // 结果组装
                ResultDataUtils.combineResultData(resultData, postResult, data.getCode());

            } catch (IllegalArgumentException e) {
                log.error("MtyyChannelCategoryServiceImpl.deleteCategory 参数校验失败, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, e.getMessage(), data.getCode());

            } catch (Exception e) {
                log.error("MtyyChannelCategoryServiceImpl.deleteCategory 服务异常, data:{}", data, e);
                ResultDataUtils.combineExceptionData(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, data.getCode());
            }
        });

        return convertResultData(resultData);
    }

    //查询门店药品分类列表
    @Override
    public GetCategoryResponse batchGetCategory(CatRequest request) {
        GetCategoryResponse response = new GetCategoryResponse().setStatus(ResultGenerator.genSuccessResult());
        Map<String, String> bizParam = Maps.newHashMap();
        //获取 app_poi_code
        Map<String, ChannelStoreDO> pois = copChannelStoreService.getChannelPoiCode(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(), Lists.newArrayList(request.getStoreId()));
        Preconditions.checkNotNull(pois, ChannelResultStatusEnum.VALIDATE_TELNET_ERROR.getDesc(), request.getStoreId());
        String channelStoreKey = KeyUtils.genChannelStoreKey(request.getBaseInfo().getTenantId(), request.getBaseInfo().getChannelId(), request.getStoreId());
        bizParam.put("app_poi_code", pois.get(channelStoreKey).getChannelPoiCode());

        Map spTagMap = mtMedicineChannelGateService.sendGetApp(mtMedicineChannelGateService.getPostUrl(ChannelPostMTMedicineEnum.MEDICINECAT_LIST), null, baseConverterService.baseRequest(request.getBaseInfo()), bizParam);

        if (MapUtils.isEmpty(spTagMap) || Objects.isNull(spTagMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(spTagMap.get(ProjectConstant.DATA))) {
            return response.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "获取美团类目失败"));
        }
        List<CatInfo> catInfoList = Lists.newArrayList();
        JSONArray catInfoJSONArray = (JSONArray) spTagMap.get(ProjectConstant.DATA);
        List<ChannelMedicineCatDTO> channelMedicineCatDTOS = catInfoJSONArray.toJavaList(ChannelMedicineCatDTO.class);
        channelMedicineCatDTOS.forEach(data -> {
            catInfoList.add(MtMedicineConverterUtil.getCatInfo(data,request));
        });
        //catInfoList.addAll(mtConverterService.channelCatInfosMapping(channelMedicineCatDTOS));
        return response.setStatus(ResultGenerator.genSuccessResult()).setCatInfoList(catInfoList);
    }

    @Override
    public GetCategoryResponse getSecondCategoryByParentId(CatRequest req) {
        return new GetCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE)).setCatInfoList(Collections.emptyList());
    }

    @Override
    public CategoryProductRulesResponse getCategoryProductRules(CategoryAttrRequest request) {
        return new CategoryProductRulesResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE)).setRuleList(Collections.emptyList());
    }

    @Override
    public CategoryAttrResponse getCategoryAttr(CategoryAttrRequest request) {
        return new CategoryAttrResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public CategoryAttrValueResponse getCategoryAttrValue(CategoryAttrValueRequest request) {
        return new CategoryAttrValueResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    /*  以下方法暂时没有用到  */
    @Override
    public UpdateCategoryResponse sortCategory(CategorySortRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    @Override
    public UpdateCategoryResponse degradeCategory(CategoryDegradeRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    @Override
    public UpdateCategoryResponse adjustCategoryLevel(CategoryLevelAdjustRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    /**
     * 将ResultData转换为UpdateCategoryResponse
     *
     * @param resultData
     * @return
     */
    private UpdateCategoryResponse convertResultData(ResultData resultData) {
        UpdateCategoryResponse resp = baseConverterService.toUpdateCategoryResponse(resultData);
        resp.getData().addAll(baseConverterService.toUpdateCategoryResponseSuc(resultData.getSucData()));
        return resp;
    }

    @Override
    public UpdateCategoryResponse updateCategoryChannelCode(CategoryUpdateRequest request) {
        throw new UnsupportedOperationException("暂不支持方法");
    }

    @Override
    public UpdateCategoryResponse updateSmartSortSwitch(CategorySmartSortSwitchRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.emptyList());
    }

    @Override
    public CategorySmartSortQueryResponse queryStoreCategorySmartSort(CategorySmartSortQueryRequest request) {
        return new CategorySmartSortQueryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE))
                .setData(Collections.emptyList());
    }

    @Override
    public RecommendCategoryResponse recommendCategory(RecommendCategoryRequest request) {
        return new RecommendCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public UpdateCategoryResponse topCategory(CategoryTopRequest request) {
        return new UpdateCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    @Override
    public GetCategoryResponse queryStoreCategoryList(CatRequest request) {
        return null;
    }
}
