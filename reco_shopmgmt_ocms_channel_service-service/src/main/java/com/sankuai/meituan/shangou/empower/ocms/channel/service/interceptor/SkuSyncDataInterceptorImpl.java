package com.sankuai.meituan.shangou.empower.ocms.channel.service.interceptor;

import com.meituan.dorado.rpc.meta.RpcResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 商品数据同步拦截器
 * <AUTHOR>
 * @Date 2019-08-30 11:50
 */
@Service("SkuSyncDataInterceptorImpl")
public class SkuSyncDataInterceptorImpl extends UpstreamSyncDataInterceptBase<ResultErrorSku> {

    private static final String SKU_CREATE = "skuCreate";
    private static final String UPC_CREATE = "upcCreate";
    private static final String UPDATE_SKU = "updateSku";
    private static final String DELETE_SKU = "deleteSku";
    private static final String UPDATE_SKU_SELL_STATUS = "updateSkuSellStatus";
    private static final String UPDATE_CUSTOM_SKU_ID = "updateCustomSkuId";
    private static final String PICTURE_UPLOAD = "pictureUpload";
    private static final String URL_PICTURE_UPLOAD = "urlPictureUpload";
    private static final String DEFAULT_PICTURE_UPLOAD = "defaultPictureUpload";

    private static final List<String> METHODS = new ArrayList<>();
    static {
        METHODS.add(SKU_CREATE);
        METHODS.add(UPC_CREATE);
        METHODS.add(UPDATE_SKU);
        METHODS.add(DELETE_SKU);
        METHODS.add(UPDATE_SKU_SELL_STATUS);
        METHODS.add(UPDATE_CUSTOM_SKU_ID);
        // Picture
        METHODS.add(PICTURE_UPLOAD);
        METHODS.add(URL_PICTURE_UPLOAD);
        METHODS.add(DEFAULT_PICTURE_UPLOAD);
    }

    @Override
    protected List<String> getErrorSkuIds(int channelId, long storeId, InterceptInfo interceptInfo){
        List<String> result;
        // UPDATE_SKU_SELL_STATUS 方法每个门店下的skuId列表不一样，需要特殊处理
        switch (interceptInfo.methodName){
            case UPDATE_SKU_SELL_STATUS:
            case UPDATE_CUSTOM_SKU_ID:
                result = interceptInfo.errorSkuIds.get(createKey(channelId, storeId));
                break;
            default:
                result = interceptInfo.commonErrorSkuIds;
                break;
        }
        return result;
    }

    @Override
    protected long getStoreIdBy(Object requestData, InterceptInfo interceptInfo) {
        long storeId;
        switch (interceptInfo.getMethodName()){
            case UPDATE_SKU_SELL_STATUS:
                storeId = ((SkuSellStatusInfoDTO)requestData).storeId;
                break;
            case UPDATE_CUSTOM_SKU_ID:
                storeId = ((UpdateCustomSkuIdDTO)requestData).storeId;
                break;
            default:
                storeId = super.getStoreIdBy(requestData, interceptInfo);
                break;
        }
        return storeId;
    }

    @Override
    protected List<String> getAffectMethods() {
        return METHODS;
    }

    @Override
    protected void parseArgument(Object argument, InterceptInfo interceptInfo){
        switch (interceptInfo.getMethodName()){
            case DELETE_SKU:
                SkuInfoDeleteRequest deleteRequest = (SkuInfoDeleteRequest)argument;
                BaseRequest deleteBaseInfo = deleteRequest.baseInfo;
                interceptInfo.tenantId = deleteBaseInfo.tenantId;
                interceptInfo.requestDataList = deleteBaseInfo.storeIdList;
                interceptInfo.channelStoreIds.put(deleteBaseInfo.channelId, ListUtils.newArrayListOrDefault(deleteBaseInfo.storeIdList));
                interceptInfo.commonErrorSkuIds = deleteRequest.paramList.stream().map(SkuInfoDeleteDTO::getSkuId).collect(Collectors.toList());
                break;
            case UPDATE_SKU_SELL_STATUS:
                SkuSellStatusInfoRequest statusRequest = (SkuSellStatusInfoRequest)argument;
                BaseRequestSimple statusBaseInfo = statusRequest.baseInfo;
                interceptInfo.tenantId = statusBaseInfo.tenantId;
                interceptInfo.requestDataList = statusRequest.paramList;
                interceptInfo.channelStoreIds.put(statusBaseInfo.channelId, statusRequest.paramList.stream().map(SkuSellStatusInfoDTO::getStoreId).distinct().collect(Collectors.toList()));
                statusRequest.paramList.forEach(item ->{
                    interceptInfo.errorSkuIds.put(createKey(statusBaseInfo.channelId, item.getStoreId()), item.skuId.stream().map(SkuIdDTO::getCustomSkuId).collect(Collectors.toList()));
                });
                break;
            case UPDATE_CUSTOM_SKU_ID:
                UpdateCustomSkuIdRequest idRequest = (UpdateCustomSkuIdRequest)argument;
                BaseRequestSimple idBaseInfo = idRequest.baseInfo;
                interceptInfo.tenantId = idBaseInfo.tenantId;
                interceptInfo.requestDataList = idRequest.paramList;
                interceptInfo.channelStoreIds.put(idBaseInfo.channelId, idRequest.paramList.stream().map(UpdateCustomSkuIdDTO::getStoreId).distinct().collect(Collectors.toList()));
                idRequest.paramList.stream()
                        .collect(Collectors.groupingBy(item -> createKey(idBaseInfo.channelId,  item.getStoreId()),
                                Collectors.mapping(item -> String.valueOf(item.getCustomSkuId()), Collectors.toList())))
                        .forEach((key, skuIds) -> interceptInfo.errorSkuIds.put(key, skuIds));
                break;
            case PICTURE_UPLOAD:
            case URL_PICTURE_UPLOAD:
            case DEFAULT_PICTURE_UPLOAD:
                PictureUploadRequest uploadRequest = (PictureUploadRequest)argument;
                BaseRequest uploadBaseInfo = uploadRequest.baseInfo;
                interceptInfo.tenantId = uploadBaseInfo.tenantId;
                interceptInfo.requestDataList = uploadBaseInfo.storeIdList;
                interceptInfo.channelStoreIds.put(uploadBaseInfo.channelId, ListUtils.newArrayListOrDefault(uploadBaseInfo.storeIdList));
                interceptInfo.commonErrorSkuIds = uploadRequest.paramList.stream().map(PictureUploadDTO::getUid).collect(Collectors.toList());
                break;
            default:
                SkuInfoRequest request = (SkuInfoRequest)argument;
                BaseRequest baseInfo = request.baseInfo;
                interceptInfo.tenantId = baseInfo.tenantId;
                interceptInfo.requestDataList = baseInfo.storeIdList;
                interceptInfo.channelStoreIds.put(baseInfo.channelId, ListUtils.newArrayListOrDefault(baseInfo.storeIdList));
                interceptInfo.commonErrorSkuIds = request.paramList.stream()
                        .map(item -> {
                            // 没有CustomSkuId时使用SkuId
                            if(item.getCustomSkuId() != null){
                                return item.getCustomSkuId();
                            }
                            else{
                                return item.getSkuId();
                            }
                        }).collect(Collectors.toList());
                break;
        }
    }

    @Override
    protected List<ResultErrorSku> getErrorDataList(RpcResult result) {
        return ((ResultData)result.getReturnVal()).getErrorData();
    }

    @Override
    protected ResultErrorSku createResultErrorSku(int channelId, long storeId, String skuId, InterceptInfo interceptInfo){
        ResultStatus status = createCustomResultStatus(channelId, storeId, interceptInfo);
        return new ResultErrorSku().setStoreId(storeId).setSkuId(skuId).setChannelId(channelId)
                .setErrorMsg(status.getMsg())
                .setErrorCode(status.getCode());
    }

    @Override
    protected RpcResult createCustomResult(Class returnType, InterceptInfo interceptInfo) {
        RpcResult result = super.createCustomResult(returnType, interceptInfo);
        result.setReturnVal(ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS));
        return result;
    }
}
