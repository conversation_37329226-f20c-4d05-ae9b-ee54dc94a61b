package com.sankuai.meituan.shangou.empower.ocms.channel.service.route;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

import javax.annotation.Resource;

import com.meituan.shangou.saas.tenant.thrift.common.enums.ChannelOnlineTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.openapi.QqwChannelOrderServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelBrandTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.TenantBizModeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.tenant.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;

import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TenantBizModeEnum.DRUNK_HORSE;
import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.TenantBizModeEnum.MEDICINE_ADULT_UNMAN_WAREHOUSE;

/**
 * @description: 渠道服务路由工厂
 * @author: chenhaie
 * @create: 2019/1/7 下午5:27
 */
@Service
@Slf4j
public class RouteServiceFactory {

    @Resource
    private Map<String, ChannelSkuService> channelSkuServiceMap;

    @Resource
    private Map<String, ChannelSpuService> channelSpuServiceMap;

    @Resource
    private Map<String, ChannelMerchantSpuService> channelMerchantSpuServiceMap;

    @Autowired
    private Map<String, ChannelOfflineCategoryService> channelOfflineCategoryServiceMap;

    @Resource
    private Map<String, ChannelCategoryService> channelCategoryServiceMap;

    @Resource
    private Map<String, ChannelPriceService> channelPriceServiceMap;

    @Resource
    private Map<String, ChannelStockService> channelStockServiceMap;

    @Resource
    private Map<String, ChannelQueryStockService> channelQueryStockServiceMap;

    @Resource
    private Map<String, ChannelPoiService> channelPoiServiceMap;

    @Resource
    private Map<String, ChannelBrandService> channelBrandServiceMap;

    @Resource
    private Map<String, ChannelQualificationService> channelQualificationServiceMap;

    @Resource
    private Map<String, ChannelProductUpdateRuleService> channelProductUpdateRuleServiceMap;

    @Resource
    private Map<String, ChannelCategoryBrandService> channelCategoryBrandServiceMap;

    @Resource
    private Map<String, ChannelOrderService> channelOrderServiceMap;

    @Resource
    private Map<String, ChannelActivityService> channelActivityServiceMap;

    @Resource
    private Map<String, ChannelCommentService> channelCommentServiceMap;

    @Resource
    private Map<String, ChannelCommonService> channelCommonServiceMap;

    @Resource
    private Map<String, ChannelPoiShippingService> channelPoiShippingServiceMap;

    @Resource
    private Map<String, ChannelSettlementService> channelSettlementServiceMap;

    @Resource
    private TenantRemoteService tenantRemoteService;

    @Resource
    private Map<String, ChannelVideoService> channelVideoServiceMap;

    @Resource
    private Map<String, ChannelVirtualConfigService> channelVirtualConfigServiceMap;

    @Resource
    private QqwChannelOrderServiceImpl qqwChannelOrderService;

    @Resource
    private Map<String, ChannelMaterialService> channelMaterialServiceMap;
    @Resource
    private Map<String, ChannelPromotionActivityService> channelPromotionActivityServiceMap;

    /**
     * 通过渠道ID获取渠道活动查询服务
     * @param channelId
     * @param tenantId
     * @return
     */
    public ChannelPromotionActivityService selectChannelPromotionActivityService(int channelId, Long tenantId) {
        DynamicChannelType dynamicChannelType = DynamicChannelType.findOf(channelId);
        Preconditions.checkNotNull(dynamicChannelType, "渠道id非法，无法获取对应的ChannelPromotionActivityService，channelId=%s", channelId);
        String serviceBeanName = dynamicChannelType.getAbbrev() + ChannelPromotionActivityService.class.getSimpleName();
        ChannelPromotionActivityService channelPromotionActivityService = channelPromotionActivityServiceMap.get(serviceBeanName);
        if (Objects.isNull(channelPromotionActivityService)) {
            log.error("channelPromotionActivityService not found channelId=%s，serviceBeanName={}", channelId, serviceBeanName);
        }
        return channelPromotionActivityService;
    }

    /**
     * 通过渠道ID获取渠道商品服务
     */
    public ChannelSkuService selectChannelSkuService(int channelId, Long tenantId) {
        ChannelSkuService channelSkuService = channelSkuServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelSkuService.class.getSimpleName());
        Preconditions.checkNotNull(channelSkuService, "channelSkuService not found channelId=%s", channelId);

        return channelSkuService;
    }

    /**
     * 通过渠道ID获取渠道商品服务(SPU)
     */
    public ChannelSpuService selectChannelSpuService(int channelId, Long tenantId) {
        ChannelSpuService channelSpuService = channelSpuServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelSpuService.class.getSimpleName());
        Preconditions.checkNotNull(channelSpuService, "channelSpuService not found channelId=%s", channelId);

        return channelSpuService;
    }

    /**
     * 通过渠道ID获取总部渠道商品服务(SPU)
     */
    public ChannelMerchantSpuService selectChannelMerchantSpuService(BaseRequestSimple baseInfo) {
        Preconditions.checkNotNull(baseInfo, "baseInfo is null!");
        ChannelMerchantSpuService channelMerchantSpuService = channelMerchantSpuServiceMap.get(getServicePrefix(baseInfo.getChannelId(), baseInfo.getTenantId()) + ChannelMerchantSpuService.class.getSimpleName());
        Preconditions.checkNotNull(channelMerchantSpuService, "channelMerchantSpuService not found channelId=%s", baseInfo.getChannelId());
        return channelMerchantSpuService;
    }

    public ChannelOfflineCategoryService selectChannelOfflineCategoryService(BaseRequestSimple baseInfo) {
        Preconditions.checkNotNull(baseInfo, "baseInfo is null!");
        ChannelOfflineCategoryService channelMerchantSpuService = channelOfflineCategoryServiceMap.get(getServicePrefix(baseInfo.getChannelId(), baseInfo.getTenantId()) + ChannelOfflineCategoryService.class.getSimpleName());
        Preconditions.checkNotNull(channelMerchantSpuService, "channelMerchantSpuService not found channelId=%s", baseInfo.getChannelId());
        return channelMerchantSpuService;
    }

    /**
     * 通过渠道ID获取渠道商品类目服务
     */
    public ChannelCategoryService selectChannelCategoryService(int channelId, Long tenantId) {
        ChannelCategoryService channelCategoryService = channelCategoryServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelCategoryService.class.getSimpleName());
        Preconditions.checkNotNull(channelCategoryService, "channelCategoryService not found channelId=%s", channelId);

        return channelCategoryService;
    }

    /**
     * 通过渠道ID获取渠道虚拟配置服务
     */
    public ChannelVirtualConfigService selectChannelVirtualConfigService(int channelId, Long tenantId) {
        ChannelVirtualConfigService channelVirtualConfigService = channelVirtualConfigServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelVirtualConfigService.class.getSimpleName());
        Preconditions.checkNotNull(channelVirtualConfigService, "channelVirtualConfigService not found channelId=%s", channelId);

        return channelVirtualConfigService;
    }

    /**
     * 通过渠道ID获取渠道商品价格服务
     */
    public ChannelPriceService selectChannelPriceService(int channelId, Long tenantId) {
        ChannelPriceService channelPriceService = channelPriceServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelPriceService.class.getSimpleName());
        Preconditions.checkNotNull(channelPriceService, "channelPriceService not found channelId=%s", channelId);

        return channelPriceService;
    }

    /**
     * 通过渠道ID获取渠道库存服务
     */
    public ChannelStockService selectChannelStockService(int channelId, Long tenantId) {
        ChannelStockService channelStockService = channelStockServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelStockService.class.getSimpleName());
        Preconditions.checkNotNull(channelStockService, "channelStockService not found channelId=%s", channelId);

        return channelStockService;
    }

    /**
     * 通过渠道ID获取渠道查询库存服务
     */
    public ChannelQueryStockService selectChannelQueryStockService(int channelId) {
        ChannelQueryStockService channelQueryStockService = channelQueryStockServiceMap.get(getServicePrefix(channelId) + ChannelQueryStockService.class.getSimpleName());
        Preconditions.checkNotNull(channelQueryStockService, "channelQueryStockService not found channelId=%s", channelId);

        return channelQueryStockService;
    }

    public ChannelVideoService selectChannelVideoService(int channelId, Long tenantId) {
        ChannelVideoService channelVideoService =
                channelVideoServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelVideoService.class.getSimpleName());
        Preconditions.checkNotNull(channelVideoService, "channelVideoService not found channelId=%s", channelId);

        return channelVideoService;
    }

    /**
     * 通过渠道ID获取渠道素材服务
     */
    public ChannelMaterialService selectChannelMaterialService(int channelId) {
        ChannelMaterialService channelMaterialService = channelMaterialServiceMap.get(getServicePrefix(channelId) + ChannelMaterialService.class.getSimpleName());
        Preconditions.checkNotNull(channelMaterialService, "channelMaterialService not found channelId=%s", channelId);

        return channelMaterialService;
    }

    /**
     * 通过渠道id获取渠道门店服务
     *
     * @param channelId
     * @return
     */
    @Deprecated
    public ChannelPoiService selectChannelPoiService(int channelId) {
        ChannelPoiService channelPoiService = channelPoiServiceMap.get(getServicePrefix(channelId) + ChannelPoiService.class.getSimpleName());
        Preconditions.checkNotNull(channelPoiService, "channelPoiService not found channelId=%s", channelId);
        return channelPoiService;
    }

    /**
     * 通过渠道id、租户id获取渠道门店服务
     *
     * @param channelId 渠道id
     * @param tenantId  租户id
     * @return 渠道门店服务
     */
    public ChannelPoiService selectChannelPoiService(int channelId, Long tenantId) {
        ChannelPoiService channelPoiService = channelPoiServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelPoiService.class.getSimpleName());
        Preconditions.checkNotNull(channelPoiService, "channelPoiService not found channelId=%s, tenantId=%s", channelId, tenantId);
        return channelPoiService;
    }

    public ChannelBrandService selectChannelBrandService(int channelId) {
        ChannelBrandService channelBrandService = channelBrandServiceMap.get(getServicePrefix(channelId) + ChannelBrandService.class.getSimpleName());
        Preconditions.checkNotNull(channelBrandService, "channelBrandService not found channelId=%s", channelId);
        return channelBrandService;
    }

    public ChannelQualificationService selectChannelQualificationService(int channelId) {
        ChannelQualificationService channelQualificationService =
                channelQualificationServiceMap.get(getServicePrefix(channelId) + ChannelQualificationService.class.getSimpleName());
        Preconditions.checkNotNull(channelQualificationService, "channelQualificationService not found channelId=%s", channelId);
        return channelQualificationService;
    }

    public ChannelProductUpdateRuleService selectChannelProductUpdateRuleService(int channelId) {
        ChannelProductUpdateRuleService channelQualificationService =
                channelProductUpdateRuleServiceMap.get(getServicePrefix(channelId) + ChannelProductUpdateRuleService.class.getSimpleName());
        Preconditions.checkNotNull(channelQualificationService, "channelProductUpdateRuleService not found channelId=%s", channelId);
        return channelQualificationService;
    }

    public ChannelCategoryBrandService selectChannelCategoryBrandService(int channelId) {
        ChannelCategoryBrandService channelCategoryBrandService =
                channelCategoryBrandServiceMap.get(getServicePrefix(channelId) + ChannelCategoryBrandService.class.getSimpleName());
        Preconditions.checkNotNull(channelCategoryBrandService, "channelCategoryBrandService not found channelId=%s", channelId);
        return channelCategoryBrandService;
    }

    public ChannelOrderService selectChannelOrderService(int channelId, Long tenantId) {
        ChannelOrderService channelOrderService =
                channelOrderServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelOrderService.class.getSimpleName());
        if (channelOrderService == null) {
            channelOrderService = getOpenApiChannelOrderService(channelId);
        }
        Preconditions.checkNotNull(channelOrderService, "channelOrderService not found channelId=%s", channelId);
        return channelOrderService;
    }

    public ChannelOrderService selectChannelOrderService(int channelId) {
        ChannelOrderService channelOrderService =
                channelOrderServiceMap.get(getServicePrefix(channelId) + ChannelOrderService.class.getSimpleName());
        if (channelOrderService == null) {
            channelOrderService = getOpenApiChannelOrderService(channelId);
        }
        Preconditions.checkNotNull(channelOrderService, "channelOrderService not found channelId=%s", channelId);
        return channelOrderService;
    }

    private ChannelOrderService getOpenApiChannelOrderService(int channelId) {
        ChannelOrderService channelOrderService = null;

        DynamicChannelType dynamicChannelType = DynamicChannelType.findOf(channelId);
        if (dynamicChannelType != null &&
                (ChannelOnlineTypeEnum.isPrivate(dynamicChannelType.getChannelStandard()) ||
                        ChannelOnlineTypeEnum.isOfflinePos(dynamicChannelType.getChannelStandard()))
        ) {
            // 自建渠道+POS渠道
            channelOrderService = qqwChannelOrderService;
        }

        return channelOrderService;
    }


    public ChannelActivityService selectChannelActivityService(int channelId, Long tenantId) {
        ChannelActivityService channelActivityService = channelActivityServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelActivityService.class.getSimpleName());
        Preconditions.checkNotNull(channelActivityService, "channelActivityService not found channelId=%s", channelId);
        return channelActivityService;
    }

    public ChannelCommentService selectChannelCommentService(int channelId, Long tenantId) {
        ChannelCommentService channelCommentService =
                channelCommentServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelCommentService.class.getSimpleName());
        Preconditions.checkNotNull(channelCommentService, "channelCommentService not found channelId=%s", channelId);
        return channelCommentService;
    }

    public ChannelCommonService selectChannelCommonService(int channelId) {
        ChannelCommonService channelCommonService = channelCommonServiceMap.get(getServicePrefix(channelId) + ChannelCommonService.class.getSimpleName());
        Preconditions.checkNotNull(channelCommonService, "channelCommonService not found channelId=%s", channelId);
        return channelCommonService;
    }

    public ChannelCommonService selectChannelCommonService(int channelId, Long tenantId) {
        ChannelCommonService channelCommonService =
                channelCommonServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelCommonService.class.getSimpleName());
        Preconditions.checkNotNull(channelCommonService, "channelCommonService not found channelId=%s", channelId);
        return channelCommonService;
    }

    @Deprecated
    public ChannelPoiShippingService selectChannelPoiShippingService(int channelId) {
        ChannelPoiShippingService channelPoiShippingService = channelPoiShippingServiceMap.get(getServicePrefix(channelId) + ChannelPoiShippingService.class.getSimpleName());
        Preconditions.checkNotNull(channelPoiShippingService, "channelPoiShippingService not found channelId=%s", channelId);
        return channelPoiShippingService;
    }

    /**
     * 通过渠道id、租户id获取渠道门店配送服务
     *
     * @param channelId 渠道id
     * @param tenantId  租户id
     * @return 渠道门店配送服务
     */
    public ChannelPoiShippingService selectChannelPoiShippingService(int channelId, Long tenantId) {
        ChannelPoiShippingService channelPoiShippingService = channelPoiShippingServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelPoiShippingService.class.getSimpleName());
        Preconditions.checkNotNull(channelPoiShippingService, "channelPoiShippingService not found channelId=%s, tenantId=%s", channelId, tenantId);
        return channelPoiShippingService;
    }

    /**
     * 根据渠道id获取渠道简码，渠道简码作为服务路由的不同前缀
     *
     * @param channelId
     * @return
     */
    private String getServicePrefix(int channelId) {
        Preconditions.checkArgument(ChannelTypeEnum.abbrevMap.containsKey(channelId), "channelId:%s is not correct!", channelId);

        return ChannelTypeEnum.abbrevMap.get(channelId);
    }

    /**
     * 根据渠道id获取渠道简码，渠道简码作为服务路由的不同前缀
     * 优先根据租户业务模式判断、如果查询租户异常，在根据兜底的mcc配置判断
     *
     * @param channelId 渠道id
     * @param tenantId  租户id
     * @return 渠道服务类前缀
     */
    public String getServicePrefix(int channelId, Long tenantId) {
        if (channelId == ChannelTypeEnum.MEITUAN_MACK.getCode()) { // 美团mock的渠道ID
            return ChannelTypeEnum.MEITUAN_MACK.getAbbrev();
        }
        Preconditions.checkArgument(ChannelTypeEnum.abbrevMap.containsKey(channelId) || channelId >= 2000, "channelId:%s is not correct!", channelId);
        boolean mtYZ = channelId == ChannelTypeEnum.MEITUAN.getCode() || channelId == ChannelTypeEnum.YOU_ZAN.getCode();
        if (!mtYZ) {
            return ChannelTypeEnum.abbrevMap.get(channelId);
        }
        try {
            // 美团渠道判断是否新供给租户
            TenantBizModeEnum tenantBizMode = tenantRemoteService.getTenantBizMode(tenantId);

            // 歪马的调用
            if (isDhNewLogic() && tenantBizMode == DRUNK_HORSE && (channelId == ChannelTypeEnum.MT_DRUNK_HORSE.getCode())) {
                return ChannelTypeEnum.abbrevMap.get(ChannelTypeEnum.MT_DRUNK_HORSE.getCode());
            }

            // 医药成人无人仓零售开放平台多规格分支
            if (Objects.equals(tenantBizMode, MEDICINE_ADULT_UNMAN_WAREHOUSE)) {
                return ChannelBrandTypeEnum.MEITUAN_HEALTH.getAbbrev();
            }

            List<String> bizModeCodeList = Optional.ofNullable(MccConfigUtil.getSingleTenantSingleAppBizModeList())
                    .orElse(Collections.emptyList());
            if (tenantBizMode == null || bizModeCodeList.contains(tenantBizMode.getCode())) {
                return ChannelTypeEnum.abbrevMap.get(channelId);
            }
            return ChannelBrandTypeEnum.abbrevMap.getOrDefault(channelId, ChannelTypeEnum.abbrevMap.get(channelId));
        } catch (Exception e) {
            // 查询租户异常、直接通过配置判断是否是歪马租户、如果是歪马租户走老逻辑; 非歪马租户走新逻辑
            List<Long> drunkHorseTenantList = Optional.ofNullable(MccConfigUtil.getDrunkHorseTenantList()).orElse(Collections.emptyList());
            return drunkHorseTenantList.contains(tenantId)
                    ? ChannelTypeEnum.abbrevMap.get(channelId)
                    : ChannelBrandTypeEnum.abbrevMap.getOrDefault(channelId, ChannelTypeEnum.abbrevMap.get(channelId));
        }
    }

    private boolean isDhNewLogic() {

        // 得出随机数范围[1,100]，mcc配0则不走新逻辑。
        int rdInt = ThreadLocalRandom.current().nextInt(100) + 1;
        int releasePct = MccConfigUtil.getDhRoutingGrayRelease();
        return (releasePct > 0 && rdInt <= releasePct);
    }

    @Deprecated
    public ChannelSettlementService selectChannelSettlementService(int channelId) {
        ChannelSettlementService channelSettlementService = channelSettlementServiceMap.get(getServicePrefix(channelId) + ChannelSettlementService.class.getSimpleName());
        Preconditions.checkNotNull(channelSettlementService, "channelSettlementService not found channelId=%s", channelId);
        return channelSettlementService;
    }

    public ChannelSettlementService selectChannelSettlementService(int channelId, Long tenantId) {
        ChannelSettlementService channelSettlementService = channelSettlementServiceMap.get(getServicePrefix(channelId, tenantId) + ChannelSettlementService.class.getSimpleName());
        Preconditions.checkNotNull(channelSettlementService, "channelSettlementService not found channelId=%s, tenantId=%s", channelId, tenantId);
        return channelSettlementService;
    }

}