package com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy;

import com.sankuai.sgfnqnh.poi.channel.client.thrift.AppThriftService;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.AppInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.TenantAppInfoDTO;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByAppIdRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByPoiRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.AppInfoQueryByTenantRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.request.TenantAppInfoQueryByAppRequest;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.response.AppInfoListQueryResponse;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.response.AppInfoQueryResponse;
import com.sankuai.sgfnqnh.poi.channel.client.thrift.dto.application.response.TenantAppInfoListQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @program: reco_shopmgmt_ocms_channel_service_2
 * @description: poi_channel 应用接口代理
 * @author: jinyi
 * @create: 2024-05-21 15:45
 **/
@Service
@Slf4j
public class PoiChannelAppThriftServiceProxy {

    @Resource(name = "poiChannelAppThriftService")
    private AppThriftService appThriftService;

    public AppInfoDTO queryAppInfoByPoi(AppInfoQueryByPoiRequest request){
        try{
            AppInfoQueryResponse response = appThriftService.queryAppInfoByPoi(request);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus()) || response.getStatus().getCode() != 0){
                log.info("appThriftService.queryAppInfoByPoi 调用失败，request={}", request);
                return null;
            }
            if (response.getData() == null){
                log.info("appThriftService.queryAppInfoByPoi 查询结果为空，request={}", request);
                return null;
            }
            return response.getData();
        }catch (Exception e){
            log.error("appThriftService.queryAppInfoByPoi 调用失败，request={}", request, e);
            return null;
        }
    }

    public AppInfoDTO queryAppInfoByAppId(AppInfoQueryByAppIdRequest request){
        try{
            AppInfoQueryResponse response = appThriftService.queryAppInfoByAppId(request);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus()) || response.getStatus().getCode() != 0){
                log.info("appThriftService.queryAppInfoByAppId 调用失败，request={}", request);
                return null;
            }
            if (response.getData() == null){
                log.info("appThriftService.queryAppInfoByAppId 查询结果为空，request={}", request);
                return null;
            }
            return response.getData();
        }catch (Exception e){
            log.error("appThriftService.queryAppInfoByAppId 调用失败，request={}", request, e);
            return null;
        }
    }

    public List<AppInfoDTO> queryAppInfoByTenant(AppInfoQueryByTenantRequest request){
        try{
            AppInfoListQueryResponse response = appThriftService.queryAppInfoByTenant(request);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus()) || response.getStatus().getCode() != 0){
                log.info("appThriftService.queryAppInfoByTenant 调用失败，request={}", request);
                return new ArrayList<>();
            }
            if (response.getData() == null){
                log.info("appThriftService.queryAppInfoByTenant 查询结果为空，request={}", request);
                return new ArrayList<>();
            }
            return response.getData();
        }catch (Exception e){
            log.error("appThriftService.queryAppInfoByTenant 调用失败，request={}", request, e);
            return new ArrayList<>();
        }
    }

    public List<TenantAppInfoDTO> queryTenantAppInfoListByApp(TenantAppInfoQueryByAppRequest request){
        try{
            TenantAppInfoListQueryResponse response = appThriftService.queryTenantAppInfoListByApp(request);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus()) || response.getStatus().getCode() != 0){
                log.info("appThriftService.queryTenantAppInfoListByApp 调用失败，request={}", request);
                return new ArrayList<>();
            }
            if (response.getData() == null){
                log.info("appThriftService.queryTenantAppInfoListByApp 查询结果为空，request={}", request);
                return new ArrayList<>();
            }
            return response.getData();
        }catch (Exception e){
            log.error("appThriftService.queryTenantAppInfoListByApp 调用失败，request={}", request, e);
            return new ArrayList<>();
        }
    }
}
