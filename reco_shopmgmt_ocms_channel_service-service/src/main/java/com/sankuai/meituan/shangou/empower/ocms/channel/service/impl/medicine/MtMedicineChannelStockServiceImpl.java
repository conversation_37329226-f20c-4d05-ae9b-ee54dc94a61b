package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.medicine.ChannelMedicineBatchDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.medicine.ChannelMedicineStockUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.medicine.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelResultStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.InvokeChannelTooMuchException;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelStockService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtMedicineConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.loop.MedicineCloseLoopService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ResultDataUtils;
import javafx.util.Pair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTMedicineEnum.MEDICINE_STOCK_BATCH_UPDATE;

/**
 * 美团医药渠道的库存同步服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/5
 */
@Service("mtMedicineChannelStockService")
public class MtMedicineChannelStockServiceImpl implements ChannelStockService {

	public static final int STOCK_UPDATE_MAX_COUNT = 50;

	@Resource
	private MtMedicineChannelGateService mtMedicineChannelGateService;

	@Resource
	private CommonLogger log;

	@Autowired
	private MedicineCloseLoopService medicineCloseLoopService;

	@Override
	public ResultSpuData updateStockBySpu(SpuStockRequest request) {
		throw new UnsupportedOperationException("MtMedicine doesn't support managed by SPU for now.");
	}

	@Override
	public ResultData updateStockMultiChannel(SkuStockMultiChannelRequest request) {
		ResultData resultData = ResultDataUtils.newResultData(ChannelResultStatusEnum.SUCCESS);

		if (CollectionUtils.isEmpty(request.getSkuStockList())) {
			return resultData;
		}

		long storeId = request.getSkuStockList().get(0).getStoreId();
		BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
				.setChannelId(request.getSkuStockList().get(0).getChannelId())
				.setStoreIdList(Lists.newArrayList(storeId));

		List<SkuStockMultiChannelDTO> filteredStockList = filterBlockedRequest(baseRequest.getTenantId(), request.getSkuStockList(), resultData);

		// 因为前面只有单门店，所以这里直接判断是否还有剩下的需要操作的列表就行
		if (CollectionUtils.isEmpty(filteredStockList)) {
			return resultData;
		}

		// 分页调用
		ListUtils.listPartition(filteredStockList, STOCK_UPDATE_MAX_COUNT)
				.forEach(data -> {
					List<String> skuIds = Lists.newArrayList();
					try {
						// 返回结果组装用标识
						skuIds = data.stream().map(SkuStockMultiChannelDTO::getSkuId).collect(Collectors.toList());

						// 业务参数转换
						List<ChannelMedicineStockUpdateDTO> list = data.stream()
								.map(MtMedicineConverterUtil::transferMedicineStockUpdateDto)
								.distinct()
								.collect(Collectors.toList());
						ChannelMedicineBatchDTO dto = MtMedicineConverterUtil.transferBatchDto(JSON.toJSONString(list));

						// 调用渠道接口
						Map<Long, ChannelResponseDTO> postResult = mtMedicineChannelGateService.sendPostAppDto(MEDICINE_STOCK_BATCH_UPDATE, baseRequest, dto);

						// 组装返回结果
						translateResult(resultData, skuIds, postResult);

					} catch (IllegalArgumentException e) {
						log.warn("MtMedicineChannelStockServiceImpl.updateStockMultiChannel 参数校验失败, data:{}", data, e);
						ResultDataUtils.combineExceptionDataList(resultData, e.getMessage(), skuIds, storeId);
					} catch (InvokeChannelTooMuchException e) {
						log.warn("MtMedicineChannelStockServiceImpl.updateStockMultiChannel 触发限流, data:{}", data, e);
						ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds, storeId);
					} catch (Exception e) {
						log.warn("MtMedicineChannelStockServiceImpl.updateStockMultiChannel 服务异常, data:{}", data, e);
						ResultDataUtils.combineExceptionDataList(resultData, ChannelResultStatusEnum.UNDEAL_ERROR, skuIds, storeId);
					}
				});
		return resultData;
	}

	@Override
	public BatchGetStockInfoResponse batchGetStockInfo(BatchGetStockInfoRequest request) {
		return null;
	}

	/**
	 * 过滤被阻断的数据，被阻断的默认为成功
	 */
	private List<SkuStockMultiChannelDTO> filterBlockedRequest(Long tenantId, List<SkuStockMultiChannelDTO> sourceList, ResultData resultData) {
		Map<Long, List<SkuStockMultiChannelDTO>> sourceMap = new HashMap<>();
		sourceMap.put(tenantId, sourceList);

		Pair<Map<Long, List<SkuStockMultiChannelDTO>>, Map<Long, List<SkuStockMultiChannelDTO>>> filterResult =
				medicineCloseLoopService.filterBlockedItems(sourceMap, MedicineCloseLoopService.MedicineCloseLoopTypeEnum.STOCK, SkuStockMultiChannelDTO::getStoreId);

		if (MapUtils.isNotEmpty(filterResult.getValue()) && filterResult.getValue().get(tenantId) != null) {
			for (SkuStockMultiChannelDTO blockedItem : filterResult.getValue().get(tenantId)) {
				resultData.getSucData().add(new ResultSuccessSku().setStoreId(blockedItem.getStoreId()).setSkuId(blockedItem.getSkuId()));
			}
		}

		// 如果有剩下的门店id，就将所有的列表返回，单独操作该门店
		return CollectionUtils.isEmpty(filterResult.getKey().get(tenantId)) ? Collections.emptyList() : filterResult.getKey().get(tenantId);
	}

	private void translateResult(ResultData resultData, List<String> skuIds, Map<Long, ChannelResponseDTO> storeIdAndResponseMap) {
		if (MapUtils.isEmpty(storeIdAndResponseMap)) {
			return;
		}

		storeIdAndResponseMap.forEach((storeId, channelResponse) -> {
			//全部成功
			if (channelResponse.isSuccess()) {
				skuIds.forEach(skuId -> resultData.getSucData().add(new ResultSuccessSku().setStoreId(storeId).setSkuId(skuId)));

				//全部失败
			} else if (isAllFailed(channelResponse)) {
				skuIds.forEach(skuId ->
						resultData.getErrorData().add(new ResultErrorSku()
								.setStoreId(storeId)
								.setSkuId(skuId)
								.setErrorCode(ResultCodeEnum.FAIL.getValue())
								.setErrorMsg(channelResponse.getErrorMsg())
						)
				);

				//部分成功，部分失败
			} else {
				Map<String, String> failedSkuIdAndErrorMessageMap = parseFailedSkuIdsFromResponse(channelResponse);
				skuIds.forEach(skuId -> {
					//失败项组装
					if (failedSkuIdAndErrorMessageMap.containsKey(skuId)) {
						resultData.getErrorData().add(new ResultErrorSku()
								.setStoreId(storeId)
								.setSkuId(skuId)
								.setErrorCode(ResultCodeEnum.FAIL.getValue())
								.setErrorMsg(failedSkuIdAndErrorMessageMap.get(skuId))
						);

						//成功项组装
					} else {
						resultData.getSucData().add(new ResultSuccessSku().setStoreId(storeId).setSkuId(skuId));
					}
				});

			}
		});
	}

	/**
	 * 根据配置的规则判断是否全部失败
	 */
	private boolean isAllFailed(ChannelResponseDTO channelResponse) {
		List<String> regexList = MccConfigUtil.getMtMedicineChannelStockAllFailedRegexList();
		if (CollectionUtils.isEmpty(regexList)) {
			return false;
		}

		for (String regex : regexList) {
			Matcher matcher = Pattern.compile(regex).matcher(channelResponse.getErrorMsg());
			if (matcher.find()) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 根据配置的规则解析出部分失败的sku及其对应的失败原因
	 * 如果没有命中任何规则，则认为调用渠道成功，不会出现在返回值集合中
	 */
	private Map<String, String> parseFailedSkuIdsFromResponse(ChannelResponseDTO channelResponse) {
		Map<String, String> skuAndErrorMsgMap = new HashMap<>();

		Optional.ofNullable(MccConfigUtil.getMtMedicineChannelStockPartialFailRegexList())
				.orElse(new ArrayList<>())
				.forEach(regexConfig -> {
					Matcher matcher = Pattern.compile(regexConfig.getRegex()).matcher(channelResponse.getErrorMsg());
					if (matcher.find()) {
						List<String> failSkuIds = Optional.ofNullable(matcher.group(regexConfig.getSkuListIndex()))
								.map(it -> it.replace("\\\"", "\""))
								.map(it -> JSONObject.parseArray(it, String.class))
								.orElse(new ArrayList<>());

						if (CollectionUtils.isNotEmpty(failSkuIds)) {
							String errorMsg = Optional.ofNullable(matcher.group(regexConfig.getMessageIndex())).orElse(channelResponse.getErrorMsg());
							failSkuIds.forEach(skuId ->
									skuAndErrorMsgMap.put(
											skuId,
											Optional.ofNullable(skuAndErrorMsgMap.get(skuId)).map(it -> it + ";" + errorMsg).orElse(errorMsg)
									)
							);
						}
					}
				});

		return skuAndErrorMsgMap;
	}
}
