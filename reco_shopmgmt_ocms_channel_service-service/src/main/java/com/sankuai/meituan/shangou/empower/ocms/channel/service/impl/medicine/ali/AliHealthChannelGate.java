package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.medicine.ali;

import com.sankuai.meituan.shangou.empower.ocms.channel.utils.HttpClientUtil;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Component
public class AliHealthChannelGate {
    private static final String BASE_URL = "https://eco.taobao.com/router/rest";

    private static final String SIGN_METHOD_MD5 = "md5";
    private static final String SIGN_METHOD_HMAC = "hmac";
    private static final String CHARSET_UTF8 = "utf-8";
    private static final String CONTENT_ENCODING_GZIP = "gzip";


    /**
     * 不带计算签名和session的请求
     */
    public String sendPostWithoutSession(String api, Map<String, Object> bizParams) throws IOException {
        Map<String, Object> params = buildParams(api, bizParams);
        return HttpClientUtil.post(BASE_URL, 5000, 5000, params);
    }


    private Map<String, Object> buildParams(String api, Map<String, Object> bizParams) throws IOException {
        Map<String, Object> params = new HashMap<>();

        // 公共参数
        params.put("method", api);
//        params.put("session", sessionKey);
        params.put("format", "json");
        params.put("v", "2.0");
        params.put("sign_method", "hmac");

        // 业务参数
        if (MapUtils.isNotEmpty(bizParams)) {
            params.putAll(bizParams);
        }

        return params;
    }

//    @Autowired
//    private ClusterRateLimiter clusterRateLimiter;
//
//
//    public Pair<AliConfigItem, String> sendPostWithoutSession(String api, Map<String, Object> bizParams) throws IOException {
//        AliConfigItem config = getAliConfig();
//        Map<String, Object> params = buildParams(api, config, bizParams);
//
//        String result = HttpClientUtil.post(BASE_URL, 5000, 5000, params);
//        return new Pair<>(config, result);
//    }
//
//    private Map<String, Object> buildParams(String api, AliConfigItem config, Map<String, Object> bizParams) throws IOException {
//        Map<String, Object> params = new HashMap<>();
//
//        // 公共参数
//        params.put("method", api);
//        params.put("app_key", config.getAppKey());
////        params.put("session", sessionKey);
//        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        params.put("timestamp", df.format(new Date()));
//        params.put("format", "json");
//        params.put("v", "2.0");
//        params.put("sign_method", "hmac");
//        params.put("ref_ent_id", config.getRelEntId());
//
//        // 业务参数
//        if (MapUtils.isNotEmpty(bizParams)) {
//            params.putAll(bizParams);
//        }
//
//        // 签名参数
//        params.put("sign", signTopRequest(params, config.getSecret(), SIGN_METHOD_HMAC));
//        return params;
//    }
//
//    /**
//     * 对TOP请求进行签名。
//     */
//    private static String signTopRequest(Map<String, Object> params, String secret, String signMethod) throws IOException {
//        // 第一步：检查参数是否已经排序
//        String[] keys = params.keySet().toArray(new String[0]);
//        Arrays.sort(keys);
//
//        // 第二步：把所有参数名和参数值串在一起
//        StringBuilder query = new StringBuilder();
//        if (SIGN_METHOD_MD5.equals(signMethod)) {
//            query.append(secret);
//        }
//        for (String key : keys) {
//            Object value = params.get(key);
//            if (StringUtils.isNotEmpty(key) && value != null) {
//                query.append(key).append(value);
//            }
//        }
//
//        // 第三步：使用MD5/HMAC加密
//        byte[] bytes;
//        if (SIGN_METHOD_HMAC.equals(signMethod)) {
//            bytes = encryptHMAC(query.toString(), secret);
//        } else {
//            query.append(secret);
//            bytes = encryptMD5(query.toString());
//        }
//
//        // 第四步：把二进制转化为大写的十六进制
//        return byte2hex(bytes);
//    }
//
//    /**
//     * 对字节流进行HMAC_MD5摘要。
//     */
//    private static byte[] encryptHMAC(String data, String secret) throws IOException {
//        byte[] bytes = null;
//        try {
//            SecretKey secretKey = new SecretKeySpec(secret.getBytes(CHARSET_UTF8), "HmacMD5");
//            Mac mac = Mac.getInstance(secretKey.getAlgorithm());
//            mac.init(secretKey);
//            bytes = mac.doFinal(data.getBytes(CHARSET_UTF8));
//        } catch (GeneralSecurityException gse) {
//            throw new IOException(gse.toString());
//        }
//        return bytes;
//    }
//
//    /**
//     * 对字符串采用UTF-8编码后，用MD5进行摘要。
//     */
//    private static byte[] encryptMD5(String data) throws IOException {
//        return encryptMD5(data.getBytes(CHARSET_UTF8));
//    }
//
//    /**
//     * 对字节流进行MD5摘要。
//     */
//    private static byte[] encryptMD5(byte[] data) throws IOException {
//        byte[] bytes = null;
//        try {
//            MessageDigest md = MessageDigest.getInstance("MD5");
//            bytes = md.digest(data);
//        } catch (GeneralSecurityException gse) {
//            throw new IOException(gse.toString());
//        }
//        return bytes;
//    }
//
//    /**
//     * 把字节流转换为十六进制表示方式。
//     */
//    private static String byte2hex(byte[] bytes) {
//        StringBuilder sign = new StringBuilder();
//        for (int i = 0; i < bytes.length; i++) {
//            String hex = Integer.toHexString(bytes[i] & 0xFF);
//            if (hex.length() == 1) {
//                sign.append("0");
//            }
//            sign.append(hex.toUpperCase());
//        }
//        return sign.toString();
//    }
//
//    private AliConfigItem getAliConfig() {
//        Pair<AliConfigItem, String> aliConfigResult = tryGetAliConfig();
//
//        if (StringUtils.isNotBlank(aliConfigResult.getValue())) {
//            throw new BizException(aliConfigResult.getValue());
//        }
//
//        return aliConfigResult.getKey();
//    }
//
//    private Pair<AliConfigItem, String> tryGetAliConfig() {
//        List<AliConfigItem> aliConfigItemList = MccDynamicConfigUtil.getAliEntInfoMap();
//
//        if (CollectionUtils.isEmpty(aliConfigItemList)) {
//            return new Pair<>(null, "找不到有效的阿里渠道配置");
//        }
//
//        for (AliConfigItem aliConfigItem : aliConfigItemList) {
//            if (!MccDynamicConfigUtil.isDrugTraceLimit()) {
//                // 不进行限流
//                aliConfigItem.setSecret(KmsUtils.getKmsValue(aliConfigItem.getRateLimiterKey()));
//                return new Pair<>(aliConfigItem, null);
//            }
//
//            // 逐一尝试是否已经被限流
//            if (clusterRateLimiter.tryAcquire(aliConfigItem.getRateLimiterKey(), aliConfigItem.getRelEntId())) {
//                aliConfigItem.setSecret(KmsUtils.getKmsValue(aliConfigItem.getRateLimiterKey()));
//                return new Pair<>(aliConfigItem, null);
//            }
//        }
//
//        return new Pair<>(null, "找不到有效的阿里渠道配置");
//    }

}
