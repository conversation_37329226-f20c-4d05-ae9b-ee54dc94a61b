package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.qnh;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiOutMapResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: wangyihao04
 * @Date: 2021-12-31 17:15
 * @Mail: <EMAIL>
 */
@Service
@Slf4j
public class QnhStoreService {

    @Resource
    private PoiThriftService poiThriftService;

    public Long mappingStoreIdByQnhStoreId(Long tenantId, String qnhStoreId) {
        PoiOutMapResponse response = poiThriftService
                .queryTenantPoiInfoMapByOutIds(tenantId, Lists.newArrayList(qnhStoreId));

        if (Objects.isNull(response) ||
                Objects.isNull(response.getPoiInfoMap()) ||
                Objects.isNull(response.getPoiInfoMap().get(qnhStoreId))) {
            throw new BizException(String.format("未查到牵牛花 {} 对应百川门店", qnhStoreId));
        }

        return response.getPoiInfoMap().get(qnhStoreId).getPoiId();
    }

    public String mappingQnhStoreIdByStoreId(Long tenantId, Long storeId) {
        PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(storeId), tenantId);

        if (Objects.isNull(response) ||
                Objects.isNull(response.getPoiInfoMap()) ||
                Objects.isNull(response.getPoiInfoMap().get(storeId))) {
            throw new BizException(String.format("未查到百川 {} 对应牵牛花门店", storeId));
        }

        return response.getPoiInfoMap().get(storeId).getOutPoiId();
    }

    public Map<Long, String> mappingQnhStoreIdByStoreIds(Long tenantId, List<Long> storeId) {
        PoiMapResponse poiMapResponse = poiThriftService.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(storeId), tenantId);
        Map<Long, String> result = Maps.newHashMap();
        poiMapResponse.getPoiInfoMap().forEach((k, v) -> {
                    result.put(k, v.getOutPoiId());
                }
        );
        return result;
    }

}
