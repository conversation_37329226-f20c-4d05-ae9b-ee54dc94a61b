package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.weixin.*;

import java.util.List;

/**
 * 请求微信开放平台接口
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/04
 */
public interface WeChatChannelCommonService {

	WeChatUpdateTokenResponse getAccessToken(Long tenantId);

	WeChatGetTicketResponse getWeChatTicket(WeChatGetTicketRequest request);

	WeChatUserInfoResponse queryUserInfo(WeChatUserInfoRequest request);

	List<WeChatUserInfoResponse> batchQueryUserInfo(long tenantId, List<String> openIdList);


	WeChatSendTemplateMessageResponse sendTemplateMessage(WeChatSendTemplateMessageRequest request);

}
