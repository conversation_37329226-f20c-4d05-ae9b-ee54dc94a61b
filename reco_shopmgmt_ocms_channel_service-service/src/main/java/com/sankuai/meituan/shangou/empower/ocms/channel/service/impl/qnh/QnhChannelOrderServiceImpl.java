package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.qnh;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.QnhProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.common.QueryDeliveryExceptionDescriptionResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.qnh.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DeliveryChannelCodeConvert;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.QnhConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.OrderStatusConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;

/**
 * 牵牛花订单
 */
@Slf4j
@Service("qnhChannelOrderService")
public class QnhChannelOrderServiceImpl implements ChannelOrderService {

    @Autowired
    private QnhChannelGateService qnhChannelGateService;

    @Resource
    private QnhStoreService qnhStoreService;

    @Resource
    private QnhSkuSpuConvertService qnhSkuSpuConvertService;

    private final List<String> qnhLocationSyncStatusList = Arrays.asList("2", "3", "4", "5");

    /**
     * 商家确认订单
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiConfirmOrder(PoiConfirmOrderRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        bizParam.put(QnhProjectConstant.STATUS, QnhOrderStatusEnum.MERCHANT_CONFIRMED.getCode());
        bizParam.put(QnhProjectConstant.TYPE, QnhOrderTypeEnum.ORDER_TYPE.getCode());
        QnhCommonResponse<String> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.ORDER_CONFIRM, bizParam);
        log.info("QnhChannelOrderServiceImpl.poiConfirmOrder, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        if (Objects.isNull(qnhCommonResponse)) {
            return ResultGenerator.genFailResult("调用牵牛花商家确认订单返回数据解析失败");
        }
        if ("0".equals(qnhCommonResponse.getCode())) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(qnhCommonResponse.getMsg());
    }

    /**
     * 获取订单详情
     *
     * @param request
     * @return
     */
    @Override
    public GetChannelOrderDetailResult getChannelOrderDetail(GetChannelOrderDetailRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        bizParam.put(QnhProjectConstant.TYPE, QnhOrderTypeEnum.ORDER_TYPE.getCode());
        QnhCommonResponse<QnhChannelOrderDetailDTO> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.ORDER_DETAIL, bizParam);
        log.info("QnhChannelOrderServiceImpl.getChannelOrderDetail, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        GetChannelOrderDetailResult orderDetailResult = new GetChannelOrderDetailResult();
        if (Objects.isNull(qnhCommonResponse)) {
            return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用牵牛花获取订单详情返回数据解析失败"));
        }
        if (!"0".equals(qnhCommonResponse.getCode())) {
            return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, qnhCommonResponse.getMsg()));
        }
        if (StringUtils.isEmpty(qnhCommonResponse.getData())) {
            return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用牵牛花获取订单详情返回数据为空"));
        }
        if (Objects.isNull(qnhCommonResponse.getStructuredData())) {
            return orderDetailResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用牵牛花获取订单详情返回数据为空"));
        }
        //返回数据映射
        ChannelOrderDetailDTO channelOrderDetailDTO = orderDetail2ChannelOrderDetailDto(request.getTenantId(), qnhCommonResponse.getStructuredData());
        log.info("QnhChannelOrderServiceImpl.getChannelOrderDetail, channelOrderDetailDTO:{}", channelOrderDetailDTO);
        return orderDetailResult.setStatus(ResultGenerator.genSuccessResult())
                .setChannelOrderDetail(channelOrderDetailDTO);
    }

    /**
     * 备餐完成
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus preparationMealComplete(PreparationMealCompleteRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        bizParam.put(QnhProjectConstant.STATUS, QnhOrderStatusEnum.PICK_DONE.getCode());
        bizParam.put(QnhProjectConstant.TYPE, QnhOrderTypeEnum.ORDER_TYPE.getCode());
        QnhCommonResponse<String> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.PICK_COMPLETE, bizParam);
        log.info("QnhChannelOrderServiceImpl.preparationMealComplete, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        if (Objects.isNull(qnhCommonResponse)) {
            return ResultGenerator.genFailResult("调用牵牛花商家备餐完成返回数据解析失败");
        }
        if ("0".equals(qnhCommonResponse.getCode())) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(qnhCommonResponse.getMsg());
    }
    /**
     * 领取拣货任务
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus startPickNotify(StartPickRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        bizParam.put(QnhProjectConstant.PICKUP_STATUS, QnhPickStatusEnum.RECEIVE.getCode());
        QnhCommonResponse<String> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.PICK_STATUS, bizParam);
        log.info("QnhChannelOrderServiceImpl.startPickNotify, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        if (Objects.isNull(qnhCommonResponse)) {
            return ResultGenerator.genFailResult("调用牵牛花开始拣货返回数据解析失败");
        }
        if ("0".equals(qnhCommonResponse.getCode())) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(qnhCommonResponse.getMsg()==null?"":qnhCommonResponse.getMsg());
    }

    @Override
    public GetOrderStatusResult getOrderStatus(GetOrderStatusRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        bizParam.put(QnhProjectConstant.TYPE, QnhOrderTypeEnum.ORDER_TYPE.getCode());
        QnhCommonResponse<QnhChannelOrderDetailDTO> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.ORDER_STATUS, bizParam);
        log.info("QnhChannelOrderServiceImpl.getOrderStatus, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        GetOrderStatusResult orderStatusResult = new GetOrderStatusResult();
        if (Objects.isNull(qnhCommonResponse)) {
            return orderStatusResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用牵牛花获取订单详情返回数据解析失败"));
        }
        if (!"0".equals(qnhCommonResponse.getCode())) {
            return orderStatusResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, qnhCommonResponse.getMsg()));
        }
        if (StringUtils.isEmpty(qnhCommonResponse.getData())) {
            return orderStatusResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用牵牛花获取订单详情返回数据为空"));
        }
        if (Objects.isNull(qnhCommonResponse.getStructuredData())) {
            return orderStatusResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用牵牛花获取订单详情返回数据为空"));
        }
        int status = OrderStatusConverter.qnhOrderStatusMapping(Integer.parseInt(qnhCommonResponse.getStructuredData().getHead().getOrder_status()));
        OrderStatusDTO orderStatusDTO = new OrderStatusDTO().setOrderId(request.getOrderId()).setStatus(status);
        orderStatusResult.setStatus(ResultGenerator.genSuccessResult()).setOrderStatus(orderStatusDTO);
        return orderStatusResult;
    }

    /**
     * 获取订单售后申请
     *
     * @param request
     * @return
     */
    @Override
    public GetOrderAfsApplyListResult getOrderAfsApplyList(GetOrderAfsApplyListRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getChannelOrderId());
        bizParam.put(QnhProjectConstant.TYPE, QnhOrderTypeEnum.RETURN_ORDER_TYPE.getCode());
        bizParam.put(QnhProjectConstant.RETURN_ORDER_ID, request.getAfterSaleId());
        QnhCommonResponse<QnhChannelOrderDetailDTO> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.RETURN_ORDER_DETAIL, bizParam);
        log.info("QnhChannelOrderServiceImpl.getOrderAfsApplyList, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        GetOrderAfsApplyListResult orderAfsApplyListResult = new GetOrderAfsApplyListResult();
        if (Objects.isNull(qnhCommonResponse)) {
            return orderAfsApplyListResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用牵牛花获取订单详情返回数据解析失败"));
        }
        if (!"0".equals(qnhCommonResponse.getCode())) {
            return orderAfsApplyListResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, qnhCommonResponse.getMsg()));
        }
        if (StringUtils.isEmpty(qnhCommonResponse.getData())) {
            return orderAfsApplyListResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用牵牛花获取订单详情返回数据为空"));
        }
        if (Objects.isNull(qnhCommonResponse.getStructuredData())) {
            return orderAfsApplyListResult.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用牵牛花获取订单详情返回数据为空"));
        }
        OrderAfsApplyDTO afsApplyDTO = toAfsApplyDTO(request.getTenantId(), qnhCommonResponse.getStructuredData());
        orderAfsApplyListResult.setStatus(ResultGenerator.genSuccessResult()).setAfsApplyList(Arrays.asList(afsApplyDTO));
        log.info("QnhChannelOrderServiceImpl.getOrderAfsApplyList, afsApplyDTO:{}", afsApplyDTO);
        return orderAfsApplyListResult;
    }

    /**
     * 同意售后
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus agreeRefund(AgreeRefundRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        //qnh 京东/elm/全球蛙/自建渠道均无afsId
        boolean sourceFlag = request.getChannelId() == ChannelTypeEnum.ELEM.getCode()
                || request.getChannelId() == ChannelTypeEnum.JD2HOME.getCode()
                || request.getChannelId() == ChannelTypeEnum.QUAN_QIU_WA.getCode()
                || request.getChannelId() == ChannelTypeEnum.SELF_CHANNEL.getCode();
        if(sourceFlag && StringUtils.isEmpty(request.getAfterSaleId())){
            bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
            bizParam.put(QnhProjectConstant.CHANNEL_KEYWORD, QnhChannelTypeEnum.valueOfEnum(request.getChannelId()).getKeyword());
            bizParam.put(QnhProjectConstant.IS_AGREE, true);
            bizParam.put(QnhProjectConstant.REGION_CODE, qnhStoreService.mappingQnhStoreIdByStoreId(request.getTenantId(), request.getStoreId()));
            QnhCommonResponse<String> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.AGREE_WAIT_REFUND, bizParam);
            log.info("QnhChannelOrderServiceImpl.agreeRefund for wait, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
            if (Objects.isNull(qnhCommonResponse)) {
                return ResultGenerator.genFailResult("调用牵牛花商家同意退款返回数据解析失败");
            }
            if ("0".equals(qnhCommonResponse.getCode())) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(qnhCommonResponse.getMsg());
        }

        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        bizParam.put(QnhProjectConstant.RETURN_ORDER_ID, request.getAfterSaleId());
        bizParam.put(QnhProjectConstant.STATUS, QnhReturnOrderStatusEnum.AGREE_REFUND.getCode());
        bizParam.put(QnhProjectConstant.TYPE, QnhOrderTypeEnum.RETURN_ORDER_TYPE.getCode());
        QnhCommonResponse<String> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.AGREE_REFUND, bizParam);
        log.info("QnhChannelOrderServiceImpl.agreeRefund, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        if (Objects.isNull(qnhCommonResponse)) {
            return ResultGenerator.genFailResult("调用牵牛花商家同意退款返回数据解析失败");
        }
        if ("0".equals(qnhCommonResponse.getCode())) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(qnhCommonResponse.getMsg());
    }

    /**
     * 拒绝售后
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus rejectRefund(RejectRefundRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        boolean sourceFlag = request.getChannelId() == ChannelTypeEnum.ELEM.getCode()
                || request.getChannelId() == ChannelTypeEnum.JD2HOME.getCode()
                || request.getChannelId() == ChannelTypeEnum.QUAN_QIU_WA.getCode()
                || request.getChannelId() == ChannelTypeEnum.SELF_CHANNEL.getCode();
        if(sourceFlag && StringUtils.isEmpty(request.getAfterSaleId()) ){
            bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
            bizParam.put(QnhProjectConstant.CHANNEL_KEYWORD, QnhChannelTypeEnum.valueOfEnum(request.getChannelId()).getKeyword());
            bizParam.put(QnhProjectConstant.IS_AGREE, false);
            bizParam.put(QnhProjectConstant.REGION_CODE, qnhStoreService.mappingQnhStoreIdByStoreId(request.getTenantId(), request.getStoreId()));
            if(StringUtils.isNotEmpty(request.getReason())){
                bizParam.put(QnhProjectConstant.REMARK, request.getReason());
            }
            QnhCommonResponse<String> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.REJECT_WAIT_REFUND, bizParam);
            log.info("QnhChannelOrderServiceImpl.rejectRefund for wait, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
            if (Objects.isNull(qnhCommonResponse)) {
                return ResultGenerator.genFailResult("调用牵牛花商家同意退款返回数据解析失败");
            }
            if ("0".equals(qnhCommonResponse.getCode())) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(qnhCommonResponse.getMsg());
        }
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        bizParam.put(QnhProjectConstant.RETURN_ORDER_ID, request.getAfterSaleId());
        bizParam.put(QnhProjectConstant.STATUS, QnhReturnOrderStatusEnum.REJECT_REFUND.getCode());
        bizParam.put(QnhProjectConstant.TYPE, QnhOrderTypeEnum.RETURN_ORDER_TYPE.getCode());
        if(StringUtils.isNotEmpty(request.getReason())){
            bizParam.put(QnhProjectConstant.REMARK, request.getReason());
        }
        QnhCommonResponse<String> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.REJECT_REFUND, bizParam);
        log.info("QnhChannelOrderServiceImpl.rejectRefund, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        if (Objects.isNull(qnhCommonResponse)) {
            return ResultGenerator.genFailResult("调用牵牛花商家拒绝退款返回数据解析失败");
        }
        if ("0".equals(qnhCommonResponse.getCode())) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(qnhCommonResponse.getMsg());
    }

    @Override
    public ResultStatus refundGoods(RefundGoodsRequest request) {
        return null;
    }

    /**
     * 商家取消订单
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiCancelOrder(PoiCancelOrderRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        bizParam.put(QnhProjectConstant.STATUS, QnhOrderStatusEnum.CANCEL.getCode());
        bizParam.put(QnhProjectConstant.TYPE, QnhOrderTypeEnum.ORDER_TYPE.getCode());
        bizParam.put(QnhProjectConstant.REASON_CODE, QnhConverterUtil.convertReasonCode(request.getChannelId(),request.getReason_code()));
        bizParam.put(QnhProjectConstant.REASON, request.getReason());
        QnhCommonResponse<String> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.ORDER_CANCEL, bizParam);
        log.info("QnhChannelOrderServiceImpl.poiCancelOrder, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        if (Objects.isNull(qnhCommonResponse)) {
            return ResultGenerator.genFailResult("调用牵牛花商家取消订单返回数据解析失败");
        }
        if ("0".equals(qnhCommonResponse.getCode())) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(qnhCommonResponse.getMsg());
    }

    /**
     * 商家部分退款
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiPartRefundApply(PoiPartRefundRequest request) {

        Map<String, Object> bizParam = Maps.newHashMap();
        QnhCommonResponse<String> qnhCommonResponse = null;
        bizParam.put(QnhProjectConstant.REASON, request.getReason());
        bizParam.put(QnhProjectConstant.REASON_CODE, QnhConverterUtil.convertReasonCode(request.getChannelId(),request.getReasonCode()));
        //克重退款
        if (ProjectConstant.MT_WEIGHT_REFUND_TYPE == request.getPartRefundType()) {
            bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
            if (CollectionUtils.isNotEmpty(request.getRefundProducts())) {
                List<Map<String, Object>> refundProducts = new ArrayList<>();
                request.getRefundProducts().forEach(product -> {
                    Map<String, Object> productBizMap = new HashMap<>();
                    productBizMap.put(QnhProjectConstant.ITEM_ID, product.getThirdPartItemId());
                    productBizMap.put(QnhProjectConstant.SKU_ID, product.getSkuId());
                    productBizMap.put(QnhProjectConstant.PRACTICAL_WEIGHT, product.getActualWeight());
                    productBizMap.put(QnhProjectConstant.SALE_PRICE, MoneyUtils.fenToYuan(product.getSalePrice()).doubleValue());
                    refundProducts.add(productBizMap);
                });
                bizParam.put(QnhProjectConstant.ITEMS, refundProducts);
            }
            qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.WEIGHT_PART_REFUND, bizParam);
        } else {
            bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
            bizParam.put(QnhProjectConstant.CHANNEL_KEYWORD, QnhChannelTypeEnum.valueOfEnum(request.getChannelId()).getKeyword());
            bizParam.put(QnhProjectConstant.REGION_CODE, qnhStoreService.mappingQnhStoreIdByStoreId(request.getTenantId(), request.getStoreId()));
            bizParam.put(QnhProjectConstant.TYPE, request.getPartRefundType());
            bizParam.put(QnhProjectConstant.WAY, "10");//暂时为仅退款
            if (CollectionUtils.isNotEmpty(request.getRefundProducts())) {
                List<Map<String, Object>> refundProducts = new ArrayList<>();
                request.getRefundProducts().forEach(product -> {
                    Map<String, Object> productBizMap = new HashMap<>();
                    productBizMap.put(QnhProjectConstant.SALE_QTY, product.getCount());
                    productBizMap.put(QnhProjectConstant.SALE_PRICE, MoneyUtils.fenToYuan(product.getSalePrice()).doubleValue());
                    productBizMap.put(QnhProjectConstant.SKU_ID, product.getSkuId());
                    productBizMap.put(QnhProjectConstant.UPC, product.getUpc());
                    if(StringUtils.isNotEmpty(product.getThirdPartSpuId())){
                        productBizMap.put(QnhProjectConstant.SPU_ID, product.getThirdPartSpuId());
                    }
                    refundProducts.add(productBizMap);
                });
                bizParam.put(QnhProjectConstant.ITEMS, refundProducts);
            }
            qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.PART_REFUND, bizParam);
        }
        log.info("QnhChannelOrderServiceImpl.poiPartRefundApply, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        if (Objects.isNull(qnhCommonResponse)) {
            return ResultGenerator.genFailResult("调用牵牛花商家退款返回数据解析失败");
        }
        if ("0".equals(qnhCommonResponse.getCode())) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(qnhCommonResponse.getMsg());
    }

    /**
     * 克重退差试算返回结果
     *
     * @param request
     * @return
     */
    @Override
    public CalculateRefundResult poiPartRefundCalculate(CalculateRefundRequest request) {
        return null;
    }

    /**
     * 商家确认收货
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus poiConfirmReceiveGoods(PoiConfirmReceiveGoodsRequest request) {
        return null;
    }

    @Override
    public PoiAdjustOrderResult poiAdjustOrder(PoiAdjustOrderRequest request) {
        return null;
    }

    /**
     * 订单应结金额查询接口
     *
     * @param request
     * @return
     */
    @Override
    public OrderShouldSettlementResult queryOrderShouldSettlementInfo(OrderShouldSettlementInfoRequest request) {
        return null;
    }

    /**
     * 商品金额拆分查询接口
     *
     * @param request
     * @return
     */
    @Override
    public GoodsSettlementResult queryGoodsSettlementInfo(GoodsSettlementInfoRequest request) {
        return null;
    }

    /**
     * 查询订单配送状态
     *
     * @param request
     * @return
     */
    @Override
    public GetLogisticsStatusResult getLogisticsStatus(GetLogisticsStatusRequest request) {
        return null;
    }

    /**
     * 更新订单配送状态
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus updateOrderDeliveryStatus(UpdateOrderDeliveryStatusRequest request) {
        return null;
    }

    /**
     * 更新配送信息
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus updateDeliveryInfo(UpdateDeliveryInfoRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        String status = QnhConverterUtil.convertDeliveryStatus(request.getStatus());
        if("-1".equals(status)){
            //不支持的状态直接返回
            return ResultGenerator.genSuccessResult();
        }
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        bizParam.put(QnhProjectConstant.CHANNEL_KEYWORD, QnhChannelTypeEnum.valueOfEnum(request.getChannelId()).getKeyword());
        bizParam.put(QnhProjectConstant.REGION_CODE, qnhStoreService.mappingQnhStoreIdByStoreId(request.getTenantId(), request.getShopId()));
        bizParam.put(QnhProjectConstant.DELIVERY_STATUS, status);
        if (StringUtils.isNotEmpty(request.getRiderName())) {
            bizParam.put(QnhProjectConstant.DELIVERY_MAN, request.getRiderName());
        }
        if (StringUtils.isNotEmpty(request.getRiderPhone())) {
            bizParam.put(QnhProjectConstant.DELIVERY_PHONE, request.getRiderPhone());
        }
        if (qnhLocationSyncStatusList.contains(status)) {
            bizParam.put(QnhProjectConstant.LAT, request.getLatitude());
            bizParam.put(QnhProjectConstant.LNG, request.getLongitude());
        }

        if (request.getDeliveryOrderId() != null && MccConfigUtil.getMtThirdCarrierOrderIdSwitch()) {
            bizParam.put(QnhProjectConstant.THRID_CARRIER_ORDER_ID, request.getDeliveryOrderId());
        }

        Integer mtDeliveryChannelCode = DeliveryChannelCodeConvert.getQnhOrderMtDeliveryChannelCode(request.getDeliveryChannelId());
        if (mtDeliveryChannelCode != null) {
            bizParam.put(QnhProjectConstant.LOGISTICS_PROVIDER_CODE, mtDeliveryChannelCode);
        }

        QnhCommonResponse<String> qnhStatusCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.UPDATE_ORDER_DELIVERY_STATUS, bizParam);
        log.info("QnhChannelOrderServiceImpl.updateDeliveryInfo.updateDeliveryStatus, request:{}, qnhStatusCommonResponse:{}", request, qnhStatusCommonResponse);

//        Map<String, Object> locationBizParam = Maps.newHashMap();
//        if (!qnhLocationSyncStatusList.contains(status) || StringUtils.isEmpty(request.getRiderName()) || StringUtils.isEmpty(request.getRiderPhone())) {
//            log.info("QnhChannelOrderServiceImpl.updateDeliveryInfo.location not sync");
//            return mergeDeliveryInfoResult(qnhStatusCommonResponse, null);
//        }
//        locationBizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
//        locationBizParam.put(QnhProjectConstant.DELIVERY_STATUS, status);
//        locationBizParam.put(QnhProjectConstant.DELIVERY_MAN, request.getRiderName());
//        locationBizParam.put(QnhProjectConstant.DELIVERY_PHONE, request.getRiderPhone());
//        locationBizParam.put(QnhProjectConstant.LAT, request.getLatitude());
//        locationBizParam.put(QnhProjectConstant.LNG, request.getLongitude());
//        QnhCommonResponse<String> qnhLocationCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.UPDATE_DELIVERY_LOCATION, bizParam);
//        log.info("QnhChannelOrderServiceImpl.updateDeliveryInfo.updateDeliveryStatus, request:{}, qnhLocationCommonResponse:{}", request, qnhLocationCommonResponse);
        return mergeDeliveryInfoResult(qnhStatusCommonResponse, null);
    }

    @Override
    public ResultStatus updateRiderInfo(UpdateDeliveryInfoRequest request) {
        return updateDeliveryInfo(request);
    }

    private ResultStatus mergeDeliveryInfoResult(QnhCommonResponse<String> qnhStatusCommonResponse, QnhCommonResponse<String> qnhLocationCommonResponse) {
        if (Objects.isNull(qnhStatusCommonResponse)) {
            return ResultGenerator.genFailResult("调用牵牛花更新配送状态返回数据解析失败");
        }
        if (Objects.isNull(qnhLocationCommonResponse)) {
            if ("0".equals(qnhStatusCommonResponse.getCode())) {
                return ResultGenerator.genSuccessResult();
            }
            return ResultGenerator.genFailResult(qnhStatusCommonResponse.getMsg());
        }
        if ("0".equals(qnhStatusCommonResponse.getCode()) && "0".equals(qnhLocationCommonResponse.getCode())) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult("调用牵牛花更新配送状态失败");
    }

    /**
     * 转商家自配送
     *
     * @param request
     * @return
     */
    @Override
    public ResultStatus selfDelivery(SelfDeliveryRequest request) {
        return null;
    }

    @Override
    public ResultStatus onlyUpdateRiderInfo(UpdateDeliveryInfoRequest request) {
        return new ResultStatus(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(), ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg(), null);
    }

    /***
     * 查询订单配送异常描述
     * **/
    @Override
    public QueryDeliveryExceptionDescriptionResponse queryDeliveryExceptionDescription(QueryDeliveryExceptionDescriptionRequest request) {
        return null;
    }

    /**
     * 查询渠道订单部分退款商品详情
     */
    @Override
    public ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(ChannelPartRefundGoodsRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        QnhCommonResponse<List<QnhChannelOrderPartRefundGoodsDetailDTO>> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.QUERY_PART_REFUND_DETAIL, bizParam);
        log.info("QnhChannelOrderServiceImpl.queryChannelPartRefundGoodsDetail, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        ChannelPartRefundGoodsResult resp = new ChannelPartRefundGoodsResult();
        if (Objects.isNull(qnhCommonResponse)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询订单部分退款商品失败"));
        }
        if (!"0".equals(qnhCommonResponse.getCode())) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, qnhCommonResponse.getMsg()));
        }
        List<PartRefundGoodDetailDTO> goodDetailDTOList = toPartRefundGoodDetailList(request.getTenantId(), request.getStoreId(), request.getChannelId(), qnhCommonResponse.getStructuredData());
        log.info("QnhChannelOrderServiceImpl.queryChannelPartRefundGoodsDetail, goodDetailDTOList:{}",goodDetailDTOList);
        return resp.setStatus(ResultGenerator.genSuccessResult()).setPartRefundGoodsList(goodDetailDTOList);
    }

    private List<PartRefundGoodDetailDTO> toPartRefundGoodDetailList(long tenantId, long storeId, Integer channelId, List<QnhChannelOrderPartRefundGoodsDetailDTO> detailDTOList) {
        List<PartRefundGoodDetailDTO> goodDetailDTOList = QnhConverterUtil.convertPartRefundGoodDetailList(detailDTOList);
        if (CollectionUtils.isEmpty(goodDetailDTOList)) {
            return Collections.emptyList();
        }
        List<String> qnhSkuList = new ArrayList<>(new HashSet<>(Lists.transform(goodDetailDTOList, new Function<PartRefundGoodDetailDTO, String>() {
            @Override
            public String apply(PartRefundGoodDetailDTO refundProductDTO) {
                return refundProductDTO.getThirdPartSkuId();
            }
        })));
        Map<String, QnhSkuSpuMappingInfo> channelInfoMap = queryChannelInfoByQnhSkuList(tenantId, storeId, channelId, qnhSkuList);
        if (MapUtils.isNotEmpty(channelInfoMap)) {
            goodDetailDTOList.forEach(sku -> {
                if (!channelInfoMap.containsKey(sku.getThirdPartSkuId())) {
                    return;
                }
                QnhSkuSpuMappingInfo info = channelInfoMap.get(sku.getThirdPartSkuId());
                sku.setSkuId(info.getCustomSkuId());
                sku.setCustomSpu(info.getCustomSpuId());
            });
        } else {
            //qnh返回的item_code为渠道sku
            goodDetailDTOList.forEach(sku -> {
                sku.setSkuId(sku.getThirdPartSkuId());
            });
        }
        return goodDetailDTOList;
    }

    /**
     * 分页查询渠道订单订单列表
     */
    @Override
    public QueryChannelOrderListResult queryChannelOrderList(QueryChannelOrderListRequest request) {
        return null;
    }

    /**
     * 分页查询渠道异常订单订单列表
     */
    @Override
    public QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(QueryChannelAbnormalOrderRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.PAGE_NO, request.getPageNo());
        bizParam.put(QnhProjectConstant.PAGE_SIZE, request.getLimit());
        bizParam.put(QnhProjectConstant.TYPE, QnhOrderTypeEnum.ORDER_TYPE.getCode());
        bizParam.put(QnhProjectConstant.START_TIME, request.getStartTime());
        bizParam.put(QnhProjectConstant.END_TIME, request.getEndTime());
        bizParam.put(QnhProjectConstant.REGION_CODE, qnhStoreService.mappingQnhStoreIdByStoreId(request.getTenantId(), request.getStoreId()));
        QnhCommonResponse<QnhChannelOrderAbnormalDataDetailDTO> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.QUERY_ABNORMAL_ORDER_LIST, bizParam);
        log.info("QnhChannelOrderServiceImpl.queryChannelAbnormalOrderList, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        QueryChannelAbnormalOrderResult resp = new QueryChannelAbnormalOrderResult();
        if (Objects.isNull(qnhCommonResponse)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询异常单失败"));
        }
        if (!"0".equals(qnhCommonResponse.getCode())) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, qnhCommonResponse.getMsg()));
        }
        QnhChannelOrderAbnormalDataDetailDTO dataDetailDTO = qnhCommonResponse.getStructuredData();
        if (Objects.isNull(dataDetailDTO)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询数据为空"));
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setOrderWithChannelList(QnhConverterUtil.convertAbOrderWithChannel(dataDetailDTO)).setTotalCount(dataDetailDTO.getCount());
    }

    @Override
    public WeightPartRefundGoodsResult queryWeightPartRefundGoodsDetail(WeightPartRefundGoodsRequest request) {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        QnhCommonResponse<List<QnhChannelOrderPartRefundGoodsDetailDTO>> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.QUERY_WEIGHT_PART_REFUND_DETAIL, bizParam);
        log.info("QnhChannelOrderServiceImpl.queryWeightPartRefundGoodsDetail, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        WeightPartRefundGoodsResult resp = new WeightPartRefundGoodsResult();
        if (Objects.isNull(qnhCommonResponse)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询异常单失败"));
        }
        if (!"0".equals(qnhCommonResponse.getCode())) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, qnhCommonResponse.getMsg()));
        }
        List<QnhChannelOrderPartRefundGoodsDetailDTO> goodsDetailDTOList = qnhCommonResponse.getStructuredData();
        List<WeightPartRefundGoodsDTO> goodsDTOList = toWeightPartGoodsList(request.getTenantId(), request.getStoreId(), request.getChannelId(), goodsDetailDTOList);
        log.info("QnhChannelOrderServiceImpl.queryWeightPartRefundGoodsDetail, goodsDTOList:{}", goodsDTOList);
        return resp.setStatus(ResultGenerator.genSuccessResult()).setChannelOrderIdList(goodsDTOList);
    }

    public QueryReturnAbnormalOrderResult queryAbnormalReturnOrderList(QueryReturnAbnormalOrderRequest request) throws TException {
        Map<String, Object> bizParam = Maps.newHashMap();
        bizParam.put(QnhProjectConstant.ORDER_ID, request.getOrderId());
        bizParam.put(QnhProjectConstant.TYPE, QnhOrderTypeEnum.RETURN_ORDER_TYPE.getCode());
        QnhCommonResponse<QnhChannelOrderAbnormalDataDetailDTO> qnhCommonResponse = qnhChannelGateService.sendPost(request.getTenantId(), ChannelPostQnhEnum.QUERY_ABNORMAL_ORDER_LIST, bizParam);
        log.info("QnhChannelOrderServiceImpl.queryAbnormalReturnOrderList, request:{}, qnhCommonResponse:{}", request, qnhCommonResponse);
        QueryReturnAbnormalOrderResult resp = new QueryReturnAbnormalOrderResult();
        if (Objects.isNull(qnhCommonResponse)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询异常单失败"));
        }
        if (!"0".equals(qnhCommonResponse.getCode())) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, qnhCommonResponse.getMsg()));
        }
        QnhChannelOrderAbnormalDataDetailDTO dataDetailDTO = qnhCommonResponse.getStructuredData();
        if (Objects.isNull(dataDetailDTO)) {
            return resp.setStatus(ResultGenerator.genResult(ResultCode.FAIL, "调用渠道查询数据为空"));
        }
        return resp.setStatus(ResultGenerator.genSuccessResult()).setAbReturnOrderList(QnhConverterUtil.convertAbReturnOrder(dataDetailDTO)).setTotalCount(dataDetailDTO.getCount());
    }

    private List<WeightPartRefundGoodsDTO> toWeightPartGoodsList(long tenantId, long storeId, Integer channelId, List<QnhChannelOrderPartRefundGoodsDetailDTO> goodsDetailDTOList) {
        if (CollectionUtils.isEmpty(goodsDetailDTOList)) {
            return Collections.emptyList();
        }
        List<WeightPartRefundGoodsDTO> goodsDTOList = QnhConverterUtil.convertWeightPartRefundGoodDetailList(goodsDetailDTOList);
        List<String> qnhSkuList = new ArrayList<>(new HashSet<>(Lists.transform(goodsDTOList, new Function<WeightPartRefundGoodsDTO, String>() {
            @Override
            public String apply(WeightPartRefundGoodsDTO refundProductDTO) {
                return refundProductDTO.getThirdPartSkuId();
            }
        })));
        Map<String, QnhSkuSpuMappingInfo> channelInfoMap = queryChannelInfoByQnhSkuList(tenantId, storeId, channelId, qnhSkuList);
        if (MapUtils.isNotEmpty(channelInfoMap)) {
            goodsDTOList.forEach(sku -> {
                if (!channelInfoMap.containsKey(sku.getThirdPartSkuId())) {
                    return;
                }
                QnhSkuSpuMappingInfo info = channelInfoMap.get(sku.getThirdPartSkuId());
                sku.setCustomSkuId(info.getCustomSkuId());
                sku.setSkuId(info.getSkuId());
            });
        } else {
            //qnh返回的item_code为渠道sku
            goodsDTOList.forEach(sku -> {
                sku.setSkuId(sku.getThirdPartSkuId());
            });
        }
        return goodsDTOList;
    }

    private List<String> orderDetailToAllSku(ChannelOrderDetailDTO channelOrderDetailDTO){
        if(channelOrderDetailDTO==null){
            return Collections.emptyList();
        }
        Set<String> qnhSkuSet=new HashSet<>();
        if(CollectionUtils.isNotEmpty(channelOrderDetailDTO.getSkuDetails())){
            qnhSkuSet.addAll(Lists.transform(channelOrderDetailDTO.getSkuDetails(), new Function<OrderProductDetailDTO, String>() {
                @Override
                public String apply(OrderProductDetailDTO orderProductDetailDTO) {
                    return orderProductDetailDTO.getThirdPartSkuId();
                }
            }));
        }

        if(CollectionUtils.isNotEmpty(channelOrderDetailDTO.getActivities())){
            qnhSkuSet.addAll(Lists.transform(channelOrderDetailDTO.getActivities(), new Function<OrderDiscountDetailDTO, String>() {
                @Override
                public String apply(OrderDiscountDetailDTO orderDiscountDetailDTO) {
                    if(orderDiscountDetailDTO.getGiftInfo()==null){
                        return "";
                    }
                    return orderDiscountDetailDTO.getGiftInfo().getThirdPartSkuId();
                }
            }));
        }
        return new ArrayList<>(qnhSkuSet);
    }

    private ChannelOrderDetailDTO orderDetail2ChannelOrderDetailDto(long tenantId, QnhChannelOrderDetailDTO channelOrderDetail) {
        try {
            ChannelOrderDetailDTO channelOrderDetailDTO = QnhConverterUtil.qnhChannelOrderDetail2ChannelDetail(channelOrderDetail);
            List<String> qnhSkuList = orderDetailToAllSku(channelOrderDetailDTO);

            long storeId = qnhStoreService.mappingStoreIdByQnhStoreId(tenantId,channelOrderDetail.getHead().getRegion_code());
            channelOrderDetailDTO.setStoreId(storeId);
            Map<String, QnhSkuSpuMappingInfo> channelInfoMap = queryChannelInfoByQnhSkuList(tenantId, storeId, channelOrderDetailDTO.getChannelId(), qnhSkuList);
            if (MapUtils.isNotEmpty(channelInfoMap)) {
                channelOrderDetailDTO.getSkuDetails().forEach(sku -> {
                    if (!channelInfoMap.containsKey(sku.getThirdPartSkuId())) {
                        return;
                    }
                    QnhSkuSpuMappingInfo info = channelInfoMap.get(sku.getThirdPartSkuId());
                    sku.setSkuId(info.getCustomSkuId());
                    sku.setCustomSpu(info.getCustomSpuId());
                });

                if(CollectionUtils.isNotEmpty(channelOrderDetailDTO.getActivities())){
                    channelOrderDetailDTO.getActivities().forEach(activity->{
                        if(activity.getGiftInfo()==null){
                            return;
                        }
                        if (!channelInfoMap.containsKey(activity.getGiftInfo().getThirdPartSkuId())) {
                            return;
                        }
                        QnhSkuSpuMappingInfo info = channelInfoMap.get(activity.getGiftInfo().getThirdPartSkuId());
                        activity.getGiftInfo().setSkuId(info.getCustomSkuId());
                        activity.getGiftInfo().setSpu(info.getCustomSpuId());
                    });
                }

                if(CollectionUtils.isNotEmpty(channelOrderDetailDTO.getSkuSharedActivities())){
                    channelOrderDetailDTO.getSkuSharedActivities().forEach(activity->{
                        if (!channelInfoMap.containsKey(activity.getThirdPartSkuId())) {
                            return;
                        }
                        QnhSkuSpuMappingInfo info = channelInfoMap.get(activity.getThirdPartSkuId());
                        activity.setCustomSkuId(info.getCustomSkuId());
                        activity.setCustomSpu(info.getCustomSpuId());
                    });
                }
            } else {
                channelOrderDetailDTO.getSkuDetails().forEach(sku -> {
                    sku.setSkuId(sku.getThirdPartSkuId());
                });
                if (CollectionUtils.isNotEmpty(channelOrderDetailDTO.getActivities())) {
                    channelOrderDetailDTO.getActivities().forEach(activity -> {
                        if (activity.getGiftInfo() == null) {
                            return;
                        }
                        activity.getGiftInfo().setSkuId(activity.getGiftInfo().getThirdPartSkuId());
                    });
                }
            }
            return channelOrderDetailDTO;
        } catch (Exception e) {
            log.error("订单映射异常", e);
            throw new IllegalArgumentException("订单映射异常");
        }
    }

    private OrderAfsApplyDTO toAfsApplyDTO(long tenantId, QnhChannelOrderDetailDTO channelOrderDetail) {
        try {
            long storeId = qnhStoreService.mappingStoreIdByQnhStoreId(tenantId,channelOrderDetail.getHead().getRegion_code());
            OrderAfsApplyDTO afsApplyDTO = QnhConverterUtil.convertToAfsApply(channelOrderDetail);
            List<String> qnhSkuList = new ArrayList<>(new HashSet<>(Lists.transform(afsApplyDTO.getAfsProductList(), new Function<RefundProductDTO, String>() {
                @Override
                public String apply(RefundProductDTO refundProductDTO) {
                    return refundProductDTO.getThirdPartSkuId();
                }
            })));
            Map<String, QnhSkuSpuMappingInfo> channelInfoMap = queryChannelInfoByQnhSkuList(tenantId, storeId, afsApplyDTO.getChannelType(), qnhSkuList);
            if (MapUtils.isNotEmpty(channelInfoMap)) {
                afsApplyDTO.getAfsProductList().forEach(sku -> {
                    if (!channelInfoMap.containsKey(sku.getThirdPartSkuId())) {
                        return;
                    }
                    QnhSkuSpuMappingInfo info = channelInfoMap.get(sku.getThirdPartSkuId());
                    sku.setSkuId(info.getCustomSkuId());
                    sku.setCustomSpu(info.getCustomSpuId());
                });
            } else {
                //qnh返回的item_code为渠道sku
                afsApplyDTO.getAfsProductList().forEach(sku -> {
                    sku.setSkuId(sku.getThirdPartSkuId());
                });
            }
            return afsApplyDTO;
        } catch (Exception e) {
            log.error("退款订单映射异常", e);
            throw new IllegalArgumentException("退款订单映射异常");
        }
    }

    private Map<String, QnhSkuSpuMappingInfo> queryChannelInfoByQnhSkuList(long tenantId, long storeId, Integer channelId, List<String> qnhSkuList) {
        if (CollectionUtils.isEmpty(qnhSkuList)) {
            return Collections.emptyMap();
        }
        try {
            List<QnhSkuSpuMappingInfo> mappingInfoList = qnhSkuSpuConvertService.queryFnInfoByQnhSkuList(tenantId, qnhSkuList);
            if (CollectionUtils.isEmpty(mappingInfoList)) {
                return Collections.emptyMap();
            }
            List<String> fnSkuIdList = new ArrayList<>(Lists.transform(mappingInfoList, new Function<QnhSkuSpuMappingInfo, String>() {

                @Override
                public String apply(QnhSkuSpuMappingInfo qnhSkuSpuMappingInfo) {
                    return qnhSkuSpuMappingInfo.getSkuId();
                }
            }));
            List<QnhSkuSpuMappingInfo> channelInfoList = qnhSkuSpuConvertService.queryCustomInfoByFnSkuList(tenantId, storeId, channelId, fnSkuIdList);
            if (CollectionUtils.isEmpty(channelInfoList)) {
                return Collections.emptyMap();
            }
            Map<String, QnhSkuSpuMappingInfo> channelInfoMap = new HashMap<>();
            channelInfoList.forEach(w -> {
                channelInfoMap.put(w.getSkuId(), w);
            });
            Map<String, QnhSkuSpuMappingInfo> channelInfoWithQnh = new HashMap<>();
            mappingInfoList.forEach(w -> {
                if (channelInfoMap.containsKey(w.getSkuId())) {
                    QnhSkuSpuMappingInfo channelInfo=channelInfoMap.get(w.getSkuId());
                    w.setCustomSkuId(channelInfo.getCustomSkuId());
                    w.setCustomSpuId(channelInfo.getCustomSpuId());
                    channelInfoWithQnh.put(w.getQnhSkuId(),w);
                }
            });
            log.info("queryChannelInfoByQnhSkuList:channelInfoWithQnh:{}",channelInfoWithQnh);
            return channelInfoWithQnh;

        } catch (Exception e) {
            log.error("queryChannelInfoByQnhSkuList error tenantId:{},storeId:{},channelId:{},qnhSkuList:{}", tenantId, storeId, channelId, qnhSkuList, e);
        }
        return Collections.emptyMap();
    }

}
