package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.ParserConfig;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.weixin.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostWeiXinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.EventPublisher;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.WeChatChannelCommonService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.WeChatConverterUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.wechat.event.UpdateTokenEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/04
 */
@Service
@Slf4j
public class WeChatChannelCommonServiceImpl implements WeChatChannelCommonService {

	@Resource
	WeChatChannelGateService weChatChannelGateService;

	@Resource
	private CopAccessConfigService copAccessConfigService;


	@Override
	public List<WeChatUserInfoResponse> batchQueryUserInfo(long tenantId, List<String> openIdList) {
		BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId)
				.setChannelId(ChannelTypeEnum.WECHAT.getCode()).setStoreIdList(Lists.newArrayList());
		Map<String, Object> bizParam = WeChatConverterUtil.buildWeChatBatchQueryUserInfoBizParam(openIdList);
		Map<String, Object> weChatUserInfoMap = weChatChannelGateService.sendPost(
				weChatChannelGateService.getPostUrl(ChannelPostWeiXinEnum.BATCH_QUERY_USER_INFO),
				null, baseRequest, bizParam
		);
		log.info("批量查询微信用户信息, openId:{}, result:{}", openIdList, weChatUserInfoMap);
		log.info("是否开启Safemode：{}", ParserConfig.getGlobalInstance().isSafeMode());
		if (weChatUserInfoMap.get("errcode") != null) {
			log.error("查询微信用户信息失败{}", openIdList);
			checkAccessToken((int)weChatUserInfoMap.get("errcode"), tenantId);
			return new ArrayList<>();
		}
		return JSON.parseArray(
				weChatUserInfoMap.get("user_info_list").toString(),
				WeChatUserInfoResponse.class);
	}

	@Override
	public WeChatUserInfoResponse queryUserInfo(WeChatUserInfoRequest request) {
		BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
			.setChannelId(ChannelTypeEnum.WECHAT.getCode()).setStoreIdList(Lists.newArrayList());
		Map<String, Object> bizParam = WeChatConverterUtil.buildWeChatQueryUserInfoBizParam(request);
		Map<String, Object> weChatUserInfoMap = weChatChannelGateService.sendGet(
			weChatChannelGateService.getPostUrl(ChannelPostWeiXinEnum.QUERY_USER_INFO),
			null, baseRequest, bizParam
		);
		log.info("查询微信用户信息, openId:{}, result:{}", request.getOpenId(), weChatUserInfoMap);
		WeChatUserInfoResponse weChatUserInfoResponse = JSON.parseObject(JSON.toJSONString(weChatUserInfoMap), WeChatUserInfoResponse.class);
		checkAccessToken(weChatUserInfoResponse.getErrcode(), request.getTenantId());
		return weChatUserInfoResponse;
	}

	@Override
	public WeChatSendTemplateMessageResponse sendTemplateMessage(WeChatSendTemplateMessageRequest request) {
		BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
				.setChannelId(ChannelTypeEnum.WECHAT.getCode()).setStoreIdList(Lists.newArrayList());
		Map<String, Object> bizParam = WeChatConverterUtil.buildWeChatSendTemplateMessageBizParam(request);
		Map<String, Object> resultMap = weChatChannelGateService.sendPost(
				weChatChannelGateService.getPostUrl(ChannelPostWeiXinEnum.SEND_TEMPLATE_MESSAGE),
				null, baseRequest, bizParam
		);
		log.info("发送模版消息, openId:{}, result:{}", request.getTouser(), JSON.toJSONString(resultMap));
		WeChatSendTemplateMessageResponse response = JSON.parseObject(JSON.toJSONString(resultMap), WeChatSendTemplateMessageResponse.class);
		checkAccessToken(response.getErrcode(), request.getTenantId());
		return response;
	}

	@Override
	public WeChatUpdateTokenResponse getAccessToken(Long tenantId) {
		BaseRequest baseRequest = new BaseRequest().setTenantId(tenantId)
				.setChannelId(ChannelTypeEnum.WECHAT.getCode()).setStoreIdList(Lists.newArrayList());
		Map<String, Object> bizParam = Maps.newHashMap();
		Map<String, Object> resultMap = weChatChannelGateService.sendGet(
				weChatChannelGateService.getPostUrl(ChannelPostWeiXinEnum.UPDATE_TOKEN),
				null, baseRequest, bizParam
		);
		log.info("更新微信公众号accessToken, tenantId:{}, result:{}", tenantId, resultMap);
		WeChatUpdateTokenResponse updateTokenResponse = JSON.parseObject(JSON.toJSONString(resultMap), WeChatUpdateTokenResponse.class);
		return updateTokenResponse;
	}

	@Override
	public WeChatGetTicketResponse getWeChatTicket(WeChatGetTicketRequest request) {
		BaseRequest baseRequest = new BaseRequest().setTenantId(request.getTenantId())
			.setChannelId(ChannelTypeEnum.WECHAT.getCode()).setStoreIdList(Lists.newArrayList());
		Map<String, Object> bizParam = WeChatConverterUtil.buildWeChatGetTicketInfoBizParam(request);
		Map<String, Object> resultMap = weChatChannelGateService.sendPost(
			weChatChannelGateService.getPostUrl(ChannelPostWeiXinEnum.GENERATE_QRCODE),
			null, baseRequest, bizParam
		);
		log.info("生成微信二维码, tenantId:{}, result:{}", request.getTenantId(), resultMap);
		WeChatGetTicketResponse weChatGetTicketResponse = JSON.parseObject(JSON.toJSONString(resultMap), WeChatGetTicketResponse.class);
		checkAccessToken(weChatGetTicketResponse.getErrcode(), request.getTenantId());
		return weChatGetTicketResponse;
	}

	private Map<String, Object> getSysParam(long tenantId) {
		String sysParamsJson = copAccessConfigService.selectSysParams(tenantId, ChannelTypeEnum.WECHAT.getCode());
		Map<String, Object> sysParam = JSON.parseObject(sysParamsJson);
		if (StringUtils.isEmpty(sysParamsJson) || !sysParam.containsKey(ProjectConstant.SECRET) || !sysParam.containsKey(ProjectConstant.WECHAT_APP_ID_KEY)) {
			log.error("updateAccessToken, 系统参数获取失败, sysParams:{}", sysParamsJson);
			return null;
		}
		return sysParam;
	}

	private void checkAccessToken(Integer errorCode, long tenantId) {
		if (errorCode != null && (errorCode.equals(Integer.valueOf(40001)) || errorCode.equals(Integer.valueOf(42001)))) {
			MetricHelper.build().name("wechat.pullnew.updateTokenAsync").count();
			Map<String, Object> sysParam = getSysParam(tenantId);
			log.error("微信公众号接口请求accessToken失效，tenant:{}", tenantId);
			EventPublisher.publishEvent(new UpdateTokenEvent(
				sysParam.get(ProjectConstant.WECHAT_APP_ID_KEY).toString(),
				sysParam.get(ProjectConstant.SECRET).toString(),
				tenantId
			));
		}
	}

}
