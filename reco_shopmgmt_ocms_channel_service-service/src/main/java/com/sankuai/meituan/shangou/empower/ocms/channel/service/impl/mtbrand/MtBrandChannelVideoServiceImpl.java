package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelVideoBindDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelVideoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelVideoUploadDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostMTEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.StoreIdNotExistException;
import com.sankuai.meituan.shangou.empower.ocms.channel.mapper.ChannelStoreMapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.BatchVideoUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.BatchVideoUploadResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoBindRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoBindResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoUploadDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.VideoUploadResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelVideoService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: wangyihao04
 * @Date: 2022-03-21 17:10
 * @Mail: <EMAIL>
 */
@Service("mtBrandChannelVideoService")
public class MtBrandChannelVideoServiceImpl implements ChannelVideoService {
    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;
    @Resource
    private MtConverterService mtConverterService;
    @Autowired
    private ChannelStoreMapper channelStoreMapper;
    @Autowired
    private CopChannelStoreService copChannelStoreService;

    public static final String NG = "ng";

    @Override
    public VideoUploadResponse uploadVideo(VideoUploadRequest request) {
        VideoUploadResponse response = new VideoUploadResponse();

        ChannelVideoUploadDTO uploadDTO = mtConverterService.uploadVideo(request);
        HashMap<Integer,ChannelResponseDTO> httpRespMap = mtBrandChannelGateService.sendPost(ChannelPostMTEnum.VIDEO_UPLOAD,
                request.getTenantId(), request.getChannelId(), uploadDTO, request.getStoreId());
        ChannelResponseDTO httpResp = httpRespMap.get(request.getStoreId());
        String data = httpResp.getData();

        ChannelResponseDTO.ChannelResponseError error = httpResp.getError();
        if (NG.equals(data)){
            response.setCode(ResultCode.FAIL.getCode());
            response.setMsg(Optional.ofNullable(error).map(ChannelResponseDTO.ChannelResponseError::getMsg).orElse(
                    "上传视频失败"));
            return response;
        }
        List<VideoUploadResult> videoUploadResults = JSON.parseArray(data, VideoUploadResult.class);
        if (CollectionUtils.isEmpty(videoUploadResults)){
            response.setCode(ResultCode.FAIL.getCode());
            response.setMsg(StringUtils.isNotBlank(httpResp.getMsg()) ? httpResp.getMsg() : "上传视频失败");
            return response;
        }
        VideoUploadResult next = videoUploadResults.iterator().next();
        response.setCode(ResultCode.SUCCESS.getCode());
        response.setVideoUploadDTO(next.buildVideoUploadDTO(request.getStoreId()));
        return response;
    }

    @Data
    public static class VideoUploadResult{
        private String app_poi_code;
        private Long video_id;
        private String video_url_mp4;
        private Integer video_state;

        private VideoUploadDTO buildVideoUploadDTO(Long storeId){
            VideoUploadDTO dto = new VideoUploadDTO();
            dto.setStoreId(storeId);
            dto.setVideoId(video_id);
            dto.setVideoUrl(video_url_mp4);
            dto.setVideoStatus(video_state);
            return dto;
        }
    }


    @Override
    public VideoBindResponse updateVideoBindRelation(VideoBindRequest request) {
        VideoBindResponse response = new VideoBindResponse();

        ChannelVideoBindDTO videoBindDTO = mtConverterService.updateVideoBind(request);
        HashMap<Integer,ChannelResponseDTO> httpRespMap= mtBrandChannelGateService.sendPost(ChannelPostMTEnum.VIDEO_BIND,
                request.getTenantId(), request.getChannelId(), videoBindDTO, request.getStoreId());

        ChannelResponseDTO httpResp =  httpRespMap.get(request.getStoreId());
        if (Objects.nonNull(httpResp) && httpResp.isSuccess()){
            response.setCode(ResultCode.SUCCESS.getCode());
            return response;
        }

        String errMsg = "更新视频关联关系异常";
        if (Objects.nonNull(httpResp) && Objects.nonNull(httpResp.getError())) {
            errMsg = httpResp.getError().getMsg();
        }
        response.setCode(ResultCode.FAIL.getCode());
        response.setMsg(errMsg);

        return response;
    }

    @Override
    public VideoDeleteResponse deleteVideo(VideoDeleteRequest request) {
        VideoDeleteResponse response = new VideoDeleteResponse();
        ChannelVideoDeleteDTO videoDeleteDTO = mtConverterService.deleteVideo(request);
        ChannelResponseDTO httpResp = mtBrandChannelGateService.sendPostReturnDto(ChannelPostMTEnum.VIDEO_DELETE,
                request.getTenantId(), request.getChannelId(), videoDeleteDTO, request.getStoreId());

        if (Objects.nonNull(httpResp) && httpResp.isSuccess()){
            response.setCode(ResultCode.SUCCESS.getCode());
            return response;
        }

        String errMsg = "删除视频异常";
        if (Objects.nonNull(httpResp) && Objects.nonNull(httpResp.getError())) {
            errMsg = httpResp.getError().getMsg();
        }
        response.setCode(ResultCode.FAIL.getCode());
        response.setMsg(errMsg);

        return response;
    }

    @Override
    public BatchVideoUploadResponse batchUploadVideo(BatchVideoUploadRequest request) {
        BatchVideoUploadResponse response = new BatchVideoUploadResponse();
        response.setCode(ResultCode.SUCCESS.getCode());

        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setTenantId(request.getTenantId());
        baseRequest.setChannelId(request.getChannelId());
        baseRequest.setStoreIdList(request.getStoreIdList());
        ChannelVideoUploadDTO uploadDTO = mtConverterService.uploadVideo(request);
        Map<Long, String> storeMap = queryOnlinePoiIdMap(request.getTenantId(), request.getChannelId(), request.getStoreIdList());
        uploadDTO.setApp_poi_codes(Joiner.on(",").join(storeMap.values()));

        ChannelResponseDTO httpResp  = mtBrandChannelGateService.sendPostReturnDto(ChannelPostMTEnum.VIDEO_UPLOAD, baseRequest, uploadDTO);
        String data = httpResp.getData();

        if (NG.equals(data)){
            ChannelResponseDTO.ChannelResponseError error = httpResp.getError();
            response.setCode(ResultCode.FAIL.getCode());
            response.setMsg(Optional.ofNullable(error).map(ChannelResponseDTO.ChannelResponseError::getMsg).orElse("批量上传视频失败"));
            return response;
        }

        List<VideoUploadResult> videoUploadResults = JSON.parseArray(data, VideoUploadResult.class);
        if (CollectionUtils.isNotEmpty(videoUploadResults)) {
            if (request.getStoreIdList().size() == 1) {
                VideoUploadDTO videoUploadDTO = videoUploadResults.get(0).buildVideoUploadDTO(request.getStoreIdList().get(0));
                response.setVideoUploadDTOList(Lists.newArrayList(videoUploadDTO));
            } else {
                List<String> channelPoiCodeList = Fun.map(videoUploadResults, VideoUploadResult::getApp_poi_code);
                List<ChannelStoreDO> channelStoreDOList = channelStoreMapper.selectPoiByChannelPoiCode(request.getTenantId(),
                       request.getChannelId(), channelPoiCodeList);
                Map<String, Long> poiIdMap = Fun.toMap(channelStoreDOList, ChannelStoreDO::getChannelOnlinePoiCode, ChannelStoreDO::getStoreId);
                response.setVideoUploadDTOList(videoUploadResults.stream()
                        .map(item -> item.buildVideoUploadDTO(poiIdMap.get(item.getApp_poi_code())))
                        .collect(Collectors.toList()));
            }
        }

        // 此处有可能存在部分成功情况
        if (CollectionUtils.isEmpty(videoUploadResults) || StringUtils.isNotBlank(httpResp.getMsg())) {
            response.setCode(ResultCode.FAIL.getCode());
            response.setMsg(StringUtils.isNotBlank(httpResp.getMsg()) ? httpResp.getMsg() : "上传视频失败");
        }
        return response;
    }

    private Map<Long, String> queryOnlinePoiIdMap(long tenantId, int channelId, List<Long> storeIds) {
        // 获取渠道门店编码
        Map<String, ChannelStoreDO> channelStoreDOMap = copChannelStoreService.getChannelPoiCode(tenantId, channelId, storeIds);
        if (MapUtils.isEmpty(channelStoreDOMap)) {
            throw new StoreIdNotExistException("未查询到配置门店");
        }
        return Fun.toMapQuietly(Lists.newArrayList(channelStoreDOMap.values()), ChannelStoreDO::getStoreId, ChannelStoreDO::getChannelOnlinePoiCode);

    }
}
