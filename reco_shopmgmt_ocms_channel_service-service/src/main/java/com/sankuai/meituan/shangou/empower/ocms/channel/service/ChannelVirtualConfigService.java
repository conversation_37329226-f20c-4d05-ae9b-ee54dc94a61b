package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.meituan.linz.thrift.response.ThriftResponse;
import com.meituan.linz.thrift.response.ThriftResponseBuilder;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ChannelProductUpdateRuleDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.ChannelProductUpdateRuleRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetQualificationRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetQualificationResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuCategoryPropertyResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.VirtualTokenGetRequest;

/**
 * @Title: ChannelVirtualConfigService
 * @Description: 渠道网关虚拟配置服务（各渠道父接口）
 * @Author: wuyongjiang
 * @Date: 2023/4/26 11:13
 */
public interface ChannelVirtualConfigService {
    /**
     * 通过虚拟配置信息推荐渠道类目
     * @param request
     * @return
     */
    default RecommendCategoryResponse recommendCategoryByVirtualConfig(RecommendCategoryRequest request) {
        return new RecommendCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    /**
     * 通过虚拟配置信息创建渠道门店商品
     * @param request
     * @return
     */
    default ResultSpuData createStoreProductsByVirtualConfig(SpuInfoRequest request) {
        return ResultGenerator.genResultSpuData(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }

    /**
     * 通过虚拟配置信息删除渠道门店商品
     * @param request
     * @return
     */
    default ResultSpuData deleteStoreProductsByVirtualConfig(SpuInfoDeleteRequest request) {
        return ResultGenerator.genResultSpuData(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }

    /**
     * 通过虚拟配置信息推荐商品售后服务
     * @param request
     * @return
     */
    default ThriftResponse<ChannelProductUpdateRuleDTO> queryChannelProductUpdateRuleByVirtualConfig(ChannelProductUpdateRuleRequest request) {
        return ThriftResponseBuilder.buildFailResp(ResultCode.UNSUPPORTED_OPERATE_TYPE.getCode(), ResultCode.UNSUPPORTED_OPERATE_TYPE.getMsg());
    }

    /**
     * 通过虚拟配置信息查询渠道资质
     * @param request
     * @return
     */
    default GetQualificationResponse queryChannelQualificationByVirtualConfig(GetQualificationRequest request) {
        return new GetQualificationResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    /**
     * 通过虚拟配置信息查询渠道类目
     * @param request
     * @return
     */
    default GetCategoryResponse batchGetCategoryByVirtualConfig(CatRequest request) {
        return new GetCategoryResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    /**
     * 通过虚拟配置信息查询渠道类目属性
     * @param request
     * @return
     */
    default CategoryAttrResponse getCategoryAttrByVirtualConfig(CategoryAttrRequest request) {
        return new CategoryAttrResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    /**
     * 通过虚拟配置信息获取token
     *
     * @param request
     * @return
     */
    default ResultStatus fetchVirtualTokenByVirtualConfig(VirtualTokenGetRequest request){
        return new ResultStatus().setCode(ResultCode.UNKNOWN_ERROR.getCode()).setMsg(ResultCode.UNKNOWN_ERROR.getMsg());
    }

    /**
     * 通过虚拟配置信息创建渠道门店商品-适用于主档 一键使用（饿了么）渠道推荐信息
     * @param request
     * @return
     */
    default ResultSpuData createStoreProductsForRecommendCategoryProperty(SpuInfoRequest request) {
        return ResultGenerator.genResultSpuData(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }

    /**
     * 通过虚拟配置信息获取渠道门店商品类目属性-适用于主档 一键使用（饿了么）渠道推荐信息
     * @param request
     * @return
     */
    default GetSpuCategoryPropertyResponse getSpuCategoryRecommendPropertyInfo(GetSpuInfoRequest request) {
        return new GetSpuCategoryPropertyResponse().setStatus(ResultGenerator.genResult(ResultCode.UNSUPPORTED_OPERATE_TYPE));
    }

    /**
     * 通过虚拟配置信息删除渠道门店商品-适用于主档 一键使用（饿了么）渠道推荐信息
     * @param request
     * @return
     */
    default ResultSpuData deleteStoreProductsForRecommendCategoryProperty(SpuInfoDeleteRequest request) {
        return ResultGenerator.genResultSpuData(ResultCode.UNSUPPORTED_OPERATE_TYPE);
    }
}
