package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.txd.TxdSkuInSpuStockDetail;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostTxdEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelStockService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.TxdConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelPoiThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ListUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.response.AlibabaWdkStockPublishResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 淘鲜达渠道商品库存内部服务接口
 *
 * <AUTHOR>
 * @date 2024/04/19 17:22
 */
@Service("txdChannelStockService")
public class TxdChannelStockServiceImpl implements ChannelStockService {

    public static final int STOCK_UPDATE_MAX_COUNT = 50;

    @Resource
    private CommonLogger logger;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private TxdBaseService txdBaseService;

    @Resource
    private ChannelPoiThriftServiceProxy channelPoiThriftServiceProxy;

    @Override
    public ResultSpuData updateStockBySpu(SpuStockRequest request) {

        //logger.info("txdChannelStockServiceImpl.updateStockBySpu,开始 request:{}", JacksonUtils.toJson(request));

        if (CollectionUtils.isEmpty(request.getParamList())) {
            return ResultGenerator.genResultSpuData(ResultCode.INVALID_PARAM, "同步参数列表为空");
        }

        boolean isRetry = request.isRetryFlag();
        BaseRequestSimple baseInfo = request.getBaseInfo();
        ResultSpuData resultData = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);

        /* 门店分组 */
        Map<Long, List<SpuStockDTO>> storeSpuMap = request.getParamList().stream().collect(Collectors.groupingBy(SpuStockDTO::getStoreId));
        //logger.info("Map<Long, List<SpuStockDTO>> storeSpuMap:{}", JacksonUtils.toJson(storeSpuMap));

        try {
            storeSpuMap.forEach((storeId, spuStockDTOList) -> {
                ChannelStoreDO channelStoreDO = copChannelStoreService.getChannelStore(baseInfo.getTenantId(), baseInfo.getChannelId(), storeId);
                String tenantAppId = channelPoiThriftServiceProxy.getTenantAppId(baseInfo.getTenantId(), storeId, baseInfo.getChannelId());
                if (null==channelStoreDO || StringUtils.isBlank(tenantAppId)){
                    throw new RuntimeException("查询渠道门店或者淘鲜达商家编码失败");
                }

                List<TxdSkuInSpuStockDetail> skuStockDetailList = new ArrayList<>();
                spuStockDTOList.forEach(spu -> skuStockDetailList.addAll(
                        spu.getSkuStockInfo().stream()
                                .map(it -> TxdSkuInSpuStockDetail.toSkuInSpuStockDetail(it, spu,channelStoreDO))
                                .collect(Collectors.toList())));

                ListUtils.listPartition(skuStockDetailList, STOCK_UPDATE_MAX_COUNT).forEach(data -> batchSyncStockCommon(baseInfo, storeId, channelStoreDO, data, resultData,tenantAppId,isRetry));
            });
        } catch (Exception e) {
            logger.warn("更新淘鲜达渠道库存异常, request {}.", request, e);
            return ResultGenerator.genResultSpuData(ResultCode.FAIL, "txdChannelStockServiceImpl.updateStockBySpu 批量修改商品库存失败");
        }
        logger.info("txdChannelStockServiceImpl.updateStockBySpu,结束 resultData:{}", JacksonUtils.toJson(resultData));
        return resultData;
    }

    private void batchSyncStockCommon(BaseRequestSimple baseInfo, Long storeId, ChannelStoreDO channelStoreDO, List<TxdSkuInSpuStockDetail> txdSkuInSpuStockDetailList,
                                      ResultSpuData resultData,String tenantAppId, boolean isRetry) {
        boolean retrySwitch = MccConfigUtil.txdChannelStockRetryBySkuSwitch();
        BaseRequest baseRequest=new BaseRequest();
        baseRequest.setTenantId(baseInfo.getTenantId());
        baseRequest.setChannelId(baseInfo.getChannelId());
        baseRequest.setStoreIdList(Lists.newArrayList(storeId));
        baseRequest.setAppId(baseInfo.getAppId());
        baseRequest.setErpTenant(baseInfo.isErpTenant());

        if (retrySwitch && isRetry) {
            logger.info("txd batchSyncStockCommon retry by single sku.");
            batchSyncStockForRetry(baseRequest,channelStoreDO, txdSkuInSpuStockDetailList, resultData,tenantAppId);
        } else {
            logger.info("txd batchSyncStockCommon run by batch sku.");
            batchSyncStock(baseRequest,channelStoreDO, txdSkuInSpuStockDetailList, resultData,tenantAppId);
        }
    }

    private void batchSyncStockForRetry(BaseRequest baseInfo, ChannelStoreDO channelStoreDO, List<TxdSkuInSpuStockDetail> txdSkuInSpuStockDetailList, ResultSpuData resultData,String tenantAppId) {
        ResultSpuData temporaryResult = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(txdSkuInSpuStockDetailList)){
            return;
        }
        try {
            // 重试任务优化：批量同步时，只要有一个sku失败会导致整体失败，导致一批重试任务永远无法重试成功
            // 所以这里启用优化：重试推送，单个sku推送一次，并且累计到最终返回结果中.
            int interval = MccConfigUtil.getTXDStockRetryInterval();
            for (TxdSkuInSpuStockDetail detail : txdSkuInSpuStockDetailList) {
                if (interval > 0) {
                    Thread.sleep(interval);
                }
                List<TxdSkuInSpuStockDetail> tempList = Lists.newArrayList(detail);
                try {
                    TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SKU_STOCK_UPDATE, baseInfo, TxdConvertUtil.buildStockRequest(tempList, channelStoreDO.getChannelPoiCode(), tenantAppId));
                    ResultSpuData temp = TxdConvertUtil.buildStockSpuResult(tempList,commonResponse);
                    temporaryResult.getSucData().addAll(temp.getSucData());
                    temporaryResult.getErrorData().addAll(temp.getErrorData());
                } catch (Exception e) {
                    logger.warn("淘鲜达发送请求同步渠道库存异常:{}", e);
                    installFailResultSpuData(temporaryResult, tempList,"调用渠道同步库存异常");
                }
            }
            resultData.getSucData().addAll(temporaryResult.getSucData());
            resultData.getErrorData().addAll(temporaryResult.getErrorData());
        } catch (Exception e) {
            logger.error("txdChannelStockServiceImpl retry 同步渠道库存异常, baseInfo:{}, txdSkuInSpuStockDetail:{}", JacksonUtils.toJson(baseInfo), JacksonUtils.toJson(txdSkuInSpuStockDetailList), e);
            installFailResultSpuData(resultData, txdSkuInSpuStockDetailList,"调用渠道同步库存异常");

        }
    }

    private void batchSyncStock(BaseRequest baseInfo, ChannelStoreDO channelStoreDO, List<TxdSkuInSpuStockDetail> data, ResultSpuData resultData,String tenantAppId) {
        ResultSpuData temporaryResult = ResultGenerator.genResultSpuData(ResultCode.SUCCESS);
        if (CollectionUtils.isEmpty(data)){
            return;
        }
        try {
            TaobaoResponse commonResponse = txdBaseService.sendPost(ChannelPostTxdEnum.SKU_STOCK_UPDATE, baseInfo, TxdConvertUtil.buildStockRequest(data,channelStoreDO.getChannelPoiCode(),tenantAppId));
            temporaryResult = TxdConvertUtil.buildStockSpuResult(data,commonResponse);
        }catch (Exception e) {
            logger.warn("淘鲜达发送请求同步渠道库存异常, e:{}", e);
            installFailResultSpuData(temporaryResult, data,"调用渠道同步库存异常");
        }
        resultData.getSucData().addAll(temporaryResult.getSucData());
        resultData.getErrorData().addAll(temporaryResult.getErrorData());
    }

    private void installFailResultSpuData(ResultSpuData resultData, List<TxdSkuInSpuStockDetail> txdSkuInSpuStockDetail, String errMsg) {
        if (CollectionUtils.isEmpty(txdSkuInSpuStockDetail)) {
            return;
        }
        txdSkuInSpuStockDetail.forEach(data -> {
            SpuKey spuKey = new SpuKey().setCustomSpuId(data.getCustomSpuId());
            List<SkuKey> skuKeys = Lists.newArrayList();
            skuKeys.add(new SkuKey().setCustomSkuId(data.getCustomSkuId()));
            spuKey.setSkus(skuKeys);
            resultData.getErrorData().add(new ResultErrorSpu().setStoreId(data.getStoreId()).setSpuInfo(spuKey)
                    .setErrorCode(ResultCodeEnum.FAIL.getValue()).setErrorMsg(errMsg));
        });
    }

    @Override
    public ResultData updateStockMultiChannel(SkuStockMultiChannelRequest request) {
        return null;
    }

    @Override
    public BatchGetStockInfoResponse batchGetStockInfo(BatchGetStockInfoRequest request) {
        return null;
    }
}
