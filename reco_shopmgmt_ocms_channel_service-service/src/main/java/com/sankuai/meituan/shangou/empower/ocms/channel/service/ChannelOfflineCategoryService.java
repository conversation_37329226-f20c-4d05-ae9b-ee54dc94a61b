package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChannelOfflineCategoryUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.ChildChannelOfflineCategoryQueryRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2024/4/19 15:53
 **/
public interface ChannelOfflineCategoryService {

    /**
     * 创建渠道线下分类
     *
     * @param request
     * @return
     */
    ChannelOfflineCategoryCreateResponse createChannelOfflineCategory(ChannelOfflineCategoryCreateRequest request);

    /**
     * 更新渠道线下分类
     *
     * @param request
     * @return
     */
    ChannelOfflineCategoryResponse updateChannelOfflineCategory(ChannelOfflineCategoryUpdateRequest request);

    /**
     * 删除渠道线下分类
     *
     * @param request
     * @return
     */
    ChannelOfflineCategoryResponse deleteChannelOfflineCategory(ChannelOfflineCategoryDeleteRequest request);

    /**
     * 查询所有渠道线下分类
     *
     * @param request
     * @return
     */
    ChannelOfflineCategoryQueryResponse queryAllChannelOfflineCategory(ChannelOfflineCategoryQueryRequest request);

    /**
     * 查询子渠道线下分类
     *
     * @param request
     * @return
     */
    ChannelOfflineCategoryQueryResponse queryChildChannelOfflineCategory(ChildChannelOfflineCategoryQueryRequest request);
}
