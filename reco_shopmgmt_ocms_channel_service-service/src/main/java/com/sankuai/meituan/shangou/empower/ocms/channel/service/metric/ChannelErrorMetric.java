package com.sankuai.meituan.shangou.empower.ocms.channel.service.metric;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.meituan.linz.boot.util.MetricReportUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.config.RaptorReportConfig;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.MetricConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostInter;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 渠道接口异常监控
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@SuppressWarnings({"deprecation", "rawtypes"})
public class ChannelErrorMetric {

    /**
     * 检查渠道请求异常并上报监控。
     */
    public <R> void report(ChannelTypeEnum channel, ChannelPostInter api, BaseRequest request, R response, Function<R, String> getErrorMessage) {
        try {
            String channelName = channel.getDesc();
            String apiName = api instanceof Enum ? ((Enum<?>) api).name() : api.getUrlShortName();
            Long tenantId = Optional.ofNullable(request)
                    .map(BaseRequest::getTenantId)
                    .orElse(null);
            reportCall(channelName, apiName, tenantId);
            if (response == null) {
                reportError(channelName, apiName, tenantId, MetricConstant.ChannelErrorMetric.ERR_REQUEST);
                return;
            }
            String errorType = parseChannelErrorType(getErrorMessage.apply(response));
            if (StringUtils.isNotEmpty(errorType)) {
                reportError(channelName, apiName, tenantId, errorType);
            }
        }
        catch (Exception e) {
            log.warn("channelErrorMetric exception", e);
        }
    }

    /**
     * 渠道异常上报监控。
     */
    public void reportError(String channel, String api, Long tenantId, String errorType) {
        try {
            Map<String, String> tags = new HashMap<>();
            tags.put(MetricConstant.ChannelErrorMetric.TAG_CHANNEL, channel);
            tags.put(MetricConstant.ChannelErrorMetric.TAG_API, api);
            tags.put(MetricConstant.ChannelErrorMetric.TAG_ERROR, errorType);
            Optional.ofNullable(tenantId)
                    .map(String::valueOf)
                    .ifPresent(val -> tags.put(MetricConstant.ChannelErrorMetric.TAG_TENANT_ID, val));
            MetricReportUtils.reportMetricForCount(RaptorReportConfig.PRODUCT_MONITOR_URL, MetricConstant.ChannelErrorMetric.ERROR_KEY, tags, 1);
        }
        catch (Exception e) {
            log.warn("channelErrorMetric exception", e);
        }
    }

    public void reportCall(String channel, String api, Long tenantId) {
        try {
            Map<String, String> tags = new HashMap<>();
            tags.put(MetricConstant.ChannelErrorMetric.TAG_CHANNEL, channel);
            tags.put(MetricConstant.ChannelErrorMetric.TAG_API, api);
            Optional.ofNullable(tenantId)
                    .map(String::valueOf)
                    .ifPresent(val -> tags.put(MetricConstant.ChannelErrorMetric.TAG_TENANT_ID, val));
            MetricReportUtils.reportMetricForCount(RaptorReportConfig.PRODUCT_MONITOR_URL, MetricConstant.ChannelErrorMetric.ALL_KEY, tags, 1);
        }
        catch (Exception e) {
            log.warn("channelErrorMetric exception", e);
        }
    }

    private String parseChannelErrorType(String errorMsg) {
        try {
            if (StringUtils.isEmpty(errorMsg)) {
                return null;
            }
            Map<String, List> errorMapping = MccConfigUtil.getChannelApiErrorMapping();
            return errorMapping.entrySet().stream()
                    .filter(entry -> {
                        List<?> keywords = entry.getValue();
                        return CollectionUtils.isNotEmpty(keywords)
                                && keywords.stream().anyMatch(keyword -> StringUtils.containsIgnoreCase(errorMsg, (String) keyword));
                    })
                    .map(Map.Entry::getKey)
                    .findFirst()
                    .orElse(null);
        }
        catch (Exception e) {
            log.warn("channelErrorMetric exception: msg={}", errorMsg, e);
            return null;
        }
    }

}
