package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelSkuCreateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.StockVendibility;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ChannelPoiHotlineUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.OrderStatusConverter;
import com.taobao.api.response.AlibabaWdkShopQueryResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;

/**
 * @program: reco_shopmgmt_ocms_channel_service_2
 * @description: 淘鲜达实体转换接口
 * @author: jinyi
 * @create: 2024-05-21 16:40
 **/
@Mapper(componentModel = "spring", imports = {ConverterUtils.class, ChannelSkuCreateDTO.class, StockVendibility.class, DateUtils.class, MoneyUtils.class, JddjOrderConvertUtil.class, JSON.class, OrderStatusConverter.class, BigDecimal.class, ChannelPoiHotlineUtil.class})
public interface TxdConverterService {

    @Mappings({
            @Mapping(target = "appPoiCode", source = "ouCode"),
            @Mapping(target = "name", source = "shopName"),
            @Mapping(target = "province", source = "provName"),
            @Mapping(target = "city", source = "cityName"),
            @Mapping(target = "county", source = "areaName"),
            @Mapping(target = "address", source = "address"),
            @Mapping(target = "channelPoiCode",source = "ouCode"),
            @Mapping(target = "openLevel", expression = "java(ConverterUtils.getTxdBizStatus(shopDo.getStatus()))"),
    })
    PoiInfo poiInfoMapping(AlibabaWdkShopQueryResponse.ShopDo shopDo);
}
