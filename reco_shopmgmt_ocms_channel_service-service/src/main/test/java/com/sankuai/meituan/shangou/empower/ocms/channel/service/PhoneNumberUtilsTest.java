package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.utils.PhoneNumberUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @createTime 2019/9/29
 * @description
 */
public class PhoneNumberUtilsTest {
    @Test
    public void testMobilePhonePattern() {
        boolean mobilePhoneNumber = Pattern.matches("\\d{11}", "18503085688");
        org.junit.Assert.assertTrue(mobilePhoneNumber);

        boolean mobilePhoneNumber2 = Pattern.matches("\\d{11}", "18689114587_1592");
        org.junit.Assert.assertFalse(mobilePhoneNumber2);

        boolean mobilePhoneNumber3 = Pattern.matches("\\d{11}", "1850****688");
        org.junit.Assert.assertFalse(mobilePhoneNumber3);
    }

    @Test
    public void patternStarMatchTest() {
        boolean starMatch = Pattern.matches("\\*{4}", "****");
        org.junit.Assert.assertTrue(starMatch);
    }

    @Test
    public void spacePatternTest() {
        boolean spaceMatch = Pattern.matches("\\w{1} \\w{1}", "a b");
        org.junit.Assert.assertTrue(spaceMatch);
    }

    @Test
    public void anyCharacterTest() {
        boolean spaceMatch = Pattern.matches(".+ .*", "a b");
        org.junit.Assert.assertTrue(spaceMatch);
    }

    @Test
    public void testCautionPhoneRemarkPattern() {
        String caution= "【如遇缺货】： 缺货时电话与我沟通 收餐人隐私号 13049479512_4121，手机号 158****9248 到店自取";
        String privacyPhoneRemarkPattern = ".+手机号 \\d{3}\\*{4}\\d{4}.*";
        boolean cautionHasPhone = Pattern.matches(privacyPhoneRemarkPattern, caution);
        org.junit.Assert.assertTrue(cautionHasPhone);
    }

    @Test
    public void testCautionPhoneRemarkPatternWithoutEnd() {
        String caution= "【如遇缺货】： 缺货时电话与我沟通 收餐人隐私号 13049479512_4121，手机号 158****9248";
        String privacyPhoneRemarkPattern = ".+手机号 \\d{3}\\*{4}\\d{4}.*";
        boolean cautionHasPhone = Pattern.matches(privacyPhoneRemarkPattern, caution);
        org.junit.Assert.assertTrue(cautionHasPhone);
    }

    @Test
    public void testCautionHasNoPhone() {
        String caution= "【如遇缺货】： 缺货时电话与我沟通 收餐人隐私号 13049479512_4121";
        String privacyPhoneRemarkPattern = ".+手机号 \\d{3}\\*{4}\\d{4}.*";
        boolean cautionHasPhone = Pattern.matches(privacyPhoneRemarkPattern, caution);
        org.junit.Assert.assertFalse(cautionHasPhone);
    }
    @Test
    public void testCautionGetPhone() {
        String caution= "【如遇缺货】： 缺货时电话与我沟通 收餐人隐私号 13049479512_4121，手机号 158****9248";
        int index = caution.indexOf("手机号 ");
        String phone = caution.substring(index+4, index+15);
        org.junit.Assert.assertEquals(phone, "158****9248");
    }

    @Test
    public void testCautionGetPhone2() {
        String caution= " 【如遇缺货】： 缺货时电话与我沟通 收餐人隐私号 18689114066_3103，手机号 134****8213 顾客未对餐具数量做选择";
        int index = caution.indexOf("手机号 ");
        String phone = caution.substring(index+4, index+15);
        org.junit.Assert.assertEquals(phone, "134****8213");
    }

    @Test
    public void testCautionGetPhone3() {
        String caution= " 【如遇缺货】： 缺货时电话与我沟通 收餐人隐私号 18689114066_3103，手机号 134****8213 顾客未对餐具数量做选择";

        String privacyPhoneRemarkPattern = ".+手机号 \\d{3}\\*{4}\\d{4}.*";
        if (Pattern.matches(privacyPhoneRemarkPattern, caution)) {
            int index = caution.indexOf("手机号 ");
            String phone = caution.substring(index + 4, index + 15);
            Assert.assertNotNull(phone);
            org.junit.Assert.assertEquals(phone, "134****8213");
            return;
        }
        Assert.fail();
    }


    @Test
    public void validMobileNumberTest() {
        String realPhone = "15812349248";
        org.junit.Assert.assertTrue(PhoneNumberUtils.isValidElevenNumberMobileNumber(realPhone));
    }

    @Test
    public void notValidMobileNumberTest() {
        String realPhone = "158****9248";
        org.junit.Assert.assertFalse(PhoneNumberUtils.isValidElevenNumberMobileNumber(realPhone));
    }

    @Test
    public void privacyPhoneTest() {
        String realPhone = "15812349248";
        org.junit.Assert.assertEquals(PhoneNumberUtils.transferToPrivacyPhone(realPhone), "158****9248");
    }

    @Test
    public void validMobileNumberExceptionTest() {
        try {
            boolean validMobile = PhoneNumberUtils.isValidElevenNumberMobileNumber(null);
            org.junit.Assert.assertFalse(validMobile);
        } catch (Exception ex) {
            Assert.assertTrue(ex instanceof IllegalArgumentException);
        }
    }

}
