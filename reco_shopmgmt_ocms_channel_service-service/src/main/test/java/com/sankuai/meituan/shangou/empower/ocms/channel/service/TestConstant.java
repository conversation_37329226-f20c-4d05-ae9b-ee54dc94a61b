package com.sankuai.meituan.shangou.empower.ocms.channel.service;

public interface TestConstant {

    /**
     * 测试基本参数枚举
     */
    enum BaseParamEnum {
        MT(1000011, 100, 1054768L),
        ELM(1000011, 200, 1000017L),
        JDDJ(1000093, 300, 1054995L),
        ;

        private long tenantId;
        private int channelId;
        private long storeId;

        BaseParamEnum(long tenantId, int channelId, long storeId){
            this.tenantId = tenantId;
            this.channelId = channelId;
            this.storeId = storeId;
        }

        public long getTenantId() {
            return tenantId;
        }

        public int getChannelId() {
            return channelId;
        }

        public long getStoreId() {
            return storeId;
        }
    }
}
