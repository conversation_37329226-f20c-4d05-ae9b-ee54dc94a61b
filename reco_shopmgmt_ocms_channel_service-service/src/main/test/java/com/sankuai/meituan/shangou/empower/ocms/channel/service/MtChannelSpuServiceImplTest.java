package com.sankuai.meituan.shangou.empower.ocms.channel.service;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpuStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelGateService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mt.MtChannelSpuServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * @author: chenzhiyang
 * @date: 2020/6/3
 * @time: 1:36 PM
 */

public class MtChannelSpuServiceImplTest {

    @InjectMocks
    MtChannelSpuServiceImpl mtChannelSpuService;

    @Mock
    private MtChannelGateService mtChannelGateService;

    @Mock
    private BaseConverterService baseConverterService;

    @Mock
    private CopChannelStoreService copChannelStoreService;

    @Mock
    private MtConverterService mtConverterService;

    @Mock
    private CommonLogger log;

    @Mock
    private ClusterRateLimiter clusterRateLimiter;

    private static final String availableTimes = "{\"monday\":\"09:20-09:30\",\"tuesday\":\"00:01-23:59\",\"wednesday\":\"00:01-23:59\",\"thursday\":\"00:01-23:59\",\"friday\":\"10:00-23:00\",\"saturday\":\"19:00-22:30\",\"sunday\":\"09:00-09:30\"}";

    private static final String properties = "[{\"property_name\":\"色泽\",\"values\":[\"红\",\"黄\",\"绿\"]}]";

    private static final String picture = "http://101.236.62.10/pic/eapi/77e117e102eb524a100eff2d4b778437_1552535168238.png?AWSAccessKeyId=689e9e56b7de41f9951b8e42e2a5ba07&Expires=1565675168&Signature=i3e5k4KBFS8GB5XPep%2FO52XUcAQ%3D";


    private final TestConstant.BaseParamEnum param = TestConstant.BaseParamEnum.MT;

    private BaseRequest baseInfo;

    private BaseRequestSimple baseSimpleInfo;

    private SpuInfoRequest request;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        baseInfo = new BaseRequest();
        baseInfo.setTenantId(param.getTenantId());
        baseInfo.setChannelId(param.getChannelId());
        List<Long> storeIdList = new ArrayList<>();
        storeIdList.add(param.getStoreId());
        baseInfo.setStoreIdList(storeIdList);

        baseSimpleInfo = new BaseRequestSimple();
        baseSimpleInfo.setTenantId(param.getTenantId());
        baseSimpleInfo.setChannelId(param.getChannelId());

        List<SpuInfoDTO> paramList = new ArrayList<>();
        SpuInfoDTO spuInfoDTO = new SpuInfoDTO();
        SkuInSpuInfoDTO skuInSpuInfoDTO1 = new SkuInSpuInfoDTO();
        SkuInSpuInfoDTO skuInSpuInfoDTO2 = new SkuInSpuInfoDTO();
        SkuInSpuInfoDTO skuInSpuInfoDTO3 = new SkuInSpuInfoDTO();
        SkuInSpuInfoDTO skuInSpuInfoDTO4 = new SkuInSpuInfoDTO();
        SkuInSpuInfoDTO skuInSpuInfoDTO5 = new SkuInSpuInfoDTO();

        skuInSpuInfoDTO1.setCustomSkuId("************-1");
        skuInSpuInfoDTO1.setSpec("份");
        skuInSpuInfoDTO1.setPrice(888);
        skuInSpuInfoDTO1.setStock(1);
        skuInSpuInfoDTO1.setUpc("EMPTY_VALUE");
        skuInSpuInfoDTO1.setWeight(10);
        skuInSpuInfoDTO1.setBoxPrice(1.2);
        skuInSpuInfoDTO1.setBoxQuantity(1);
        skuInSpuInfoDTO1.setUnit("份");
        skuInSpuInfoDTO1.setMinPurchaseQuantity(1);
        skuInSpuInfoDTO1.setAvailableTimes(availableTimes);

        skuInSpuInfoDTO2.setCustomSkuId("************-2");
        skuInSpuInfoDTO2.setSpec("份");
        skuInSpuInfoDTO2.setPrice(888);
        skuInSpuInfoDTO2.setStock(1);
        skuInSpuInfoDTO2.setUpc("6926603315799");
        skuInSpuInfoDTO2.setWeight(10);
        skuInSpuInfoDTO2.setBoxPrice(1.2);
        skuInSpuInfoDTO2.setBoxQuantity(1);
        skuInSpuInfoDTO2.setUnit("盒");
        skuInSpuInfoDTO2.setMinPurchaseQuantity(2);
        skuInSpuInfoDTO2.setAvailableTimes(availableTimes);

        skuInSpuInfoDTO3.setCustomSkuId("************-3");
        skuInSpuInfoDTO3.setSpec("份");
        skuInSpuInfoDTO3.setPrice(888);
        skuInSpuInfoDTO3.setStock(1);
        skuInSpuInfoDTO3.setUpc("6926603315799");
        skuInSpuInfoDTO3.setWeight(10);
        skuInSpuInfoDTO3.setBoxPrice(1.2);
        skuInSpuInfoDTO3.setBoxQuantity(1);
        skuInSpuInfoDTO3.setUnit("盒");
        skuInSpuInfoDTO3.setMinPurchaseQuantity(2);
        skuInSpuInfoDTO3.setAvailableTimes(availableTimes);

        skuInSpuInfoDTO4.setCustomSkuId("************-4");
        skuInSpuInfoDTO4.setSpec("份");
        skuInSpuInfoDTO4.setPrice(888);
        skuInSpuInfoDTO4.setStock(1);
        skuInSpuInfoDTO4.setUpc("6926603315799");
        skuInSpuInfoDTO4.setWeight(10);
        skuInSpuInfoDTO4.setBoxPrice(1.2);
        skuInSpuInfoDTO4.setBoxQuantity(1);
        skuInSpuInfoDTO4.setUnit("箱");
        skuInSpuInfoDTO4.setMinPurchaseQuantity(2);
        skuInSpuInfoDTO4.setAvailableTimes(availableTimes);

        skuInSpuInfoDTO5.setCustomSkuId("************-5");
        skuInSpuInfoDTO5.setSpec("份");
        skuInSpuInfoDTO5.setPrice(888);
        skuInSpuInfoDTO5.setStock(1);
        skuInSpuInfoDTO5.setUpc("6920459947395");
        skuInSpuInfoDTO5.setWeight(10);
        skuInSpuInfoDTO5.setLadderBoxPrice(1.2);
        skuInSpuInfoDTO5.setLadderBoxQuantity(1);
        skuInSpuInfoDTO5.setUnit("件");
        skuInSpuInfoDTO5.setMinPurchaseQuantity(5);
        skuInSpuInfoDTO5.setAvailableTimes(availableTimes);

        spuInfoDTO.setCustomSpuId("6921168504039");
        spuInfoDTO.setName("SPU测试1");
        spuInfoDTO.setDescription("SPU测试1");
        spuInfoDTO.setLeafStoreCategoryCode("8122");
        spuInfoDTO.setSequence(-1);
        spuInfoDTO.setStatus(SpuStatusEnum.OFF_LINE.getCode());
        spuInfoDTO.setChannelCategoryId("200003020");
        spuInfoDTO.setBrandName("SPU测试1");
        spuInfoDTO.setProductionArea("SPU测试");
        spuInfoDTO.setIsSpecialty(2);
        spuInfoDTO.setProperties(properties);
        List<String> pictures = Lists.newArrayList();
        pictures.add(picture);
        spuInfoDTO.setPictures(pictures);
        spuInfoDTO.setSkus(Lists.newArrayList(skuInSpuInfoDTO1,skuInSpuInfoDTO2,skuInSpuInfoDTO3,skuInSpuInfoDTO4,skuInSpuInfoDTO5));
        paramList.add(spuInfoDTO);

        request = new SpuInfoRequest();
        request.setBaseInfo(baseInfo)
                .setParamList(paramList);
    }

    @Test
    public void spuCreate() throws Throwable {
        // 构造参数
        SpuInfoRequest mockRequest = mock(SpuInfoRequest.class);
        Map<Long, ChannelResponseDTO> postResult = mock(Map.class);
        when(mtChannelGateService.sendPost(anyObject(),anyObject(),anyObject())).thenReturn(postResult);

        // 测试方法
        ResultSpuData result = mtChannelSpuService.spuCreate(mockRequest);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());

        result = mtChannelSpuService.spuCreate(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());
    }

    @Test
    public void upcCreate() throws Throwable {
        // 构造参数
        SpuInfoRequest mockRequest = mock(SpuInfoRequest.class);
        Map<Long, ChannelResponseDTO> postResult = mock(Map.class);
        when(mtChannelGateService.sendPost(anyObject(),anyObject(),anyObject())).thenReturn(postResult);

        // 测试方法
        ResultSpuData result = mtChannelSpuService.upcCreate(mockRequest);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());

        result = mtChannelSpuService.upcCreate(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());
    }

    @Test
    public void updateBySpuOrUpc() throws Throwable {
        // 构造参数
        SpuInfoRequest mockRequest = mock(SpuInfoRequest.class);
        Map<Long, ChannelResponseDTO> postResult = mock(Map.class);
        when(mtChannelGateService.sendPost(anyObject(),anyObject(),anyObject())).thenReturn(postResult);

        // 测试方法
        ResultSpuData result = mtChannelSpuService.updateBySpuOrUpc(mockRequest);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());

        result = mtChannelSpuService.updateBySpuOrUpc(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());
    }

    @Test
    public void deleteSpu() throws Throwable {
        // 构造参数
        SpuInfoDeleteRequest request = new SpuInfoDeleteRequest();
        request.setBaseInfo(baseSimpleInfo)
                .setParamList(Lists.newArrayList(new SpuInfoDeleteDTO()
                        .setStoreId(param.getStoreId())
                        .setCustomSpuId("6971501250161")));
        Map<Long, ChannelResponseDTO> postResult = mock(Map.class);
        when(mtChannelGateService.sendPost(anyObject(),anyObject(),anyObject())).thenReturn(postResult);

        // 测试方法
        ResultSpuData result = mtChannelSpuService.deleteSpu(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());

        result = mtChannelSpuService.deleteSpu(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());
    }

    @Test
    public void deleteSku() throws Throwable {
        // 构造参数
        SkuInSpuInfoDeleteRequest request = new SkuInSpuInfoDeleteRequest();
        request.setBaseInfo(baseSimpleInfo)
                .setParamList(Lists.newArrayList(new SkuInSpuInfoDeleteDTO()
                        .setStoreId(param.getStoreId())
                        .setCustomSpuId("1249669942257659991")
                        .setCustomSkuId("6900068800236-1")));
        Map<Long, ChannelResponseDTO> postResult = mock(Map.class);
        when(mtChannelGateService.sendPost(anyObject(),anyObject(),anyObject())).thenReturn(postResult);
        // 测试方法
        ResultSpuData result = mtChannelSpuService.deleteSku(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());

        result = mtChannelSpuService.deleteSku(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());

    }

    @Test
    public void updateSpuSellStatus() throws Throwable {
        // 构造参数
        SpuSellStatusInfoRequest request = new SpuSellStatusInfoRequest();
        request.setBaseInfo(baseSimpleInfo);
        List<SpuInfoSellStatusDTO> paramList = new ArrayList<>();
        SpuInfoSellStatusDTO dto = new SpuInfoSellStatusDTO();
        dto.setStoreId(param.getStoreId());
        dto.setCustomSpuIds(Lists.newArrayList("6921168504039","222","333"));
        dto.setSpuStatus(SpuStatusEnum.ON_LINE.getCode());
        paramList.add(dto);
        request.setParamList(paramList);
        // 测试方法
        Map<Long, ChannelResponseDTO> postResult = mock(Map.class);
        when(mtChannelGateService.sendPost(anyObject(),anyObject(),anyObject())).thenReturn(postResult);
        ResultSpuData result = mtChannelSpuService.updateSpuSellStatus(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());

        result = mtChannelSpuService.updateSpuSellStatus(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());
    }

    @Test
    public void updateCustomSpuIdByOriginId() throws Throwable {
        // 测试方法
        UpdateCustomSpuIdByOriginIdRequest request = new UpdateCustomSpuIdByOriginIdRequest();
        request.setBaseInfo(baseSimpleInfo);
        List<UpdateCustomSpuIdByOriginIdDTO> paramList = new ArrayList<>();
        UpdateCustomSpuIdByOriginIdDTO dto = new UpdateCustomSpuIdByOriginIdDTO();
        dto.setStoreId(param.getStoreId());
        dto.setCustomSpuIdOrigin("1249669942438015070");
        dto.setCustomSpuIdCurrent("11981809108186682");
        paramList.add(dto);
        request.setParamList(paramList);

        Map<Long, ChannelResponseDTO> postResult = mock(Map.class);
        when(mtChannelGateService.sendPost(anyObject(),anyObject(),anyObject())).thenReturn(postResult);
        ResultSpuData result = mtChannelSpuService.updateCustomSpuIdByOriginId(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());

        // 测试方法
        result = mtChannelSpuService.updateCustomSpuIdByOriginId(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());
    }

    @Test
    public void updateCustomSpuIdByNameAndSpec() throws Throwable {
        // 构造参数
        UpdateCustomSpuIdByNameAndSpecRequest request = new UpdateCustomSpuIdByNameAndSpecRequest();
        request.setBaseInfo(baseSimpleInfo);
        List<UpdateCustomSpuIdByNameAndSpecDTO> paramList = new ArrayList<>();
        UpdateCustomSpuIdByNameAndSpecDTO dto = new UpdateCustomSpuIdByNameAndSpecDTO();
        dto.setStoreId(param.getStoreId());
        dto.setName("11");
        dto.setCategoryCode("62791");
        dto.setCustomSpuId("11981809108186682");
        paramList.add(dto);
        request.setParamList(paramList);

        SpuInfoRequest mockRequest = mock(SpuInfoRequest.class);
        Map<Long, ChannelResponseDTO> postResult = mock(Map.class);
        when(mtChannelGateService.sendPost(anyObject(),anyObject(),anyObject())).thenReturn(postResult);

        // 测试方法
        ResultSpuData result = mtChannelSpuService.updateCustomSpuIdByNameAndSpec(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());

        result = mtChannelSpuService.updateCustomSpuIdByNameAndSpec(request);
        Assert.assertEquals(ResultCode.SUCCESS.getCode(), result.getStatus().getCode());
    }

    @Test
    public void getSpuInfo() throws Throwable {
        // 构造参数
        GetSpuInfoRequest request = new GetSpuInfoRequest();
        request.setBaseInfo(baseSimpleInfo);
        request.setStoreId(param.getStoreId());
        request.setCustomSpuId("11981809108186683");

        Map getResult = mock(Map.class);
        when(mtChannelGateService.sendGet(anyObject(),anyObject(),anyObject(),anyObject())).thenReturn(getResult);

        // 测试方法
        GetSpuInfoResponse result = mtChannelSpuService.getSpuInfo(request);
        Assert.assertNull(result);
    }

    @Test
    public void batchGetSpuInfo() throws Throwable {
        // 构造参数
        BatchGetSpuInfoRequest request = new BatchGetSpuInfoRequest();
        request.setBaseInfo(baseSimpleInfo);
        request.setStoreId(param.getStoreId());
        request.setPageNum(1);
        request.setPageSize(20);

        Map getResult = mock(Map.class);
        when(mtChannelGateService.sendGet(anyObject(),anyObject(),anyObject(),anyObject())).thenReturn(getResult);

        // 测试方法
        BatchGetSpuInfoResponse result = mtChannelSpuService.batchGetSpuInfo(request);
        Assert.assertEquals(ResultCode.FAIL.getCode(), result.getStatus().getCode());

        Map<String, ChannelStoreDO> channelStoreDOMap = new HashMap<>();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDOMap.put(KeyUtils.genChannelStoreKey(param.getTenantId(), param.getChannelId(),param.getStoreId()), channelStoreDO);
        when(copChannelStoreService.getChannelPoiCode(param.getTenantId(),param.getChannelId(),Lists.newArrayList(param.getStoreId()))).thenReturn(channelStoreDOMap);
        when(clusterRateLimiter.tryAcquire(anyString(),anyString())).thenReturn(true);
        result = mtChannelSpuService.batchGetSpuInfo(request);
        Assert.assertEquals(ResultCode.FAIL_ALLOW_RETRY.getCode(), result.getStatus().getCode());
    }
}
