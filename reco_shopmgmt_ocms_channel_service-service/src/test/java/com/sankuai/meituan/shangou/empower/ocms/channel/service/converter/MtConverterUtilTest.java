package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MccConfigUtil.class})
@PowerMockIgnore({"javax.management.*"})
public class MtConverterUtilTest {
    @Before
    public void setUp() {
        PowerMockito.mockStatic(MccConfigUtil.class);
        PowerMockito.when(MccConfigUtil.getMtSkuUpcMaxLength())
                .thenReturn(10);
    }

    @Test
    public void convertUpcBySkuInSpuInfoDto() {
        SkuInSpuInfoDTO sku = new SkuInSpuInfoDTO();
        String upc = MtConverterUtil.convertUpc(sku);
        assertEquals("no_upc", upc);
        
        sku.setUpc("");
        upc = MtConverterUtil.convertUpc(sku);
        assertEquals("no_upc", upc);
        
        sku.setUpc("abc");
        upc = MtConverterUtil.convertUpc(sku);
        assertEquals("abc", upc);

        sku.setUpc("111111111111");
        upc = MtConverterUtil.convertUpc(sku);
        assertNull(upc);
    }

    @Test
    public void convertUpc() {
        String upc = MtConverterUtil.convertUpc((String)null);
        assertEquals("no_upc", upc);
        upc = MtConverterUtil.convertUpc("");
        assertEquals("no_upc", upc);
        upc = MtConverterUtil.convertUpc("abc");
        assertEquals("abc", upc);
        upc = MtConverterUtil.convertUpc("11111111111");
        assertNull(upc);
    }
}