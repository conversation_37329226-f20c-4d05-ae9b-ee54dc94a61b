package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelPriceQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelStorePriceInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuPriceResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class JddjChannelPriceServiceImplTest {

    @InjectMocks
    private JddjChannelPriceServiceImpl jddjChannelPriceService;

    @Mock
    private CopChannelStoreService copChannelStoreService;

    @Mock
    private JddjConverterService jddjConverterService;

    @Mock
    private JddjChannelGateService jddjChannelGateService;

    @Mock
    private BaseConverterService baseConverterService;

    @Mock
    private CommonLogger log;

    private BatchGetSkuPriceRequest request;
    private BaseRequestSimple baseRequestSimple;
    private BaseRequest baseRequest;
    private ChannelStoreDO channelStore;

    @Before
    public void setUp() {
        // 初始化请求对象
        baseRequestSimple = new BaseRequestSimple();
        baseRequestSimple.setTenantId(1001L);
        baseRequestSimple.setChannelId(2001);

        request = new BatchGetSkuPriceRequest();
        request.setBaseInfo(baseRequestSimple);
        request.setStoreId(3001L);
        request.setChannelSkuIds(Arrays.asList("10001", "10002", "10003"));

        // 初始化基础请求对象
        baseRequest = new BaseRequest();
        baseRequest.setTenantId(1001L);
        baseRequest.setChannelId(2001);
        baseRequest.setStoreIdList(Collections.singletonList(3001L));

        // 初始化门店对象
        channelStore = new ChannelStoreDO();
        channelStore.setChannelPoiCode("JDDJ_POI_001");

        // 设置基础Mock行为
        when(baseConverterService.baseRequest(any(BaseRequestSimple.class))).thenReturn(baseRequest);
    }

    /**
     * 测试正常场景：所有参数正确，接口调用成功，返回正确的价格信息
     */
    @Test
    public void testBatchGetSkuPriceSuccess() throws Throwable {
        // arrange
        when(copChannelStoreService.getChannelStore(anyLong(), anyInt(), anyLong())).thenReturn(channelStore);

        List<ChannelStorePriceInfo> priceInfoList = new ArrayList<>();
        ChannelStorePriceInfo priceInfo1 = new ChannelStorePriceInfo();
        priceInfo1.setSkuId(10001L);
        priceInfo1.setPrice(1000L);

        ChannelStorePriceInfo priceInfo2 = new ChannelStorePriceInfo();
        priceInfo2.setSkuId(10002L);
        priceInfo2.setPrice(2000L);

        priceInfoList.add(priceInfo1);
        priceInfoList.add(priceInfo2);

        ChannelResponseDTO<List<ChannelStorePriceInfo>> responseDTO = new ChannelResponseDTO<>();
        responseDTO.setSuccess(true);
        responseDTO.setDataResponse(new ChannelResponseResult<>());
        responseDTO.getDataResponse().setResultData(priceInfoList);

        when(jddjChannelGateService.sendPostAppDto(
                eq(ChannelPostJDDJEnum.SHOP_PRICE_LIST),
                any(BaseRequest.class),
                any(ChannelPriceQueryDTO.class)
        )).thenReturn(responseDTO);

        // act
        BatchGetSkuPriceResponse response = jddjChannelPriceService.batchGetSkuPrice(request);

        // assert
        assertNotNull(response);
        assertEquals(ResultCode.SUCCESS, response.getStatus().getCode());
        assertNotNull(response.getSkuPriceInfos());
        assertEquals(2, response.getSkuPriceInfos().size());

        // 验证第一个价格信息
        SkuPriceInfoDTO skuPrice1 = response.getSkuPriceInfos().get(0);
        assertEquals("10001", skuPrice1.getChannelSkuId());
        assertEquals(1000L, skuPrice1.getPrice());

        // 验证第二个价格信息
        SkuPriceInfoDTO skuPrice2 = response.getSkuPriceInfos().get(1);
        assertEquals("10002", skuPrice2.getChannelSkuId());
        assertEquals(2000L, skuPrice2.getPrice());

        // 验证方法调用
        verify(copChannelStoreService).getChannelStore(1001L, 2001, 3001L);

        // 验证查询DTO参数
        ArgumentCaptor<ChannelPriceQueryDTO> queryDTOCaptor = ArgumentCaptor.forClass(ChannelPriceQueryDTO.class);
        verify(jddjChannelGateService).sendPostAppDto(
                eq(ChannelPostJDDJEnum.SHOP_PRICE_LIST),
                any(BaseRequest.class),
                queryDTOCaptor.capture()
        );

        ChannelPriceQueryDTO capturedQueryDTO = queryDTOCaptor.getValue();
        assertEquals("JDDJ_POI_001", capturedQueryDTO.getStationNo());
        assertEquals(3, capturedQueryDTO.getSkuIds().size());
        assertTrue(capturedQueryDTO.getSkuIds().contains(10001L));
        assertTrue(capturedQueryDTO.getSkuIds().contains(10002L));
        assertTrue(capturedQueryDTO.getSkuIds().contains(10003L));
    }

    /**
     * 测试异常场景1：请求中的channelSkuIds为空
     */
    @Test
    public void testBatchGetSkuPriceWithEmptySkuIds() throws Throwable {
        // arrange
        request.setChannelSkuIds(Collections.emptyList());

        // act
        BatchGetSkuPriceResponse response = jddjChannelPriceService.batchGetSkuPrice(request);

        // assert
        assertNotNull(response);
        assertEquals(ResultCode.FAIL, response.getStatus().getCode());
        assertEquals("sku列表不能为空", response.getStatus().getMsg());

        // 验证方法不被调用
        verify(copChannelStoreService, never()).getChannelStore(anyLong(), anyInt(), anyLong());
        verify(jddjChannelGateService, never()).sendPostAppDto(
                any(ChannelPostJDDJEnum.class),
                any(BaseRequest.class),
                any(ChannelPriceQueryDTO.class)
        );
    }

    /**
     * 测试异常场景2：查询门店信息失败（channelStore为null）
     */
    @Test
    public void testBatchGetSkuPriceWithNullChannelStore() throws Throwable {
        // arrange
        when(copChannelStoreService.getChannelStore(anyLong(), anyInt(), anyLong())).thenReturn(null);

        // act
        BatchGetSkuPriceResponse response = jddjChannelPriceService.batchGetSkuPrice(request);

        // assert
        assertNotNull(response);
        assertEquals(ResultCode.FAIL, response.getStatus().getCode());
        assertEquals("查询门店信息失败", response.getStatus().getMsg());

        // 验证方法调用
        verify(copChannelStoreService).getChannelStore(1001L, 2001, 3001L);
        verify(jddjChannelGateService, never()).sendPostAppDto(
                any(ChannelPostJDDJEnum.class),
                any(BaseRequest.class),
                any(ChannelPriceQueryDTO.class)
        );
    }

    /**
     * 测试异常场景3：京东到家接口返回为空
     */
    @Test
    public void testBatchGetSkuPriceWithNullApiResponse() throws Throwable {
        // arrange
        when(copChannelStoreService.getChannelStore(anyLong(), anyInt(), anyLong())).thenReturn(channelStore);
        when(jddjChannelGateService.sendPostAppDto(
                eq(ChannelPostJDDJEnum.SHOP_PRICE_LIST),
                any(BaseRequest.class),
                any(ChannelPriceQueryDTO.class)
        )).thenReturn(null);

        // act
        BatchGetSkuPriceResponse response = jddjChannelPriceService.batchGetSkuPrice(request);

        // assert
        assertNotNull(response);
        assertEquals(ResultCode.FAIL, response.getStatus().getCode());
        assertEquals("京东接口返回值为空", response.getStatus().getMsg());

        // 验证方法调用
        verify(copChannelStoreService).getChannelStore(1001L, 2001, 3001L);
        verify(jddjChannelGateService).sendPostAppDto(
                eq(ChannelPostJDDJEnum.SHOP_PRICE_LIST),
                any(BaseRequest.class),
                any(ChannelPriceQueryDTO.class)
        );
    }

    /**
     * 测试异常场景4：京东到家接口返回不成功（success为false）
     */
    @Test
    public void testBatchGetSkuPriceWithFailedApiResponse() throws Throwable {
        // arrange
        when(copChannelStoreService.getChannelStore(anyLong(), anyInt(), anyLong())).thenReturn(channelStore);

        ChannelResponseDTO<List<ChannelStorePriceInfo>> responseDTO = new ChannelResponseDTO<>();
        responseDTO.setSuccess(false);
        responseDTO.setMsg("接口调用失败");

        when(jddjChannelGateService.sendPostAppDto(
                eq(ChannelPostJDDJEnum.SHOP_PRICE_LIST),
                any(BaseRequest.class),
                any(ChannelPriceQueryDTO.class)
        )).thenReturn(responseDTO);

        // act
        BatchGetSkuPriceResponse response = jddjChannelPriceService.batchGetSkuPrice(request);

        // assert
        assertNotNull(response);
        assertEquals(ResultCode.FAIL, response.getStatus().getCode());
        assertEquals("接口调用失败", response.getStatus().getMsg());

        // 验证方法调用
        verify(copChannelStoreService).getChannelStore(1001L, 2001, 3001L);
        verify(jddjChannelGateService).sendPostAppDto(
                eq(ChannelPostJDDJEnum.SHOP_PRICE_LIST),
                any(BaseRequest.class),
                any(ChannelPriceQueryDTO.class)
        );
    }

    /**
     * 测试边界场景：接口返回的价格信息列表为空（但接口调用成功）
     */
    @Test
    public void testBatchGetSkuPriceWithEmptyPriceList() throws Throwable {
        // arrange
        when(copChannelStoreService.getChannelStore(anyLong(), anyInt(), anyLong())).thenReturn(channelStore);

        ChannelResponseDTO<List<ChannelStorePriceInfo>> responseDTO = new ChannelResponseDTO<>();
        responseDTO.setSuccess(true);
        responseDTO.setDataResponse(new ChannelResponseResult<>());
        responseDTO.getDataResponse().setResultData(Collections.emptyList());

        when(jddjChannelGateService.sendPostAppDto(
                eq(ChannelPostJDDJEnum.SHOP_PRICE_LIST),
                any(BaseRequest.class),
                any(ChannelPriceQueryDTO.class)
        )).thenReturn(responseDTO);

        // act
        BatchGetSkuPriceResponse response = jddjChannelPriceService.batchGetSkuPrice(request);

        // assert
        assertNotNull(response);
        assertEquals(ResultCode.SUCCESS, response.getStatus().getCode());
        assertNotNull(response.getSkuPriceInfos());
        assertEquals(0, response.getSkuPriceInfos().size());

        // 验证方法调用
        verify(copChannelStoreService).getChannelStore(1001L, 2001, 3001L);
        verify(jddjChannelGateService).sendPostAppDto(
                eq(ChannelPostJDDJEnum.SHOP_PRICE_LIST),
                any(BaseRequest.class),
                any(ChannelPriceQueryDTO.class)
        );
    }
}