package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.given;

import java.util.Map;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelCreateSubProductResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelGetDistributedProductResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelProductDetailResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DouyinResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConverterServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultSingleSpuData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.SpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuInfoDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SingleSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDeleteDTO;

@RunWith(MockitoJUnitRunner.class)
public class DouyinChannelSpuServiceImplTest {
    @InjectMocks
    DouyinChannelSpuServiceImpl douyinChannelSpuService;

    @Mock
    private DouyinChannelGateService douyinChannelGateService;

    @Spy
    private DouyinConverterService douyinConverterService = new DouyinConverterServiceImpl();

    @Mock
    private CopChannelStoreService copChannelStoreService;

    @Spy
    private BaseConverterService baseConverterService = new BaseConverterServiceImpl();

    // 总部商品渠道sku和spuid
    Long merchantChannelSpuId = 111110000L, merchantChannelSkuId = 222220000L;
    // 店品渠道的sku和spuid
    Long storeChannelSpuId = 111110001L, storeChannelSkuId = 222220001L;

    @Test
    public void testCreateSingleSpuValid() {
        SingleSpuInfoRequest singleSpuInfoRequest = new SingleSpuInfoRequest();
        singleSpuInfoRequest.setBaseInfo(new BaseRequest().setStoreIdList(Lists.newArrayList(1L)));
        singleSpuInfoRequest.setParam(
                new SpuInfoDTO().setMerchantChannelSpuId(merchantChannelSpuId.toString()).setCustomSpuId("1").setSpuId("1").setStatus(1)
                        .setSkus(Lists.newArrayList(new SkuInSpuInfoDTO().setMerchantChannelSkuId(merchantChannelSkuId.toString())
                                .setCustomSkuId("10").setSkuId("10"))));
        ResultSingleSpuData singleSpu = douyinChannelSpuService.createSingleSpu(singleSpuInfoRequest);
        Assert.assertNotNull(singleSpu);
        Assert.assertNotNull(singleSpu.getSucData());
        Assert.assertEquals(singleSpu.getSucData().getSpuInfo().getChannelSpuId(), storeChannelSpuId.toString());
        Assert.assertEquals(singleSpu.getSucData().getSpuInfo().getSkus().get(0).getChannelSkuId(), storeChannelSkuId.toString());
    }

    @Test
    public void testUpdateSpuCreateValid() {
        SingleSpuInfoRequest singleSpuInfoRequest = new SingleSpuInfoRequest();
        singleSpuInfoRequest.setBaseInfo(new BaseRequest().setStoreIdList(Lists.newArrayList(1L)));
        singleSpuInfoRequest.setParam(
                new SpuInfoDTO().setMerchantChannelSpuId(merchantChannelSpuId.toString()).setCustomSpuId("1").setSpuId("1").setStatus(1)
                        .setSkus(Lists.newArrayList(new SkuInSpuInfoDTO().setMerchantChannelSkuId(merchantChannelSkuId.toString())
                                .setCustomSkuId("10").setSkuId("10"))));
        ResultSingleSpuData singleSpu = douyinChannelSpuService.updateSingleSpu(singleSpuInfoRequest);
        Assert.assertNotNull(singleSpu);
        Assert.assertNotNull(singleSpu.getSucData());
        Assert.assertEquals(singleSpu.getSucData().getSpuInfo().getChannelSpuId(), storeChannelSpuId.toString());
        Assert.assertEquals(singleSpu.getSucData().getSpuInfo().getSkus().get(0).getChannelSkuId(), storeChannelSkuId.toString());
    }

    @Test
    public void testDeleteSingleSpuValid() {
        SingleSpuInfoDeleteRequest singleSpuInfoRequest = new SingleSpuInfoDeleteRequest();
        singleSpuInfoRequest.setBaseInfo(new BaseRequestSimple());
        singleSpuInfoRequest.setParam(
                new SpuInfoDeleteDTO().setStoreId(1L).setCustomSpuId("1").setChannelSpuId(storeChannelSpuId.toString())
                        .setSpuKey(new SpuKey().setCustomSpuId("1").setChannelSpuId(storeChannelSpuId.toString()))
        );
        ResultSingleSpuData singleSpu = douyinChannelSpuService.deleteSingleSpu(singleSpuInfoRequest);
        Assert.assertNotNull(singleSpu);
        Assert.assertNotNull(singleSpu.getSucData());
        Assert.assertEquals(singleSpu.getSucData().getSpuInfo().getChannelSpuId(), storeChannelSpuId.toString());
    }

    @Test
    public void testParseDetail(){
        String json = "{\"code\":10000,\"data\":{\"account_template_id\":null,\"after_sale_service\":\"{\\\\\\\"supply_7day_return\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"supply_day_return_code\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"supply_day_return_copywriting\\\\\\\":\\\\\\\"不支持7天无理由退货\\\\\\\",\\\\\\\"supply_day_return_days\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"supply_day_return_selector\\\\\\\":\\\\\\\"7-0\\\\\\\"}\",\"after_sale_service_v2\":{\"is_large_product\":null},\"appoint_delivery_day\":0,\"brand_id\":null,\"car_vin_code\":null,\"category_detail\":{\"first_cid\":38944,\"first_cname\":\"水果蔬菜\",\"fourth_cid\":0,\"fourth_cname\":\"\",\"second_cid\":20188,\"second_cname\":\"水果\",\"third_cid\":22360,\"third_cname\":\"香蕉\"},\"cdf_category\":null,\"check_status\":3,\"create_time\":\"2023-12-28 19:51:24\",\"delay_rule\":{\"config_type\":0,\"config_value\":0,\"enable\":false,\"end_time\":0,\"start_time\":0},\"delivery_delay_day\":2,\"delivery_method\":null,\"description\":\"<p><img src=\\\\\\\"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/PTWpUjYg_m_e4bd7dec13d531a4b02d5fd3794485cf_sx_87616_www800-800\\\\\\\" style=\\\\\\\"max-width:100%;\\\\\\\"/></p>\",\"discount_price\":500,\"draft_status\":3,\"extra\":\"{\\\\\\\"category_detail\\\\\\\":{\\\\\\\"enable\\\\\\\":null,\\\\\\\"first_cid\\\\\\\":38944,\\\\\\\"first_cname\\\\\\\":\\\\\\\"水果蔬菜\\\\\\\",\\\\\\\"fourth_cid\\\\\\\":0,\\\\\\\"fourth_cname\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"is_leaf\\\\\\\":true,\\\\\\\"second_cid\\\\\\\":20188,\\\\\\\"second_cname\\\\\\\":\\\\\\\"水果\\\\\\\",\\\\\\\"third_cid\\\\\\\":22360,\\\\\\\"third_cname\\\\\\\":\\\\\\\"香蕉\\\\\\\"},\\\\\\\"class_quality\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"is_publish\\\\\\\":1,\\\\\\\"quality_opId\\\\\\\":\\\\\\\"7320525248154124555\\\\\\\",\\\\\\\"quality_report\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"spec_seq_info\\\\\\\":{\\\\\\\"child_spec_seq\\\\\\\":[\\\\\\\"规格\\\\\\\"],\\\\\\\"spec_values_seq\\\\\\\":[[\\\\\\\"120g\\\\\\\"]]}}\",\"format_update_time\":\"2024-01-05 16:16:49\",\"freight_id\":*********,\"img\":\"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/PTWpUjYg_m_e4bd7dec13d531a4b02d5fd3794485cf_sx_87616_www800-800\",\"is_create\":0,\"is_sub_product\":false,\"limit_per_buyer\":0,\"logistics_info\":{\"brand_country_id\":0,\"customs_clear_type\":0,\"net_weight_qty\":0,\"origin_country_id\":0,\"source_country_id\":0,\"tax_payer\":0},\"long_pic_url\":null,\"main_image_three_to_four\":\"[]\",\"main_pic_3_4\":[],\"main_product_id\":3658792861115251700,\"market_price\":500,\"material_video_id\":null,\"maximum_per_order\":0,\"micro_app_id\":null,\"minimum_per_order\":1,\"mobile\":\" \",\"name\":\"12281553大珍联营标品价格\",\"name_prefix\":null,\"need_recharge_mode\":false,\"open_user_id\":0,\"out_product_id\":*********,\"outer_product_id\":\"*********\",\"pay_type\":1,\"pic\":[\"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/PTWpUjYg_m_e4bd7dec13d531a4b02d5fd3794485cf_sx_87616_www800-800\"],\"pickup_method\":\"0\",\"presell_config_level\":null,\"presell_delay\":0,\"presell_delivery_type\":null,\"presell_end_time\":null,\"presell_type\":0,\"price_has_tax\":0,\"product_audit_info\":{\"material_audit_reason\":[],\"material_audit_status\":null},\"product_format\":\"{}\",\"product_format_new\":\"{}\",\"product_id\":3658805941287171600,\"product_id_str\":\"3658805941287171432\",\"product_type\":0,\"quality_list\":[],\"recommend_remark\":\"\",\"reduce_type\":2,\"reference_price\":0,\"sale_channel_type\":null,\"sale_limit_id\":146218689649511700,\"sell_channel\":[0],\"sell_num\":0,\"short_product_name\":\"\",\"size_info_template_id\":null,\"spec_id\":1786526336252964,\"spec_pics\":[],\"spec_prices\":[{\"code\":\"*********\",\"customs_report_info\":{\"bar_code\":\"\",\"first_measure_qty\":0,\"first_measure_unit\":\"\",\"g_model\":\"\",\"hs_code\":\"\",\"report_brand_name\":\"\",\"report_name\":\"\",\"second_measure_qty\":0,\"second_measure_unit\":\"\",\"unit\":\"\",\"usage\":\"\"},\"delivery_infos\":[{\"info_type\":\"weight\",\"info_unit\":\"g\",\"info_value\":\"48.0\"}],\"gold_process_charge\":null,\"lock_step_stock_num\":0,\"lock_stock_num\":0,\"out_sku_id\":*********,\"outer_sku_id\":\"*********\",\"presell_delay\":null,\"price\":500,\"prom_step_stock_num\":0,\"prom_stock_num\":0,\"promotion_step_stock_num\":null,\"promotion_stock_num\":null,\"sell_properties\":[{\"perperty_id\":0,\"property_name\":\"规格\",\"remark\":\"\",\"value_id\":0,\"value_name\":\"120g\",\"value_spec_detail_id\":1786526336481291}],\"sku_classification_type\":null,\"sku_id\":1786526225345595,\"sku_status\":true,\"sku_type\":1,\"spec_detail_id1\":1786526336481291,\"spec_detail_id2\":0,\"spec_detail_id3\":0,\"spec_detail_ids\":[1786526336481291],\"step_stock_num\":0,\"stock_num\":0,\"stock_num_map\":{\"*********\":200},\"supplier_id\":\"\",\"tax_exemption_sku_info\":{\"is_suit\":null,\"suit_num\":null,\"volume\":null}}],\"specs\":[{\"id\":1786526336252980,\"is_leaf\":0,\"name\":\"规格\",\"pid\":0,\"property_id\":0,\"spec_id\":1786526336252964,\"values\":[{\"extra\":{},\"id\":1786526336481291,\"is_leaf\":1,\"name\":\"120g\",\"pid\":1786526336252980,\"spec_id\":1786526336252964,\"status\":0,\"value_id\":\"0\"}]}],\"spu_id\":0,\"standard_brand_id\":*********,\"start_sale_type\":0,\"status\":0,\"store_id\":*********,\"update_time\":\"2024-01-05T16:16:49+08:00\",\"weight_unit\":1,\"weight_value\":48,\"white_back_ground_pic_url\":null},\"log_id\":\"2024010516541518020A79196753039613\",\"msg\":\"success\",\"sub_code\":\"\",\"sub_msg\":\"\"}";
        ChannelPostDouyinEnum channelPostInter = ChannelPostDouyinEnum.SPU_DETAIL;
        ChannelResponseDTO channelResponseDTO = JSON.parseObject(json, ChannelResponseDTO.class);
        Assert.assertNotNull(channelResponseDTO);
        if (StringUtils.isNotBlank(channelResponseDTO.getData())) {
            channelResponseDTO.setCoreData(JSON.parseObject(channelResponseDTO.getData(), channelPostInter.getResultClass()));
        }
        Assert.assertNotNull(channelResponseDTO.getCoreData());
        ChannelProductDetailResult result = (ChannelProductDetailResult) channelResponseDTO.getCoreData();
        Assert.assertNotNull(result.getSpec_prices());
        Integer stock =
                Optional.ofNullable(result.getSpec_prices().get(0).getStock_num_map()).map(Map::values).map(stocks -> stocks.stream().reduce(Long::sum).get()).orElse(0L).intValue();
        Assert.assertTrue(stock == 200);
    }


    @Test
    public void testParseGetDistributeResult(){
        String json = "{\"code\":10000,\"data\":{\"page\":0,\"size\":10,\"store_products\":[{\"main_product_id\":\"3658792861115251867\",\"sku_mapping\":[{\"main_sku_id\":\"3392121390409986\",\"store_sku_id\":\"1786526225345595\"}],\"store_info\":{\"store_id\":*********},\"store_product_id\":\"3658805941287171432\"}],\"total\":1},\"log_id\":\"202312291456185531AC54B8C2ED943115\",\"msg\":\"success\",\"sub_code\":\"\",\"sub_msg\":\"\"}";
        ChannelPostDouyinEnum channelPostInter = ChannelPostDouyinEnum.GET_DISTRIBUTED_PRODUCT;
        ChannelResponseDTO channelResponseDTO = JSON.parseObject(json, ChannelResponseDTO.class);
        Assert.assertNotNull(channelResponseDTO);
        if (StringUtils.isNotBlank(channelResponseDTO.getData())) {
            channelResponseDTO.setCoreData(JSON.parseObject(channelResponseDTO.getData(), channelPostInter.getResultClass()));
        }
        Assert.assertNotNull(channelResponseDTO.getCoreData());
        ChannelGetDistributedProductResult result = (ChannelGetDistributedProductResult) channelResponseDTO.getCoreData();
        Assert.assertNotNull(result);
        Assert.assertNotNull(CollectionUtils.isNotEmpty(result.getStore_products()));
    }

    @Before
    public void initMock() {
        // 模拟铺品操作
        ChannelResponseDTO<ChannelCreateSubProductResult> postResult = new ChannelResponseDTO() {{
            setCode(Integer.valueOf(DouyinResultCodeEnum.SUCCESS.getCode().toString()));
            setCoreData(new ChannelCreateSubProductResult());
        }};
        given(douyinChannelGateService.sendPost(argThat((t) -> {
            return t == ChannelPostDouyinEnum.CREATE_SUB_PRODUCT;
        }), any(BaseRequest.class), any())).willReturn(postResult);

        ChannelResponseDTO<ChannelGetDistributedProductResult> distributeResult = new ChannelResponseDTO() {{
            setCode(Integer.valueOf(DouyinResultCodeEnum.SUCCESS.getCode().toString()));
            setCoreData(new ChannelGetDistributedProductResult() {{
                setStore_products(Lists.newArrayList(new StoreProduct() {{
                    setMain_product_id(merchantChannelSpuId);
                    setStore_product_id(storeChannelSpuId);
                    setSku_mapping(Lists.newArrayList(new SkuMapping() {{
                        setMain_sku_id(merchantChannelSkuId);
                        setStore_sku_id(storeChannelSkuId);
                    }}));
                }}));
            }});
        }};
        // 模拟铺品结果
        given(douyinChannelGateService.sendPost(argThat((t) -> {
            return t == ChannelPostDouyinEnum.GET_DISTRIBUTED_PRODUCT;
        }), any(BaseRequest.class), any())).willReturn(distributeResult);

        given(copChannelStoreService.getChannelStore(anyLong(), anyInt(), anyLong())).willReturn(new ChannelStoreDO() {{
            setChannelOnlinePoiCode("99999");
        }});

        // 模拟修改价格
        given(douyinChannelGateService.sendPost(argThat((t) -> {
            return t == ChannelPostDouyinEnum.SKU_EDIT_PRICE;
        }), any(BaseRequest.class), any())).willReturn(new ChannelResponseDTO() {{
            setCode(Integer.valueOf(DouyinResultCodeEnum.SUCCESS.getCode().toString()));
        }});

        // 模拟同步库存
        given(douyinChannelGateService.sendPost(argThat((t) -> {
            return t == ChannelPostDouyinEnum.SKU_SYNC_STOCK;
        }), any(BaseRequest.class), any())).willReturn(new ChannelResponseDTO() {{
            setCode(Integer.valueOf(DouyinResultCodeEnum.SUCCESS.getCode().toString()));
        }});

        // 模拟上架
        given(douyinChannelGateService.sendPost(argThat((t) -> {
            return t == ChannelPostDouyinEnum.PRODUCT_ON_SHELF;
        }), any(BaseRequest.class), any())).willReturn(new ChannelResponseDTO() {{
            setCode(Integer.valueOf(DouyinResultCodeEnum.SUCCESS.getCode().toString()));
        }});

        // 模拟下架
        given(douyinChannelGateService.sendPost(argThat((t) -> {
            return t == ChannelPostDouyinEnum.PRODUCT_OFF_SHELF;
        }), any(BaseRequest.class), any())).willReturn(new ChannelResponseDTO() {{
            setCode(Integer.valueOf(DouyinResultCodeEnum.SUCCESS.getCode().toString()));
        }});
    }
}
