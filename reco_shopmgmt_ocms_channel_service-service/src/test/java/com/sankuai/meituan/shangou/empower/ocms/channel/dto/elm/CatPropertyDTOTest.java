package com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.AttrValueInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryAttrInfo;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.CategoryAttrValueTypeEnum;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.ChannelSpecialAttrEnum;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * CatPropertyDTO单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class CatPropertyDTOTest {

    @Test
    public void testConvert2Info_TextType() {
        // 准备测试数据 - 文本类型
        CatPropertyDTO dto = new CatPropertyDTO();
        dto.setPropertyId(1L);
        dto.setCategoryId(100L);
        dto.setPropertyName("测试属性");
        dto.setRequired(true);
        dto.setSaleProp(false);
        dto.setInputProp(true);
        dto.setSortOrder(10);
        dto.setMultiSelect(false);
        dto.setEnumProp(false);

        // 执行测试
        CategoryAttrInfo result = dto.convert2Info();

        // 验证结果
        Assert.assertEquals("1", result.getAttrId());
        Assert.assertEquals("测试属性", result.getAttrName());
        Assert.assertEquals(CategoryAttrValueTypeEnum.TEXT.getCode(), result.getAttrValueType());
        Assert.assertEquals("50", result.getTextMaxLength());
        Assert.assertEquals("1", result.getSupportExtend());
        Assert.assertEquals("2", result.getNeed());
        Assert.assertEquals(10, result.getSequence());
        Assert.assertEquals(0, result.getSupportPicture());
        Assert.assertNull(result.getValueList());
    }

    @Test
    public void testConvert2Info_SingleSelector() {
        // 准备测试数据 - 单选类型
        CatPropertyDTO dto = new CatPropertyDTO();
        dto.setPropertyId(2L);
        dto.setCategoryId(100L);
        dto.setPropertyName("单选属性");
        dto.setRequired(true);
        dto.setSaleProp(false);
        dto.setInputProp(false);
        dto.setSortOrder(20);
        dto.setMultiSelect(false);
        dto.setEnumProp(true);

        // 添加属性值
        List<CatPropertyValueDTO> propertyValues = new ArrayList<>();
        CatPropertyValueDTO value1 = new CatPropertyValueDTO();
        value1.setValueId(101L);
        value1.setValueData("选项1");
        value1.setSortOrder(1);
        propertyValues.add(value1);

        CatPropertyValueDTO value2 = new CatPropertyValueDTO();
        value2.setValueId(102L);
        value2.setValueData("选项2");
        value2.setSortOrder(2);
        propertyValues.add(value2);

        dto.setPropertyValues(propertyValues);

        // 执行测试
        CategoryAttrInfo result = dto.convert2Info();

        // 验证结果
        Assert.assertEquals("2", result.getAttrId());
        Assert.assertEquals("单选属性", result.getAttrName());
        Assert.assertEquals(CategoryAttrValueTypeEnum.SINGLE_SELECTOR.getCode(), result.getAttrValueType());
        Assert.assertEquals("2", result.getSupportExtend());
        Assert.assertEquals("1", result.getNeed());
        Assert.assertEquals(20, result.getSequence());
        Assert.assertEquals(0, result.getSupportPicture());

        // 验证属性值列表
        Assert.assertNotNull(result.getValueList());
        Assert.assertEquals(2, result.getValueList().size());

        AttrValueInfo valueInfo1 = result.getValueList().get(0);
        Assert.assertEquals("101", valueInfo1.getValueId());
        Assert.assertEquals("选项1", valueInfo1.getValue());

        AttrValueInfo valueInfo2 = result.getValueList().get(1);
        Assert.assertEquals("102", valueInfo2.getValueId());
        Assert.assertEquals("选项2", valueInfo2.getValue());
    }

    @Test
    public void testConvert2Info_MultiSelector() {
        // 准备测试数据 - 多选类型
        CatPropertyDTO dto = new CatPropertyDTO();
        dto.setPropertyId(3L);
        dto.setCategoryId(100L);
        dto.setPropertyName("多选属性");
        dto.setRequired(false);
        dto.setSaleProp(false);
        dto.setInputProp(false);
        dto.setSortOrder(30);
        dto.setMultiSelect(true);
        dto.setEnumProp(true);

        // 添加属性值
        List<CatPropertyValueDTO> propertyValues = new ArrayList<>();
        CatPropertyValueDTO value1 = new CatPropertyValueDTO();
        value1.setValueId(201L);
        value1.setValueData("多选项1");
        value1.setSortOrder(1);
        propertyValues.add(value1);

        CatPropertyValueDTO value2 = new CatPropertyValueDTO();
        value2.setValueId(202L);
        value2.setValueData("多选项2");
        value2.setSortOrder(2);
        propertyValues.add(value2);

        dto.setPropertyValues(propertyValues);

        // 执行测试
        CategoryAttrInfo result = dto.convert2Info();

        // 验证结果
        Assert.assertEquals("3", result.getAttrId());
        Assert.assertEquals("多选属性", result.getAttrName());
        Assert.assertEquals(CategoryAttrValueTypeEnum.MULTI_SELECTOR.getCode(), result.getAttrValueType());
        Assert.assertEquals("2", result.getSupportExtend());
        Assert.assertEquals("2", result.getNeed());
        Assert.assertEquals(30, result.getSequence());
        Assert.assertEquals(0, result.getSupportPicture());

        // 验证属性值列表
        Assert.assertNotNull(result.getValueList());
        Assert.assertEquals(2, result.getValueList().size());

        AttrValueInfo valueInfo1 = result.getValueList().get(0);
        Assert.assertEquals("201", valueInfo1.getValueId());
        Assert.assertEquals("多选项1", valueInfo1.getValue());

        AttrValueInfo valueInfo2 = result.getValueList().get(1);
        Assert.assertEquals("202", valueInfo2.getValueId());
        Assert.assertEquals("多选项2", valueInfo2.getValue());
    }

    @Test
    public void testConvert2Info_SaleProp() {
        // 准备测试数据 - 销售属性
        CatPropertyDTO dto = new CatPropertyDTO();
        dto.setPropertyId(4L);
        dto.setCategoryId(100L);
        dto.setPropertyName("销售属性");
        dto.setRequired(true);
        dto.setSaleProp(true);
        dto.setInputProp(false);
        dto.setSortOrder(40);
        dto.setMultiSelect(false);
        dto.setEnumProp(true);

        // 添加属性值
        List<CatPropertyValueDTO> propertyValues = new ArrayList<>();
        CatPropertyValueDTO value1 = new CatPropertyValueDTO();
        value1.setValueId(301L);
        value1.setValueData("规格1");
        value1.setSortOrder(1);
        propertyValues.add(value1);

        dto.setPropertyValues(propertyValues);

        // 执行测试
        CategoryAttrInfo result = dto.convert2Info();

        // 验证结果
        Assert.assertEquals("4", result.getAttrId());
        Assert.assertEquals("销售属性", result.getAttrName());
        Assert.assertEquals(CategoryAttrValueTypeEnum.SINGLE_SELECTOR.getCode(), result.getAttrValueType());
        Assert.assertEquals("2", result.getSupportExtend());
        Assert.assertEquals("1", result.getNeed());
        Assert.assertEquals(40, result.getSequence());
        Assert.assertEquals(1, result.getSupportPicture()); // 销售属性支持图片

        // 验证属性值列表
        Assert.assertNotNull(result.getValueList());
        Assert.assertEquals(1, result.getValueList().size());

        AttrValueInfo valueInfo = result.getValueList().get(0);
        Assert.assertEquals("301", valueInfo.getValueId());
        Assert.assertEquals("规格1", valueInfo.getValue());
    }

    @Test
    public void testConvert2Info_BrandAttr() {
        // 准备测试数据 - 品牌属性
        CatPropertyDTO dto = new CatPropertyDTO();
        dto.setPropertyId(ChannelSpecialAttrEnum.ELM_BRAND.getCode());
        dto.setCategoryId(100L);
        dto.setPropertyName("品牌");
        dto.setRequired(true);
        dto.setSaleProp(false);
        dto.setInputProp(false);
        dto.setSortOrder(50);
        dto.setMultiSelect(false);
        dto.setEnumProp(true);

        // 添加属性值 - 品牌属性的可选值不会返回
        List<CatPropertyValueDTO> propertyValues = new ArrayList<>();
        CatPropertyValueDTO value1 = new CatPropertyValueDTO();
        value1.setValueId(401L);
        value1.setValueData("品牌1");
        value1.setSortOrder(1);
        propertyValues.add(value1);

        dto.setPropertyValues(propertyValues);

        // 执行测试
        CategoryAttrInfo result = dto.convert2Info();

        // 验证结果
        Assert.assertEquals(String.valueOf(ChannelSpecialAttrEnum.ELM_BRAND.getCode()), result.getAttrId());
        Assert.assertEquals("品牌", result.getAttrName());
        Assert.assertEquals(CategoryAttrValueTypeEnum.SINGLE_SELECTOR.getCode(), result.getAttrValueType());
        Assert.assertEquals("2", result.getSupportExtend());
        Assert.assertEquals("1", result.getNeed());
        Assert.assertEquals(50, result.getSequence());
        Assert.assertEquals(0, result.getSupportPicture());

        // 验证品牌属性的可选值不会返回
        Assert.assertNull(result.getValueList());
    }
}

