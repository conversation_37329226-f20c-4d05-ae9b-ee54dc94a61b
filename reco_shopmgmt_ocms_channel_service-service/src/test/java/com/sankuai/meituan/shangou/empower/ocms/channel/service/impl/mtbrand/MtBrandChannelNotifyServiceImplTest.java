package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.CategoryAdjustTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryDegradeRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoDegradeDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoSortDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoSortItemDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryInfoUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryLevelAdjustDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryLevelAdjustRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySmartSortSwitchRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategorySortRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CategoryUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.RecommendCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.CommonThreadPool;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtBrandChannelNotifyServiceImplTest {

    @Mock
    private BaseConverterService baseConverterService;

    @Mock
    private MtConverterService mtConverterService;

    @Mock
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Mock
    private CopChannelStoreService copChannelStoreService;

    @Mock
    private CommonThreadPool commonThreadPool;

    @Mock
    private ClusterRateLimiter clusterRateLimiter;

    @Mock
    private CopAccessConfigService copAccessConfigService;

    @Mock
    private CommonLogger log;

    @InjectMocks
    private MtBrandChannelCategoryServiceImpl mtBrandChannelCategoryService;

    @Mock
    private MtBrandChannelSkuServiceImpl mtBrandChannelSkuService;

    @Test
    public void createCategoryTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        CategoryRequest request = new CategoryRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));

        CategoryInfoDTO categoryInfoDTO = new CategoryInfoDTO();
        categoryInfoDTO.setStoreId(storeId);
        categoryInfoDTO.setCode("123");
        categoryInfoDTO.setName("leimu");
        categoryInfoDTO.setLevel(3);
        categoryInfoDTO.setSort(1);
        categoryInfoDTO.setParentCode("12");
        categoryInfoDTO.setParentName("fuleimu");

        List<CategoryInfoDTO> paramList = Lists.newArrayList(categoryInfoDTO);
        request.setParamList(paramList);

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        when(copChannelStoreService.getChannelPoiCode(any(), any())).thenReturn(channelStoreDOMap);

        Map<String, Object> map = Maps.newHashMap();
        map.put("data", "ok");
        when(mtBrandChannelGateService.sendPost(any(), any(), any())).thenReturn(map);

        mtBrandChannelCategoryService.createCategory(request);
    }

    @Test
    public void updateCategoryTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        CategoryUpdateRequest request = new CategoryUpdateRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));

        CategoryInfoUpdateDTO categoryInfoDTO = new CategoryInfoUpdateDTO();
        categoryInfoDTO.setStoreId(storeId);
        categoryInfoDTO.setCode("123");
        categoryInfoDTO.setName("leimu");
        categoryInfoDTO.setChannelCategoryCode("1");
        categoryInfoDTO.setLevel(3);
        categoryInfoDTO.setSort(1);

        List<CategoryInfoUpdateDTO> paramList = Lists.newArrayList(categoryInfoDTO);
        request.setParamList(paramList);

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        when(copChannelStoreService.getChannelPoiCode(any(), any())).thenReturn(channelStoreDOMap);

        Map<String, Object> map = Maps.newHashMap();
        map.put("data", "ok");
        when(mtBrandChannelGateService.sendPost(any(), any(), any())).thenReturn(map);

        mtBrandChannelCategoryService.updateCategory(request);
    }

    @Test
    public void deleteCategoryTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        CategoryDeleteRequest request = new CategoryDeleteRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));

        CategoryInfoDeleteDTO categoryInfoDTO = new CategoryInfoDeleteDTO();
        categoryInfoDTO.setStoreId(storeId);
        categoryInfoDTO.setCode("123");
        categoryInfoDTO.setChannelCategoryCode("1");

        List<CategoryInfoDeleteDTO> paramList = Lists.newArrayList(categoryInfoDTO);
        request.setParamList(paramList);

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        when(copChannelStoreService.getChannelPoiCode(any(), any())).thenReturn(channelStoreDOMap);

        Map<String, Object> map = Maps.newHashMap();
        map.put("data", "ok");
        when(mtBrandChannelGateService.sendPost(any(), any(), any())).thenReturn(map);

        mtBrandChannelCategoryService.deleteCategory(request);
    }

    @Test
    public void sortCategoryTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        CategorySortRequest request = new CategorySortRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));

        CategoryInfoSortDTO categoryInfoDTO = new CategoryInfoSortDTO();
        categoryInfoDTO.setStoreId(storeId);
        categoryInfoDTO.setCode("12");
        categoryInfoDTO.setChannelCategoryCode("1");
        categoryInfoDTO.setChannelCategoryCode("1");

        CategoryInfoSortItemDTO categoryInfoSortItemDTO = new CategoryInfoSortItemDTO();
        categoryInfoSortItemDTO.setCode("123");
        categoryInfoSortItemDTO.setChannelCategoryCode("1");
        categoryInfoSortItemDTO.setName("1");
        categoryInfoSortItemDTO.setSort(1);
        List<CategoryInfoSortItemDTO> sortItemList = Lists.newArrayList(categoryInfoSortItemDTO);
        categoryInfoDTO.setSortItemList(sortItemList);

        List<CategoryInfoSortDTO> paramList = Lists.newArrayList(categoryInfoDTO);
        request.setParamList(paramList);

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        ChannelCategoryDTO channelCategoryDTO = new ChannelCategoryDTO();
        channelCategoryDTO.setCategory_code("1");
        channelCategoryDTO.setCategory_code("1");
        List<ChannelCategoryDTO> channelCategoryDTOS = Lists.newArrayList(channelCategoryDTO);
        when(mtConverterService.sortCategory(anyList())).thenReturn(channelCategoryDTOS);

        when(copChannelStoreService.getChannelPoiCode(any(), any())).thenReturn(channelStoreDOMap);

        Map<String, Object> map = Maps.newHashMap();
        map.put("data", "ok");
        when(mtBrandChannelGateService.sendPost(any(), any(), any())).thenReturn(map);

        mtBrandChannelCategoryService.sortCategory(request);
    }

    @Test
    public void batchGetCategoryTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        CatRequest request = new CatRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));

        List<String> paramList = Lists.newArrayList("123");
        request.setIds(paramList);

        CopAccessConfigDO configDO = new CopAccessConfigDO();
        configDO.setAppId(123L);
        CopAccessConfigDO configDO2 = new CopAccessConfigDO();
        configDO2.setAppId(123L);
        List<CopAccessConfigDO> copAccessConfigDOS = Lists.newArrayList(configDO, configDO2);
        when(copAccessConfigService.findTenantChannelConfigApp(any(), any())).thenReturn(copAccessConfigDOS);

        //when(mtBrandChannelGateService.sendGet(any(), any(), any(), any())).thenReturn(map);

        mtBrandChannelCategoryService.batchGetCategory(request);
    }

    @Test
    public void degradeCategoryTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        CategoryDegradeRequest request = new CategoryDegradeRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));

        CategoryInfoDegradeDTO categoryInfoDegradeDTO = new CategoryInfoDegradeDTO();
        categoryInfoDegradeDTO.setChannelCode("123");
        categoryInfoDegradeDTO.setStoreId(storeId);
        List<CategoryInfoDegradeDTO> paramList = Lists.newArrayList(categoryInfoDegradeDTO);
        request.setParamList(paramList);

        mtBrandChannelCategoryService.degradeCategory(request);
    }

    @Test
    public void adjustCategoryLevelTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        CategoryLevelAdjustRequest request = new CategoryLevelAdjustRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));

        CategoryLevelAdjustDTO categoryInfoDegradeDTO = new CategoryLevelAdjustDTO();
        categoryInfoDegradeDTO.setChannelCode("123");
        categoryInfoDegradeDTO.setStoreId(storeId);
        categoryInfoDegradeDTO.setAdjustType(CategoryAdjustTypeEnum.DEGRADE);
        List<CategoryLevelAdjustDTO> paramList = Lists.newArrayList(categoryInfoDegradeDTO);
        request.setParamList(paramList);

        mtBrandChannelCategoryService.adjustCategoryLevel(request);
    }

    @Test
    public void updateCategoryChannelCodeTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        CategoryUpdateRequest request = new CategoryUpdateRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));

        CategoryInfoUpdateDTO categoryInfoDTO = new CategoryInfoUpdateDTO();
        categoryInfoDTO.setStoreId(storeId);
        categoryInfoDTO.setCode("123");
        categoryInfoDTO.setChannelCategoryCode("1");

        List<CategoryInfoUpdateDTO> paramList = Lists.newArrayList(categoryInfoDTO);
        request.setParamList(paramList);

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        when(copChannelStoreService.getChannelPoiCode(any(), any())).thenReturn(channelStoreDOMap);

        mtBrandChannelCategoryService.updateCategoryChannelCode(request);
    }

    @Test
    public void updateSmartSortSwitchTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        CategorySmartSortSwitchRequest request = new CategorySmartSortSwitchRequest();
        request.setStoreId(storeId);
        request.setChannelId(channeldId);
        request.setTenantId(tenantId);
        request.setCategoryCode("1");
        request.setChannelCategoryCode("2");

        mtBrandChannelCategoryService.updateSmartSortSwitch(request);
    }

    @Test
    public void recommendCategoryTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        RecommendCategoryRequest request = new RecommendCategoryRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));
        request.setUpcCode("1");
        request.setName("name");

        CopAccessConfigDO configDO = new CopAccessConfigDO();
        configDO.setAppId(123L);
        CopAccessConfigDO configDO2 = new CopAccessConfigDO();
        configDO2.setAppId(123L);
        List<CopAccessConfigDO> copAccessConfigDOS = Lists.newArrayList(configDO, configDO2);
        when(copAccessConfigService.findTenantChannelConfigApp(any(), any())).thenReturn(copAccessConfigDOS);

        mtBrandChannelCategoryService.recommendCategory(request);
    }

}