package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.CatRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSkuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;
import java.util.concurrent.ExecutorService;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtBrandChannelSkuServiceImplTest {

    @Mock
    private CopChannelStoreService copChannelStoreService;

    @Mock
    private BaseConverterService baseConverterService;

    @Mock
    private MtConverterService mtConverterService;

    @Mock
    private ClusterRateLimiter clusterRateLimiter;

    @Mock
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Mock
    private CommonLogger log;

    @Mock(name = "mtPictureUploadThreadPool")
    private ExecutorService mtPictureUploadThreadPool;

    @InjectMocks
    private MtBrandChannelSkuServiceImpl mtBrandChannelSkuService;

    @Test
    public void getSkuInfoTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        when(copChannelStoreService.getChannelPoiCode(anyLong(), anyInt(), anyList())).thenReturn(channelStoreDOMap);

        Map<String, Object> sysParams = Maps.newHashMap();
        sysParams.put("app_id", 123L);
        when(mtBrandChannelGateService.getSysParam(any())).thenReturn(sysParams);

        GetSkuInfoRequest request = new GetSkuInfoRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));
        request.setStoreId(storeId);

        mtBrandChannelSkuService.getSkuInfo(request);
    }

    @Test
    public void batchGetSkuInfoTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        when(copChannelStoreService.getChannelPoiCode(anyLong(), anyInt(), anyList())).thenReturn(channelStoreDOMap);

        Map<String, Object> sysParams = Maps.newHashMap();
        sysParams.put("app_id", 123L);
        when(mtBrandChannelGateService.getSysParam(any())).thenReturn(sysParams);

        BatchGetSkuInfoRequest request = new BatchGetSkuInfoRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));
        request.setStoreId(storeId);

        mtBrandChannelSkuService.batchGetSkuInfo(request);
    }

    @Test
    public void batchGetChannelStoreCategoryInfoTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        when(copChannelStoreService.getChannelPoiCode(anyLong(), anyInt(), anyList())).thenReturn(channelStoreDOMap);

        Map<String, Object> sysParams = Maps.newHashMap();
        sysParams.put("app_id", 123L);
        when(mtBrandChannelGateService.getSysParam(any())).thenReturn(sysParams);

        CatRequest request = new CatRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));
        request.setStoreId(storeId);

        mtBrandChannelSkuService.batchGetChannelStoreCategoryInfo(request);
    }

}