package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelCategoryQueryResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DouyinResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConverterServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doNothing;

@RunWith(MockitoJUnitRunner.class)
public class DouyinChannelSkuServiceImplTest {
    @InjectMocks
    DouyinChannelSkuServiceImpl douyinChannelSkuService;

    @Mock
    private DouyinChannelGateService douyinChannelGateService;

    @Mock
    private CommonLogger log;

    @Spy
    private DouyinConverterService douyinConverterService = new DouyinConverterServiceImpl();

    @Mock
    private CopChannelStoreService copChannelStoreService;

    @Spy
    private BaseConverterService baseConverterService = new BaseConverterServiceImpl();

    Long categoryCode = 111110000L;
    String categoryName = "my-test";
    Long parentCode = 11111L;
    String parentName = "my";

    @Test
    public void testCreateCategoryValid() {
        CatRequest request = new CatRequest();
        request.setBaseInfo(new BaseRequestSimple());
        GetCategoryResponse categoryResponse = douyinChannelSkuService.batchGetChannelStoreCategoryInfo(request);
        Assert.assertNotNull(categoryResponse);
        Assert.assertEquals(categoryResponse.getStatus(), ResultGenerator.genSuccessResult());
        Assert.assertNotNull(categoryResponse.getCatInfoList());

        Assert.assertNotNull(categoryResponse.getCatInfoList().get(0));
        Assert.assertEquals(categoryResponse.getCatInfoList().get(0).getCatId(), parentCode.toString());
        Assert.assertEquals(categoryResponse.getCatInfoList().get(0).getName(), parentName);
        Assert.assertEquals(categoryResponse.getCatInfoList().get(0).getParentId(), (String) null);
        Assert.assertEquals(categoryResponse.getCatInfoList().get(0).getDepth(), 1);
        Assert.assertEquals(categoryResponse.getCatInfoList().get(0).getSequence(), 1);

        Assert.assertNotNull(categoryResponse.getCatInfoList().get(1));
        Assert.assertEquals(categoryResponse.getCatInfoList().get(1).getCatId(), categoryCode.toString());
        Assert.assertEquals(categoryResponse.getCatInfoList().get(1).getName(), categoryName);
        Assert.assertEquals(categoryResponse.getCatInfoList().get(1).getParentId(), parentCode.toString());
        Assert.assertEquals(categoryResponse.getCatInfoList().get(1).getDepth(), 2);
        Assert.assertEquals(categoryResponse.getCatInfoList().get(1).getSequence(), 2);
    }

    @Before
    public void initMock() {
        // 模拟店内分类装修操作
        given(douyinChannelGateService.sendPost(argThat((t) -> {
            return t == ChannelPostDouyinEnum.SHOP_MARKET_CATEGORY_QUERY;
        }), any(BaseRequestSimple.class), any(), any())).willReturn(new ChannelResponseDTO(){{
            setCode(Integer.valueOf(DouyinResultCodeEnum.SUCCESS.getCode()));
            setCoreData(new ChannelCategoryQueryResult() {{
                setCategory_info_list(Collections.singletonList(new CategoryInfoResult(){{
                    setCategory_id(parentCode);
                    setCategory_name(parentName);
                    setRank(1L);
                    setChildren_category_info_list(Collections.singletonList(new CategoryInfoResult(){{
                        setCategory_id(categoryCode);
                        setCategory_name(categoryName);
                        setRank(2L);
                    }}));
                }}));
            }});
        }});

        doNothing().when(log).info((String) any(), (Object) any());
    }
}
