package com.sankuai.meituan.shangou.empower.ocms.channel.service.converter;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.ChannelSpuCreateOrUpdateDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.PropValueDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.elm.SkuSpecDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.SpecTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelLeafStoreCategory;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SpuInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MoneyUtils;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest(MccConfigUtil.class)
class ElmConverterServiceTest {

    @Resource
    private ElmConverterService elmConverterService;

    @Test
    @Ignore
    void convertTest() {
        SpuInfoDTO param = new SpuInfoDTO();
        param.setName("木盾");
        param.setBrandName("Hyrule");
        param.setPictures(Collections.emptyList());
        param.setChannelCategoryId("202056409"); // DTP(RX)
        param.setChannelSpuId("46726384627638156"); // random number
        param.setCustomSpuId("32957489271"); // random number
        List<ChannelLeafStoreCategory> categories = new ArrayList<>();
        ChannelLeafStoreCategory category = new ChannelLeafStoreCategory();
        category.setCategoryCode("202056409");
        category.setCategoryName("DTP(RX)");
        categories.add(category);
        param.setLeafStoreCategoryList(categories);
        param.setStatus(1);
        param.setDescription("普通盾");
        param.setSellPoint("也可作锅盖使用");
        param.setProperties("测试属性");
        param.setSpecType(SpecTypeEnum.MULTI.getCode());
        List<SkuInSpuInfoDTO> skus = new ArrayList<>();
        SkuInSpuInfoDTO sku = new SkuInSpuInfoDTO();
        String availableTimes = "{\"monday\":\"09:20-09:30\",\"tuesday\":\"00:01-23:59\",\"wednesday\":\"00:01-23:59\",\"thursday\":\"00:01-23:59\",\"friday\":\"10:00-23:00\",\"saturday\":\"19:00-22:30\",\"sunday\":\"09:00-09:30\"}";
        sku.setAvailableTimes(availableTimes);
        sku.setCustomSkuId("************-2");
        sku.setSpec("份");
        sku.setPrice(850);
        sku.setStock(3);
        sku.setUpc("");
        sku.setWeight(20);
        sku.setBoxPrice(1.2);
        sku.setBoxQuantity(1);
        sku.setUnit("面");
        sku.setMinPurchaseQuantity(1);
        skus.add(sku);
        param.setSkus(skus);
        ChannelSpuCreateOrUpdateDTO dto = elmConverterService.convert(param, true, false, 1900008L,1,true , false, false, false);

        SkuSpecDTO specDTO = new SkuSpecDTO();
        specDTO.setSku_spec_custom_id(sku.getCustomSkuId());
        PropValueDTO prop = PropValueDTO.buildSelfDefinePropValue();
        prop.setValueText(sku.getSpec());
        specDTO.setSpec_property(prop);
        specDTO.setUpc(sku.getUpc());
        specDTO.setLeft_num(sku.getStock());
        specDTO.setSale_price(MoneyUtils.yuanToFen(sku.getPrice()));
        specDTO.setWeight(sku.getWeight());
        List<SkuSpecDTO> specDTOS = new ArrayList<>();
        specDTOS.add(specDTO);

        assertEquals(specDTOS, dto.getSku_spec());
    }
}