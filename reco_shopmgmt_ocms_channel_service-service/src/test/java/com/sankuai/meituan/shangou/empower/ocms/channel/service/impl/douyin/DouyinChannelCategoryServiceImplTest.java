package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.douyin;

import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelCategoryOperateResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.douyin.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostDouyinEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DouyinResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.DouyinConverterServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doNothing;

@RunWith(MockitoJUnitRunner.class)
public class DouyinChannelCategoryServiceImplTest {
    @InjectMocks
    DouyinChannelCategoryServiceImpl douyinChannelCategoryService;

    @Mock
    private DouyinChannelGateService douyinChannelGateService;

    @Mock
    private CommonLogger log;

    @Spy
    private DouyinConverterService douyinConverterService = new DouyinConverterServiceImpl();

    @Mock
    private CopChannelStoreService copChannelStoreService;

    @Spy
    private BaseConverterService baseConverterService = new BaseConverterServiceImpl();

    String categoryCode = "111110000";

    @Test
    public void testCreateCategoryValid() {
        CategoryRequest request = new CategoryRequest();
        request.setBaseInfo(new BaseRequestSimple());
        request.setParamList(Collections.singletonList(new CategoryInfoDTO().setName("my-category").setSort(10)));
        CreateCategoryResponse categoryResponse = douyinChannelCategoryService.createCategory(request);
        Assert.assertNotNull(categoryResponse);
        Assert.assertNotNull(categoryResponse.getData());
        Assert.assertEquals(categoryResponse.getData().get(0).getResultCode(), 0);
        Assert.assertEquals(categoryResponse.getData().get(0).getChannelCode(), categoryCode);
    }

    @Test
    public void testUpdateCategoryValid() {
        CategoryUpdateRequest request = new CategoryUpdateRequest();
        request.setBaseInfo(new BaseRequestSimple());
        request.setParamList(Collections.singletonList(new CategoryInfoUpdateDTO(){{
            setChannelCategoryCode(categoryCode);
            setName("my-category2");
            setSort(10);
        }}));
        UpdateCategoryResponse categoryResponse = douyinChannelCategoryService.updateCategory(request);
        Assert.assertNotNull(categoryResponse);
        Assert.assertNotNull(categoryResponse.getData());
        Assert.assertEquals(categoryResponse.getData().get(0).getResultCode(), 0);
    }

    @Test
    public void testDeleteCategoryValid() {
        CategoryDeleteRequest request = new CategoryDeleteRequest();
        request.setBaseInfo(new BaseRequestSimple());
        request.setParamList(Collections.singletonList(new CategoryInfoDeleteDTO(){{
            setChannelCategoryCode(categoryCode);
        }}));
        UpdateCategoryResponse categoryResponse = douyinChannelCategoryService.deleteCategory(request);
        Assert.assertNotNull(categoryResponse);
        Assert.assertNotNull(categoryResponse.getData());
        Assert.assertEquals(categoryResponse.getData().get(0).getResultCode(), 0);
    }

    @Test
    public void testSortCategoryValid() {
        CategorySortRequest request = new CategorySortRequest();
        request.setBaseInfo(new BaseRequestSimple());
        request.setParamList(Collections.singletonList(new CategoryInfoSortDTO(){{
            setChannelCategoryCode(categoryCode);
            setSortItemList(Collections.singletonList(new CategoryInfoSortItemDTO(){{
                setChannelCategoryCode(categoryCode);
                setName("my-test2");
                setSort(10);
            }}));
        }}));
        UpdateCategoryResponse categoryResponse = douyinChannelCategoryService.sortCategory(request);
        Assert.assertNotNull(categoryResponse);
        Assert.assertNotNull(categoryResponse.getData());
        Assert.assertEquals(categoryResponse.getData().get(0).getResultCode(), 0);
    }

    @Before
    public void initMock() {
        // 模拟店内分类装修操作
        given(douyinChannelGateService.sendPost(argThat((t) -> {
            return t == ChannelPostDouyinEnum.SHOP_MARKET_CATEGORY_OPERATE;
        }), any(BaseRequestSimple.class), any(), any())).willReturn(new ChannelResponseDTO() {{
            setCode(Integer.valueOf(DouyinResultCodeEnum.SUCCESS.getCode()));
            setCoreData(new ChannelCategoryOperateResult(){{
                setCategory_id(Long.valueOf(categoryCode));
            }});
        }});

        doNothing().when(log).info((String) any(), (Object) any());
    }
}
