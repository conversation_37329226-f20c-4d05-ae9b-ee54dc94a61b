package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.yz;

import org.junit.Test;

import com.meituan.linz.boot.util.JacksonUtils;
import com.youzan.cloud.open.sdk.core.client.auth.Token;
import com.youzan.cloud.open.sdk.core.client.core.DefaultYZClient;
import com.youzan.cloud.open.sdk.core.client.core.YouZanClient;
import com.youzan.cloud.open.sdk.gen.v4_0_0.api.YouzanItemcategoriesTagAdd;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanItemcategoriesTagAddParams;
import com.youzan.cloud.open.sdk.gen.v4_0_0.model.YouzanItemcategoriesTagAddResult;

public class YzClientTest {
    /**
     * {"expires":1731975239784,"refreshToken":"264934847f01f31530c30dc591f0cc0","timestamp":1731377639820,"token":"a0b38c40b80313ae9308dd462369570"}
     *
     * @throws Exception
     */
    @Test
    public void test1() throws Exception {
        YouZanClient yzClient = new DefaultYZClient();
        Token token = new Token("a0b38c40b80313ae9308dd462369570");

        YouzanItemcategoriesTagAdd youzanItemcategoriesTagAdd = new YouzanItemcategoriesTagAdd();


//创建参数对象,并设置参数
        YouzanItemcategoriesTagAddParams youzanItemcategoriesTagAddParams = new YouzanItemcategoriesTagAddParams();

        YouzanItemcategoriesTagAddParams.YouzanItemcategoriesTagAddParamsAddgroupparam youzanItemcategoriesTagAddParamsAddGroupParam = new YouzanItemcategoriesTagAddParams.YouzanItemcategoriesTagAddParamsAddgroupparam();
        youzanItemcategoriesTagAddParams.setAddGroupParam(youzanItemcategoriesTagAddParamsAddGroupParam);
        youzanItemcategoriesTagAddParamsAddGroupParam.setName("level3");
        youzanItemcategoriesTagAddParamsAddGroupParam.setType(0);
        // l1-> 288736776
        // l2 -> 288743120,288735156
        youzanItemcategoriesTagAddParamsAddGroupParam.setUpperid(288743120L);

        youzanItemcategoriesTagAdd.setAPIParams(youzanItemcategoriesTagAddParams);
        YouzanItemcategoriesTagAddResult result = yzClient.invoke(youzanItemcategoriesTagAdd, token, YouzanItemcategoriesTagAddResult.class);
        System.out.println(JacksonUtils.toJson(result));
    }
}
