package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.jddj;

import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelResponseResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.jddj.ChannelSkuStatusResponceResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelPostJDDJEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConvertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.JddjConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * @Author: luokai14
 * @Date: 2022/8/26 2:03 下午
 * @Mail: <EMAIL>
 */
@RunWith(MockitoJUnitRunner.class)
public class JddjChannelSpuServiceImplTest {

    @Mock
    JddjConverterService jddjConverterService;

    @Mock
    JddjChannelGateService jddjChannelGateService;


    @InjectMocks
    JddjChannelSkuServiceImpl jddjChannelSkuService;

    @InjectMocks
    JddjChannelSpuServiceImpl jddjChannelSpuService;

    @Mock
    JddjChannelSkuServiceImpl jddjChannelSkuService2;

    @Test
    public void skuUpdateSaleStatusTest() {
        SpuInfoRequest request = new SpuInfoRequest();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setTenantId(1000338);
        baseRequest.setChannelId(300);
        baseRequest.setStoreIdList(Lists.newArrayList(49869788L));
        request.setBaseInfo(baseRequest);
        //spu信息
        SpuInfoDTO spuInfoDTO = new SpuInfoDTO();
        spuInfoDTO.setSpuId("1561644385722986516");
        spuInfoDTO.setCustomSpuId("1561644385722986516");
        spuInfoDTO.setStatus(1);
        //sku信息
        SkuInSpuInfoDTO skuInSpuInfoDTO1 = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO1.setSkuId("1561644385735569502");
        skuInSpuInfoDTO1.setCustomSkuId("1561644385735569502");
        SkuInSpuInfoDTO skuInSpuInfoDTO2 = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO2.setSkuId("1561650375914713132");
        skuInSpuInfoDTO2.setCustomSkuId("1561650375914713132");
        spuInfoDTO.setSkus(Lists.newArrayList(skuInSpuInfoDTO1, skuInSpuInfoDTO2));

        //spu信息
        SpuInfoDTO spuInfoDTO2 = new SpuInfoDTO();
        spuInfoDTO2.setSpuId("1561644385722986516");
        spuInfoDTO2.setCustomSpuId("1561644385722986516");
        spuInfoDTO2.setStatus(1);
        //sku信息
        SkuInSpuInfoDTO skuInSpuInfoDTO3 = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO3.setSkuId("1561644385735569502");
        skuInSpuInfoDTO3.setCustomSkuId("1561644385735569502");
        SkuInSpuInfoDTO skuInSpuInfoDTO4 = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO4.setSkuId("1561650375914713132");
        skuInSpuInfoDTO4.setCustomSkuId("1561650375914713132");
        spuInfoDTO.setSkus(Lists.newArrayList(skuInSpuInfoDTO3, skuInSpuInfoDTO4));

        request.setParamList(Lists.newArrayList(spuInfoDTO, spuInfoDTO));

        //模拟京东返回信息
        Map<Long, ChannelResponseDTO> postResult = new HashMap<>();
        ChannelResponseDTO channelResponseDTO = new ChannelResponseDTO();
        channelResponseDTO.setCode("0");
        channelResponseDTO.setMsg("成功");
        channelResponseDTO.setSuccess(true);
        channelResponseDTO.setData("返回数据");
        ChannelResponseResult channelResponseResult = new ChannelResponseResult();
        channelResponseResult.setRet("true");
        channelResponseResult.setRetCode("0");
        channelResponseResult.setRetMsg("成功");
        channelResponseResult.setData("返回数据");
        //模拟返回sku信息
        ChannelSkuStatusResponceResult channelSkuStatusResponceResult1 = new ChannelSkuStatusResponceResult();
        channelSkuStatusResponceResult1.setCode(0);
        channelSkuStatusResponceResult1.setOutSkuId("1561644385735569502");
        channelSkuStatusResponceResult1.setMsg("修改失败2");

        ChannelSkuStatusResponceResult channelSkuStatusResponceResult2 = new ChannelSkuStatusResponceResult();
        channelSkuStatusResponceResult2.setCode(0);
        channelSkuStatusResponceResult2.setOutSkuId("1561650375914713132");
        channelSkuStatusResponceResult2.setMsg("修改失败");

        channelResponseResult.setDataList(Lists.newArrayList(channelSkuStatusResponceResult1, channelSkuStatusResponceResult2));
        channelResponseDTO.setDataResponse(channelResponseResult);
        postResult.put(49869788L, channelResponseDTO);


        SkuSellStatusInfoRequest skuSellStatusInfoRequest = new SkuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple = new BaseRequestSimple();
        baseRequestSimple.setTenantId(1000338);
        baseRequestSimple.setChannelId(300);
        skuSellStatusInfoRequest.setBaseInfo(baseRequestSimple);

        SkuSellStatusInfoDTO skuSellStatusInfoDTO = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO.setStoreId(49869788L);
        skuSellStatusInfoDTO.setSkuStatus(1);
        SkuIdDTO skuIdDTO1 = new SkuIdDTO();
        skuIdDTO1.setCustomSkuId("1561644385735569502");
        SkuIdDTO skuIdDTO2 = new SkuIdDTO();
        skuIdDTO2.setCustomSkuId("1561650375914713132");
        skuSellStatusInfoDTO.setSkuId(Lists.newArrayList(skuIdDTO1, skuIdDTO2));
        skuSellStatusInfoRequest.setParamList(Lists.newArrayList(skuSellStatusInfoDTO));

        when(jddjConverterService.updateSkuSellStatus(any())).thenReturn(JddjConvertUtil.convert2JDDJSellStatusRequestParam(spuInfoDTO));
        when(jddjChannelGateService.sendPost(ChannelPostJDDJEnum.SKU_SELL_STATIS, skuSellStatusInfoRequest.getBaseInfo(), 49869788L, JddjConvertUtil.convert2JDDJSellStatusRequestParam(spuInfoDTO))).thenReturn(postResult);
        jddjChannelSkuService.updateSkuSellStatus(skuSellStatusInfoRequest);
    }

    @Test
    public void spuCreateTest() {

        SpuInfoRequest request = new SpuInfoRequest();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setTenantId(1000338);
        baseRequest.setChannelId(300);
        baseRequest.setStoreIdList(Lists.newArrayList(49869788L));
        request.setBaseInfo(baseRequest);
        //spu信息
        SpuInfoDTO spuInfoDTO = new SpuInfoDTO();
        spuInfoDTO.setSpuId("1561644385722986516");
        spuInfoDTO.setCustomSpuId("1561644385722986516");
        spuInfoDTO.setStatus(1);
        //sku信息
        SkuInSpuInfoDTO skuInSpuInfoDTO1 = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO1.setSkuId("1561644385735569502");
        skuInSpuInfoDTO1.setCustomSkuId("1561644385735569502");
        SkuInSpuInfoDTO skuInSpuInfoDTO2 = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO2.setSkuId("1561650375914713132");
        skuInSpuInfoDTO2.setCustomSkuId("1561650375914713132");
        spuInfoDTO.setSkus(Lists.newArrayList(skuInSpuInfoDTO1, skuInSpuInfoDTO2));

        //spu信息
        SpuInfoDTO spuInfoDTO2 = new SpuInfoDTO();
        spuInfoDTO2.setSpuId("1561644385722986516");
        spuInfoDTO2.setCustomSpuId("1561644385722986516");
        spuInfoDTO2.setStatus(1);
        //sku信息
        SkuInSpuInfoDTO skuInSpuInfoDTO3 = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO3.setSkuId("1561644385735569502");
        skuInSpuInfoDTO3.setCustomSkuId("1561644385735569502");
        SkuInSpuInfoDTO skuInSpuInfoDTO4 = new SkuInSpuInfoDTO();
        skuInSpuInfoDTO4.setSkuId("1561650375914713132");
        skuInSpuInfoDTO4.setCustomSkuId("1561650375914713132");
        spuInfoDTO.setSkus(Lists.newArrayList(skuInSpuInfoDTO3, skuInSpuInfoDTO4));

        request.setParamList(Lists.newArrayList(spuInfoDTO, spuInfoDTO));

        //模拟京东返回信息
        Map<Long, ChannelResponseDTO> postResult = new HashMap<>();
        ChannelResponseDTO channelResponseDTO = new ChannelResponseDTO();
        channelResponseDTO.setCode("0");
        channelResponseDTO.setMsg("成功");
        channelResponseDTO.setSuccess(true);
        channelResponseDTO.setData("返回数据");
        ChannelResponseResult channelResponseResult = new ChannelResponseResult();
        channelResponseResult.setRet("true");
        channelResponseResult.setRetCode("0");
        channelResponseResult.setRetMsg("成功");
        channelResponseResult.setData("返回数据");
        //模拟返回sku信息
        ChannelSkuStatusResponceResult channelSkuStatusResponceResult1 = new ChannelSkuStatusResponceResult();
        channelSkuStatusResponceResult1.setCode(0);
        channelSkuStatusResponceResult1.setOutSkuId("1561644385735569502");
        channelSkuStatusResponceResult1.setMsg("修改失败2");

        ChannelSkuStatusResponceResult channelSkuStatusResponceResult2 = new ChannelSkuStatusResponceResult();
        channelSkuStatusResponceResult2.setCode(0);
        channelSkuStatusResponceResult2.setOutSkuId("1561650375914713132");
        channelSkuStatusResponceResult2.setMsg("修改失败");

        channelResponseResult.setDataList(Lists.newArrayList(channelSkuStatusResponceResult1, channelSkuStatusResponceResult2));
        channelResponseDTO.setDataResponse(channelResponseResult);
        postResult.put(49869788L, channelResponseDTO);


        SkuSellStatusInfoRequest skuSellStatusInfoRequest = new SkuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple = new BaseRequestSimple();
        baseRequestSimple.setTenantId(1000338);
        baseRequestSimple.setChannelId(300);
        skuSellStatusInfoRequest.setBaseInfo(baseRequestSimple);

        SkuSellStatusInfoDTO skuSellStatusInfoDTO = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO.setStoreId(49869788L);
        skuSellStatusInfoDTO.setSkuStatus(1);
        SkuIdDTO skuIdDTO1 = new SkuIdDTO();
        skuIdDTO1.setCustomSkuId("1561644385735569502");
        SkuIdDTO skuIdDTO2 = new SkuIdDTO();
        skuIdDTO2.setCustomSkuId("1561650375914713132");

        skuSellStatusInfoDTO.setSkuId(Lists.newArrayList(skuIdDTO1, skuIdDTO2));

        skuSellStatusInfoRequest.setParamList(Lists.newArrayList(skuSellStatusInfoDTO));


        when(jddjConverterService.updateSkuSellStatus(any())).thenReturn(JddjConvertUtil.convert2JDDJSellStatusRequestParam(spuInfoDTO));
        when(jddjChannelGateService.sendPost(ChannelPostJDDJEnum.SKU_SELL_STATIS, skuSellStatusInfoRequest.getBaseInfo(), 49869788L, JddjConvertUtil.convert2JDDJSellStatusRequestParam(spuInfoDTO))).thenReturn(postResult);

        ResultData resultData = jddjChannelSkuService.updateSkuSellStatus(skuSellStatusInfoRequest);
        when(jddjChannelSkuService2.updateSkuSellStatus(skuSellStatusInfoRequest)).thenReturn(resultData);
        jddjChannelSpuService.spuCreate(request);
    }

    @Test
    public void deleteSpuTest() {
        SpuInfoDeleteRequest request = new SpuInfoDeleteRequest();
        BaseRequestSimple baseRequestSimple = new BaseRequestSimple();
        baseRequestSimple.setChannelId(300);
        baseRequestSimple.setTenantId(1000338);
        request.setBaseInfo(baseRequestSimple);
        SpuInfoDeleteDTO spuInfoDeleteDTO = new SpuInfoDeleteDTO();
        spuInfoDeleteDTO.setCustomSpuId("1561644385722986516");
        spuInfoDeleteDTO.setStoreId(49869788);
        SpuKey spuKey = new SpuKey();
        SkuKey skuKey1 = new SkuKey();
        skuKey1.setCustomSkuId("1561644385735569502");

        SkuKey skuKey2 = new SkuKey();
        skuKey2.setCustomSkuId("1561650375914713132");
        spuKey.setSkus(Lists.newArrayList(skuKey1, skuKey2));
        spuInfoDeleteDTO.setSpuKey(spuKey);
        request.setParamList(Lists.newArrayList(spuInfoDeleteDTO));


        SkuSellStatusInfoRequest skuSellStatusInfoRequest = new SkuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple2 = new BaseRequestSimple();
        baseRequestSimple2.setTenantId(1000338);
        baseRequestSimple2.setChannelId(300);
        skuSellStatusInfoRequest.setBaseInfo(baseRequestSimple2);

        SkuSellStatusInfoDTO skuSellStatusInfoDTO = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO.setStoreId(49869788L);
        skuSellStatusInfoDTO.setSkuStatus(2);
        SkuIdDTO skuIdDTO1 = new SkuIdDTO();
        skuIdDTO1.setCustomSkuId("1561644385735569502");
        SkuIdDTO skuIdDTO2 = new SkuIdDTO();
        skuIdDTO2.setCustomSkuId("1561650375914713132");

        skuSellStatusInfoDTO.setSkuId(Lists.newArrayList(skuIdDTO1, skuIdDTO2));

        skuSellStatusInfoRequest.setParamList(Lists.newArrayList(skuSellStatusInfoDTO));

        ResultData resultData = new ResultData();
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(0);
        resultStatus.setMsg("");
        resultData.setStatus(resultStatus);
        ResultErrorSku resultErrorSku = new ResultErrorSku();
        resultErrorSku.setSkuId("1561644385735569502");
        resultErrorSku.setErrorMsg("修改失败");
        resultErrorSku.setErrorCode(1);

        ResultSuccessSku resultSuccessSku = new ResultSuccessSku();
        resultSuccessSku.setSkuId("1561650375914713132");
        resultSuccessSku.setSpuId("1561644385722986516");

        resultData.setErrorData(Lists.newArrayList(resultErrorSku));
        resultData.setSucData(Lists.newArrayList(resultSuccessSku));

        when(jddjChannelSkuService2.updateSkuSellStatus(skuSellStatusInfoRequest)).thenReturn(resultData);
        jddjChannelSpuService.deleteSpu(request);

    }

    @Test
    public void deleteSkuTest(){
        SkuInSpuInfoDeleteRequest request = new SkuInSpuInfoDeleteRequest();
        BaseRequestSimple baseRequestSimple = new BaseRequestSimple();
        baseRequestSimple.setChannelId(300);
        baseRequestSimple.setTenantId(1000338);
        request.setBaseInfo(baseRequestSimple);
        SkuInSpuInfoDeleteDTO sku = new SkuInSpuInfoDeleteDTO();
        sku.setCustomSkuId("1561644385735569502");
        sku.setCustomSpuId("1561644385722986516");
        sku.setStoreId(49869788);
        SkuInSpuInfoDeleteDTO sku2 = new SkuInSpuInfoDeleteDTO();
        sku2.setCustomSkuId("1561650375914713132");
        sku2.setCustomSpuId("1561644385722986516");
        sku2.setStoreId(49869788);

        SkuInSpuInfoDeleteDTO sku3 = new SkuInSpuInfoDeleteDTO();
        sku3.setCustomSkuId("1561650375914713131");
        sku3.setCustomSpuId("1561644385722986517");
        sku3.setStoreId(49869788);
        SkuInSpuInfoDeleteDTO sku4 = new SkuInSpuInfoDeleteDTO();
        sku4.setCustomSkuId("1561650375914713130");
        sku4.setCustomSpuId("1561644385722986518");
        sku4.setStoreId(49869789);
        request.setParamList(Lists.newArrayList(sku,sku2,sku3,sku4));



        SkuSellStatusInfoRequest skuSellStatusInfoRequest = new SkuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple2 = new BaseRequestSimple();
        baseRequestSimple2.setTenantId(1000338);
        baseRequestSimple2.setChannelId(300);
        skuSellStatusInfoRequest.setBaseInfo(baseRequestSimple2);
        SkuSellStatusInfoDTO skuSellStatusInfoDTO = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO.setStoreId(49869788L);
        skuSellStatusInfoDTO.setSkuStatus(2);
        SkuIdDTO skuIdDTO1 = new SkuIdDTO();
        skuIdDTO1.setCustomSkuId("1561644385735569502");
        SkuIdDTO skuIdDTO2 = new SkuIdDTO();
        skuIdDTO2.setCustomSkuId("1561650375914713132");
        skuSellStatusInfoDTO.setSkuId(Lists.newArrayList(skuIdDTO1, skuIdDTO2));
        skuSellStatusInfoRequest.setParamList(Lists.newArrayList(skuSellStatusInfoDTO));

        ResultData resultData = new ResultData();
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(0);
        resultStatus.setMsg("");
        resultData.setStatus(resultStatus);
        ResultErrorSku resultErrorSku = new ResultErrorSku();
        resultErrorSku.setSkuId("1561644385735569502");
        resultErrorSku.setErrorMsg("修改失败");
        resultErrorSku.setErrorCode(1);
        ResultSuccessSku resultSuccessSku = new ResultSuccessSku();
        resultSuccessSku.setSkuId("1561650375914713132");
        resultSuccessSku.setSpuId("1561644385722986516");
        resultData.setErrorData(Lists.newArrayList(resultErrorSku));
        resultData.setSucData(Lists.newArrayList(resultSuccessSku));


        SkuSellStatusInfoRequest skuSellStatusInfoRequest2 = new SkuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple3 = new BaseRequestSimple();
        baseRequestSimple3.setTenantId(1000338);
        baseRequestSimple3.setChannelId(300);
        skuSellStatusInfoRequest2.setBaseInfo(baseRequestSimple3);
        SkuSellStatusInfoDTO skuSellStatusInfoDTO2 = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO2.setStoreId(49869788L);
        skuSellStatusInfoDTO2.setSkuStatus(2);
        SkuIdDTO skuIdDTO3 = new SkuIdDTO();
        skuIdDTO3.setCustomSkuId("1561650375914713131");
        skuSellStatusInfoDTO2.setSkuId(Lists.newArrayList(skuIdDTO3));
        skuSellStatusInfoRequest2.setParamList(Lists.newArrayList(skuSellStatusInfoDTO2));

        ResultData resultData2 = new ResultData();
        ResultStatus resultStatus2 = new ResultStatus();
        resultStatus2.setCode(0);
        resultStatus2.setMsg("");
        resultData2.setStatus(resultStatus2);
        ResultSuccessSku resultSuccessSku2 = new ResultSuccessSku();
        resultSuccessSku2.setSkuId("1561650375914713132");
        resultSuccessSku2.setSpuId("1561644385722986516");
        resultData2.setErrorData(Lists.newArrayList());
        resultData2.setSucData(Lists.newArrayList(resultSuccessSku2));

        SkuSellStatusInfoRequest skuSellStatusInfoRequest3 = new SkuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple4 = new BaseRequestSimple();
        baseRequestSimple4.setTenantId(1000338);
        baseRequestSimple4.setChannelId(300);
        skuSellStatusInfoRequest3.setBaseInfo(baseRequestSimple4);
        SkuSellStatusInfoDTO skuSellStatusInfoDTO3 = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO3.setStoreId(49869789);
        skuSellStatusInfoDTO3.setSkuStatus(2);
        SkuIdDTO skuIdDTO4 = new SkuIdDTO();
        skuIdDTO4.setCustomSkuId("1561650375914713130");
        skuSellStatusInfoDTO3.setSkuId(Lists.newArrayList(skuIdDTO4));
        skuSellStatusInfoRequest3.setParamList(Lists.newArrayList(skuSellStatusInfoDTO3));

        ResultData resultData3 = new ResultData();
        ResultStatus resultStatus3 = new ResultStatus();
        resultStatus3.setCode(0);
        resultStatus3.setMsg("");
        resultData3.setStatus(resultStatus3);
        ResultErrorSku resultErrorSku3 = new ResultErrorSku();
        resultErrorSku3.setSkuId("1561644385735569502");
        resultErrorSku3.setErrorMsg("修改失败");
        resultErrorSku3.setErrorCode(1);
        resultData3.setErrorData(Lists.newArrayList(resultErrorSku3));
        resultData3.setSucData(Lists.newArrayList());


        when(jddjChannelSkuService2.updateSkuSellStatus(skuSellStatusInfoRequest)).thenReturn(resultData);
        when(jddjChannelSkuService2.updateSkuSellStatus(skuSellStatusInfoRequest2)).thenReturn(resultData2);
        when(jddjChannelSkuService2.updateSkuSellStatus(skuSellStatusInfoRequest3)).thenReturn(resultData3);

        jddjChannelSpuService.deleteSku(request);
    }

    @Test
    public void updateSpuSellStatusTest() {
        SpuSellStatusInfoRequest request = new SpuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple = new BaseRequestSimple();
        baseRequestSimple.setChannelId(300);
        baseRequestSimple.setTenantId(1000338);
        request.setBaseInfo(baseRequestSimple);


        SpuInfoSellStatusDTO  spuInfoSellStatusDTO = new SpuInfoSellStatusDTO();
        spuInfoSellStatusDTO.setSpuStatus(1);
        SpuKey spuKey = new SpuKey();
        SkuKey skuKey = new SkuKey();
        skuKey.setCustomSkuId("1561644385735569502");
        SkuKey skuKey2 = new SkuKey();
        skuKey2.setCustomSkuId("1561650375914713132");
        spuKey.setSkus(Lists.newArrayList(skuKey,skuKey2));
        spuKey.setCustomSpuId("1561644385722986516");
        SpuKey spuKey2 = new SpuKey();
        SkuKey skuKey3 = new SkuKey();
        skuKey3.setCustomSkuId("1561650375914713131");
        spuKey2.setSkus(Lists.newArrayList(skuKey3));
        spuKey2.setCustomSpuId("1561644385722986517");
        spuInfoSellStatusDTO.setSpuKeys(Lists.newArrayList(spuKey,spuKey2));
        spuInfoSellStatusDTO.setStoreId(49869788);


        SpuInfoSellStatusDTO  spuInfoSellStatusDTO2 = new SpuInfoSellStatusDTO();
        spuInfoSellStatusDTO2.setSpuStatus(2);
        SpuKey spuKey3 = new SpuKey();
        SkuKey skuKey4 = new SkuKey();
        skuKey4.setCustomSkuId("1561650375914713130");
        spuKey3.setCustomSpuId("1561644385722986518");
        spuKey3.setSkus(Lists.newArrayList(skuKey4));
        spuInfoSellStatusDTO2.setSpuKeys(Lists.newArrayList(spuKey3));
        spuInfoSellStatusDTO2.setStoreId(49869789);

        request.setParamList(Lists.newArrayList(spuInfoSellStatusDTO,spuInfoSellStatusDTO2));



        SkuSellStatusInfoRequest skuSellStatusInfoRequest = new SkuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple2 = new BaseRequestSimple();
        baseRequestSimple2.setTenantId(1000338);
        baseRequestSimple2.setChannelId(300);
        skuSellStatusInfoRequest.setBaseInfo(baseRequestSimple2);
        SkuSellStatusInfoDTO skuSellStatusInfoDTO = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO.setStoreId(49869788L);
        skuSellStatusInfoDTO.setSkuStatus(1);
        SkuIdDTO skuIdDTO1 = new SkuIdDTO();
        skuIdDTO1.setCustomSkuId("1561644385735569502");
        SkuIdDTO skuIdDTO2 = new SkuIdDTO();
        skuIdDTO2.setCustomSkuId("1561650375914713132");
        skuSellStatusInfoDTO.setSkuId(Lists.newArrayList(skuIdDTO1, skuIdDTO2));
        skuSellStatusInfoRequest.setParamList(Lists.newArrayList(skuSellStatusInfoDTO));

        ResultData resultData = new ResultData();
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(0);
        resultStatus.setMsg("");
        resultData.setStatus(resultStatus);
        ResultErrorSku resultErrorSku = new ResultErrorSku();
        resultErrorSku.setSkuId("1561644385735569502");
        resultErrorSku.setErrorMsg("修改失败");
        resultErrorSku.setErrorCode(1);
        ResultSuccessSku resultSuccessSku = new ResultSuccessSku();
        resultSuccessSku.setSkuId("1561650375914713132");
        resultSuccessSku.setSpuId("1561644385722986516");
        resultData.setErrorData(Lists.newArrayList(resultErrorSku));
        resultData.setSucData(Lists.newArrayList(resultSuccessSku));

        SkuSellStatusInfoRequest skuSellStatusInfoRequest2 = new SkuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple3 = new BaseRequestSimple();
        baseRequestSimple3.setTenantId(1000338);
        baseRequestSimple3.setChannelId(300);
        skuSellStatusInfoRequest2.setBaseInfo(baseRequestSimple3);
        SkuSellStatusInfoDTO skuSellStatusInfoDTO2 = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO2.setStoreId(49869788L);
        skuSellStatusInfoDTO2.setSkuStatus(1);
        SkuIdDTO skuIdDTO3 = new SkuIdDTO();
        skuIdDTO3.setCustomSkuId("1561650375914713131");
        skuSellStatusInfoDTO2.setSkuId(Lists.newArrayList(skuIdDTO3));
        skuSellStatusInfoRequest2.setParamList(Lists.newArrayList(skuSellStatusInfoDTO2));

        ResultData resultData2 = new ResultData();
        ResultStatus resultStatus2 = new ResultStatus();
        resultStatus2.setCode(0);
        resultStatus2.setMsg("");
        resultData2.setStatus(resultStatus2);
        ResultSuccessSku resultSuccessSku2 = new ResultSuccessSku();
        resultSuccessSku2.setSkuId("1561650375914713131");
        resultSuccessSku2.setSpuId("1561644385722986517");
        resultData2.setErrorData(Lists.newArrayList());
        resultData2.setSucData(Lists.newArrayList(resultSuccessSku2));

        SkuSellStatusInfoRequest skuSellStatusInfoRequest3 = new SkuSellStatusInfoRequest();
        BaseRequestSimple baseRequestSimple4 = new BaseRequestSimple();
        baseRequestSimple4.setTenantId(1000338);
        baseRequestSimple4.setChannelId(300);
        skuSellStatusInfoRequest3.setBaseInfo(baseRequestSimple4);
        SkuSellStatusInfoDTO skuSellStatusInfoDTO3 = new SkuSellStatusInfoDTO();
        skuSellStatusInfoDTO3.setStoreId(49869789);
        skuSellStatusInfoDTO3.setSkuStatus(2);
        SkuIdDTO skuIdDTO4 = new SkuIdDTO();
        skuIdDTO4.setCustomSkuId("1561650375914713130");
        skuSellStatusInfoDTO3.setSkuId(Lists.newArrayList(skuIdDTO4));
        skuSellStatusInfoRequest3.setParamList(Lists.newArrayList(skuSellStatusInfoDTO3));

        ResultData resultData3 = new ResultData();
        ResultStatus resultStatus3 = new ResultStatus();
        resultStatus3.setCode(0);
        resultStatus3.setMsg("");
        resultData3.setStatus(resultStatus3);
        ResultErrorSku resultErrorSku3 = new ResultErrorSku();
        resultErrorSku3.setSkuId("1561650375914713130");
        resultErrorSku3.setSpuId("1561644385722986517");
        resultErrorSku3.setErrorMsg("修改失败");
        resultErrorSku3.setErrorCode(1);
        resultData3.setErrorData(Lists.newArrayList(resultErrorSku3));
        resultData3.setSucData(Lists.newArrayList());


        when(jddjChannelSkuService2.updateSkuSellStatus(skuSellStatusInfoRequest)).thenReturn(resultData);
        when(jddjChannelSkuService2.updateSkuSellStatus(skuSellStatusInfoRequest2)).thenReturn(resultData2);
        when(jddjChannelSkuService2.updateSkuSellStatus(skuSellStatusInfoRequest3)).thenReturn(resultData3);

        jddjChannelSpuService.updateSpuSellStatus(request);
    }
}
