package com.sankuai.meituan.shangou.empower.ocms.channel.utils;

import com.meituan.shangou.saas.common.enums.AuditTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.model.AuditParams;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.FarmPaoTuiLaunchDeliveryRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/4
 */
public class FarmPaoTuiSignUtilsTest {

	@Test
	public void name() {
		FarmPaoTuiLaunchDeliveryRequest request = new FarmPaoTuiLaunchDeliveryRequest("CBEDD1D2E1472081558122A9B231F1DE", 1622796299L, "1400735278565482530", "7", null, null,null);
		assertEquals("CBEDD1D2E1472081558122A9B231F1DE", FarmPaoTuiSignUtils.generateSignatureFromRequest(request, "83e5999c630a516dda3248232991d265"));
	}

	@Test
	public void testAuditMap() {
		AuditParams auditParams = AuditParams.builder().build();
		DrunkHorseAuditTypeUtils.judgeAuditType(auditParams);
	}


}
