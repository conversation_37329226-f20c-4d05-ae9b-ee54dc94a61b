package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.txd;

import java.util.ArrayList;
import java.util.List;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;

import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.internal.mapping.ApiField;
import com.taobao.api.request.AlibabaCdcTxdBatchSkuPriceUpdateRequest;
import com.taobao.api.request.AlibabaWdkSkuAddRequest;
import com.taobao.api.request.AlibabaWdkSkuCategoryQueryRequest;
import com.taobao.api.request.AlibabaWdkSkuQueryRequest;
import com.taobao.api.request.AlibabaWdkSkuStoreskuScrollQueryRequest;
import com.taobao.api.request.AlibabaWdkSkuUpdateRequest;
import com.taobao.api.response.AlibabaCdcTxdBatchSkuPriceUpdateResponse;
import com.taobao.api.response.AlibabaWdkSkuAddResponse;
import com.taobao.api.response.AlibabaWdkSkuCategoryQueryResponse;
import com.taobao.api.response.AlibabaWdkSkuQueryResponse;
import com.taobao.api.response.AlibabaWdkSkuStoreskuScrollQueryResponse;
import com.taobao.api.response.AlibabaWdkSkuUpdateResponse;

import lombok.Getter;
import lombok.Setter;

public class TxdClientTest {

    public static final String HTTPS_ECO_TAOBAO_COM_ROUTER_REST = "https://eco.taobao.com/router/rest";
    public static final String APP_KEY = "34635640";
    public static final String APP_SECRET = "3b7f8007741d7faa16865f45c58edb11";
    public static final String SESSION = "50002J01018rYHElZzGVeiqiUrEefh1502a672v3mTVhnfwK1ETSol3nxP16EDZMV0Q7";

    @Test
    public void testSkuQuery() {
        try {
            TaobaoClient client = new DefaultTaobaoClient(HTTPS_ECO_TAOBAO_COM_ROUTER_REST, APP_KEY, APP_SECRET);
            AlibabaWdkSkuQueryRequest req = new AlibabaWdkSkuQueryRequest();
            AlibabaWdkSkuQueryRequest.SkuQueryDo obj1 = new AlibabaWdkSkuQueryRequest.SkuQueryDo();
            obj1.setOuCode("TBXSDDJC001");
            obj1.setSkuCodes(Lists.newArrayList("213344_25013344211145"));
            req.setParam(obj1);
            AlibabaWdkSkuQueryResponse rsp = client.execute(req, SESSION);
            System.out.println(rsp.getBody());
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testStoreSkuQuery() {
        try {
            TaobaoClient client = new DefaultTaobaoClient(HTTPS_ECO_TAOBAO_COM_ROUTER_REST, APP_KEY, APP_SECRET);
            AlibabaWdkSkuStoreskuScrollQueryRequest req = new AlibabaWdkSkuStoreskuScrollQueryRequest();
            req.setStoreId("TBXSDDJC002");
            req.setScrollId(null);
            AlibabaWdkSkuStoreskuScrollQueryResponse rsp = client.execute(req, SESSION);
            System.out.println(rsp.getResult().getScrollId());


            req.setScrollId(rsp.getResult().getScrollId());
            rsp = client.execute(req, SESSION);
            System.out.println(rsp.getResult().getScrollId());
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testCategoryQuery() {
        try {
            TaobaoClient client = new DefaultTaobaoClient(HTTPS_ECO_TAOBAO_COM_ROUTER_REST, APP_KEY, APP_SECRET);
            AlibabaWdkSkuCategoryQueryRequest req = new AlibabaWdkSkuCategoryQueryRequest();
            AlibabaWdkSkuCategoryQueryRequest.CategoryDo obj1 = new AlibabaWdkSkuCategoryQueryRequest.CategoryDo();
            obj1.setCode("");
            req.setParam(obj1);
            AlibabaWdkSkuCategoryQueryResponse rsp = client.execute(req, SESSION);
            System.out.println(rsp.getBody());
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testAddSku() {
        try {
            TaobaoClient client = new DefaultTaobaoClient(HTTPS_ECO_TAOBAO_COM_ROUTER_REST, APP_KEY, APP_SECRET);
            AlibabaWdkSkuAddRequest req = new AlibabaWdkSkuAddRequest();
            List<AlibabaWdkSkuAddRequest.SkuDo> params = new ArrayList<AlibabaWdkSkuAddRequest.SkuDo>();
            AlibabaWdkSkuAddRequest.SkuDo skuDo = new AlibabaWdkSkuAddRequest.SkuDo();
            params.add(skuDo);

            skuDo.setOuCode("TBXSDDJC003");
            skuDo.setSkuCode("292662841");
            skuDo.setBarcodes("653146636353299");
            skuDo.setSkuPrice("123");
            skuDo.setCategoryCode("setup_default_1");

            req.setParamList(params);
            AlibabaWdkSkuAddResponse rsp = client.execute(req, SESSION);
            System.out.println(rsp.getBody());
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testUpdateSku() {
        try {
            TaobaoClient client = new DefaultTaobaoClient(HTTPS_ECO_TAOBAO_COM_ROUTER_REST, APP_KEY, APP_SECRET);
            AlibabaWdkSkuUpdateRequest req = new AlibabaWdkSkuUpdateRequest();
            List<AlibabaWdkSkuUpdateRequest.SkuDo> params = new ArrayList<AlibabaWdkSkuUpdateRequest.SkuDo>();
            AlibabaWdkSkuUpdateRequest.SkuDo skuDo = new AlibabaWdkSkuUpdateRequest.SkuDo();
            params.add(skuDo);

            skuDo.setOuCode("TBXSDDJC002");
            skuDo.setSkuCode("295912733");
            skuDo.setBarcodes("6700673726328");
            skuDo.setMemberPrice("123");
            req.setParamList(params);
            AlibabaWdkSkuUpdateResponse rsp = client.execute(req, SESSION);
            System.out.println(rsp.getBody());
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testUpdatePrice() {
        try {
            TaobaoClient client = new DefaultTaobaoClient(HTTPS_ECO_TAOBAO_COM_ROUTER_REST, APP_KEY, APP_SECRET);

            AlibabaCdcTxdBatchSkuPriceUpdateRequest aliRequest = new AlibabaCdcTxdBatchSkuPriceUpdateRequest();
            List<AlibabaCdcTxdBatchSkuPriceUpdateRequest.SkuPriceDO> skuPriceDOS = new ArrayList();
            skuPriceDOS.add(new SkuPriceDOExt() {{
                setStoreId("TBXSDDJC002");
                setSkuCode("295912733");
                setSalePrice("34.6");
                setMemberPrice("33.6");
            }});
            aliRequest.setParamList(skuPriceDOS);

            AlibabaCdcTxdBatchSkuPriceUpdateResponse rsp = client.execute(aliRequest, SESSION);
            System.out.println(rsp.getBody());
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Getter
    @Setter
    public static class SkuPriceDOExt extends  AlibabaCdcTxdBatchSkuPriceUpdateRequest.SkuPriceDO {
        @ApiField("member_price")
        private String memberPrice;
    }
}
