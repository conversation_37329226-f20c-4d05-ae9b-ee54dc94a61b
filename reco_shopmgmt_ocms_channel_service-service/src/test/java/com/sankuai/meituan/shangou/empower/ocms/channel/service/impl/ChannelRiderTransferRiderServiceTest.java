/**
 * meituan.com Inc. Copyright (c) 2010-2024 All Rights Reserved.
 */
package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.dianping.lion.client.Lion;
import com.doudian.open.utils.JsonUtil;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.platform.common.util.AssertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MaltFarmAggRiderTransferDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.OrderRiderTransferRiderNotifyDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelOrderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.health.HealthChannelRiderTransferRiderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelOrderThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.TmsThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.LogisticsStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DeliveryOrderCheckWrapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.FarmPaoTuiSignUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.HttpClientUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.UrlUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version :ChannelRiderTransferRiderServiceText.java v1.0 2024/6/25 10:52 wb_wenke Exp $
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MccConfigUtil.class, Lion.class, HttpClientUtil.class, FarmPaoTuiSignUtils.class})
@SuppressStaticInitializationFor("com.sankuai.meituan.shangou.empower.ocms.channel.utils.HttpClientUtil")
@PowerMockRunnerDelegate(SpringRunner.class)
@PowerMockIgnore({"javax.management.*", "javax.script.*"})
public class ChannelRiderTransferRiderServiceTest {

    @InjectMocks
    private HealthChannelRiderTransferRiderService healthChannelRiderTransferRiderService;

    @Mock
    private CommonLogger log = new CommonLogger();

    @Mock
    private TenantService tenantService;

    @Mock
    private ChannelOrderThriftServiceProxy channelOrderThriftServiceProxy;

    @Mock
    private TmsThriftServiceProxy tmsThriftServiceProxy;

    @Mock
    private DeliveryOrderCheckWrapper deliveryOrderCheckWrapper;

    @Mock
    private BizOrderThriftService bizOrderThriftServiceClient;

    @Mock
    private ChannelOrderService healthChannelOrderService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(MccConfigUtil.class);
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.mockStatic(FarmPaoTuiSignUtils.class);
    }

    /**
     * 测试非药品成人无人仓租户直接返回null的场景
     */
    @Test
    public void testGetMaltFarmAggRiderTransferDTO_NonMedicineAdultUnmanWarehouse() {
        // arrange
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.MT_MEDICINE;
        OrderNotifyRequest request = new OrderNotifyRequest();
        Long tenantId = 1L;

        when(tenantService.isMedicineAdultUnmanWarehouse(tenantId)).thenReturn(false);

        // act
        MaltFarmAggRiderTransferDTO result = healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(channelTypeEnum, request, tenantId,false);

        // assert
        AssertUtil.assertTrue(null == result, "result should be null");
    }

    /**
     * 测试租户不在灰度列表中直接返回null的场景
     */
    @Test
    public void testGetMaltFarmAggRiderTransferDTO_TenantNotInGrayList() {
        // arrange
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.MT_MEDICINE;
        OrderNotifyRequest request = new OrderNotifyRequest();
        Long tenantId = 2L;

        when(tenantService.isMedicineAdultUnmanWarehouse(tenantId)).thenReturn(true);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayTenantSwitch()).thenReturn(true);
        List<Long> grayTenants = new ArrayList<>();
        grayTenants.add(1L);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferTiderGrayTenantIds()).thenReturn(grayTenants);

        // act
        MaltFarmAggRiderTransferDTO result = healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(channelTypeEnum, request, tenantId,false);

        // assert
        AssertUtil.assertTrue(null == result, "result should be null");
    }

    /**
     * 测试查询订单信息为空直接返回null的场景
     */
    @Test
    public void testGetMaltFarmAggRiderTransferDTO_OrderInfoNotPresent() {
        // arrange
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.MT_MEDICINE;
        OrderNotifyRequest request = new OrderNotifyRequest();
        setRequestBody(request);
        Long tenantId = 1L;

        when(tenantService.isMedicineAdultUnmanWarehouse(tenantId)).thenReturn(true);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayTenantSwitch()).thenReturn(false);
        when(channelOrderThriftServiceProxy.queryOrderInfo(any())).thenReturn(Optional.empty());

        // act
        MaltFarmAggRiderTransferDTO result = healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(channelTypeEnum, request, tenantId,false);

        // assert
        AssertUtil.assertTrue(null == result, "result should be null");
    }

    private void setRequestBody(OrderNotifyRequest request) {
        OrderRiderTransferRiderNotifyDTO notifyDTO = new OrderRiderTransferRiderNotifyDTO();
        notifyDTO.setApp_poi_code("1");
        notifyDTO.setWm_order_id_view("123456");
        notifyDTO.setRider_name("张三（测试）");
        notifyDTO.setRider_phone("189123456789_1234");
        // 真实请求中的body是URL编码后的字符串
        String encodeBody = UrlUtil.urlEnCodeSafe(JsonUtil.toJson(notifyDTO));
        String decodeBody = UrlUtil.urlDecodeSafeBean(encodeBody);
        request.setBody(decodeBody);
    }

    /**
     * 测试查询运单信息为空直接返回null的场景
     */
    @Test
    public void testGetMaltFarmAggRiderTransferDTO_DeliveryOrdersNotPresent() {
        // arrange
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.MT_MEDICINE;
        OrderNotifyRequest request = new OrderNotifyRequest();
        setRequestBody(request);

        Long tenantId = 1L;
        BizOrderModel bizOrderModel = new BizOrderModel();
        bizOrderModel.setOrderId(1L);

        when(tenantService.isMedicineAdultUnmanWarehouse(tenantId)).thenReturn(true);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayTenantSwitch()).thenReturn(false);
        when(channelOrderThriftServiceProxy.queryOrderInfo(any())).thenReturn(Optional.of(bizOrderModel));
        when(tmsThriftServiceProxy.queryDeliveryOrderByOrderId(bizOrderModel.getOrderId())).thenReturn(Optional.empty());

        // act
        MaltFarmAggRiderTransferDTO result = healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(channelTypeEnum, request, tenantId,false);

        // assert
        AssertUtil.assertTrue(null == result, "result should be null");
    }


    /**
     * 测试非跑腿运单校验失败直接返回null的场景
     */
    @Test
    public void testGetMaltFarmAggRiderTransferDTO_DeliveryOrdersNotValid() {
        // arrange
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.MT_MEDICINE;
        OrderNotifyRequest request = new OrderNotifyRequest();
        setRequestBody(request);
        Long tenantId = 1L;
        BizOrderModel bizOrderModel = new BizOrderModel();
        bizOrderModel.setOrderId(1L);
        List<TDeliveryOrder> tDeliveryOrders = new ArrayList<>();

        when(tenantService.isMedicineAdultUnmanWarehouse(tenantId)).thenReturn(true);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayTenantSwitch()).thenReturn(false);
        when(channelOrderThriftServiceProxy.queryOrderInfo(any())).thenReturn(Optional.of(bizOrderModel));
        when(tmsThriftServiceProxy.queryDeliveryOrderByOrderId(bizOrderModel.getOrderId())).thenReturn(Optional.of(tDeliveryOrders));
        when(deliveryOrderCheckWrapper.checkValidPaoTuiOrder(tDeliveryOrders)).thenReturn(false);

        // act
        MaltFarmAggRiderTransferDTO result = healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(channelTypeEnum, request, tenantId,false);

        // assert
        AssertUtil.assertTrue(null == result, "result should be null");
    }

    /**
     * 测试租户在灰度列表中且为跑腿运单校验成功的场景
     */
    @Test
    public void testGetMaltFarmAggRiderTransferDTO_TenantInGrayListAndDeliveryOrdersValid() {
        // arrange
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.MT_MEDICINE;
        OrderNotifyRequest request = new OrderNotifyRequest();
        request.setChannelCode(channelTypeEnum.getAbbrev());
        setRequestBody(request);
        Long tenantId = 1L;
        BizOrderModel bizOrderModel = new BizOrderModel();
        bizOrderModel.setOrderId(1L);
        bizOrderModel.setShopId(1L);
        List<TDeliveryOrder> tDeliveryOrders = new ArrayList<>();
        TDeliveryOrder tDeliveryOrder = new TDeliveryOrder();
        tDeliveryOrder.setOrderId(1L);
        tDeliveryOrder.setActiveStatus(true);
        tDeliveryOrders.add(tDeliveryOrder);

        when(tenantService.isMedicineAdultUnmanWarehouse(tenantId)).thenReturn(true);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayTenantSwitch()).thenReturn(true);
        List<Long> grayTenants = new ArrayList<>();
        grayTenants.add(1L);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferTiderGrayTenantIds()).thenReturn(grayTenants);
        when(channelOrderThriftServiceProxy.queryOrderInfo(any())).thenReturn(Optional.of(bizOrderModel));
        when(tmsThriftServiceProxy.queryDeliveryOrderByOrderId(bizOrderModel.getOrderId())).thenReturn(Optional.of(tDeliveryOrders));
        when(deliveryOrderCheckWrapper.checkValidPaoTuiOrder(tDeliveryOrders)).thenReturn(true);
        when(deliveryOrderCheckWrapper.getDeliveryPlatform(tDeliveryOrders)).thenReturn(Lists.newArrayList(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM));
        GetLogisticsStatusResult getLogisticsStatusResult = new GetLogisticsStatusResult();
        LogisticsStatusDTO logisticsStatusDTO = new LogisticsStatusDTO();
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ResultCode.SUCCESS.getCode());
        getLogisticsStatusResult.setStatus(resultStatus);
        logisticsStatusDTO.setStatus(1);
        logisticsStatusDTO.setRiderName("张三（测试）");
        logisticsStatusDTO.setRiderPhone("189123456789_1234");
        getLogisticsStatusResult.setLogisticsStatus(logisticsStatusDTO);
        when(healthChannelOrderService.getLogisticsStatus(any())).thenReturn(getLogisticsStatusResult);
        when(MccConfigUtil.getMtRiderTransferRiderFillRiderPhoneSwitch()).thenReturn(true);
        when(FarmPaoTuiSignUtils.generateSignatureFromRequest(any())).thenReturn("signStr");

        // act
        MaltFarmAggRiderTransferDTO result = healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(channelTypeEnum, request, tenantId,true);

        // assert
        AssertUtil.assertTrue(null != result, "result should be not null");
        AssertUtil.assertTrue(result.getDispatcherName().equals("张三（测试）"), "result size should be 1");
        AssertUtil.assertTrue(result.getDispatcherMobile().equals("189123456789_1234"), "result orderId should be 1");
    }

    /**
     * 测试门店不在灰度列表中直接返回null的场景
     */
    @Test
    public void testGetMaltFarmAggRiderTransferDTO_ShopNotInGrayList() {
        // arrange
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.MT_MEDICINE;
        OrderNotifyRequest request = new OrderNotifyRequest();
        setRequestBody(request);
        Long tenantId = 1L;
        BizOrderModel bizOrderModel = new BizOrderModel();
        bizOrderModel.setOrderId(1L);
        bizOrderModel.setShopId(2L);

        when(tenantService.isMedicineAdultUnmanWarehouse(tenantId)).thenReturn(true);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayTenantSwitch()).thenReturn(true);
        List<Long> grayTenants = new ArrayList<>();
        grayTenants.add(1L);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferTiderGrayTenantIds()).thenReturn(grayTenants);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayPoiSwitch()).thenReturn(true);
        List<Long> grayPoiId = new ArrayList<>();
        grayPoiId.add(1L);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayPoiIds()).thenReturn(grayPoiId);
        when(channelOrderThriftServiceProxy.queryOrderInfo(any())).thenReturn(Optional.of(bizOrderModel));

        // act
        MaltFarmAggRiderTransferDTO result = healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(channelTypeEnum, request, tenantId,false);

        // assert
        AssertUtil.assertTrue(null == result, "result should be null");
    }

    /**
     * 测试门店在灰度列表中且运单校验成功的场景
     */
    @Test
    public void testGetMaltFarmAggRiderTransferDTO_ShopInGrayListAndDeliveryOrdersValid() {
        // arrange
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.MT_MEDICINE;
        OrderNotifyRequest request = new OrderNotifyRequest();
        request.setChannelCode(channelTypeEnum.getAbbrev());
        setRequestBody(request);
        Long tenantId = 1L;
        BizOrderModel bizOrderModel = new BizOrderModel();
        bizOrderModel.setOrderId(1L);
        bizOrderModel.setShopId(1L);
        List<TDeliveryOrder> tDeliveryOrders = new ArrayList<>();
        TDeliveryOrder tDeliveryOrder = new TDeliveryOrder();
        tDeliveryOrder.setOrderId(1L);
        tDeliveryOrder.setActiveStatus(true);
        tDeliveryOrders.add(tDeliveryOrder);

        when(tenantService.isMedicineAdultUnmanWarehouse(tenantId)).thenReturn(true);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayTenantSwitch()).thenReturn(true);
        List<Long> grayTenants = new ArrayList<>();
        grayTenants.add(1L);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferTiderGrayTenantIds()).thenReturn(grayTenants);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayPoiSwitch()).thenReturn(true);
        List<Long> grayPoiId = new ArrayList<>();
        grayPoiId.add(1L);
        when(MccConfigUtil.getDeliveryPaotuiRiderTransferRiderGrayPoiIds()).thenReturn(grayPoiId);
        when(channelOrderThriftServiceProxy.queryOrderInfo(any())).thenReturn(Optional.of(bizOrderModel));
        when(tmsThriftServiceProxy.queryDeliveryOrderByOrderId(bizOrderModel.getOrderId())).thenReturn(Optional.of(tDeliveryOrders));
        when(deliveryOrderCheckWrapper.checkValidPaoTuiOrder(tDeliveryOrders)).thenReturn(true);
        when(deliveryOrderCheckWrapper.getDeliveryPlatform(tDeliveryOrders)).thenReturn(Lists.newArrayList(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM));
        GetLogisticsStatusResult getLogisticsStatusResult = new GetLogisticsStatusResult();
        LogisticsStatusDTO logisticsStatusDTO = new LogisticsStatusDTO();
        logisticsStatusDTO.setStatus(1);
        logisticsStatusDTO.setRiderName("张三（测试）");
        logisticsStatusDTO.setRiderPhone("189123456789_1234");
        getLogisticsStatusResult.setLogisticsStatus(logisticsStatusDTO);
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ResultCode.SUCCESS.getCode());
        getLogisticsStatusResult.setStatus(resultStatus);
        when(healthChannelOrderService.getLogisticsStatus(any())).thenReturn(getLogisticsStatusResult);
        when(MccConfigUtil.getMtRiderTransferRiderFillRiderPhoneSwitch()).thenReturn(true);
        when(FarmPaoTuiSignUtils.generateSignatureFromRequest(any())).thenReturn("signStr");

        // act
        MaltFarmAggRiderTransferDTO result = healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(channelTypeEnum, request, tenantId,true);

        // assert
        AssertUtil.assertTrue(null != result, "result should be not null");
        AssertUtil.assertTrue(result.getDispatcherName().equals("张三（测试）"), "result size should be 1");
        AssertUtil.assertTrue(result.getDispatcherMobile().equals("189123456789_1234"), "result orderId should be 1");
    }
}