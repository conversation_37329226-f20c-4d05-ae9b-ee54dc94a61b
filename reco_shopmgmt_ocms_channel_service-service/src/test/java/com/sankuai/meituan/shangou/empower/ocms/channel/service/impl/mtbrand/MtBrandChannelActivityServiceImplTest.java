package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.CanModifyPriceResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.CopChannelStoreServiceImpl;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ActTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelActivityInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelPoiParamInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryCanModifyPriceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QueryCanModifyPriceResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.StoreCustomSpuKey;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * @author:huchangwu
 * @date: 2019/1/22
 * @time: 下午4:26
 */
@RunWith(MockitoJUnitRunner.class)
public class MtBrandChannelActivityServiceImplTest {

    @InjectMocks
    private MtBrandChannelActivityServiceImpl mtChannelActivityService;

    @Mock
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Mock
    private MtConverterService mtConverterService;

    @Mock(name = "mtBrandChannelCommonService")
    private MtBrandChannelCommonServiceImpl mtBrandChannelBaseService;

    @Mock
    private CopChannelStoreServiceImpl copChannelStoreService;

    @Mock
    private CommonLogger log;

    //    @Mock(name = "mtActivityThreadPool")
    //    private ExecutorService executorService = Executors.newSingleThreadExecutor();

    @Test
    public void saveActivityTest() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        long storeId = 4957025;
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(1000045)
                .setChannelId(100)
                .setStoreIdList(Lists.newArrayList(storeId));

        ChannelActivityInfo channelActivityInfo = new ChannelActivityInfo();
        channelActivityInfo.setStoreId(storeId);

        List<ChannelActivityInfo> activityList = Lists.newArrayList(channelActivityInfo);

        when(mtBrandChannelBaseService.queryPoiActivity(any())).thenReturn(Lists.newArrayList());

        when(mtBrandChannelGateService.sendPost(any(), any(), any())).thenReturn(Maps.newHashMap());

        //新建活动
        mtChannelActivityService.saveActivity(ActTypeEnum.CREATE, baseRequest, activityList);

    }

    @Test
    public void batchDeleteTest() {
        long storeId = 4957025;
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(1000045)
                .setChannelId(100)
                .setStoreIdList(Lists.newArrayList(storeId));

        ChannelPoiParamInfo channelPoiParamInfo = new ChannelPoiParamInfo();
        List<ChannelPoiParamInfo> channelPoiParamInfoList = Lists.newArrayList(channelPoiParamInfo);

        mtChannelActivityService.batchDelete(baseRequest, channelPoiParamInfoList);
    }

    //    String successData = "{\"data\":[{\"app_poi_code\":\"604348\",\"result_list\":[{\"app_spu_code\":\"mtcode_1342009013742624798\",\"can_modify_price\":1}]},{\"app_poi_code\":\"685842\",\"result_list\":[{\"app_spu_code\":\"mtcode_1372446607689146420\",\"can_modify_price\":0}]}],\"result_code\":1}";
    String successData = "{\"data\":[{\"app_poi_code\":\"604348\",\"result_list\":[{\"app_food_code\":\"mtcode_1342009013742624798\",\"can_modify_price\":1}]},{\"app_poi_code\":\"685842\",\"result_list\":[{\"app_food_code\":\"mtcode_1372446607689146420\",\"can_modify_price\":0}]}],\"result_code\":1}";

    //    String failedData = "{\"data\":\"ng\",\"error_list\":[{\"code\":3022,\"msg\":\"不存在此商品\",\"app_poi_code\":\"678t678\",\"app_spu_code\":\"901374262479800\"}],\"result_code\":3}";
    String failedData = "{\"data\":\"ng\",\"error_list\":[{\"code\":3022,\"msg\":\"不存在此商品\",\"app_poi_code\":\"678t678\",\"app_food_code\":\"901374262479800\"}],\"result_code\":3}";

    @Test()
    public void querySpuCanModifyPriceTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        StoreCustomSpuKey storeCustomSpuKey = new StoreCustomSpuKey();
        storeCustomSpuKey.setStoreId(storeId);
        storeCustomSpuKey.setCustomSpuIds(Sets.newHashSet("mtcode_1342009013742624798", "mtcode_1342009013742624799"));

        StoreCustomSpuKey storeCustomSpuKey2 = new StoreCustomSpuKey();
        storeCustomSpuKey2.setStoreId(storeId + 2);
        storeCustomSpuKey2.setCustomSpuIds(Sets.newHashSet("mtcode_1372446607689146420"));

        StoreCustomSpuKey storeCustomSpuKey3 = new StoreCustomSpuKey();
        storeCustomSpuKey3.setStoreId(storeId + 3);
        storeCustomSpuKey3.setCustomSpuIds(Sets.newHashSet("901374262479800"));

        List<StoreCustomSpuKey> customSpuIds = Lists.newArrayList(storeCustomSpuKey, storeCustomSpuKey2,
                storeCustomSpuKey3);

        QueryCanModifyPriceRequest request = new QueryCanModifyPriceRequest();
        request.setTenantId(tenantId);
        request.setChannelId(channeldId);
        request.setCustomSpuIds(customSpuIds);

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        String channelStoreKey2 = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId + 2);
        String channelStoreKey3 = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId + 3);

        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDO.setStoreId(storeId);
        channelStoreDO.setAppId(123L);

        ChannelStoreDO channelStoreDO2 = new ChannelStoreDO();
        channelStoreDO2.setChannelOnlinePoiCode("685842");
        channelStoreDO2.setStoreId(storeId + 2);
        channelStoreDO2.setAppId(123L);

        ChannelStoreDO channelStoreDO3 = new ChannelStoreDO();
        channelStoreDO3.setChannelOnlinePoiCode("678t678");
        channelStoreDO3.setStoreId(storeId + 3);
        channelStoreDO3.setAppId(456L);

        channelStoreDOMap.put(channelStoreKey, channelStoreDO);
        channelStoreDOMap.put(channelStoreKey2, channelStoreDO2);
        channelStoreDOMap.put(channelStoreKey3, channelStoreDO3);

        when(copChannelStoreService.getChannelPoiCode(anyLong(), anyInt(), any())).thenReturn(channelStoreDOMap);

        Map<String, Object> resultString = JSONObject.parseObject(successData);
        Map<String, Object> failedString = JSONObject.parseObject(failedData);
        when(mtBrandChannelGateService.sendEncodedGetApp(any(), any(), any(), any()))
                .thenReturn(resultString)
                .thenReturn(failedString);

        QueryCanModifyPriceResponse response = mtChannelActivityService.querySpuCanModifyPrice(request, 0);
        System.out.println(response);
    }

    @Test
    public void buildQueryCanModifyPriceResponseTest() {
        QueryCanModifyPriceResponse response = new QueryCanModifyPriceResponse();
        response.setStatus(ResultGenerator.genFailResult("默认失败"));

        CanModifyPriceResult channelResult = JSON.parseObject(successData, CanModifyPriceResult.class);
        CanModifyPriceResult failedResult = JSON.parseObject(failedData, CanModifyPriceResult.class);

        Map<String, ChannelStoreDO> channelOnlinePoiMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setStoreId(4957025L);

        ChannelStoreDO channelStoreDO2 = new ChannelStoreDO();
        channelStoreDO2.setStoreId(4957066L);
        channelOnlinePoiMap.put("685842", channelStoreDO);
        channelOnlinePoiMap.put("604348", channelStoreDO);
        channelOnlinePoiMap.put("678t678", channelStoreDO2);

        //        response = mtChannelActivityService.buildQueryCanModifyPriceResponse(response, failedResult, channelOnlinePoiMap);
        //        response = mtChannelActivityService.buildQueryCanModifyPriceResponse(response, channelResult, channelOnlinePoiMap);
        //        response = mtChannelActivityService.buildQueryCanModifyPriceResponse(response, channelResult, channelOnlinePoiMap);
        //        String failedData2 = "{\"data\":\"ng\",\"error\":\"不存在此商品\",\"result_code\":3}";
        //        CanModifyPriceResult failedResult2 = JSON.parseObject(failedData2, CanModifyPriceResult.class);
        //        response = mtChannelActivityService.buildQueryCanModifyPriceResponse(response, failedResult2, channelOnlinePoiMap);

        System.out.println(response);
    }

}