/**
 * meituan.com Inc. Copyright (c) 2010-2024 All Rights Reserved.
 */
package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.doudian.open.utils.JsonUtil;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.order.platform.common.util.AssertUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.CopAccessConfigDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MaltFarmAggRiderTransferDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.PaoTuiRiderTransferDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelNotifyEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.health.HealthChannelRiderTransferRiderService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.TenantService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.ChannelOrderThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.proxy.TmsThriftServiceProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.DeliveryOrderCheckWrapper;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.HttpClientUtil;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 骑手转骑手测试:ChannelOrderCallbackServiceImplTest.java v1.0 2024/6/4 10:36 wb_wenke Exp $
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({MccConfigUtil.class, Lion.class, HttpClientUtil.class})
@SuppressStaticInitializationFor("com.sankuai.meituan.shangou.empower.ocms.channel.utils.HttpClientUtil")
@PowerMockRunnerDelegate(SpringRunner.class)
@PowerMockIgnore({"javax.management.*", "javax.script.*"})
public class ChannelOrderCallbackServiceImplTest {

    @InjectMocks
    private ChannelOrderCallbackServiceImpl channelOrderCallbackService;

    @Mock
    private CommonLogger log = new CommonLogger();

    @Mock
    private TenantService tenantServiceMock;

    @Mock
    private CopAccessConfigService copAccessConfigService;

    @Mock
    private ChannelOrderThriftServiceProxy channelOrderThriftServiceProxy;

    @Mock
    private TmsThriftServiceProxy tmsThriftServiceProxy;

    @Mock
    private DeliveryOrderCheckWrapper deliveryOrderCheckWrapper;

    @Mock
    private HealthChannelRiderTransferRiderService healthChannelRiderTransferRiderService;

    private String tenantAppId = "123456";
    private Long tenantId = 123456L;
    private Long poiId = 666888L;
    private Long orderId = 1234567890L;
    private Long deliveryOrderId = 123456789000L;
    private Long currTimestamp = System.currentTimeMillis();

    private ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.MT_MEDICINE;
    private ChannelNotifyEnum notifyEnum = ChannelNotifyEnum.MT_ORDER_RIDER_TRANSFER_RIDER_NOTIFY;
    private OrderNotifyRequest request = new OrderNotifyRequest();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        // mock 配置信息
        CopAccessConfigDO copAccessConfigDO = new CopAccessConfigDO();
        copAccessConfigDO.setTenantAppId(tenantAppId);
        copAccessConfigDO.setChannelId(ChannelTypeEnum.MT_MEDICINE.getCode());
        copAccessConfigDO.setTenantId(tenantId);
        copAccessConfigDO.setAppId(123457L);
        when(copAccessConfigService.selectByTenantAppIdAndChannelId(eq(tenantAppId), any())).thenReturn(copAccessConfigDO);

        // mock lion
        PowerMockito.mockStatic(MccConfigUtil.class);
        PowerMockito.mockStatic(Lion.class);
        ConfigRepository configRepository = Mockito.mock(ConfigRepository.class);
        when(Lion.getConfigRepository()).thenReturn(configRepository);

        setPaoTuiParam();

        // mock 订单信息
        BizOrderModel bizOrderModel = new BizOrderModel();
        bizOrderModel.setOrderId(orderId);
        bizOrderModel.setShopId(poiId);
        when(channelOrderThriftServiceProxy.queryOrderInfo(any()))
            .thenReturn(Optional.of(bizOrderModel));

        List<TDeliveryOrder> tDeliveryOrders = new ArrayList<>();
        TDeliveryOrder tDeliveryOrder = new TDeliveryOrder();
        tDeliveryOrder.setId(deliveryOrderId);
        tDeliveryOrder.setActiveStatus(true);
        tDeliveryOrder.setOrderId(orderId);
        tDeliveryOrder.setDeliveryOrderId(deliveryOrderId + "");
        tDeliveryOrders.add(tDeliveryOrder);
        when(tmsThriftServiceProxy.queryDeliveryOrderByOrderId(eq(orderId)))
            .thenReturn(Optional.of(tDeliveryOrders));

        // mock 跑腿订单
        when(healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(any(), any(), any(), any())).thenReturn(getMaltFarmAggRiderTransferDTO());

//        PaoTuiRiderTransferDTO paoTuiRiderTransferDTO = new PaoTuiRiderTransferDTO();
//        paoTuiRiderTransferDTO.setRiderName("张三");
//        paoTuiRiderTransferDTO.setRiderPhone("12345678901");
//        paoTuiRiderTransferDTO.setTimestamp(currTimestamp);
//        when(healthChannelRiderTransferRiderService.getPaoTuiRiderTransferByOrderNotifyRequest(any(), any(), any())).thenReturn(paoTuiRiderTransferDTO);

        // mock 跑腿密钥
        when(Lion.getConfigRepository().get("delivery.farm.paotui.secret","36e5999c627a486dda3248132981d265")).thenReturn("36e5999c627a486dda3248132981d265");

        // mock 跑腿订单校验
        when(deliveryOrderCheckWrapper.checkValidPaoTuiOrder(tDeliveryOrders))
            .thenReturn(true);

        // mock 发送麦芽田的订单
        when(deliveryOrderCheckWrapper.getDeliveryPlatform(tDeliveryOrders))
            .thenReturn(Lists.newArrayList(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM));

        // mock 麦芽田降级开关
        when(MccConfigUtil.isMaltFallbackSwitch())
            .thenReturn(false);
        // mock 麦芽田连接超时时间
        when(MccConfigUtil.getMaltHttpConnectionTimeOut())
            .thenReturn(12000);
        // mock 麦芽田socket超时时间
        when(MccConfigUtil.getMaltHttpSocketTimeOut())
            .thenReturn(20000);

        // mock 麦芽田骑手转骑手回调地址
        String callbackUrl = "https://open.maiyatian.com/callback/delivery/?channel=shuguopai";
        ReflectionTestUtils.setField(channelOrderCallbackService, "callbackUrl", callbackUrl);

        // mock 跑腿转骑手结果
//        HttpClientUtil httpClientUtil = PowerMockito.mock(HttpClientUtil.class);
//        PowerMockito.whenNew(HttpClientUtil.class).withNoArguments().thenReturn(httpClientUtil);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.postJson(anyString(),
            anyInt(),
            anyInt(),
            anyString()))
            .thenReturn("{\"data\":\"" + ProjectConstant.OK + "\"}");
    }

    public MaltFarmAggRiderTransferDTO getMaltFarmAggRiderTransferDTO(){
        MaltFarmAggRiderTransferDTO maltFarmAggRiderTransferDTO = new MaltFarmAggRiderTransferDTO();
        maltFarmAggRiderTransferDTO.setOrderId(deliveryOrderId+"");
        maltFarmAggRiderTransferDTO.setDispatcherName("张三(测试)");
        maltFarmAggRiderTransferDTO.setDispatcherMobile("12345678901_1234");
        maltFarmAggRiderTransferDTO.setStatus(20);
        maltFarmAggRiderTransferDTO.setTimestamp((int) System.currentTimeMillis());
        maltFarmAggRiderTransferDTO.setSign("sign");
        return maltFarmAggRiderTransferDTO;
    }

    /**
     * 设置跑腿参数
     */
    private void setPaoTuiParam() {
        JSONObject paotuiBody = new JSONObject();
        paotuiBody.put("app_poi_code", tenantAppId);
        paotuiBody.put("wm_order_id_view", orderId + "");
        paotuiBody.put("rider_name", "张三(骑手)");
        paotuiBody.put("rider_phone", "18966668888");
        request.setBody(JsonUtil.toJson(paotuiBody));
        request.setTimestamp(System.currentTimeMillis() + "");
    }

    /**
     * 测试doRiderTransferRider方法，当租户ID为null时，应返回成功结果。
     */
    @Test
    public void testDoRiderTransferRiderWithNullTenantId() throws Throwable {
        // arrange
        request.setTenantAppId(null);

        // act
        ResultStatus result = ReflectionTestUtils.invokeMethod(channelOrderCallbackService, "doRiderTransferRider", channelTypeEnum, notifyEnum,
            request);

        // assert
        AssertUtil.notNull(result, "result should not be null");
        AssertUtil.assertTrue(result.getCode() == ResultCode.SUCCESS.getCode(), "result should be success");
    }

    /**
     * 测试doRiderTransferRider方法，当租户ID有效，但非无人仓租户时，应返回失败结果。
     */
    @Test
    public void testDoRiderTransferRiderWithInvalidTenantId() throws Throwable {
        PowerMockito.when(MccConfigUtil.getQnhTenantId(Mockito.any())).thenReturn(tenantId);
        when(tenantServiceMock.isMedicineAdultUnmanWarehouse(any())).thenReturn(false);

        // arrange
        request.setTenantAppId("123456");

        when(healthChannelRiderTransferRiderService.getMaltFarmAggRiderTransferDTO(any(), any(), any(), any())).thenReturn(getMaltFarmAggRiderTransferDTO());

        // act
        ResultStatus result = (ResultStatus) ReflectionTestUtils.invokeMethod(channelOrderCallbackService, "doRiderTransferRider", channelTypeEnum,
            notifyEnum, request);

        // assert
        AssertUtil.notNull(result, "result should not be null");
        AssertUtil.assertTrue(result.getCode() == ResultCode.SUCCESS.getCode(), "result should be fail");
    }

    /**
     * 测试doRiderTransferRider方法，当租户ID有效，且为无人仓租户，但灰度开关打开且租户不在灰度列表中时，应返回成功结果。
     */
    @Test
    public void testDoRiderTransferRiderWithValidTenantIdAndGraySwitchOn() throws Throwable {
        PowerMockito.when(MccConfigUtil.getQnhTenantId(Mockito.any())).thenReturn(tenantId);
        when(tenantServiceMock.isMedicineAdultUnmanWarehouse(tenantId)).thenReturn(true);

        // arrange
        OrderNotifyRequest request = new OrderNotifyRequest();
        request.setTenantAppId("validTenantId");

        // act
        ResultStatus result = ReflectionTestUtils.invokeMethod(channelOrderCallbackService, "doRiderTransferRider", channelTypeEnum, notifyEnum, request);

        // assert
        AssertUtil.notNull(result, "result should not be null");
        AssertUtil.assertTrue(result.getCode() == ResultCode.SUCCESS.getCode(), "result should be success");
    }

    /**
     * 测试doRiderTransferRider方法，当租户ID有效，且为无人仓租户，灰度开关打开且租户在灰度列表中时，执行后续逻辑
     */
    @Test
    public void testDoRiderTransferRiderWithValidTenantIdAndGraySwitchOn2() throws Throwable {
        PowerMockito.when(MccConfigUtil.getQnhTenantId(Mockito.any())).thenReturn(tenantId);
        when(tenantServiceMock.isMedicineAdultUnmanWarehouse(tenantId)).thenReturn(true);

        when(Lion.getConfigRepository().getBooleanValue("delivery.paotui.rider.transfer.rider.gray.tenant.switch", true))
            .thenReturn(true);
        when(Lion.getConfigRepository().getList(eq("delivery.paotui.rider.transfer.rider.gray.tenantIds"), any(), any(ArrayList.class)))
            .thenReturn(Lists.newArrayList(1001, 1002, tenantId));
        when(Lion.getConfigRepository().getBooleanValue("delivery.paotui.rider.transfer.rider.gray.poi.switch", true))
            .thenReturn(true);
        when(Lion.getConfigRepository().getList(eq("delivery.paotui.rider.transfer.rider.gray.poiIds"), any(), any(ArrayList.class)))
            .thenReturn(Lists.newArrayList(1001, 1002, poiId));

        // arrange
        request.setTenantAppId("validTenantId");

        // act
        ResultStatus result = ReflectionTestUtils.invokeMethod(channelOrderCallbackService, "doRiderTransferRider", channelTypeEnum, notifyEnum,
            request);

        // assert
        AssertUtil.notNull(result, "result should not be null");
        AssertUtil.assertTrue(result.getCode() == ResultCode.SUCCESS.getCode(), "result should be success");
    }

}