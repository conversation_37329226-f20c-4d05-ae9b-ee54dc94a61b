/*
package com.sankuai.meituan.shangou.empower.ocms.channel.utils;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.TenantCancelOrderReasonEnum;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

*/
/***
 * author : <EMAIL> 
 * data : 2020/7/22 
 * time : 下午5:12
 **//*

@RunWith(MockitoJUnitRunner.class)
public class OrderRefundCodeConventerTest {

    @Test
    public void mtRefundCodeMapping() {

        Assert.assertEquals(2011, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.CLOSING_TIME));
        Assert.assertEquals(2007, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.SELF_DEFINED));
        Assert.assertEquals(2008, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.SHOP_TOO_BUSI));
        Assert.assertEquals(2006, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.CUSTOMER_APPLY));
        Assert.assertEquals(2004, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.DELIVERY_DELAY));
        Assert.assertEquals(2009, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.PRODUCT_SAIL_OUT));
        Assert.assertEquals(2013, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.REDUPLICATE_ORDER));
        Assert.assertEquals(2005, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.AFTER_SALE_COMPLAIN));
        Assert.assertEquals(2010, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.DELIVERY_INFO_WRONG));
        Assert.assertEquals(2003, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.TENANT_CANCEL_ORDER));
        Assert.assertEquals(2002, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.TENANT_MODIFY_ORDER));
        Assert.assertEquals(2012, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.CUSTOMER_NOT_CONNECT));
        Assert.assertEquals(2010, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.OUT_OF_DELIVERY_RANGE));
        Assert.assertEquals(2006, OrderRefundCodeConventer.mtRefundCodeMapping(TenantCancelOrderReasonEnum.DELIVERY_RIDER_DROP_PRODUCT));

    }

    @Test
    public void elmRefundCodeMapping() {
        Assert.assertEquals(2, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.CLOSING_TIME.getValue()));
        Assert.assertEquals(-1, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.SELF_DEFINED.getValue()));

        Assert.assertEquals(7, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.SHOP_TOO_BUSI.getValue()));
        Assert.assertEquals(5, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.CUSTOMER_APPLY.getValue()));
        Assert.assertEquals(6, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.REDUPLICATE_ORDER.getValue()));
        Assert.assertEquals(3, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.PRODUCT_SAIL_OUT.getValue()));
        Assert.assertEquals(6, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.PRODUCT_SAIL_OUT.getValue()));
        Assert.assertEquals(8, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.CUSTOMER_NOT_CONNECT.getValue()));

        Assert.assertEquals(1, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.PRODUCT_PRICE_CHANGED.getValue()));
        Assert.assertEquals(4, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.CUSTOMER_NOT_CONNECT.getValue()));
        Assert.assertEquals(9, OrderRefundCodeConventer.elmRefundCodeMapping(TenantCancelOrderReasonEnum.CUSTOMER_NOT_CONNECT.getValue()));
    }


}
*/
