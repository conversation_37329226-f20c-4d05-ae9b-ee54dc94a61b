package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopAccessConfigService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.QuerySkuActivityInfoRequest;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtBrandChannelBaseServiceImplTest {

    @Mock
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Mock
    private CopChannelStoreService copChannelStoreService;

    @Mock
    private MtConverterService mtConverterService;

    @Mock
    private CopAccessConfigService copAccessConfigService;

    @Mock
    private CommonLogger log;

    @InjectMocks
    private MtBrandChannelCommonServiceImpl mtBrandChannelBaseService;

    @Test
    public void queryPoiActivityTest() {
        Long tenantId = 1000094L;
        int channelId = 100;
        Long storeId = 4987267L;
        BaseRequest baseRequest = new BaseRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreIdList(Lists.newArrayList(storeId));

        mtBrandChannelBaseService.queryPoiActivity(baseRequest);
    }

    @Test
    public void querySkuActivityInfoListTest() {
        Long tenantId = 1000094L;
        int channelId = 100;
        Long storeId = 4987267L;
        String customSpuId = "1234";
        QuerySkuActivityInfoRequest request = new QuerySkuActivityInfoRequest()
                .setTenantId(tenantId)
                .setChannelId(channelId)
                .setStoreId(storeId)
                .setCustomSpuIds(Lists.newArrayList(customSpuId));

        mtBrandChannelBaseService.querySkuActivityInfoList(request);
    }

    @Test
    public void authTest() {
        Map<String, String> params = Maps.newHashMap();
        params.put("app_id", "123");

        when(copAccessConfigService.selectTenantId(any(), any())).thenReturn(123L);

        when(copAccessConfigService.selectAppSysParams(any(), any(), any())).thenReturn(
                "{\"secret\":\"xxxxx\",\"app_id\":\"8341\"}");

        mtBrandChannelBaseService.auth("", params, "", "");
    }

}