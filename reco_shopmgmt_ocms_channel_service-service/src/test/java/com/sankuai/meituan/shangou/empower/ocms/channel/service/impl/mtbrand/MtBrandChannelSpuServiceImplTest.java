package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.CommonLogger;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ClusterRateLimiter;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.BaseConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.BatchGetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.GetSpuInfosRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.KeyUtils;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtBrandChannelSpuServiceImplTest {

    @Mock
    private CommonLogger log;

    @Mock
    private BaseConverterService baseConverterService;

    @Mock
    private MtConverterService mtConverterService;

    @Mock
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Mock
    private CopChannelStoreService copChannelStoreService;

    @Mock
    private ClusterRateLimiter clusterRateLimiter;

    @InjectMocks
    private MtBrandChannelSpuServiceImpl mtBrandChannelSpuService;

    @Test
    public void getSpuInfoTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        when(copChannelStoreService.getChannelPoiCode(anyLong(), anyInt(), anyList())).thenReturn(channelStoreDOMap);

        Map<String, Object> sysParams = Maps.newHashMap();
        sysParams.put("app_id", 123L);
        when(mtBrandChannelGateService.getSysParam(any())).thenReturn(sysParams);

        GetSpuInfoRequest request = new GetSpuInfoRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));
        request.setStoreId(storeId);

        mtBrandChannelSpuService.getSpuInfo(request);
    }

    @Test
    public void getSpuInfoListTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        when(copChannelStoreService.getChannelPoiCode(anyLong(), anyInt(), anyList())).thenReturn(channelStoreDOMap);

        Map<String, Object> sysParams = Maps.newHashMap();
        sysParams.put("app_id", 123L);
        when(mtBrandChannelGateService.getSysParam(any())).thenReturn(sysParams);

        GetSpuInfosRequest request = new GetSpuInfosRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));
        request.setStoreId(storeId);
        request.setCustomSpuIds(Lists.newArrayList("123"));

        mtBrandChannelSpuService.getSpuInfoList(request);
    }

    @Test
    public void batchGetSkuInfoTest() {
        Long tenantId = 1000045L;
        int channeldId = 100;
        Long storeId = 4957025L;

        String channelStoreKey = KeyUtils.genChannelStoreKey(tenantId, channeldId, storeId);
        Map<String, ChannelStoreDO> channelStoreDOMap = Maps.newHashMap();
        ChannelStoreDO channelStoreDO = new ChannelStoreDO();
        channelStoreDO.setChannelOnlinePoiCode("604348");
        channelStoreDOMap.put(channelStoreKey, channelStoreDO);

        when(copChannelStoreService.getChannelPoiCode(anyLong(), anyInt(), anyList())).thenReturn(channelStoreDOMap);

        Map<String, Object> sysParams = Maps.newHashMap();
        sysParams.put("app_id", 123L);
        when(mtBrandChannelGateService.getSysParam(any())).thenReturn(sysParams);

        BatchGetSpuInfoRequest request = new BatchGetSpuInfoRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(tenantId).setChannelId(channeldId));
        request.setStoreId(storeId);

        mtBrandChannelSpuService.batchGetSpuInfo(request);
    }

}