<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
        <artifactId>reco_shopmgmt_ocms_channel_service</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../</relativePath>
    </parent>

    <artifactId>reco_shopmgmt_ocms_channel_service-service</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.17</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.2.4.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>store-saas-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_channel_service-client</artifactId>
            <version>${ocms.channel.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_channel_service-dao</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.sgshopmgmt</groupId>
            <artifactId>reco_store_saas_product_biz-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>thrift-xframe-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_biz_client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou</groupId>
                    <artifactId>store-saas-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_platform_common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-client</artifactId>
            <version>1.5.8</version>
        </dependency>
        <!-- http client-->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.octo</groupId>
            <artifactId>octo-async</artifactId>
            <version>1.1.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan</groupId>
                    <artifactId>jmonitor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <!--中台管理服务-->
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
            <version>${ocms.service.client.version}</version>
        </dependency>
        <!--检验框架-->
        <dependency>
            <groupId>net.sf.oval</groupId>
            <artifactId>oval</artifactId>
            <version>1.87</version>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>${xstream.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.saas.crm</groupId>
            <artifactId>store-saas-mif-client</artifactId>
            <version>${saas.mif.version}</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>${guava-retry.version}</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_biz_client</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.1.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_management_client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtconfig-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.youzan.cloud</groupId>
            <artifactId>open-sdk-gen</artifactId>
            <version>1.0.28.44963202407041415-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-cluster-limiter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.youzan.cloud</groupId>
            <artifactId>open-sdk-api</artifactId>
            <version>1.0.12-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.youzan.cloud</groupId>
            <artifactId>open-sdk-core</artifactId>
            <version>1.0.22-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.youzan.cloud</groupId>
                    <artifactId>open-sdk-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.youzan.cloud</groupId>
                    <artifactId>cloud-open-data-security-client-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.youzan.cloud</groupId>
            <artifactId>cloud-open-data-security-client-sdk</artifactId>
            <version>1.0.7-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.saas.crm</groupId>
            <artifactId>reco_store_saas_promotion_client</artifactId>
            <version>1.20${type}</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.linz</groupId>
            <artifactId>linz-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.platform</groupId>
            <artifactId>shangou_empower_product_client</artifactId>
            <version>${empower.product.client}${type}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.sgshopmgmt</groupId>
            <artifactId>productplatform-sdk</artifactId>
            <version>${productplatform.sdk.version}${type}</version>
        </dependency>

        <dependency>
            <groupId>net.sf.oval</groupId>
            <artifactId>oval</artifactId>
            <version>3.2.0</version>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.tsp.orderfulfillment</groupId>
            <artifactId>merchant-client</artifactId>
            <version>1.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai.service.order</groupId>
            <artifactId>waimai_service_order_clientassembly</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_service_virtual_product_client</artifactId>
            <version>2.99.60</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai.service.activity</groupId>
            <artifactId>waimai_service_activity_client</artifactId>
            <version>2.40.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.service.mobile</groupId>
                    <artifactId>mtthrift</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>waimai_service_c_base_virtual_product_client</artifactId>
                    <groupId>com.sankuai.meituan.waimai.service.cproduct</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waimai-thrift-tools</artifactId>
                    <groupId>com.sankuai.meituan.waimai</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waimai_service_promotion_client</artifactId>
                    <groupId>com.sankuai.meituan.waimai.promotion</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waimai_service_order_clientassembly</artifactId>
                    <groupId>com.sankuai.meituan.waimai.service.order</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waimai_service_virtual_product_idl</artifactId>
                    <groupId>com.sankuai.meituan.waimai</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai.poi</groupId>
            <artifactId>waimai_service_poiquery_client</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai.productquery</groupId>
            <artifactId>waimai_service_productquery_client</artifactId>
            <version>1.2.8-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.security</groupId>
            <artifactId>sec-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <artifactId>mafka-client_2.10</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgfnqnh.tenant</groupId>
            <artifactId>qnh-tenant-task-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>shangou_open_push_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.sgfnqnh.empower</groupId>
            <artifactId>reco_qnh_migrate-client</artifactId>
        </dependency>

        <!-- 商品质量计算-->
        <dependency>
            <groupId>com.sankuai.shangou.product</groupId>
            <artifactId>quality-query-client</artifactId>
            <version>2.0.9-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.cip.crane</groupId>
                    <artifactId>crane-client:1.3.3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.linz</groupId>
            <artifactId>linz-product</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_high_level_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.douyin.sdk</groupId>
            <artifactId>doudian-sdk-java</artifactId>
        </dependency>

        <!--  淘宝开放平台SDK  -->
        <dependency>
            <groupId>com.taobao.sdk</groupId>
            <artifactId>taobao-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.sgfulfillment.comment</groupId>
            <artifactId>fulfillment-comment-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_delivery_service-client</artifactId>
            <version>1.2.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
        </dependency>

        <!--淘鲜达开放平台 SDK-->
        <dependency>
            <groupId>com.taobao.sdk</groupId>
            <artifactId>taobao-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgfnqnh.poi</groupId>
            <artifactId>reco_qnh_poi_api-client</artifactId>
            <version>${sgfnqnh.poi.version}${type}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.sgfnqnh.poi</groupId>
            <artifactId>reco_qnh_poi_channel-client</artifactId>
        </dependency>

        <!--淘鲜达开放平台 SDK-->
        <dependency>
            <groupId>com.taobao.sdk</groupId>
            <artifactId>taobao-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-supplychain-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>stock-biz-idl</artifactId>
            <version>4.8.21</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
            <artifactId>infrastructure-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.sgfnqnh.finance</groupId>
            <artifactId>reco_sgfnqnh_finance_tax-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>RELEASE</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>