package com.sankuai.meituan.shangou.empower.ocms.channel;


import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordCheckRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SensitiveWordDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuInfoDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSpuThriftService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelSpuThriftServiceTest {

    @Autowired
    private ChannelSpuThriftService.Iface channelSpuThriftService;

    @Test
    public void testCheckSensitiveWords() throws Exception{

        List<SensitiveWordDTO> sensitiveWordList = Lists.newArrayList();
        SensitiveWordDTO sensitiveWordDTO = new SensitiveWordDTO();
        sensitiveWordDTO.setContent("毒品");
        sensitiveWordDTO.setType(1);

        sensitiveWordList.add(sensitiveWordDTO);

        SensitiveWordCheckRequest request = new SensitiveWordCheckRequest();
        request.setBaseInfo(new BaseRequestSimple().setTenantId(1000011).setChannelId(100))
                .setStoreId(1000L)
                .setSensitiveWordList(sensitiveWordList)
                ;

        channelSpuThriftService.checkSensitiveWords(request);
    }

}
