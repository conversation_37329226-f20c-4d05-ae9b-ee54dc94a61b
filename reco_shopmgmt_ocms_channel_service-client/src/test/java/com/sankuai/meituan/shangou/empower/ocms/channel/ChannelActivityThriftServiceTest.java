package com.sankuai.meituan.shangou.empower.ocms.channel;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelActivityThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Title:
 * @Description:
 * <AUTHOR>
 * @Date 2019-09-12 10:52
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelActivityThriftServiceTest {
    @Autowired(required = false)
    private ChannelActivityThriftService.Iface channelActivityThriftService;

    private final TestConstant.BaseParamEnum mtParam = TestConstant.BaseParamEnum.MT;
    private final TestConstant.BaseParamEnum elmParam = TestConstant.BaseParamEnum.ELM;

    @Test
    public void saveActivityForInterceptor() throws TException {
        ActivitySaveRequest request = new ActivitySaveRequest();
        request.setTenantId(mtParam.getTenantId());
        request.setTimeStamp(1552310575);
        request.setActType(ActTypeEnum.EDIT);

        // Case1: 部分渠道门店禁用线上数据同步
        request.setActivityList(createActivityInfo(mtParam));
        ActivityResponse data = channelActivityThriftService.saveActivity(request);
        log.info("ChannelActivityThriftServiceTest.saveActivity, activityResponse:{}", data);
        Assert.isTrue(data.status.code == 0, "case1:saveActivity the status of result is wrong!!");
        Assert.isTrue(data.errorData.stream().filter(error -> error.getData().getCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT, "case1:saveActivity.error count is wrong!!");

        // Case2: 租户渠道未开通
        request.setActivityList(createActivityInfo(elmParam));
        data = channelActivityThriftService.saveActivity(request);
        log.info("ChannelSkuThriftServiceTest.updateSkuSellStatus, {}", data);
        Assert.isTrue(data.status.code == 0, "case2:saveActivity case2 the status of result is wrong!!");
        Assert.isTrue(data.errorData.stream().filter(error -> error.getData().getCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT * 2, "case2:saveActivity.error count is wrong!!");

    }

    private List<ChannelActivityInfo> createActivityInfo(TestConstant.BaseParamEnum param){
        List<ChannelActivityInfo> channelActivityInfoList = new ArrayList<>();

        AtomicInteger index = new AtomicInteger(1);
        param.getStoreIds().forEach(storeId -> {
            for (int i = 1; i<= CommonTestUtils.TEST_ITEM_COUNT;i++){
                String skuId = "69201903091" + i;
                channelActivityInfoList.add(new ChannelActivityInfo()
                        .setChannelId(param.getChannelId())
                        .setStoreId(storeId)
                        // .setChannelActivityId("")
                        .setChannelActivityId("305256903") //京东活动ID
                        .setActivityName("活动测试" + skuId)
                        .setStartTime(1552924800)
                        .setEndTime(1553742000)
                        .setAdvertising("")
                        .setActivityType(ActivityTypeEnum.NORMAL)
                        .setSettingType(SettingTypeEnum.DISCOUNT_PRICE)
                        .setUserType(UserTypeEnum.STORE_NEW)
                        .setPeriod("10:12-14:12")
                        .setWeeksTime("1,2,3,4,5,6")
                        .setLimitDevice(false)
                        .setLimitPin(false)
                        .setLimitCount(2)
                        .setLimitDaily(false)
                        .setOrderLimit(-1)
                        .setDayLimit(-1)
                        .setSkuId(skuId)
                        .setPromotionPrice(1)
                        .setLimitSkuCount(2)
                        .setDiscountCoefficient(9.7)
                        .setSequence(index.getAndIncrement())
                        .setItemOpType(ItemOpTypeEnum.UPDATE));
            }
        });
        return channelActivityInfoList;
    }

    @Test
    public void batchDeleteForInterceptor() throws Exception {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        ActivityDeleteRequest request = new ActivityDeleteRequest();
        request.setTenantId(mtParam.getTenantId());
        request.setTimeStamp(1552310575);

        // Case1: 部分渠道门店禁用线上数据同步
        request.setChannelPoiInfoList(createChannelPoiParamInfo(mtParam));
        ActivityResponse data = channelActivityThriftService.batchDelete(request);
        log.info("ChannelActivityThriftServiceTest.{}, activityResponse:{}",method, data);
        Assert.isTrue(data.status.code == 0, String.format("case1:%s the status of result is wrong!!", method));
        Assert.isTrue(data.errorData.stream().filter(error -> error.getData().getCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT, String.format("case1:%s.error count is wrong!!", method));

        // Case2: 租户渠道未开通
        request.setChannelPoiInfoList(createChannelPoiParamInfo(elmParam));
        data = channelActivityThriftService.batchDelete(request);
        log.info("ChannelSkuThriftServiceTest.updateSkuSellStatus, {}", data);
        Assert.isTrue(data.status.code == 0, String.format("case2:%s the status of result is wrong!!", method));
        Assert.isTrue(data.errorData.stream().filter(error -> error.getData().getCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT * 2, String.format("case2:%s.error count is wrong!!", method));
    }

    private List<ChannelPoiParamInfo> createChannelPoiParamInfo(TestConstant.BaseParamEnum param){
        List<ChannelPoiParamInfo> channelPoiParamInfo = new ArrayList<>();

        param.getStoreIds().forEach(storeId -> {
            ChannelPoiParamInfo info = new ChannelPoiParamInfo()
                    .setStoreId(storeId)
                    .setChannelId(param.getChannelId())
                    .setChannelActivityIdList(Lists.newArrayList());
            for (int i = 1; i<= CommonTestUtils.TEST_ITEM_COUNT;i++){
                info.getChannelActivityIdList().add("69201903091" + i);
            }
            channelPoiParamInfo.add(info);
        });
        return channelPoiParamInfo;
    }

    @Test
    public void queryActivityListForInterceptor() throws TException {
        BaseRequest request = CommonTestUtils.createBaseInfo(mtParam);

        // Case1: 部分渠道门店禁用线上数据同步，无法验证结果，只能看log把禁用掉的门店过滤掉就好
        QueryActivityResponse response = channelActivityThriftService.queryActivityList(request);
        Assert.isTrue(response.getStatus().getCode() == 0, "case1:queryActivityList error!!!");

        // Case2: 租户渠道未开通
        request.setChannelId(elmParam.getChannelId());
        response = channelActivityThriftService.queryActivityList(request);
        Assert.isTrue(response.getStatus().getCode() == 0, "case2:queryActivityList error!!!");
    }
}
