package com.sankuai.meituan.shangou.empower.ocms.channel;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderCallbackThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * @Title: ChannelOrderCallbackThriftServiceTest
 * @Description:
 * @Author: mixiaofeng
 * @Date: 2019-09-09 21:06
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelOrderCallbackThriftServiceTest {
    @Resource
    private ChannelOrderCallbackThriftService.Iface copOrderCallbackThriftService;

    private int channelId = 200;
    private long tenantId = 1000011L;
    private long storeId = 1000017L;
    private String orderId = "27058911013567981";

    @Test
    public void testOrderNotify() throws TException {
        OrderNotifyRequest request = new OrderNotifyRequest();
        request.setChannelCode("jddj");
        request.setAction("afterSaleBillStatus");
        request.setTenantAppId("74be6bfcdb174385a708d1ea0cdc5580");
        request.setJdParamJson("{\"billId\":\"23143291\",\"statusId\":\"10\",\"timestamp\":\"2019-07-14 17:45:42\"}");
        try {
            ResultStatus resultStatus = copOrderCallbackThriftService.orderNotify(request);
            Assert.isTrue(resultStatus.code == 1, "testOrderNotify");
        }
            catch (Exception exc) {
        }
    }
}
