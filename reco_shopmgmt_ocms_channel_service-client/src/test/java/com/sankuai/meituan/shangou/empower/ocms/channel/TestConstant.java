package com.sankuai.meituan.shangou.empower.ocms.channel;

import org.assertj.core.util.Lists;

import java.util.List;

public interface TestConstant {

    /**
     * 测试基本参数枚举
     */
    enum BaseParamEnum {
        MT(1000011, 100, Lists.newArrayList(1000017L, 1000019L)),
        ELM(1000011, 200, Lists.newArrayList(1000017L, 1054763L)),
        JDDJ(1000011, 300, Lists.newArrayList(1000017L)),
        ;

        private long tenantId;
        private int channelId;
        private List<Long> storeIds;

        BaseParamEnum(long tenantId, int channelId, List<Long> storeIds){
            this.tenantId = tenantId;
            this.channelId = channelId;
            this.storeIds = storeIds;
        }

        public long getTenantId() {
            return tenantId;
        }

        public int getChannelId() {
            return channelId;
        }

        public List<Long> getStoreIds() {
            return storeIds;
        }
    }
}
