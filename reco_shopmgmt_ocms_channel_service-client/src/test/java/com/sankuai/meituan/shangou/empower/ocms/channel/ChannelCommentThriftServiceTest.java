package com.sankuai.meituan.shangou.empower.ocms.channel;


import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentListQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentListQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentReplyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentReplyResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentRuleQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment.CommentRuleQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCommentThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.util.Assert;

@Slf4j
public class ChannelCommentThriftServiceTest {

    private static ChannelCommentThriftService.Iface copCommentThriftService;

    private final TestConstant.BaseParamEnum mtParam = TestConstant.BaseParamEnum.MT;
    private final TestConstant.BaseParamEnum elmParam = TestConstant.BaseParamEnum.ELM;

    @BeforeClass
    public static void init() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setAppKey("com.sankuai.shangou.empower.ocmschannel");
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.ocmschannel");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCommentThriftService"));
        proxy.setRemoteServerPort(8090);
        proxy.setServerIpPorts("127.0.0.1:8090");
        proxy.setTimeout(30000);
        proxy.setRemoteUniProto(true);
        proxy.afterPropertiesSet();
        copCommentThriftService = (ChannelCommentThriftService.Iface) proxy.getObject();
    }

    @Test
    public void testQueryMtCommentList() throws TException {
        CommentListQueryRequest request = new CommentListQueryRequest();
        request.setTenantId(1000011).setChannelId(100).setStoreId(1000017)
                .setStartTime("2020-07-01 00:00:00").setEndTime("2020-07-07 23:49:00")
                .setPageNum(1).setPageSize(10);
        CommentListQueryResponse response = copCommentThriftService.queryCommentList(request);
        System.out.println("response: " + response);
        //System.out.println(String.format("testQueryMtCommentList, request: %s, response:%s",
        //        JSON.toJSONString(request, SerializerFeature.IgnoreNonFieldGetter), JSON.toJSONString(response, SerializerFeature.IgnoreNonFieldGetter)));
    }

    @Test
    public void testMtReply() throws TException {

        CommentReplyRequest request = new CommentReplyRequest();
        request.setTenantId(1000011).setChannelId(100).setStoreId(1000017).setChannelCommentId("2672322368").setReplyContent("感谢您的支持");
        CommentReplyResponse response = copCommentThriftService.reply(request);
        //System.out.println(String.format("testQueryJddjCommentList, request: %s, response:%s",
        //        JSON.toJSONString(request, SerializerFeature.IgnoreNonFieldGetter), JSON.toJSONString(response, SerializerFeature.IgnoreNonFieldGetter)));
    }

    @Test
    public void testQueryElmCommentList() throws TException {
        CommentListQueryRequest request = new CommentListQueryRequest();
        request.setTenantId(1000011).setChannelId(200).setStoreId(1000017)
                .setStartTime("2019-07-01 00:00:00").setEndTime("2019-07-31 00:00:00")
                .setPageNum(1).setPageSize(10);
        CommentListQueryResponse response = copCommentThriftService.queryCommentList(request);
        //System.out.println(String.format("testQueryElmCommentList, request: %s, response:%s",
        //        JSON.toJSONString(request, SerializerFeature.IgnoreNonFieldGetter), JSON.toJSONString(response, SerializerFeature.IgnoreNonFieldGetter)));
    }

    @Test
    public void testQueryJddjCommentList() throws TException {
        CommentListQueryRequest request = new CommentListQueryRequest();
        request.setTenantId(1000011).setChannelId(300).setChannelCommentId("916714639000641");
        CommentListQueryResponse response = copCommentThriftService.queryCommentList(request);
        //System.out.println(String.format("testQueryJddjCommentList, request: %s, response:%s",
        //        JSON.toJSONString(request, SerializerFeature.IgnoreNonFieldGetter), JSON.toJSONString(response, SerializerFeature.IgnoreNonFieldGetter)));
    }

    @Test
    public void queryCommentListForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        CommentListQueryRequest request = new CommentListQueryRequest();
        request.setTenantId(mtParam.getTenantId())
                .setChannelId(mtParam.getChannelId());

        // Case1: 渠道门店禁用线上数据同步
        request.setStoreId(mtParam.getStoreIds().get(1)); //1000019
        CommentListQueryResponse data = copCommentThriftService.queryCommentList(request);
        log.info("ChannelCommentThriftService.{}, data:{}",method, data);
        Assert.isTrue(data.status.code == 10, String.format("case1:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().contains("未开通!"), String.format("case1:%s.error count is wrong!!",method));

        // Case2: 租户渠道未开通
        request.setChannelId(elmParam.getChannelId());
        data = copCommentThriftService.queryCommentList(request);
        log.info("ChannelCommentThriftService.{}, {}", method, data);
        Assert.isTrue(data.status.code == 10, String.format("case2:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().equals("渠道未开通"), String.format("case2:%s.error count is wrong!!",method));
    }

    @Test
    public void replyForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        CommentReplyRequest request = new CommentReplyRequest();
        request.setTenantId(mtParam.getTenantId())
                .setChannelId(mtParam.getChannelId())
                .setChannelCommentId("2672322368")
                .setReplyContent("感谢您的支持");

        // Case1: 渠道门店禁用线上数据同步
        request.setStoreId(mtParam.getStoreIds().get(1)); //1000019
        CommentReplyResponse data = copCommentThriftService.reply(request);
        log.info("ChannelCommentThriftService.{}, data:{}",method, data);
        Assert.isTrue(data.status.code == 10, String.format("case1:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().contains("未开通!"), String.format("case1:%s.error count is wrong!!",method));

        // Case2: 租户渠道未开通
        request.setChannelId(elmParam.getChannelId());
        data = copCommentThriftService.reply(request);
        log.info("ChannelCommentThriftService.{}, {}", method, data);
        Assert.isTrue(data.status.code == 10, String.format("case2:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().equals("渠道未开通"), String.format("case2:%s.error count is wrong!!",method));
    }

    @Test
    public void testQueryCommentRules() throws TException {
        CommentRuleQueryRequest request = new CommentRuleQueryRequest();
        request.setTenantId(1000011);
        CommentRuleQueryResponse response = copCommentThriftService.queryCommentRule(request);
        System.out.println("queryCommentRule response: " + response);
    }
}
