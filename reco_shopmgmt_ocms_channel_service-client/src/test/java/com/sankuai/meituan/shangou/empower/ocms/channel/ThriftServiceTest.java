package com.sankuai.meituan.shangou.empower.ocms.channel;


import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ActivityTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ItemOpTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.SettingTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.UserTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ActivityResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ActivitySaveRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.ChannelActivityInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelActivityThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class ThriftServiceTest {

    private static ChannelActivityThriftService.Iface channelActivityThriftService;

    private final TestConstant.BaseParamEnum mtParam = TestConstant.BaseParamEnum.MT;
    private final TestConstant.BaseParamEnum elmParam = TestConstant.BaseParamEnum.ELM;

    @BeforeClass
    public static void init() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setAppKey("com.sankuai.shangou.empower.ocmschannel");
        proxy.setRemoteAppkey("com.sankuai.shangou.empower.ocmschannel");
        proxy.setServiceInterface(Class.forName("com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelActivityThriftService"));
        proxy.setRemoteServerPort(8090);
        proxy.setServerIpPorts("*************:8090");
        proxy.setTimeout(30000);
        proxy.setRemoteUniProto(true);
        proxy.afterPropertiesSet();
        channelActivityThriftService = (ChannelActivityThriftService.Iface) proxy.getObject();
    }

    @Test
    public void saveActivityForInterceptor() throws TException {
        ActivitySaveRequest request = new ActivitySaveRequest();
        request.setTenantId(mtParam.getTenantId());
        request.setTimeStamp(1552310575);
        request.setActType(null);
        request.setActivityList(createActivityInfo(mtParam));

        // 参数校验
        ActivityResponse data = channelActivityThriftService.saveActivity(request);
        Assert.isTrue(data.status.code != 0, "活动类型不能为空");
    }

    private List<ChannelActivityInfo> createActivityInfo(TestConstant.BaseParamEnum param){
        List<ChannelActivityInfo> channelActivityInfoList = new ArrayList<>();

        AtomicInteger index = new AtomicInteger(1);
        param.getStoreIds().forEach(storeId -> {
            for (int i = 1; i<= CommonTestUtils.TEST_ITEM_COUNT;i++){
                String skuId = "69201903091" + i;
                channelActivityInfoList.add(new ChannelActivityInfo()
                        .setChannelId(param.getChannelId())
                        .setStoreId(storeId)
                        // .setChannelActivityId("")
                        .setChannelActivityId("305256903") //京东活动ID
                        .setActivityName("活动测试" + skuId)
                        .setStartTime(1552924800)
                        .setEndTime(1553742000)
                        .setAdvertising("")
                        .setActivityType(ActivityTypeEnum.NORMAL)
                        .setSettingType(SettingTypeEnum.DISCOUNT_PRICE)
                        .setUserType(UserTypeEnum.STORE_NEW)
                        .setPeriod("10:12-14:12")
                        .setWeeksTime("1,2,3,4,5,6")
                        .setLimitDevice(false)
                        .setLimitPin(false)
                        .setLimitCount(2)
                        .setLimitDaily(false)
                        .setOrderLimit(-1)
                        .setDayLimit(-1)
                        .setSkuId(skuId)
                        .setPromotionPrice(1)
                        .setLimitSkuCount(2)
                        .setDiscountCoefficient(9.7)
                        .setSequence(index.getAndIncrement())
                        .setItemOpType(ItemOpTypeEnum.UPDATE));
            }
        });
        return channelActivityInfoList;
    }
}
