package com.sankuai.meituan.shangou.empower.ocms.channel;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BatchGetPoiDetailsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.GetPoiIdsResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.GetPoiInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelBaseMsgThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.stream.Collectors;

/**
 * @Title:
 * @Description:
 * <AUTHOR>
 * @Date 2019-09-12 17:19
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelBaseMsgThriftServiceTest {
    private final String disabledStoreCode = "4957073";

    @Autowired(required = false)
    private ChannelBaseMsgThriftService.Iface channelBaseMsgThriftService;

    private final TestConstant.BaseParamEnum mtParam = TestConstant.BaseParamEnum.MT;
    private final TestConstant.BaseParamEnum elmParam = TestConstant.BaseParamEnum.ELM;

    @Test
    public void batchGetPoiInfoTestForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        // Case1: 部分渠道门店禁用线上数据同步, 1000017的poi code是t_7YbuOcCwuZ, 1000019的是4957073
        BaseRequestSimple request = CommonTestUtils.createBaseSimpleInfo(mtParam);
        GetPoiInfoResponse data = channelBaseMsgThriftService.batchGetPoiInfo(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, data:{}",method, data);
        Assert.isTrue(data.status.code == 0, String.format("case1:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getPoiInfoList().stream().noneMatch(info -> info.getAppPoiCode().equals(disabledStoreCode)), String.format("case1:%s.error count is wrong!!",method));

        // Case2: 租户渠道未开通
        request.setChannelId(elmParam.getChannelId());
        data = channelBaseMsgThriftService.batchGetPoiInfo(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, {}", method, data);
        Assert.isTrue(data.status.code == 10, String.format("case2:%s the status of result is wrong!!", method));
        Assert.isTrue(CollectionUtils.isEmpty(data.getPoiInfoList()), String.format("case2:%s.error count is wrong!!",method));
        Assert.isTrue(data.getStatus().getMsg().equals("渠道未开通"), String.format("case2:%s.error count is wrong!!",method));
    }

    @Test
    public void batchGetPoiIdsTestForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        // Case1: 部分渠道门店禁用线上数据同步, 1000017的poi code是t_7YbuOcCwuZ, 1000019的是4957073
        BaseRequestSimple request = CommonTestUtils.createBaseSimpleInfo(mtParam);
        GetPoiIdsResponse data = channelBaseMsgThriftService.batchGetPoiIds(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, data:{}",method, data);
        Assert.isTrue(data.status.code == 0, String.format("case1:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStoreIds().stream().noneMatch(info -> info.equals(disabledStoreCode)), String.format("case1:%s.error count is wrong!!",method));

        // Case2: 租户渠道未开通
        request.setChannelId(elmParam.getChannelId());
        data = channelBaseMsgThriftService.batchGetPoiIds(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, {}", method, data);
        Assert.isTrue(data.status.code == 10, String.format("case2:%s the status of result is wrong!!", method));
        Assert.isTrue(CollectionUtils.isEmpty(data.getStoreIds()), String.format("case2:%s.error count is wrong!!",method));
        Assert.isTrue(data.getStatus().getMsg().equals("渠道未开通"), String.format("case2:%s.error count is wrong!!",method));
    }

    @Test
    public void batchGetPoiDetailsTestForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        // Case1: 部分渠道门店禁用线上数据同步, 1000017的poi code是t_7YbuOcCwuZ, 1000019的是4957073
        BatchGetPoiDetailsRequest request = new BatchGetPoiDetailsRequest();
        request.setStoreIds(mtParam.getStoreIds().stream().map(storeId -> storeId.toString()).collect(Collectors.toList()));
        request.setTenantId(mtParam.getTenantId());
        request.setChannelId(mtParam.getChannelId());
        GetPoiInfoResponse data = channelBaseMsgThriftService.batchGetPoiDetails(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, data:{}",method, data);
        Assert.isTrue(data.status.code == 0, String.format("case1:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getPoiInfoList().stream().noneMatch(info -> info.getAppPoiCode().equals(disabledStoreCode)), String.format("case1:%s.error count is wrong!!",method));

        // Case2: 租户渠道未开通
        request.setChannelId(elmParam.getChannelId());
        data = channelBaseMsgThriftService.batchGetPoiDetails(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, {}", method, data);
        Assert.isTrue(data.status.code == 10, String.format("case2:%s the status of result is wrong!!", method));
        Assert.isTrue(CollectionUtils.isEmpty(data.getPoiInfoList()), String.format("case2:%s.error count is wrong!!",method));
        Assert.isTrue(data.getStatus().getMsg().equals("渠道未开通或渠道门店未启用线上数据同步"), String.format("case2:%s.error count is wrong!!",method));

        // Case3: 部分渠道门店禁用线上数据同步
        request.setStoreIds(Lists.newArrayList(mtParam.getStoreIds().get(1).toString()));
        request.setChannelId(mtParam.getChannelId());
        data = channelBaseMsgThriftService.batchGetPoiDetails(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, {}", method, data);
        Assert.isTrue(data.status.code == 10, String.format("case3:%s the status of result is wrong!!", method));
        Assert.isTrue(CollectionUtils.isEmpty(data.getPoiInfoList()), String.format("case3:%s.error count is wrong!!",method));
        Assert.isTrue(data.getStatus().getMsg().equals("渠道未开通或渠道门店未启用线上数据同步"), String.format("case3:%s.error count is wrong!!",method));
    }

    @Test
    public void batchGetCategoryTestForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        CatRequest request = new CatRequest();
        BaseRequestSimple baseInfo = CommonTestUtils.createBaseSimpleInfo(mtParam);
        request.setStoreId(mtParam.getStoreIds().get(1)); //1000019
        request.setBaseInfo(baseInfo);
        request.setIds(Lists.newArrayList("0"));

        // Case1: 渠道门店禁用线上数据同步
        GetCategoryResponse data = channelBaseMsgThriftService.batchGetCategory(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, data:{}",method, data);
        Assert.isTrue(data.status.code == 10, String.format("case1:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().contains("未开通!"), String.format("case1:%s.error count is wrong!!",method));

        // Case2: 租户渠道未开通
        baseInfo.setChannelId(elmParam.getChannelId());
        data = channelBaseMsgThriftService.batchGetCategory(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, {}", method, data);
        Assert.isTrue(data.status.code == 10, String.format("case2:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().equals("渠道未开通"), String.format("case2:%s.error count is wrong!!",method));
    }

    @Test
    public void batchGetBrandTestForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        GetBrandRequest request = new GetBrandRequest();
        BaseRequestSimple baseInfo = CommonTestUtils.createBaseSimpleInfo(mtParam);
        request.setBaseInfo(baseInfo);

        // Case1: 租户渠道开通，调用超时，暂时先注释掉
//        GetBrandResponse data = channelBaseMsgThriftService.batchGetBrand(request);
//        log.info("ChannelBaseMsgThriftServiceTest.{}, data:{}",method, data);
//        Assert.isTrue(data.status.code == 0, String.format("case1:%s the status of result is wrong!!", method));

        // Case2: 租户渠道未开通
        baseInfo.setChannelId(elmParam.getChannelId());
        GetBrandResponse data = channelBaseMsgThriftService.batchGetBrand(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, {}", method, data);
        Assert.isTrue(data.status.code == 10, String.format("case2:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().equals("渠道未开通"), String.format("case2:%s.error count is wrong!!",method));
    }

    @Test
    public void batchGetSkuInfoTestForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        BatchGetSkuInfoRequest request = new BatchGetSkuInfoRequest();
        BaseRequestSimple baseInfo = CommonTestUtils.createBaseSimpleInfo(mtParam);
        request.setBaseInfo(baseInfo);
        request.setStoreId(mtParam.getStoreIds().get(1));

        // Case1: 渠道门店禁用线上数据同步
        BatchGetSkuInfoResponse data = channelBaseMsgThriftService.batchGetSkuInfo(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, data:{}",method, data);
        Assert.isTrue(data.status.code == 10, String.format("case1:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().contains("未开通!"), String.format("case2:%s.error count is wrong!!",method));

        // Case2: 租户渠道未开通
        baseInfo.setChannelId(elmParam.getChannelId());
        data = channelBaseMsgThriftService.batchGetSkuInfo(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, {}", method, data);
        Assert.isTrue(data.status.code == 10, String.format("case2:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().equals("渠道未开通"), String.format("case2:%s.error count is wrong!!",method));
    }

    @Test
    public void batchGetChannelStoreCatInfoTestForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        CatRequest request = new CatRequest();
        BaseRequestSimple baseInfo = CommonTestUtils.createBaseSimpleInfo(mtParam);
        request.setStoreId(mtParam.getStoreIds().get(1)); //1000019
        request.setBaseInfo(baseInfo);
        request.setIds(Lists.newArrayList("0"));

        // Case1: 渠道门店禁用线上数据同步
        GetCategoryResponse data = channelBaseMsgThriftService.batchGetChannelStoreCatInfo(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, data:{}",method, data);
        Assert.isTrue(data.status.code == 10, String.format("case1:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().contains("未开通!"), String.format("case1:%s.error count is wrong!!",method));

        // Case2: 租户渠道未开通
        baseInfo.setChannelId(elmParam.getChannelId());
        data = channelBaseMsgThriftService.batchGetChannelStoreCatInfo(request);
        log.info("ChannelBaseMsgThriftServiceTest.{}, {}", method, data);
        Assert.isTrue(data.status.code == 10, String.format("case2:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getStatus().getMsg().equals("渠道未开通"), String.format("case2:%s.error count is wrong!!",method));
    }
}
