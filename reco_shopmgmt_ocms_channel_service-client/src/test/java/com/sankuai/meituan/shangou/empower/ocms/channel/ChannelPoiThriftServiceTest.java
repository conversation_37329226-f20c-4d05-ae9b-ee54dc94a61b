package com.sankuai.meituan.shangou.empower.ocms.channel;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ChannelPoiIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiPrebookDaysUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiPromotionInfoUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.PoiShippingTimeUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi.ShippingTime;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/3 11:51
 * @Description:
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelPoiThriftServiceTest {

    @Resource
    private ChannelPoiThriftService.Iface channelPoiThriftService;


    @Test
    public void testOpen() throws TException {
        ChannelPoiIdRequest channelPoiIdRequest = new ChannelPoiIdRequest();

        channelPoiIdRequest.setTenantId(1000011L);
        channelPoiIdRequest.setChannelId(100);
        channelPoiIdRequest.setChannelPoiCode("t_7YbuOcCwuZ");
        channelPoiThriftService.poiOpen(channelPoiIdRequest);
    }



    @Test
    public void testClose() throws TException {
        ChannelPoiIdRequest channelPoiIdRequest = new ChannelPoiIdRequest();

        channelPoiIdRequest.setTenantId(1000011L);
        channelPoiIdRequest.setChannelId(100);
        channelPoiIdRequest.setChannelPoiCode("t_7YbuOcCwuZ");
        channelPoiThriftService.poiClose(channelPoiIdRequest);
    }


    @Test
    public void testUpdatePromotionInfo() throws TException {
        PoiPromotionInfoUpdateRequest channelPoiIdRequest = new PoiPromotionInfoUpdateRequest();

        channelPoiIdRequest.setTenantId(1000011L);
        channelPoiIdRequest.setChannelId(100);
        channelPoiIdRequest.setChannelPoiCode("t_7YbuOcCwuZ");
        channelPoiIdRequest.setPromotionInfo("这是测试的公告,此门店为测试门店,不会真的配送");
        channelPoiThriftService.poiPromotionInfoUpdate(channelPoiIdRequest);
    }

    @Test
    public void testUpdateShippingTime() throws TException {
        PoiShippingTimeUpdateRequest channelPoiIdRequest = new PoiShippingTimeUpdateRequest();

        channelPoiIdRequest.setTenantId(1000011L);
        channelPoiIdRequest.setChannelId(100);
        channelPoiIdRequest.setChannelPoiCode("4957074");

        List<ShippingTime> shippingTimes = Lists.newArrayList();
        shippingTimes.add(new ShippingTime("09:00", "12:00"));
        shippingTimes.add(new ShippingTime("13:00", "18:00"));
        shippingTimes.add(new ShippingTime("19:00", "22:00"));
        channelPoiIdRequest.setShippingTimes(shippingTimes);

//        Set<Integer> shippingDays = Sets.newHashSet();
//        shippingDays.addAll(Arrays.asList(1,4));
//        channelPoiIdRequest.setShippingDays(shippingDays);

        ResultStatus resultStatus = channelPoiThriftService.poiShippingTimeUpdate(channelPoiIdRequest);
        //System.out.println(JSONObject.toJSONString(resultStatus));
    }

    @Test
    public void testOpenPrebookStatus() throws TException {
        ChannelPoiIdRequest channelPoiIdRequest = new ChannelPoiIdRequest();

        channelPoiIdRequest.setTenantId(1000011L);
        channelPoiIdRequest.setChannelId(100);
        channelPoiIdRequest.setChannelPoiCode("4957074");

        ResultStatus resultStatus = channelPoiThriftService.poiPrebookStatusOpen(channelPoiIdRequest);
        //System.out.println(JSONObject.toJSONString(resultStatus));
    }

    @Test
    public void testClosePrebookStatus() throws TException {
        ChannelPoiIdRequest channelPoiIdRequest = new ChannelPoiIdRequest();

        channelPoiIdRequest.setTenantId(1000011L);
        channelPoiIdRequest.setChannelId(100);
        channelPoiIdRequest.setChannelPoiCode("4957074");

        ResultStatus resultStatus = channelPoiThriftService.poiPrebookStatusClose(channelPoiIdRequest);
        //System.out.println(JSONObject.toJSONString(resultStatus));
    }

    @Test
    public void testUpdatePrebookDays() throws TException {
        PoiPrebookDaysUpdateRequest request = new PoiPrebookDaysUpdateRequest();
        request.setTenantId(1000011L);
        request.setChannelId(100);
        request.setChannelPoiCode("4957074");
        request.setPrebookMinDays(3);
        request.setPrebookMaxDays(7);

        ResultStatus resultStatus = channelPoiThriftService.poiPrebookDaysUpdate(request);
        //System.out.println(JSONObject.toJSONString(resultStatus));
    }



    @Test
    public void testElmOpen() throws TException {
        ChannelPoiIdRequest channelPoiIdRequest = new ChannelPoiIdRequest();

        channelPoiIdRequest.setTenantId(1000011L);
        channelPoiIdRequest.setChannelId(200);
        channelPoiIdRequest.setChannelPoiCode("test_995381_62470");
        channelPoiThriftService.poiOpen(channelPoiIdRequest);
    }



    @Test
    public void testElmClose() throws TException {
        ChannelPoiIdRequest channelPoiIdRequest = new ChannelPoiIdRequest();

        channelPoiIdRequest.setTenantId(1000011L);
        channelPoiIdRequest.setChannelId(200);
        channelPoiIdRequest.setChannelPoiCode("test_995381_62470");
        channelPoiThriftService.poiClose(channelPoiIdRequest);
    }



    @Test
    public void testElmUpdatePromotionInfo() throws TException {
        PoiPromotionInfoUpdateRequest channelPoiIdRequest = new PoiPromotionInfoUpdateRequest();

        channelPoiIdRequest.setTenantId(1000011L);
        channelPoiIdRequest.setChannelId(200);
        channelPoiIdRequest.setChannelPoiCode("test_995381_62470");
        channelPoiIdRequest.setPromotionInfo("这是测试的公告,此门店为测试门店,不会真的配送");
        channelPoiThriftService.poiPromotionInfoUpdate(channelPoiIdRequest);
    }

    @Test
    public void testElmUpdateShippingTime() throws TException {
        PoiShippingTimeUpdateRequest channelPoiIdRequest = new PoiShippingTimeUpdateRequest();

        channelPoiIdRequest.setTenantId(1000011L);
        channelPoiIdRequest.setChannelId(200);
        channelPoiIdRequest.setChannelPoiCode("test_995381_62470");

        List<ShippingTime> shippingTimes = Lists.newArrayList();
        shippingTimes.add(new ShippingTime("09:00", "10:00"));
        shippingTimes.add(new ShippingTime("13:00", "18:00"));
        shippingTimes.add(new ShippingTime("19:00", "22:00"));
        channelPoiIdRequest.setShippingTimes(shippingTimes);
        ResultStatus resultStatus = channelPoiThriftService.poiShippingTimeUpdate(channelPoiIdRequest);
        //System.out.println(JSONObject.toJSONString(resultStatus));
    }

}
