package com.sankuai.meituan.shangou.empower.ocms.channel;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.AgreeRefundRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.BatchPullPhoneRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.BatchPullPhoneResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetOrderStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetOrderStatusResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GoodsSettlementInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GoodsSettlementResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderShouldSettlementInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.OrderShouldSettlementResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiAdjustOrderRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiAdjustOrderResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiCancelOrderRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiConfirmOrderRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiConfirmReceiveGoodsRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PoiPartRefundRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.PreparationMealCompleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.RejectRefundRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelOrderThriftServiceTest {

    @Resource
    private ChannelOrderDockingThriftService.Iface copOrderDockingThriftService;

    private int channelId = 100;
    private int unOpenedChannelId = 200;
    private long tenantId = 1000011L;
    private long storeId = 1000017L;
    private long unOpenedStoreId = 1000019L;
    private String orderId = "27058913440520561";
    //private String orderId = "1565166270022988084";

    @Test
    public void getChannelOrderDetail() throws TException {
        GetChannelOrderDetailRequest orderDetailRequest = new GetChannelOrderDetailRequest();
        orderDetailRequest.setTenantId(1000011).setChannelId(200).setOrderId(orderId);
        GetChannelOrderDetailResult channelOrderDetailResult = copOrderDockingThriftService
            .getChannelOrderDetail(orderDetailRequest);
        log.info("ChannelOrderThriftServiceTest.testPictureUpload, {}", channelOrderDetailResult);
        Assert.isTrue(channelOrderDetailResult.status.code == 0, "getChannelOrderDetail Error!!");
    }

    @Test
    public void testPoiConfirmOrder() throws TException {
        PoiConfirmOrderRequest request = new PoiConfirmOrderRequest();
        request.setChannelId(channelId);
        request.setIsAgreed(true);
        request.setOrderId(orderId);
        request.setTenantId(tenantId);
        request.setOperator("sys");//自动接单设置操作人-京东需要操作人

        ResultStatus result = copOrderDockingThriftService.poiConfirmOrder(request);
    }

    @Test
    public void testGetChannelOrderDetail() throws TException {
        GetChannelOrderDetailRequest request = new GetChannelOrderDetailRequest();
        request.setChannelId(channelId);
        request.setOrderId(orderId);
        request.setTenantId(tenantId);
        GetChannelOrderDetailResult result = copOrderDockingThriftService.getChannelOrderDetail(request);
    }

    @Test
    public void testPreparationMealComplete() throws TException {
        PreparationMealCompleteRequest request = new PreparationMealCompleteRequest();
        request.setChannelId(channelId);
        request.setOrderId(orderId);
        request.setTenantId(tenantId);
        ResultStatus resultStatus = copOrderDockingThriftService.preparationMealComplete(request);
    }

    @Test
    public void testAgreeRefund() throws TException {
        AgreeRefundRequest request = new AgreeRefundRequest();
        request.setChannelId(channelId);
        request.setOrderId(orderId);
        request.setReason("vvvv");
        request.setTenantId(tenantId);
        request.setAfsApplyType(1234);
        log.info("商家同意退款,request:{}", request);
        ResultStatus resultStatus = copOrderDockingThriftService.agreeRefund(request);
    }

    @Test
    public void testRejectRefund() throws TException {
        RejectRefundRequest request = new RejectRefundRequest();
        request.setChannelId(channelId);
        request.setOrderId(orderId);
        request.setReason("vvvv");
        request.setTenantId(tenantId);
        request.setAfsApplyType(1234);
        log.info("商家驳回退款,request:{}", request);
        ResultStatus resultStatus = copOrderDockingThriftService.rejectRefund(request);
        log.info("商家驳回退款,order:{},response:{}", request.getOrderId(), resultStatus);
    }

    @Test
    public  void testGetOrderStatus() throws TException {
        GetOrderStatusRequest request = new GetOrderStatusRequest();
        request.setChannelId(channelId);
        request.setOrderId(orderId);
        request.setTenantId(tenantId);
        GetOrderStatusResult result = copOrderDockingThriftService.getOrderStatus(request);
    }

    @Test
    public void testBatchPullPhone() throws TException {
        BatchPullPhoneRequest batchPullPhoneRequest = new BatchPullPhoneRequest();
        batchPullPhoneRequest.setChannelId(channelId);
        batchPullPhoneRequest.setTenantId(tenantId);
        batchPullPhoneRequest.setOffset(0);
        batchPullPhoneRequest.setLimit(100);
        BatchPullPhoneResult result = copOrderDockingThriftService.batchPullPhone(batchPullPhoneRequest);
        Assert.isTrue(result.getStatus().getCode() == 0, result.status.msg);

        batchPullPhoneRequest.setChannelId(unOpenedChannelId);
        BatchPullPhoneResult result2 = copOrderDockingThriftService.batchPullPhone(batchPullPhoneRequest);
        Assert.isTrue(result2.getStatus().getCode() == 0, result.status.msg);
    }

    @Test
    public void testPoiCancelOrder() throws TException {
        PoiCancelOrderRequest poiCancelOrderRequest = new PoiCancelOrderRequest();
        poiCancelOrderRequest.setChannelId(channelId);
        poiCancelOrderRequest.setOrderId(orderId);
        poiCancelOrderRequest.setReason("request.getReason()");
        poiCancelOrderRequest.setTenantId(tenantId);
        poiCancelOrderRequest.setReason_code(1);
        poiCancelOrderRequest.setOperatorId("eeee");
        //poiCancelOrderRequest.se

        log.info("商家取消订单,请求:{}", poiCancelOrderRequest);
        ResultStatus resultStatus = copOrderDockingThriftService.poiCancelOrder(poiCancelOrderRequest);

    }

    @Test
    public void testPoiPartRefundApply() throws TException {
        PoiPartRefundRequest request = new PoiPartRefundRequest();
        request.setOrderId(orderId);
//        List<String> skuIdList = refundRequest.getRefundProductInfos().stream().map(e -> e.getSkuId()).collect(Collectors.toList());
//        //内部skuId转customerSkuId
//        Map<String, String> skuId2CustomerSkuIdMap = channelSkuService.findCustomSkuIdBySkuIds(refundRequest.getTenantId(), refundRequest.getChannelType().getValue(), refundRequest.getShopId(), skuIdList, true);
//        log.info("商家部分退款，转换sku, order:{}, mapping:{}", refundRequest.getChannelOrderId(), skuId2CustomerSkuIdMap);
//        request.setRefundProducts(convertRefundProduct(refundRequest.getChannelOrderId(), refundRequest.getRefundProductInfos(), skuId2CustomerSkuIdMap));
        request.setChannelId(channelId);
        request.setTenantId(tenantId);
        request.setReason("vvvvv");
        request.setOperatorId("gggg");
        request.setReasonCode(-1);
        log.info("商家发起部分退款申请，req:{}", request);
        ResultStatus result = copOrderDockingThriftService.poiPartRefundApply(request);
    }

    @Test
    public void testPoiModifyOrder() throws TException {
        PoiAdjustOrderRequest request = new PoiAdjustOrderRequest();
        request.setChannelId(channelId);
        request.setOptUser("optuser");
        request.setOrderId(orderId);
        request.setReason("reason");
        request.setTenantId(tenantId);

        PoiAdjustOrderResult result = copOrderDockingThriftService.poiModifyOrder(request);
        log.info("调整订单结果，order：{}, 结果:{}", orderId, result);

    }

    @Test
    public void testConfirmReceiveGoods() throws TException {
        PoiConfirmReceiveGoodsRequest request = new PoiConfirmReceiveGoodsRequest();
        request.setChannelId(channelId);
        request.setOperateTime(System.currentTimeMillis());
        request.setOrderId(orderId);
        request.setTenantId(tenantId);
        log.info("商家请求收到退货，req:{}", request);

        ResultStatus resultStatus = copOrderDockingThriftService.confirmReceiveGoods(request);
        log.info("商家取消订单，订单:{}, 结果:{}", request.getOrderId(), resultStatus);
    }

    @Test
    public void testQueryOrderShouldSettlementInfo() throws TException {
        OrderShouldSettlementInfoRequest request = new OrderShouldSettlementInfoRequest();
        request.setChannelId(channelId);
        request.setOrderId(orderId);
        request.setTenantId(tenantId);
        log.info("请求商家实收金额，req:{}", request);
        OrderShouldSettlementResult result = copOrderDockingThriftService.queryOrderShouldSettlementInfo(request);
        log.info("请求商家实收金额，order:{}, resp:{}", orderId, result);
    }

    @Test
    public void testQueryGoodsSettlementInfo() throws TException {
        GoodsSettlementInfoRequest request = new GoodsSettlementInfoRequest();
        request.setChannelId(channelId);
        request.setOrderId(orderId);
        request.setTenantId(tenantId);
        log.info("请求商家商品活动分摊金额，req:{}", request);
        GoodsSettlementResult result = copOrderDockingThriftService.queryGoodsSettlementInfo(request);
        log.info("请求商家商品活动分摊金额，order:{}, resp:{}", orderId, result);
    }

    @Test
    public void testGetLogisticsStatus() throws TException {
        GetLogisticsStatusRequest request = new GetLogisticsStatusRequest();
        request.setChannelId(channelId);
        request.setOrderId(orderId);
        request.setTenantId(tenantId);
        GetLogisticsStatusResult result = copOrderDockingThriftService.getLogisticsStatus(request);
    }
}
