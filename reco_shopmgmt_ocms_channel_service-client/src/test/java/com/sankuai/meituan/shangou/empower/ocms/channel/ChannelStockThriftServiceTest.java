package com.sankuai.meituan.shangou.empower.ocms.channel;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelStockThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Title: ChannelPriceThriftServiceTest
 * @Description:
 * <AUTHOR>
 * @Date 2019/9/11 20:22
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelStockThriftServiceTest {
    @Resource
    private ChannelStockThriftService.Iface channelStockThriftService;

    private final TestConstant.BaseParamEnum mtParam = TestConstant.BaseParamEnum.MT;
    private final TestConstant.BaseParamEnum elmParam = TestConstant.BaseParamEnum.ELM;

    @Test
    public void updateStockMultiChannelForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SkuStockMultiChannelRequest request = new SkuStockMultiChannelRequest();
        request.setTenantId(mtParam.getTenantId());
        request.setSkuStockList(createMultiChannelDTOs(mtParam, CommonTestUtils.TEST_ITEM_COUNT));

        // Case1: 美团渠道，部分渠道门店禁用线上数据同步
        ResultData result = channelStockThriftService.updateStockMultiChannel(request);
        Assert.isTrue(result.status.code == 0, String.format("case1:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT, String.format("case1:%s count is wrong!!", method));

        // Case2: 租户渠道未开通
        request.setSkuStockList(createMultiChannelDTOs(elmParam, CommonTestUtils.TEST_ITEM_COUNT));
        result = channelStockThriftService.updateStockMultiChannel(request);
        Assert.isTrue(result.status.code == 0, String.format("case2:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT * 2, String.format("case2:%s count is wrong!!", method));

    }

    private List<SkuStockMultiChannelDTO> createMultiChannelDTOs(TestConstant.BaseParamEnum param, int dataCount){
        List<SkuStockMultiChannelDTO> list = Lists.newArrayList();
        param.getStoreIds().forEach(storeId -> {
            for (int i = 1;i <= dataCount; i ++){
                SkuStockMultiChannelDTO dto = new SkuStockMultiChannelDTO();
                dto.setChannelId(param.getChannelId());
                dto.setStoreId(storeId);
                dto.setSkuId("ZT-156265413173895" + i);
                dto.setChannelSkuId("114598120254139601" + i);
                dto.setStockQty(618);
                dto.setTimestamp(System.currentTimeMillis());
                list.add(dto);
            }
        });
        return list;
    }

    @Test
    public void updateStockForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        BaseRequest baseInfo = CommonTestUtils.createBaseInfo(mtParam);
        SkuStockRequest request = new SkuStockRequest();
        request.setBaseInfo(baseInfo);
        List<SkuStockDTO> list = new ArrayList<>();
        for (int i = 1; i<=CommonTestUtils.TEST_ITEM_COUNT; i++){
            list.add(new SkuStockDTO().setSkuId("************" + i).setStockQty(10).setTimestamp(System.currentTimeMillis()));
        }
        request.setSkuStockList(list);

        // Case1: 部分渠道门店禁用线上数据同步
        baseInfo.setChannelId(mtParam.getChannelId());
        ResultData result = channelStockThriftService.updateStock(request);
        Assert.isTrue(result.status.code == 0, String.format("case1:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT, String.format("case1:%s count is wrong!!", method));

        // Case2: 租户渠道未开通
        baseInfo.setChannelId(elmParam.getChannelId());
        result = channelStockThriftService.updateStock(request);
        Assert.isTrue(result.status.code == 0, String.format("case2:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT * 2, String.format("case2:%s count is wrong!!", method));
    }
}
