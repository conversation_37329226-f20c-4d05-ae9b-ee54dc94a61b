package com.sankuai.meituan.shangou.empower.ocms.channel;

import com.google.common.base.Stopwatch;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSkuThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelSkuThriftServiceTest {

    private final TestConstant.BaseParamEnum mtParam = TestConstant.BaseParamEnum.MT;
    private final TestConstant.BaseParamEnum elmParam = TestConstant.BaseParamEnum.ELM;
    private final TestConstant.BaseParamEnum jdParam = TestConstant.BaseParamEnum.JDDJ;

    @Autowired(required = false)
    private ChannelSkuThriftService.Iface copSkuThriftService;

    private List<SkuInfoDTO> createSkuInfoDTOs(int count){
        List<SkuInfoDTO> skuInfoDTOs = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            SkuInfoDTO dto = new SkuInfoDTO();
            dto.setUpc("6948960102161");
            dto.setFrontCategoryName("测试类目11");
            // dto.setCategory("20309");
            // dto.setCategory("10012436272");
            dto.setSkuId("2019031692122"+i);
            if (i==1) {
                dto.setName("测试类目11测试类目11测试类目11测试类目11测试类目11"+1);
            } else {
                dto.setName("测试商品世纪风ee"+1);
            }

            dto.setBoxPrice(1.2);
            dto.setBoxQuantity(1);
            dto.setBrand("35247");
            // dto.setBrand("1457391037");
//            dto.setBrand("1514613263");
            dto.setFrontCategory("1");
            dto.setSkuStatus((byte)1);
            dto.setPrice(888);
            dto.setSpec("500g/份");
            dto.setStock(1);
            dto.setWeight(10);
            dto.setMinPurchaseQuantity(1);
            dto.setUnit("单位");

            // 饿了么测试分类 1：15512618670642 2：15512620220655 15518556650614
            // dto.setChannelFrontCategory("15518556650614");
            dto.setChannelFrontCategory("69");
            List<String> pictures = Lists.newArrayList();
            // pictures.add("7F1BF850B86759F2443076DB5D738C28");
            // pictures.add("https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1552400438799&di=b77325927590c2a11b53f1c49bb49abc&imgtype=0&src=http%3A%2F%2Fimg.jdzj.com%2FUserDocument%2F2015c%2FIron510%2FPicture%2F2016718151455.jpg");
            pictures.add("http://101.236.62.10/pic/eapi/77e117e102eb524a100eff2d4b778437_1552535168238.png?AWSAccessKeyId=689e9e56b7de41f9951b8e42e2a5ba07&Expires=1565675168&Signature=i3e5k4KBFS8GB5XPep%2FO52XUcAQ%3D");
            dto.setPictures(pictures);
            skuInfoDTOs.add(dto);
        }
        return skuInfoDTOs;
    }

    @Test
    public void deleteSku() throws TException {
        SkuInfoDeleteRequest request = new SkuInfoDeleteRequest();
        request.setBaseInfo(new BaseRequest().setTenantId(1000011)
            .setChannelId(200)
            .setStoreIdList(Lists.newArrayList(1000033L)))
            .setParamList(Lists.newArrayList(
                new SkuInfoDeleteDTO().setSkuId("155184843206283")));
        ResultData resultData = copSkuThriftService.deleteSku(request);
        Assert.isTrue(resultData.status.code == 0, "deleteSku Error!!");
        log.info("ChannelSkuThriftServiceTest.deleteSku, {}", resultData);
    }

    @Test
    public void getPictureUploadStatus() throws TException {
        PictureUploadStatusRequest request = new PictureUploadStatusRequest();
        request.setBaseInfo(new BaseRequest().setTenantId(1000011)
            .setChannelId(300)
            .setStoreIdList(Lists.newArrayList(1000017L)))
            .setParam(new PictureUploadStatusDTO()
                .setChannelSkuIdList(Lists.newArrayList("2024898154")));
        PictureUploadResult pictureUploadResult = copSkuThriftService.getPictureUploadStatus(request);
        log.info("ChannelSkuThriftServiceTest.getPictureUploadStatus, {}", pictureUploadResult);
        Assert.isTrue(pictureUploadResult.status.code == 0, "getPictureUploadStatus Error!!");
    }

    @Test
    public void updateSkuSellStatusForInterceptor() throws TException {
        // 租户1000011L未开通渠道200
        // 门店1000017L在渠道100禁用线上数据同步
        SkuSellStatusInfoRequest request = new SkuSellStatusInfoRequest();
        List<SkuSellStatusInfoDTO> paramList = new ArrayList<>();
        List<SkuIdDTO> skuIdDTOS = new ArrayList<>();
        ResultData data;
        skuIdDTOS.add(new SkuIdDTO("1169590659187851337", "305196971997261292344"));
        mtParam.getStoreIds().forEach(storeId ->
                paramList.add(new SkuSellStatusInfoDTO().setSkuId(skuIdDTOS).setSkuStatus(2).setStoreId(storeId))
        );

        // Case1: 部分渠道门店禁用线上数据同步
        request.setBaseInfo(new BaseRequestSimple().setTenantId(mtParam.getTenantId()).setChannelId(mtParam.getChannelId())).setParamList(paramList);
        data = copSkuThriftService.updateSkuSellStatus(request);
        log.info("ChannelSkuThriftServiceTest.updateSkuSellStatus, {}", data);
        Assert.isTrue(data.status.code == 0, "case1:updateSkuSellStatus the status of result is wrong!!");
        Assert.isTrue(data.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == 1, "case1:updateSkuSellStatus.error count is wrong!!");

        // Case2: 租户渠道未开通
//        request.setBaseInfo(new BaseRequestSimple().setTenantId(elmParam.getTenantId()).setChannelId(elmParam.getChannelId())).setParamList(paramList);
//        data = copSkuThriftService.updateSkuSellStatus(request);
//        log.info("ChannelSkuThriftServiceTest.updateSkuSellStatus, {}", data);
//        Assert.isTrue(data.status.code == 0, "case2:updateSkuSellStatus case2 the status of result is wrong!!");
//        Assert.isTrue(data.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == 2, "case2:updateSkuSellStatus.error count is wrong!!");
    }

    // 京东到家：2022614463
    @Test
    public void testSkuCreateForInterceptor() {
        skuInfoRequestCommonTest(request -> {
            try {
                return copSkuThriftService.skuCreate(request);
            } catch (TException e) {
                e.printStackTrace();
            }
            return null;
        });
    }

    @Test
    public void testUpcCreateForInterceptor() {
        skuInfoRequestCommonTest(request -> {
            try {
                return copSkuThriftService.upcCreate(request);
            } catch (TException e) {
                e.printStackTrace();
            }
            return null;
        });
    }

    @Test
    public void testUpcUpdateForInterceptor() {
        skuInfoRequestCommonTest(request -> {
            try {
                return copSkuThriftService.updateSku(request);
            } catch (TException e) {
                e.printStackTrace();
            }
            return null;
        });
    }

    private void skuInfoRequestCommonTest(Function<SkuInfoRequest, ResultData> skuFunction){
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SkuInfoRequest request = new SkuInfoRequest();
        BaseRequest baseInfo = CommonTestUtils.createBaseInfo(mtParam);
        request.setBaseInfo(baseInfo);
        request.setParamList(createSkuInfoDTOs(CommonTestUtils.TEST_ITEM_COUNT));

        // Case1: 部分渠道门店禁用线上数据同步
        baseInfo.setChannelId(mtParam.getChannelId());

        Stopwatch stopwatch = Stopwatch.createStarted();
        ResultData result = skuFunction.apply(request);
        log.info("ChannelSkuThriftServiceTest.{}, result:{}",method, result);
        long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("{} cost={}",method,elapsed);
        Assert.isTrue(result.status.code == 0, String.format("case1:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT, String.format("case1:%s count is wrong!!", method));

        // Case2: 租户渠道未开通
//        baseInfo.setChannelId(elmParam.getChannelId());
//        result = skuFunction.apply(request);
//        log.info("ChannelSkuThriftServiceTest.{}, result:{}",method, result);
//        elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
//        log.info("{} cost={}",method,elapsed);
//        Assert.isTrue(result.status.code == 0, String.format("case2:%s Error!!", method));
//        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT * 2, String.format("case2:%s count is wrong!!", method));
    }

    @Test
    public void deleteSkuForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SkuInfoDeleteRequest request = new SkuInfoDeleteRequest();
        BaseRequest baseInfo = CommonTestUtils.createBaseInfo(mtParam);
        request.setBaseInfo(baseInfo);
        List<SkuInfoDeleteDTO> paramList = new ArrayList<>();
        for(int i = 1; i <= CommonTestUtils.TEST_ITEM_COUNT; i++){
            paramList.add(new SkuInfoDeleteDTO().setSkuId("15518484320628" + i));
        }
        request.setParamList(paramList);

        // Case1: 部分渠道门店禁用线上数据同步
        baseInfo.setChannelId(mtParam.getChannelId());

        Stopwatch stopwatch = Stopwatch.createStarted();
        ResultData result = copSkuThriftService.deleteSku(request);
        log.info("ChannelSkuThriftServiceTest.{}, result:{}",method, result);
        long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("{} cost={}",method,elapsed);
        Assert.isTrue(result.status.code == 0, String.format("case1:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT, String.format("case1:%s count is wrong!!", method));

        // Case2: 租户渠道未开通
//        baseInfo.setChannelId(elmParam.getChannelId());
//        result = copSkuThriftService.deleteSku(request);
//        log.info("ChannelSkuThriftServiceTest.{}, result:{}",method, result);
//        elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
//        log.info("{} cost={}",method,elapsed);
//        Assert.isTrue(result.status.code == 0, String.format("case2:%s Error!!", method));
//        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT * 2, String.format("case2:%s count is wrong!!", method));
    }

    @Test
    public void getPictureUploadStatusForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PictureUploadStatusRequest request = new PictureUploadStatusRequest();
        BaseRequest baseInfo = CommonTestUtils.createBaseInfo(jdParam);
        request.setBaseInfo(baseInfo)
                .setParam(new PictureUploadStatusDTO()
                        .setChannelSkuIdList(Lists.newArrayList("6970155722307", "6970155722308")));

        // Case1: 渠道门店禁用线上数据同步
        Stopwatch stopwatch = Stopwatch.createStarted();
        PictureUploadResult result = copSkuThriftService.getPictureUploadStatus(request);
        log.info("ChannelSkuThriftServiceTest.{}, result:{}",method, result);
        long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("{} cost={}",method,elapsed);
        Assert.isTrue(result.status.code == 10, String.format("case1:%s status Error!!", method));
        Assert.isTrue(result.status.getMsg().equals("渠道未开通或渠道门店未启用线上数据同步"), String.format("case1:%s msg Error!!", method));

        // Case2: 租户渠道未开通, 目前这个方法只在JD实现了，所以需要改数据库才能测试这个case
//        Assert.isTrue(result.status.code == 10, String.format("case2:%s status Error!!", method));
//        Assert.isTrue(result.status.getMsg().equals("渠道未开通"), String.format("case2:%s msg Error!!", method));
    }

    @Test
    public void updateCustomSkuIdForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        UpdateCustomSkuIdRequest request = new UpdateCustomSkuIdRequest();
        BaseRequestSimple baseInfo = new BaseRequestSimple()
                .setTenantId(mtParam.getTenantId())
                .setChannelId(mtParam.getChannelId());
        request.setBaseInfo(baseInfo);

        List<UpdateCustomSkuIdDTO> paramList = new ArrayList<>();
        mtParam.getStoreIds().forEach(storeId -> {
            for (int i = 1; i<= CommonTestUtils.TEST_ITEM_COUNT;i++){
                UpdateCustomSkuIdDTO dto = new UpdateCustomSkuIdDTO();
                dto.setStoreId(storeId);
                dto.setCustomSkuId("ZT-11535003142849126522" + i);
                dto.setSkuId("ZT-11535003142849126522" + i);
                dto.setSkuName("健力宝" + i);
                dto.setSpec("500ml");
                dto.setCategoryName("测试二级1-1");
                paramList.add(dto);
            }
        });
        request.setParamList(paramList);

        // Case1: 部分渠道门店禁用线上数据同步
        baseInfo.setChannelId(mtParam.getChannelId());

        Stopwatch stopwatch = Stopwatch.createStarted();
        ResultData result = copSkuThriftService.updateCustomSkuId(request);
        log.info("ChannelSkuThriftServiceTest.{}, result:{}",method, result);
        long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("{} cost={}",method,elapsed);
        Assert.isTrue(result.status.code == 0, String.format("case1:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT, String.format("case1:%s count is wrong!!", method));

        // Case2: 租户渠道未开通
        baseInfo.setChannelId(elmParam.getChannelId());
        result = copSkuThriftService.updateCustomSkuId(request);
        log.info("ChannelSkuThriftServiceTest.{}, result:{}",method, result);
        elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("{} cost={}",method,elapsed);
        Assert.isTrue(result.status.code == 0, String.format("case2:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT * 2, String.format("case2:%s count is wrong!!", method));
    }

    @Test
    public void urlPictureUploadForInterceptor() {
        pictureUpLoadCommonTest(request -> {
            try {
                return copSkuThriftService.urlPictureUpload(request);
            } catch (TException e) {
                e.printStackTrace();
            }
            return null;
        });
    }

    @Test
    public void pictureUploadForInterceptor() {
        pictureUpLoadCommonTest(request -> {
            try {
                return copSkuThriftService.pictureUpload(request);
            } catch (TException e) {
                e.printStackTrace();
            }
            return null;
        });
    }

    @Test
    public void defaultPictureUploadForInterceptor() {
        pictureUpLoadCommonTest(request -> {
            try {
                return copSkuThriftService.defaultPictureUpload(request);
            } catch (TException e) {
                e.printStackTrace();
            }
            return null;
        });
    }

    private void pictureUpLoadCommonTest(Function<PictureUploadRequest, ResultData> skuFunction){
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PictureUploadRequest request = new PictureUploadRequest();
        BaseRequest baseInfo = CommonTestUtils.createBaseInfo(mtParam);
        request.setBaseInfo(baseInfo);
        List<PictureUploadDTO> paramList = new ArrayList<>();
        PictureUploadDTO data;
        for (int i=0;i<CommonTestUtils.TEST_ITEM_COUNT;i++) {
            data = new PictureUploadDTO();
            data.setUrl("http://s3plus.sankuai.com/eapi/86ba4f8e0e626179ca55bc2f2896ee85_1552878413361.jpg?AWSAccessKeyId=689e9e56b7de41f9951b8e42e2a5ba07&Expires=1868238413&Signature=0co%2BqNily4jfVvA%2BvMnprmtBERI%3D");
            data.setPictureName("f49fdab56bf668c9ee995d95366efe84_1551173323150.jpg");
            data.setUid("DFEE5227D7F246E996931EC9B7146CAB"+i);
            paramList.add(data);
        }

        request.setParamList(paramList);

        // Case1: 部分渠道门店禁用线上数据同步
        baseInfo.setChannelId(mtParam.getChannelId());

        Stopwatch stopwatch = Stopwatch.createStarted();
        ResultData result = skuFunction.apply(request);
        log.info("ChannelSkuThriftServiceTest.{}, result:{}",method, result);
        long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("{} cost={}",method,elapsed);
        Assert.isTrue(result.status.code == 0, String.format("case1:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT, String.format("case1:%s count is wrong!!", method));

        // Case2: 租户渠道未开通
        baseInfo.setChannelId(elmParam.getChannelId());
        result = skuFunction.apply(request);
        log.info("ChannelSkuThriftServiceTest.{}, result:{}",method, result);
        elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("{} cost={}",method,elapsed);
        Assert.isTrue(result.status.code == 0, String.format("case2:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT * 2, String.format("case2:%s count is wrong!!", method));
    }

    @Test
    public void getSkuInfoForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        GetSkuInfoRequest request = new GetSkuInfoRequest();
        BaseRequestSimple baseInfo = new BaseRequestSimple()
                .setTenantId(mtParam.getTenantId())
                .setChannelId(mtParam.getChannelId());
        request.setCustomSkuId("mtcode_1128271876691370049");
        request.setBaseInfo(baseInfo);

        // Case1: 渠道门店禁用线上数据同步
        request.setStoreId(mtParam.getStoreIds().get(1)/* 1000019 */);
        GetSkuInfoResponse result = copSkuThriftService.getSkuInfo(request);
        Assert.isTrue(result.status.code == 10, String.format("case1:%s status Error!!", method));
        Assert.isTrue(result.status.getMsg().contains("未开通!"), String.format("case1:%s msg Error!!", method));

        // Case1: 租户渠道未开通
        baseInfo.setChannelId(elmParam.getChannelId());
        result = copSkuThriftService.getSkuInfo(request);
        Assert.isTrue(result.status.code == 10, String.format("case2:%s status Error!!", method));
        Assert.isTrue(result.status.getMsg().equals("渠道未开通"), String.format("case2:%s msg Error!!", method));
    }
}
