package com.sankuai.meituan.shangou.empower.ocms.channel;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.onelimiter.LimitResult;
import com.dianping.rhino.onelimiter.OneLimiter;
import com.dianping.rhino.onelimiter.OneLimiterConstants;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.Tracer;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.channel.client.ChannelPriceThriftClient;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultData;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuPriceMultiChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPriceThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @Title: ChannelPriceThriftServiceTest
 * @Description:
 * <AUTHOR>
 * @Date 2019/5/28 12:14
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelPriceThriftServiceTest {
    private final TestConstant.BaseParamEnum mtParam = TestConstant.BaseParamEnum.MT;
    private final TestConstant.BaseParamEnum elmParam = TestConstant.BaseParamEnum.ELM;
    private static final AtomicLong count = new AtomicLong(0);
    private static final ThreadPoolExecutor threadPoolExecutor;
    public static ThreadLocal<Integer> threadLocal = new ThreadLocal();
    static {
        // 原生线程池
        threadPoolExecutor = new ThreadPoolExecutor(20, 20, 10, TimeUnit.MINUTES, new ArrayBlockingQueue<>(200),
                new ThreadFactoryBuilder().setNameFormat("CommonTaskThreadPool-%d").build());
    }

    @Resource
    private ChannelPriceThriftService.Iface channelPriceThriftService;

    @Resource
    private ChannelPriceThriftClient channelPriceThriftClient;
    private static final OneLimiter ONE_LIMITER = Rhino.newOneLimiter();
    @Test
    public void testAsyncPriceUpdate1() throws TException, InterruptedException {

        List<Callable<Integer>> callables = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            String num = String.valueOf(i);
            callables.add(()->{
                Tracer.serverRecv();
                Map<String, String> params = Maps.newHashMap();
                params.put(OneLimiterConstants.UUID, "123");
                LimitResult result = ONE_LIMITER.run("elm_sku.price.update.batch", params);
                log.info("thread " + num + " result:"+ result + " time "  + System.currentTimeMillis() + " " + Tracer.getServerSpan().getTraceId());
                Tracer.serverSend();
                return 0;
            });
        }
        threadPoolExecutor.invokeAll(callables);
        Thread.sleep(5000);
    }

    @Test
    public void testAsyncPriceUpdate() throws TException, InterruptedException {
        SkuPriceMultiChannelRequest request = new SkuPriceMultiChannelRequest();
        request.setTenantId(1000011L);
        request.setAsyncInvoke(true);
        SkuPriceMultiChannelDTO skuPriceMultiChannelDTO1 = new SkuPriceMultiChannelDTO();
        skuPriceMultiChannelDTO1.setSkuId("2207981009");
        skuPriceMultiChannelDTO1.setStoreId(1000017);
        skuPriceMultiChannelDTO1.setChannelId(200);
        skuPriceMultiChannelDTO1.setPrice(1.7);
        skuPriceMultiChannelDTO1.setTimestamp(System.nanoTime() / 1000);
        request.setParamList(Arrays.asList(skuPriceMultiChannelDTO1));
        List<Callable<Integer>> callables = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            String num = String.valueOf(i);
            callables.add(()->{
                ResultData resultData = channelPriceThriftClient.updatePriceMultiChannel(request);
                System.out.println("thread " + num + " result:"+ resultData.getStatus().getCode() +" " + resultData.getSucData().size() + " "  + System.currentTimeMillis());
                return 0;
            });
        }
        callables.add(() -> {
            SkuPriceMultiChannelRequest request2 = new SkuPriceMultiChannelRequest();
            request2.setTenantId(1000011L);
            request2.setAsyncInvoke(false);
            request2.setParamList(Arrays.asList(skuPriceMultiChannelDTO1));
            ResultData resultData = channelPriceThriftClient.updatePriceMultiChannel(request2);
            System.out.println("thread sync result:"+ resultData.getStatus().getCode());
            return 0;
        });

        threadPoolExecutor.invokeAll(callables);
//        threadPoolExecutor.awaitTermination(10000, TimeUnit.SECONDS);
    }


    @Test
    public void testUpdatePriceMultiChannel() {
        SkuPriceMultiChannelRequest request = new SkuPriceMultiChannelRequest();
        request.setTenantId(1000011L);
        List<SkuPriceMultiChannelDTO> paramList = Lists.newArrayList();
        request.setParamList(paramList);
        SkuPriceMultiChannelDTO skuPriceMultiChannelDTO = new SkuPriceMultiChannelDTO();
        skuPriceMultiChannelDTO.setSkuId("2207981009");
        skuPriceMultiChannelDTO.setStoreId(1000017);
        skuPriceMultiChannelDTO.setChannelId(100);
        skuPriceMultiChannelDTO.setPrice(2.2);
        skuPriceMultiChannelDTO.setTimestamp(System.nanoTime() / 1000);
        paramList.add(skuPriceMultiChannelDTO);

        SkuPriceMultiChannelDTO skuPriceMultiChannelDTO1 = new SkuPriceMultiChannelDTO();
        skuPriceMultiChannelDTO1.setSkuId("2207981009");
        skuPriceMultiChannelDTO1.setStoreId(1000017);
        skuPriceMultiChannelDTO1.setChannelId(200);
        skuPriceMultiChannelDTO1.setPrice(1.7);
        skuPriceMultiChannelDTO1.setTimestamp(System.nanoTime() / 1000);
        paramList.add(skuPriceMultiChannelDTO1);

        SkuPriceMultiChannelDTO skuPriceMultiChannelDTO2 = new SkuPriceMultiChannelDTO();
        skuPriceMultiChannelDTO2.setSkuId("2388726");
        skuPriceMultiChannelDTO2.setStoreId(1000017);
        skuPriceMultiChannelDTO2.setChannelId(200);
        skuPriceMultiChannelDTO2.setPrice(1.7);
        skuPriceMultiChannelDTO2.setTimestamp(System.nanoTime() / 1000);
        paramList.add(skuPriceMultiChannelDTO2);

        for (int i = 0; i < 5; i++) {
            int seq = i;
            new Thread(() -> {
                try {
                    long start = System.currentTimeMillis();
                    ResultData resultData = channelPriceThriftService.updatePriceMultiChannel(request);
                    log.info("{} response:{} cost:{}", seq, JacksonUtils.simpleSerialize(resultData), System.currentTimeMillis() - start);
                } catch (TException e) {
                    log.error("error", e);
                }
            }).start();

        }
        try {
            TimeUnit.SECONDS.sleep(60);
        } catch (InterruptedException e) {
            //do nothing
        }
    }

    @Test
    public void updatePriceMultiChannelForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SkuPriceMultiChannelRequest request = new SkuPriceMultiChannelRequest();
        request.setTenantId(mtParam.getTenantId());
        request.setParamList(createMultiChannelSkuPriceDTOs(mtParam, CommonTestUtils.TEST_ITEM_COUNT));

        // Case1: 美团渠道，部分渠道门店禁用线上数据同步
        ResultData result = channelPriceThriftService.updatePriceMultiChannel(request);
        Assert.isTrue(result.status.code == 0, String.format("case1:%s Error!!", method));
        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT, String.format("case1:%s count is wrong!!", method));

        // Case2: 租户渠道未开通
//        request.setParamList(createMultiChannelSkuPriceDTOs(elmParam, CommonTestUtils.TEST_ITEM_COUNT));
//        result = channelPriceThriftService.updatePriceMultiChannel(request);
//        Assert.isTrue(result.status.code == 0, String.format("case2:%s Error!!", method));
//        Assert.isTrue(result.errorData.stream().filter(error -> error.getErrorCode() == 10).count() == CommonTestUtils.TEST_ITEM_COUNT * 2, String.format("case2:%s count is wrong!!", method));

    }

    private List<SkuPriceMultiChannelDTO> createMultiChannelSkuPriceDTOs(TestConstant.BaseParamEnum param, int dataCount){
        List<SkuPriceMultiChannelDTO> list = Lists.newArrayList();
        param.getStoreIds().forEach(storeId -> {
            for (int i = 1;i <= dataCount; i ++){
                SkuPriceMultiChannelDTO dto = new SkuPriceMultiChannelDTO();
                dto.setSkuId("238872" + i);
                dto.setStoreId(storeId);
                dto.setChannelId(param.getChannelId());
                dto.setPrice(1.7);
                dto.setTimestamp(System.currentTimeMillis());
                list.add(dto);
            }
        });
        return list;
    }
}
