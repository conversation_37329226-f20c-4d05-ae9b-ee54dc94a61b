package com.sankuai.meituan.shangou.empower.ocms.channel;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;

/**
 * @Description: 通用测试帮助类
 * <AUTHOR>
 * @Date 2019-09-11 20:14
 */
public class CommonTestUtils {

    public static final int TEST_ITEM_COUNT = 2;


    public static BaseRequest createBaseInfo(TestConstant.BaseParamEnum param){
        BaseRequest baseInfo = new BaseRequest();
        baseInfo.setStoreIdList(param.getStoreIds());
        baseInfo.setTenantId(param.getTenantId());
        baseInfo.setChannelId(param.getChannelId());
        return baseInfo;
    }

    public static BaseRequestSimple createBaseSimpleInfo(TestConstant.BaseParamEnum param){
        BaseRequestSimple baseSimpleInfo = new BaseRequestSimple();
        baseSimpleInfo.setTenantId(param.getTenantId());
        baseSimpleInfo.setChannelId(param.getChannelId());
        return baseSimpleInfo;
    }
}
