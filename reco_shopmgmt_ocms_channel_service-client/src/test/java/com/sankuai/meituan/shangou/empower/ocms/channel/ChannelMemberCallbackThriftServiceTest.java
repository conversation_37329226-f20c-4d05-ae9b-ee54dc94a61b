package com.sankuai.meituan.shangou.empower.ocms.channel;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member.MemberCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member.MemberCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member.MemberQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member.MemberQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelMemberCallbackThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * @Title: ChannelMemberCallbackThriftServiceTest
 * @Description:
 * @Author: mixiaofeng
 * @Date: 2019-09-09 21:06
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelMemberCallbackThriftServiceTest {
    @Resource
    private ChannelMemberCallbackThriftService.Iface copMemberCallbackThriftService;

    private int channelId = 200;
    private long tenantId = 1000011L;
    private long storeId = 1000017L;
    private String orderId = "15508992626236";

    @Test
    public void testGetMember() throws TException {
        MemberQueryRequest request = new MemberQueryRequest();
        request.setSig("a");
        request.setChannelCode("mt");
        request.setTenantAppId("2752");
        request.setTimestamp(System.currentTimeMillis()/1000);
        request.setAppPoiCode("t_7YbuOcCwuZ");

        request.setMobile("13222272711");
        MemberQueryResponse response = copMemberCallbackThriftService.getMember(request);
        Assert.isTrue(response.code == 1, "testGetMember");
    }

    @Test
    public void testCreateMember() throws TException {
        MemberCreateRequest request = new MemberCreateRequest();
        request.setTenantAppId("2752");
        request.setTimestamp(System.currentTimeMillis()/1000);
        request.setAppPoiCode("t_7YbuOcCwuZ");
        request.setSig("a");
        request.setChannelCode("mt");

        request.setMobile("13222272711");
        request.setName("zzzt");
        request.setGender(1);
        request.setBirthday("1992-01-01");
        //request.setCardCode("");

        MemberCreateResponse response = copMemberCallbackThriftService.createMember(request);
        Assert.isTrue(response.code == 1, "testCreateMember");
    }
}
