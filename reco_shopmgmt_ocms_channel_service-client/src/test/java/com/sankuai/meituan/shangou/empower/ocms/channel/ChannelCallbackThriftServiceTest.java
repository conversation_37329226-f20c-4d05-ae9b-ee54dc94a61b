package com.sankuai.meituan.shangou.empower.ocms.channel;

import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.SkuNotifyRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCallbackThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * @Title:
 * @Description:
 * <AUTHOR>
 * @Date 2019-09-15 20:47
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ChannelCallbackThriftServiceTest {
    @Resource
    private ChannelCallbackThriftService.Iface channelCallbackService;

    @Test
    public void skuNotifyForInterceptor() throws TException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        SkuNotifyRequest request = new SkuNotifyRequest();
        request.setTenantAppId("62470");
        request.setChannelCode("elm");
        request.setAction("newSkuNotify");
        request.setData("%5b%7b%22app_food_code%22%3a%22mtcode_1128271876691370049%22%2c%22app_id%22%3a2828%2c%22app_poi_code%22%3a%22t_7YbuOcCwuZ%22%2c%22categoryName%22%3a%22%e8%8f%8c%e8%8f%87%22%2c%22diffContents%22%3a%22%7b%5c%22skus%5c%22%3a%5b%7b%5c%22diffContentMap%5c%22%3a%7b%5c%22stock%5c%22%3a%7b%5c%22result%5c%22%3a-1%2c%5c%22origin%5c%22%3a0%7d%2c%5c%22is_sold_out%5c%22%3a%7b%5c%22result%5c%22%3a1%2c%5c%22origin%5c%22%3a0%7d%7d%2c%5c%22id%5c%22%3a%5c%22%5c%22%7d%2c%7b%5c%22diffContentMap%5c%22%3a%7b%5c%22stock%5c%22%3a%7b%5c%22result%5c%22%3a-1%2c%5c%22origin%5c%22%3a0%7d%2c%5c%22is_sold_out%5c%22%3a%7b%5c%22result%5c%22%3a1%2c%5c%22origin%5c%22%3a0%7d%7d%2c%5c%22id%5c%22%3a%5c%22%5c%22%7d%5d%7d%22%2c%22name%22%3a%22%e7%99%bd%e8%98%91%e8%8f%87%22%2c%22opAppKey%22%3a%22%e5%95%86%e5%ae%b6%e7%ab%af_%e7%a7%bb%e5%8a%a8%e7%ab%af%22%2c%22opName%22%3a%22%e5%95%86%e5%ae%b6%e8%b4%a6%e5%8f%b70%22%2c%22timestamp%22%3a1561530816%2c%22utime%22%3a1561038838%7d%5d");

        // Case1: 渠道未开通，需要修改数据库，mt渠道未开通来验证
        ResultStatus data = channelCallbackService.skuNotify(request);
        log.info("ChannelCallbackThriftService.{}, data:{}",method, data);
        Assert.isTrue(data.getCode() == 10, String.format("case1:%s the status of result is wrong!!", method));
        Assert.isTrue(data.getMsg().equals("渠道未开通"), String.format("case1:%s.error count is wrong!!",method));
    }
}
