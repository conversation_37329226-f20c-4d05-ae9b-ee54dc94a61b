namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.settlement

include "ResultStatus.thrift"
include "PageInfo.thrift"
/**
 * @TypeDoc(
 *     description = "分页查询渠道账单汇总列表参数"
 * )
 */
struct ChannelSettlementPageRequest{

        /**
         * @FieldDoc(
         *     description = "租户id",
         *     example = "1"
         * )
         */
        1: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "123"
         * )
         */
        2: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = "123"
         * )
         */
        3: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "账单开始时间",
         *     example = "123"
         * )
         */
        4: i64 accountTimeStart;

        /**
         * @FieldDoc(
         *     description = "账单结束时间",
         *     example = "123"
         * )
         */
        5: i64 accountTimeEnd;
        /**
         * @FieldDoc(
         *     description = "账单状态",
         *     example = "123"
         * )
         */
        6: i32 settlementStatus;
        /**
         * @FieldDoc(
         *     description = "页码",
         *     example = "123"
         * )
         */
        7: i32 pageNo;
        /**
         * @FieldDoc(
         *     description = "每页多少条",
         *     example = "123"
         * )
         */
        8: i32 pageSize;

}


/**
 * @TypeDoc(
 *     description = "结算账户信息"
 * )
 */
struct SettlementAccountInfo{

        /**
         * @TypeDoc(
         *     description = "收款人"
         * )
         */
        1: string payeeName;

        /**
         * @TypeDoc(
         *     description = "银行类型"
         * )
         */
        2: string bankType;

        /**
         * @TypeDoc(
         *     description = "银行账户类型（对公还是对私）"
         * )
         */
        3: string bankAccountType;

        /**
         * @TypeDoc(
         *     description = "银行具体支行名称"
         * )
         */
        4: string bankSubName;

        /**
         * @TypeDoc(
         *     description = "银行城市"
         * )
         */
        5: string bankCity;

        /**
         * @TypeDoc(
         *     description = "银行所在省份"
         * )
         */
        6: string bankProvince;

}

/**
 * @TypeDoc(
 *     description = "费用信息"
 * )
 */
struct ChannelSettlementFeeDTO{

        /**
         * @TypeDoc(
         *     description = "商品总额"
         * )
         */
        i32 totalGoodsAmount;

        /**
         * @TypeDoc(
         *     description = "商家承担补贴"
         * )
         */
        i32 storeSubsidyAmount;

        /**
         * @TypeDoc(
         *     description = "平台服务费"
         * )
         */
        i32 platformChargeFee;

        /**
         * @TypeDoc(
         *     description = "商家自配送费用"
         * )
         */
        i32 storeFreightFee;

        /**
         * @TypeDoc(
         *     description = "包装费用"
         * )
         */
        i32 packageBillAmount;

        /**
         * @TypeDoc(
         *     description = "取件服务费"
         * )
         */
        i32 pickUpAmount;

        /**
         * @TypeDoc(
         *     description = "公益捐款"
         * )
         */
        i32 donationAmount;

        /**
         * @TypeDoc(
         *     description = "取件服务费不开票"
         * )
         */
        i32 pickUpFeeNotAmount;

}

struct SettlementFeeDetail{

        1: string feeKey;

        2: string feeDesc;

        3: i64 feeValue;
}


struct SettlementExtraInfoDetail{

        1: string feeKey;

        2: string feeDesc;

        3: string feeValue;
}


/**
 * @TypeDoc(
 *     description = "渠道账单汇总"
 * )
 */
struct ChannelSettlementDTO {
        /**
         * @TypeDoc(
         *     description = "渠道id"
         * )
         * ChannelTypeEnum
         */
        1: i32 channelId;

        /**
         * @TypeDoc(
         *     description = "结算单id"
         * )
         */
        2: string settleOrderId;

        /**
         * @TypeDoc(
         *     description = "结算单状态"
         * )
         */
        3: i32 settleStatus;

        /**
         * @TypeDoc(
         *     description = "结算单状态"
         * )
         */
        4: string settleStatusDesc;

        /**
         * @TypeDoc(
         *     description = "结算完成日期"
         * )
         */
        5: i64 settleFinishDate;

        /**
         * @TypeDoc(
         *     description = "结算金额"
         * )
         */
        6: i64 settleAmount;

        /**
         * @TypeDoc(
         *     description = "备注信息"
         * )
         */
        7: string remark;

        /**
         * @TypeDoc(
         *     description = "账期开始时间"
         * )
         */
        8: i64 accountTimeStart;

        /**
         * @TypeDoc(
         *     description = "账期结束时间"
         * )
         */
        9: i64 accountTimeEnd;
        /**
         * @TypeDoc(
         *     description = "结算方式"
         * )
         */
        10: i32 payMethod;

        /**
        * @TypeDoc(
        *     description = "结算方式"
        * )
        */
        11: string payMethodDesc;

        /**
         * @TypeDoc(
         *     description = "结算账户"
         * )
         */
        12: string accountNo;

        /**
         * @TypeDoc(
         *     description = "账户信息 包含
         *     payeeName:收款人
         *     如果结算方式为银行卡 则需要关注以下字段
         *     bankType:银行类型
         *     bankAccountType:银行账户类型（对公还是对私）
         *     bankSubName:银行具体支行名称
         *     bankCity:银行城市
         *     bankProvince:银行所在省份
         *     outFlowId:打款凭据
         *     "
         * )
         */
        13: SettlementAccountInfo accountInfo;

        /**
         * @TypeDoc(
         *     description = "打款凭据"
         * )
         */
        14: string outFlowId;

        /**
         * @TypeDoc(
         *     description = "费用信息"
         * )
         */
        15: ChannelSettlementFeeDTO channelSettlementFeeDto;

        16: list<SettlementFeeDetail> settlementFeeDetailList;

        17: i32 orderCount;

        /**
         * @TypeDoc(
         *     description = "账单原始数据"
         * )
         */
        18: string originalSettlementData;

}

/**
 * @TypeDoc(
 *     description = "渠道账单汇总分页信息"
 * )
 */
struct ChannelSettlementPageDTO{

        /**
         * @TypeDoc(
         *     description = "页码信息"
         * )
         */
        1: PageInfo.PageInfo pageInfo;

        /**
         * @TypeDoc(
         *     description = "渠道账单汇总列表"
         * )
         */
        2: list<ChannelSettlementDTO> channelSettlementDtoList;
}


/**
 * @TypeDoc(
 *     description = "渠道账单汇总列表"
 * )
 */
struct ChannelSettlementPageResponse{

        /**
         * @TypeDoc(
         *     description = "状态"
         * )
         */
        1: ResultStatus.ResultStatus resultStatus;

        /**
         * @TypeDoc(
         *     description = "渠道账单汇总分页信息"
         * )
         */
        2: ChannelSettlementPageDTO channelSettlementPageDto;
}

/**
* @TypeDoc(
*     description = "通过id查询渠道账单明细请求"
* )
*/
struct ChannelOrderSettlementByIdRequest{

        /**
         * @FieldDoc(
         *     description = "租户id",
         *     example = "1"
         * )
         */
        1: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "123"
         * )
         */
        2: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "结算单号",
         *     example = "123"
         * )
         */
        3: string settlementId;

        4: i32 pageNo;

        5: i32 pageSize;

        6: i64 shopId;
}


/**
* @TypeDoc(
*     description = "商品参与活动详情"
* )
*/
struct ChannelSettlementSkuActivityDetail{

        /**
        * @TypeDoc(
        *     description = "活动ID"
        * )
        *
        */
        1: string actId;

        /**
        * @TypeDoc(
        *     description = "活动类型"
        * )
        *
        */
        2: string type;

        /**
        * @TypeDoc(
        *     description = "本活动id及活动类型下商家承担的金额，单位分"
        * )
        *
        */
        3: i32 poiCharge;
}


/**
* @TypeDoc(
*     description = "渠道订单结算，商品优惠详情"
* )
*/
struct ChannelSettlementSkuBenefitDetail{

        /**
        * @TypeDoc(
        *     description = "spu"
        * )
        *
        */
        1: string spu;

        /**
        * @TypeDoc(
        *     description = "sku"
        * )
        *
        */
        2: string skuId;

        /**
        * @TypeDoc(
        *     description = "sku"
        * )
        *
        */
        3: string skuName;

        /**
        * @TypeDoc(
        *     description = "商品数量，count=0时，表示退差价"
        * )
        */
        4: i32 count;

        /**
        * @TypeDoc(
        *     description = "商品原价总价，单位分"
        * )
        *
        */
        5: i32 totalOriginPrice;

        /**
        * @TypeDoc(
        *     description = "商品实付价总价，单位分"
        * )
        *
        */
        6: i32 totalActivityPrice;

        /**
       * @TypeDoc(
       *     description = "商品优惠总金额，包括商家承担金额和美团承担金额，单位分"
       * )
       *
       */
        7: i32 totalReducePrice;

        /**
         * @TypeDoc(
         *     description = "商品优惠商家承担总金额，单位分。"
         * )
         *
         */
        8: i32 totalPoiCharge;

        /**
         * @TypeDoc(
         *     description = "商品参与活动详情，json格式数组。"
         * )
         *
         */
        9: list<ChannelSettlementSkuActivityDetail> wmAppOrderActDetails;

}


/**
* @TypeDoc(
*     description = "sku商品参与的配送费活动详情"
* )
*/
struct ChannelSettlementSkuShippingActDetail{
        /**
        * @TypeDoc(
        *     description = "参与配送费活动的活动类型"
        * )
        *
        */
        1: string type;

        /**
        * @TypeDoc(
        *     description = "本活动id及活动类型下商家承担的金额，单位分"
        * )
        *
        */
        2: i32 poiCharge;
}

/**
* @TypeDoc(
*     description = "商家承担配送费活动分摊明细"
* )
*/
struct ChannelSettlementSkuShippingDetail{

        /**
        * @TypeDoc(
        *     description = "sku"
        * )
        *
        */
        1: string skuId;

        /**
        * @TypeDoc(
        *     description = "sku"
        * )
        *
        */
        2: string skuName;

        /**
        * @TypeDoc(
        *     description = "商品数量，count=0时，表示退差价"
        * )
        */
        3: i32 count;

        /**
        * @TypeDoc(
        *     description = "商品原价总价，单位分"
        * )
        *
        */
        4: i32 totalOriginPrice;

        /**
         * @TypeDoc(
         *     description = "商品优惠商家承担总金额，单位分。"
         * )
         *
         */
        5: i32 totalPoiCharge;

        /**
         * @TypeDoc(
         *     description = "sku商品参与的配送费活动详情"
         * )
         *
         */
        6: list<ChannelSettlementSkuShippingActDetail> wmAppOrderShippingActDetailList;

}

/**
* @TypeDoc(
*     description = "渠道账单明细"
* )
*/
struct ChannelOrderSettlementDTO{
        /**
         * @TypeDoc(
         *     description = "渠道id"
         * )
         *
         */
        1: i32 channelId;

        /**
         * @TypeDoc(
         *     description = "结算单id"
         * )
         */
        2: string channelSettleOrderId;

        /**
         * @TypeDoc(
         *     description = "渠道订单交易类型"
         * )
         */
        4: i32 orderChargeType;

        /**
         * @TypeDoc(
         *     description = "结算金额（单位：毫；1分 = 100毫）"
         * )
         */
        5: i64 settleMilliAmt;

        /**
         * @TypeDoc(
         *     description = "渠道订单id"
         * )
         */
        6: string channelOrderId;

        /**
         * @TypeDoc(
         *     description = "结算时间"
         * )
         *
         */
        9: i64 settlementDate;

        /**
         * @TypeDoc(
         *     description = "渠道结算类型描述"
         * )
         *
         */
        12: string orderChargeTypeDesc;

        /**
         * @TypeDoc(
         *     description = "下单时间"
         * )
         *
         */
        13: i64 orderTime;

        /**
          * @TypeDoc(
          *     description = "原始结算状态"
          * )
          *
          */
        14: string rawSettleStatus;
        /**
         * @TypeDoc(
         *     description = "原始结算状态描述"
         * )
         */
        17: string rawSettleStatusDesc;

        16: list<SettlementFeeDetail> settlementFeeDetailList;

        /**
         * @TypeDoc(
         *     description = "美团账单日期"
         * )
         */
        19: i64 settlementFinishDate;

        //mt 特有参数

        /**
         * @TypeDoc(
         *     description = "退款id"
         * )
         */
        20: string refundId;

        /**
         * @TypeDoc(
         *     description = "退款时间"
         * )
         */
        21: i64 refundTime;

        /**
         * @TypeDoc(
         *     description = "订单完成时间"
         * )
         */
        22: i64 orderFinishTime;

        23: string outId;

        //elm 特有参数
        /**
         * @TypeDoc(
         *     description = "订单序号"
         * )
         */
        24: string orderIndex;

        /**
         * @TypeDoc(
         *     description = "饿百订单号"
         * )
         */
        25: string extOrderId;

        26: string id;

        /**
        * @TypeDoc(
        *     description = "商家承担成本的商品优惠分摊明细"
        * )
        */
        27: list<ChannelSettlementSkuBenefitDetail> skuBenefitDetailList;

        /**
        * @TypeDoc(
        *     description = "商家承担配送费活动分摊明细"
        * )
        */
        28: list<ChannelSettlementSkuShippingDetail> wmAppOrderSkuShippingDetailList;

        //mt 特有参数
        /**
        * @TypeDoc(
        *     description = "标识退款是部分退还是退差价：billChargeType为26时，1代表退差价，0代表部分退"
        * )
        */
        29: i32 chargeFeeType;

        30: list<SettlementExtraInfoDetail> settlementExtraInfoDetailList;

        /**
         * @TypeDoc(
         *     description = "渠道店铺ID（目前只有京东使用）"
         * )
         */
        31: optional string channelShopId;

        /**
         * @TypeDoc(
         *     description = "渠道账单原始数据"
         * )
         */
        32: optional string originalOrderSettlementData;


}


/**
 * @TypeDoc(
 *     description = "渠道账单明细汇总信息"
 * )
 */
struct ChannelOrderSettlementSummary{
        /**
         * @TypeDoc(
         *     description = "数据总条数"
         * )
         */
        1: i32 totalCount;

        /**
         * @TypeDoc(
         *     description = "账单金额总数（单位：毫；1分 = 100毫）"
         * )
         */
        2: i64 totalSettleMilliSum;

}

/**
 * @TypeDoc(
 *     description = "渠道账单明细分页信息"
 * )
 */
struct ChannelOrderSettlementPageDTO{
        /**
         * @TypeDoc(
         *     description = "分页信息"
         * )
         */
        1: PageInfo.PageInfo pageInfo;

        /**
         * @TypeDoc(
         *     description = "渠道账单明细"
         * )
         */
        2: list<ChannelOrderSettlementDTO> channelOrderSettlementDtoList;

        /**
         * @TypeDoc(
         *     description = "渠道账单明细汇总"
         * )
         */
        3: ChannelOrderSettlementSummary channelOrderSettlementSummary;
}


/**
* @TypeDoc(
*     description = "渠道账单汇总及分页明细"
* )
*/
struct ChannelSettlementAndDetailResponse{

        /**
        * @TypeDoc(
        *     description = "状态"
        * )
        */
        1: ResultStatus.ResultStatus resultStatus;

        /**
         * @TypeDoc(
         *     description = "渠道账单汇总"
         * )
         */
        2: ChannelSettlementDTO channelSettlementDto;

        /**
         * @TypeDoc(
         *     description = "渠道账单明细分页信息"
         * )
         */
        3: ChannelOrderSettlementPageDTO channelOrderSettlementPageDto;
}


/**
 * @TypeDoc(
 *     description = "分页查询渠道账单明细请求"
 * )
 */
struct ChannelOrderSettlementPageRequest{
        /**
          * @FieldDoc(
          *     description = "租户id",
          *     example = "1"
          * )
          */
        1: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "123"
         * )
         */
        2: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = "123"
         * )
         */
        3: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "账单开始时间",
         *     example = "123"
         * )
         */
        4: i64 accountTimeStart;

        /**
         * @FieldDoc(
         *     description = "账单结束时间",
         *     example = "123"
         * )
         */
        5: i64 accountTimeEnd;

        /**
         * @FieldDoc(
         *     description = "页码",
         *     example = "123"
         * )
         */
        7: i32 pageNo;
        /**
         * @FieldDoc(
         *     description = "每页多少条",
         *     example = "123"
         * )
         */
        8: i32 pageSize;

}

/**
 * @TypeDoc(
 *     description = "分页查询渠道账单明细返回"
 * )
 */
struct ChannelOrderSettlementPageResponse{

        /**
         * @TypeDoc(
         *     description = "状态"
         * )
         */
        1: ResultStatus.ResultStatus resultStatus;

        /**
         * @TypeDoc(
         *     description = "渠道账单明细分页信息"
         * )
         */
        2: ChannelOrderSettlementPageDTO channelOrderSettlementPageDto;

}

/**
 * @TypeDoc(
 *     description = "分页查询渠道账单"
 * )
 */
struct QnhChannelOrderSettlementPageRequest{
        /**
         * @FieldDoc(
         *     description = "页码",
         *     example = "123"
         * )
         */
        1: i32 pageNo;
        /**
         * @FieldDoc(
         *     description = "每页多少条",
         *     example = "123"
         * )
         */
        2: i32 pageSize;

        /**
         * @FieldDoc(
         *     description = "租户id",
         *     example = "1"
         * )
         */
        3: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "123"
         * )
         */
        4: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = "123"
         * )
         */
        5: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "账单开始时间",
         *     example = "123"
         * )
         */
        6: i64 accountTimeStart;

        /**
         * @FieldDoc(
         *     description = "账单结束时间",
         *     example = "123"
         * )
         */
        7: i64 accountTimeEnd;
}

/**
 * @TypeDoc(
 *     description = "渠道账单明细分页信息"
 * )
 */
struct QnhChannelOrderSettlementDTO{
        /**
         * @TypeDoc(
         *     description = "渠道id"
         * )
         *
         */
        1: i32 channelId;

        /**
         * @TypeDoc(
         *     description = "渠道订单id"
         * )
         */
        2: string channelOrderId;

        /**
         * @TypeDoc(
         *     description = "下单时间"
         * )
         *
         */
        3: i64 orderTime;

        /**
         * @TypeDoc(
         *     description = "渠道订单交易类型"
         * )
         */
        4: i32 orderChargeType;

        /**
         * @TypeDoc(
         *     description = "渠道结算类型描述"
         * )
         *
         */
        5: string orderChargeTypeDesc;

        /**
         * @TypeDoc(
         *     description = "结算单id"
         * )
         */
        6: string outSettlementId;

        /**
         * @TypeDoc(
         *     description = "结算金额（单位：1分）"
         * )
         */
        7: i64 settlementAmt;

        /**
         * @TypeDoc(
         *     description = "结算时间"
         * )
         *
         */
        8: i64 settlementDate;

        /**
          * @TypeDoc(
          *     description = "结算状态"
          * )
          *
          */
        9: string rawSettleStatus;

        /**
         * @TypeDoc(
         *     description = "结算状态描述"
         * )
         */
        10: string rawSettleStatusDesc;

        /**
         * @TypeDoc(
         *     description = "结算金额明细"
         * )
         */
        11: list<SettlementFeeDetail> settlementFeeDetailList;

        /**
         * @TypeDoc(
         *     description = "配送方式"
         * )
         */
        12: string deliveryType;

        // 美团特有字段

        /**
         * @TypeDoc(
         *     description = "账单日期"
         * )
         */
        30: i64 settlementFinishDate;

        /**
         * @TypeDoc(
         *     description = "退款id"
         * )
         */
        31: string refundId;

        /**
         * @TypeDoc(
         *     description = "退款时间"
         * )
         */
        32: i64 refundTime;

        /**
        * @TypeDoc(
        *     description = "标识退款是部分退还是退差价：billChargeType为26时，1代表退差价，0代表部分退"
        * )
        */
        33: i32 chargeFeeType;

        /**
         * @TypeDoc(
         *     description = "订单完成时间"
         * )
         */
        34: i64 orderFinishTime;

        /**
        * @TypeDoc(
        *     description = "商家承担成本的商品优惠分摊明细"
        * )
        */
        35: list<ChannelSettlementSkuBenefitDetail> skuBenefitDetailList;

        /**
        * @TypeDoc(
        *     description = "商家承担配送费活动分摊明细"
        * )
        */
        36: list<ChannelSettlementSkuShippingDetail> orderSkuShippingDetailList;

        // 饿了么特有字段

        /**
         * @TypeDoc(
         *     description = "订单序号"
         * )
         */
        50: string orderIndex;

        51: list<SettlementExtraInfoDetail> settlementExtraInfoDetailList;

}

/**
 * @TypeDoc(
 *     description = "分页查询渠道账单明细返回"
 * )
 */
struct QnhChannelOrderSettlementPageResponse{

        /**
         * @TypeDoc(
         *     description = "状态"
         * )
         */
        1: ResultStatus.ResultStatus resultStatus;

        /**
         * @FieldDoc(
         *     description = "总记录数",
         *     example = ""
         * )
         */
        2: i32 totalCount;

        /**
         * @TypeDoc(
         *     description = "渠道账单明细"
         * )
         */
        3: list<QnhChannelOrderSettlementDTO> channelOrderSettlementDtoList;

}

/**
 * @TypeDoc(
 *     description = "分页查询对账单接口"
 * )
 */
struct ChannelBalanceBillPageRequest{

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = ""
         * )
         */
        1: optional list<i64> shopIds;

        /**
         * @FieldDoc(
         *     description = "订单号",
         *     example = "123"
         * )
         */
        2: optional list<i64> orderIds;

        /**
         * @FieldDoc(
         *     description = "订单类型，不填写为全部，1:异常调整,1001:正向订单,1002:售后退货,1003:售后退款,1004:售后退货申诉,1005:售后退款申诉,1006:售后换新申诉,1016:售后换新,1017:售后直赔",
         *     example = "1"
         * )
         */
        3: optional i32 orderType;

        /**
         * @FieldDoc(
         *     description = "订单状态 1:已完成",
         *     example = "1"
         * )
         */
        4: optional i32 orderStatus;

        /**
         * @FieldDoc(
         *     description = "结算状态，待打款(20002)、结算成功(20003)、结算失败(20004,20011)、驳回(20005)、打款失败(20009)、冻结中(20014)",
         *     example = "1"
         * )
         */
        5: optional i32 settleStatus;


        /**
         * @FieldDoc(
         *     description = "下单开始时间（20170706）与完成订单开始时间至少填写一个",
         *     example = "20170706"
         * )
         */
        6: optional string orderStartTime;

        /**
         * @FieldDoc(
         *     description = "下单截止时间（20170706）与完成订单截止时间至少填写一个",
         *     example = "20170706"
         * )
         */
        7: optional string orderEndTime;

        /**
         * @FieldDoc(
         *     description = "下单开始时间（20170706）与下单开始时间至少填写一个，天选业务表示代扣完成时间",
         *     example = "20170706"
         * )
         */
        8: optional string finishStartTime;

        /**
         * @FieldDoc(
         *     description = "下单截止时间，时间范围不能超3个月，天选业务表示代扣完成时间",
         *     example = "20170706"
         * )
         */
        9: optional string finishEndTime;

        /**
         * @FieldDoc(
         *     description = "页码 不传默认第一页",
         *     example = "1"
         * )
         */
        10: optional i32 pageNum;

        /**
         * @FieldDoc(
         *     description = "每页最高数量为200条，默认10",
         *     example = "10"
         * )
         */
        11: optional i32 pageSize;

        /**
         * @FieldDoc(
         *     description = "是否查询京东融合的对账单，需要查询京东融合对账单时填true，不传默认false",
         *     example = "true"
         * )
         */
        12: optional bool jdFusion;

        /**
         * @FieldDoc(
         *     description = "结算单号",
         *     example = ""
         * )
         */
        13: optional string jdPayReqId;

        /**
         * @FieldDoc(
         *     description = "付款单号",
         *     example = ""
         * )
         */
        14: optional string jdPayMark;

        /**
         * @FieldDoc(
         *     description = "1-到家账单，2-小时购账单，3-天选账单 （与jdFusion兼容） 【billType=1】等同jdFusion=false 【billType=2】 等同 jdFusion=true 【billType=3】 新类型,jdFusion无对应值 若jdFusion和billType同时有参数值，则以billType为准",
         *     example = "1"
         * )
         */
        15: optional i32 billType;

        /**
         * @FieldDoc(
         *     description = "小时购交易上行订单,针对billType=2 OC00007:用户提单对账单，NOC00007:主站小时购对账单",
         *     example = ""
         * )
         */
        16: optional string orderChannelDesc;

        /**
         * @FieldDoc(
         *     description = "租户id",
         *     example = "1"
         * )
         */
        17: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "123"
         * )
         */
        18: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = "123"
         * )
         */
        19: i64 shopId;
}

/**
 * @TypeDoc(
 *     description = "查询订单计费明细接口"
 * )
 */
struct ChannelCheckBillPageRequest{

        /**
         * @FieldDoc(
         *     description = "订单号（最大个数为50个，和下面的参数互斥、如果有订单号，则下面的参数都无效）",
         *     example = ""
         * )
         */
        1: optional list<i64> orderIds;

        /**
         * @FieldDoc(
         *     description = "订单下单开始时间（开始时间和结束时间必须同时存在，且时间段区间为2个月，不填默认为最近2个月订单）",
         *     example = "1234567890"
         * )
         */
        2: optional i64 startTime;

        /**
         * @FieldDoc(
         *     description = "订单下单结束时间",
         *     example = "9876543210"
         * )
         */
        3: optional i64 endTime;

        /**
         * @FieldDoc(
         *     description = "分页时的当前页（如果是按照订单开始和结束时间查询，则分页必须）",
         *     example = "1"
         * )
         */
        4: optional i32 pageNo;

        /**
         * @FieldDoc(
         *     description = "分页每页大小，默认为20",
         *     example = "20"
         * )
         */
        5: optional i32 pageSize;

        /**
         * @FieldDoc(
         *     description = "租户id",
         *     example = "1"
         * )
         */
        6: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "123"
         * )
         */
        7: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = "123"
         * )
         */
        8: i64 shopId;
}

/**
* @TypeDoc(
*     description = "分页查询对账单接口返回结果"
* )
*/
struct BalanceBillDTO{

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        1: i32 id;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        2: i64 orgCode;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        3: string orgName;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        4: i64 stationId;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        5: string stationName;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        6: i64 businessStartTime;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        7: i64 businessFinishTime;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        8: i64 orderId;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        9: i32 orderStatus;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        10: string goodsBill;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        11: string dueAmount;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        12: string settleStatus;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        13: i32 settleStatusCode;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        14: i64 settleOrderId;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        15: string settleOrderIdStr;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        16: string freightBill;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        17: string packageBill;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        18: string commission;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        19: string orderType;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        20: i32 orderTypeCode;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        21: string storeSubsidy;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        22: string subsidy;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        23: string createTime;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        24: string marketBill;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        25: i64 giftcardBill;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        26: i64 marketingServiceFee;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        27: i32 marketingServiceFeeStatus;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        28: string originalAmount;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        29: string paymentSubsidies;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        30: string storeFreightSubsidy;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        31: string storeFreightAmount;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        32: string pickupAmount;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        33: string pickupServiceAmount;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        34: string accountTime;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        35: i64 settleFinishTime;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        36: string deliveryTypeStr;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        37: string withdrawId;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        38: string orderSourceName;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        39: string payChannelName;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        40: string offlineSettlementAmount;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        41: i32 billOrderType;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        42: i32 ApiBillOrderType;

}

/**
* @TypeDoc(
*     description = "分页查询对账单接口返回结果"
* )
*/
struct SettleOrderPage{

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        1: i64 pageNum;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        2: i32 pageSize;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        3: i64 total;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        4: i32 startRow;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        5: i32 endRow;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        6: i32 pages;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        7: list<BalanceBillDTO> result;
}


/**
* @TypeDoc(
*     description = "分页查询对账单接口返回结果"
* )
*/
struct BalanceBillListContent{

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型，使用double的话会丢失精度，所以使用string"
        * )
        */
        1: string summaryDueAmount;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型，使用double的话会丢失精度，所以使用string"
        * )
        */
        2: string summaryGoodsBill;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型，使用double的话会丢失精度，所以使用string"
        * )
        */
        3: string currentDueAmount;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型，使用double的话会丢失精度，所以使用string"
        * )
        */
        4: string currentGoodsBill;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        5: SettleOrderPage billList;
}

/**
* @TypeDoc(
*     description = "分页查询对账单接口返回结果"
* )
*/
struct BalanceBillListData{

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        1: i32 code;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        2: string msg;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        3: BalanceBillListContent content;
}

/**
* @TypeDoc(
*     description = "分页查询对账单接口返回结果"
* )
*/
struct BalanceBillListResponseDTO{

        /**
        * @TypeDoc(
        *     description = "状态"
        * )
        */
        1: BalanceBillListData balanceBillListData;
}


/**
* @TypeDoc(
*     description = "分页查询对账单接口返回结果"
* )
*/
struct ChannelBalanceBillPageResponse{

        /**
        * @TypeDoc(
        *     description = "状态"
        * )
        */
        1: ResultStatus.ResultStatus resultStatus;

        /**
         * @TypeDoc(
         *     description = "分页查询对账单接口返回结果"
         * )
         */
        2: list<BalanceBillDTO> balanceBillDTOList;
}


/**
* @TypeDoc(
*     description = "查询订单计费明细接口返回结果"
* )
*/
struct BillSearchResult{

        /**
        * @TypeDoc(
        *     description = "订单号"
        * )
        */
        1: i64 orderId;

        /**
         * @TypeDoc(
         *     description = "订单货款结算状态0 未计费、1计费未结算、2 已结算"
         * )
         */
        2: i32 googsSettlementStatus;
        /**
        * @TypeDoc(
        *     description = "货款(商品实际支付金额)"
        * )
        */
        3: i64 goodsBill;

        /**
         * @TypeDoc(
         *     description = "货款佣金= (用户支付的sku的实际金额+到家承担的市场费+餐盒费)*扣点"
         * )
         */
        4: i64 goodsCommissionBill;

        /**
        * @TypeDoc(
        *     description = "运费=商家自配送费-商家支付小费-商家支付远距离运费-商家承担运费补贴"
        * )
        */
        5: i64 freightBill;

        /**
         * @TypeDoc(
         *     description = "运费佣金（如果是达达配送则没有运费佣金，否则是运费（不含取件服务费）*扣点）"
         * )
         */
        6: i64 freightCommissionBill;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型，保底佣金补差（单位：元）"
        * )
        */
        7: string diffLessCommision;

        /**
         * @TypeDoc(
         *     description = "餐盒费"
         * )
         */
        8: i64 packageBill;

        /**
        * @TypeDoc(
        *     description = "市场费结算状态0 未计费、1计费未结算、2 已结算,3 无市场费"
        * )
        */
        9: i32 marketSettlementStatus;

        /**
         * @TypeDoc(
         *     description = "市场费"
         * )
         */
        10: i64 marketBill;

        /**
        * @TypeDoc(
        *     description = "营销服务费用"
        * )
        */
        11: i64 marketingServiceFee;

        /**
         * @TypeDoc(
         *     description = "营销服务费用状态"
         * )
         */
        12: i32 marketingServiceFeeStatus;

        /**
        * @TypeDoc(
        *     description = "对账单类型，5006 货款对账单 5008 市场费对账单"
        * )
        */
        13: i32 billOrderType;
}

/**
* @TypeDoc(
*     description = "查询订单计费明细接口返回结果"
* )
*/
struct ChannelCheckBillResponse{

        /**
        * @TypeDoc(
        *     description = "状态"
        * )
        */
        1: ResultStatus.ResultStatus resultStatus;

        /**
         * @TypeDoc(
         *     description = "查询订单计费明细接口返回结果"
         * )
         */
        2: list<BillSearchResult> billSearchResultList;

        /**
         * @TypeDoc(
         *     description = "总数"
         * )
         */
        3: i64 totalCount;

        /**
         * @TypeDoc(
         *     description = "京东平台级code"
         * )
         */
        4: string platCode;

        /**
         * @TypeDoc(
         *     description = "京东平台级msg"
         * )
         */
        5: string platMsg;

}

/**
 * @TypeDoc(
 *     description = "分页查询对账单接口"
 * )
 */
struct JDBalanceBillPageRequest{

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = ""
         * )
         */
        1: optional list<i64> shopIds;

        /**
         * @FieldDoc(
         *     description = "订单号",
         *     example = "123"
         * )
         */
        2: optional list<i64> orderIds;

        /**
         * @FieldDoc(
         *     description = "订单类型，不填写为全部，1:异常调整,1001:正向订单,1002:售后退货,1003:售后退款,1004:售后退货申诉,1005:售后退款申诉,1006:售后换新申诉,1016:售后换新,1017:售后直赔",
         *     example = "1"
         * )
         */
        3: optional i32 orderType;

        /**
         * @FieldDoc(
         *     description = "订单状态 1:已完成",
         *     example = "1"
         * )
         */
        4: optional i32 orderStatus;

        /**
         * @FieldDoc(
         *     description = "结算状态，待打款(20002)、结算成功(20003)、结算失败(20004,20011)、驳回(20005)、打款失败(20009)、冻结中(20014)",
         *     example = "1"
         * )
         */
        5: optional i32 settleStatus;


        /**
         * @FieldDoc(
         *     description = "下单开始时间（20170706）与完成订单开始时间至少填写一个",
         *     example = "20170706"
         * )
         */
        6: optional string orderStartTime;

        /**
         * @FieldDoc(
         *     description = "下单截止时间（20170706）与完成订单截止时间至少填写一个",
         *     example = "20170706"
         * )
         */
        7: optional string orderEndTime;

        /**
         * @FieldDoc(
         *     description = "下单开始时间（20170706）与下单开始时间至少填写一个，天选业务表示代扣完成时间",
         *     example = "20170706"
         * )
         */
        8: optional string finishStartTime;

        /**
         * @FieldDoc(
         *     description = "下单截止时间，时间范围不能超3个月，天选业务表示代扣完成时间",
         *     example = "20170706"
         * )
         */
        9: optional string finishEndTime;

        /**
         * @FieldDoc(
         *     description = "页码 不传默认第一页",
         *     example = "1"
         * )
         */
        10: optional i32 pageNum;

        /**
         * @FieldDoc(
         *     description = "每页最高数量为200条，默认10",
         *     example = "10"
         * )
         */
        11: optional i32 pageSize;

        /**
         * @FieldDoc(
         *     description = "是否查询京东融合的对账单，需要查询京东融合对账单时填true，不传默认false",
         *     example = "true"
         * )
         */
        12: optional bool jdFusion;

        /**
         * @FieldDoc(
         *     description = "结算单号",
         *     example = ""
         * )
         */
        13: optional string jdPayReqId;

        /**
         * @FieldDoc(
         *     description = "付款单号",
         *     example = ""
         * )
         */
        14: optional string jdPayMark;

        /**
         * @FieldDoc(
         *     description = "1-到家账单，2-小时购账单，3-天选账单 （与jdFusion兼容） 【billType=1】等同jdFusion=false 【billType=2】 等同 jdFusion=true 【billType=3】 新类型,jdFusion无对应值 若jdFusion和billType同时有参数值，则以billType为准",
         *     example = "1"
         * )
         */
        15: optional i32 billType;

        /**
         * @FieldDoc(
         *     description = "小时购交易上行订单,针对billType=2 OC00007:用户提单对账单，NOC00007:主站小时购对账单",
         *     example = ""
         * )
         */
        16: optional string orderChannelDesc;

        /**
         * @FieldDoc(
         *     description = "租户id",
         *     example = "1"
         * )
         */
        17: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "123"
         * )
         */
        18: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "源订单列表",
         *     example = ""
         * )
         */
        19: optional string srcOrderIds;

}


/**
* @TypeDoc(
*     description = "分页查询对账单接口返回结果"
* )
*/
struct JDBalanceBillDTO{

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        1: i32 id;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        2: i64 orgCode;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        3: string orgName;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        4: i64 stationId;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        5: string stationName;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        6: i64 businessStartTime;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        7: i64 businessFinishTime;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        8: i64 orderId;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        9: i32 orderStatus;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        10: string goodsBill;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        11: string dueAmount;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        12: string settleStatus;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        13: i32 settleStatusCode;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        14: i64 settleOrderId;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        15: string settleOrderIdStr;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        16: string freightBill;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        17: string packageBill;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        18: string commission;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        19: string orderType;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        20: i32 orderTypeCode;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        21: string storeSubsidy;

        /**
        * @TypeDoc(
        *     description = "BigDecimal 类型"
        * )
        */
        22: string subsidy;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        23: string createTime;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        24: string marketBill;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        25: i64 giftcardBill;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        26: i64 marketingServiceFee;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        27: i32 marketingServiceFeeStatus;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        28: string originalAmount;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        29: string paymentSubsidies;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        30: string storeFreightSubsidy;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        31: string storeFreightAmount;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        32: string pickupAmount;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        33: string pickupServiceAmount;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        34: string accountTime;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        35: i64 settleFinishTime;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        36: string deliveryTypeStr;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        37: string withdrawId;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        38: string orderSourceName;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        39: string payChannelName;

        /**
        * @TypeDoc(
        *     description = "BigDecimal类型"
        * )
        */
        40: string offlineSettlementAmount;

        /**
        * @TypeDoc(
        *     description = ""
        * )
        */
        41: i32 billOrderType;

        /**
        * @TypeDoc(
        *     description = "源订单号"
        * )
        */
        42: i64 srcOrderId;

        /**
        * @TypeDoc(
        *     description = "关联订单号 非正向单时才存在 如果此单为售后单，则关联单号为正向单。如果是售后申述单，则关联单号为售后单"
        * )
        */
        43: i64 relationId;

        /**
        * @TypeDoc(
        *     description = "关联单号（兼容字段：到家、小时购不用，天选使用）"
        * )
        */
        44: string salesRelationId;

        /**
        * @TypeDoc(
        *     description = "基础服务费(BigDecimal类型)"
        * )
        */
        45: string performanceServiceFee;

        /**
        * @TypeDoc(
        *     description = "开票金额(BigDecimal类型)"
        * )
        */
        46: string invoiceFee;

        /**
        * @TypeDoc(
        *     description = "平台运费补贴(BigDecimal类型)"
        * )
        */
        47: string platformFreightSubsidy;

        /**
        * @TypeDoc(
        *     description = "医保渠道结算单号业务场景：小时达业务 默认：null"
        * )
        */
        48: string ybSettleId;

        /**
        * @TypeDoc(
        *     description = "医保统筹金额，BigDecimal类型"
        * )
        */
        49: string fundPay;

        /**
        * @TypeDoc(
        *     description = "医保个账金额，BigDecimal类型"
        * )
        */
        50: string psnAcctPay;

        /**
        * @TypeDoc(
        *     description = "医保自付金额，BigDecimal类型"
        * )
        */
        51: string ownPayAmt;

        /**
        * @TypeDoc(
        *     description = "商家服务费，BigDecimal类型"
        * )
        */
        52: string supplierServiceAmount;

        /**
        * @TypeDoc(
        *     description = "钱包结算完成时间"
        * )
        */
        53: string detailSettleFinishTime;

        /**
        * @TypeDoc(
        *     description = "平台补贴暂扣（单位：元），BigDecimal类型"
        * )
        */
        54: string platformSubsidySuspend;

        /**
        * @TypeDoc(
        *     description = "商家收包装费（单位：元），BigDecimal类型"
        * )
        */
        55: string packagingFeeAmount;

        /**
        * @TypeDoc(
        *     description = "外部门店编号"
        * )
        */
        56: string outStationNo;

        /**
        * @TypeDoc(
        *     description = "平台责赔付"
        * )
        */
        57: string platformCompensation;
}

/**
* @TypeDoc(
*     description = "分页查询对账单接口:SettleOrderPage"
* )
*/
struct JDBalanceBillSettleOrderPage{

        /**
        * @TypeDoc(
        *     description = "第几页"
        * )
        */
        1: i64 pageNum;

        /**
         * @TypeDoc(
         *     description = "每页条数"
         * )
         */
        2: i32 pageSize;

        /**
         * @TypeDoc(
         *     description = "总条数"
         * )
         */
        3: i64 total;

        /**
         * @TypeDoc(
         *     description = "开始行"
         * )
         */
        4: i32 startRow;

        /**
         * @TypeDoc(
         *     description = "结束行"
         * )
         */
        5: i32 endRow;

        /**
         * @TypeDoc(
         *     description = "总页数"
         * )
         */
        6: i32 pages;

        /**
         * @TypeDoc(
         *     description = "对账单分页列表"
         * )
         */
        7: list<JDBalanceBillDTO> result;
}

/**
* @TypeDoc(
*     description = "分页查询对账单接口:BalanceBillListContent"
* )
*/
struct JDBalanceBillListContent{

        /**
        * @TypeDoc(
        *     description = "总应结（单位：元）BigDecimal"
        * )
        */
        1: string summaryDueAmount;

        /**
         * @TypeDoc(
         *     description = "总货款（单位：元）BigDecimal"
         * )
         */
        2: string summaryGoodsBill;

        /**
         * @TypeDoc(
         *     description = "当前页应结（单位：元）BigDecimal"
         * )
         */
        3: string currentDueAmount;

        /**
         * @TypeDoc(
         *     description = "当前页货款（单位：元）BigDecimal"
         * )
         */
        4: string currentGoodsBill;

        /**
         * @TypeDoc(
         *     description = "结束行"
         * )
         */
        5: JDBalanceBillSettleOrderPage billList;
}

/**
* @TypeDoc(
*     description = "分页查询对账单接口:data"
* )
*/
struct JDBalanceBillData{

        /**
        * @TypeDoc(
        *     description = "状态码，200为成功其他均为失败"
        * )
        */
        1: i32 code;

        /**
         * @TypeDoc(
         *     description = "描述信息"
         * )
         */
        2: string msg;

        /**
         * @TypeDoc(
         *     description = "返回结果"
         * )
         */
        3: JDBalanceBillListContent content;
}

/**
* @TypeDoc(
*     description = "分页查询对账单接口:总结果"
* )
*/
struct JDBalanceBillResult{

        /**
        * @TypeDoc(
        *     description = "0表示成功，其他均为失败"
        * )
        */
        1: string code;

        /**
         * @TypeDoc(
         *     description = "结果描述"
         * )
         */
        2: string msg;

        /**
         * @TypeDoc(
         *     description = "返回对象，该属性值为字符串型JSON数据，请先按照字符串取值，再解析转换成JSON对象。"
         * )
         */
        3: JDBalanceBillData data;
}

/**
* @TypeDoc(
*     description = "分页查询对账单接口返回总结果"
* )
*/
struct JDBalanceBillPageResponse{

        /**
        * @TypeDoc(
        *     description = "状态"
        * )
        */
        1: ResultStatus.ResultStatus resultStatus;

        /**
         * @TypeDoc(
         *     description = "分页查询对账单接口返回总结果"
         * )
         */
        2: JDBalanceBillResult result;
}

struct DataItem {
        /**
         * @TypeDoc(
         *     description = "商家减收或渠道减收政府补贴"
         * )
         */
        1: i64 governmentPromotionShopReduceAmount;
        /**
         * @TypeDoc(
         *     description = "账户类型，1：聚合账户，2：保证金账户"
         * )
         */
        2: i32 accountType;
        /**
         * @TypeDoc(
         *     description = "达人补贴金额（分）"
         * )
         */
        3: i64 authorCouponAmount;
        /**
         * @TypeDoc(
         *     description = "用户支付时，银行出资的补贴金额，算作商家的收入（分）"
         * )
         */
        4: i64 bankPayPromotionAmount;
        /**
         * @TypeDoc(
         *     description = "管理单元ID"
         * )
         */
        5: i64 unitShopId;
        /**
         * @TypeDoc(
         *     description = "门店名称"
         * )
         */
        6: string storeShopName;
        /**
         * @TypeDoc(
         *     description = "商家支付服务商的佣金（分）"
         * )
         */
        7: i64 partnerCommission;
        /**
         * @TypeDoc(
         *     description = "管理单元分成（分) "
         * )
         */
        8: i64 unitDivide;
        /**
         * @TypeDoc(
         *     description = "下单类型，1：即时送达，2：预约送达 "
         * )
         */
        9: i32 deliveryType;

        /**
         * @TypeDoc(
         *     description = "流量来源，1：鲁班广告，2：精选联盟，3,：值点商城，4：小店自卖，5：橙子建站，6：POI，7：抖+，8：穿山甲，9：服务市场，10：服务市场外包客服，11：学浪"
         * )
         */
        10: i32 source;

        /**
         * @TypeDoc(
         *     description = "混资券商家出资金额（分）"
         * )
         */
        11: i64 mixedCouponShopCostApportAmount;

        /**
         * @TypeDoc(
         *     description = "平台服务费（分）"
         * )
         */
        12: i64 platformServiceFee;

        /**
         * @TypeDoc(
         *     description = "服务商ID"
         * )
         */
        13: i64 partnerShopId;

        /**
         * @TypeDoc(
         *     description = "免佣标识，正向单：0-不涉及免佣，1-免佣，2-不免佣；逆向单：均为0"
         * )
         */
        14: i32 freeCommissionFlag;

        /**
         * @TypeDoc(
         *     description = "免佣标识，正向单：0-不涉及免佣，1-免佣，2-不免佣；逆向单：均为0"
         * )
         */
        15: i64 shopCoupon;

        /**
         * @TypeDoc(
         *     description = "总公司分成（分）"
         * )
         */
        16: i64 mainDivide;

        /**
         * @TypeDoc(
         *     description = "打包费（分）"
         * )
         */
        17: i64 packingAmount;

        /**
         * @TypeDoc(
         *     description = "打包费（分）"
         * )
         */
        18: i64 mainShopId;

        /**
         * @TypeDoc(
         *     description = "达人ID"
         * )
         */
        19: i64 authorId;

        /**
         * @TypeDoc(
         *     description = "订单完成时间，用户点击确认收货时间"
         * )
         */
        20: string orderFinishTime;

        /**
         * @TypeDoc(
         *     description = "支出合计（分）"
         * )
         */
        21: i64 outcomeTotalAmount;

        /**
         * @TypeDoc(
         *     description = "平台运费补贴金额（分）"
         * )
         */
        22: i64 postPromotionAmount;

        /**
         * @TypeDoc(
         *     description = "抖音支付补贴金额（分）"
         * )
         */
        23: i64 ztPlatformPayPromotionAmount;

        /**
         * @TypeDoc(
         *     description = "管理单元名称"
         * )
         */
        24: string unitShopName;

        /**
         * @TypeDoc(
         *     description = "子订单号"
         * )
         */
        25: string orderId;
        /**
         * @TypeDoc(
         *     description = "服务商名称"
         * )
         */
        26: string partnerName;

        /**
         * @TypeDoc(
         *     description = "用户实付金额（分）"
         * )
         */
        27: i64 payAmount;

        /**
         * @TypeDoc(
         *     description = "运费（分）"
         * )
         */
        28: i64 postAmount;

        /**
         * @TypeDoc(
         *     description = "收入合计（分）"
         * )
         */
        29: i64 incomeTotalAmount;

        /**
         * @TypeDoc(
         *     description = "用户以旧换新购买商品，旧机扣款，由二手商出资（分）"
         * )
         */
        30: i64 recyclerAmount;

        /**
         * @TypeDoc(
         *     description = "订单号"
         * )
         */
        31: string shopOrderId;

        /**
         * @TypeDoc(
         *     description = "商品直降金额（分）"
         * )
         */
        32: i64 shopDeductionAmount;

        /**
         * @TypeDoc(
         *     description = "平台给消费者补贴的运费（分）"
         * )
         */
        33: i64 postDeductionPlatform;

        /**
         * @TypeDoc(
         *     description = "门店ID"
         * )
         */
        34: i64 storeShopId;

        /**
         * @TypeDoc(
         *     description = "达人名称"
         * )
         */
        35: string authorName;

        /**
         * @TypeDoc(
         *     description = "商家支出团长的佣金（分）"
         * )
         */
        36: i64 colonelCommission;

        /**
         * @TypeDoc(
         *     description = "结算金额（分）"
         * )
         */
        37: i64 settleAmount;

        /**
         * @TypeDoc(
         *     description = "订单交易时间，正向用户实付完成时间，逆向退款完成时间"
         * )
         */
        38: string orderTradeTime;

        /**
         * @TypeDoc(
         *     description = "平台商品补贴金额（分）"
         * )
         */
        39: i64 promotionAmount;

        /**
         * @TypeDoc(
         *     description = "商家支出抖客的佣金（分）"
         * )
         */
        40: i64 douCustomerCommission;

        /**
         * @TypeDoc(
         *     description = "订单提交时间，电商交易订单创建时间：正向用户创建订单时间，逆向用户申请售后时间"
         * )
         */
        41: string orderCreateTime;

        /**
         * @TypeDoc(
         *     description = "结算状态，1：待结算，2：已结算，3：部分退款（结算前），4：部分退款（结算后）， 5：全额退款（结算前），6：全额退款（结算后）"
         * )
         */
        42: i32 settleStatus;

        /**
         * @TypeDoc(
         *     description = "平台服务费费率"
         * )
         */
        43: string platformServiceFeeRate;

        /**
         * @TypeDoc(
         *     description = "订单类型，1：商品单，2：运费及打包费"
         * )
         */
        44: i32 orderType;

        /**
         * @TypeDoc(
         *     description = "结算时间"
         * )
         */
        45: string settleTime;

        /**
         * @TypeDoc(
         *     description = "备注"
         * )
         */
        46: string remark;

        /**
         * @TypeDoc(
         *     description = "商品总价（分）"
         * )
         */
        47: i64 productPriceTotalAmount;

        /**
         * @TypeDoc(
         *     description = "抖音月付营销补贴金额（分）"
         * )
         */
        48: i64 zrPlatformPayPromotionAmount;

        /**
         * @TypeDoc(
         *     description = "总公司名称"
         * )
         */
        49: string mainShopName;

        /**
         * @TypeDoc(
         *     description = "商家支出达人的佣金（分）"
         * )
         */
        50: i64 authorCommission;

        /**
         * @TypeDoc(
         *     description = "总店支出至门店金额（分）"
         * )
         */
        51: i64 storeDivide;

        /**
         * @TypeDoc(
         *     description = "商家给消费者补贴的运费（分）"
         * )
         */
        52: i64 postDeductionShop;

        /**
         * @TypeDoc(
         *     description = "售后编号"
         * )
         */
        53: string refundSalesNo;
}


struct DouyinBalanceBillResult{
         /**
         * @TypeDoc(
         *     description = "状态码，100000为成功其他均为失败"
         * )
         */
        1: string code;

        /**
         * @TypeDoc(
         *     description = "描述信息"
         * )
         */
        2: string codeMsg;

        /**
         * @TypeDoc(
         *     description = "满足当前查询条件的数据是否还有下一批，true代表还有数据，false代表没有数据"
         * )
         */
        3: optional bool hasNext;

        /**
         * @TypeDoc(
         *     description = "下一次查询的游标"
         * )
         */
        4: string nextCursor;

        /**
         * @TypeDoc(
         *     description = "交易账单流水"
         * )
         */
        5: list<DataItem> data;
}



/**
* @TypeDoc(
*     description = "分页查询对账单接口返回总结果"
* )
*/
struct DouyinBalanceBillPageResponse{

         /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus status;

         /**
         * @TypeDoc(
         *     description = "分页查询对账单接口返回总结果"
         * )
         */
        2: DouyinBalanceBillResult result;
}


struct DouyinBalanceBillPageRequest {

        /**
         * @TypeDoc(
         *     description = "查询开始时间，格式为：yyyy-MM-dd HH:mm:ss，订单号未传的情况下，开始时间必须传"
         * )
         */
        1: string startTime;

        /**
         * @TypeDoc(
         *     description = "查询结束时间，和end_time的时间间隔建议不超过31天，格式为：yyyy-MM-dd HH:mm:ss，订单号未传的情况下，结束时间必须传"
         * )
         */
        2: string endTime;

        /**
         * @TypeDoc(
         *     description = "时间类型   1 订单提交时间 2 订单完成时间  3 订单交易时间 4 订单结算时间订单交易时间：正向用户支付完成时间，逆向退款完成时间"
         * )
         */
        3: i32 timeType;

        /**
         * @TypeDoc(
         *     description = "店铺订单号(如果订单号未传，则时间必须传)"
         * )
         */
        4: string shopOrderId;

         /**
         * @TypeDoc(
         *     description = "子订单号(如果订单号未传，则时间必须传)"
         * )
         */
        5: string orderId;

        /**
         * @TypeDoc(
         *     description = "查询游标，查询第一页时传空，后续查询使用前一次查询返回的next_cursor"
         * )
         */
        6: string cursor;

        /**
         * @TypeDoc(
         *     description = "查询数量，支持范围1～100"
         * )
         */
        7: i32 size;

        /**
         * @FieldDoc(
         *     description = "租户id",
         *     example = "1"
         * )
         */
        8: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "123"
         * )
         */
        9: i32 channelId;
}
