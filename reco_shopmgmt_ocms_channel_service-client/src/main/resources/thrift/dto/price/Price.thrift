namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"
include "BaseRequest.thrift"

/**
 * @TypeDoc(
 *     description = "价格信息"
 * )
 */
struct SkuPriceDTO{

    /**
     * @FieldDoc(
     *     description = "赋能商品SKU编码",
     *     example = "10"
     * )
     */
    1: string skuId;

    /**
     * @FieldDoc(
     *     description = "渠道价格"
     * )
     */
    2:  optional double price;

    /**
     * @FieldDoc(
     *     description = "时间戳",
     *     example = "1545714137000"
     * )
     */
    3: i64 timestamp;

    /**
     * @FieldDoc(
     *     description = "渠道商品SKU编码",
     *     example = "10"
     * )
     */
    4: string channelSkuId;

    /**
    * 商家SPU编码
    **/
    6: optional string spuId;
    /**
    * 渠道SPU编码
    **/
    7: optional string channelSpuId;
    /**
    * 规格类型(1单规格2多规格)，饿了么渠道使用
    **/
    8: optional i32 specType =1;
    /**
    *总部渠道商品channelSpuId，有赞渠道需要使用
**/
    9: optional string merchantSpuId;

    10: optional string customSkuId;

    11: optional string customSpuId;
}

/**
 * @TypeDoc(
 *     description = "价格信息"
 * )
 */
struct SkuInSpuPriceDTO{

    /**
     * @FieldDoc(
     *     description = "商家商品SKU编码",
     *     example = "10"
     * )
     */
    1: string customSkuId;

    /**
     * @FieldDoc(
     *     description = "渠道价格"
     * )
     */
    2:  optional double price;

    /**
     * @FieldDoc(
     *     description = "时间戳",
     *     example = "1545714137000"
     * )
     */
    3: i64 timestamp;

}

/**
 * @TypeDoc(
 *     description = "价格信息"
 * )
 */
struct SpuPriceDTO{

    /**
* @FieldDoc(
*     description = "门店ID",
*     example = "1"
* )
*/
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商家商品SPU编码",
     *     example = "10"
     * )
     */
    2: string customSpuId;

    /**
     * @FieldDoc(
     *     description = "时间戳",
     *     example = "1545714137000"
     * )
     */
    3: list<SkuInSpuPriceDTO> skuPriceInfo;

}

/**
 * @TypeDoc(
 *     description = "多渠道多门店价格信息"
 * )
 */
struct SkuPriceMultiChannelDTO {
    
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    1: i32 channelId;
    
    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = "1"
     * )
     */
    2: i64 storeId;
    
    /**
     * @FieldDoc(
     *     description = "商家SKU编码",
     *     example = "10"
     * )
     */
    3: string skuId;
    
    /**
     * @FieldDoc(
     *     description = "渠道价格"
     * )
     */
    4:  optional double price;
    
    /**
     * @FieldDoc(
     *     description = "时间戳",
     *     example = "1545714137000"
     * )
     */
    5: i64 timestamp;

    /**
     * @FieldDoc(
     *     description = "渠道商品编码",
     *     example = "10"
     * )
     */
    6: string channelSkuId;

    /**
    * 商家SPU编码
    **/
    7: optional string spuId;
    /**
    * 渠道SPU编码
    **/
    8: optional string channelSpuId;
    /**
    * 规格类型(1单规格2多规格)，饿了么渠道使用
    **/
    9: optional i32 specType =1;
    /**
    *总部渠道商品channelSpuId,有赞使用
**/
    10: optional string merchantSpuId;

    11: optional string customSkuId;

    12: optional string customSpuId;
}

/**
 * @TypeDoc(
 *     description = "价格信息"
 * )
 */
struct SkuPriceRequest {

    /**
     * @FieldDoc(
     *     description = "租户Id",
     *     example = "1"
     * )
     */
    1: i64 tenantId;

    /**
    * @FieldDoc(
    *     description = "渠道ID",
    *     example = "1"
    * )
     */
    2: i32 channelId;

    3: i64 storeId;
    // 是否异步调用，默认为false
    4: bool asyncInvoke;

    /**
     * @FieldDoc(
     *     description = "价格信息",
     *     example = "1"
     *
     * )
     */
    5: list<SkuPriceDTO> paramList;

     /**
     * @FieldDoc(
     *     description = "是否使用渠道id操作商品，默认false（当前仅支持饿了么单规格）",
     *     example = "false"
     *
     * )
     */
    6: optional bool optByChannelSpuId;

    /**
    * @FieldDoc(
    *     description = "1-优先保留活动，商品更新失败；2-商品可更新成功，平台补贴活动会下线。不传参默认是1。"
    * )
    */
    7: i32 actCheckType;
}

/**
 * @TypeDoc(
 *     description = "价格信息"
 * )
 */
struct SingleSkuPriceRequest {

    /**
     * @FieldDoc(
     *     description = "租户Id",
     *     example = "1"
     * )
     */
    1: i64 tenantId;

    /**
    * @FieldDoc(
    *     description = "渠道ID",
    *     example = "1"
    * )
     */
    2: i32 channelId;

    3: i64 storeId;
    // 是否异步调用，默认为false
    4: bool asyncInvoke;

    /**
     * @FieldDoc(
     *     description = "价格信息",
     *     example = "1"
     *
     * )
     */
    5: SkuPriceDTO param;

     /**
     * @FieldDoc(
     *     description = "是否使用渠道id操作商品，默认false（当前仅支持饿了么单规格）",
     *     example = "false"
     *
     * )
     */
    6: optional bool optByChannelSpuId;
}

/**
 * @TypeDoc(
 *     description = "价格信息"
 * )
 */
struct SpuPriceRequest {

    /**
        * @FieldDoc(
        *     description = "租户渠道Id",
        *     example = "1"
        * )
        */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "价格信息",
     *     example = "1"
     *
     * )
     */
    2: list<SpuPriceDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "多渠道多门店价格接口入参"
 * )
 */
struct SkuPriceMultiChannelRequest {
    
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "多渠道多门店价格信息",
     *     example = "1"
     *
     * )
     */
    2: list<SkuPriceMultiChannelDTO> paramList;

    // 是否异步调用，默认为false
    3: bool asyncInvoke;

    /**
     * @FieldDoc(
     *     description = "是否使用渠道id操作商品，默认false",
     *     example = "false"
     *
     * )
     */
    4: optional bool optByChannelSpuId;

    /**
    * @FieldDoc(
    *     description = "1-优先保留活动，商品更新失败；2-商品可更新成功，平台补贴活动会下线。不传参默认是1。"
    * )
    */
    5: optional i32 actCheckType;
}

struct SkuPriceInfoDTO {
    1: string channelSkuId;

    /**
     * @FieldDoc(
     *     description = "渠道门店sku价格，单位：分",
     *     example = "1"
     * )
     */
    2: i64 price;
}

struct BatchGetSkuPriceRequest{

    /**
     * @FieldDoc(
     *     description = "基础信息",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    2: i64 storeId;

    3: list<string> channelSkuIds;

}

struct BatchGetSkuPriceResponse{

    1: ResultStatus.ResultStatus status;

    2: list<SkuPriceInfoDTO> skuPriceInfos;
}