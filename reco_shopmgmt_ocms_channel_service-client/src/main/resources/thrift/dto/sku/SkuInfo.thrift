namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"
include "BaseRequest.thrift"
include "PageInfo.thrift"

/**
 * @TypeDoc(
 *     description = "商品店内分类信息"
 * )
 */
struct ChannelStoreCategory{
        /**
        * @FieldDoc(
        *     description = "店内一级分类编码",
        *     example = "0102"
        * )
        */
        1:  string firstCategoryCode;

        /**
         * @FieldDoc(
         *     description = "店内一级分类名称",
         *     example = "面食"
         * )
         */
        2:  string firstCategoryName;

        /**
         * @FieldDoc(
         *     description = "店内二级分类编码",
         *     example = "0102"
         * )
         */
        3:  string secondaryCategoryCode

        /**
         * @FieldDoc(
         *     description = "店内二级分类名称",
         *     example = "面食"
         * )
         */
        4:  string secondaryCategoryName;
}

/**
 * @TypeDoc(
 *     description = "商品末级店内分类信息"
 * )
 */
struct ChannelLeafStoreCategory{
        /**
        * @FieldDoc(
        *     description = "店内末级分类编码",
        *     example = "0102"
        * )
        */
        1:  string categoryCode;

        /**
         * @FieldDoc(
         *     description = "店内末级分类名称",
         *     example = "面食"
         * )
         */
        2:  string categoryName;
}

/**
 * @TypeDoc(
 *     description = "药品限购详情"
 * )
 */
struct ChannelMedicineLimitSaleInfo{
        /**
        * @FieldDoc(
        *     description = "是否限制购买数量：
               (1)字段取值范围：1.true-开启；false-关闭。
               (2)如开启限制，则对SPU下所有SKU生效，每个SKU均按设置规则执行限购。
               (3)如入参选择“不限制”，则后续参数入参也无效。
               (4)如入参选择“限制”，则开启单个买家限购功能。",
        *     example = "true"
        * )
        */
        1:  bool limitSale;

        /**
         * @FieldDoc(
         *     description = "限购规则：
         *       1-限制下单顾客每X天限购数量，X为frequency，不传默认为1；
         *       2-限制整个周期内下单顾客限购数量。",
         *     example = "1"
         * )
         */
        2:  i32 type;

        /**
         * @FieldDoc(
         *     description = "限购循环天数：最大31，最小1",
         *     example = "1"
         * )
         */
        3:  i32 frequency;

        /**
         * @FieldDoc(
         *     description = "限购开始日期：开始时间不得早于当前日期的7天前。
         *     begin在thrift源码中标注的关键字列表中，不可使用",
         *     example = "20200216"
         * )
         */
        4:  string begindate;

        /**
         * @FieldDoc(
         *     description = "限购结束日期：结束日期不得早于开始日期，且与开始日期相距不得超出90天。
         *     end在thrift源码中标注的关键字列表中，不可使用",
         *     example = "20200221"
         * )
         */
        5:  string enddate;

        /**
         * @FieldDoc(
         *     description = "限购数量：(1)字段范围：整数，最小为1。(2)如入参为小数，则向下取整（例如：入参数量为2.8，最终取2）。",
         *     example = "10"
         * )
         */
        6:  double count;
}

/**
 * @TypeDoc(
 *     description = "无货取消订单触发库存清零相关配置"
 * )
 */
struct ChannelMedicineStockConfig{
        /**
        * @FieldDoc(
        *     description = "是否开启“无货取消订单触发库存清零”配置的总开关：true-开；false-关。",
        *     example = "true"
        * )
        */
        1:  bool reset;

        /**
         * @FieldDoc(
         *     description = "触发库存清零的取消订单方式：1-门店因无货取消订单；2-买家因无货取消订单。",
         *     example = "[1,2]"
         * )
         */
        2:  list<i64> type;

        /**
         * @FieldDoc(
         *     description = "触发库存清零后是否禁止开放平台接口更新库存：true-是，禁止更新库存；false-否，允许更新库存。",
         *     example = "true"
         * )
         */
        3:  bool limitOpenSyncStock;

        /**
        * @FieldDoc(
        *     description = "商品触发库存清零后，且limit_open_sync_stock为true的情况下，允许开放平台接口更新库存的时间点，
        *     日期为次日的“时:分”。limit_open_sync_stock为false时，无需关注此字段。",
        *     example = "00:00"
        * )
        */
        4:  string schedule;

        /**
         * @FieldDoc(
         *     description = "是否允许次日自动补充库存：true-是，自动补充库存；false-否，不会自动补充库存。
         *       注：如设置为“是”，则当商品开启“无货取消订单触发库存清零”配置，且该商品当天触发过清零规则并且当前库存为0，则该商品第二天零点会执行自动补充库存规则。",
         *     example = "true"
         * )
         */
        5:  bool syncNextDay;

        /**
        * @FieldDoc(
        *     description = "次日自动补充库存数量",
        *     example = "2"
        * )
        */
        6:  i32 syncCount;
}

/**
 * @TypeDoc(
 *     description = "提供给上游的渠道商品Sku信息"
 * )
 */
struct SkuInfoDTO{

        /**
         * @FieldDoc(
         *     description = "商家SKU编码",
         *     example = "10"
         * )
         */
        1: string skuId;

        /**
         * @FieldDoc(
         *     description = "商品名称",
         *     example = "安慕希希腊风味常温酸奶原味"
         * )
         */
        2: string name;

        /**
         * @FieldDoc(
         *     description = "历史单分类：前台分类编码；多分类：此字段不能设置值",
         *     example = "1"
         * )
         */
        3:  string frontCategory;

        /**
         * @FieldDoc(
         *     description = "历史单分类：前台分类名称；多分类：此字段不能设置值",
         *     example = "面食"
         * )
         */
        4:  string frontCategoryName;

        /**
         * @FieldDoc(
         *     description = "商家类目（后台类目）",
         *     example = "99"
         * )
         */
        5: string category;

        /**
         * @FieldDoc(
         *     description = "商家品牌编码",
         *     example = "10"
         * )
         */
        6: string brand;

        /**
         * @FieldDoc(
         *     description = "最小购买数量",
         *     example = "1"
         * )
         */
        7: optional double minPurchaseQuantity;

        /**
         * @FieldDoc(
         *     description = "规格名称",
         *     example = "大"
         * )
         */
        8: string spec;

        /**
         * @FieldDoc(
         *     description = "重量",
         *     example = "5.1"
         * )
         */
        9: optional double weight;

        /**
         * @FieldDoc(
         *     description = "包装盒价格",
         *     example = "23.5"
         * )
         */
        10: optional double boxPrice;

        /**
         * @FieldDoc(
         *     description = "包装盒数量",
         *     example = "1"
         * )
         */
        11: optional i32 boxQuantity;

        /**
         * @FieldDoc(
         *     description = "主图",
         *     example = "[2B86BE5A5C23786232DE37A4717E0D93]"
         * )
         */
        12: list<string> pictures;

        /**
         * @FieldDoc(
         *     description = "商品上下架状态（1-上架；2-下架）",
         *     example = "1"
         * )
         */
        13: i32 skuStatus;

        /**
         * @FieldDoc(
         *     description = "价格",
         *     example = "1.5"
         * )
         */
        14: optional double price;

        /**
         * @FieldDoc(
         *     description = "库存数量",
         *     example = "99"
         * )
         */
        15: optional i32 stock;

        /**
         * @FieldDoc(
         *     description = "UPC码",
         *     example = "5155660008"
         * )
         */
        16: string upc;

        /**
         * @FieldDoc(
         *     description = "渠道末级店内分类编码，历史单分类：分类编码；多分类：此字段不设置值",
         *     example = "1"
         * )
         */
        17:  string channelFrontCategory;

        /**
         * @FieldDoc(
         *     description = "渠道末级店内分类名称，历史单分类：分类名称；多分类：此字段不设置值",
         *     example = "1"
         * )
         */
        18:  string channelFrontCategoryName;

        /**
         * @FieldDoc(
         *     description = "单位",
         *     example = "份"
         * )
         */
        19:  string unit;

        /**
         * @FieldDoc(
         *     description = "商品创建来源",
         *     example = {}
         * )
         */
        20: optional i32 sourceType;

        /**
         * @FieldDoc(
         *     description = "一级类目",
         *     example = {}
         * )
         */
        21: string categoryFirst;

        /**
         * @FieldDoc(
         *     description = "二级类目",
         *     example = {}
         * )
         */
        22: string categorySecond;
        /**
         * @FieldDoc(
         *     description = "品牌名称",
         *     example = {}
         * )
         */
        23: string brandName;
        /**
         * @FieldDoc(
         *     description = "产地",
         *     example = {}
         * )
         */
        24: string productionArea;

        /**
             * @FieldDoc(
             *     description = "当前分类下的排序序号",
             *     example = {}
             * )
             */
        25: i32 sequence;

        /**
         * @FieldDoc(
         *     description = "是否是标品(0:非标品,1:是标品)",
         *     example = {}
         * )
         */
        26: i32 isSp;

        /**
         * @FieldDoc(
         *     description = "商家自定义SKU编码",
         *     example = "10"
         * )
         */
        27: string customSkuId;

        /**
        * @FieldDoc(
        *     description = "渠道商品编码",
        *     example = "10"
        * )
        */
        28: string channelSkuId;
        /**
        * @FieldDoc(
        *     description = "渠道店内分类列表，一级分类 + 二级分类 1、用于回调新增商品处理店内分类 2、用于单个、批量商品查询返回店内分类（elm、jddj渠道的数据在ChannelStoreCategory一级分类里边，美团ChannelStoreCategory一级、二级分类有可能都有值）",
        *     example = ""
        * )
        */
        29: list<ChannelStoreCategory> categoryList;

        /**
        * @FieldDoc(
        *     description = "商品描述，此字段信息建议长度不超过150个字符。",
        *     example = ""
        * )
        */
        32: string description;
        /**
        * @FieldDoc(
        *     description = "商品的补充标题",
        *     example = "可调节功率"
        * )
        */
        33: string flavour;
        /**
        * @FieldDoc(
        *     description = "商品的标品名",
        *     example = "飞利浦多功率电吹风"
        * )
        */
        34: string productName;
        /**
        * @FieldDoc(
        *     description = "是否为“力荐”商品，字段取值范围：0-否， 1-是。",
        *     example = "1"
        * )
        */
        36: i32 isSpecialty;
        /**
        * @FieldDoc(
        *     description = "商品的可售时间，如未单独设置可售时间，则默认商品可售时间与门店营业时间一致。",
        *     example = ""
        * )
        */
        37: string availableTimes;
        /**
        * @FieldDoc(
        *     description = "视频ID",
        *     example = "3241324"
        * )
        */
        38: i64 videoId;
        /**
        * @FieldDoc(
        *     description = "视频URL，MP4格式",
        *     example = "http://123.123.123"
        * )
        */
        39: string videoUrlMp4;
        /**
        * @FieldDoc(
        *     description = "商品属性：(1)字段信息为json格式数组，最多支持上传10组属性。(2)若填写该商品属性，则property_name和values必填。",
        *     example = ""
        * )
        */
        40: string properties;

        /**
        * @FieldDoc(
        *     description = "历史单分类结构：此字段不设置值；多分类：渠道店内末级分类列表,用于新增、修改商品绑定多分类；为了兼容单、多分类，单分类作为多分类的一种情况处理，所以单分类的时候，这个列表的size=1",
        *     example = ""
        * )
        */
        41: list<ChannelLeafStoreCategory> leafStoreCategoryList;

        /**
        * @FieldDoc(
        *     description = "（医药相关）国药准字",
        *     example = "Z10940042"
        * )
        */
        42: string medicineNo;

        /**
        * @FieldDoc(
        *     description = "创建药品的时间，为10位秒级的时间戳。",
        *     example = "1559183402"
        * )
        */
        43: i32 ctime;

        /**
        * @FieldDoc(
        *     description = "最近一次更新药品的时间，为10位秒级的时间戳。",
        *     example = "1559183402"
        * )
        */
        44: i32 utime;

        /**
        * @FieldDoc(
        *     description = "药品的处方类型，1-OTC；2-非药品；3-处方药；4-通用品。",
        *     example = "1"
        * )
        */
        45: i32 medicineType;

        /**
        * @FieldDoc(
        *     description = "无货取消订单触发库存清零相关配置。",
        *     example = "{"limit_sync_stock":true,"reset":true,"schedule":"00:00:00","sync_count":2,"sync_next_day":true,"type":[1,2]}"
        * )
        */
        46: string stockConfig;

        /**
        * @FieldDoc(
        *     description = "是否触发库存清零规则且当前禁止开放平台接口更新库存：
        *        true-是，禁止更新库存；false-否，允许更新库存。
        *        库存清零规则具体配置请查看stock_config下参数内容。",
        *     example = "true"
        * )
        */
        47: bool limitOpenSyncStockNow;


        /**
        * @FieldDoc(
        *     description = "审核状态",
        *     example = "默认0，0-未送审"
        * )
        */
        48: i32 auditStatus;

        /**
        * @FieldDoc(
        *     description = "信息是否完整",
        *     example = "0，默认完整"
        * )
        */
        49: i32 isComplete;

        /**
        * @FieldDoc(
        *     description = "商品名称补充语",
        *     example = ""
        * )
        */
        50: string nameSupplement;

        /**
        * @FieldDoc(
        *     description = "商品名称补充语顺序。0：补充语后置；1：补充语前置。（适用于医药健康门店商品）",
        *     example = "1"
        * )
        */
        51: i32 nameSupplementSeq;
}

/**
 * @TypeDoc(
 *     description = "商品Sku信息请求参数"
 * )
 */
struct SkuInfoRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequest baseInfo;

    /**
     * @FieldDoc(
     *     description = "商品Sku信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<SkuInfoDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "批量删除商品Sku信息"
 * )
 */
struct SkuInfoDeleteDTO{

    /**
     * @FieldDoc(
     *     description = "商家SKU编码",
     *     example = "10"
     * )
     */
    1: string skuId;
}


/**
 * @TypeDoc(
 *     description = "批量删除商品Sku信息请求参数"
 * )
 */
struct SkuInfoDeleteRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequest baseInfo;

    /**
     * @FieldDoc(
     *     description = "图片上传信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<SkuInfoDeleteDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "图片上传"
 * )
 */
struct PictureUploadDTO{
    /**
     * @FieldDoc(
     *     description = "图片字节流",
     *     example = "10"
     * )
     */
    1: binary pictureData;

    /**
     * @FieldDoc(
     *     description = "图片名称",
     *     example = "10"
     * )
     */
    2: string pictureName;


    /**
     * @FieldDoc(
     *     description = "唯一标识，用于返回结果区分哪个图片的URL",
     *     example = "10"
     * )
     */
    3: string uid;

    /**
     * @FieldDoc(
     *     description = "唯一标识，用于返回结果区分哪个图片的URL",
     *     example = "http://********:8001/pic/1111.jpeg"
     * )
     */
    4: string url;
}

/**
 * @TypeDoc(
 *     description = "默认图片请求参数"
 * )
 */
struct DefaultPictureRequest{

    /**
         * @FieldDoc(
         *     description = "租户渠道Id",
         *     example = {}
         * )
         */
    1: BaseRequest.BaseRequest baseInfo;
}

/**
 * @TypeDoc(
 *     description = "图片上传请求参数"
 * )
 */
struct PictureUploadRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequest baseInfo;

    /**
     * @FieldDoc(
     *     description = "图片上传信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<PictureUploadDTO> paramList;

    /**
     * @FieldDoc(
     *     description = "是否获取有赞图片url",
     *     example = "true"
     *
     * )
     */
    3: optional bool supportYzUrl;
}

/**
 * @TypeDoc(
 *     description = "图片上传结果查询"
 * )
 */
struct PictureUploadStatusDTO{

    /**
     * @FieldDoc(
     *     description = "渠道商品Id集合",
     *     example = "10"
     * )
     */
    1: list<string> channelSkuIdList;
}

/**
 * @TypeDoc(
 *     description = "图片处理状态信息"
 * )
 */
struct ImgHandleQueryDTO {

    /**
     * @FieldDoc(
     *     description = "渠道商品ID",
     *     example = "1"
     * )
     */
    1: string channelSkuId;

    /**
     * @FieldDoc(
     *     description = "图片原始路径",
     *     example = ""
     * )
     */
    2: string sourceImgUrl;

    /**
     * @FieldDoc(
     *     description = "处理状态",
     *     example = "1"
     * )
     */
    3: i32 handleStatus;

    /**
     * @FieldDoc(
     *     description = "处理状态说明",
     *     example = "成功"
     * )
     */
    4: string handleStatusDesc;

    /**
     * @FieldDoc(
     *     description = "备注",
     *     example = "1"
     * )
     */
    5: string remark;

    /**
     * @FieldDoc(
     *     description = "错误信息",
     *     example = "1"
     * )
     */
    6: string errorMsg;

    /**
     * @FieldDoc(
     *     description = "图片展示顺序（商品图片）",
     *     example = "1"
     * )
     */
    7: i32 skuImgSort;
    /**
     * @FieldDoc(
     *     description = "是否主图（1是，0否）",
     *     example = "1"
     * )
     */
    8: i32 isMain;
    /**
     * @FieldDoc(
     *     description = "图片类型（1商品图片，2详情图片）",
     *     example = "1"
     * )
     */
    9: i32 imgType;

}

/**
 * @TypeDoc(
 *     description = "图片上传结果查询"
 * )
 */
struct PictureUploadStatusRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequest baseInfo;


    /**
     * @FieldDoc(
     *     description = "图片上传结果查询结合",
     *     example = "10"
     * )
     */
    2: PictureUploadStatusDTO param;
}

/**
 * @TypeDoc(
 *     description = "查询图片处理结果出参"
 * )
 */
struct PictureUploadResult {
    /**
     * @FieldDoc(
     *     description = "接口调用结果状态，调用结果全部成功或部分成功时返回0，需要解析sucData和errorData，其他场景只返回对应异常码",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "图片处理状态信息",
     *     example = "ImgHandleQueryDTO"
     * )
     */
    2: list<ImgHandleQueryDTO> imgHandleList;

}

/**
 * @TypeDoc(
 *     description = "商品编码信息"
 * )
 */
struct SkuIdDTO {
    /**
     * @FieldDoc(
     *     description = "渠道商品编码",
     *     example = "10"
     * )
     */
    1: string skuId;
    /**
     * @FieldDoc(
     *     description = "自定义编码",
     *     example = "10"
     * )
     */
    2: string customSkuId;
}

/**
 * @TypeDoc(
 *     description = "商品上架/下架信息"
 * )
 */
struct SkuSellStatusInfoDTO {

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商家SKU编码",
     *     example = "10"
     * )
     */
    2: list<SkuIdDTO> skuId;

    /**
     * @FieldDoc(
     *     description = "商品上下架状态（1-上架；2-下架）",
     *     example = "1"
     * )
     */
    3: i32 skuStatus;
}

/**
 * @TypeDoc(
 *     description = "更新customSkuId"
 * )
 */
struct UpdateCustomSkuIdDTO {

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = ""
         * )
         */
        1: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "商家SKU编码",
         *     example = "10"
         * )
         */
        2: string customSkuId;

        /**
         * @FieldDoc(
         *     description = "商品编码",
         *     example = "1"
         * )
         */
        3: string skuId;

        /**
         * @FieldDoc(
         *     description = "商品编码",
         *     example = "1"
         * )
         */
        4: string skuName;
        /**
         * @FieldDoc(
         *     description = "商品分类编码",
         *     example = "1"
         * )
         */
        5: string categoryCode;
        /**
         * @FieldDoc(
         *     description = "商品规格",
         *     example = "1"
         * )
         */
        6: string spec;
        /**
         * @FieldDoc(
         *     description = "商品分类名称",
         *     example = "1"
         * )
         */
        7: string categoryName;
}

/**
 * @TypeDoc(
 *     description = "商品上架/下架入参"
 * )
 */
struct SkuSellStatusInfoRequest {
    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = ""
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "图片处理状态信息",
     *     example = "ImgHandleQueryDTO"
     * )
     */
    2: list<SkuSellStatusInfoDTO> paramList;

     /**
     * @FieldDoc(
     *     description = "是否使用渠道id操作商品，默认false",
     *     example = "false"
     *
     * )
     */
    3: optional bool optByChannelSpuId;
}

/**
 * @TypeDoc(
 *     description = "获取单个商品请求"
 * )
 */
struct GetSkuInfoRequest {
        /**
             * @FieldDoc(
             *     description = "请求基本信息",
             *     example = {}
             * )
             */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = ""
         * )
         */
        2: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "商家SKU编码",
         *     example = "10"
         * )
         */
        3: string customSkuId;
        /**
        * @FieldDoc(
        *     description = "渠道SKU编码",
        *     example = "10"
        * )
        */
        4: string channelSkuId;

        /**
         * @FieldDoc(
         *     description = "是否无视异常数据过滤，医药使用，避免过滤掉category_code的数据",
         *     example = ""
         * )
         */
        5: bool ignoreCheck = false;
}

/**
 * @TypeDoc(
 *     description = "获取单个商品信息"
 * )
 */
struct GetSkuInfoResponse {
        /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
        1: ResultStatus.ResultStatus status;

        /**
     * @FieldDoc(
     *     description = "商品Sku信息列表",
     *     example = "1"
     *
     * )
     */
        2: SkuInfoDTO skuInfo;
}

/**
 * @TypeDoc(
 *     description = "批量拉取商品请求"
 * )
 */
struct BatchGetSkuInfoRequest {
    /**
         * @FieldDoc(
         *     description = "请求基本信息",
         *     example = {}
         * )
         */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    2: i64 storeId;

        /**
     * @FieldDoc(
     *     description = "页码",
     *     example = ""
     * )
     */
    3: i32 pageNum = 1;

        /**
         * @FieldDoc(
         *     description = "每页大小",
         *     example = ""
         * )
         */
    4: i32 pageSize = 20;

    /**
     * @FieldDoc(
     *     description = "是否无视异常数据过滤，医药使用，避免过滤掉category_code的数据",
     *     example = ""
     * )
     */
    5: bool ignoreCheck = false;

}

/**
 * @TypeDoc(
 *     description = "通过游标的方式批量拉取商品请求"
 * )
 */
struct BatchGetSkuInfoByOffsetRequest {
    /**
         * @FieldDoc(
         *     description = "请求基本信息",
         *     example = {}
         * )
         */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    2: i64 storeId;


    /**
     * @FieldDoc(
     *     description = "商品id游标",
     *     example = ""
     * )
     */
    3: i64 skuIdOffset;

}
/**
 * @TypeDoc(
 *     description = "通过游标批量拉取商品信息"
 * )
 */
struct BatchGetSkuInfoByOffsetResponse {
    /**
 * @FieldDoc(
 *     description = "结果状态",
 *     example = ""
 * )
 */
    1: ResultStatus.ResultStatus status;

    /**
 * @FieldDoc(
 *     description = "商品Sku信息列表",
 *     example = "1"
 *
 * )
 */
    2: list<SkuInfoDTO> skuInfos;

    /**
        * @FieldDoc(
        *     description = "游标信息",
        *     example = "{}"
        * )
        */
    3: i64 skuIdOffset;
}

/**
 * @TypeDoc(
 *     description = "批量拉取商品信息"
 * )
 */
struct BatchGetSkuInfoResponse {
    /**
 * @FieldDoc(
 *     description = "结果状态",
 *     example = ""
 * )
 */
    1: ResultStatus.ResultStatus status;

    /**
 * @FieldDoc(
 *     description = "商品Sku信息列表",
 *     example = "1"
 *
 * )
 */
    2: list<SkuInfoDTO> skuInfos;

    /**
        * @FieldDoc(
        *     description = "分页信息",
        *     example = "{}"
        * )
        */
    3: PageInfo.PageInfo pageInfo;
}

/**
 * @TypeDoc(
 *     description = "更换新商品编码请求"
 * )
 */
struct UpdateCustomSkuIdRequest {

        /**
        * @FieldDoc(
        *     description = "更新渠道customSkuId",
        *     example = {}
        * )
        */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
        * @FieldDoc(
        *     description = "更新数据信息",
        *     example = "UpdateCustomSkuIdDTO"
        * )
        */
        2: list<UpdateCustomSkuIdDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "商品通知"
 * )
 */
struct SkuNotifyRequest {
        /**
 * @FieldDoc(
 *     description = "租户唯一标识",
 *     example = "1"
 * )
 */
        1: string tenantAppId;
        /**
         * @FieldDoc(
         *     description = "渠道CODE唯一标识",
         *     example = "1"
         * )
         */
        2: string channelCode;
        /**
     * @FieldDoc(
     *     description = "操作",
     *     example = "cmd"
     * )
     */
        3: string action;
        /**
         * @FieldDoc(
         *     description = "线上渠道表示",
         *     example = "t_IvAoWBz4Mj"
         * )
         */
        4:  string data;
}

/**
 * @TypeDoc(
 *     description = "更换customSkuId"
 * )
 */
struct ChangeCustomSkuIdDTO {
        /**
         * @FieldDoc(
         *     description = "门店编码",
         *     example = ""
         * )
         */
        1: i64 storeId;
        /**
         * @FieldDoc(
         *     description = "新商家自定义编码",
         *     example = "10"
         * )
         */
        2: string customSkuId;
        /**
         * @FieldDoc(
         *     description = "原商家自定义编码",
         *     example = "10"
         * )
         */
        4: string orgCustomSkuId;
        /**
         * @FieldDoc(
         *     description = "新渠道商品编码",
         *     example = "1"
         * )
         */
        3: string channelSkuId;
        /**
         * @FieldDoc(
         *     description = "原渠道商品编码",
         *     example = "10"
         * )
         */
        5: string orgChannelSkuId;
}

/**
 * @TypeDoc(
 *     description = "更换商品商家自定义编码请求"
 * )
 */
struct ChangeCustomSkuIdRequest {
        /**
        * @FieldDoc(
        *     description = "基础参数",
        *     example = {}
        * )
        */
        1: BaseRequest.BaseRequestSimple baseInfo;
        /**
        * @FieldDoc(
        *     description = "更新数据信息",
        *     example = "UpdateCustomSkuIdDTO"
        * )
        */
        2: list<ChangeCustomSkuIdDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "图片URL和ID查询成功信息"
 * )
 */
struct PictureUrl2IdSuccessDTO {
        /**
        * @FieldDoc(
        *     description = "图片URL",
        *     example = {}
        * )
        */
        1: string pictureUrl;
        /**
        * @FieldDoc(
        *     description = "图片ID",
        *     example = {}
        * )
        */
        2: string pictureId;
}

/**
 * @TypeDoc(
 *     description = "图片URL和ID查询错误信息"
 * )
 */
struct PictureUrl2IdErrorDTO {
        /**
        * @FieldDoc(
        *     description = "图片URL",
        *     example = {}
        * )
        */
        1: string pictureUrl;
        /**
        * @FieldDoc(
        *     description = "错误码",
        *     example = {}
        * )
        */
        2: i32 code;
        /**
        * @FieldDoc(
        *     description = "错误信息",
        *     example = {}
        * )
        */
        3: string msg;
}

/**
 * @TypeDoc(
 *     description = "根据图片URL批量上传图片返回信息"
 * )
 */
struct BatchUploadPictureByUrlsResponse {
        /**
         * @FieldDoc(
         *     description = "结果状态",
         *     example = ""
         * )
         */
         1: ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "结果状态",
         *     example = ""
         * )
         */
         2: list<PictureUrl2IdSuccessDTO> successList;

        /**
         * @FieldDoc(
         *     description = "结果状态",
         *     example = ""
         * )
         */
         3: list<PictureUrl2IdErrorDTO> errorList;
}

/**
 * @TypeDoc(
 *     description = "商品违规 预警/处罚"
 * )
 */
struct SkuViolationNotifyRequest {
        /**
         * @FieldDoc(
         *     description = "租户唯一标识",
         *     example = "1"
         * )
         */
        1: string tenantAppId;
        /**
         * @FieldDoc(
         *     description = "渠道CODE唯一标识",
         *     example = "1"
         * )
         */
        2: string channelCode;
        /**
         * @FieldDoc(
         *     description = "操作",
         *     example = "cmd"
         * )
         */
        3: string action;

        /**
         * @FieldDoc(
         *     description = "处罚ID / 预警ID",
         *     example = {}
         * )
         */
        4: i64 id;
        /**
         * @FieldDoc(
         *     description = "违规场景信息",
         *     example = {}
         * )
         */
        5: string sceneName;
        /**
         * @FieldDoc(
         *     description = "违规原因",
         *     example = {}
         * )
         */
        6: string reason;
        /**
         * @FieldDoc(
         *     description = "处罚方式",
         *     example = {}
         * )
         */
        7: string punishMethod;
        /**
         * @FieldDoc(
         *     description = "违规时间",
         *     example = {}
         * )
         */
        8: i64 ctime;
        /**
         * @FieldDoc(
         *     description = "处罚状态：1-待确认，2-待处罚，3-处罚中，4-已处罚，5-取消处罚，6-撤销处罚，7-处罚失败，8-免除处罚",
         *     example = {}
         * )
         */
        9: string punishStatus;
        /**
         * @FieldDoc(
         *     description = "门店相关信息",
         *     example = {}
         * )
         */
        10: string poiInfo;
        /**
         * @FieldDoc(
         *     description = "规则相关信息",
         *     example = {}
         * )
         */
        11: string ruleInfo;
        /**
         * @FieldDoc(
         *     description = "商品信息",
         *     example = {}
         * )
         */
        12: string spuData;
        /**
         * @FieldDoc(
         *     description = "输入参数计算后的签名结果",
         *     example = {}
         * )
         */
        13: string sig;

}

/**
 * @TypeDoc(
 *     description = "渠道商品标签请求"
 * )
 */
struct SkuTagNotifyRequest {
        /**
         * @FieldDoc(
         *     description = "租户唯一标识",
         *     example = "1"
         * )
         */
        1: string tenantAppId;
        /**
         * @FieldDoc(
         *     description = "渠道CODE唯一标识",
         *     example = "1"
         * )
         */
        2: string channelCode;
        /**
         * @FieldDoc(
         *     description = "渠道门店",
         *     example = "cmd"
         * )
         */
        3: string appPoiCode;

        /**
         * @FieldDoc(
         *     description = "渠道商品code",
         *     example = {}
         * )
         */
        4: string appSpuCode;
        /**
         * @FieldDoc(
         *     description = "商品名称",
         *     example = {}
         * )
         */
        5: string name;
        /**
         * @FieldDoc(
         *     description = "标签信息",
         *     example = {}
         * )
         */
        6: string labelData;
}

struct ProblemReq{

    1: i64 problemCode;

    2: string problemName;

    3: string problemMsg;

}

/**
 * @TypeDoc(
 *     description = "商品管家异常项"
 * )
 */
struct EffectReq{
    /**
     * @FieldDoc(
     *     description = "商品管家 异常信息列表323",
     *     example = "[]"
     * )
     */
    1: i64 effectCode;

    /**
     * @FieldDoc(
     *     description = "商品管家 异常信息列表3342",
     *     example = "[]"
     * )
     */
    2: string effectName;

    3: list<ProblemReq> problemItem;
}

/**
 * @TypeDoc(
 *     description = "商品管家 异常相关数据入参"
 * )
 */
struct SkuManagerAbnormalChangeRequest{
        /**
         * @FieldDoc(
         *     description = "租户唯一标识",
         *     example = "1"
         * )
         */
        1: string tenantAppId;
        /**
         * @FieldDoc(
         *     description = "渠道CODE唯一标识",
         *     example = "1"
         * )
         */
        2: string channelCode;
        /**
         * @FieldDoc(
         *     description = "渠道门店唯一标识",
         *     example = "1"
         * )
         */
        3: string appPoiCode;

        /**
         * @FieldDoc(
         *     description = "渠道商品标识",
         *     example = "1"
         * )
         */
        4: string appSpuCode;
        /**
         * @FieldDoc(
         *     description = "商品管家 异常信息列表",
         *     example = "[]"
         * )
         */
        5: string qualityInfo;

        /**
         * @FieldDoc(
         *     description = "渠道请求标识sig",
         *     example = "[]"
         * )
         */
        6: string sig;

        /**
        * @FieldDoc(
        *     description = "商品变更时间",
        *     example = "[]"
        * )
        */
        7: i64 utime;

}

/**
 * @TypeDoc(
 *     description = "商品申诉状态通知数据入参"
 * )
 */
struct SkuAppealInfoNotifyRequest{
        /**
         * @FieldDoc(
         *     description = "租户唯一标识",
         *     example = "1"
         * )
         */
        1: string tenantAppId;
        /**
         * @FieldDoc(
         *     description = "渠道CODE唯一标识",
         *     example = "1"
         * )
         */
        2: string channelCode;
        /**
         * @FieldDoc(
         *     description = "渠道门店唯一标识",
         *     example = "1"
         * )
         */
        3: string appPoiCode;

        /**
         * @FieldDoc(
         *     description = "渠道商品标识",
         *     example = "1"
         * )
         */
        4: string appSpuCode;
        /**
         * @FieldDoc(
         *     description = "申诉结果变更 1-申诉中，2-申诉通过，3-申诉驳回， 4-申诉撤销，5-部分申诉通过",
         *     example = "[]"
         * )
         */
        5: i32 appealResult;

        /**
         * @FieldDoc(
         *     description = "商品申诉类型 1-商品信息质量审核，2-商品信息合规审核，3-商品管家质量问题",
         *     example = "[]"
         * )
         */
        6: i32 appealType;
}

/**
 * @TypeDoc(
 *     description = "查询违规商品信息请求参数"
 * )
 */
struct BatchGetViolateSkuRequest{

    /**
     * @FieldDoc(
     *     description = "基础信息",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "skuId列表",
     *     example = "1"
     *
     * )
     */
    2: list<string> channelSkuIds;
}

/**
 * @TypeDoc(
 *     description = "查询违规商品信息"
 * )
 */
struct ViolateSkuDTO {

    /**
     * @FieldDoc(
     *     description = "渠道Sku编码",
     *     example = "1"
     * )
     */
    1: string channelSkuId;

    /**
     * @FieldDoc(
     *     description = "图片原始路径",
     *     example = ""
     * )
     */
    2: string firstReasonName;

    /**
     * @FieldDoc(
     *     description = "二级问题原因",
     *     example = "1"
     * )
     */
    3: string secondReasonName;

    /**
     * @FieldDoc(
     *     description = "原因描述",
     *     example = "成功"
     * )
     */
    4: string reasonDesc;

    /**
     * @FieldDoc(
     *     description = "原因具体核验的值",
     *     example = "1"
     * )
     */
    5: string reasonValue;

    /**
     * @FieldDoc(
     *     description = "影响的字段",
     *     example = "1"
     * )
     */
    6: string manageField;

    /**
     * @FieldDoc(
     *     description = "影响的字段",
     *     example = "1"
     * )
     */
    7: list<string> manageFieldDesc;
}

/**
 * @TypeDoc(
 *     description = "查询违规商品信息响应"
 * )
 */
struct BatchGetViolateSkuResponse{
    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
     1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "违规商品信息",
     *     example = "1"
     *
     * )
     */
    2: list<ViolateSkuDTO> violateSkuList;
}


/**
 * @TypeDoc(
 *     description = "更换customSkuId"
 * )
 */
struct CustomSkuIdChangeDTO {
        /**
         * @FieldDoc(
         *     description = "商家新自定义编码",
         *     example = "10"
         * )
         */
        1: string customSpuId;
        /**
         * @FieldDoc(
         *     description = "原商家自定义编码",
         *     example = "10"
         * )
         */
        2: string orgCustomSpuId;
        /**
         * @FieldDoc(
         *     description = "新渠道商品编码",
         *     example = "1"
         * )
         */
        3: string customSkuId;
        /**
         * @FieldDoc(
         *     description = "原渠道商品编码",
         *     example = "10"
         * )
         */
        4: string orgCustomSkuId;
}

/**
 * @TypeDoc(
 *     description = "更换商品商家自定义编码请求"
 * )
 */
struct CustomSkuIdChangeRequest {
        /**
        * @FieldDoc(
        *     description = "基础参数",
        *     example = {}
        * )
        */
        1: BaseRequest.BaseChannelPoiRequestSimple baseInfo;
        /**
        * @FieldDoc(
        *     description = "更新数据信息",
        *     example = "UpdateCustomSkuIdDTO"
        * )
        */
        2: list<CustomSkuIdChangeDTO> paramList;
}



struct SkuSaleInfoDTO {
    1: string channelSkuId;
    2: i32 saleStatus;
    3: i32 stock;
}

struct BatchGetSkuSaleInfoRequest{

    /**
     * @FieldDoc(
     *     description = "基础信息",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    2: i64 storeId;

    3: list<string> channelSkuIds;

}

struct BatchGetSkuSaleInfoResponse{

    1: ResultStatus.ResultStatus status;

    2: list<SkuSaleInfoDTO> skuSaleInfos;
}


