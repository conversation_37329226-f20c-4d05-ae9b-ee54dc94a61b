namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi

/**
 * @TypeDoc(
 *     description = "门店基本信息"
 * )
 */
struct ChannelPoiAuthRequest {

        /**
         * @FieldDoc(
         *     description = "门店渠道内部编码编码",
         *     example = ""
         * )
         */
        1: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = ""
         * )
         */
        2: i32 channelId;


        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = ""
         * )
         */
        3: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "自定义门店编码",
         *     example = ""
         * )
         */
        4: string storeCode;

}