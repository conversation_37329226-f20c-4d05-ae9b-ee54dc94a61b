namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi


/**
 * @TypeDoc(
 *     description = "渠道门店类目信息查询请求"
 * )
 */
struct ChannelPoiCategoryRequest{


        /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
    1: required i64 tenantId;


        /**
     * @FieldDoc(
     *     description = "渠道id",
     *     example = "100"
     * )
     */
    2: required i32 channelId;

   /**
     * @FieldDoc(
     *     description = "渠道门店编码列表",
     *     example = "[]"
     * )
     */
    3: required list<string> channelPoiCodeList;

}

/**
 * @TypeDoc(
 *     description = "渠道门店品类DTO"
 * )
 */
struct ChannelPoiCategoryDTO{


    /**
     * @FieldDoc(
     *     description = "渠道id",
     *     example = "100"
     * )
     */
    1: required i32 channelId;


    /**
      * @FieldDoc(
      *     description = "渠道门店编码",
      *     example = "100"
      * )
      */
    2: required string channelPoiCode;

    /**
      * @FieldDoc(
      *     description = "渠道门店的一级类目id",
      *     example = "100"
      * )
      */
    3: required i64 cat1Id;

    /**
      * @FieldDoc(
      *     description = "渠道门店的一级类目名称",
      *     example = "100"
      * )
      */
    4: required string cat1Name;
}

/**
 * @TypeDoc(
 *     description = "渠道类目信息返回值"
 * )
 */
struct ChannelPoiCategoryResponse{
    /**
     * @FieldDoc(
     *     description = "状态码",
     *     example = "0"
     * )
     */
    1: required i32 code;

    /**
     * @FieldDoc(
     *     description = "状态描述",
     *     example = "SUCCESS"
     * )
     */
    2: required string msg;

    /**
     * @FieldDoc(
     *     description = "渠道类目信息",
     *     example = "SUCCESS"
     * )
     */
    3: required list<ChannelPoiCategoryDTO> data;


}
