namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi

/**
 * @TypeDoc(
 *     description = "门店地址更改"
 * )
 */
struct PoiAddressUpdateRequest {

    /**
     * @FieldDoc(
     *     description = "渠道门店编码",
     *     example = ""
     * )
     */
    1: string channelPoiCode;

    2: i32 channelId;

    3: i64 tenantId;

    4: string address;

    5: double longitude;

    6: double latitude;
}

/**
 * @TypeDoc(
 *     description = "售后地址信息"
 * )
 */
struct UpdateSaleafterAddressMessageRequest {

    1: i64 provinceId;

    2: i64 cityId;

    3: i64 districtId;

    4: optional i64 streetId;

    5: optional i64 addressId;

    6: string receiverName;

    7: string phone;

    8: string detail;

    9: string channelPoiCode;

    10: i64 tenantId;

    11: i64 poiId;

    12: i32 channelId;
}
/**
 * @TypeDoc(
 *     description = "省份列表"
 * )
 */
struct GetProvinceListRequest{

    1: i64 tenantId;

    2: i64 poiId;

    3: string channelPoiCode;

    4: i64 provinceId;

}
