namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi


struct ShippingTime {
        1: string startTime;
        2: string endTime;
}
/**
 * @TypeDoc(
 *     description = "门店营业时间更改"
 * )
 */
struct PoiShippingTimeUpdateRequest {

    /**
     * @FieldDoc(
     *     description = "渠道门店编码",
     *     example = ""
     * )
     */
    1: string channelPoiCode;

    2: i32 channelId;

    3: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "门店营业时间",
     *     example = ""
     * )
     */
    4: list<ShippingTime> shippingTimes;

    /**
     * @FieldDoc(
     *     description = "门店营业日期，如周一、周二、周日",
     *     example = "[0,1,6]"
     * )
     */
    5: optional set<i32> shippingDays;

    /**
     * @FieldDoc(
     *     description = "商家门店id",
     *     example = ""
     * )
     */
    6: optional i64 storeId;
}

