namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi
include "PoiShippingTimeUpdateRequest.thrift"

/**
 * @TypeDoc(
 *     description = "门店基本信息"
 * )
 */
struct PoiInfo {

    /**
     * @FieldDoc(
     *     description = "商家门店编码",
     *     example = ""
     * )
     */
    1: string appPoiCode;

    /**
     * @FieldDoc(
     *     description = "门店名",
     *     example = ""
     * )
     */
    2: string name;

    /**
     * @FieldDoc(
     *     description = "省",
     *     example = ""
     * )
     */
    3: string province;

    /**
     * @FieldDoc(
     *     description = "市",
     *     example = ""
     * )
     */
    4: string city;

    /**
     * @FieldDoc(
     *     description = "县",
     *     example = ""
     * )
     */
    5: string county;

    /**
     * @FieldDoc(
     *     description = "地址",
     *     example = ""
     * )
     */
    6: string address;

    /**
     * @FieldDoc(
     *     description = "经度",
     *     example = ""
     * )
     */
    7: double longitude;

    /**
     * @FieldDoc(
     *     description = "纬度",
     *     example = ""
     * )
     */
    8: double latitude;

    /**
     * @FieldDoc(
     *     description = "图片地址",
     *     example = ""
     * )
     */
    9: string picUrl;

    /**
     * @FieldDoc(
     *     description = "是否支持发票",
     *     example = ""
     * )
     */
    10: i32 invoiceSupport;

    /**
     * @FieldDoc(
     *     description = "营业状态",
     *     example = ""
     * )
     */
    11: i32 openLevel;

    /**
     * @FieldDoc(
     *     description = "是否上线",
     *     example = ""
     * )
     */
    12: i32 isOnline;

    /**
     * @FieldDoc(
     *     description = "渠道门店编码",
     *     example = ""
     * )
     */
    13: string channelPoiCode;

    /**
     * @FieldDoc(
     *     description = "最晚歇业时间, 定时自动上架用, 从零点开始的毫秒数",
     *     example = ""
     * )
     */
    14: i64 closeTime;


    /**
    * @FieldDoc(
    *     description = "营业时间",
    *     example = ""
    * )
    */
    15: string shippingTime;


    /**
    * @FieldDoc(
    *     description = "门店公告",
    *     example = ""
    * )
    */
    16: string promotionInfo;

    /**
    * @FieldDoc(
    *     description = "是否支持营业时间范围外预下单",
    *     example = "1-支持；0-不支持"
    * )
    */
    17: i32 preBook;

    /**
    * @FieldDoc(
    *     description = "商家接受预订日期的最早日期，范围：-1-7",
    *     example = "0-当天"
    * )
    */
    18: i32 preBookMinDays = -1;

    /**
    * @FieldDoc(
    *     description = "商家接受预订日期的最长日期，范围：-1-7",
    *     example = "0-当天 -1-无效"
    * )
    */
    19: i32 preBookMaxDays = -1;

    /**
     * @FieldDoc(
     *     description = "租户品牌应用id",
     *     example = "1"
     * )
     */
    20: i64 appId = 1;

    /**
         * @FieldDoc(
         *     description = "渠道门店客服电话",
         *     example = "1"
         * )
         */
    21: string hotline;

    /**
         * @FieldDoc(
         *     description = "物理品牌",
         *     example = "1"
         * )
         */
    22: optional i64 originBrandId;

    /**
     * @FieldDoc(
     *     description = "appKey",
     *     example = "1"
     * )
     */
    23: string appKey;

}

/**
* 门店商品权限列表
**/
struct PoiAuthDetailDTO {
    /**
    * 2-门店自主管理商品
    **/
    1: i32 authType;

    /**
    * 1-有效 2-无效
    **/
    2: i32 status;
}