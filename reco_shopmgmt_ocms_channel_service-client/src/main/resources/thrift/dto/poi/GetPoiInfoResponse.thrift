namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi

include "ResultStatus.thrift"
include "PoiInfo.thrift"

/**
 * @TypeDoc(
 *     description = "查询门店基本信息响应结果"
 * )
 */
struct GetPoiInfoResponse {

    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "门店列表",
     *     example = ""
     * )
     */
    2: list<PoiInfo.PoiInfo> poiInfoList;
}

/**
 * @TypeDoc(
 *     description = "商家门店编码传输类，主要是带上门店对应的品牌信息（一租户多品牌需求）"
 * )
 */
struct AppPoiCodeDTO {
    /**
     * @FieldDoc(
     *     description = "商家门店编码",
     *     example = ""
     * )
     */
    1: string appPoiCode;

    /**
     * @FieldDoc(
     *     description = "租户品牌应用id",
     *     example = "1"
     * )
     */
    2: i64 appId;
}

/**
 * @TypeDoc(
 *     description = "查询门店Id列表响应结果"
 * )
 */
struct GetPoiIdsResponse {

        /**
         * @FieldDoc(
         *     description = "结果状态",
         *     example = ""
         * )
         */
        1: ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "门店ID列表",
         *     example = ""
         * )
         */
        2: list<string> storeIds;

        /**
         * @FieldDoc(
         *     description = "商家门店编码传输类列表，可选，目前只有美团渠道+新供给侧有值",
         *     example = ""
         * )
         */
        3: optional list<AppPoiCodeDTO> appPoiCodeDTOList;

}

struct UpdateSaleafterAddressMessageResponse{

         1: ResultStatus.ResultStatus status;

         2: i64 addressId;
}

struct ProvinceResponse{

    2: string province;

    3: i64 provinceId;

}
struct GetProvinceListResponse{

    1: ResultStatus.ResultStatus status;

    2: list<ProvinceResponse> provinceList;
}

struct ProvinceDistrictsStreetDTO{

    1: i64 id;

    2: string name;

    3: string level;

    4: i64 parentId;

}

struct ProvinceDistrictsDistrictsDTO{

    1: i64 id;

    2: string name;

    3: string level;

    4: i64 parentId;

    5: list<ProvinceDistrictsStreetDTO> subDistricts;

}

struct ProvinceDistrictsCityDTO{

    1: i64 id;

    2: string name;

    3: string level;

    4: i64 parentId;

    5: list<ProvinceDistrictsDistrictsDTO> subDistricts;

}

struct GetProvinceDistrictsListResponse{

    1: ResultStatus.ResultStatus status;

    2: list<ProvinceDistrictsCityDTO> data;
}

struct QueryPoiAuthDetailResponse {

    1: ResultStatus.ResultStatus status;

    2: list<PoiInfo.PoiAuthDetailDTO> poiAuthDetailList;
}