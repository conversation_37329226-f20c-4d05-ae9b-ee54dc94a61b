namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi

/**
 * @TypeDoc(
 *     description = "门店基本信息"
 * )
 */
struct ChannelPoiCallbackRequest {

        /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
        1: required string tenantAppId;

        /**
         * @FieldDoc(
         *     description = "渠道CODE唯一标识",
         *     example = "mt"
         * )
         */
        2: required string channelCode;


        /**
         * @FieldDoc(
         *     description = "签名结果",
         *     example = ""
         * )
         */
        3: required string sig;


        /**
         * @FieldDoc(
         *     description = "商家门店code",
         *     example = ""
         * )
         */
        4: string appPoiCode;

}

/**
 * @TypeDoc(
 *     description = "门店信息"
 * )
 */
struct ChannelPoiInfoDto {
    1: string appPoiCode;

    2: string poiName;

    3: i64 wmPoiId;

    4: i32 poiStatus;
}

/**
 * @TypeDoc(
 *     description = "门店解绑"
 * )
 */
struct ChannelPoiBindUnbindCallbackRequest {

        /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
        1: required string tenantAppId;

        /**
         * @FieldDoc(
         *     description = "渠道CODE唯一标识",
         *     example = "mt"
         * )
         */
        2: required string channelCode;


        /**
         * @FieldDoc(
         *     description = "签名结果",
         *     example = ""
         * )
         */
        3: required string sig;

      /**
         * @FieldDoc(
         *     description = "门店信息",
         *     example = ""
         * )
         */
        4: required string poiInfo;

        /**
         * @FieldDoc(
         *     description = "操作类型：1-绑定门店；2-解绑门店；3-官网推送token",
         *     example = ""
         * )
         */
        5: required string opType;

        /**
         * @FieldDoc(
         *     description = "官网推送token信息",
         *     example = ""
         * )
         */
        6: required string tokenInfo;

        /**
         * @FieldDoc(
         *     description = "自定义参数",
         *     example = ""
         * )
         */
        7: required string customParam;
}

/**
 * @TypeDoc(
 *     description = "百川新创建app回调"
 * )
 */
struct ChannelAddAppCallbackRequest {

        /**
         * @FieldDoc(
         *     description = "模板app的app_id，用于签名验证",
         *     example = "1"
         * )
         */
        1: required string tenantAppId;

         /**
         * @FieldDoc(
         *     description = "模板app的app_id，用于签名验证",
         *     example = "1"
         * )
         */
        2: required string createApInfo;


        /**
         * @FieldDoc(
         *     description = "签名结果",
         *     example = ""
         * )
         */
        3: required string sig;
}

/**
 * @TypeDoc(
 *     description = "美团token"
 * )
 */
struct MtTokenMessage {

        /**
         * @FieldDoc(
         *     description = "accessToken",
         *     example = "1"
         * )
         */
        1: required string accessToken;


        /**
         * @FieldDoc(
         *     description = "accessToken过期时间戳",
         *     example = "1"
         * )
         */
        2: required i64 expires;


        /**
        * @FieldDoc(
        *     description = "refreshToken",
        *     example = "1"
        * )
        */
        3: required string refreshToken;


        /**
        * @FieldDoc(
        *     description = "refreshToken过期时间戳",
        *     example = "1"
        * )
        */
        4: required i64 refreshExpires;


}

/**
 * @TypeDoc(
 *     description = "set美团token缓存请求"
 * )
 */
struct SetMtTokenCacheRequest{

        /**
        * @FieldDoc(
        *     description = "tenantId",
        *     example = "1"
        * )
        */
        1: required i64 tenantId;

        /**
        * @FieldDoc(
        *     description = "channelPoiCode",
        *     example = "1"
        * )
        */
        2: required string channelPoiCode;


        /**
        * @FieldDoc(
        *     description = "accessToken",
        *     example = "1"
        * )
        */
        3: required MtTokenMessage mtTokenMessage;

        /**
        * @FieldDoc(
        *     description = "appKey",
        *     example = "1"
        * )
        */
        4: required string appKey;

}

