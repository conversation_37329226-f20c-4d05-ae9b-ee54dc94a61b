namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi

/**
 * @TypeDoc(
 *     description = "获取渠道接口签名请求"
 * )
 */
struct ChannelPoiSignRequest {

    /**
     * @FieldDoc(
     *     description = "商家门店编码",
     *     example = ""
     * )
     */
    1: string channelPoiCode;

    2: i32 channelId;

    3: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "商家门店id",
     *     example = ""
     * )
     */
    4: optional i64 storeId;

    /**
     * @FieldDoc(
     *     description = "请求链接",
     *     example = ""
     * )
     */
    5: optional string baseUrl;

    /**
     * @FieldDoc(
     *     description = "商家门店id",
     *     example = ""
     * )
     */
    6: optional string method;

    /**
     * @FieldDoc(
     *     description = "参与签名的参数",
     *     example = ""
     * )
     */
    7: map<string, string> param;

}