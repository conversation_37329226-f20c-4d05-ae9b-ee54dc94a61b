namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poi

/**
 * @TypeDoc(
 *     description = "门店的接受预定的日期范围更改"
 * )
 */
struct PoiPrebookDaysUpdateRequest {

        /**
         * @FieldDoc(
         *     description = "渠道门店编码",
         *     example = ""
         * )
         */
        1: string channelPoiCode;

        2: i32 channelId;

        3: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "商家接受预订日期的最早日期，范围：0-7",
         *     example = "0-当天"
         * )
         */
        4: i32 prebookMinDays = -1;

        /**
         * @FieldDoc(
         *     description = "商家接受预订日期的最长日期，范围：0-7",
         *     example = "0-当天"
         * )
         */
        5: i32 prebookMaxDays = -1;

        /**
         * @FieldDoc(
         *     description = "商家门店id",
         *     example = ""
         * )
         */
        6: optional i64 storeId;

}