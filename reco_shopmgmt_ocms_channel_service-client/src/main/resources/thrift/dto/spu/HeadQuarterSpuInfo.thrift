namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"
include "BaseRequest.thrift"
include "PageInfo.thrift"


/**
 * @TypeDoc(
 *     description = "总部商品sku"
 * )
 */
struct HeadQuarterSkuInfoDTO {

    1: string customSkuId;

    2: string spec;

    3: string upc;

    4: string stock;

    5: string price;

    6: string unit;

    7: i32 minOrderCount;

    8: string availableTimes;

    10: string ladderBoxNum;

    11: string ladderBoxPrice;

    12: string weightForUnit;

    13: string weightUnit;

    14: i32 isSellFlag;

    15: bool allowNoUpc;
}

/**
 * @TypeDoc(
 *     description = "总部商品spu"
 * )
 */
struct HeadQuarterSpuInfoDTO {

    1: string appSpuCode;

    2: string name;

    3: string categoryName;

    4: string secondaryCategoryName;

    5: string categoryList;

    6: i32 isSoldOut;

    7: string description;

    8: string picture;

    9: i32 sequence;

    13: list<HeadQuarterSkuInfoDTO> skuList;

    14: i64 tagId;

    15: string properties;

    16: string sellPoint;

    17: string commonAttrValue;

    18: string pictureContents;

    19: i32 isSpecialty;

    20: i32 poiCount;

    25: i32 auditStatus;

    26: i32 normAuditType;
}


/**
 * @TypeDoc(
 *     description = "总部商品spu查询响应"
 * )
 */
struct GetHeadQuarterSpuInfoResponse {
    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "商品列表",
     *     example = ""
     * )
     */
    2: list<HeadQuarterSpuInfoDTO> spuInfoList;

    /**
     * @FieldDoc(
     *     description = "分页信息",
     *     example = ""
     * )
     */
    3: PageInfo.PageInfo pageInfo;
}
