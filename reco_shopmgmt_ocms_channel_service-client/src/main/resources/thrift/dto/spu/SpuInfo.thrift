namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"
include "BaseRequest.thrift"
include "PageInfo.thrift"
include "SkuInfo.thrift"
include "ResultData.thrift"

/**
 * @TypeDoc(
 *     description = "商品主体信息"
 * )
 */
struct SpuEntityDTO {
   /**
     * @FieldDoc(
     *     description = "主体id"
     * )
     */
    1: string entityId;
       /**
     * @FieldDoc(
     *     description = "主体信息"
     * )
     */
    2: string entityType;

}


/**
 * @TypeDoc(
 *     description = "商品销售属性"
 * )
 */
struct SaleAttrValueDTO {
   /**
     * @FieldDoc(
     *     description = "销售属性id"
     * )
     */
    1: i64 attrId;
       /**
     * @FieldDoc(
     *     description = "销售销售属性值id"
     * )
     */
    2: i64 valueId;
       /**
     * @FieldDoc(
     *     description = "销售属性值，支持自定义"
     * )
     */
    3: string value;
    /**
     * @FieldDoc(
     *     description = "销售属性名称"
     * )
     */
    4: string attrName;
    /**
     * @FieldDoc(
     *     description = "销售属性图片"
     * )
     */
    5: list<string> pictureUrl;
}

/**
 * @TypeDoc(
 *     description = "提供给上游的渠道商品SKU信息"
 * )
 */
struct SkuInSpuInfoDTO{

    /**
     * @FieldDoc(
     *     description = "赋能商品SKU编码",
     *     example = "10"
     * )
     */
    1: string skuId;

    /**
     * @FieldDoc(
     *     description = "商家商品SKU编码",
     *     example = "10"
     * )
     */
    2: string customSkuId;

    /**
     * @FieldDoc(
     *     description = "sku的规格名称。字段信息限定长度不能超过20个字符。不允许上传emoji等表情符。",
     *     example = "大"
     * )
     */
    3: string spec;

    /**
        * @FieldDoc(
        *     description = "单位",
        *     example = "份"
        * )
        */
    4:  string unit;

    /**
        * @FieldDoc(
        *     description = "最小购买数量",
        *     example = "1"
        * )
        */
    5: i32 minPurchaseQuantity;

    //todo weight_for_unit weight_unit
    /**
     * @FieldDoc(
     *     description = "sku的重量，单位为克/g；字段信息最大允许上传200000。不能传负数或小数",
     *     example = "430"
     * )
     */
    6: optional i32 weight;

    /**
     * @FieldDoc(
     *     description = "sku的重量数值信息，最多支持两位小数",
     *     example = "1.12"
     * )
     */
    7: optional double weightForUnit;

    /**
     * @FieldDoc(
     *     description = "sku的重量数值单位，枚举值",
     *     example = "升(L)"
     * )
     */
    8: optional string weightUnit;

    /**
    * @FieldDoc(
    *     description = "sku的价格，单位是元，最多支持2位小数，不能为负数；字段信息最大允许上传30000",
    *     example = "1.5"
    * )
    */
    9: optional double price;

    /**
     * @FieldDoc(
     *     description = "sku的库存量，不能为负数或小数；传*表示设置商品库存为平台当前允许的最大值9999999",
     *     example = "99"
     * )
     */
    10: optional i32 stock;

    /**
     * @FieldDoc(
     *     description = "sku的商品包装上的条形码编号，UPC/EAN码；字符长度8位或者13位；字符内容范围是中文、字母、数字。更新时传递EMPTY_VALUE支持置空",
     *     example = "5155660008"
     * )
     */
    11: string upc;

    /**
     * @FieldDoc(
     *     description = "包装盒价格,包装费老计费规则，表示：商品sku单个打包盒的价格，单位是元，不能为负数，需在0-100之间。",
     *     example = "23.5"
     * )
     */
    12: double boxPrice;

    /**
     * @FieldDoc(
     *     description = "包装盒数量,包装费老计费规则，表示：商品sku单件需使用打包盒的数量，不能为负数或小数，需在0-100之间。",
     *     example = "1"
     * )
     */
    13: i32 boxQuantity;

    /**
 * @FieldDoc(
 *     description = "包装盒价格,包装费阶梯计价规则，表示：每M件商品收取N元包装费中的M。1.仅支持传[1,100]的整数",
 *     example = "23.5"
 * )
 */
    14: optional double ladderBoxPrice;

    /**
     * @FieldDoc(
     *     description = "包装盒数量,包装费阶梯计价规则，表示：每M件商品收取N元包装费中的N。1.仅支持传[0,2]，最多支持两位小数。",
     *     example = "1"
     * )
     */
    15: optional i32 ladderBoxQuantity;

    /**
    * @FieldDoc(
    *     description = "商品的可售时间，如未单独设置可售时间，则默认商品可售时间与门店营业时间一致。",
    *     example = "monday"
    * )
    */
    16: string availableTimes;

    /**
    * @FieldDoc(
    *     description = "渠道商品SKU编码",
    *     example = "10"
    * )
    */
    17: optional string channelSkuId;

    /**
   * @FieldDoc(
   *     description = "料位码",
   *     example = "10"
   * )
   */
    18: optional string locationCode;

    /**
    * @FieldDoc(
    *     description = "是否使用包装费阶梯计划规则，1-使用，0-不使用",
    *     example = "1"
    * )
    */
    19: optional i32 isLadderBox;

    /**
    * @FieldDoc(
    *     description = "是否自定义规格，饿了么用",
    *     example = "1"
    * )
    */
    20: optional i32 isCustomSpec;

     /**
    * @FieldDoc(
    *     description = "饿了么用 是否需要使用channelSkuId更新customSkuId,
    *     example = "1"
    * )
    */
    21: optional bool updateCumstomSkuId;

    /**
     * @FieldDoc(
     *     description = "京东更新用 规格变更类型 1 创建，2 更新，3删除
     *     example = "1"
     * )
     */
    22: optional i32 changeType;

    /**
     * @FieldDoc(
     *     description = "商品销售属性列表（美团外卖用）"
     * )
     */
     23: optional list<SaleAttrValueDTO> saleAttrValueList;

    /**
     * @FieldDoc(
     *     description = "商品池维度的渠道商品SkU编码",
     *     example = "10"
     * )
     */
     24: optional string merchantChannelSkuId;

    /**
    * @FieldDoc(
    *     description = "渠道类目的来源，用于区分是医药类目还是零售类目",
    * )
    */
    25: optional i32 channelCategorySourceType;

    /**
    *  @FieldDoc(
    * description = "规格图片链接（美团用）"
    * )
    */ 26: optional string skuPicture;


    /**
      *@FieldDoc(
      *   description = "主档建议零售价",
      *)
     */
    27: optional i64 merchantSuggestPrice;

    /**
      *@FieldDoc(
      *   description = "规格类目属性",
      *)
     */
    28: optional string skuAttrValues;


}

/**
 * @TypeDoc(
 *     description = "提供给上游的渠道商品SKU重量信息"
 * )
 */
struct SkuWeightInfoDto{

    /**
     * @FieldDoc(
     *     description = "赋能商品SKU编码",
     *     example = "10"
     * )
     */
    1: string skuId;

    /**
     * @FieldDoc(
     *     description = "商家商品SKU编码",
     *     example = "10"
     * )
     */
    2: string customSkuId;

    /**
     * @FieldDoc(
     *     description = "带单位的重量；字段信息最大允许上传200000。可以传小数",
     *     example = "430"
     * )
     */
    3: string weightForUnit;

    /**
     * @FieldDoc(
     *     description = "重量单位",
     *     example = "千克(kg)"
     * )
     */
    4: string weightUnit;

    /**
     * @FieldDoc(
     *     description = "sku的重量，单位为克/g；字段信息最大允许上传200000。不能传负数或小数；此字段不起作用，通过weightForUnit和weightUnit更新渠道商品重量",
     *     example = "430"
     * )
     */
    5: optional i32 weight;

}

struct SpuWeightInfoDto{

    /**
     * @FieldDoc(
     *     description = "赋能商品SpU编码",
     *     example = "10"
     * )
     */
    1: string spuId;

    /**
     * @FieldDoc(
     *     description = "商家商品SpU编码",
     *     example = "10"
     * )
     */
    2: string customSpuId;

    /**
    * @FieldDoc(
    *     description = "商品sku信息的json格式数",
    *     example = "json数据"
    * )
    */
    3: list<SkuWeightInfoDto> skuWeightInfoDtoList;
}

struct SpuWeightInfoRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequest baseInfo;

    /**
    * @FieldDoc(
    *     description = "商品sku信息的json格式数",
    *     example = "json数据"
    * )
    */
    2: list<SpuWeightInfoDto> spuWeightInfoDtoList;
}


/**
 * @TypeDoc(
 *     description = "商品限购详情"
 * )
 */
struct LimitSaleInfoDTO{
    /**
    * @FieldDoc(
    *     description = "是否限制购买数量：(1)字段取值范围：1.true-开启；false-关闭。(2)如开启限制，则对SPU下所有SKU生效，每个SKU均按设置规则执行限购。(3)如入参选择“不限制”，则后续参数入参也无效。(4)如入参选择“限制”，则开启单个买家限购功能",
    *     example = "1"
    * )
    */
    1: bool limitSale;
    /**
    * @FieldDoc(
    *     description = "限购规则",
    *     example = "1"
    * )
    */
    2: i32 type;
    /**
    * @FieldDoc(
    *     description = "限购循环天数",
    *     example = "1"
    * )
    */
    3: i32 frequency;
    /**
    * @FieldDoc(
    *     description = "限购开始日期",
    *     example = "1"
    * )
    */
    4: string beginTime;
    /**
    * @FieldDoc(
    *     description = "限购结束日期：结束日期不得早于开始日期，且与开始日期相距不得超出90天",
    *     example = "1"
    * )
    */
    5: string endTime;
    /**
    * @FieldDoc(
    *     description = "限购数量：(1)字段范围：整数，最小为1。(2)如入参为小数，则向下取整（例如：入参数量为2.8，最终取2）",
    *     example = "1"
    * )
    */
    6: i32 count;
}

/**
 * @TypeDoc(
 *     description = "无货取消订单触发库存清零相关配置"
 * )
 */
struct StockConfigDTO{
    /**
    * @FieldDoc(
    *     description = "是否开启“无货取消订单触发库存清零”配置的总开关",
    *     example = "true"
    * )
    */
    1: bool reset;
    /**
    * @FieldDoc(
    *     description = "触发库存清零的取消订单方式：1-门店因无货取消订单；2-买家因无货取消订单。",
    *     example = "1"
    * )
    */
    2: i32 type;
    /**
    * @FieldDoc(
    *     description = "触发库存清零后是否禁止开放平台接口更新库存：true-是，禁止更新库存；false-否，允许更新库存。",
    *     example = "1"
    * )
    */
    3: bool limit_open_sync_stock;
    /**
   * @FieldDoc(
   *     description = "商品触发库存清零后，且limit_open_sync_stock为true的情况下，允许开放平台接口更新库存的时间点",
   *     example = "1"
   * )
   */
    4: i32 schedule;
    /**
    * @FieldDoc(
    *     description = "是否允许次日自动补充库存：true-是，自动补充库存；false-否，不会自动补充库存",
    *     example = "1"
    * )
    */
    5: bool sync_next_day;
    /**
    * @FieldDoc(
    *     description = "限购结束日期：结束日期不得早于开始日期，且与开始日期相距不得超出90天",
    *     example = "1"
    * )
    */
    6: string sync_count;
}

/**
 * @TypeDoc(
 *     description = "普通属性值列表"
 * )
 */
struct ValueListDTO{

    /**
    * @FieldDoc(
    *     description = "普通属性值Id",
    *     example = "25"
    * )
    */
    1: i64 valueId;

    /**
    * @FieldDoc(
    *     description = "普通属性值名称",
    *     example = "老人"
    * )
    */
    2: string value;
}

/**
 * @TypeDoc(
 *     description = "商品普通属性"
 * )
 */
struct CommonAttributeDTO{

    /**
    * @FieldDoc(
    *     description = "是普通属性Id",
    *     example = "true"
    * )
    */
    1: i64 attrId;

    /**
    * @FieldDoc(
    *     description = "普通属性名称",
    *     example = "1"
    * )
    */
    2: string attrName;

    /**
    * @FieldDoc(
    *     description = "普通属性值列表",
    *     example = "1"
    * )
    */
    3: optional list<ValueListDTO> valueList;
}

/**
 * @TypeDoc(
 *     description = "商品普通属性"
 * )
 */
struct OpenSaleAttributeDTO{

    /**
    * @FieldDoc(
    *     description = "销售属性id",
    *     example = "true"
    * )
    */
    1: i64 attrId;

    /**
    * @FieldDoc(
    *     description = "销售属性值id",
    *     example = "1"
    * )
    */
    2: i64 valueId;

    /**
    * @FieldDoc(
    *     description = "销售属性值",
    *     example = "1"
    * )
    */
    3: string value;
}

struct ChannelMedicalDeviceQuaInfoDTO {

    /**
    * @FieldDoc(
    *     description = "医疗器械资质图",
    *     example = "[http://p0.meituan.net/xianfu/63d5cb4cf120c89da74c1dd3e7c8bae651375.jpg, A71E7F8D324B269F6557EE24799335A9]"
    * )
    */
    1: list<string> quaPictures;

    /**
    * @FieldDoc(
    *     description = "批准日期",
    *     example = "2024-01-01"
    * )
    */
    2: string quaApprovalDate;

    /**
    * @FieldDoc(
    *     description = "有效期至",
    *     example = "2025-01-01"
    * )
    */
    3: string quaEffectiveDate;
}

struct ProductSpecialPicture {

    /**
     * @FieldDoc(
     *     description = "特殊图类型",
     *     example = ""
     * )
     */
    1: i32 specialPictureType;
    /**
     * @FieldDoc(
     *     description = "标题",
     *     example = ""
     * )
     */
    2: string specialPictureTitle;

    3: list<string> specialPictures;
}
/**
 * @TypeDoc(
 *     description = "提供给上游的渠道商品SPU信息"
 * )
 */
struct SpuInfoDTO{

    /**
     * @FieldDoc(
     *     description = "赋能商品SPU编码",
     *     example = "10"
     * )
     */
    1: string spuId;

    /**
 * @FieldDoc(
 *     description = "商家商品SPU编码：(1) 不同门店之间商品id可以重复，同一门店内商品id不允许重复；(2)字段信息限定长度不超过128个字符",
 *     example = "10"
 * )
 */
    2: string customSpuId;

    /**
     * @FieldDoc(
     *     description = "商品名称：(1)此字段信息限定长度不超过30个字符；(2)不允许上传emoji等表情符；(3)在门店的同一分类下商品名称不允许重复",
     *     example = "安慕希希腊风味常温酸奶原味"
     * )
     */
    3: string name;

    /**
    * @FieldDoc(
    *     description = "商品描述，此字段信息建议长度不超过150个字符。不允许传入html类信息。更新时传递EMPTY_VALUE支持置空。",
    *     example = ""
    * )
    */
    4: string description;

    /**
     * @FieldDoc(
     *     description = "主图",
     *     example = "[2B86BE5A5C23786232DE37A4717E0D93]"
     * )
     */
    5: list<string> pictures;

    /**
     * @FieldDoc(
     *     description = "渠道类目编码",
     *     example = "99"
     * )
     */
    6: string channelCategoryId;

    /**
     * @FieldDoc(
     *     description = "商品的品牌名称：(1)字段信息限定长度不超过30个字符；(2)不允许上传emoji等表情符",
     *     example = "飞利浦"
     * )
     */
    7: string brandName;

    /**
     * @FieldDoc(
     *     description = "商品的产地：(1)字段信息限定长度不超过30个字符；(2)不允许上传emoji等表情符，仅支持中文、字母",
     *     example = "北京"
     * )
     */
    8: string productionArea;
//
//    /**
//         * @FieldDoc(
//         *     description = "最小购买数量",
//         *     example = "1"
//         * )
//         */
//    9: double minPurchaseQuantity;
//
//    /**
//        * @FieldDoc(
//        *     description = "单位",
//        *     example = "份"
//        * )
//        */
//    10:  string unit;

    /**
     * @FieldDoc(
     *     description = "商品上下架状态（1-上架；2-下架）",
     *     example = "1"
     * )
     */
    11: i32 status;

    /**
     * @FieldDoc(
     *     description = "商品创建来源",
     *     example = "1"
     * )
     */
    12: optional i32 sourceType;

    /**
         * @FieldDoc(
         *     description = "当前分类下的排序序号",
         *     example = "1"
         * )
         */
    13: i32 sequence;

    /**
     * @FieldDoc(
     *     description = "是否是标品(0:非标品,1:是标品)，默认是标品因为thrift默认值是0会导致标品被改成非标品",
     *     example = "1"
     * )
     */
    14: i32 isSp=1;

    /**
    * @FieldDoc(
    *     description = "分类id列表，用于同步商品多个分类信息。",
    *     example = "1"
    * )
    */
    15: list<string> frontCategoryCodes;

    /**
    * @FieldDoc(
    *     description = "是否为“力荐”商品，字段取值范围：0-否， 1-是。",
    *     example = "1"
    * )
    */
    16: i32 isSpecialty;

    /**
    * @FieldDoc(
    *     description = "商品属性：(1)字段信息为json格式数组，最多支持上传10组属性。(2)若填写该商品属性，则property_name和values必填。",
    *     example = ""
    * )
    */
    17: string properties;

    /**
        * @FieldDoc(
        *     description = "渠道商品SPU编码",
        *     example = "10"
        * )
        */
    18: optional string channelSpuId;

    /**
        * @FieldDoc(
        *     description = "商品限购详情",
        *     example = "limitSale:true,type:1,begin:20200216,count:10.6,end:20200221"
        * )
        */
    19: optional LimitSaleInfoDTO limitSaleInfo;

    /**
            * @FieldDoc(
            *     description = "无货取消订单触发库存清零相关配置",
            *     example = ""
            * )
            */
    20: optional StockConfigDTO stockConfig;

    /**
        * @FieldDoc(
        *     description = "视频ID",
        *     example = "3241324"
        * )
        */
    21: i64 videoId;

    /**
    * @FieldDoc(
    *     description = "视频URL，MP4格式",
    *     example = "http://123.123.123"
    * )
    */
    22: string videoUrlMp4;

    /**
    * @FieldDoc(
    *     description = "商品的补充标题",
    *     example = "安慕希希腊风味常温酸奶原味"
    * )
    */
    23: string flavour;

    /**
    * @FieldDoc(
    *     description = "商品普通属性",
    *     example = "商品普通属性"
    * )
    */
    24: optional CommonAttributeDTO commonAttribue;

    /**
    * @FieldDoc(
    *     description = "商品sku信息的json格式数",
    *     example = "json数据"
    * )
    */
    25: list<SkuInSpuInfoDTO> skus;

    /**
* @FieldDoc(
*     description = "标品名",
*     example = "标品名"
* )
*/
    26: string productName;

    /**
       * @FieldDoc(
       *     description = "渠道店内分类列表，一级分类 + 二级分类 1、用于回调新增商品处理店内分类 2、用于单个、批量商品查询返回店内分类（elm、jddj渠道的数据在ChannelStoreCategory一级分类里边，美团ChannelStoreCategory一级、二级分类有可能都有值）",
       *     example = "渠道店内分类列表"
       * )
       */
    27: list<SkuInfo.ChannelStoreCategory> categoryList;

    /**
       * @FieldDoc(
       *     description = "历史单分类结构：此字段不设置值；多分类：渠道店内末级分类列表,用于新增、修改商品绑定多分类；为了兼容单、多分类，单分类作为多分类的一种情况处理，所以单分类的时候，这个列表的size=1",
       *     example = ""
       * )
       */
    28: list<SkuInfo.ChannelLeafStoreCategory> leafStoreCategoryList;

    /**
         * @FieldDoc(
         *     description = "渠道末级店内分类编码，历史单分类：分类编码；多分类：此字段不设置值",
         *     example = "1"
         * )
         */
    29: string leafStoreCategoryCode;

    /**
    * @FieldDoc(
    *     description = "审核状态",
    *     example = "默认0，0-未送审"
    * )
    */
    30: i32 auditStatus;

    /**
    * @FieldDoc(
    *     description = "信息是否完整",
    *     example = "0，默认完整"
    * )
    */
    31: i32 isComplete;

    /**
    * @FieldDoc(
    *     description = "商品卖点",
    *     example = "健康低脂"
    * )
    */
    32: string sellPoint;

    /**
    * @FieldDoc(
    *     description = "图片详情",
    *     example = "[http://p0.meituan.net/xianfu/63d5cb4cf120c89da74c1dd3e7c8bae651375.jpg, A71E7F8D324B269F6557EE24799335A9]"
    * )
    */
    33: list<string> pictureContents;

    /**
    * @FieldDoc(
    *     description = "商品普通属性列表，支持多个商品属性",
    *     example = "商品普通属性"
    * )
    */
    34: optional string commonAttributes;

    /**
        * @FieldDoc(
        *     description = "资质缺失原因",
        *     example = "70000缺少售卖资质 ，表示需要上传相关资质，80000超出经营范围"
        * )
        */
    35: list<i32> indicatorItems;
    /**
       * @FieldDoc(
       *     description = "规格类型(1单规格2多规格)，饿了么渠道使用",
       *     example = "1"
       * )
       */
    36: optional i32 specType;

    /**
    * @FieldDoc(
    *     description = "视频封面链接",
    *     example = "http://picture.meituan.com/1234.jpg"
    * )
    */
    37: optional string videoCoverUrl;
    /**
    * @FieldDoc(
    *     description = "合规审核类型",
    *     example = "信息合规审核方式 1-先审后发 2-先发后审 3-不送审"
    * )
    */
    38: i32 normAuditType;

    /**
    * @FieldDoc(
    *     description = "合规审核状态",
    *     example = "信息合规审核状态 1-审核中 2-审核通过 3-审核驳回"
    * )
    */
    39: i32 normAuditStatus;
    /**
    * @FieldDoc(
    *     description = "选填，是否单点不送，0-否，1-是"
    * )
    */
    40: i32 forbidSingleOrder;

    /**
        * @FieldDoc(
        *     description = "商品池维度的渠道商品SPU编码，有赞区分商品池维度和门店维度",
        *     example = "10"
        * )
        */
    41: optional string merchantChannelSpuId;

    /**
    * @FieldDoc(
    *     description = "商品是否处于活动中，饿了么渠道会返回此字段",
    * )
    */
    42: optional bool inActivity;

    43: optional string merchantCustomSpuId;

    /**
    * @FieldDoc(
    *     description = "配送要求",
    * )
    */
    44: optional string deliveryRequirement;
    /**
    * @FieldDoc(
    *     description = "线下类目渠道编码",
    *     example = "10"
    * )
    */
    45: optional string offlineChannelCategoryCode;

    /**
    * @FieldDoc(
    *     description = "渠道商品医疗器械资质信息",
    *     example = {}
    * )
    */
    46: optional ChannelMedicalDeviceQuaInfoDTO medicalDeviceQuaInfo;


    /**
    * @FieldDoc(
    *     description = "是否为“组合”商品。",
    *     example = "1"
    * )
    */
    47: optional bool combination;

    /**
    * @FieldDoc(
    *     description = "类目属性是否使用推荐回填"
    * )
    */
    48: optional bool recommendCategoryProperty;

    /**
    * @FieldDoc(
    *     description = "淘鲜达不强制覆盖upc, true则不覆盖，false则覆盖"
    * )
    */
    49: optional bool unCoverTxdUpc;

    /**
    * @FieldDoc(
    *     description = "商品发布规则",
    *     example = "json"
    * )
    */
    50: optional string productRules;
    /**
    * @FieldDoc(
    *     description = "SPUID（到家多规格商品）",
    *     example = "json"
    * )
    */
    51: optional string superId;
    /**
     * @FieldDoc(
     *     description = "品牌id",
     *     example = "99"
     * )
     */
    52: optional string brandId;
    /**
     * @FieldDoc(
     *     description = "商家商品上下架状态(1:上架;2:下架;4:删除;)",
     *     example = "1"
     * )
     */
    53: optional i32 fixedStatus;
    /**
     * @FieldDoc(
     *     description = "商品主体信息",
     *     example = "{}"
     * )
     */
    54: optional SpuEntityDTO entityInfo;

    /**
     * @FieldDoc(
     *     description = "资质图",
     *     example = "[2B86BE5A5C23786232DE37A4717E0D93]"
     * )
     */
    55: optional list<string> qualificationPictures;

    /**
      * @FieldDoc(
      *     description = "商品名称补充语",
      *     example = ""
      * )
    */
    56: string nameSupplement;

    /**
     * @FieldDoc(
     *     description = "商品名称补充语顺序。0：补充语后置；1：补充语前置。（适用于医药健康门店商品）",
     *     example = "1"
     * )
     */
    57: i32 nameSupplementSeq;

     /**
         * @FieldDoc(
         *     description = "基础名称（无补充语）",
         *     example = "电灯"
         * )
         */
     58: string baseName;

    /**
     * @FieldDoc(
     *     description = "特殊图",
     *     example = ""
     * )
     */
    59: optional list<ProductSpecialPicture> specialPictureInfos;
}

/**
 * @TypeDoc(
 *     description = "提供给上游的渠道商品店内分类信息"
 * )
 */
struct SpuStoreCategoryCodeDTO{

        /**
         * @FieldDoc(
         *     description = "赋能商品SPU编码",
         *     example = "10"
         * )
         */
        1: string spuId;

        /**
     * @FieldDoc(
     *     description = "商家商品SPU编码：(1) 不同门店之间商品id可以重复，同一门店内商品id不允许重复；(2)字段信息限定长度不超过128个字符",
     *     example = "10"
     * )
     */
        2: string customSpuId;

        /**
           * @FieldDoc(
           *     description = "历史单分类结构：此字段不设置值；多分类：渠道店内末级分类列表,用于新增、修改商品绑定多分类；为了兼容单、多分类，单分类作为多分类的一种情况处理，所以单分类的时候，这个列表的size=1",
           *     example = ""
           * )
           */
        3: list<string> storeCategoryCodeList;
}

/**
 * @TypeDoc(
 *     description = "商品SKU信息请求参数"
 * )
 */
struct SpuInfoRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequest baseInfo;

    /**
     * @FieldDoc(
     *     description = "商品Sku信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<SpuInfoDTO> paramList;

     /**
     * @FieldDoc(
     *     description = "是否使用渠道id操作商品，默认false, 当前仅支持elm渠道updateBySpuOrUpc接口",
     *     example = "false"
     *
     * )
     */
    3: optional bool optByChannelSpuId;
    /**
    * @FieldDoc(
    *     description = "1-优先保留活动，商品更新失败；2-商品可更新成功，平台补贴活动会下线。不传参默认是1。"
    * )
    */
    4: i32 actCheckType;
    /**
    * @FieldDoc(
    *     description = "是否移除活动品更新受限字段"
    * )
    */
    5: optional bool removeLimitedField;

    /**
    * @FieldDoc(
    *     description = "京东是否开启门店自定义分类"
    * )
    */
    6: optional bool isJddStoreManageCategory;
}

/**
 * @TypeDoc(
 *     description = "商品SPU信息请求参数"
 * )
 */
struct SingleSpuInfoRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequest baseInfo;

    /**
     * @FieldDoc(
     *     description = "商品Spu信息",
     *     example = "1"
     *
     * )
     */
    2: SpuInfoDTO param;

     /**
     * @FieldDoc(
     *     description = "是否使用渠道id操作商品，默认false, 当前仅支持elm渠道updateBySpuOrUpc接口",
     *     example = "false"
     *
     * )
     */
    3: optional bool optByChannelSpuId;

    /**
         * @FieldDoc(
         *     description = "是否同步当前spu下所有sku的价格和库存,默认否(只会同步新增sku的价格和库存)",
         *     example = "false"
         *
         * )
     */
    4: optional bool syncAllSkuPriceAndStock;
}


struct ChannelSpuDistributeInfoRequest{

    1: BaseRequest.BaseRequestSimple baseInfo;

    2: ResultData.SpuKey spuKey;

    3: list<i64> storeIds;
}


/**
 * @TypeDoc(
 *     description = "商品店内分类信息请求参数"
 * )
 */
struct SpuStoreCategoryInfoRequest{

        /**
         * @FieldDoc(
         *     description = "租户渠道Id",
         *     example = "1"
         * )
         */
        1: BaseRequest.BaseRequest baseInfo;

        /**
         * @FieldDoc(
         *     description = "商品Sku信息列表",
         *     example = "1"
         *
         * )
         */
        2: list<SpuStoreCategoryCodeDTO> paramList;


}

/**
 * @TypeDoc(
 *     description = "商品 upc 的修改信息"
 * )
 */
struct SpuUpcUpdateDto{

        /**
         * @FieldDoc(
         *     description = "商家商品SPU编码",
         *     example = "10"
         * )
         */
        1: string customSpuId;

        /**
         * @FieldDoc(
         *     description = "upc 操作类型，2-删除对应条码，3-更换主条码",
         *     example = "3"
         * )
         */
        2: i32 type;

        /**
           * @FieldDoc(
           *     description = "条码列表",
           *     example = ""
           * )
           */
        3: list<string> upcList;
}

/**
 * @TypeDoc(
 *     description = "修改一个门店下商品 upc 的请求参数"
 * )
 */
struct SpuUpcUpdateRequest{

        /**
         * @FieldDoc(
         *     description = "租户渠道Id",
         *     example = ""
         * )
         */
        1: BaseRequest.BaseRequest baseInfo;

        /**
         * @FieldDoc(
         *     description = "商品 upc 修改列表",
         *     example = ""
         *
         * )
         */
        2: list<SpuUpcUpdateDto> paramList;
}

/**
 * @TypeDoc(
 *     description = "批量删除商品SPU信息"
 * )
 */
struct SpuInfoDeleteDTO{

    /**
 * @FieldDoc(
 *     description = "门店id",
 *     example = ""
 * )
 */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商家SPU编码",
     *     example = "10"
     * )
     */
    2: string customSpuId;


    3: ResultData.SpuKey spuKey;

    /**
     * @FieldDoc(
     *     description = "渠道SPU编码",
     *     example = "10"
     * )
     */
    4: optional string channelSpuId;

    /**
     * @FieldDoc(
     *     description = "渠道总部SPU编码",
     *     example = "10"
     * )
     */
    5: optional string merchantChannelSpuId;


    /**
    *  是否主档商品停售
    **/
    6: optional bool merchantSpuOffline;
}

/**
 * @TypeDoc(
 *     description = "根据ChannelSpuId批量删除商品SPU信息参数"
 * )
 */
struct SpuInfoDeleteByChannelSpuIdDTO{
    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    1: i64 storeId;
    /**
     * @FieldDoc(
     *     description = "渠道商品SpuId",
     *     example = ""
     * )
     */
    2: string channelSpuId;
}

/**
 * @TypeDoc(
 *     description = "根据ChannelSpuId批量删除商品Spu信息请求参数"
 * )
 */
struct SpuInfoDeleteByChannelSpuIdRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = ""
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "根据ChannelSpuId批量删除商品SPU信息参数",
     *     example = "1"
     *
     * )
     */
    2: list<SpuInfoDeleteByChannelSpuIdDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "批量修改商品SPU上下架状态"
 * )
 */
struct SpuInfoSellStatusDTO{

    /**
 * @FieldDoc(
 *     description = "门店id",
 *     example = ""
 * )
 */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商家SPU编码",
     *     example = "10"
     * )
     */
    2: list<string> customSpuIds;

    /**
     * @FieldDoc(
     *     description = "商品上下架状态（1-上架；2-下架）",
     *     example = "1"
     * )
     */
    3: i32 spuStatus;


    4: list<ResultData.SpuKey> spuKeys;

    /**
     * @FieldDoc(
     *     description = "渠道SPU编码",
     *     example = "10"
     * )
     */
    5: optional list<string> channelSpuIds;

}

/**
 * @TypeDoc(
 *     description = "批量修改商品SPU上下架状态"
 * )
 */
struct SingleSpuSellStatusDTO{

    /**
 * @FieldDoc(
 *     description = "门店id",
 *     example = ""
 * )
 */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商家SPU编码",
     *     example = "10"
     * )
     */
    2: optional string customSpuId;

    /**
     * @FieldDoc(
     *     description = "商品上下架状态（1-上架；2-下架）",
     *     example = "1"
     * )
     */
    3: i32 spuStatus;


    4: optional ResultData.SpuKey spuKey;

    /**
     * @FieldDoc(
     *     description = "渠道SPU编码",
     *     example = "10"
     * )
     */
    5: optional string channelSpuId;

    /**
     * @FieldDoc(
     *     description = "渠道总部SPU编码",
     *     example = "10"
     * )
     */
    6: optional string merchantChannelSpuId;
}

/**
 * @TypeDoc(
 *     description = "批量删除商品SKU信息"
 * )
 */
struct SkuInSpuInfoDeleteDTO{

    /**
* @FieldDoc(
*     description = "门店id",
*     example = ""
* )
*/
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商家SPU编码",
     *     example = "10"
     * )
     */
    2: string customSpuId;

    /**
     * @FieldDoc(
     *     description = "商家SKU编码",
     *     example = "10"
     * )
     */
    3: string customSkuId;
}

/**
 * @TypeDoc(
 *     description = "批量删除商品SPU信息请求参数"
 * )
 */
struct SpuInfoDeleteRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = ""
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "图片上传信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<SpuInfoDeleteDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "单个删除商品SPU信息请求参数"
 * )
 */
struct SingleSpuInfoDeleteRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = ""
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "商品Spu信息",
     *     example = "1"
     *
     * )
     */
    2: SpuInfoDeleteDTO param;

    /**
     * @FieldDoc(
     *     description = "强制删除",
     *     example = "true"
     * )
     */
    3: optional bool forceDelete;
}

/**
 * @TypeDoc(
 *     description = "批量删除商品SKU信息请求参数"
 * )
 */
struct SkuInSpuInfoDeleteRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = ""
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "图片上传信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<SkuInSpuInfoDeleteDTO> paramList;
    /**
     * @FieldDoc(
     *     description = "1-优先保留活动，商品更新失败；2-商品可更新成功，平台补贴活动会下线。不传参默认是1。"
     * )
     */
    3: i32 actCheckType;
}

/**
 * @TypeDoc(
 *     description = "单个删除商品SKU信息请求参数"
 * )
 */
struct SingleSkuInSpuInfoDeleteRequest{

        /**
         * @FieldDoc(
         *     description = "租户渠道Id",
         *     example = ""
         * )
         */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
         * @FieldDoc(
         *     description = "图片上传信息列表",
         *     example = "1"
         *
         * )
         */
        2: SkuInSpuInfoDeleteDTO param;
}

/**
 * @TypeDoc(
 *     description = "商品上架/下架信息请求参数"
 * )
 */
struct SpuSellStatusInfoRequest {

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = ""
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "商家SPU编码",
     *     example = "10"
     * )
     */
    2: list<SpuInfoSellStatusDTO> paramList;

     /**
     * @FieldDoc(
     *     description = "是否使用渠道id操作商品，默认false",
     *     example = "false"
     *
     * )
     */
    3: optional bool optByChannelSpuId;
}

/**
 * @TypeDoc(
 *     description = "商品上架/下架信息请求参数"
 * )
 */
struct SingleSpuSellStatusRequest {

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = ""
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "商家SPU编码",
     *     example = "10"
     * )
     */
    2: SingleSpuSellStatusDTO param;

     /**
     * @FieldDoc(
     *     description = "是否使用渠道id操作商品，默认false",
     *     example = "false"
     *
     * )
     */
    3: optional bool optByChannelSpuId;
}

/**
 * @TypeDoc(
 *     description = "批量修改商品SPU上下架状态（基于渠道商品编码）"
 * )
 */
struct SpuInfoSellStatusByChannelSpuIdDTO{

        /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
        1: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "渠道SPU编码",
         *     example = "10"
         * )
         */
        2: list<string> channelSpuIds;

        /**
         * @FieldDoc(
         *     description = "商品上下架状态（1-上架；2-下架）",
         *     example = "1"
         * )
         */
        3: i32 spuStatus;
}

/**
* @TypeDoc(
*     description = "批量修改商品SPU上下架状态（基于渠道商品编码）"
* )
*/
struct SpuInfoSellStatusByChannelSkuIdDTO{

        /**
        * @FieldDoc(
        *     description = "渠道SPU编码",
        *     example = "10"
        * )
        */
        1: string channelSkuId;

        /**
        * @FieldDoc(
        *     description = "商品上下架状态（1-上架；2-下架）",
        *     example = "1"
        * )
        */
        2: i32 saleStatus;
}

/**
 * @TypeDoc(
 *     description = "商品上架/下架信息请求参数（基于渠道商品编码）"
 * )
 */
struct SpuSellStatusInfoByChannelSpuIdRequest {

        /**
         * @FieldDoc(
         *     description = "租户渠道Id",
         *     example = ""
         * )
         */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
         * @FieldDoc(
         *     description = "渠道SPU编码",
         *     example = "10"
         * )
         */
        2: list<SpuInfoSellStatusByChannelSpuIdDTO> paramList;
}

/**
* @TypeDoc(
*     description = "商品上架/下架信息请求参数（基于渠道商品编码）"
* )
*/
struct SpuSellStatusInfoByChannelSkuIdRequest {
        /**
        * @FieldDoc(
        *     description = "租户渠道Id",
        *     example = ""
        * )
        */
        1: BaseRequest.BaseRequestSimple baseInfo;
        /**
        * @FieldDoc(
        *     description = "门店id",
        *     example = ""
        * )
        */
        2: i64 storeId;
        /**
        * @FieldDoc(
        *     description = "渠道SKU编码",
        *     example = "10"
        * )
        */
        3: list<SpuInfoSellStatusByChannelSkuIdDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "更新商品编码信息"
 * )
 */
struct UpdateCustomSpuIdByOriginIdDTO {

    /**
* @FieldDoc(
*     description = "门店id",
*     example = ""
* )
*/
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商家SPU编码",
     *     example = "10"
     * )
     */
    2: string customSpuIdOrigin;

    /**
     * @FieldDoc(
     *     description = "商家SPU编码",
     *     example = "10"
     * )
     */
    3: string customSpuIdCurrent;

    /**
    * @FieldDoc(
    *     description = "商家SKU编码",
    *     example = "10"
    * )
    */
    4: string customSkuIdOrigin;

    /**
     * @FieldDoc(
     *     description = "商家SKU编码",
     *     example = "10"
     * )
     */
    5: string customSkuIdCurrent;
    /**
         * @FieldDoc(
         *     description = "渠道SPU编码",
         *     example = "10"
         * )
         */
    6: optional string channelSpuId;
    /**
         * @FieldDoc(
         *     description = "渠道SKU编码",
         *     example = "10"
         * )
         */
    7: optional string channelSkuId;

    /**
     * @FieldDoc(
     *     description = "是否使用美团商品id",
     *     example = "true"
     * )
     */
    8: optional bool useMtProductId;
}

/**
 * @TypeDoc(
 *     description = "根据名称规格更新商品编码"
 * )
 */
struct UpdateCustomSpuIdByNameAndSpecDTO {

    /**
* @FieldDoc(
*     description = "门店id",
*     example = ""
* )
*/
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商品编码",
     *     example = "1"
     * )
     */
    2: string name;

    /**
    * @FieldDoc(
    *     description = "商品分类编码",
    *     example = "1"
    * )
    */
    3: string categoryCode;

    /**
     * @FieldDoc(
     *     description = "商品规格",
     *     example = "1"
     * )
     */
    4: string spec;

    /**
    * @FieldDoc(
    *     description = "商家SPU编码",
    *     example = "10"
    * )
    */
    5: string customSpuId;

    /**
     * @FieldDoc(
     *     description = "商家SKU编码",
     *     example = "10"
     * )
     */
    6: string customSkuId;

    /**
    * 店内分类名称
**/
    7: string categoryName;

}

/**
 * @TypeDoc(
 *     description = "更换新商品编码请求参数"
 * )
 */
struct UpdateCustomSpuIdByOriginIdRequest {

    /**
    * @FieldDoc(
    *     description = "更新渠道customSkuId",
    *     example = {}
    * )
    */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
    * @FieldDoc(
    *     description = "更新数据信息",
    *     example = "UpdateCustomSkuIdDTO"
    * )
    */
    2: list<UpdateCustomSpuIdByOriginIdDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "根据名称规格更换新商品编码请求参数"
 * )
 */
struct UpdateCustomSpuIdByNameAndSpecRequest {

    /**
    * @FieldDoc(
    *     description = "更新渠道customSkuId",
    *     example = {}
    * )
    */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
    * @FieldDoc(
    *     description = "更新数据信息",
    *     example = "UpdateCustomSkuIdDTO"
    * )
    */
    2: list<UpdateCustomSpuIdByNameAndSpecDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "获取单个商品请求参数"
 * )
 */
struct GetSpuInfoRequest {
    /**
         * @FieldDoc(
         *     description = "请求基本信息",
         *     example = {}
         * )
         */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
    * @FieldDoc(
    *     description = "门店id",
    *     example = ""
    * )
    */
    2: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商家自定义SPU编码",
     *     example = "10"
     * )
     */
    3: string customSpuId;

    /**
     * @FieldDoc(
     *     description = "商家spu编码",
     *     example = "10"
     * )
     */
    4: string spuId;

    /**
     * @FieldDoc(
     *     description = "upc（当前仅支持饿了么渠道）",
     *     example = "10"
     * )
     */
    5: string upc;

    /**
     * @FieldDoc(
     *     description = "渠道商品编码（当前仅支持饿了么渠道）",
     *     example = "10"
     * )
     */
    6: optional string channelSpuId;

    /**
     * @FieldDoc(
     *     description = "规格类型（1 单规格 2多规格）",
     *     example = "1"
     * )
     */
    7: optional i32 specType;
    /**
     * @FieldDoc(
     *     description = "规格信息（京东渠道使用）",
     *     example = "1"
     * )
     */
    8: optional list<ResultData.SkuKey> skuList;

    /**
     * @FieldDoc(
     *     description = "商家自定义sku编码（京东渠道使用）",
     *     example = "1"
     * )
     */
    9: optional string customSkuId;
}

/**
 * @TypeDoc(
 *     description = "获取单个商品信息返回信息"
 * )
 */
struct GetSpuInfoResponse {
    /**
 * @FieldDoc(
 *     description = "结果状态",
 *     example = ""
 * )
 */
    1: ResultStatus.ResultStatus status;

    /**
 * @FieldDoc(
 *     description = "商品Sku信息列表",
 *     example = "1"
 *
 * )
 */
    2: SpuInfoDTO spuInfo;
}

/**
 * @TypeDoc(
 *     description = "获取多个商品请求参数"
 * )
 */
struct GetSpuInfosRequest {
        /**
         * @FieldDoc(
         *     description = "请求基本信息",
         *     example = {}
         * )
         */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
        * @FieldDoc(
        *     description = "门店id",
        *     example = ""
        * )
        */
        2: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "商家SPU编码集合(最大100个)",
         *     example = "10"
         * )
         */
        3: list<string> customSpuIds;
}

/**
 * @TypeDoc(
 *     description = "获取店铺内分类下商品"
 * )
 */
struct GetSpuInfosByCategoryRequest {
        /**
         * @FieldDoc(
         *     description = "请求基本信息",
         *     example = {}
         * )
         */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
        * @FieldDoc(
        *     description = "门店id",
        *     example = ""
        * )
        */
        2: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "渠道店内分类id",
         *     example = "10"
         * )
         */
        3: string channelCategoryId;


        /**
         * @FieldDoc(
         *     description = "页码",
         *     example = ""
         * )
         */
        4: i32 pageNum = 1;

        /**
         * @FieldDoc(
         *     description = "每页大小",
         *     example = ""
         * )
         */
        5: i32 pageSize = 20;
}

/**
 * @TypeDoc(
 *     description = "多个商品返回信息"
 * )
 */
struct GetSpuInfosResponse {
        /**
         * @FieldDoc(
         *     description = "结果状态",
         *     example = ""
         * )
         */
        1: ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "商品Sku信息列表",
         *     example = "1"
         *
         * )
         */
        2: list<SpuInfoDTO> spuInfos;
}

/**
 * @TypeDoc(
 *     description = "批量拉取商品请求参数"
 * )
 */
struct BatchGetSpuInfoRequest {
    /**
         * @FieldDoc(
         *     description = "请求基本信息",
         *     example = {}
         * )
         */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    2: i64 storeId;

        /**
     * @FieldDoc(
     *     description = "页码",
     *     example = ""
     * )
     */
    3: i32 pageNum = 1;

        /**
         * @FieldDoc(
         *     description = "每页大小",
         *     example = ""
         * )
         */
    4: i32 pageSize = 20;


        /**
         * @FieldDoc(
         *     description = "offset",
         *     example = ""
         * )
         */
    5: string offset;
        /**
         * @FieldDoc(
         *     description = "是否为查询有赞主店（等价于查询有赞主档商品）",
         *     example = ""
         * )
         */
    6: optional bool yzMainStore;

    /**
         * @FieldDoc(
         *     description = （是否查询主档商品(替代yzMainStore字段), true:是）",
         *     example = ""
         * )
         */
    7: optional bool tenantProduct;

    /**
      * @FieldDoc(
      *     description = （是否使用游标查询）",
      *     example = ""
      * )
      */
    8: optional bool useOffset;

    9: optional string name;

    /**
    * 使用商品状态过滤
    **/
    10: optional bool useStatus;

    /**
    * 商品状态 对于抖音渠道：商品状态  0-在线；1-下线；2-删除
    **/
    11: optional i32 status;

    /**
    * 传入1时，表示获取未绑定商品自定义分类的商品列表，门店维度管理自定义分类时有效
    * 当前仅支持饿了么
    **/
    12: optional i32 unCategory;


    /**
    * 京东默认查询所有规格，设置该值为true后查询多规格商品
    **/
    13: optional bool multiSpec;

    /**
    * 是否查询渠道店内分类
    * 节约流量
    **/
    14: optional bool needCategory = true;

    /**
    * 是否使用美团商品id
    **/
    15: optional bool useMtProductId = false;
}

/**
 * @TypeDoc(
 *     description = "批量拉取商品返回信息"
 * )
 */
struct BatchGetSpuInfoResponse {
    /**
 * @FieldDoc(
 *     description = "结果状态",
 *     example = ""
 * )
 */
    1: ResultStatus.ResultStatus status;

    /**
 * @FieldDoc(
 *     description = "商品Sku信息列表",
 *     example = "1"
 *
 * )
 */
    2: list<SpuInfoDTO> spuInfos;

    /**
        * @FieldDoc(
        *     description = "分页信息",
        *     example = "{}"
        * )
        */
    3: PageInfo.PageInfo pageInfo;

    /**
        * @FieldDoc(
        *     description = "offset",
        *     example = "{}"
        * )
        */
    4: string offset;

}

/**
 * @TypeDoc(
 *     description = "通过游标的方式批量拉取商品请求"
 * )
 */
struct BatchGetSpuInfoByOffsetRequest {
    /**
         * @FieldDoc(
         *     description = "请求基本信息",
         *     example = {}
         * )
         */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    2: i64 storeId;


    /**
     * @FieldDoc(
     *     description = "商品id游标",
     *     example = ""
     * )
     */
    3: i64 skuIdOffset;

    /**
    *@FieldDoc(
    *     description = "每页数量",
    *     example = ""
    * )
    */
    4: i32 pageSize;

}
/**
 * @TypeDoc(
 *     description = "通过游标批量拉取商品信息"
 * )
 */
struct BatchGetSpuInfoByOffsetResponse {
    /**
 * @FieldDoc(
 *     description = "结果状态",
 *     example = ""
 * )
 */
    1: ResultStatus.ResultStatus status;

    /**
 * @FieldDoc(
 *     description = "商品Sku信息列表",
 *     example = "1"
 *
 * )
 */
    2: list<SpuInfoDTO> spuInfos;

    /**
        * @FieldDoc(
        *     description = "游标信息",
        *     example = "{}"
        * )
        */
    3: i64 skuIdOffset;
}

/**
 * @TypeDoc(
 *     description = "查询门店商品审核状态请求信息"
 * )
 */
struct QueryAuditStatusRequest {
        /**
             * @FieldDoc(
             *     description = "请求基本信息",
             *     example = {}
             * )
             */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = ""
         * )
         */
        2: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = ""
         * )
         */
        3: string customSpuId;

        /**
         * @FieldDoc(
         *     description = "审核状态，-1表示默认查所有",
         *     example = ""
         * )
         */
        4: i32 auditStatus = -1;

        /**
     * @FieldDoc(
     *     description = "页码",
     *     example = ""
     * )
     */
        5: i32 pageNum = 1;

        /**
         * @FieldDoc(
         *     description = "每页大小",
         *     example = ""
         * )
         */
        6: i32 pageSize = 20;

        /**
         * @FieldDoc(
         *     description = "商品合规审核状态",
         *     example = ""
         * )
         */
        7: i32 normAuditStatus = -1;

        /**
        * @FieldDoc(
        *     description = "是否医药",
        *     example = ""
        * )
        */
        8: bool isMedicine = false;
}


struct QueryQualityProblemRequest {
        /**
        * @FieldDoc(
        *     description = "请求基本信息",
        *     example = {}
        * )
        */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
        * @FieldDoc(
        *     description = "门店id",
        *     example = ""
        * )
        */
        2: i64 storeId;

        /**
        * @FieldDoc(
        *     description = "自定义商品ID",
        *     example = ""
        * )
        */
        3: list<string> customSpuIds;

        /**
        * @FieldDoc(
        *     description = "诊断类型 1-管家质量诊断",
        *     example = ""
        * )
        */
        4: i32 diagnosisType = -1;
}

/**
 * @TypeDoc(
 *     description = "提交门店商品申诉状态请求信息"
 * )
 */
struct SubmitAppealInfoRequest {
        /**
             * @FieldDoc(
             *     description = "请求基本信息",
             *     example = {}
             * )
             */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = ""
         * )
         */
        2: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "商品id",
         *     example = ""
         * )
         */
        3: string customSpuId;

        /**
         * @FieldDoc(
         *     description = "发起申诉的具体问题项列表",
         *     example = ""
         * )
         */
        4: list<string> appealProblemCodeList;

        /**
         * @FieldDoc(
         *     description = "申诉补充描述",
         *     example = ""
         * )
         */
        5: string appealExplainDesc;

        /**
         * @FieldDoc(
         *     description = "申诉补充上传图片",
         *     example = ""
         * )
         */
        6: list<string> appealPictureUrls;
}

/**
 * @TypeDoc(
 *     description = "驳回原因(最末级)"
 * )
 */
struct RejectProblemDTO {

        /**
         * @FieldDoc(
         *     description = "驳回原因分类code",
         *     example = ""
         * )
         */
        1: string problemUniqCode;

        /**
         * @FieldDoc(
         *     description = "驳回原因分类描述",
         *     example = ""
         * )
         */
        2: string problemName;
}

/**
 * @TypeDoc(
 *     description = "质量审核命中的驳回原因"
 * )
 */
struct AuditProblemDTO {

        /**
         * @FieldDoc(
         *     description = "驳回原因一级分类code",
         *     example = ""
         * )
         */
        1: string firstProblemUniqCode;

        /**
         * @FieldDoc(
         *     description = "驳回原因一级分类描述",
         *     example = ""
         * )
         */
        2: string firstProblemName;

        /**
         * @FieldDoc(
         *     description = "驳回原因二级分类列表",
         *     example = ""
         * )
         */
        3: list<RejectProblemDTO> secendRejectProblemDTOList;
}

/**
 * @TypeDoc(
 *     description = "商品申诉详情"
 * )
 */
struct AppealInfoDTO {

        /**
         * @FieldDoc(
         *     description = "商品申诉状态：1-申诉中，2.申诉成功，3-申诉失败，20-不可申诉，21-可申诉，22-逾期未申诉。
         *     当appeal_type字段有值时才有含义",
         *     example = ""
         * )
         */
        1: i32 appealStatus;

        /**
         * @FieldDoc(
         *     description = "申诉处理反馈，仅申诉失败、申诉成功时才有该字段  当appeal_type字段有值时才有含义",
         *     example = ""
         * )
         */
        2: string appealRejectComment;

        /**
         * @FieldDoc(
         *     description = "申诉类型。 1-信息质量审核申诉，2-信息合规审核申诉，3-商品管家申诉",
         *     example = ""
         * )
         */
        3: i32 appealType;

        /**
         * @FieldDoc(
         *     description = "申诉问题描述。质量审核申诉的问题项或合规审核申诉的问题项 （当appeal_type字段有值时才有含义）",
         *     example = ""
         * )
         */
        4: string appealProblemName;
}

/**
 * @TypeDoc(
 *     description = "合规审核驳回原因"
 * )
 */
struct NormAuditViolationDTO {

        /**
         * @FieldDoc(
         *     description = "商品合规审核被驳回的原因",
         *     example = ""
         * )
         */
        1: string normAuditComment;

        /**
         * @FieldDoc(
         *     description = "商品合规审核问题图片信息",
         *     example = ""
         * )
         */
        2: list<string> imgUrlList;

        /**
         * @FieldDoc(
         *     description = "合规审核违规原因分类列表",
         *     example = ""
         * )
         */
        3: list<RejectProblemDTO> normViolationProblemDTOList;
}

/**
 * @TypeDoc(
 *     description = "门店商品审核状态信息"
 * )
 */
struct AuditStatusDTO {
        /**
         * @FieldDoc(
         *     description = "商品ID",
         *     example = ""
         * )
         */
        1: string customSpuId;

        /**
         * @FieldDoc(
         *     description = "商品名称",
         *     example = ""
         * )
         */
        2: string name;

        /**
         * @FieldDoc(
         *     description = "商品上下架状态。0-上架，1-下架",
         *     example = ""
         * )
         */
        3: i32 isSoldOut;

        /**
         * @FieldDoc(
         *     description = "商品创建时间",
         *     example = ""
         * )
         */
        4: i64 ctime;

        /**
         * @FieldDoc(
         *     description = "商品上一次修改时间",
         *     example = ""
         * )
         */
        5: i64 utime;

        /**
         * @FieldDoc(
         *     description = "商品审核状态",
         *     example = ""
         * )
         */
        6: i32 auditStatus;

        /**
         * @FieldDoc(
         *     description = "商品审核被驳回的原因",
         *     example = ""
         * )
         */
        7: string comment;

        /**
         * @FieldDoc(
         *     description = "驳回宽限时间",
         *     example = ""
         * )
         */
        8: i64 gracePeriodEndTime;

        /**
         * @FieldDoc(
         *     description = "商品合规审核状态",
         *     example = ""
         * )
         */
        9: i32 normAuditStatus;

        /**
         * @FieldDoc(
         *     description = "商品审核状态",
         *     example = ""
         * )
         */
        10: i32 normAuditType;

        /**
         * @FieldDoc(
         *     description = "商品审核被驳回的原因",
         *     example = ""
         * )
         */
        11: list<NormAuditViolationDTO> normAuditViolationDTOList;
        /**
         * @FieldDoc(
         *     description = "平台推荐的渠道类目id",
         *     example = ""
         * )
         */
        12: string tagId;

        /**
         * @FieldDoc(
         *     description = "质量审核命中的驳回原因分类",
         *     example = ""
         * )
         */
        13: list<AuditProblemDTO> auditProblemDTOList;

        /**
         * @FieldDoc(
         *     description = "商品申诉详情列表",
         *     example = ""
         * )
         */
        14: list<AppealInfoDTO> appealInfoDTOList;
}

/**
 * @TypeDoc(
 *     description = "查询门店商品审核状态返回信息"
 * )
 */
struct QueryAuditStatusResponse {
        /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
        1: ResultStatus.ResultStatus status;

        /**
        * @FieldDoc(
        *     description = "返回的商品审核状态信息",
        *     example = "{}"
        * )
        */
        2: list<AuditStatusDTO> auditStatusList;
}


struct ProblemItem {
        /**
        * @FieldDoc(
        *     description = "问题项code",
        *     example = ""
        * )
        */
        1: i64 problemCode;

        /**
        * @FieldDoc(
        *     description = "问题项文案",
        *     example = "{}"
        * )
        */
        2: string problemName;
        /**
        * @FieldDoc(
        *     description = "整改文案",
        *     example = "{}"
        * )
        */
        3: string problemMsg;
}

struct QualityInfo {
        /**
        * @FieldDoc(
        *     description = "影响类型code, 100000-影响流量, 200000-投诉风险",
        *     example = ""
        * )
        */
        1: i64 effectCode;

        /**
        * @FieldDoc(
        *     description = "影响文案",
        *     example = "{}"
        * )
        */
        2: string effectName;
        /**
        * @FieldDoc(
        *     description = "问题项列表",
        *     example = "{}"
        * )
        */
        3: list<ProblemItem> problemItem;
}

/**
* @TypeDoc(
*     description = "门店商品商品管家信息"
* )
*/
struct QualityProblemDTO {
        /**
        * @FieldDoc(
        *     description = "商品ID",
        *     example = ""
        * )
        */
        1: string customSpuId;

        /**
        * @FieldDoc(
        *     description = "商品ID",
        *     example = ""
        * )
        */
        2: list<QualityInfo> qualityInfo;

        /**
        * @FieldDoc(
        *     description = "可申诉的二级问题项列表",
        *     example = ""
        * )
        */
        3: list<RejectProblemDTO> qualityProblemDTOList;

        /**
        * @FieldDoc(
        *     description = "商品申诉详情列表",
        *     example = ""
        * )
        */
        4: list<AppealInfoDTO> appealInfoDTOList;
}
/**
* @TypeDoc(
*     description = "查询门店商品商品管家质量信息"
* )
*/
struct QueryQualityProblemResponse {
        /**
        * @FieldDoc(
        *     description = "结果状态",
        *     example = ""
        * )
        */
        1: ResultStatus.ResultStatus status;

        /**
        * @FieldDoc(
        *     description = "返回的商品审核状态信息",
        *     example = "{}"
        * )
        */
        2: list<QualityProblemDTO> qualityProblemList;
}



/**
 * @TypeDoc(
 *     description = "根据商品UPC或商品名称查询平台推荐类目请求参数"
 * )
 */
struct RecommendChannelCategoryQueryRequest {
        /**
             * @FieldDoc(
             *     description = "请求基本信息",
             *     example = {}
             * )
             */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = ""
         * )
         */
        2: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "商品包装上的条形码编号, upc与name必须且至多填写一个",
         *     example = ""
         * )
         */
        3: string upc;

        /**
         * @FieldDoc(
         *     description = "商品名称, upc与name必须且至多填写一个",
         *     example = ""
         * )
         */
        4: string name;
}

/**
 * @TypeDoc(
 *     description = "根据商品UPC或商品名称推荐的平台推荐类目信息"
 * )
 */
struct RecommendChannelCategoryDTO {
        /**
         * @FieldDoc(
         *     description = "商品包装上的条形码编号, upc与name必须且至多填写一个",
         *     example = ""
         * )
         */
        1: string upc;

        /**
         * @FieldDoc(
         *     description = "商品名称, upc与name必须且至多填写一个",
         *     example = ""
         * )
         */
        2: string name;

        /**
         * @FieldDoc(
         *     description = "渠道类目编码",
         *     example = ""
         * )
         */
        3: string channelCategoryId;
}


/**
 * @TypeDoc(
 *     description = "根据商品UPC或商品名称查询平台推荐类目响应"
 * )
 */
struct RecommendChannelCategoryQueryResponse {
        /**
         * @FieldDoc(
         *     description = "结果状态",
         *     example = ""
         * )
         */
        1: ResultStatus.ResultStatus status;

        /**
        * @FieldDoc(
        *     description = "返回的商品审核状态信息",
        *     example = "{}"
        * )
        */
        2: RecommendChannelCategoryDTO recommendChannelCategoryDTO;
}

struct QueryAppFoodCodeBySkuIdRequest{
        1: BaseRequest.BaseRequestSimple baseInfo;
        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = ""
         * )
         */
        2: i64 storeId;

        3: list<string> skuIds;
}

struct SkuIdWithAppFoodCodeDTO {
        1: string skuId;
        2: list<string> appfoodCodes;
}

struct SkuId2AppFoodCodeResponse{
        1: ResultStatus.ResultStatus status;
        2: list<SkuIdWithAppFoodCodeDTO> data;

}

/**
 * @TypeDoc(
 *     description = "分类排序数据对象"
 * )
 */
struct CategoryRankDTO {
        /**
        * @FieldDoc(
        *     description = "渠道店内分类ID",
        *     example = ""
        * )
        */
        1: string categoryId;
        /**
        * @FieldDoc(
        *     description = "商品排序优先级",
        *     example = ""
        * )
        */
        2: i32 rank;
}
/**
 * @TypeDoc(
 *     description = "更新渠道与店内分类(支持排序)"
 * )
 */
struct UpdateCustomSpuStoreCategoryRequest{
        1: BaseRequest.BaseRequestSimple baseInfo;
        2: i64 storeId;
        /**
        * @FieldDoc(
        *     description = "渠道商品自定义编码",
        *     example = ""
        * )
        */
        3: string customSpuId;
        /**
        * @FieldDoc(
        *     description = "分类排序列表",
        *     example = ""
        * )
        */
        4: list<CategoryRankDTO> categoryRankList;
}


/**
 * @TypeDoc(
 *     description = "更新商品信息(不传字段表示不更新)"
 * )
 */
struct UpdateSpuOptionFieldRequest{

        1: BaseRequest.BaseRequestSimple baseInfo;

        2: i64 storeId;
        /**
        * @FieldDoc(
        *     description = "渠道商品自定义编码",
        *     example = ""
        * )
        */
        3: string customSpuId;
        /**
        * @FieldDoc(
        *     description = "渠道类目id ",
        *     example = ""
        * )
        */
        4: optional string channelCategoryId;

        /**
        * @FieldDoc(
        *     description = "渠道类目属性 ",
        *     example = ""
        * )
        */
        5: optional string commonAttributes;

         /**
        * @FieldDoc(
        *     description = "商品规格类型",
        *     example = ""
        * )
        */
        6: optional i32 specType;

        /**
        * @FieldDoc(
        *     description = "商品名称补充语",
        *     example = ""
        * )
        */
        7: string nameSupplement;

        /**
        * @FieldDoc(
        *     description = "商品名称补充语顺序。0：补充语后置；1：补充语前置。（适用于医药健康门店商品）",
        *     example = "1"
        * )
        */
        8: i32 nameSupplementSeq;

}

struct ProductStoreCategoryDTO{
        /**
        * @FieldDoc(
        *     description = "渠道商品自定义编码",
        *     example = ""
        * )
        */
        1: string customSpuId;
        /**
        * @FieldDoc(
        *     description = "分类排序列表",
        *     example = ""
        * )
        */
        2: list<CategoryRankDTO> categoryRankList;
}

struct BatchUpdateCustomSpuStoreCategoryRequest{
        1: BaseRequest.BaseRequestSimple baseInfo;
        2: i64 storeId;
        3: list<ProductStoreCategoryDTO> productStoreCategoryList;
}


/**
 * @TypeDoc(
 *     description = "更新渠道与店内分类(支持排序)"
 * )
 */
struct SensitiveWordDTO {
    /**
    * @FieldDoc(
    *     description = "校验文本类型。1:商品名称,2:商品描述,3:分类和分类描述,4:规格名称,5:属性和属性值,6:商品单位,7:自定义属性,9:套餐,10:商品图片特效,21:标品名称,22:商品标准名称,23:标品品牌中文名称,24:商家名称,101:药品标品名称",
    *     example = ""
    * )
    */
    1: i32 type;
    /**
    * @FieldDoc(
    *     description = "校验内容",
    *     example = ""
    * )
    */
    2: string content;
}

/**
 * @TypeDoc(
 *     description = "更新渠道与店内分类(支持排序)"
 * )
 */
struct SensitiveWordCheckRequest {
    1: BaseRequest.BaseRequestSimple baseInfo;
    /**
    * @FieldDoc(
    *     description = "门店id",
    *     example = ""
    * )
    */
    2: i64 storeId;
    /**
    * @FieldDoc(
    *     description = "校验内容列表",
    *     example = ""
    * )
    */
    3: list<SensitiveWordDTO> sensitiveWordList;

    /**
    * @FieldDoc(
    *     description = "末级后台类目id 传递则支持敏感词类目白名单校验规则",
    *     example = ""
    * )
    */
    4: i64 lastCategoryId;
}

struct SensitiveWordCheckResultDTO {
    /**
     * @FieldDoc(
     *     description = "校验内容",
     *     example = ""
     * )
     */
    1: string content;
    /**
     * @FieldDoc(
     *     description = "包含敏感词列表",
     *     example = ""
     * )
     */
    2: set<string> sensitiveWordSet;
}

/**
 * @TypeDoc(
 *     description = "更新渠道与店内分类(支持排序)"
 * )
 */
struct SensitiveWordCheckResponse {
    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;
    /**
     * @FieldDoc(
     *     description = "敏感词校验结果",
     *     example = ""
     * )
     */
    2: optional list<SensitiveWordCheckResultDTO> checkResultList;
}

/**
 * @TypeDoc(
 *     description = "更新渠道与店内分类(支持排序)"
 * )
 */
struct QueryNormAuditDelSpuRequest {
     /**
      * @FieldDoc(
      *     description = "请求基本信息",
      *     example = {}
      * )
      */
      1: BaseRequest.BaseRequestSimple baseInfo;

      /**
       * @FieldDoc(
       *     description = "门店id",
       *     example = ""
       * )
       */
       2: i64 storeId;

      /**
       * @FieldDoc(
       *     description = "门店id",
       *     example = ""
       * )
       */
       3: string customSpuId;

      /**
       * @FieldDoc(
       *     description = "页码",
       *     example = ""
       * )
       */
       5: i32 pageNum = 1;

      /**
       * @FieldDoc(
       *     description = "每页大小",
       *     example = ""
       * )
       */
       6: i32 pageSize = 20;
}

/**
 * @TypeDoc(
 *     description = "更新渠道与店内分类(支持排序)"
 * )
 */
struct QueryNormAuditDelSpuResponse {
   /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
     1: ResultStatus.ResultStatus status;

     /**
       * @FieldDoc(
       *     description = "返回合规审核删除商品详情",
       *     example = "{}"
       * )
       */
     2: list<AuditStatusDTO> auditStatusList;
}

/**
 * @TypeDoc(
 *     description = "查询渠道内部spuId请求（非appFoodCode 或者 customSpuId）"
 * )
 */
struct QueryChannelSpuIdRequest{
        /**
            * @FieldDoc(
            *     description = "请求基础信息",
            *     example = "1"
            * )
            */
        1: BaseRequest.BaseRequest baseInfo;

        /**
       * @FieldDoc(
       *     description = "门店id",
       *     example = ""
       * )
       */
        2: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "自定义商品id列表，单次调用不超过100个",
         *     example = "1"
         *
         * )
         */
        3: list<string> customSpuIdList;
}

/**
 * @TypeDoc(
 *     description = "查询渠道内部spuId数据对象"
 * )
 */
struct ChannelSpuIdDTO{
        /**
         * @FieldDoc(
         *     description = "自定义商品ID",
         *     example = ""
         * )
         */
        1: string customSpuId;

        /**
         * @FieldDoc(
         *     description = "渠道内部商品ID",
         *     example = ""
         * )
         */
        2: string channelSpuId;
}

/**
 * @TypeDoc(
 *     description = "查询渠道内部spuId返回"
 * )
 */
struct QueryChannelSpuIdResponse{
        /**
         * @FieldDoc(
         *     description = "结果状态",
         *     example = ""
         * )
         */
        1: ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "结果数据",
         *     example = ""
         * )
         */
        2: list<ChannelSpuIdDTO> channelSpuIdList;
}


/**
 * @TypeDoc(
 *     description = "动态信息修复时渠道商品U信息"
 * )
 */
struct DynamicRepairSpuInfoDTO{

     /**
         * @FieldDoc(
         *     description = "商家商品SPU编码：(1) 不同门店之间商品id可以重复，同一门店内商品id不允许重复；(2)字段信息限定长度不超过128个字符",
         *     example = "10"
         * )
         */
    1: string customSpuId;

    /**
     * @FieldDoc(
     *     description = "渠道类目编码",
     *     example = "99"
     * )
     */
    2: string channelCategoryId;

    /**
    * @FieldDoc(
    *     description = "商品普通属性列表，支持多个商品属性",
    *     example = "商品普通属性"
    * )
    */
    3: string commonAttributes;
}

/**
 * @TypeDoc(
 *     description = "动态信息数据问题修复请求信息"
 * )
 */
struct DynamicInfoRepairRequest{

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequest baseInfo;

    /**
     * @FieldDoc(
     *     description = "商品信息列表，一次最多200个商品",
     *     example = "1"
     *
     * )
     */
    2: list<DynamicRepairSpuInfoDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "商品类目属性(值)信息"
 * )
 */
struct SpuCategoryPropertyInfo {
    /**
     * @FieldDoc(
     *     description = "属性id",
     *     example = ""
     * )
     */
    1: optional string propId;

    /**
     * @FieldDoc(
     *     description = "属性文本",
     *     example = ""
     * )
     */
    2: optional string propText;

    /**
     * @FieldDoc(
     *     description = "属性值id",
     *     example = ""
     * )
     */
    3: optional string valueId;

    /**
     * @FieldDoc(
     *     description = "属性值文本",
     *     example = ""
     * )
     */
    4: optional string valueText;

    /**
     * @FieldDoc(
     *     description = "是否销售属性",
     *     example = ""
     * )
     */
    5: optional bool saleProp;
}

/**
 * @TypeDoc(
 *     description = "获取商品类目属性(值)返回对象"
 * )
 */
struct GetSpuCategoryPropertyResponse {

    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "商品类目属性(值)列表",
     *     example = ""
     * )
     */
    2: list<SpuCategoryPropertyInfo> propertyList;

}



