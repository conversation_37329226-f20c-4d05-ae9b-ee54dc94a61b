namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "BaseRequest.thrift"

/**
 * @TypeDoc(
 *     description = "拉取类目"
 * )
 */
struct CatRequest {

    /**
     * @FieldDoc(
     *     description = "租户和渠道基本信息",
     *     example = ""
     * )
     */
    1: required BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "父分类id列表，1级为0",
     *     example = ""
     * )
     */
    2: required list<string> ids;

    /**
     * @FieldDoc(
     *     description = "分类层级",
     *     example = ""
     * )
     */
    3: optional i32 depth;

    /**
 * @FieldDoc(
 *     description = "门店id",
 *     example = ""
 * )
 */
    4: optional i64 storeId;

    /**
     * @FieldDoc(
     *      description = "查询分类类型，用于美团渠道区分查询的标准分类类型，1: 零售 2: 医药"
     * )
    **/
    5: optional i32 categoryType;

    /**
     * @FieldDoc(
     *      description = "是否校验UPC"（目前仅京东渠道使用）true:校验 false:不校验
     * )
    **/
    6: optional bool checkUpc;
}