namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "BaseRequest.thrift"

/**
 * @TypeDoc(
 *     description = "拉取类目属性类型"
 * )
 */
enum ChannelCategoryAttrTypeEnum {
    SALE,
    NORMAL,
    SPECIAL
}

/**
 * @TypeDoc(
 *     description = "拉取类目属性"
 * )
 */
struct CategoryAttrRequest {

    /**
     * @FieldDoc(
     *     description = "租户和渠道基本信息",
     *     example = ""
     * )
     */
    1: required BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "末级类目id",
     *     example = ""
     * )
     */
    2: required i64 categoryId;

    /**
     * @FieldDoc(
     *     description = "门店ID(饿了么渠道必传)",
     *     example = ""
     * )
     */
    3: optional i64 storeId;

    /**
     * @FieldDoc(
     *     description = "需要查询的属性类型，比如京东，类目属性分为了普通属性、销售属性、特殊属性",
     *     rule = "仅京东渠道有效，且不传时默认为SALE(销售属性)",
     *     example = "SALE"
     * )
     */
    4: optional ChannelCategoryAttrTypeEnum attrType;
}