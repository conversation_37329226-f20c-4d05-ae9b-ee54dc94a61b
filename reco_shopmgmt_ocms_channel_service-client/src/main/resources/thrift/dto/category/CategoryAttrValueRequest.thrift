namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "BaseRequest.thrift"

/**
 * @TypeDoc(
 *     description = "获取特殊属性值"
 * )
 */
struct CategoryAttrValueRequest {

    /**
     * @FieldDoc(
     *     description = "租户和渠道基本信息",
     *     example = ""
     * )
     */
    1: required BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "属性id",
     *     example = ""
     * )
     */
    2: required i64 attrId;

    /**
     * @FieldDoc(
     *     description = "属性值的关键词",
     *     example = ""
     * )
     */
    3: required string keyword;

    /**
     * @FieldDoc(
     *     description = "页数",
     *     example = ""
     * )
     */
    4: required i32 pageNo;

    /**
     * @FieldDoc(
     *     description = "每页大小",
     *     example = ""
     * )
     */
    5: required i32 pageSize;

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    6: optional i64 storeId;

}