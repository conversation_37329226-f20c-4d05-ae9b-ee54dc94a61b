namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "AttrOptionInfo.thrift"

/**
 * @TypeDoc(
 *     description = "模块值展示规则"
 * )
 */
struct ModuleShowFormat {
    /**
     * @TypeDoc(
     *     description = "需要组合展示的模块列表，根据struct_attr_template_modules中的sequence来组合展示"
     * )
     */
    1: list<i32> moduleSequenceList;
}

struct ValidateRule {
    /**
     * @TypeDoc(
     *     description = "当为1-文本时表示字符串类型为最小字符数，当为4-数字类型时表示数字部分可录入的最小值"
     * )
     */
    1: string min;
    /**
     * @TypeDoc(
     *     description = "当为1-文本时表示字符串类型为最大字符数，当为4-数字类型时表示数字部分可录入的最大值"
     * )
     */
    2: string max;
    /**
     * @TypeDoc(
     *     description = "当为1-文本时有值，表示文本录入的字符类型组合。 1-中文，2-英文，3-数字，4-标点符合（含符号）"
     * )
     */
    3: string characterType;
    /**
     * @TypeDoc(
     *     description = "当模块输入类型为4-数字时有值，表示数字部分可录入的小数点位数，当返回为空或者为0是表示无小数位或者为整数。"
     * )
     */
    4: i32 precision;
    /**
     * @TypeDoc(
     *     description = "录入方式为文本时，是否有符号限制。0=没有 1=有"
     * )
     */
    5: i32 hasSymbolLimit;
    /**
     * @TypeDoc(
     *     description = "录入方式为文本且有符号限制时表示允许录入的符号列表"
     * )
     */
    6: list<string> allowSymbols;
}

/**
 * @TypeDoc(
 *     description = "当前模版属性值的录入类型"
 * )
 */
struct StructAttrValueFormatType {
    /**
     * @TypeDoc(
     *     description = "组合规则，1-单品，2-多类组合，3-自定义"
     * )
     */
    1: i32 formatType;

    /**
     * @TypeDoc(
     *     description = "组合限制 当formatType=2时，最多可以增加多少个单品"
     * )
     */
    2: i32 combinationLimit;
    /**
     * @TypeDoc(
     *     description = "录入值示例"
     * )
     */
    3: string tips;
}

/**
 * @TypeDoc(
 *     description = "结构化属性值模板列表"
 * )
 */
struct StructAttrTemplateModule {
    /**
     * @TypeDoc(
     *     description = "模块顺序"
     * )
     */
    1: i32 sequence;
    /**
     * @TypeDoc(
     *     description = "当前模块的提示信息"
     * )
     */
    2: string placeholder;
    /**
     * @TypeDoc(
     *     description = "是否必填：1-必填,2-非必填。"
     * )
     */
    3: i32 need;
    /**
     * @TypeDoc(
     *     description = "当前模块输入类型，枚举值如下：1-文本，3-固定文本（商家无需修改，直接传），4-数字，5-下拉"
     * )
     */
    4: i32 inputType;
    /**
     * @TypeDoc(
     *     description = "模块输入类型为2-数字+单位时有值，表示单位的选项列表"
     * )
     */
    5: list<AttrOptionInfo.AttrOptionInfo> optionList;
    /**
     * @TypeDoc(
     *     description = "模块值，当输入类型为3-固定文本时有值，商家无需修改，建品时直接回传即可。"
     * )
     */
    6: string moduleValue;
    /**
     * @TypeDoc(
     *     description = "填写规则，模块输入类型为1-文本，2-数字+单位时有值"
     * )
     */
    7: ValidateRule validateRule;
}

/**
 * @TypeDoc(
 *     description = "销售属性模板"
 * )
 */
struct StructAttrTemplate {
    1: list<StructAttrValueFormatType> structAttrValueFormatTypes;
    2: list<StructAttrTemplateModule> structAttrTemplateModules;
    3: list<ModuleShowFormat> moduleShowFormat;
}

