namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "AttrValueInfo.thrift"
include "AttrOptionInfo.thrift"
include "StructAttrTemplate.thrift"
/**
 * @TypeDoc(
 *     description = "类目属性信息"
 * )
 */
struct CategoryAttrInfo {

    /**
     * @FieldDoc(
     *     description = "属性id",
     *     example = ""
     * )
     */
    1: string attrId;

    /**
     * @FieldDoc(
     *     description = "属性名称",
     *     example = ""
     * )
     */
    2: string attrName;

    /**
     * @FieldDoc(
     *     description = "属性值类型，1-单选,2-多选,3-文本,10-置顶格式录入",
     *     example = ""
     * )
     */
    3: string attrValueType;

    /**
     * @FieldDoc(
     *     description = "是否必传，1-必传,2-非必传",
     *     example = ""
     * )
     */
    4: string need;

    /**
     * @FieldDoc(
     *     description = "属性值可录入的字符类型，1-中文；2-字母；3-数字；4-标点符号",
     *     example = ""
     * )
     */
    5: string characterType;

    /**
     * @FieldDoc(
     *     description = "属性值可录入的最大字符长度",
     *     example = ""
     * )
     */
    6: string textMaxLength;

    /**
     * @FieldDoc(
     *     description = "属性的顺序值",
     *     example = ""
     * )
     */
    7: i32 sequence;

    /**
     * @FieldDoc(
     *     description = "属性值的可扩展性",
     *     example = ""
     * )
     */
    8: string supportExtend;

    /**
     * @FieldDoc(
     *     description = "值列表",
     *     example = ""
     * )
     */
    9: list<AttrValueInfo.AttrValueInfo> valueList;

    /**
    * @FieldDoc(
    *     description = "是否支持设置规格图片 1-是 0-否",
    *     example = ""
    * )
    */
    10: i32 supportPicture
    /**
     * @FieldDoc(
     *     description = "最大多选数",
     *     example = ""
     * )
     */
    11: i64 multiSelectMax

    /**
     * @FieldDoc(
     *     description = "属性类型，0 绑定属性 1关键属性 2售卖属性 3 商品属性",
     *     example = ""
     * )
     */
    12: i64 propertyType;

    /**
     * @FieldDoc(
     *     description = "商品属性是否有支持商家自定义，1=支持自定义，0=不支持自定义。 使用场景：当开发者传入自定义的属性值时需在创建或更新商品接口的属性时，需把【product_format_new】字段中传入diy_type=1并且value=0；",
     *     example = ""
     * )
     */
    13: i64 diyType;

    /**
     * @FieldDoc(
     *     description = "0:非重要属性，1:重要属性",
     *     example = ""
     * )
     */
    14: i64 importantType;

    /**
     * @FieldDoc(
     *     description = "0:非重要属性，1:重要属性",
     *     example = ""
     * )
     */
    15: list<AttrOptionInfo.AttrOptionInfo> optionList;

    /**
     * @FieldDoc(
     *     description = "录入方式为“单选”或“多选”时，录入属性值数量不能超过该限制；0表示录入属性值数量无上限；录入方式为“文本”或“日期”时或“文本+选项” ，本参数无参考意义。",
     *     example = ""
     * )
     */
    16: i32 optionMaxSize;

    /**
     * @FieldDoc(
     *     description = "该字段仅当属性值类型为10-指定格式录入时有效，为结构化属性值模板，表示当前属性可以按以下模版录入属性值。",
     *     example = ""
     * )
     */
    17: list<StructAttrTemplate.StructAttrTemplate> structAttrTemplate

}