namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

/**
 * @TypeDoc(
 *     description = "店内分类基本信息"
 * )
 */
struct CatInfo {

    /**
     * @FieldDoc(
     *     description = "店内分类id",
     *     example = ""
     * )
     */
    1: string catId;

    /**
     * @FieldDoc(
     *     description = "店内分类名称",
     *     example = ""
     * )
     */
    2: string name;

    /**
     * @FieldDoc(
     *     description = "父分类id",
     *     example = ""
     * )
     */
    3: string parentId;

    /**
     * @FieldDoc(
     *     description = "分类层级",
     *     example = ""
     * )
     */
    4: i32 depth;

    /**
    * @FieldDoc(
    *     description = "分类名称层级路径",
    *     example = ""
    * )
    */
    5: string namePath;

    /**
    * @FieldDoc(
    *     description = "排序",
    *     example = ""
    * )
    */
    6: i32 sequence;

    /**
    * @FieldDoc(
    *     description = "智能排序开关",
    *     example = ""
    * )
    */
    7: i32 smartSort;
    /**
    * @FieldDoc(
    *     description = "父分类名称，可空，目前仅查询美团店内分类列表有值",
    *     example = ""
    * )
    */
    8: string parentName;
    /**
        * @FieldDoc(
        *     description = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效)",
        *     example = ""
        * )
        */
    9: i32 checkUpcStatus;
        /**
        * @FieldDoc(
        *     description = "牵牛花定义的店内分类id (京东和有赞渠道因为渠道接口未返回为空)",
        *     example = ""
        * )
        */
    10: string customCatId;

          /**
            * @FieldDoc(
            *     description = "是否是叶子节点，仅抖音、京东在使用",
            *     example = ""
            * )
            */
    11: bool isLeaf;
}