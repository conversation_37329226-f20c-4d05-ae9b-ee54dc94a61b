namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"
include "CatInfo.thrift"

/**
 * @TypeDoc(
 *     description = "批量获取类目信息返回对象"
 * )
 */
struct GetCategoryResponse {

    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "类目列表",
     *     example = ""
     * )
     */
    2: list<CatInfo.CatInfo> catInfoList;

    /**
     * @FieldDoc(
     *     description = "查询失败分类id信息",
     *     example = ""
     * )
     */
    3: map<string, string> failCatMsgMap;
}