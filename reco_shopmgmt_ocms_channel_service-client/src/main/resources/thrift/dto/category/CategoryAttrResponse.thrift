namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"
include "CategoryAttrInfo.thrift"

/**
 * @TypeDoc(
 *     description = "获取类目属性列表"
 * )
 */
struct CategoryAttrResponse {

    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "普通属性列表",
     *     example = ""
     * )
     */
    2: list<CategoryAttrInfo.CategoryAttrInfo> generalAttrList;

    /**
     * @FieldDoc(
     *     description = "销售属性列表",
     *     example = ""
     * )
     */
    3: list<CategoryAttrInfo.CategoryAttrInfo> saleAttrList;

    /**
    * @FieldDoc(
    *     description = "该类目的商品条形码（UPC）是否必填，0-必填，1-选填",
    *     example = ""
    * )
    */
    4: i32 upcRequired;


    /**
    * @FieldDoc(
    *     description = "该类目的重量是否必填，0-必填，1-选填",
    *     example = ""
    * )
    */
    5: i32 weightRequired;

     /**
        * @FieldDoc(
        *     description = "规格属性列表",
        *     example = ""
        * )
        */
    6: list<CategoryAttrInfo.CategoryAttrInfo> skuAttrList;
}