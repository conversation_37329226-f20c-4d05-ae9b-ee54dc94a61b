namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"

struct RuleValue{

    /**
     * @FieldDoc(
     *     description = "选项ID",
     *     example = ""
     * )
     */
    1: i64 valueId;

    /**
     * @FieldDoc(
     *     description = "选项名",
     *     example = ""
     * )
     */
    2: string valueName;

    /**
     * @FieldDoc(
     *     description = "是否默认勾选项",
     *     example = ""
     * )
     */
    3: bool byDefault;
}

struct ProductRule{

    /**
     * @FieldDoc(
     *     description = "规则ID",
     *     example = ""
     * )
     */
    1: i64 ruleId;

    /**
     * @FieldDoc(
     *     description = "规则名",
     *     example = ""
     * )
     */
    2: string ruleName;

    /**
     * @FieldDoc(
     *     description = "选项值列表",
     *     example = ""
     * )
     */
    3: list<RuleValue> valueList;
}

struct QualificationPicturesInfo{
    /**
     * @FieldDoc(
     *     description = "资质图标题",
     *     example = ""
     * )
     */
    1: string qualificationPictureTitle;

    /**
     * @FieldDoc(
     *     description = "资质图描述",
     *     example = ""
     * )
     */
    2: string qualificationPictureDescription;

    /**
     * @FieldDoc(
     *     description = "资质图示例图片",
     *     example = ""
     * )
     */
    3: string qualificationPictureExample;
}

struct ProductQuaPicUnstructRule{

    /**
     * @FieldDoc(
     *     description = "是否需要资质图,1-需要,0-不需要",
     *     example = ""
     * )
     */
    1: i32 isQualificationPicturesNeed;

    /**
     * @FieldDoc(
     *     description = "是否需要资质图,1-需要,0-不需要",
     *     example = ""
     * )
     */
    2: list<QualificationPicturesInfo> infos;
}

struct ProductSpecialPictureRule{
    /**
     * @FieldDoc(
     *     description = "特殊图类型",
     *     example = ""
     * )
     */
    1: i32 specialPictureType;
    /**
     * @FieldDoc(
     *     description = "标题",
     *     example = ""
     * )
     */
    2: string specialPictureTitle;
    /**
     * @FieldDoc(
     *     description = "描述",
     *     example = ""
     * )
     */
    3: string specialPictureDescription;
    /**
     * @FieldDoc(
     *     description = "示例",
     *     example = ""
     * )
     */
    4: string specialPictureExample;
    /**
     * @FieldDoc(
     *     description = "是否必填",
     *     example = ""
     * )
     */
    5: i32 isRequired;
    /**
     * @FieldDoc(
     *     description = "是否展示",
     *     example = ""
     * )
     */
    6: i32 isDisplayed;
    /**
     * @FieldDoc(
     *     description = "数量",
     *     example = ""
     * )
     */
    7: i32 numLimit;
}

struct ProductUnstructRule{

    /**
     * @FieldDoc(
     *     description = "资质图规则",
     *     example = ""
     * )
     */
    1: ProductQuaPicUnstructRule quaPicUnstructRule;

    /**
     * @FieldDoc(
     *     description = "特殊图规则",
     *     example = ""
     * )
     */
    2: list<ProductSpecialPictureRule> specialPictureRules;
}


/**
 * @TypeDoc(
 *     description = "获取类目相应商品发布规则"
 * )
 */
struct CategoryProductRulesResponse {

    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "商品发布规则列表",
     *     example = ""
     * )
     */
    2: list<ProductRule> ruleList;

    /**
     * @FieldDoc(
     *     description = "商品发布非结构化规则",
     *     example = ""
     * )
     */
    3: ProductUnstructRule unstructRule;
}