namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "BaseRequest.thrift"
include "ResultStatus.thrift"
include "CategoryAdjustTypeEnum.thrift"
include "ProductChannelUnifyErrorEnum.thrift"

/**
 * @TypeDoc(
 *     description = "前台分类信息"
 * )
 */
struct CategoryInfoDTO{

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "分类Code"
     * )
     */
    2: string code;

    /**
     * @FieldDoc(
     *     description = "类目名称"
     * )
     */
    3: string name;

    /**
     * @FieldDoc(
     *     description = "层级"
     * )
     */
    4: i32 level

    /**
     * @FieldDoc(
     *     description = "排序"
     * )
     */
    5: i32 sort;

    /**
     * @FieldDoc(
     *     description = "父类目ID"
     * )
     */
    6: string parentCode;

    /**
     * @FieldDoc(
     *     description = "父类目ID（渠道返回的ID，顶级类目为0）"
     * )
     */
    7: string channelParentCode;

    /**
     * @FieldDoc(
     *     description = "父类目名称",
     *     example = ""
     * )
     */
    8: string parentName;

}

/**
 * @TypeDoc(
 *     description = "前台分类信息删除"
 * )
 */
struct CategoryInfoDeleteDTO{

    /**
     * @FieldDoc(
     *     description = "分类Code"
     * )
     */
    1: string code;

    /**
     * @FieldDoc(
     *     description = "渠道返回的分类Code"
     * )
     */
    2: string channelCategoryCode;

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    3: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "渠道分类名称（美团渠道可用名称删除）"
     * )
     */
    4: optional string channelCategoryName = "";

    /**
     * @FieldDoc(
     *     description = "强制删除(0否1是;美团渠道生效，分类下的商品移至未分类)"
     * )
     */
    5: optional i32 forceDelete;

    /**
     * @FieldDoc(
     *     description = "父分类code",
     *     example = ""
     * )
     */
    6: string parentCode;

    /**
     * @FieldDoc(
     *     description = "父渠道分类code",
     *     example = ""
     * )
     */
    7: string channelParentCategoryCode;


}

/**
 * @TypeDoc(
 *     description = "前台分类信息排序子分类信息"
 * )
 */
struct CategoryInfoSortItemDTO{

    /**
     * @FieldDoc(
     *     description = "子分类Code"
     * )
     */
    1: string code;

    /**
     * @FieldDoc(
     *     description = "渠道返回的子分类Code"
     * )
     */
    2: string channelCategoryCode;

    /**
     * @FieldDoc(
     *     description = "类目名称"
     * )
     */
    3: string name;

    /**
     * @FieldDoc(
     *     description = "子分类序号"
     * )
     */
    4: i32 sort;
}

/**
 * @TypeDoc(
 *     description = "分类更新对象"
 * )
 */
struct CategoryInfoUpdateDTO {

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "分类code",
     *     example = ""
     * )
     */
    2: string code;

    /**
     * @FieldDoc(
     *     description = "分类名",
     *     example = ""
     * )
     */
    3: string name;

    /**
     * @FieldDoc(
     *     description = "渠道分类code",
     *     example = ""
     * )
     */
    4: string channelCategoryCode;

    /**
     * @FieldDoc(
     *     description = "排序",
     *     example = ""
     * )
     */
    5: i32 sort;

    /**
     * @FieldDoc(
     *     description = "分类的层级",
     *     example = "1"
     * )
     */
    6: i32 level;

    /**
     * @FieldDoc(
     *     description = "父分类code",
     *     example = ""
     * )
     */
    7: string parentCode;

    /**
     * @FieldDoc(
     *     description = "父渠道分类code",
     *     example = ""
     * )
     */
    8: string channelParentCategoryCode;
}

struct CategoryInfoTopDTO {

        /**
        * @FieldDoc(
        *     description = "门店id",
        *     example = ""
        * )
        */
        1: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "分类code",
         *     example = ""
         * )
         */
        2: string code;

        /**
         * @FieldDoc(
         *     description = "分类名",
         *     example = ""
         * )
         */
        3: string name;

        /**
         * @FieldDoc(
         *     description = "渠道分类code",
         *     example = ""
         * )
         */
        4: string channelCategoryCode;
        /**
  * @FieldDoc(
  *     description = "排序",
  *     example = ""
  * )
  */
        5: i32 sort;
        /**
        * @FieldDoc(
        *     description = "是否开启置顶：(1)字段取值范围：0-否，1-是；(2)仅支持一级分类设置分时置顶。",
        *     example = "1"
        * )
        */
        6: i32 topFlag;

        /**
         * @FieldDoc(
         *     description = "置顶周期：(1)1,2,3,4,5,6,7分别表示周一至周日",
         *     example = "1,3,4"
         * )
         */
        7: optional string weeksTime;

        /**
         * @FieldDoc(
         *     description = "置顶时段：(1)最多支持设置5个时间段，多个时间段之间用英文逗号分隔；",
         *     example = "00:00-09:00,11:30-16:30"
         * )
         */
        8: optional string period;
}

/**
 * @TypeDoc(
 *     description = "前台分类信息排序"
 * )
 */
struct CategoryInfoSortDTO{

    /**
     * @FieldDoc(
     *     description = "分类Code（要排序的分类的父分类，一级分类为0）"
     * )
     */
    1: string code;

    /**
     * @FieldDoc(
     *     description = "渠道返回的分类Code（要排序的分类的父分类，一级分类为0）"
     * )
     */
    2: string channelCategoryCode;

    /**
     * @FieldDoc(
     *     description = "子分类列表（要排序的分类列表，要包括父分类下的全部子分类）"
     * )
     */
    3: list<CategoryInfoSortItemDTO> sortItemList;

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    4: i64 storeId;
}

/**
 * @TypeDoc(
 *     description = "分类信息请求参数"
 * )
 */
struct CategoryRequest {

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "分类信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<CategoryInfoDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "分类信息请求参数"
 * )
 */
struct CategoryUpdateRequest {

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "分类信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<CategoryInfoUpdateDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "分类信息请求参数"
 * )
 */
struct CategoryTopRequest {

        /**
         * @FieldDoc(
         *     description = "租户渠道Id",
         *     example = "1"
         * )
         */
        1: BaseRequest.BaseRequestSimple baseInfo;

        /**
         * @FieldDoc(
         *     description = "分类信息列表",
         *     example = "1"
         *
         * )
         */
        2: list<CategoryInfoTopDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "分类删除信息请求参数"
 * )
 */
struct CategoryDeleteRequest {

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "分类删除信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<CategoryInfoDeleteDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "分类排序信息请求参数"
 * )
 */
struct CategorySortRequest {

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "分类排序信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<CategoryInfoSortDTO> paramList;
}

/**
 * @TypeDoc(
 *     description = "门店维度创建前台分类结果"
 * )
 */
struct CategoryPoiResult {

    /**
     * @FieldDoc(
     *     description = "门店id，京东为0",
     *     example = ""
     * )
     */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "平台code",
     *     example = ""
     * )
     */
    2: string code;

    /**
     * @FieldDoc(
     *     description = "渠道编码",
     *     example = ""
     * )
     */
    3: string channelCode;

    /**
     * @FieldDoc(
     *     description = "结果码",
     *     example = ""
     * )
     */
    4: i32 resultCode;

    /**
     * @FieldDoc(
     *     description = "失败描述",
     *     example = ""
     * )
     */
    5: string msg;

    /**
     * @FieldDoc(
     *     description = "渠道统一错误码",
     *     example = "STORE_SPU_NOT_EXIST"
     * )
     */
    6: ProductChannelUnifyErrorEnum.ProductChannelUnifyErrorEnum channelUnifyError;
}

/**
 * @TypeDoc(
 *     description = "创建前台分类返回结果"
 * )
 */
struct CreateCategoryResponse {

    /**
     * @FieldDoc(
     *     description = "结果",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "门店维度创建前台分类结果",
     *     example = ""
     * )
     */
    2: list<CategoryPoiResult> data;

}

/**
 * @TypeDoc(
 *     description = "更新前台分类返回结果"
 * )
 */
struct UpdateCategoryResponse {
    /**
        * @FieldDoc(
        *     description = "结果",
        *     example = ""
        * )
        */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "门店维度创建前台分类结果",
     *     example = ""
     * )
     */
    2: list<CategoryPoiResult> data;
}

/**
 * @TypeDoc(
 *     description = "推荐类目及类目属性请求"
 * )
 */
struct RecommendCategoryRequest {
    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "upc",
     *     example = ""
     * )
     */
    2: string upcCode;

    /**
     * @FieldDoc(
     *     description = "商品名称",
     *     example = ""
     * )
     */
    3: string name;

    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = ""
     * )
     */
    4: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "查询类型：1-类目， 2-类目属性，不传默认为1",
     *     example = ""
     * )
     */
    5: i32 queryType;

    /**
     * @FieldDoc(
     *     description = "渠道类目id",
     *     example = ""
     * )
     */
    6: string categoryId;

    /**
     * @FieldDoc(
     *     description = "虚拟渠道系统参数",
     *     example = ""
     * )
     */
    7: string virtualSysParam;
}

/**
 * @TypeDoc(
 *     description = "推荐渠道类目及类目属性返回结果,如果没有推荐,则字段全部为null"
 * )
 */
struct RecommendCategoryResponse {
    /**
        * @FieldDoc(
        *     description = "结果",
        *     example = ""
        * )
        */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "门店维度创建前台分类结果",
     *     example = ""
     * )
     */
    2: string upcCode;

    3: string name;

    4: i64 channelCategoryCode;

    /**
     * @FieldDoc(
     *     description = "渠道类目属性",
     *     example = ""
     * )
     */
    5: string categoryProperties;

    /**
     * @FieldDoc(
     *     description = "重量单位对应的重量值",
     *     example = ""
     * )
     */
    6: double weightForUnit;

    /**
     * @FieldDoc(
     *     description = "重量单位",
     *     example = ""
     * )
     */
    7: string weightUnit;

    /**
    * 推荐品牌id，目前只支持京东
**/
    8: i64 brandId;
}


/**
 * @TypeDoc(
 *     description = "分类更新对象"
 * )
 */
struct CategoryInfoDegradeDTO {

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "分类Code"
     * )
     */
    2: string code;


    /**
     * @FieldDoc(
     *     description = "渠道分类Code"
     * )
     */
    3: string channelCode;

    /**
     * @FieldDoc(
     *     description = "类目名称"
     * )
     */
    4: string name;

    /**
     * @FieldDoc(
     *     description = "排序"
     * )
     */
    5: i32 sort;

    /**
     * @FieldDoc(
     *     description = "父类目ID"
     * )
     */
    6: string parentCode;

    /**
     * @FieldDoc(
     *     description = "父类目ID（渠道返回的ID，顶级类目为0）"
     * )
     */
    7: string channelParentCode;

    /**
     * @FieldDoc(
     *     description = "父类目名称",
     *     example = ""
     * )
     */
    8: string parentName;
}

/**
 * @TypeDoc(
 *     description = "分类排序信息请求参数"
 * )
 */
struct CategoryDegradeRequest {

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "分类排序信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<CategoryInfoDegradeDTO> paramList;
}


/**
 * @TypeDoc(
 *     description = "分类等级调整更新对象"
 * )
 */
struct CategoryLevelAdjustDTO {

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "分类Code"
     * )
     */
    2: string code;


    /**
     * @FieldDoc(
     *     description = "渠道分类Code"
     * )
     */
    3: string channelCode;

    /**
     * @FieldDoc(
     *     description = "类目名称"
     * )
     */
    4: string name;

    /**
     * @FieldDoc(
     *     description = "排序"
     * )
     */
    5: i32 sort;

    /**
     * @FieldDoc(
     *     description = "父类目ID"
     * )
     */
    6: string parentCode;

    /**
     * @FieldDoc(
     *     description = "父类目ID（渠道返回的ID，顶级类目为0）"
     * )
     */
    7: string channelParentCode;

    /**
     * @FieldDoc(
     *     description = "父类目名称",
     *     example = ""
     * )
     */
    8: string parentName;

    /**
     * @FieldDoc(
     *     description = "等级调整类型",
     *     example = ""
     * )
     */
    9: CategoryAdjustTypeEnum.CategoryAdjustTypeEnum adjustType;

    /**
     * @FieldDoc(
     *     description = "调整之前的父分类编码",
     *     example = ""
     * )
     */
    10: string oldParentCode;

    /**
     * @FieldDoc(
     *     description = "调整之前的父分类渠道编码",
     *     example = ""
     * )
     */
    11: string oldChannelParentCode;
}

/**
 * @TypeDoc(
 *     description = "分类等级调整信息请求参数"
 * )
 */
struct CategoryLevelAdjustRequest {

    /**
     * @FieldDoc(
     *     description = "租户渠道Id",
     *     example = "1"
     * )
     */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "分类排序信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<CategoryLevelAdjustDTO> paramList;
}

struct CategorySmartSortSwitchRequest {

    /**
     * @FieldDoc(
     *     description = "子分类Code"
     * )
     */
    1: string categoryCode;

    /**
     * @FieldDoc(
     *     description = "渠道返回的子分类Code"
     * )
     */
    2: string channelCategoryCode;

    /**
     * @FieldDoc(
     *     description = "开关状态  true 开启  false 关闭"
     * )
     **/
    3: bool smartSort;

    /**
     * @FieldDoc(
     *     description = "门店id"
     * )
     **/
    4: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "渠道id"
     * )
     **/
    5: i32 channelId;

    /**
     * @FieldDoc(
     *     description = "商户id"
     * )
     **/
    6: i64 tenantId;
}

struct CategorySmartSortQueryRequest {

        /**
         * @FieldDoc(
         *     description = "子分类Code"
         * )
         */
        1: string categoryCode;

        /**
         * @FieldDoc(
         *     description = "渠道返回的子分类Code"
         * )
         */
        2: string channelCategoryCode;


        /**
         * @FieldDoc(
         *     description = "门店id"
         * )
         **/
        3: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "渠道id"
         * )
         **/
        4: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "商户id"
         * )
         **/
        5: i64 tenantId;
}

struct CategorySmartSortDTO {
        /**
         * @FieldDoc(
         *     description = "渠道分类"
         * )
         **/
        1: string channelCategoryCode;

        /**
         * @FieldDoc(
         *     description = "智能排序开关  1关闭, 2打开"
         * )
         **/
        2: bool smartSort;

        /**
         * @FieldDoc(
         *     description = "分类名称"
         * )
         **/
        3: string name;
}

struct CategorySmartSortQueryResponse {
        /**
            * @FieldDoc(
            *     description = "结果",
            *     example = ""
            * )
            */
        1: ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "品类智能开关",
         *     example = ""
         * )
         */
        2: list<CategorySmartSortDTO> data;
}