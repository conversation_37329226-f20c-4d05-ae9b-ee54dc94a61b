namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

/**
 * @TypeDoc(
 *     description = "类目属性值信息"
 * )
 */
struct AttrValueInfo {

    /**
     * @FieldDoc(
     *     description = "值id",
     *     example = ""
     * )
     */
    1: string valueId;

    /**
     * @FieldDoc(
     *     description = "值文本",
     *     example = ""
     * )
     */
    2: string value;

    /**
     * @FieldDoc(
     *     description = "值id路径",
     *     example = ""
     * )
     */
    3: optional string valueIdPath;

    /**
     * @FieldDoc(
     *     description = "值文本路径",
     *     example = ""
     * )
     */
    4: optional string valuePath;

    /**
     * @FieldDoc(
     *     description = "属性值顺序",
     *     example = ""
     * )
     */
    5: optional i64 sequence;

    /**
     * @FieldDoc(
     *     description = "属性名称",
     *     example = ""
     * )
     */
    6: optional string name;


}