namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto
include "ResultStatus.thrift"

struct MarketOrderListRequest {
    1: i64 queryStartTime;
    2: i64 queryEndTime;
    3: i64 queryOffset
    4: i64 tenantId;
    5: i64 channelId;
    6: i64 qnhAppId;
}

struct MarketMessageRespDTO {
    1: string orderId;
    2: string orderStatus;
    3: string platformShopId;
    4: string specId;
    5: string specName;
    6: string validDate;
    7: string totalPrice;
    8: string openId;
    9: i64 createTime;
    10: string userType;
}

struct MarketOrderListResponse {
        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = {}
         * )
         */
        1: ResultStatus.ResultStatus status;

       /**
        * @FieldDoc(
        *     description = "marketMessageRespDTO",
        *     example = ""
        * )
        */
       2: list<MarketMessageRespDTO> data;
}