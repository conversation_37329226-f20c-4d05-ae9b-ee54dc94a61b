namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

/**
 * @TypeDoc(
 *     description = "获取token（迁移用）"
 * )
 */
struct TokenGetRequest {

    /**
     * @FieldDoc(
     *     description = "租户id",
     *     example = ""
     * )
     */
    1: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = ""
     * )
     */
    2: i32 channelId;

    /**
    * @FieldDoc(
     *     description = "channelPoiCodes",
    *     example = "渠道门店ID（三方）"
    * )
    */
    3: list<string> channelPoiCodes;

    /**
     * @FieldDoc(
     *     description = "appid",
     *     example = ""
     * )
     */
    4: i64 appId;

}