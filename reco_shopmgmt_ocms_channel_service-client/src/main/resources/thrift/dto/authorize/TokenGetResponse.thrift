namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"
include "TokenInfo.thrift"

/**
 * @TypeDoc(
 *     description = "活动信息响应数据"
 * )
 */
struct TokenGetResponse {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = {}
     * )
     */
    1: optional ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "token列表",
     *     example = {}
     * )
     */
    2: optional list<TokenInfo.TokenInfo> tokenList;
}