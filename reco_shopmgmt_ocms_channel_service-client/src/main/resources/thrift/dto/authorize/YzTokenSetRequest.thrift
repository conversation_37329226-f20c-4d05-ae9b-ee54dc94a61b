namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

/**
 * @TypeDoc(
 *     description = "有赞token设置请求"
 * )
 */
struct YzTokenSetRequest {

    /**
     * @FieldDoc(
     *     description = "租户id",
     *     example = ""
     * )
     */
    1: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = ""
     * )
     */
    2: i64 poiId;

    /**
     * @FieldDoc(
     *     description = "类型1-总店，2-子店",
     *     example = ""
     * )
     */
    3: i32 type;

    /**
    * @FieldDoc(
     *     description = "grantId",
    *     example = ""
    * )
    */
    4: string grantId;


    /**
    * @FieldDoc(
     *     description = "token",
    *     example = ""
    * )
    */
    5: string token;

    /**
    * @FieldDoc(
     *     description = "refreshToken",
    *     example = ""
    * )
    */
    6: string refreshToken;


    /**
     * @FieldDoc(
     *     description = "过期时间",
     *     example = ""
     * )
     */
    7: i64 expires;

    /**
     * @FieldDoc(
     *     description = "存储时间",
     *     example = ""
     * )
     */
    8: i64 timestamp;

    /**
    * @FieldDoc(
     *     description = "clientId",
    *     example = ""
    * )
    */
    9: string clientId;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = ""
     * )
     */
    10: i32 channelId;

}