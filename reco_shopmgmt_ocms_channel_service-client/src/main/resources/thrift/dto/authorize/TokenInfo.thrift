namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

/**
 * @TypeDoc(
 *     description = "保存活动返回数据"
 * )
 */
struct TokenInfo {

    /**
    * @FieldDoc(
     *     description = "channelPoiCode",
    *     example = ""
    * )
    */
    1: optional string channelPoiCode;


    /**
    * @FieldDoc(
     *     description = "token",
    *     example = ""
    * )
    */
    2: optional string token;

    /**
    * @FieldDoc(
     *     description = "refreshToken",
    *     example = ""
    * )
    */
    3: optional string refreshToken;


    /**
     * @FieldDoc(
     *     description = "过期时间",
     *     example = ""
     * )
     */
    4: optional i64 expires;

    /**
     * @FieldDoc(
     *     description = "刷新token过期时间",
     *     example = ""
     * )
     */
    5: optional i64 refreshExpires;
}