namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto
include "ResultStatus.thrift"

struct YouzanIsvAuthRequest{
        /**
         * @FieldDoc(
         *     description = "appkey",
         *     example = ""
         * )
         */
        1: string appkey;

        /**
         * @FieldDoc(
         *     description = "secret",
         *     example = ""
         * )
         */
        2: string secret;


        /**
        * @FieldDoc(
         *     description = "授权码",
        *     example = ""
        * )
        */
        3: string code;


}

struct YouzanIsvTokenDTO{
        /**
        * @FieldDoc(
        *     description = "accessToken",
        *     example = ""
        * )
        */
        1: required string accessToken;

        /**
        * @FieldDoc(
        *     description = "refreshToken",
        *     example = ""
        * )
        */
        2: required string refreshToken;


        /**
        * @FieldDoc(
        *     description = "accessTokenGrantDate",
        *     example = ""
        * )
        */
        3: required i64 accessTokenGrantDate;

        /**
        * @FieldDoc(
        *     description = "accessTokenExpireDate",
        *     example = ""
        * )
        */
        4: required i64 accessTokenExpireDate;
        /**
        * @FieldDoc(
        *     description = "refreshTokenGrantDate",
        *     example = ""
        * )
        */
        5: required i64 refreshTokenGrantDate;
        /**
        * @FieldDoc(
        *     description = "refreshTokenExpireDate",
        *     example = ""
        * )
        */
        6: required i64 refreshTokenExpireDate;


        /**
        * @FieldDoc(
        *     description = "shopId",
        *     example = ""
        * )
        */
        7: required string shopId;

}

struct YouzanIsvAuthResponse{

        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = {}
         * )
         */
        1: required ResultStatus.ResultStatus status;

       /**
        * @FieldDoc(
        *     description = "token",
        *     example = ""
        * )
        */
       2: required YouzanIsvTokenDTO data;

}

struct YouzanIsvRefreshTokenRequest{
        /**
         * @FieldDoc(
         *     description = "appkey",
         *     example = ""
         * )
         */
        1: string appkey;

        /**
         * @FieldDoc(
         *     description = "secret",
         *     example = ""
         * )
         */
        2: string secret;


        /**
        * @FieldDoc(
         *     description = "refreshToken",
        *     example = ""
        * )
        */
        3: string refreshToken;


}


struct YouzanIsvRefreshTokenResponse{

        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = {}
         * )
         */
        1: required ResultStatus.ResultStatus status;

       /**
        * @FieldDoc(
        *     description = "data",
        *     example = ""
        * )
        */
       2: required YouzanIsvTokenDTO data;

}