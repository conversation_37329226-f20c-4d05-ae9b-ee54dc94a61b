namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto
include "ResultStatus.thrift"


struct TxdTokenInfoDTO{

     /**
     * @FieldDoc(
     *     description = "淘鲜达商家编码",
     *     example = {}
     * )
     */
    1: required string merchantCode;

    /**
     * @FieldDoc(
     *     description = "access_token",
     *     example = {}
     * )
     */
    2: required string accessToken;

    /**
     * @FieldDoc(
     *     description = "access_tokens授权时间",
     *     example = {}
     * )
     */
    3: required i64 accessTokenGrantDate;


    /**
     * @FieldDoc(
     *     description = "access_token过期时间",
     *     example = {}
     * )
     */
    4: required i64 accessTokenExpireDate;

    /**
     * @FieldDoc(
     *     description = "refreshToken",
     *     example = {}
     * )
     */
    5: required string refreshToken;

    /**
     * @FieldDoc(
     *     description = "refreshTokenGrantDate",
     *     example = {}
     * )
     */
    6: required i64 refreshTokenGrantDate;

    /**
     * @FieldDoc(
     *     description = "refreshTokenExpireDate",
     *     example = {}
     * )
     */
    7: required i64 refreshTokenExpireDate;
}

struct TxdTokenResponse{
     /**
     * @FieldDoc(
     *     description = "状态",
     *     example = {}
     * )
     */
    1: required ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "token信息",
     *     example = {}
     * )
     */
    2: required TxdTokenInfoDTO data;


}