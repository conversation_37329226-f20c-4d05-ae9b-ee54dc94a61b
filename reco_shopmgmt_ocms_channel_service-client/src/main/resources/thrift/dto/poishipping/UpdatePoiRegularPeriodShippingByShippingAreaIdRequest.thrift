namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping

include "UpdatePoiShippingRequest.thrift"
/**
 * @TypeDoc(
 *     description = "更新门店配送范围请求"
 * )
 */
struct UpdatePoiRegularPeriodShippingByShippingAreaIdRequest {

        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = ""
         * )
         */
        1: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = ""
         * )
         */
        2: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = ""
         * )
         */
        3: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "起送价 单位:元",
         *     example = ""
         * )
         */
        4: double minOrderPrice;

        /**
         * @FieldDoc(
         *     description = "配送费 单位:元",
         *     example = ""
         * )
         */
        5: double shippingFee;

        /**
         * @FieldDoc(
         *     description = "配送范围坐标集合",
         *     example = ""
         * )
         */
        6: list<UpdatePoiShippingRequest.Coordinate> coordinates;

        /**
        * @FieldDoc(
        *     description = "业务方配送范围id 修改配送范围时可以不传 新增配送范围时必传",
        *     example = ""
        * )
        */
        7: optional string appShippingCode;

        /**
        * @FieldDoc(
        *     description = "美团配送范围id 修改配送范围时必传 新增配送范围时不传",
        *     example = ""
        * )
        */
        8: optional i64 shippingAreaId;

}