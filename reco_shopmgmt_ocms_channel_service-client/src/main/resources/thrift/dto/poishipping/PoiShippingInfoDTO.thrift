namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping

struct Coordination {
        /**
         * @FieldDoc(
         *     description = "纬度",
         *     example = ""
         * )
         */
        1: i64 x;

        /**
         * @FieldDoc(
         *     description = "经度",
         *     example = ""
         * )
         */
        2: i64 y;
}

struct PoiShippingInfoDTO {
        /**
         * @FieldDoc(
         *     description = "APP方提供的配送范围id",
         *     example = ""
         * )
         */
        1: string appShippingCode;

        /**
         * @FieldDoc(
         *     description = "配送范围边界坐标点",
         *     example = ""
         * )
         */
        2: list<Coordination> area;

        /**
         * @FieldDoc(
         *     description = "该配送区域的起送价，单位是元",
         *     example = ""
         * )
         */
        3: double minPrice;

        /**
         * @FieldDoc(
         *     description = "该配送区域的配送费，单位是元",
         *     example = ""
         * )
         */
        4: double shippingFee;

        /**
         * @FieldDoc(
         *     description = "配置范围类型，1表示多个配送范围由多个多边形组成",
         *     example = ""
         * )
         */
        5: i32 type;

        6: optional i64 mtShippingId;
}