namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping

struct Coordinate {

        /**
         * @FieldDoc(
         *     description = "精度",
         *     example = ""
         * )
         */
        1: double longitude;

        /**
         * @FieldDoc(
         *     description = "纬度",
         *     example = ""
         * )
         */
        2: double latitude;
}

/**
 * @TypeDoc(
 *     description = "重置门店配送范围请求"
 * )
 */
struct ResetPoiShippingRequest {

        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = ""
         * )
         */
        1: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = ""
         * )
         */
        2: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = ""
         * )
         */
        3: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "起送费 美团、饿了么渠道必传",
         *     example = ""
         * )
         */
        4: i32 minOrderPrice;

        /**
         * @FieldDoc(
         *     description = "配送费 饿了么渠道必传",
         *     example = ""
         * )
         */
        5: i32 shippingFee;

        /**
         * @FieldDoc(
         *     description = "配送时长 饿了么渠道必传",
         *     example = ""
         * )
         */
        6: i32 shippingTime;

        /**
         * @FieldDoc(
         *     description = "配送范围坐标集合",
         *     example = ""
         * )
         */
        7: list<Coordinate> coordinates;

        /**
         * @FieldDoc(
         *     description = "配送范围id; 非必传，默认1",
         *     example = ""
         * )
         */
        8: optional string app_shipping_code;

}