namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping

include "UpdatePoiShippingRequest.thrift"
struct PoiShippingAreaInfoDTO {
        /**
         * @FieldDoc(
         *     description = "APP方提供的配送范围id",
         *     example = ""
         * )
         */
        1: string appShippingCode;

        /**
         * @FieldDoc(
         *     description = "配送范围边界坐标点",
         *     example = ""
         * )
         */
        2: list<UpdatePoiShippingRequest.Coordinate> area;

        /**
         * @FieldDoc(
         *     description = "该配送区域的起送价，单位是元",
         *     example = ""
         * )
         */
        3: double minPrice;

        /**
         * @FieldDoc(
         *     description = "该配送区域的配送费，单位是元",
         *     example = ""
         * )
         */
        4: double shippingFee;

        /**
         * @FieldDoc(
         *     description = "配置范围类型，1表示多个配送范围由多个多边形组成",
         *     example = ""
         * )
         */
        5: i32 type;

        /**
         * @FieldDoc(
         *     description = "配送范围唯一键 更新美团渠道时必传",
         *     example = ""
         * )
         */
        6: i64 shippingAreaId;

        /**
         * @FieldDoc(
         *     description = "渠道门店id",
         *     example = ""
         * )
         */
        7: i64 channelPoiId;

        /**
         * @FieldDoc(
         *     description = "时段名称",
         *     example = ""
         * )
         */
        8: string shippingPeriodName;

        /**
         * @FieldDoc(
         *     description = "生效时段开始时间",
         *     example = ""
         * )
         */
        9: string timeRangeBegin;

        /**
         * @FieldDoc(
         *     description = "生效时段结束时间",
         *     example = ""
         * )
         */
        10: string timeRangeEnd;

        /**
        * @FieldDoc(
        *     description = "渠道id",
        *     example = ""
        * )
        */
        11: i32 channelId;


        /**
        * @FieldDoc(
        *     description = "中台门店id",
        *     example = ""
        * )
        */
        12: i64 poiId;

}