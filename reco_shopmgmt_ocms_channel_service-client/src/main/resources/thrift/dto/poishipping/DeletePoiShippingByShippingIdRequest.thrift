namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping

/**
 * @TypeDoc(
 *     description = "通过shippingAreaId删除门店配送范围请求"
 * )
 */
struct DeletePoiShippingByShippingIdRequest {

        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = ""
         * )
         */
        1: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = ""
         * )
         */
        2: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = ""
         * )
         */
        3: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "配送范围id; 必传",
         *     example = ""
         * )
         */
        4: i64 shippingAreaId;

}