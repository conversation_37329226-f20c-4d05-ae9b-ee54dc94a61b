namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping

/**
 * @TypeDoc(
 *     description = "批量更新门店配送范围请求"
 * )
 */

 struct DistanceMarkUpRule {

    1: string distance;

    2: string markupNum;

 }

struct WeightMarkUpRule {

    1: string weight;

    2: string step;

    3: string markupNum;

}

struct TimeMarkUpRule {

    1: string timeRange;

    2: string markupNum;

}

struct PoiShippingFeeInfo {

    1: string minimumDeliveryAmount;

    2: string baseDeliveryFee;

    3: DistanceMarkUpRule distanceMarkUpRule;

    4: WeightMarkUpRule weightMarkUpRule;

    5: TimeMarkUpRule timeMarkUpRule;

}

struct BatchUpdatePoiShippingFeeRequest {

        /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = ""
     * )
     */
        1: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = ""
         * )
         */
        2: list<i64> storeIds;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = ""
         * )
         */
        3: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "渠道配送费信息",
         *     example = ""
         * )
         */
        4: PoiShippingFeeInfo poiShippingFeeInfo;

}