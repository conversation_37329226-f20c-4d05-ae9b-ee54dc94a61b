namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping

include "UpdatePoiShippingRequest.thrift"
/**
 * @TypeDoc(
 *     description = "批量更新门店配送范围请求"
 * )
 */

struct PoiShippingInfo {

        1: i32 appShippingCode;

        2: i32 minPrice;

        3: list<UpdatePoiShippingRequest.Coordinate> coordinateList;

        //<------以下为新增字段------>
        4: optional i64 mtShippingId;

        //该配送区域的配送费，单位是元。
        5: optional double shippingFee;

        //按距离加价规则的执行类型：1-启用，2-不启用，3-保持原值 如果门店没有初始规则且不需要，则不传即可
        6: optional i32 distanceMarkupExecuteType;
        //按距离加价， 1.上传多个规则时，需按从小到大顺序上传 2.最多支持设置5个加价规则 3.传启用则新上传的规则覆盖原有规则 4.传不启用则删除已有规则
        7: optional string distanceMarkupFactors;

        //按重量加价规则的执行类型：1-启用，2-不启用，3-保持原值 如果门店没有初始规则且不需要，则不传即可
        8: optional i32 weightMarkupExecuteType;
        //按重量加价， 1.上传多个规则时，需按从小到大顺序上传 2.最多支持设置5个加价规则 3.传启用则新上传的规则覆盖原有规则 4.传不启用则删除已有规则
        9: optional string weightMarkupFactors;

        //按时段加价规则的执行类型：1-启用，2-不启用，3-保持原值 如果门店没有初始规则且不需要，则不传即可
        10: optional i32 timeMarkupExecuteType;
        //按时段加价， 1.最多支持设置5个加价规则 2.传启用则新上传的规则覆盖原有规则 3.传不启用则删除已有规则
        11: optional string timeMarkupFactors;

        12: optional double minPriceDouble;

}

struct BatchUpdatePoiShippingRequest {

        /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = ""
     * )
     */
        1: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = ""
         * )
         */
        2: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = ""
         * )
         */
        3: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "渠道配送信息",
         *     example = ""
         * )
         */
        4: list<PoiShippingInfo> poiShippingInfo;

        /**
         * @FieldDoc(
         *     description = "渠道配送信息",
         *     example = ""
         * )
         */
        5: optional list<string> logisticsCodes;

}