namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.wechat



/**
 * @TypeDoc(
 *     description = "微信公众号消息回调接口请求参数"
 * )
 */
struct WeChatNotifyRequest {
    /**
     * @FieldDoc(
     *     description = "开发者微信号",
     *     example = "1"
     * )
     */
    1: string toUserName;
    /**
     * @FieldDoc(
     *     description = "发送方帐号（一个OpenID）",
     *     example = "1"
     * )
     */
    2: string fromUserName;
    /**
     * @FieldDoc(
     *     description = "消息创建时间",
     *     example = "cmd"
     * )
     */
    3: i64 createTime;
    /**
     * @FieldDoc(
     *     description = "消息类型",
     *     example = "image"
     * )
     */
    4: string msgType;
    /**
     * @FieldDoc(
     *     description = "文本消息内容",
     *     example = "1"
     * )
     */
    5: string content;
    /**
     * @FieldDoc(
     *     description = "消息id，64位整型",
     *     example = "admin"
     * )
     */
    6: i64 msgId;


    /**
     * @FieldDoc(
     *     description = "消息类型",
     *     example = "admin"
     * )
     */
    7: string event;

    /**
     * @FieldDoc(
     *     description = "纬度",
     *     example = "admin"
     * )
     */
    8: double latitude;


    /**
    * @FieldDoc(
    *     description = "消息类型",
    *     example = "admin"
    * )
    */
    9: double longitude;

    /**
    * @FieldDoc(
    *     description = "消息类型",
    *     example = "admin"
    * )
    */
    10: double precision;


    /**
    * @FieldDoc(
    *     description = "签名",
    *     example = "134"
    * )
    */
    11: string signature;


    /**
   * @FieldDoc(
   *     description = "时间戳",
   *     example = "134"
   * )
   */
    12: i64 timestamp;


    /**
   * @FieldDoc(
   *     description = "随机数",
   *     example = "134"
   * )
   */
    13: string nonce;


    /**
   * @FieldDoc(
   *     description = "回包",
   *     example = "134"
   * )
   */
    14: string echostr;


    /**
   * @FieldDoc(
   *     description = "微信公众号唯一ID和fromUserName相同",
   *     example = "134"
   * )
   */
    15: string openId;


    /**
       * @FieldDoc(
       *     description = "微信公众号唯一ID和fromUserName相同",
       *     example = "134"
       * )
       */
    16: string eventKey;


}

