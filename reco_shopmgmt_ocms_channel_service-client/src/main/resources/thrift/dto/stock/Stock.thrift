namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "BaseRequest.thrift"
include "ResultStatus.thrift"
include "PageInfo.thrift"

/**
 * @TypeDoc(
 *     description = "库存信息"
 * )
 */
struct SkuStockDTO{

    /**
     * @FieldDoc(
     *     description = "商家SKU编码",
     *     example = "10"
     * )
     */
    1: string skuId;

    /**
     * @FieldDoc(
     *     description = "库存",
     *     example = "1"
     * )
     */
    2:  optional double stockQty;

    /**
     * @FieldDoc(
     *     description = "时间戳",
     *     example = "1545714137000"
     * )
     */
    3: i64 timestamp;

    /**
     * @FieldDoc(
     *     description = "操作人",
     *     example = "jdpin"
     * )
     */
    4: string operator;

    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = "1"
     * )
     */
    5: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "渠道商品编码",
     *     example = "10"
     * )
     */
    6: string channelSkuId;
}

/**
 * @TypeDoc(
 *     description = "库存信息"
 * )
 */
struct SkuInSpuStockDTO{

    /**
     * @FieldDoc(
     *     description = "商家商品SKU编码",
     *     example = "10"
     * )
     */
    1: string customSkuId;

    /**
     * @FieldDoc(
     *     description = "时间戳",
     *     example = "1545714137000"
     * )
     */
    2: i64 timestamp;

    /**
     * @FieldDoc(
     *     description = "操作人",
     *     example = "jdpin"
     * )
     */
    3: string operator;

    /**
     * @FieldDoc(
     *     description = "库存",
     *     example = "1"
     * )
     */
    4:  optional double stockQty;

    /**
     * @FieldDoc(
     *     description = "商品类型，1单规格 2多规格",
     *     example = "1"
     * )
     */
    5:  i32 specType;

    /**
     * @FieldDoc(
     *     description = "渠道定义的商品编码",
     *     example = "13923"
     * )
     */
    6:  optional string channelSkuId;
}

/**
 * @TypeDoc(
 *     description = "库存信息"
 * )
 */
struct SpuStockDTO{

    /**
 * @FieldDoc(
 *     description = "门店ID",
 *     example = "1"
 * )
 */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商家商品SPU编码",
     *     example = "10"
     * )
     */
    2: string customSpuId;

    /**
     * @FieldDoc(
     *     description = "时间戳",
     *     example = "1545714137000"
     * )
     */
    3: list<SkuInSpuStockDTO> skuStockInfo;

    /**
     * @FieldDoc(
     *     description = "渠道总店商品SPU编码，针对有赞渠道",
     *     example = "10"
     * )
     */
    4: optional string merchantChannelSpuId;

     /**
      * @FieldDoc(
      *     description = "渠道商品SpuId",
      *     example = "10"
      *
      * )
      */
     5: optional string channelSpuId;
}

/**
 * @TypeDoc(
 *     description = "多渠道多门店库存信息"
 * )
 */
struct SkuStockMultiChannelDTO{
    
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    1: i32 channelId;
    
    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = "1"
     * )
     */
    2: i64 storeId;
    
    /**
     * @FieldDoc(
     *     description = "商家SKU编码",
     *     example = "10"
     * )
     */
    3: string skuId;
    
    /**
     * @FieldDoc(
     *     description = "库存",
     *     example = "1"
     * )
     */
    4: optional double stockQty;
    
    /**
     * @FieldDoc(
     *     description = "时间戳",
     *     example = "1545714137000"
     * )
     */
    5: i64 timestamp;
    
    /**
     * @FieldDoc(
     *     description = "操作人",
     *     example = "jdpin"
     * )
     */
    6: string operator;
    
    /**
     * @FieldDoc(
     *     description = "渠道商品编码",
     *     example = "10"
     * )
     */
    7: string channelSkuId;
}

/**
 * @TypeDoc(
 *     description = "库存信息接口入参"
 * )
 */
struct SkuStockRequest {

        /**
         * @FieldDoc(
         *     description = "租户渠道Id",
         *     example = "1"
         * )
         */
        1: BaseRequest.BaseRequest baseInfo;

        /**
         * @FieldDoc(
         *     description = "库存信息列表",
         *     example = "1"
         *
         * )
         */
        2: list<SkuStockDTO> skuStockList;
}

/**
 * @TypeDoc(
 *     description = "库存信息接口入参"
 * )
 */
struct SpuStockRequest {

    /**
         * @FieldDoc(
         *     description = "租户渠道Id",
         *     example = "1"
         * )
         */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "库存信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<SpuStockDTO> paramList;

    /**
     * @FieldDoc(
     *     description = "是否是重试流量",
     *     example = "false"
     * )
     */
    3: optional bool retryFlag;
}

/**
 * @TypeDoc(
 *     description = "多渠道多门店库存接口入参"
 * )
 */
struct SkuStockMultiChannelRequest {
    
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "渠道门店库存信息列表",
     *     example = "1"
     *
     * )
     */
    2: list<SkuStockMultiChannelDTO> skuStockList;
}

/**
 * @TypeDoc(
 *     description = "支持MT多规格、有赞渠道的多渠道多门店库存信息"
 * )
 */
struct YouzanUpdateStockDTO{

        /**
         * @FieldDoc(
         *     description = "商家SKU编码",
         *     example = "10"
         * )
         */
        1: string skuId;

        /**
         * @FieldDoc(
         *     description = "库存",
         *     example = "1"
         * )
         */
        2: double stockQty;

}

struct YouzanUpdateStockRequest {
        /**
   * @FieldDoc(
   *     description = "租户ID",
   *     example = "1"
   * )
   */
        1: i64 tenantId;

        2: i64 storeId;

        3: i32 channelId;

        4: string warehouseCode;

        5: string sourceOrderNo;

        6: list<YouzanUpdateStockDTO> stockParamList;

        /**
 * @FieldDoc(
 *     description = "操作人",
 *     example = "jdpin"
 * )
 */
        7: string operator;
}

/**
 * @TypeDoc(
 *     description = "渠道库存信息"
 * )
 */
struct ChannelSkuStockDTO{

    /**
     * @FieldDoc(
     *     description = "商家SKU编码",
     *     example = "10"
     * )
     */
    1: string skuId;

    /**
     * @FieldDoc(
     *     description = "渠道商品编码",
     *     example = "10"
     * )
     */
    2: string customSkuId;

    /**
     * @FieldDoc(
     *     description = "商品SPU编码",
     *     example = "10"
     * )
     */
    3: string spuId;

    /**
     * @FieldDoc(
     *     description = "渠道商品SPU编码",
     *     example = "10"
     * )
     */
    4: string customSpuId;

    /**
     * @FieldDoc(
     *     description = "库存",
     *     example = "1"
     * )
     */
    5:  optional double stockQty;

    /**
     * @FieldDoc(
     *     description = "商品名称补充语",
     *     example = ""
     * )
     */
    6: string nameSupplement;

    /**
     * @FieldDoc(
     *     description = "商品名称补充语顺序。0：补充语后置；1：补充语前置。（适用于医药健康门店商品）",
     *     example = "1"
     * )
     */
    7: i32 nameSupplementSeq;
}

/**
 * @TypeDoc(
 *     description = "批量拉取商品库存请求"
 * )
 */
struct BatchGetStockInfoRequest {
    /**
         * @FieldDoc(
         *     description = "请求基本信息",
         *     example = {}
         * )
         */
    1: BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    2: i64 storeId;

     /**
     * @FieldDoc(
     *     description = "页码",
     *     example = ""
     * )
     */
    3: i32 pageNum = 1;

        /**
         * @FieldDoc(
         *     description = "每页大小",
         *     example = ""
         * )
         */
    4: i32 pageSize = 20;

    /**
     * @FieldDoc(
     *     description = "商品id游标，饿了么渠道查询场景需要传递，第一次传入0，后续每次使用返回结果中的sku_id_offset当作下次的入参",
     *     example = ""
     * )
     */
    5: string skuIdOffset;
}

/**
 * @TypeDoc(
 *     description = "批量拉取SKU库存信息"
 * )
 */
struct BatchGetStockInfoResponse {
    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
    * @FieldDoc(
    *     description = "分页信息",
    *     example = "{}"
    * )
    */
    2: PageInfo.PageInfo pageInfo;

    /**
     * @FieldDoc(
     *     description = "商品id游标，饿了么渠道查询场景返回",
     *     example = ""
     * )
     */
    3: string skuIdOffset;

    /**
     * @FieldDoc(
     *     description = "查询类型：0、按pageinfo查询；1、按游标查询。参考QueryTypeEnum",
     *     example = ""
     * )
     */
    4: i32 queryType;

    /**
     * @FieldDoc(
     *     description = "商品Sku库存信息列表",
     *     example = "1"
     *
     * )
     */
    5: list<ChannelSkuStockDTO> skuStocks;
}
