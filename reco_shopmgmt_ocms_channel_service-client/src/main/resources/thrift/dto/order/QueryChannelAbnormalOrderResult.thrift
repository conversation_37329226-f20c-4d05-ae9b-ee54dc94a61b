namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order
include "ResultStatus.thrift"


struct AbnormalOrderWithChannel{

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        1: i32 channelId;
        /**
         * @FieldDoc(
         *     description = "渠道订单号",
         *     example = "channelOrderDetail"
         * )
         */
        2: string channelOrderId;

}

struct QueryChannelAbnormalOrderResult{
        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus status;



        /**
         * @FieldDoc(
         *     description = "渠道订单号列表信息",
         *     example = "channelOrderDetail"
         * )
         */
        2: list<string> channelOrderIdList;

        /**
         * @FieldDoc(
         *     description = "带有渠道信息订单号列表信息",
         *     example = "channelOrderDetail"
         * )
         */
        3: optional list<AbnormalOrderWithChannel> orderWithChannelList;

        /**
         * @FieldDoc(
         *     description = "总个数",
         *     example = "totalCount"
         * )
         */
        4: optional i64 totalCount;
}