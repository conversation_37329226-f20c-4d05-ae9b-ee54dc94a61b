namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "RefundItemAuditRequesst.thrift"
/**
 * @TypeDoc(
 *     description = "订单确认退款请求回调服务请求参数"
 * )
 */
struct AgreeRefundRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "退款原因",
     *     example = "因***原因确认退款"
     * )
     */
    4: string reason;
    /**
     * @FieldDoc(
     *     description = "售前、售后申请（京东必传）",
     *     example = "1"
     * )
     */
    5: i32 afsApplyType;
        /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "1"
     * )
     */
    6: i64 storeId;
         /**
     * @FieldDoc(
     *     description = "本次退款申请的退款id，可用于商家区分多次部分退款。",
     *     example = "1"
     * )
     */
    7: string afterSaleId;
     /**
     * @FieldDoc(
     *     description = "是否返货",
     *     example = "true"
     * )
     */
    8: optional bool isReturnGoods;
    /**
     * @FieldDoc(
     *     description = "渠道商品明细(目前仅淘鲜达使用)",
     *     example = "123"
     * )
     */
    9: optional list<RefundItemAuditRequesst.RefundItemAuditRequest> channelRefundItem;
}