namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "美团配送中返货，返货单状态推送数据"
 * )
 */
struct MtReturnDuringDeliveryRequest {
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "27061900338318741"
     * )
     */
    1: string orderId;
    /**
     * @FieldDoc(
     *     description = "返货配送单id",
     *     example = "30487784900"
     * )
     */
    2: string dispatchOrderId;
    /**
     * @FieldDoc(
     *     description = "返货发起方 参考值： 1：用户 2：骑手",
     *     example = "1"
     * )
     */
    3: string opRole;
    /**
     * @FieldDoc(
     *     description = "渠道CODE唯一标识",
     *     example = "1"
     * )
     */
    4: string channelCode;
    /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
    5: string tenantAppId;
    /**
     * @FieldDoc(
     *     description = "商家验货结果，当商家收到货并发起验货之后有值 参考值： 1：商家验收通过 2：商家验收不通过 3：系统自动验收通过",
     *     example = "1"
     * )
     */
    6:i32 poiCheckResult
        /**
     * @FieldDoc(
     *     description = "返货配送单状态 参考值： 31：返货中 32：返货成功 33：返货失败",
     *     example = "1"
     * )
     */
    7:i32 status
    /**
     * @FieldDoc(
     *     description = "退单Id",
     *     example = "1"
     * )
     */
    8:string refundId

}