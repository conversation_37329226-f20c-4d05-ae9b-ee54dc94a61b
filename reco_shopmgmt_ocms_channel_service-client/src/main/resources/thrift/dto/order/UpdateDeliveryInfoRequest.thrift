namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "OrderDeliveryStatusEnum.thrift"

struct UpdateDeliveryInfoRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        2: i32 channelId;
        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = "1"
         * )
         */
        3: i64 shopId;
        /**
         * @FieldDoc(
         *     description = "订单ID",
         *     example = "19472638492372738"
         * )
         */
        4: string orderId;

        /**
         * @FieldDoc(
         *     description = "骑手姓名",
         *     example = ""
         * )
         */
        5: string riderName;

        /**
         * @FieldDoc(
         *     description = "骑手电话",
         *     example = ""
         * )
         */
        6: string riderPhone;

        /**
         * @FieldDoc(
         *     description = "配送状态 参见DeliveryStatus",
         *     example = ""
         * )
         */
        7: i32 status

        /**
         * @FieldDoc(
         *     description = "骑手位置纬度",
         *     example = ""
         * )
         */
        8: double latitude;

        /**
         * @FieldDoc(
         *     description = "骑手位置经度",
         *     example = ""
         * )
         */
        9: double longitude;

        /**
        * @FieldDoc(
        *     description = "配送渠道",
        *     example = ""
        * )
        */
        10: i32 deliveryChannelId;

        /**
        * @FieldDoc(
        *     description = "是否只同步骑手位置",
        *     example = ""
        * )
        */
        11: optional bool onlySyncRiderPosition = false;

        /**
        * @FieldDoc(
        *     description = "赋能内部运单id",
        *     example = ""
        * )
        */
        12: string deliveryOrderId;
        /**
        * @FieldDoc(
        *     description = "运单变更类型，不同业务定义不一样，其中歪马：0-更新运单状态以及骑手位置（onlySyncRiderPosition=true才更新骑手位置），1-暂停配送；2-继续配送",
        *     example = ""
        * )
        */
        13: i32 changeType = 0;
        /**
        * @FieldDoc(
        *     description = "扩展字段，其值取决于changeType",
        *     example = ""
        * )
        */
        14: string extJson;

        /**
        * @FieldDoc(
        *     description = "渠道ID",
        *     example = ""
        * )
        */
        15: i32 orderBizType;

        /**
        * @FieldDoc(
        *     description = "取消原因，仅配送状态为已取消时生效",
        *     example = ""
        * )
        */
        16: optional string cancelReason;

        /**
        * @FieldDoc(
        *     description = "取消原因码，仅配送状态为已取消时生效",
        *     example = ""
        * )
        */
        17: optional i32 cancelReasonCode = 0;

}