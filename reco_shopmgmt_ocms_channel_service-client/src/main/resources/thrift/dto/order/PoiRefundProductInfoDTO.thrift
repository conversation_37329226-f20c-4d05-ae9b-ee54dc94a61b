namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

struct RefundProductInfoDTO {

    /**
     * @FieldDoc(
     *     description = "商品sku",
     * )
     * @Deprecated  见customSkuId, 由于美团渠道skuId使用的app_food_code，之后会切换成sku_id, 订单部分不会对历史数据进行清洗。所以沿用skuId字段将区分不了到底是真的sku_id还是老的app_food_code
     */
    1: string skuId;

    /**
     * @FieldDoc(
     *     description = "退款数量"
     * )
     */
    2: i32 count;

    /**
     * @FieldDoc(
     *     description = "实际拣货重量,克"
     * )
     */
    3: optional double actualWeight;

    /**
     * @FieldDoc(
     *     description = "商品spu"
     * )
     */
    4: optional string customSpu;


    /**
     * @FieldDoc(
     *     description = "商品sku"
     * )
     */
    5: optional string customSkuId;

    /**
     * @FieldDoc(
     *     description = "商品行id"
     * )
     */
    6: optional i64 itemId;

    /**
     * @FieldDoc(
     *     description = "售价，分"
     * )
     */
    7: optional i32 salePrice;

    /**
     * @FieldDoc(
     *     description = "upc"
     * )
     */
    8: optional string upc;

    /**
     * @FieldDoc(
     *     description = "三方平台itemId"
     * )
     */
    9: optional string thirdPartItemId;

    /**
     * @FieldDoc(
     *     description = "三方平台spuId"
     * )
     */
    10: optional string thirdPartSpuId;

    /**
     * @FieldDoc(
     *     description = "渠道订单明细ID"
     * )
     */
    11: optional string channelOrderItemId;

    /**
     * @FieldDoc(
     *     description = "商品退款金额(分)"
     * )
     */
    12: optional i32 refundAmount;

    /**
     * @FieldDoc(
     *     description = "商品总实付总金额(分)-目前有赞使用"
     * )
     */
    13: optional i32 totalPayAmtMinusDiscount;

    /**
     * @FieldDoc(
     *     description = "商品名称",
     * )
     */
    14: optional string skuName;

    /**
     * @FieldDoc(
     *     description = "平台商品Id(饿了么用)",
     * )
     */
    15: optional string baiduProductId;
}