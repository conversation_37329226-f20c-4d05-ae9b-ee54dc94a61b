namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "退差调整商品订单级优惠数据"
 * )
 */
struct RefDiffAdjustSkuDiscountDTO {
        /**
         * @FieldDoc(
         *     description = "一级优惠类型"
         * )
         */
        1: optional i32 discountType;
        /**
         * @FieldDoc(
         *     description = "二级优惠类型"
         * )
         */
        2: optional i32 detailDiscountType;
        /**
         * @FieldDoc(
         *     description = "优惠编号"
         * )
         */
        3: optional string discountCodes;
        /**
         * @FieldDoc(
         *     description = "优惠金额（单位：分）"
         * )
         */
        4: optional i64 skuDiscountMoney;
        /**
         * @FieldDoc(
         *     description = "商家承担比例"
         * )
         */
        5: optional i32 venderShareProportion;
        /**
         * @FieldDoc(
         *     description = "平台承担比例"
         * )
         */
        6: optional i32 platShareProportion;
        /**
         * @FieldDoc(
         *     description = "商家承担金额（单位：分）"
         * )
         */
        7: optional i64 venderPayMoney;
        /**
         * @FieldDoc(
         *     description = "平台承担金额（单位：分）"
         * )
         */
        8: optional i64 platPayMoney;
}

/**
 * @TypeDoc(
 *     description = "退差调整商品"
 * )
 */
struct RefDiffAdjustSkuDTO {
        /**
         * @FieldDoc(
         *     description = "商品skuId"
         * )
         */
        1: optional string skuId;

        /**
         * @FieldDoc(
         *     description = "外部商家商品skuId"
         * )
         */
        2: optional string outSkuId;

        /**
         * @FieldDoc(
         *     description = "商品名称"
         * )
         */
        3: optional string wareName;

        /**
         * @FieldDoc(
         *     description = "京东价（单品优惠后的价格，单位：分）"
         * )
         */
        4: optional i64 payPrice;

        /**
         * @FieldDoc(
         *     description = "商品的标重（单位：g）"
         * )
         */
        5: optional double skuWeight;

        /**
         * @FieldDoc(
         *     description = "商品的实捡重量（单位：g）"
         * )
         */
        6: optional double skuActualWeight;

        /**
         * @FieldDoc(
         *     description = "退差调整的重量（单位：g）"
         * )
         */
        7: optional double skuCutWeight;

        /**
         * @FieldDoc(
         *     description = "退差京东价（单位：分）"
         * )
         */
        8: optional i64 cutPayPrice;

        /**
         * @FieldDoc(
         *     description = "实捡京东价（单位：分）"
         * )
         */
        9: optional i64 actualPayPrice;

        /**
         * @FieldDoc(
         *     description = "退差现金支付金额（单位：分）"
         * )
         */
        10: optional i64 cashMoney;

        /**
         * @FieldDoc(
         *     description = "退差订单级优惠金额（单位：分）"
         * )
         */
        11: optional i64 virtualMoney;

        /**
         * @FieldDoc(
         *     description = "退差到家积分金额（单位：分）"
         * )
         */
        12: optional i64 platformIntegralDeductMoney;

        /**
         * @FieldDoc(
         *     description = "退差礼名卡抵扣货款（单位：分）"
         * )
         */
        13: optional i64 giftCardProductMoney;

        /**
         * @FieldDoc(
         *     description = "单品促销的id"
         * )
         */
        14: optional i64 promotionId;

        /**
         * @FieldDoc(
         *     description = "单品优惠类型"
         * )
         */
        15: optional i32 promotionType;

        /**
         * @FieldDoc(
         *     description = "单品促销商家承担金额（单位：分）"
         * )
         */
        16: optional i64 venderPayMoney;

        /**
         * @FieldDoc(
         *     description = "单品促销平台承担金额（单位：分）"
         * )
         */
        17: optional i64 platPayMoney;

        /**
         * @FieldDoc(
         *     description = "单品促销商家承担比例"
         * )
         */
        18: optional i32 venderShareProportion;

        /**
         * @FieldDoc(
         *     description = "单品促销平台承担比例"
         * )
         */
        19: optional i32 platShareProportion;

        /**
         * @FieldDoc(
         *     description = "份数"
         * )
         */
        20: optional double copiesNum;

        /**
         * @FieldDoc(
         *     description = "退差调整商品订单级优惠集合"
         * )
         */
        21: optional list<RefDiffAdjustSkuDiscountDTO> refDiffAdjustSkuDiscountList;
}

/**
 * @TypeDoc(
 *     description = "退差价逆向单详情"
 * )
 */
struct JddjQueryReverseResultDto {
    /**
     * @FieldDoc(
     *     description = "退差价逆向单号"
     * )
     */
    1: optional string refDiffReverseOrderId;

    /**
     * @FieldDoc(
     *     description = "退差价逆向单对应的订单号"
     * )
     */
    2: optional string orderId;

    /**
     * @FieldDoc(
     *     description = "退差价逆向单创建时间"
     * )
     */
    3: optional string creatTime;

    /**
     * @FieldDoc(
     *     description = "商家id"
     * )
     */
    4: optional string venderId;

    /**
     * @FieldDoc(
     *     description = "商家名称"
     * )
     */
    5: optional string venderName;

    /**
     * @FieldDoc(
     *     description = "退差单总金额(单位：分)"
     * )
     */
    6: optional i64 refDiffMoney;

    /**
     * @FieldDoc(
     *     description = "退差单现金支付金额(单位：分)"
     * )
     */
    7: optional i64 cashMoney;

    /**
     * @FieldDoc(
     *     description = "退差单订单级优惠金额(单位：分)"
     * )
     */
    8: optional i64 virtualMoney;

    /**
     * @FieldDoc(
     *     description = "退差单到家积分金额(单位：分)"
     * )
     */
    9: optional i64 platformIntegralDeductMoney;

    /**
     * @FieldDoc(
     *     description = "退差单到家积分数量"
     * )
     */
    10: optional i64 platformIntegralDeductNum;

    /**
     * @FieldDoc(
     *     description = "退差单礼品卡抵扣货款(单位：分)"
     * )
     */
    11: optional i64 giftCardProductMoney;

    /**
     * @FieldDoc(
     *     description = "差价退款状态（0-初始化、10-退款中、20-退款完成、30-退款失败）"
     * )
     */
    12: optional i32 refDiffStatus;

    /**
     * @FieldDoc(
     *     description = "退差调整商品集合"
     * )
     */
    13: optional list<RefDiffAdjustSkuDTO> refDiffAdjustSkuList;

}