namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "商家转自配送请求参数"
 * )
 */
struct SelfDeliveryRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        2: i32 channelId;
        /**
         * @FieldDoc(
         *     description = "订单ID",
         *     example = "19472638492372738"
         * )
         */
        3: string orderId;
        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = "123"
         * )
         */
        4: optional i64 storeId;

        /**
         * @FieldDoc(
         *     description = "操作人",
         *     example = "操作人"
         * )
         */
        5: optional string operator;

        /**
         * @FieldDoc(
         *     description = "渠道配送运单号",
         *     example = "渠道配送运单号"
         * )
         */
         6: optional string channelDeliveryId;

         /**
          * @FieldDoc(
          *     description = "配送渠道",
          *     example = "配送渠道"
          * )
          */
          7: optional string deliveryChannel;

          /**
            * @FieldDoc(
            *     description = "渠道门店ID",
            *     example = "渠道门店ID"
            * )
            */
          8: optional string channelStoreId;

          /**
           * @FieldDoc(
           *     description = "是否是牵牛花管理配送",
           *     example = "是否是牵牛花管理配送"
           * )
           */
           9: optional i32 isQnhManagement;


           /**
            * @FieldDoc(
            *     description = "自配送单号",
            *     example = "自配送单号"
            * )
            */
            10: optional string deliveryOrderId;
}