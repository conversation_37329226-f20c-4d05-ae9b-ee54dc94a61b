namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "订单信息修改回调服务请求参数"
 * )
 */
struct OrderChangeCallbackRequest {
    /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
    1: string tenantAppId;
    /**
     * @FieldDoc(
     *     description = "渠道CODE唯一标识",
     *     example = "1"
     * )
     */
    2: string channelCode;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "变更类型",
     *     example = "1"
     * )
     */
    4: string opType;
    /**
     * @FieldDoc(
     *     description = "变更人",
     *     example = "1"
     * )
     */
    5: string opRole;
}