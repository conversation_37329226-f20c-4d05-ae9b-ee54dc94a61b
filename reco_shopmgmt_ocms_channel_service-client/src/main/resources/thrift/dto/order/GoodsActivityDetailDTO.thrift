namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order


/**
 * @TypeDoc(
 *     description = "商品分摊活动详情"
 * )
 */
struct GoodsSharedActivityItem{
        /**
          * @FieldDoc(
          *     description = "优惠类型",
          *     example = "1"
          * )
          */
        1: string channelPromotionType;

        /**
         * @FieldDoc(
         *     description = "优惠类型说明",
         *     example = "1"
         * )
         */
        2:string promotionRemark;

        /**
        * @FieldDoc(
        *     description = "渠道承担金额",
        *     example = "1"
        * )
        */
        3: i64 channelCost;

        /**
         * @FieldDoc(
         *     description = "商家承担金额",
         *     example = "10"
         * )
         */
        4: i64 tenantCost;

        /**
         * @FieldDoc(
         *     description = "活动ID",
         *     example = "122242"
         * )
         */
        5: string activityId;


        /**
        * @FieldDoc(
        *     description = "参与本次活动的商品数量",
        *     example = "9"
        * )
        */
        6: i32 promotionCount;

  /**
        * @FieldDoc(
        *     description = "活动数据模板类型，新版本货号活动才有值",
        *     example = "9"
        * )
        */
        7: string actConfType;


  /**
        * @FieldDoc(
        *     description = "活动数据模板类型下的活动id，新版本货号活动才有值，目前传的type是2，维度上的活动id",
        *     example = "2"
        * )
        */
        8: string actConfActivityId;


}
/**
 * @TypeDoc(
 *     description = "商品活动详情"
 * )
 */
struct GoodsActivityDetailDTO {
    /**
     * @FieldDoc(
     *     description = "customSkuId",
     *     example = "1"
     * )
     */
    1: string customSkuId;
    /**
     * @FieldDoc(
     *     description = "购买数量",
     *     example = "4"
     * )
     */
    2: i64 skuCount;
    /**
     * @FieldDoc(
     *     description = "总优惠金额",
     *     example = "1"
     * )
     */
    3: i64 totalDiscount;
    /**
     * @FieldDoc(
     *     description = "活动价（单价）",
     *     example = "1"
     * )
     */
    4: i64 activityPrice;


    /**
    * @FieldDoc(
    *     description = "商品原单价",
    *     example = "9"
    * )
    */
    5: i64 originPrice;



    /**
     * @FieldDoc(
     *     description = "渠道承担金额",
     *     example = "1"
     * )
     */
    6: i64 channelCost;

    /**
     * @FieldDoc(
     *     description = "商家承担金额",
     *     example = "10"
     * )
     */
    7: i64 tenantCost;

    /**
     * @FieldDoc(
     *     description = "代理商承担金额",
     *     example = "10"
     * )
     */
    8: i64 agentCost;

    /**
     * @FieldDoc(
     *     description = "物流承担金额",
     *     example = "1"
     * )
     */
    9: i64 logisticsCost;

    /**
      * @FieldDoc(
      *     description = "平台积分承担金额",
      *     example = "1"
      * )
      */
    10: i64 channelJiFenCost;


    /**
     * @FieldDoc(
     *     description = "活动说明",
     *     example = "88折"
     * )
     */
    11: string remark;
    /**
     * @FieldDoc(
     *     description = "渠道促销类型",
     *     example = ""
     * )
     */
    12: string channelPromotionType;


    /**
     * @FieldDoc(
     *     description = "商品参与的活动分摊金额",
     *     example = ""
     * )
     */
    13: list<GoodsSharedActivityItem> goodActivityDetail;

    /**
    * @FieldDoc(
    *     description = "是否有优惠活动",
    *     example = ""
    * )
    */
    14: bool hasPromotion;

    /**
     * @FieldDoc(
     *     description = "customSpu",
     *     example = "12322"
     * )
     */
    15: string customSpu;

    16: string channelItemId;

    /**
     * @FieldDoc(
     *     description = "三方平台skuId",
     *     example = "12322"
     * )
     */
    17: string thirdPartSkuId;

    /**
     * @FieldDoc(
     *     description = "三方平台spuId",
     *     example = "12322"
     * )
     */
    18: string thirdPartSpuId;

    /**
     * @FieldDoc(
     *     description = "三方平台itemId",
     *     example = "12322"
     * )
     */
    19: string thirdPartItemId;
}