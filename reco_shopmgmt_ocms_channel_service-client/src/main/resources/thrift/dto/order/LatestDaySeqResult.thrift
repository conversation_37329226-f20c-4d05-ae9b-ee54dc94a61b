namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ResultStatus.thrift"
include "OrderDaySeq.thrift"

/**
 * @TypeDoc(
 *     description = "查询订单最新状态服务接口返回结果"
 * )
 */
struct LatestDaySeqResult {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = "status"
     * )
     */
    1: ResultStatus.ResultStatus status;
    /**
    * @FieldDoc(
    *     description = "单号相关信息",
    *     example = ""
    * )
    */
    2: list<OrderDaySeq.OrderDaySeq> orderDaySeqInfos;
}