namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "RejectReasonCodeDTO.thrift"
include "ResultStatus.thrift"

/**
 * @TypeDoc(
 *     description = "获取审核拒绝原因返回结果"
 * )
 */
struct RejectReasonCodeResponse {

    /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
    1: ResultStatus.ResultStatus status;

    /**@FieldDoc(
            description = "拒绝原因列表",example = "rejectReasonCodeDtoList"
    )**/
    2: list<RejectReasonCodeDTO.RejectReasonCodeDTO> rejectReasonCodeDtoList;
}