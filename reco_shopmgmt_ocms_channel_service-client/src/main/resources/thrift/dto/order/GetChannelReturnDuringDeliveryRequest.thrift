namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "获取订单详情服务请求参数"
 * )
 */
struct GetChannelReturnDuringDeliveryRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "19473"
     * )
     */
    4: i64 sotreId;
    /**
     * @FieldDoc(
     *     description = "appId",
     *     example = "1"
     * )
     */
    5: optional i64 appId;
    /**
    * @FieldDoc(
    *     description = "返货配送单id",
    *     example = "1"
    * )
    */
    6: optional string dispatchOrderId;

    7:optional string channelPoiId;
}