namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

struct Message {
    /**
     * @FieldDoc(
     *     description = "消息类型",
     *     example = "100"
     * )
     */
    1: string tag;
    /**
     * @FieldDoc(
     *     description = "消息记录ID",
     *     example = "0443477XXXXXXX48874::xxx:1618990587:6837374XXXXXXX89128"
     * )
     */
    2: string msg_id;
    /**
     * @FieldDoc(
     *     description = "消息体，不同的消息，消息体各不相同",
     *     example = ""
     * )
     */
    3: string data;
}

struct OrderBatchNotifyRequest {
    /**
     * @FieldDoc(
     *     description = "防伪签名，可通过该字段值判断消息是否伪造",
     *     example = "100"
     * )
     */
    1: string eventSign;
    /**
     * @FieldDoc(
     *     description = "对应开发者后台的app_key",
     *     example = "10034324"
     * )
     */
    2: string appid;
    /**
     * @FieldDoc(
     *     description = "渠道类型，参考ChannelType",
     *     example = "DYCSXSD"
     * )
     */
    3: string channelCode;
    /**
     * @FieldDoc(
     *     description = "批量回调消息",
     *     example = "[]"
     * )
     */
    4: list<Message> messageList;
}