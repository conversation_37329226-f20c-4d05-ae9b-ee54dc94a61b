namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order


include "PoiRefundProductInfoDTO.thrift"


/**
 * @TypeDoc(
 *     description = "调整商品"
 * )
 */
struct AdjustOrderProductInfoDTO {

        /**
         * @FieldDoc(
         *     description = "商品sku"
         * )
         */
        1: optional string skuId;

        /**
         * @FieldDoc(
         *     description = "调整后商品数量"
         * )
         */
        2: required i32 remindSkuCount;

        /**
         * @FieldDoc(
         *     description = "门店商品SKU编码"
         * )
         */
         3: optional string extCustomSkuId;

}

/**
 * @TypeDoc(
 *     description = "调整商品"
 * )
 */
struct OrderProductInfoAfterAdjustDTO {

        /**
         * @FieldDoc(
         *     description = "商品sku"
         * )
         */
        1: required string skuId;

        /**
         * @FieldDoc(
         *     description = "调整后商品数量"
         * )
         */
        2: required i32 skuCount;

        /**
         * @FieldDoc(
         *     description = "sku名称"
         * )
         */
        3: string skuName;

        /**
         * @FieldDoc(
         *     description = "调整后商品销售价格"
         * )
         */
        4: i64 salePrice;

        /**
         * @FieldDoc(
         *     description = "upc码"
         * )
         */
        5: string upc;
}


/**
 * @TypeDoc(
 *     description = "商家发起调整订单请求参数"
 * )
 */
struct PoiAdjustOrderRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "退款原因",
     *     example = "部分退款申请原因"
     * )
     */
    4: string reason;
    /**
     * @FieldDoc(
     *     description = "退款商品列表",
     *     example = "[{\"skuId\" : \"134\", \"count\" : 2}]"
     * )
     */
    5: required list<AdjustOrderProductInfoDTO> modifyProducts;
    /**
     * @FieldDoc(
     *     description = "操作人",
     *     example = "张三"
     * )
     */
    6: required string optUser;
    /**
     * @FieldDoc(
     *     description = "内部门店编码",
     *     example = "129"
     * )
     */
    7: i64 storeId;
}


struct AfterAdjustOrderInfo{
        /**
         * @FieldDoc(
         *     description = "订单ID",
         *     example = "19472638492372738"
         * )
         */
        1: i64 orderId;


        /**
        * @FieldDoc(
        *     description = "缺货总数",
        *     example = "2"
        * )
        */
        2: i32 adjustCount;


        /**
        * @FieldDoc(
        *     description = "调整后的订单总金额",
        *     example = "2"
        * )
        */
        4: i32 orderTotalMoney;


        /**
        * @FieldDoc(
        *     description = "调整后的订单优惠总金额",
        *     example = "2"
        * )
        */
        5: i32 orderDiscountMoney;



        /**
        * @FieldDoc(
        *     description = "调整后的订单运费总金额",
        *     example = "2"
        * )
        */
        6: i32 orderFreightMoney;


        /**
        * @FieldDoc(
        *     description = "调整后用户应付金额",
        *     example = "2"
        * )
        */
        7: i32 orderBuyerPayableMoney;

        /**
        * @FieldDoc(
        *     description = "调整后商品信息",
        *     example = "2"
        * )
        */
        8: list<OrderProductInfoAfterAdjustDTO> products;
        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = "1"
         * )
         */
        9: optional i32 storeId;
}