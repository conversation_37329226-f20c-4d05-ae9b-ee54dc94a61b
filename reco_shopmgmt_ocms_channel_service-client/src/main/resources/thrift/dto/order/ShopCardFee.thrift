namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "购物卡金额信息"
 * )
 */
struct ShopCardFee {
    /**
     * @FieldDoc(
     *     description = "使用购物卡总金额	",
     *     example = "10"
     * )
     */
    1: i32 totalFee;
     /**
     * @FieldDoc(
     *     description = "本金金额",
     *     example = "10"
     * )
     */
    2: i32 baseFee;
     /**
     * @FieldDoc(
     *     description = "赠金金额",
     *     example = "12"
     * )
     */
    3: i32 giveFee;
     /**
     * @FieldDoc(
     *     description = "赠金平台分摊",
     *     example = "11"
     * )
     */
    4: i32 platformGiveFeeShare;
     /**
     * @FieldDoc(
     *     description = "赠金商家分摊",
     *     example = "12"
     * )
     */
    5: i32 shopGiveFeeShare;




}