namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "批量查询客服赔付商家责任请求参数"
 * )
 */
struct PoiBatchCompensationRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: list<string> orderViewIds;
      /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "admin"
     * )
     */
    4: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "渠道店铺ID",
     *     example = "5"
     * )
     */
    5: string channelPoiId;
    /**
     * @FieldDoc(
     *     description = "appId",
     *     example = "1"
     * )
     */
    6: optional i64 appId;
    /**
     * @FieldDoc(
     *     description = "赔付类型",
     *     example = "0-非闪电送，不冰比赔付 1-不冰必赔 2-闪电送超时赔付"
     * )
     */
    7: optional i64 compensateType;
}