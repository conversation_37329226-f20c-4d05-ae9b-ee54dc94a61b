namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "通过自提码查询详情参数"
 * )
 */
struct SelfFetchCodeGetRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "自提码",
     *     example = "50412092715"
     * )
     */
    3: string code;
    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "123"
     * )
     */
    4: i64 storeId;
}