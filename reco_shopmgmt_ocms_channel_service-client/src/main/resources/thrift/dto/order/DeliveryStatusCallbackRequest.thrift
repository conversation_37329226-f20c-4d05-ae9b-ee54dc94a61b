namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "订单配送状态回调服务请求参数"
 * )
 */
struct DeliveryStatusCallbackRequest {
    /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
    1: string tenantAppId;
    /**
     * @FieldDoc(
     *     description = "渠道CODE唯一标识",
     *     example = "1"
     * )
     */
    2: string channelCode;
    /**
     * @FieldDoc(
     *     description = "配送状态",
     *     example = "1"
     * )
     */
    3: i32 deliveryStatus;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    4: string orderId;
    /**
     * @FieldDoc(
     *     description = "骑手名称",
     *     example = "admin"
     * )
     */
    5: string dispatcherName;
    /**
     * @FieldDoc(
     *     description = "骑手电话",
     *     example = "15888888888"
     * )
     */
    6: string dispatcherMobile;
}