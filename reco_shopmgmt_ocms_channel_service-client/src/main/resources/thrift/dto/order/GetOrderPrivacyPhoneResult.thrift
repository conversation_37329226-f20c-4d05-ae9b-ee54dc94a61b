namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ResultStatus.thrift"

/**
 * @TypeDoc(
 *     description = "渠道隐私号失效时获取新的隐私号返回数据"
 * )
 */
struct GetOrderPrivacyPhoneResult{
        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus status;
        /**
         * @FieldDoc(
         *     description = "订单收货人联系电话隐私号",
         *     example = "13812345678_1236"
         * )
         */
        2: string privacyPhone;
}