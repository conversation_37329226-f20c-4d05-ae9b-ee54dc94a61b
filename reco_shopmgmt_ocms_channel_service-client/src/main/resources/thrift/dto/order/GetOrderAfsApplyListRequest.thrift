namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "AfterSaleApplyBODto.thrift"
/**
 * @TypeDoc(
 *     description = "查询订单售后记录接口请求参数"
 * )
 */
struct GetOrderAfsApplyListRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道类型",
     *     example = "1"
     * )
     */
    2: i32 channelType;
    /**
     * @FieldDoc(
     *     description = "渠道订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string channelOrderId;

    /**
     * @FieldDoc(
     *     description = "售后订单ID",
     *     example = "19472638492372738"
     * )
     */
    4: string afterSaleId;

 /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "1"
     * )
     */
    5: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "appId",
     *     example = "1"
     * )
     */
    6: optional i64 appId;

    /**
     * @FieldDoc(
     *     description = "申请过的售后退单",
     *     example = "1"
     * )
     */
    7: optional list<AfterSaleApplyBODto.AfterSaleApplyBODto> afterSaleApplyBODto;

    /**
     * @FieldDoc(
     *     description = "用户发起还是商家发起",
     *     example = "1"
     * )
     */
    8: optional i32 applyType;

    /**
     * @FieldDoc(
     *     description = "淘鲜达退款ID，只有审核同意时用到",
     *     example = "1"
     * )
     */
    13: optional string txdRefundId;
    /**
     * @FieldDoc(
     *     description = "订单来源，淘鲜达可以用这个判断单双模型。4为单模型，31为双模型，其余情况依赖租户、配置",
     *     example = "1"
     * )
     */
    14: optional string orderFrom;
}