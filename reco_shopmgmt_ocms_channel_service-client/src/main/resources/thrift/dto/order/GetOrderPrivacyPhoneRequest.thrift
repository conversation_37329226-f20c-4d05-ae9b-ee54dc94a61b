namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "渠道隐私号失效时获取新的隐私号请求参数"
 * )
 */
struct GetOrderPrivacyPhoneRequest {
        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "1"
         * )
         */
        1: i32 channelId;
        /**
         * @FieldDoc(
         *     description = "订单ID",
         *     example = "19472638492372738"
         * )
         */
        2: string orderId;
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        3: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "门店id",
         * )
         */
        4: i64 storeId;

        /**
        * 渠道售后单ID
        **/
        5: optional string afterSaleId;

}