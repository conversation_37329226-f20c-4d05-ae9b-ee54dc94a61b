namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "OrderDeliveryDetailDTO.thrift"
include "OrderInvoiceDetailDTO.thrift"
include "OrderProductDetailDTO.thrift"
include "OrderDiscountDetailDTO.thrift"
include "GoodsActivityDetailDTO.thrift"
include "FavoritesStatusEnum.thrift"
include "ActivityShareDetailDTO.thrift"
include "ShopCardFee.thrift"
include "OrderPaymentInfo.thrift"
include "RedpackInfo.thrift"

/**
 * @TypeDoc(
 *     description = "渠道订单详细信息"
 * )
 */
struct ChannelOrderDetailDTO {
        /**
         * @FieldDoc(
         *     description = "渠道订单ID",
         *     example = "1928374857328384738"
         * )
         */
        1: string channelOrderId;
        /**
         * @FieldDoc(
         *     description = "实付金额",
         *     example = "100"
         * )
         */
        2: i32 actualPayAmt;
        /**
         * @FieldDoc(
         *     description = "原始金额",
         *     example = "200"
         * )
         */
        3: i32 originalAmt;
        /**
         * @FieldDoc(
         *     description = "商家实收金额",
         *     example = "120"
         * )
         */
        4: i32 bizReceiveAmt;
        /**
         * @FieldDoc(
         *     description = "配送费",
         *     example = "10"
         * )
         */
        5: i32 freight;
        /**
         * @FieldDoc(
         *     description = "渠道订单创建时间",
         *     example = "1573847384000"
         * )
         */
        6: i64 createTime;
        /**
         * @FieldDoc(
         *     description = "是否需要发票",
         *     example = "true"
         * )
         */
        7: bool isNeedInvoice;
        /**
         * @FieldDoc(
         *     description = "城市ID",
         *     example = "100110"
         * )
         */
        8: i32 cityId;
        /**
         * @FieldDoc(
         *     description = "支付类型",
         *     example = "1"
         * )
         */
        9: i32 payType;
        /**
         * @FieldDoc(
         *     description = "支付状态",
         *     example = "1"
         * )
         */
        10: i32 payStatus;
        /**
         * @FieldDoc(
         *     description = "操作人",
         *     example = "admin"
         * )
         */
        11: string operator;
        /**
         * @FieldDoc(
         *     description = "订单流水号",
         *     example = "123"
         * )
         */
        12: i64 orderSerialNumber;
        /**
         * @FieldDoc(
         *     description = "打包费金额",
         *     example = "10"
         * )
         */
        13: i32 packageAmt;
        /**
         * @FieldDoc(
         *     description = "订单配送信息",
         *     example = "详见OrderDeliveryDetailDTO"
         * )
         */
        14: OrderDeliveryDetailDTO.OrderDeliveryDetailDTO deliveryDetail;
        /**
         * @FieldDoc(
         *     description = "订单发票信息",
         *     example = "详见OrderInvoiceDetailDTO"
         * )
         */
        15: OrderInvoiceDetailDTO.OrderInvoiceDetailDTO invoiceDetail;
        /**
         * @FieldDoc(
         *     description = "订单商品信息",
         *     example = "详见OrderProductDetailDTO"
         * )
         */
        16: list<OrderProductDetailDTO.OrderProductDetailDTO> skuDetails;
        /**
         * @FieldDoc(
         *     description = "订单活动信息",
         *     example = "详见OrderDiscountDetailDTO"
         * )
         */
        17: list<OrderDiscountDetailDTO.OrderDiscountDetailDTO> activities;

        /**
         * @FieldDoc(
         *     description = "扩展字段",
         *     example = "无"
         * )
         */
        18: string extData;
        /**
         * @FieldDoc(
         *     description = "内部门店编码",
         *     example = "129"
         * )
         */
        19: i64 storeId;
        /**
         * @FieldDoc(
         *     description = "订单总优惠金额",
         *     example = "10"
         * )
         */
        20: i32 totalDiscount;
        /**
         * @FieldDoc(
         *     description = "平台服务费",
         *     example = "10"
         * )
         */
        21: i32 platformFee;
        /**
         * @FieldDoc(
         *     description = "是否是预约订单",
         *     example = "true"
         * )
         */
        22: optional bool isBooking = true;
        /**
         * @FieldDoc(
         *     description = "用户积分抵扣金额",
         *     example = "100"
         * )
         */
        23: i32 pointsDeductionMoney;
        /**
         * @FieldDoc(
         *     description = "备注",
         *     example = "多辣"
         * )
         */
        24: string comment;

        /**
         * @FieldDoc(
         *     description = "渠道订单号2-主要用于饿百渠道饿了么订单号",
         *     example = "124532"
         * )
         */
        25: string channelExtraOrderId;

        /**
         * @FieldDoc(
         *     description = "商品分摊后的优惠活动",
         *     example = ""
         * )
         */
        26: list<GoodsActivityDetailDTO.GoodsActivityDetailDTO> skuSharedActivities;
        /**
         * @FieldDoc(
         *     description = "渠道门店名称",
         *     example = ""
         * )
         */
        27: string channelStoreName;

        /**
         * @FieldDoc(
         *     description = "会员卡code",
         *     example = ""
         * )
         */
        28: string memberCardCode;

        /**
         * @FieldDoc(
         *     description = "订单数据是否降级, 1:是，0:否",
         *     example = ""
         * )
         */
        29: optional i32 downFlag = 0;
        /**
         * @FieldDoc(
         *     description = "渠道订单取消时间",
         *     example = "1573847384000"
         * )
         */
        30: i64 cancelTime;
        /**
         * @FieldDoc(
         *     description = "渠道订单完成时间",
         *     example = "1573847384000"
         * )
         */
        31: i64 completedTime;
        /**
         * @FieldDoc(
         *     description = "商家接单时间",
         *     example = "1573847384000"
         * )
         */
        32: i64 confirmTime;
        /**
         * @FieldDoc(
         *     description = "骑手取单时间",
         *     example = "1573847384000"
         * )
         */
        33: i64 logisticFetchTime;
        /**
                 * @FieldDoc(
                 *     description = "骑手送达时间",
                 *     example = "1573847384000"
                 * )
                 */
        34: i64 logisticCompletedTime;


        /**
         * @FieldDoc(
         *     description = "骑手姓名",
         *     example = "张三"
         * )
         */
        35: string dispatcherName;

        /**
        * @FieldDoc(
        *     description = "骑手电话",
        *     example = "13540003000"
        * )
        */
        36: string dispatcherPhone;

        /**
       * @FieldDoc(
       *     description = "原始门店id(开放平台下发)",
       *     example = "9274498"
       * )
       */
        37: optional string originalPoiId;


        /**
        * @FieldDoc(
        *     description = "用户id",
        *     example = "124566"
        * )
        */
        38: string userId;

        /**
        * @FieldDoc(
        *     description = "收藏：0 渠道未下发、1已收藏、2未收藏",
        *     example = "1"
        * )
        */
        39: optional FavoritesStatusEnum.FavoritesStatusEnum favoritesStatus;
        /**
        * @FieldDoc(
        *     description = "渠道订单状态、对应OrderStatusEnum",
        *     example = "10"
        * )
        */
        40: i32 channelOrderStatus;
           /**
        * @FieldDoc(
        *     description = "渠道订单支付时间",
        *     example = "10"
        * )
        */
        41: i64 payTime;
        /**
        * @FieldDoc(
        *     description = "zhuangtai",
        *     example = "10"
        * )
        */
        42: i32 status;


        /**
        * @FieldDoc(
        *     description = "降级的字段，参考OrderDegradeModuleEnum",
        *     example = "[1]"
        * )
        */
        43: list<i32> degradeModules;

        /**
                * @FieldDoc(
                *     description = "渠道Id",
                *     example = "10"
                * )
                */
        44: i32 channelId;

        /**
        * 城市隐私号服务是否降级: true-降级；false-未降级
        **/
        45: bool cityPrivacyDegrade;

        /**
         * @FieldDoc(
         *     description = "美配新收费模式&企客配送模式下的订单履约服务费金额",
         *     example = "200"
         * )
         */
        46: i32 performanceServiceFee;

        /**
         * @FieldDoc(
         *     description = "订单用户类型，是否为会员",
         *     example = "10，15"
         * @see com.meituan.shangou.saas.order.platform.enums.UserTypeEnum
         * )
         */
        47: i32 orderUserType;


        /**
         * @FieldDoc(
         *     description = "商家运费优惠",
         *     example = "200"
         * )
         */
        48: i32 poiLogisticsPromotion;

        /**
         * @FieldDoc(
         *     description = "平台运费优惠",
         *     example = "2"
         * )
         */
        49: i32 platLogisticsPromotion;

        /**
         * @FieldDoc(
         *     description = "商家运费小费",
         *     example = "1"
         * )
         */
        50: i32 poiLogisticsTips;

        /**
         * @FieldDoc(
         *     description = "用户运费小费",
         *     example = "1"
         * )
         */
        51: i32 customerLogisticsTips;

        /**
         * @FieldDoc(
         *     description = "积分抵扣",
         *     example = "1"
         * )
         */
        52: i32 scoreDeduction;

        /**
         * @FieldDoc(
         *     description = "自提服务费",
         *     example = "1"
         * )
         */
        53: i32 selfPickServiceFee;

        /**
         * @FieldDoc(
         *     description = "平台收包装费",
         *     example = "1"
         * )
         */
        54: i32 platPackageAmt;

        /**
         * @FieldDoc(
         *     description = "商家收包装费",
         *     example = "1"
         * )
         */
        55: i32 poiPackageAmt;

        /**
         * @FieldDoc(
         *     description = "用户支付包装费",
         *     example = "1"
         * )
         */
        56: i32 payPackageAmt;

        /**
         * @FieldDoc(
         *     description = "营业即送",
         *     example = "1"
         * )
         */
        57: bool isOpeningDelivery;

        /**
         * @FieldDoc(
         *     description = "平台整单优惠",
         *     example = "1"
         * )
         */
        58: i32 platPromotion;


        /**
         * @FieldDoc(
         *     description = "平台单品优惠",
         *     example = "1"
         * )
         */
        59: i32 platItemPromotion;

        /**
         * @FieldDoc(
         *     description = "商家整单优惠",
         *     example = "1"
         * )
         */
        60: i32 poiPromotion;

        /**
         * @FieldDoc(
         *     description = "商家单品优惠",
         *     example = "1"
         * )
         */
        61: i32 poiItemPromotion;

        /**
         * @FieldDoc(
         *     description = "商家包装费优惠",
         *     example = "1"
         * )
         */
        62: i32 poiPackagePromotion;

        /**
         * @FieldDoc(
         *     description = "平台包装费优惠",
         *     example = "1"
         * )
         */
        63: i32 platPackagePromotion;

        /**
         * @FieldDoc(
         *     description = "子渠道",
         *     example = "1"
         * )
         */
        64: i32 subBizCode;

        /**
         * @FieldDoc(
         *     description = "返还运费",
         *     example = "0"
         * )
         */
        65: i32 rebackFreight;

        /**
         * @FieldDoc(
         *     description = "商家运费收入",
         *     example = "200"
         * )
         */
        69: i32 poiLogisticsIncome;


        /**
        * @FieldDoc(
        *     description = "商品分摊的活动"
        * )
        */
        70: optional list<ActivityShareDetailDTO.ActivityShareDetailDTO> activityShareDetailList;


        /**
         * @FieldDoc(
         *     description = "买家昵称"
         * )
         */
         71: optional string buyerNick;

        /**
         * @FieldDoc(
         *     description = "买家账号"
         * )
         */
         72: optional string buyerAccount;

        /**
         * @FieldDoc(
         *     description = "渠道支付方式(原始值)"
         * )
         */
         73: optional string channelPayMode;


        /**
         * @FieldDoc(
         *     description = "百川状态"
         * )
         */
         74: optional i32 baichuanStatus;

         /**
         * @FieldDoc(
         *     description = "订单来源"
         * )
        **/
        75: optional string orderSource;

        /**
         * @FieldDoc(
         *     description = "三方会员卡号"
         * )
        **/
        76: optional string threeCardNo;

        /**
                 * @FieldDoc(
                 *     description = "代签点"
                 * )
                **/
        77: optional string deliveryPosition;
            /**
             * @FieldDoc(
             *     description = "购物金"
             * )
            **/
        78: optional ShopCardFee.ShopCardFee shopCardFee;
        /**
                     * @FieldDoc(
                     *     description = "定位地址"
                     * )
                    **/
        79: optional string positioningAddress;



        /**
         * @FieldDoc(
         *     description = "订单流水号", 渠道流水号可能不是数字，这个字段保留渠道原始的值
         *     example = "1-0315"
         * )
         */
        80: string orderSerialNumberStr;

        /**
                     * @FieldDoc(
                     *     description = "订单级别总优惠，整单总优惠"
                     * )
                    **/
        81: optional i32 totalOrderPromotion;

        /**
                            * @FieldDoc(
                             *     description = "开平平台用户id"
                             * )
                            **/
        82: optional i64 openUserId;

        /**
                               * @FieldDoc(
                               *     description = "新备注（只含用户所填备注）"
                               * )
                               **/
        83: optional string newComment;


        /**
         * @FieldDoc(
         *     description = "订单支付信息"
         * )
        **/
        84: optional list<OrderPaymentInfo.OrderPaymentInfo> orderPaymentInfoList;

         /**
        * @FieldDoc(
         *     description = "是否闪电送订单，1：是，0：否"
         * )
        **/
        85: optional i32 isFastOrder=0;

        /**
        * @FieldDoc(
         *     description = "闪电送费用，单位：分"
         * )
        **/
        86: optional i32 fastDeliveryAmt;

        /**
          * @FieldDoc(
          *     description = "地址变更费"
          * )
          **/
        87: optional string addressChangeFee;

        /**
         * @FieldDoc(
         *     description = "基础运费",
         *     example = "200"
         * )
         */
        88: optional i32 baseFreight;

        /**
         * @FieldDoc(
         *     description = "重量运费",
         *     example = "200"
         * )
         */
        89: optional i32 weightFreight;

        /**
         * @FieldDoc(
         *     description = "距离运费",
         *     example = "200"
         * )
         */
        90: optional i32 distanceFreight;

        /**
         * @FieldDoc(
         *     description = "时段运费",
         *     example = "200"
         * )
         */
        91: optional i32 timeFrameFreight;

        /**
         * @FieldDoc(
         *     description = "商家支付远距离运费",
         *     example = "200"
         * )
         */
        92: optional i32 poiFarDistanceFreight;

        /**
         * @FieldDoc(
         *     description = "红包信息",
         *     example = "10"
         * )
         */
        93: RedpackInfo.RedpackInfo redpackInfo;

        /**
          * @FieldDoc(
          *     description = "三方门店ID"
          * )
          **/
        94: optional string appPoiCode;

        /**
          * @FieldDoc(
          *     description = "订单来源，目前仅淘鲜达使用，这个字段可以用来区分单双模型，理论上这个字段可以用来区分渠道各种细化的订单"
          * )
          **/
        95: optional string orderFrom;

        /**
          * @FieldDoc(
          *     description = "渠道用户Id"
          * )
          **/
        96: optional string channelUserId;

        /**
         * @FieldDoc(
         *     description = "是否为美团名酒馆订单",
         *     example = "true：是"
         * )
         */
        97: optional bool mtFamousTavern;

                /**
         * @FieldDoc(
         *     description = "是否为美团发财酒订单",
         *     example = "true：是"
         * )
         */
        98: optional bool mtFacaiWine;

        /**
         * @FieldDoc(
         *     description = "是否为四轮汽车送订单",
         *     example = "true：是"
         * )
         */
        99: optional bool fourWheelDelivery;
        /**
         * @FieldDoc(
         *     description = "是否是超客订单",
         *     example = "0-否，1-是"
         * )
         */
        100: i32 superCustomerOrder;

        /**
          * @FieldDoc(
          *     description = "渠道订单来源，目前饿了么使用1 饿了么星选APP， 2 饿了么APP或淘宝小时达， 3 微信小程序，5 淘宝闪购"
          * )
          **/
        101: optional string channelOrderFrom;


                /**
         * @FieldDoc(
         *     description = "缺货信息",
         * )
         */
        102: optional string stockOutInfo;
}

struct DrunkHorseOrderExtra {
        /**
        * @FieldDoc(
        *     description = "是否品牌新客，1是，0不是",
        *     example = "1"
        * )
        */
        1: i32 isBrandFirstOrder;
        /**
            * @FieldDoc(
            *     description = "支付流水号",
            *     example = "10"
            * )
        */
        2: string traceNo

        /**
            * @FieldDoc(
            *     description = "用户ID",
            *     example = "10"
            * )
        */
        3: string outerUserId

        /**
            * @FieldDoc(
            *     description = "商城ID",
            *     example = "100039501"
            * )
        */
        4: string mallId


}