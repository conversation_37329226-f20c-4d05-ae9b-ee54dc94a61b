namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "渠道订单赔付信息"
 * )
 */
struct OrderCompensationDTO {
        /**
         * @FieldDoc(
         *     description = "渠道订单ID",
         *     example = "143470120000"
         * )
         */
        1: string channelOrderId;
        /**
         * @FieldDoc(
         *     description = "赔付类型， 1：不冰必赔， 2：闪电送超时赔付",
         *     example = "1"
         * )
         */
        2: i32 compensateType;
        /**
         * @FieldDoc(
         *     description = "赔付金额",
         *     example = "100"
         * )
         */
        3: i32 amount;
        /**
         * @FieldDoc(
         *     description = "原因",
         *     example = "执行不冰必赔服务承诺"
         * )
         */
        4: string reason;
        /**
         * @FieldDoc(
         *     description = "赔付时间",
         *     example = "1573847384000"
         * )
         */
        5: i64 compensateTime;
        /**
         * @FieldDoc(
         *     description = "责任方，1-商家，2-平台",
         *     example = "10"
         * )
         */
        6: i32 responsibleParty;
        /**
         * @FieldDoc(
         *     description = "是否超时，1-超时，2-未超时",
         *     example = "1"
         * )
         */
        7: i32 isTimeout;

}
