namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "获取订单分页列表请求参数"
 * )
 */
struct QueryChannelAfsOrderListRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        2: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = "1"
         * )
         */
        3: optional i32 shopId;

        /**
         * @FieldDoc(
         *     description = "当前页",
         *     example = "1"
         * )
         */
        4: i32 page;
        /**
         * @FieldDoc(
         *     description = "页大小",
         *     example = "20"
         * )
         */
        5: optional i32 pageSize;

        /**
         * @FieldDoc(
         *     description = "起始时间戳",
         *     example = "10"
         * )
         */
        6: optional i64 startTime;

        /**
         * @FieldDoc(
         *     description = "结束时间戳",
         *     example = "10"
         * )
         */
        7: optional i64 endTime;

        /**
         * @FieldDoc(
         *     description = "appId",
         *     example = "1"
         * )
         */
        8: optional i64 appId;

        /**
         * @FieldDoc(
         *     description = "渠道店铺ID（目前仅有赞使用(分店编码)）",
         *     example = "10"
         * )
         */
        9: optional string channelPoiId;
        /**
         * @FieldDoc(
         *     description = "扩展信息es:分页游标",
         *     example = "10"
         * )
         */
        10: map<string,string>  extraInfo;
}