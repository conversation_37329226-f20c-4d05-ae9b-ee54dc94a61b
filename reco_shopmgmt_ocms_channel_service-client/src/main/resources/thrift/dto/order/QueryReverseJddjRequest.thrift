namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "查询订单售后记录接口请求参数"
 * )
 */
struct QueryReverseJddjRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道类型",
     *     example = "1"
     * )
     */
    2: i32 channelType;
    /**
     * @FieldDoc(
     *     description = "渠道订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string channelOrderId;

    /**
     * @FieldDoc(
     *     description = "售后订单ID",
     *     example = "19472638492372738"
     * )
     */
    4: optional string refDiffReverseOrderId;

    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = "1"
     * )
     */
    5: i64 shopId;
}