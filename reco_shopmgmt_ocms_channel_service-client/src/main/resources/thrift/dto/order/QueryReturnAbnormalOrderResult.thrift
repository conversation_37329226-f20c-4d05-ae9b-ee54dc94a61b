namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order
include "ResultStatus.thrift"


struct AbnormalReturnOrderInfo{

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        1: i32 channelId;
        /**
         * @FieldDoc(
         *     description = "渠道订单号",
         *     example = "channelOrderDetail"
         * )
         */
        2: string channelOrderId;
        /**
         * @FieldDoc(
         *     description = "退单号",
         *     example = "channelOrderDetail"
         * )
         */
        3: string returnOrderId;

}

struct QueryReturnAbnormalOrderResult{
        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "退单号列表信息",
         *     example = "returnOrderList"
         * )
         */
        2: list<AbnormalReturnOrderInfo> abReturnOrderList;

        /**
         * @FieldDoc(
         *     description = "总个数",
         *     example = "totalCount"
         * )
         */
        3: i64 totalCount;
}