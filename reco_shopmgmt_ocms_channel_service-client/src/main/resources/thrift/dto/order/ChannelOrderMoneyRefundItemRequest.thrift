namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "查询渠道订单金额退可退商品列表请求参数"
 * )
 */
struct ChannelOrderMoneyRefundItemRequest {
        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "1"
         * )
         */
        1: i32 channelId;
        /**
         * @FieldDoc(
         *     description = "订单ID",
         *     example = "19472638492372738"
         * )
         */
        2: string orderId;
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        3: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "门店id",
         * )
         */
        4: i64 storeId;

         /**
          * @FieldDoc(
          *     description = "需要查询的渠道商品明细行id(仅淘鲜达使用)",
          *     example = "1"
          * )
          */
         5: optional list<string> channelOrderItemIds;

         /**
           * @FieldDoc(
           *     description = "订单来源，目前仅淘鲜达使用，这个字段可以用来区分单双模型，理论上这个字段可以用来区分渠道各种细化的订单",
           *     example = "1"
           * )
           */
          6: optional string orderFrom;
}