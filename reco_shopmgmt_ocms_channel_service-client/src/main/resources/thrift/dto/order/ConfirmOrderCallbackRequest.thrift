namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "已确认订单回调服务请求参数"
 * )
 */
struct ConfirmOrderCallbackRequest {
    /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
    1: string tenantAppId;
    /**
     * @FieldDoc(
     *     description = "渠道CODE唯一标识",
     *     example = "1"
     * )
     */
    2: string channelCode;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "订单状态",
     *     example = "5"
     * )
     */
    4: string status;
    /**
     * @FieldDoc(
     *     description = "消息时间",
     *     example = "2019-02-14 14:00:00"
     * )
     */
    5: string timestamp;
}