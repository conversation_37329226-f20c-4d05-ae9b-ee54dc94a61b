namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order


/**
 * @TypeDoc(
 *     description = "商品分摊活动详情"
 * )
 */
struct ActivityShareDetailDTO {

/**
* @FieldDoc(
*     description = "活动ID",
*     example = "122242"
* )
*/
1: optional string activityId;

/**
* @FieldDoc(
*     description = "渠道的活动名称"；
*     example = "122242"
* )
*/
2: optional string promotionRemark


/**
* @FieldDoc(
*     description = "活动类型编码",
*     example = "1"
* )
*/
3: optional string channelPromotionType;


/**
* @FieldDoc(
*     description = "渠道活动类型编码枚举名称",
*     example = "1"
* )
*/
4: optional string channelPromotionTypeName;


/**
* @FieldDoc(
*     description = "skuId",
*     example = "1"
* )
*/
5: optional string skuId;

/**
* @FieldDoc(
*     description = "参与本次活动的商品数量",
*     example = "9"
* )
*/
6: optional i32 promotionCount;


/**
* @FieldDoc(
*     description = "购买数量",
*     example = "4"
* )
*/
7: optional i32 skuCount;

/**
* @FieldDoc(
*     description = "商品原价总额",
*     example = "9"
* )
*/
8: optional i32 totalOriginPrice;

/**
* @FieldDoc(
*     description = "商品活动价总额",
*     example = "1"
* )
*/
9: optional i32 totalActivityPrice;



/**
* @FieldDoc(
*     description = "渠道承担总金额",
*     example = "1"
* )
*/
10: optional i32 channelCost;

/**
* @FieldDoc(
*     description = "商家承担总金额",
*     example = "10"
* )
*/
11: optional i32 tenantCost;

/**
* @FieldDoc(
*     description = "整合营销到家结算(平台)",
*     example = "1"
* )
*/
12: optional i32 platMarketPromotion;

/**
* @FieldDoc(
*     description = "整合营销推广结算(商家)",
*     example = "10"
* )
*/
13: optional i32 poiMarketPromotion;

/**
* @FieldDoc(
*     description = "整合营销推广结算(供应商)",
*     example = "1"
* )
*/
14: optional i32 supplierMarketPromotion;

/**
* @FieldDoc(
*     description = "customSpu",
*     example = "1"
* )
*/
15: optional string customSpu;

/**
* @FieldDoc(
*     description = "ERP促销ID",
*     example = "1"
* )
*/
16: optional string promotionCodeErp;

/**
* @FieldDoc(
*     description = "参与本次活动的商品数量",
*     example = "9"
* )
*/
17: optional string decimalPromotionCount;

/**
* @FieldDoc(
*     description = "购买数量",
*     example = "4"
* )
*/
18: optional string decimalSkuCount;

}