namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order


/**
 * @TypeDoc(
 *     description = "接收售后临时参数"
 * )
 */
struct AfterSaleApplyBODto {

    /**
     * @FieldDoc(
     *     description = "渠道订单明细orderItemId",
     *     example = "19472638492372738"
     * )
     */
    1: string orderItemId;

    /**
     * @FieldDoc(
     *     description = "已申请数量",
     *     example = "1.5"
     * )
     */
    2: string count;

    /**
     * @FieldDoc(
     *     description = "已申请退款金额（单位分）",
     *     example = ""
     * )
     */
    3: i32 payAmt;

    /**
     * @FieldDoc(
     *     description = "已申请的数量（可退数量采用）",
     *     example = "1.5"
     * )
     */
    4: string refundCountDecimal;

    /**
     * @FieldDoc(
     *     description = "已申请商家整单优惠",
     *     example = "3"
     * )
     */
    5: i32 poiPromotion;

    /**
     * @FieldDoc(
     *     description = "已申请商家单品优惠",
     *     example = "3"
     * )
     */
    6: i32 poiItemPromotion;

    /**
     * @FieldDoc(
     *     description = "已申请商品重量",
     *     example = "3L"
     * )
     */
    7: double weight;

    /**
     * @FieldDoc(
     *     description = "运费金额",
     *     example = "3"
     * )
     */
    8: optional i32 payFreightAmt;

}