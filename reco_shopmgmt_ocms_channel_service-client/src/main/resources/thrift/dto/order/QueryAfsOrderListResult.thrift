namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order
include "ChannelOrderDetailDTO.thrift"
include "ResultStatus.thrift"

struct AfsOrderInfo{
        /**
         * @FieldDoc(
         *     description = "渠道订单Id",
         *     example = "12312312"
         * )
         */
    1: string channelOrderId;

        /**
         * @FieldDoc(
         *     description = "渠道退单Id",
         *     example = "12312312"
         * )
         */
    2: string channelAfsOrderId;

        /**
         * @FieldDoc(
         *     description = "退单状态",
         *     example = ""
         * )
         */
    3: i32 afsStatus;

}

/**
 * @TypeDoc(
 *     description = "获取渠道门店退单分页列表接口返回结果"
 * )
 */
struct QueryAfsOrderListResult {
        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus status;
        /**
         * @FieldDoc(
         *     description = "渠道订单号列表信息",
         *     example = "channelOrderDetail"
         * )
         */
        2: list<AfsOrderInfo> channelAfsOrderIdList;
        /**
         * @FieldDoc(
         *     description = "当前页",
         *     example = "1"
         * )
         */
        3: i32 page;
        /**
         * @FieldDoc(
         *     description = "总页数",
         *     example = "10"
         * )
         */
        4: i32 pageCount;
        /**
         * @FieldDoc(
         *     description = "总数",
         *     example = "100"
         * )
         */
        5: i32 total;
        /**
         * @FieldDoc(
         *     description = "扩展信息",
         *     example = ""
         * )
         */
        6: map<string,string> extraInfo;



}
