namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order


/**
 * @TypeDoc(
 *     description = "获取订单克重退款详情返回结果"
 * )
 */
struct WeightPartRefundGoodsDTO {

        /**
         * @FieldDoc(
         *     description = " 商品明细 id",
         *     example = "id"
         * )
         */
        1: string thirdPartItemId;
        /**
         * @FieldDoc(
         *     description = "重量,克",
         *     example = "weight"
         * )
         */
        2: double weight;
        /**
         * @FieldDoc(
         *     description = "可退数量",
         *     example = "allow_ret_qty"
         * )
         */
        3: double allow_ret_qty;
        /**
          * @FieldDoc(
          *     description = "商品名称",
          *     example = "item_name"
          * )
          */
        4: string item_name;
        /**
          * @FieldDoc(
          *     description = "条形码",
          *     example = "barcode"
          * )
          */
        5: string barcode;
        /**
          * @FieldDoc(
          *     description = "skuId",
          *     example = "skuId"
          * )
          */
        6: string skuId;
        /**
          * @FieldDoc(
          *     description = "customSkuId",
          *     example = "customSkuId"
          * )
          */
        7: string customSkuId;
        /**
          * @FieldDoc(
          *     description = "数量",
          *     example = "sale_qty"
          * )
          */
        8: double sale_qty;
        /**
          * @FieldDoc(
          *     description = "单价",
          *     example = "sale_price"
          * )
          */
        9: i32 sale_price;
        /**
          * @FieldDoc(
          *     description = "原价",
          *     example = "original_price"
          * )
          */
        10: i32 original_price;
        /**
          * @FieldDoc(
          *     description = "金额",
          *     example = "sale_value"
          * )
          */
        11: i32 sale_value;

        /**
        * @FieldDoc(
        *     description = "三方平台skuId",
        *     example = "123"
        * )
        */
        12: string thirdPartSkuId;

        /**
        * @FieldDoc(
        *     description = "三方平台spuId",
        *     example = "123"
        * )
        */
        13: string thirdPartSpuId;

}