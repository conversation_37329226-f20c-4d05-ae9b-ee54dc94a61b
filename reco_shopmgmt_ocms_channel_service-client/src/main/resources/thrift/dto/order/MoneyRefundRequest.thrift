namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

struct MoneyRefundProductInfoDTO {
        /**
         * @FieldDoc(
         *     description = "商品sku",
         * )
         * @Deprecated  见customSkuId, 由于美团渠道skuId使用的app_food_code，之后会切换成sku_id, 订单部分不会对历史数据进行清洗。所以沿用skuId字段将区分不了到底是真的sku_id还是老的app_food_code
         */
        1: optional string skuId;
        /**
         * @FieldDoc(
         *     description = "商品sku"
         * )
         */
        2: optional string customSkuId;
        /**
         * @FieldDoc(
         *     description = "渠道订单明细ID"
         * )
         */
        3: optional string channelOrderItemId;
        /**
         * @FieldDoc(
         *     description = "商品退款金额(分)"
         * )
         */
        4: optional i32 refundAmount;
        /**
         * @FieldDoc(
         *     description = "实付价格",
         * )
         */
        5: i32 currentPrice;

        /**
         * @FieldDoc(
         *     description = "商品名称",
         * )
         */
        6: optional string skuName;

        /**@FieldDoc(
                description = "申请退款重量，毫克 (抖音渠道需要)"
        )**/
        7: optional i64 applyWeight;

        /**@FieldDoc(
                description = "申请退款数量，(暂时仅美团渠道使用)"
        )**/
        8: optional i32 count;

        /**@FieldDoc(
                description = "商品spu，(暂时仅美团渠道使用)"
        )**/
        9: optional string customSpu;

}

/**
 * @TypeDoc(
 *     description = "金额退单请求参数"
 * )
 */
struct MoneyRefundRequest {
        /**
         * @FieldDoc(
         *     description = "",
         *     example = "1"
         * )
         */
        1: i32 channelId;
        /**
         * @FieldDoc(
         *     description = "订单ID",
         *     example = "19472638492372738"
         * )
         */
        2: string orderId;
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        3: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "门店id",
         * )
         */
        4: i64 storeId;
        /**
             * @FieldDoc(
             *     description = "退款原因code",
             * )
             */
        5: i32 reasonCode;
        /**
            * @FieldDoc(
            *     description = "详细退款原因信息",
            * )
            */
        6: string reasonRemarks;
        /**
         * @FieldDoc(
         *     description = "退款商品列表",
         * )
         */
        7: list<MoneyRefundProductInfoDTO> refundProductInfoList;
}