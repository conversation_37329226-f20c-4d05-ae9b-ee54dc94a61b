namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ResultStatus.thrift"
include "PoiAdjustOrderRequest.thrift"

/**
 * @TypeDoc(
 *     description = "获取渠道订单详情接口返回结果"
 * )
 */
struct PoiAdjustOrderResult {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = "status"
     * )
     */
    1: ResultStatus.ResultStatus status;
    /**
     * @FieldDoc(
     *     description = "渠道订单详细信息",
     *     example = "channelOrderDetail"
     * )
     */
    2: PoiAdjustOrderRequest.AfterAdjustOrderInfo afterAdjustOrderInfo;
}