namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "PoiRefundProductInfoDTO.thrift"

/**
 * @TypeDoc(
 *     description = "商家取消订单服务请求参数"
 * )
 */
struct PoiCancelOrderRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "操作类型",
     *     example = "true"
     * )
     */
    4: string reason;
    /**
     * @FieldDoc(
     *     description = "操作人",
     *     example = "admin"
     * )
     */
    5: i32 reason_code;

    /**
    * @FieldDoc(
    *     description = "操作人id",
    *     example = "1234"
    * )
    */
    6: optional string operatorId;

    /**
     * @FieldDoc(
     *     description = "退款商品列表,京东渠道必传",
     *     example = "[{\"skuId\" : \"134\", \"count\" : 2}]"
     * )
     */
    7: optional list<PoiRefundProductInfoDTO.RefundProductInfoDTO> refundProducts;
       /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "admin"
     * )
     */
    8: i64 storeId;
}