namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "RefundTypeEnum.thrift"
include "RefundProdectInfoDTO.thrift"

/**
 * @TypeDoc(
 *     description = "消息回调接口请求参数"
 * )
 */
struct OrderNotifyRequest {
    /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
    1: string tenantAppId;
    /**
     * @FieldDoc(
     *     description = "渠道CODE唯一标识",
     *     example = "1"
     * )
     */
    2: string channelCode;
    /**
     * @FieldDoc(
     *     description = "操作",
     *     example = "cmd"
     * )
     */
    3: string action;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    4: string orderId;
    /**
     * @FieldDoc(
     *     description = "消息状态（消息状态、订单状态）",
     *     example = "1"
     * )
     */
    5: string status;
    /**
     * @FieldDoc(
     *     description = "配送员名称",
     *     example = "admin"
     * )
     */
    6: string dispatcherName;
    /**
     * @FieldDoc(
     *     description = "配送员电话",
     *     example = "15888888888"
     * )
     */
    7: string dispatcherMobile;
    /**
     * @FieldDoc(
     *     description = "消息时间",
     *     example = "2019-02-14 14:00:00"
     * )
     */
    8: string timestamp;
    /**
     * @FieldDoc(
     *     description = "变更类型",
     *     example = "1"
     * )
     */
    9: string opType;
    /**
     * @FieldDoc(
     *     description = "变更人",
     *     example = "1"
     * )
     */
    10: string opRole;
    /**
     * @FieldDoc(
     *     description = "退款原因",
     *     example = "不想买了"
     * )
     */
    11: string refundReason;
    /**
     * @FieldDoc(
     *     description = "图片",
     *     example = {}
     * )
     */
    12: optional string pictureJson = "[]";
    /**
     * @FieldDoc(
     *     description = "退款金额",
     *     example = {}
     * )
     */
    13: string refundPrice;
    /**
     * @FieldDoc(
     *     description = "退款类型",
     *     example = {}
     * )
     */
    14: i32 refundType;
    /**
     * @FieldDoc(
     *     description = "通知类型",
     *     example = {}
     * )
     */
    15: string notifyType;
    /**
     * @FieldDoc(
     *     description = "退款商品信息",
     *     example = {}
     * )
     */
    16: optional string productJson = "[]";
    /**
    * @FieldDoc(
    *     description = "退款发起类型",
    *     example = {1}
    * )
    */
    17: string resType;
    /**
     * @FieldDoc(
     *     description = "是否申诉退款",
     *     example = {1}
     * )
     */
    18: i32 isAppeal;
    /**
     * @FieldDoc(
     *     description = "取消原因",
     *     example = "不要了"
     * )
     */
    19: string cancelReason;
    /**
     * @FieldDoc(
     *     description = "配送状态",
     *     example = "20"
     * )
     */
    20: string deliveryStatus;
    /**
     * @FieldDoc(
     *     description = "饿百消息数据",
     *     example = {}
     * )
     */
    21: string body;
    /**
     * @FieldDoc(
     *     description = "拣货完成信息",
     *     example = {}
     * )
     */
    22: string pickUpData;
    /**
     * @FieldDoc(
     *     description = "退款ID",
     *     example = {1}
     * )
     */
    23: string refundId;
    /**
     * @FieldDoc(
     *     description = "申请退款时间",
     *     example = {1}
     * )
     */
    24: string refundApplyTime;
    /**
     * @FieldDoc(
     *     description = "订单详情表单，主要解决美团外卖创建订单时获取的订单详情数据",
     *     example = {1}
     * )
     */
    25: map<string,string> orderDetailForm;
    /**
     * @FieldDoc(
     *     description = "京东消息信息",
     *     example = {}
     * )
     */
    26: optional string jdParamJson;
    /**
     * @FieldDoc(
     *     description = "签名",
     *     example = {}
     * )
     */
    27: optional string sign;
      /**
     * @FieldDoc(
     *     description = "退款状态",
     *     example = {}
     * )
     */
    28: optional i32 refundStatus;
        /**
     * @FieldDoc(
     *     description = "指退款成功后支付平台返回的流水号，一个退款ID对应一个退款流水号（",
     *     example = {}
     * )
     */
    29: optional i64 refundFlow;
        /**
     * @FieldDoc(
     *     description = "waimai门店id",
     *     example = {}
     * )
     */
    30: optional i64 wmPoiId;
        /**
     * @FieldDoc(
     *     description = "申请售后的场景",
     *     example = {}
     * )
     */
    31: optional i32 applyOpScenario;

       /**
     * @FieldDoc(
     *     description = "渠道门店编码",
     *     example = {}
     * )
     */
    32: optional string appPoiCode;

   /**
     * @FieldDoc(
     *     description = "美团配送内部id",
     *     example = {}
     * )
     */
    33: optional string mtPkgId;

    /**
     * @FieldDoc(
     *     description = "退款服务类型, 区分是否已开通退货退款售后业务。 未开通的场景： 0-退款流程或申诉流程 已开通场景： 1-仅退款流程 2-退款退货流程",
     *     example = {}
     * )
     */
    34: optional string serviceType;

    /**
     * @FieldDoc(
     *     description = "加密京东消息信息",
     *     example = {}
     * )
     */
    35: optional string encryptJdParamJson;

    /**
    * @FieldDoc(
    *     description = "订单配送方式",
    *     example = {}
    * )
    */
    36: optional string platformCode;
    
    /**
    * @FieldDoc(
     *     description = "失败类型",
     *     example = {}
     * )
     */
    37: optional string failType;

    /**
     * @FieldDoc(
     *     description = "处理的最晚时间",
     *     example = {}
     * )
     */
    38: optional string dealDeadline;

    /**
         * @FieldDoc(
         *     description = "极速退",
         *     example = "1"
         * )
         */
    39: optional i32 extremeSpeedRefund;

     /**
             * @FieldDoc(
             *     description = "客户端类型",
             *     example = {}
             * )
             */
    40: optional string orderSource;

    /**
         * @FieldDoc(
         *     description = "退款操作code",
         *     example = {}
         * )
     */
     41: optional i32 opCode;

    /**
             * @FieldDoc(
             *     description = "订单取消的原因code",
             *     example = {}
             * )
       */
     42: optional i32 reasonCode;

     /**
                  * @FieldDoc(
                  *     description = "订单取消操作人类型",
                  *     example = {}
                  * )
            */
     43: optional i32 dealOpType;

        /**
          * @FieldDoc(
          *     description = "是否锁单",
          *     example = {}
          * )
          */
     44: optional i32 isLockedOrder;

      /**
          * @FieldDoc(
          *     description = "赔付类型",
          *     example = {}
          * )
          */
     45: optional i32 compensateType;
      /**
        * @FieldDoc(
        *     description = "赔付责任方",
        *     example = {}
        * )
        */
      46: optional i32 responsibleParty;
      /**
        * @FieldDoc(
        *     description = "订单详情表单，主要解决美团外卖创建订单时获取的订单详情数据, 新数据字段",
        *     example = {1}
        * )
     */
      47: map<string,string> channelOrderDetailForm;

        /**
         * @FieldDoc(
         *     description = "变更时间",
         *     example = {1558909972}
         * )
         */
      48: optional string changeTime;
        /**
        * @FieldDoc(
        *     description = "售后单退货物流方式(return_goods_way)为4-美团取退、3-跑腿时，售后退货的物流状态",
        *     example = {}
        * )
        */
      49: optional i32 logisticsStatus;

        /**
        * @FieldDoc(
        *     description = "退货退款流程(service_type = 2)下，当售后单状态(status)为21-终审已同意时，为1标识是否是【仅退款无需退货】。",
        *     example = {}
        * )
        */
      50: optional i32 finalReviewType;

        /**
        * @FieldDoc(
        *     description = "用户发起退货退款时，所选的物流方式 1-自行退回 2-商家自行取回或商家安排骑手取货 3-跑腿 4-美团取退。",
        *     example = {}
        * )
        */
      51: optional i32 returnGoodsWay;
       /**
          * @FieldDoc(
          *     description = "订单是否发四轮配送 1-否 2-是",
          *     example = {}
          * )
          */
      52: optional i32 isFourWheelDelivery;
      /**
        * @FieldDoc(
        *     description = "当订单是四轮配送时有值。1-手动发四轮 2-自动发四轮",
        *     example = {}
        * )
        */
      53: optional i32 isManual;

      /**
       * @FieldDoc(
       *     description = "渠道推送消息执行时长",
       *     example = {}
       * )
       */
     54: optional i64 runTime;

     /**
     * @FieldDoc(
     *     description = "接收消息时间",
     *     example = {}
     * )
    */
    55: optional i64 pushDate;

    /**
        * @FieldDoc(
        *     description = "美团跑腿订单聚合平台可发配送状态 0：可发配送 1：不可发配送",
        *     example = {}
        * )
    */
    56: optional i32 deliveryAvailable;

}