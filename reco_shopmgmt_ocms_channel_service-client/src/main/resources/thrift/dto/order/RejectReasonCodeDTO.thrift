namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order
/**
 * @TypeDoc(
 *     description = "获取审核拒绝原因的数据"
 * )
 */
struct RejectReasonCodeDTO {

    /**@FieldDoc(
            description = "售后审核拒绝原因枚举编码",example = "1"
    )**/
    1: i64 rejectReasonCode;

    /**@FieldDoc(
            description = "售后审核拒绝原因文案",example = "reason"
    )**/
    2: string reason;

    /**@FieldDoc(
            description = "凭证描述文案",example = "evidenceDescription"
    )**/
    3: string evidenceDescription;

    /**@FieldDoc(
            description = "是否需要上传凭证，Y必填，N非必填",example = "Y"
    )**/
    4: string evidenceNeed;

    /**@FieldDoc(
            description = "凭证示例图片链接",example = "image"
    )**/
    5: string image;

    /**@FieldDoc(
            description = "订单类型，即订单信息中order_type   枚举：0-普通实物订单 1-全款预售订单  2-虚拟商品订单 3-快闪店订单 4-电子券  5-三方核销 6-服务市场 -1-通用,不考虑订单类型",
            example = "1"
    )**/
    6: i64 orderType;

    /**@FieldDoc(
            description = "是否收到货，0未收到 1收到 -1通用,不考虑是否收到货"
            ,example = "1"
            )**/
    7: i64 pkg;
} 