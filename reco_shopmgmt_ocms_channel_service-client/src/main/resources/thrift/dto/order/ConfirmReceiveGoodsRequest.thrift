namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "商家确认收到退货请求参数"
 * )
 */
struct PoiConfirmReceiveGoodsRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;

    /**
     * @FieldDoc(
     *     description = "操作时间",
     *     example = ""
     * )
     */
    4: i64 operateTime;

    /**
     * @FieldDoc(
     *     description = "内部门店编码",
     *     example = "129"
     * )
     */
    5: i64 storeId;
}

