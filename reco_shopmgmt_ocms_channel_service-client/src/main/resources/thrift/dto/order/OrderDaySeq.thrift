namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order


/**
 * @TypeDoc(
 *     description = "订单日单号门店相关信息"
 * )
 */
struct OrderDaySeq {
    /**
     * @FieldDoc(
     *     description = "订单id",
     *     example = "101110"
     * )
     */
    1: i64 orderId;
    /**
     * @FieldDoc(
     *     description = "日单号，非必传",
     *     example = "12"
     * )
     */
    2: i32 daySeq;

     /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    3: i64 storeId;


}