namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

struct RefundProductDTO {
    /**
     * @FieldDoc(
     *     description = "商品名称"
     * )
     */
    1: string skuName;
    /**
     * @FieldDoc(
     *     description = "商品sku"
     * )
     */
    2: string skuId;
    /**
     * @FieldDoc(
     *     description = "退款数量"
     * )
     */
    3: i32 count;
    /**
     * @FieldDoc(
     *     description = "退款金额"
     * )
     */
    4: i32 skuRefundAmount;

    /**
     * @FieldDoc(
     *     description = "退款重量"
     * )
     */
    5:  optional double refundWeight;

    /**
     * @FieldDoc(
     *     description = "商品spu"
     * )
     */
    6: string customSpu;

    /**
     * @FieldDoc(
     *     description = "商品spu"
     * )
     */
    7: i32 foodPrice;

    /**
     * @FieldDoc(
     *     description = "包装盒费用"
     * )
     */
    8: i32 boxPrice;
    /**
     * @FieldDoc(
     *     description = "包装盒数量"
     * )
     */
    9: i32 boxNum;


    /**
     * @FieldDoc(
     *     description = "商品规格"
     * )
     */
    10: string spec;
       /**
     * @FieldDoc(
     *     description = "商品行id"
     * )
     */
    11: i64 itemId;

    /**
    * @FieldDoc(
    *     description = "三方平台skuId",
    *     example = "123"
    * )
    */
    12: string thirdPartSkuId;

    /**
    * @FieldDoc(
    *     description = "三方平台spuId",
    *     example = "123"
    * )
    */
    13: string thirdPartSpuId;

    /**
    * @FieldDoc(
    *     description = "三方平台itemId",
    *     example = "123"
    * )
    */
    14: string thirdPartItemId;


    /**
    * @FieldDoc(
    *     description = "对应渠道订单明细ID",
    *     example = ""
    * )
    */
     15: string channelOrderItemId;


    /**
     * @FieldDoc(
     *     description = "退给商家优惠"
     * )
     */
    17: i32 refundPoiPromotion;
    /**
     * @FieldDoc(
     *     description = "退给平台优惠"
     * )
     */
    18: i32 refundPlatPromotion;

    /**
     * @FieldDoc(
     *     description = "退给平台订单级别优惠"
     * )
     */
    19: i32 refundPlatOrderPromotion;

    /**
     * @FieldDoc(
     *     description = "退给商家订单级别优惠"
     * )
     */
    20: i32 refundPoiOrderPromotion;

     /**
         * @FieldDoc(
         *     description = "退给平台商品级别优惠"
         * )
         */
     21: i32 refundPlatItemPromotion;

        /**
         * @FieldDoc(
         *     description = "退给商家商品级别优惠"
         * )
         */
      22: i32 refundPoiItemPromotion;

    /**
         * @FieldDoc(
         *     description = "应退餐盒费"
         * )
         */
    23: i32 boxAmt;

    /**
         * @FieldDoc(
         *     description = "金额退数量"
         * )
         */
    24: double partialRefundCount;

    /**
         * @FieldDoc(
         *     description = "0:整件退，部分退：1"
         * )
         */
    25: i32 partialRefundFlag;


    /**
     * @FieldDoc(
     *     description = "部分退款比例（部分退款的比例，仅部分退款商品才有
     *     。同时可用于标记该商品是否存在部分退款的情况。小数且保留两位小数）
     *     商品全额退款的时候此字段会展示100%"
     * )
     */
    26: double partialRefundRatioShow

    /**
         * @FieldDoc(
         *     description = "退货具体数量，有赞计算可退数量时使用"
         * )
         */
    27: optional double refundCountDecimal;

    /**
         * @FieldDoc(
         *     description = "是否赠品"
         * )
         */
    28: optional bool freeGift;

    /**
         * @FieldDoc(
         *     description = "门店单品总优惠"
         * )
         */
    29: optional i32 poiTotalItemPromotion;

    /**
         * @FieldDoc(
         *     description = "平台单品总优惠"
         * )
         */
    30: optional i32 platTotalItemPromotion;

    /**
         * @FieldDoc(
         *     description = "美团渠道金额宽容度数量"
         * )
         */
    31: optional i32 flexCount;

}