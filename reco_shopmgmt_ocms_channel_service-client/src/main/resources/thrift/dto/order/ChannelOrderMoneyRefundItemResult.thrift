namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ResultStatus.thrift"

/**
 * @TypeDoc(
 *     description = "可退商品详情"
 * )
 */
struct ChannelOrderMoneyRefundItemDTO{
        /**
         * @FieldDoc(
         *     description = "商品skuid",
         * )
         */
        1: string skuId;
        /**
         * @FieldDoc(
         *     description = "商品名称",
         * )
         */
        2: string skuName;
        /**
         * @FieldDoc(
         *     description = "可退商品数量",
         * )
         */
        3: i32 canRefundSkuCount;
        /**
         * @FieldDoc(
         *     description = "可退金额，分",
         * )
         */
        4: i32 canRefundMoney;
        /**
         * @FieldDoc(
         *     description = "实付价格",
         * )
         */
        5: i32 currentPrice;
        /**
         * @FieldDoc(
         *     description = "渠道订单商品行id",
         * )
         */
        6: string channelItemId;
        /**
         * @FieldDoc(
         *     description = "渠道订单商品spu",
         * )
         */
        7: optional string customSpu;
}

/**
 * @TypeDoc(
 *     description = "查询渠道订单金额退可退商品列表返回结果"
 * )
 */
struct ChannelOrderMoneyRefundItemResult {
        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus status;
        /**
         * @FieldDoc(
         *     description = "可退商品列表",
         *     example = "channelOrderDetail"
         * )
         */
        2: list<ChannelOrderMoneyRefundItemDTO> moneyRefundItemDTOList;
}