namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "EvidenceDTO.thrift"

/**
 * @TypeDoc(
 *     description = "订单退货退款请求回调服务请求参数"
 * )
 */
struct RefundGoodsRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "退款原因",
     *     example = "因***原因退款"
     * )
     */
    4: string reason;
    /**
     * @FieldDoc(
     *     description = "退款原因code",
     *     example = "因***原因确认退款"
     * )
     */
    5: i32 reasonCode;
    /**
     * @FieldDoc(
     *     description = "售前、售后申请（京东必传）",
     *     example = "1"
     * )
     */
    6: i32 afsApplyType;
        /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "1"
     * )
     */
    7: i64 storeId;
     /**
     * @FieldDoc(
     *     description = "本次退款申请的退款id，可用于商家区分多次部分退款。",
     *     example = "1"
     * )
     */
    8: string afterSaleId;
     /**
     * @FieldDoc(
     *     description = "审核当前售后单的结果类型，取值范围：0-同意退货；1-同意退款；2-驳回退货/退款。",
     *     example = "1"
     * )
     */
    9: i32 reviewType;
    /**
     * @FieldDoc(
     *     description = "审核阶段：1-初审阶段；2-终审阶段",
     *     example = "1"
     * )
     */
    10: i32 auditStage;


     /**
      * @FieldDoc(
      *     description = "凭据列表，上送给渠道",
      *     example = "1"
      * )
    */
    11: optional list<EvidenceDTO.EvidenceDTO> evidenceDTOList;
    /**
     * @FieldDoc(
     *     description = "是否返货",
     *     example = "true"
     * )
     */
    12: bool isReturnGoods;
}