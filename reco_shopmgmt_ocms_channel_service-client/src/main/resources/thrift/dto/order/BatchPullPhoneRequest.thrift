namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "批量拉取用户真实手机号接口请求参数"
 * )
 */
struct BatchPullPhoneRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = "1"
     * )
     */
    3: optional i64 storeId;
    /**
     * @FieldDoc(
     *     description = "偏移量",
     *     example = {}
     * )
     */
    4: i32 offset;
    /**
     * @FieldDoc(
     *     description = "每页条数",
     *     example = {}
     * )
     */
    5: i32 limit;
}