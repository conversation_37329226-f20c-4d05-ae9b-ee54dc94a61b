namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ResultStatus.thrift"

/**
 * @TypeDoc(
 *     description = "查询退单商家反货运费返回数据"
 * )
 */
struct GetOrderRefundGoodsFeeResult{
        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus status;
        /**
         * @FieldDoc(
         *     description = "返货费用，单位为元",
         *     example = "11.00"
         * )
         */
        2: string totalReturnGoodsFee;
}