namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "核验自提码参数"
 * )
 */
struct VerifySelfFetchCodeRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "数字自提码",
     *     example = "50412092715"
     * )
     */
    3: optional string code;
    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "123"
     * )
     */
    4: i64 storeId;
     /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    5: optional string orderId;
    /**
    * @FieldDoc(
    *     description = "操作人",
    *     example = "1234"
    * )
    */
    6: optional string operator;

    /**
     * @FieldDoc(
     *     description = "地址自提码",
     *     example = "elenr://self-pick-up/d9e9d3c2-2d2c-44fa-bb11-dd1ac2d2b650"
     * )
     */
    7: optional string qrCode;
}