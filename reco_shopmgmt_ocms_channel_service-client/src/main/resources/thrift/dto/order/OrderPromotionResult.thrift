namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ResultStatus.thrift"


struct OrderItemPromotionResult {

    /**
     * @FieldDoc(
     *     description = "平台整单优惠",
     *     example = "1"
     * )
    */
    1: i32 platPromotion;

    /**
     * @FieldDoc(
     *     description = "商家整单优惠",
     *     example = "1"
     * )
    */
    2: i32 poiPromotion;

    /**
     * @FieldDoc(
     *     description = "平台单品优惠",
     *     example = "1"
     * )
    */
    3: i32 platItemPromotion;

    /**
     * @FieldDoc(
     *     description = "商家单品优惠",
     *     example = "1"
     * )
    */
    4: i32 poiItemPromotion;

    /**
     * @FieldDoc(
     *     description = "商家整合营销",
     *     example = "1"
     * )
    */
    5: i32 poiMarketPromotion;

    /**
     * @FieldDoc(
     *     description = "整合营销推广结算(供应商)",
     *     example = "1"
     * )
    */
    6: i32 supplierMarketPromotion;

    /**
     * @FieldDoc(
     *     description = "整合营销推广结算(供应商)",
     *     example = "1"
     * )
    */
    7: string customSkuId;

    /**
     * @FieldDoc(
     *     description = "数量",
     *     example = "1"
     * )
    */
    8: i32 count;

    /**
     * @FieldDoc(
     *     description = "促销价钱",
     *     example = "1"
     * )
    */
    9: i32 promotionPrice;



    /**
     * @FieldDoc(
     *     description = "渠道SkuId",
     *     example = "1"
     * )
    */
    10: string channelSkuId

    /**
     * @FieldDoc(
     *     description = "sku Name名称",
     *     example = "1"
     * )
    */
    11: string skuName;

    /**
     * @FieldDoc(
     *     description = "upc 条码",
     *     example = "1"
     * )
    */
    12: string upc;

    /**
     * @FieldDoc(
     *     description = "渠道行号",
     *     example = "1"
     * )
    */
    13: optional string channelItemId;
}


/**
 * @TypeDoc(
 *     description = "订单优惠信息"
 * )
 */
struct OrderPromotionResult {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = "status"
     * )
     */
    1: ResultStatus.ResultStatus status;



    /**
     * @FieldDoc(
     *     description = "商品优惠信息",
     *     example = "1"
     * )
    */
    2: list<OrderItemPromotionResult> skuPromotions;
}