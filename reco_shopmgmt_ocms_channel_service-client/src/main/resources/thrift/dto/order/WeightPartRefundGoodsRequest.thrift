namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "查询订单克重详情接口请求参数"
 * )
 */
struct WeightPartRefundGoodsRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = "12"
     * )
     */
    2: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "100"
     * )
     */
    3: i32 channelId;

    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    4: string orderId;
}