namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order
include "OrderCompensationDTO.thrift"
include "ResultStatus.thrift"
/**
 * @TypeDoc(
 *     description = "获取渠道订单赔付信息返回结果"
 * )
 */
struct QueryCompensationOrderListResult {
        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus status;
        /**
         * @FieldDoc(
         *     description = "渠道订单号列表信息",
         *     example = "channelOrderDetail"
         * )
         */
        2: list<OrderCompensationDTO.OrderCompensationDTO> orderCompensationDTOList;
}