namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ResultStatus.thrift"
include "OrderShouldSettlementInfo.thrift"
include "GoodsActivityDetailDTO.thrift"
include "ActivityShareDetailDTO.thrift"
include "OrderDiscountDetailDTO.thrift"

/**
 * @TypeDoc(
 *     description = "获取商家应结金额"
 * )
 */
struct GoodsSettlementResult {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = "status"
     * )
     */
    1: ResultStatus.ResultStatus status;
    /**
     * @FieldDoc(
     *     description = "商家应结金额明细",
     *     example = ""
     * )
     */
    2: list<GoodsActivityDetailDTO.GoodsActivityDetailDTO> goodSettlementInfos;
    /**
     * @FieldDoc(
     *     description = "商品分摊的活动金额明细",
     *     example = ""
     * )
     */
    3: optional list<ActivityShareDetailDTO.ActivityShareDetailDTO> activityShareDetailDTOList;
    /**
     * @FieldDoc(
     *     description = "运费优惠活动",
     *     example = ""
     * )
     */
    4: optional list<OrderDiscountDetailDTO.OrderDiscountDetailDTO> freightActivityDTOList;
}