namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "订单发票信息"
 * )
 */
struct OrderInvoiceDetailDTO {
    /**
     * @FieldDoc(
     *     description = "发票抬头",
     *     example = "无"
     * )
     */
    1: string invoiceTitle;
    /**
     * @FieldDoc(
     *     description = "纳税人识别号",
     *     example = "无"
     * )
     */
    2: string taxNo;
    /**
     * @FieldDoc(
     *     description = "发票类型0.纸质发票1.电子发票",
     *     example = "1"
     * )
     */
    3: optional i32 invoiceType = 1;
    /**
     * @FieldDoc(
     *     description = "发票金额 -1000表示JDDJ回传为null，然后后续重新设值为实付金额",
     *     example = "1"
     * )
     */
    4: optional i32 invoiceMoney = -1000;
}