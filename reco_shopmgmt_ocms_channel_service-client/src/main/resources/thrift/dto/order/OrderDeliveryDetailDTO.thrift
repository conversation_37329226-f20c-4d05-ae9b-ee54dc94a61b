namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "订单配送信息"
 * )
 */
struct OrderDeliveryDetailDTO {
    /**
     * @FieldDoc(
     *     description = "用户姓名",
     *     example = "张三"
     * )
     */
    1: string userName;
    /**
     * @FieldDoc(
     *     description = "用户下单地址",
     *     example = "望京"
     * )
     */
    2: string userAddress;
    /**
     * @FieldDoc(
     *     description = "用户联系电话,可能是虚拟号，可能是真实手机号",
     *     example = "15888888888"
     * )
     */
    3: string userPhone;
    /**
     * @FieldDoc(
     *     description = "预计送达时间",
     *     example = "15288738000"
     * )
     */
    4: i64 arrivalTime;
    /**
     * @FieldDoc(
     *     description = "配送方式",
     *     example = "美团配送"
     * )
     */
    5: string deliveryMethod;
    /**
     * @FieldDoc(
     *     description = "最迟到达时间",
     *     example = "15288738000"
     * )
     */
    6: i64 arrivalEndTime;
    /**
     * @FieldDoc(
     *     description = "中间号是否过期",
     *     example = "true"
     * )
     */
    7: bool userPhoneIsValid;
    /**
         * @FieldDoc(
         *     description = "订单收货地址的纬度  火星坐标系",
         *     example = ""
         * )
         */
    8: double latitude;

    /**
     * @FieldDoc(
     *     description = "订单收货地址的经度  火星坐标系",
     *     example = ""
     * )
     */
    9: double longitude;
    /**
     * @FieldDoc(
     *     description = "隐私号码",
     *     example = "185****5688"
     * )
     */
    10: string userPrivacyPhone;

    /**
     * @FieldDoc(
     *     description = "是否为商家自配送  0:否 1:是",
     *     example = ""
     * )
     */
    11: i32 isSelfDelivery;

    /**
     * @FieldDoc(
     *     description = "原始配送类型  0:未知 5:专送 10:快送 15:混合送 20:众包 25:商家自配送",
     *     example = ""
     * )
     */
    12: i32 originalDeliveryType;
    /**
     * @FieldDoc(
     *     description = "用户备用隐私号码",
     *     example = "["185****5688"]"
     * )
     */
    13: list<string> backUpUserPrivacyPhone;

    /**
    * 是否使用隐私号
    **/
    14: i32 usePrivacyPhone;


    /**
    * 自提码
    **/
    15: string selfFetchCode;

     /**
          * @FieldDoc(
          *     description = "商家最晚拣货完成时间"
            * )
    **/
    16: optional i64 pickUpEndTime;

     /**
       * @FieldDoc(
       * description = "闪电送商家建议拣货完成时间"
       * )
     **/
     17: optional i64 fastDeliveryPickUpTime;

    /**
     * @FieldDoc(
     *     description = "美团集合店商家电话"
     * )
     */
     18: optional string gatherPoiPhone;

    /**
     * @FieldDoc(
     *     description = "美团集合店售后电话"
     * )
     */
     19: optional string gatherAfterSalesPhone;

    /**
     * @FieldDoc(
     *     description = "美团集合店自定义电话"
     * )
     */
     20: optional string gatherCustomPhone;

    /**
     * @FieldDoc(
     *     description = "美团集合店售前电话"
     * )
     */
     21: optional string gatherPreSalePhone;

     /**
      * 贵品取货码
      */
     22: optional string expensiveProductPickupCode;
}