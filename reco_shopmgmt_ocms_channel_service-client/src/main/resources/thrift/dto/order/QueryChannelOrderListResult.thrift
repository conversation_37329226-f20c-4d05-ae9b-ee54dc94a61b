namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order
include "ChannelOrderDetailDTO.thrift"
include "ResultStatus.thrift"
/**
 * @TypeDoc(
 *     description = "获取渠道订单分页列表接口返回结果"
 * )
 */
struct QueryChannelOrderListResult {
        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus status;
        /**
         * @FieldDoc(
         *     description = "渠道订单号列表信息",
         *     example = "channelOrderDetail"
         * )
         */
        2: list<string> channelOrderIdList;
        /**
         * @FieldDoc(
         *     description = "当前页",
         *     example = "1"
         * )
         */
        3: i32 page;
        /**
         * @FieldDoc(
         *     description = "总页数",
         *     example = "10"
         * )
         */
        4: i32 pageCount;
        /**
         * @FieldDoc(
         *     description = "总数",
         *     example = "100"
         * )
         */
        5: i32 total;


        /**
         * @FieldDoc(
         *     description = "游标",
         *     example = "100"
         * )
         */
        6: string vernier;

        /**
         * @FieldDoc(
         *     description = "是否有下一页数据",
         *     example = "1"
         * )
         */
        7: i32 hasMore;
          /**
         * @FieldDoc(
         *     description = "key 门店Id value是 订单Id",
         *     example = "100"
         * )
         */
        8: map<string,i64> channelOrderIdMap;

        /**
         * @FieldDoc(
         *     description = "下一页码,目前仅淘鲜达使用",
         *     example = "1"
         * )
         */
        9: i32 nextPage;


}