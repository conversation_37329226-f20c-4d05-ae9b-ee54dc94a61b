namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "配送状态"
 * )
 */
struct LogisticsStatusDTO {
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "143470120000"
     * )
     */
    1: string orderId;
    /**
     * @FieldDoc(
     *     description = "配送状态",
     *     example = "1"
     * )
     */
    2: i32 status;
    /**
     * @FieldDoc(
     *     description = "骑手名字",
     *     example = "账上"
     * )
     */
    3: string riderName;
    /**
     * @FieldDoc(
     *     description = "骑手电话",
     *     example = "13500913241"
     * )
     */
    4: string riderPhone;

}