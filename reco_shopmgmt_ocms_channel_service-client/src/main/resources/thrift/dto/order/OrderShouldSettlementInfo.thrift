namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "订单发票信息"
 * )
 */
struct OrderShouldSettlementInfo {
    /**
     * @FieldDoc(
     *     description = "订单号",
     *     example = ""
     * )
     */
    1: required string orderId;
    /**
     * @FieldDoc(
     *     description = "商家应结金额",
     *     example = "10"
     * )
     */
    2: required i32 settlementAmount;
    /**
     * @FieldDoc(
     *     description = "用户实付金额",
     *     example = "1"
     * )
     */
    3: optional i32 userActualPaidGoodsMoney;

    /**
     * @FieldDoc(
     *     description = "完成计费时间",
     *     example = ""
     * )
     */
    4: required i64 billTime;

    /**
     * @FieldDoc(
     *     description = "费用明细",
     *     example = ""
     * )
     */
    5: map<string, i64> billDetailItem

    /**
     * @FieldDoc(
     *     description = "总佣金",
     *     example = ""
     * )
     */
    6: i32 totalGoodsCommission;

    /**
     * @FieldDoc(
     *     description = "运费佣金",
     *     example = ""
     * )
     */
    7: i32 freightCommission;

    /**
     * @FieldDoc(
     *     description = "保底佣金补差",
     *     example = ""
     * )
     */
    8: i32 guaranteedCommission;

    /**
     * @FieldDoc(
     *     description = "商家承担运费补贴",
     *     example = ""
     * )
     */
    9: i32 venderFreightDiscountMoney;

    /**
     * @FieldDoc(
     *     description = "商家付达达小费",
     *     example = ""
     * )
     */
    10: i32 venderPaidTips;
    /**
     * @FieldDoc(
     *     description = "远距离运费",
     *     example = ""
     * )
     */
    11: i32 distanceFreightMoney;

    /**
     * @FieldDoc(
     *     description = "商家自配送运费",
     *     example = ""
     * )
     */
    12: i32 venderDeliveryFreight;
    /**
     * @FieldDoc(
     *     description = "平台承担运费补贴",
     *     example = ""
     * )
     */
    13: i32 platFreightDiscountMoney;
    /**
     * @FieldDoc(
     *     description = "内部门店编码",
     *     example = "129"
     * )
     */
    14: i64 storeId;
    /**
     * @FieldDoc(
     *     description = "基础服务费",
     *     example = "129"
     * )
     */
    15: i32 baseServiceMoney;
}