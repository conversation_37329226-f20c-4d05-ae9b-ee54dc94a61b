namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order



/**
 * @TypeDoc(
 *     description = "商家发起部分退款请求参数"
 * )
 */
struct CalculateRefundRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "app_food_code",
     *     example = "app_food_code"
     * )
     */
    4: string appFoodCode;
    /**
     * @FieldDoc(
     *     description = "sku_id",
     *     example = "[{\"skuId\" : \"134\", \"count\" : 2}]"
     * )
     */
    5: string skuId;
    /**
    * @FieldDoc(
    *     description = "实际重量",
    *     example = "1.1"
    * )
    */
    6: double actualWeight;
      /**
    * @FieldDoc(
    *     description = "门店id",
    *     example = "11"
    * )
    */
    7: i64 storeId;
    /**
    * @FieldDoc(
    *     description = "订单内商品行维度的商品标识id",
    *     example = "23682975"
    * )
    */
    8: string itemId;

}