namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order



/**
 * @TypeDoc(
 *     description = "商家发起部分退款请求参数"
 * )
 */
struct DaySeqRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
        /**
     * @FieldDoc(
     *     description = "中台门店ids",
     *     example = "101110"
     * )
     */
    3: list<i64> storeIds;
    /**
     * @FieldDoc(
     *     description = "日单号，非必传",
     *     example = "12"
     * )
     */
    4: list<i32> daySeqs;

     /**
     * @FieldDoc(
     *     description = "时间",
     *     example = ""
     * )
     */
    5: i64 dateTime;


}