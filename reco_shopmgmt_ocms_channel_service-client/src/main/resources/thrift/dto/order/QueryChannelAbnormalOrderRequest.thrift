namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order


struct QueryChannelAbnormalOrderRequest{
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        2: i32 channelId;
        /**
         * @FieldDoc(
         *     description = "异常类型ActivityTypeEnum",
         *     example = "19472638492372738"
         * )
         */
        3: i32 type;
        /**
         * @FieldDoc(
         *     description = "分页偏移量",
         *     example = "admin"
         * )
         */
        4: i32 offset;
        /**
         * @FieldDoc(
         *     description = "分页拉取每页数量，最大不超过200，牵牛花最大100",
         *     example = "admin"
         * )
         */
        5: i32 limit;

        /**
         * @FieldDoc(
         *     description = "起始时间戳",
         *     example = "10"
         * )
         */
        6: i64 startTime;

        /**
         * @FieldDoc(
         *     description = "结束时间戳,不能晚于当前时间",
         *     example = "10"
         * )
         */
        7: i64 endTime;
        /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = "10"
         * )
         */
        8: optional i64 storeId;
        /**
         * @FieldDoc(
         *     description = "页码，从1开始",
         *     example = "10"
         * )
         */
        9: optional i32 pageNo;
        /**
         * @FieldDoc(
         *     description = "appId",
         *     example = "1"
         * )
         */
        10: optional i64 appId;
}