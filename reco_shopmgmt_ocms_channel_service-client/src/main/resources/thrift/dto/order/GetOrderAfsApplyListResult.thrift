namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ResultStatus.thrift"
include "OrderAfsApplyDTO.thrift"

/**
 * @TypeDoc(
 *     description = "查询订单最新状态服务接口返回结果"
 * )
 */
struct GetOrderAfsApplyListResult {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = "status"
     * )
     */
    1: ResultStatus.ResultStatus status;
    /**
    * @FieldDoc(
    *     description = "渠道订单售后列表",
    *     example = "详见OrderAfsApplyDTO"
    * )
    */
    2: list<OrderAfsApplyDTO.OrderAfsApplyDTO> afsApplyList;
   /**
    * @FieldDoc(
    *     description = "地址变更费（美团用）",
    *     example = "10.00"
    * )
    */
    3: optional string addressChangeFee;
}