namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order
/**
 * @TypeDoc(
 *     description = "查询渠道部分退款商品详情返回"
 * )
 */
struct PartRefundGoodDetailDTO {
        /**
          * @FieldDoc(
          *     description = "商品名称"
          * )
          */
        1: string skuName;
        /**
         * @FieldDoc(
         *     description = "商品sku"
         * )
         */
        2: string skuId;
        /**
         * @FieldDoc(
         *     description = "可退数量"
         * )
         */
        3: i32 count;
        /**
         * @FieldDoc(
         *     description = "商品项金额"
         * )
         */
        4: i32 refundPrice;
        /**
         * @FieldDoc(
         *     description = "appFoodCode， 见customSpu"
         * )
         * @depressed
         */
        5:string appFoodCode;

        /**
        * @FieldDoc(
        *     description = "商品sku"
        * )
        */
        6: string customSpu;
           /**
        * @FieldDoc(
        *     description = "商品行ID"
        * )
        */
        7: i64 itemId;

        /**
        * @FieldDoc(
        *     description = "三方平台skuId",
        *     example = "123"
        * )
        */
        8: string thirdPartSkuId;

        /**
        * @FieldDoc(
        *     description = "三方平台spuId",
        *     example = "123"
        * )
        */
        9: string thirdPartSpuId;

        /**
        * @FieldDoc(
        *     description = "三方平台itemId",
        *     example = "123"
        * )
        */
        10: string thirdPartItemId;
}