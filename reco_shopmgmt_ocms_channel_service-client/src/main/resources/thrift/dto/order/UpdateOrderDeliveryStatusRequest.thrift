namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order


struct UpdateOrderDeliveryStatusRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        2: i32 channelId;
        /**
         * @FieldDoc(
         *     description = "订单ID",
         *     example = "19472638492372738"
         * )
         */
        3: string orderId;
        /**
         * @FieldDoc(
         *     description = "配送状态 参见DeliveryStatus",
         *     example = ""
         * )
         */
        4: i32 status
                /**
         * @FieldDoc(
         *     description = "门店id",
         *     example = "1"
         * )
         */
        5: i64 storeId;
}