namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "查询退单商家反货运费请求参数"
 * )
 */
struct GetOrderRefundGoodsFeeRequest {
        /**
         * @FieldDoc(
         *     description = "渠道id",
         *     example = "1"
         * )
         */
        1: i32 channelId;
        /**
         * @FieldDoc(
         *     description = "订单ID",
         *     example = "19472638492372738"
         * )
         */
        2: string orderId;
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        3: i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "门店id",
         * )
         */
        4: i64 storeId;

}