namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "RefundProductDTO.thrift"
include "ShopCardFee.thrift"
include "RedpackInfo.thrift"

/**
 * @TypeDoc(
 *     description = "订单售后记录DTO"
 * )
 */
struct OrderAfsApplyDTO {
    /**
     * @FieldDoc(
     *     description = "渠道类型，参考ChannelType",
     *     example = "100"
     * )
     */
    1: i32 channelType;
    /**
     * @FieldDoc(
     *     description = "渠道订单ID",
     *     example = "143470120000"
     * )
     */
    2: string channelOrderId;
    /**
     * @FieldDoc(
     *     description = "售后ID",
     *     example = ""
     * )
     */
    3: string afterSaleId;
    /**
     * @FieldDoc(
     *     description = "申请售后时间",
     *     example = ""
     * )
     */
    4: i64 refundApplyTime;
     /**
     * @FieldDoc(
     *     description = "退款金额",
     *     example = ""
     * )
     */
    5: i32 refundPrice;
     /**
     * @FieldDoc(
     *     description = "售后状态，参考OrderAllRefundType",
     *     example = ""
     * )
     */
    6: i32 afterSaleStatus;
     /**
     * @FieldDoc(
     *     description = "售后状态，参考AfterSaleRecordStatus",
     *     example = ""
     * )
     */
    7: i32 afterSaleRecordStatus;
     /**
     * @FieldDoc(
     *     description = "退款类型：全单退、部分退，elm的只能获取部分退",
     *     example = ""
     * )
     */
    8: i32 refundType;
     /**
     * @FieldDoc(
     *     description = "售后商品信息",
     *     example = ""
     * )
     */
    9: list<RefundProductDTO.RefundProductDTO> afsProductList;
    /**
     * @FieldDoc(
     *     description = "售后审核时间",
     *     example = ""
     * )
     */
    10: i64 refundAuditTime;

    /**
     * @FieldDoc(
     *     description = "申请原因",
     *     example = ""
     * )
     */
    11: string applyReason;


    /**
    * @FieldDoc(
    *     description = "商家审批原因",
    *     example = ""
    * )
    */
    12: string resReason;

    /**
     * @FieldDoc(
     *     description = "是否申诉退款",
     *     example = "true"
     * )
     */
    13: bool isAppeal;
        /**
     * @FieldDoc(
     *     description = "退款状态",
     *     example = "1"
     * )
     */
    14: i32 refundStatus;
        /**
     * @FieldDoc(
     *     description = "操作员类型",
     *     example = "1"
     * )
     */
    15: i32 applyOpType;
            /**
     * @FieldDoc(
     *     description = "指退款成功后支付平台返回的流水号，一个退款ID对应一个退款流水号",
     *     example = "1"
     * )
     */
    16: string refundFlow;

    /**
     * @FieldDoc(
     *     description = "退款原因图片",
     *     example = "1"
     * )
     */
    17: string refundImgUrl;

    /**
     * @FieldDoc(
     *     description = "佣金类型：1：退佣金，2：剩余佣金"
     *     example = "1"
     * )
     */
    18: i32 commissionType;

    /**
     * @FieldDoc(
     *     description = "退单佣金"
     *     example = "1"
     * )
     */
    19: i32 commission;

    /**
     * @FieldDoc(
     *     description = "平台包装费"
     *     example = "1"
     * )
     */
    20: i32 platPackageAmt;

    /**
     * @FieldDoc(
     *     description = "应退运费"
     *     example = "1"
     * )
     */
    21: i32 freight;

    /**
     * @FieldDoc(
     *     description = "商家运费收入"
     *     example = "1"
     * )
     */
    22: i32 poiLogisticIncome;

    /**
     * @FieldDoc(
     *     description = "积分抵扣"
     *     example = "1"
     * )
     */
    23: i32 scoreDeduction;

    /**
     * @FieldDoc(
     *     description = "自提服务费"
     *     example = "1"
     * )
     */
    24: i32 selfPickServiceFee;

    /**
     * @FieldDoc(
     *     description = "应退商品金额"
     *     example = "1"
     * )
     */
    25: i32 itemRefundAmt;

    /**
     * @FieldDoc(
     *     description = "售后单总优惠金额"
     *     example = "1"
     * )
     */
    26: i32 refundPromotion;

    /**
     * @FieldDoc(
     *     description = "退货取件费"
     *     example = "1"
     * )
     */
    27: i32 afsFreight;

    /**
      * @FieldDoc(
      *     description = "平台运费优惠"
      *     example = "1"
      * )
      */
     28: i32 platLogisticsPromotion;


       /**
      * @FieldDoc(
      *     description = "平台运费优惠"
      *     example = "1"
      * )
      */
     29: i32 poiLogisticsPromotion;

       /**
      * @FieldDoc(
      *     description = "售后类型：0-未知，1-仅退款，2-退货退款"
      *     example = "2"
      * )
      */
     30: optional i32 serviceType;

    /**
      * @FieldDoc(
      *     description = "售后类型：1-售中，2-售后"
      *     example = "2"
      * )
      */
     31: optional i32 afsApplyType;

     /**
           * @FieldDoc(
           *     description = "门店包装收入(单位:分)"
           *     example = "1"
           * )
           */
     32: optional i32 poiPackageIncome

       /**
           * @FieldDoc(
           *     description = "平台包装收入(单位:分)"
           *     example = "1"
           * )
           */
     33: optional i32 platPackageIncome

  /**
      * @FieldDoc(
      *     description = "门店包装优惠(单位:分)"
      *     example = "1"
      * )
      */
     34: optional i32 poiPackagePromotion

  /**
      * @FieldDoc(
      *     description = "平台包装优惠(单位:分)"
      *     example = "2"
      * )
      */
     35: optional i32 platPackagePromotion

  /**
      * @FieldDoc(
      *     description = "支付包装费(单位:分)"
      *     example = "2"
      * )
      */
     36: optional i32 payPackageFee

  /**
      * @FieldDoc(
      *     description = "总包装费(单位:分)"
      *     example = "2"
      * )
      */
     37: optional i32 originalPackageFee


        /**
       * @FieldDoc(
       *     description = "渠道售后状态："
       *     example = "2"
       * )
       */
     38: optional string channelAfsStatus;


        /**
       * @FieldDoc(
       *     description = "扩展字段"
       *     example = "2"
       * )
       */
     39: optional map<string,string> extend;

     /**
     * @FieldDoc(
     *     description = "退款审批类别"
     *     example = "2"
     * )
     */
     40: optional i32 auditType;

     /**
     * @FieldDoc(
     *     description = "门店优惠"
     *     example = "2"
     * )
     */
     41: optional i32 poiPromotion

     /**
     * @FieldDoc(
     *     description = "平台优惠"
     *     example = "2"
     * )
     */
     42: optional i32 platPromotion

     /**
     * @FieldDoc(
     *     description = "退款成功后当前订单剩余【商户应收】金额，单位：分"
     *     example = "234"
     * )
     */
     43: optional i32 merchantIncome

                 /**
             * @FieldDoc(
             *     description = "购物金"
             * )
            **/
     44: optional ShopCardFee.ShopCardFee shopCardFee;

     /**
     * @FieldDoc(
     *     description = "是否可以返货(配送中返货使用)"
     *     example = "1"
     * )
     */
     45: optional i32 isCanReturnGoods;

     /**
     * @FieldDoc(
     *     description = "是否返货(配送中返货使用)"
     *     example = "0"
     * )
     */
     46: optional i32 isReturnGoods;

    /**
     * @FieldDoc(
     *     description = "地址变更费"
     *     example = "10.00"
     * )
     */
     47: optional string addressChangeFee;

     /**
      * @FieldDoc(
      *     description = "红包支付金额"
      *     example = null
      * )
      */
      48: optional RedpackInfo.RedpackInfo redpackInfo;

     /**
      * @FieldDoc(
      *     description = "售后申请审核时效，秒级时间戳，超过审核时效则系统自动处理售后"
      *     example = "1720679819"
      * )
      */
      49: optional i64 processDeadline;

    /**
         * @FieldDoc(
         *     description = "退货状态"
         *     example = "10.00"
         * )
         */
      50: optional i32 returnGoodsStatusType;
          /**
         * @FieldDoc(
         *     description = "商家取货地址",
         *     example = "10"
         * )
         */
        51: optional string pickUpRefundGoodsAddress;
        /**
         * @FieldDoc(
         *     description = "用户隐私号",
         *     example = "10"
         * )
         */
        52: optional string refundGoodsPrivacyContactPhone;
        /**
         * @FieldDoc(
         *     description = "渠道退单额外类型，见ChannelExtRefundTypeEnum",
         *     example = "1"
         * )
         */
        53: optional i32 channelExtRefundType;
        /**
         * @FieldDoc(
         *     description = "扣除用户配送费金额分",
         *     example = "1"
         * )
         */
        54: optional i32 costCustomerFreightFee;
        /**
         * @FieldDoc(
         *     description = "渠道退单额外类型，见ChannelExtRefundTypeEnum",
         *     example = "1"
         * )
         */
        55: optional i32 logisticsStatus;
        /**
         * @FieldDoc(
         *     description = "是否为仅退款无需退货,退货退款流程(service_type = 2)下，当售后单状态(status)为21-终审已同意时，为1标识是否是【仅退款无需退货】。",
         *     example = "1"
         * )
         */
        56: optional bool onlyRefund;
        /**
         * @FieldDoc(
         *     description = "退货方式。",
         *     example = "1"
         * )
         */
        57: optional i32 returnGoodsWay;

        /**
         * @FieldDoc(
         *     description = "退货运费责任方，当returnGoodsWay为用户呼叫骑手送回 需要告知是商家还是用户承担运费。",
         *     example = "1"
         * )
         */
        58: optional i32 returnFreightDuty;

        /**
         * @FieldDoc(
         *     description = "初审自动审核类型（1-消极售后自动同意初审）",
         *     example = "1"
         * )
         */
        59: optional i32 firstAutoNegoType;

        /**
         * @FieldDoc(
         *     description = "退货运费责任展示文案码值（与returnFreightDuty有区别）",
         *     example = "1"
         * )
         */
        60: optional i32 refundGoodFreightNewType;
}