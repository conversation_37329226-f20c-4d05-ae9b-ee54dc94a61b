namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order




struct ChannelGiftInfo {
        /**
         * @FieldDoc(
         *     description = "赠品skuId",
         *     example = "1"
         * )
         */
        1: string skuId;
        /**
         * @FieldDoc(
         *     description = "主品skuId",
         *     example = "4"
         * )
         */
        2: string mainSkuId;
        /**
         * @FieldDoc(
         *     description = "赠品名称",
         *     example = "赠品"
         * )
         */
        3: string name;
        /**
         * @FieldDoc(
         *     description = "数量",
         *     example = "1"
         * )
         */
        4: i32 quantity;
        /**
         * @FieldDoc(
         *     description = "赠品spu",
         *     example = "1"
         * )
         */
        5: string spu;

        /**
        * @FieldDoc(
        *     description = "三方平台skuId",
        *     example = "123"
        * )
        */
        6: string thirdPartSkuId;

        /**
        * @FieldDoc(
        *     description = "三方平台spuId",
        *     example = "123"
        * )
        */
        7: string thirdPartSpuId;
}


/**
 * @TypeDoc(
 *     description = "订单优惠活动信息"
 * )
 */
struct OrderDiscountDetailDTO {
    /**
     * @FieldDoc(
     *     description = "活动ID",
     *     example = "1"
     * )
     */
    1: string activityId;
    /**
     * @FieldDoc(
     *     description = "总优惠金额",
     *     example = "4"
     * )
     */
    2: i64 actDiscount;
    /**
     * @FieldDoc(
     *     description = "渠道承担金额",
     *     example = "1"
     * )
     */
    3: i64 channelCharge;
    /**
     * @FieldDoc(
     *     description = "商家承担金额",
     *     example = "1"
     * )
     */
    4: i64 bizCharge;
    /**
     * @FieldDoc(
     *     description = "代理商承担金额",
     *     example = "1"
     * )
     */
    5: i64 agentCharge;
    /**
     * @FieldDoc(
     *     description = "物流承担金额",
     *     example = "1"
     * )
     */
    6: i64 logisticsCharge;
    /**
     * @FieldDoc(
     *     description = "活动说明",
     *     example = "88折"
     * )
     */
    7: string remark;
    /**
     * @FieldDoc(
     *     description = "参与此活动的商品",
     *     example = "199928"
     * )
     */
    8: list<string> skuInfo;
     /**
      * @FieldDoc(
      *     description = "此活动的赠品信息",
      *     example = "giftInfo"
      * )
      */
    9: ChannelGiftInfo giftInfo;
    /**
     * @FieldDoc(
     *     description = "活动类型",
     *     example = "zeng"
     * )
     */
    10: string type;
    /**
     * @FieldDoc(
     *     description = "商品行ID",
     *     example = "123"
     * )
     */
    11: i64 itemId;

    /**
      * @FieldDoc(
      *     description = "活动子类型",
      *     example = "1"
      * )
      */
    12: string subType;
}