namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "渠道返货单详情"
 * )
 */
struct ChannelReturnDuringDeliveryDTO {
        /**
         * @FieldDoc(
         *     description = "渠道返货单Id",
         *     example = "1928374857328384738"
         * )
         */
        1: optional string dispatchOrderId;
        /**
         * @FieldDoc(
         *     description = "预计送达时间",
         *     example = "100"
         * )
         */
        2: optional string deliveryTime;
        /**
         * @FieldDoc(
         *     description = "退款id，当前返货配送单是因为用户申请退款时有值。",
         *     example = "200"
         * )
         */
        3: optional string refundId;
        /**
         * @FieldDoc(
         *     description = "返货配送单状态 参考值： 31：返货中 32：返货成功 33：返货失败",
         *     example = "31"
         * )
         */
        4: optional i32 status;
        /**
         * @FieldDoc(
         *     description = "状态变更时间",
         *     example = "1704353664"
         * )
         */
        5: optional string time;
        /**
         * @FieldDoc(
         *     description = "返货发起方 参考值： 1：用户 2：骑手",
         *     example = "1"
         * )
         */
        6: optional i32 opRole;
        /**
         * @FieldDoc(
         *     description = "商家验货结果，当商家收到货并发起验货之后有值 参考值： 1：商家验收通过 2：商家验收不通过 3：系统自动验收通过",
         *     example = "1"
         * )
         */
        7: optional i32 poiCheckResult;
        /**
         * @FieldDoc(
         *     description = "商家验货不通过时填写的原因",
         *     example = "货物损坏"
         * )
         */
        8: optional string rejectReason;
}
