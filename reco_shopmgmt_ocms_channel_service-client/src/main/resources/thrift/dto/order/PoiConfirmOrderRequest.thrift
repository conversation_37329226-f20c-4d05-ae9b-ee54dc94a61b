namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "商家确认订单服务请求参数"
 * )
 */
struct PoiConfirmOrderRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "操作类型",
     *     example = "true"
     * )
     */
    4: bool isAgreed;
    /**
     * @FieldDoc(
     *     description = "操作人",
     *     example = "admin"
     * )
     */
    5: string operator;
      /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "admin"
     * )
     */
    6: i64 storeId;
}