namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order


include "PoiRefundProductInfoDTO.thrift"

/**
 * @TypeDoc(
 *     description = "商家发起部分退款请求参数"
 * )
 */
struct PoiPartRefundRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "退款原因",
     *     example = "部分退款申请原因"
     * )
     */
    4: string reason;
    /**
     * @FieldDoc(
     *     description = "退款商品列表",
     *     example = "[{\"skuId\" : \"134\", \"count\" : 2}]"
     * )
     */
    5: list<PoiRefundProductInfoDTO.RefundProductInfoDTO> refundProducts;
    /**
    * @FieldDoc(
    *     description = "退款原因（京东必传）",
    *     example = "1"
    * )
    */
    6: optional i32 reasonCode;

    /**
    * @FieldDoc(
    *     description = "操作人id",
    *     example = "1234"
    * )
    */
    7: optional string operatorId;

    /**
    * @FieldDoc(
    *     description = "部分退款类型",
    *     example = "1234"
    * )
    */
    8: optional i32 partRefundType;

       /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "admin"
     * )
     */
    9: i64 storeId;

    /**
      * @FieldDoc(
      *     description = "运费退费(分)(目前只有有赞使用)",
      *     example = "1"
      * )
    */
    10: optional i32 freight;

    /**
      * @FieldDoc(
      *     description = "售后单号(目前只有私域渠道使用)",
      *     example = "1"
      * )
      */
    11: optional string afterSaleId;

    /**
      * @FieldDoc(
      *     description = "包装费退费(分)(目前只有有赞使用)",
      *     example = "1"
      * )
    */
    12: optional i32 packageAmt;

}