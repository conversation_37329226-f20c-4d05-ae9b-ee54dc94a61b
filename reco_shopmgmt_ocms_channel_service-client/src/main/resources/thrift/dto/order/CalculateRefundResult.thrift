namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ResultStatus.thrift"
include "CalculateRefundDTO.thrift"

/**
 * @TypeDoc(
 *     description = "查询订单最新状态服务接口返回结果"
 * )
 */
struct CalculateRefundResult {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = "status"
     * )
     */
    1: ResultStatus.ResultStatus status;
    /**
     * @FieldDoc(
     *     description = "结果",
     *     example = "channelOrderDetail"
     * )
     */
    2: CalculateRefundDTO.CalculateRefundDTO calculateRefundDTO;
}