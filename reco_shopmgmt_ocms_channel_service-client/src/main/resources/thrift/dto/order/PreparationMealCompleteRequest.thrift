namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order
include "PreparationMealCompleteItemInfo.thrift"
/**
 * @TypeDoc(
 *     description = "推送出参完成服务请求参数"
 * )
 */
struct PreparationMealCompleteRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "操作人",
     *     example = "admin"
     * )
     */
    4: string operator;
    /**
    * @FieldDoc(
    *     description = "门店ID",
    *     example = "123"
    * )
    */
    5: optional i64 storeId;

    /**
    * @FieldDoc(
    *     description = "拣货完成商品信息，淘鲜达在上报备货完成和打包完成时都需要商品信息",
    *     example = ""
    * )
    */
    6: optional list<PreparationMealCompleteItemInfo.PreparationMealCompleteItemInfo> itemInfoList;

/**
    * @FieldDoc(
    *     description = "扩展订单Id，淘鲜达使用这个而不是 orderId",
    *     example = ""
    * )
    */
    7: optional string extOrderId;

    /**
     * @FieldDoc(
     *     description = "拣货完成时间戳(毫秒)",
     *     example = "1"
     * )
     */
    8: optional i64 completeTime;

    /**
     * @FieldDoc(
     *     description = "商家接单时间戳(毫秒)",
     *     example = "1"
     * )
     */
    9: optional i64 merchantTakeOrderTime;
}