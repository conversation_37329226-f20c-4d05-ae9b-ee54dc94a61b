namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "标记签收请求参数"
 * )
 */
struct MarkSignByOrderIdRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = "123"
     * )
     */
    3: i64 storeId;
     /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "19472638492372738"
     * )
     */
    4: string orderId;
    /**
    * @FieldDoc(
    *     description = "操作人",
    *     example = "1234"
    * )
    */
    5: optional string operator;
}