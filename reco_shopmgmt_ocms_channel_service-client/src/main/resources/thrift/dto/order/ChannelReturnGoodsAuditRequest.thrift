namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "商家审核返货请求参数"
 * )
 */
struct ChannelReturnGoodsAuditRequest {
    /**
     * @FieldDoc(
     *     description = "渠道订单Id",
     *     example = "1"
     * )
     */
    1: string viewOrderId;
    /**
     * @FieldDoc(
     *     description = "渠道Id",
     *     example = "1"
     * )
     */
    2: optional i32 channelId;
    /**
     * @FieldDoc(
     *     description = "租户Id",
     *     example = "19472638492372738"
     * )
     */
    3: optional i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "售后单Id",
     *     example = "1273864928173"
     * )
     */
    4: optional string afterSaleId;
    /**
     * @FieldDoc(
     *     description = "审核结果",
     *     example = "admin"
     * )
     */
    5: optional i32 auditResult;
      /**
     * @FieldDoc(
     *     description = "拒绝code",
     *     example = "1"
     * )
     */
    6: optional i64 rejectReasonCode;
    /**
     * @FieldDoc(
     *     description = "拒绝原因",
     *     example = "缺少货品"
     * )
     */
    7: optional string rejectOtherReason;

    /**
     * @FieldDoc(
     *     description = "应用Id",
     *     example = "应用Id"
     * )
     */
    8: optional i64 dispatchOrderId;
    /**
     * @FieldDoc(
     *     description = "商店Id",
     *     example = "商店Id"
     * )
     */
    9: optional i64 storeId;

}