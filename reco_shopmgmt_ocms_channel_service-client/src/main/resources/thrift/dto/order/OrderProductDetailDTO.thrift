namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ProductLabelDTO.thrift"
include "ProductPickingDateRangeDTO.thrift"
/**
 * @TypeDoc(
 *     description = "订单商品信息"
 * )
 */
struct OrderProductDetailDTO {
    /**
     * @FieldDoc(
     *     description = "商家商品编码",
     *     example = "1"
     * )
     */
    1: string skuId;
    /**
     * @FieldDoc(
     *     description = "商家商品名称",
     *     example = "苹果"
     * )
     */
    2: string skuName;
    /**
     * @FieldDoc(
     *     description = "商品数量",
     *     example = "1"
     * )
     */
    3: i32 quantity;
    /**
     * @FieldDoc(
     *     description = "非标品重量",
     *     example = "1"
     * )
     */
    4: i32 unitCount;
    /**
     * @FieldDoc(
     *     description = "单位",
     *     example = "g"
     * )
     */
    5: string unit;
    /**
     * @FieldDoc(
     *     description = "商品UPC码",
     *     example = "1"
     * )
     */
    6: string upcCode;
    /**
     * @FieldDoc(
     *     description = "商品规格",
     *     example = "1"
     * )
     */
    7: string specification;
    /**
     * @FieldDoc(
     *     description = "标品/非标品",
     *     example = "1"
     * )
     */
    8: i32 sellType;
    /**
     * @FieldDoc(
     *     description = "销售单价",
     *     example = "1"
     * )
     */
    9: i32 salePrice;
    /**
     * @FieldDoc(
     *     description = "商品参与的促销信息",
     *     example = "详见ProductDiscountInfoDTO"
     * )
     */
    10: list<string> activitieInfo;
    /**
     * @FieldDoc(
     *     description = "商家商品编码",
     *     example = "111"
     * )
     */
    11: string channelSkuId;
    /**
     * @FieldDoc(
     *     description = "餐盒单价",
     *     example = "1"
     * )
     */
    12: i32 packagePrice;
    /**
     * @FieldDoc(
     *     description = "餐盒数量",
     *     example = "1"
     * )
     */
    13: i32 packageCount;
    /**
     * @FieldDoc(
     *     description = "餐盒费总价",
     *     example = "1"
     * )
     */
    14: i32 packageFee;
    /**
     * @FieldDoc(
     *     description = "商品属性,多属性用逗号隔开",
     *     example = "多糖,少冰"
     * )
     */
    15: string skuProperty;

    /**
     * @FieldDoc(
     *     description = "商品重量，单位:克",
     *     example = "100"
     * )
     */
    16: i64 weight;


    /**
     * @FieldDoc(
     *     description = "商家在渠道的spu",
     *     example = "1242224432"
     * )
     */
    17: string customSpu;

    /**
    * @FieldDoc(
    *     description = "商品原价",
    *     example = "123"
    * )
    */
    18: i32 originalPrice;

    /**
    * @FieldDoc(
    *     description = "商品项(订单商品明细)id",
    *     example = "123"
    * )
    */
    19: string channelItemId;

    20: string extData;

    /**
    * @FieldDoc(
    *     description = "商品图片地址、多张图片以逗号分隔",
    *     example = "https://img/234r53"
    * )
    */
    21: string skuPicUrls

    /**
    * @FieldDoc(
    *     description = "商品实付总金额（分）：去除订单纬度优惠、商品纬度优惠以后的商品实付总价，当前只有赞有值",
    *     example = "123"
    * )
    */
    22: i32 totalPayAmtMinusDiscount;

     /**
    * @FieldDoc(
    *     description = "商品行id",
    *     example = "123"
    * )
    */
    23: i64 itemId;

    /**
    * @FieldDoc(
    *     description = "三方平台skuId",
    *     example = "123"
    * )
    */
    24: string thirdPartSkuId;

    /**
    * @FieldDoc(
    *     description = "三方平台spuId",
    *     example = "123"
    * )
    */
    25: string thirdPartSpuId;

    /**
    * @FieldDoc(
    *     description = "三方平台ItemId",
    *     example = "123"
    * )
    */
    26: string thirdPartItemId;

    /**
    * @FieldDoc(
    *     description = "总优惠",
    *     example = "123"
    * )
    */
    27: i32 totalDiscount;

    /**
    * @FieldDoc(
    *     description = "平台承担金额",
    *     example = "123"
    * )
    */
    28: i32 channelCost;

    /**
    * @FieldDoc(
    *     description = "商家承担金额",
    *     example = "123"
    * )
    */
    29: i32 tenantCost;

    /**
         * @FieldDoc(
         *     description = "平台整单优惠",
         *     example = "1"
         * )
         */
    30: i32 platPromotion;


        /**
         * @FieldDoc(
         *     description = "平台单品优惠",
         *     example = "1"
         * )
         */
    31: i32 platItemPromotion;

        /**
         * @FieldDoc(
         *     description = "商家整单优惠",
         *     example = "1"
         * )
         */
    32: i32 poiPromotion;

        /**
         * @FieldDoc(
         *     description = "商家单品优惠",
         *     example = "1"
         * )
         */
    33: i32 poiItemPromotion;

    /**
     * @FieldDoc(
     *     description = "销售实际单价，减去单品折扣",
     *     example = "1"
     * )
     */
    34: i32 actualSalePrice;

    /**
     * @FieldDoc(
     *     description = "商品类型 0-普通 1-赠品 2-储值卡",
     *     example = "1"
     * )
     */
    35: i32 itemType;

    /**
    * @FieldDoc(
    *     description = "商品项(订单商品明细)id，目前仅淘鲜达使用，新增这个字段的原因是淘鲜达的接口里，
    *     有时候要用 outSubOrderId 字段，有时候要用 bizSubOrderId 字段进行交互，因此需要都存下来，这里存 bizSubOrderId",
    *     example = "123"
    * )
    */
    36: string bizChannelItemId;

    /**
     * @FieldDoc(
     *     description = "积分抵扣金额",
     *     example = "1"
     * )
     */
    37: i32 platformIntegralDeductMoney = 0;

    /**
    * @FieldDoc(
    *     description = "商品标签列表",
    *     example = "[]"
    * )
    */
    38: optional list<ProductLabelDTO.ProductLabelDTO> channelLabelList;

    /**
        * @FieldDoc(
        *     description = "是否代销商品,1-是,0-否",
        *     example = "1"
        * )
     */
     39: optional i32 consignmentProduct;


    /**
        * @FieldDoc(
        *     description = "代销主体id",
        *     example = "123"
        * )
     */
    40: optional string consignmentAgentId;

    /**
        * @FieldDoc(
        *     description = "平台商品Id",
        *     example = "17327717062254441"
        * )
     */
    41: optional string baiduProductId;

    /**
    * @FieldDoc(
    *     description = "拣货时间段（新鲜承诺）",
    *     example = ""
    * )
    */
    42: optional ProductPickingDateRangeDTO.ProductPickingDateRangeDTO productPickingDateRange;

}