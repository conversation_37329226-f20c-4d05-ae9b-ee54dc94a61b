namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "ResultStatus.thrift"
include "OrderStatusDTO.thrift"

/**
 * @TypeDoc(
 *     description = "查询订单最新状态服务接口返回结果"
 * )
 */
struct GetOrderStatusResult {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = "status"
     * )
     */
    1: ResultStatus.ResultStatus status;
    /**
     * @FieldDoc(
     *     description = "渠道订单最新状态值",
     *     example = "channelOrderDetail"
     * )
     */
    2: OrderStatusDTO.OrderStatusDTO orderStatus;
}