namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order


struct UpdateDeliveryProofPhotoRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: i64 tenantId;

        /**
        * @FieldDoc(
        *     description = "门店ID",
        *     example = "1"
        * )
        */
        2: i64 storeId;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        3: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "渠道订单ID",
         *     example = "1"
         * )
         */
        4: string orderId;

        /**
         * @FieldDoc(
         *     description = "图片url列表",
         *     example = "1"
         * )
         */
        5:list<string> photoUrlList;

        /**
         * @FieldDoc(
         *     description = "送达位置-经度",
         *     example = "1"
         * )
         */
         6:double longitude;


        /**
         * @FieldDoc(
         *     description = "送达位置-纬度",
         *     example = "1"
         * )
         */
         7:double latitude;

        /**
         * @FieldDoc(
         *     description = "运单id",
         *     example = "1"
         * )
         */
         8:i64 deliveryOrderId;

         /**
         * @FieldDoc(
         *     description = "配送员电话",
         *     example = "1"
         * )
         */
         9:string riderPhone;


         /**
         * @FieldDoc(
         *     description = "骑手姓名",
         *     example = ""
         * )
         */
         10: string riderName;


        /**
        * @FieldDoc(
        *     description = "配送渠道",
        *     example = ""
        * )
        */
        11: i32 deliveryChannelId;
}