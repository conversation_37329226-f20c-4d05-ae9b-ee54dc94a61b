namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

include "RefundTypeEnum.thrift"
include "RefundProdectInfoDTO.thrift"

/**
 * @TypeDoc(
 *     description = "取消订单回调接口"
 * )
 */
struct CancelOrderCallbackRequest {
    /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
    1: string tenantAppId;
    /**
     * @FieldDoc(
     *     description = "渠道CODE唯一标识",
     *     example = "1"
     * )
     */
    2: string channelCode;
    /**
     * @FieldDoc(
     *     description = "订单ID",
     *     example = "194837264837283929"
     * )
     */
    3: string orderId;
    /**
     * @FieldDoc(
     *     description = "取消原因",
     *     example = "不想买了"
     * )
     */
    4: string reason;
    /**
     * @FieldDoc(
     *     description = "图片",
     *     example = "无"
     * )
     */
    5: optional string pictureJson = "[]";
    /**
     * @FieldDoc(
     *     description = "退款金额",
     *     example = ""
     * )
     */
    6: string refundPrice;
    /**
     * @FieldDoc(
     *     description = "退款类型",
     *     example = "部分or全部"
     * )
     */
    7: i32 refundType;
    /**
     * @FieldDoc(
     *     description = "通知类型",
     *     example = "apply"
     * )
     */
    8: string notifyType;
    /**
     * @FieldDoc(
     *     description = "退款商品信息",
     *     example = "无"
     * )
     */
    9: optional string productJson = "[]";
    /**
     * @FieldDoc(
     *     description = "表示商家/用户发起部分退款申请",
     *     example = "无"
     * )
     */
    10: string status;
    /**
     * @FieldDoc(
     *     description = "退款类型",
     *     example = "1"
     * )
     */
    11: i32 resType;
    /**
     * @FieldDoc(
     *     description = "是否申诉退款",
     *     example = "1"
     * )
     */
    12: i32 isAppeal;
}