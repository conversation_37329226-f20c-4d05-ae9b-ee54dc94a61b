namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

/**
 * @TypeDoc(
 *     description = "获取地址变更退费请求参数"
 * )
 */
struct RefundAddressChangeFeeDTO {

    /**
     * @FieldDoc(
     *     description = "渠道订单号",
     *     example = "19472638492372738"
     * )
     */
    1: i64 orderId;
    /**
     * @FieldDoc(
     *     description = "地址变更退费",
     *     example = "1"
     * )
     */
    2: string money;
    /**
     * @FieldDoc(
     *     description = "类型",
     *     example = "1"
     * )
     */
    3: optional i32 type;
    /**
     * @FieldDoc(
     *     description = "退单时间（秒级时间戳）",
     *     example = "9473687656"
     * )
     */
    4: optional i32 refundTime;
}