namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order

struct RiderInfoChangeRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        2: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = "1"
         * )
         */
        3: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "渠道订单ID",
         *     example = "19472638492372738"
         * )
         */
        4: string orderId;

        /**
         * @FieldDoc(
         *     description = "骑手姓名",
         *     example = ""
         * )
         */
        5: string riderName;

        /**
         * @FieldDoc(
         *     description = "骑手电话",
         *     example = ""
         * )
         */
        6: string riderPhone;

        /**
        * @FieldDoc(
        *     description = "牵牛花配送承运商ID",
        *     example = ""
        * )
        */
        7: i64 deliveryChannelId;

        /**
        * @FieldDoc(
        *     description = "赋能内部运单id",
        *     example = ""
        * )
        */
        8: i64 deliveryOrderId;
}