namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment

struct ChannelCommentItemTagDTO {
        1: optional i64 skuId;
        2: optional string skuName;
        3: optional list<string> itemTags;
        /**
         * 租户在渠道的spu
         **/
        4: optional string customerSpu;
}

/**
 * @TypeDoc(
 *     description = "评价"
 * )
 */
struct ChannelCommentDTO {
        1: required string commentId;
        2: optional string commentContent;
        3: required string commentTime;
        4: optional string addCommentContent;
        5: optional string addCommentTime;
        6: required string commentLevel;
        7: optional list<string> commentPictures;
        8: optional string deliveryCommentContent;
        9: optional list<string> deliveryCommentLabels;
        10: optional i32 orderScore;
        11: optional i32 qualityScore;
        12: optional i32 packingScore;
        13: optional i32 deliveryScore;
        14: optional list<string> praiseItemList;
        15: optional list<string> criticItemList;
        16: optional string replyContent;
        17: optional string replyStatus;
        18: optional string replyTime;
        19: optional string orderId;
        20: optional list<string> orderItemList;
        21: bool valid = true;
        22: optional list<ChannelCommentItemTagDTO> itemTagList;
}
struct CommentContentDTO{
        1: optional string commentId;
        2: required string commentTime;
        3: required string commentContent;
        4: optional list<string> pictureList;
}


