namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment

/**
 * @TypeDoc(
 *     description = "评价列表查询请求参数"
 * )
 */
struct CommentListQueryRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: required i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        2: required i32 channelId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = "1"
         * )
         */
        3: required i64 storeId;

        /**
         * @FieldDoc(
         *     description = "渠道评价id",
         *     example = {}
         * )
         */
        4: optional string channelCommentId;

        /**
         * @FieldDoc(
         *     description = "活动开始日期",
         *     example = {}
         * )
         */
        5: optional string startTime;
        /**
         * @FieldDoc(
         *     description = "活动截止日期",
         *     example = {}
         * )
         */
        6: optional string endTime;
        /**
         * @FieldDoc(
         *     description = "回复状态列表",
         *     example = {},
         *     rule = "0:未回复, 1:已回复"
         * )
         */
        7: optional string replyStatus;
        /**
         * @FieldDoc(
         *     description = "当前页",
         *     example = {}
         * )
         */
        8: i32 pageNum = 1;
        /**
         * @FieldDoc(
         *     description = "每页行数",
         *     example = {}
         * )
         */
        9: i32 pageSize = 20;
        /**
                * @FieldDoc(
                *     description = "渠道订单号",
                *     example = {}
                * )
                */
        10: optional string channelOrderId;
}

/**
 * @TypeDoc(
 *     description = "评价回复请求参数"
 * )
 */
struct CommentReplyRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: required i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        2: required i32 channelId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = "1"
         * )
         */
        3: required i64 storeId;

        /**
         * @FieldDoc(
         *     description = "渠道评价id",
         *     example = {}
         * )
         */
        4: required string channelCommentId;

        /**
         * @FieldDoc(
         *     description = "回复内容",
         *     example = {}
         * )
         */
        5: required string replyContent;

        /**
         * @FieldDoc(
         *     description = "回复人",
         *     example = {}
         * )
         */
        6: optional string replyUser;
}


/**
 * @TypeDoc(
 *     description = "评价规则查询请求"
 * )
 */
struct CommentRuleQueryRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: required i64 tenantId;
        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = "123"
         * )
         */
        2: optional i64 storeId;
}
