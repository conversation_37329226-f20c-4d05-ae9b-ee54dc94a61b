namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.comment

include "ChannelCommentDTO.thrift"
include "ChannelCommentRuleDTO.thrift"
include "PageInfo.thrift"
include "ResultStatus.thrift"

/**
 * @TypeDoc(
 *     description = "评价信息分页查询请求出参"
 * )
 */
struct CommentListQueryResponse {
        /**
         * @FieldDoc(
         *     description = "接口调用结果状态",
         *     example = {}
         * )
         */
        1: required ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "评价列表",
         *     example = {}
         * )
         */
        2: optional list<ChannelCommentDTO.ChannelCommentDTO> commentDTOList;

        /**
         * @FieldDoc(
         *     description = "分页信息",
         *     example = {}
         * )
         */
        3: optional PageInfo.PageInfo pageInfo;
}

/**
 * @TypeDoc(
 *     description = "评价回复响应"
 * )
 */
struct CommentReplyResponse {
        /**
        * @FieldDoc(
        *     description = "接口调用结果状态",
        *     example = {}
        * )
        */
        1: required ResultStatus.ResultStatus status;

}


/**
 * @TypeDoc(
 *     description = "评价规则查询响应"
 * )
 */
struct CommentRuleQueryResponse {
        /**
        * @FieldDoc(
        *     description = "接口调用结果状态",
        *     example = {}
        * )
        */
        1: required ResultStatus.ResultStatus status;
        /**
        * @FieldDoc(
        *     description = "评价规则",
        *     example = {}
        * )
        */
        2: optional ChannelCommentRuleDTO.ChannelCommentRuleDTO commentRuleDTO;
}
