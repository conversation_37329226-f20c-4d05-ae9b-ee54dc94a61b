namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.application

include "ResultStatus.thrift"

/**
 * @TypeDoc(
 *     description = "应用租户关系DTO"
 * )
 */
struct AppInfoDTO {
     /**
        * @FieldDoc(
        *           description = "渠道id"
        *       )
     **/
    1: required i32 channelId;
     /**
        * @FieldDoc(
        *           description = "appkey"
        *       )
     **/
    2: required string appKey;
     /**
        * @FieldDoc(
        *           description = "secret"
        *       )
     **/
    3: required string secret;
     /**
        * @FieldDoc(
        *           description = "子应用key"
        *       )
     **/
    4: required string subAppKey;
     /**
        * @FieldDoc(
        *           description = "授权token"
        *       )
     **/
    5: required string accessToken;
     /**
        * @FieldDoc(
        *           description = "接入类型"
        *       )
     **/
    6: required i32 type;
     /**
        * @FieldDoc(
        *           description = "牵牛花应用id"
        *       )
     **/
    7: required i64 qnhAppId;
     /**
        * @FieldDoc(
        *           description = "应用名称"
        *       )
     **/
    8: required string appName;
}

/**
 * @TypeDoc(
 *     description = "应用信息查询请求"
 * )
 */
struct AppInfoQueryRequest {

    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;
}

/**
 * @TypeDoc(
 *     description = "应用信息查询响应"
 * )
 */
struct AppInfoQueryResponse {

    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "应用信息",
     *     example = ""
     * )
     */
    2: AppInfoDTO data;
}