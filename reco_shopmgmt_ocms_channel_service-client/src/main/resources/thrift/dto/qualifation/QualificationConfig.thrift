namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "MatchableRule.thrift"
/**
 * @TypeDoc(
 *     description = "店内分类基本信息"
 * )
 */
struct QualificationConfig {

    /**
     * @FieldDoc(
     *     description = "资质ID",
     *     example = ""
     * )
     */
    1: string key;

    /**
     * @FieldDoc(
     *     description = "资质名",
     *     example = ""
     * )
     */
    2: string name;

    /**
     * @FieldDoc(
     *     description = "填写提示",
     *     example = ""
     * )
     */
    3: list<string> textList;

    /**
     * @FieldDoc(
     *     description = "是否必填",
     *     example = ""
     * )
     */
    4: bool isRequired;

    /**
     * @FieldDoc(
     *     description = "商品类目属性可能触发的规则（会导致资质是否必填发生变化）",
     *     example = ""
     * )
     */
    5: list<MatchableRule.MatchableRule> matchableRuleList;
}