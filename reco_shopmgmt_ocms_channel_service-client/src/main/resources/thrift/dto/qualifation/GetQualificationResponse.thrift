namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"
include "QualificationConfig.thrift"

/**
 * @TypeDoc(
 *     description = "批量获取类目信息返回对象"
 * )
 */
struct GetQualificationResponse {

    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "类目列表",
     *     example = ""
     * )
     */
    2: list<QualificationConfig.QualificationConfig> qualificationConfigList;

}