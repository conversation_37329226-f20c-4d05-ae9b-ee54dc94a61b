namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "RuleClause.thrift"
/**
 * @TypeDoc(
 *     description = "商品类目属性可能触发的规则（会导致资质是否必填发生变化）"
 * )
 */
struct MatchableRule {

    /**
     * @FieldDoc(
     *     description = "key为类目属性id，当该属性填了如下属性值时，命中规则",
     *     example = ""
     * )
     */
    1: map<i64,RuleClause.RuleClause> ruleClauseMap;

    /**
     * @FieldDoc(
     *     description = "命中该规则后 该资质是否必填",
     *     example = ""
     * )
     */
    2: bool isQualificationRequired;
}