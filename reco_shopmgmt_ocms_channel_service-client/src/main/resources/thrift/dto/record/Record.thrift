namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.record

/**
 * @TypeDoc(
 *     description = "请求记录"
 * )
 */
struct Record {

    /**
     * @FieldDoc(
     *     description = "记录ID",
     *     example = ""
     * )
     */
    1: string id;

    /**
     * @FieldDoc(
     *     description = "接口名",
     *     example = ""
     * )
     */
    2: string interfaceName;

    /**
     * @FieldDoc(
     *     description = "访问的url",
     *     example = ""
     * )
     */
    3: string url;

    /**
     * @FieldDoc(
     *     description = "渠道",
     *     example = ""
     * )
     */
    4: i32 channel;

    /**
     * @FieldDoc(
     *     description = "租户",
     *     example = ""
     * )
     */
    5: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "请求内容",
     *     example = ""
     * )
     */
    6: string requestBody;

    /**
     * @FieldDoc(
     *     description = "请求时间",
     *     example = ""
     * )
     */
    7: string requestTime;

    /**
     * @FieldDoc(
     *     description = "请求结果",
     *     example = ""
     * )
     */
    8: string resultStatus;

    /**
     * @FieldDoc(
     *     description = "返回信息",
     *     example = ""
     * )
     */
    9: string resultInfo;

    /**
     * @FieldDoc(
     *     description = "重试次数",
     *     example = ""
     * )
     */
    10: i32 retryTimes;

    /**
     * @FieldDoc(
     *     description = "更新时间",
     *     example = ""
     * )
     */
    11: i64 updateTime;

    /**
     * @FieldDoc(
     *     description = "有效性",
     *     example = ""
     * )
     */
    12: i32 valid;

}