namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.record

/**
 * @TypeDoc(
 *     description = "查询推送记录请求参数"
 * )
 */
struct RecordSelectRequest {

    /**
     * @FieldDoc(
     *     description = "接口名",
     *     example = ""
     * )
     */
    1: optional string interfaceName;

    /**
     * @FieldDoc(
     *     description = "渠道",
     *     example = ""
     * )
     */
    2: optional i32 channel;

    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = ""
     * )
     */
    3: optional i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = ""
     * )
     */
    4: optional i64 poiId;

    /**
     * @FieldDoc(
     *     description = "开始时间unixTime,精确到秒",
     *     example = ""
     * )
     */
    5: optional i64 startTime;

    /**
     * @FieldDoc(
     *     description = "结束时间unixTime,精确到秒",
     *     example = ""
     * )
     */
    6: optional i64 endTime;

    /**
     * @FieldDoc(
     *     description = "推送结果:0成功1失败-1全部",
     *     example = ""
     * )
     */
    7: optional i32 resultStatus;

    /**
     * @FieldDoc(
     *     description = "页码",
     *     example = ""
     * )
     */
    8: optional i32 pageNum;

    /**
     * @FieldDoc(
     *     description = "每页大小",
     *     example = ""
     * )
     */
    9: optional i32 pageSize;


}