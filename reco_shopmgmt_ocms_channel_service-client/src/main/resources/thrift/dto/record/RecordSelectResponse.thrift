namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.record

include "ResultStatus.thrift"
include "RecordList.thrift"

/**
 * @TypeDoc(
 *     description = "查询推送记录列表"
 * )
 */
struct RecordSelectResponse {
    /**
     * @FieldDoc(
     *     description = "接口调用结果状态，调用结果全部成功或部分成功时返回0，需要解析sucData和errorData，其他场景只返回对应异常码",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "推送列表",
     *     example = ""
     * )
     */
    2: RecordList.RecordList recordList;


}