namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"
include "PageInfo.thrift"
include "BrandInfo.thrift"

/**
 * @TypeDoc(
 *     description = "获取品牌返回结果"
 * )
 */
struct GetBrandResponse {

    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "分页信息",
     *     example = ""
     * )
     */
    2: PageInfo.PageInfo pageInfo;

    /**
     * @FieldDoc(
     *     description = "品牌信息",
     *     example = ""
     * )
     */
    3: list<BrandInfo.BrandInfo> brandInfoList;

}