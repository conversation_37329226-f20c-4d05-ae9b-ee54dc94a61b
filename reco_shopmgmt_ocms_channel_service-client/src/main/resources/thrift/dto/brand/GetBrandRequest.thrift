namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "BaseRequest.thrift"

/**
 * @TypeDoc(
 *     description = "获取品牌数据请求参数"
 * )
 */
struct GetBrandRequest {

    /**
     * @FieldDoc(
     *     description = "租户和渠道基本信息",
     *     example = ""
     * )
     */
    1: required BaseRequest.BaseRequestSimple baseInfo;

    /**
     * @FieldDoc(
     *     description = "品牌名",
     *     example = ""
     * )
     */
    2: optional string brandName;

    /**
     * @FieldDoc(
     *     description = "页码",
     *     example = ""
     * )
     */
    3: optional i32 pageNum = 1;


     /**
     * @FieldDoc(
     *     description = "类目id",
     *     example = ""
     * )
     */
    4: optional string categoryId;

    /**
     * @FieldDoc(
     *     description = "门店id",
     *     example = ""
     * )
     */
    5: optional i64 storeId;
}