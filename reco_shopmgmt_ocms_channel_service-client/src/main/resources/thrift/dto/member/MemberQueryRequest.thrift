namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member

/**
 * @TypeDoc(
 *     description = "会员查询请求"
 * )
 */
struct MemberQueryRequest {

    /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
    1: required string tenantAppId;

    /**
     * @FieldDoc(
     *     description = "渠道CODE唯一标识",
     *     example = "mt"
     * )
     */
    2: required string channelCode;


    /**
     * @FieldDoc(
     *     description = "签名结果",
     *     example = ""
     * )
     */
    3: required string sig;

    /**
     * @FieldDoc(
     *     description = "时间戳",
     *     example = ""
     * )
     */
    4: required i64 timestamp;

    /**
     * @FieldDoc(
     *     description = "商家门店code",
     *     example = ""
     * )
     */
    5: string appPoiCode;


    /**
     * @FieldDoc(
     *     description = "手机号",
     *     example = ""
     * )
     */
    6: string mobile;


    /**
     * @FieldDoc(
     *     description = "手机号",
     *     example = ""
     * )
     */
    7: string cardCode;

}