namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member

/**
 * @TypeDoc(
 *     description = "会员信息"
 * )
 */
struct MemberInfoDTO{

    /**
     * @FieldDoc(
     *     description = "商家门店code",
     *     example = "1"
     * )
     */
    1: string registerPoiCode;

    /**
     * @FieldDoc(
     *     description = "手机号",
     *     example = "mt"
     * )
     */
    2: string mobile;


    /**
     * @FieldDoc(
     *     description = "等级code",
     *     example = ""
     * )
     */
    3: string levelCode;

   /**
    * @FieldDoc(
    *     description = "卡号",
    *     example = ""
    * )
    */
   4: string cardCode;

    /**
     * @FieldDoc(
     *     description = "卡状态",
     *     example = ""
     * )
     */
    5: i32 statusCode;


    /**
     * @FieldDoc(
     *     description = "积分",
     *     example = ""
     * )
     */
    6: string totalScore;


    /**
     * @FieldDoc(
     *     description = "身份证号",
     *     example = ""
     * )
     */
    7: string identityCard;

    /**
     * @FieldDoc(
     *     description = "性别0未知、1男、2女",
     *     example = ""
     * )
     */
    8: i32 gender;

    /**
     * @FieldDoc(
     *     description = "生日：yyyy-MM-dd",
     *     example = ""
     * )
     */
    9: string birthday;

    /**
     * @FieldDoc(
     *     description = "会员名",
     *     example = ""
     * )
     */
    10: string name;

    /**
     * @FieldDoc(
     *     description = "会员Id",
     *     example = ""
     * )
     */
    11: string memberId;

    /**
     * @FieldDoc(
     *     description = "注册时间",
     *     example = ""
     * )
     */
    12: i32 registerTime;
}