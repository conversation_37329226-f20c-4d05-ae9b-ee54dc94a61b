namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member

/**
 * @TypeDoc(
 *     description = "会员创建成功返回的会员信息"
 * )
 */
struct MemberCreateResultDTO{

    /**
     * @FieldDoc(
     *     description = "等级code",
     *     example = ""
     * )
     */
    1: string levelCode;

   /**
    * @FieldDoc(
    *     description = "卡号",
    *     example = ""
    * )
    */
   2: string cardCode;

    /**
     * @FieldDoc(
     *     description = "卡状态",
     *     example = ""
     * )
     */
    3: i32 statusCode;


    /**
     * @FieldDoc(
     *     description = "会员Id",
     *     example = ""
     * )
     */
    4: string memberId;

    /**
     * @FieldDoc(
     *     description = "注册时间",
     *     example = ""
     * )
     */
    5: i32 registerTime;
}