namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.member

/**
 * @TypeDoc(
 *     description = "会员新增请求"
 * )
 */
struct MemberCreateRequest {

    /**
     * @FieldDoc(
     *     description = "租户唯一标识",
     *     example = "1"
     * )
     */
    1: required string tenantAppId;

    /**
     * @FieldDoc(
     *     description = "渠道CODE唯一标识",
     *     example = "mt"
     * )
     */
    2: required string channelCode;


    /**
     * @FieldDoc(
     *     description = "签名结果",
     *     example = ""
     * )
     */
    3: required string sig;

    /**
     * @FieldDoc(
     *     description = "时间戳",
     *     example = ""
     * )
     */
    4: required i64 timestamp;

    /**
     * @FieldDoc(
     *     description = "商家门店code",
     *     example = ""
     * )
     */
    5: required string appPoiCode;


    /**
     * @FieldDoc(
     *     description = "手机号",
     *     example = ""
     * )
     */
    6: string mobile;


    /**
     * @FieldDoc(
     *     description = "会员姓名",
     *     example = ""
     * )
     */
    7: string name;

    /**
     * @FieldDoc(
     *     description = "性别0未知、1男、2女",
     *     example = ""
     * )
     */
    8: i32 gender;

    /**
     * @FieldDoc(
     *     description = "生日：yyyy-MM-dd",
     *     example = ""
     * )
     */
    9: string birthday;

}