namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery


include "GoodsDTO.thrift"
include "TransportInfo.thrift"

/**
 * @TypeDoc(
 *     description = "创建配送订单请求"
 * )
 */
struct CreateDeliveryRequest {

        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = ""
         * )
         */
        1:  i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = ""
         * )
         */
        2: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "配送渠道ID",
         *     example = ""
         * )
         */
        3: i32 deliveryChannelId;

        /**
         * @FieldDoc(
         *     description = "配送标识",
         *     example = ""
         * )
         */
        4: string deliveryId;

        /**
         * @FieldDoc(
         *     description = "渠道订单号",
         *     example = ""
         * )
         */
        5: string orderId;

        /**
         * @FieldDoc(
         *     description = "配送服务代码",
         *     example = ""
         * )
         */
        6: string deliveryServiceCode;

        /**
        * @FieldDoc(
        *     description = "收件人名称",
        *     example = ""
        * )
        */
        7: string receiverName;

        /**
        * @FieldDoc(
        *     description = "收件人地址",
        *     example = ""
        * )
        */
        8: string receiverAddress;

        /**
        * @FieldDoc(
        *     description = "收件人电话",
        *     example = ""
        * )
        */
        9: string receiverPhone;

        /**
        * @FieldDoc(
        *     description = "收件人地址经度",
        *     example = "收件人地址经度 （火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 116398419"
        * )
        */
        10: double receiverLongitude;

        /**
        * @FieldDoc(
        *     description = "收件人地址纬度",
        *     example = "收件人地址纬度 （火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 116398419"
        * )
        */
        11: double receiverLatitude;

        /**
        * @FieldDoc(
        *     description = "坐标类型",
        *     example = "坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0）"
        * )
        */
        12: i32 coordinateType;

        /**
        * @FieldDoc(
        *     description = "货物价格 单位:分",
        *     example = ""
        * )
        */
        13: i32 goodsValue;

        /**
        * @FieldDoc(
        *     description = "货物重量 单位:g",
        *     example = ""
        * )
        */
        14: i32 goodsWeight;

        /**
        * @FieldDoc(
        *     description = "货物列表",
        *     example = ""
        * )
        */
        15: list<GoodsDTO.GoodsDTO> goods;

        /**
        * @FieldDoc(
        *     description = "期望取货时间 时区为GMT+8，当前距离Epoch（1970年1月1日) 以毫秒计算的时间",
        *     example = ""
        * )
        */
        16: i64 expectPickupTime;

        /**
        * @FieldDoc(
        *     description = "期望送达时间 时区为GMT+8，当前距离Epoch（1970年1月1日) 以毫秒计算的时间",
        *     example = ""
        * )
        */
        17: i64 expectDeliveryTime;

        /**
        * @FieldDoc(
        *     description = "配送订单类型  0:即时单   1:预约单",
        *     example = ""
        * )
        */
        18: i32 orderType;

        /**
        * @FieldDoc(
        *     description = "门店流水号   用于骑手门店取货识别",
        *     example = ""
        * )
        */
        19: string shopSequence;

        /**
        * @FieldDoc(
        *     description = "备注",
        *     example = ""
        * )
        */
        20: string note;

        /**
        * @FieldDoc(
        *     description = "骑手应付金额  单位:分",
        *     example = ""
        * )
        */
        21: i32 cashOnDelivery;


        /**
        * @FieldDoc(
        *     description = "骑手应收金额 单位:分",
        *     example = ""
        * )
        */
        22: i32 cashOnPickup;

        /**
        * @FieldDoc(
        *     description = "发票抬头",
        *     example = ""
        * )
        */
        23: string invoiceTitle = "";

        /**
        * @FieldDoc(
        *     description = "配送点信息",
        *     example = ""
        * )
        */
        24: TransportInfo.TransportInfo transportInfo;

        /**
        * @FieldDoc(
        *     description = "订单系统类型",
        *     example = ""
        * )
        */
        25: i32 orderBizType;

        /**
        * @FieldDoc(
        *     description = "订单渠道订单号",
        *     example = ""
        * )
        */
        26: string channelOrderId;
}
