namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery

struct TransportInfo {


        /**
        * @FieldDoc(
        *     description = "配送点名称",
        *     example = ""
        * )
        */
        1: string transportName;

        /**
        * @FieldDoc(
        *     description = "配送点地址",
        *     example = ""
        * )
        */
        2: string transportAddress;

        /**
        * @FieldDoc(
        *     description = "配送点经度",
        *     example = ""
        * )
        */
        3: double transportLongitude;

        /**
        * @FieldDoc(
        *     description = "配送点纬度",
        *     example = ""
        * )
        */
        4: double transportLatitude;

        /**
        * @FieldDoc(
        *     description = "坐标来源",
        *     example = ""
        * )
        */
        5: i32 coordinateType

        /**
               * @FieldDoc(
               *     description = "配送点电话",
               *     example = ""
               * )
               */
        6: string transportTel
}