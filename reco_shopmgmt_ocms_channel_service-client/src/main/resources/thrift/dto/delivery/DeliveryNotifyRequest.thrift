namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery

struct DeliveryNotifyRequest {

        /**
         * @FieldDoc(
         *     description = "配送渠道code",
         *     example = ""
         * )
         */
        1: string channelCode;

        /**
         * @FieldDoc(
         *     description = "配送渠道ID",
         *     example = ""
         * )
         */
        2: string deliveryId;

        /**
         * @FieldDoc(
         *     description = "渠道配送ID",
         *     example = ""
         * )
         */
        3: string channelDeliveryId;

        /**
         * @FieldDoc(
         *     description = "外部订单号",
         *     example = ""
         * )
         */
        4: string orderId;

        /**
         * @FieldDoc(
         *     description = "配送订单状态",
         *     example = ""
         * )
         */
        5: i32 status;

        /**
         * @FieldDoc(
         *     description = "骑手名称",
         *     example = ""
         * )
         */
        6: string riderName;

        /**
         * @FieldDoc(
         *     description = "骑手电话",
         *     example = ""
         * )
         */
        7: string riderPhone;

        /**
         * @FieldDoc(
         *     description = "取消原因类型",
         *     example = ""
         * )
         */
        8: i32 cancelReasonType;

        /**
         * @FieldDoc(
         *     description = "取消原因描述",
         *     example = ""
         * )
         */
        9: string cancelReasonDesc;

        /**
         * @FieldDoc(
         *     description = "开放平台分配的appkey",
         *     example = ""
         * )
         */
        10: string appkey;

        /**
         * @FieldDoc(
         *     description = "时间戳",
         *     example = ""
         * )
         */
        11: i64 timestamp;

        /**
         * @FieldDoc(
         *     description = "数字签名",
         *     example = ""
         * )
         */
        12: string sign

        /**
         * @FieldDoc(
         *     description = "异常ID",
         *     example = ""
         * )
         */
        13: i64 exceptionId;

        /**
         * @FieldDoc(
         *     description = "订单异常代码",
         *     example = ""
         * )
         */
        14: i32 exceptionCode;

        /**
         * @FieldDoc(
         *     description = "订单异常描述",
         *     example = ""
         * )
         */
        15: string exceptionDesc;

        /**
         * @FieldDoc(
         *     description = "配送员上报异常时间",
         *     example = ""
         * )
         */
        16: i64 exceptionTime;
}