namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery

struct CancelDeliveryRequest {

        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = ""
         * )
         */
        1:  i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = ""
         * )
         */
        2: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "配送渠道ID",
         *     example = ""
         * )
         */
        3: i32 deliveryChannelId;

        /**
         * @FieldDoc(
         *     description = "配送标识",
         *     example = ""
         * )
         */
        4: i64 deliveryId;

        /**
         * @FieldDoc(
         *     description = "渠道配送单号",
         *     example = ""
         * )
         */
        5: string channelDeliveryId;

        /**
         * @FieldDoc(
         *     description = "取消原因类型",
         *     example = ""
         * )
         */
        6: i32 cancelReasonType;

        /**
         * @FieldDoc(
         *     description = "取消原因描述",
         *     example = ""
         * )
         */
        7: string cancelReasonDesc;
}