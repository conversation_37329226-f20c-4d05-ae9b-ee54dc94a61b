namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery

struct GoodsDTO {


        /**
        * @FieldDoc(
        *     description = "货物名称",
        *     example = ""
        * )
        */
        1: string name;

        /**
        * @FieldDoc(
        *     description = "货物数量",
        *     example = ""
        * )
        */
        2: i32 count;

        /**
        * @FieldDoc(
        *     description = "货物单价  单位:分",
        *     example = ""
        * )
        */
        3: i32 price;

        /**
        * @FieldDoc(
        *     description = "货物单位",
        *     example = ""
        * )
        */
        4: string unit;

        /**
        * @FieldDoc(
        *     description = "货物单件重量 单位:g",
        *     example = ""
        * )
        */
        5: i32 weight
}