namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery

struct CheckDeliverableRequest {

        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = ""
         * )
         */
        1:  i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "门店ID",
         *     example = ""
         * )
         */
        2: i64 shopId;

        /**
         * @FieldDoc(
         *     description = "配送渠道ID",
         *     example = ""
         * )
         */
        3: i32 deliveryChannelId;

        /**
         * @FieldDoc(
         *     description = "配送服务代码",
         *     example = ""
         * )
         */
        4: string deliveryServiceCode;

        /**
        * @FieldDoc(
        *     description = "收件人地址",
        *     example = ""
        * )
        */
        5: string receiverAddress;

        /**
        * @FieldDoc(
        *     description = "收件人地址经度",
        *     example = "收件人地址经度 （火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 116398419"
        * )
        */
        6: double receiverLongitude;

        /**
        * @FieldDoc(
        *     description = "收件人地址纬度",
        *     example = "收件人地址纬度 （火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 116398419"
        * )
        */
        7: double receiverLatitude;

        /**
        * @FieldDoc(MTDeliveryRequestParamBuilder
        *     description = "坐标类型",
        *     example = "坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0）"
        * )
        */
        8: i32 coordinateType;

        /**
        * @FieldDoc(
        *     description = "期望送达时间 时区为GMT+8，当前距离Epoch（1970年1月1日) 以毫秒计算的时间",
        *     example = ""
        * )
        */
        9: i64 expectDeliveryTime;


}