namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery

include "ResultStatus.thrift"

struct LocationInfo {

        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus status;

        /**
        * @FieldDoc(
        *     description = "纬度",
        *     example = "116398419"
        * )
        */
        2: double latitude;

        /**
        * @FieldDoc(
        *     description = "经度",
        *     example = "116398419"
        * )
        */
        3: double longitude;
}

struct RangeInfo {
        /**
         * @FieldDoc(
         *     description = "范围id",
         *     example = ""
         * )
         */
        1: i32 rangeId;

        /**
         * @FieldDoc(
         *     description = "范围",
         *     example = ""
         * )
         */
        2: list<LocationInfo> ranges;
}