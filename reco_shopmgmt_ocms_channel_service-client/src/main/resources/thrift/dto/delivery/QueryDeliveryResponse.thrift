namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery

include "ResultStatus.thrift"

struct QueryDeliveryResponse {

        /**
         * @FieldDoc(
         *     description = "结果状态",
         *     example = "status"
         * )
         */
        1: ResultStatus.ResultStatus resultStatus;

        /**
         * @FieldDoc(
         *     description = "配送标识",
         *     example = ""
         * )
         */
        2: i64 deliveryId;

        /**
         * @FieldDoc(
         *     description = "渠道配送单号",
         *     example = ""
         * )
         */
        3: string channelDeliveryId;

        /**
         * @FieldDoc(
         *     description = "外部订单号",
         *     example = ""
         * )
         */
        4: string orderId;

        /**
         * @FieldDoc(
         *     description = "配送单状态",
         *     example = ""
         * )
         */
        5: i32 status;

        /**
         * @FieldDoc(
         *     description = "订单状态变更时间",
         *     example = ""
         * )
         */
        6: i64 operate;

        /**
         * @FieldDoc(
         *     description = "骑手姓名",
         *     example = ""
         * )
         */
        7: string riderName;

        /**
         * @FieldDoc(
         *     description = "骑手电话",
         *     example = ""
         * )
         */
        8: string riderPhone;

        /**
         * @FieldDoc(
         *     description = "取消原因类型",
         *     example = ""
         * )
         */
        9: i32 cancelReasonType;

        /**
         * @FieldDoc(
         *     description = "取消原因描述",
         *     example = ""
         * )
         */
        10: string cancelReasonDesc;
}