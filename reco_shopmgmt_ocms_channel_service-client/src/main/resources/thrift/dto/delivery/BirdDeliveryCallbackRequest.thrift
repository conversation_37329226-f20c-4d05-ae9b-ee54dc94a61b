namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery

struct BirdDeliveryCallbackRequest {

        /**
         * @FieldDoc(
         *     description = "开放平台分配的appkey",
         *     example = ""
         * )
         */
        1: string appId;

        /**
         * @FieldDoc(
         *     description = "经过URLEncode的数据",
         *     example = ""
         * )
         */
        2: string data;

        /**
         * @FieldDoc(
         *     description = "请求盐",
         *     example = ""
         * )
         */
        3: i32 salt;

        /**
         * @FieldDoc(
         *     description = "请求签名",
         *     example = ""
         * )
         */
        4: string signature;

}