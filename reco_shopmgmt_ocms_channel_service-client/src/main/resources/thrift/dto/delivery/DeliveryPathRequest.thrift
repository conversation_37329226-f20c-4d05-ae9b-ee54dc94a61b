namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.delivery

struct DeliveryPathRequest {

        /**
         * @FieldDoc(
         *     description = "订单号，商家可根据订单号查询配送流程的订单骑手位置坐标列表。",
         *     example = ""
         * )
         */
        1:  i64 viewOrderId;

        /**
         * @FieldDoc(
         *     description = "三方门店code，需要和订单号对应。",
         *     example = ""
         * )
         */
        2: string appPoiCode;

        /**
        * @FieldDoc(
        *     description = "配送渠道ID",
        *     example = ""
        * )
        */
        3: i32 deliveryChannelId;

        /**
      * @FieldDoc(
      *     description = "租户ID",
      *     example = ""
      * )
      */
        4:  i64 tenantId;
        /**
               * @FieldDoc(
               *     description = "订单渠道id",
               *     example = ""
               * )
               */
        5: i32 channelId;
        /**
              * @FieldDoc(
              *     description = "storeid",
              *     example = ""
              * )
              */
        6: i64 storeId;
}