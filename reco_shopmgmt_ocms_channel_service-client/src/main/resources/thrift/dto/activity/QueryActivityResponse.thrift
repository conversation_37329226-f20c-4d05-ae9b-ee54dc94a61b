namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"

/**
 * @TypeDoc(
 *     description = "折扣商品活动信息"
 * )
 */
struct ChannelActDataInfoDTO {
    /**
     * @FieldDoc(
     *     description = "商品编码",
     *     example = {}
     * )
     */
    1: optional string skuId;
    /**
     * @FieldDoc(
     *     description = "活动类型",
     *     example = {}
     * )
     */
    2: optional i32 userType;
    /**
     * @FieldDoc(
     *     description = "开始时间",
     *     example = {}
     * )
     */
    3: optional i32 startTime;
    /**
     * @FieldDoc(
     *     description = "结束时间",
     *     example = {}
     * )
     */
    4: optional i32 endTime;
    /**
     * @FieldDoc(
     *     description = "每单限购",
     *     example = {}
     * )
     */
    5: optional i32 orderLimit;
    /**
     * @FieldDoc(
     *     description = "每日限购",
     *     example = {}
     * )
     */
    6: optional i32 dayLimit;
    /**
     * @FieldDoc(
     *     description = "生效时段",
     *     example = {}
     * )
     */
    7: optional string period;
    /**
     * @FieldDoc(
     *     description = "生效周期",
     *     example = {}
     * )
     */
    8: optional string weeksTime;
    /**
     * @FieldDoc(
     *     description = "活动类别",
     *     example = {}
     * )
     */
    9: optional i32 settingType;
    /**
     * @FieldDoc(
     *     description = "活动价",
     *     example = {}
     * )
     */
    10: optional double actPrice;
    /**
     * @FieldDoc(
     *     description = "折扣系数",
     *     example = {}
     * )
     */
    11: optional double discountCoefficient;
    /**
     * @FieldDoc(
     *     description = "序号",
     *     example = {}
     * )
     */
    12: optional i32 sequence;
    /**
     * @FieldDoc(
     *     description = "渠道活动ID",
     *     example = {}
     * )
     */
    13: optional string channelActivityId;
        /**
         * @FieldDoc(
         *     description = "活动当前的状态",
         *     example = {}
         * )
         */
    14: optional i32 status;
            /**
             * @FieldDoc(
             *     description = "真正的skuId",
             *     example = {}
             * )
             */
    15: optional string realSkuId;
}

/**
 * @TypeDoc(
 *     description = "查询门店折扣商品列表"
 * )
 */
struct QueryActivityResponse {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = {}
     * )
     */
    1: optional ResultStatus.ResultStatus status;
    /**
     * @FieldDoc(
     *     description = "活动列表",
     *     example = {}
     * )
     */
    2: optional list<ChannelActDataInfoDTO> actList;
}