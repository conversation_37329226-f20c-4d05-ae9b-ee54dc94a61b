namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ChannelPoiParamInfo.thrift"
include "ActTypeEnum.thrift"
include "ItemOpTypeEnum.thrift"
include "ActivityTypeEnum.thrift"
include "SettingTypeEnum.thrift"
include "UserTypeEnum.thrift"


/**
 * @TypeDoc(
 *     description = "活动信息"
 * )
 */
struct ChannelActivityInfo {
    /**
     * @FieldDoc(
     *     description = "门店编码",
     *     example = {}
     * )
     */
    1: optional i64 storeId;
    /**
     * @FieldDoc(
     *     description = "渠道活动ID",
     *     example = {100}
     * )
     */
    2: optional string channelActivityId;
    /**
     * @FieldDoc(
     *     description = "活动名称",
     *     example = {}
     * )
     */
    3: optional string activityName;
    /**
     * @FieldDoc(
     *     description = "开始时间",
     *     example = {1551766583}
     * )
     */
    4: optional i64 startTime;
    /**
     * @FieldDoc(
     *     description = "结束时间",
     *     example = {1551766583}
     * )
     */
    5: optional i64 endTime;
    /**
     * @FieldDoc(
     *     description = "广告词",
     *     example = {}
     * )
     */
    6: optional string advertising;
    /**
     * @FieldDoc(
     *     description = "促销类型，1-普通折扣",
     *     example = {}
     * )
     */
    7: optional ActivityTypeEnum.ActivityTypeEnum activityType;
    /**
     * @FieldDoc(
     *     description = "活动类型，0-按折扣系数开展活动，1-按折扣价格开展活动",
     *     example = {}
     * )
     */
    8: optional SettingTypeEnum.SettingTypeEnum settingType;
    /**
     * @FieldDoc(
     *     description = "用户类型，0-不限，1-门店新客",
     *     example = {}
     * )
     */
    9: optional UserTypeEnum.UserTypeEnum userType;
    /**
     * @FieldDoc(
     *     description = "生效时间段，最多3段，00:00:-23:59",
     *     example = {}
     * )
     */
    10: optional string period;
    /**
     * @FieldDoc(
     *     description = "生效活动周期，1,2,3,4,5,6,7",
     *     example = {}
     * )
     */
    11: optional string weeksTime;
    /**
     * @FieldDoc(
     *     description = "是否设备限购",
     *     example = {}
     * )
     */
    12: optional bool limitDevice = false;
    /**
     * @FieldDoc(
     *     description = "是否账号限购",
     *     example = {}
     * )
     */
    13: optional bool limitPin = false;
    /**
     * @FieldDoc(
     *     description = "限购数量，0-不限，如账号限购、设备限购有一个为1，则限购件数必须大于0的整数",
     *     example = {}
     * )
     */
    14: optional i32 limitCount;
    /**
     * @FieldDoc(
     *     description = "是否按日限购",
     *     example = {}
     * )
     */
    15: optional bool limitDaily = false;
    /**
     * @FieldDoc(
     *     description = "每单限购，只能是正整数或-1，最大为10",
     *     example = {}
     * )
     */
    16: optional i32 orderLimit;
    /**
     * @FieldDoc(
     *     description = "当日活动库存，只能是正整数或-1",
     *     example = {}
     * )
     */
    17: optional i32 dayLimit;
    /**
     * @FieldDoc(
     *     description = "商家商品编码",
     *     example = {1}
     * )
     */
    18: optional string skuId;
    /**
     * @FieldDoc(
     *     description = "促销价，单位分",
     *     example = {}
     * )
     */
    19: optional i32 promotionPrice;
    /**
     * @FieldDoc(
     *     description = "商品总限购数",
     *     example = {19}
     * )
     */
    20: optional i32 limitSkuCount;
    /**
     * @FieldDoc(
     *     description = "折扣系数",
     *     example = {8.8}
     * )
     */
    21: optional double discountCoefficient;
    /**
     * @FieldDoc(
     *     description = "序号，商品在活动内的排序，值越小越靠前，必须为大于0的正整数",
     *     example = {}
     * )
     */
    22: optional i32 sequence;
    /**
     * @FieldDoc(
     *     description = "商品操作类型",
     *     example = {}
     * )
     */
    23: optional ItemOpTypeEnum.ItemOpTypeEnum itemOpType;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = {1}
     * )
     */
    24: optional i32 channelId;
    /**
     * @FieldDoc(
     *     description = "内部活动ID",
     *     example = {1}
     * )
     */
    25: optional i64 activityId;
}

/**
 * @TypeDoc(
 *     description = "创建/更新活动请求参数"
 * )
 */
struct ActivitySaveRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = {1}
     * )
     */
    1: optional i64 tenantId;
    /**
     * @FieldDoc(
     *     description = "活动信息参数",
     *     example = {}
     * )
     */
    2: optional list<ChannelActivityInfo> activityList;
    /**
     * @FieldDoc(
     *     description = "请求时间，时间戳",
     *     example = {}
     * )
     */
    3: optional i32 timeStamp;
    /**
     * @FieldDoc(
     *     description = "活动操作类型",
     *     example = {}
     * )
     */
    4: optional ActTypeEnum.ActTypeEnum actType;
}