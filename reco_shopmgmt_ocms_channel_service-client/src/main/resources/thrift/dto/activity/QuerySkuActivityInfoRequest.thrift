namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

/**
 * @TypeDoc(
 *     description = "批量查询商品促销活动信息请求参数"
 * )
 */
struct QuerySkuActivityInfoRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = {1}
     * )
     */
    1: required i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "渠道门信息",
     *     example = {}
     * )
     */
    2: required i32 channelId;

    /**
     * @FieldDoc(
     *     description = "渠道门店信息",
     *     example = {}
     * )
     */
    3: required i64 storeId;

    /**
     * @FieldDoc(
     *     description = "渠道门店商品信息集合",
     *     example = {}
     * )
     */
    4: required list<string> customSpuIds;

    /**
     * @FieldDoc(
     *     description = "请求时间，时间戳",
     *     example = {}
     * )
     */
    5: optional i32 timeStamp;

    /**
     * @FieldDoc(
     *     description = "查询的活动类型，为空则是默认的0",
     *     example = {}
     * )
     */
    6: optional list<i32> activityTypeList;
}