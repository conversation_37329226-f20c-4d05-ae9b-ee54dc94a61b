namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"

struct StoreCustomSpuKey {
        /**
        * 门店id
**/
        1: required i64 storeId;
        /**
        * 渠道门店spus,不允许重复
**/
        2: set<string> customSpuIds;
}


/**
 * @TypeDoc(
 *     description = "批量查询商品促销活动信息请求参数"
 * )
 */
struct QueryCanModifyPriceRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = {1}
         * )
         */
        1: required i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "渠道信息",
         *     example = {}
         * )
         */
        2: required i32 channelId;

        /**
         * @FieldDoc(
         *     description = "渠道门店商品信息集合",
         *     example = {}
         * )
         */
        3: required list<StoreCustomSpuKey> customSpuIds;
}

struct SpuCanModifyPriceInfo {
        /**
        * 渠道spu
**/
        1: required string customSpuId;
        /**
        * 是否能改价
**/
        2: required bool canModifyPrice;
}

struct StoreSpuCanModifyPriceInfo {
        /**
        * 门店id
**/
        1: required i64 storeId;
        /**
        * spu能否改价信息
**/
        2: required list<SpuCanModifyPriceInfo> spuCanModifyPriceInfos;
}

struct StoreSpuErrorInfo{
        1: required i64 storeId;
        2: required string customSpuId;
        3: required string errorMsg;
}

struct QueryCanModifyPriceResponse {
        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = {}
         * )
         */
        1: required ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "门店spu能否改价信息集合",
         *     example = {}
         * )
         */
        2: optional list<StoreSpuCanModifyPriceInfo> storeSpuCanModifyPriceInfos;

        3: optional list<StoreSpuErrorInfo> errorSpuInfos;
}

