namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"

struct SkuActivityInfo {

        /**
         * @FieldDoc(
         *     description = "活动编码",
         *     example = {}
         * )
         */
        1: optional i64 activityId;

        /**
         * @FieldDoc(
         *     description = "商品ID",
         *     example = {}
         * )
         */
        2: optional string customSpuId;

        /**
         * @FieldDoc(
         *     description = "活动名称",
         *     example = {}
         * )
         */
        3: optional string activityName;

        /**
         * @FieldDoc(
         *     description = "活动类型：2-满减；17-折扣商品；20-第二份半价；23-买赠；27-指定商品满减；40-加价换购；43-X元M件；56-爆品；",
         *     example = {}
         * )
         */
        4: optional i32 activityType;

        /**
         * @FieldDoc(
         *     description = "活动状态：0-活动未开始；1-活动已生效；5-活动冻结中",
         *     example = {}
         * )
         */
        5: optional i32 activityStatus;

        /**
         * @FieldDoc(
         *     description = "活动开始时间，10位秒级时间戳",
         *     example = {}
         * )
         */
        6: optional i64 startTime;

        /**
         * @FieldDoc(
         *     description = "活动结束时间，10位秒级时间戳",
         *     example = {}
         * )
         */
        7: optional i64 endTime;

        /**
         * @FieldDoc(
         *     description = "商品价格是否可修改：0-可以修改；1-不能修改",
         *     example = {}
         * )
         */
        8: optional i32 isCanModifyPrice;
}

struct QuerySkuActivityInfoResponse {

        /**
         * @FieldDoc(
         *     description = "状态",
         *     example = {}
         * )
         */
        1: optional ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "活动信息集",
         *     example = {}
         * )
         */
        2: optional list<SkuActivityInfo> skuActivityInfos;
}