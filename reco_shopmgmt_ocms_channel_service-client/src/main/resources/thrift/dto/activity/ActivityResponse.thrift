namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"
include "ActivityResultDataInfo.thrift"

/**
 * @TypeDoc(
 *     description = "活动信息响应数据"
 * )
 */
struct ActivityResponse {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = {}
     * )
     */
    1: optional ResultStatus.ResultStatus status;
    /**
     * @FieldDoc(
     *     description = "成功数据",
     *     example = {}
     * )
     */
    2: optional list<ActivityResultDataInfo.ActivityResultDataInfo> successData;
    /**
     * @FieldDoc(
     *     description = "失败数据",
     *     example = {}
     * )
     */
    3: optional list<ActivityResultDataInfo.ActivityResultDataInfo> errorData;
}