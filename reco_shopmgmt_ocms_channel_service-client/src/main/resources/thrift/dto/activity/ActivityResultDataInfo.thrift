namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"

/**
 * @TypeDoc(
 *     description = "保存活动返回数据"
 * )
 */
struct ActivityResultDataInfo {
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = {1}
     * )
     */
    1: optional i32 channelId;
    /**
     * @FieldDoc(
     *     description = "渠道门店编码",
     *     example = {1}
     * )
     */
    2: optional i64 storeId;
    /**
     * @FieldDoc(
     *     description = "渠道活动ID",
     *     example = {1}
     * )
     */
    3: optional string channelActivityId;
    /**
     * @FieldDoc(
     *     description = "商家商品编码",
     *     example = {1}
     * )
     */
    4: optional string skuId;
    /**
     * @FieldDoc(
     *     description = "错误信息",
     *     example = {}
     * )
     */
    5: optional ResultStatus.ResultStatus data;
}