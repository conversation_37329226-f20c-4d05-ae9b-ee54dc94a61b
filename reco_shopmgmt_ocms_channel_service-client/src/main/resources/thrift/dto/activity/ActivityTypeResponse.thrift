namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto

include "ResultStatus.thrift"

/**
 * @TypeDoc(
 *     description = "活动类型信息"
 * )
 */
struct ActivityTypeInfoDTO {
    /**
     * @FieldDoc(
     *     description = "活动类型code",
     *     example = {1}
     * )
     */
    1: optional string code;
    /**
     * @FieldDoc(
     *     description = "活动类型名称",
     *     example = {1}
     * )
     */
    2: optional string name;
    /**
     * @FieldDoc(
     *     description = "渠道编码",
     *     example = {1}
     * )
     */
    3: optional i32 orderBizType;
    /**
     * @FieldDoc(
     *     description = "促销类型",
     *     example = {1}
     * )
     */
    4: optional string promotionType;

    /**
     * @FieldDoc(
     *     description = "活动类型",
     *     example = {1}
     * )
     */
    5: optional i32 activityType;
}

/**
 * @TypeDoc(
 *     description = "活动类型查询返回结果"
 * )
 */
struct ActivityTypeResponse {
    /**
     * @FieldDoc(
     *     description = "状态",
     *     example = {}
     * )
     */
    1: optional ResultStatus.ResultStatus status;
    /**
     * @FieldDoc(
     *     description = "活动类型信息",
     *     example = {1}
     * )
     */
    2: optional list<ActivityTypeInfoDTO> actTypeList;
}
