namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common

include "ResultStatus.thrift"
include "ProductChannelUnifyErrorEnum.thrift"

/**
 * @TypeDoc(
 *     description = "SKU信息"
 * )
 */
struct SkuKey{
        /**
        * @FieldDoc(
        *     description = "商家SKU编码",
        *     example = "10"
        * )
        */
        1: string customSkuId;
        /**
        * @FieldDoc(
        *     description = "渠道SKU编码",
        *     example = "10"
        * )
        */
        2: string channelSkuId;
        /**
         * @FieldDoc(
         *     description = "渠道总部SKU编码",
         *     example = "10"
         * )
         */
        3: optional string merchantChannelSkuId;
}

/**
 * @TypeDoc(
 *     description = "SPU信息"
 * )
 */
struct SpuKey{
        1: string customSpuId;
        /**
         * @FieldDoc(
         *     description = "渠道SPU编码",
         *     example = "10"
         * )
         */
        2: string channelSpuId;
        /**
         * @FieldDoc(
         *     description = "SKU编码",
         *     example = "10"
         * )
         */
        3: list<SkuKey> skus;
        /**
         * @FieldDoc(
         *     description = "渠道类目末级编码",
         *     example = "10000003"
         * )
         */
        4: optional string categoryId;
         /**
         * @FieldDoc(
         *     description = "规格",
         *     example = 1
         * )
         */
        5: optional i32 specType;
        /**
         * @FieldDoc(
         *     description = "渠道总部商品spuid",
         *     example = "10000003"
         * )
         */
        6: optional string merchantChannelSpuId;



        7: optional i64 storeId;
}

/**
 * @TypeDoc(
 *     description = "接口调用错误数据信息"
 * )
 */
struct ResultErrorSku {
    
    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = "1"
     *
     * )
     */
    1: i64 storeId;
    
    /**
     * @FieldDoc(
     *     description = "商家SKU编码",
     *     example = "10"
     * )
     */
    2: string skuId;
    
    /**
     * @FieldDoc(
     *     description = "错误描述",
     *     example = "**价格必须大于0"
     * )
     */
    3: string errorMsg;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    4: i32 channelId;

    /**
     * @FieldDoc(
     *     description = "具体错误状态码, 默认可重试",
     *     example = "1"
     * )
     */
    5: i32 errorCode = 4;
    /**
     * @FieldDoc(
     *     description = "渠道商品编码",
     *     example = {}
     * )
     */
    6: string channelSkuId;
    /**
    * 商家SPU编码
    **/
    7: optional string spuId;
    /**
    * 渠道SPU编码
    **/
    8: optional string channelSpuId;

    /**
     * @FieldDoc(
     *     description = "渠道统一错误码",
     *     example = "STORE_SPU_NOT_EXIST"
     * )
     */
    9: ProductChannelUnifyErrorEnum.ProductChannelUnifyErrorEnum channelUnifyError;
}

/**
 * @TypeDoc(
 *     description = "接口调用成功数据信息"
 * )
 */
struct ResultSuccessSku {

    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = "1"
     *
     * )
     */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "商家SKU编码，获取本次请求的唯一码",
     *     example = "10"
     * )
     */
    2: string skuId;

    /**
     * @FieldDoc(
     *     description = "调用渠道接口返回的渠道生成的唯一码，比如新增商品该字段为渠道的商品编码，图片上传接口该字段为图片URL或者Hash码",
     *     example = "10"
     * )
     */
    3: string channelResultInfo;
    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    4: i32 channelId;
    /**
     * @FieldDoc(
     *     description = "渠道商品编码",
     *     example = {}
     * )
     */
    5: string channelSkuId;
    /**
    * 商家SPU编码
**/
    6: optional string spuId;
    /**
    * 渠道SPU编码
**/
    7: optional string channelSpuId;
}

/**
 * @TypeDoc(
 *     description = "接口调用错误数据信息"
 * )
 */
struct ResultErrorSpu {

    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = "1"
     *
     * )
     */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "错误描述",
     *     example = "**价格必须大于0"
     * )
     */
    2: string errorMsg;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    3: i32 channelId;

    /**
     * @FieldDoc(
     *     description = "具体错误状态码, 默认可重试",
     *     example = "1"
     * )
     */
    4: i32 errorCode = 4;

    /**
     * @FieldDoc(
     *     description = "商家商品SKU编码",
     *     example = {}
     * )
     */
    5: SpuKey spuInfo;

    /**
     * @FieldDoc(
     *     description = "渠道统一错误码",
     *     example = "STORE_SPU_NOT_EXIST"
     * )
     */
    6: ProductChannelUnifyErrorEnum.ProductChannelUnifyErrorEnum channelUnifyError;
}

/**
 * @TypeDoc(
 *     description = "接口调用成功数据信息"
 * )
 */
struct ResultSuccessSpu {

    /**
     * @FieldDoc(
     *     description = "门店ID",
     *     example = "1"
     *
     * )
     */
    1: i64 storeId;

    /**
     * @FieldDoc(
     *     description = "调用渠道接口返回的渠道生成的唯一码，比如新增商品该字段为渠道的商品编码，图片上传接口该字段为图片URL或者Hash码",
     *     example = "10"
     * )
     */
    2: string channelResultInfo;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    3: i32 channelId;

    /**
     * @FieldDoc(
     *     description = "商品信息",
     *     example = {}
     * )
     */
    4: SpuKey spuInfo;

}

/**
 * @TypeDoc(
 *     description = "接口调用返回错误结果信息"
 * )
 */
struct ResultData {
    /**
     * @FieldDoc(
     *     description = "接口调用结果状态，调用结果全部成功或部分成功时返回0，需要解析sucData和errorData，其他场景只返回对应异常码",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "错误列表",
     *     example = ""
     * )
     */
    2: list<ResultErrorSku> errorData;

    /**
     * @FieldDoc(
     *     description = "成功列表",
     *     example = ""
     * )
     */
    3: list<ResultSuccessSku> sucData;
}

/**
 * @TypeDoc(
 *     description = "接口调用返回错误结果信息"
 * )
 */
struct ResultSingleData {
    /**
     * @FieldDoc(
     *     description = "接口调用结果状态，调用结果全部成功或部分成功时返回0，需要解析sucData和errorData，其他场景只返回对应异常码",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "错误sku",
     *     example = ""
     * )
     */
    2: optional ResultErrorSku errorData;

    /**
     * @FieldDoc(
     *     description = "成功sku",
     *     example = ""
     * )
     */
    3: optional ResultSuccessSku sucData;
}

/**
 * @TypeDoc(
 *     description = "接口调用返回错误结果信息"
 * )
 */
struct ResultSpuData {
        /**
         * @FieldDoc(
         *     description = "接口调用结果状态，调用结果全部成功或部分成功时返回0，需要解析sucData和errorData，其他场景只返回对应异常码",
         *     example = ""
         * )
         */
        1: ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "错误列表",
         *     example = ""
         * )
         */
        2: list<ResultErrorSpu> errorData;

        /**
         * @FieldDoc(
         *     description = "成功列表",
         *     example = ""
         * )
         */
        3: list<ResultSuccessSpu> sucData;
}

/**
 * @TypeDoc(
 *     description = "接口调用返回错误结果信息"
 * )
 */
struct ResultSingleSpuData {
        /**
         * @FieldDoc(
         *     description = "接口调用结果状态，调用结果全部成功或部分成功时返回0，需要解析sucData和errorData，其他场景只返回对应异常码",
         *     example = ""
         * )
         */
        1: ResultStatus.ResultStatus status;

        /**
         * @FieldDoc(
         *     description = "错误spu",
         *     example = ""
         * )
         */
        2: optional ResultErrorSpu errorData;

        /**
         * @FieldDoc(
         *     description = "成功spu",
         *     example = ""
         * )
         */
        3: optional ResultSuccessSpu sucData;
}

/**
 * @TypeDoc(
 *     description = "地区信息"
 * )
 */
struct AreaInfo {
    /**
     * @FieldDoc(
     *     description = "地区id",
     *     example = ""
     * )
     */
    1: string areaId;

    /**
     * @FieldDoc(
     *     description = "地区名",
     *     example = ""
     * )
     */
    2: string areaName;

    /**
     * @FieldDoc(
     *     description = "父地区id",
     *     example = ""
     * )
     */
    3: string parentId;

    /**
     * @FieldDoc(
     *     description = "地区id路径",
     *     example = ""
     * )
     */
    4: string areaIdPath;

    /**
     * @FieldDoc(
     *     description = "地区名路径",
     *     example = ""
     * )
     */
    5: string areaNamePath;

    /**
     * @FieldDoc(
     *     description = "地区层级",
     *     example = ""
     * )
     */
    6: string level;
}

/**
 * @TypeDoc(
 *     description = "接口调用返回错误结果信息"
 * )
 */
struct GetAreaListResponse {
    /**
     * @FieldDoc(
     *     description = "结果状态",
     *     example = ""
     * )
     */
    1: ResultStatus.ResultStatus status;

    /**
     * @FieldDoc(
     *     description = "地区信息",
     *     example = ""
     * )
     */
    2: list<AreaInfo> areaList;
}