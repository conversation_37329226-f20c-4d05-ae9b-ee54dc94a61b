namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common

/**
 * @TypeDoc(
 *     description = "接口调用错误数据信息"
 * )
 */
struct BaseRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;

    /**
     * @FieldDoc(
     *     description = "门店ID,因为现在各渠道接口都是门店级别的，又考虑到多门店操作信息相同的情况，门店定义为List，如果操作参数多门店相同则可以传入多门店，如果多门店参数不同，一次调用批量操作仅是门店级别",
     *     example = "1"
     * )
     */
    3: list<i64> storeIdList;
    // 是否异步调用
    4: bool asyncInvoke;
    /**
     * @FieldDoc(
     *     description = "租户品牌应用id",
     *     example = 0
     * )
     */
    5:optional i64 appId;

    /**
     * @FieldDoc(
     *     description = "是否erp租户表示(true：erp租户)",
     *     example = true
     * )
     */
    6:optional bool erpTenant;

    /**
         * @FieldDoc(
         *     description = "订单ID",
         *     example = true
         * )
         */
    7:optional string orderId;
}

/**
 * @TypeDoc(
 *     description = "接口调用错误数据信息"
 * )
 */
struct BatchGetPoiDetailsRequest {
        /**
         * @FieldDoc(
         *     description = "租户ID",
         *     example = "1"
         * )
         */
        1: i64 tenantId;

        /**
         * @FieldDoc(
         *     description = "渠道ID",
         *     example = "1"
         * )
         */
        2: i32 channelId;

        /**
         * @FieldDoc(
         *     description = "门店ID,因为现在各渠道接口都是门店级别的，又考虑到多门店操作信息相同的情况，门店定义为List，如果操作参数多门店相同则可以传入多门店，如果多门店参数不同，一次调用批量操作仅是门店级别",
         *     example = "1"
         * )
         */
        3: list<string> storeIds;

        /**
         * @FieldDoc(
         *     description = "是否异步调用，默认为false",
         *     example = "true"
         * )
         */
        4: bool asyncInvoke;

        /**
         * @FieldDoc(
         *     description = "租户品牌应用id，如果设置了 appId，则需要保证 storeIds 都归属在此 appId 下",
         *     example = 0
         * )
         */
        5: optional i64 appId;
}

/**
 * @TypeDoc(
 *     description = "接口调用错误数据信息"
 * )
 */
struct BaseRequestSimple {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;

    /**
     * @FieldDoc(
     *     description = "是否erp租户表示(true：erp租户)",
     *     example = true
     * )
     */
    3:optional bool erpTenant;

    /**
     * @FieldDoc(
     *     description = "应用ID（适用于京东等总部管品渠道的多应用场景）",
     *     example = true
     * )
     */
    4:optional i64 appId;
}

/**
 * @TypeDoc(
 *     description = "接口调用错误数据信息"
 * )
 */
struct BatchGetPoiIdsRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;

    /**
     * @FieldDoc(
    *     description = "系统参数",
    **/
    3: string sysParams;
}

/**
 * @TypeDoc(
 *     description = "基础分页请求参数"
 * )
 */
struct BasePageRequest {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;

    /**
     * @FieldDoc(
     *     description = "页码",
     *     example = "1"
     * )
     */
    3: i32 pageNo;

    /**
     * @FieldDoc(
     *     description = "每页数量",
     *     example = "1"
     * )
     */
    4: i32 pageSize;
}

/**
 * @TypeDoc(
 *     description = "开放平台接口鉴权请求"
 * )
 */
struct AuthForOpenApiRequest {
        /**
         * @FieldDoc(
         *     description = "url",
         *     example = ""
         * )
         */
        1: string url;
        /**
         * @FieldDoc(
         *     description = "请求参数",
         *     example = ""
         * )
         */
        2: map<string, string> params;
        /**
         * @FieldDoc(
         *     description = "源IP",
         *     example = ""
         * )
         */
        3: string ip;

}

/**
 * @TypeDoc(
 *     description = "查询地区列表请求"
 * )
 */
struct GetAreaListRequest{
    /**
     * @FieldDoc(
     *     description = "租户和渠道基本信息",
     *     example = ""
     * )
     */
    1: required BaseRequestSimple baseInfo;

    /**
        * @FieldDoc(
        *     description = "父地区ID",
        *     example = 630000
        * )
    */
    2: optional i64 parentAreaId;

    /**
        * @FieldDoc(
        *     description = "关键字",
        *     example = "成都"
        * )
    */
    3: optional string keyword;
}

/**
 * @TypeDoc(
 *     description = "接口调用错误数据信息"
 * )
 */
struct BaseChannelPoiRequestSimple {
    /**
     * @FieldDoc(
     *     description = "租户ID",
     *     example = "1"
     * )
     */
    1: i64 tenantId;

    /**
     * @FieldDoc(
     *     description = "渠道ID",
     *     example = "1"
     * )
     */
    2: i32 channelId;

    /**
     * @FieldDoc(
     *     description = "门店信息",
     *     example = "1"
     * )
     */
    3: i64 poiId;
}