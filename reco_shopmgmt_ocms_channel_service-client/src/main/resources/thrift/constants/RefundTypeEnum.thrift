namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants

/**
 * @TypeDoc(
 *     description = "退款类型枚举"
 * )
 */
enum RefundTypeEnum {
    /**
     * @FieldDoc(
     *     description = "全部退款"
     * )
     */
    ALL = 1,
    /**
     * @FieldDoc(
     *     description = "部分退款"
     * )
     */
    PART = 2;


    /**
     * @FieldDoc(
     *     description = "克重退款"
     * )
     */
    WEIGHT = 3;

    /**
     * @FieldDoc(
     *     description = "初审退货退款"
     * )
     */
    FIRST_REFUND_GOODS = 4;

    /**
     * @FieldDoc(
     *     description = "终审退货退款"
     * )
     */
    FINAL_REFUND_GOODS = 5;

     /**
     * @FieldDoc(
     *     description = "售后退款"
     * )
     */
    AFTER_SALE_REFUND = 6;

    /**
     * @FieldDoc(
     *     description = "用户拒收"
     * )
     */
    REJECT_BY_CUSTOMER = 9;

    /**
     * @FieldDoc(
     *     description = "再次申请退款"
     * )
     */
    RE_APPLY=10;

    /**
     * @FieldDoc(
     *     description = "售后转申诉"
     * )
     */
    CHANGE_TO_APPEAL = 11,

    /**
     * @FieldDoc(
     *     description = "用户退货发货"
     * )
     */
    USER_RETURN_GOODS = 12;

    /**
     * @FieldDoc(
     *     description = "金额退"
     * )
     */
    AMOUNT = 13;

    /**
     * @FieldDoc(
     *     description = "审核仅退款"
     * )
     */
    REFUND_REVIEW_ONLY = 14;
}