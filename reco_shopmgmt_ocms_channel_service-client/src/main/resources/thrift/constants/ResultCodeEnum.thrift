namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants

/**
 * @TypeDoc(
 *     description = "统一状态码枚举"
 * )
 */
enum ResultCodeEnum {
    /**
     * @FieldDoc(
     *     description = "成功"
     * )
     */
    SUCCESS = 0,
    /**
     * @FieldDoc(
     *     description = "失败"
     * )
     */
    FAIL = 1,
    /**
     * @FieldDoc(
     *     description = "无效请求"
     * )
     */
    INVALID_REQUEST = 2,
    /**
     * @FieldDoc(
     *     description = "参数不合法"
     * )
     */
    INVALID_PARAM = 3,
    /**
     * @FieldDoc(
     *     description = "未开通此业务"
     * )
     */
    UNSUPPORTED_BUSINESS_TYPE = 10,
    /**
     * @FieldDoc(
     *     description = "调用渠道失败，请重试"
     * )
     */
    FAIL_ALLOW_RETRY = 4,
    /**
     * @FieldDoc(
     *     description = "未知渠道编码，请检查网关配置"
     * )
     */
    CHANNEL_CODE_INVALID = 100,
    /**
     * @FieldDoc(
     *     description = "订单状态类型转换异常，请检查渠道订单状态"
     * )
     */
    CHANNEL_ORDER_STATUS_INVALID = 101,
    /**
     * @FieldDoc(
     *     description = "配送状态类型转换异常，请检查渠道配送状态"
     * )
     */
    CHANNEL_DELIVERY_STATUS_INVALID = 102,
    /**
     * @FieldDoc(
     *     description = "未知的操作类型，请检查网关配置"
     * )
     */
    CHANNEL_NOTIFY_UNKNOWN = 103,
    /**
     * @FieldDoc(
     *     description = "未知的取消类型，请检查网关配置"
     * )
     */
    CHANNEL_RES_TYPE_UNKNOWN = 104,
    /**
     * @FieldDoc(
     *     description = "未知的订单类型，请检查网关配置"
     * )
     */
    CHANNEL_ORDER_STATUS_UNKNOWN = 105,
    /**
     * @FieldDoc(
     *     description = "订单已经调整过了"
     * )
     */
    ORDER_HAS_ADJUSTED = 106,

    /**
     * @FieldDoc(
     *     description = "该订单不存在"
     * )
     */
    ORDER_NOT_EXIST = 201,
    /**
     * @FieldDoc(
     *     description = "商家已完成备餐，不能重复备餐"
     * )
     */
    MEAL_COMPLETE = 205,
    /**
     * @FieldDoc(
     *     description = "未获取到配送员信息"
     * )
     */
    GET_DELIVERY_MSG = 208,
    /**
     * @FieldDoc(
     *     description = "操作失败此订单退款申请已经被处理"
     * )
     */
    REFUND_ALREADY_COMPLETED = 301,

    /**
     * @FieldDoc(
     *     description = "评价已回复"
     * )
     */
    CHANNEL_COMMENT_ALREADY_REPLIED = 700,

    /**
    * @FieldDoc(
    *     description = "美团渠道错误：门店不存在"
    * )
    */
    MT_STORE_NOT_EXIST = 803

    MT_QUERY_SPU_EXIST_ERROR = 809,

    /**
     * @FieldDoc(
     *     description = "未处理异常"
     * )
     */
    UNDEAL_ERROR = 900,

    /**
     * @FieldDoc(
     *     description = "触发限流"
     * )
     */
    TRIGGER_LIMIT = 999;

    /**
     * @FieldDoc(
     *     description = "渠道门店配置错误"
     * )
     */
    CHANNEL_STORE_CONFIG_ERROR=1000;

    /**
     * @FieldDoc(
     *     description = "渠道门店权限错误"
     * )
     */
    CHANNEL_STORE_AUTH_ERROR=1001;


    /**
     * @FieldDoc(
     *     description = "渠道系统异常"
     * )
     */
    CHANNEL_SYSTEM_ERROR=1099;

    /**
    * @FieldDoc(
    *     description = "更新有赞库存业务错误"
    * )
    */
    YZ_STOCK_BIZ_ERROR = 2001;

    /**
    * @FieldDoc(
    *     description = "更新有赞库存系统错误"
    * )
    */
    YZ_STOCK_SYS_ERROR = 2002;


    /**
      * @FieldDoc(
      *     description = "淘鲜达不允许售中用户发起退款"
      * )
      */
    TXD_CAN_NOT_CANCEL_CODE = 2510;

    /**
      * @FieldDoc(
      *     description = "参数不合法，自定义返回提示语"
      * )
      */
    INVALID_PARAM_CUSTOM_TIPS = 3001;

}