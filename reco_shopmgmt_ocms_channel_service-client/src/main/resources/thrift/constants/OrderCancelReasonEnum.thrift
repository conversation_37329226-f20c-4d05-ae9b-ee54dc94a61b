namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants

/**
 * @TypeDoc(
 *     description = "商家取消订单原因"
 * )
 */
enum TenantCancelOrderReasonEnum {
    /**
     * @FieldDoc(
     *     description = "自定义原因"
     * )
     */
    SELF_DEFINED = -1;

    /**
     * @FieldDoc(
     *     description = "全部退款"
     * )
     */
    OUT_OF_DELIVERY_RANGE = 1,
    /**
     * @FieldDoc(
     *     description = "店铺已打烊"
     * )
     */
    CLOSING_TIME = 2;

    /**
     * @FieldDoc(
     *     description = "商品已售完"
     * )
     */
    PRODUCT_SAIL_OUT = 3;
    /**
     * @FieldDoc(
     *     description = "商品价格发生变化"
     * )
     */
    PRODUCT_PRICE_CHANGED = 4;

    /**
     * @FieldDoc(
     *     description = "用户申请"
     * )
     */
    CUSTOMER_APPLY = 5;

    /**
    * @FieldDoc(
    *     description = "重复订单"
    * )
    */
    REDUPLICATE_ORDER = 6;

    /**
     * @FieldDoc(
     *     description = "店铺太忙"
     * )
     */
    SHOP_TOO_BUSI = 7;

    /**
     * @FieldDoc(
     *     description = "客户信息错误"
     * )
     */
    DELIVERY_INFO_WRONG = 8;

    /**
     * @FieldDoc(
     *     description = "假订单"
     * )
     */
    SPOOFING_ORDER = 9;

    /**
     * @FieldDoc(
     *     description = "非客户原因修改订单"
     * )
     */
    TENANT_MODIFY_ORDER = 10;

    /**
     * @FieldDoc(
     *     description = "非客户原因取消订单"
     * )
     */
    TENANT_CANCEL_ORDER = 11;

    /**
     * @FieldDoc(
     *     description = "配送延迟"
     * )
     */
    DELIVERY_DELAY = 12;

    /**
     * @FieldDoc(
     *     description = "售后投诉"
     * )
     */
    AFTER_SALE_COMPLAIN = 13;

    /**
     * @FieldDoc(
     *     description = "顾客联系不上"
     * )
     */
    CUSTOMER_NOT_CONNECT = 14;

    /**
     * @FieldDoc(
     *     description = "配送员取餐慢"
     * )
     */
    DELIVERY_RIDER_TAKE_MEAL_TOO_SLOW = 15;

    /**
     * @FieldDoc(
     *     description = "配送员送餐慢"
     * )
     */
    DELIVERY_RIDER_DISTRIBUTE_TOO_SLOW = 16;

    /**
     * @FieldDoc(
     *     description = "配送员丢餐，少餐，餐洒"
     * )
     */
    DELIVERY_RIDER_DROP_PRODUCT = 17;

}