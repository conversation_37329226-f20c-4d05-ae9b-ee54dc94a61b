namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants

/**
 * @TypeDoc(
 *     description = "渠道结算状态"
 * )
 */
enum ChannelSettleStatus {
        /**
         * @FieldDoc(
         *     description = "等待打款"
         * )
         */
        WAIT_TO_SETTLE = 1,
        /**
         * @FieldDoc(
         *     description = "结算成功"
         * )
         */
        SETTLE_SUCCESS = 2,


        /**
         * @FieldDoc(
         *     description = "部分结清"
         * )
         */
        PART_SETTLE_SUCCESS = 4,

        /**
         * @FieldDoc(
         *     description = "结算失败"
         * )
         */
        SETTLE_FAIL = 10,


        /**
         * @FieldDoc(
         *     description = "结算被驳回"
         * )
         */
        SETTLE_REJECT = 11,


        /**
         * @FieldDoc(
         *     description = "打款失败"
         * )
         */
        SETTLE_PAY_FAIL = 12,

        /**
         * @FieldDoc(
         *     description = "冻结中"
         * )
         */
        SETTLE_FREEZE = 13,


        /**
                 * @FieldDoc(
                 *     description = "冻结中"
                 * )
                 */
        UNKNOWN = -1,


}

/**
 * @TypeDoc(
 *     description = "结算单打款方式"
 * )
 */
enum ChannelSettlePayMethod{
        /**
         * @FieldDoc(
         *     description = "银行卡"
         * )
         */
        BANK = 2;
        /**
         * @FieldDoc(
         *     description = "支付宝"
         * )
         */
        ALIPAY= 1
        /**
         * @FieldDoc(
         *     description = "余额钱包"
         * )
         */
        BALANCE = 3;
}
