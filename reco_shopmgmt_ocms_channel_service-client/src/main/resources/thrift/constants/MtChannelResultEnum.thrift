namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants

/**
 * @TypeDoc(
 *     description = "美团渠道新版本结果枚举"
 * )
 */
enum MtChannelResultEnum {

    /**
     * @FieldDoc(
     *     description = "全部查询成功"
     * )
     */
    ALL_SUCCESS = 1,
    /**
     * @FieldDoc(
     *     description = "部分成功"
     * )
     */
    PART_SUCCESS = 2;
    /**
     * @FieldDoc(
     *     description = "全部失败"
     * )
     */
    ALL_FAILED = 3;

    /**
     * @FieldDoc(
     *     description = "签名或限流失败"
     * )
     */
    SYSTEM_ERROR = 4;

}

/**
 * @TypeDoc(
 *     description = "美团图片URL上传失败枚举"
 * )
 */
enum MtPictureUrlUploadErrorEnum {
        /**
         * @FieldDoc(
         *     description = "图片URL不符合p*.meituan.net(com)规范"
         * )
         */
        IMAGE_URL_ERROR = 6003,
        /**
         * @FieldDoc(
         *     description = "图片URL不存在"
         * )
         */
        IMAGE_URL_NOT_EXIST = 6004;
        /**
         * @FieldDoc(
         *     description = "图片URL重复"
         * )
         */
        DUPLICATED_IMAGE_URL = 6005;
}