namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants

/**
 * @TypeDoc(
 *     description = "商品模块渠道统一错误枚举"
 * )
 */

enum ProductChannelUnifyErrorEnum {
    /**
     * @FieldDoc(
     *     description = "门店商品不存在"
     * )
     */
    STORE_SPU_NOT_EXIST = 1,

    /**
     * @FieldDoc(
     *     description = "商品错挂渠道类目"
     * )
     */
    PRODUCT_WITH_WRONG_CHANNEL_CATEGORY = 2,

    /**
     * @FieldDoc(
     *     description = "门店商品已存在"
     * )
     */
    STORE_SPU_EXIST = 3;

    /**
     * @FieldDoc(
     *     description = "字段为空"
     * )
     */
    EMPTY_FIELD = 4;
    
    /**
     * @FieldDoc(
     *     description = "不允许修改商品原价"
     * )
     */
    NOT_ALLOW_MODIFY_ORIGIN_PRICE = 5;

    /**
     * @FieldDoc(
     *     description = "商品是标品"
     * )
     */
    STANDARD_PRODUCT = 6;

    /**
     * @FieldDoc(
     *     description = "店内分类不存在"
     * )
     */
    STORE_CATEGORY_NOT_EXIST = 7;

    /**
     * @FieldDoc(
     *     description = "渠道类目不存在"
     * )
     */
    CHANNEL_CATEGORY_NOT_EXIST = 8;

    /**
     * @FieldDoc(
     *     description = "店内分类已存在"
     * )
     */
    STORE_CATEGORY_EXIST = 9;

    /**
     * @FieldDoc(
     *     description = "店内分类一级分类下有商品"
     * )
     */
    FIRST_LEVEL_CATEGORY_WITH_PRODUCT = 10;

    /**
     * @FieldDoc(
     *     description = "商品条码信息不正确"
     * )
     */
    UPC_INCORRECT = 11;

    /**
     * @FieldDoc(
     *     description = "渠道类目不允许创建商品"
     * )
     */
    CHANNEL_CATEGORY_NOT_ALLOW_CREATE_PRODUCT = 12;

    /**
     * @FieldDoc(
     *     description = "商品不支持称重"
     * )
     */
    NOT_SUPPORT_WEIGHING = 13;

    /**
     * @FieldDoc(
     *     description = "等待确认结果"
     * )
     */
    WAITING_CONFIRM_RESULT = 14;

    /**
     * @FieldDoc(
     *     description = "不允许退差价"
     * )
     */
    NOT_ALLOW_REFUND_PRICE_DIFFERENCE = 15;

    /**
     * @FieldDoc(
     *     description = "不允许修改商品详情"
     * )
     */
    NOT_ALLOW_MODIFY_PRODUCT_DETAIL = 16;

    /**
     * @FieldDoc(
     *     description = "限流"
     * )
     */
    RATE_LIMIT = 17;

    /**
     * @FieldDoc(
     *     description = "系统异常"
     * )
     */
    SYSTEM_ERROR = 18;

    /**
     * @FieldDoc(
     *     description = "缺失经营许可资质"
     * )
     */
    LACK_BUSINESS_LICENSE = 19;

    /**
     * @FieldDoc(
     *     description = "超过价格红线"
     * )
     */
    EXCEED_PRICE_RED_LINE = 20;

    /**
     * @FieldDoc(
     *     description = "图片不存在"
     * )
     */
    PICTURE_NOT_EXIT = 21;

    /**
     * @FieldDoc(
     *     description = "token失效"
     * )
     */
    TOKEN_INVALID = 22;

    /**
     * @FieldDoc(
     *     description = "其他"
     * )
     */
    OTHER = -1;

    /**
     * @FieldDoc(
     *     description = "通用可重试错误"
     * )
     */
    CAN_RETRY = 23;

   /**
     * @FieldDoc(
     *     description = "规格不存在"
     * )
     */
    SKU_NOT_EXIST = 24;

    /**
     * @FieldDoc(
     *     description = "商品参与了平台促销活动"
     * )
     */
     PARTICIPATE_PLATFORM_PROMOTION = 25;

     /**
     * @FieldDoc(
     *     description = "商品spu名称在该店内分类中已存在"
     * )
     */
     SPU_NAME_EXIST_IN_STORE_CATEGORY = 26;

     /**
     * @FieldDoc(
     *     description = "条码已存在，占用商品id"
     * )
     */
     SPU_UPC_EXIST = 27;

     /**
     * @FieldDoc(
     *     description = "包装费已下线"
     * )
     */
     PACKAGING_FEE_OFFLINE = 28;

    /**
     * @FieldDoc(
     *     description = "门店商品超时"
     * )
     */
     STORE_SPU_TIME_OUT = 3002;

     /**
     * @FieldDoc(
     *     description = "美团GIF图补偿"
     * )
     */
     MT_GIF_PICTURE_COMPENSATE = 3004;

}