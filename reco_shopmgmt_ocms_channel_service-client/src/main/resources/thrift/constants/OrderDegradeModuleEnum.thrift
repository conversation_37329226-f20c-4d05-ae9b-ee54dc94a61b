namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants

/**
 * @TypeDoc(
 *     description = "订单降级枚举"
 * )
 * 美团请参考：
 *
 * 0：订单商品详情
 * 1：订单优惠信息
 * 2：商品优惠详情
 * 3：订单用户会员信息
 * 4：订单维度的商家对账信息
 * 5：订单维度的商家对账信息(元)
 * 6：订单收货人地址
 * 7：订单配送方式
 * 8：开放平台用户id
 * 9：部分退款商品信息
 * 10：退货退款物流信息
 * 11：部分订单基本信息(包括订单优惠信息、订单商品详情、门店信息等)
 * 12：sku信息
 * 13：spu信息
 * 14：商品信息(可能是sku或spu等商品相关信息获取时降级)
 * 15：替换折扣价为原价
 */
enum OrderDegradeModuleEnum {
    /**
     * @FieldDoc(
     *     description = "订单商品"
     * )
     */
    ORDER_SKU = 0,
    /**
     * @FieldDoc(
     *     description = "订单优惠信息"
     * )
     */
    ORDER_PROMOTION = 1;

    /**
     * @FieldDoc(
     *     description = "商品优惠详情"
     * )
     */
    SKU_PROMOTION = 2;

    /**
     * @FieldDoc(
     *     description = "会员信息"
     * )
     */
    MENBERSHIP = 3;


    /**
     * @FieldDoc(
     *     description = "订单收货人地址"
     * )
     */
    ADDRESS = 6;


    /**
     * @FieldDoc(
     *     description = "订单配送方式"
     * )
     */
    DELIVERY_METHOD = 7;


    /**
     * @FieldDoc(
     *     description = "部分退款商品信息"
     * )
     */
    PART_REFUND_INFO = 9;

    /**
     * @FieldDoc(
     *     description = "部分订单基本信息"
     * )
     */
    BASE_ORDER_INFO = 11;


    /**
     * @FieldDoc(
     *     description = "部分订单基本信息"
     * )
     */
    SKU = 12;

    /**
     * @FieldDoc(
     *     description = "部分订单基本信息"
     * )
     */
    SPU = 13;

    /**
     * @FieldDoc(
     *     description = "仓信息(百川内部逻辑降级)"
     * )
     */
    WARE_HOUSE=10000;

    /**
         * @FieldDoc(
         *     description = "订单维度的商家对账信息"
         * )
         */
    BIZ_RECEIVE_AMOUNT = 4;
    /**
         * @FieldDoc(
         *     description = "订单维度的商家对账信息(元)"
         * )
         */
    BIZ_RECEIVE_AMOUNT_YUAN = 5;

    /***
    * 开放平台用户id
    * ***/
    USER_ID = 8;

    /***
    * 退货退款物流信息
    * **/
    REFUND_DELIVERY_INFO = 10;
    /**
    * 商品信息(可能是sku或spu等商品相关信息获取时降级)
    * **/
    PRODUCT_INFO = 14;

    /***
    * 替换折扣价为原价
    * **/
    PRODUCT_DISCOUNT_AMOUNT = 15;

    /***
    * 淘鲜达拣货完成上报
    **/
    TXD_ORDER_PICK_COMPLETE_REPORT = 2201;

    /**
    * 美团敏感信息
    **/
    MT_USER_SENSITIVE_DATA = 16;

    /**
     * 抖音敏感信息
     **/
    DY_USER_SENSITIVE_DATA = 2301;


}