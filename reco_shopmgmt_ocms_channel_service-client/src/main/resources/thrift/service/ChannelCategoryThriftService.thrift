namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultData.thrift"
include "Category.thrift"
include "CategoryAttrRequest.thrift"
include "CategoryAttrResponse.thrift"
include "CategoryAttrValueRequest.thrift"
include "CategoryAttrValueResponse.thrift"
include "CategoryProductRulesResponse.thrift"
include "GetCategoryResponse.thrift"
include "CatRequest.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关商品前台分类服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台前台分类业务，前台分类创建修改删除、分类顺序调整业务。",
 *     description = "为赋能中台提供渠道商品前台分类对接服务，主要包含中台分类数据维护信息同步到相关渠道平台。"
 * )
 */
service ChannelCategoryThriftService {
    
    /**
     * @MethodDoc(
     *     description = "该接口用于接收中台商品模块创建分类消息，调用渠道接口将新建分类信息同步到各渠道平台",
     *     displayName = "创建商品前台分类接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    Category.CreateCategoryResponse createCategory(1: Category.CategoryRequest request);

    /**
     * @MethodDoc(
     *     description = "该接口用于接收中台商品模块修改分类消息，调用渠道接口将修改分类信息同步到各渠道平台",
     *     displayName = "修改商品前台分类接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    Category.UpdateCategoryResponse updateCategory(1: Category.CategoryUpdateRequest request);

    /**
     * @MethodDoc(
     *     description = "该接口用于接收中台商品模块删除分类消息，调用渠道接口将删除分类信息同步到各渠道平台",
     *     displayName = "删除商品前台分类接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    Category.UpdateCategoryResponse deleteCategory(1: Category.CategoryDeleteRequest request);

    /**
     * @MethodDoc(
     *     description = "该接口用于接收中台商品模块分类排序消息，调用渠道接口将分类排序信息同步到各渠道平台",
     *     displayName = "商品前台分类排序接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    Category.UpdateCategoryResponse sortCategory(1: Category.CategorySortRequest request);

    /**
     * @MethodDoc(
     *     description = "该接口用于接收中台商品模块分类降序消息，调用渠道接口将分类排序信息同步到各渠道平台",
     *     displayName = "商品前台分类降序接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    Category.UpdateCategoryResponse degradeCategory(1: Category.CategoryDegradeRequest request);


    /**
     * @MethodDoc(
     *     description = "该接口用于接收中台商品模块分类降序消息，调用渠道接口将分类排序信息同步到各渠道平台",
     *     displayName = "商品前台分类降序接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    Category.UpdateCategoryResponse adjustCategoryLevel(1: Category.CategoryLevelAdjustRequest request);

    /**
     * @MethodDoc(
     *     description = "该接口用于用于更新渠道前台分类的渠道编码",
     *     displayName = "修改商品前台分类渠道编码接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    Category.UpdateCategoryResponse updateCategoryChannelCode(1: Category.CategoryUpdateRequest request);

    /**
     * @MethodDoc(
     *     description = "该接口用于更新渠道前台分类的智能分类开关",
     *     displayName = "更新渠道前台分类的智能分类开关",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    Category.UpdateCategoryResponse updateSmartSortSwitch(1: Category.CategorySmartSortSwitchRequest request);

    /**
     * @MethodDoc(
     *     description = "该接口用于查询渠道前台分类的智能分类开关",
     *     displayName = "查询渠道前台分类的智能分类开关",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    Category.CategorySmartSortQueryResponse querySmartSortSwitch(1: Category.CategorySmartSortQueryRequest request)

    /**
     * @MethodDoc(
     *     description = "该接口用于推荐美团类目",
     *     displayName = "推荐美团类目",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData, 没有推荐则返回为空",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    Category.RecommendCategoryResponse recommendCategory(1: Category.RecommendCategoryRequest request)

    /**
     * @MethodDoc(
     *     description = "该接口用于获取线上渠道类目相应的商品发布规则（包含7天无理由等）",
     *     displayName = "获取类目商品发布规则",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    CategoryProductRulesResponse.CategoryProductRulesResponse getCategoryProductRules(1: CategoryAttrRequest.CategoryAttrRequest request);

    /**
     * @MethodDoc(
     *     description = "该接口用于获取线上渠道类目属性信息",
     *     displayName = "获取渠道类目属性信息",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    CategoryAttrResponse.CategoryAttrResponse getCategoryAttr(1: CategoryAttrRequest.CategoryAttrRequest request);

    /**
     * @MethodDoc(
     *     description = "该接口用于获取线上渠道类目属性信息(虚拟渠道账号)",
     *     displayName = "通过虚拟账户获取渠道类目属性信息",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    CategoryAttrResponse.CategoryAttrResponse getCategoryAttrByVirtualConfig(1: CategoryAttrRequest.CategoryAttrRequest request);

    /**
     * @MethodDoc(
     *     description = "接口用于获取线上渠道类目特殊属性的属性值信息",
     *     displayName = "获取渠道类目特殊属性的属性值信息",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    CategoryAttrValueResponse.CategoryAttrValueResponse getCategoryAttrValue(1: CategoryAttrValueRequest.CategoryAttrValueRequest request);

    /**
 * @MethodDoc(
 *     description = "该接口用于置顶美团类目",
 *     displayName = "置顶美团类目",
 *     parameters = {
 *         @ParamDoc(
 *             name = "request",
 *             description = "请求参数",
 *             example = "request"
 *         )
 *     },
 *     returnValueDescription = "返回数据",
 *     extensions = {
 *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
 *     }
 * )
 */
    Category.UpdateCategoryResponse topCategory(1: Category.CategoryTopRequest request)


    /**
     * @MethodDoc(
     *     description = "接口用于获取渠道前台分类的列表信息",
     *     displayName = "获取渠道前台分类的列表信息",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    GetCategoryResponse.GetCategoryResponse queryStoreCategoryList(1: CatRequest.CatRequest catRequest)

    /**
     * @MethodDoc(
     *     description = "根据一级店内分类id 查询二级店内分类列表",
     *     displayName = "根据一级店内分类id 查询二级店内分类列表",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    GetCategoryResponse.GetCategoryResponse getSecondCategoryByParentId(1: CatRequest.CatRequest catRequest)
}