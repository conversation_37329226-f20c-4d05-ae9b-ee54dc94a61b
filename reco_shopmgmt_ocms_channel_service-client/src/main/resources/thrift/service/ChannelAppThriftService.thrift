namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "AppInfo.thrift"

service ChannelAppThriftService {
        /**
        * @MethodDoc(
        *     displayName = "根据租户id查询应用信息",
        *     description = "根据租户id查询应用信息",
        *     parameters = {
        *         @ParamDoc(
        *                    name = "request",
        *                    description = "根据租户id查询应用信息的请求",
        *         )
        *     },
        *     extensions = {
        *         @ExtensionDoc(
        *                    name = "SECURITY_PRIVILEGE",
        *                    content = "鉴权逻辑为：tenantId=data.tenantId")
        *     },
        *     example = "请求示例：AppInfoQueryRequest"
        *             + "\n" +
        *             "返回示例：AppInfoQueryResponse"
        * )
        */
        AppInfo.AppInfoQueryResponse queryAppInfoByTenant(1: AppInfo.AppInfoQueryRequest request);
}