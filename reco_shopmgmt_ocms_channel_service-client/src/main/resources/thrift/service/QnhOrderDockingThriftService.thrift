namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultStatus.thrift"
include "PoiConfirmOrderRequest.thrift"
include "GetChannelOrderDetailRequest.thrift"
include "GetChannelOrderDetailResult.thrift"
include "PreparationMealCompleteRequest.thrift"
include "AgreeRefundRequest.thrift"
include "RejectRefundRequest.thrift"
include "GetOrderStatusRequest.thrift"
include "GetOrderStatusResult.thrift"
include "BatchPullPhoneRequest.thrift"
include "BatchPullPhoneResult.thrift"
include "PoiCancelOrderRequest.thrift"
include "PoiPartRefundRequest.thrift"
include "ConfirmReceiveGoodsRequest.thrift"
include "PoiAdjustOrderRequest.thrift"
include "PoiAdjustOrderResult.thrift"
include "OrderShouldSettlementInfoRequest.thrift"
include "StartPickRequest.thrift"
include "GetOrderAfsApplyListRequest.thrift"
include "GetOrderAfsApplyListResult.thrift"

include "OrderShouldSettlementResult.thrift"
include "GoodsSettlementResult.thrift"
include "GoodsSettlementInfoRequest.thrift"
include "GetLogisticsStatusRequest.thrift"
include "GetLogisticsStatusResult.thrift"

include "UpdateOrderDeliveryStatusRequest.thrift"
include "UpdateDeliveryInfoRequest.thrift"

include "SelfDeliveryRequest.thrift"
include "ChannelPartRefundGoodsRequest.thrift"
include "ChannelPartRefundGoodsResult.thrift"
include "QueryChannelOrderListResult.thrift"
include "QueryChannelOrderListRequest.thrift"
include "QueryChannelAbnormalOrderRequest.thrift"
include "QueryChannelAbnormalOrderResult.thrift"
include "CalculateRefundResult.thrift"
include "CalculateRefundRequest.thrift"
include "WeightPartRefundGoodsRequest.thrift"
include "WeightPartRefundGoodsResult.thrift"
include "QueryReturnAbnormalOrderRequest.thrift"
include "QueryReturnAbnormalOrderResult.thrift"


/**
 * @InterfaceDoc(
 *     displayName = "牵牛花订单业务服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台订单业务，订单回调消息处理、订单履约管理等订单业务。",
 *     description = "为赋能中台提供牵牛花订单对接服务，主要包含渠道订单回调消息处理，商家履约操作服务。"
 * )
 */
service QnhOrderDockingThriftService {

        /**
         * @MethodDoc(
         *     description = "该接口用于中台管理端商家手动确认订单，调用各渠道确认订单接口，完成订单确认操作",
         *     displayName = "商家确认订单接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus poiConfirmOrder(1: PoiConfirmOrderRequest.PoiConfirmOrderRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于中台订单模块，查询渠道订单详情，处理订单业务",
         *     displayName = "获取渠道订单详情接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "订单详细信息",
         *     example = "GetChannelOrderDetailResult result = channelOrderThriftService.getChannelOrderDetail(request)",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        GetChannelOrderDetailResult.GetChannelOrderDetailResult getChannelOrderDetail(1: GetChannelOrderDetailRequest.GetChannelOrderDetailRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于中台订单模块，拣货完成后调用各渠道出餐接口，商家确认已完成出餐",
         *     displayName = "推送出餐完成接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus preparationMealComplete(1: PreparationMealCompleteRequest.PreparationMealCompleteRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于中台订单模块，商家同意订单退款请求操作",
         *     displayName = "商家确认订单退款接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "status",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus agreeRefund(1: AgreeRefundRequest.AgreeRefundRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于中台订单模块，商家驳回订单退款请求操作",
         *     displayName = "驳回订单退款申请接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus rejectRefund(1: RejectRefundRequest.RejectRefundRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于中台订单模块，查询指定订单在各渠道的最新状态",
         *     displayName = "订单最新状态查询接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        GetOrderStatusResult.GetOrderStatusResult getOrderStatus(1: GetOrderStatusRequest.GetOrderStatusRequest request);

        /**
        * @MethodDoc(
        *     description = "该接口用于中台订单模块，查询指定订单在各渠道的售后列表",
        *     displayName = "订单售后列表查询接口",
        *     parameters = {
        *         @ParamDoc(
        *             name = "request",
        *             description = "请求参数",
        *             example = "request"
        *         )
        *     },
        *     returnValueDescription = "查询结果",
        *     example = "afsApplyList",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        GetOrderAfsApplyListResult.GetOrderAfsApplyListResult getOrderAfsApplyList(1: GetOrderAfsApplyListRequest.GetOrderAfsApplyListRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于中台订单模块，批量拉取订单用户真实手机号",
         *     displayName = "批量拉取用户真实手机号接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        BatchPullPhoneResult.BatchPullPhoneResult batchPullPhone(1: BatchPullPhoneRequest.BatchPullPhoneRequest request);

        /**
         * @MethodDoc(
         *     displayName = "商家取消订单接口",
         *     description = "该接口用于中台管理端商家取消订单，由商家发起订单取消",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "请求参数", example = {})
         *     },
         *     returnValueDescription = "返回数据",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus poiCancelOrder(1: PoiCancelOrderRequest.PoiCancelOrderRequest request);

        /**
        * @MethodDoc(
        *     displayName = "商家发起部分退款申请接口",
        *     description = "该接口用于中台管理端商家在缺货时发起部分退款",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        ResultStatus.ResultStatus poiPartRefundApply(1: PoiPartRefundRequest.PoiPartRefundRequest request);
        /**
               * @MethodDoc(
               *     displayName = "商家发起部分退款申请试算接口",
               *     description = "该接口用于中台管理端商家在缺货时发起部分退款试算",
               *     parameters = {
               *         @ParamDoc( name = "request", description = "请求参数", example = {})
               *     },
               *     returnValueDescription = "返回数据",
               *     example = "status",
               *     extensions = {
               *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
               *     }
               * )
               */
        CalculateRefundResult.CalculateRefundResult poiPartRefundApplyCalculate(1: CalculateRefundRequest.CalculateRefundRequest request);



        /**
        * @MethodDoc(
        *     displayName = "商家发起订单调整接口",
        *     description = "商家拣货发现缺货时，调整订单商品数量",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        PoiAdjustOrderResult.PoiAdjustOrderResult poiModifyOrder(1: PoiAdjustOrderRequest.PoiAdjustOrderRequest request);


        /**
        * @MethodDoc(
        *     displayName = "收到退货接口",
        *     description = "用户拒收或者妥投失败，售后等场景，商家收到退货后调用该接口",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        ResultStatus.ResultStatus confirmReceiveGoods(1: ConfirmReceiveGoodsRequest.PoiConfirmReceiveGoodsRequest request);



        /**
        * @MethodDoc(
        *     displayName = "查询订单应结金额",
        *     description = "查询订单应结金额",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        OrderShouldSettlementResult.OrderShouldSettlementResult queryOrderShouldSettlementInfo(1: OrderShouldSettlementInfoRequest.OrderShouldSettlementInfoRequest request);

        /**
        * @MethodDoc(
        *     displayName = "查询商品分拆金额",
        *     description = "查询商品分拆金额",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        GoodsSettlementResult.GoodsSettlementResult queryGoodsSettlementInfo(1: GoodsSettlementInfoRequest.GoodsSettlementInfoRequest request);



        /**
         * @MethodDoc(
         *     description = "查询配送状态，骑手姓名，电话等",
         *     displayName = "查询配送状态，骑手姓名，电话等",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "配送状态",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        GetLogisticsStatusResult.GetLogisticsStatusResult getLogisticsStatus(1: GetLogisticsStatusRequest.GetLogisticsStatusRequest request);

        /**
         * @MethodDoc(
         *     description = "在商家自配送场景下，更新订单的配送状态",
         *     displayName = "在商家自配送场景下，更新订单的配送状态",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "配送状态",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus updateOrderDeliveryStatus(1: UpdateOrderDeliveryStatusRequest.UpdateOrderDeliveryStatusRequest request)

        /**
         * @MethodDoc(
         *     description = "在商家自配送场景下，更新配送信息",
         *     displayName = "在商家自配送场景下，更新配送信息",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "配送状态",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus updateDeliveryInfo(1: UpdateDeliveryInfoRequest.UpdateDeliveryInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "商家转自配送",
         *     displayName = "商家转自配送",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "转自配送结果状态",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus selfDelivery(1: SelfDeliveryRequest.SelfDeliveryRequest request);

        /**
         * @MethodDoc(
         *     description = "查询渠道部分退款商品详情",
         *     displayName = "查询渠道部分退款商品详情",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "查询渠道部分退款商品详情结果",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ChannelPartRefundGoodsResult.ChannelPartRefundGoodsResult queryChannelPartRefundGoodsDetail(1: ChannelPartRefundGoodsRequest.ChannelPartRefundGoodsRequest request)


        /**
         * @MethodDoc(
         *     description = "分页查询渠道订单列表",
         *     displayName = "分页查询渠道订单列表",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "分页查询渠道订单列表",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        QueryChannelOrderListResult.QueryChannelOrderListResult queryChannelOrderList(1: QueryChannelOrderListRequest.QueryChannelOrderListRequest request)

        /**
         * @MethodDoc(
         *     description = "分页查询渠道异常订单列表",
         *     displayName = "分页查询渠道异常订单列表",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "分页查询渠道异常订单列表",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        QueryChannelAbnormalOrderResult.QueryChannelAbnormalOrderResult queryChannelAbnormalOrderList(1: QueryChannelAbnormalOrderRequest.QueryChannelAbnormalOrderRequest request)

        /**
         * @MethodDoc(
         *     description = "查询克重详情",
         *     displayName = "查询克重详情",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "查询克重详情",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        WeightPartRefundGoodsResult.WeightPartRefundGoodsResult queryWeightPartRefundGoodsDetail(1: WeightPartRefundGoodsRequest.WeightPartRefundGoodsRequest request)

        /**
         * @MethodDoc(
         *     description = "查询异常退单列表",
         *     displayName = "查询异常退单列表",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "查询异常退单列表",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        QueryReturnAbnormalOrderResult.QueryReturnAbnormalOrderResult queryAbnormalReturnOrderList(1: QueryReturnAbnormalOrderRequest.QueryReturnAbnormalOrderRequest request)

        /**
         * @MethodDoc(
         *     description = "该接口用于中台订单模块，领取任务，开始拣货周知",
         *     displayName = "推送领取任务，开始拣货接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "status",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus startPickNotify(1: StartPickRequest.StartPickRequest request);

}