namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "Category.thrift"
include "ResultData.thrift"
include "SpuInfo.thrift"
include "BaseRequest.thrift"
include "ResultStatus.thrift"
include "DouyinAccessCodeRequest.thrift"
include "VirtualTokenGetRequest.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关虚拟配置服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台通过虚拟的渠道请求系统参数调用渠道接口的业务（租户不用实际绑定该渠道）。",
 *     description = "为赋能中台提供可以不用实际绑定某渠道，通过虚拟的渠道请求系统参数调用该渠道接口的服务。"
 * )
 */

service ChannelVirtualConfigThriftService {
    /**
     * @MethodDoc(
     *     description = "该接口用于通过虚拟的渠道请求系统参数，获取推荐渠道类目",
     *     displayName = "推荐渠道类目",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             exam `ple = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData, 没有推荐则返回为空",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     */
    Category.RecommendCategoryResponse recommendCategoryByVirtualConfig(1: Category.RecommendCategoryRequest request)

    /**
     * @MethodDoc(
     *     description = "该接口用于通过虚拟的渠道请求系统参数，去测试门店创建渠道商品",
     *     displayName = "创建渠道商品",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             exam `ple = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData, 没有推荐则返回为空",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     */
    ResultData.ResultSpuData createStoreProductsByVirtualConfig(1: SpuInfo.SpuInfoRequest request);

    /**
     * @MethodDoc(
     *     description = "该接口用于通过虚拟的渠道请求系统参数，去测试门店删除渠道商品",
     *     displayName = "删除渠道商品",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             exam `ple = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData, 没有推荐则返回为空",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     */
    ResultData.ResultSpuData deleteStoreProductsByVirtualConfig(1: SpuInfo.SpuInfoDeleteRequest request);

        /**
         * @MethodDoc(
         *     displayName = "虚拟账号获取token"
         *     description = "租户新增渠道通过虚拟账号获取token",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "租户新增渠道通过虚拟账号获取token",
         *         example = "request")
         *     },
         *     returnValueDescription = "租户新增渠道通过虚拟账号获取token否更新成功",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         *
         * @param request
         */
    ResultStatus.ResultStatus fetchVirtualTokenByVirtualConfig(1: VirtualTokenGetRequest.VirtualTokenGetRequest
    request);

    /**
     * @MethodDoc(
     *     description = "接口适用于主档 一键使用（饿了么）渠道推荐信息，通过虚拟的渠道请求系统参数，去测试门店创建渠道商品",
     *     displayName = "创建渠道商品",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             exam `ple = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData, 没有推荐则返回为空",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     */
    ResultData.ResultSpuData createStoreProductsForRecommendCategoryProperty(1: SpuInfo.SpuInfoRequest request);

    /**
     * @MethodDoc(
     *     description = "接口适用于主档 一键使用（饿了么）渠道推荐信息，通过虚拟的渠道请求系统参数，去测试门店删除渠道商品",
     *     displayName = "删除渠道商品",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             exam `ple = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData, 没有推荐则返回为空",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     */
    ResultData.ResultSpuData deleteStoreProductForRecommendCategoryProperty(1: SpuInfo.SpuInfoDeleteRequest request);

    /**
     * @MethodDoc(
     *     description = "接口适用于主档 一键使用（饿了么）渠道推荐信息，通过虚拟的渠道请求系统参数，去测试门店获取渠道商品类目属性",
     *     displayName = "获取渠道商品类目属性",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             exam `ple = "request"
     *         )
     *     },
     *     returnValueDescription = "渠道商品类目属性",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     */
     SpuInfo.GetSpuCategoryPropertyResponse getSpuRecommendCategoryPropertyInfo(1: SpuInfo.GetSpuInfoRequest request);
}
