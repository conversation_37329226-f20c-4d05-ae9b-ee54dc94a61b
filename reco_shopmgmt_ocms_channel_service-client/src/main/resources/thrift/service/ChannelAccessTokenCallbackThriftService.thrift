namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultStatus.thrift"
include "GetElmAccessTokenRequest.thrift"
include "JddjAccessTokenRequest.thrift"
include "YzAccessCodeRequest.thrift"
include "DouyinAccessCodeRequest.thrift"
include "YzTokenSetRequest.thrift"
include "TokenGetRequest.thrift"
include "TokenGetResponse.thrift"
include "ElmIsvAuth.thrift"
include "TxdTokenRequest.thrift"
include "TxdTokenResponse.thrift"
include "MtIsvAuth.thrift"
include "DeleteDouyinTokenRequest.thrift"
include "YouzanIsvAuth.thrift"
include "MarketOrder.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "饿了么渠道网关授权令牌回调服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景：渠道方回调该服务，获取饿了么开放平台返回的令牌并保存。",
 *     description = "为商家提供饿了么渠道对接服务，饿了么平台升级后，获取平台信息需加入权限令牌。"
 * )
 */
service ChannelAccessTokenCallbackThriftService {

        /**
         * @MethodDoc(
         *     displayName = "根据tenantAppId和渠道及临时令牌获取饿了么开放平台权限令牌参数",
         *     description = "根据tenantAppId和渠道及临时令牌获取饿了么开放平台权限令牌参数",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "权限令牌获取参数",
         *         example = "request")
         *     },
         *     returnValueDescription = "令牌获取是否成功",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus getAccessToken(1: GetElmAccessTokenRequest.GetElmAccessTokenRequest request);

       /**
         * @MethodDoc(
         *     displayName = "jddj token回调更新"
         *     description = "jddj token回调更新",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "jddj token回调",
         *         example = "request")
         *     },
         *     returnValueDescription = "token是否更新成功",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus jddjToken(1: JddjAccessTokenRequest.JddjAccessTokenRequest request);


        /**
         * @MethodDoc(
         *     displayName = "有赞 token回调更新"
         *     description = "有赞 token回调更新",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "有赞 token回调",
         *         example = "request")
         *     },
         *     returnValueDescription = "token是否更新成功",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus yzToken(1: YzAccessCodeRequest.YzAccessCodeRequest request);

        /**
      * @MethodDoc(
      *     displayName = "抖音 token回调更新"
      *     description = "抖音 token回调更新",
      *     parameters = {
      *         @ParamDoc(
      *         name = "request",
      *         description = "抖音 token回调",
      *         example = "request")
      *     },
      *     returnValueDescription = "token是否更新成功",
      *     extensions = {
      *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
      *     }
      * )
      */
        ResultStatus.ResultStatus douyinToken(1: DouyinAccessCodeRequest.DouyinAccessCodeRequest request);

  /**
      * @MethodDoc(
      *     displayName = "删除抖音Token"
      *     description = "删除抖音Token",
      *     parameters = {
      *         @ParamDoc(
      *         name = "request",
      *         description = "删除抖音Token请求",
      *         example = "request")
      *     },
      *     returnValueDescription = "token是否删除成功",
      *     extensions = {
      *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
      *     }
      * )
      */
     ResultStatus.ResultStatus deleteDouyinToken(1: DeleteDouyinTokenRequest.DeleteDouyinTokenRequest request);

     /**
      * @MethodDoc(
      *     displayName = "淘鲜达 token回调更新"
      *     description = "淘鲜达 token回调更新",
      *     parameters = {
      *         @ParamDoc(
      *         name = "request",
      *         description = "淘鲜达 token回调",
      *         example = "request")
      *     },
      *     returnValueDescription = "token是否更新成功",
      *     extensions = {
      *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
      *     }
      * )
      */
        TxdTokenResponse.TxdTokenResponse txdToken(1: TxdTokenRequest.TxdTokenRequest request);

        /**
         * @MethodDoc(
         *     displayName = "有赞 token 直接更新（迁移用）"
         *     description = "有赞 token 直接更新（迁移用）",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "有赞 token回调",
         *         example = "request")
         *     },
         *     returnValueDescription = "token是否更新成功",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus yzTokenSet(1: YzTokenSetRequest.YzTokenSetRequest request);

        /**
         * @MethodDoc(
         *     displayName = "获取token（迁移用）"
         *     description = "获取token（迁移用）",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "获取token（迁移用）",
         *         example = "request")
         *     },
         *     returnValueDescription = "token是否获取成功",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        TokenGetResponse.TokenGetResponse getToken(1: TokenGetRequest.TokenGetRequest request);

        /**
         * @MethodDoc(
         *     displayName = "手动刷新token（迁移校验，请勿使用）"
         *     description = "手动刷新token（迁移校验，请勿使用）",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "手动刷新token（迁移校验，请勿使用）",
         *         example = "request")
         *     },
         *     returnValueDescription = "手动刷新token结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus refreshToken(1: TokenGetRequest.TokenGetRequest request);

        /**
         * @MethodDoc(
         *     displayName = "获取饿了么ISV应用的token"
         *     description = "获取饿了么ISV应用的token",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "获取饿了么ISV应用的token",
         *         example = "request")
         *     },
         *     returnValueDescription = "饿了么ISV应用的token",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ElmIsvAuth.ElmIsvAuthResponse getElmIsvToken(1: ElmIsvAuth.ElmIsvAuthRequest request);

        /**
         * @MethodDoc(
         *     displayName = "刷新饿了么ISV应用的token"
         *     description = "刷新饿了么ISV应用的token",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "刷新饿了么ISV应用的token",
         *         example = "request")
         *     },
         *     returnValueDescription = "饿了么ISV应用的token",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ElmIsvAuth.ElmIsvRefreshTokenResponse refreshElmIsvToken(1: ElmIsvAuth.ElmIsvRefreshTokenRequest request);


        /**
         * @MethodDoc(
         *     displayName = "获取美团ISV应用的token"
         *     description = "获取美团ISV应用的token",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "获取美团ISV应用的token",
         *         example = "request")
         *     },
         *     returnValueDescription = "美团ISV应用的token",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        MtIsvAuth.MtIsvAuthResponse getMtIsvToken(1: MtIsvAuth.MtIsvAuthRequest request);

        /**
         * @MethodDoc(
         *     displayName = "刷新美团ISV应用的token"
         *     description = "刷新美团ISV应用的token",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "刷新美团ISV应用的token",
         *         example = "request")
         *     },
         *     returnValueDescription = "美团ISV应用的token",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        MtIsvAuth.MtIsvRefreshTokenResponse refreshMtIsvToken(1: MtIsvAuth.MtIsvRefreshTokenRequest request);

        /**
                 * @MethodDoc(
                 *     displayName = "获取有赞ISV应用的token"
                 *     description = "获取有赞ISV应用的token",
                 *     parameters = {
                 *         @ParamDoc(
                 *         name = "request",
                 *         description = "获取有赞ISV应用的token",
                 *         example = "request")
                 *     },
                 *     returnValueDescription = "有赞ISV应用的token",
                 *     extensions = {
                 *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
                 *     }
                 * )
                 */
                YouzanIsvAuth.YouzanIsvAuthResponse getYouzanIsvToken(1: YouzanIsvAuth.YouzanIsvAuthRequest request);

                /**
                 * @MethodDoc(
                 *     displayName = "刷新有赞ISV应用的token"
                 *     description = "刷新有赞ISV应用的token",
                 *     parameters = {
                 *         @ParamDoc(
                 *         name = "request",
                 *         description = "刷新有赞ISV应用的token",
                 *         example = "request")
                 *     },
                 *     returnValueDescription = "有赞ISV应用的token",
                 *     extensions = {
                 *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
                 *     }
                 * )
                 */
                YouzanIsvAuth.YouzanIsvRefreshTokenResponse refreshYouzanIsvToken(1: YouzanIsvAuth.YouzanIsvRefreshTokenRequest request);
        /**
         * @MethodDoc(
         *     displayName = "获取一段时间内的服务市场订单",
         *     description = "获取一段时间内的服务市场订单",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "获取服务市场订单参数",
         *         example = "request")
         *     },
         *     returnValueDescription = "服务市场订单列表",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        MarketOrder.MarketOrderListResponse getMarketOrderList(1: MarketOrder.MarketOrderListRequest request);

}