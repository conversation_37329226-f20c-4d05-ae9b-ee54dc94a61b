namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultData.thrift"
include "Price.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关价格服务",
 *     type = "octo.thrift",
 *     scenarios = "适用于赋能中台业务，中台商品价格修改同步更新各渠道价格，用于商品线上定价。",
 *     description = "为赋能中台提供渠道商品价格对接服务，主要包含中台商品价格修改信息同步到相关渠道平台。"
 * )
 */
service ChannelPriceThriftService {

    /**
     * @MethodDoc(
     *     description = "该接口用于接收中台价格模块修改价格消息，调用渠道接口将修改价格信息同步到各渠道平台",
     *     displayName = "多渠道更新商品价格接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    ResultData.ResultData updatePriceMultiChannel(1: Price.SkuPriceMultiChannelRequest request);

    /**
    * @MethodDoc(
    *     description = "该接口用于接收中台价格模块修改价格消息，调用渠道接口将修改价格信息同步到各渠道平台",
     *     displayName = "更新单渠道商品价格接口",
    *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
    *     returnValueDescription = "处理结果",
    *     example = "resultData",
    *     extensions = {
    *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
    *     }
    * )
    */
    ResultData.ResultData updatePrice(1: Price.SkuPriceRequest request);

    /**
    * @MethodDoc(
    *     description = "该接口用于接收中台价格模块修改价格消息，调用渠道接口将修改价格信息同步到各渠道平台",
    *     displayName = "更新单渠道单个商品价格接口",
    *     parameters = {
    *         @ParamDoc(
    *             name = "request",
    *             description = "请求参数",
    *             example = "request"
    *         )
    *     },
    *     returnValueDescription = "处理结果",
    *     example = "resultData",
    *     extensions = {
    *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
    *     }
    * )
    */
    ResultData.ResultSingleData updateSinglePrice(1: Price.SingleSkuPriceRequest request);


    /**
    * @MethodDoc(
    *     description = "该接口用于批量查询单个门店的sku价格，调用渠道接口将查询指定渠道的sku价格",
    *     displayName = "批量查询单门店的sku价格",
    *     parameters = {
    *         @ParamDoc(
    *             name = "request",
    *             description = "请求参数",
    *             example = "request"
    *         )
    *     },
    *     returnValueDescription = "处理结果",
    *     example = "resultData",
    *     extensions = {
    *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
    *     }
    * )
    */
    Price.BatchGetSkuPriceResponse batchGetSkuPrice(1: Price.BatchGetSkuPriceRequest request);
}