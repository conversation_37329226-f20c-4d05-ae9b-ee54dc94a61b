namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "BaseRequest.thrift"
include "GetPoiInfoResponse.thrift"
include "CatRequest.thrift"
include "GetCategoryResponse.thrift"
include "GetBrandRequest.thrift"
include "GetBrandResponse.thrift"
include "SkuInfo.thrift"
include "HeadQuarterSpuInfo.thrift"
include "ResultData.thrift"
include "GetQualificationResponse.thrift"
include "GetQualificationRequest.thrift"
include "ResultStatus.thrift"
include "ChannelPoiRequest.thrift"
include "ChannelConfig.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关基本信息服务",
 *     type = "octo.thrift",
 *     scenarios = "适用于赋能中台业务，主要用于拉取中台业务需要的线上渠道基础信息，包括门店信息、分类信息、品牌信息等",
 *     description = "该服务主要用于线上渠道信息拉取，提供渠道门店信息、分类信息、品牌信息等"
 * )
 */
service ChannelBaseMsgThriftService {
    
    /**
     * @MethodDoc(
     *     description = "支持多渠道查询租户下全部门店信息",
     *     displayName = "批量查询门店信息服务",
     *     parameters = {
     *         @ParamDoc(
     *             name = "req",
     *             description = "请求参数",
     *             example = "req"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    GetPoiInfoResponse.GetPoiInfoResponse batchGetPoiInfo(1: BaseRequest.BaseRequestSimple req);

    /**
     * @MethodDoc(
     *     description = "支持多渠道查询租户下全部门店信息",
     *     displayName = "批量查询门店信息服务",
     *     parameters = {
     *         @ParamDoc(
     *             name = "req",
     *             description = "请求参数",
     *             example = "req"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    GetPoiInfoResponse.GetPoiIdsResponse batchGetPoiIds(1: BaseRequest.BaseRequestSimple req);

        /**
         * @MethodDoc(
         *     description = "支持多渠道查询租户下全部门店信息",
         *     displayName = "批量查询门店信息服务",
         *     parameters = {
         *         @ParamDoc(
         *             name = "req",
         *             description = "请求参数",
         *             example = "req"
         *         )
         *     },
         *     returnValueDescription = "返回数据",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        GetPoiInfoResponse.GetPoiIdsResponse batchGetPoiIdsBySysParam(1: BaseRequest.BatchGetPoiIdsRequest
        req);

    /**
     * @MethodDoc(
     *     description = "根据门店ID列表查询门店详情",
     *     displayName = "批量查询门店详情信息服务",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "req"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    GetPoiInfoResponse.GetPoiInfoResponse batchGetPoiDetails(1: BaseRequest.BatchGetPoiDetailsRequest request);

    GetPoiInfoResponse.GetPoiIdsResponse getPoiInnerId(1: BaseRequest.BatchGetPoiDetailsRequest request);
    
    /**
     * @MethodDoc(
     *     description = "改接口用于批量获取线上渠道类目信息",
     *     displayName = "批量获取类目信息",
     *     parameters = {
     *         @ParamDoc(
     *             name = "req",
     *             description = "请求参数",
     *             example = "req"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    GetCategoryResponse.GetCategoryResponse batchGetCategory(1: CatRequest.CatRequest req);

    /**
     * @MethodDoc(
     *     description = "批量获取线上渠道类目信息（虚拟账号渠道）",
     *     displayName = "通过虚拟账号批量获取类目信息",
     *     parameters = {
     *         @ParamDoc(
     *             name = "req",
     *             description = "请求参数",
     *             example = "req"
     *         )
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    GetCategoryResponse.GetCategoryResponse batchGetCategoryByVirtualConfig(1: CatRequest.CatRequest req);

    /**
     * @MethodDoc(
     *     displayName = "批量获取品牌信息",
     *     description = "该接口用于批量拉取线上渠道品牌信息",
     *     parameters = {
     *         @ParamDoc( name = "req", description = "请求参数", example = "req")
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    GetBrandResponse.GetBrandResponse batchGetBrand(1: GetBrandRequest.GetBrandRequest req);

    /**
     * @MethodDoc(
     *     displayName = "批量获取品牌信息",
     *     description = "该接口用于批量拉取线上渠道品牌信息",
     *     parameters = {
     *         @ParamDoc( name = "req", description = "请求参数", example = "req")
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    SkuInfo.BatchGetSkuInfoResponse batchGetSkuInfo(1: SkuInfo.BatchGetSkuInfoRequest req);

    /**
       * @MethodDoc(
       *     displayName = "批量获取前台分类信息",
       *     description = "该接口用于批量拉取线上前台分类信息",
       *     parameters = {
       *         @ParamDoc( name = "req", description = "请求参数", example = "req")
       *     },
       *     returnValueDescription = "返回数据",
       *     extensions = {
       *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
       *     }
       * )
       */
    GetCategoryResponse.GetCategoryResponse batchGetChannelStoreCatInfo(1: CatRequest.CatRequest req);

    /**
       * @MethodDoc(
       *     displayName = "批量获取总部商品信息",
       *     description = "该接口用于批量获取总部商品信息",
       *     parameters = {
       *         @ParamDoc( name = "req", description = "请求参数", example = "req")
       *     },
       *     returnValueDescription = "返回数据",
       *     extensions = {
       *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
       *     }
       * )
       */
    HeadQuarterSpuInfo.GetHeadQuarterSpuInfoResponse batchGetHeadQuarterSpuInfo(1: BaseRequest.BasePageRequest req);

    /**
       * @MethodDoc(
       *     displayName = "获取地区列表",
       *     description = "该接口用于获取地区列表",
       *     parameters = {
       *         @ParamDoc( name = "req", description = "请求参数", example = "req")
       *     },
       *     returnValueDescription = "返回数据",
       *     extensions = {
       *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
       *     }
       * )
       */
    ResultData.GetAreaListResponse getAreaList(1: BaseRequest.GetAreaListRequest req);

    /**
       * @MethodDoc(
       *     displayName = "获取资质",
       *     description = "获取资质",
       *     parameters = {
       *         @ParamDoc( name = "req", description = "请求参数", example = "req")
       *     },
       *     returnValueDescription = "返回数据",
       *     extensions = {
       *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
       *     }
       * )
       */
    GetQualificationResponse.GetQualificationResponse getQualification(1: GetQualificationRequest.GetQualificationRequest req);

    /**
     * @MethodDoc(
     *     displayName = "获取资质",
     *     description = "通过虚拟账号渠道获取资质",
     *     parameters = {
     *         @ParamDoc( name = "req", description = "请求参数", example = "req")
     *     },
     *     returnValueDescription = "返回数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    GetQualificationResponse.GetQualificationResponse getQualificationByVirtualConfig(1: GetQualificationRequest.GetQualificationRequest req);

    /**
       * @MethodDoc(
       *     displayName = "获取门店权限信息",
       *     description = "获取门店权限信息",
       *     parameters = {
       *         @ParamDoc( name = "req", description = "请求参数", example = "req")
       *     },
       *     returnValueDescription = "返回数据",
       *     extensions = {
       *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
       *     }
       * )
       */
    GetPoiInfoResponse.QueryPoiAuthDetailResponse queryPoiAuthDetailInfo(1: BaseRequest.BaseChannelPoiRequestSimple req);

    /**
       * @MethodDoc(
       *     displayName = "更新门店权限信息",
       *     description = "更新门店权限信息",
       *     parameters = {
       *         @ParamDoc( name = "req", description = "请求参数", example = "req")
       *     },
       *     returnValueDescription = "返回数据",
       *     extensions = {
       *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
       *     }
       * )
       */
    ResultStatus.ResultStatus updatePoiAuthInfo(1: ChannelPoiRequest.UpdatePoiAuthInfoRequest req);

    /**
       * @MethodDoc(
       *     displayName = "更新门店权限信息",
       *     description = "更新门店权限信息",
       *     parameters = {
       *         @ParamDoc( name = "req", description = "请求参数", example = "req")
       *     },
       *     returnValueDescription = "返回数据",
       *     extensions = {
       *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
       *     }
       * )
       */
    ResultStatus.ResultStatus saveStoreProductAbility(1: ChannelConfig.SaveOrgProductAbilityRequest
    request);
}