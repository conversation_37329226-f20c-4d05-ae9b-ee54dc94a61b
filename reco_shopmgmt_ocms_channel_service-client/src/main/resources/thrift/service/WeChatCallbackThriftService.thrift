namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultStatus.thrift"
include "WeChatNotifyRequest.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关订单回调服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台订单业务，订单回调消息处理、订单履约管理等订单业务。",
 *     description = "为赋能中台提供渠道订单对接服务，主要包含渠道订单回调消息处理，商家履约操作服务。"
 * )
 */
service WeChatCallbackThriftService {

    /**
     * @MethodDoc(
     *     description = "该接口用于接收微信公众号关注，取关，位置信息等",
     *     displayName = "微信公众号回调处理接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "status",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    ResultStatus.ResultStatus wechatNotify(1: WeChatNotifyRequest.WeChatNotifyRequest request, 2:binary requestBody);


    /**
     * @MethodDoc(
     *     description = "该接口用于接收微信公众号关注，取关，位置信息等",
     *     displayName = "微信公众号认证服务",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "status",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    ResultStatus.ResultStatus auth(1: WeChatNotifyRequest.WeChatNotifyRequest request);

}