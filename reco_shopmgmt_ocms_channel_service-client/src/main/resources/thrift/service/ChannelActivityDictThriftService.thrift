namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ActivityTypeResponse.thrift"

/**
 * @InterfaceDoc(
 *     type = "octo.thrift",
 *     displayName = "渠道公共服务类",
 *     description = "渠道活动类型基础字典",
 *     scenarios = "渠道活动类型基础字典"
 * )
 */
service ChannelActivityDictThriftService {

    /**
    * @MethodDoc(
    *     displayName = "渠道活动类型基础字典查询接口",
    *     description = "根据渠道查询渠道的活动类型",
    *     parameters = {
    *         @ParamDoc( name = "request", description = "渠道活动类型基础字典查询请求入参", example = {})
    *     },
    *     returnValueDescription = "活动类型查询结果",
    *     extensions = {
    *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
    *     }
    * )
    */
    ActivityTypeResponse.ActivityTypeResponse queryActivityDict();
}