namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultData.thrift"
include "SpuInfo.thrift"
include "BaseRequest.thrift"
include "ResultStatus.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关商品服务(SPU)",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台商品业务，商品创建修改删除，商品图片上传，商品图片上传结果查询业务。",
 *     description = "为赋能中台提供渠道商品对接服务，主要包含中台商品数据维护信息同步到相关渠道平台。"
 * )
 */
service ChannelSpuThriftService {

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块按sku创建商品消息，调用渠道接口将新增商品信息同步到各渠道平台",
         *     displayName = "根据SPU创建商品接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData spuCreate(1: SpuInfo.SpuInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块按UPC创建商品消息，调用渠道接口将新增商品信息同步到各渠道平台",
         *     displayName = "根据UPC创建商品接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData upcCreate(1: SpuInfo.SpuInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块更新商品消息，调用渠道接口将更新商品信息同步到各渠道平台",
         *     displayName = "商品更新接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData updateBySpuOrUpc(1: SpuInfo.SpuInfoRequest request);


        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块更新商品重量消息，调用渠道接口将更新商品信息同步到各渠道平台",
         *     displayName = "根据SPU更新商品重量接口，weight",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData updateWeightBySpu(1: SpuInfo.SpuWeightInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块更新商品消息，调用渠道接口将更新商品店内分类信息同步到各渠道平台",
         *     displayName = "商品店内分类更新接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData updateSpuStoreCategory(1: SpuInfo.SpuStoreCategoryInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于修改一个门店下的商品 upc",
         *     displayName = "该接口用于修改一个门店下的商品 upc",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData updateSpuUpc(1: SpuInfo.SpuUpcUpdateRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块删除商品消息，调用渠道接口将删除商品信息同步到各渠道平台",
         *     displayName = "商品批量删除接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData deleteSpu(1: SpuInfo.SpuInfoDeleteRequest request);

        ResultData.ResultSpuData deleteCategoryAndSpu(1: BaseRequest.BaseRequest request);

        /**
 * @MethodDoc(
 *     description = "该接口用于接收中台商品模块删除商品消息，调用渠道接口将删除商品信息同步到各渠道平台",
 *     displayName = "商品批量删除接口",
 *     parameters = {
 *         @ParamDoc(
 *             name = "request",
 *             description = "请求参数",
 *             example = "request"
 *         )
 *     },
 *     returnValueDescription = "处理结果",
 *     example = "resultData",
 *     extensions = {
 *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
 *     }
 * )
 */
        ResultData.ResultSpuData deleteSku(1: SpuInfo.SkuInSpuInfoDeleteRequest request);

        /**
* @MethodDoc(
*     description = "该接口用于接收中台商品规格删除商品消息，调用渠道接口将删除商品信息同步到各渠道平台",
*     displayName = "规格单个删除接口",
*     parameters = {
*         @ParamDoc(
*             name = "request",
*             description = "请求参数",
*             example = "request"
*         )
*     },
*     returnValueDescription = "处理结果",
*     example = "resultData",
*     extensions = {
*         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
*     }
* )
*/
        ResultData.ResultSingleSpuData deleteSingleSku(1: SpuInfo.SingleSkuInSpuInfoDeleteRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品上架/下架消息，调用渠道接口将商品进行上架下架操作",
         *     displayName = "商品上架下架接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData updateSpuSellStatus(1: SpuInfo.SpuSellStatusInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "调用渠道接口将商品进行上架下架操作（基于渠道商品编码）",
         *     displayName = "商品上架下架接口（基于渠道商品编码）",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData updateSpuSellStatusByChannelSpuId(1: SpuInfo.SpuSellStatusInfoByChannelSpuIdRequest request);


        /**
        * @MethodDoc(
        *     description = "调用渠道接口将商品进行上架下架操作（基于渠道商品编码）",
        *     displayName = "商品上架下架接口（基于渠道商品编码）",
        *     parameters = {
        *         @ParamDoc(
        *             name = "request",
        *             description = "请求参数",
        *             example = "request"
        *         )
        *     },
        *     returnValueDescription = "处理结果",
        *     example = "resultData",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        ResultData.ResultData updateSpuSellStatusByChannelSkuId(1:
        SpuInfo.SpuSellStatusInfoByChannelSkuIdRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于根据原商品编码更换新商品编码，调用渠道接口将更新商品信息同步到各渠道平台",
         *     displayName = "根据原商品编码更换新商品编码",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData updateCustomSpuIdByOriginId(1: SpuInfo.UpdateCustomSpuIdByOriginIdRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于根据原商品编码更换新商品编码，调用渠道接口将更新商品信息同步到各渠道平台",
         *     displayName = "根据原商品编码更换新商品编码",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData updateCustomSpuIdByNameAndSpec(1: SpuInfo.UpdateCustomSpuIdByNameAndSpecRequest request);

        /**
            * @MethodDoc(
            *     displayName = "获取单个商品信息",
            *     description = "该接口用于拉取单个商品信息",
            *     parameters = {
            *         @ParamDoc(
            *         name = "request",
            *         description = "获取单个商品信息",
            *         example = "request"
            *         )
            *     },
            *     returnValueDescription = "获取单个商品信息",
            *     extensions = {
            *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
            *     }
            * )
            */
        SpuInfo.GetSpuInfoResponse getSpuInfo(1: SpuInfo.GetSpuInfoRequest request);

        /**
            * @MethodDoc(
            *     displayName = "获取多个商品信息(最多100个)",
            *     description = "该接口用于获取多个商品信息",
            *     parameters = {
            *         @ParamDoc(
            *         name = "request",
            *         description = "获取多个商品信息",
            *         example = "request"
            *         )
            *     },
            *     returnValueDescription = "多个商品信息",
            *     extensions = {
            *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
            *     }
            * )
            */
        SpuInfo.GetSpuInfosResponse getSpuInfos(1: SpuInfo.GetSpuInfosRequest request);

        /**
         * @MethodDoc(
         *     displayName = "批量获取商品信息",
         *     description = "该接口用于批量拉取线上渠道商品信息",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "请求参数", example = "request")
         *     },
         *     returnValueDescription = "返回数据",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        SpuInfo.BatchGetSpuInfoResponse batchGetSpuInfo(1: SpuInfo.BatchGetSpuInfoRequest request);

        /**
         * @MethodDoc(
         *     displayName = "批量获取商品审核状态信息",
         *     description = "该接口用于批量拉取线上渠道商品审核状态信息",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "请求参数", example = "request")
         *     },
         *     returnValueDescription = "返回数据",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        SpuInfo.QueryAuditStatusResponse getAuditStatus(1: SpuInfo.QueryAuditStatusRequest request);

        /**
        * @MethodDoc(
        *     displayName = "批量获取商品管家质量信息",
        *     description = "批量获取商品管家质量信息",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = "request")
        *     },
        *     returnValueDescription = "返回数据",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        SpuInfo.QueryQualityProblemResponse queryQualityProblem(1:
        SpuInfo.QueryQualityProblemRequest request);

        /**
         * @MethodDoc(
         *     displayName = "提交商品申诉状态信息",
         *     description = "该接口用于提交商品申诉状态信息",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "请求参数", example = "request")
         *     },
         *     returnValueDescription = "返回数据",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus submitAppealInfo(1: SpuInfo.SubmitAppealInfoRequest request);

        /**
         * @MethodDoc(
         *     displayName = "根据商品UPC或商品名称查询平台推荐类目信息",
         *     description = "根据商品UPC或商品名称查询平台推荐类目信息",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "请求参数", example = "request")
         *     },
         *     returnValueDescription = "返回数据",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        SpuInfo.RecommendChannelCategoryQueryResponse queryRecommendChannelCategory(1: SpuInfo.RecommendChannelCategoryQueryRequest request);
        /**
        * 根据skuId 查询其对应的appfoodcode
        **/
        SpuInfo.SkuId2AppFoodCodeResponse queryAppFoodCodeBySkuId(1: SpuInfo.QueryAppFoodCodeBySkuIdRequest request);

        /**
         * @MethodDoc(
         *     displayName = "更新渠道商品分类信息(目前仅支持饿了么渠道)",
         *     description = "更新渠道商品分类信息(目前仅支持饿了么渠道)",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "请求参数", example = "request")
         *     },
         *     returnValueDescription = "返回数据",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus updateCustomSpuStoreCategory(1: SpuInfo.UpdateCustomSpuStoreCategoryRequest request);

        ResultData.ResultSpuData batchUpdateSpuStoreCategoryCode(1: SpuInfo.BatchUpdateCustomSpuStoreCategoryRequest request);


         /**
         * @MethodDoc(
         *     displayName = "通过游标获取商品列表",
         *     description = "通过游标获取商品列表",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "获取商品列表",
         *         example = "request"
         *         )
         *     },
         *     returnValueDescription = "获取商品列表",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        SpuInfo.BatchGetSpuInfoByOffsetResponse elmBatchGetSpuInfoByOffset(1: SpuInfo.BatchGetSpuInfoByOffsetRequest request);

      /**
         * @MethodDoc(
         *     displayName = "商品信息中敏感词校验(目前仅支持美团外卖渠道)",
         *     description = "商品信息中敏感词校验(目前仅支持美团外卖渠道)",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "请求参数", example = "request")
         *     },
         *     returnValueDescription = "返回数据",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        SpuInfo.SensitiveWordCheckResponse checkSensitiveWords(1: SpuInfo.SensitiveWordCheckRequest request);

      /**
         * @MethodDoc(
         *     displayName = "获取合规审核删除商品信息详情(目前仅支持美团外卖渠道)",
         *     description = "获取合规审核删除商品信息详情(目前仅支持美团外卖渠道)",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "请求参数", example = "request")
         *     },
         *     returnValueDescription = "返回数据",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        SpuInfo.QueryNormAuditDelSpuResponse queryNormAuditDelSpuInfo(1: SpuInfo.QueryNormAuditDelSpuRequest request);

        /**
         * @MethodDoc(
         *     displayName = "查询渠道商品内部id，如闪购侧商品的实体id",
         *     description = "查询渠道商品内部id，如闪购侧商品的实体id",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "请求参数", example = "request")
         *     },
         *     returnValueDescription = "返回数据",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        SpuInfo.QueryChannelSpuIdResponse queryChannelSpuId(1: SpuInfo.QueryChannelSpuIdRequest request);

        /**
         * @MethodDoc(
         *     description = "根据渠道商品spu编码批量删除elm商家端商品",
         *     displayName = "根据渠道商品spu编码批量删除elm商家端商品",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData elmDeleteSpuByChannelSpuId(1: SpuInfo.SpuInfoDeleteByChannelSpuIdRequest request);

        /**
         * @MethodDoc(
         *     description = "根据SPU单个创建饿了么渠道商品接口",
         *     displayName = "根据SPU单个创建饿了么渠道商品接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData elmSingleCreateSpu(1: SpuInfo.SpuInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "根据SPU单个更新饿了么渠道商品接口",
         *     displayName = "根据SPU单个更新饿了么渠道商品接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData elmSingleUpdateSpu(1: SpuInfo.SpuInfoRequest request);



        ResultData.ResultSpuData elmSingleUpdateOptionalField(1: SpuInfo.SpuInfoRequest request);

       /**
        * @MethodDoc(
        *     displayName = "获取店铺内分类下商品",
        *     description = "获取店铺内分类下商品",
        *     parameters = {
        *         @ParamDoc(
        *         name = "request",
        *         description = "获取多个商品信息",
        *         example = "request"
        *         )
        *     },
        *     returnValueDescription = "多个商品信息",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        SpuInfo.BatchGetSpuInfoResponse getSpuInfosByCategory(1: SpuInfo.GetSpuInfosByCategoryRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口的目的是针对平台动态信息缺陷，重新推送动态信息，此接口其他业务不可使用，否则后果自负",
         *     displayName = "商品更新接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData batchRepushSpuDynamicInfo(1: SpuInfo.DynamicInfoRepairRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块按sku创建商品消息，调用渠道接口将新增商品信息同步到各渠道平台",
         *     displayName = "根据SPU创建商品接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSingleSpuData createSingleSpu(1: SpuInfo.SingleSpuInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块更新商品消息，调用渠道接口将更新商品信息同步到各渠道平台",
         *     displayName = "商品更新接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSingleSpuData updateSingleSpu(1: SpuInfo.SingleSpuInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块删除商品消息，调用渠道接口将删除商品信息同步到各渠道平台",
         *     displayName = "商品批量删除接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSingleSpuData deleteSingleSpu(1: SpuInfo.SingleSpuInfoDeleteRequest
        request);

        /**
         * @MethodDoc(
         *     description = "调用渠道接口将商品进行上架下架操作（基于渠道商品编码）",
         *     displayName = "商品上架下架接口（基于渠道商品编码）",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSingleSpuData updateSingleSpuSellStatus(1: SpuInfo.SingleSpuSellStatusRequest
        request);


        /**
         * @MethodDoc(
         *     description = "更新单个商品信息，字段可选，不传表示不更新",
         *     displayName = "单个更新商品信息接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus updateOptionFieldBySpu(1: SpuInfo.UpdateSpuOptionFieldRequest request);

        SpuInfo.GetSpuCategoryPropertyResponse getSpuCategoryPropertyInfo(1: SpuInfo.GetSpuInfoRequest request);


        ResultData.ResultSpuData distributeToStoreAsync(1: SpuInfo.ChannelSpuDistributeInfoRequest request);

        ResultData.ResultSpuData getStoreDistributeStatus(1: SpuInfo.ChannelSpuDistributeInfoRequest request);

}