namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ChannelSettlementQuery.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关结算业务服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能结算业务：渠道账单拉取等",
 *     description = "为赋能结算系统提供渠道对接服务，主要包含渠道账单拉取。"
 * )
 */
service ChannelSettlementThriftService {

        /**
        * @MethodDoc(
        *     displayName = "根据账期区间，分页查询渠道结算单汇总",
        *     description = "该接口用于分页查询不同渠道的结算单列表",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        ChannelSettlementQuery.ChannelSettlementPageResponse getChannelSettlementList(1: ChannelSettlementQuery.ChannelSettlementPageRequest request);

        /**
        * @MethodDoc(
        *     displayName = "根据渠道结算单号，分页查询渠道结算明细",
        *     description = "该接口用于查询不同渠道的结算单详情",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        ChannelSettlementQuery.ChannelSettlementAndDetailResponse getChannelOrderSettlementListById(1: ChannelSettlementQuery.ChannelOrderSettlementByIdRequest request);

        /**
        * @MethodDoc(
        *     displayName = "根据账期区间，分页查询渠道结算单明细",
        *     description = "用于查询渠道结算订单列表",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        ChannelSettlementQuery.ChannelOrderSettlementPageResponse getChannelOrderSettlementList(1: ChannelSettlementQuery.ChannelOrderSettlementPageRequest request);

        /**
        * @MethodDoc(
        *     displayName = "分页查询对账单接口",
        *     description = "分页查询对账单接口",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        ChannelSettlementQuery.ChannelBalanceBillPageResponse getChannelBalanceBillList(1: ChannelSettlementQuery.ChannelBalanceBillPageRequest request);

        /**
        * @MethodDoc(
        *     displayName = "查询订单计费明细接口",
        *     description = "查询订单计费明细接口",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        ChannelSettlementQuery.ChannelCheckBillResponse getChannelCheckBillList(1: ChannelSettlementQuery.ChannelCheckBillPageRequest request);

        /**
        * @MethodDoc(
        *     displayName = "分页查询对账单接口",
        *     description = "分页查询对账单接口",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        ChannelSettlementQuery.JDBalanceBillPageResponse getBalanceBill(1: ChannelSettlementQuery.JDBalanceBillPageRequest request);

        /**
        * @MethodDoc(
        *     displayName = "分页查询对账单接口",
        *     description = "分页查询对账单接口",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "请求参数", example = {})
        *     },
        *     returnValueDescription = "返回数据",
        *     example = "status",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        ChannelSettlementQuery.DouyinBalanceBillPageResponse getDouyinBalanceBill(1: ChannelSettlementQuery.DouyinBalanceBillPageRequest request);
}