namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultData.thrift"
include "SkuInfo.thrift"
include "BaseRequest.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关商品服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台商品业务，商品创建修改删除，商品图片上传，商品图片上传结果查询业务。",
 *     description = "为赋能中台提供渠道商品对接服务，主要包含中台商品数据维护信息同步到相关渠道平台。"
 * )
 */
service ChannelSkuThriftService {

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块按sku创建商品消息，调用渠道接口将新增商品信息同步到各渠道平台",
         *     displayName = "根据SKU创建商品接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultData skuCreate(1: SkuInfo.SkuInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块按UPC创建商品消息，调用渠道接口将新增商品信息同步到各渠道平台",
         *     displayName = "根据UPC创建商品接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultData upcCreate(1: SkuInfo.SkuInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块更新商品消息，调用渠道接口将更新商品信息同步到各渠道平台",
         *     displayName = "商品更新接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultData updateSku(1: SkuInfo.SkuInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块删除商品消息，调用渠道接口将删除商品信息同步到各渠道平台",
         *     displayName = "商品批量删除接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultData deleteSku(1: SkuInfo.SkuInfoDeleteRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块图片上传消息，调用渠道接口将图片同步到各渠道平台",
         *     displayName = "图片上传接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultData pictureUpload(1: SkuInfo.PictureUploadRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块图片URL上传消息，调用渠道接口将图片同步到各渠道平台",
         *     displayName = "指定URL路径的图片上传接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultData urlPictureUpload(1: SkuInfo.PictureUploadRequest request);

        /**
             * @MethodDoc(
             *     displayName = "默认图片上传接口",
             *     description = "该接口用于接收中台商品模块查询默认商品图片上传结果",
             *     parameters = {
             *         @ParamDoc( name = "request", description = "请求参数", example = "request")
             *     },
             *     returnValueDescription = "处理结果",
             *     extensions = {
             *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
             *     }
             * )
             */
        ResultData.ResultData defaultPictureUpload(1: SkuInfo.PictureUploadRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块查询商品图片上传结果，调用渠道接口查询图片上传结果返回中台",
         *     displayName = "获取商品图片上传状态接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        SkuInfo.PictureUploadResult getPictureUploadStatus(1: SkuInfo.PictureUploadStatusRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品上架/下架消息，调用渠道接口将商品进行上架下架操作",
         *     displayName = "商品上架下架接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultData updateSkuSellStatus(1: SkuInfo.SkuSellStatusInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于根据原商品编码更换新商品编码，调用渠道接口将更新商品信息同步到各渠道平台",
         *     displayName = "根据原商品编码更换新商品编码",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultData updateCustomSkuId(1: SkuInfo.UpdateCustomSkuIdRequest request);

        /**
             * @MethodDoc(
             *     displayName = "获取单个商品信息",
             *     description = "获取单个商品信息",
             *     parameters = {
             *         @ParamDoc(
             *         name = "request",
             *         description = "获取单个商品信息",
             *         example = "request"
             *         )
             *     },
             *     returnValueDescription = "获取单个商品信息",
             *     extensions = {
             *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
             *     }
             * )
             */
        SkuInfo.GetSkuInfoResponse getSkuInfo(1: SkuInfo.GetSkuInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于根据原商品编码更换新商品编码，调用渠道接口将更新商品信息同步到各渠道平台",
         *     displayName = "根据原商品编码更换新商品编码",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultData changeCustomSkuId(1: SkuInfo.ChangeCustomSkuIdRequest request);

        /**
             * @MethodDoc(
             *     displayName = "获取商品列表",
             *     description = "获取商品列表",
             *     parameters = {
             *         @ParamDoc(
             *         name = "request",
             *         description = "获取商品列表",
             *         example = "request"
             *         )
             *     },
             *     returnValueDescription = "获取商品列表",
             *     extensions = {
             *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
             *     }
             * )
             */
        SkuInfo.BatchGetSkuInfoResponse getSkuInfoList(1: SkuInfo.BatchGetSkuInfoRequest request);


        /**
             * @MethodDoc(
             *     displayName = "通过游标获取商品列表",
             *     description = "通过游标获取商品列表",
             *     parameters = {
             *         @ParamDoc(
             *         name = "request",
             *         description = "获取商品列表",
             *         example = "request"
             *         )
             *     },
             *     returnValueDescription = "获取商品列表",
             *     extensions = {
             *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
             *     }
             * )
             */
        SkuInfo.BatchGetSkuInfoByOffsetResponse getSkuInfoListByOffset(1: SkuInfo.BatchGetSkuInfoByOffsetRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于根据图片URL批量查询图片ID，目前仅支持美团渠道，每次最多传50个图片URL",
         *     displayName = "根据图片URL批量查询图片ID",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        SkuInfo.BatchUploadPictureByUrlsResponse batchUploadPicturesByUrl(1: SkuInfo.PictureUploadRequest request);


        /**
        * @MethodDoc(
        *     displayName = "查询违规商品信息接口(目前仅支持京东渠道)",
        *     description = "查询违规商品信息接口（目前仅支持京东渠道）",
        *     parameters = {
        *         @ParamDoc(
        *         name = "request",
        *         description = "获取多个商品信息",
        *         example = "request"
        *         )
        *     },
        *     returnValueDescription = "多个商品信息",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
        *     }
        * )
        */
        SkuInfo.BatchGetViolateSkuResponse batchGetViolateSku(1: SkuInfo.BatchGetViolateSkuRequest request);


        /**
         * @MethodDoc(
         *     description = "该接口用于根据原商品编码更换新商品编码，调用渠道接口将更新商品信息同步到各渠道平台",
         *     displayName = "根据原商品编码更换新商品编码",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultData batchChangeCustomSkuIdForCleanJob(1: SkuInfo.CustomSkuIdChangeRequest request);



        SkuInfo.BatchGetSkuSaleInfoResponse batchGetSkuSaleInfo(1: SkuInfo.BatchGetSkuSaleInfoRequest request);

}