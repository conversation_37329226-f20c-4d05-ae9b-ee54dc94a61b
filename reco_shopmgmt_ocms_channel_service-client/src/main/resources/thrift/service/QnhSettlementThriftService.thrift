namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ChannelSettlementQuery.thrift"


/**
 * @InterfaceDoc(
 *     displayName = "牵牛花结算服务",
 *     type = "octo.thrift",
 *     scenarios = "适用于赋能中台业务，拉取渠道账单",
 *     description = "拉取渠道账单"
 * )
 */
service QnhSettlementThriftService {

        /**
            * @MethodDoc(
            *     displayName = "根据账期区间，分页查询渠道结算单明细",
            *     description = "用于查询渠道结算订单列表",
            *     parameters = {
            *         @ParamDoc( name = "request", description = "请求参数", example = {})
            *     },
            *     returnValueDescription = "返回数据",
            *     example = "status",
            *     extensions = {
            *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
            *     }
            * )
            */
        ChannelSettlementQuery.QnhChannelOrderSettlementPageResponse getChannelOrderSettlementList(1: ChannelSettlementQuery.QnhChannelOrderSettlementPageRequest request);

}