namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "RecordSelectResponse.thrift"
include "RecordSelectRequest.thrift"

service ChannelRequestRecordThriftService {
     /**
     * @MethodDoc(
     *     description = "该接口用于查询调用线上渠道接口调用记录，接口调用失败可以根据此接口获取调用接口的信息记录",
     *     displayName = "接口用于查询调用线上渠道接口调用记录",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    RecordSelectResponse.RecordSelectResponse recordSelect(1: RecordSelectRequest.RecordSelectRequest request);

}