namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultStatus.thrift"
include "DeliveryNotifyRequest.thrift"
include "BirdDeliveryCallbackRequest.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关配送回调服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台第三方配送业务。",
 *     description = "为赋能中台提供接入第三方配送平台能力。"
 * )
 */
service ChannelDeliveryCallbackThriftService {

        ResultStatus.ResultStatus deliveryNotify(1: DeliveryNotifyRequest.DeliveryNotifyRequest request);

        void deliveryNotifyForHummingBird(1: BirdDeliveryCallbackRequest.BirdDeliveryCallbackRequest request);

        void deliverStoreChangeForHummingBird(1: BirdDeliveryCallbackRequest.BirdDeliveryCallbackRequest request);
}