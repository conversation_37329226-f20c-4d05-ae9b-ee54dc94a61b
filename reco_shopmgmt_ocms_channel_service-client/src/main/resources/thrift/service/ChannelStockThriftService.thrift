namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultData.thrift"
include "Stock.thrift"
include "ResultStatus.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关库存服务",
 *     type = "octo.thrift",
 *     scenarios = "适用于赋能中台业务，根据中台库存分配策略，同步修改各渠道线上库存数。",
 *     description = "为赋能中台提供渠道商品库存对接服务，主要包含中台商品库存修改信息同步到相关渠道平台。"
 * )
 */
service ChannelStockThriftService {
    
    /**
     * @MethodDoc(
     *     description = "该接口用于接收中台库存模块更新商品库存消息，调用渠道接口将更新库存信息同步到各渠道平台",
     *     displayName = "更新商品库存接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    ResultData.ResultData updateStock(1: Stock.SkuStockRequest request);
    
    /**
     * @MethodDoc(
     *     description = "该接口用于接收中台库存模块更新商品库存消息，调用渠道接口将更新库存信息同步到各渠道平台",
     *     displayName = "多渠道更新商品库存接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    ResultData.ResultData updateStockMultiChannel(1: Stock.SkuStockMultiChannelRequest request);

    /**
   * @MethodDoc(
   *     description = "该接口用于接收中台价格模块修改价格消息，调用渠道接口将修改价格信息同步到各渠道平台",
    *     displayName = "更新单渠道商品价格接口",
   *     parameters = {
    *         @ParamDoc(
    *             name = "request",
    *             description = "请求参数",
    *             example = "request"
    *         )
    *     },
   *     returnValueDescription = "处理结果",
   *     example = "resultData",
   *     extensions = {
   *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
   *     }
   * )
   */
    ResultData.ResultSpuData updateStockBySpu(1: Stock.SpuStockRequest request);

    /**
   * @MethodDoc(
   *     description = "该接口用于接收中台渠道库存模块修改库存请求，将修改库存信息同步到各渠道平台",
    *     displayName = "新版更新多渠道库存信息",
   *     parameters = {
    *         @ParamDoc(
    *             name = "request",
    *             description = "请求参数",
    *             example = "request"
    *         )
    *     },
   *     returnValueDescription = "处理结果",
   *     example = "resultData",
   *     extensions = {
   *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
   *     }
   * )
   */
    ResultStatus.ResultStatus updateYouzanStock(1: Stock.YouzanUpdateStockRequest request);

    /**
   * @MethodDoc(
   *     description = "该接口用于批量拉取库存信息",
    *     displayName = "批量拉取库存信息",
   *     parameters = {
    *         @ParamDoc(
    *             name = "request",
    *             description = "请求参数",
    *             example = "request"
    *         )
    *     },
   *     returnValueDescription = "处理结果",
   *     example = "resultData",
   *     extensions = {
   *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
   *     }
   * )
   */
    Stock.BatchGetStockInfoResponse batchGetStockInfo(1: Stock.BatchGetStockInfoRequest req)
}