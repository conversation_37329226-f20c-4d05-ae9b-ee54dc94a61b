namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultStatus.thrift"
include "CreateDeliveryRequest.thrift"
include "CreateDeliveryResponse.thrift"
include "CancelDeliveryRequest.thrift"
include "QueryDeliveryRequest.thrift"
include "QueryDeliveryResponse.thrift"
include "QueryRiderLocationRequest.thrift"
include "LocationInfo.thrift"
include "CheckDeliverableRequest.thrift"
include "QueryDeliveryRangeRequest.thrift"
include "QueryDeliveryRangeResponse.thrift"
include "QueryDeliveryStoreRequest.thrift"
include "QueryDeliveryStoreResponse.thrift"
include "DeliveryPathRequest.thrift"
include "DeliveryPathResponse.thrift"


/**
 * @InterfaceDoc(
 *     displayName = "渠道网关配送服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台第三方配送业务。",
 *     description = "为赋能中台提供接入第三方配送平台能力。"
 * )
 */
service ChannelDeliveryThriftService {

        /**
         * @MethodDoc(
         *     displayName = "创建配送运单",
         *     description = "创建配送运单",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        CreateDeliveryResponse.CreateDeliveryResponse createDelivery(1: CreateDeliveryRequest.CreateDeliveryRequest request);

        /**
         * @MethodDoc(
         *     displayName = "取消运单",
         *     description = "取消运单",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus cancelDelivery(1: CancelDeliveryRequest.CancelDeliveryRequest request);

        /**
         * @MethodDoc(
         *     displayName = "查询运单",
         *     description = "查询运单",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        QueryDeliveryResponse.QueryDeliveryResponse queryDelivery(1: QueryDeliveryRequest.QueryDeliveryRequest request);

        /**
         * @MethodDoc(
         *     displayName = "查询骑手位置",
         *     description = "查询骑手位置",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        LocationInfo.LocationInfo queryRiderLocation(1: QueryRiderLocationRequest.QueryRiderLocationRequest request);

        /**
         * @MethodDoc(
         *     displayName = "检查订单是否可配送",
         *     description = "检查订单是否可配送",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus checkDeliverable(1: CheckDeliverableRequest.CheckDeliverableRequest request)

        /**
         * @MethodDoc(
         *     displayName = "查询配送门店的配送范围",
         *     description = "查询配送门店的配送范围",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        QueryDeliveryRangeResponse.QueryDeliveryRangeResponse queryDeliveryRange(1: QueryDeliveryRangeRequest.QueryDeliveryRangeRequest request);

        /**
         * @MethodDoc(
         *     displayName = "查询配送门店的信息",
         *     description = "查询配送门店的信息",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        QueryDeliveryStoreResponse.QueryDeliveryStoreResponse queryChannelStore(1: QueryDeliveryStoreRequest.QueryDeliveryStoreRequest request);

        /**
        * @MethodDoc(
        *     displayName = "查询配送路径",
        *     description = "查询配送路径",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
        *     },
        *     returnValueDescription = "验签结果",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
        *     }
        * )
        */
        DeliveryPathResponse.DeliveryPathResponse queryDeliveryPath(1: DeliveryPathRequest.DeliveryPathRequest request);
}