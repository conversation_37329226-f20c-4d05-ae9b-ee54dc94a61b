namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ActivitySaveRequest.thrift"
include "ActivityResponse.thrift"
include "ActivityDeleteRequest.thrift"
include "BaseRequest.thrift"
include "QueryActivityResponse.thrift"
include "QuerySkuActivityInfoRequest.thrift"
include "QuerySkuActivityInfoResponse.thrift"
include "QueryCanModifyPrice.thrift"

/**
 * @InterfaceDoc(
 *     type = "octo.thrift",
 *     displayName = "渠道网关促销服务",
 *     description = "为赋能中台提供渠道促销对接服务，主要用于中台促销业务，同步中台促销数据到各个线上平台。",
 *     scenarios = "适用场景，适用赋能中台促销管理业务(提供新建促销活动、删除促销活动及查询促销活动列表等功能)"
 * )
 */
service ChannelActivityThriftService {
    
    /**
     * @MethodDoc(
     *     displayName = "创建/更新线上活动",
     *     description = "该接口用于中台促销业务，中台创建活动或修改活动信息时，同步活动到各个线上平台",
     *     parameters = {
     *         @ParamDoc( name = "request", description = "请求参数", example = {})
     *     },
     *     returnValueDescription = "返回处理结果数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    ActivityResponse.ActivityResponse saveActivity(1: ActivitySaveRequest.ActivitySaveRequest request);
    
    /**
     * @MethodDoc(
     *     displayName = "批量删除线上活动",
     *     description = "该接口用于中台促销业务，中台删除活动后调用该接口，同步删除各个线上平台活动",
     *     parameters = {
     *         @ParamDoc( name = "request", description = "请求参数", example = {})
     *     },
     *     returnValueDescription = "返回处理结果数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    ActivityResponse.ActivityResponse batchDelete(1: ActivityDeleteRequest.ActivityDeleteRequest request);
    
    /**
     * @MethodDoc(
     *     displayName = "查询门店折扣商品",
     *     description = "该接口用于查询美团门店折扣商品活动数据，用于数据比对，便于排查数据问题",
     *     parameters = {
     *         @ParamDoc( name = "request", description = "请求参数", example = {})
     *     },
     *     returnValueDescription = "返回活动数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    QueryActivityResponse.QueryActivityResponse queryActivityList(1: BaseRequest.BaseRequest request);

    /**
     * @MethodDoc(
     *     displayName = "查询门店商品的促销活动信息",
     *     description = "该接口用于查询美团门店折扣商品活动数据，用于数据比对，便于排查数据问题",
     *     parameters = {
     *         @ParamDoc( name = "request", description = "请求参数", example = {})
     *     },
     *     returnValueDescription = "返回活动数据",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    QuerySkuActivityInfoResponse.QuerySkuActivityInfoResponse querySkuActivityInfo(1:QuerySkuActivityInfoRequest.QuerySkuActivityInfoRequest request);

    /**
    * 查询门店spu是否可以改价信息，参加某些活动的商品不能改价。
**/
    QueryCanModifyPrice.QueryCanModifyPriceResponse querySpuCanModifyPrice(1: QueryCanModifyPrice.QueryCanModifyPriceRequest request);
}