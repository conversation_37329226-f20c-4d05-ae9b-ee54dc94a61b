namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "MemberCreateResponse.thrift"
include "MemberQueryResponse.thrift"
include "MemberQueryRequest.thrift"
include "MemberCreateRequest.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关会员回调服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景：渠道方回调该服务，将消息转发到赋能会员。",
 *     description = "为赋能会员提供渠道对接服务，主要包含渠道回调消息处理。"
 * )
 */
service ChannelMemberCallbackThriftService {

        /**
         * @MethodDoc(
         *     displayName = "根据手机号或卡号查询会员数据",
         *     description = "根据手机号或卡号查询会员数据",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "会员查询参数",
         *         example = "request")
         *     },
         *     returnValueDescription = "会员信息",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        MemberQueryResponse.MemberQueryResponse getMember(1: MemberQueryRequest.MemberQueryRequest request);

        /**
         * @MethodDoc(
         *     displayName = "根据手机号等信息创建会员",
         *     description = "根据手机号等信息创建会员",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "会员创建请求参数",
         *         example = "request")
         *     },
         *     returnValueDescription = "会员创建结果数据",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        MemberCreateResponse.MemberCreateResponse createMember(1: MemberCreateRequest.MemberCreateRequest request);

}