namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultStatus.thrift"
include "SkuInfo.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关回调服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台，回调消息处理。",
 *     description = "为赋能中台提供渠道对接服务，主要包含渠道回调消息处理。"
 * )
 */
service ChannelCallbackThriftService {

        /**
         * @MethodDoc(
         *     displayName = "系统存活接口",
         *     description = "系统存活接口",
         *     returnValueDescription = "系统存活接口",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus monitorAlive();

        /**
         * @MethodDoc(
         *     displayName = "商品消息推送",
         *     description = "商品消息推送",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "商品消息推送",
         *         example = "request")
         *     },
         *     returnValueDescription = "商品消息推送",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus skuNotify(1: SkuInfo.SkuNotifyRequest request);

        /**
         * @MethodDoc(
         *     displayName = "商品违规 预警/处罚 消息推送",
         *     description = "商品违规 预警/处罚 消息推送",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "商品违规 预警/处罚 消息推送",
         *         example = "request")
         *     },
         *     returnValueDescription = "商品违规 预警/处罚 消息推送",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus skuViolationNotify(1: SkuInfo.SkuViolationNotifyRequest request);

        /**
         * @MethodDoc(
         *     displayName = "商品申诉状态 消息推送",
         *     description = "商品申诉状态 消息推送",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "商品申诉状态 消息推送",
         *         example = "request")
         *     },
         *     returnValueDescription = "商品申诉状态 消息推送",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus skuAppealInfoNotify(1: SkuInfo.SkuAppealInfoNotifyRequest request);

        /**
         * @MethodDoc(
         *     displayName = "商品管家 异常信息变更推送",
         *     description = "商品管家 异常信息变更推送",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "商品管家 异常信息变更推送",
         *         example = "request")
         *     },
         *     returnValueDescription = "商品管家 异常信息变更推送",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus skuManagerAbnormalChange(1: SkuInfo.SkuManagerAbnormalChangeRequest request);

        /**
         * @MethodDoc(
         *     displayName = "商品打标 消息推送",
         *     description = "商品打标 消息推送",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "商品打标 消息推送",
         *         example = "request")
         *     },
         *     returnValueDescription = "商品打标 消息推送",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus skuTagNotify(1: SkuInfo.SkuTagNotifyRequest request);

}