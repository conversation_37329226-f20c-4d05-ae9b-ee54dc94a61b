namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultData.thrift"
include "SpuInfo.thrift"
include "BaseRequest.thrift"
include "ResultStatus.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关商品服务(SPU)，提供数据清洗接口",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台商品业务，商品创建修改删除，商品图片上传，商品图片上传结果查询业务。",
 *     description = "为赋能中台提供渠道商品对接服务，主要包含中台商品数据维护信息同步到相关渠道平台。"
 * )
 */
service ChannelSpuCleanerThriftService {
        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块按sku创建商品消息，调用渠道接口将新增商品信息同步到各渠道平台",
         *     displayName = "根据SPU创建商品接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData spuCreateForCleaner(1: SpuInfo.SpuInfoRequest request);

        /**
         * @MethodDoc(
         *     description = "该接口用于接收中台商品模块更新商品消息，调用渠道接口将更新商品信息同步到各渠道平台",
         *     displayName = "商品更新接口",
         *     parameters = {
         *         @ParamDoc(
         *             name = "request",
         *             description = "请求参数",
         *             example = "request"
         *         )
         *     },
         *     returnValueDescription = "处理结果",
         *     example = "resultData",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultData.ResultSpuData updateBySpuOrUpcForCleaner(1: SpuInfo.SpuInfoRequest request);


}