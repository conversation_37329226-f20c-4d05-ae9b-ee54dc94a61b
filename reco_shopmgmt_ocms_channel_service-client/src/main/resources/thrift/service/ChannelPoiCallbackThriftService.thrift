namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ChannelPoiCallbackRequest.thrift"
include "ResultStatus.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关门店回调服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景：渠道方回调该服务，将消息转发到赋能租户系统。",
 *     description = "为门店提供渠道对接服务，主要包含渠道回调消息处理。"
 * )
 */
service ChannelPoiCallbackThriftService {

        /**
         * @MethodDoc(
         *     displayName = "触发同步渠道门店信息",
         *     description = "触发同步渠道门店信息",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "会员查询参数",
         *         example = "request")
         *     },
         *     returnValueDescription = "会员信息",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus syncChannelStore(1: ChannelPoiCallbackRequest.ChannelPoiCallbackRequest request);


        /**
         * @MethodDoc(
         *     displayName = "解绑渠道门店",
         *     description = "解绑渠道门店",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "参数",
         *         example = "request")
         *     },
         *     returnValueDescription = "",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus unBindChannelStore(1: ChannelPoiCallbackRequest.ChannelPoiBindUnbindCallbackRequest request);

        /**
         * @MethodDoc(
         *     displayName = "应用市场申请信息推送",
         *     description = "应用市场申请信息推送",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "参数",
         *         example = "request")
         *     },
         *     returnValueDescription = "",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus applyApp(1: ChannelPoiCallbackRequest.ChannelAddAppCallbackRequest request);


        /**
         * @MethodDoc(
         *     displayName = "应用市场审核结果推送",
         *     description = "应用市场审核结果推送",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "参数",
         *         example = "request")
         *     },
         *     returnValueDescription = "",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus applyAppCall(1: ChannelPoiCallbackRequest.ChannelAddAppCallbackRequest request);


         /**
         * @MethodDoc(
         *     displayName = "设置美团渠道的token缓存",
         *     description = "设置美团渠道的token缓存",
         *     parameters = {
         *         @ParamDoc(
         *         name = "request",
         *         description = "参数",
         *         example = "request")
         *     },
         *     returnValueDescription = "",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus setMtTokenCache(1: ChannelPoiCallbackRequest.SetMtTokenCacheRequest request);
}