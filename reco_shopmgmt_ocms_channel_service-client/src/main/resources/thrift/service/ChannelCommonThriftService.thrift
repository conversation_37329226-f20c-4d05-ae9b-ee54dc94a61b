namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "BaseRequest.thrift"
include "ResultStatus.thrift"

/**
 * @InterfaceDoc(
 *     type = "octo.thrift",
 *     displayName = "渠道公共服务类",
 *     description = "中台渠道公共服务类，提供鉴权功能",
 *     scenarios = "中台渠道公共服务类，提供鉴权功能"
 * )
 */
service ChannelCommonThriftService {

    /**
    * @MethodDoc(
    *     displayName = "开放平台回调验签接口",
    *     description = "根据各渠道开放平台的参数验签",
    *     parameters = {
    *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
    *     },
    *     returnValueDescription = "验签结果",
    *     extensions = {
    *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
    *     }
    * )
    */
    ResultStatus.ResultStatus authForOpenApi(BaseRequest.AuthForOpenApiRequest request);
}