namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultStatus.thrift"
include "ChannelPoiIdRequest.thrift"
include "ChannelPoiAuthRequest.thrift"
include "PoiPromotionInfoUpdateRequest.thrift"
include "PoiShippingTimeUpdateRequest.thrift"
include "GetPoiPromotionInfoResponse.thrift"
include "ChannelPoiStatusResponse.thrift"
include "PoiPrebookDaysUpdateRequest.thrift"
include "ChannelPoiSignRequest.thrift"
include "ChannelPoiSignResponse.thrift"
include "ChannelPoiRequest.thrift"
include "PoiAddressUpdateRequest.thrift"
include "GetPoiInfoResponse.thrift"
include "ChannelPoiCategory.thrift"

/**
 * @InterfaceDoc(
 *     type = "octo.thrift",
 *     displayName = "渠道网关门店管理服务",
 *     description = "为赋能中台提供渠道门店的管理功能,包括设置营业状态, 营业时间及公告等。",
 *     scenarios = "为赋能中台提供渠道门店的管理功能,包括设置营业状态, 营业时间及公告等。"
 * )
 */
service ChannelPoiThriftService {

        /**
        * 门店开始营业
        **/
        ResultStatus.ResultStatus poiOpen(1: ChannelPoiIdRequest.ChannelPoiIdRequest request);


        /**
        * 门店停止营业
        **/
        ResultStatus.ResultStatus poiClose(1: ChannelPoiIdRequest.ChannelPoiIdRequest request);


        /**
        * 更新门店公告信息
        **/
        ResultStatus.ResultStatus poiPromotionInfoUpdate(1: PoiPromotionInfoUpdateRequest.PoiPromotionInfoUpdateRequest request);


        /**
        * 更新门店营业时间
        **/
        ResultStatus.ResultStatus poiShippingTimeUpdate(1: PoiShippingTimeUpdateRequest.PoiShippingTimeUpdateRequest request);


        /**
        * 使门店接收预订单
        **/
        ResultStatus.ResultStatus poiPrebookStatusOpen(1: ChannelPoiIdRequest.ChannelPoiIdRequest request);


        /**
        * 使门店不接收预订单
        **/
        ResultStatus.ResultStatus poiPrebookStatusClose(1: ChannelPoiIdRequest.ChannelPoiIdRequest request);

        /**
        * 更新门店的接受预定的日期范围
        **/
        ResultStatus.ResultStatus poiPrebookDaysUpdate(1: PoiPrebookDaysUpdateRequest.PoiPrebookDaysUpdateRequest request);


        /**
        * 查询门店公告信息
        **/
        GetPoiPromotionInfoResponse.GetPoiPromotionInfoResponse getPoiPromotionInfo(1: ChannelPoiIdRequest.ChannelPoiIdRequest request);

        /**
        * 查询门店营业状态
        **/
        ChannelPoiStatusResponse.ChannelPoiStatusResponse getPoiStatus(1: ChannelPoiIdRequest.ChannelPoiIdRequest request);


        /**
        * 门店授权
        **/
        ResultStatus.ResultStatus poiAuth(1: ChannelPoiAuthRequest.ChannelPoiAuthRequest request);



        /**
        * 获取渠道签名
        **/
        ChannelPoiSignResponse.ChannelPoiSignResponse getChannelSign(1: ChannelPoiSignRequest.ChannelPoiSignRequest request);

        /**
        * 获取渠道签名
        **/
        ResultStatus.ResultStatus poiHotlineUpdate(1: ChannelPoiRequest.ChannelPoiHotlineRequest request);

        /**
        * 更新门店营业时间
        **/
        ResultStatus.ResultStatus poiAddressUpdate(1: PoiAddressUpdateRequest.PoiAddressUpdateRequest request);

        /**
        * 更新渠道门店售后地址信息
        **/
        GetPoiInfoResponse.UpdateSaleafterAddressMessageResponse updateSaleafterAddressMessage(1: PoiAddressUpdateRequest.UpdateSaleafterAddressMessageRequest request);

        /**
        * 获取省份列表
        **/
        GetPoiInfoResponse.GetProvinceListResponse getProvinceList(1: PoiAddressUpdateRequest.GetProvinceListRequest request);

        /**
        * 获取省下级区域列表
        **/
        GetPoiInfoResponse.GetProvinceDistrictsListResponse getProvinceDistrictsList(1: PoiAddressUpdateRequest.GetProvinceListRequest request);

        /**
        * 查询渠道门店类目信息
        **/
        ChannelPoiCategory.ChannelPoiCategoryResponse queryChannelPoiCategory(1: ChannelPoiCategory.ChannelPoiCategoryRequest request);

}