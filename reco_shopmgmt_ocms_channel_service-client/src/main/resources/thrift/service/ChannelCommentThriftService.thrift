namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ChannelCommentRequest.thrift"
include "ChannelCommentResponse.thrift"

/**
 * @InterfaceDoc(
 *     type = "octo.thrift",
 *     displayName = "渠道评价服务类",
 *     description = "中台渠道评价服务类，提供评论查询、回复、统计功能",
 *     scenarios = "中台渠道评价服务类，提供评论查询、回复、统计功能"
 * )
 */
service ChannelCommentThriftService {

        /**
         * @MethodDoc(
         *     displayName = "查询渠道评价列表信息",
         *     description = "根据指定条件过滤查询活动列表，分页展示",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "查询评价列表信息请求入参", example = {})
         *     },
         *     returnValueDescription = "根据指定条件过滤查询评价列表，分页展示",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "根据租户id鉴权")
         *     }
         * )
         */
        ChannelCommentResponse.CommentListQueryResponse queryCommentList(ChannelCommentRequest.CommentListQueryRequest request);

                /**
         * @MethodDoc(
         *     displayName = "评价回复",
         *     description = "评价回复",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "评价回复请求参数", example = {})
         *     },
         *     returnValueDescription = "  ",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "根据租户id鉴权")
         *     }
         * )
         */
        ChannelCommentResponse.CommentReplyResponse reply(ChannelCommentRequest.CommentReplyRequest request);

        /**
         * @MethodDoc(
         *     displayName = "查询评价规则",
         *     description = "查询评价规则",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "查询评价规则请求入参", example = {})
         *     },
         *     returnValueDescription = "评价规则",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "根据租户id鉴权")
         *     }
         * )
         */
        ChannelCommentResponse.CommentRuleQueryResponse queryCommentRule(ChannelCommentRequest.CommentRuleQueryRequest request);

}