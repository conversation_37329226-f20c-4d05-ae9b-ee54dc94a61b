namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultStatus.thrift"
include "ChangeDeliveryPlatformRequest.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关订单回调服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台订单业务，订单回调消息处理、订单履约管理等订单业务。",
 *     description = "为赋能中台提供渠道订单对接服务，主要包含渠道订单回调消息处理，商家履约操作服务。"
 * )
 */
service ChannelDeliverySyncThriftService {

    /**
     * @MethodDoc(
     *     description = "该接口用于接收各渠道订单状态变更、取消订单、配送状态变更、订单修改等消息，调用中台订单模块，处理订单取消业务",
     *     displayName = "渠道消息回调处理接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "status",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    ResultStatus.ResultStatus syncDeliveryPlatformChange(1: ChangeDeliveryPlatformRequest.ChangeDeliveryPlatformRequest request);


}