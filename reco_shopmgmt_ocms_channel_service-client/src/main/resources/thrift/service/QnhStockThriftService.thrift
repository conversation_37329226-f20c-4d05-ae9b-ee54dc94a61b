namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultData.thrift"
include "Stock.thrift"
include "ResultStatus.thrift"

/**
 * @InterfaceDoc(
 *     displayName = "牵牛花库存服务",
 *     type = "octo.thrift",
 *     scenarios = "适用于赋能中台业务，根据中台库存分配策略，同步修改各渠道线上库存数。",
 *     description = "为赋能中台提供渠道商品库存对接服务，主要包含中台商品库存修改信息同步到相关渠道平台。"
 * )
 */
service QnhStockThriftService {

    /**
     * @MethodDoc(
     *     description = "该接口用于接收中台库存模块更新商品库存消息，调用牵牛花接口将更新库存信息同步到牵牛花平台",
     *     displayName = "牵牛花更新商品库存接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     */
    ResultData.ResultData updateStockMultiChannel(1: Stock.SkuStockMultiChannelRequest request);

}