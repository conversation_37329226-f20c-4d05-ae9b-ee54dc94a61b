namespace java com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service

include "ResultStatus.thrift"
include "UpdatePoiShippingRequest.thrift"
include "BatchUpdatePoiShippingRequest.thrift"
include "ResetPoiShippingRequest.thrift"
include "DeletePoiShippingRequest.thrift"
include "QueryPoiShippingRequest.thrift"
include "QueryPoiShippingResponse.thrift"
include "BatchQueryPoiShippingAreaRequest.thrift"
include "QueryPoiShippingAreaResponse.thrift"
include "UpdatePoiRegularPeriodShippingByShippingAreaIdRequest.thrift"
include "UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest.thrift"
include "DeletePoiShippingByShippingIdRequest.thrift"
include "BatchUpdatePoiShippingFeeRequest.thrift"

/**
 * @InterfaceDoc(
 *     type = "octo.thrift",
 *     displayName = "渠道网关门店配送管理服务",
 *     description = "为赋能中台提供渠道门店的管理功能,包括设置营业状态, 营业时间及公告等。",
 *     scenarios = "为赋能中台提供渠道门店的管理功能,包括设置营业状态, 营业时间及公告等。"
 * )
 */
service ChannelPoiShippingThriftService {

        /**
         * @MethodDoc(
         *     displayName = "更新门店配送范围",
         *     description = "更新门店配送范围",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus updatePoiShipping(UpdatePoiShippingRequest.UpdatePoiShippingRequest request);

        /**
         * @MethodDoc(
         *     displayName = "更新门店配送范围",
         *     description = "更新门店配送范围",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus batchUpdatePoiShipping(BatchUpdatePoiShippingRequest.BatchUpdatePoiShippingRequest request);

        /**
         * @MethodDoc(
         *     displayName = "重置门店配送范围",
         *     description = "重置门店配送范围",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus resetPoiShipping(ResetPoiShippingRequest.ResetPoiShippingRequest request);

        /**
         * @MethodDoc(
         *     displayName = "删除门店配送范围",
         *     description = "删除门店配送范围",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "开放平台验签filter请求入参", example = {})
         *     },
         *     returnValueDescription = "验签结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus deletePoiShipping(DeletePoiShippingRequest.DeletePoiShippingRequest request);

        /**
         * @MethodDoc(
         *     displayName = "查询门店配送范围",
         *     description = "查询门店配送范围",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "查询门店配送范围请求入参", example = {})
         *     },
         *     returnValueDescription = "门店配送范围",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        QueryPoiShippingResponse.QueryPoiShippingResponse queryPoiShipping(QueryPoiShippingRequest.QueryPoiShippingRequest request);

        /**
        * @MethodDoc(
        *     displayName = "批量查询门店配送范围",
        *     description = "批量查询门店配送范围",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "批量查询门店配送范围请求入参", example = {})
        *     },
        *     returnValueDescription = "批量查询门店配送范围",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
        *     }
        * )
        */
        QueryPoiShippingAreaResponse.QueryPoiShippingAreaResponse batchQueryShippingInfo(BatchQueryPoiShippingAreaRequest.BatchQueryPoiShippingAreaRequest request);

        /**
        * @MethodDoc(
        *     displayName = "查询单门店配送范围(响应包含shippingAreaId)",
        *     description = "查询单门店配送范围(响应包含shippingAreaId)",
        *     parameters = {
        *         @ParamDoc( name = "request", description = "查询单门店配送范围请求入参", example = {})
        *     },
        *     returnValueDescription = "批量查询门店配送范围",
        *     extensions = {
        *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
        *     }
        * )
        */
        QueryPoiShippingAreaResponse.QueryPoiShippingAreaResponse queryPoiShippingAreaInfo(QueryPoiShippingRequest.QueryPoiShippingRequest request);

         /**
         * @MethodDoc(
         *     displayName = "通过shippingAreaId来更新正常时段配送范围",
         *     description = "通过shippingAreaId来更新正常时段配送范围",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "通过shippingAreaId来更新正常时段配送范围入参", example = {})
         *     },
         *     returnValueDescription = "更新结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus updatePoiRegularPeriodShippingByShippingAreaId(UpdatePoiRegularPeriodShippingByShippingAreaIdRequest.UpdatePoiRegularPeriodShippingByShippingAreaIdRequest request);

         /**
         * @MethodDoc(
         *     displayName = "通过shippingAreaId来更新特殊时段配送范围",
         *     description = "通过shippingAreaId来更新特殊时段配送范围",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "通过shippingAreaId来更新特殊时段配送范围入参", example = {})
         *     },
         *     returnValueDescription = "更新结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus updatePoiSpecialPeriodShippingByShippingAreaId(UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest.UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest request);


         /**
         * @MethodDoc(
         *     displayName = "通过shippingAreaId来删除配送范围",
         *     description = "通过shippingAreaId来删除配送范围",
         *     parameters = {
         *         @ParamDoc( name = "request", description = "通过shippingAreaId来删除配送范围入参", example = {})
         *     },
         *     returnValueDescription = "删除结果",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无须鉴权")
         *     }
         * )
         */
        ResultStatus.ResultStatus deletePoiShippingByShippingAreaId(DeletePoiShippingByShippingIdRequest.DeletePoiShippingByShippingIdRequest request);

}