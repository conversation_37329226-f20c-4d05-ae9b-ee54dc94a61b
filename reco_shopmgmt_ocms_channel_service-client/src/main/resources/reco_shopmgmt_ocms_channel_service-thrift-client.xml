<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans  http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- 用来初始化配置 -->
    <bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <!-- https://wiki.sankuai.com/pages/viewpage.action?pageId=1252888869 -->
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
        <property name="locations">
            <list>
                <!--用来存放和profile相关的配置信息-->
                <value>classpath:META-INF/app.properties</value>
            </list>
        </property>
    </bean>

    <bean id="channelThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="dapChannelThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="maltChannelThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <!-- 数据清洗创建商品接口单独使用 -->
    <bean id="cleanerChannelThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="10000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="dapDeliveryThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <!-- 渠道订单对接服务 -->
    <bean id="copOrderDockingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_order_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 渠道配送对接服务 -->
    <bean id="copDeliveryDockingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelDeliveryDockingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 渠道结算对接服务 -->
    <bean id="copSettlementThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSettlementThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_settlement_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>
    <bean id="copQnhSettlementThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhSettlementThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_settlement_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 渠道订单回调服务 -->
    <bean id="copOrderCallbackThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderCallbackThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_order_callback_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8081"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 渠道分类对接服务 -->
    <bean id="copCategoryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCategoryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_category_thrift_timeout','10000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 渠道虚拟配置商品服务-->
    <bean id="channelVirtualConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelVirtualConfigThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_activity_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 渠道价格对接服务 -->
    <bean id="copPriceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPriceThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_price_thrift_timeout','10000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <!--<property name="serverIpPorts" value="**************:8090"/>-->
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelPriceThriftClient" class="com.sankuai.meituan.shangou.empower.ocms.channel.client.ChannelPriceThriftClient">
        <property name="channelPriceThriftService" ref="copPriceThriftService"/>
    </bean>

    <!-- 渠道商品对接服务 -->
    <bean id="copSkuThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSkuThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_sku_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>
    <bean id="copSpuThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSpuThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_sku_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 渠道库存对接服务 -->
    <bean id="copStockThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelStockThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_stock_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 牵牛花库存对接服务 -->
    <bean id="qnhStockThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhStockThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('qnh_stock_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 基础数据对接服务 -->
    <bean id="channelBaseMsgThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelBaseMsgThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_base_thrift_timeout','10000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="ocmsChannelClientThreadPool"
          class="com.sankuai.meituan.shangou.empower.ocms.channel.threadpool.TaskThreadPoolManager">
    </bean>

    <bean id="channelPoiThriftClient" class="com.sankuai.meituan.shangou.empower.ocms.channel.client.ChannelPoiThriftClient">
        <property name="channelBaseMsgThriftService" ref="channelBaseMsgThriftService"/>
    </bean>


    <!-- 渠道促销对接服务 -->
    <bean id="copActivityThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelActivityThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_activity_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 渠道评价对接服务 -->
    <bean id="copCommentThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCommentThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_comment_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 渠道公共服务 -->
    <bean id="channelCommonThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCommonThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_comment_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 配送服务 -->
    <bean id="channelDeliveryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 配送回调服务 -->
    <bean id="channelDeliveryCallbackThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliveryCallbackThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_callback_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8081"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelPoiThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_callback_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelPoiShippingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiShippingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_poi_shipping_thrift_timeout','8000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>
    <!-- 三方聚合运力服务 -->
    <bean id="channelAggDeliveryThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelAggDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="maltChannelAggDeliveryThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="maltChannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MaltChannelAggDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('malt_channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="dapChannelAggDeliveryThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="dapChannelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapChannelAggDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('dap_channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 三方聚合运力服务异常回调 -->
    <bean id="channelAggDeliveryCallbackThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelAggDeliveryCallbackThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8081"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 麦芽田聚合运力服务状态回调 -->
    <bean id="farmDeliveryCallbackThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.FarmDeliveryCallbackThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8081"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 青云智送聚合运力服务状态回调 -->
    <bean id="dapDeliveryCallbackThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="dapDeliveryThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapDeliveryCallbackThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8081"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 同步跑腿状态到青云智送 -->
    <bean id="dapPaoTuiPostThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="dapDeliveryThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapPaoTuiThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('farm_paotui_post_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 牵牛花订单 -->
    <bean id="qnhOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhOrderDockingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('qnh_order_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 牵牛花服务 -->
    <bean id="qnhProductThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.qnh.service.QnhProductThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('qnh_product_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 同步跑腿状态到麦芽田 -->
    <bean id="farmPaoTuiPostThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.FarmPaoTuiPostThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('farm_paotui_post_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8081"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 医药渠道网关回调服务 -->
    <bean id="medicineChannelCallbackThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MedicineChannelCallbackThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('medicine_channel_callback_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8081"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelVideoThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelVideoThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_video_thrift_timeout','20000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelMerchantSpuThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift.ChannelMerchantSpuThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_merchant_spu_thrift_timeout','10000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>


    <bean id="ChannelCategoryBrandThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift.ChannelCategoryBrandThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_merchant_spu_thrift_timeout','10000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 平台配送服务 -->
    <bean id="orderChannelDeliveryThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.OrderChannelDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelSpuThriftServiceV2" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelSpuThriftServiceV2"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_merchant_spu_thrift_timeout','10000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelBrandThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift.ChannelBrandThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_merchant_spu_thrift_timeout','3000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 活动类型信息对接服务 -->
    <bean id="channelActivityDictThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelActivityDictThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_activity_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8091"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelSpuCleanerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="cleanerChannelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSpuCleanerThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_spu_for_cleaner_thrift_timeout','60000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="mtAppThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MtAppThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_merchant_spu_thrift_timeout','3000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelQualityThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelQualityThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_quality_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="aliHealthChannelThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.medicine.thrift.AliHealthChannelThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_quality_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="channelMaterialThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift.ChannelMaterialThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_material_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>


    <bean id="channelActivityPromotionThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelActivityPromotionThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_activity_promotion_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>


    <bean id="copChannelOfflineCategoryThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelOfflineCategoryThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_material_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!--    发票上传    -->
    <bean id="qnhInvoiceThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.channel.qnh.service.invoice.QnhInvoiceThriftService"/>
        <property name="timeout" value="8000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 发票申请 -->
    <bean id="channelInvoiceCallbackThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="channelThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelInvoiceCallbackThriftService"/>
        <property name="timeout" value="8000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8081"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>


</beans>
