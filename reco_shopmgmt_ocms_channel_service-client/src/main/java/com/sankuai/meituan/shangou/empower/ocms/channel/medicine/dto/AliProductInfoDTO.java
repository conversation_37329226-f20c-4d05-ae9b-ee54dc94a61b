package com.sankuai.meituan.shangou.empower.ocms.channel.medicine.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@Data
@TypeDoc(description = "商品基础信息，有效期等")
@ThriftStruct
public class AliProductInfoDTO {

    @FieldDoc(
            description = "批次号",
            example = {}
    )
    @ThriftField(1)
    private String batchNo;
    @FieldDoc(
            description = "有效期至",
            example = {}
    )
    @ThriftField(2)
    private String expireDate;
    @FieldDoc(
            description = "最小包装数量",
            example = {}
    )
    @ThriftField(3)
    private String pkgAmount;
    @FieldDoc(
            description = "生产日期",
            example = {}
    )
    @ThriftField(4)
    private String produceDateStr;
}
