package com.sankuai.meituan.shangou.empower.ocms.channel.component;

import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service类服务订单渠道的代理，根据渠道id（channelId）来决定最终调用哪个渠道的Service
 *
 * @param <T> 针对订单渠道的Serivce接口
 * <AUTHOR>
 * @since 2023/3/1
 */
@SuppressWarnings({"unused"})
@Slf4j
public abstract class ServiceChannelProxy<T extends ServingChannel> implements ServingChannel {

    /**
     * 订单渠道及其对应的Service，用不可变的Map来保证线程安全
     */
    private final Map<Integer, T> services;

    public ServiceChannelProxy(List<T> services) {
        Map<Integer, T> serviceMap = services.stream()
                .filter(service -> Objects.nonNull(service.ofOrderChannel()))
                .collect(Collectors.toMap(ServingChannel::ofOrderChannel, Function.identity()));
        this.services = Collections.unmodifiableMap(serviceMap);
        log.info("Channel services: {}", services);
    }

    /**
     * 获取订单渠道对应的Service
     */
    @NotNull
    protected final T getService(Integer channelId) {
        if (channelId == null) {
            throw new IllegalArgumentException("channelId must not be null");
        }
        T service = services.get(channelId);
        if (service == null) {
            throw new IllegalArgumentException("No service for channelId: " + channelId);
        }
        log.info("Delegated service: {}, channel: {}", service.getClass().getSimpleName(), channelId);
        return service;
    }

    @Override
    public final Integer ofOrderChannel() {
        return null;
    }

}
