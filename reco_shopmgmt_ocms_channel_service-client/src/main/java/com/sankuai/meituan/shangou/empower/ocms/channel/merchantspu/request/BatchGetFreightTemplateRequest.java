package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.TenantChannelRequest;

import lombok.Data;

/**
 * @Author: luokai14
 * @Date: 2024/1/23 11:13 上午
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "查询运费模板请求"
)
@Data
@ThriftStruct
public class BatchGetFreightTemplateRequest {
    @FieldDoc(
            description = "基础信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public TenantChannelRequest baseInfo;


    @FieldDoc(
            description = "运费模板名称，支持模糊搜索",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String name;

    @FieldDoc(
            description = "页数（默认为0，第一页从0开始",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Integer pageNum = 0;

    @FieldDoc(
            description = "每页模板数（默认为10），最大值是100",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer pageSize = 10;


}
