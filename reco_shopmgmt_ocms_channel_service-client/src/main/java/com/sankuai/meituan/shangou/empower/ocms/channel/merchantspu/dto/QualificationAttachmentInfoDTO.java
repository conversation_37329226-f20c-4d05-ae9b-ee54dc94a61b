package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: xiechengliang
 * @Date: 2023/12/19 12:10 下午
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "资质内容对象"
)
@Data
@ThriftStruct
public class QualificationAttachmentInfoDTO {
    @FieldDoc(
            description = "资质类型,1-图片",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    private Integer mediaType;

    @FieldDoc(
            description = "资质url",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    private String url;
}
