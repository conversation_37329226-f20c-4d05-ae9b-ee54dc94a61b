package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift;

import org.apache.thrift.TException;

import com.facebook.swift.service.ThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.RecommendCategoryBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.RecommendCategoryBrandResponse;
/**
 * <AUTHOR>
 * @description 获取类目&品牌服务
 * @since 2023/12/19 16:03
 */

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关商品类目品牌服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，查询渠道推荐类目、品牌",
 *     description = "为赋能中台推荐类目、品牌"
 * )
 */
@ThriftService
public interface ChannelCategoryBrandThriftService {

    /**
     * @MethodDoc(
     *     description = "该接口查询平台推荐的类目与品牌",
     *     displayName = "查询推荐类目与品牌接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     * 
     * @param request
     */
    public RecommendCategoryBrandResponse queryRecommendCategoryBrand(RecommendCategoryBrandRequest request) throws TException;

}
