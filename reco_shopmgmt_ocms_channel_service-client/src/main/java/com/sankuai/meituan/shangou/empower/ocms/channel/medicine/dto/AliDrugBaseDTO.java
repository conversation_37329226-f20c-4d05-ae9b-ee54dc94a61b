package com.sankuai.meituan.shangou.empower.ocms.channel.medicine.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@Data
@TypeDoc(description = "药品基础信息")
@ThriftStruct
public class AliDrugBaseDTO {

    @FieldDoc(
            description = "批准文号",
            example = {}
    )
    @ThriftField(1)
    private String approvalLicenceNo;
    @FieldDoc(
            description = "药品id",
            example = {}
    )
    @ThriftField(2)
    private String drugEntBaseInfoId;
    @FieldDoc(
            description = "有效期",
            example = {}
    )
    @ThriftField(3)
    private String exprie;
    @FieldDoc(
            description = "药品名称",
            example = {}
    )
    @ThriftField(4)
    private String physicName;
    @FieldDoc(
            description = "药品类型描述",
            example = {}
    )
    @ThriftField(5)
    private String physicTypeDesc;
    @FieldDoc(
            description = "包装规格",
            example = {}
    )
    @ThriftField(6)
    private String pkgSpecCrit;
    @FieldDoc(
            description = "制剂规格",
            example = {}
    )
    @ThriftField(7)
    private String prepnSpec;
    @FieldDoc(
            description = "剂型描述",
            example = {}
    )
    @ThriftField(8)
    private String prepnTypeDesc;
}
