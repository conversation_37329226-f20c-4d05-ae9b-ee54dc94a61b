package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * 渠道商品医疗器械资质信息
 * 实际和thrift中定义的ChannelMedicalDeviceQuaInfoDTO一样，在这里定义只是因为注解和thrift文件形式的不互通，会导致填充不上值
 */
@TypeDoc(
        description = "渠道商品医疗器械资质信息"
)
@Data
@ThriftStruct
public class ChannelMedicalDeviceQuaInfoDTO {

    @FieldDoc(
            description = "医疗器械资质图",
            example = "[http://p0.meituan.net/xianfu/63d5cb4cf120c89da74c1dd3e7c8bae651375.jpg, A71E7F8D324B269F6557EE24799335A9]"
    )
    @ThriftField(1)
    List<String> quaPictures;

    @FieldDoc(
            description = "批准日期",
            example = "2024-01-01"
    )
    @ThriftField(2)
    String quaApprovalDate;

    @FieldDoc(
            description = "有效期至",
            example = "2025-01-01"
    )
    @ThriftField(3)
    String quaEffectiveDate;
}
