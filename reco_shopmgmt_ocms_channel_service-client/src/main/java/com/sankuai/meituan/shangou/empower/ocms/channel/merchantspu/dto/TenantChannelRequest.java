package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/31
 **/
@TypeDoc(
        description = "租户渠道请求"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class TenantChannelRequest {

    @FieldDoc(
            description = "租户ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public long tenantId;

    @FieldDoc(
            description = "渠道ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public int channelId;

    @FieldDoc(
            description = "APP ID",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(value = 3, requiredness = ThriftField.Requiredness.OPTIONAL)
    public int appId;

    @FieldDoc(
            description = "操作来源",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(value = 4, requiredness = ThriftField.Requiredness.OPTIONAL)
    public int source;

    public BaseRequestSimple convert2Base() {
        // 注意：不要在此将appId赋值到BaseRequestSimple，特别是添加默认1的逻辑，
        // 因为TenantChannelRequest可能给非京东总部渠道使用，底层某些基础接口在获取渠道参数时会认为appId的优先级大于门店id，可能造成调用渠道错误！
        return new BaseRequestSimple(tenantId, channelId);
    }
}
