package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/31
 **/
@TypeDoc(
        description = "渠道返回结果"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
public class ChannelStatus {
    @FieldDoc(
            description = "返回编码(0成功,其他失败)",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public int code;

    @FieldDoc(
            description = "返回结果",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String msg;


    public static ChannelStatus build(int code, String msg) {
        return new ChannelStatus(code, msg);
    }

    public static ChannelStatus buildSuccess() {
        return new ChannelStatus(0, "成功");
    }
}
