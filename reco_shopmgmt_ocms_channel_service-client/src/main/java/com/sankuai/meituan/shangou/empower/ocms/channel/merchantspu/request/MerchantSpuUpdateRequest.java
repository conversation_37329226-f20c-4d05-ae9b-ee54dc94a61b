package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.TenantChannelRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/16
 **/
@TypeDoc(
        description = "总部商品批量更新请求"
)
@Data
@ThriftStruct
public class MerchantSpuUpdateRequest {

    @FieldDoc(
            description = "基础请求信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public TenantChannelRequest baseInfo;


    @FieldDoc(
            description = "多规格参数列表(单次最多5个)",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<MerchantSpuDTO> paramList;
}
