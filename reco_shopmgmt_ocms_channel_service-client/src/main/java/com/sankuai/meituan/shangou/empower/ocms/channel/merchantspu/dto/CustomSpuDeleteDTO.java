package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/24
 * 渠道商品删除数据队形
 **/
@Data
public class CustomSpuDeleteDTO {


    @FieldDoc(
            description = "商家自定义Spu编码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String customSpuId;

    @FieldDoc(
            description = "规格类型(1单规格2多规格)",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer specType;

    @FieldDoc(
            description = "渠道SPU编码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public String channelSpuId;
}
