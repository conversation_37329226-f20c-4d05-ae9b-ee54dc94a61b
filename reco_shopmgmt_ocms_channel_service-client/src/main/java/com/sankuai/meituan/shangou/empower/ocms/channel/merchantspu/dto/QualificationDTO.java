package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @Author: xiechengliang
 * @Date: 2023/12/19 12:09 下午
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "资质内容对象"
)
@Data
@ThriftStruct
public class QualificationDTO {

    @FieldDoc(
            description = "资质key",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    private String qualityKey;

    @FieldDoc(
            description = "资质名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    private String qualityName;

    @FieldDoc(
            description = "资质列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    private List<QualificationAttachmentInfoDTO> qualityAttachments;
}
