package com.sankuai.meituan.shangou.empower.ocms.channel.medicine.thrift;

import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.medicine.request.AliHealthQueryDrugByDrugTraceRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.medicine.response.QueryDrugInfoByDrugTraceResponse;

@ThriftService
public interface AliHealthChannelThriftService {
    @MethodDoc(
            displayName = "queryDrugInfoByDrugTrace",
            description = "通过溯源码信息查询药品",
            parameters = {
                    @ParamDoc(name = "request", description = "查询请求")
            },
            returnValueDescription = "药品信息列表"
    )
    QueryDrugInfoByDrugTraceResponse queryDrugInfoByDrugTrace(AliHealthQueryDrugByDrugTraceRequest request);
}
