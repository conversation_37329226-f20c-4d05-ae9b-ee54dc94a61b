package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.TenantChannelRequest;
import lombok.Data;

/**
 * @Author: luokai14
 * @Date: 2023/12/19 10:25 上午
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "总部商品单个删除请求"
)
@Data
@ThriftStruct
public class MerchantSpuSingleDeleteRequest {
    @FieldDoc(
            description = "基础请求信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public TenantChannelRequest baseInfo;


    @FieldDoc(
            description = "商家自定义Spu编码",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(2)
    public String customSpuId;

    @FieldDoc(
            description = "渠道SPU编码",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(3)
    public String channelSpuId;

    @FieldDoc(
            description = "牵牛花Spu编码",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(4)
    public String spuId;


    @FieldDoc(
            description = "是否永久删除",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public Boolean deleteForever;

    @FieldDoc(
            description = "规格类型:京东渠道使用",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public Integer specType;

}
