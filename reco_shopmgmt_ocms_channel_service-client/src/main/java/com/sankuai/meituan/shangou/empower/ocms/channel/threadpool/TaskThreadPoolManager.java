package com.sankuai.meituan.shangou.empower.ocms.channel.threadpool;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;

import com.dianping.cat.Cat;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 线程池配置
 */
@Slf4j
public class TaskThreadPoolManager {
    private static final ThreadPoolExecutor SYNC_PRICE_POOL;

    private static final Map<AsyncTaskType, ThreadPoolExecutor> MONITORED_THREAD_POOLS = Maps.newConcurrentMap();
    private static final Map<AsyncTaskType, ListeningExecutorService> EXECUTOR_SERVICE_HOLDER = Maps.newConcurrentMap();

    static {
        SYNC_PRICE_POOL = Rhino
                .newThreadPool("sync-price-thread-pool", DefaultThreadPoolProperties.Setter()
                        .withCoreSize(20).withMaxSize(50).withBlockingQueue(new SynchronousQueue<>())
                        .withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy())
                        .withThreadFactory(new ThreadFactoryBuilder()
                                .setNameFormat(AsyncTaskType.SYNC_PRICE_TASK.getThreadPoolName() + "-%d").build()))
                .getExecutor();

        // 注册到Manager
        TaskThreadPoolManager.registerThreadPool(AsyncTaskType.SYNC_PRICE_TASK, SYNC_PRICE_POOL);
    }

    static void registerThreadPool(AsyncTaskType asyncTaskType, ThreadPoolExecutor threadPoolExecutor) {
        if (asyncTaskType == null || threadPoolExecutor == null) {
            log.warn("registerThreadPool param error! asyncTaskType:{}", asyncTaskType);
            return;
        }
        // 线程池监控
        MONITORED_THREAD_POOLS.put(asyncTaskType, threadPoolExecutor);

        // 为支持全链路压测，对原生线程池进行包装
        ExecutorService executorService = new ExecutorServiceTraceWrapper(threadPoolExecutor);
        ListeningExecutorService listeningExecutorService = MoreExecutors.listeningDecorator(executorService);
        EXECUTOR_SERVICE_HOLDER.put(asyncTaskType, listeningExecutorService);
        log.info("ThreadPool {} registered", asyncTaskType.getThreadPoolName());
    }

    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void printThreadPoolStatus() {
        MONITORED_THREAD_POOLS.forEach((asyncTaskType, threadPoolExecutor) -> {
            int activeCount = threadPoolExecutor.getActiveCount();
            long taskCount = threadPoolExecutor.getTaskCount();
            int queueSize = threadPoolExecutor.getQueue().size();
            long completedTaskCount = threadPoolExecutor.getCompletedTaskCount();

            int queueThresholdSize = (int) (queueSize * 0.8D);
            log.info("#TaskThreadPoolManager.{} printThreadPoolStatus#队列大小:{}, 加入到线程池的任务数:{}, 正在执行任务数:{}, 执行完的任务数: {}", asyncTaskType.getThreadPoolName(), queueSize, taskCount, activeCount, completedTaskCount);
            if (queueSize >= queueThresholdSize) {
                Cat.logMetricForCount("TaskThreadPoolManager_" + asyncTaskType.getThreadPoolName() + "_queueSize_exceed");
                log.warn("#TaskThreadPoolManager.{} printThreadPoolStatus#队列大小:{},超出阈值：{} 加入到线程池的任务数:{}, 正在执行任务数:{}, 执行完的任务数: {}", asyncTaskType.getThreadPoolName(), queueSize, queueThresholdSize, taskCount, activeCount, completedTaskCount);
            }
        });
    }

    public static ExecutorService getExecutorService(AsyncTaskType threadPoolType) {
        return EXECUTOR_SERVICE_HOLDER.get(threadPoolType);
    }

    public static <R> List<R> concurrentExecute(AsyncTaskType asyncTaskType,
                                                List<Callable<R>> callableList,
                                                String errorMsg) {

        if (CollectionUtils.isEmpty(callableList)) {
            return Collections.emptyList();
        }

        Preconditions.checkNotNull(asyncTaskType, "Submit runnableTask Error. asyncTaskType is null");
        Preconditions.checkArgument(EXECUTOR_SERVICE_HOLDER.containsKey(asyncTaskType), "Submit runnableTask Error. No executor service! ThreadPoolName: " + asyncTaskType.getThreadPoolName());

        // 使用包装后的线程池提交任务
        ExecutorService executorService = getExecutorService(asyncTaskType);
        List<Future<R>> futureList = Lists.newArrayList();
        callableList.forEach(callable -> {
            Future<R> future = executorService.submit(callable);
            futureList.add(future);
        });

        return parseResult(futureList, errorMsg);
    }

    private static <R> List<R> parseResult(List<Future<R>> futureList, String errorMsg) {
        if (CollectionUtils.isEmpty(futureList)) {
            return Collections.emptyList();
        }

        List<R> resultList = Lists.newArrayList();
        futureList.forEach(future -> {
            try {
                R result = future.get(10L, TimeUnit.SECONDS);

                if (result == null) {
                    return;
                }
                resultList.add(result);

            }
            catch (Exception e) {
                log.warn(errorMsg, e);
                if (e.getCause() instanceof BizException) {
                    throw (BizException) e.getCause();
                }
                else {
                    throw new BizException(1, errorMsg);
                }
            }
        });

        return resultList;
    }


    /**
     * 异步任务类型 与线程池一一对应 添加枚举需要注册对应线程池
     */
    public enum AsyncTaskType {
        SYNC_PRICE_TASK(3, "SyncSkuPriceTaskThreadPool"),
        ;

        @Getter
        private int value;

        @Getter
        private String threadPoolName;

        AsyncTaskType(int value, String threadPoolName) {
            this.value = value;
            this.threadPoolName = threadPoolName;
        }
    }
}
