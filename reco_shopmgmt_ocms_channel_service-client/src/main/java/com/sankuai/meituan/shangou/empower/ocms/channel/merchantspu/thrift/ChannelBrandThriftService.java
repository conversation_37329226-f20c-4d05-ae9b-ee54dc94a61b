package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift;

import com.facebook.swift.service.ThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.RecommendBrandRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.RecommendBrandResponse;
import org.apache.thrift.TException;

/**
 * @InterfaceDoc(
 *     displayName = "渠道网关商品品牌服务",
 *     type = "octo.thrift",
 *     scenarios = "适用场景，适用赋能中台查询推荐品牌，品牌列表",
 *     description = "为赋能中台提供渠道商品荐品牌，品牌列表。"
 * )
 */
@ThriftService
public interface ChannelBrandThriftService {

    /**
     * @MethodDoc(
     *     description = "该接口查询平台推荐的品牌",
     *     displayName = "查询推荐品牌接口",
     *     parameters = {
     *         @ParamDoc(
     *             name = "request",
     *             description = "请求参数",
     *             example = "request"
     *         )
     *     },
     *     returnValueDescription = "处理结果",
     *     example = "resultData",
     *     extensions = {
     *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无需鉴权")
     *     }
     * )
     * 
     * @param request
     */
    public RecommendBrandResponse queryRecommendBrand(RecommendBrandRequest request) throws TException;

}
