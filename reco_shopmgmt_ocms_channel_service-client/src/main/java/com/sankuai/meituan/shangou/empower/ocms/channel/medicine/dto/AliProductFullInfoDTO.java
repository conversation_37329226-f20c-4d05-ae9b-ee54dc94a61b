package com.sankuai.meituan.shangou.empower.ocms.channel.medicine.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

@Data
@TypeDoc(description = "商品基础信息，有效期等")
@ThriftStruct
public class AliProductFullInfoDTO {

    @FieldDoc(
            description = "追溯码",
            example = {}
    )
    @ThriftField(1)
    private String code;
    @FieldDoc(
            description = "码状态（A:已激活;I:已核注;O:已核销;C:已注销;S:已售出;E:码不存在）",
            example = {}
    )
    @ThriftField(2)
    private String codeStatus;
    @FieldDoc(
            description = "码生产信息对象",
            example = {}
    )
    @ThriftField(3)
    private List<AliProductInfoDTO> productInfoList;
    @FieldDoc(
            description = "药品基本信息对象",
            example = {}
    )
    @ThriftField(4)
    private AliDrugBaseDTO drugEntBaseDTO;
}
