package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.CustomSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.TenantChannelRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/16
 **/
@TypeDoc(
        description = "总部商品批量删除请求"
)
@Data
@ThriftStruct
public class MerchantSpuDeleteRequest {

    @FieldDoc(
            description = "基础请求信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public TenantChannelRequest baseInfo;


    @FieldDoc(
            description = "删除列表(单次最多10个)",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<CustomSpuDeleteDTO> paramList;

    @FieldDoc(
        description = "是否支持渠道spuId删除商品，默认false",
        requiredness = Requiredness.OPTIONAL
    )
		@ThriftField(value = 3, requiredness = ThriftField.Requiredness.OPTIONAL)
    public boolean optByChannelSpuId;
}
