package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/16
 **/
@TypeDoc(
        description = "总部规格数据对象"
)
@Data
@ThriftStruct
public class MerchantSkuDTO {

    @FieldDoc(
            description = "商家自定义SKU编码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String customSkuId;

    @FieldDoc(
            description = "规格属性值，多规格必传，需要和SPU的销售属性的顺序保持一致",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<SaleAttrValueDTO> attrValueList;

    @FieldDoc(
            description = "总部建议零售价,单位:分",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public Long suggestSalePrice;

    @FieldDoc(
            description = "sku的重量，单位为克/g",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer weight;

    @FieldDoc(
            description = "UPC编码",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    public String upc;

    /*@FieldDoc(
            description = "状态(1上架2下架)",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public Integer status;*/

    @FieldDoc(
            description = "商家端SKU编码,更新渠道恢复操作时需要传入",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public String channelSkuId;

    @FieldDoc(
            description = "多规格更新操作类型，更新多规格时传递(1恢复;2新增;3更新;4删除)",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    public Integer multiSpecOptType;

    @FieldDoc(
            description = "规格值名称",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(9)
    public String specValueName;

    @FieldDoc(
            description = "状态(1上架 2下架 4 删除) 京东渠道使用",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(value = 10, requiredness = ThriftField.Requiredness.OPTIONAL)
    public Integer fixedStatus;
}
