package com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.shipping;

import com.facebook.swift.codec.ThriftField;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
// 距离加价对象
public class DistanceRaiseFee {

    //开始距离 单位：千米
    @ThriftField(1)
    private String startDistance;

    // 结束距离 单位：千米
    @ThriftField(2)
    private String endDistance;

    // 加价金额 单位：元
    @ThriftField(3)
    private String raiseFee;
}