package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

/**
 * @Author: luokai14
 * @Date: 2024/1/23 11:36 上午
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "运费模板"
)
@Data
@ThriftStruct
public class FreightTemplateDTO {

    @FieldDoc(
            description = "运费模板id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    Integer id;

    @FieldDoc(
            description = "运费模板名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    String templateName;

    /**
     * 发货省份id
     */
    @FieldDoc(
            description = "发货省份id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    String productProvince;

    @FieldDoc(
            description = "发货城市id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    String productCity;


    @FieldDoc(
            description = "计价方式-1.按重量计价 2.按数量计价",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    Integer calculateType;

    @FieldDoc(
            description = "快递方式-1.快递 目前仅支持1",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    Integer transferType;


    @FieldDoc(
            description = "模板类型-0:阶梯计价 1:固定运费 2:卖家包邮 3:货到付款",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(7)
    Integer ruleType;

    @FieldDoc(
            description = "固定运费金额(单位:分) 固定运费模板必填 1-9900之间的整数",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(8)
    Integer fixedAmount;
}
