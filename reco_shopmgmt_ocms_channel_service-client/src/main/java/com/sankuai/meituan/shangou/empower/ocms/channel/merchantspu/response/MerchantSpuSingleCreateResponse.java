package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuResult;
import lombok.Data;

/**
 * @Author: luokai14
 * @Date: 2023/12/19 10:29 上午
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "总部单个创建返回"
)
@Data
@ThriftStruct
public class MerchantSpuSingleCreateResponse {
    @FieldDoc(
            description = "返回状态"
    )
    @ThriftField(1)
    private ChannelStatus status;

    @FieldDoc(
            description = "创建结果"
    )
    @ThriftField(2)
    private MerchantSpuResult spuResult;
}
