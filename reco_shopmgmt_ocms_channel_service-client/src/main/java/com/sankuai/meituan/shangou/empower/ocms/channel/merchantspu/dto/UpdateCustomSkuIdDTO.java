package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2024/9/30
 **/
@TypeDoc(
        description = "更新渠道sku编码"
)
@Data
@ThriftStruct
public class UpdateCustomSkuIdDTO {

    @FieldDoc(
            description = "渠道SPU编码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String channelSkuId;

    @FieldDoc(
            description = "商家自定义SPU编码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String customSkuId;

}
