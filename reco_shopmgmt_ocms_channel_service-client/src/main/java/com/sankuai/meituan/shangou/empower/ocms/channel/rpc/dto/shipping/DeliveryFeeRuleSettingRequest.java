package com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.shipping;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;
import lombok.ToString;

import java.lang.String;
import java.util.List;

/**
 * 配送费规则设置
 *
 * <AUTHOR>
 * @since 2025/9/9
 */
@Data
@ToString
@ThriftStruct
public class DeliveryFeeRuleSettingRequest {

    //租户id
    @ThriftField(1)
    private Long tenantId;

    //门店id
    @ThriftField(2)
    private Long storeId;

    //门店id
    @ThriftField(3)
    private String channelStoreId;

    //配送渠道
    @ThriftField(4)
    private Integer channelId;

    //订单限重 单位：千克
    @ThriftField(5)
    private String orderWeightLimit;

    // 基础起送价 单位：元
    @ThriftField(6)
    private String baseMinimumDeliveryFee;

    // 起送价 配送距离加价 最多5档
    @ThriftField(7)
    private List<DistanceRaiseFee> minimumDistanceRaiseFeeList;

    // 起送价 配送重量加价 最多5档
    @ThriftField(8)
    private List<WeightRaiseFee> minimumWeightRaiseFeeList;

    // 起送价 配送时间加价 最多5档
    @ThriftField(9)
    private List<TimeRaiseFee> minimumTimeRaiseFeeList;

    //基础配送费 单位：元
    @ThriftField(10)
    private String baseDeliveryFee;

    // 配送距离加价 最多5档
    @ThriftField(11)
    private List<DistanceRaiseFee> distanceRaiseFeeList;

    // 配送重量加价 最多5档
    @ThriftField(12)
    private List<WeightRaiseFee> weightRaiseFeeList;

    // 配送时间加价 最多5档
    @ThriftField(13)
    private List<TimeRaiseFee> timeRaiseFeeList;

    // 美团配送方式，仅channelId = 美团时有值
    @ThriftField(14)
    private List<String> logisticsCodes;
}
