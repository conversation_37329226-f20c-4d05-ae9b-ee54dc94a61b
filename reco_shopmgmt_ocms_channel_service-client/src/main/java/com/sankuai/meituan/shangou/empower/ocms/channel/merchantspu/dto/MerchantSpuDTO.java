package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/16
 **/
@TypeDoc(
        description = "总部多规格数据对象"
)
@Data
@ThriftStruct
public class MerchantSpuDTO {

    @FieldDoc(
            description = "商家自定义SPU编码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String customSpuId;

    @FieldDoc(
            description = "规格类型(1单规格2多规格)",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Integer specType;

    @FieldDoc(
            description = "渠道商品名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public String name;

    @FieldDoc(
            description = "渠道商品图片",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public List<String> images;

    @FieldDoc(
            description = "渠道店内分类列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public List<String> channelStoreCategoryCode;

    @FieldDoc(
            description = "渠道类目ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(6)
    public String channelCategoryId;

    /*@FieldDoc(
            description = "渠道品牌ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(7)
    public String channelBrandId;*/

    @FieldDoc(
            description = "商品描述",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    public String description;

    /*@FieldDoc(
            description = "状态(1上架2下架)",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(9)
    public Integer status;*/

    @FieldDoc(
            description = "销售属性集合,多规格必传,仅传递需要新增的销售属性",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(10)
    public List<SaleAttrRelationDTO> saleAttrRelationsList;

    @FieldDoc(
            description = "规格集合",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(11)
    public List<MerchantSkuDTO> skuList;

    @FieldDoc(
            description = "商品卖点",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(12)
    public String sellPoint;

    @FieldDoc(
            description = "渠道SPU编码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(13)
    public String channelSpuId;

    @FieldDoc(
            description = "品牌编码",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(14)
    public String brandCode;


    @FieldDoc(
            description = "配送要求，1常温,2冷藏,3冷冻",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(15)
    public Integer deliveryRequirement;


    @FieldDoc(
            description = "是否允许退差价，1是,2否",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(16)
    public Integer allowWeightRefund;


    @FieldDoc(
            description = "商品spuId",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(17)
    public String spuId;

    @FieldDoc(
            description = "抖音商品类型:0-普通，3-虚拟，6玉石闪购，7云闪购",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(18)
    public Integer productType;

    @FieldDoc(
            description = "渠道图片url",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(19)
    public List<String> pictureChannelUrlList;

    @FieldDoc(
            description = "渠道图详url",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(20)
    public List<String> pictureContentChannelUrlList;

    @FieldDoc(
            description = "抖音减库存类型",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(21)
    public Integer reduceType;

    @FieldDoc(
            description = "抖音运费模板id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(22)
    public Integer freightId;

    @FieldDoc(
            description = "商家号码",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(23)
    public String mobile;

    @FieldDoc(
            description = "是否审核商品",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(24)
    public Boolean commit;

    @FieldDoc(
            description = "资质列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(25)
    public List<QualificationDTO> qualityList;

    @FieldDoc(
            description = "类目属性",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(26)
    public String categoryProperties;

    @FieldDoc(
            description = "售后服务",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(27)
    public Map<String,String> afterSaleService;

    @FieldDoc(
            description = "渠道视频id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(28)
    public String channelVideoId;

    @FieldDoc(
            description = "渠道审核状态",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(29)
    public Integer douyinAuditStatus;

    @FieldDoc(
            description = "医药设备资质信息",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(30)
    public ChannelMedicalDeviceQuaInfoDTO medicalDeviceQuaInfo;
}
