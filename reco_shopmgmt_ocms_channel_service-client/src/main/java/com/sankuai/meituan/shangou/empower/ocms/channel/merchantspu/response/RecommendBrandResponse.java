package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import lombok.Data;
import java.util.List;

@TypeDoc(
        description = "获取渠道推荐品牌返回结果"
)
@Data
@ThriftStruct
public class RecommendBrandResponse {

  @FieldDoc(
          description = "返回状态"
  )
  @ThriftField(1)
  private ChannelStatus status;

  @FieldDoc(
          description = "推荐品牌列表"
  )
  @ThriftField(2)
  private List<String> brandIdList;
}

