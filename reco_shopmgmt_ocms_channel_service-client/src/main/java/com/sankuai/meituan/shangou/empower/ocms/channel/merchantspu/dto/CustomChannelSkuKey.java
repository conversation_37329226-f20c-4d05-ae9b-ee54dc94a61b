package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/31
 **/
@TypeDoc(
        description = "渠道规格KEY"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class CustomChannelSkuKey {

    @FieldDoc(
            description = "自定义规格ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String customSkuId;

    @FieldDoc(
            description = "商家端规格ID",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String channelSkuId;
}
