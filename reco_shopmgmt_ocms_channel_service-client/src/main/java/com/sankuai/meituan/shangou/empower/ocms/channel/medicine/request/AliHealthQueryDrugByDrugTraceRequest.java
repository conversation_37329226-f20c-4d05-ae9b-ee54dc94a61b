package com.sankuai.meituan.shangou.empower.ocms.channel.medicine.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通过溯源码查询药品信息接口
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ThriftStruct
public class AliHealthQueryDrugByDrugTraceRequest extends AliHealthChannelBaseRequest {

    @FieldDoc(
            description = "关联entId",
            example = {}
    )
    @ThriftField(4)
    private String relEntId;

    @FieldDoc(
            description = "溯源码，多个溯源码用英文逗号分隔",
            example = {}
    )
    @ThriftField(5)
    private String drugTrace;
}
