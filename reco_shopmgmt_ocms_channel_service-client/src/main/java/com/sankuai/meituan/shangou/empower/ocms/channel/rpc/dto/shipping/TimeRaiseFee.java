package com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.shipping;

import com.facebook.swift.codec.ThriftField;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
//时间加价对象
public class TimeRaiseFee {

    //开始时间
    @ThriftField(1)
    private String startTime;

    //结束时间
    @ThriftField(2)
    private String endTime;

    //加价金额 单位：元
    @ThriftField(3)
    private String raiseFee;
}