package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.TenantChannelRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.UpdateCustomSpuIdDTO;
import lombok.Data;

import java.util.List;

/**
 * @Author: chenlishu
 * @Date: 2024/9/30
 */
@TypeDoc(
        description = "更新商家自定义编码"
)
@Data
@ThriftStruct
public class UpdateCustomSpuIdRequest {
    @FieldDoc(
            description = "基础信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public TenantChannelRequest baseInfo;


    @FieldDoc(
            description = "参数列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public List<UpdateCustomSpuIdDTO> paramList;

}
