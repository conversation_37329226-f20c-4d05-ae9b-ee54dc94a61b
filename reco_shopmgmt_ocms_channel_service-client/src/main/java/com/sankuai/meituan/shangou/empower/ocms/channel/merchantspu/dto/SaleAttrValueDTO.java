package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/16
 * 销售属性值对象
 **/
@TypeDoc(
        description = "销售属性值对象，多规格时使用"
)
@Data
@ThriftStruct
public class SaleAttrValueDTO {

    @FieldDoc(
            description = "销售属性名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String saleAttrName;

    @FieldDoc(
            description = "销售属性值",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String saleAttrValue;
}
