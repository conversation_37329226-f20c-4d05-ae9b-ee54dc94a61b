package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.TenantChannelRequest;
import lombok.Data;

import java.util.List;

/**
 * @Author: luokai14
 * @Date: 2023/12/21 2:15 下午
 * @Mail: <EMAIL>
 */

@TypeDoc(
        description = "更新商品店内分类"
)
@Data
@ThriftStruct
public class UpdateProductStoreCategoryRequest {

    @FieldDoc(
            description = "基础信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public TenantChannelRequest baseInfo;

    @FieldDoc(
            description = "商品id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String channelSpuId;

    @FieldDoc(
            description = "店内分类id列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public List<String> channelStoreCategoryList;


    @FieldDoc(
            description = "规格类型-京东渠道使用",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer specType;


    @FieldDoc(
            description = "商品自定义id-京东渠道使用",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public String  customSpuId;



}
