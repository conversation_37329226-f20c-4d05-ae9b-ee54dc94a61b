package com.sankuai.meituan.shangou.empower.ocms.channel.exception;

import com.sankuai.meituan.shangou.saas.common.exception.BizException;

/**
 * 配送操作失败异常
 *
 * <AUTHOR>
 * @since 2023/3/23
 */
public class DeliveryOperationFailedException extends BizException {

    private static final long serialVersionUID = -4993944238711908162L;

    public DeliveryOperationFailedException(String message) {
        super(message);
    }

    public DeliveryOperationFailedException(Integer errCode,String message) {
        super(errCode,message);
    }

    public DeliveryOperationFailedException(String message, Throwable e) {
        super(message, e);
    }

}
