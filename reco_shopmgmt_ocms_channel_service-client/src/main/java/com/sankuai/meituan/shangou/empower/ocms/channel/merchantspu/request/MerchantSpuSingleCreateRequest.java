package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.TenantChannelRequest;
import lombok.Data;

/**
 * @Author: luokai14
 * @Date: 2023/12/19 10:24 上午
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "总部商品单个创建请求"
)
@Data
@ThriftStruct
public class MerchantSpuSingleCreateRequest {
    @FieldDoc(
            description = "基础请求信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public TenantChannelRequest baseInfo;


    @FieldDoc(
            description = "创建参数",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public MerchantSpuDTO merchantSpuDTO;
}
