package com.sankuai.meituan.shangou.empower.ocms.channel.medicine.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * 阿里健康渠道基础请求
 */
@Data
@ThriftStruct
public class AliHealthChannelBaseRequest {

    @FieldDoc(
            description = "appKey",
            example = {}
    )
    @ThriftField(1)
    private String appKey;

    @FieldDoc(
            description = "时间戳，格式：yyyy-MM-dd HH:mm:ss",
            example = {}
    )
    @ThriftField(2)
    private String timestamp;

    @FieldDoc(
            description = "因为阿里的访问secret比较麻烦，每个key有自己的限流，所以采用直接由客户端计算签名，v: 2.0, sign_method: hmac, format: json",
            example = {}
    )
    @ThriftField(3)
    private String sign;

}
