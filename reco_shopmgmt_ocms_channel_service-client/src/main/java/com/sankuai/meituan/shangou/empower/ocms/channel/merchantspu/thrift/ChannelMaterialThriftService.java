package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift;

import org.apache.thrift.TException;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialSearchRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialSearchResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialUploadRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.product.MaterialUploadResponse;

/**
 * 渠道素材接口
 *
 * <AUTHOR>
 * @since 2023-12-20
 */
@ThriftService
public interface ChannelMaterialThriftService {
    @MethodDoc(
            displayName = "批量上传素材接口",
            description = "批量上传素材接口",
            returnValueDescription = "批量上传素材接口返回值",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量上传素材接口请求"
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "框架异常，主要包括超时等"
                    )
            },
            example = "请求示例:\n\n" + "返回示例:\n"
    )
    @ThriftMethod
    MaterialUploadResponse batchUploadMaterial(MaterialUploadRequest request);

    @MethodDoc(
            displayName = "切换工具虚拟账号批量上传素材接口",
            description = "切换工具虚拟账号批量上传素材接口",
            returnValueDescription = "切换工具虚拟账号批量上传素材接口返回值",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "切换工具虚拟账号批量上传素材接口请求"
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "框架异常，主要包括超时等"
                    )
            },
            example = "请求示例:\n\n" + "返回示例:\n"
    )
    @ThriftMethod
    MaterialUploadResponse batchUploadMaterialByVirtualConfig(MaterialUploadRequest request);

    @MethodDoc(
            displayName = "搜索素材详情接口",
            description = "搜索素材详情接口",
            returnValueDescription = "搜索素材详情接口返回值",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "搜索素材详情接口请求"
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "框架异常，主要包括超时等"
                    )
            },
            example = "请求示例:\n\n" + "返回示例:\n"
    )
    @ThriftMethod
    MaterialSearchResponse searchMaterial(MaterialSearchRequest request);

    @MethodDoc(
            displayName = "切换工具虚拟账号搜索素材详情接口",
            description = "切换工具虚拟账号搜索素材详情接口",
            returnValueDescription = "切换工具虚拟账号搜索素材详情接口返回值",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "切换工具虚拟账号搜索素材详情接口请求"
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "框架异常，主要包括超时等"
                    )
            },
            example = "请求示例:\n\n" + "返回示例:\n"
    )
    @ThriftMethod
    MaterialSearchResponse searchMaterialByVirtualConfig(MaterialSearchRequest request);
}
