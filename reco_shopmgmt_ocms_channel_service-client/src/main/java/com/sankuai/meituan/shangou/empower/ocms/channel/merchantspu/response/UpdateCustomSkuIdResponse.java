package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.MerchantSkuResult;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/16
 **/
@TypeDoc(
        description = "总部商品批量更新返回"
)
@Data
@ThriftStruct
public class UpdateCustomSkuIdResponse {

    @FieldDoc(
            description = "返回状态"
    )
    @ThriftField(1)
    private ChannelStatus status;

    @FieldDoc(
            description = "SPU结果列表"
    )
    @ThriftField(2)
    private List<MerchantSkuResult> spuResultList;
}
