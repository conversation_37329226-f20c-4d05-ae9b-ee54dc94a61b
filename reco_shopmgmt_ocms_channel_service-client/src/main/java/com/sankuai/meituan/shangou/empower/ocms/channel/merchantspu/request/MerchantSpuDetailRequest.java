package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.CustomSpuDeleteDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.TenantChannelRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/11/28
 **/
@TypeDoc(
        description = "获取商品详情"
)
@Data
@ThriftStruct
public class MerchantSpuDetailRequest {

    @FieldDoc(
            description = "基础请求信息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public TenantChannelRequest baseInfo;

    @FieldDoc(
            description = "商品spuId",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String customSpuId;

    @FieldDoc(
            description = "渠道spuId",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public String channelSpuId;

    @FieldDoc(
            description = "规格类型(1单规格2多规格)",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer specType;

    @FieldDoc(
            description = "是否使用渠道spuId操作（true是，默认false）",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(value = 5, requiredness = ThriftField.Requiredness.OPTIONAL)
    public boolean optByChannelSpuId;
}
