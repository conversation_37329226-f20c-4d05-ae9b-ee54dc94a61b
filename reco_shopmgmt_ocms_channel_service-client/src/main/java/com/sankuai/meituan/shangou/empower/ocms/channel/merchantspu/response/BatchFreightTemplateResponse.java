package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response;

import java.util.List;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.FreightTemplateDTO;

import lombok.Data;

/**
 * @Author: luokai14
 * @Date: 2024/1/23 11:41 上午
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "运费模板返回"
)
@Data
@ThriftStruct
public class BatchFreightTemplateResponse {
    @FieldDoc(
            description = "返回状态"
    )
    @ThriftField(1)
    private ChannelStatus status;

    @FieldDoc(
            description = "结果列表"
    )
    @ThriftField(2)
    List<FreightTemplateDTO> freightTemplateDTOList;

}
