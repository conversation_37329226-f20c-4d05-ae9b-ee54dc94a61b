package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/16
 **/
@TypeDoc(
        description = "总部多规格结果"
)
@Data
@ThriftStruct
public class MerchantSkuResult {

    @FieldDoc(
            description = "商家自定义SPU编码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String customSkuId;

    @FieldDoc(
            description = "渠道SPU编码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public String channelSkuId;

    @FieldDoc(
            description = "SKU返回值列表",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public List<CustomChannelSkuKey> skuResultList;

    @FieldDoc(
            description = "结果状态码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public Integer resultCode;

    @FieldDoc(
            description = "结果状态消息",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(5)
    public String resultMsg;

    @FieldDoc(
        description = "统一渠道错误码",
        requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    public Integer channelUnifyError;

    @FieldDoc(
        description = "渠道返回信息",
        requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    public String channelResultInfo;


    public MerchantSkuResult buildResult(Integer code, String msg) {
        this.resultCode = code;
        this.resultMsg = msg;
        return this;
    }

    public MerchantSkuResult buildResult(Integer code, String msg, Integer channelUnifyError) {
        this.resultCode = code;
        this.resultMsg = msg;
        this.channelUnifyError = channelUnifyError;
        return this;
    }
}
