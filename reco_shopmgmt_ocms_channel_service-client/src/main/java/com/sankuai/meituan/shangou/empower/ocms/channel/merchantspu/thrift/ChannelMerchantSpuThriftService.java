package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.UpdateCustomSkuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.UpdateCustomSpuIdRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.BatchGetFreightTemplateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleCreateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleDeleteRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuSingleUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.MerchantSpuUpdateRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request.UpdateProductStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.BatchFreightTemplateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSkuListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleCreateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleDeleteResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuSingleUpdateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.MerchantSpuUpdateResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.UpdateCustomSkuIdResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response.UpdateCustomSpuIdResponse;
import org.apache.thrift.TException;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/16
 * 渠道总部商品接口
 **/
@ThriftService
public interface ChannelMerchantSpuThriftService {


    @MethodDoc(
            displayName = "批量创建总部商品接口",
            description = "批量创建总部商品接口",
            returnValueDescription = "批量创建总部商品接口返回值",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量创建总部商品接口请求"
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "框架异常，主要包括超时等"
                    )
            },
            example = "请求示例:\n\n" + "返回示例:\n"
    )
    @ThriftMethod
    MerchantSpuCreateResponse batchCreateSpu(MerchantSpuCreateRequest request);

    @MethodDoc(
            displayName = "批量更新总部商品接口",
            description = "批量更新总部商品接口",
            returnValueDescription = "批量更新总部商品接口返回值",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量更新总部商品接口请求"
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "框架异常，主要包括超时等"
                    )
            },
            example = "请求示例:\n\n" + "返回示例:\n"
    )
    @ThriftMethod
    MerchantSpuUpdateResponse batchUpdateSpu(MerchantSpuUpdateRequest request);

    @MethodDoc(
            displayName = "批量删除总部商品接口",
            description = "批量删除总部商品接口",
            returnValueDescription = "批量删除总部商品接口返回值",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量更新总部商品接口请求"
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "框架异常，主要包括超时等"
                    )
            },
            example = "请求示例:\n\n" + "返回示例:\n"
    )
    @ThriftMethod
    MerchantSpuDeleteResponse batchDeleteSpu(MerchantSpuDeleteRequest request);

    @ThriftMethod
    MerchantSpuDetailResponse getSpuDetail(MerchantSpuDetailRequest request);

    @ThriftMethod
    MerchantSkuListResponse getMerchantSkuList(MerchantSpuDetailRequest request);


    @MethodDoc(
            displayName = "获取商品详情单规格",
            description = "获取商品详情单规格",
            returnValueDescription = "获取商品详情单规格接口返回值",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取商品详情单规格接口请求"
                    )
            },
            exceptions = {
                    @ExceptionDoc(
                            type = TException.class,
                            description = "框架异常，主要包括超时等"
                    )
            },
            example = "请求示例:\n\n" + "返回示例:\n"
    )
    @ThriftMethod
    MerchantSpuDetailResponse getSpuDetailSingle(MerchantSpuDetailRequest request);

    @ThriftMethod
    MerchantSpuCreateResponse getSpuCreateStatus(MerchantSpuDetailRequest request);


    @ThriftMethod
    MerchantSpuSingleCreateResponse singleCreateSpu(MerchantSpuSingleCreateRequest request);



    @ThriftMethod
    MerchantSpuSingleUpdateResponse singleUpdateSpu(MerchantSpuSingleUpdateRequest request);


    @ThriftMethod
    MerchantSpuSingleDeleteResponse singleDeleteSpu(MerchantSpuSingleDeleteRequest request);


    @ThriftMethod
    MerchantSpuSingleUpdateResponse singleUpdateSpuStoreCategory(UpdateProductStoreCategoryRequest request);

    @ThriftMethod
    BatchFreightTemplateResponse getFreightTemplateList(BatchGetFreightTemplateRequest request);

    @ThriftMethod
    UpdateCustomSpuIdResponse updateCustomSpuId(UpdateCustomSpuIdRequest request);

    @ThriftMethod
    UpdateCustomSkuIdResponse updateCustomSkuId(UpdateCustomSkuIdRequest request);

}
