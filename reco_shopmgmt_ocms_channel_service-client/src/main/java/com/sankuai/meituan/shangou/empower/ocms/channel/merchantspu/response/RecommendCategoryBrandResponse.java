package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;

import lombok.Data;
/**
 * <AUTHOR>
 * @description 获取类目&品牌返回
 * @since 2023/12/19 16:03
 */
@TypeDoc(
        description = "获取渠道推荐品牌返回结果"
)
@Data
@ThriftStruct
public class RecommendCategoryBrandResponse {

  @FieldDoc(
          description = "返回状态"
  )
  @ThriftField(1)
  private ChannelStatus status;

  @FieldDoc(
          description = "推荐品牌"
  )
  @ThriftField(2)
  private String brandId;

  @FieldDoc(
          description = "推荐类目"
  )
  @ThriftField(3)
  private Long categoryCode;
}

