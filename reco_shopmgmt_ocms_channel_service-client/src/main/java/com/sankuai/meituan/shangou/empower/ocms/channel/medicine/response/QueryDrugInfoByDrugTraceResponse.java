package com.sankuai.meituan.shangou.empower.ocms.channel.medicine.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.channel.medicine.dto.AliProductFullInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "返回结果")
public class QueryDrugInfoByDrugTraceResponse {

    @FieldDoc(
            name = "code",
            description = "状态码，非0即为调用失败",
            example = {"0"}
    )
    @ThriftField(1)
    private Integer code;

    @FieldDoc(
            name = "message",
            description = "返回信息",
            example = {}
    )
    @ThriftField(2)
    private String message;

    @FieldDoc(
            name = "dataList",
            description = "列表结果",
            example = {}
    )
    @ThriftField(3)
    private List<AliProductFullInfoDTO> dataList;
}
