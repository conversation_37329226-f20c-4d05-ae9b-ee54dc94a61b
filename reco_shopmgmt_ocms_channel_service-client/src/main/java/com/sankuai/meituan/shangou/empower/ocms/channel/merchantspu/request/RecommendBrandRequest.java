package com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;



@TypeDoc(
        description = "获取品牌数据请求参数"
)
@Data
@ThriftStruct
public class RecommendBrandRequest {

  @FieldDoc(
          description = "租户ID",
          requiredness = Requiredness.REQUIRED
  )
  @ThriftField(1)
  public Long tenantId;

  @FieldDoc(
          description = "渠道ID",
          requiredness = Requiredness.REQUIRED
  )
  @ThriftField(2)
  public Integer channelId;


  @FieldDoc(
          description = "商品名称",
          requiredness = Requiredness.REQUIRED
  )
  @ThriftField(3)
  public String spuName;

  @FieldDoc(
          description = "商品名称",
          requiredness = Requiredness.REQUIRED
  )
  @ThriftField(4)
  public String categoryId;
}


