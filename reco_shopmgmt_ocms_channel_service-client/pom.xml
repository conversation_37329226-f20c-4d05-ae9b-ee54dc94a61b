<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
        <artifactId>reco_shopmgmt_ocms_channel_service</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../</relativePath>
    </parent>

    <artifactId>reco_shopmgmt_ocms_channel_service-client</artifactId>
    <version>${ocms.channel.client.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>thrift-xframe-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>store-saas-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.linz</groupId>
            <artifactId>linz-thrift</artifactId>
            <version>1.0.9.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
            <version>0.9.3-mt2</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.17</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.sankuai.dolphin</groupId>
                <artifactId>thrift-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
                <version>1.2.4</version>
                <configuration>
                    <appkey>com.sankuai.shangou.empower.ocmschannel</appkey>
                    <codeDir>${basedir}/src/main/java
                    </codeDir><!--生成代码目标路径。可以不填写。运行generate命令时，默认路经为target/generated-source -->
                    <thriftVersion>0.8.0</thriftVersion><!--Thrift编译器版本。可以不填写。默认0.8.0 -->
                    <language>java</language><!--目标语言。可以不填写。默认java-->
                    <idlDir>${basedir}/src/main/resources/thrift</idlDir><!--idl文件所在路径。可以不填写。 -->
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>**/*.thrift</exclude>
                </excludes>
            </resource>
        </resources>
    </build>
</project>
