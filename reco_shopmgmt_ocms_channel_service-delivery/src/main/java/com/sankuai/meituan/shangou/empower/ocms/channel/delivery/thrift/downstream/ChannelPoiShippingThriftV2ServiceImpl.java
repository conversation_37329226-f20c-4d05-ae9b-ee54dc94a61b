package com.sankuai.meituan.shangou.empower.ocms.channel.delivery.thrift.downstream;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.TimeMarkupFactor;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.DistanceMarkupFactor;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MarkupExecuteTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.WeightMarkupFactor;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.QueryLogisticsCodeResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.shipping.DeliveryFeeRuleSettingRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.shipping.QueryLogisticsCodesCodeRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.shipping.DistanceRaiseFee;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelPoiShippingThriftV2Service;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.route.RouteServiceFactory;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/9/9
 */

@Slf4j
@Service
public class ChannelPoiShippingThriftV2ServiceImpl implements ChannelPoiShippingThriftV2Service {

    @Resource
    private RouteServiceFactory routeServiceFactory;

    @Override
    public QueryLogisticsCodeResponse queryChannelDeliveryLogicsCode(QueryLogisticsCodesCodeRequest request) {

        List<ChannelPoiInfo> channelPoiInfoList = routeServiceFactory.selectChannelPoiService(request.getChannelId(), request.getTenantId()).queryChannelPoiInfoList(
                request.getTenantId(), request.getChannelStoreCodes(), request.getChannelId()
        );
        Map<String, List<String>> resMap = Optional.ofNullable(channelPoiInfoList)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(
                        ChannelPoiInfo::getApp_poi_code,
                        it -> StringUtils.isNotBlank(it.getLogistics_codes()) ? Lists.newArrayList(it.getLogistics_codes().split(Constant.COMMA)) : Lists.newArrayList(),
                        (older, newer) -> newer
                ));
        return new QueryLogisticsCodeResponse(ChannelStatus.buildSuccess(), resMap);
    }

    @Override
    public ResultStatus updateDeliveryFeeRuleSetting(DeliveryFeeRuleSettingRequest request) {
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ResultCode.SUCCESS.getCode());
        resultStatus.setMsg(StringUtils.EMPTY);

        //美团比较特殊，配送范围相关接口有两套：自配送和企客
        if (Objects.equals(request.getChannelId(), DynamicChannelType.MEITUAN.getChannelId())) {
            //自配和企客可以共存吗？
            boolean containsCorporateDelivery = request.getLogisticsCodes().stream().anyMatch(code -> MccConfigUtil.getCorporateLogisticsCodes().contains(code));
            //企客相关接口
            if (containsCorporateDelivery) {

            } else {
                //非企客配送，走自配接口，不再识别
                QueryPoiShippingRequest queryPoiShippingRequest = new QueryPoiShippingRequest();
                queryPoiShippingRequest.setTenantId(request.getTenantId());
                queryPoiShippingRequest.setPoiId(request.getStoreId());
                queryPoiShippingRequest.setChannelId(request.getChannelId());
                QueryPoiShippingResponse queryPoiShippingResponse = routeServiceFactory.selectChannelPoiShippingService(request.getChannelId(), request.getTenantId()).queryPoiShipping(queryPoiShippingRequest);
                if (!Objects.equals(queryPoiShippingResponse.getCode(), ResultCode.SUCCESS.getCode())) {
                    resultStatus.setCode(queryPoiShippingResponse.getCode());
                    resultStatus.setMsg(queryPoiShippingResponse.getMsg());
                    return resultStatus;
                }
                List<PoiShippingInfoDTO> poiShippingInfoDTOList = queryPoiShippingResponse.getPoiShippingInfoDTOList();
                if (CollectionUtils.isEmpty(poiShippingInfoDTOList)) {
                    log.info("no poiShippingInfoDTOList, request = {}", request);
                    return resultStatus;
                }

                BatchUpdatePoiShippingRequest batchUpdatePoiShippingRequest = new BatchUpdatePoiShippingRequest();
                batchUpdatePoiShippingRequest.setTenantId(request.getTenantId());
                batchUpdatePoiShippingRequest.setShopId(request.getStoreId());
                batchUpdatePoiShippingRequest.setChannelId(request.getChannelId());
                batchUpdatePoiShippingRequest.setPoiShippingInfo(
                        poiShippingInfoDTOList.stream()
                                .map(
                                        dto -> {
                                            PoiShippingInfo poiShippingInfo = new PoiShippingInfo();
                                            poiShippingInfo.setAppShippingCode(Integer.parseInt(dto.getAppShippingCode()));
                                            List<Coordinate> coordinateList = Optional.ofNullable(dto.getArea()).orElse(Lists.newArrayList())
                                                    .stream()
                                                    .map(
                                                            area -> {
                                                                Coordinate coordinate = new Coordinate();
                                                                coordinate.setLongitude(area.getY());
                                                                coordinate.setLatitude(area.getX());
                                                                return coordinate;
                                                            }
                                                    ).collect(Collectors.toList());
                                            poiShippingInfo.setCoordinateList(coordinateList);
                                            poiShippingInfo.setMtShippingId(dto.getMtShippingId());

                                            poiShippingInfo.setMinPriceDouble(new BigDecimal(request.getBaseMinimumDeliveryFee()).doubleValue());
                                            poiShippingInfo.setShippingFee(new BigDecimal(request.getBaseDeliveryFee()).doubleValue());
                                            if (CollectionUtils.isNotEmpty(request.getDistanceRaiseFeeList())) {
                                                poiShippingInfo.setDistanceMarkupExecuteType(MarkupExecuteTypeEnum.ENABLE.getCode());

                                                List<DistanceRaiseFee>  distanceRaiseFeeList = request.getDistanceRaiseFeeList().stream().map(
                                                        distanceRaiseFee -> {
                                                            DistanceMarkupFactor distanceMarkupFactor = new DistanceMarkupFactor();
                                                            distanceMarkupFactor.setDistance(new BigDecimal(distanceRaiseFee.getStartDistance()).doubleValue());
                                                            distanceMarkupFactor.setMarkupNum(new BigDecimal(distanceRaiseFee.getRaiseFee()).doubleValue());
                                                            return distanceRaiseFee;
                                                        }
                                                ).collect(Collectors.toList());
                                                poiShippingInfo.setDistanceMarkupFactors(JSON.toJSONString(distanceRaiseFeeList));
                                            } else {
                                                poiShippingInfo.setDistanceMarkupExecuteType(MarkupExecuteTypeEnum.DISABLE.getCode());
                                            }

                                            if(CollectionUtils.isNotEmpty(request.getWeightRaiseFeeList())) {
                                                poiShippingInfo.setWeightMarkupExecuteType(MarkupExecuteTypeEnum.ENABLE.getCode());
                                                List<WeightMarkupFactor> weightMarkupFactors = request.getWeightRaiseFeeList().stream().map(
                                                        weightRaiseFee -> {
                                                            WeightMarkupFactor weightMarkupFactor = new WeightMarkupFactor();
                                                            weightMarkupFactor.setWeight(new BigDecimal(weightRaiseFee.getStartWeight()).doubleValue());
                                                            weightMarkupFactor.setStep(weightRaiseFee.getRaiseStep());
                                                            weightMarkupFactor.setMarkupNum(new BigDecimal(weightRaiseFee.getRaiseFee()).doubleValue());
                                                            return weightMarkupFactor;
                                                        }
                                                ).collect(Collectors.toList());

                                                poiShippingInfo.setWeightMarkupFactors(JSON.toJSONString(weightMarkupFactors));
                                            } else {
                                                poiShippingInfo.setWeightMarkupExecuteType(MarkupExecuteTypeEnum.DISABLE.getCode());
                                            }

                                            if (CollectionUtils.isNotEmpty(request.getTimeRaiseFeeList())) {
                                                poiShippingInfo.setTimeMarkupExecuteType(MarkupExecuteTypeEnum.ENABLE.getCode());
                                                List<TimeMarkupFactor> timeMarkupFactors = request.getTimeRaiseFeeList().stream().map(
                                                        timeRaiseFee -> {
                                                            TimeMarkupFactor timeMarkupFactor = new TimeMarkupFactor();
                                                            String timeRange = timeRaiseFee.getStartTime() + "-" + timeRaiseFee.getEndTime();
                                                            timeMarkupFactor.setTimeRange(timeRange);
                                                            timeMarkupFactor.setMarkupNum(new BigDecimal(timeRaiseFee.getRaiseFee()).doubleValue());
                                                            return timeMarkupFactor;
                                                        }
                                                ).collect(Collectors.toList());
                                                poiShippingInfo.setTimeMarkupFactors(JSON.toJSONString(timeMarkupFactors));
                                            } else {
                                                poiShippingInfo.setTimeMarkupExecuteType(MarkupExecuteTypeEnum.DISABLE.getCode());
                                            }

                                            return poiShippingInfo;
                                        }
                                ).collect(Collectors.toList())
                );

                routeServiceFactory.selectChannelPoiShippingService(request.getChannelId(), request.getTenantId()).batchUpdatePoiShipping(batchUpdatePoiShippingRequest);
            }
        }

        return resultStatus;
    }
}
